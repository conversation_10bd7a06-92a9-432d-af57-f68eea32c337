import {
  esm_default
} from "./chunk-ISMEUBKR.js";
import "./chunk-C5KNTEDU.js";
import {
  fromStore
} from "./chunk-5IRPM5PB.js";
import "./chunk-MZKCMDML.js";
import {
  add_locations,
  check_target,
  each,
  hmr,
  if_block,
  index,
  legacy_api,
  prop,
  rest_props,
  set_attributes,
  snippet,
  wrap_snippet
} from "./chunk-CRCQ7E27.js";
import {
  clsx
} from "./chunk-U7P2NEEE.js";
import {
  append,
  comment,
  set_text,
  template
} from "./chunk-OSNF6FE7.js";
import {
  FILENAME,
  HMR,
  child,
  derived_safe_equal,
  first_child,
  get,
  getContext,
  noop,
  pop,
  proxy,
  push,
  reset,
  set,
  setContext,
  state,
  strict_equals,
  template_effect,
  untrack,
  user_derived,
  user_effect
} from "./chunk-PJ2X7CWE.js";
import "./chunk-VIZMNZTH.js";
import "./chunk-HNWPC2PS.js";
import "./chunk-RVAV4ZRS.js";
import {
  __privateAdd,
  __privateGet,
  __privateSet,
  __publicField
} from "./chunk-KWPVD4H7.js";

// node_modules/formsnap/node_modules/svelte-toolbelt/dist/utils/is.js
function isFunction(value) {
  return typeof value === "function";
}
function isObject(value) {
  return value !== null && typeof value === "object";
}
var CLASS_VALUE_PRIMITIVE_TYPES = ["string", "number", "bigint", "boolean"];
function isClassValue(value) {
  if (value === null || value === void 0)
    return true;
  if (CLASS_VALUE_PRIMITIVE_TYPES.includes(typeof value))
    return true;
  if (Array.isArray(value))
    return value.every((item) => isClassValue(item));
  if (typeof value === "object") {
    if (Object.getPrototypeOf(value) !== Object.prototype)
      return false;
    return true;
  }
  return false;
}

// node_modules/formsnap/node_modules/svelte-toolbelt/dist/box/box.svelte.js
var BoxSymbol = Symbol("box");
var isWritableSymbol = Symbol("is-writable");
function isBox(value) {
  return isObject(value) && BoxSymbol in value;
}
function isWritableBox(value) {
  return box.isBox(value) && isWritableSymbol in value;
}
function box(initialValue) {
  let current = state(proxy(initialValue));
  return {
    [BoxSymbol]: true,
    [isWritableSymbol]: true,
    get current() {
      return get(current);
    },
    set current(v) {
      set(current, v, true);
    }
  };
}
function boxWith(getter, setter) {
  const derived = user_derived(getter);
  if (setter) {
    return {
      [BoxSymbol]: true,
      [isWritableSymbol]: true,
      get current() {
        return get(derived);
      },
      set current(v) {
        setter(v);
      }
    };
  }
  return {
    [BoxSymbol]: true,
    get current() {
      return getter();
    }
  };
}
function boxFrom(value) {
  if (box.isBox(value)) return value;
  if (isFunction(value)) return box.with(value);
  return box(value);
}
function boxFlatten(boxes) {
  return Object.entries(boxes).reduce(
    (acc, [key, b]) => {
      if (!box.isBox(b)) {
        return Object.assign(acc, { [key]: b });
      }
      if (box.isWritableBox(b)) {
        Object.defineProperty(acc, key, {
          get() {
            return b.current;
          },
          set(v) {
            b.current = v;
          }
        });
      } else {
        Object.defineProperty(acc, key, {
          get() {
            return b.current;
          }
        });
      }
      return acc;
    },
    {}
  );
}
function toReadonlyBox(b) {
  if (!box.isWritableBox(b)) return b;
  return {
    [BoxSymbol]: true,
    get current() {
      return b.current;
    }
  };
}
box.from = boxFrom;
box.with = boxWith;
box.flatten = boxFlatten;
box.readonly = toReadonlyBox;
box.isBox = isBox;
box.isWritableBox = isWritableBox;

// node_modules/formsnap/node_modules/svelte-toolbelt/dist/utils/compose-handlers.js
function composeHandlers(...handlers) {
  return function(e) {
    var _a;
    for (const handler of handlers) {
      if (!handler)
        continue;
      if (e.defaultPrevented)
        return;
      if (typeof handler === "function") {
        handler.call(this, e);
      } else {
        (_a = handler.current) == null ? void 0 : _a.call(this, e);
      }
    }
  };
}

// node_modules/formsnap/node_modules/svelte-toolbelt/dist/utils/strings.js
var NUMBER_CHAR_RE = /\d/;
var STR_SPLITTERS = ["-", "_", "/", "."];
function isUppercase(char = "") {
  if (NUMBER_CHAR_RE.test(char))
    return void 0;
  return char !== char.toLowerCase();
}
function splitByCase(str) {
  const parts = [];
  let buff = "";
  let previousUpper;
  let previousSplitter;
  for (const char of str) {
    const isSplitter = STR_SPLITTERS.includes(char);
    if (isSplitter === true) {
      parts.push(buff);
      buff = "";
      previousUpper = void 0;
      continue;
    }
    const isUpper = isUppercase(char);
    if (previousSplitter === false) {
      if (previousUpper === false && isUpper === true) {
        parts.push(buff);
        buff = char;
        previousUpper = isUpper;
        continue;
      }
      if (previousUpper === true && isUpper === false && buff.length > 1) {
        const lastChar = buff.at(-1);
        parts.push(buff.slice(0, Math.max(0, buff.length - 1)));
        buff = lastChar + char;
        previousUpper = isUpper;
        continue;
      }
    }
    buff += char;
    previousUpper = isUpper;
    previousSplitter = isSplitter;
  }
  parts.push(buff);
  return parts;
}
function pascalCase(str) {
  if (!str)
    return "";
  return splitByCase(str).map((p) => upperFirst(p)).join("");
}
function camelCase(str) {
  return lowerFirst(pascalCase(str || ""));
}
function upperFirst(str) {
  return str ? str[0].toUpperCase() + str.slice(1) : "";
}
function lowerFirst(str) {
  return str ? str[0].toLowerCase() + str.slice(1) : "";
}

// node_modules/formsnap/node_modules/svelte-toolbelt/dist/utils/css-to-style-obj.js
function cssToStyleObj(css) {
  if (!css)
    return {};
  const styleObj = {};
  function iterator(name, value) {
    if (name.startsWith("-moz-") || name.startsWith("-webkit-") || name.startsWith("-ms-") || name.startsWith("-o-")) {
      styleObj[pascalCase(name)] = value;
      return;
    }
    if (name.startsWith("--")) {
      styleObj[name] = value;
      return;
    }
    styleObj[camelCase(name)] = value;
  }
  esm_default(css, iterator);
  return styleObj;
}

// node_modules/formsnap/node_modules/svelte-toolbelt/dist/utils/execute-callbacks.js
function executeCallbacks(...callbacks) {
  return (...args) => {
    for (const callback of callbacks) {
      if (typeof callback === "function") {
        callback(...args);
      }
    }
  };
}

// node_modules/formsnap/node_modules/svelte-toolbelt/dist/utils/style-to-css.js
function createParser(matcher, replacer) {
  const regex = RegExp(matcher, "g");
  return (str) => {
    if (typeof str !== "string") {
      throw new TypeError(`expected an argument of type string, but got ${typeof str}`);
    }
    if (!str.match(regex))
      return str;
    return str.replace(regex, replacer);
  };
}
var camelToKebab = createParser(/[A-Z]/, (match) => `-${match.toLowerCase()}`);
function styleToCSS(styleObj) {
  if (!styleObj || typeof styleObj !== "object" || Array.isArray(styleObj)) {
    throw new TypeError(`expected an argument of type object, but got ${typeof styleObj}`);
  }
  return Object.keys(styleObj).map((property) => `${camelToKebab(property)}: ${styleObj[property]};`).join("\n");
}

// node_modules/formsnap/node_modules/svelte-toolbelt/dist/utils/style.js
function styleToString(style = {}) {
  return styleToCSS(style).replace("\n", " ");
}
var srOnlyStyles = {
  position: "absolute",
  width: "1px",
  height: "1px",
  padding: "0",
  margin: "-1px",
  overflow: "hidden",
  clip: "rect(0, 0, 0, 0)",
  whiteSpace: "nowrap",
  borderWidth: "0",
  transform: "translateX(-100%)"
};
var srOnlyStylesString = styleToString(srOnlyStyles);

// node_modules/formsnap/node_modules/svelte-toolbelt/dist/utils/merge-props.js
function isEventHandler(key) {
  var _a;
  return key.length > 2 && key.startsWith("on") && key[2] === ((_a = key[2]) == null ? void 0 : _a.toLowerCase());
}
function mergeProps(...args) {
  const result = { ...args[0] };
  for (let i = 1; i < args.length; i++) {
    const props = args[i];
    for (const key in props) {
      const a = result[key];
      const b = props[key];
      const aIsFunction = typeof a === "function";
      const bIsFunction = typeof b === "function";
      if (aIsFunction && typeof bIsFunction && isEventHandler(key)) {
        const aHandler = a;
        const bHandler = b;
        result[key] = composeHandlers(aHandler, bHandler);
      } else if (aIsFunction && bIsFunction) {
        result[key] = executeCallbacks(a, b);
      } else if (key === "class") {
        const aIsClassValue = isClassValue(a);
        const bIsClassValue = isClassValue(b);
        if (aIsClassValue && bIsClassValue) {
          result[key] = clsx(a, b);
        } else if (aIsClassValue) {
          result[key] = clsx(a);
        } else if (bIsClassValue) {
          result[key] = clsx(b);
        }
      } else if (key === "style") {
        const aIsObject = typeof a === "object";
        const bIsObject = typeof b === "object";
        const aIsString = typeof a === "string";
        const bIsString = typeof b === "string";
        if (aIsObject && bIsObject) {
          result[key] = { ...a, ...b };
        } else if (aIsObject && bIsString) {
          const parsedStyle = cssToStyleObj(b);
          result[key] = { ...a, ...parsedStyle };
        } else if (aIsString && bIsObject) {
          const parsedStyle = cssToStyleObj(a);
          result[key] = { ...parsedStyle, ...b };
        } else if (aIsString && bIsString) {
          const parsedStyleA = cssToStyleObj(a);
          const parsedStyleB = cssToStyleObj(b);
          result[key] = { ...parsedStyleA, ...parsedStyleB };
        } else if (aIsObject) {
          result[key] = a;
        } else if (bIsObject) {
          result[key] = b;
        } else if (aIsString) {
          result[key] = a;
        } else if (bIsString) {
          result[key] = b;
        }
      } else {
        result[key] = b !== void 0 ? b : a;
      }
    }
  }
  if (typeof result.style === "object") {
    result.style = styleToString(result.style).replaceAll("\n", " ");
  }
  if (result.hidden !== true) {
    result.hidden = void 0;
    delete result.hidden;
  }
  if (result.disabled !== true) {
    result.disabled = void 0;
    delete result.disabled;
  }
  return result;
}

// node_modules/formsnap/node_modules/svelte-toolbelt/dist/utils/use-ref-by-id.svelte.js
function useRefById({
  id,
  ref,
  deps = () => true,
  onRefChange = () => {
  },
  getRootNode = () => strict_equals(typeof document, "undefined", false) ? document : void 0
}) {
  const dependencies = user_derived(() => deps());
  const rootNode = user_derived(() => getRootNode());
  user_effect(() => {
    id.current;
    get(dependencies);
    get(rootNode);
    return untrack(() => {
      var _a;
      const node = (_a = get(rootNode)) == null ? void 0 : _a.getElementById(id.current);
      if (node) {
        ref.current = node;
      } else {
        ref.current = null;
      }
      onRefChange(ref.current);
    });
  });
  user_effect(() => {
    return () => {
      ref.current = null;
      onRefChange(null);
    };
  });
}

// node_modules/formsnap/node_modules/svelte-toolbelt/dist/utils/use-on-change.svelte.js
function useOnChange(getDep, onChange) {
  const dep = user_derived(getDep);
  user_effect(() => {
    get(dep);
    untrack(() => onChange(get(dep)));
  });
}

// node_modules/formsnap/dist/internal/utils/errors.js
function extractErrorArray(errors) {
  if (Array.isArray(errors))
    return [...errors];
  if (typeof errors === "object" && "_errors" in errors) {
    if (errors._errors !== void 0)
      return [...errors._errors];
  }
  return [];
}

// node_modules/formsnap/dist/internal/utils/path.js
function getValueAtPath(path, obj) {
  const keys = path.split(/[[\].]/).filter(Boolean);
  let value = obj;
  for (const key of keys) {
    if (typeof value !== "object" || value === null) {
      return void 0;
    }
    value = value[key];
  }
  return value;
}

// node_modules/formsnap/dist/internal/utils/attributes.js
function getAriaDescribedBy({ fieldErrorsId = void 0, descriptionId = void 0, errors }) {
  let describedBy = "";
  if (descriptionId) {
    describedBy += `${descriptionId} `;
  }
  if (errors.length && fieldErrorsId) {
    describedBy += fieldErrorsId;
  }
  return describedBy ? describedBy.trim() : void 0;
}
function getAriaRequired(constraints) {
  if (!("required" in constraints))
    return void 0;
  return constraints.required ? "true" : void 0;
}
function getAriaInvalid(errors) {
  return errors && errors.length ? "true" : void 0;
}
function getDataFsError(errors) {
  return errors && errors.length ? "" : void 0;
}

// node_modules/formsnap/dist/internal/utils/id.js
var count = 0;
function useId(prefix = "formsnap") {
  count++;
  return `${prefix}-${count}`;
}

// node_modules/formsnap/dist/formsnap.svelte.js
var _name, _formErrors, _formConstraints, _formTainted, _formData, __name, _errors, _constraints, _tainted, _errorNode, _descriptionNode, _errorId, _descriptionId, _snippetProps;
var FormFieldState = class {
  constructor(props) {
    __privateAdd(this, _name);
    __privateAdd(this, _formErrors);
    __privateAdd(this, _formConstraints);
    __privateAdd(this, _formTainted);
    __privateAdd(this, _formData);
    __publicField(this, "form");
    __privateAdd(this, __name, user_derived(() => __privateGet(this, _name).current));
    __privateAdd(this, _errors, user_derived(() => extractErrorArray(getValueAtPath(__privateGet(this, _name).current, structuredClone(__privateGet(this, _formErrors).current)))));
    __privateAdd(this, _constraints, user_derived(() => getValueAtPath(__privateGet(this, _name).current, structuredClone(__privateGet(this, _formConstraints).current)) ?? {}));
    __privateAdd(this, _tainted, user_derived(() => __privateGet(this, _formTainted).current ? strict_equals(getValueAtPath(__privateGet(this, _name).current, structuredClone(__privateGet(this, _formTainted).current)), true) : false));
    __privateAdd(this, _errorNode, state(null));
    __privateAdd(this, _descriptionNode, state(null));
    __privateAdd(this, _errorId, state());
    __privateAdd(this, _descriptionId, state());
    __privateAdd(this, _snippetProps, user_derived(() => ({
      value: __privateGet(this, _formData).current[__privateGet(this, _name).current],
      errors: this.errors,
      tainted: this.tainted,
      constraints: this.constraints
    })));
    __privateSet(this, _name, props.name);
    this.form = props.form.current;
    __privateSet(this, _formErrors, fromStore(props.form.current.errors));
    __privateSet(this, _formConstraints, fromStore(props.form.current.constraints));
    __privateSet(this, _formTainted, fromStore(props.form.current.tainted));
    __privateSet(this, _formData, fromStore(props.form.current.form));
    user_effect(() => {
      if (this.errorNode && this.errorNode.id) {
        this.errorId = this.errorNode.id;
      }
    });
    user_effect(() => {
      if (this.descriptionNode && this.descriptionNode.id) {
        this.descriptionId = this.descriptionNode.id;
      }
    });
  }
  get name() {
    return get(__privateGet(this, __name));
  }
  set name(value) {
    set(__privateGet(this, __name), value);
  }
  get errors() {
    return get(__privateGet(this, _errors));
  }
  set errors(value) {
    set(__privateGet(this, _errors), value);
  }
  get constraints() {
    return get(__privateGet(this, _constraints));
  }
  set constraints(value) {
    set(__privateGet(this, _constraints), value);
  }
  get tainted() {
    return get(__privateGet(this, _tainted));
  }
  set tainted(value) {
    set(__privateGet(this, _tainted), value);
  }
  get errorNode() {
    return get(__privateGet(this, _errorNode));
  }
  set errorNode(value) {
    set(__privateGet(this, _errorNode), value, true);
  }
  get descriptionNode() {
    return get(__privateGet(this, _descriptionNode));
  }
  set descriptionNode(value) {
    set(__privateGet(this, _descriptionNode), value, true);
  }
  get errorId() {
    return get(__privateGet(this, _errorId));
  }
  set errorId(value) {
    set(__privateGet(this, _errorId), value, true);
  }
  get descriptionId() {
    return get(__privateGet(this, _descriptionId));
  }
  set descriptionId(value) {
    set(__privateGet(this, _descriptionId), value, true);
  }
  get snippetProps() {
    return get(__privateGet(this, _snippetProps));
  }
  set snippetProps(value) {
    set(__privateGet(this, _snippetProps), value);
  }
};
_name = new WeakMap();
_formErrors = new WeakMap();
_formConstraints = new WeakMap();
_formTainted = new WeakMap();
_formData = new WeakMap();
__name = new WeakMap();
_errors = new WeakMap();
_constraints = new WeakMap();
_tainted = new WeakMap();
_errorNode = new WeakMap();
_descriptionNode = new WeakMap();
_errorId = new WeakMap();
_descriptionId = new WeakMap();
_snippetProps = new WeakMap();
var _name2, _formErrors2, _formConstraints2, _formTainted2, _formData2, _field, __name2, _errors2, _constraints2, _tainted2, _errorNode2, _descriptionNode2, _derivedDescriptionNode, _value, _errorId2, _descriptionId2, _snippetProps2;
var ElementFieldState = class {
  constructor(props, field) {
    __privateAdd(this, _name2);
    __privateAdd(this, _formErrors2);
    __privateAdd(this, _formConstraints2);
    __privateAdd(this, _formTainted2);
    __privateAdd(this, _formData2);
    __privateAdd(this, _field);
    __publicField(this, "form");
    __privateAdd(this, __name2, user_derived(() => {
      const [path] = splitArrayPath(__privateGet(this, _name2).current);
      return path;
    }));
    __privateAdd(this, _errors2, user_derived(() => extractErrorArray(getValueAtPath(__privateGet(this, _name2).current, __privateGet(this, _formErrors2).current))));
    __privateAdd(this, _constraints2, user_derived(() => getValueAtPath(__privateGet(this, _name2).current, __privateGet(this, _formConstraints2).current) ?? {}));
    __privateAdd(this, _tainted2, user_derived(() => __privateGet(this, _formTainted2).current ? strict_equals(getValueAtPath(__privateGet(this, _name2).current, __privateGet(this, _formTainted2).current), true) : false));
    __privateAdd(this, _errorNode2, state(null));
    __privateAdd(this, _descriptionNode2, state(null));
    __privateAdd(this, _derivedDescriptionNode, user_derived(() => {
      if (this.descriptionNode) return this.descriptionNode;
      if (__privateGet(this, _field).descriptionNode) return __privateGet(this, _field).descriptionNode;
      return null;
    }));
    __privateAdd(this, _value, user_derived(() => {
      return getValueAtPath(__privateGet(this, _name2).current, __privateGet(this, _formData2).current);
    }));
    __privateAdd(this, _errorId2, state());
    __privateAdd(this, _descriptionId2, state());
    __privateAdd(this, _snippetProps2, user_derived(() => ({
      value: __privateGet(this, _formData2).current[__privateGet(this, _name2).current],
      errors: this.errors,
      tainted: this.tainted,
      constraints: (
        // @ts-expect-error - this type is wonky
        __privateGet(this, _formConstraints2).current[__privateGet(this, _name2).current] ?? {}
      )
    })));
    __privateSet(this, _name2, props.name);
    this.form = props.form.current;
    __privateSet(this, _formErrors2, fromStore(props.form.current.errors));
    __privateSet(this, _formConstraints2, fromStore(props.form.current.constraints));
    __privateSet(this, _formTainted2, fromStore(props.form.current.tainted));
    __privateSet(this, _formData2, fromStore(props.form.current.form));
    __privateSet(this, _field, field);
    useOnChange(() => this.errorNode, (v) => {
      if (v && v.id) {
        this.errorId = v.id;
      }
    });
    useOnChange(() => this.descriptionNode, (v) => {
      if (v && v.id) {
        this.descriptionId = v.id;
      }
    });
  }
  get name() {
    return get(__privateGet(this, __name2));
  }
  set name(value) {
    set(__privateGet(this, __name2), value);
  }
  get errors() {
    return get(__privateGet(this, _errors2));
  }
  set errors(value) {
    set(__privateGet(this, _errors2), value);
  }
  get constraints() {
    return get(__privateGet(this, _constraints2));
  }
  set constraints(value) {
    set(__privateGet(this, _constraints2), value);
  }
  get tainted() {
    return get(__privateGet(this, _tainted2));
  }
  set tainted(value) {
    set(__privateGet(this, _tainted2), value);
  }
  get errorNode() {
    return get(__privateGet(this, _errorNode2));
  }
  set errorNode(value) {
    set(__privateGet(this, _errorNode2), value, true);
  }
  get descriptionNode() {
    return get(__privateGet(this, _descriptionNode2));
  }
  set descriptionNode(value) {
    set(__privateGet(this, _descriptionNode2), value, true);
  }
  get derivedDescriptionNode() {
    return get(__privateGet(this, _derivedDescriptionNode));
  }
  set derivedDescriptionNode(value) {
    set(__privateGet(this, _derivedDescriptionNode), value);
  }
  get value() {
    return get(__privateGet(this, _value));
  }
  set value(value) {
    set(__privateGet(this, _value), value);
  }
  get errorId() {
    return get(__privateGet(this, _errorId2));
  }
  set errorId(value) {
    set(__privateGet(this, _errorId2), value, true);
  }
  get descriptionId() {
    return get(__privateGet(this, _descriptionId2));
  }
  set descriptionId(value) {
    set(__privateGet(this, _descriptionId2), value, true);
  }
  get snippetProps() {
    return get(__privateGet(this, _snippetProps2));
  }
  set snippetProps(value) {
    set(__privateGet(this, _snippetProps2), value);
  }
};
_name2 = new WeakMap();
_formErrors2 = new WeakMap();
_formConstraints2 = new WeakMap();
_formTainted2 = new WeakMap();
_formData2 = new WeakMap();
_field = new WeakMap();
__name2 = new WeakMap();
_errors2 = new WeakMap();
_constraints2 = new WeakMap();
_tainted2 = new WeakMap();
_errorNode2 = new WeakMap();
_descriptionNode2 = new WeakMap();
_derivedDescriptionNode = new WeakMap();
_value = new WeakMap();
_errorId2 = new WeakMap();
_descriptionId2 = new WeakMap();
_snippetProps2 = new WeakMap();
var _ref, _id, _errorAttr, _snippetProps3, _fieldErrorsProps, _errorProps;
var FieldErrorsState = class {
  constructor(props, field) {
    __privateAdd(this, _ref);
    __privateAdd(this, _id);
    __publicField(this, "field");
    __privateAdd(this, _errorAttr, user_derived(() => getDataFsError(this.field.errors)));
    __privateAdd(this, _snippetProps3, user_derived(() => ({
      errors: this.field.errors,
      errorProps: this.errorProps
    })));
    __privateAdd(this, _fieldErrorsProps, user_derived(() => ({
      id: __privateGet(this, _id).current,
      "data-fs-error": get(__privateGet(this, _errorAttr)),
      "data-fs-field-errors": "",
      "aria-live": "assertive"
    })));
    __privateAdd(this, _errorProps, user_derived(() => ({
      "data-fs-field-error": "",
      "data-fs-error": get(__privateGet(this, _errorAttr))
    })));
    __privateSet(this, _ref, props.ref);
    __privateSet(this, _id, props.id);
    this.field = field;
    useRefById({
      id: __privateGet(this, _id),
      ref: __privateGet(this, _ref),
      onRefChange: (node) => {
        this.field.errorNode = node;
      }
    });
  }
  get snippetProps() {
    return get(__privateGet(this, _snippetProps3));
  }
  set snippetProps(value) {
    set(__privateGet(this, _snippetProps3), value);
  }
  get fieldErrorsProps() {
    return get(__privateGet(this, _fieldErrorsProps));
  }
  set fieldErrorsProps(value) {
    set(__privateGet(this, _fieldErrorsProps), value);
  }
  get errorProps() {
    return get(__privateGet(this, _errorProps));
  }
  set errorProps(value) {
    set(__privateGet(this, _errorProps), value);
  }
};
_ref = new WeakMap();
_id = new WeakMap();
_errorAttr = new WeakMap();
_snippetProps3 = new WeakMap();
_fieldErrorsProps = new WeakMap();
_errorProps = new WeakMap();
var _ref2, _id2, _props;
var DescriptionState = class {
  constructor(props, field) {
    __privateAdd(this, _ref2);
    __privateAdd(this, _id2);
    __publicField(this, "field");
    __privateAdd(this, _props, user_derived(() => ({
      id: __privateGet(this, _id2).current,
      "data-fs-error": getDataFsError(this.field.errors),
      "data-fs-description": ""
    })));
    __privateSet(this, _ref2, props.ref);
    __privateSet(this, _id2, props.id);
    this.field = field;
    useRefById({
      id: __privateGet(this, _id2),
      ref: __privateGet(this, _ref2),
      onRefChange: (node) => {
        this.field.descriptionNode = node;
      }
    });
  }
  get props() {
    return get(__privateGet(this, _props));
  }
  set props(value) {
    set(__privateGet(this, _props), value);
  }
};
_ref2 = new WeakMap();
_id2 = new WeakMap();
_props = new WeakMap();
var _id3, __id, _props2, _labelProps;
var ControlState = class {
  constructor(props, field) {
    __privateAdd(this, _id3);
    __publicField(this, "field");
    __publicField(this, "labelId", box(useId()));
    __privateAdd(this, __id, state(proxy(useId())));
    __privateAdd(this, _props2, user_derived(() => ({
      id: this.id,
      name: this.field.name,
      "data-fs-error": getDataFsError(this.field.errors),
      "aria-describedby": getAriaDescribedBy({
        fieldErrorsId: this.field.errorId,
        descriptionId: this.field.descriptionId,
        errors: this.field.errors
      }),
      "aria-invalid": getAriaInvalid(this.field.errors),
      "aria-required": getAriaRequired(this.field.constraints),
      "data-fs-control": ""
    })));
    __privateAdd(this, _labelProps, user_derived(() => ({
      id: this.labelId.current,
      "data-fs-label": "",
      "data-fs-error": getDataFsError(this.field.errors),
      for: this.id
    })));
    __privateSet(this, _id3, props.id);
    this.field = field;
    useOnChange(() => __privateGet(this, _id3).current, (v) => {
      this.id = v;
    });
  }
  get id() {
    return get(__privateGet(this, __id));
  }
  set id(value) {
    set(__privateGet(this, __id), value, true);
  }
  get props() {
    return get(__privateGet(this, _props2));
  }
  set props(value) {
    set(__privateGet(this, _props2), value);
  }
  get labelProps() {
    return get(__privateGet(this, _labelProps));
  }
  set labelProps(value) {
    set(__privateGet(this, _labelProps), value);
  }
};
_id3 = new WeakMap();
__id = new WeakMap();
_props2 = new WeakMap();
_labelProps = new WeakMap();
var _ref3, _id4;
var LabelState = class {
  constructor(props, control) {
    __privateAdd(this, _ref3);
    __privateAdd(this, _id4);
    __publicField(this, "control");
    __privateSet(this, _ref3, props.ref);
    __privateSet(this, _id4, props.id);
    this.control = control;
    this.control.labelId = __privateGet(this, _id4);
    useRefById({ id: __privateGet(this, _id4), ref: __privateGet(this, _ref3) });
  }
  get props() {
    return this.control.labelProps;
  }
};
_ref3 = new WeakMap();
_id4 = new WeakMap();
var _ref4, _id5, _props3;
var LegendState = class {
  constructor(props, field) {
    __privateAdd(this, _ref4);
    __privateAdd(this, _id5);
    __publicField(this, "field");
    __privateAdd(this, _props3, user_derived(() => ({
      id: __privateGet(this, _id5).current,
      "data-fs-error": getDataFsError(this.field.errors),
      "data-fs-legend": ""
    })));
    __privateSet(this, _ref4, props.ref);
    __privateSet(this, _id5, props.id);
    this.field = field;
    useRefById({ id: __privateGet(this, _id5), ref: __privateGet(this, _ref4) });
  }
  get props() {
    return get(__privateGet(this, _props3));
  }
  set props(value) {
    set(__privateGet(this, _props3), value);
  }
};
_ref4 = new WeakMap();
_id5 = new WeakMap();
_props3 = new WeakMap();
var FORM_FIELD_CTX = Symbol.for("formsnap.form-field");
var FORM_CONTROL_CTX = Symbol.for("formsnap.form-control");
function useField(props) {
  return setContext(FORM_FIELD_CTX, new FormFieldState(props));
}
function useElementField(props) {
  const formField = getField();
  return setContext(FORM_FIELD_CTX, new ElementFieldState(props, formField));
}
function getField() {
  return getContext(FORM_FIELD_CTX);
}
function useFieldErrors(props) {
  return new FieldErrorsState(props, getField());
}
function useDescription(props) {
  return new DescriptionState(props, getField());
}
function useControl(props) {
  return setContext(FORM_CONTROL_CTX, new ControlState(props, getField()));
}
function _getFormControl() {
  return getContext(FORM_CONTROL_CTX);
}
function useLabel(props) {
  return new LabelState(props, _getFormControl());
}
function useLegend(props) {
  return new LegendState(props, getField());
}
function splitArrayPath(name) {
  const [path, index2] = name.split(/[[\]]/);
  return [path, index2];
}
function useFormField(props) {
  const fieldState = getContext(FORM_FIELD_CTX);
  const form = fieldState.form;
  const errorsId = user_derived(() => props.errorsId ? props.errorsId() : void 0);
  const descriptionId = user_derived(() => props.descriptionId ? props.descriptionId() : void 0);
  useOnChange(() => get(errorsId), (v) => {
    if (v) {
      fieldState.errorId = v;
    }
  });
  useOnChange(() => get(descriptionId), (v) => {
    if (v) {
      fieldState.descriptionId = v;
    }
  });
  return {
    form,
    get name() {
      return fieldState.name;
    },
    get errors() {
      return fieldState.errors;
    },
    get constraints() {
      return fieldState.constraints;
    },
    get tainted() {
      return fieldState.tainted;
    },
    get errorsId() {
      return fieldState.errorId;
    },
    get descriptionId() {
      return fieldState.descriptionId;
    }
  };
}
function useFormControl(props) {
  const controlState = getContext(FORM_CONTROL_CTX);
  const id = user_derived(() => props.id ? props.id() : void 0);
  useOnChange(() => get(id), (v) => {
    if (v) {
      controlState.id = v;
    }
  });
  return {
    get id() {
      return controlState.id;
    },
    get labelProps() {
      return controlState.labelProps;
    },
    get props() {
      return controlState.props;
    }
  };
}
var getFormControl = useFormControl;
var getFormField = useFormField;

// node_modules/formsnap/dist/components/description.svelte
Description[FILENAME] = "node_modules/formsnap/dist/components/description.svelte";
var root_2 = add_locations(template(`<div><!></div>`), Description[FILENAME], [[37, 1]]);
function Description($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Description);
  let id = prop($$props, "id", 19, useId), ref = prop($$props, "ref", 15, null), restProps = rest_props(
    $$props,
    [
      "$$slots",
      "$$events",
      "$$legacy",
      "id",
      "ref",
      "children",
      "child"
    ],
    "restProps"
  );
  const descriptionState = useDescription({
    id: box.with(() => id()),
    ref: box.with(() => ref(), (v) => ref(v))
  });
  const mergedProps = user_derived(() => mergeProps(restProps, descriptionState.props));
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      snippet(node_1, () => $$props.child, () => ({ props: get(mergedProps) }));
      append($$anchor2, fragment_1);
    };
    var alternate = ($$anchor2) => {
      var div = root_2();
      let attributes;
      var node_2 = child(div);
      snippet(node_2, () => $$props.children ?? noop);
      reset(div);
      template_effect(() => attributes = set_attributes(div, attributes, { ...get(mergedProps) }));
      append($$anchor2, div);
    };
    if_block(node, ($$render) => {
      if ($$props.child) $$render(consequent);
      else $$render(alternate, false);
    });
  }
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Description = hmr(Description, () => Description[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Description[HMR].source;
    set(Description[HMR].source, module.default[HMR].original);
  });
}
var description_default = Description;

// node_modules/formsnap/dist/components/field.svelte
Field[FILENAME] = "node_modules/formsnap/dist/components/field.svelte";
function Field($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Field);
  const fieldState = useField({
    form: box.with(() => $$props.form),
    name: box.with(() => $$props.name)
  });
  var fragment = comment();
  var node = first_child(fragment);
  snippet(node, () => $$props.children ?? noop, () => fieldState.snippetProps);
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Field = hmr(Field, () => Field[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Field[HMR].source;
    set(Field[HMR].source, module.default[HMR].original);
  });
}
var field_default = Field;

// node_modules/formsnap/dist/components/control.svelte
Control[FILENAME] = "node_modules/formsnap/dist/components/control.svelte";
function Control($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Control);
  let id = prop($$props, "id", 19, useId);
  const controlState = useControl({ id: box.with(() => id()) });
  var fragment = comment();
  var node = first_child(fragment);
  snippet(node, () => $$props.children ?? noop, () => ({ props: controlState.props }));
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Control = hmr(Control, () => Control[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Control[HMR].source;
    set(Control[HMR].source, module.default[HMR].original);
  });
}
var control_default = Control;

// node_modules/formsnap/dist/components/label.svelte
Label[FILENAME] = "node_modules/formsnap/dist/components/label.svelte";
var root_22 = add_locations(template(`<label><!></label>`), Label[FILENAME], [[51, 1]]);
function Label($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Label);
  let id = prop($$props, "id", 19, useId), ref = prop($$props, "ref", 15, null), restProps = rest_props(
    $$props,
    [
      "$$slots",
      "$$events",
      "$$legacy",
      "id",
      "ref",
      "children",
      "child"
    ],
    "restProps"
  );
  const labelState = useLabel({
    id: box.with(() => id()),
    ref: box.with(() => ref(), (v) => ref(v))
  });
  const mergedProps = user_derived(() => mergeProps(restProps, labelState.props));
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      snippet(node_1, () => $$props.child, () => ({ props: get(mergedProps) }));
      append($$anchor2, fragment_1);
    };
    var alternate = ($$anchor2) => {
      var label = root_22();
      let attributes;
      var node_2 = child(label);
      snippet(node_2, () => $$props.children ?? noop);
      reset(label);
      template_effect(() => attributes = set_attributes(label, attributes, { ...get(mergedProps) }));
      append($$anchor2, label);
    };
    if_block(node, ($$render) => {
      if ($$props.child) $$render(consequent);
      else $$render(alternate, false);
    });
  }
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Label = hmr(Label, () => Label[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Label[HMR].source;
    set(Label[HMR].source, module.default[HMR].original);
  });
}
var label_default = Label;

// node_modules/formsnap/dist/components/field-errors.svelte
Field_errors[FILENAME] = "node_modules/formsnap/dist/components/field-errors.svelte";
var root_5 = add_locations(template(`<div> </div>`), Field_errors[FILENAME], [[51, 4]]);
var root_23 = add_locations(template(`<div><!></div>`), Field_errors[FILENAME], [[46, 1]]);
function Field_errors($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Field_errors);
  let id = prop($$props, "id", 19, useId), ref = prop($$props, "ref", 15, null), restProps = rest_props(
    $$props,
    [
      "$$slots",
      "$$events",
      "$$legacy",
      "id",
      "ref",
      "children",
      "child"
    ],
    "restProps"
  );
  const fieldErrorsState = useFieldErrors({
    id: box.with(() => id()),
    ref: box.with(() => ref(), (v) => ref(v))
  });
  const mergedProps = user_derived(() => mergeProps(restProps, fieldErrorsState.fieldErrorsProps));
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      var render_arg = derived_safe_equal(() => ({
        props: get(mergedProps),
        ...fieldErrorsState.snippetProps
      }));
      snippet(node_1, () => $$props.child, () => get(render_arg));
      append($$anchor2, fragment_1);
    };
    var alternate = ($$anchor2) => {
      var div = root_23();
      let attributes;
      var node_2 = child(div);
      {
        var consequent_1 = ($$anchor3) => {
          var fragment_2 = comment();
          var node_3 = first_child(fragment_2);
          snippet(node_3, () => $$props.children, () => fieldErrorsState.snippetProps);
          append($$anchor3, fragment_2);
        };
        var alternate_1 = ($$anchor3) => {
          var fragment_3 = comment();
          var node_4 = first_child(fragment_3);
          each(node_4, 17, () => fieldErrorsState.field.errors, index, ($$anchor4, error) => {
            var div_1 = root_5();
            let attributes_1;
            var text = child(div_1, true);
            reset(div_1);
            template_effect(() => {
              attributes_1 = set_attributes(div_1, attributes_1, { ...fieldErrorsState.errorProps });
              set_text(text, get(error));
            });
            append($$anchor4, div_1);
          });
          append($$anchor3, fragment_3);
        };
        if_block(node_2, ($$render) => {
          if ($$props.children) $$render(consequent_1);
          else $$render(alternate_1, false);
        });
      }
      reset(div);
      template_effect(() => attributes = set_attributes(div, attributes, { ...get(mergedProps) }));
      append($$anchor2, div);
    };
    if_block(node, ($$render) => {
      if ($$props.child) $$render(consequent);
      else $$render(alternate, false);
    });
  }
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Field_errors = hmr(Field_errors, () => Field_errors[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Field_errors[HMR].source;
    set(Field_errors[HMR].source, module.default[HMR].original);
  });
}
var field_errors_default = Field_errors;

// node_modules/formsnap/dist/components/fieldset.svelte
Fieldset[FILENAME] = "node_modules/formsnap/dist/components/fieldset.svelte";
var root_3 = add_locations(template(`<fieldset><!></fieldset>`), Fieldset[FILENAME], [[69, 3]]);
function Fieldset($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Fieldset);
  let id = prop($$props, "id", 19, useId), ref = prop($$props, "ref", 15, null), restProps = rest_props(
    $$props,
    [
      "$$slots",
      "$$events",
      "$$legacy",
      "id",
      "ref",
      "form",
      "name",
      "child",
      "children"
    ],
    "restProps"
  );
  useRefById({
    id: box.with(() => id()),
    ref: box.with(() => ref(), (v) => ref(v))
  });
  const mergedProps = user_derived(() => mergeProps(restProps, { id: id(), "data-fs-fieldset": "" }));
  var fragment = comment();
  var node = first_child(fragment);
  {
    const children = wrap_snippet(Fieldset, ($$anchor2, $$arg0) => {
      let value = () => $$arg0 == null ? void 0 : $$arg0().value;
      value();
      let errors = () => $$arg0 == null ? void 0 : $$arg0().errors;
      errors();
      let tainted = () => $$arg0 == null ? void 0 : $$arg0().tainted;
      tainted();
      let constraints = () => $$arg0 == null ? void 0 : $$arg0().constraints;
      constraints();
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      {
        var consequent = ($$anchor3) => {
          var fragment_2 = comment();
          var node_2 = first_child(fragment_2);
          snippet(node_2, () => $$props.child, () => ({
            props: get(mergedProps),
            value: value(),
            errors: errors(),
            tainted: tainted(),
            constraints: constraints()
          }));
          append($$anchor3, fragment_2);
        };
        var alternate = ($$anchor3) => {
          var fieldset = root_3();
          let attributes;
          var node_3 = child(fieldset);
          snippet(node_3, () => $$props.children ?? noop, () => ({
            value: value(),
            errors: errors(),
            tainted: tainted(),
            constraints: constraints()
          }));
          reset(fieldset);
          template_effect(
            ($0) => attributes = set_attributes(fieldset, attributes, {
              ...get(mergedProps),
              "data-fs-error": $0
            }),
            [() => getDataFsError(errors())]
          );
          append($$anchor3, fieldset);
        };
        if_block(node_1, ($$render) => {
          if ($$props.child) $$render(consequent);
          else $$render(alternate, false);
        });
      }
      append($$anchor2, fragment_1);
    });
    field_default(node, {
      get form() {
        return $$props.form;
      },
      get name() {
        return $$props.name;
      },
      children,
      $$slots: { default: true }
    });
  }
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Fieldset = hmr(Fieldset, () => Fieldset[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Fieldset[HMR].source;
    set(Fieldset[HMR].source, module.default[HMR].original);
  });
}
var fieldset_default = Fieldset;

// node_modules/formsnap/dist/components/legend.svelte
Legend[FILENAME] = "node_modules/formsnap/dist/components/legend.svelte";
var root_24 = add_locations(template(`<legend><!></legend>`), Legend[FILENAME], [[40, 1]]);
function Legend($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Legend);
  let id = prop($$props, "id", 19, useId), ref = prop($$props, "ref", 15, null), restProps = rest_props(
    $$props,
    [
      "$$slots",
      "$$events",
      "$$legacy",
      "id",
      "ref",
      "children",
      "child"
    ],
    "restProps"
  );
  const legendState = useLegend({
    id: box.with(() => id()),
    ref: box.with(() => ref(), (v) => ref(v))
  });
  const mergedProps = user_derived(() => mergeProps(restProps, legendState.props));
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      snippet(node_1, () => $$props.child, () => ({ props: get(mergedProps) }));
      append($$anchor2, fragment_1);
    };
    var alternate = ($$anchor2) => {
      var legend = root_24();
      let attributes;
      var node_2 = child(legend);
      snippet(node_2, () => $$props.children ?? noop);
      reset(legend);
      template_effect(() => attributes = set_attributes(legend, attributes, { ...get(mergedProps) }));
      append($$anchor2, legend);
    };
    if_block(node, ($$render) => {
      if ($$props.child) $$render(consequent);
      else $$render(alternate, false);
    });
  }
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Legend = hmr(Legend, () => Legend[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Legend[HMR].source;
    set(Legend[HMR].source, module.default[HMR].original);
  });
}
var legend_default = Legend;

// node_modules/formsnap/dist/components/element-field.svelte
Element_field[FILENAME] = "node_modules/formsnap/dist/components/element-field.svelte";
function Element_field($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Element_field);
  const elementFieldState = useElementField({
    form: box.with(() => $$props.form),
    name: box.with(() => $$props.name)
  });
  var fragment = comment();
  var node = first_child(fragment);
  snippet(node, () => $$props.children ?? noop, () => elementFieldState.snippetProps);
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Element_field = hmr(Element_field, () => Element_field[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Element_field[HMR].source;
    set(Element_field[HMR].source, module.default[HMR].original);
  });
}
var element_field_default = Element_field;
export {
  control_default as Control,
  description_default as Description,
  element_field_default as ElementField,
  field_default as Field,
  field_errors_default as FieldErrors,
  fieldset_default as Fieldset,
  label_default as Label,
  legend_default as Legend,
  getFormControl,
  getFormField,
  useFormControl,
  useFormField
};
//# sourceMappingURL=formsnap.js.map
