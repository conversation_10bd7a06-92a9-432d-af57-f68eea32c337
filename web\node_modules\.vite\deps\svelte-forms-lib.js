import "./chunk-RIXFT5AQ.js";
import "./chunk-C5KNTEDU.js";
import "./chunk-5IRPM5PB.js";
import "./chunk-MZKCMDML.js";
import {
  add_locations,
  check_target,
  derived,
  get,
  hmr,
  if_block,
  init,
  init_select,
  legacy_api,
  legacy_rest_props,
  prop,
  remove_input_defaults,
  select_option,
  set_attributes,
  setup_stores,
  slot,
  store_get,
  validate_store,
  writable
} from "./chunk-CRCQ7E27.js";
import "./chunk-U7P2NEEE.js";
import {
  append,
  comment,
  set_text,
  template
} from "./chunk-OSNF6FE7.js";
import {
  FILENAME,
  HMR,
  apply,
  child,
  event,
  first_child,
  getContext,
  pop,
  push,
  remove_textarea_child,
  reset,
  set,
  setContext,
  template_effect
} from "./chunk-PJ2X7CWE.js";
import "./chunk-VIZMNZTH.js";
import "./chunk-HNWPC2PS.js";
import "./chunk-RVAV4ZRS.js";
import "./chunk-KWPVD4H7.js";

// node_modules/svelte-forms-lib/lib/components/key.js
var key = {};

// node_modules/dequal/lite/index.mjs
var has = Object.prototype.hasOwnProperty;
function dequal(foo, bar) {
  var ctor, len;
  if (foo === bar) return true;
  if (foo && bar && (ctor = foo.constructor) === bar.constructor) {
    if (ctor === Date) return foo.getTime() === bar.getTime();
    if (ctor === RegExp) return foo.toString() === bar.toString();
    if (ctor === Array) {
      if ((len = foo.length) === bar.length) {
        while (len-- && dequal(foo[len], bar[len])) ;
      }
      return len === -1;
    }
    if (!ctor || typeof foo === "object") {
      len = 0;
      for (ctor in foo) {
        if (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;
        if (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;
      }
      return Object.keys(bar).length === len;
    }
  }
  return foo !== foo && bar !== bar;
}

// node_modules/svelte-forms-lib/lib/util.js
function subscribeOnce(observable) {
  return new Promise((resolve) => {
    observable.subscribe(resolve)();
  });
}
function update(object, path, value) {
  object.update((o) => {
    set2(o, path, value);
    return o;
  });
}
function cloneDeep(object) {
  return JSON.parse(JSON.stringify(object));
}
function isNullish(value) {
  return value === void 0 || value === null;
}
function isEmpty(object) {
  return isNullish(object) || Object.keys(object).length <= 0;
}
function getValues(object) {
  let results = [];
  for (const [, value] of Object.entries(object)) {
    const values = typeof value === "object" ? getValues(value) : [value];
    results = [...results, ...values];
  }
  return results;
}
function getErrorsFromSchema(initialValues, schema, errors = {}) {
  for (const key2 in schema) {
    switch (true) {
      case (schema[key2].type === "object" && !isEmpty(schema[key2].fields)): {
        errors[key2] = getErrorsFromSchema(
          initialValues[key2],
          schema[key2].fields,
          { ...errors[key2] }
        );
        break;
      }
      case schema[key2].type === "array": {
        const values = initialValues && initialValues[key2] ? initialValues[key2] : [];
        errors[key2] = values.map((value) => {
          const innerError = getErrorsFromSchema(
            value,
            schema[key2].innerType.fields,
            { ...errors[key2] }
          );
          return Object.keys(innerError).length > 0 ? innerError : "";
        });
        break;
      }
      default: {
        errors[key2] = "";
      }
    }
  }
  return errors;
}
var deepEqual = dequal;
function assignDeep(object, value) {
  if (Array.isArray(object)) {
    return object.map((o) => assignDeep(o, value));
  }
  const copy = {};
  for (const key2 in object) {
    copy[key2] = typeof object[key2] === "object" && !isNullish(object[key2]) ? assignDeep(object[key2], value) : value;
  }
  return copy;
}
function set2(object, path, value) {
  if (new Object(object) !== object) return object;
  if (!Array.isArray(path)) {
    path = path.toString().match(/[^.[\]]+/g) || [];
  }
  const result = path.slice(0, -1).reduce(
    (accumulator, key2, index) => new Object(accumulator[key2]) === accumulator[key2] ? accumulator[key2] : accumulator[key2] = Math.trunc(Math.abs(path[index + 1])) === +path[index + 1] ? [] : {},
    object
  );
  result[path[path.length - 1]] = value;
  return object;
}
var util = {
  assignDeep,
  cloneDeep,
  deepEqual,
  getErrorsFromSchema,
  getValues,
  isEmpty,
  isNullish,
  set: set2,
  subscribeOnce,
  update
};

// node_modules/svelte-forms-lib/lib/create-form.js
var NO_ERROR = "";
var IS_TOUCHED = true;
function isCheckbox(element) {
  return element.getAttribute && element.getAttribute("type") === "checkbox";
}
function isFileInput(element) {
  return element.getAttribute && element.getAttribute("type") === "file";
}
function resolveValue(element) {
  if (isFileInput(element)) {
    return element.files;
  } else if (isCheckbox(element)) {
    return element.checked;
  } else {
    return element.value;
  }
}
var createForm = (config) => {
  let initialValues = config.initialValues || {};
  const validationSchema = config.validationSchema;
  const validateFunction = config.validate;
  const onSubmit = config.onSubmit;
  const getInitial = {
    values: () => util.cloneDeep(initialValues),
    errors: () => validationSchema ? util.getErrorsFromSchema(initialValues, validationSchema.fields) : util.assignDeep(initialValues, NO_ERROR),
    touched: () => util.assignDeep(initialValues, !IS_TOUCHED)
  };
  const form = writable(getInitial.values());
  const errors = writable(getInitial.errors());
  const touched = writable(getInitial.touched());
  const isSubmitting = writable(false);
  const isValidating = writable(false);
  const isValid = derived(errors, ($errors) => {
    const noErrors = util.getValues($errors).every((field) => field === NO_ERROR);
    return noErrors;
  });
  const modified = derived(form, ($form) => {
    const object = util.assignDeep($form, false);
    for (let key2 in $form) {
      object[key2] = !util.deepEqual($form[key2], initialValues[key2]);
    }
    return object;
  });
  const isModified = derived(modified, ($modified) => {
    return util.getValues($modified).includes(true);
  });
  function validateField(field) {
    return util.subscribeOnce(form).then((values) => validateFieldValue(field, values[field]));
  }
  function validateFieldValue(field, value) {
    updateTouched(field, true);
    if (validationSchema) {
      isValidating.set(true);
      return validationSchema.validateAt(field, get(form)).then(() => util.update(errors, field, "")).catch((error) => util.update(errors, field, error.message)).finally(() => {
        isValidating.set(false);
      });
    }
    if (validateFunction) {
      isValidating.set(true);
      return Promise.resolve().then(() => validateFunction({ [field]: value })).then(
        (errs) => util.update(errors, field, !util.isNullish(errs) ? errs[field] : "")
      ).finally(() => {
        isValidating.set(false);
      });
    }
    return Promise.resolve();
  }
  function updateValidateField(field, value) {
    updateField(field, value);
    return validateFieldValue(field, value);
  }
  function handleChange(event2) {
    const element = event2.target;
    const field = element.name || element.id;
    const value = resolveValue(element);
    return updateValidateField(field, value);
  }
  function handleSubmit(event2) {
    if (event2 && event2.preventDefault) {
      event2.preventDefault();
    }
    isSubmitting.set(true);
    return util.subscribeOnce(form).then((values) => {
      if (typeof validateFunction === "function") {
        isValidating.set(true);
        return Promise.resolve().then(() => validateFunction(values)).then((error) => {
          if (util.isNullish(error) || util.getValues(error).length === 0) {
            return clearErrorsAndSubmit(values);
          } else {
            errors.set(error);
            isSubmitting.set(false);
          }
        }).finally(() => isValidating.set(false));
      }
      if (validationSchema) {
        isValidating.set(true);
        return validationSchema.validate(values, { abortEarly: false }).then(() => clearErrorsAndSubmit(values)).catch((yupErrors) => {
          if (yupErrors && yupErrors.inner) {
            const updatedErrors = getInitial.errors();
            yupErrors.inner.map(
              (error) => util.set(updatedErrors, error.path, error.message)
            );
            errors.set(updatedErrors);
          }
          isSubmitting.set(false);
        }).finally(() => isValidating.set(false));
      }
      return clearErrorsAndSubmit(values);
    });
  }
  function handleReset() {
    form.set(getInitial.values());
    errors.set(getInitial.errors());
    touched.set(getInitial.touched());
  }
  function clearErrorsAndSubmit(values) {
    return Promise.resolve().then(() => errors.set(getInitial.errors())).then(() => onSubmit(values, form, errors)).finally(() => isSubmitting.set(false));
  }
  function updateField(field, value) {
    util.update(form, field, value);
  }
  function updateTouched(field, value) {
    util.update(touched, field, value);
  }
  function updateInitialValues(newValues) {
    initialValues = newValues;
    handleReset();
  }
  return {
    form,
    errors,
    touched,
    modified,
    isValid,
    isSubmitting,
    isValidating,
    isModified,
    handleChange,
    handleSubmit,
    handleReset,
    updateField,
    updateValidateField,
    updateTouched,
    validateField,
    updateInitialValues,
    state: derived(
      [
        form,
        errors,
        touched,
        modified,
        isValid,
        isValidating,
        isSubmitting,
        isModified
      ],
      ([
        $form,
        $errors,
        $touched,
        $modified,
        $isValid,
        $isValidating,
        $isSubmitting,
        $isModified
      ]) => ({
        form: $form,
        errors: $errors,
        touched: $touched,
        modified: $modified,
        isValid: $isValid,
        isSubmitting: $isSubmitting,
        isValidating: $isValidating,
        isModified: $isModified
      })
    )
  };
};

// node_modules/svelte-forms-lib/lib/components/Form.svelte
Form[FILENAME] = "node_modules/svelte-forms-lib/lib/components/Form.svelte";
var root = add_locations(template(`<form><!></form>`), Form[FILENAME], [[50, 0]]);
function Form($$anchor, $$props) {
  check_target(new.target);
  const $$sanitized_props = legacy_rest_props($$props, [
    "children",
    "$$slots",
    "$$events",
    "$$legacy"
  ]);
  const $$restProps = legacy_rest_props($$sanitized_props, [
    "initialValues",
    "validate",
    "validationSchema",
    "onSubmit",
    "context"
  ]);
  push($$props, false, Form);
  let initialValues = prop($$props, "initialValues", 24, () => ({}));
  let validate = prop($$props, "validate", 8, null);
  let validationSchema = prop($$props, "validationSchema", 8, null);
  let onSubmit = prop($$props, "onSubmit", 8, () => {
    throw new Error("onSubmit is a required property in <Form /> when using the fallback context");
  });
  let context = prop($$props, "context", 24, () => createForm({
    initialValues: initialValues(),
    onSubmit: onSubmit(),
    validate: validate(),
    validationSchema: validationSchema()
  }));
  const {
    form,
    errors,
    touched,
    state,
    handleChange,
    handleSubmit,
    updateField,
    updateInitialValues,
    updateTouched,
    updateValidateField,
    validateField
  } = context();
  setContext(key, {
    form,
    errors,
    touched,
    state,
    handleChange,
    handleSubmit,
    updateField,
    updateInitialValues,
    updateTouched,
    updateValidateField,
    validateField
  });
  init();
  var form_1 = root();
  let attributes;
  var node = child(form_1);
  slot(
    node,
    $$props,
    "default",
    {
      form,
      errors,
      touched,
      state,
      handleChange,
      handleSubmit,
      updateField,
      updateInitialValues,
      updateTouched,
      updateValidateField,
      validateField
    },
    null
  );
  reset(form_1);
  template_effect(() => attributes = set_attributes(form_1, attributes, { ...$$restProps }));
  event("submit", form_1, function(...$$args) {
    apply(() => handleSubmit, this, $$args, Form, [50, 17]);
  });
  append($$anchor, form_1);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Form = hmr(Form, () => Form[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Form[HMR].source;
    set(Form[HMR].source, module.default[HMR].original);
  });
}
var Form_default = Form;

// node_modules/svelte-forms-lib/lib/components/Textarea.svelte
Textarea[FILENAME] = "node_modules/svelte-forms-lib/lib/components/Textarea.svelte";
var root2 = add_locations(template(`<textarea></textarea>`), Textarea[FILENAME], [[11, 0]]);
function Textarea($$anchor, $$props) {
  check_target(new.target);
  const $$sanitized_props = legacy_rest_props($$props, [
    "children",
    "$$slots",
    "$$events",
    "$$legacy"
  ]);
  push($$props, false, Textarea);
  const [$$stores, $$cleanup] = setup_stores();
  const $form = () => (validate_store(form, "form"), store_get(form, "$form", $$stores));
  let name = prop($$props, "name", 8);
  const { form, handleChange } = getContext(key);
  init();
  var textarea = root2();
  remove_textarea_child(textarea);
  let attributes;
  template_effect(() => attributes = set_attributes(textarea, attributes, {
    name: name(),
    ...$$sanitized_props,
    value: $form()[name()]
  }));
  event("change", textarea, function(...$$args) {
    apply(() => handleChange, this, $$args, Textarea, [11, 28]);
  });
  event("blur", textarea, function(...$$args) {
    apply(() => handleChange, this, $$args, Textarea, [11, 51]);
  });
  append($$anchor, textarea);
  var $$pop = pop({ ...legacy_api() });
  $$cleanup();
  return $$pop;
}
if (import.meta.hot) {
  Textarea = hmr(Textarea, () => Textarea[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Textarea[HMR].source;
    set(Textarea[HMR].source, module.default[HMR].original);
  });
}
var Textarea_default = Textarea;

// node_modules/svelte-forms-lib/lib/components/Field.svelte
Field[FILENAME] = "node_modules/svelte-forms-lib/lib/components/Field.svelte";
var root3 = add_locations(template(`<input>`), Field[FILENAME], [[11, 0]]);
function Field($$anchor, $$props) {
  check_target(new.target);
  const $$sanitized_props = legacy_rest_props($$props, [
    "children",
    "$$slots",
    "$$events",
    "$$legacy"
  ]);
  push($$props, false, Field);
  const [$$stores, $$cleanup] = setup_stores();
  const $form = () => (validate_store(form, "form"), store_get(form, "$form", $$stores));
  let name = prop($$props, "name", 8);
  let type = prop($$props, "type", 8, "text");
  const { form, handleChange } = getContext(key);
  init();
  var input = root3();
  remove_input_defaults(input);
  let attributes;
  template_effect(() => attributes = set_attributes(input, attributes, {
    name: name(),
    type: type(),
    value: $form()[name()],
    ...$$sanitized_props
  }));
  event("change", input, function(...$$args) {
    apply(() => handleChange, this, $$args, Field, [15, 13]);
  });
  event("blur", input, function(...$$args) {
    apply(() => handleChange, this, $$args, Field, [16, 11]);
  });
  append($$anchor, input);
  var $$pop = pop({ ...legacy_api() });
  $$cleanup();
  return $$pop;
}
if (import.meta.hot) {
  Field = hmr(Field, () => Field[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Field[HMR].source;
    set(Field[HMR].source, module.default[HMR].original);
  });
}
var Field_default = Field;

// node_modules/svelte-forms-lib/lib/components/Select.svelte
Select[FILENAME] = "node_modules/svelte-forms-lib/lib/components/Select.svelte";
var root4 = add_locations(template(`<select><!></select>`), Select[FILENAME], [[10, 0]]);
function Select($$anchor, $$props) {
  check_target(new.target);
  const $$sanitized_props = legacy_rest_props($$props, [
    "children",
    "$$slots",
    "$$events",
    "$$legacy"
  ]);
  push($$props, false, Select);
  const [$$stores, $$cleanup] = setup_stores();
  const $form = () => (validate_store(form, "form"), store_get(form, "$form", $$stores));
  let name = prop($$props, "name", 8);
  const { form, handleChange } = getContext(key);
  init();
  var select = root4();
  let attributes;
  init_select(select, () => attributes.value);
  var node = child(select);
  slot(node, $$props, "default", {}, null);
  reset(select);
  template_effect(() => {
    attributes = set_attributes(select, attributes, {
      name: name(),
      value: $form()[name()],
      ...$$sanitized_props
    });
    if ("value" in attributes) {
      select_option(select, attributes.value);
    }
  });
  event("change", select, function(...$$args) {
    apply(() => handleChange, this, $$args, Select, [13, 13]);
  });
  event("blur", select, function(...$$args) {
    apply(() => handleChange, this, $$args, Select, [14, 11]);
  });
  append($$anchor, select);
  var $$pop = pop({ ...legacy_api() });
  $$cleanup();
  return $$pop;
}
if (import.meta.hot) {
  Select = hmr(Select, () => Select[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Select[HMR].source;
    set(Select[HMR].source, module.default[HMR].original);
  });
}
var Select_default = Select;

// node_modules/svelte-forms-lib/lib/components/ErrorMessage.svelte
ErrorMessage[FILENAME] = "node_modules/svelte-forms-lib/lib/components/ErrorMessage.svelte";
var root_1 = add_locations(template(`<small> </small>`), ErrorMessage[FILENAME], [[11, 2]]);
function ErrorMessage($$anchor, $$props) {
  check_target(new.target);
  const $$sanitized_props = legacy_rest_props($$props, [
    "children",
    "$$slots",
    "$$events",
    "$$legacy"
  ]);
  push($$props, false, ErrorMessage);
  const [$$stores, $$cleanup] = setup_stores();
  const $errors = () => (validate_store(errors, "errors"), store_get(errors, "$errors", $$stores));
  let name = prop($$props, "name", 8);
  const { errors } = getContext(key);
  init();
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var small = root_1();
      let attributes;
      var text = child(small, true);
      reset(small);
      template_effect(() => {
        attributes = set_attributes(small, attributes, { ...$$sanitized_props });
        set_text(text, $errors()[name()]);
      });
      append($$anchor2, small);
    };
    if_block(node, ($$render) => {
      if ($errors()[name()]) $$render(consequent);
    });
  }
  append($$anchor, fragment);
  var $$pop = pop({ ...legacy_api() });
  $$cleanup();
  return $$pop;
}
if (import.meta.hot) {
  ErrorMessage = hmr(ErrorMessage, () => ErrorMessage[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = ErrorMessage[HMR].source;
    set(ErrorMessage[HMR].source, module.default[HMR].original);
  });
}
var ErrorMessage_default = ErrorMessage;
export {
  ErrorMessage_default as ErrorMessage,
  Field_default as Field,
  Form_default as Form,
  Select_default as Select,
  Textarea_default as Textarea,
  createForm,
  key
};
//# sourceMappingURL=svelte-forms-lib.js.map
