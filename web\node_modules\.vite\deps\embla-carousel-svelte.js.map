{"version": 3, "sources": ["../../embla-carousel-svelte/src/components/emblaCarouselSvelte.ts"], "sourcesContent": ["import { ActionReturn } from 'svelte/action'\nimport {\n  areOptionsEqual,\n  arePluginsEqual,\n  canUseDOM\n} from 'embla-carousel-reactive-utils'\nimport EmblaCarousel, {\n  EmblaCarouselType,\n  EmblaOptionsType,\n  EmblaPluginType\n} from 'embla-carousel'\n\ntype EmblaCarouselParameterType = {\n  options: EmblaOptionsType\n  plugins: EmblaPluginType[]\n}\n\ntype EmblaCarouselAttributesType = {\n  'on:emblaInit'?: (evt: CustomEvent<EmblaCarouselType>) => void\n  onemblaInit?: (evt: CustomEvent<EmblaCarouselType>) => void\n}\n\nexport type EmblaCarouselSvelteType = ActionReturn<\n  EmblaCarouselParameterType,\n  EmblaCarouselAttributesType\n>\n\nfunction emblaCarouselSvelte(\n  emblaNode: HTMLElement,\n  emblaConfig: EmblaCarouselParameterType = { options: {}, plugins: [] }\n): EmblaCarouselSvelteType {\n  let storedEmblaConfig = emblaConfig\n  let emblaApi: EmblaCarouselType\n\n  if (canUseDOM()) {\n    EmblaCarousel.globalOptions = emblaCarouselSvelte.globalOptions\n    emblaApi = EmblaCarousel(\n      emblaNode,\n      storedEmblaConfig.options,\n      storedEmblaConfig.plugins\n    )\n    emblaApi.on('init', () =>\n      emblaNode.dispatchEvent(\n        new CustomEvent('emblaInit', { detail: emblaApi })\n      )\n    )\n  }\n\n  return {\n    destroy: () => {\n      if (emblaApi) emblaApi.destroy()\n    },\n    update: (newEmblaConfig) => {\n      const optionsChanged = !areOptionsEqual(\n        storedEmblaConfig.options,\n        newEmblaConfig.options\n      )\n      const pluginsChanged = !arePluginsEqual(\n        storedEmblaConfig.plugins,\n        newEmblaConfig.plugins\n      )\n\n      if (!optionsChanged && !pluginsChanged) return\n      storedEmblaConfig = newEmblaConfig\n\n      if (emblaApi) {\n        emblaApi.reInit(storedEmblaConfig.options, storedEmblaConfig.plugins)\n      }\n    }\n  }\n}\n\ndeclare namespace emblaCarouselSvelte {\n  let globalOptions: EmblaOptionsType | undefined\n}\n\nemblaCarouselSvelte.globalOptions = undefined\n\nexport default emblaCarouselSvelte\n"], "mappings": ";;;;;;;;;;;AA2BA,SAASA,oBACPC,WACAC,cAA0C;EAAEC,SAAS,CAAA;EAAIC,SAAS,CAAA;AAAI,GAAA;AAEtE,MAAIC,oBAAoBH;AACxB,MAAII;AAEJ,MAAIC,UAAS,GAAI;AACfC,kBAAcC,gBAAgBT,oBAAoBS;AAClDH,eAAWE,cACTP,WACAI,kBAAkBF,SAClBE,kBAAkBD,OAAO;AAE3BE,aAASI,GAAG,QAAQ,MAClBT,UAAUU,cACR,IAAIC,YAAY,aAAa;MAAEC,QAAQP;KAAU,CAAC,CACnD;EAEL;AAEA,SAAO;IACLQ,SAASA,MAAK;AACZ,UAAIR,SAAUA,UAASQ,QAAO;;IAEhCC,QAASC,oBAAkB;AACzB,YAAMC,iBAAiB,CAACC,gBACtBb,kBAAkBF,SAClBa,eAAeb,OAAO;AAExB,YAAMgB,iBAAiB,CAACC,gBACtBf,kBAAkBD,SAClBY,eAAeZ,OAAO;AAGxB,UAAI,CAACa,kBAAkB,CAACE,eAAgB;AACxCd,0BAAoBW;AAEpB,UAAIV,UAAU;AACZA,iBAASe,OAAOhB,kBAAkBF,SAASE,kBAAkBD,OAAO;MACtE;IACF;;AAEJ;AAMAJ,oBAAoBS,gBAAgBa;", "names": ["emblaCarouselSvelte", "emblaNode", "emblaConfig", "options", "plugins", "storedEmblaConfig", "emblaApi", "canUseDOM", "EmblaCarousel", "globalOptions", "on", "dispatchEvent", "CustomEvent", "detail", "destroy", "update", "newEmblaConfig", "optionsChanged", "areOptionsEqual", "pluginsChanged", "arePluginsEqual", "reInit", "undefined"]}