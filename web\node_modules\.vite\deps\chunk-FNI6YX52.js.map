{"version": 3, "sources": ["../../d3-interpolate-path/build/d3-interpolate-path.js"], "sourcesContent": ["(function (global, factory) {\ntypeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\ntypeof define === 'function' && define.amd ? define(['exports'], factory) :\n(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.d3 = global.d3 || {}));\n}(this, (function (exports) { 'use strict';\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _createForOfIteratorHelper(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n\n  if (!it) {\n    if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n      if (it) o = it;\n      var i = 0;\n\n      var F = function () {};\n\n      return {\n        s: F,\n        n: function () {\n          if (i >= o.length) return {\n            done: true\n          };\n          return {\n            done: false,\n            value: o[i++]\n          };\n        },\n        e: function (e) {\n          throw e;\n        },\n        f: F\n      };\n    }\n\n    throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n\n  var normalCompletion = true,\n      didErr = false,\n      err;\n  return {\n    s: function () {\n      it = it.call(o);\n    },\n    n: function () {\n      var step = it.next();\n      normalCompletion = step.done;\n      return step;\n    },\n    e: function (e) {\n      didErr = true;\n      err = e;\n    },\n    f: function () {\n      try {\n        if (!normalCompletion && it.return != null) it.return();\n      } finally {\n        if (didErr) throw err;\n      }\n    }\n  };\n}\n\n/**\n * de Casteljau's algorithm for drawing and splitting bezier curves.\n * Inspired by https://pomax.github.io/bezierinfo/\n *\n * @param {Number[][]} points Array of [x,y] points: [start, control1, control2, ..., end]\n *   The original segment to split.\n * @param {Number} t Where to split the curve (value between [0, 1])\n * @return {Object} An object { left, right } where left is the segment from 0..t and\n *   right is the segment from t..1.\n */\nfunction decasteljau(points, t) {\n  var left = [];\n  var right = [];\n\n  function decasteljauRecurse(points, t) {\n    if (points.length === 1) {\n      left.push(points[0]);\n      right.push(points[0]);\n    } else {\n      var newPoints = Array(points.length - 1);\n\n      for (var i = 0; i < newPoints.length; i++) {\n        if (i === 0) {\n          left.push(points[0]);\n        }\n\n        if (i === newPoints.length - 1) {\n          right.push(points[i + 1]);\n        }\n\n        newPoints[i] = [(1 - t) * points[i][0] + t * points[i + 1][0], (1 - t) * points[i][1] + t * points[i + 1][1]];\n      }\n\n      decasteljauRecurse(newPoints, t);\n    }\n  }\n\n  if (points.length) {\n    decasteljauRecurse(points, t);\n  }\n\n  return {\n    left: left,\n    right: right.reverse()\n  };\n}\n/**\n * Convert segments represented as points back into a command object\n *\n * @param {Number[][]} points Array of [x,y] points: [start, control1, control2, ..., end]\n *   Represents a segment\n * @return {Object} A command object representing the segment.\n */\n\n\nfunction pointsToCommand(points) {\n  var command = {};\n\n  if (points.length === 4) {\n    command.x2 = points[2][0];\n    command.y2 = points[2][1];\n  }\n\n  if (points.length >= 3) {\n    command.x1 = points[1][0];\n    command.y1 = points[1][1];\n  }\n\n  command.x = points[points.length - 1][0];\n  command.y = points[points.length - 1][1];\n\n  if (points.length === 4) {\n    // start, control1, control2, end\n    command.type = 'C';\n  } else if (points.length === 3) {\n    // start, control, end\n    command.type = 'Q';\n  } else {\n    // start, end\n    command.type = 'L';\n  }\n\n  return command;\n}\n/**\n * Runs de Casteljau's algorithm enough times to produce the desired number of segments.\n *\n * @param {Number[][]} points Array of [x,y] points for de Casteljau (the initial segment to split)\n * @param {Number} segmentCount Number of segments to split the original into\n * @return {Number[][][]} Array of segments\n */\n\n\nfunction splitCurveAsPoints(points, segmentCount) {\n  segmentCount = segmentCount || 2;\n  var segments = [];\n  var remainingCurve = points;\n  var tIncrement = 1 / segmentCount; // x-----x-----x-----x\n  // t=  0.33   0.66   1\n  // x-----o-----------x\n  // r=  0.33\n  //       x-----o-----x\n  // r=         0.5  (0.33 / (1 - 0.33))  === tIncrement / (1 - (tIncrement * (i - 1))\n  // x-----x-----x-----x----x\n  // t=  0.25   0.5   0.75  1\n  // x-----o----------------x\n  // r=  0.25\n  //       x-----o----------x\n  // r=         0.33  (0.25 / (1 - 0.25))\n  //             x-----o----x\n  // r=         0.5  (0.25 / (1 - 0.5))\n\n  for (var i = 0; i < segmentCount - 1; i++) {\n    var tRelative = tIncrement / (1 - tIncrement * i);\n    var split = decasteljau(remainingCurve, tRelative);\n    segments.push(split.left);\n    remainingCurve = split.right;\n  } // last segment is just to the end from the last point\n\n\n  segments.push(remainingCurve);\n  return segments;\n}\n/**\n * Convert command objects to arrays of points, run de Casteljau's algorithm on it\n * to split into to the desired number of segments.\n *\n * @param {Object} commandStart The start command object\n * @param {Object} commandEnd The end command object\n * @param {Number} segmentCount The number of segments to create\n * @return {Object[]} An array of commands representing the segments in sequence\n */\n\n\nfunction splitCurve(commandStart, commandEnd, segmentCount) {\n  var points = [[commandStart.x, commandStart.y]];\n\n  if (commandEnd.x1 != null) {\n    points.push([commandEnd.x1, commandEnd.y1]);\n  }\n\n  if (commandEnd.x2 != null) {\n    points.push([commandEnd.x2, commandEnd.y2]);\n  }\n\n  points.push([commandEnd.x, commandEnd.y]);\n  return splitCurveAsPoints(points, segmentCount).map(pointsToCommand);\n}\n\nvar commandTokenRegex = /[MLCSTQAHVZmlcstqahv]|-?[\\d.e+-]+/g;\n/**\n * List of params for each command type in a path `d` attribute\n */\n\nvar typeMap = {\n  M: ['x', 'y'],\n  L: ['x', 'y'],\n  H: ['x'],\n  V: ['y'],\n  C: ['x1', 'y1', 'x2', 'y2', 'x', 'y'],\n  S: ['x2', 'y2', 'x', 'y'],\n  Q: ['x1', 'y1', 'x', 'y'],\n  T: ['x', 'y'],\n  A: ['rx', 'ry', 'xAxisRotation', 'largeArcFlag', 'sweepFlag', 'x', 'y'],\n  Z: []\n}; // Add lower case entries too matching uppercase (e.g. 'm' == 'M')\n\nObject.keys(typeMap).forEach(function (key) {\n  typeMap[key.toLowerCase()] = typeMap[key];\n});\n\nfunction arrayOfLength(length, value) {\n  var array = Array(length);\n\n  for (var i = 0; i < length; i++) {\n    array[i] = value;\n  }\n\n  return array;\n}\n/**\n * Converts a command object to a string to be used in a `d` attribute\n * @param {Object} command A command object\n * @return {String} The string for the `d` attribute\n */\n\n\nfunction commandToString(command) {\n  return \"\".concat(command.type).concat(typeMap[command.type].map(function (p) {\n    return command[p];\n  }).join(','));\n}\n/**\n * Converts command A to have the same type as command B.\n *\n * e.g., L0,5 -> C0,5,0,5,0,5\n *\n * Uses these rules:\n * x1 <- x\n * x2 <- x\n * y1 <- y\n * y2 <- y\n * rx <- 0\n * ry <- 0\n * xAxisRotation <- read from B\n * largeArcFlag <- read from B\n * sweepflag <- read from B\n *\n * @param {Object} aCommand Command object from path `d` attribute\n * @param {Object} bCommand Command object from path `d` attribute to match against\n * @return {Object} aCommand converted to type of bCommand\n */\n\n\nfunction convertToSameType(aCommand, bCommand) {\n  var conversionMap = {\n    x1: 'x',\n    y1: 'y',\n    x2: 'x',\n    y2: 'y'\n  };\n  var readFromBKeys = ['xAxisRotation', 'largeArcFlag', 'sweepFlag']; // convert (but ignore M types)\n\n  if (aCommand.type !== bCommand.type && bCommand.type.toUpperCase() !== 'M') {\n    var aConverted = {};\n    Object.keys(bCommand).forEach(function (bKey) {\n      var bValue = bCommand[bKey]; // first read from the A command\n\n      var aValue = aCommand[bKey]; // if it is one of these values, read from B no matter what\n\n      if (aValue === undefined) {\n        if (readFromBKeys.includes(bKey)) {\n          aValue = bValue;\n        } else {\n          // if it wasn't in the A command, see if an equivalent was\n          if (aValue === undefined && conversionMap[bKey]) {\n            aValue = aCommand[conversionMap[bKey]];\n          } // if it doesn't have a converted value, use 0\n\n\n          if (aValue === undefined) {\n            aValue = 0;\n          }\n        }\n      }\n\n      aConverted[bKey] = aValue;\n    }); // update the type to match B\n\n    aConverted.type = bCommand.type;\n    aCommand = aConverted;\n  }\n\n  return aCommand;\n}\n/**\n * Interpolate between command objects commandStart and commandEnd segmentCount times.\n * If the types are L, Q, or C then the curves are split as per de Casteljau's algorithm.\n * Otherwise we just copy commandStart segmentCount - 1 times, finally ending with commandEnd.\n *\n * @param {Object} commandStart Command object at the beginning of the segment\n * @param {Object} commandEnd Command object at the end of the segment\n * @param {Number} segmentCount The number of segments to split this into. If only 1\n *   Then [commandEnd] is returned.\n * @return {Object[]} Array of ~segmentCount command objects between commandStart and\n *   commandEnd. (Can be segmentCount+1 objects if commandStart is type M).\n */\n\n\nfunction splitSegment(commandStart, commandEnd, segmentCount) {\n  var segments = []; // line, quadratic bezier, or cubic bezier\n\n  if (commandEnd.type === 'L' || commandEnd.type === 'Q' || commandEnd.type === 'C') {\n    segments = segments.concat(splitCurve(commandStart, commandEnd, segmentCount)); // general case - just copy the same point\n  } else {\n    var copyCommand = _extends({}, commandStart); // convert M to L\n\n\n    if (copyCommand.type === 'M') {\n      copyCommand.type = 'L';\n    }\n\n    segments = segments.concat(arrayOfLength(segmentCount - 1).map(function () {\n      return copyCommand;\n    }));\n    segments.push(commandEnd);\n  }\n\n  return segments;\n}\n/**\n * Extends an array of commandsToExtend to the length of the referenceCommands by\n * splitting segments until the number of commands match. Ensures all the actual\n * points of commandsToExtend are in the extended array.\n *\n * @param {Object[]} commandsToExtend The command object array to extend\n * @param {Object[]} referenceCommands The command object array to match in length\n * @param {Function} excludeSegment a function that takes a start command object and\n *   end command object and returns true if the segment should be excluded from splitting.\n * @return {Object[]} The extended commandsToExtend array\n */\n\n\nfunction extend(commandsToExtend, referenceCommands, excludeSegment) {\n  // compute insertion points:\n  // number of segments in the path to extend\n  var numSegmentsToExtend = commandsToExtend.length - 1; // number of segments in the reference path.\n\n  var numReferenceSegments = referenceCommands.length - 1; // this value is always between [0, 1].\n\n  var segmentRatio = numSegmentsToExtend / numReferenceSegments; // create a map, mapping segments in referenceCommands to how many points\n  // should be added in that segment (should always be >= 1 since we need each\n  // point itself).\n  // 0 = segment 0-1, 1 = segment 1-2, n-1 = last vertex\n\n  var countPointsPerSegment = arrayOfLength(numReferenceSegments).reduce(function (accum, d, i) {\n    var insertIndex = Math.floor(segmentRatio * i); // handle excluding segments\n\n    if (excludeSegment && insertIndex < commandsToExtend.length - 1 && excludeSegment(commandsToExtend[insertIndex], commandsToExtend[insertIndex + 1])) {\n      // set the insertIndex to the segment that this point should be added to:\n      // round the insertIndex essentially so we split half and half on\n      // neighbouring segments. hence the segmentRatio * i < 0.5\n      var addToPriorSegment = segmentRatio * i % 1 < 0.5; // only skip segment if we already have 1 point in it (can't entirely remove a segment)\n\n      if (accum[insertIndex]) {\n        // TODO - Note this is a naive algorithm that should work for most d3-area use cases\n        // but if two adjacent segments are supposed to be skipped, this will not perform as\n        // expected. Could be updated to search for nearest segment to place the point in, but\n        // will only do that if necessary.\n        // add to the prior segment\n        if (addToPriorSegment) {\n          if (insertIndex > 0) {\n            insertIndex -= 1; // not possible to add to previous so adding to next\n          } else if (insertIndex < commandsToExtend.length - 1) {\n            insertIndex += 1;\n          } // add to next segment\n\n        } else if (insertIndex < commandsToExtend.length - 1) {\n          insertIndex += 1; // not possible to add to next so adding to previous\n        } else if (insertIndex > 0) {\n          insertIndex -= 1;\n        }\n      }\n    }\n\n    accum[insertIndex] = (accum[insertIndex] || 0) + 1;\n    return accum;\n  }, []); // extend each segment to have the correct number of points for a smooth interpolation\n\n  var extended = countPointsPerSegment.reduce(function (extended, segmentCount, i) {\n    // if last command, just add `segmentCount` number of times\n    if (i === commandsToExtend.length - 1) {\n      var lastCommandCopies = arrayOfLength(segmentCount, _extends({}, commandsToExtend[commandsToExtend.length - 1])); // convert M to L\n\n      if (lastCommandCopies[0].type === 'M') {\n        lastCommandCopies.forEach(function (d) {\n          d.type = 'L';\n        });\n      }\n\n      return extended.concat(lastCommandCopies);\n    } // otherwise, split the segment segmentCount times.\n\n\n    return extended.concat(splitSegment(commandsToExtend[i], commandsToExtend[i + 1], segmentCount));\n  }, []); // add in the very first point since splitSegment only adds in the ones after it\n\n  extended.unshift(commandsToExtend[0]);\n  return extended;\n}\n/**\n * Takes a path `d` string and converts it into an array of command\n * objects. Drops the `Z` character.\n *\n * @param {String|null} d A path `d` string\n */\n\n\nfunction pathCommandsFromString(d) {\n  // split into valid tokens\n  var tokens = (d || '').match(commandTokenRegex) || [];\n  var commands = [];\n  var commandArgs;\n  var command; // iterate over each token, checking if we are at a new command\n  // by presence in the typeMap\n\n  for (var i = 0; i < tokens.length; ++i) {\n    commandArgs = typeMap[tokens[i]]; // new command found:\n\n    if (commandArgs) {\n      command = {\n        type: tokens[i]\n      }; // add each of the expected args for this command:\n\n      for (var a = 0; a < commandArgs.length; ++a) {\n        command[commandArgs[a]] = +tokens[i + a + 1];\n      } // need to increment our token index appropriately since\n      // we consumed token args\n\n\n      i += commandArgs.length;\n      commands.push(command);\n    }\n  }\n\n  return commands;\n}\n/**\n * Interpolate from A to B by extending A and B during interpolation to have\n * the same number of points. This allows for a smooth transition when they\n * have a different number of points.\n *\n * Ignores the `Z` command in paths unless both A and B end with it.\n *\n * This function works directly with arrays of command objects instead of with\n * path `d` strings (see interpolatePath for working with `d` strings).\n *\n * @param {Object[]} aCommandsInput Array of path commands\n * @param {Object[]} bCommandsInput Array of path commands\n * @param {(Function|Object)} interpolateOptions\n * @param {Function} interpolateOptions.excludeSegment a function that takes a start command object and\n *   end command object and returns true if the segment should be excluded from splitting.\n * @param {Boolean} interpolateOptions.snapEndsToInput a boolean indicating whether end of input should\n *   be sourced from input argument or computed.\n * @returns {Function} Interpolation function that maps t ([0, 1]) to an array of path commands.\n */\n\nfunction interpolatePathCommands(aCommandsInput, bCommandsInput, interpolateOptions) {\n  // make a copy so we don't mess with the input arrays\n  var aCommands = aCommandsInput == null ? [] : aCommandsInput.slice();\n  var bCommands = bCommandsInput == null ? [] : bCommandsInput.slice();\n\n  var _ref = _typeof(interpolateOptions) === 'object' ? interpolateOptions : {\n    excludeSegment: interpolateOptions,\n    snapEndsToInput: true\n  },\n      excludeSegment = _ref.excludeSegment,\n      snapEndsToInput = _ref.snapEndsToInput; // both input sets are empty, so we don't interpolate\n\n\n  if (!aCommands.length && !bCommands.length) {\n    return function nullInterpolator() {\n      return [];\n    };\n  } // do we add Z during interpolation? yes if both have it. (we'd expect both to have it or not)\n\n\n  var addZ = (aCommands.length === 0 || aCommands[aCommands.length - 1].type === 'Z') && (bCommands.length === 0 || bCommands[bCommands.length - 1].type === 'Z'); // we temporarily remove Z\n\n  if (aCommands.length > 0 && aCommands[aCommands.length - 1].type === 'Z') {\n    aCommands.pop();\n  }\n\n  if (bCommands.length > 0 && bCommands[bCommands.length - 1].type === 'Z') {\n    bCommands.pop();\n  } // if A is empty, treat it as if it used to contain just the first point\n  // of B. This makes it so the line extends out of from that first point.\n\n\n  if (!aCommands.length) {\n    aCommands.push(bCommands[0]); // otherwise if B is empty, treat it as if it contains the first point\n    // of A. This makes it so the line retracts into the first point.\n  } else if (!bCommands.length) {\n    bCommands.push(aCommands[0]);\n  } // extend to match equal size\n\n\n  var numPointsToExtend = Math.abs(bCommands.length - aCommands.length);\n\n  if (numPointsToExtend !== 0) {\n    // B has more points than A, so add points to A before interpolating\n    if (bCommands.length > aCommands.length) {\n      aCommands = extend(aCommands, bCommands, excludeSegment); // else if A has more points than B, add more points to B\n    } else if (bCommands.length < aCommands.length) {\n      bCommands = extend(bCommands, aCommands, excludeSegment);\n    }\n  } // commands have same length now.\n  // convert commands in A to the same type as those in B\n\n\n  aCommands = aCommands.map(function (aCommand, i) {\n    return convertToSameType(aCommand, bCommands[i]);\n  }); // create mutable interpolated command objects\n\n  var interpolatedCommands = aCommands.map(function (aCommand) {\n    return _objectSpread2({}, aCommand);\n  });\n\n  if (addZ) {\n    interpolatedCommands.push({\n      type: 'Z'\n    });\n    aCommands.push({\n      type: 'Z'\n    }); // required for when returning at t == 0\n  }\n\n  return function pathCommandInterpolator(t) {\n    // at 1 return the final value without the extensions used during interpolation\n    if (t === 1 && snapEndsToInput) {\n      return bCommandsInput == null ? [] : bCommandsInput;\n    } // work with aCommands directly since interpolatedCommands are mutated\n\n\n    if (t === 0) {\n      return aCommands;\n    } // interpolate the commands using the mutable interpolated command objs\n\n\n    for (var i = 0; i < interpolatedCommands.length; ++i) {\n      // if (interpolatedCommands[i].type === 'Z') continue;\n      var aCommand = aCommands[i];\n      var bCommand = bCommands[i];\n      var interpolatedCommand = interpolatedCommands[i];\n\n      var _iterator = _createForOfIteratorHelper(typeMap[interpolatedCommand.type]),\n          _step;\n\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var arg = _step.value;\n          interpolatedCommand[arg] = (1 - t) * aCommand[arg] + t * bCommand[arg]; // do not use floats for flags (#27), round to integer\n\n          if (arg === 'largeArcFlag' || arg === 'sweepFlag') {\n            interpolatedCommand[arg] = Math.round(interpolatedCommand[arg]);\n          }\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n    }\n\n    return interpolatedCommands;\n  };\n}\n/** @typedef InterpolateOptions  */\n\n/**\n * Interpolate from A to B by extending A and B during interpolation to have\n * the same number of points. This allows for a smooth transition when they\n * have a different number of points.\n *\n * Ignores the `Z` character in paths unless both A and B end with it.\n *\n * @param {String} a The `d` attribute for a path\n * @param {String} b The `d` attribute for a path\n * @param {((command1, command2) => boolean|{\n *   excludeSegment?: (command1, command2) => boolean;\n *   snapEndsToInput?: boolean\n * })} interpolateOptions The excludeSegment function or an options object\n *    - interpolateOptions.excludeSegment a function that takes a start command object and\n *      end command object and returns true if the segment should be excluded from splitting.\n *    - interpolateOptions.snapEndsToInput a boolean indicating whether end of input should\n *      be sourced from input argument or computed.\n * @returns {Function} Interpolation function that maps t ([0, 1]) to a path `d` string.\n */\n\nfunction interpolatePath(a, b, interpolateOptions) {\n  var aCommands = pathCommandsFromString(a);\n  var bCommands = pathCommandsFromString(b);\n\n  var _ref2 = _typeof(interpolateOptions) === 'object' ? interpolateOptions : {\n    excludeSegment: interpolateOptions,\n    snapEndsToInput: true\n  },\n      excludeSegment = _ref2.excludeSegment,\n      snapEndsToInput = _ref2.snapEndsToInput;\n\n  if (!aCommands.length && !bCommands.length) {\n    return function nullInterpolator() {\n      return '';\n    };\n  }\n\n  var commandInterpolator = interpolatePathCommands(aCommands, bCommands, {\n    excludeSegment: excludeSegment,\n    snapEndsToInput: snapEndsToInput\n  });\n  return function pathStringInterpolator(t) {\n    // at 1 return the final value without the extensions used during interpolation\n    if (t === 1 && snapEndsToInput) {\n      return b == null ? '' : b;\n    }\n\n    var interpolatedCommands = commandInterpolator(t); // convert to a string (fastest concat: https://jsperf.com/join-concat/150)\n\n    var interpolatedString = '';\n\n    var _iterator2 = _createForOfIteratorHelper(interpolatedCommands),\n        _step2;\n\n    try {\n      for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n        var interpolatedCommand = _step2.value;\n        interpolatedString += commandToString(interpolatedCommand);\n      }\n    } catch (err) {\n      _iterator2.e(err);\n    } finally {\n      _iterator2.f();\n    }\n\n    return interpolatedString;\n  };\n}\n\nexports.interpolatePath = interpolatePath;\nexports.interpolatePathCommands = interpolatePathCommands;\nexports.pathCommandsFromString = pathCommandsFromString;\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\n})));\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAU,QAAQ,SAAS;AAC5B,aAAO,YAAY,YAAY,OAAO,WAAW,cAAc,QAAQ,OAAO,IAC9E,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,CAAC,SAAS,GAAG,OAAO,KACvE,SAAS,OAAO,eAAe,cAAc,aAAa,UAAU,MAAM,QAAQ,OAAO,KAAK,OAAO,MAAM,CAAC,CAAC;AAAA,IAC9G,GAAE,SAAO,SAAUA,UAAS;AAAE;AAE9B,eAAS,QAAQ,QAAQ,gBAAgB;AACvC,YAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,YAAI,OAAO,uBAAuB;AAChC,cAAI,UAAU,OAAO,sBAAsB,MAAM;AAEjD,cAAI,gBAAgB;AAClB,sBAAU,QAAQ,OAAO,SAAU,KAAK;AACtC,qBAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,YACtD,CAAC;AAAA,UACH;AAEA,eAAK,KAAK,MAAM,MAAM,OAAO;AAAA,QAC/B;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,eAAe,QAAQ;AAC9B,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAEpD,cAAI,IAAI,GAAG;AACT,oBAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,8BAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,YAC1C,CAAC;AAAA,UACH,WAAW,OAAO,2BAA2B;AAC3C,mBAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,UAC1E,OAAO;AACL,oBAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,qBAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,YACjF,CAAC;AAAA,UACH;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,QAAQ,KAAK;AACpB;AAEA,YAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AACvE,oBAAU,SAAUC,MAAK;AACvB,mBAAO,OAAOA;AAAA,UAChB;AAAA,QACF,OAAO;AACL,oBAAU,SAAUA,MAAK;AACvB,mBAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,UAC3H;AAAA,QACF;AAEA,eAAO,QAAQ,GAAG;AAAA,MACpB;AAEA,eAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,YAAI,OAAO,KAAK;AACd,iBAAO,eAAe,KAAK,KAAK;AAAA,YAC9B;AAAA,YACA,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,UAAU;AAAA,UACZ,CAAC;AAAA,QACH,OAAO;AACL,cAAI,GAAG,IAAI;AAAA,QACb;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,WAAW;AAClB,mBAAW,OAAO,UAAU,SAAU,QAAQ;AAC5C,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAI,SAAS,UAAU,CAAC;AAExB,qBAAS,OAAO,QAAQ;AACtB,kBAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,uBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,eAAO,SAAS,MAAM,MAAM,SAAS;AAAA,MACvC;AAEA,eAAS,4BAA4B,GAAG,QAAQ;AAC9C,YAAI,CAAC,EAAG;AACR,YAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAC7D,YAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,YAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AACvD,YAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AACnD,YAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AAAA,MACjH;AAEA,eAAS,kBAAkB,KAAK,KAAK;AACnC,YAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAE/C,iBAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,eAAO;AAAA,MACT;AAEA,eAAS,2BAA2B,GAAG,gBAAgB;AACrD,YAAI,KAAK,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAE9E,YAAI,CAAC,IAAI;AACP,cAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,4BAA4B,CAAC,MAAM,kBAAkB,KAAK,OAAO,EAAE,WAAW,UAAU;AACpH,gBAAI,GAAI,KAAI;AACZ,gBAAI,IAAI;AAER,gBAAI,IAAI,WAAY;AAAA,YAAC;AAErB,mBAAO;AAAA,cACL,GAAG;AAAA,cACH,GAAG,WAAY;AACb,oBAAI,KAAK,EAAE,OAAQ,QAAO;AAAA,kBACxB,MAAM;AAAA,gBACR;AACA,uBAAO;AAAA,kBACL,MAAM;AAAA,kBACN,OAAO,EAAE,GAAG;AAAA,gBACd;AAAA,cACF;AAAA,cACA,GAAG,SAAU,GAAG;AACd,sBAAM;AAAA,cACR;AAAA,cACA,GAAG;AAAA,YACL;AAAA,UACF;AAEA,gBAAM,IAAI,UAAU,uIAAuI;AAAA,QAC7J;AAEA,YAAI,mBAAmB,MACnB,SAAS,OACT;AACJ,eAAO;AAAA,UACL,GAAG,WAAY;AACb,iBAAK,GAAG,KAAK,CAAC;AAAA,UAChB;AAAA,UACA,GAAG,WAAY;AACb,gBAAI,OAAO,GAAG,KAAK;AACnB,+BAAmB,KAAK;AACxB,mBAAO;AAAA,UACT;AAAA,UACA,GAAG,SAAU,GAAG;AACd,qBAAS;AACT,kBAAM;AAAA,UACR;AAAA,UACA,GAAG,WAAY;AACb,gBAAI;AACF,kBAAI,CAAC,oBAAoB,GAAG,UAAU,KAAM,IAAG,OAAO;AAAA,YACxD,UAAE;AACA,kBAAI,OAAQ,OAAM;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAYA,eAAS,YAAY,QAAQ,GAAG;AAC9B,YAAI,OAAO,CAAC;AACZ,YAAI,QAAQ,CAAC;AAEb,iBAAS,mBAAmBC,SAAQC,IAAG;AACrC,cAAID,QAAO,WAAW,GAAG;AACvB,iBAAK,KAAKA,QAAO,CAAC,CAAC;AACnB,kBAAM,KAAKA,QAAO,CAAC,CAAC;AAAA,UACtB,OAAO;AACL,gBAAI,YAAY,MAAMA,QAAO,SAAS,CAAC;AAEvC,qBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,kBAAI,MAAM,GAAG;AACX,qBAAK,KAAKA,QAAO,CAAC,CAAC;AAAA,cACrB;AAEA,kBAAI,MAAM,UAAU,SAAS,GAAG;AAC9B,sBAAM,KAAKA,QAAO,IAAI,CAAC,CAAC;AAAA,cAC1B;AAEA,wBAAU,CAAC,IAAI,EAAE,IAAIC,MAAKD,QAAO,CAAC,EAAE,CAAC,IAAIC,KAAID,QAAO,IAAI,CAAC,EAAE,CAAC,IAAI,IAAIC,MAAKD,QAAO,CAAC,EAAE,CAAC,IAAIC,KAAID,QAAO,IAAI,CAAC,EAAE,CAAC,CAAC;AAAA,YAC9G;AAEA,+BAAmB,WAAWC,EAAC;AAAA,UACjC;AAAA,QACF;AAEA,YAAI,OAAO,QAAQ;AACjB,6BAAmB,QAAQ,CAAC;AAAA,QAC9B;AAEA,eAAO;AAAA,UACL;AAAA,UACA,OAAO,MAAM,QAAQ;AAAA,QACvB;AAAA,MACF;AAUA,eAAS,gBAAgB,QAAQ;AAC/B,YAAI,UAAU,CAAC;AAEf,YAAI,OAAO,WAAW,GAAG;AACvB,kBAAQ,KAAK,OAAO,CAAC,EAAE,CAAC;AACxB,kBAAQ,KAAK,OAAO,CAAC,EAAE,CAAC;AAAA,QAC1B;AAEA,YAAI,OAAO,UAAU,GAAG;AACtB,kBAAQ,KAAK,OAAO,CAAC,EAAE,CAAC;AACxB,kBAAQ,KAAK,OAAO,CAAC,EAAE,CAAC;AAAA,QAC1B;AAEA,gBAAQ,IAAI,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC;AACvC,gBAAQ,IAAI,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC;AAEvC,YAAI,OAAO,WAAW,GAAG;AAEvB,kBAAQ,OAAO;AAAA,QACjB,WAAW,OAAO,WAAW,GAAG;AAE9B,kBAAQ,OAAO;AAAA,QACjB,OAAO;AAEL,kBAAQ,OAAO;AAAA,QACjB;AAEA,eAAO;AAAA,MACT;AAUA,eAAS,mBAAmB,QAAQ,cAAc;AAChD,uBAAe,gBAAgB;AAC/B,YAAI,WAAW,CAAC;AAChB,YAAI,iBAAiB;AACrB,YAAI,aAAa,IAAI;AAerB,iBAAS,IAAI,GAAG,IAAI,eAAe,GAAG,KAAK;AACzC,cAAI,YAAY,cAAc,IAAI,aAAa;AAC/C,cAAI,QAAQ,YAAY,gBAAgB,SAAS;AACjD,mBAAS,KAAK,MAAM,IAAI;AACxB,2BAAiB,MAAM;AAAA,QACzB;AAGA,iBAAS,KAAK,cAAc;AAC5B,eAAO;AAAA,MACT;AAYA,eAAS,WAAW,cAAc,YAAY,cAAc;AAC1D,YAAI,SAAS,CAAC,CAAC,aAAa,GAAG,aAAa,CAAC,CAAC;AAE9C,YAAI,WAAW,MAAM,MAAM;AACzB,iBAAO,KAAK,CAAC,WAAW,IAAI,WAAW,EAAE,CAAC;AAAA,QAC5C;AAEA,YAAI,WAAW,MAAM,MAAM;AACzB,iBAAO,KAAK,CAAC,WAAW,IAAI,WAAW,EAAE,CAAC;AAAA,QAC5C;AAEA,eAAO,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC;AACxC,eAAO,mBAAmB,QAAQ,YAAY,EAAE,IAAI,eAAe;AAAA,MACrE;AAEA,UAAI,oBAAoB;AAKxB,UAAI,UAAU;AAAA,QACZ,GAAG,CAAC,KAAK,GAAG;AAAA,QACZ,GAAG,CAAC,KAAK,GAAG;AAAA,QACZ,GAAG,CAAC,GAAG;AAAA,QACP,GAAG,CAAC,GAAG;AAAA,QACP,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,KAAK,GAAG;AAAA,QACpC,GAAG,CAAC,MAAM,MAAM,KAAK,GAAG;AAAA,QACxB,GAAG,CAAC,MAAM,MAAM,KAAK,GAAG;AAAA,QACxB,GAAG,CAAC,KAAK,GAAG;AAAA,QACZ,GAAG,CAAC,MAAM,MAAM,iBAAiB,gBAAgB,aAAa,KAAK,GAAG;AAAA,QACtE,GAAG,CAAC;AAAA,MACN;AAEA,aAAO,KAAK,OAAO,EAAE,QAAQ,SAAU,KAAK;AAC1C,gBAAQ,IAAI,YAAY,CAAC,IAAI,QAAQ,GAAG;AAAA,MAC1C,CAAC;AAED,eAAS,cAAc,QAAQ,OAAO;AACpC,YAAI,QAAQ,MAAM,MAAM;AAExB,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,gBAAM,CAAC,IAAI;AAAA,QACb;AAEA,eAAO;AAAA,MACT;AAQA,eAAS,gBAAgB,SAAS;AAChC,eAAO,GAAG,OAAO,QAAQ,IAAI,EAAE,OAAO,QAAQ,QAAQ,IAAI,EAAE,IAAI,SAAU,GAAG;AAC3E,iBAAO,QAAQ,CAAC;AAAA,QAClB,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,MACd;AAuBA,eAAS,kBAAkB,UAAU,UAAU;AAC7C,YAAI,gBAAgB;AAAA,UAClB,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,IAAI;AAAA,QACN;AACA,YAAI,gBAAgB,CAAC,iBAAiB,gBAAgB,WAAW;AAEjE,YAAI,SAAS,SAAS,SAAS,QAAQ,SAAS,KAAK,YAAY,MAAM,KAAK;AAC1E,cAAI,aAAa,CAAC;AAClB,iBAAO,KAAK,QAAQ,EAAE,QAAQ,SAAU,MAAM;AAC5C,gBAAI,SAAS,SAAS,IAAI;AAE1B,gBAAI,SAAS,SAAS,IAAI;AAE1B,gBAAI,WAAW,QAAW;AACxB,kBAAI,cAAc,SAAS,IAAI,GAAG;AAChC,yBAAS;AAAA,cACX,OAAO;AAEL,oBAAI,WAAW,UAAa,cAAc,IAAI,GAAG;AAC/C,2BAAS,SAAS,cAAc,IAAI,CAAC;AAAA,gBACvC;AAGA,oBAAI,WAAW,QAAW;AACxB,2BAAS;AAAA,gBACX;AAAA,cACF;AAAA,YACF;AAEA,uBAAW,IAAI,IAAI;AAAA,UACrB,CAAC;AAED,qBAAW,OAAO,SAAS;AAC3B,qBAAW;AAAA,QACb;AAEA,eAAO;AAAA,MACT;AAeA,eAAS,aAAa,cAAc,YAAY,cAAc;AAC5D,YAAI,WAAW,CAAC;AAEhB,YAAI,WAAW,SAAS,OAAO,WAAW,SAAS,OAAO,WAAW,SAAS,KAAK;AACjF,qBAAW,SAAS,OAAO,WAAW,cAAc,YAAY,YAAY,CAAC;AAAA,QAC/E,OAAO;AACL,cAAI,cAAc,SAAS,CAAC,GAAG,YAAY;AAG3C,cAAI,YAAY,SAAS,KAAK;AAC5B,wBAAY,OAAO;AAAA,UACrB;AAEA,qBAAW,SAAS,OAAO,cAAc,eAAe,CAAC,EAAE,IAAI,WAAY;AACzE,mBAAO;AAAA,UACT,CAAC,CAAC;AACF,mBAAS,KAAK,UAAU;AAAA,QAC1B;AAEA,eAAO;AAAA,MACT;AAcA,eAAS,OAAO,kBAAkB,mBAAmB,gBAAgB;AAGnE,YAAI,sBAAsB,iBAAiB,SAAS;AAEpD,YAAI,uBAAuB,kBAAkB,SAAS;AAEtD,YAAI,eAAe,sBAAsB;AAKzC,YAAI,wBAAwB,cAAc,oBAAoB,EAAE,OAAO,SAAU,OAAO,GAAG,GAAG;AAC5F,cAAI,cAAc,KAAK,MAAM,eAAe,CAAC;AAE7C,cAAI,kBAAkB,cAAc,iBAAiB,SAAS,KAAK,eAAe,iBAAiB,WAAW,GAAG,iBAAiB,cAAc,CAAC,CAAC,GAAG;AAInJ,gBAAI,oBAAoB,eAAe,IAAI,IAAI;AAE/C,gBAAI,MAAM,WAAW,GAAG;AAMtB,kBAAI,mBAAmB;AACrB,oBAAI,cAAc,GAAG;AACnB,iCAAe;AAAA,gBACjB,WAAW,cAAc,iBAAiB,SAAS,GAAG;AACpD,iCAAe;AAAA,gBACjB;AAAA,cAEF,WAAW,cAAc,iBAAiB,SAAS,GAAG;AACpD,+BAAe;AAAA,cACjB,WAAW,cAAc,GAAG;AAC1B,+BAAe;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AAEA,gBAAM,WAAW,KAAK,MAAM,WAAW,KAAK,KAAK;AACjD,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC;AAEL,YAAI,WAAW,sBAAsB,OAAO,SAAUC,WAAU,cAAc,GAAG;AAE/E,cAAI,MAAM,iBAAiB,SAAS,GAAG;AACrC,gBAAI,oBAAoB,cAAc,cAAc,SAAS,CAAC,GAAG,iBAAiB,iBAAiB,SAAS,CAAC,CAAC,CAAC;AAE/G,gBAAI,kBAAkB,CAAC,EAAE,SAAS,KAAK;AACrC,gCAAkB,QAAQ,SAAU,GAAG;AACrC,kBAAE,OAAO;AAAA,cACX,CAAC;AAAA,YACH;AAEA,mBAAOA,UAAS,OAAO,iBAAiB;AAAA,UAC1C;AAGA,iBAAOA,UAAS,OAAO,aAAa,iBAAiB,CAAC,GAAG,iBAAiB,IAAI,CAAC,GAAG,YAAY,CAAC;AAAA,QACjG,GAAG,CAAC,CAAC;AAEL,iBAAS,QAAQ,iBAAiB,CAAC,CAAC;AACpC,eAAO;AAAA,MACT;AASA,eAAS,uBAAuB,GAAG;AAEjC,YAAI,UAAU,KAAK,IAAI,MAAM,iBAAiB,KAAK,CAAC;AACpD,YAAI,WAAW,CAAC;AAChB,YAAI;AACJ,YAAI;AAGJ,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,wBAAc,QAAQ,OAAO,CAAC,CAAC;AAE/B,cAAI,aAAa;AACf,sBAAU;AAAA,cACR,MAAM,OAAO,CAAC;AAAA,YAChB;AAEA,qBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,EAAE,GAAG;AAC3C,sBAAQ,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC;AAAA,YAC7C;AAIA,iBAAK,YAAY;AACjB,qBAAS,KAAK,OAAO;AAAA,UACvB;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAqBA,eAAS,wBAAwB,gBAAgB,gBAAgB,oBAAoB;AAEnF,YAAI,YAAY,kBAAkB,OAAO,CAAC,IAAI,eAAe,MAAM;AACnE,YAAI,YAAY,kBAAkB,OAAO,CAAC,IAAI,eAAe,MAAM;AAEnE,YAAI,OAAO,QAAQ,kBAAkB,MAAM,WAAW,qBAAqB;AAAA,UACzE,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,QACnB,GACI,iBAAiB,KAAK,gBACtB,kBAAkB,KAAK;AAG3B,YAAI,CAAC,UAAU,UAAU,CAAC,UAAU,QAAQ;AAC1C,iBAAO,SAAS,mBAAmB;AACjC,mBAAO,CAAC;AAAA,UACV;AAAA,QACF;AAGA,YAAI,QAAQ,UAAU,WAAW,KAAK,UAAU,UAAU,SAAS,CAAC,EAAE,SAAS,SAAS,UAAU,WAAW,KAAK,UAAU,UAAU,SAAS,CAAC,EAAE,SAAS;AAE3J,YAAI,UAAU,SAAS,KAAK,UAAU,UAAU,SAAS,CAAC,EAAE,SAAS,KAAK;AACxE,oBAAU,IAAI;AAAA,QAChB;AAEA,YAAI,UAAU,SAAS,KAAK,UAAU,UAAU,SAAS,CAAC,EAAE,SAAS,KAAK;AACxE,oBAAU,IAAI;AAAA,QAChB;AAIA,YAAI,CAAC,UAAU,QAAQ;AACrB,oBAAU,KAAK,UAAU,CAAC,CAAC;AAAA,QAE7B,WAAW,CAAC,UAAU,QAAQ;AAC5B,oBAAU,KAAK,UAAU,CAAC,CAAC;AAAA,QAC7B;AAGA,YAAI,oBAAoB,KAAK,IAAI,UAAU,SAAS,UAAU,MAAM;AAEpE,YAAI,sBAAsB,GAAG;AAE3B,cAAI,UAAU,SAAS,UAAU,QAAQ;AACvC,wBAAY,OAAO,WAAW,WAAW,cAAc;AAAA,UACzD,WAAW,UAAU,SAAS,UAAU,QAAQ;AAC9C,wBAAY,OAAO,WAAW,WAAW,cAAc;AAAA,UACzD;AAAA,QACF;AAIA,oBAAY,UAAU,IAAI,SAAU,UAAU,GAAG;AAC/C,iBAAO,kBAAkB,UAAU,UAAU,CAAC,CAAC;AAAA,QACjD,CAAC;AAED,YAAI,uBAAuB,UAAU,IAAI,SAAU,UAAU;AAC3D,iBAAO,eAAe,CAAC,GAAG,QAAQ;AAAA,QACpC,CAAC;AAED,YAAI,MAAM;AACR,+BAAqB,KAAK;AAAA,YACxB,MAAM;AAAA,UACR,CAAC;AACD,oBAAU,KAAK;AAAA,YACb,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAEA,eAAO,SAAS,wBAAwB,GAAG;AAEzC,cAAI,MAAM,KAAK,iBAAiB;AAC9B,mBAAO,kBAAkB,OAAO,CAAC,IAAI;AAAA,UACvC;AAGA,cAAI,MAAM,GAAG;AACX,mBAAO;AAAA,UACT;AAGA,mBAAS,IAAI,GAAG,IAAI,qBAAqB,QAAQ,EAAE,GAAG;AAEpD,gBAAI,WAAW,UAAU,CAAC;AAC1B,gBAAI,WAAW,UAAU,CAAC;AAC1B,gBAAI,sBAAsB,qBAAqB,CAAC;AAEhD,gBAAI,YAAY,2BAA2B,QAAQ,oBAAoB,IAAI,CAAC,GACxE;AAEJ,gBAAI;AACF,mBAAK,UAAU,EAAE,GAAG,EAAE,QAAQ,UAAU,EAAE,GAAG,QAAO;AAClD,oBAAI,MAAM,MAAM;AAChB,oCAAoB,GAAG,KAAK,IAAI,KAAK,SAAS,GAAG,IAAI,IAAI,SAAS,GAAG;AAErE,oBAAI,QAAQ,kBAAkB,QAAQ,aAAa;AACjD,sCAAoB,GAAG,IAAI,KAAK,MAAM,oBAAoB,GAAG,CAAC;AAAA,gBAChE;AAAA,cACF;AAAA,YACF,SAAS,KAAK;AACZ,wBAAU,EAAE,GAAG;AAAA,YACjB,UAAE;AACA,wBAAU,EAAE;AAAA,YACd;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAuBA,eAAS,gBAAgB,GAAG,GAAG,oBAAoB;AACjD,YAAI,YAAY,uBAAuB,CAAC;AACxC,YAAI,YAAY,uBAAuB,CAAC;AAExC,YAAI,QAAQ,QAAQ,kBAAkB,MAAM,WAAW,qBAAqB;AAAA,UAC1E,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,QACnB,GACI,iBAAiB,MAAM,gBACvB,kBAAkB,MAAM;AAE5B,YAAI,CAAC,UAAU,UAAU,CAAC,UAAU,QAAQ;AAC1C,iBAAO,SAAS,mBAAmB;AACjC,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,YAAI,sBAAsB,wBAAwB,WAAW,WAAW;AAAA,UACtE;AAAA,UACA;AAAA,QACF,CAAC;AACD,eAAO,SAAS,uBAAuB,GAAG;AAExC,cAAI,MAAM,KAAK,iBAAiB;AAC9B,mBAAO,KAAK,OAAO,KAAK;AAAA,UAC1B;AAEA,cAAI,uBAAuB,oBAAoB,CAAC;AAEhD,cAAI,qBAAqB;AAEzB,cAAI,aAAa,2BAA2B,oBAAoB,GAC5D;AAEJ,cAAI;AACF,iBAAK,WAAW,EAAE,GAAG,EAAE,SAAS,WAAW,EAAE,GAAG,QAAO;AACrD,kBAAI,sBAAsB,OAAO;AACjC,oCAAsB,gBAAgB,mBAAmB;AAAA,YAC3D;AAAA,UACF,SAAS,KAAK;AACZ,uBAAW,EAAE,GAAG;AAAA,UAClB,UAAE;AACA,uBAAW,EAAE;AAAA,UACf;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,MAAAJ,SAAQ,kBAAkB;AAC1B,MAAAA,SAAQ,0BAA0B;AAClC,MAAAA,SAAQ,yBAAyB;AAEjC,aAAO,eAAeA,UAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA,IAE5D,CAAE;AAAA;AAAA;", "names": ["exports", "obj", "points", "t", "extended"]}