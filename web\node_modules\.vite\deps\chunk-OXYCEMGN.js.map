{"version": 3, "sources": ["../../d3-force/src/center.js", "../../d3-force/src/constant.js", "../../d3-force/src/jiggle.js", "../../d3-force/src/collide.js", "../../d3-force/src/link.js", "../../d3-dispatch/src/dispatch.js", "../../d3-timer/src/timer.js", "../../d3-force/src/lcg.js", "../../d3-force/src/simulation.js", "../../d3-force/src/manyBody.js", "../../d3-force/src/radial.js", "../../d3-force/src/x.js", "../../d3-force/src/y.js"], "sourcesContent": ["export default function(x, y) {\n  var nodes, strength = 1;\n\n  if (x == null) x = 0;\n  if (y == null) y = 0;\n\n  function force() {\n    var i,\n        n = nodes.length,\n        node,\n        sx = 0,\n        sy = 0;\n\n    for (i = 0; i < n; ++i) {\n      node = nodes[i], sx += node.x, sy += node.y;\n    }\n\n    for (sx = (sx / n - x) * strength, sy = (sy / n - y) * strength, i = 0; i < n; ++i) {\n      node = nodes[i], node.x -= sx, node.y -= sy;\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = +_, force) : x;\n  };\n\n  force.y = function(_) {\n    return arguments.length ? (y = +_, force) : y;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n\n  return force;\n}\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function(random) {\n  return (random() - 0.5) * 1e-6;\n}\n", "import {quadtree} from \"d3-quadtree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\n\nfunction x(d) {\n  return d.x + d.vx;\n}\n\nfunction y(d) {\n  return d.y + d.vy;\n}\n\nexport default function(radius) {\n  var nodes,\n      radii,\n      random,\n      strength = 1,\n      iterations = 1;\n\n  if (typeof radius !== \"function\") radius = constant(radius == null ? 1 : +radius);\n\n  function force() {\n    var i, n = nodes.length,\n        tree,\n        node,\n        xi,\n        yi,\n        ri,\n        ri2;\n\n    for (var k = 0; k < iterations; ++k) {\n      tree = quadtree(nodes, x, y).visitAfter(prepare);\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        ri = radii[node.index], ri2 = ri * ri;\n        xi = node.x + node.vx;\n        yi = node.y + node.vy;\n        tree.visit(apply);\n      }\n    }\n\n    function apply(quad, x0, y0, x1, y1) {\n      var data = quad.data, rj = quad.r, r = ri + rj;\n      if (data) {\n        if (data.index > node.index) {\n          var x = xi - data.x - data.vx,\n              y = yi - data.y - data.vy,\n              l = x * x + y * y;\n          if (l < r * r) {\n            if (x === 0) x = jiggle(random), l += x * x;\n            if (y === 0) y = jiggle(random), l += y * y;\n            l = (r - (l = Math.sqrt(l))) / l * strength;\n            node.vx += (x *= l) * (r = (rj *= rj) / (ri2 + rj));\n            node.vy += (y *= l) * r;\n            data.vx -= x * (r = 1 - r);\n            data.vy -= y * r;\n          }\n        }\n        return;\n      }\n      return x0 > xi + r || x1 < xi - r || y0 > yi + r || y1 < yi - r;\n    }\n  }\n\n  function prepare(quad) {\n    if (quad.data) return quad.r = radii[quad.data.index];\n    for (var i = quad.r = 0; i < 4; ++i) {\n      if (quad[i] && quad[i].r > quad.r) {\n        quad.r = quad[i].r;\n      }\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length, node;\n    radii = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], radii[node.index] = +radius(node, i, nodes);\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.iterations = function(_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n\n  force.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n\n  return force;\n}\n", "import constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\n\nfunction index(d) {\n  return d.index;\n}\n\nfunction find(nodeById, nodeId) {\n  var node = nodeById.get(nodeId);\n  if (!node) throw new Error(\"node not found: \" + nodeId);\n  return node;\n}\n\nexport default function(links) {\n  var id = index,\n      strength = defaultStrength,\n      strengths,\n      distance = constant(30),\n      distances,\n      nodes,\n      count,\n      bias,\n      random,\n      iterations = 1;\n\n  if (links == null) links = [];\n\n  function defaultStrength(link) {\n    return 1 / Math.min(count[link.source.index], count[link.target.index]);\n  }\n\n  function force(alpha) {\n    for (var k = 0, n = links.length; k < iterations; ++k) {\n      for (var i = 0, link, source, target, x, y, l, b; i < n; ++i) {\n        link = links[i], source = link.source, target = link.target;\n        x = target.x + target.vx - source.x - source.vx || jiggle(random);\n        y = target.y + target.vy - source.y - source.vy || jiggle(random);\n        l = Math.sqrt(x * x + y * y);\n        l = (l - distances[i]) / l * alpha * strengths[i];\n        x *= l, y *= l;\n        target.vx -= x * (b = bias[i]);\n        target.vy -= y * b;\n        source.vx += x * (b = 1 - b);\n        source.vy += y * b;\n      }\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n\n    var i,\n        n = nodes.length,\n        m = links.length,\n        nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d])),\n        link;\n\n    for (i = 0, count = new Array(n); i < m; ++i) {\n      link = links[i], link.index = i;\n      if (typeof link.source !== \"object\") link.source = find(nodeById, link.source);\n      if (typeof link.target !== \"object\") link.target = find(nodeById, link.target);\n      count[link.source.index] = (count[link.source.index] || 0) + 1;\n      count[link.target.index] = (count[link.target.index] || 0) + 1;\n    }\n\n    for (i = 0, bias = new Array(m); i < m; ++i) {\n      link = links[i], bias[i] = count[link.source.index] / (count[link.source.index] + count[link.target.index]);\n    }\n\n    strengths = new Array(m), initializeStrength();\n    distances = new Array(m), initializeDistance();\n  }\n\n  function initializeStrength() {\n    if (!nodes) return;\n\n    for (var i = 0, n = links.length; i < n; ++i) {\n      strengths[i] = +strength(links[i], i, links);\n    }\n  }\n\n  function initializeDistance() {\n    if (!nodes) return;\n\n    for (var i = 0, n = links.length; i < n; ++i) {\n      distances[i] = +distance(links[i], i, links);\n    }\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.links = function(_) {\n    return arguments.length ? (links = _, initialize(), force) : links;\n  };\n\n  force.id = function(_) {\n    return arguments.length ? (id = _, force) : id;\n  };\n\n  force.iterations = function(_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initializeStrength(), force) : strength;\n  };\n\n  force.distance = function(_) {\n    return arguments.length ? (distance = typeof _ === \"function\" ? _ : constant(+_), initializeDistance(), force) : distance;\n  };\n\n  return force;\n}\n", "var noop = {value: () => {}};\n\nfunction dispatch() {\n  for (var i = 0, n = arguments.length, _ = {}, t; i < n; ++i) {\n    if (!(t = arguments[i] + \"\") || (t in _) || /[\\s.]/.test(t)) throw new Error(\"illegal type: \" + t);\n    _[t] = [];\n  }\n  return new Dispatch(_);\n}\n\nfunction Dispatch(_) {\n  this._ = _;\n}\n\nfunction parseTypenames(typenames, types) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    if (t && !types.hasOwnProperty(t)) throw new Error(\"unknown type: \" + t);\n    return {type: t, name: name};\n  });\n}\n\nDispatch.prototype = dispatch.prototype = {\n  constructor: Dispatch,\n  on: function(typename, callback) {\n    var _ = this._,\n        T = parseTypenames(typename + \"\", _),\n        t,\n        i = -1,\n        n = T.length;\n\n    // If no callback was specified, return the callback of the given type and name.\n    if (arguments.length < 2) {\n      while (++i < n) if ((t = (typename = T[i]).type) && (t = get(_[t], typename.name))) return t;\n      return;\n    }\n\n    // If a type was specified, set the callback for the given type and name.\n    // Otherwise, if a null callback was specified, remove callbacks of the given name.\n    if (callback != null && typeof callback !== \"function\") throw new Error(\"invalid callback: \" + callback);\n    while (++i < n) {\n      if (t = (typename = T[i]).type) _[t] = set(_[t], typename.name, callback);\n      else if (callback == null) for (t in _) _[t] = set(_[t], typename.name, null);\n    }\n\n    return this;\n  },\n  copy: function() {\n    var copy = {}, _ = this._;\n    for (var t in _) copy[t] = _[t].slice();\n    return new Dispatch(copy);\n  },\n  call: function(type, that) {\n    if ((n = arguments.length - 2) > 0) for (var args = new Array(n), i = 0, n, t; i < n; ++i) args[i] = arguments[i + 2];\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  },\n  apply: function(type, that, args) {\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (var t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  }\n};\n\nfunction get(type, name) {\n  for (var i = 0, n = type.length, c; i < n; ++i) {\n    if ((c = type[i]).name === name) {\n      return c.value;\n    }\n  }\n}\n\nfunction set(type, name, callback) {\n  for (var i = 0, n = type.length; i < n; ++i) {\n    if (type[i].name === name) {\n      type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n      break;\n    }\n  }\n  if (callback != null) type.push({name: name, value: callback});\n  return type;\n}\n\nexport default dispatch;\n", "var frame = 0, // is an animation frame pending?\n    timeout = 0, // is a timeout pending?\n    interval = 0, // are any timers active?\n    pokeDelay = 1000, // how frequently we check for clock skew\n    taskHead,\n    taskTail,\n    clockLast = 0,\n    clockNow = 0,\n    clockSkew = 0,\n    clock = typeof performance === \"object\" && performance.now ? performance : Date,\n    setFrame = typeof window === \"object\" && window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : function(f) { setTimeout(f, 17); };\n\nexport function now() {\n  return clockNow || (setFrame(clearNow), clockNow = clock.now() + clockSkew);\n}\n\nfunction clearNow() {\n  clockNow = 0;\n}\n\nexport function Timer() {\n  this._call =\n  this._time =\n  this._next = null;\n}\n\nTimer.prototype = timer.prototype = {\n  constructor: Timer,\n  restart: function(callback, delay, time) {\n    if (typeof callback !== \"function\") throw new TypeError(\"callback is not a function\");\n    time = (time == null ? now() : +time) + (delay == null ? 0 : +delay);\n    if (!this._next && taskTail !== this) {\n      if (taskTail) taskTail._next = this;\n      else taskHead = this;\n      taskTail = this;\n    }\n    this._call = callback;\n    this._time = time;\n    sleep();\n  },\n  stop: function() {\n    if (this._call) {\n      this._call = null;\n      this._time = Infinity;\n      sleep();\n    }\n  }\n};\n\nexport function timer(callback, delay, time) {\n  var t = new Timer;\n  t.restart(callback, delay, time);\n  return t;\n}\n\nexport function timerFlush() {\n  now(); // Get the current time, if not already set.\n  ++frame; // Pretend we’ve set an alarm, if we haven’t already.\n  var t = taskHead, e;\n  while (t) {\n    if ((e = clockNow - t._time) >= 0) t._call.call(undefined, e);\n    t = t._next;\n  }\n  --frame;\n}\n\nfunction wake() {\n  clockNow = (clockLast = clock.now()) + clockSkew;\n  frame = timeout = 0;\n  try {\n    timerFlush();\n  } finally {\n    frame = 0;\n    nap();\n    clockNow = 0;\n  }\n}\n\nfunction poke() {\n  var now = clock.now(), delay = now - clockLast;\n  if (delay > pokeDelay) clockSkew -= delay, clockLast = now;\n}\n\nfunction nap() {\n  var t0, t1 = taskHead, t2, time = Infinity;\n  while (t1) {\n    if (t1._call) {\n      if (time > t1._time) time = t1._time;\n      t0 = t1, t1 = t1._next;\n    } else {\n      t2 = t1._next, t1._next = null;\n      t1 = t0 ? t0._next = t2 : taskHead = t2;\n    }\n  }\n  taskTail = t0;\n  sleep(time);\n}\n\nfunction sleep(time) {\n  if (frame) return; // Soonest alarm already set, or will be.\n  if (timeout) timeout = clearTimeout(timeout);\n  var delay = time - clockNow; // Strictly less than if we recomputed clockNow.\n  if (delay > 24) {\n    if (time < Infinity) timeout = setTimeout(wake, time - clock.now() - clockSkew);\n    if (interval) interval = clearInterval(interval);\n  } else {\n    if (!interval) clockLast = clock.now(), interval = setInterval(poke, pokeDelay);\n    frame = 1, setFrame(wake);\n  }\n}\n", "// https://en.wikipedia.org/wiki/Linear_congruential_generator#Parameters_in_common_use\nconst a = 1664525;\nconst c = 1013904223;\nconst m = 4294967296; // 2^32\n\nexport default function() {\n  let s = 1;\n  return () => (s = (a * s + c) % m) / m;\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {timer} from \"d3-timer\";\nimport lcg from \"./lcg.js\";\n\nexport function x(d) {\n  return d.x;\n}\n\nexport function y(d) {\n  return d.y;\n}\n\nvar initialRadius = 10,\n    initialAngle = Math.PI * (3 - Math.sqrt(5));\n\nexport default function(nodes) {\n  var simulation,\n      alpha = 1,\n      alphaMin = 0.001,\n      alphaDecay = 1 - Math.pow(alphaMin, 1 / 300),\n      alphaTarget = 0,\n      velocityDecay = 0.6,\n      forces = new Map(),\n      stepper = timer(step),\n      event = dispatch(\"tick\", \"end\"),\n      random = lcg();\n\n  if (nodes == null) nodes = [];\n\n  function step() {\n    tick();\n    event.call(\"tick\", simulation);\n    if (alpha < alphaMin) {\n      stepper.stop();\n      event.call(\"end\", simulation);\n    }\n  }\n\n  function tick(iterations) {\n    var i, n = nodes.length, node;\n\n    if (iterations === undefined) iterations = 1;\n\n    for (var k = 0; k < iterations; ++k) {\n      alpha += (alphaTarget - alpha) * alphaDecay;\n\n      forces.forEach(function(force) {\n        force(alpha);\n      });\n\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        if (node.fx == null) node.x += node.vx *= velocityDecay;\n        else node.x = node.fx, node.vx = 0;\n        if (node.fy == null) node.y += node.vy *= velocityDecay;\n        else node.y = node.fy, node.vy = 0;\n      }\n    }\n\n    return simulation;\n  }\n\n  function initializeNodes() {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.index = i;\n      if (node.fx != null) node.x = node.fx;\n      if (node.fy != null) node.y = node.fy;\n      if (isNaN(node.x) || isNaN(node.y)) {\n        var radius = initialRadius * Math.sqrt(0.5 + i), angle = i * initialAngle;\n        node.x = radius * Math.cos(angle);\n        node.y = radius * Math.sin(angle);\n      }\n      if (isNaN(node.vx) || isNaN(node.vy)) {\n        node.vx = node.vy = 0;\n      }\n    }\n  }\n\n  function initializeForce(force) {\n    if (force.initialize) force.initialize(nodes, random);\n    return force;\n  }\n\n  initializeNodes();\n\n  return simulation = {\n    tick: tick,\n\n    restart: function() {\n      return stepper.restart(step), simulation;\n    },\n\n    stop: function() {\n      return stepper.stop(), simulation;\n    },\n\n    nodes: function(_) {\n      return arguments.length ? (nodes = _, initializeNodes(), forces.forEach(initializeForce), simulation) : nodes;\n    },\n\n    alpha: function(_) {\n      return arguments.length ? (alpha = +_, simulation) : alpha;\n    },\n\n    alphaMin: function(_) {\n      return arguments.length ? (alphaMin = +_, simulation) : alphaMin;\n    },\n\n    alphaDecay: function(_) {\n      return arguments.length ? (alphaDecay = +_, simulation) : +alphaDecay;\n    },\n\n    alphaTarget: function(_) {\n      return arguments.length ? (alphaTarget = +_, simulation) : alphaTarget;\n    },\n\n    velocityDecay: function(_) {\n      return arguments.length ? (velocityDecay = 1 - _, simulation) : 1 - velocityDecay;\n    },\n\n    randomSource: function(_) {\n      return arguments.length ? (random = _, forces.forEach(initializeForce), simulation) : random;\n    },\n\n    force: function(name, _) {\n      return arguments.length > 1 ? ((_ == null ? forces.delete(name) : forces.set(name, initializeForce(_))), simulation) : forces.get(name);\n    },\n\n    find: function(x, y, radius) {\n      var i = 0,\n          n = nodes.length,\n          dx,\n          dy,\n          d2,\n          node,\n          closest;\n\n      if (radius == null) radius = Infinity;\n      else radius *= radius;\n\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        dx = x - node.x;\n        dy = y - node.y;\n        d2 = dx * dx + dy * dy;\n        if (d2 < radius) closest = node, radius = d2;\n      }\n\n      return closest;\n    },\n\n    on: function(name, _) {\n      return arguments.length > 1 ? (event.on(name, _), simulation) : event.on(name);\n    }\n  };\n}\n", "import {quadtree} from \"d3-quadtree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\nimport {x, y} from \"./simulation.js\";\n\nexport default function() {\n  var nodes,\n      node,\n      random,\n      alpha,\n      strength = constant(-30),\n      strengths,\n      distanceMin2 = 1,\n      distanceMax2 = Infinity,\n      theta2 = 0.81;\n\n  function force(_) {\n    var i, n = nodes.length, tree = quadtree(nodes, x, y).visitAfter(accumulate);\n    for (alpha = _, i = 0; i < n; ++i) node = nodes[i], tree.visit(apply);\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length, node;\n    strengths = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], strengths[node.index] = +strength(node, i, nodes);\n  }\n\n  function accumulate(quad) {\n    var strength = 0, q, c, weight = 0, x, y, i;\n\n    // For internal nodes, accumulate forces from child quadrants.\n    if (quad.length) {\n      for (x = y = i = 0; i < 4; ++i) {\n        if ((q = quad[i]) && (c = Math.abs(q.value))) {\n          strength += q.value, weight += c, x += c * q.x, y += c * q.y;\n        }\n      }\n      quad.x = x / weight;\n      quad.y = y / weight;\n    }\n\n    // For leaf nodes, accumulate forces from coincident quadrants.\n    else {\n      q = quad;\n      q.x = q.data.x;\n      q.y = q.data.y;\n      do strength += strengths[q.data.index];\n      while (q = q.next);\n    }\n\n    quad.value = strength;\n  }\n\n  function apply(quad, x1, _, x2) {\n    if (!quad.value) return true;\n\n    var x = quad.x - node.x,\n        y = quad.y - node.y,\n        w = x2 - x1,\n        l = x * x + y * y;\n\n    // Apply the Barnes-Hut approximation if possible.\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (w * w / theta2 < l) {\n      if (l < distanceMax2) {\n        if (x === 0) x = jiggle(random), l += x * x;\n        if (y === 0) y = jiggle(random), l += y * y;\n        if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n        node.vx += x * quad.value * alpha / l;\n        node.vy += y * quad.value * alpha / l;\n      }\n      return true;\n    }\n\n    // Otherwise, process points directly.\n    else if (quad.length || l >= distanceMax2) return;\n\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (quad.data !== node || quad.next) {\n      if (x === 0) x = jiggle(random), l += x * x;\n      if (y === 0) y = jiggle(random), l += y * y;\n      if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n    }\n\n    do if (quad.data !== node) {\n      w = strengths[quad.data.index] * alpha / l;\n      node.vx += x * w;\n      node.vy += y * w;\n    } while (quad = quad.next);\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.distanceMin = function(_) {\n    return arguments.length ? (distanceMin2 = _ * _, force) : Math.sqrt(distanceMin2);\n  };\n\n  force.distanceMax = function(_) {\n    return arguments.length ? (distanceMax2 = _ * _, force) : Math.sqrt(distanceMax2);\n  };\n\n  force.theta = function(_) {\n    return arguments.length ? (theta2 = _ * _, force) : Math.sqrt(theta2);\n  };\n\n  return force;\n}\n", "import constant from \"./constant.js\";\n\nexport default function(radius, x, y) {\n  var nodes,\n      strength = constant(0.1),\n      strengths,\n      radiuses;\n\n  if (typeof radius !== \"function\") radius = constant(+radius);\n  if (x == null) x = 0;\n  if (y == null) y = 0;\n\n  function force(alpha) {\n    for (var i = 0, n = nodes.length; i < n; ++i) {\n      var node = nodes[i],\n          dx = node.x - x || 1e-6,\n          dy = node.y - y || 1e-6,\n          r = Math.sqrt(dx * dx + dy * dy),\n          k = (radiuses[i] - r) * strengths[i] * alpha / r;\n      node.vx += dx * k;\n      node.vy += dy * k;\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length;\n    strengths = new Array(n);\n    radiuses = new Array(n);\n    for (i = 0; i < n; ++i) {\n      radiuses[i] = +radius(nodes[i], i, nodes);\n      strengths[i] = isNaN(radiuses[i]) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _, initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = +_, force) : x;\n  };\n\n  force.y = function(_) {\n    return arguments.length ? (y = +_, force) : y;\n  };\n\n  return force;\n}\n", "import constant from \"./constant.js\";\n\nexport default function(x) {\n  var strength = constant(0.1),\n      nodes,\n      strengths,\n      xz;\n\n  if (typeof x !== \"function\") x = constant(x == null ? 0 : +x);\n\n  function force(alpha) {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.vx += (xz[i] - node.x) * strengths[i] * alpha;\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length;\n    strengths = new Array(n);\n    xz = new Array(n);\n    for (i = 0; i < n; ++i) {\n      strengths[i] = isNaN(xz[i] = +x(nodes[i], i, nodes)) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : x;\n  };\n\n  return force;\n}\n", "import constant from \"./constant.js\";\n\nexport default function(y) {\n  var strength = constant(0.1),\n      nodes,\n      strengths,\n      yz;\n\n  if (typeof y !== \"function\") y = constant(y == null ? 0 : +y);\n\n  function force(alpha) {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.vy += (yz[i] - node.y) * strengths[i] * alpha;\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length;\n    strengths = new Array(n);\n    yz = new Array(n);\n    for (i = 0; i < n; ++i) {\n      strengths[i] = isNaN(yz[i] = +y(nodes[i], i, nodes)) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : y;\n  };\n\n  return force;\n}\n"], "mappings": ";;;;;AAAe,SAAR,eAAiBA,IAAGC,IAAG;AAC5B,MAAI,OAAO,WAAW;AAEtB,MAAID,MAAK,KAAM,CAAAA,KAAI;AACnB,MAAIC,MAAK,KAAM,CAAAA,KAAI;AAEnB,WAAS,QAAQ;AACf,QAAI,GACA,IAAI,MAAM,QACV,MACA,KAAK,GACL,KAAK;AAET,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,aAAO,MAAM,CAAC,GAAG,MAAM,KAAK,GAAG,MAAM,KAAK;AAAA,IAC5C;AAEA,SAAK,MAAM,KAAK,IAAID,MAAK,UAAU,MAAM,KAAK,IAAIC,MAAK,UAAU,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAClF,aAAO,MAAM,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,IAC3C;AAAA,EACF;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,YAAQ;AAAA,EACV;AAEA,QAAM,IAAI,SAAS,GAAG;AACpB,WAAO,UAAU,UAAUD,KAAI,CAAC,GAAG,SAASA;AAAA,EAC9C;AAEA,QAAM,IAAI,SAAS,GAAG;AACpB,WAAO,UAAU,UAAUC,KAAI,CAAC,GAAG,SAASA;AAAA,EAC9C;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,CAAC,GAAG,SAAS;AAAA,EACrD;AAEA,SAAO;AACT;;;ACvCe,SAAR,iBAAiBC,IAAG;AACzB,SAAO,WAAW;AAChB,WAAOA;AAAA,EACT;AACF;;;ACJe,SAAR,eAAiB,QAAQ;AAC9B,UAAQ,OAAO,IAAI,OAAO;AAC5B;;;ACEA,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE,IAAI,EAAE;AACjB;AAEA,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE,IAAI,EAAE;AACjB;AAEe,SAAR,gBAAiB,QAAQ;AAC9B,MAAI,OACA,OACA,QACA,WAAW,GACX,aAAa;AAEjB,MAAI,OAAO,WAAW,WAAY,UAAS,iBAAS,UAAU,OAAO,IAAI,CAAC,MAAM;AAEhF,WAAS,QAAQ;AACf,QAAI,GAAG,IAAI,MAAM,QACb,MACA,MACA,IACA,IACA,IACA;AAEJ,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,aAAO,SAAS,OAAO,GAAG,CAAC,EAAE,WAAW,OAAO;AAC/C,WAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,eAAO,MAAM,CAAC;AACd,aAAK,MAAM,KAAK,KAAK,GAAG,MAAM,KAAK;AACnC,aAAK,KAAK,IAAI,KAAK;AACnB,aAAK,KAAK,IAAI,KAAK;AACnB,aAAK,MAAM,KAAK;AAAA,MAClB;AAAA,IACF;AAEA,aAAS,MAAM,MAAM,IAAI,IAAI,IAAI,IAAI;AACnC,UAAI,OAAO,KAAK,MAAM,KAAK,KAAK,GAAG,IAAI,KAAK;AAC5C,UAAI,MAAM;AACR,YAAI,KAAK,QAAQ,KAAK,OAAO;AAC3B,cAAIC,KAAI,KAAK,KAAK,IAAI,KAAK,IACvBC,KAAI,KAAK,KAAK,IAAI,KAAK,IACvB,IAAID,KAAIA,KAAIC,KAAIA;AACpB,cAAI,IAAI,IAAI,GAAG;AACb,gBAAID,OAAM,EAAG,CAAAA,KAAI,eAAO,MAAM,GAAG,KAAKA,KAAIA;AAC1C,gBAAIC,OAAM,EAAG,CAAAA,KAAI,eAAO,MAAM,GAAG,KAAKA,KAAIA;AAC1C,iBAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,IAAI;AACnC,iBAAK,OAAOD,MAAK,MAAM,KAAK,MAAM,OAAO,MAAM;AAC/C,iBAAK,OAAOC,MAAK,KAAK;AACtB,iBAAK,MAAMD,MAAK,IAAI,IAAI;AACxB,iBAAK,MAAMC,KAAI;AAAA,UACjB;AAAA,QACF;AACA;AAAA,MACF;AACA,aAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,IAChE;AAAA,EACF;AAEA,WAAS,QAAQ,MAAM;AACrB,QAAI,KAAK,KAAM,QAAO,KAAK,IAAI,MAAM,KAAK,KAAK,KAAK;AACpD,aAAS,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACnC,UAAI,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE,IAAI,KAAK,GAAG;AACjC,aAAK,IAAI,KAAK,CAAC,EAAE;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAEA,WAAS,aAAa;AACpB,QAAI,CAAC,MAAO;AACZ,QAAI,GAAG,IAAI,MAAM,QAAQ;AACzB,YAAQ,IAAI,MAAM,CAAC;AACnB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,QAAO,MAAM,CAAC,GAAG,MAAM,KAAK,KAAK,IAAI,CAAC,OAAO,MAAM,GAAG,KAAK;AAAA,EACrF;AAEA,QAAM,aAAa,SAAS,QAAQ,SAAS;AAC3C,YAAQ;AACR,aAAS;AACT,eAAW;AAAA,EACb;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,aAAa,CAAC,GAAG,SAAS;AAAA,EACvD;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,CAAC,GAAG,SAAS;AAAA,EACrD;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAAS;AAAA,EACzG;AAEA,SAAO;AACT;;;AChGA,SAAS,MAAM,GAAG;AAChB,SAAO,EAAE;AACX;AAEA,SAAS,KAAK,UAAU,QAAQ;AAC9B,MAAI,OAAO,SAAS,IAAI,MAAM;AAC9B,MAAI,CAAC,KAAM,OAAM,IAAI,MAAM,qBAAqB,MAAM;AACtD,SAAO;AACT;AAEe,SAAR,aAAiB,OAAO;AAC7B,MAAI,KAAK,OACL,WAAW,iBACX,WACA,WAAW,iBAAS,EAAE,GACtB,WACA,OACA,OACA,MACA,QACA,aAAa;AAEjB,MAAI,SAAS,KAAM,SAAQ,CAAC;AAE5B,WAAS,gBAAgB,MAAM;AAC7B,WAAO,IAAI,KAAK,IAAI,MAAM,KAAK,OAAO,KAAK,GAAG,MAAM,KAAK,OAAO,KAAK,CAAC;AAAA,EACxE;AAEA,WAAS,MAAM,OAAO;AACpB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,YAAY,EAAE,GAAG;AACrD,eAAS,IAAI,GAAG,MAAM,QAAQ,QAAQC,IAAGC,IAAG,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG;AAC5D,eAAO,MAAM,CAAC,GAAG,SAAS,KAAK,QAAQ,SAAS,KAAK;AACrD,QAAAD,KAAI,OAAO,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,MAAM,eAAO,MAAM;AAChE,QAAAC,KAAI,OAAO,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,MAAM,eAAO,MAAM;AAChE,YAAI,KAAK,KAAKD,KAAIA,KAAIC,KAAIA,EAAC;AAC3B,aAAK,IAAI,UAAU,CAAC,KAAK,IAAI,QAAQ,UAAU,CAAC;AAChD,QAAAD,MAAK,GAAGC,MAAK;AACb,eAAO,MAAMD,MAAK,IAAI,KAAK,CAAC;AAC5B,eAAO,MAAMC,KAAI;AACjB,eAAO,MAAMD,MAAK,IAAI,IAAI;AAC1B,eAAO,MAAMC,KAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAEA,WAAS,aAAa;AACpB,QAAI,CAAC,MAAO;AAEZ,QAAI,GACA,IAAI,MAAM,QACVC,KAAI,MAAM,QACV,WAAW,IAAI,IAAI,MAAM,IAAI,CAAC,GAAGC,OAAM,CAAC,GAAG,GAAGA,IAAG,KAAK,GAAG,CAAC,CAAC,CAAC,GAC5D;AAEJ,SAAK,IAAI,GAAG,QAAQ,IAAI,MAAM,CAAC,GAAG,IAAID,IAAG,EAAE,GAAG;AAC5C,aAAO,MAAM,CAAC,GAAG,KAAK,QAAQ;AAC9B,UAAI,OAAO,KAAK,WAAW,SAAU,MAAK,SAAS,KAAK,UAAU,KAAK,MAAM;AAC7E,UAAI,OAAO,KAAK,WAAW,SAAU,MAAK,SAAS,KAAK,UAAU,KAAK,MAAM;AAC7E,YAAM,KAAK,OAAO,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK;AAC7D,YAAM,KAAK,OAAO,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,KAAK;AAAA,IAC/D;AAEA,SAAK,IAAI,GAAG,OAAO,IAAI,MAAMA,EAAC,GAAG,IAAIA,IAAG,EAAE,GAAG;AAC3C,aAAO,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,MAAM,KAAK,OAAO,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,IAAI,MAAM,KAAK,OAAO,KAAK;AAAA,IAC3G;AAEA,gBAAY,IAAI,MAAMA,EAAC,GAAG,mBAAmB;AAC7C,gBAAY,IAAI,MAAMA,EAAC,GAAG,mBAAmB;AAAA,EAC/C;AAEA,WAAS,qBAAqB;AAC5B,QAAI,CAAC,MAAO;AAEZ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC5C,gBAAU,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,GAAG,GAAG,KAAK;AAAA,IAC7C;AAAA,EACF;AAEA,WAAS,qBAAqB;AAC5B,QAAI,CAAC,MAAO;AAEZ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC5C,gBAAU,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,GAAG,GAAG,KAAK;AAAA,IAC7C;AAAA,EACF;AAEA,QAAM,aAAa,SAAS,QAAQ,SAAS;AAC3C,YAAQ;AACR,aAAS;AACT,eAAW;AAAA,EACb;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,QAAQ,GAAG,WAAW,GAAG,SAAS;AAAA,EAC/D;AAEA,QAAM,KAAK,SAAS,GAAG;AACrB,WAAO,UAAU,UAAU,KAAK,GAAG,SAAS;AAAA,EAC9C;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,aAAa,CAAC,GAAG,SAAS;AAAA,EACvD;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,mBAAmB,GAAG,SAAS;AAAA,EACnH;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,mBAAmB,GAAG,SAAS;AAAA,EACnH;AAEA,SAAO;AACT;;;ACpHA,IAAI,OAAO,EAAC,OAAO,MAAM;AAAC,EAAC;AAE3B,SAAS,WAAW;AAClB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG;AAC3D,QAAI,EAAE,IAAI,UAAU,CAAC,IAAI,OAAQ,KAAK,KAAM,QAAQ,KAAK,CAAC,EAAG,OAAM,IAAI,MAAM,mBAAmB,CAAC;AACjG,MAAE,CAAC,IAAI,CAAC;AAAA,EACV;AACA,SAAO,IAAI,SAAS,CAAC;AACvB;AAEA,SAAS,SAAS,GAAG;AACnB,OAAK,IAAI;AACX;AAEA,SAAS,eAAe,WAAW,OAAO;AACxC,SAAO,UAAU,KAAK,EAAE,MAAM,OAAO,EAAE,IAAI,SAAS,GAAG;AACrD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,GAAG;AAChC,QAAI,KAAK,EAAG,QAAO,EAAE,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC;AACnD,QAAI,KAAK,CAAC,MAAM,eAAe,CAAC,EAAG,OAAM,IAAI,MAAM,mBAAmB,CAAC;AACvE,WAAO,EAAC,MAAM,GAAG,KAAU;AAAA,EAC7B,CAAC;AACH;AAEA,SAAS,YAAY,SAAS,YAAY;AAAA,EACxC,aAAa;AAAA,EACb,IAAI,SAAS,UAAU,UAAU;AAC/B,QAAI,IAAI,KAAK,GACT,IAAI,eAAe,WAAW,IAAI,CAAC,GACnC,GACA,IAAI,IACJ,IAAI,EAAE;AAGV,QAAI,UAAU,SAAS,GAAG;AACxB,aAAO,EAAE,IAAI,EAAG,MAAK,KAAK,WAAW,EAAE,CAAC,GAAG,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,IAAI,GAAI,QAAO;AAC3F;AAAA,IACF;AAIA,QAAI,YAAY,QAAQ,OAAO,aAAa,WAAY,OAAM,IAAI,MAAM,uBAAuB,QAAQ;AACvG,WAAO,EAAE,IAAI,GAAG;AACd,UAAI,KAAK,WAAW,EAAE,CAAC,GAAG,KAAM,GAAE,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,MAAM,QAAQ;AAAA,eAC/D,YAAY,KAAM,MAAK,KAAK,EAAG,GAAE,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,MAAM,IAAI;AAAA,IAC9E;AAEA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,WAAW;AACf,QAAI,OAAO,CAAC,GAAG,IAAI,KAAK;AACxB,aAAS,KAAK,EAAG,MAAK,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM;AACtC,WAAO,IAAI,SAAS,IAAI;AAAA,EAC1B;AAAA,EACA,MAAM,SAAS,MAAM,MAAM;AACzB,SAAK,IAAI,UAAU,SAAS,KAAK,EAAG,UAAS,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,EAAG,MAAK,CAAC,IAAI,UAAU,IAAI,CAAC;AACpH,QAAI,CAAC,KAAK,EAAE,eAAe,IAAI,EAAG,OAAM,IAAI,MAAM,mBAAmB,IAAI;AACzE,SAAK,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,EAAE,MAAM,MAAM,MAAM,IAAI;AAAA,EACrF;AAAA,EACA,OAAO,SAAS,MAAM,MAAM,MAAM;AAChC,QAAI,CAAC,KAAK,EAAE,eAAe,IAAI,EAAG,OAAM,IAAI,MAAM,mBAAmB,IAAI;AACzE,aAAS,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,EAAE,MAAM,MAAM,MAAM,IAAI;AAAA,EACzF;AACF;AAEA,SAAS,IAAI,MAAM,MAAM;AACvB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQE,IAAG,IAAI,GAAG,EAAE,GAAG;AAC9C,SAAKA,KAAI,KAAK,CAAC,GAAG,SAAS,MAAM;AAC/B,aAAOA,GAAE;AAAA,IACX;AAAA,EACF;AACF;AAEA,SAAS,IAAI,MAAM,MAAM,UAAU;AACjC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,QAAI,KAAK,CAAC,EAAE,SAAS,MAAM;AACzB,WAAK,CAAC,IAAI,MAAM,OAAO,KAAK,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC;AAChE;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY,KAAM,MAAK,KAAK,EAAC,MAAY,OAAO,SAAQ,CAAC;AAC7D,SAAO;AACT;AAEA,IAAO,mBAAQ;;;ACnFf,IAAI,QAAQ;AAAZ,IACI,UAAU;AADd,IAEI,WAAW;AAFf,IAGI,YAAY;AAHhB,IAII;AAJJ,IAKI;AALJ,IAMI,YAAY;AANhB,IAOI,WAAW;AAPf,IAQI,YAAY;AARhB,IASI,QAAQ,OAAO,gBAAgB,YAAY,YAAY,MAAM,cAAc;AAT/E,IAUI,WAAW,OAAO,WAAW,YAAY,OAAO,wBAAwB,OAAO,sBAAsB,KAAK,MAAM,IAAI,SAAS,GAAG;AAAE,aAAW,GAAG,EAAE;AAAG;AAElJ,SAAS,MAAM;AACpB,SAAO,aAAa,SAAS,QAAQ,GAAG,WAAW,MAAM,IAAI,IAAI;AACnE;AAEA,SAAS,WAAW;AAClB,aAAW;AACb;AAEO,SAAS,QAAQ;AACtB,OAAK,QACL,KAAK,QACL,KAAK,QAAQ;AACf;AAEA,MAAM,YAAY,MAAM,YAAY;AAAA,EAClC,aAAa;AAAA,EACb,SAAS,SAAS,UAAU,OAAO,MAAM;AACvC,QAAI,OAAO,aAAa,WAAY,OAAM,IAAI,UAAU,4BAA4B;AACpF,YAAQ,QAAQ,OAAO,IAAI,IAAI,CAAC,SAAS,SAAS,OAAO,IAAI,CAAC;AAC9D,QAAI,CAAC,KAAK,SAAS,aAAa,MAAM;AACpC,UAAI,SAAU,UAAS,QAAQ;AAAA,UAC1B,YAAW;AAChB,iBAAW;AAAA,IACb;AACA,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,UAAM;AAAA,EACR;AAAA,EACA,MAAM,WAAW;AACf,QAAI,KAAK,OAAO;AACd,WAAK,QAAQ;AACb,WAAK,QAAQ;AACb,YAAM;AAAA,IACR;AAAA,EACF;AACF;AAEO,SAAS,MAAM,UAAU,OAAO,MAAM;AAC3C,MAAI,IAAI,IAAI;AACZ,IAAE,QAAQ,UAAU,OAAO,IAAI;AAC/B,SAAO;AACT;AAEO,SAAS,aAAa;AAC3B,MAAI;AACJ,IAAE;AACF,MAAI,IAAI,UAAU;AAClB,SAAO,GAAG;AACR,SAAK,IAAI,WAAW,EAAE,UAAU,EAAG,GAAE,MAAM,KAAK,QAAW,CAAC;AAC5D,QAAI,EAAE;AAAA,EACR;AACA,IAAE;AACJ;AAEA,SAAS,OAAO;AACd,cAAY,YAAY,MAAM,IAAI,KAAK;AACvC,UAAQ,UAAU;AAClB,MAAI;AACF,eAAW;AAAA,EACb,UAAE;AACA,YAAQ;AACR,QAAI;AACJ,eAAW;AAAA,EACb;AACF;AAEA,SAAS,OAAO;AACd,MAAIC,OAAM,MAAM,IAAI,GAAG,QAAQA,OAAM;AACrC,MAAI,QAAQ,UAAW,cAAa,OAAO,YAAYA;AACzD;AAEA,SAAS,MAAM;AACb,MAAI,IAAI,KAAK,UAAU,IAAI,OAAO;AAClC,SAAO,IAAI;AACT,QAAI,GAAG,OAAO;AACZ,UAAI,OAAO,GAAG,MAAO,QAAO,GAAG;AAC/B,WAAK,IAAI,KAAK,GAAG;AAAA,IACnB,OAAO;AACL,WAAK,GAAG,OAAO,GAAG,QAAQ;AAC1B,WAAK,KAAK,GAAG,QAAQ,KAAK,WAAW;AAAA,IACvC;AAAA,EACF;AACA,aAAW;AACX,QAAM,IAAI;AACZ;AAEA,SAAS,MAAM,MAAM;AACnB,MAAI,MAAO;AACX,MAAI,QAAS,WAAU,aAAa,OAAO;AAC3C,MAAI,QAAQ,OAAO;AACnB,MAAI,QAAQ,IAAI;AACd,QAAI,OAAO,SAAU,WAAU,WAAW,MAAM,OAAO,MAAM,IAAI,IAAI,SAAS;AAC9E,QAAI,SAAU,YAAW,cAAc,QAAQ;AAAA,EACjD,OAAO;AACL,QAAI,CAAC,SAAU,aAAY,MAAM,IAAI,GAAG,WAAW,YAAY,MAAM,SAAS;AAC9E,YAAQ,GAAG,SAAS,IAAI;AAAA,EAC1B;AACF;;;AC5GA,IAAM,IAAI;AACV,IAAM,IAAI;AACV,IAAM,IAAI;AAEK,SAAR,cAAmB;AACxB,MAAI,IAAI;AACR,SAAO,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK;AACvC;;;ACJO,SAASC,GAAE,GAAG;AACnB,SAAO,EAAE;AACX;AAEO,SAASC,GAAE,GAAG;AACnB,SAAO,EAAE;AACX;AAEA,IAAI,gBAAgB;AAApB,IACI,eAAe,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC;AAE9B,SAAR,mBAAiB,OAAO;AAC7B,MAAI,YACA,QAAQ,GACR,WAAW,MACX,aAAa,IAAI,KAAK,IAAI,UAAU,IAAI,GAAG,GAC3C,cAAc,GACd,gBAAgB,KAChB,SAAS,oBAAI,IAAI,GACjB,UAAU,MAAM,IAAI,GACpB,QAAQ,iBAAS,QAAQ,KAAK,GAC9B,SAAS,YAAI;AAEjB,MAAI,SAAS,KAAM,SAAQ,CAAC;AAE5B,WAAS,OAAO;AACd,SAAK;AACL,UAAM,KAAK,QAAQ,UAAU;AAC7B,QAAI,QAAQ,UAAU;AACpB,cAAQ,KAAK;AACb,YAAM,KAAK,OAAO,UAAU;AAAA,IAC9B;AAAA,EACF;AAEA,WAAS,KAAK,YAAY;AACxB,QAAI,GAAG,IAAI,MAAM,QAAQ;AAEzB,QAAI,eAAe,OAAW,cAAa;AAE3C,aAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,gBAAU,cAAc,SAAS;AAEjC,aAAO,QAAQ,SAAS,OAAO;AAC7B,cAAM,KAAK;AAAA,MACb,CAAC;AAED,WAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,eAAO,MAAM,CAAC;AACd,YAAI,KAAK,MAAM,KAAM,MAAK,KAAK,KAAK,MAAM;AAAA,YACrC,MAAK,IAAI,KAAK,IAAI,KAAK,KAAK;AACjC,YAAI,KAAK,MAAM,KAAM,MAAK,KAAK,KAAK,MAAM;AAAA,YACrC,MAAK,IAAI,KAAK,IAAI,KAAK,KAAK;AAAA,MACnC;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,kBAAkB;AACzB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AAClD,aAAO,MAAM,CAAC,GAAG,KAAK,QAAQ;AAC9B,UAAI,KAAK,MAAM,KAAM,MAAK,IAAI,KAAK;AACnC,UAAI,KAAK,MAAM,KAAM,MAAK,IAAI,KAAK;AACnC,UAAI,MAAM,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,GAAG;AAClC,YAAI,SAAS,gBAAgB,KAAK,KAAK,MAAM,CAAC,GAAG,QAAQ,IAAI;AAC7D,aAAK,IAAI,SAAS,KAAK,IAAI,KAAK;AAChC,aAAK,IAAI,SAAS,KAAK,IAAI,KAAK;AAAA,MAClC;AACA,UAAI,MAAM,KAAK,EAAE,KAAK,MAAM,KAAK,EAAE,GAAG;AACpC,aAAK,KAAK,KAAK,KAAK;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAEA,WAAS,gBAAgB,OAAO;AAC9B,QAAI,MAAM,WAAY,OAAM,WAAW,OAAO,MAAM;AACpD,WAAO;AAAA,EACT;AAEA,kBAAgB;AAEhB,SAAO,aAAa;AAAA,IAClB;AAAA,IAEA,SAAS,WAAW;AAClB,aAAO,QAAQ,QAAQ,IAAI,GAAG;AAAA,IAChC;AAAA,IAEA,MAAM,WAAW;AACf,aAAO,QAAQ,KAAK,GAAG;AAAA,IACzB;AAAA,IAEA,OAAO,SAAS,GAAG;AACjB,aAAO,UAAU,UAAU,QAAQ,GAAG,gBAAgB,GAAG,OAAO,QAAQ,eAAe,GAAG,cAAc;AAAA,IAC1G;AAAA,IAEA,OAAO,SAAS,GAAG;AACjB,aAAO,UAAU,UAAU,QAAQ,CAAC,GAAG,cAAc;AAAA,IACvD;AAAA,IAEA,UAAU,SAAS,GAAG;AACpB,aAAO,UAAU,UAAU,WAAW,CAAC,GAAG,cAAc;AAAA,IAC1D;AAAA,IAEA,YAAY,SAAS,GAAG;AACtB,aAAO,UAAU,UAAU,aAAa,CAAC,GAAG,cAAc,CAAC;AAAA,IAC7D;AAAA,IAEA,aAAa,SAAS,GAAG;AACvB,aAAO,UAAU,UAAU,cAAc,CAAC,GAAG,cAAc;AAAA,IAC7D;AAAA,IAEA,eAAe,SAAS,GAAG;AACzB,aAAO,UAAU,UAAU,gBAAgB,IAAI,GAAG,cAAc,IAAI;AAAA,IACtE;AAAA,IAEA,cAAc,SAAS,GAAG;AACxB,aAAO,UAAU,UAAU,SAAS,GAAG,OAAO,QAAQ,eAAe,GAAG,cAAc;AAAA,IACxF;AAAA,IAEA,OAAO,SAAS,MAAM,GAAG;AACvB,aAAO,UAAU,SAAS,KAAM,KAAK,OAAO,OAAO,OAAO,IAAI,IAAI,OAAO,IAAI,MAAM,gBAAgB,CAAC,CAAC,GAAI,cAAc,OAAO,IAAI,IAAI;AAAA,IACxI;AAAA,IAEA,MAAM,SAASD,IAAGC,IAAG,QAAQ;AAC3B,UAAI,IAAI,GACJ,IAAI,MAAM,QACV,IACA,IACA,IACA,MACA;AAEJ,UAAI,UAAU,KAAM,UAAS;AAAA,UACxB,WAAU;AAEf,WAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,eAAO,MAAM,CAAC;AACd,aAAKD,KAAI,KAAK;AACd,aAAKC,KAAI,KAAK;AACd,aAAK,KAAK,KAAK,KAAK;AACpB,YAAI,KAAK,OAAQ,WAAU,MAAM,SAAS;AAAA,MAC5C;AAEA,aAAO;AAAA,IACT;AAAA,IAEA,IAAI,SAAS,MAAM,GAAG;AACpB,aAAO,UAAU,SAAS,KAAK,MAAM,GAAG,MAAM,CAAC,GAAG,cAAc,MAAM,GAAG,IAAI;AAAA,IAC/E;AAAA,EACF;AACF;;;ACtJe,SAAR,mBAAmB;AACxB,MAAI,OACA,MACA,QACA,OACA,WAAW,iBAAS,GAAG,GACvB,WACA,eAAe,GACf,eAAe,UACf,SAAS;AAEb,WAAS,MAAM,GAAG;AAChB,QAAI,GAAG,IAAI,MAAM,QAAQ,OAAO,SAAS,OAAOC,IAAGC,EAAC,EAAE,WAAW,UAAU;AAC3E,SAAK,QAAQ,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,QAAO,MAAM,CAAC,GAAG,KAAK,MAAM,KAAK;AAAA,EACtE;AAEA,WAAS,aAAa;AACpB,QAAI,CAAC,MAAO;AACZ,QAAI,GAAG,IAAI,MAAM,QAAQC;AACzB,gBAAY,IAAI,MAAM,CAAC;AACvB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,QAAO,MAAM,CAAC,GAAG,UAAUA,MAAK,KAAK,IAAI,CAAC,SAASA,OAAM,GAAG,KAAK;AAAA,EAC3F;AAEA,WAAS,WAAW,MAAM;AACxB,QAAIC,YAAW,GAAG,GAAGC,IAAG,SAAS,GAAGJ,IAAGC,IAAG;AAG1C,QAAI,KAAK,QAAQ;AACf,WAAKD,KAAIC,KAAI,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9B,aAAK,IAAI,KAAK,CAAC,OAAOG,KAAI,KAAK,IAAI,EAAE,KAAK,IAAI;AAC5C,UAAAD,aAAY,EAAE,OAAO,UAAUC,IAAGJ,MAAKI,KAAI,EAAE,GAAGH,MAAKG,KAAI,EAAE;AAAA,QAC7D;AAAA,MACF;AACA,WAAK,IAAIJ,KAAI;AACb,WAAK,IAAIC,KAAI;AAAA,IACf,OAGK;AACH,UAAI;AACJ,QAAE,IAAI,EAAE,KAAK;AACb,QAAE,IAAI,EAAE,KAAK;AACb;AAAG,QAAAE,aAAY,UAAU,EAAE,KAAK,KAAK;AAAA,aAC9B,IAAI,EAAE;AAAA,IACf;AAEA,SAAK,QAAQA;AAAA,EACf;AAEA,WAAS,MAAM,MAAM,IAAI,GAAGE,KAAI;AAC9B,QAAI,CAAC,KAAK,MAAO,QAAO;AAExB,QAAIL,KAAI,KAAK,IAAI,KAAK,GAClBC,KAAI,KAAK,IAAI,KAAK,GAClB,IAAII,MAAK,IACT,IAAIL,KAAIA,KAAIC,KAAIA;AAIpB,QAAI,IAAI,IAAI,SAAS,GAAG;AACtB,UAAI,IAAI,cAAc;AACpB,YAAID,OAAM,EAAG,CAAAA,KAAI,eAAO,MAAM,GAAG,KAAKA,KAAIA;AAC1C,YAAIC,OAAM,EAAG,CAAAA,KAAI,eAAO,MAAM,GAAG,KAAKA,KAAIA;AAC1C,YAAI,IAAI,aAAc,KAAI,KAAK,KAAK,eAAe,CAAC;AACpD,aAAK,MAAMD,KAAI,KAAK,QAAQ,QAAQ;AACpC,aAAK,MAAMC,KAAI,KAAK,QAAQ,QAAQ;AAAA,MACtC;AACA,aAAO;AAAA,IACT,WAGS,KAAK,UAAU,KAAK,aAAc;AAG3C,QAAI,KAAK,SAAS,QAAQ,KAAK,MAAM;AACnC,UAAID,OAAM,EAAG,CAAAA,KAAI,eAAO,MAAM,GAAG,KAAKA,KAAIA;AAC1C,UAAIC,OAAM,EAAG,CAAAA,KAAI,eAAO,MAAM,GAAG,KAAKA,KAAIA;AAC1C,UAAI,IAAI,aAAc,KAAI,KAAK,KAAK,eAAe,CAAC;AAAA,IACtD;AAEA;AAAG,UAAI,KAAK,SAAS,MAAM;AACzB,YAAI,UAAU,KAAK,KAAK,KAAK,IAAI,QAAQ;AACzC,aAAK,MAAMD,KAAI;AACf,aAAK,MAAMC,KAAI;AAAA,MACjB;AAAA,WAAS,OAAO,KAAK;AAAA,EACvB;AAEA,QAAM,aAAa,SAAS,QAAQ,SAAS;AAC3C,YAAQ;AACR,aAAS;AACT,eAAW;AAAA,EACb;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAAS;AAAA,EAC3G;AAEA,QAAM,cAAc,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,eAAe,IAAI,GAAG,SAAS,KAAK,KAAK,YAAY;AAAA,EAClF;AAEA,QAAM,cAAc,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,eAAe,IAAI,GAAG,SAAS,KAAK,KAAK,YAAY;AAAA,EAClF;AAEA,QAAM,QAAQ,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,IAAI,GAAG,SAAS,KAAK,KAAK,MAAM;AAAA,EACtE;AAEA,SAAO;AACT;;;ACjHe,SAAR,eAAiB,QAAQK,IAAGC,IAAG;AACpC,MAAI,OACA,WAAW,iBAAS,GAAG,GACvB,WACA;AAEJ,MAAI,OAAO,WAAW,WAAY,UAAS,iBAAS,CAAC,MAAM;AAC3D,MAAID,MAAK,KAAM,CAAAA,KAAI;AACnB,MAAIC,MAAK,KAAM,CAAAA,KAAI;AAEnB,WAAS,MAAM,OAAO;AACpB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC5C,UAAI,OAAO,MAAM,CAAC,GACd,KAAK,KAAK,IAAID,MAAK,MACnB,KAAK,KAAK,IAAIC,MAAK,MACnB,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE,GAC/B,KAAK,SAAS,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,QAAQ;AACnD,WAAK,MAAM,KAAK;AAChB,WAAK,MAAM,KAAK;AAAA,IAClB;AAAA,EACF;AAEA,WAAS,aAAa;AACpB,QAAI,CAAC,MAAO;AACZ,QAAI,GAAG,IAAI,MAAM;AACjB,gBAAY,IAAI,MAAM,CAAC;AACvB,eAAW,IAAI,MAAM,CAAC;AACtB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,eAAS,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,GAAG,KAAK;AACxC,gBAAU,CAAC,IAAI,MAAM,SAAS,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,MAAM,CAAC,GAAG,GAAG,KAAK;AAAA,IACtE;AAAA,EACF;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,YAAQ,GAAG,WAAW;AAAA,EACxB;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAAS;AAAA,EAC3G;AAEA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAAS;AAAA,EACzG;AAEA,QAAM,IAAI,SAAS,GAAG;AACpB,WAAO,UAAU,UAAUD,KAAI,CAAC,GAAG,SAASA;AAAA,EAC9C;AAEA,QAAM,IAAI,SAAS,GAAG;AACpB,WAAO,UAAU,UAAUC,KAAI,CAAC,GAAG,SAASA;AAAA,EAC9C;AAEA,SAAO;AACT;;;ACtDe,SAAR,UAAiBC,IAAG;AACzB,MAAI,WAAW,iBAAS,GAAG,GACvB,OACA,WACA;AAEJ,MAAI,OAAOA,OAAM,WAAY,CAAAA,KAAI,iBAASA,MAAK,OAAO,IAAI,CAACA,EAAC;AAE5D,WAAS,MAAM,OAAO;AACpB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AAClD,aAAO,MAAM,CAAC,GAAG,KAAK,OAAO,GAAG,CAAC,IAAI,KAAK,KAAK,UAAU,CAAC,IAAI;AAAA,IAChE;AAAA,EACF;AAEA,WAAS,aAAa;AACpB,QAAI,CAAC,MAAO;AACZ,QAAI,GAAG,IAAI,MAAM;AACjB,gBAAY,IAAI,MAAM,CAAC;AACvB,SAAK,IAAI,MAAM,CAAC;AAChB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,gBAAU,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,CAACA,GAAE,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,MAAM,CAAC,GAAG,GAAG,KAAK;AAAA,IACzF;AAAA,EACF;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,YAAQ;AACR,eAAW;AAAA,EACb;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAAS;AAAA,EAC3G;AAEA,QAAM,IAAI,SAAS,GAAG;AACpB,WAAO,UAAU,UAAUA,KAAI,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAASA;AAAA,EACpG;AAEA,SAAO;AACT;;;ACtCe,SAAR,UAAiBC,IAAG;AACzB,MAAI,WAAW,iBAAS,GAAG,GACvB,OACA,WACA;AAEJ,MAAI,OAAOA,OAAM,WAAY,CAAAA,KAAI,iBAASA,MAAK,OAAO,IAAI,CAACA,EAAC;AAE5D,WAAS,MAAM,OAAO;AACpB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AAClD,aAAO,MAAM,CAAC,GAAG,KAAK,OAAO,GAAG,CAAC,IAAI,KAAK,KAAK,UAAU,CAAC,IAAI;AAAA,IAChE;AAAA,EACF;AAEA,WAAS,aAAa;AACpB,QAAI,CAAC,MAAO;AACZ,QAAI,GAAG,IAAI,MAAM;AACjB,gBAAY,IAAI,MAAM,CAAC;AACvB,SAAK,IAAI,MAAM,CAAC;AAChB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,gBAAU,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,CAACA,GAAE,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,MAAM,CAAC,GAAG,GAAG,KAAK;AAAA,IACzF;AAAA,EACF;AAEA,QAAM,aAAa,SAAS,GAAG;AAC7B,YAAQ;AACR,eAAW;AAAA,EACb;AAEA,QAAM,WAAW,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAAS;AAAA,EAC3G;AAEA,QAAM,IAAI,SAAS,GAAG;AACpB,WAAO,UAAU,UAAUA,KAAI,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,WAAW,GAAG,SAASA;AAAA,EACpG;AAEA,SAAO;AACT;", "names": ["x", "y", "x", "x", "y", "x", "y", "m", "i", "c", "now", "x", "y", "x", "y", "node", "strength", "c", "x2", "x", "y", "x", "y"]}