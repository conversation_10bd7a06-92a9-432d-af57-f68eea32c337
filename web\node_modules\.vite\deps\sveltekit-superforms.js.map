{"version": 3, "sources": ["../../sveltekit-superforms/dist/index.js"], "sourcesContent": ["import SuperDebug from './client/SuperDebug.svelte';\nexport default SuperDebug;\nexport { SuperFormError, SchemaError } from './errors.js';\n// Everything from client/index.ts\nexport { superForm, intProxy, numberProxy, booleanProxy, dateProxy, fieldProxy, formFieldProxy, stringProxy, arrayProxy, fileProxy, fileFieldProxy, filesProxy, filesFieldProxy, defaults, defaultValues, schemaShape, actionResult, superValidate, message, setMessage, setError, withFiles, removeFiles, fail } from './client/index.js';\nexport { splitPath } from './stringPath.js';\nexport { mergeFormUnion } from './utils.js';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAO,eAAQ;", "names": []}