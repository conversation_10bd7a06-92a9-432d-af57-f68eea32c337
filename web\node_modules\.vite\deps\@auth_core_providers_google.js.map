{"version": 3, "sources": ["../../@auth/core/providers/google.js"], "sourcesContent": ["/**\n * Add Google login to your page.\n *\n * ### Setup\n *\n * #### Callback URL\n * ```\n * https://example.com/api/auth/callback/google\n * ```\n *\n * #### Configuration\n *```ts\n * import { Auth } from \"@auth/core\"\n * import <PERSON> from \"@auth/core/providers/google\"\n *\n * const request = new Request(origin)\n * const response = await Auth(request, {\n *   providers: [\n *     Google({ clientId: GOOGLE_CLIENT_ID, clientSecret: GOOGLE_CLIENT_SECRET }),\n *   ],\n * })\n * ```\n *\n * ### Resources\n *\n *  - [Google OAuth documentation](https://developers.google.com/identity/protocols/oauth2)\n *  - [Google OAuth Configuration](https://console.developers.google.com/apis/credentials)\n *\n * ### Notes\n *\n * By default, Auth.js assumes that the Google provider is\n * based on the [Open ID Connect](https://openid.net/specs/openid-connect-core-1_0.html) specification.\n *\n *\n * The \"Authorized redirect URIs\" used when creating the credentials must include your full domain and end in the callback path. For example;\n *\n * - For production: `https://{YOUR_DOMAIN}/api/auth/callback/google`\n * - For development: `http://localhost:3000/api/auth/callback/google`\n *\n * :::warning\n * Google only provides Refresh Token to an application the first time a user signs in.\n *\n * To force Google to re-issue a Refresh Token, the user needs to remove the application from their account and sign in again:\n * https://myaccount.google.com/permissions\n *\n * Alternatively, you can also pass options in the `params` object of `authorization` which will force the Refresh Token to always be provided on sign in, however this will ask all users to confirm if they wish to grant your application access every time they sign in.\n *\n * If you need access to the RefreshToken or AccessToken for a Google account and you are not using a database to persist user accounts, this may be something you need to do.\n *\n * ```ts\n * const options = {\n *   providers: [\n *     Google({\n *       clientId: process.env.GOOGLE_ID,\n *       clientSecret: process.env.GOOGLE_SECRET,\n *       authorization: {\n *         params: {\n *           prompt: \"consent\",\n *           access_type: \"offline\",\n *           response_type: \"code\"\n *         }\n *       }\n *     })\n *   ],\n * }\n * ```\n *\n * :::\n *\n * :::tip\n * Google also returns a `email_verified` boolean property in the OAuth profile.\n *\n * You can use this property to restrict access to people with verified accounts at a particular domain.\n *\n * ```ts\n * const options = {\n *   ...\n *   callbacks: {\n *     async signIn({ account, profile }) {\n *       if (account.provider === \"google\") {\n *         return profile.email_verified && profile.email.endsWith(\"@example.com\")\n *       }\n *       return true // Do different verification for other providers that don't have `email_verified`\n *     },\n *   }\n *   ...\n * }\n * ```\n *\n * :::\n * :::tip\n *\n * The Google provider comes with a [default configuration](https://github.com/nextauthjs/next-auth/blob/main/packages/core/src/providers/google.ts).\n * To override the defaults for your use case, check out [customizing a built-in OAuth provider](https://authjs.dev/guides/configuring-oauth-providers).\n *\n * :::\n *\n * :::info **Disclaimer**\n *\n * If you think you found a bug in the default configuration, you can [open an issue](https://authjs.dev/new/provider-issue).\n *\n * Auth.js strictly adheres to the specification and it cannot take responsibility for any deviation from\n * the spec by the provider. You can open an issue, but if the problem is non-compliance with the spec,\n * we might not pursue a resolution. You can ask for more help in [Discussions](https://authjs.dev/new/github-discussions).\n *\n * :::\n */\nexport default function Google(options) {\n    return {\n        id: \"google\",\n        name: \"Google\",\n        type: \"oidc\",\n        issuer: \"https://accounts.google.com\",\n        style: {\n            brandColor: \"#1a73e8\",\n        },\n        options,\n    };\n}\n"], "mappings": ";;;AA2Ge,SAAR,OAAwB,SAAS;AACpC,SAAO;AAAA,IACH,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,OAAO;AAAA,MACH,YAAY;AAAA,IAChB;AAAA,IACA;AAAA,EACJ;AACJ;", "names": []}