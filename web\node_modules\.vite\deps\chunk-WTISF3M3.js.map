{"version": 3, "sources": ["../../robust-predicates/esm/util.js", "../../robust-predicates/esm/orient2d.js", "../../robust-predicates/esm/orient3d.js", "../../robust-predicates/esm/incircle.js", "../../robust-predicates/esm/insphere.js", "../../delaunator/index.js", "../../d3-delaunay/src/path.js", "../../d3-delaunay/src/polygon.js", "../../d3-delaunay/src/voronoi.js", "../../d3-delaunay/src/delaunay.js"], "sourcesContent": ["export const epsilon = 1.1102230246251565e-16;\nexport const splitter = 134217729;\nexport const resulterrbound = (3 + 8 * epsilon) * epsilon;\n\n// fast_expansion_sum_zeroelim routine from oritinal code\nexport function sum(elen, e, flen, f, h) {\n    let Q, Qnew, hh, bvirt;\n    let enow = e[0];\n    let fnow = f[0];\n    let eindex = 0;\n    let findex = 0;\n    if ((fnow > enow) === (fnow > -enow)) {\n        Q = enow;\n        enow = e[++eindex];\n    } else {\n        Q = fnow;\n        fnow = f[++findex];\n    }\n    let hindex = 0;\n    if (eindex < elen && findex < flen) {\n        if ((fnow > enow) === (fnow > -enow)) {\n            Qnew = enow + Q;\n            hh = Q - (Qnew - enow);\n            enow = e[++eindex];\n        } else {\n            Qnew = fnow + Q;\n            hh = Q - (Qnew - fnow);\n            fnow = f[++findex];\n        }\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        while (eindex < elen && findex < flen) {\n            if ((fnow > enow) === (fnow > -enow)) {\n                Qnew = Q + enow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (enow - bvirt);\n                enow = e[++eindex];\n            } else {\n                Qnew = Q + fnow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n                fnow = f[++findex];\n            }\n            Q = Qnew;\n            if (hh !== 0) {\n                h[hindex++] = hh;\n            }\n        }\n    }\n    while (eindex < elen) {\n        Qnew = Q + enow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (enow - bvirt);\n        enow = e[++eindex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    while (findex < flen) {\n        Qnew = Q + fnow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n        fnow = f[++findex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nexport function sum_three(alen, a, blen, b, clen, c, tmp, out) {\n    return sum(sum(alen, a, blen, b, tmp), tmp, clen, c, out);\n}\n\n// scale_expansion_zeroelim routine from oritinal code\nexport function scale(elen, e, b, h) {\n    let Q, sum, hh, product1, product0;\n    let bvirt, c, ahi, alo, bhi, blo;\n\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    let enow = e[0];\n    Q = enow * b;\n    c = splitter * enow;\n    ahi = c - (c - enow);\n    alo = enow - ahi;\n    hh = alo * blo - (Q - ahi * bhi - alo * bhi - ahi * blo);\n    let hindex = 0;\n    if (hh !== 0) {\n        h[hindex++] = hh;\n    }\n    for (let i = 1; i < elen; i++) {\n        enow = e[i];\n        product1 = enow * b;\n        c = splitter * enow;\n        ahi = c - (c - enow);\n        alo = enow - ahi;\n        product0 = alo * blo - (product1 - ahi * bhi - alo * bhi - ahi * blo);\n        sum = Q + product0;\n        bvirt = sum - Q;\n        hh = Q - (sum - bvirt) + (product0 - bvirt);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        Q = product1 + sum;\n        hh = sum - (Q - product1);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nexport function negate(elen, e) {\n    for (let i = 0; i < elen; i++) e[i] = -e[i];\n    return elen;\n}\n\nexport function estimate(elen, e) {\n    let Q = e[0];\n    for (let i = 1; i < elen; i++) Q += e[i];\n    return Q;\n}\n\nexport function vec(n) {\n    return new Float64Array(n);\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum} from './util.js';\n\nconst ccwerrboundA = (3 + 16 * epsilon) * epsilon;\nconst ccwerrboundB = (2 + 12 * epsilon) * epsilon;\nconst ccwerrboundC = (9 + 64 * epsilon) * epsilon * epsilon;\n\nconst B = vec(4);\nconst C1 = vec(8);\nconst C2 = vec(12);\nconst D = vec(16);\nconst u = vec(4);\n\nfunction orient2dadapt(ax, ay, bx, by, cx, cy, detsum) {\n    let acxtail, acytail, bcxtail, bcytail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const acx = ax - cx;\n    const bcx = bx - cx;\n    const acy = ay - cy;\n    const bcy = by - cy;\n\n    s1 = acx * bcy;\n    c = splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcx;\n    c = splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    B[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    B[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    B[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    B[3] = u3;\n\n    let det = estimate(4, B);\n    let errbound = ccwerrboundB * detsum;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - acx;\n    acxtail = ax - (acx + bvirt) + (bvirt - cx);\n    bvirt = bx - bcx;\n    bcxtail = bx - (bcx + bvirt) + (bvirt - cx);\n    bvirt = ay - acy;\n    acytail = ay - (acy + bvirt) + (bvirt - cy);\n    bvirt = by - bcy;\n    bcytail = by - (bcy + bvirt) + (bvirt - cy);\n\n    if (acxtail === 0 && acytail === 0 && bcxtail === 0 && bcytail === 0) {\n        return det;\n    }\n\n    errbound = ccwerrboundC * detsum + resulterrbound * Math.abs(det);\n    det += (acx * bcytail + bcy * acxtail) - (acy * bcxtail + bcx * acytail);\n    if (det >= errbound || -det >= errbound) return det;\n\n    s1 = acxtail * bcy;\n    c = splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = splitter * bcy;\n    bhi = c - (c - bcy);\n    blo = bcy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcx;\n    c = splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = splitter * bcx;\n    bhi = c - (c - bcx);\n    blo = bcx - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C1len = sum(4, B, 4, u, C1);\n\n    s1 = acx * bcytail;\n    c = splitter * acx;\n    ahi = c - (c - acx);\n    alo = acx - ahi;\n    c = splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acy * bcxtail;\n    c = splitter * acy;\n    ahi = c - (c - acy);\n    alo = acy - ahi;\n    c = splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const C2len = sum(C1len, C1, 4, u, C2);\n\n    s1 = acxtail * bcytail;\n    c = splitter * acxtail;\n    ahi = c - (c - acxtail);\n    alo = acxtail - ahi;\n    c = splitter * bcytail;\n    bhi = c - (c - bcytail);\n    blo = bcytail - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = acytail * bcxtail;\n    c = splitter * acytail;\n    ahi = c - (c - acytail);\n    alo = acytail - ahi;\n    c = splitter * bcxtail;\n    bhi = c - (c - bcxtail);\n    blo = bcxtail - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    u[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    u[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    u[3] = u3;\n    const Dlen = sum(C2len, C2, 4, u, D);\n\n    return D[Dlen - 1];\n}\n\nexport function orient2d(ax, ay, bx, by, cx, cy) {\n    const detleft = (ay - cy) * (bx - cx);\n    const detright = (ax - cx) * (by - cy);\n    const det = detleft - detright;\n\n    const detsum = Math.abs(detleft + detright);\n    if (Math.abs(det) >= ccwerrboundA * detsum) return det;\n\n    return -orient2dadapt(ax, ay, bx, by, cx, cy, detsum);\n}\n\nexport function orient2dfast(ax, ay, bx, by, cx, cy) {\n    return (ay - cy) * (bx - cx) - (ax - cx) * (by - cy);\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum, scale} from './util.js';\n\nconst o3derrboundA = (7 + 56 * epsilon) * epsilon;\nconst o3derrboundB = (3 + 28 * epsilon) * epsilon;\nconst o3derrboundC = (26 + 288 * epsilon) * epsilon * epsilon;\n\nconst bc = vec(4);\nconst ca = vec(4);\nconst ab = vec(4);\nconst at_b = vec(4);\nconst at_c = vec(4);\nconst bt_c = vec(4);\nconst bt_a = vec(4);\nconst ct_a = vec(4);\nconst ct_b = vec(4);\nconst bct = vec(8);\nconst cat = vec(8);\nconst abt = vec(8);\nconst u = vec(4);\n\nconst _8 = vec(8);\nconst _8b = vec(8);\nconst _16 = vec(8);\nconst _12 = vec(12);\n\nlet fin = vec(192);\nlet fin2 = vec(192);\n\nfunction finadd(finlen, alen, a) {\n    finlen = sum(finlen, fin, alen, a, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction tailinit(xtail, ytail, ax, ay, bx, by, a, b) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3, negate;\n    if (xtail === 0) {\n        if (ytail === 0) {\n            a[0] = 0;\n            b[0] = 0;\n            return 1;\n        } else {\n            negate = -ytail;\n            s1 = negate * ax;\n            c = splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            s1 = ytail * bx;\n            c = splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        }\n    } else {\n        if (ytail === 0) {\n            s1 = xtail * ay;\n            c = splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            a[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            a[1] = s1;\n            negate = -xtail;\n            s1 = negate * by;\n            c = splitter * negate;\n            ahi = c - (c - negate);\n            alo = negate - ahi;\n            c = splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            b[0] = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            b[1] = s1;\n            return 2;\n        } else {\n            s1 = xtail * ay;\n            c = splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = splitter * ay;\n            bhi = c - (c - ay);\n            blo = ay - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = ytail * ax;\n            c = splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = splitter * ax;\n            bhi = c - (c - ax);\n            blo = ax - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            a[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            a[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            a[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            a[3] = u3;\n            s1 = ytail * bx;\n            c = splitter * ytail;\n            ahi = c - (c - ytail);\n            alo = ytail - ahi;\n            c = splitter * bx;\n            bhi = c - (c - bx);\n            blo = bx - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = xtail * by;\n            c = splitter * xtail;\n            ahi = c - (c - xtail);\n            alo = xtail - ahi;\n            c = splitter * by;\n            bhi = c - (c - by);\n            blo = by - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            b[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            b[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            b[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            b[3] = u3;\n            return 4;\n        }\n    }\n}\n\nfunction tailadd(finlen, a, b, k, z) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, u3;\n    s1 = a * b;\n    c = splitter * a;\n    ahi = c - (c - a);\n    alo = a - ahi;\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    c = splitter * k;\n    bhi = c - (c - k);\n    blo = k - bhi;\n    _i = s0 * k;\n    c = splitter * s0;\n    ahi = c - (c - s0);\n    alo = s0 - ahi;\n    u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n    _j = s1 * k;\n    c = splitter * s1;\n    ahi = c - (c - s1);\n    alo = s1 - ahi;\n    _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n    _k = _i + _0;\n    bvirt = _k - _i;\n    u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n    u3 = _j + _k;\n    u[2] = _k - (u3 - _j);\n    u[3] = u3;\n    finlen = finadd(finlen, 4, u);\n    if (z !== 0) {\n        c = splitter * z;\n        bhi = c - (c - z);\n        blo = z - bhi;\n        _i = s0 * z;\n        c = splitter * s0;\n        ahi = c - (c - s0);\n        alo = s0 - ahi;\n        u[0] = alo * blo - (_i - ahi * bhi - alo * bhi - ahi * blo);\n        _j = s1 * z;\n        c = splitter * s1;\n        ahi = c - (c - s1);\n        alo = s1 - ahi;\n        _0 = alo * blo - (_j - ahi * bhi - alo * bhi - ahi * blo);\n        _k = _i + _0;\n        bvirt = _k - _i;\n        u[1] = _i - (_k - bvirt) + (_0 - bvirt);\n        u3 = _j + _k;\n        u[2] = _k - (u3 - _j);\n        u[3] = u3;\n        finlen = finadd(finlen, 4, u);\n    }\n    return finlen;\n}\n\nfunction orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail;\n    let adytail, bdytail, cdytail;\n    let adztail, bdztail, cdztail;\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _k, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    s1 = bdx * cdy;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = sum(\n        sum(\n            scale(4, bc, adz, _8), _8,\n            scale(4, ca, bdz, _8b), _8b, _16), _16,\n        scale(4, ab, cdz, _8), _8, fin);\n\n    let det = estimate(finlen, fin);\n    let errbound = o3derrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    bvirt = az - adz;\n    adztail = az - (adz + bvirt) + (bvirt - dz);\n    bvirt = bz - bdz;\n    bdztail = bz - (bdz + bvirt) + (bvirt - dz);\n    bvirt = cz - cdz;\n    cdztail = cz - (cdz + bvirt) + (bvirt - dz);\n\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 &&\n        adytail === 0 && bdytail === 0 && cdytail === 0 &&\n        adztail === 0 && bdztail === 0 && cdztail === 0) {\n        return det;\n    }\n\n    errbound = o3derrboundC * permanent + resulterrbound * Math.abs(det);\n    det +=\n        adz * (bdx * cdytail + cdy * bdxtail - (bdy * cdxtail + cdx * bdytail)) + adztail * (bdx * cdy - bdy * cdx) +\n        bdz * (cdx * adytail + ady * cdxtail - (cdy * adxtail + adx * cdytail)) + bdztail * (cdx * ady - cdy * adx) +\n        cdz * (adx * bdytail + bdy * adxtail - (ady * bdxtail + bdx * adytail)) + cdztail * (adx * bdy - ady * bdx);\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    const at_len = tailinit(adxtail, adytail, bdx, bdy, cdx, cdy, at_b, at_c);\n    const bt_len = tailinit(bdxtail, bdytail, cdx, cdy, adx, ady, bt_c, bt_a);\n    const ct_len = tailinit(cdxtail, cdytail, adx, ady, bdx, bdy, ct_a, ct_b);\n\n    const bctlen = sum(bt_len, bt_c, ct_len, ct_b, bct);\n    finlen = finadd(finlen, scale(bctlen, bct, adz, _16), _16);\n\n    const catlen = sum(ct_len, ct_a, at_len, at_c, cat);\n    finlen = finadd(finlen, scale(catlen, cat, bdz, _16), _16);\n\n    const abtlen = sum(at_len, at_b, bt_len, bt_a, abt);\n    finlen = finadd(finlen, scale(abtlen, abt, cdz, _16), _16);\n\n    if (adztail !== 0) {\n        finlen = finadd(finlen, scale(4, bc, adztail, _12), _12);\n        finlen = finadd(finlen, scale(bctlen, bct, adztail, _16), _16);\n    }\n    if (bdztail !== 0) {\n        finlen = finadd(finlen, scale(4, ca, bdztail, _12), _12);\n        finlen = finadd(finlen, scale(catlen, cat, bdztail, _16), _16);\n    }\n    if (cdztail !== 0) {\n        finlen = finadd(finlen, scale(4, ab, cdztail, _12), _12);\n        finlen = finadd(finlen, scale(abtlen, abt, cdztail, _16), _16);\n    }\n\n    if (adxtail !== 0) {\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, adxtail, bdytail, cdz, cdztail);\n        }\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, -adxtail, cdytail, bdz, bdztail);\n        }\n    }\n    if (bdxtail !== 0) {\n        if (cdytail !== 0) {\n            finlen = tailadd(finlen, bdxtail, cdytail, adz, adztail);\n        }\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, -bdxtail, adytail, cdz, cdztail);\n        }\n    }\n    if (cdxtail !== 0) {\n        if (adytail !== 0) {\n            finlen = tailadd(finlen, cdxtail, adytail, bdz, bdztail);\n        }\n        if (bdytail !== 0) {\n            finlen = tailadd(finlen, -cdxtail, bdytail, adz, adztail);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nexport function orient3d(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n\n    const det =\n        adz * (bdxcdy - cdxbdy) +\n        bdz * (cdxady - adxcdy) +\n        cdz * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * Math.abs(adz) +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * Math.abs(bdz) +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * Math.abs(cdz);\n\n    const errbound = o3derrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n\n    return orient3dadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, permanent);\n}\n\nexport function orient3dfast(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n    const adz = az - dz;\n    const bdz = bz - dz;\n    const cdz = cz - dz;\n\n    return adx * (bdy * cdz - bdz * cdy) +\n        bdx * (cdy * adz - cdz * ady) +\n        cdx * (ady * bdz - adz * bdy);\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum, sum_three, scale} from './util.js';\n\nconst iccerrboundA = (10 + 96 * epsilon) * epsilon;\nconst iccerrboundB = (4 + 48 * epsilon) * epsilon;\nconst iccerrboundC = (44 + 576 * epsilon) * epsilon * epsilon;\n\nconst bc = vec(4);\nconst ca = vec(4);\nconst ab = vec(4);\nconst aa = vec(4);\nconst bb = vec(4);\nconst cc = vec(4);\nconst u = vec(4);\nconst v = vec(4);\nconst axtbc = vec(8);\nconst aytbc = vec(8);\nconst bxtca = vec(8);\nconst bytca = vec(8);\nconst cxtab = vec(8);\nconst cytab = vec(8);\nconst abt = vec(8);\nconst bct = vec(8);\nconst cat = vec(8);\nconst abtt = vec(4);\nconst bctt = vec(4);\nconst catt = vec(4);\n\nconst _8 = vec(8);\nconst _16 = vec(16);\nconst _16b = vec(16);\nconst _16c = vec(16);\nconst _32 = vec(32);\nconst _32b = vec(32);\nconst _48 = vec(48);\nconst _64 = vec(64);\n\nlet fin = vec(1152);\nlet fin2 = vec(1152);\n\nfunction finadd(finlen, a, alen) {\n    finlen = sum(finlen, fin, a, alen, fin2);\n    const tmp = fin; fin = fin2; fin2 = tmp;\n    return finlen;\n}\n\nfunction incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent) {\n    let finlen;\n    let adxtail, bdxtail, cdxtail, adytail, bdytail, cdytail;\n    let axtbclen, aytbclen, bxtcalen, bytcalen, cxtablen, cytablen;\n    let abtlen, bctlen, catlen;\n    let abttlen, bcttlen, cattlen;\n    let n1, n0;\n\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    s1 = bdx * cdy;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cdx * bdy;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cdx * ady;\n    c = splitter * cdx;\n    ahi = c - (c - cdx);\n    alo = cdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = adx * cdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * cdy;\n    bhi = c - (c - cdy);\n    blo = cdy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ca[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ca[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ca[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ca[3] = u3;\n    s1 = adx * bdy;\n    c = splitter * adx;\n    ahi = c - (c - adx);\n    alo = adx - ahi;\n    c = splitter * bdy;\n    bhi = c - (c - bdy);\n    blo = bdy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bdx * ady;\n    c = splitter * bdx;\n    ahi = c - (c - bdx);\n    alo = bdx - ahi;\n    c = splitter * ady;\n    bhi = c - (c - ady);\n    blo = ady - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n\n    finlen = sum(\n        sum(\n            sum(\n                scale(scale(4, bc, adx, _8), _8, adx, _16), _16,\n                scale(scale(4, bc, ady, _8), _8, ady, _16b), _16b, _32), _32,\n            sum(\n                scale(scale(4, ca, bdx, _8), _8, bdx, _16), _16,\n                scale(scale(4, ca, bdy, _8), _8, bdy, _16b), _16b, _32b), _32b, _64), _64,\n        sum(\n            scale(scale(4, ab, cdx, _8), _8, cdx, _16), _16,\n            scale(scale(4, ab, cdy, _8), _8, cdy, _16b), _16b, _32), _32, fin);\n\n    let det = estimate(finlen, fin);\n    let errbound = iccerrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - adx;\n    adxtail = ax - (adx + bvirt) + (bvirt - dx);\n    bvirt = ay - ady;\n    adytail = ay - (ady + bvirt) + (bvirt - dy);\n    bvirt = bx - bdx;\n    bdxtail = bx - (bdx + bvirt) + (bvirt - dx);\n    bvirt = by - bdy;\n    bdytail = by - (bdy + bvirt) + (bvirt - dy);\n    bvirt = cx - cdx;\n    cdxtail = cx - (cdx + bvirt) + (bvirt - dx);\n    bvirt = cy - cdy;\n    cdytail = cy - (cdy + bvirt) + (bvirt - dy);\n    if (adxtail === 0 && bdxtail === 0 && cdxtail === 0 && adytail === 0 && bdytail === 0 && cdytail === 0) {\n        return det;\n    }\n\n    errbound = iccerrboundC * permanent + resulterrbound * Math.abs(det);\n    det += ((adx * adx + ady * ady) * ((bdx * cdytail + cdy * bdxtail) - (bdy * cdxtail + cdx * bdytail)) +\n        2 * (adx * adxtail + ady * adytail) * (bdx * cdy - bdy * cdx)) +\n        ((bdx * bdx + bdy * bdy) * ((cdx * adytail + ady * cdxtail) - (cdy * adxtail + adx * cdytail)) +\n        2 * (bdx * bdxtail + bdy * bdytail) * (cdx * ady - cdy * adx)) +\n        ((cdx * cdx + cdy * cdy) * ((adx * bdytail + bdy * adxtail) - (ady * bdxtail + bdx * adytail)) +\n        2 * (cdx * cdxtail + cdy * cdytail) * (adx * bdy - ady * bdx));\n\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n        s1 = adx * adx;\n        c = splitter * adx;\n        ahi = c - (c - adx);\n        alo = adx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = ady * ady;\n        c = splitter * ady;\n        ahi = c - (c - ady);\n        alo = ady - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        aa[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        aa[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        aa[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        aa[3] = u3;\n    }\n    if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n        s1 = bdx * bdx;\n        c = splitter * bdx;\n        ahi = c - (c - bdx);\n        alo = bdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = bdy * bdy;\n        c = splitter * bdy;\n        ahi = c - (c - bdy);\n        alo = bdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        bb[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        bb[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        bb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        bb[3] = u3;\n    }\n    if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n        s1 = cdx * cdx;\n        c = splitter * cdx;\n        ahi = c - (c - cdx);\n        alo = cdx - ahi;\n        s0 = alo * alo - (s1 - ahi * ahi - (ahi + ahi) * alo);\n        t1 = cdy * cdy;\n        c = splitter * cdy;\n        ahi = c - (c - cdy);\n        alo = cdy - ahi;\n        t0 = alo * alo - (t1 - ahi * ahi - (ahi + ahi) * alo);\n        _i = s0 + t0;\n        bvirt = _i - s0;\n        cc[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n        _j = s1 + _i;\n        bvirt = _j - s1;\n        _0 = s1 - (_j - bvirt) + (_i - bvirt);\n        _i = _0 + t1;\n        bvirt = _i - _0;\n        cc[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n        u3 = _j + _i;\n        bvirt = u3 - _j;\n        cc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n        cc[3] = u3;\n    }\n\n    if (adxtail !== 0) {\n        axtbclen = scale(4, bc, adxtail, axtbc);\n        finlen = finadd(finlen, sum_three(\n            scale(axtbclen, axtbc, 2 * adx, _16), _16,\n            scale(scale(4, cc, adxtail, _8), _8, bdy, _16b), _16b,\n            scale(scale(4, bb, adxtail, _8), _8, -cdy, _16c), _16c, _32, _48), _48);\n    }\n    if (adytail !== 0) {\n        aytbclen = scale(4, bc, adytail, aytbc);\n        finlen = finadd(finlen, sum_three(\n            scale(aytbclen, aytbc, 2 * ady, _16), _16,\n            scale(scale(4, bb, adytail, _8), _8, cdx, _16b), _16b,\n            scale(scale(4, cc, adytail, _8), _8, -bdx, _16c), _16c, _32, _48), _48);\n    }\n    if (bdxtail !== 0) {\n        bxtcalen = scale(4, ca, bdxtail, bxtca);\n        finlen = finadd(finlen, sum_three(\n            scale(bxtcalen, bxtca, 2 * bdx, _16), _16,\n            scale(scale(4, aa, bdxtail, _8), _8, cdy, _16b), _16b,\n            scale(scale(4, cc, bdxtail, _8), _8, -ady, _16c), _16c, _32, _48), _48);\n    }\n    if (bdytail !== 0) {\n        bytcalen = scale(4, ca, bdytail, bytca);\n        finlen = finadd(finlen, sum_three(\n            scale(bytcalen, bytca, 2 * bdy, _16), _16,\n            scale(scale(4, cc, bdytail, _8), _8, adx, _16b), _16b,\n            scale(scale(4, aa, bdytail, _8), _8, -cdx, _16c), _16c, _32, _48), _48);\n    }\n    if (cdxtail !== 0) {\n        cxtablen = scale(4, ab, cdxtail, cxtab);\n        finlen = finadd(finlen, sum_three(\n            scale(cxtablen, cxtab, 2 * cdx, _16), _16,\n            scale(scale(4, bb, cdxtail, _8), _8, ady, _16b), _16b,\n            scale(scale(4, aa, cdxtail, _8), _8, -bdy, _16c), _16c, _32, _48), _48);\n    }\n    if (cdytail !== 0) {\n        cytablen = scale(4, ab, cdytail, cytab);\n        finlen = finadd(finlen, sum_three(\n            scale(cytablen, cytab, 2 * cdy, _16), _16,\n            scale(scale(4, aa, cdytail, _8), _8, bdx, _16b), _16b,\n            scale(scale(4, bb, cdytail, _8), _8, -adx, _16c), _16c, _32, _48), _48);\n    }\n\n    if (adxtail !== 0 || adytail !== 0) {\n        if (bdxtail !== 0 || bdytail !== 0 || cdxtail !== 0 || cdytail !== 0) {\n            s1 = bdxtail * cdy;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * cdy;\n            bhi = c - (c - cdy);\n            blo = cdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * cdytail;\n            c = splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            s1 = cdxtail * -bdy;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * -bdy;\n            bhi = c - (c - -bdy);\n            blo = -bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * -bdytail;\n            c = splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = splitter * -bdytail;\n            bhi = c - (c - -bdytail);\n            blo = -bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            bctlen = sum(4, u, 4, v, bct);\n            s1 = bdxtail * cdytail;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdxtail * bdytail;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            bctt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            bctt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            bctt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            bctt[3] = u3;\n            bcttlen = 4;\n        } else {\n            bct[0] = 0;\n            bctlen = 1;\n            bctt[0] = 0;\n            bcttlen = 1;\n        }\n        if (adxtail !== 0) {\n            const len = scale(bctlen, bct, adxtail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(axtbclen, axtbc, adxtail, _16), _16,\n                scale(len, _16c, 2 * adx, _32), _32, _48), _48);\n\n            const len2 = scale(bcttlen, bctt, adxtail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * adx, _16), _16,\n                scale(len2, _8, adxtail, _16b), _16b,\n                scale(len, _16c, adxtail, _32), _32, _32b, _64), _64);\n\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, cc, adxtail, _8), _8, bdytail, _16), _16);\n            }\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, bb, -adxtail, _8), _8, cdytail, _16), _16);\n            }\n        }\n        if (adytail !== 0) {\n            const len = scale(bctlen, bct, adytail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(aytbclen, aytbc, adytail, _16), _16,\n                scale(len, _16c, 2 * ady, _32), _32, _48), _48);\n\n            const len2 = scale(bcttlen, bctt, adytail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * ady, _16), _16,\n                scale(len2, _8, adytail, _16b), _16b,\n                scale(len, _16c, adytail, _32), _32, _32b, _64), _64);\n        }\n    }\n    if (bdxtail !== 0 || bdytail !== 0) {\n        if (cdxtail !== 0 || cdytail !== 0 || adxtail !== 0 || adytail !== 0) {\n            s1 = cdxtail * ady;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * ady;\n            bhi = c - (c - ady);\n            blo = ady - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = cdx * adytail;\n            c = splitter * cdx;\n            ahi = c - (c - cdx);\n            alo = cdx - ahi;\n            c = splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -cdy;\n            n0 = -cdytail;\n            s1 = adxtail * n1;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * n0;\n            c = splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            catlen = sum(4, u, 4, v, cat);\n            s1 = cdxtail * adytail;\n            c = splitter * cdxtail;\n            ahi = c - (c - cdxtail);\n            alo = cdxtail - ahi;\n            c = splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adxtail * cdytail;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * cdytail;\n            bhi = c - (c - cdytail);\n            blo = cdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            catt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            catt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            catt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            catt[3] = u3;\n            cattlen = 4;\n        } else {\n            cat[0] = 0;\n            catlen = 1;\n            catt[0] = 0;\n            cattlen = 1;\n        }\n        if (bdxtail !== 0) {\n            const len = scale(catlen, cat, bdxtail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(bxtcalen, bxtca, bdxtail, _16), _16,\n                scale(len, _16c, 2 * bdx, _32), _32, _48), _48);\n\n            const len2 = scale(cattlen, catt, bdxtail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * bdx, _16), _16,\n                scale(len2, _8, bdxtail, _16b), _16b,\n                scale(len, _16c, bdxtail, _32), _32, _32b, _64), _64);\n\n            if (cdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, aa, bdxtail, _8), _8, cdytail, _16), _16);\n            }\n            if (adytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, cc, -bdxtail, _8), _8, adytail, _16), _16);\n            }\n        }\n        if (bdytail !== 0) {\n            const len = scale(catlen, cat, bdytail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(bytcalen, bytca, bdytail, _16), _16,\n                scale(len, _16c, 2 * bdy, _32), _32, _48), _48);\n\n            const len2 = scale(cattlen, catt, bdytail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * bdy, _16), _16,\n                scale(len2, _8, bdytail, _16b), _16b,\n                scale(len, _16c, bdytail, _32), _32,  _32b, _64), _64);\n        }\n    }\n    if (cdxtail !== 0 || cdytail !== 0) {\n        if (adxtail !== 0 || adytail !== 0 || bdxtail !== 0 || bdytail !== 0) {\n            s1 = adxtail * bdy;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * bdy;\n            bhi = c - (c - bdy);\n            blo = bdy - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = adx * bdytail;\n            c = splitter * adx;\n            ahi = c - (c - adx);\n            alo = adx - ahi;\n            c = splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            u[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            u[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            u[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            u[3] = u3;\n            n1 = -ady;\n            n0 = -adytail;\n            s1 = bdxtail * n1;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * n1;\n            bhi = c - (c - n1);\n            blo = n1 - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdx * n0;\n            c = splitter * bdx;\n            ahi = c - (c - bdx);\n            alo = bdx - ahi;\n            c = splitter * n0;\n            bhi = c - (c - n0);\n            blo = n0 - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 + t0;\n            bvirt = _i - s0;\n            v[0] = s0 - (_i - bvirt) + (t0 - bvirt);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 + t1;\n            bvirt = _i - _0;\n            v[1] = _0 - (_i - bvirt) + (t1 - bvirt);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            v[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            v[3] = u3;\n            abtlen = sum(4, u, 4, v, abt);\n            s1 = adxtail * bdytail;\n            c = splitter * adxtail;\n            ahi = c - (c - adxtail);\n            alo = adxtail - ahi;\n            c = splitter * bdytail;\n            bhi = c - (c - bdytail);\n            blo = bdytail - bhi;\n            s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n            t1 = bdxtail * adytail;\n            c = splitter * bdxtail;\n            ahi = c - (c - bdxtail);\n            alo = bdxtail - ahi;\n            c = splitter * adytail;\n            bhi = c - (c - adytail);\n            blo = adytail - bhi;\n            t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n            _i = s0 - t0;\n            bvirt = s0 - _i;\n            abtt[0] = s0 - (_i + bvirt) + (bvirt - t0);\n            _j = s1 + _i;\n            bvirt = _j - s1;\n            _0 = s1 - (_j - bvirt) + (_i - bvirt);\n            _i = _0 - t1;\n            bvirt = _0 - _i;\n            abtt[1] = _0 - (_i + bvirt) + (bvirt - t1);\n            u3 = _j + _i;\n            bvirt = u3 - _j;\n            abtt[2] = _j - (u3 - bvirt) + (_i - bvirt);\n            abtt[3] = u3;\n            abttlen = 4;\n        } else {\n            abt[0] = 0;\n            abtlen = 1;\n            abtt[0] = 0;\n            abttlen = 1;\n        }\n        if (cdxtail !== 0) {\n            const len = scale(abtlen, abt, cdxtail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(cxtablen, cxtab, cdxtail, _16), _16,\n                scale(len, _16c, 2 * cdx, _32), _32, _48), _48);\n\n            const len2 = scale(abttlen, abtt, cdxtail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * cdx, _16), _16,\n                scale(len2, _8, cdxtail, _16b), _16b,\n                scale(len, _16c, cdxtail, _32), _32, _32b, _64), _64);\n\n            if (adytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, bb, cdxtail, _8), _8, adytail, _16), _16);\n            }\n            if (bdytail !== 0) {\n                finlen = finadd(finlen, scale(scale(4, aa, -cdxtail, _8), _8, bdytail, _16), _16);\n            }\n        }\n        if (cdytail !== 0) {\n            const len = scale(abtlen, abt, cdytail, _16c);\n            finlen = finadd(finlen, sum(\n                scale(cytablen, cytab, cdytail, _16), _16,\n                scale(len, _16c, 2 * cdy, _32), _32, _48), _48);\n\n            const len2 = scale(abttlen, abtt, cdytail, _8);\n            finlen = finadd(finlen, sum_three(\n                scale(len2, _8, 2 * cdy, _16), _16,\n                scale(len2, _8, cdytail, _16b), _16b,\n                scale(len, _16c, cdytail, _32), _32, _32b, _64), _64);\n        }\n    }\n\n    return fin[finlen - 1];\n}\n\nexport function incircle(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const bdx = bx - dx;\n    const cdx = cx - dx;\n    const ady = ay - dy;\n    const bdy = by - dy;\n    const cdy = cy - dy;\n\n    const bdxcdy = bdx * cdy;\n    const cdxbdy = cdx * bdy;\n    const alift = adx * adx + ady * ady;\n\n    const cdxady = cdx * ady;\n    const adxcdy = adx * cdy;\n    const blift = bdx * bdx + bdy * bdy;\n\n    const adxbdy = adx * bdy;\n    const bdxady = bdx * ady;\n    const clift = cdx * cdx + cdy * cdy;\n\n    const det =\n        alift * (bdxcdy - cdxbdy) +\n        blift * (cdxady - adxcdy) +\n        clift * (adxbdy - bdxady);\n\n    const permanent =\n        (Math.abs(bdxcdy) + Math.abs(cdxbdy)) * alift +\n        (Math.abs(cdxady) + Math.abs(adxcdy)) * blift +\n        (Math.abs(adxbdy) + Math.abs(bdxady)) * clift;\n\n    const errbound = iccerrboundA * permanent;\n\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return incircleadapt(ax, ay, bx, by, cx, cy, dx, dy, permanent);\n}\n\nexport function incirclefast(ax, ay, bx, by, cx, cy, dx, dy) {\n    const adx = ax - dx;\n    const ady = ay - dy;\n    const bdx = bx - dx;\n    const bdy = by - dy;\n    const cdx = cx - dx;\n    const cdy = cy - dy;\n\n    const abdet = adx * bdy - bdx * ady;\n    const bcdet = bdx * cdy - cdx * bdy;\n    const cadet = cdx * ady - adx * cdy;\n    const alift = adx * adx + ady * ady;\n    const blift = bdx * bdx + bdy * bdy;\n    const clift = cdx * cdx + cdy * cdy;\n\n    return alift * bcdet + blift * cadet + clift * abdet;\n}\n", "import {epsilon, splitter, resulterrbound, estimate, vec, sum, sum_three, scale, negate} from './util.js';\n\nconst isperrboundA = (16 + 224 * epsilon) * epsilon;\nconst isperrboundB = (5 + 72 * epsilon) * epsilon;\nconst isperrboundC = (71 + 1408 * epsilon) * epsilon * epsilon;\n\nconst ab = vec(4);\nconst bc = vec(4);\nconst cd = vec(4);\nconst de = vec(4);\nconst ea = vec(4);\nconst ac = vec(4);\nconst bd = vec(4);\nconst ce = vec(4);\nconst da = vec(4);\nconst eb = vec(4);\n\nconst abc = vec(24);\nconst bcd = vec(24);\nconst cde = vec(24);\nconst dea = vec(24);\nconst eab = vec(24);\nconst abd = vec(24);\nconst bce = vec(24);\nconst cda = vec(24);\nconst deb = vec(24);\nconst eac = vec(24);\n\nconst adet = vec(1152);\nconst bdet = vec(1152);\nconst cdet = vec(1152);\nconst ddet = vec(1152);\nconst edet = vec(1152);\nconst abdet = vec(2304);\nconst cddet = vec(2304);\nconst cdedet = vec(3456);\nconst deter = vec(5760);\n\nconst _8 = vec(8);\nconst _8b = vec(8);\nconst _8c = vec(8);\nconst _16 = vec(16);\nconst _24 = vec(24);\nconst _48 = vec(48);\nconst _48b = vec(48);\nconst _96 = vec(96);\nconst _192 = vec(192);\nconst _384x = vec(384);\nconst _384y = vec(384);\nconst _384z = vec(384);\nconst _768 = vec(768);\n\nfunction sum_three_scale(a, b, c, az, bz, cz, out) {\n    return sum_three(\n        scale(4, a, az, _8), _8,\n        scale(4, b, bz, _8b), _8b,\n        scale(4, c, cz, _8c), _8c, _16, out);\n}\n\nfunction liftexact(alen, a, blen, b, clen, c, dlen, d, x, y, z, out) {\n    const len = sum(\n        sum(alen, a, blen, b, _48), _48,\n        negate(sum(clen, c, dlen, d, _48b), _48b), _48b, _96);\n\n    return sum_three(\n        scale(scale(len, _96, x, _192), _192, x, _384x), _384x,\n        scale(scale(len, _96, y, _192), _192, y, _384y), _384y,\n        scale(scale(len, _96, z, _192), _192, z, _384z), _384z, _768, out);\n}\n\nfunction insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0, u3;\n\n    s1 = ax * by;\n    c = splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ay;\n    c = splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ab[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ab[3] = u3;\n    s1 = bx * cy;\n    c = splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * by;\n    c = splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bc[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bc[3] = u3;\n    s1 = cx * dy;\n    c = splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * cy;\n    c = splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    cd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    cd[3] = u3;\n    s1 = dx * ey;\n    c = splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * dy;\n    c = splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    de[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    de[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    de[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    de[3] = u3;\n    s1 = ex * ay;\n    c = splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * ey;\n    c = splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ea[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ea[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ea[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ea[3] = u3;\n    s1 = ax * cy;\n    c = splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cx * ay;\n    c = splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ac[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ac[3] = u3;\n    s1 = bx * dy;\n    c = splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dx * by;\n    c = splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    bd[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    bd[3] = u3;\n    s1 = cx * ey;\n    c = splitter * cx;\n    ahi = c - (c - cx);\n    alo = cx - ahi;\n    c = splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ex * cy;\n    c = splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = splitter * cy;\n    bhi = c - (c - cy);\n    blo = cy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ce[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ce[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    ce[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    ce[3] = u3;\n    s1 = dx * ay;\n    c = splitter * dx;\n    ahi = c - (c - dx);\n    alo = dx - ahi;\n    c = splitter * ay;\n    bhi = c - (c - ay);\n    blo = ay - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = ax * dy;\n    c = splitter * ax;\n    ahi = c - (c - ax);\n    alo = ax - ahi;\n    c = splitter * dy;\n    bhi = c - (c - dy);\n    blo = dy - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    da[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    da[3] = u3;\n    s1 = ex * by;\n    c = splitter * ex;\n    ahi = c - (c - ex);\n    alo = ex - ahi;\n    c = splitter * by;\n    bhi = c - (c - by);\n    blo = by - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bx * ey;\n    c = splitter * bx;\n    ahi = c - (c - bx);\n    alo = bx - ahi;\n    c = splitter * ey;\n    bhi = c - (c - ey);\n    blo = ey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    eb[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    eb[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    u3 = _j + _i;\n    bvirt = u3 - _j;\n    eb[2] = _j - (u3 - bvirt) + (_i - bvirt);\n    eb[3] = u3;\n\n    const abclen = sum_three_scale(ab, bc, ac, cz, az, -bz, abc);\n    const bcdlen = sum_three_scale(bc, cd, bd, dz, bz, -cz, bcd);\n    const cdelen = sum_three_scale(cd, de, ce, ez, cz, -dz, cde);\n    const dealen = sum_three_scale(de, ea, da, az, dz, -ez, dea);\n    const eablen = sum_three_scale(ea, ab, eb, bz, ez, -az, eab);\n    const abdlen = sum_three_scale(ab, bd, da, dz, az, bz, abd);\n    const bcelen = sum_three_scale(bc, ce, eb, ez, bz, cz, bce);\n    const cdalen = sum_three_scale(cd, da, ac, az, cz, dz, cda);\n    const deblen = sum_three_scale(de, eb, bd, bz, dz, ez, deb);\n    const eaclen = sum_three_scale(ea, ac, ce, cz, ez, az, eac);\n\n    const deterlen = sum_three(\n        liftexact(cdelen, cde, bcelen, bce, deblen, deb, bcdlen, bcd, ax, ay, az, adet), adet,\n        liftexact(dealen, dea, cdalen, cda, eaclen, eac, cdelen, cde, bx, by, bz, bdet), bdet,\n        sum_three(\n            liftexact(eablen, eab, deblen, deb, abdlen, abd, dealen, dea, cx, cy, cz, cdet), cdet,\n            liftexact(abclen, abc, eaclen, eac, bcelen, bce, eablen, eab, dx, dy, dz, ddet), ddet,\n            liftexact(bcdlen, bcd, abdlen, abd, cdalen, cda, abclen, abc, ex, ey, ez, edet), edet, cddet, cdedet), cdedet, abdet, deter);\n\n    return deter[deterlen - 1];\n}\n\nconst xdet = vec(96);\nconst ydet = vec(96);\nconst zdet = vec(96);\nconst fin = vec(1152);\n\nfunction liftadapt(a, b, c, az, bz, cz, x, y, z, out) {\n    const len = sum_three_scale(a, b, c, az, bz, cz, _24);\n    return sum_three(\n        scale(scale(len, _24, x, _48), _48, x, xdet), xdet,\n        scale(scale(len, _24, y, _48), _48, y, ydet), ydet,\n        scale(scale(len, _24, z, _48), _48, z, zdet), zdet, _192, out);\n}\n\nfunction insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent) {\n    let ab3, bc3, cd3, da3, ac3, bd3;\n\n    let aextail, bextail, cextail, dextail;\n    let aeytail, beytail, ceytail, deytail;\n    let aeztail, beztail, ceztail, deztail;\n\n    let bvirt, c, ahi, alo, bhi, blo, _i, _j, _0, s1, s0, t1, t0;\n\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n\n    s1 = aex * bey;\n    c = splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = bex * aey;\n    c = splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ab[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ab[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ab3 = _j + _i;\n    bvirt = ab3 - _j;\n    ab[2] = _j - (ab3 - bvirt) + (_i - bvirt);\n    ab[3] = ab3;\n    s1 = bex * cey;\n    c = splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * bey;\n    c = splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bc[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bc[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bc3 = _j + _i;\n    bvirt = bc3 - _j;\n    bc[2] = _j - (bc3 - bvirt) + (_i - bvirt);\n    bc[3] = bc3;\n    s1 = cex * dey;\n    c = splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * cey;\n    c = splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    cd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    cd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    cd3 = _j + _i;\n    bvirt = cd3 - _j;\n    cd[2] = _j - (cd3 - bvirt) + (_i - bvirt);\n    cd[3] = cd3;\n    s1 = dex * aey;\n    c = splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = aex * dey;\n    c = splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    da[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    da[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    da3 = _j + _i;\n    bvirt = da3 - _j;\n    da[2] = _j - (da3 - bvirt) + (_i - bvirt);\n    da[3] = da3;\n    s1 = aex * cey;\n    c = splitter * aex;\n    ahi = c - (c - aex);\n    alo = aex - ahi;\n    c = splitter * cey;\n    bhi = c - (c - cey);\n    blo = cey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = cex * aey;\n    c = splitter * cex;\n    ahi = c - (c - cex);\n    alo = cex - ahi;\n    c = splitter * aey;\n    bhi = c - (c - aey);\n    blo = aey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    ac[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    ac[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    ac3 = _j + _i;\n    bvirt = ac3 - _j;\n    ac[2] = _j - (ac3 - bvirt) + (_i - bvirt);\n    ac[3] = ac3;\n    s1 = bex * dey;\n    c = splitter * bex;\n    ahi = c - (c - bex);\n    alo = bex - ahi;\n    c = splitter * dey;\n    bhi = c - (c - dey);\n    blo = dey - bhi;\n    s0 = alo * blo - (s1 - ahi * bhi - alo * bhi - ahi * blo);\n    t1 = dex * bey;\n    c = splitter * dex;\n    ahi = c - (c - dex);\n    alo = dex - ahi;\n    c = splitter * bey;\n    bhi = c - (c - bey);\n    blo = bey - bhi;\n    t0 = alo * blo - (t1 - ahi * bhi - alo * bhi - ahi * blo);\n    _i = s0 - t0;\n    bvirt = s0 - _i;\n    bd[0] = s0 - (_i + bvirt) + (bvirt - t0);\n    _j = s1 + _i;\n    bvirt = _j - s1;\n    _0 = s1 - (_j - bvirt) + (_i - bvirt);\n    _i = _0 - t1;\n    bvirt = _0 - _i;\n    bd[1] = _0 - (_i + bvirt) + (bvirt - t1);\n    bd3 = _j + _i;\n    bvirt = bd3 - _j;\n    bd[2] = _j - (bd3 - bvirt) + (_i - bvirt);\n    bd[3] = bd3;\n\n    const finlen = sum(\n        sum(\n            negate(liftadapt(bc, cd, bd, dez, bez, -cez, aex, aey, aez, adet), adet), adet,\n            liftadapt(cd, da, ac, aez, cez, dez, bex, bey, bez, bdet), bdet, abdet), abdet,\n        sum(\n            negate(liftadapt(da, ab, bd, bez, dez, aez, cex, cey, cez, cdet), cdet), cdet,\n            liftadapt(ab, bc, ac, cez, aez, -bez, dex, dey, dez, ddet), ddet, cddet), cddet, fin);\n\n    let det = estimate(finlen, fin);\n    let errbound = isperrboundB * permanent;\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    bvirt = ax - aex;\n    aextail = ax - (aex + bvirt) + (bvirt - ex);\n    bvirt = ay - aey;\n    aeytail = ay - (aey + bvirt) + (bvirt - ey);\n    bvirt = az - aez;\n    aeztail = az - (aez + bvirt) + (bvirt - ez);\n    bvirt = bx - bex;\n    bextail = bx - (bex + bvirt) + (bvirt - ex);\n    bvirt = by - bey;\n    beytail = by - (bey + bvirt) + (bvirt - ey);\n    bvirt = bz - bez;\n    beztail = bz - (bez + bvirt) + (bvirt - ez);\n    bvirt = cx - cex;\n    cextail = cx - (cex + bvirt) + (bvirt - ex);\n    bvirt = cy - cey;\n    ceytail = cy - (cey + bvirt) + (bvirt - ey);\n    bvirt = cz - cez;\n    ceztail = cz - (cez + bvirt) + (bvirt - ez);\n    bvirt = dx - dex;\n    dextail = dx - (dex + bvirt) + (bvirt - ex);\n    bvirt = dy - dey;\n    deytail = dy - (dey + bvirt) + (bvirt - ey);\n    bvirt = dz - dez;\n    deztail = dz - (dez + bvirt) + (bvirt - ez);\n    if (aextail === 0 && aeytail === 0 && aeztail === 0 &&\n        bextail === 0 && beytail === 0 && beztail === 0 &&\n        cextail === 0 && ceytail === 0 && ceztail === 0 &&\n        dextail === 0 && deytail === 0 && deztail === 0) {\n        return det;\n    }\n\n    errbound = isperrboundC * permanent + resulterrbound * Math.abs(det);\n\n    const abeps = (aex * beytail + bey * aextail) - (aey * bextail + bex * aeytail);\n    const bceps = (bex * ceytail + cey * bextail) - (bey * cextail + cex * beytail);\n    const cdeps = (cex * deytail + dey * cextail) - (cey * dextail + dex * ceytail);\n    const daeps = (dex * aeytail + aey * dextail) - (dey * aextail + aex * deytail);\n    const aceps = (aex * ceytail + cey * aextail) - (aey * cextail + cex * aeytail);\n    const bdeps = (bex * deytail + dey * bextail) - (bey * dextail + dex * beytail);\n    det +=\n        (((bex * bex + bey * bey + bez * bez) * ((cez * daeps + dez * aceps + aez * cdeps) +\n        (ceztail * da3 + deztail * ac3 + aeztail * cd3)) + (dex * dex + dey * dey + dez * dez) *\n        ((aez * bceps - bez * aceps + cez * abeps) + (aeztail * bc3 - beztail * ac3 + ceztail * ab3))) -\n        ((aex * aex + aey * aey + aez * aez) * ((bez * cdeps - cez * bdeps + dez * bceps) +\n        (beztail * cd3 - ceztail * bd3 + deztail * bc3)) + (cex * cex + cey * cey + cez * cez) *\n        ((dez * abeps + aez * bdeps + bez * daeps) + (deztail * ab3 + aeztail * bd3 + beztail * da3)))) +\n        2 * (((bex * bextail + bey * beytail + bez * beztail) * (cez * da3 + dez * ac3 + aez * cd3) +\n        (dex * dextail + dey * deytail + dez * deztail) * (aez * bc3 - bez * ac3 + cez * ab3)) -\n        ((aex * aextail + aey * aeytail + aez * aeztail) * (bez * cd3 - cez * bd3 + dez * bc3) +\n        (cex * cextail + cey * ceytail + cez * ceztail) * (dez * ab3 + aez * bd3 + bez * da3)));\n\n    if (det >= errbound || -det >= errbound) {\n        return det;\n    }\n\n    return insphereexact(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez);\n}\n\nexport function insphere(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez) {\n    const aex = ax - ex;\n    const bex = bx - ex;\n    const cex = cx - ex;\n    const dex = dx - ex;\n    const aey = ay - ey;\n    const bey = by - ey;\n    const cey = cy - ey;\n    const dey = dy - ey;\n    const aez = az - ez;\n    const bez = bz - ez;\n    const cez = cz - ez;\n    const dez = dz - ez;\n\n    const aexbey = aex * bey;\n    const bexaey = bex * aey;\n    const ab = aexbey - bexaey;\n    const bexcey = bex * cey;\n    const cexbey = cex * bey;\n    const bc = bexcey - cexbey;\n    const cexdey = cex * dey;\n    const dexcey = dex * cey;\n    const cd = cexdey - dexcey;\n    const dexaey = dex * aey;\n    const aexdey = aex * dey;\n    const da = dexaey - aexdey;\n    const aexcey = aex * cey;\n    const cexaey = cex * aey;\n    const ac = aexcey - cexaey;\n    const bexdey = bex * dey;\n    const dexbey = dex * bey;\n    const bd = bexdey - dexbey;\n\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n\n    const det =\n        (clift * (dez * ab + aez * bd + bez * da) - dlift * (aez * bc - bez * ac + cez * ab)) +\n        (alift * (bez * cd - cez * bd + dez * bc) - blift * (cez * da + dez * ac + aez * cd));\n\n    const aezplus = Math.abs(aez);\n    const bezplus = Math.abs(bez);\n    const cezplus = Math.abs(cez);\n    const dezplus = Math.abs(dez);\n    const aexbeyplus = Math.abs(aexbey) + Math.abs(bexaey);\n    const bexceyplus = Math.abs(bexcey) + Math.abs(cexbey);\n    const cexdeyplus = Math.abs(cexdey) + Math.abs(dexcey);\n    const dexaeyplus = Math.abs(dexaey) + Math.abs(aexdey);\n    const aexceyplus = Math.abs(aexcey) + Math.abs(cexaey);\n    const bexdeyplus = Math.abs(bexdey) + Math.abs(dexbey);\n    const permanent =\n        (cexdeyplus * bezplus + bexdeyplus * cezplus + bexceyplus * dezplus) * alift +\n        (dexaeyplus * cezplus + aexceyplus * dezplus + cexdeyplus * aezplus) * blift +\n        (aexbeyplus * dezplus + bexdeyplus * aezplus + dexaeyplus * bezplus) * clift +\n        (bexceyplus * aezplus + aexceyplus * bezplus + aexbeyplus * cezplus) * dlift;\n\n    const errbound = isperrboundA * permanent;\n    if (det > errbound || -det > errbound) {\n        return det;\n    }\n    return -insphereadapt(ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz, ex, ey, ez, permanent);\n}\n\nexport function inspherefast(pax, pay, paz, pbx, pby, pbz, pcx, pcy, pcz, pdx, pdy, pdz, pex, pey, pez) {\n    const aex = pax - pex;\n    const bex = pbx - pex;\n    const cex = pcx - pex;\n    const dex = pdx - pex;\n    const aey = pay - pey;\n    const bey = pby - pey;\n    const cey = pcy - pey;\n    const dey = pdy - pey;\n    const aez = paz - pez;\n    const bez = pbz - pez;\n    const cez = pcz - pez;\n    const dez = pdz - pez;\n\n    const ab = aex * bey - bex * aey;\n    const bc = bex * cey - cex * bey;\n    const cd = cex * dey - dex * cey;\n    const da = dex * aey - aex * dey;\n    const ac = aex * cey - cex * aey;\n    const bd = bex * dey - dex * bey;\n\n    const abc = aez * bc - bez * ac + cez * ab;\n    const bcd = bez * cd - cez * bd + dez * bc;\n    const cda = cez * da + dez * ac + aez * cd;\n    const dab = dez * ab + aez * bd + bez * da;\n\n    const alift = aex * aex + aey * aey + aez * aez;\n    const blift = bex * bex + bey * bey + bez * bez;\n    const clift = cex * cex + cey * cey + cez * cez;\n    const dlift = dex * dex + dey * dey + dez * dez;\n\n    return (clift * dab - dlift * abc) + (alift * bcd - blift * cda);\n}\n", "\nconst EPSILON = Math.pow(2, -52);\nconst EDGE_STACK = new Uint32Array(512);\n\nimport {orient2d} from 'robust-predicates';\n\nexport default class Delaunator {\n\n    static from(points, getX = defaultGetX, getY = defaultGetY) {\n        const n = points.length;\n        const coords = new Float64Array(n * 2);\n\n        for (let i = 0; i < n; i++) {\n            const p = points[i];\n            coords[2 * i] = getX(p);\n            coords[2 * i + 1] = getY(p);\n        }\n\n        return new Delaunator(coords);\n    }\n\n    constructor(coords) {\n        const n = coords.length >> 1;\n        if (n > 0 && typeof coords[0] !== 'number') throw new Error('Expected coords to contain numbers.');\n\n        this.coords = coords;\n\n        // arrays that will store the triangulation graph\n        const maxTriangles = Math.max(2 * n - 5, 0);\n        this._triangles = new Uint32Array(maxTriangles * 3);\n        this._halfedges = new Int32Array(maxTriangles * 3);\n\n        // temporary arrays for tracking the edges of the advancing convex hull\n        this._hashSize = Math.ceil(Math.sqrt(n));\n        this._hullPrev = new Uint32Array(n); // edge to prev edge\n        this._hullNext = new Uint32Array(n); // edge to next edge\n        this._hullTri = new Uint32Array(n); // edge to adjacent triangle\n        this._hullHash = new Int32Array(this._hashSize); // angular edge hash\n\n        // temporary arrays for sorting points\n        this._ids = new Uint32Array(n);\n        this._dists = new Float64Array(n);\n\n        this.update();\n    }\n\n    update() {\n        const {coords, _hullPrev: hullPrev, _hullNext: hullNext, _hullTri: hullTri, _hullHash: hullHash} =  this;\n        const n = coords.length >> 1;\n\n        // populate an array of point indices; calculate input data bbox\n        let minX = Infinity;\n        let minY = Infinity;\n        let maxX = -Infinity;\n        let maxY = -Infinity;\n\n        for (let i = 0; i < n; i++) {\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n            if (x < minX) minX = x;\n            if (y < minY) minY = y;\n            if (x > maxX) maxX = x;\n            if (y > maxY) maxY = y;\n            this._ids[i] = i;\n        }\n        const cx = (minX + maxX) / 2;\n        const cy = (minY + maxY) / 2;\n\n        let i0, i1, i2;\n\n        // pick a seed point close to the center\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            const d = dist(cx, cy, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist) {\n                i0 = i;\n                minDist = d;\n            }\n        }\n        const i0x = coords[2 * i0];\n        const i0y = coords[2 * i0 + 1];\n\n        // find the point closest to the seed\n        for (let i = 0, minDist = Infinity; i < n; i++) {\n            if (i === i0) continue;\n            const d = dist(i0x, i0y, coords[2 * i], coords[2 * i + 1]);\n            if (d < minDist && d > 0) {\n                i1 = i;\n                minDist = d;\n            }\n        }\n        let i1x = coords[2 * i1];\n        let i1y = coords[2 * i1 + 1];\n\n        let minRadius = Infinity;\n\n        // find the third point which forms the smallest circumcircle with the first two\n        for (let i = 0; i < n; i++) {\n            if (i === i0 || i === i1) continue;\n            const r = circumradius(i0x, i0y, i1x, i1y, coords[2 * i], coords[2 * i + 1]);\n            if (r < minRadius) {\n                i2 = i;\n                minRadius = r;\n            }\n        }\n        let i2x = coords[2 * i2];\n        let i2y = coords[2 * i2 + 1];\n\n        if (minRadius === Infinity) {\n            // order collinear points by dx (or dy if all x are identical)\n            // and return the list as a hull\n            for (let i = 0; i < n; i++) {\n                this._dists[i] = (coords[2 * i] - coords[0]) || (coords[2 * i + 1] - coords[1]);\n            }\n            quicksort(this._ids, this._dists, 0, n - 1);\n            const hull = new Uint32Array(n);\n            let j = 0;\n            for (let i = 0, d0 = -Infinity; i < n; i++) {\n                const id = this._ids[i];\n                const d = this._dists[id];\n                if (d > d0) {\n                    hull[j++] = id;\n                    d0 = d;\n                }\n            }\n            this.hull = hull.subarray(0, j);\n            this.triangles = new Uint32Array(0);\n            this.halfedges = new Uint32Array(0);\n            return;\n        }\n\n        // swap the order of the seed points for counter-clockwise orientation\n        if (orient2d(i0x, i0y, i1x, i1y, i2x, i2y) < 0) {\n            const i = i1;\n            const x = i1x;\n            const y = i1y;\n            i1 = i2;\n            i1x = i2x;\n            i1y = i2y;\n            i2 = i;\n            i2x = x;\n            i2y = y;\n        }\n\n        const center = circumcenter(i0x, i0y, i1x, i1y, i2x, i2y);\n        this._cx = center.x;\n        this._cy = center.y;\n\n        for (let i = 0; i < n; i++) {\n            this._dists[i] = dist(coords[2 * i], coords[2 * i + 1], center.x, center.y);\n        }\n\n        // sort the points by distance from the seed triangle circumcenter\n        quicksort(this._ids, this._dists, 0, n - 1);\n\n        // set up the seed triangle as the starting hull\n        this._hullStart = i0;\n        let hullSize = 3;\n\n        hullNext[i0] = hullPrev[i2] = i1;\n        hullNext[i1] = hullPrev[i0] = i2;\n        hullNext[i2] = hullPrev[i1] = i0;\n\n        hullTri[i0] = 0;\n        hullTri[i1] = 1;\n        hullTri[i2] = 2;\n\n        hullHash.fill(-1);\n        hullHash[this._hashKey(i0x, i0y)] = i0;\n        hullHash[this._hashKey(i1x, i1y)] = i1;\n        hullHash[this._hashKey(i2x, i2y)] = i2;\n\n        this.trianglesLen = 0;\n        this._addTriangle(i0, i1, i2, -1, -1, -1);\n\n        for (let k = 0, xp, yp; k < this._ids.length; k++) {\n            const i = this._ids[k];\n            const x = coords[2 * i];\n            const y = coords[2 * i + 1];\n\n            // skip near-duplicate points\n            if (k > 0 && Math.abs(x - xp) <= EPSILON && Math.abs(y - yp) <= EPSILON) continue;\n            xp = x;\n            yp = y;\n\n            // skip seed triangle points\n            if (i === i0 || i === i1 || i === i2) continue;\n\n            // find a visible edge on the convex hull using edge hash\n            let start = 0;\n            for (let j = 0, key = this._hashKey(x, y); j < this._hashSize; j++) {\n                start = hullHash[(key + j) % this._hashSize];\n                if (start !== -1 && start !== hullNext[start]) break;\n            }\n\n            start = hullPrev[start];\n            let e = start, q;\n            while (q = hullNext[e], orient2d(x, y, coords[2 * e], coords[2 * e + 1], coords[2 * q], coords[2 * q + 1]) >= 0) {\n                e = q;\n                if (e === start) {\n                    e = -1;\n                    break;\n                }\n            }\n            if (e === -1) continue; // likely a near-duplicate point; skip it\n\n            // add the first triangle from the point\n            let t = this._addTriangle(e, i, hullNext[e], -1, -1, hullTri[e]);\n\n            // recursively flip triangles from the point until they satisfy the Delaunay condition\n            hullTri[i] = this._legalize(t + 2);\n            hullTri[e] = t; // keep track of boundary triangles on the hull\n            hullSize++;\n\n            // walk forward through the hull, adding more triangles and flipping recursively\n            let n = hullNext[e];\n            while (q = hullNext[n], orient2d(x, y, coords[2 * n], coords[2 * n + 1], coords[2 * q], coords[2 * q + 1]) < 0) {\n                t = this._addTriangle(n, i, q, hullTri[i], -1, hullTri[n]);\n                hullTri[i] = this._legalize(t + 2);\n                hullNext[n] = n; // mark as removed\n                hullSize--;\n                n = q;\n            }\n\n            // walk backward from the other side, adding more triangles and flipping\n            if (e === start) {\n                while (q = hullPrev[e], orient2d(x, y, coords[2 * q], coords[2 * q + 1], coords[2 * e], coords[2 * e + 1]) < 0) {\n                    t = this._addTriangle(q, i, e, -1, hullTri[e], hullTri[q]);\n                    this._legalize(t + 2);\n                    hullTri[q] = t;\n                    hullNext[e] = e; // mark as removed\n                    hullSize--;\n                    e = q;\n                }\n            }\n\n            // update the hull indices\n            this._hullStart = hullPrev[i] = e;\n            hullNext[e] = hullPrev[n] = i;\n            hullNext[i] = n;\n\n            // save the two new edges in the hash table\n            hullHash[this._hashKey(x, y)] = i;\n            hullHash[this._hashKey(coords[2 * e], coords[2 * e + 1])] = e;\n        }\n\n        this.hull = new Uint32Array(hullSize);\n        for (let i = 0, e = this._hullStart; i < hullSize; i++) {\n            this.hull[i] = e;\n            e = hullNext[e];\n        }\n\n        // trim typed triangle mesh arrays\n        this.triangles = this._triangles.subarray(0, this.trianglesLen);\n        this.halfedges = this._halfedges.subarray(0, this.trianglesLen);\n    }\n\n    _hashKey(x, y) {\n        return Math.floor(pseudoAngle(x - this._cx, y - this._cy) * this._hashSize) % this._hashSize;\n    }\n\n    _legalize(a) {\n        const {_triangles: triangles, _halfedges: halfedges, coords} = this;\n\n        let i = 0;\n        let ar = 0;\n\n        // recursion eliminated with a fixed-size stack\n        while (true) {\n            const b = halfedges[a];\n\n            /* if the pair of triangles doesn't satisfy the Delaunay condition\n             * (p1 is inside the circumcircle of [p0, pl, pr]), flip them,\n             * then do the same check/flip recursively for the new pair of triangles\n             *\n             *           pl                    pl\n             *          /||\\                  /  \\\n             *       al/ || \\bl            al/    \\a\n             *        /  ||  \\              /      \\\n             *       /  a||b  \\    flip    /___ar___\\\n             *     p0\\   ||   /p1   =>   p0\\---bl---/p1\n             *        \\  ||  /              \\      /\n             *       ar\\ || /br             b\\    /br\n             *          \\||/                  \\  /\n             *           pr                    pr\n             */\n            const a0 = a - a % 3;\n            ar = a0 + (a + 2) % 3;\n\n            if (b === -1) { // convex hull edge\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n                continue;\n            }\n\n            const b0 = b - b % 3;\n            const al = a0 + (a + 1) % 3;\n            const bl = b0 + (b + 2) % 3;\n\n            const p0 = triangles[ar];\n            const pr = triangles[a];\n            const pl = triangles[al];\n            const p1 = triangles[bl];\n\n            const illegal = inCircle(\n                coords[2 * p0], coords[2 * p0 + 1],\n                coords[2 * pr], coords[2 * pr + 1],\n                coords[2 * pl], coords[2 * pl + 1],\n                coords[2 * p1], coords[2 * p1 + 1]);\n\n            if (illegal) {\n                triangles[a] = p1;\n                triangles[b] = p0;\n\n                const hbl = halfedges[bl];\n\n                // edge swapped on the other side of the hull (rare); fix the halfedge reference\n                if (hbl === -1) {\n                    let e = this._hullStart;\n                    do {\n                        if (this._hullTri[e] === bl) {\n                            this._hullTri[e] = a;\n                            break;\n                        }\n                        e = this._hullPrev[e];\n                    } while (e !== this._hullStart);\n                }\n                this._link(a, hbl);\n                this._link(b, halfedges[ar]);\n                this._link(ar, bl);\n\n                const br = b0 + (b + 1) % 3;\n\n                // don't worry about hitting the cap: it can only happen on extremely degenerate input\n                if (i < EDGE_STACK.length) {\n                    EDGE_STACK[i++] = br;\n                }\n            } else {\n                if (i === 0) break;\n                a = EDGE_STACK[--i];\n            }\n        }\n\n        return ar;\n    }\n\n    _link(a, b) {\n        this._halfedges[a] = b;\n        if (b !== -1) this._halfedges[b] = a;\n    }\n\n    // add a new triangle given vertex indices and adjacent half-edge ids\n    _addTriangle(i0, i1, i2, a, b, c) {\n        const t = this.trianglesLen;\n\n        this._triangles[t] = i0;\n        this._triangles[t + 1] = i1;\n        this._triangles[t + 2] = i2;\n\n        this._link(t, a);\n        this._link(t + 1, b);\n        this._link(t + 2, c);\n\n        this.trianglesLen += 3;\n\n        return t;\n    }\n}\n\n// monotonically increases with real angle, but doesn't need expensive trigonometry\nfunction pseudoAngle(dx, dy) {\n    const p = dx / (Math.abs(dx) + Math.abs(dy));\n    return (dy > 0 ? 3 - p : 1 + p) / 4; // [0..1]\n}\n\nfunction dist(ax, ay, bx, by) {\n    const dx = ax - bx;\n    const dy = ay - by;\n    return dx * dx + dy * dy;\n}\n\nfunction inCircle(ax, ay, bx, by, cx, cy, px, py) {\n    const dx = ax - px;\n    const dy = ay - py;\n    const ex = bx - px;\n    const ey = by - py;\n    const fx = cx - px;\n    const fy = cy - py;\n\n    const ap = dx * dx + dy * dy;\n    const bp = ex * ex + ey * ey;\n    const cp = fx * fx + fy * fy;\n\n    return dx * (ey * cp - bp * fy) -\n           dy * (ex * cp - bp * fx) +\n           ap * (ex * fy - ey * fx) < 0;\n}\n\nfunction circumradius(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = (ey * bl - dy * cl) * d;\n    const y = (dx * cl - ex * bl) * d;\n\n    return x * x + y * y;\n}\n\nfunction circumcenter(ax, ay, bx, by, cx, cy) {\n    const dx = bx - ax;\n    const dy = by - ay;\n    const ex = cx - ax;\n    const ey = cy - ay;\n\n    const bl = dx * dx + dy * dy;\n    const cl = ex * ex + ey * ey;\n    const d = 0.5 / (dx * ey - dy * ex);\n\n    const x = ax + (ey * bl - dy * cl) * d;\n    const y = ay + (dx * cl - ex * bl) * d;\n\n    return {x, y};\n}\n\nfunction quicksort(ids, dists, left, right) {\n    if (right - left <= 20) {\n        for (let i = left + 1; i <= right; i++) {\n            const temp = ids[i];\n            const tempDist = dists[temp];\n            let j = i - 1;\n            while (j >= left && dists[ids[j]] > tempDist) ids[j + 1] = ids[j--];\n            ids[j + 1] = temp;\n        }\n    } else {\n        const median = (left + right) >> 1;\n        let i = left + 1;\n        let j = right;\n        swap(ids, median, i);\n        if (dists[ids[left]] > dists[ids[right]]) swap(ids, left, right);\n        if (dists[ids[i]] > dists[ids[right]]) swap(ids, i, right);\n        if (dists[ids[left]] > dists[ids[i]]) swap(ids, left, i);\n\n        const temp = ids[i];\n        const tempDist = dists[temp];\n        while (true) {\n            do i++; while (dists[ids[i]] < tempDist);\n            do j--; while (dists[ids[j]] > tempDist);\n            if (j < i) break;\n            swap(ids, i, j);\n        }\n        ids[left + 1] = ids[j];\n        ids[j] = temp;\n\n        if (right - i + 1 >= j - left) {\n            quicksort(ids, dists, i, right);\n            quicksort(ids, dists, left, j - 1);\n        } else {\n            quicksort(ids, dists, left, j - 1);\n            quicksort(ids, dists, i, right);\n        }\n    }\n}\n\nfunction swap(arr, i, j) {\n    const tmp = arr[i];\n    arr[i] = arr[j];\n    arr[j] = tmp;\n}\n\nfunction defaultGetX(p) {\n    return p[0];\n}\nfunction defaultGetY(p) {\n    return p[1];\n}\n", "const epsilon = 1e-6;\n\nexport default class Path {\n  constructor() {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n  }\n  moveTo(x, y) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  }\n  lineTo(x, y) {\n    this._ += `L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arc(x, y, r) {\n    x = +x, y = +y, r = +r;\n    const x0 = x + r;\n    const y0 = y;\n    if (r < 0) throw new Error(\"negative radius\");\n    if (this._x1 === null) this._ += `M${x0},${y0}`;\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) this._ += \"L\" + x0 + \",\" + y0;\n    if (!r) return;\n    this._ += `A${r},${r},0,1,1,${x - r},${y}A${r},${r},0,1,1,${this._x1 = x0},${this._y1 = y0}`;\n  }\n  rect(x, y, w, h) {\n    this._ += `M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${+w}v${+h}h${-w}Z`;\n  }\n  value() {\n    return this._ || null;\n  }\n}\n", "export default class Polygon {\n  constructor() {\n    this._ = [];\n  }\n  moveTo(x, y) {\n    this._.push([x, y]);\n  }\n  closePath() {\n    this._.push(this._[0].slice());\n  }\n  lineTo(x, y) {\n    this._.push([x, y]);\n  }\n  value() {\n    return this._.length ? this._ : null;\n  }\n}\n", "import Path from \"./path.js\";\nimport Polygon from \"./polygon.js\";\n\nexport default class Voronoi {\n  constructor(delaunay, [xmin, ymin, xmax, ymax] = [0, 0, 960, 500]) {\n    if (!((xmax = +xmax) >= (xmin = +xmin)) || !((ymax = +ymax) >= (ymin = +ymin))) throw new Error(\"invalid bounds\");\n    this.delaunay = delaunay;\n    this._circumcenters = new Float64Array(delaunay.points.length * 2);\n    this.vectors = new Float64Array(delaunay.points.length * 2);\n    this.xmax = xmax, this.xmin = xmin;\n    this.ymax = ymax, this.ymin = ymin;\n    this._init();\n  }\n  update() {\n    this.delaunay.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const {delaunay: {points, hull, triangles}, vectors} = this;\n    let bx, by; // lazily computed barycenter of the hull\n\n    // Compute circumcenters.\n    const circumcenters = this.circumcenters = this._circumcenters.subarray(0, triangles.length / 3 * 2);\n    for (let i = 0, j = 0, n = triangles.length, x, y; i < n; i += 3, j += 2) {\n      const t1 = triangles[i] * 2;\n      const t2 = triangles[i + 1] * 2;\n      const t3 = triangles[i + 2] * 2;\n      const x1 = points[t1];\n      const y1 = points[t1 + 1];\n      const x2 = points[t2];\n      const y2 = points[t2 + 1];\n      const x3 = points[t3];\n      const y3 = points[t3 + 1];\n\n      const dx = x2 - x1;\n      const dy = y2 - y1;\n      const ex = x3 - x1;\n      const ey = y3 - y1;\n      const ab = (dx * ey - dy * ex) * 2;\n\n      if (Math.abs(ab) < 1e-9) {\n        // For a degenerate triangle, the circumcenter is at the infinity, in a\n        // direction orthogonal to the halfedge and away from the “center” of\n        // the diagram <bx, by>, defined as the hull’s barycenter.\n        if (bx === undefined) {\n          bx = by = 0;\n          for (const i of hull) bx += points[i * 2], by += points[i * 2 + 1];\n          bx /= hull.length, by /= hull.length;\n        }\n        const a = 1e9 * Math.sign((bx - x1) * ey - (by - y1) * ex);\n        x = (x1 + x3) / 2 - a * ey;\n        y = (y1 + y3) / 2 + a * ex;\n      } else {\n        const d = 1 / ab;\n        const bl = dx * dx + dy * dy;\n        const cl = ex * ex + ey * ey;\n        x = x1 + (ey * bl - dy * cl) * d;\n        y = y1 + (dx * cl - ex * bl) * d;\n      }\n      circumcenters[j] = x;\n      circumcenters[j + 1] = y;\n    }\n\n    // Compute exterior cell rays.\n    let h = hull[hull.length - 1];\n    let p0, p1 = h * 4;\n    let x0, x1 = points[2 * h];\n    let y0, y1 = points[2 * h + 1];\n    vectors.fill(0);\n    for (let i = 0; i < hull.length; ++i) {\n      h = hull[i];\n      p0 = p1, x0 = x1, y0 = y1;\n      p1 = h * 4, x1 = points[2 * h], y1 = points[2 * h + 1];\n      vectors[p0 + 2] = vectors[p1] = y0 - y1;\n      vectors[p0 + 3] = vectors[p1 + 1] = x1 - x0;\n    }\n  }\n  render(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {delaunay: {halfedges, inedges, hull}, circumcenters, vectors} = this;\n    if (hull.length <= 1) return null;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = Math.floor(i / 3) * 2;\n      const tj = Math.floor(j / 3) * 2;\n      const xi = circumcenters[ti];\n      const yi = circumcenters[ti + 1];\n      const xj = circumcenters[tj];\n      const yj = circumcenters[tj + 1];\n      this._renderSegment(xi, yi, xj, yj, context);\n    }\n    let h0, h1 = hull[hull.length - 1];\n    for (let i = 0; i < hull.length; ++i) {\n      h0 = h1, h1 = hull[i];\n      const t = Math.floor(inedges[h1] / 3) * 2;\n      const x = circumcenters[t];\n      const y = circumcenters[t + 1];\n      const v = h0 * 4;\n      const p = this._project(x, y, vectors[v + 2], vectors[v + 3]);\n      if (p) this._renderSegment(x, y, p[0], p[1], context);\n    }\n    return buffer && buffer.value();\n  }\n  renderBounds(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    context.rect(this.xmin, this.ymin, this.xmax - this.xmin, this.ymax - this.ymin);\n    return buffer && buffer.value();\n  }\n  renderCell(i, context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const points = this._clip(i);\n    if (points === null || !points.length) return;\n    context.moveTo(points[0], points[1]);\n    let n = points.length;\n    while (points[0] === points[n-2] && points[1] === points[n-1] && n > 1) n -= 2;\n    for (let i = 2; i < n; i += 2) {\n      if (points[i] !== points[i-2] || points[i+1] !== points[i-1])\n        context.lineTo(points[i], points[i + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *cellPolygons() {\n    const {delaunay: {points}} = this;\n    for (let i = 0, n = points.length / 2; i < n; ++i) {\n      const cell = this.cellPolygon(i);\n      if (cell) cell.index = i, yield cell;\n    }\n  }\n  cellPolygon(i) {\n    const polygon = new Polygon;\n    this.renderCell(i, polygon);\n    return polygon.value();\n  }\n  _renderSegment(x0, y0, x1, y1, context) {\n    let S;\n    const c0 = this._regioncode(x0, y0);\n    const c1 = this._regioncode(x1, y1);\n    if (c0 === 0 && c1 === 0) {\n      context.moveTo(x0, y0);\n      context.lineTo(x1, y1);\n    } else if (S = this._clipSegment(x0, y0, x1, y1, c0, c1)) {\n      context.moveTo(S[0], S[1]);\n      context.lineTo(S[2], S[3]);\n    }\n  }\n  contains(i, x, y) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return false;\n    return this.delaunay._step(i, x, y) === i;\n  }\n  *neighbors(i) {\n    const ci = this._clip(i);\n    if (ci) for (const j of this.delaunay.neighbors(i)) {\n      const cj = this._clip(j);\n      // find the common edge\n      if (cj) loop: for (let ai = 0, li = ci.length; ai < li; ai += 2) {\n        for (let aj = 0, lj = cj.length; aj < lj; aj += 2) {\n          if (ci[ai] === cj[aj]\n              && ci[ai + 1] === cj[aj + 1]\n              && ci[(ai + 2) % li] === cj[(aj + lj - 2) % lj]\n              && ci[(ai + 3) % li] === cj[(aj + lj - 1) % lj]) {\n            yield j;\n            break loop;\n          }\n        }\n      }\n    }\n  }\n  _cell(i) {\n    const {circumcenters, delaunay: {inedges, halfedges, triangles}} = this;\n    const e0 = inedges[i];\n    if (e0 === -1) return null; // coincident point\n    const points = [];\n    let e = e0;\n    do {\n      const t = Math.floor(e / 3);\n      points.push(circumcenters[t * 2], circumcenters[t * 2 + 1]);\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n    } while (e !== e0 && e !== -1);\n    return points;\n  }\n  _clip(i) {\n    // degenerate case (1 valid point: return the box)\n    if (i === 0 && this.delaunay.hull.length === 1) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    const points = this._cell(i);\n    if (points === null) return null;\n    const {vectors: V} = this;\n    const v = i * 4;\n    return this._simplify(V[v] || V[v + 1]\n        ? this._clipInfinite(i, points, V[v], V[v + 1], V[v + 2], V[v + 3])\n        : this._clipFinite(i, points));\n  }\n  _clipFinite(i, points) {\n    const n = points.length;\n    let P = null;\n    let x0, y0, x1 = points[n - 2], y1 = points[n - 1];\n    let c0, c1 = this._regioncode(x1, y1);\n    let e0, e1 = 0;\n    for (let j = 0; j < n; j += 2) {\n      x0 = x1, y0 = y1, x1 = points[j], y1 = points[j + 1];\n      c0 = c1, c1 = this._regioncode(x1, y1);\n      if (c0 === 0 && c1 === 0) {\n        e0 = e1, e1 = 0;\n        if (P) P.push(x1, y1);\n        else P = [x1, y1];\n      } else {\n        let S, sx0, sy0, sx1, sy1;\n        if (c0 === 0) {\n          if ((S = this._clipSegment(x0, y0, x1, y1, c0, c1)) === null) continue;\n          [sx0, sy0, sx1, sy1] = S;\n        } else {\n          if ((S = this._clipSegment(x1, y1, x0, y0, c1, c0)) === null) continue;\n          [sx1, sy1, sx0, sy0] = S;\n          e0 = e1, e1 = this._edgecode(sx0, sy0);\n          if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n          if (P) P.push(sx0, sy0);\n          else P = [sx0, sy0];\n        }\n        e0 = e1, e1 = this._edgecode(sx1, sy1);\n        if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n        if (P) P.push(sx1, sy1);\n        else P = [sx1, sy1];\n      }\n    }\n    if (P) {\n      e0 = e1, e1 = this._edgecode(P[0], P[1]);\n      if (e0 && e1) this._edge(i, e0, e1, P, P.length);\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      return [this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax, this.xmin, this.ymin];\n    }\n    return P;\n  }\n  _clipSegment(x0, y0, x1, y1, c0, c1) {\n    // for more robustness, always consider the segment in the same order\n    const flip = c0 < c1;\n    if (flip) [x0, y0, x1, y1, c0, c1] = [x1, y1, x0, y0, c1, c0];\n    while (true) {\n      if (c0 === 0 && c1 === 0) return flip ? [x1, y1, x0, y0] : [x0, y0, x1, y1];\n      if (c0 & c1) return null;\n      let x, y, c = c0 || c1;\n      if (c & 0b1000) x = x0 + (x1 - x0) * (this.ymax - y0) / (y1 - y0), y = this.ymax;\n      else if (c & 0b0100) x = x0 + (x1 - x0) * (this.ymin - y0) / (y1 - y0), y = this.ymin;\n      else if (c & 0b0010) y = y0 + (y1 - y0) * (this.xmax - x0) / (x1 - x0), x = this.xmax;\n      else y = y0 + (y1 - y0) * (this.xmin - x0) / (x1 - x0), x = this.xmin;\n      if (c0) x0 = x, y0 = y, c0 = this._regioncode(x0, y0);\n      else x1 = x, y1 = y, c1 = this._regioncode(x1, y1);\n    }\n  }\n  _clipInfinite(i, points, vx0, vy0, vxn, vyn) {\n    let P = Array.from(points), p;\n    if (p = this._project(P[0], P[1], vx0, vy0)) P.unshift(p[0], p[1]);\n    if (p = this._project(P[P.length - 2], P[P.length - 1], vxn, vyn)) P.push(p[0], p[1]);\n    if (P = this._clipFinite(i, P)) {\n      for (let j = 0, n = P.length, c0, c1 = this._edgecode(P[n - 2], P[n - 1]); j < n; j += 2) {\n        c0 = c1, c1 = this._edgecode(P[j], P[j + 1]);\n        if (c0 && c1) j = this._edge(i, c0, c1, P, j), n = P.length;\n      }\n    } else if (this.contains(i, (this.xmin + this.xmax) / 2, (this.ymin + this.ymax) / 2)) {\n      P = [this.xmin, this.ymin, this.xmax, this.ymin, this.xmax, this.ymax, this.xmin, this.ymax];\n    }\n    return P;\n  }\n  _edge(i, e0, e1, P, j) {\n    while (e0 !== e1) {\n      let x, y;\n      switch (e0) {\n        case 0b0101: e0 = 0b0100; continue; // top-left\n        case 0b0100: e0 = 0b0110, x = this.xmax, y = this.ymin; break; // top\n        case 0b0110: e0 = 0b0010; continue; // top-right\n        case 0b0010: e0 = 0b1010, x = this.xmax, y = this.ymax; break; // right\n        case 0b1010: e0 = 0b1000; continue; // bottom-right\n        case 0b1000: e0 = 0b1001, x = this.xmin, y = this.ymax; break; // bottom\n        case 0b1001: e0 = 0b0001; continue; // bottom-left\n        case 0b0001: e0 = 0b0101, x = this.xmin, y = this.ymin; break; // left\n      }\n      // Note: this implicitly checks for out of bounds: if P[j] or P[j+1] are\n      // undefined, the conditional statement will be executed.\n      if ((P[j] !== x || P[j + 1] !== y) && this.contains(i, x, y)) {\n        P.splice(j, 0, x, y), j += 2;\n      }\n    }\n    return j;\n  }\n  _project(x0, y0, vx, vy) {\n    let t = Infinity, c, x, y;\n    if (vy < 0) { // top\n      if (y0 <= this.ymin) return null;\n      if ((c = (this.ymin - y0) / vy) < t) y = this.ymin, x = x0 + (t = c) * vx;\n    } else if (vy > 0) { // bottom\n      if (y0 >= this.ymax) return null;\n      if ((c = (this.ymax - y0) / vy) < t) y = this.ymax, x = x0 + (t = c) * vx;\n    }\n    if (vx > 0) { // right\n      if (x0 >= this.xmax) return null;\n      if ((c = (this.xmax - x0) / vx) < t) x = this.xmax, y = y0 + (t = c) * vy;\n    } else if (vx < 0) { // left\n      if (x0 <= this.xmin) return null;\n      if ((c = (this.xmin - x0) / vx) < t) x = this.xmin, y = y0 + (t = c) * vy;\n    }\n    return [x, y];\n  }\n  _edgecode(x, y) {\n    return (x === this.xmin ? 0b0001\n        : x === this.xmax ? 0b0010 : 0b0000)\n        | (y === this.ymin ? 0b0100\n        : y === this.ymax ? 0b1000 : 0b0000);\n  }\n  _regioncode(x, y) {\n    return (x < this.xmin ? 0b0001\n        : x > this.xmax ? 0b0010 : 0b0000)\n        | (y < this.ymin ? 0b0100\n        : y > this.ymax ? 0b1000 : 0b0000);\n  }\n  _simplify(P) {\n    if (P && P.length > 4) {\n      for (let i = 0; i < P.length; i+= 2) {\n        const j = (i + 2) % P.length, k = (i + 4) % P.length;\n        if (P[i] === P[j] && P[j] === P[k] || P[i + 1] === P[j + 1] && P[j + 1] === P[k + 1]) {\n          P.splice(j, 2), i -= 2;\n        }\n      }\n      if (!P.length) P = null;\n    }\n    return P;\n  }\n}\n", "import Delaunator from \"delaunator\";\nimport Path from \"./path.js\";\nimport Polygon from \"./polygon.js\";\nimport Voronoi from \"./voronoi.js\";\n\nconst tau = 2 * Math.PI, pow = Math.pow;\n\nfunction pointX(p) {\n  return p[0];\n}\n\nfunction pointY(p) {\n  return p[1];\n}\n\n// A triangulation is collinear if all its triangles have a non-null area\nfunction collinear(d) {\n  const {triangles, coords} = d;\n  for (let i = 0; i < triangles.length; i += 3) {\n    const a = 2 * triangles[i],\n          b = 2 * triangles[i + 1],\n          c = 2 * triangles[i + 2],\n          cross = (coords[c] - coords[a]) * (coords[b + 1] - coords[a + 1])\n                - (coords[b] - coords[a]) * (coords[c + 1] - coords[a + 1]);\n    if (cross > 1e-10) return false;\n  }\n  return true;\n}\n\nfunction jitter(x, y, r) {\n  return [x + Math.sin(x + y) * r, y + Math.cos(x - y) * r];\n}\n\nexport default class Delaunay {\n  static from(points, fx = pointX, fy = pointY, that) {\n    return new Delaunay(\"length\" in points\n        ? flatArray(points, fx, fy, that)\n        : Float64Array.from(flatIterable(points, fx, fy, that)));\n  }\n  constructor(points) {\n    this._delaunator = new Delaunator(points);\n    this.inedges = new Int32Array(points.length / 2);\n    this._hullIndex = new Int32Array(points.length / 2);\n    this.points = this._delaunator.coords;\n    this._init();\n  }\n  update() {\n    this._delaunator.update();\n    this._init();\n    return this;\n  }\n  _init() {\n    const d = this._delaunator, points = this.points;\n\n    // check for collinear\n    if (d.hull && d.hull.length > 2 && collinear(d)) {\n      this.collinear = Int32Array.from({length: points.length/2}, (_,i) => i)\n        .sort((i, j) => points[2 * i] - points[2 * j] || points[2 * i + 1] - points[2 * j + 1]); // for exact neighbors\n      const e = this.collinear[0], f = this.collinear[this.collinear.length - 1],\n        bounds = [ points[2 * e], points[2 * e + 1], points[2 * f], points[2 * f + 1] ],\n        r = 1e-8 * Math.hypot(bounds[3] - bounds[1], bounds[2] - bounds[0]);\n      for (let i = 0, n = points.length / 2; i < n; ++i) {\n        const p = jitter(points[2 * i], points[2 * i + 1], r);\n        points[2 * i] = p[0];\n        points[2 * i + 1] = p[1];\n      }\n      this._delaunator = new Delaunator(points);\n    } else {\n      delete this.collinear;\n    }\n\n    const halfedges = this.halfedges = this._delaunator.halfedges;\n    const hull = this.hull = this._delaunator.hull;\n    const triangles = this.triangles = this._delaunator.triangles;\n    const inedges = this.inedges.fill(-1);\n    const hullIndex = this._hullIndex.fill(-1);\n\n    // Compute an index from each point to an (arbitrary) incoming halfedge\n    // Used to give the first neighbor of each point; for this reason,\n    // on the hull we give priority to exterior halfedges\n    for (let e = 0, n = halfedges.length; e < n; ++e) {\n      const p = triangles[e % 3 === 2 ? e - 2 : e + 1];\n      if (halfedges[e] === -1 || inedges[p] === -1) inedges[p] = e;\n    }\n    for (let i = 0, n = hull.length; i < n; ++i) {\n      hullIndex[hull[i]] = i;\n    }\n\n    // degenerate case: 1 or 2 (distinct) points\n    if (hull.length <= 2 && hull.length > 0) {\n      this.triangles = new Int32Array(3).fill(-1);\n      this.halfedges = new Int32Array(3).fill(-1);\n      this.triangles[0] = hull[0];\n      inedges[hull[0]] = 1;\n      if (hull.length === 2) {\n        inedges[hull[1]] = 0;\n        this.triangles[1] = hull[1];\n        this.triangles[2] = hull[1];\n      }\n    }\n  }\n  voronoi(bounds) {\n    return new Voronoi(this, bounds);\n  }\n  *neighbors(i) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, collinear} = this;\n\n    // degenerate case with several collinear points\n    if (collinear) {\n      const l = collinear.indexOf(i);\n      if (l > 0) yield collinear[l - 1];\n      if (l < collinear.length - 1) yield collinear[l + 1];\n      return;\n    }\n\n    const e0 = inedges[i];\n    if (e0 === -1) return; // coincident point\n    let e = e0, p0 = -1;\n    do {\n      yield p0 = triangles[e];\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) return; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        const p = hull[(_hullIndex[i] + 1) % hull.length];\n        if (p !== p0) yield p;\n        return;\n      }\n    } while (e !== e0);\n  }\n  find(x, y, i = 0) {\n    if ((x = +x, x !== x) || (y = +y, y !== y)) return -1;\n    const i0 = i;\n    let c;\n    while ((c = this._step(i, x, y)) >= 0 && c !== i && c !== i0) i = c;\n    return c;\n  }\n  _step(i, x, y) {\n    const {inedges, hull, _hullIndex, halfedges, triangles, points} = this;\n    if (inedges[i] === -1 || !points.length) return (i + 1) % (points.length >> 1);\n    let c = i;\n    let dc = pow(x - points[i * 2], 2) + pow(y - points[i * 2 + 1], 2);\n    const e0 = inedges[i];\n    let e = e0;\n    do {\n      let t = triangles[e];\n      const dt = pow(x - points[t * 2], 2) + pow(y - points[t * 2 + 1], 2);\n      if (dt < dc) dc = dt, c = t;\n      e = e % 3 === 2 ? e - 2 : e + 1;\n      if (triangles[e] !== i) break; // bad triangulation\n      e = halfedges[e];\n      if (e === -1) {\n        e = hull[(_hullIndex[i] + 1) % hull.length];\n        if (e !== t) {\n          if (pow(x - points[e * 2], 2) + pow(y - points[e * 2 + 1], 2) < dc) return e;\n        }\n        break;\n      }\n    } while (e !== e0);\n    return c;\n  }\n  render(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {points, halfedges, triangles} = this;\n    for (let i = 0, n = halfedges.length; i < n; ++i) {\n      const j = halfedges[i];\n      if (j < i) continue;\n      const ti = triangles[i] * 2;\n      const tj = triangles[j] * 2;\n      context.moveTo(points[ti], points[ti + 1]);\n      context.lineTo(points[tj], points[tj + 1]);\n    }\n    this.renderHull(context);\n    return buffer && buffer.value();\n  }\n  renderPoints(context, r) {\n    if (r === undefined && (!context || typeof context.moveTo !== \"function\")) r = context, context = null;\n    r = r == undefined ? 2 : +r;\n    const buffer = context == null ? context = new Path : undefined;\n    const {points} = this;\n    for (let i = 0, n = points.length; i < n; i += 2) {\n      const x = points[i], y = points[i + 1];\n      context.moveTo(x + r, y);\n      context.arc(x, y, r, 0, tau);\n    }\n    return buffer && buffer.value();\n  }\n  renderHull(context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {hull, points} = this;\n    const h = hull[0] * 2, n = hull.length;\n    context.moveTo(points[h], points[h + 1]);\n    for (let i = 1; i < n; ++i) {\n      const h = 2 * hull[i];\n      context.lineTo(points[h], points[h + 1]);\n    }\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  hullPolygon() {\n    const polygon = new Polygon;\n    this.renderHull(polygon);\n    return polygon.value();\n  }\n  renderTriangle(i, context) {\n    const buffer = context == null ? context = new Path : undefined;\n    const {points, triangles} = this;\n    const t0 = triangles[i *= 3] * 2;\n    const t1 = triangles[i + 1] * 2;\n    const t2 = triangles[i + 2] * 2;\n    context.moveTo(points[t0], points[t0 + 1]);\n    context.lineTo(points[t1], points[t1 + 1]);\n    context.lineTo(points[t2], points[t2 + 1]);\n    context.closePath();\n    return buffer && buffer.value();\n  }\n  *trianglePolygons() {\n    const {triangles} = this;\n    for (let i = 0, n = triangles.length / 3; i < n; ++i) {\n      yield this.trianglePolygon(i);\n    }\n  }\n  trianglePolygon(i) {\n    const polygon = new Polygon;\n    this.renderTriangle(i, polygon);\n    return polygon.value();\n  }\n}\n\nfunction flatArray(points, fx, fy, that) {\n  const n = points.length;\n  const array = new Float64Array(n * 2);\n  for (let i = 0; i < n; ++i) {\n    const p = points[i];\n    array[i * 2] = fx.call(that, p, i, points);\n    array[i * 2 + 1] = fy.call(that, p, i, points);\n  }\n  return array;\n}\n\nfunction* flatIterable(points, fx, fy, that) {\n  let i = 0;\n  for (const p of points) {\n    yield fx.call(that, p, i, points);\n    yield fy.call(that, p, i, points);\n    ++i;\n  }\n}\n"], "mappings": ";AAAO,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,kBAAkB,IAAI,IAAI,WAAW;AAG3C,SAAS,IAAI,MAAM,GAAG,MAAM,GAAG,GAAG;AACrC,MAAI,GAAG,MAAM,IAAI;AACjB,MAAI,OAAO,EAAE,CAAC;AACd,MAAI,OAAO,EAAE,CAAC;AACd,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAK,OAAO,SAAW,OAAO,CAAC,MAAO;AAClC,QAAI;AACJ,WAAO,EAAE,EAAE,MAAM;AAAA,EACrB,OAAO;AACH,QAAI;AACJ,WAAO,EAAE,EAAE,MAAM;AAAA,EACrB;AACA,MAAI,SAAS;AACb,MAAI,SAAS,QAAQ,SAAS,MAAM;AAChC,QAAK,OAAO,SAAW,OAAO,CAAC,MAAO;AAClC,aAAO,OAAO;AACd,WAAK,KAAK,OAAO;AACjB,aAAO,EAAE,EAAE,MAAM;AAAA,IACrB,OAAO;AACH,aAAO,OAAO;AACd,WAAK,KAAK,OAAO;AACjB,aAAO,EAAE,EAAE,MAAM;AAAA,IACrB;AACA,QAAI;AACJ,QAAI,OAAO,GAAG;AACV,QAAE,QAAQ,IAAI;AAAA,IAClB;AACA,WAAO,SAAS,QAAQ,SAAS,MAAM;AACnC,UAAK,OAAO,SAAW,OAAO,CAAC,MAAO;AAClC,eAAO,IAAI;AACX,gBAAQ,OAAO;AACf,aAAK,KAAK,OAAO,UAAU,OAAO;AAClC,eAAO,EAAE,EAAE,MAAM;AAAA,MACrB,OAAO;AACH,eAAO,IAAI;AACX,gBAAQ,OAAO;AACf,aAAK,KAAK,OAAO,UAAU,OAAO;AAClC,eAAO,EAAE,EAAE,MAAM;AAAA,MACrB;AACA,UAAI;AACJ,UAAI,OAAO,GAAG;AACV,UAAE,QAAQ,IAAI;AAAA,MAClB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,SAAS,MAAM;AAClB,WAAO,IAAI;AACX,YAAQ,OAAO;AACf,SAAK,KAAK,OAAO,UAAU,OAAO;AAClC,WAAO,EAAE,EAAE,MAAM;AACjB,QAAI;AACJ,QAAI,OAAO,GAAG;AACV,QAAE,QAAQ,IAAI;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,SAAS,MAAM;AAClB,WAAO,IAAI;AACX,YAAQ,OAAO;AACf,SAAK,KAAK,OAAO,UAAU,OAAO;AAClC,WAAO,EAAE,EAAE,MAAM;AACjB,QAAI;AACJ,QAAI,OAAO,GAAG;AACV,QAAE,QAAQ,IAAI;AAAA,IAClB;AAAA,EACJ;AACA,MAAI,MAAM,KAAK,WAAW,GAAG;AACzB,MAAE,QAAQ,IAAI;AAAA,EAClB;AACA,SAAO;AACX;AAsDO,SAAS,SAAS,MAAM,GAAG;AAC9B,MAAI,IAAI,EAAE,CAAC;AACX,WAAS,IAAI,GAAG,IAAI,MAAM,IAAK,MAAK,EAAE,CAAC;AACvC,SAAO;AACX;AAEO,SAAS,IAAI,GAAG;AACnB,SAAO,IAAI,aAAa,CAAC;AAC7B;;;ACvIA,IAAM,gBAAgB,IAAI,KAAK,WAAW;AAC1C,IAAM,gBAAgB,IAAI,KAAK,WAAW;AAC1C,IAAM,gBAAgB,IAAI,KAAK,WAAW,UAAU;AAEpD,IAAM,IAAI,IAAI,CAAC;AACf,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,EAAE;AACjB,IAAM,IAAI,IAAI,EAAE;AAChB,IAAM,IAAI,IAAI,CAAC;AAEf,SAAS,cAAc,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,QAAQ;AACnD,MAAI,SAAS,SAAS,SAAS;AAC/B,MAAI,OAAO,GAAG,KAAK,KAAK,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAIA;AAE9D,QAAM,MAAM,KAAK;AACjB,QAAM,MAAM,KAAK;AACjB,QAAM,MAAM,KAAK;AACjB,QAAM,MAAM,KAAK;AAEjB,OAAK,MAAM;AACX,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,MAAM;AACX,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,OAAK,MAAM,KAAK,UAAU,KAAK;AAC/B,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,EAAAA,MAAK,KAAK;AACV,UAAQA,MAAK;AACb,IAAE,CAAC,IAAI,MAAMA,MAAK,UAAU,KAAK;AACjC,IAAE,CAAC,IAAIA;AAEP,MAAI,MAAM,SAAS,GAAG,CAAC;AACvB,MAAI,WAAW,eAAe;AAC9B,MAAI,OAAO,YAAY,CAAC,OAAO,UAAU;AACrC,WAAO;AAAA,EACX;AAEA,UAAQ,KAAK;AACb,YAAU,MAAM,MAAM,UAAU,QAAQ;AACxC,UAAQ,KAAK;AACb,YAAU,MAAM,MAAM,UAAU,QAAQ;AACxC,UAAQ,KAAK;AACb,YAAU,MAAM,MAAM,UAAU,QAAQ;AACxC,UAAQ,KAAK;AACb,YAAU,MAAM,MAAM,UAAU,QAAQ;AAExC,MAAI,YAAY,KAAK,YAAY,KAAK,YAAY,KAAK,YAAY,GAAG;AAClE,WAAO;AAAA,EACX;AAEA,aAAW,eAAe,SAAS,iBAAiB,KAAK,IAAI,GAAG;AAChE,SAAQ,MAAM,UAAU,MAAM,WAAY,MAAM,UAAU,MAAM;AAChE,MAAI,OAAO,YAAY,CAAC,OAAO,SAAU,QAAO;AAEhD,OAAK,UAAU;AACf,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,UAAU;AACf,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,OAAK,MAAM,KAAK,UAAU,KAAK;AAC/B,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,EAAAA,MAAK,KAAK;AACV,UAAQA,MAAK;AACb,IAAE,CAAC,IAAI,MAAMA,MAAK,UAAU,KAAK;AACjC,IAAE,CAAC,IAAIA;AACP,QAAM,QAAQ,IAAI,GAAG,GAAG,GAAG,GAAG,EAAE;AAEhC,OAAK,MAAM;AACX,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,MAAM;AACX,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,MAAM;AACZ,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,OAAK,MAAM,KAAK,UAAU,KAAK;AAC/B,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,EAAAA,MAAK,KAAK;AACV,UAAQA,MAAK;AACb,IAAE,CAAC,IAAI,MAAMA,MAAK,UAAU,KAAK;AACjC,IAAE,CAAC,IAAIA;AACP,QAAM,QAAQ,IAAI,OAAO,IAAI,GAAG,GAAG,EAAE;AAErC,OAAK,UAAU;AACf,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,UAAU;AACf,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,MAAI,WAAW;AACf,QAAM,KAAK,IAAI;AACf,QAAM,UAAU;AAChB,OAAK,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM;AACrD,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,OAAK,MAAM,KAAK,UAAU,KAAK;AAC/B,OAAK,KAAK;AACV,UAAQ,KAAK;AACb,IAAE,CAAC,IAAI,MAAM,KAAK,UAAU,QAAQ;AACpC,EAAAA,MAAK,KAAK;AACV,UAAQA,MAAK;AACb,IAAE,CAAC,IAAI,MAAMA,MAAK,UAAU,KAAK;AACjC,IAAE,CAAC,IAAIA;AACP,QAAM,OAAO,IAAI,OAAO,IAAI,GAAG,GAAG,CAAC;AAEnC,SAAO,EAAE,OAAO,CAAC;AACrB;AAEO,SAAS,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC7C,QAAM,WAAW,KAAK,OAAO,KAAK;AAClC,QAAM,YAAY,KAAK,OAAO,KAAK;AACnC,QAAM,MAAM,UAAU;AAEtB,QAAM,SAAS,KAAK,IAAI,UAAU,QAAQ;AAC1C,MAAI,KAAK,IAAI,GAAG,KAAK,eAAe,OAAQ,QAAO;AAEnD,SAAO,CAAC,cAAc,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM;AACxD;;;ACjLA,IAAM,gBAAgB,IAAI,KAAK,WAAW;AAC1C,IAAM,gBAAgB,IAAI,KAAK,WAAW;AAC1C,IAAM,gBAAgB,KAAK,MAAM,WAAW,UAAU;AAEtD,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAMC,KAAI,IAAI,CAAC;AAEf,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,EAAE;AAElB,IAAI,MAAM,IAAI,GAAG;AACjB,IAAI,OAAO,IAAI,GAAG;;;ACxBlB,IAAM,gBAAgB,KAAK,KAAK,WAAW;AAC3C,IAAM,gBAAgB,IAAI,KAAK,WAAW;AAC1C,IAAM,gBAAgB,KAAK,MAAM,WAAW,UAAU;AAEtD,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAMC,KAAI,IAAI,CAAC;AACf,IAAM,IAAI,IAAI,CAAC;AACf,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAM,QAAQ,IAAI,CAAC;AACnB,IAAMC,OAAM,IAAI,CAAC;AACjB,IAAMC,OAAM,IAAI,CAAC;AACjB,IAAMC,OAAM,IAAI,CAAC;AACjB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAClB,IAAM,OAAO,IAAI,CAAC;AAElB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,OAAM,IAAI,EAAE;AAClB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAElB,IAAIC,OAAM,IAAI,IAAI;AAClB,IAAIC,QAAO,IAAI,IAAI;;;ACnCnB,IAAM,gBAAgB,KAAK,MAAM,WAAW;AAC5C,IAAM,gBAAgB,IAAI,KAAK,WAAW;AAC1C,IAAM,gBAAgB,KAAK,OAAO,WAAW,UAAU;AAEvD,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAChB,IAAM,KAAK,IAAI,CAAC;AAEhB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAElB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,OAAO,IAAI,IAAI;AACrB,IAAM,QAAQ,IAAI,IAAI;AACtB,IAAM,QAAQ,IAAI,IAAI;AACtB,IAAM,SAAS,IAAI,IAAI;AACvB,IAAM,QAAQ,IAAI,IAAI;AAEtB,IAAMC,MAAK,IAAI,CAAC;AAChB,IAAMC,OAAM,IAAI,CAAC;AACjB,IAAM,MAAM,IAAI,CAAC;AACjB,IAAMC,OAAM,IAAI,EAAE;AAClB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAMC,OAAM,IAAI,EAAE;AAClB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,MAAM,IAAI,EAAE;AAClB,IAAM,OAAO,IAAI,GAAG;AACpB,IAAM,QAAQ,IAAI,GAAG;AACrB,IAAM,QAAQ,IAAI,GAAG;AACrB,IAAM,QAAQ,IAAI,GAAG;AACrB,IAAM,OAAO,IAAI,GAAG;AAgVpB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAM,OAAO,IAAI,EAAE;AACnB,IAAMC,OAAM,IAAI,IAAI;;;ACpYpB,IAAM,UAAU,KAAK,IAAI,GAAG,GAAG;AAC/B,IAAM,aAAa,IAAI,YAAY,GAAG;AAItC,IAAqB,aAArB,MAAqB,YAAW;AAAA,EAE5B,OAAO,KAAK,QAAQ,OAAO,aAAa,OAAO,aAAa;AACxD,UAAM,IAAI,OAAO;AACjB,UAAM,SAAS,IAAI,aAAa,IAAI,CAAC;AAErC,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,YAAM,IAAI,OAAO,CAAC;AAClB,aAAO,IAAI,CAAC,IAAI,KAAK,CAAC;AACtB,aAAO,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC;AAAA,IAC9B;AAEA,WAAO,IAAI,YAAW,MAAM;AAAA,EAChC;AAAA,EAEA,YAAY,QAAQ;AAChB,UAAM,IAAI,OAAO,UAAU;AAC3B,QAAI,IAAI,KAAK,OAAO,OAAO,CAAC,MAAM,SAAU,OAAM,IAAI,MAAM,qCAAqC;AAEjG,SAAK,SAAS;AAGd,UAAM,eAAe,KAAK,IAAI,IAAI,IAAI,GAAG,CAAC;AAC1C,SAAK,aAAa,IAAI,YAAY,eAAe,CAAC;AAClD,SAAK,aAAa,IAAI,WAAW,eAAe,CAAC;AAGjD,SAAK,YAAY,KAAK,KAAK,KAAK,KAAK,CAAC,CAAC;AACvC,SAAK,YAAY,IAAI,YAAY,CAAC;AAClC,SAAK,YAAY,IAAI,YAAY,CAAC;AAClC,SAAK,WAAW,IAAI,YAAY,CAAC;AACjC,SAAK,YAAY,IAAI,WAAW,KAAK,SAAS;AAG9C,SAAK,OAAO,IAAI,YAAY,CAAC;AAC7B,SAAK,SAAS,IAAI,aAAa,CAAC;AAEhC,SAAK,OAAO;AAAA,EAChB;AAAA,EAEA,SAAS;AACL,UAAM,EAAC,QAAQ,WAAW,UAAU,WAAW,UAAU,UAAU,SAAS,WAAW,SAAQ,IAAK;AACpG,UAAM,IAAI,OAAO,UAAU;AAG3B,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AAEX,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,YAAM,IAAI,OAAO,IAAI,CAAC;AACtB,YAAM,IAAI,OAAO,IAAI,IAAI,CAAC;AAC1B,UAAI,IAAI,KAAM,QAAO;AACrB,UAAI,IAAI,KAAM,QAAO;AACrB,UAAI,IAAI,KAAM,QAAO;AACrB,UAAI,IAAI,KAAM,QAAO;AACrB,WAAK,KAAK,CAAC,IAAI;AAAA,IACnB;AACA,UAAM,MAAM,OAAO,QAAQ;AAC3B,UAAM,MAAM,OAAO,QAAQ;AAE3B,QAAI,IAAI,IAAI;AAGZ,aAAS,IAAI,GAAG,UAAU,UAAU,IAAI,GAAG,KAAK;AAC5C,YAAM,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC;AACvD,UAAI,IAAI,SAAS;AACb,aAAK;AACL,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,UAAM,MAAM,OAAO,IAAI,EAAE;AACzB,UAAM,MAAM,OAAO,IAAI,KAAK,CAAC;AAG7B,aAAS,IAAI,GAAG,UAAU,UAAU,IAAI,GAAG,KAAK;AAC5C,UAAI,MAAM,GAAI;AACd,YAAM,IAAI,KAAK,KAAK,KAAK,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC;AACzD,UAAI,IAAI,WAAW,IAAI,GAAG;AACtB,aAAK;AACL,kBAAU;AAAA,MACd;AAAA,IACJ;AACA,QAAI,MAAM,OAAO,IAAI,EAAE;AACvB,QAAI,MAAM,OAAO,IAAI,KAAK,CAAC;AAE3B,QAAI,YAAY;AAGhB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,UAAI,MAAM,MAAM,MAAM,GAAI;AAC1B,YAAM,IAAI,aAAa,KAAK,KAAK,KAAK,KAAK,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC;AAC3E,UAAI,IAAI,WAAW;AACf,aAAK;AACL,oBAAY;AAAA,MAChB;AAAA,IACJ;AACA,QAAI,MAAM,OAAO,IAAI,EAAE;AACvB,QAAI,MAAM,OAAO,IAAI,KAAK,CAAC;AAE3B,QAAI,cAAc,UAAU;AAGxB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,aAAK,OAAO,CAAC,IAAK,OAAO,IAAI,CAAC,IAAI,OAAO,CAAC,KAAO,OAAO,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC;AAAA,MACjF;AACA,gBAAU,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI,CAAC;AAC1C,YAAM,OAAO,IAAI,YAAY,CAAC;AAC9B,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK;AACxC,cAAM,KAAK,KAAK,KAAK,CAAC;AACtB,cAAM,IAAI,KAAK,OAAO,EAAE;AACxB,YAAI,IAAI,IAAI;AACR,eAAK,GAAG,IAAI;AACZ,eAAK;AAAA,QACT;AAAA,MACJ;AACA,WAAK,OAAO,KAAK,SAAS,GAAG,CAAC;AAC9B,WAAK,YAAY,IAAI,YAAY,CAAC;AAClC,WAAK,YAAY,IAAI,YAAY,CAAC;AAClC;AAAA,IACJ;AAGA,QAAI,SAAS,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI,GAAG;AAC5C,YAAM,IAAI;AACV,YAAM,IAAI;AACV,YAAM,IAAI;AACV,WAAK;AACL,YAAM;AACN,YAAM;AACN,WAAK;AACL,YAAM;AACN,YAAM;AAAA,IACV;AAEA,UAAM,SAAS,aAAa,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACxD,SAAK,MAAM,OAAO;AAClB,SAAK,MAAM,OAAO;AAElB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,WAAK,OAAO,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,CAAC;AAAA,IAC9E;AAGA,cAAU,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI,CAAC;AAG1C,SAAK,aAAa;AAClB,QAAI,WAAW;AAEf,aAAS,EAAE,IAAI,SAAS,EAAE,IAAI;AAC9B,aAAS,EAAE,IAAI,SAAS,EAAE,IAAI;AAC9B,aAAS,EAAE,IAAI,SAAS,EAAE,IAAI;AAE9B,YAAQ,EAAE,IAAI;AACd,YAAQ,EAAE,IAAI;AACd,YAAQ,EAAE,IAAI;AAEd,aAAS,KAAK,EAAE;AAChB,aAAS,KAAK,SAAS,KAAK,GAAG,CAAC,IAAI;AACpC,aAAS,KAAK,SAAS,KAAK,GAAG,CAAC,IAAI;AACpC,aAAS,KAAK,SAAS,KAAK,GAAG,CAAC,IAAI;AAEpC,SAAK,eAAe;AACpB,SAAK,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAExC,aAAS,IAAI,GAAG,IAAI,IAAI,IAAI,KAAK,KAAK,QAAQ,KAAK;AAC/C,YAAM,IAAI,KAAK,KAAK,CAAC;AACrB,YAAM,IAAI,OAAO,IAAI,CAAC;AACtB,YAAM,IAAI,OAAO,IAAI,IAAI,CAAC;AAG1B,UAAI,IAAI,KAAK,KAAK,IAAI,IAAI,EAAE,KAAK,WAAW,KAAK,IAAI,IAAI,EAAE,KAAK,QAAS;AACzE,WAAK;AACL,WAAK;AAGL,UAAI,MAAM,MAAM,MAAM,MAAM,MAAM,GAAI;AAGtC,UAAI,QAAQ;AACZ,eAAS,IAAI,GAAG,MAAM,KAAK,SAAS,GAAG,CAAC,GAAG,IAAI,KAAK,WAAW,KAAK;AAChE,gBAAQ,UAAU,MAAM,KAAK,KAAK,SAAS;AAC3C,YAAI,UAAU,MAAM,UAAU,SAAS,KAAK,EAAG;AAAA,MACnD;AAEA,cAAQ,SAAS,KAAK;AACtB,UAAI,IAAI,OAAO;AACf,aAAO,IAAI,SAAS,CAAC,GAAG,SAAS,GAAG,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,KAAK,GAAG;AAC7G,YAAI;AACJ,YAAI,MAAM,OAAO;AACb,cAAI;AACJ;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,MAAM,GAAI;AAGd,UAAI,IAAI,KAAK,aAAa,GAAG,GAAG,SAAS,CAAC,GAAG,IAAI,IAAI,QAAQ,CAAC,CAAC;AAG/D,cAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC;AACjC,cAAQ,CAAC,IAAI;AACb;AAGA,UAAIC,KAAI,SAAS,CAAC;AAClB,aAAO,IAAI,SAASA,EAAC,GAAG,SAAS,GAAG,GAAG,OAAO,IAAIA,EAAC,GAAG,OAAO,IAAIA,KAAI,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,IAAI,GAAG;AAC5G,YAAI,KAAK,aAAaA,IAAG,GAAG,GAAG,QAAQ,CAAC,GAAG,IAAI,QAAQA,EAAC,CAAC;AACzD,gBAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC;AACjC,iBAASA,EAAC,IAAIA;AACd;AACA,QAAAA,KAAI;AAAA,MACR;AAGA,UAAI,MAAM,OAAO;AACb,eAAO,IAAI,SAAS,CAAC,GAAG,SAAS,GAAG,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,IAAI,GAAG;AAC5G,cAAI,KAAK,aAAa,GAAG,GAAG,GAAG,IAAI,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AACzD,eAAK,UAAU,IAAI,CAAC;AACpB,kBAAQ,CAAC,IAAI;AACb,mBAAS,CAAC,IAAI;AACd;AACA,cAAI;AAAA,QACR;AAAA,MACJ;AAGA,WAAK,aAAa,SAAS,CAAC,IAAI;AAChC,eAAS,CAAC,IAAI,SAASA,EAAC,IAAI;AAC5B,eAAS,CAAC,IAAIA;AAGd,eAAS,KAAK,SAAS,GAAG,CAAC,CAAC,IAAI;AAChC,eAAS,KAAK,SAAS,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI;AAAA,IAChE;AAEA,SAAK,OAAO,IAAI,YAAY,QAAQ;AACpC,aAAS,IAAI,GAAG,IAAI,KAAK,YAAY,IAAI,UAAU,KAAK;AACpD,WAAK,KAAK,CAAC,IAAI;AACf,UAAI,SAAS,CAAC;AAAA,IAClB;AAGA,SAAK,YAAY,KAAK,WAAW,SAAS,GAAG,KAAK,YAAY;AAC9D,SAAK,YAAY,KAAK,WAAW,SAAS,GAAG,KAAK,YAAY;AAAA,EAClE;AAAA,EAEA,SAAS,GAAG,GAAG;AACX,WAAO,KAAK,MAAM,YAAY,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,SAAS,IAAI,KAAK;AAAA,EACvF;AAAA,EAEA,UAAU,GAAG;AACT,UAAM,EAAC,YAAY,WAAW,YAAY,WAAW,OAAM,IAAI;AAE/D,QAAI,IAAI;AACR,QAAI,KAAK;AAGT,WAAO,MAAM;AACT,YAAM,IAAI,UAAU,CAAC;AAiBrB,YAAM,KAAK,IAAI,IAAI;AACnB,WAAK,MAAM,IAAI,KAAK;AAEpB,UAAI,MAAM,IAAI;AACV,YAAI,MAAM,EAAG;AACb,YAAI,WAAW,EAAE,CAAC;AAClB;AAAA,MACJ;AAEA,YAAM,KAAK,IAAI,IAAI;AACnB,YAAM,KAAK,MAAM,IAAI,KAAK;AAC1B,YAAM,KAAK,MAAM,IAAI,KAAK;AAE1B,YAAM,KAAK,UAAU,EAAE;AACvB,YAAM,KAAK,UAAU,CAAC;AACtB,YAAM,KAAK,UAAU,EAAE;AACvB,YAAM,KAAK,UAAU,EAAE;AAEvB,YAAM,UAAU;AAAA,QACZ,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,QACjC,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,QACjC,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,QACjC,OAAO,IAAI,EAAE;AAAA,QAAG,OAAO,IAAI,KAAK,CAAC;AAAA,MAAC;AAEtC,UAAI,SAAS;AACT,kBAAU,CAAC,IAAI;AACf,kBAAU,CAAC,IAAI;AAEf,cAAM,MAAM,UAAU,EAAE;AAGxB,YAAI,QAAQ,IAAI;AACZ,cAAI,IAAI,KAAK;AACb,aAAG;AACC,gBAAI,KAAK,SAAS,CAAC,MAAM,IAAI;AACzB,mBAAK,SAAS,CAAC,IAAI;AACnB;AAAA,YACJ;AACA,gBAAI,KAAK,UAAU,CAAC;AAAA,UACxB,SAAS,MAAM,KAAK;AAAA,QACxB;AACA,aAAK,MAAM,GAAG,GAAG;AACjB,aAAK,MAAM,GAAG,UAAU,EAAE,CAAC;AAC3B,aAAK,MAAM,IAAI,EAAE;AAEjB,cAAM,KAAK,MAAM,IAAI,KAAK;AAG1B,YAAI,IAAI,WAAW,QAAQ;AACvB,qBAAW,GAAG,IAAI;AAAA,QACtB;AAAA,MACJ,OAAO;AACH,YAAI,MAAM,EAAG;AACb,YAAI,WAAW,EAAE,CAAC;AAAA,MACtB;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,MAAM,GAAG,GAAG;AACR,SAAK,WAAW,CAAC,IAAI;AACrB,QAAI,MAAM,GAAI,MAAK,WAAW,CAAC,IAAI;AAAA,EACvC;AAAA;AAAA,EAGA,aAAa,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG;AAC9B,UAAM,IAAI,KAAK;AAEf,SAAK,WAAW,CAAC,IAAI;AACrB,SAAK,WAAW,IAAI,CAAC,IAAI;AACzB,SAAK,WAAW,IAAI,CAAC,IAAI;AAEzB,SAAK,MAAM,GAAG,CAAC;AACf,SAAK,MAAM,IAAI,GAAG,CAAC;AACnB,SAAK,MAAM,IAAI,GAAG,CAAC;AAEnB,SAAK,gBAAgB;AAErB,WAAO;AAAA,EACX;AACJ;AAGA,SAAS,YAAY,IAAI,IAAI;AACzB,QAAM,IAAI,MAAM,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE;AAC1C,UAAQ,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACtC;AAEA,SAAS,KAAK,IAAI,IAAI,IAAI,IAAI;AAC1B,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,SAAO,KAAK,KAAK,KAAK;AAC1B;AAEA,SAAS,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC9C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAE1B,SAAO,MAAM,KAAK,KAAK,KAAK,MACrB,MAAM,KAAK,KAAK,KAAK,MACrB,MAAM,KAAK,KAAK,KAAK,MAAM;AACtC;AAEA,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC1C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,IAAI,OAAO,KAAK,KAAK,KAAK;AAEhC,QAAM,KAAK,KAAK,KAAK,KAAK,MAAM;AAChC,QAAM,KAAK,KAAK,KAAK,KAAK,MAAM;AAEhC,SAAO,IAAI,IAAI,IAAI;AACvB;AAEA,SAAS,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC1C,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK;AAEhB,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,QAAM,IAAI,OAAO,KAAK,KAAK,KAAK;AAEhC,QAAM,IAAI,MAAM,KAAK,KAAK,KAAK,MAAM;AACrC,QAAM,IAAI,MAAM,KAAK,KAAK,KAAK,MAAM;AAErC,SAAO,EAAC,GAAG,EAAC;AAChB;AAEA,SAAS,UAAU,KAAK,OAAO,MAAM,OAAO;AACxC,MAAI,QAAQ,QAAQ,IAAI;AACpB,aAAS,IAAI,OAAO,GAAG,KAAK,OAAO,KAAK;AACpC,YAAM,OAAO,IAAI,CAAC;AAClB,YAAM,WAAW,MAAM,IAAI;AAC3B,UAAI,IAAI,IAAI;AACZ,aAAO,KAAK,QAAQ,MAAM,IAAI,CAAC,CAAC,IAAI,SAAU,KAAI,IAAI,CAAC,IAAI,IAAI,GAAG;AAClE,UAAI,IAAI,CAAC,IAAI;AAAA,IACjB;AAAA,EACJ,OAAO;AACH,UAAM,SAAU,OAAO,SAAU;AACjC,QAAI,IAAI,OAAO;AACf,QAAI,IAAI;AACR,SAAK,KAAK,QAAQ,CAAC;AACnB,QAAI,MAAM,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,KAAK,CAAC,EAAG,MAAK,KAAK,MAAM,KAAK;AAC/D,QAAI,MAAM,IAAI,CAAC,CAAC,IAAI,MAAM,IAAI,KAAK,CAAC,EAAG,MAAK,KAAK,GAAG,KAAK;AACzD,QAAI,MAAM,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC,EAAG,MAAK,KAAK,MAAM,CAAC;AAEvD,UAAM,OAAO,IAAI,CAAC;AAClB,UAAM,WAAW,MAAM,IAAI;AAC3B,WAAO,MAAM;AACT;AAAG;AAAA,aAAY,MAAM,IAAI,CAAC,CAAC,IAAI;AAC/B;AAAG;AAAA,aAAY,MAAM,IAAI,CAAC,CAAC,IAAI;AAC/B,UAAI,IAAI,EAAG;AACX,WAAK,KAAK,GAAG,CAAC;AAAA,IAClB;AACA,QAAI,OAAO,CAAC,IAAI,IAAI,CAAC;AACrB,QAAI,CAAC,IAAI;AAET,QAAI,QAAQ,IAAI,KAAK,IAAI,MAAM;AAC3B,gBAAU,KAAK,OAAO,GAAG,KAAK;AAC9B,gBAAU,KAAK,OAAO,MAAM,IAAI,CAAC;AAAA,IACrC,OAAO;AACH,gBAAU,KAAK,OAAO,MAAM,IAAI,CAAC;AACjC,gBAAU,KAAK,OAAO,GAAG,KAAK;AAAA,IAClC;AAAA,EACJ;AACJ;AAEA,SAAS,KAAK,KAAK,GAAG,GAAG;AACrB,QAAM,MAAM,IAAI,CAAC;AACjB,MAAI,CAAC,IAAI,IAAI,CAAC;AACd,MAAI,CAAC,IAAI;AACb;AAEA,SAAS,YAAY,GAAG;AACpB,SAAO,EAAE,CAAC;AACd;AACA,SAAS,YAAY,GAAG;AACpB,SAAO,EAAE,CAAC;AACd;;;AC/dA,IAAMC,WAAU;AAEhB,IAAqB,OAArB,MAA0B;AAAA,EACxB,cAAc;AACZ,SAAK,MAAM,KAAK;AAAA,IAChB,KAAK,MAAM,KAAK,MAAM;AACtB,SAAK,IAAI;AAAA,EACX;AAAA,EACA,OAAO,GAAG,GAAG;AACX,SAAK,KAAK,IAAI,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC;AAAA,EACpE;AAAA,EACA,YAAY;AACV,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK;AACrC,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,OAAO,GAAG,GAAG;AACX,SAAK,KAAK,IAAI,KAAK,MAAM,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EAC9C;AAAA,EACA,IAAI,GAAG,GAAG,GAAG;AACX,QAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;AACrB,UAAM,KAAK,IAAI;AACf,UAAM,KAAK;AACX,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,iBAAiB;AAC5C,QAAI,KAAK,QAAQ,KAAM,MAAK,KAAK,IAAI,EAAE,IAAI,EAAE;AAAA,aACpC,KAAK,IAAI,KAAK,MAAM,EAAE,IAAIA,YAAW,KAAK,IAAI,KAAK,MAAM,EAAE,IAAIA,SAAS,MAAK,KAAK,MAAM,KAAK,MAAM;AAC5G,QAAI,CAAC,EAAG;AACR,SAAK,KAAK,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE,IAAI,KAAK,MAAM,EAAE;AAAA,EAC5F;AAAA,EACA,KAAK,GAAG,GAAG,GAAG,GAAG;AACf,SAAK,KAAK,IAAI,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAAA,EACtF;AAAA,EACA,QAAQ;AACN,WAAO,KAAK,KAAK;AAAA,EACnB;AACF;;;ACpCA,IAAqB,UAArB,MAA6B;AAAA,EAC3B,cAAc;AACZ,SAAK,IAAI,CAAC;AAAA,EACZ;AAAA,EACA,OAAO,GAAG,GAAG;AACX,SAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,EACpB;AAAA,EACA,YAAY;AACV,SAAK,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC;AAAA,EAC/B;AAAA,EACA,OAAO,GAAG,GAAG;AACX,SAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,EACpB;AAAA,EACA,QAAQ;AACN,WAAO,KAAK,EAAE,SAAS,KAAK,IAAI;AAAA,EAClC;AACF;;;ACbA,IAAqB,UAArB,MAA6B;AAAA,EAC3B,YAAY,UAAU,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,GAAG;AACjE,QAAI,GAAG,OAAO,CAAC,UAAU,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,OAAO,CAAC,OAAQ,OAAM,IAAI,MAAM,gBAAgB;AAChH,SAAK,WAAW;AAChB,SAAK,iBAAiB,IAAI,aAAa,SAAS,OAAO,SAAS,CAAC;AACjE,SAAK,UAAU,IAAI,aAAa,SAAS,OAAO,SAAS,CAAC;AAC1D,SAAK,OAAO,MAAM,KAAK,OAAO;AAC9B,SAAK,OAAO,MAAM,KAAK,OAAO;AAC9B,SAAK,MAAM;AAAA,EACb;AAAA,EACA,SAAS;AACP,SAAK,SAAS,OAAO;AACrB,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,UAAM,EAAC,UAAU,EAAC,QAAQ,MAAM,UAAS,GAAG,QAAO,IAAI;AACvD,QAAI,IAAI;AAGR,UAAM,gBAAgB,KAAK,gBAAgB,KAAK,eAAe,SAAS,GAAG,UAAU,SAAS,IAAI,CAAC;AACnG,aAAS,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,GAAG,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG;AACxE,YAAM,KAAK,UAAU,CAAC,IAAI;AAC1B,YAAM,KAAK,UAAU,IAAI,CAAC,IAAI;AAC9B,YAAM,KAAK,UAAU,IAAI,CAAC,IAAI;AAC9B,YAAMC,MAAK,OAAO,EAAE;AACpB,YAAMC,MAAK,OAAO,KAAK,CAAC;AACxB,YAAM,KAAK,OAAO,EAAE;AACpB,YAAM,KAAK,OAAO,KAAK,CAAC;AACxB,YAAM,KAAK,OAAO,EAAE;AACpB,YAAM,KAAK,OAAO,KAAK,CAAC;AAExB,YAAM,KAAK,KAAKD;AAChB,YAAM,KAAK,KAAKC;AAChB,YAAM,KAAK,KAAKD;AAChB,YAAM,KAAK,KAAKC;AAChB,YAAMC,OAAM,KAAK,KAAK,KAAK,MAAM;AAEjC,UAAI,KAAK,IAAIA,GAAE,IAAI,MAAM;AAIvB,YAAI,OAAO,QAAW;AACpB,eAAK,KAAK;AACV,qBAAWC,MAAK,KAAM,OAAM,OAAOA,KAAI,CAAC,GAAG,MAAM,OAAOA,KAAI,IAAI,CAAC;AACjE,gBAAM,KAAK,QAAQ,MAAM,KAAK;AAAA,QAChC;AACA,cAAM,IAAI,MAAM,KAAK,MAAM,KAAKH,OAAM,MAAM,KAAKC,OAAM,EAAE;AACzD,aAAKD,MAAK,MAAM,IAAI,IAAI;AACxB,aAAKC,MAAK,MAAM,IAAI,IAAI;AAAA,MAC1B,OAAO;AACL,cAAM,IAAI,IAAIC;AACd,cAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,cAAM,KAAK,KAAK,KAAK,KAAK;AAC1B,YAAIF,OAAM,KAAK,KAAK,KAAK,MAAM;AAC/B,YAAIC,OAAM,KAAK,KAAK,KAAK,MAAM;AAAA,MACjC;AACA,oBAAc,CAAC,IAAI;AACnB,oBAAc,IAAI,CAAC,IAAI;AAAA,IACzB;AAGA,QAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC5B,QAAI,IAAI,KAAK,IAAI;AACjB,QAAI,IAAI,KAAK,OAAO,IAAI,CAAC;AACzB,QAAI,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC;AAC7B,YAAQ,KAAK,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,UAAI,KAAK,CAAC;AACV,WAAK,IAAI,KAAK,IAAI,KAAK;AACvB,WAAK,IAAI,GAAG,KAAK,OAAO,IAAI,CAAC,GAAG,KAAK,OAAO,IAAI,IAAI,CAAC;AACrD,cAAQ,KAAK,CAAC,IAAI,QAAQ,EAAE,IAAI,KAAK;AACrC,cAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,KAAK;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO,SAAS;AACd,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,UAAU,EAAC,WAAW,SAAS,KAAI,GAAG,eAAe,QAAO,IAAI;AACvE,QAAI,KAAK,UAAU,EAAG,QAAO;AAC7B,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,EAAE,GAAG;AAChD,YAAM,IAAI,UAAU,CAAC;AACrB,UAAI,IAAI,EAAG;AACX,YAAM,KAAK,KAAK,MAAM,IAAI,CAAC,IAAI;AAC/B,YAAM,KAAK,KAAK,MAAM,IAAI,CAAC,IAAI;AAC/B,YAAM,KAAK,cAAc,EAAE;AAC3B,YAAM,KAAK,cAAc,KAAK,CAAC;AAC/B,YAAM,KAAK,cAAc,EAAE;AAC3B,YAAM,KAAK,cAAc,KAAK,CAAC;AAC/B,WAAK,eAAe,IAAI,IAAI,IAAI,IAAI,OAAO;AAAA,IAC7C;AACA,QAAI,IAAI,KAAK,KAAK,KAAK,SAAS,CAAC;AACjC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,WAAK,IAAI,KAAK,KAAK,CAAC;AACpB,YAAM,IAAI,KAAK,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI;AACxC,YAAM,IAAI,cAAc,CAAC;AACzB,YAAM,IAAI,cAAc,IAAI,CAAC;AAC7B,YAAMG,KAAI,KAAK;AACf,YAAM,IAAI,KAAK,SAAS,GAAG,GAAG,QAAQA,KAAI,CAAC,GAAG,QAAQA,KAAI,CAAC,CAAC;AAC5D,UAAI,EAAG,MAAK,eAAe,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;AAAA,IACtD;AACA,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,aAAa,SAAS;AACpB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,YAAQ,KAAK,KAAK,MAAM,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK,OAAO,KAAK,IAAI;AAC/E,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,WAAW,GAAG,SAAS;AACrB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,SAAS,KAAK,MAAM,CAAC;AAC3B,QAAI,WAAW,QAAQ,CAAC,OAAO,OAAQ;AACvC,YAAQ,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AACnC,QAAI,IAAI,OAAO;AACf,WAAO,OAAO,CAAC,MAAM,OAAO,IAAE,CAAC,KAAK,OAAO,CAAC,MAAM,OAAO,IAAE,CAAC,KAAK,IAAI,EAAG,MAAK;AAC7E,aAASD,KAAI,GAAGA,KAAI,GAAGA,MAAK,GAAG;AAC7B,UAAI,OAAOA,EAAC,MAAM,OAAOA,KAAE,CAAC,KAAK,OAAOA,KAAE,CAAC,MAAM,OAAOA,KAAE,CAAC;AACzD,gBAAQ,OAAO,OAAOA,EAAC,GAAG,OAAOA,KAAI,CAAC,CAAC;AAAA,IAC3C;AACA,YAAQ,UAAU;AAClB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,CAAC,eAAe;AACd,UAAM,EAAC,UAAU,EAAC,OAAM,EAAC,IAAI;AAC7B,aAAS,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,IAAI,GAAG,EAAE,GAAG;AACjD,YAAM,OAAO,KAAK,YAAY,CAAC;AAC/B,UAAI,KAAM,MAAK,QAAQ,GAAG,MAAM;AAAA,IAClC;AAAA,EACF;AAAA,EACA,YAAY,GAAG;AACb,UAAM,UAAU,IAAI;AACpB,SAAK,WAAW,GAAG,OAAO;AAC1B,WAAO,QAAQ,MAAM;AAAA,EACvB;AAAA,EACA,eAAe,IAAI,IAAI,IAAI,IAAI,SAAS;AACtC,QAAI;AACJ,UAAM,KAAK,KAAK,YAAY,IAAI,EAAE;AAClC,UAAM,KAAK,KAAK,YAAY,IAAI,EAAE;AAClC,QAAI,OAAO,KAAK,OAAO,GAAG;AACxB,cAAQ,OAAO,IAAI,EAAE;AACrB,cAAQ,OAAO,IAAI,EAAE;AAAA,IACvB,WAAW,IAAI,KAAK,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AACxD,cAAQ,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACzB,cAAQ,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,SAAS,GAAG,GAAG,GAAG;AAChB,SAAK,IAAI,CAAC,GAAG,MAAM,OAAO,IAAI,CAAC,GAAG,MAAM,GAAI,QAAO;AACnD,WAAO,KAAK,SAAS,MAAM,GAAG,GAAG,CAAC,MAAM;AAAA,EAC1C;AAAA,EACA,CAAC,UAAU,GAAG;AACZ,UAAM,KAAK,KAAK,MAAM,CAAC;AACvB,QAAI,GAAI,YAAW,KAAK,KAAK,SAAS,UAAU,CAAC,GAAG;AAClD,YAAM,KAAK,KAAK,MAAM,CAAC;AAEvB,UAAI,GAAI,MAAM,UAAS,KAAK,GAAG,KAAK,GAAG,QAAQ,KAAK,IAAI,MAAM,GAAG;AAC/D,iBAAS,KAAK,GAAG,KAAK,GAAG,QAAQ,KAAK,IAAI,MAAM,GAAG;AACjD,cAAI,GAAG,EAAE,MAAM,GAAG,EAAE,KACb,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,KACxB,IAAI,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,KAAK,KAAK,EAAE,KAC3C,IAAI,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK,KAAK,KAAK,EAAE,GAAG;AACnD,kBAAM;AACN,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,MAAM,GAAG;AACP,UAAM,EAAC,eAAe,UAAU,EAAC,SAAS,WAAW,UAAS,EAAC,IAAI;AACnE,UAAM,KAAK,QAAQ,CAAC;AACpB,QAAI,OAAO,GAAI,QAAO;AACtB,UAAM,SAAS,CAAC;AAChB,QAAI,IAAI;AACR,OAAG;AACD,YAAM,IAAI,KAAK,MAAM,IAAI,CAAC;AAC1B,aAAO,KAAK,cAAc,IAAI,CAAC,GAAG,cAAc,IAAI,IAAI,CAAC,CAAC;AAC1D,UAAI,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI;AAC9B,UAAI,UAAU,CAAC,MAAM,EAAG;AACxB,UAAI,UAAU,CAAC;AAAA,IACjB,SAAS,MAAM,MAAM,MAAM;AAC3B,WAAO;AAAA,EACT;AAAA,EACA,MAAM,GAAG;AAEP,QAAI,MAAM,KAAK,KAAK,SAAS,KAAK,WAAW,GAAG;AAC9C,aAAO,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,IAChG;AACA,UAAM,SAAS,KAAK,MAAM,CAAC;AAC3B,QAAI,WAAW,KAAM,QAAO;AAC5B,UAAM,EAAC,SAAS,EAAC,IAAI;AACrB,UAAMC,KAAI,IAAI;AACd,WAAO,KAAK,UAAU,EAAEA,EAAC,KAAK,EAAEA,KAAI,CAAC,IAC/B,KAAK,cAAc,GAAG,QAAQ,EAAEA,EAAC,GAAG,EAAEA,KAAI,CAAC,GAAG,EAAEA,KAAI,CAAC,GAAG,EAAEA,KAAI,CAAC,CAAC,IAChE,KAAK,YAAY,GAAG,MAAM,CAAC;AAAA,EACnC;AAAA,EACA,YAAY,GAAG,QAAQ;AACrB,UAAM,IAAI,OAAO;AACjB,QAAI,IAAI;AACR,QAAI,IAAI,IAAI,KAAK,OAAO,IAAI,CAAC,GAAG,KAAK,OAAO,IAAI,CAAC;AACjD,QAAI,IAAI,KAAK,KAAK,YAAY,IAAI,EAAE;AACpC,QAAI,IAAI,KAAK;AACb,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC7B,WAAK,IAAI,KAAK,IAAI,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,IAAI,CAAC;AACnD,WAAK,IAAI,KAAK,KAAK,YAAY,IAAI,EAAE;AACrC,UAAI,OAAO,KAAK,OAAO,GAAG;AACxB,aAAK,IAAI,KAAK;AACd,YAAI,EAAG,GAAE,KAAK,IAAI,EAAE;AAAA,YACf,KAAI,CAAC,IAAI,EAAE;AAAA,MAClB,OAAO;AACL,YAAI,GAAG,KAAK,KAAK,KAAK;AACtB,YAAI,OAAO,GAAG;AACZ,eAAK,IAAI,KAAK,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,OAAO,KAAM;AAC9D,WAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAAA,QACzB,OAAO;AACL,eAAK,IAAI,KAAK,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,OAAO,KAAM;AAC9D,WAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AACvB,eAAK,IAAI,KAAK,KAAK,UAAU,KAAK,GAAG;AACrC,cAAI,MAAM,GAAI,MAAK,MAAM,GAAG,IAAI,IAAI,GAAG,EAAE,MAAM;AAC/C,cAAI,EAAG,GAAE,KAAK,KAAK,GAAG;AAAA,cACjB,KAAI,CAAC,KAAK,GAAG;AAAA,QACpB;AACA,aAAK,IAAI,KAAK,KAAK,UAAU,KAAK,GAAG;AACrC,YAAI,MAAM,GAAI,MAAK,MAAM,GAAG,IAAI,IAAI,GAAG,EAAE,MAAM;AAC/C,YAAI,EAAG,GAAE,KAAK,KAAK,GAAG;AAAA,YACjB,KAAI,CAAC,KAAK,GAAG;AAAA,MACpB;AAAA,IACF;AACA,QAAI,GAAG;AACL,WAAK,IAAI,KAAK,KAAK,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACvC,UAAI,MAAM,GAAI,MAAK,MAAM,GAAG,IAAI,IAAI,GAAG,EAAE,MAAM;AAAA,IACjD,WAAW,KAAK,SAAS,IAAI,KAAK,OAAO,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,QAAQ,CAAC,GAAG;AACrF,aAAO,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,IAChG;AACA,WAAO;AAAA,EACT;AAAA,EACA,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAEnC,UAAM,OAAO,KAAK;AAClB,QAAI,KAAM,EAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAC5D,WAAO,MAAM;AACX,UAAI,OAAO,KAAK,OAAO,EAAG,QAAO,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;AAC1E,UAAI,KAAK,GAAI,QAAO;AACpB,UAAI,GAAG,GAAG,IAAI,MAAM;AACpB,UAAI,IAAI,EAAQ,KAAI,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,KAAK,KAAK,IAAI,KAAK;AAAA,eACnE,IAAI,EAAQ,KAAI,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,KAAK,KAAK,IAAI,KAAK;AAAA,eACxE,IAAI,EAAQ,KAAI,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,KAAK,KAAK,IAAI,KAAK;AAAA,UAC5E,KAAI,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO,KAAK,KAAK,IAAI,KAAK;AACjE,UAAI,GAAI,MAAK,GAAG,KAAK,GAAG,KAAK,KAAK,YAAY,IAAI,EAAE;AAAA,UAC/C,MAAK,GAAG,KAAK,GAAG,KAAK,KAAK,YAAY,IAAI,EAAE;AAAA,IACnD;AAAA,EACF;AAAA,EACA,cAAc,GAAG,QAAQ,KAAK,KAAK,KAAK,KAAK;AAC3C,QAAI,IAAI,MAAM,KAAK,MAAM,GAAG;AAC5B,QAAI,IAAI,KAAK,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,GAAG,EAAG,GAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACjE,QAAI,IAAI,KAAK,SAAS,EAAE,EAAE,SAAS,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,GAAG,KAAK,GAAG,EAAG,GAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACpF,QAAI,IAAI,KAAK,YAAY,GAAG,CAAC,GAAG;AAC9B,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,KAAK,KAAK,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG;AACxF,aAAK,IAAI,KAAK,KAAK,UAAU,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC3C,YAAI,MAAM,GAAI,KAAI,KAAK,MAAM,GAAG,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI,EAAE;AAAA,MACvD;AAAA,IACF,WAAW,KAAK,SAAS,IAAI,KAAK,OAAO,KAAK,QAAQ,IAAI,KAAK,OAAO,KAAK,QAAQ,CAAC,GAAG;AACrF,UAAI,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AAAA,IAC7F;AACA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,GAAG,IAAI,IAAI,GAAG,GAAG;AACrB,WAAO,OAAO,IAAI;AAChB,UAAI,GAAG;AACP,cAAQ,IAAI;AAAA,QACV,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,GAAQ,IAAI,KAAK,MAAM,IAAI,KAAK;AAAM;AAAA,QACxD,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,IAAQ,IAAI,KAAK,MAAM,IAAI,KAAK;AAAM;AAAA,QACxD,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,GAAQ,IAAI,KAAK,MAAM,IAAI,KAAK;AAAM;AAAA,QACxD,KAAK;AAAQ,eAAK;AAAQ;AAAA,QAC1B,KAAK;AAAQ,eAAK,GAAQ,IAAI,KAAK,MAAM,IAAI,KAAK;AAAM;AAAA,MAC1D;AAGA,WAAK,EAAE,CAAC,MAAM,KAAK,EAAE,IAAI,CAAC,MAAM,MAAM,KAAK,SAAS,GAAG,GAAG,CAAC,GAAG;AAC5D,UAAE,OAAO,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK;AAAA,MAC7B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS,IAAI,IAAI,IAAI,IAAI;AACvB,QAAI,IAAI,UAAU,GAAG,GAAG;AACxB,QAAI,KAAK,GAAG;AACV,UAAI,MAAM,KAAK,KAAM,QAAO;AAC5B,WAAK,KAAK,KAAK,OAAO,MAAM,MAAM,EAAG,KAAI,KAAK,MAAM,IAAI,MAAM,IAAI,KAAK;AAAA,IACzE,WAAW,KAAK,GAAG;AACjB,UAAI,MAAM,KAAK,KAAM,QAAO;AAC5B,WAAK,KAAK,KAAK,OAAO,MAAM,MAAM,EAAG,KAAI,KAAK,MAAM,IAAI,MAAM,IAAI,KAAK;AAAA,IACzE;AACA,QAAI,KAAK,GAAG;AACV,UAAI,MAAM,KAAK,KAAM,QAAO;AAC5B,WAAK,KAAK,KAAK,OAAO,MAAM,MAAM,EAAG,KAAI,KAAK,MAAM,IAAI,MAAM,IAAI,KAAK;AAAA,IACzE,WAAW,KAAK,GAAG;AACjB,UAAI,MAAM,KAAK,KAAM,QAAO;AAC5B,WAAK,KAAK,KAAK,OAAO,MAAM,MAAM,EAAG,KAAI,KAAK,MAAM,IAAI,MAAM,IAAI,KAAK;AAAA,IACzE;AACA,WAAO,CAAC,GAAG,CAAC;AAAA,EACd;AAAA,EACA,UAAU,GAAG,GAAG;AACd,YAAQ,MAAM,KAAK,OAAO,IACpB,MAAM,KAAK,OAAO,IAAS,MAC1B,MAAM,KAAK,OAAO,IACnB,MAAM,KAAK,OAAO,IAAS;AAAA,EACnC;AAAA,EACA,YAAY,GAAG,GAAG;AAChB,YAAQ,IAAI,KAAK,OAAO,IAClB,IAAI,KAAK,OAAO,IAAS,MACxB,IAAI,KAAK,OAAO,IACjB,IAAI,KAAK,OAAO,IAAS;AAAA,EACjC;AAAA,EACA,UAAU,GAAG;AACX,QAAI,KAAK,EAAE,SAAS,GAAG;AACrB,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAI,GAAG;AACnC,cAAM,KAAK,IAAI,KAAK,EAAE,QAAQ,KAAK,IAAI,KAAK,EAAE;AAC9C,YAAI,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG;AACpF,YAAE,OAAO,GAAG,CAAC,GAAG,KAAK;AAAA,QACvB;AAAA,MACF;AACA,UAAI,CAAC,EAAE,OAAQ,KAAI;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AACF;;;ACtUA,IAAM,MAAM,IAAI,KAAK;AAArB,IAAyB,MAAM,KAAK;AAEpC,SAAS,OAAO,GAAG;AACjB,SAAO,EAAE,CAAC;AACZ;AAEA,SAAS,OAAO,GAAG;AACjB,SAAO,EAAE,CAAC;AACZ;AAGA,SAAS,UAAU,GAAG;AACpB,QAAM,EAAC,WAAW,OAAM,IAAI;AAC5B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK,GAAG;AAC5C,UAAM,IAAI,IAAI,UAAU,CAAC,GACnB,IAAI,IAAI,UAAU,IAAI,CAAC,GACvB,IAAI,IAAI,UAAU,IAAI,CAAC,GACvB,SAAS,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,MACtD,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC;AACrE,QAAI,QAAQ,MAAO,QAAO;AAAA,EAC5B;AACA,SAAO;AACT;AAEA,SAAS,OAAO,GAAG,GAAG,GAAG;AACvB,SAAO,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;AAC1D;AAEA,IAAqB,WAArB,MAAqB,UAAS;AAAA,EAC5B,OAAO,KAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,MAAM;AAClD,WAAO,IAAI,UAAS,YAAY,SAC1B,UAAU,QAAQ,IAAI,IAAI,IAAI,IAC9B,aAAa,KAAK,aAAa,QAAQ,IAAI,IAAI,IAAI,CAAC,CAAC;AAAA,EAC7D;AAAA,EACA,YAAY,QAAQ;AAClB,SAAK,cAAc,IAAI,WAAW,MAAM;AACxC,SAAK,UAAU,IAAI,WAAW,OAAO,SAAS,CAAC;AAC/C,SAAK,aAAa,IAAI,WAAW,OAAO,SAAS,CAAC;AAClD,SAAK,SAAS,KAAK,YAAY;AAC/B,SAAK,MAAM;AAAA,EACb;AAAA,EACA,SAAS;AACP,SAAK,YAAY,OAAO;AACxB,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,UAAM,IAAI,KAAK,aAAa,SAAS,KAAK;AAG1C,QAAI,EAAE,QAAQ,EAAE,KAAK,SAAS,KAAK,UAAU,CAAC,GAAG;AAC/C,WAAK,YAAY,WAAW,KAAK,EAAC,QAAQ,OAAO,SAAO,EAAC,GAAG,CAAC,GAAE,MAAM,CAAC,EACnE,KAAK,CAAC,GAAG,MAAM,OAAO,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,OAAO,IAAI,IAAI,CAAC,CAAC;AACxF,YAAM,IAAI,KAAK,UAAU,CAAC,GAAG,IAAI,KAAK,UAAU,KAAK,UAAU,SAAS,CAAC,GACvE,SAAS,CAAE,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAE,GAC9E,IAAI,OAAO,KAAK,MAAM,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC;AACpE,eAAS,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,IAAI,GAAG,EAAE,GAAG;AACjD,cAAM,IAAI,OAAO,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC;AACpD,eAAO,IAAI,CAAC,IAAI,EAAE,CAAC;AACnB,eAAO,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;AAAA,MACzB;AACA,WAAK,cAAc,IAAI,WAAW,MAAM;AAAA,IAC1C,OAAO;AACL,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,YAAY,KAAK,YAAY,KAAK,YAAY;AACpD,UAAM,OAAO,KAAK,OAAO,KAAK,YAAY;AAC1C,UAAM,YAAY,KAAK,YAAY,KAAK,YAAY;AACpD,UAAM,UAAU,KAAK,QAAQ,KAAK,EAAE;AACpC,UAAM,YAAY,KAAK,WAAW,KAAK,EAAE;AAKzC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,EAAE,GAAG;AAChD,YAAM,IAAI,UAAU,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC;AAC/C,UAAI,UAAU,CAAC,MAAM,MAAM,QAAQ,CAAC,MAAM,GAAI,SAAQ,CAAC,IAAI;AAAA,IAC7D;AACA,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,gBAAU,KAAK,CAAC,CAAC,IAAI;AAAA,IACvB;AAGA,QAAI,KAAK,UAAU,KAAK,KAAK,SAAS,GAAG;AACvC,WAAK,YAAY,IAAI,WAAW,CAAC,EAAE,KAAK,EAAE;AAC1C,WAAK,YAAY,IAAI,WAAW,CAAC,EAAE,KAAK,EAAE;AAC1C,WAAK,UAAU,CAAC,IAAI,KAAK,CAAC;AAC1B,cAAQ,KAAK,CAAC,CAAC,IAAI;AACnB,UAAI,KAAK,WAAW,GAAG;AACrB,gBAAQ,KAAK,CAAC,CAAC,IAAI;AACnB,aAAK,UAAU,CAAC,IAAI,KAAK,CAAC;AAC1B,aAAK,UAAU,CAAC,IAAI,KAAK,CAAC;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ,QAAQ;AACd,WAAO,IAAI,QAAQ,MAAM,MAAM;AAAA,EACjC;AAAA,EACA,CAAC,UAAU,GAAG;AACZ,UAAM,EAAC,SAAS,MAAM,YAAY,WAAW,WAAW,WAAAC,WAAS,IAAI;AAGrE,QAAIA,YAAW;AACb,YAAM,IAAIA,WAAU,QAAQ,CAAC;AAC7B,UAAI,IAAI,EAAG,OAAMA,WAAU,IAAI,CAAC;AAChC,UAAI,IAAIA,WAAU,SAAS,EAAG,OAAMA,WAAU,IAAI,CAAC;AACnD;AAAA,IACF;AAEA,UAAM,KAAK,QAAQ,CAAC;AACpB,QAAI,OAAO,GAAI;AACf,QAAI,IAAI,IAAI,KAAK;AACjB,OAAG;AACD,YAAM,KAAK,UAAU,CAAC;AACtB,UAAI,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI;AAC9B,UAAI,UAAU,CAAC,MAAM,EAAG;AACxB,UAAI,UAAU,CAAC;AACf,UAAI,MAAM,IAAI;AACZ,cAAM,IAAI,MAAM,WAAW,CAAC,IAAI,KAAK,KAAK,MAAM;AAChD,YAAI,MAAM,GAAI,OAAM;AACpB;AAAA,MACF;AAAA,IACF,SAAS,MAAM;AAAA,EACjB;AAAA,EACA,KAAK,GAAG,GAAG,IAAI,GAAG;AAChB,SAAK,IAAI,CAAC,GAAG,MAAM,OAAO,IAAI,CAAC,GAAG,MAAM,GAAI,QAAO;AACnD,UAAM,KAAK;AACX,QAAI;AACJ,YAAQ,IAAI,KAAK,MAAM,GAAG,GAAG,CAAC,MAAM,KAAK,MAAM,KAAK,MAAM,GAAI,KAAI;AAClE,WAAO;AAAA,EACT;AAAA,EACA,MAAM,GAAG,GAAG,GAAG;AACb,UAAM,EAAC,SAAS,MAAM,YAAY,WAAW,WAAW,OAAM,IAAI;AAClE,QAAI,QAAQ,CAAC,MAAM,MAAM,CAAC,OAAO,OAAQ,SAAQ,IAAI,MAAM,OAAO,UAAU;AAC5E,QAAI,IAAI;AACR,QAAI,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC;AACjE,UAAM,KAAK,QAAQ,CAAC;AACpB,QAAI,IAAI;AACR,OAAG;AACD,UAAI,IAAI,UAAU,CAAC;AACnB,YAAM,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC;AACnE,UAAI,KAAK,GAAI,MAAK,IAAI,IAAI;AAC1B,UAAI,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI;AAC9B,UAAI,UAAU,CAAC,MAAM,EAAG;AACxB,UAAI,UAAU,CAAC;AACf,UAAI,MAAM,IAAI;AACZ,YAAI,MAAM,WAAW,CAAC,IAAI,KAAK,KAAK,MAAM;AAC1C,YAAI,MAAM,GAAG;AACX,cAAI,IAAI,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,GAAI,QAAO;AAAA,QAC7E;AACA;AAAA,MACF;AAAA,IACF,SAAS,MAAM;AACf,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAAS;AACd,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,QAAQ,WAAW,UAAS,IAAI;AACvC,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,EAAE,GAAG;AAChD,YAAM,IAAI,UAAU,CAAC;AACrB,UAAI,IAAI,EAAG;AACX,YAAM,KAAK,UAAU,CAAC,IAAI;AAC1B,YAAM,KAAK,UAAU,CAAC,IAAI;AAC1B,cAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,cAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AAAA,IAC3C;AACA,SAAK,WAAW,OAAO;AACvB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,aAAa,SAAS,GAAG;AACvB,QAAI,MAAM,WAAc,CAAC,WAAW,OAAO,QAAQ,WAAW,YAAa,KAAI,SAAS,UAAU;AAClG,QAAI,KAAK,SAAY,IAAI,CAAC;AAC1B,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,OAAM,IAAI;AACjB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK,GAAG;AAChD,YAAM,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,IAAI,CAAC;AACrC,cAAQ,OAAO,IAAI,GAAG,CAAC;AACvB,cAAQ,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG;AAAA,IAC7B;AACA,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,WAAW,SAAS;AAClB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,MAAM,OAAM,IAAI;AACvB,UAAM,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,KAAK;AAChC,YAAQ,OAAO,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC;AACvC,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,YAAMC,KAAI,IAAI,KAAK,CAAC;AACpB,cAAQ,OAAO,OAAOA,EAAC,GAAG,OAAOA,KAAI,CAAC,CAAC;AAAA,IACzC;AACA,YAAQ,UAAU;AAClB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,UAAM,UAAU,IAAI;AACpB,SAAK,WAAW,OAAO;AACvB,WAAO,QAAQ,MAAM;AAAA,EACvB;AAAA,EACA,eAAe,GAAG,SAAS;AACzB,UAAM,SAAS,WAAW,OAAO,UAAU,IAAI,SAAO;AACtD,UAAM,EAAC,QAAQ,UAAS,IAAI;AAC5B,UAAM,KAAK,UAAU,KAAK,CAAC,IAAI;AAC/B,UAAM,KAAK,UAAU,IAAI,CAAC,IAAI;AAC9B,UAAM,KAAK,UAAU,IAAI,CAAC,IAAI;AAC9B,YAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,YAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,YAAQ,OAAO,OAAO,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC;AACzC,YAAQ,UAAU;AAClB,WAAO,UAAU,OAAO,MAAM;AAAA,EAChC;AAAA,EACA,CAAC,mBAAmB;AAClB,UAAM,EAAC,UAAS,IAAI;AACpB,aAAS,IAAI,GAAG,IAAI,UAAU,SAAS,GAAG,IAAI,GAAG,EAAE,GAAG;AACpD,YAAM,KAAK,gBAAgB,CAAC;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,gBAAgB,GAAG;AACjB,UAAM,UAAU,IAAI;AACpB,SAAK,eAAe,GAAG,OAAO;AAC9B,WAAO,QAAQ,MAAM;AAAA,EACvB;AACF;AAEA,SAAS,UAAU,QAAQ,IAAI,IAAI,MAAM;AACvC,QAAM,IAAI,OAAO;AACjB,QAAM,QAAQ,IAAI,aAAa,IAAI,CAAC;AACpC,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAM,IAAI,OAAO,CAAC;AAClB,UAAM,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,GAAG,GAAG,MAAM;AACzC,UAAM,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,GAAG,GAAG,MAAM;AAAA,EAC/C;AACA,SAAO;AACT;AAEA,UAAU,aAAa,QAAQ,IAAI,IAAI,MAAM;AAC3C,MAAI,IAAI;AACR,aAAW,KAAK,QAAQ;AACtB,UAAM,GAAG,KAAK,MAAM,GAAG,GAAG,MAAM;AAChC,UAAM,GAAG,KAAK,MAAM,GAAG,GAAG,MAAM;AAChC,MAAE;AAAA,EACJ;AACF;", "names": ["u3", "u", "bc", "ca", "ab", "u", "abt", "bct", "cat", "_8", "_16", "fin", "fin2", "ab", "bc", "_8", "_8b", "_16", "_48", "fin", "n", "epsilon", "x1", "y1", "ab", "i", "v", "collinear", "h"]}