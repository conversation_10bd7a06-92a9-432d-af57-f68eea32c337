{"version": 3, "sources": ["../../paneforge/dist/internal/utils/aria.js", "../../paneforge/dist/internal/utils/assert.js", "../../paneforge/dist/internal/constants.js", "../../paneforge/dist/internal/utils/compare.js", "../../paneforge/dist/internal/utils/is.js", "../../paneforge/dist/internal/utils/resize.js", "../../paneforge/dist/internal/helpers.js", "../../paneforge/dist/internal/utils/useId.js", "../../paneforge/dist/internal/utils/adjust-layout.js", "../../paneforge/dist/internal/utils/style.js", "../../paneforge/dist/internal/utils/storage.js", "../../paneforge/dist/paneforge.svelte.js", "../../paneforge/dist/components/pane-group.svelte", "../../paneforge/dist/components/pane.svelte", "../../paneforge/dist/components/pane-resizer.svelte"], "sourcesContent": ["/**\n * A utility function that calculates the `aria-valuemax`, `aria-valuemin`,\n * and `aria-valuenow` values for a pane based on its layout and constraints.\n */\nexport function calculateAriaValues({ layout, panesArray, pivotIndices, }) {\n    let currentMinSize = 0;\n    let currentMaxSize = 100;\n    let totalMinSize = 0;\n    let totalMaxSize = 0;\n    const firstIndex = pivotIndices[0];\n    // A pane's effective min/max sizes also need to account for other pane's sizes.\n    for (let i = 0; i < panesArray.length; i++) {\n        const constraints = panesArray[i].constraints;\n        const { maxSize = 100, minSize = 0 } = constraints;\n        if (i === firstIndex) {\n            currentMinSize = minSize;\n            currentMaxSize = maxSize;\n        }\n        else {\n            totalMinSize += minSize;\n            totalMaxSize += maxSize;\n        }\n    }\n    const valueMax = Math.min(currentMaxSize, 100 - totalMinSize);\n    const valueMin = Math.max(currentMinSize, 100 - totalMaxSize);\n    const valueNow = layout[firstIndex];\n    return {\n        valueMax,\n        valueMin,\n        valueNow,\n    };\n}\n", "export function assert(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexpectedCondition, message = \"Asser<PERSON> failed!\") {\n    if (!expectedCondition) {\n        console.error(message);\n        throw new Error(message);\n    }\n}\n", "export const LOCAL_STORAGE_DEBOUNCE_INTERVAL = 100;\nexport const PRECISION = 10;\n", "import { PRECISION } from \"../constants.js\";\n/**\n * Compares two numbers for equality with a given fractional precision.\n */\nexport function areNumbersAlmostEqual(actual, expected, fractionDigits = PRECISION) {\n    return compareNumbersWithTolerance(actual, expected, fractionDigits) === 0;\n}\n/**\n * Compares two numbers with a given tolerance.\n *\n * @returns `-1` if `actual` is less than `expected`, `0` if they are equal,\n * and `1` if `actual` is greater than `expected`.\n */\nexport function compareNumbersWithTolerance(actual, expected, fractionDigits = PRECISION) {\n    const roundedActual = roundTo(actual, fractionDigits);\n    const roundedExpected = roundTo(expected, fractionDigits);\n    return Math.sign(roundedActual - roundedExpected);\n}\n/**\n * Compares two arrays for equality.\n */\nexport function areArraysEqual(arrA, arrB) {\n    if (arrA.length !== arrB.length)\n        return false;\n    for (let index = 0; index < arrA.length; index++) {\n        if (arrA[index] !== arrB[index])\n            return false;\n    }\n    return true;\n}\n/**\n * Rounds a number to a given number of decimal places.\n */\nfunction roundTo(value, decimals) {\n    return Number.parseFloat(value.toFixed(decimals));\n}\n", "export const isBrowser = typeof document !== \"undefined\";\nexport function isHTMLElement(element) {\n    return element instanceof HTMLElement;\n}\nexport function isKeyDown(event) {\n    return event.type === \"keydown\";\n}\nexport function isMouseEvent(event) {\n    return event.type.startsWith(\"mouse\");\n}\nexport function isTouchEvent(event) {\n    return event.type.startsWith(\"touch\");\n}\n", "import { PRECISION } from \"../constants.js\";\nimport { assert } from \"./assert.js\";\nimport { compareNumbersWithTolerance } from \"./compare.js\";\n/**\n * Resizes a pane based on its constraints.\n */\nexport function resizePane({ paneConstraints: paneConstraintsArray, paneIndex, initialSize, }) {\n    const paneConstraints = paneConstraintsArray[paneIndex];\n    assert(paneConstraints != null, \"Pane constraints should not be null.\");\n    const { collapsedSize = 0, collapsible, maxSize = 100, minSize = 0 } = paneConstraints;\n    let newSize = initialSize;\n    if (compareNumbersWithTolerance(newSize, minSize) < 0) {\n        newSize = getAdjustedSizeForCollapsible(newSize, collapsible, collapsedSize, minSize);\n    }\n    newSize = Math.min(maxSize, newSize);\n    return Number.parseFloat(newSize.toFixed(PRECISION));\n}\n/**\n * Adjusts the size of a pane based on its collapsible state.\n *\n * If the pane is collapsible, the size will be snapped to the collapsed size\n * or the minimum size based on the halfway point.\n */\nfunction getAdjustedSizeForCollapsible(size, collapsible, collapsedSize, minSize) {\n    if (!collapsible)\n        return minSize;\n    // Snap collapsible panes closed or open based on the halfway point.\n    const halfwayPoint = (collapsedSize + minSize) / 2;\n    return compareNumbersWithTolerance(size, halfwayPoint) < 0 ? collapsedSize : minSize;\n}\n", "import { calculateAriaValues } from \"./utils/aria.js\";\nimport { assert } from \"./utils/assert.js\";\nimport { areNumbersAlmostEqual } from \"./utils/compare.js\";\nimport { isBrowser, isHTMLElement, isKeyDown, isMouseEvent, isTouchEvent } from \"./utils/is.js\";\nimport { resizePane } from \"./utils/resize.js\";\nexport function noop() { }\nexport function updateResizeHandleAriaValues({ groupId, layout, panesArray, }) {\n    const resizeHandleElements = getResizeHandleElementsForGroup(groupId);\n    for (let index = 0; index < panesArray.length - 1; index++) {\n        const { valueMax, valueMin, valueNow } = calculateAriaValues({\n            layout,\n            panesArray: panesArray,\n            pivotIndices: [index, index + 1],\n        });\n        const resizeHandleEl = resizeHandleElements[index];\n        if (isHTMLElement(resizeHandleEl)) {\n            const paneData = panesArray[index];\n            resizeHandleEl.setAttribute(\"aria-controls\", paneData.opts.id.current);\n            resizeHandleEl.setAttribute(\"aria-valuemax\", `${Math.round(valueMax)}`);\n            resizeHandleEl.setAttribute(\"aria-valuemin\", `${Math.round(valueMin)}`);\n            resizeHandleEl.setAttribute(\"aria-valuenow\", valueNow != null ? `${Math.round(valueNow)}` : \"\");\n        }\n    }\n    return () => {\n        resizeHandleElements.forEach((resizeHandleElement) => {\n            resizeHandleElement.removeAttribute(\"aria-controls\");\n            resizeHandleElement.removeAttribute(\"aria-valuemax\");\n            resizeHandleElement.removeAttribute(\"aria-valuemin\");\n            resizeHandleElement.removeAttribute(\"aria-valuenow\");\n        });\n    };\n}\nexport function getResizeHandleElementsForGroup(groupId) {\n    if (!isBrowser)\n        return [];\n    return Array.from(document.querySelectorAll(`[data-pane-resizer-id][data-pane-group-id=\"${groupId}\"]`));\n}\nexport function getResizeHandleElementIndex(groupId, id) {\n    if (!isBrowser)\n        return null;\n    const handles = getResizeHandleElementsForGroup(groupId);\n    const index = handles.findIndex((handle) => handle.getAttribute(\"data-pane-resizer-id\") === id);\n    return index ?? null;\n}\nexport function getPivotIndices(groupId, dragHandleId) {\n    const index = getResizeHandleElementIndex(groupId, dragHandleId);\n    return index != null ? [index, index + 1] : [-1, -1];\n}\nexport function paneDataHelper(panesArray, pane, layout) {\n    const paneConstraintsArray = panesArray.map((paneData) => paneData.constraints);\n    const paneIndex = findPaneDataIndex(panesArray, pane);\n    const paneConstraints = paneConstraintsArray[paneIndex];\n    const isLastPane = paneIndex === panesArray.length - 1;\n    const pivotIndices = isLastPane ? [paneIndex - 1, paneIndex] : [paneIndex, paneIndex + 1];\n    const paneSize = layout[paneIndex];\n    return {\n        ...paneConstraints,\n        paneSize,\n        pivotIndices,\n    };\n}\nexport function findPaneDataIndex(panesArray, pane) {\n    return panesArray.findIndex((prevPaneData) => prevPaneData.opts.id.current === pane.opts.id.current);\n}\n// Layout should be pre-converted into percentages\nexport function callPaneCallbacks(panesArray, layout, paneIdToLastNotifiedSizeMap) {\n    for (let index = 0; index < layout.length; index++) {\n        const size = layout[index];\n        const paneData = panesArray[index];\n        assert(paneData);\n        const { collapsedSize = 0, collapsible } = paneData.constraints;\n        const lastNotifiedSize = paneIdToLastNotifiedSizeMap[paneData.opts.id.current];\n        // invert the logic from below\n        if (!(lastNotifiedSize == null || size !== lastNotifiedSize))\n            continue;\n        paneIdToLastNotifiedSizeMap[paneData.opts.id.current] = size;\n        const { onCollapse, onExpand, onResize } = paneData.callbacks;\n        onResize?.(size, lastNotifiedSize);\n        if (collapsible && (onCollapse || onExpand)) {\n            if (onExpand &&\n                (lastNotifiedSize == null || lastNotifiedSize === collapsedSize) &&\n                size !== collapsedSize) {\n                onExpand();\n            }\n            if (onCollapse &&\n                (lastNotifiedSize == null || lastNotifiedSize !== collapsedSize) &&\n                size === collapsedSize) {\n                onCollapse();\n            }\n        }\n    }\n}\nexport function getUnsafeDefaultLayout({ panesArray }) {\n    const layout = Array(panesArray.length);\n    const paneConstraintsArray = panesArray.map((paneData) => paneData.constraints);\n    let numPanesWithSizes = 0;\n    let remainingSize = 100;\n    // Distribute default sizes first\n    for (let index = 0; index < panesArray.length; index++) {\n        const paneConstraints = paneConstraintsArray[index];\n        assert(paneConstraints);\n        const { defaultSize } = paneConstraints;\n        if (defaultSize != null) {\n            numPanesWithSizes++;\n            layout[index] = defaultSize;\n            remainingSize -= defaultSize;\n        }\n    }\n    // Remaining size should be distributed evenly between panes without default sizes\n    for (let index = 0; index < panesArray.length; index++) {\n        const paneConstraints = paneConstraintsArray[index];\n        assert(paneConstraints);\n        const { defaultSize } = paneConstraints;\n        if (defaultSize != null) {\n            continue;\n        }\n        const numRemainingPanes = panesArray.length - numPanesWithSizes;\n        const size = remainingSize / numRemainingPanes;\n        numPanesWithSizes++;\n        layout[index] = size;\n        remainingSize -= size;\n    }\n    return layout;\n}\n// All units must be in percentages\nexport function validatePaneGroupLayout({ layout: prevLayout, paneConstraints, }) {\n    const nextLayout = [...prevLayout];\n    const nextLayoutTotalSize = nextLayout.reduce((accumulated, current) => accumulated + current, 0);\n    // Validate layout expectations\n    if (nextLayout.length !== paneConstraints.length) {\n        throw new Error(`Invalid ${paneConstraints.length} pane layout: ${nextLayout\n            .map((size) => `${size}%`)\n            .join(\", \")}`);\n    }\n    else if (!areNumbersAlmostEqual(nextLayoutTotalSize, 100)) {\n        for (let index = 0; index < paneConstraints.length; index++) {\n            const unsafeSize = nextLayout[index];\n            assert(unsafeSize != null);\n            const safeSize = (100 / nextLayoutTotalSize) * unsafeSize;\n            nextLayout[index] = safeSize;\n        }\n    }\n    let remainingSize = 0;\n    // First pass: Validate the proposed layout given each pane's constraints\n    for (let index = 0; index < paneConstraints.length; index++) {\n        const unsafeSize = nextLayout[index];\n        assert(unsafeSize != null);\n        const safeSize = resizePane({\n            paneConstraints,\n            paneIndex: index,\n            initialSize: unsafeSize,\n        });\n        if (unsafeSize !== safeSize) {\n            remainingSize += unsafeSize - safeSize;\n            nextLayout[index] = safeSize;\n        }\n    }\n    // If there is additional, left over space, assign it to any pane(s) that permits it\n    // (It's not worth taking multiple additional passes to evenly distribute)\n    if (!areNumbersAlmostEqual(remainingSize, 0)) {\n        for (let index = 0; index < paneConstraints.length; index++) {\n            const prevSize = nextLayout[index];\n            assert(prevSize != null);\n            const unsafeSize = prevSize + remainingSize;\n            const safeSize = resizePane({\n                paneConstraints,\n                paneIndex: index,\n                initialSize: unsafeSize,\n            });\n            if (prevSize !== safeSize) {\n                remainingSize -= safeSize - prevSize;\n                nextLayout[index] = safeSize;\n                // Once we've used up the remainder, bail\n                if (areNumbersAlmostEqual(remainingSize, 0)) {\n                    break;\n                }\n            }\n        }\n    }\n    return nextLayout;\n}\nexport function getPaneGroupElement(id) {\n    if (!isBrowser)\n        return null;\n    const element = document.querySelector(`[data-pane-group][data-pane-group-id=\"${id}\"]`);\n    if (element) {\n        return element;\n    }\n    return null;\n}\nexport function getResizeHandleElement(id) {\n    if (!isBrowser)\n        return null;\n    const element = document.querySelector(`[data-pane-resizer-id=\"${id}\"]`);\n    if (element) {\n        return element;\n    }\n    return null;\n}\nexport function getDragOffsetPercentage(e, dragHandleId, dir, initialDragState) {\n    const isHorizontal = dir === \"horizontal\";\n    const handleElement = getResizeHandleElement(dragHandleId);\n    assert(handleElement);\n    const groupId = handleElement.getAttribute(\"data-pane-group-id\");\n    assert(groupId);\n    const { initialCursorPosition } = initialDragState;\n    const cursorPosition = getResizeEventCursorPosition(dir, e);\n    const groupElement = getPaneGroupElement(groupId);\n    assert(groupElement);\n    const groupRect = groupElement.getBoundingClientRect();\n    const groupSizeInPixels = isHorizontal ? groupRect.width : groupRect.height;\n    const offsetPixels = cursorPosition - initialCursorPosition;\n    const offsetPercentage = (offsetPixels / groupSizeInPixels) * 100;\n    return offsetPercentage;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/movementX\nexport function getDeltaPercentage(e, dragHandleId, dir, initialDragState, keyboardResizeBy) {\n    if (isKeyDown(e)) {\n        const isHorizontal = dir === \"horizontal\";\n        let delta = 0;\n        if (e.shiftKey) {\n            delta = 100;\n        }\n        else if (keyboardResizeBy != null) {\n            delta = keyboardResizeBy;\n        }\n        else {\n            delta = 10;\n        }\n        let movement = 0;\n        switch (e.key) {\n            case \"ArrowDown\":\n                movement = isHorizontal ? 0 : delta;\n                break;\n            case \"ArrowLeft\":\n                movement = isHorizontal ? -delta : 0;\n                break;\n            case \"ArrowRight\":\n                movement = isHorizontal ? delta : 0;\n                break;\n            case \"ArrowUp\":\n                movement = isHorizontal ? 0 : -delta;\n                break;\n            case \"End\":\n                movement = 100;\n                break;\n            case \"Home\":\n                movement = -100;\n                break;\n        }\n        return movement;\n    }\n    else {\n        if (initialDragState == null)\n            return 0;\n        return getDragOffsetPercentage(e, dragHandleId, dir, initialDragState);\n    }\n}\nexport function getResizeEventCursorPosition(dir, e) {\n    const isHorizontal = dir === \"horizontal\";\n    if (isMouseEvent(e)) {\n        return isHorizontal ? e.clientX : e.clientY;\n    }\n    else if (isTouchEvent(e)) {\n        const firstTouch = e.touches[0];\n        assert(firstTouch);\n        return isHorizontal ? firstTouch.screenX : firstTouch.screenY;\n    }\n    else {\n        throw new Error(`Unsupported event type \"${e.type}\"`);\n    }\n}\nexport function getResizeHandlePaneIds(groupId, handleId, panesArray) {\n    const handle = getResizeHandleElement(handleId);\n    const handles = getResizeHandleElementsForGroup(groupId);\n    const index = handle ? handles.indexOf(handle) : -1;\n    const idBefore = panesArray[index]?.opts.id.current ?? null;\n    const idAfter = panesArray[index + 1]?.opts.id.current ?? null;\n    return [idBefore, idAfter];\n}\n", "let count = 0;\n/**\n * Generates a unique ID based on a global counter.\n */\nexport function useId(prefix = \"paneforge\") {\n    count++;\n    return `${prefix}-${count}`;\n}\n", "import { assert } from \"./assert.js\";\nimport { areNumbersAlmostEqual, compareNumbersWithTolerance } from \"./compare.js\";\nimport { resizePane } from \"./resize.js\";\n/**\n * Adjusts the layout of panes based on the delta of the resize handle.\n * All units must be in percentages; pixel values should be pre-converted.\n *\n * Credit: https://github.com/bvaughn/react-resizable-panels\n */\nexport function adjustLayoutByDelta({ delta, layout: prevLayout, paneConstraints: paneConstraintsArray, pivotIndices, trigger, }) {\n    if (areNumbersAlmostEqual(delta, 0))\n        return prevLayout;\n    const nextLayout = [...prevLayout];\n    const [firstPivotIndex, secondPivotIndex] = pivotIndices;\n    let deltaApplied = 0;\n    // A resizing pane affects the panes before or after it.\n    //\n    // A negative delta means the pane(s) immediately after the resize handle should grow/expand by decreasing its offset.\n    // Other panes may also need to shrink/contract (and shift) to make room, depending on the min weights.\n    //\n    // A positive delta means the pane(s) immediately before the resize handle should \"expand\".\n    // This is accomplished by shrinking/contracting (and shifting) one or more of the panes after the resize handle.\n    {\n        // If this is a resize triggered by a keyboard event, our logic for expanding/collapsing is different.\n        // We no longer check the halfway threshold because this may prevent the pane from expanding at all.\n        if (trigger === \"keyboard\") {\n            {\n                // Check if we should expand a collapsed pane\n                const index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n                const paneConstraints = paneConstraintsArray[index];\n                assert(paneConstraints);\n                if (paneConstraints.collapsible) {\n                    const prevSize = prevLayout[index];\n                    assert(prevSize != null);\n                    const paneConstraints = paneConstraintsArray[index];\n                    assert(paneConstraints);\n                    const { collapsedSize = 0, minSize = 0 } = paneConstraints;\n                    if (areNumbersAlmostEqual(prevSize, collapsedSize)) {\n                        const localDelta = minSize - prevSize;\n                        //DEBUG.push(`  -> expand delta: ${localDelta}`);\n                        if (compareNumbersWithTolerance(localDelta, Math.abs(delta)) > 0) {\n                            delta = delta < 0 ? 0 - localDelta : localDelta;\n                            //DEBUG.push(`  -> delta: ${delta}`);\n                        }\n                    }\n                }\n            }\n            {\n                // Check if we should collapse a pane at its minimum size\n                const index = delta < 0 ? firstPivotIndex : secondPivotIndex;\n                const paneConstraints = paneConstraintsArray[index];\n                assert(paneConstraints);\n                const { collapsible } = paneConstraints;\n                if (collapsible) {\n                    const prevSize = prevLayout[index];\n                    assert(prevSize != null);\n                    const paneConstraints = paneConstraintsArray[index];\n                    assert(paneConstraints);\n                    const { collapsedSize = 0, minSize = 0 } = paneConstraints;\n                    if (areNumbersAlmostEqual(prevSize, minSize)) {\n                        const localDelta = prevSize - collapsedSize;\n                        if (compareNumbersWithTolerance(localDelta, Math.abs(delta)) > 0) {\n                            delta = delta < 0 ? 0 - localDelta : localDelta;\n                        }\n                    }\n                }\n            }\n        }\n    }\n    {\n        // Pre-calculate max available delta in the opposite direction of our pivot.\n        // This will be the maximum amount we're allowed to expand/contract the panes in the primary direction.\n        // If this amount is less than the requested delta, adjust the requested delta.\n        // If this amount is greater than the requested delta, that's useful information too–\n        // as an expanding pane might change from collapsed to min size.\n        const increment = delta < 0 ? 1 : -1;\n        let index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n        let maxAvailableDelta = 0;\n        while (true) {\n            const prevSize = prevLayout[index];\n            assert(prevSize != null);\n            const maxSafeSize = resizePane({\n                paneConstraints: paneConstraintsArray,\n                paneIndex: index,\n                initialSize: 100,\n            });\n            const delta = maxSafeSize - prevSize;\n            maxAvailableDelta += delta;\n            index += increment;\n            if (index < 0 || index >= paneConstraintsArray.length) {\n                break;\n            }\n        }\n        const minAbsDelta = Math.min(Math.abs(delta), Math.abs(maxAvailableDelta));\n        delta = delta < 0 ? 0 - minAbsDelta : minAbsDelta;\n    }\n    {\n        // Delta added to a pane needs to be subtracted from other panes (within the constraints that those panes allow).\n        const pivotIndex = delta < 0 ? firstPivotIndex : secondPivotIndex;\n        let index = pivotIndex;\n        while (index >= 0 && index < paneConstraintsArray.length) {\n            const deltaRemaining = Math.abs(delta) - Math.abs(deltaApplied);\n            const prevSize = prevLayout[index];\n            assert(prevSize != null);\n            const unsafeSize = prevSize - deltaRemaining;\n            const safeSize = resizePane({\n                paneConstraints: paneConstraintsArray,\n                paneIndex: index,\n                initialSize: unsafeSize,\n            });\n            if (!areNumbersAlmostEqual(prevSize, safeSize)) {\n                deltaApplied += prevSize - safeSize;\n                nextLayout[index] = safeSize;\n                if (deltaApplied\n                    .toPrecision(3)\n                    .localeCompare(Math.abs(delta).toPrecision(3), undefined, {\n                    numeric: true,\n                }) >= 0) {\n                    break;\n                }\n            }\n            if (delta < 0) {\n                index--;\n            }\n            else {\n                index++;\n            }\n        }\n    }\n    // If we were unable to resize any of the panes, return the previous state.\n    // This will essentially bailout and ignore e.g. drags past a pane's boundaries\n    if (areNumbersAlmostEqual(deltaApplied, 0)) {\n        return prevLayout;\n    }\n    {\n        // Now distribute the applied delta to the panes in the other direction\n        const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n        const prevSize = prevLayout[pivotIndex];\n        assert(prevSize != null);\n        const unsafeSize = prevSize + deltaApplied;\n        const safeSize = resizePane({\n            paneConstraints: paneConstraintsArray,\n            paneIndex: pivotIndex,\n            initialSize: unsafeSize,\n        });\n        // Adjust the pivot pane before, but only by the amount that surrounding panes were able to shrink/contract.\n        nextLayout[pivotIndex] = safeSize;\n        // Edge case where expanding or contracting one pane caused another one to change collapsed state\n        if (!areNumbersAlmostEqual(safeSize, unsafeSize)) {\n            let deltaRemaining = unsafeSize - safeSize;\n            const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n            let index = pivotIndex;\n            while (index >= 0 && index < paneConstraintsArray.length) {\n                const prevSize = nextLayout[index];\n                assert(prevSize != null);\n                const unsafeSize = prevSize + deltaRemaining;\n                const safeSize = resizePane({\n                    paneConstraints: paneConstraintsArray,\n                    paneIndex: index,\n                    initialSize: unsafeSize,\n                });\n                if (!areNumbersAlmostEqual(prevSize, safeSize)) {\n                    deltaRemaining -= safeSize - prevSize;\n                    nextLayout[index] = safeSize;\n                }\n                if (areNumbersAlmostEqual(deltaRemaining, 0))\n                    break;\n                delta > 0 ? index-- : index++;\n            }\n        }\n    }\n    const totalSize = nextLayout.reduce((total, size) => size + total, 0);\n    if (!areNumbersAlmostEqual(totalSize, 100))\n        return prevLayout;\n    return nextLayout;\n}\n", "/* Global cursor state */\nlet currentState = null;\n/* Global cursor element */\nlet element = null;\n/**\n * Returns the cursor style for a given cursor state.\n */\nexport function getCursorStyle(state) {\n    switch (state) {\n        case \"horizontal\":\n            return \"ew-resize\";\n        case \"horizontal-max\":\n            return \"w-resize\";\n        case \"horizontal-min\":\n            return \"e-resize\";\n        case \"vertical\":\n            return \"ns-resize\";\n        case \"vertical-max\":\n            return \"n-resize\";\n        case \"vertical-min\":\n            return \"s-resize\";\n    }\n}\n/**\n * Resets the global cursor style to the default.\n */\nexport function resetGlobalCursorStyle() {\n    if (element === null)\n        return;\n    document.head.removeChild(element);\n    currentState = null;\n    element = null;\n}\n/**\n * Sets the global cursor style to the given state.\n */\nexport function setGlobalCursorStyle(state) {\n    if (currentState === state)\n        return;\n    currentState = state;\n    const style = getCursorStyle(state);\n    if (element === null) {\n        element = document.createElement(\"style\");\n        document.head.appendChild(element);\n    }\n    element.innerHTML = `*{cursor: ${style}!important;}`;\n}\n/**\n * Computes the flexbox style for a pane given its layout and drag state.\n */\nexport function computePaneFlexBoxStyle({ defaultSize, dragState, layout, panesArray, paneIndex, precision = 3, }) {\n    const size = layout[paneIndex];\n    let flexGrow;\n    if (size == null) {\n        // Initial render (before panes have registered themselves)\n        // To support server rendering, fallback to default size\n        flexGrow = defaultSize ?? \"1\";\n    }\n    else if (panesArray.length === 1) {\n        //  Single pane group should always fill full width/height\n        flexGrow = \"1\";\n    }\n    else {\n        flexGrow = size.toPrecision(precision);\n    }\n    return {\n        flexBasis: 0,\n        flexGrow,\n        flexShrink: 1,\n        // Without this, pane sizes may be unintentionally overridden by their content\n        overflow: \"hidden\",\n        // Disable pointer events inside of a pane during resize\n        // This avoid edge cases like nested iframes\n        pointerEvents: dragState !== null ? \"none\" : undefined,\n    };\n}\n", "import { LOCAL_STORAGE_DEBOUNCE_INTERVAL } from \"../constants.js\";\n/**\n * Initializes the storage object with the appropriate getItem\n *  and setItem functions depending on the environment (browser or not).\n */\nexport function initializeStorage(storageObject) {\n    try {\n        if (typeof localStorage === \"undefined\") {\n            throw new TypeError(\"localStorage is not supported in this environment\");\n        }\n        storageObject.getItem = (name) => localStorage.getItem(name);\n        storageObject.setItem = (name, value) => localStorage.setItem(name, value);\n    }\n    catch (err) {\n        console.error(err);\n        storageObject.getItem = () => null;\n        storageObject.setItem = () => { };\n    }\n}\n/**\n * Returns the key to use for storing the pane group state in local storage.\n */\nfunction getPaneGroupKey(autoSaveId) {\n    return `paneforge:${autoSaveId}`;\n}\n/**\n * Returns a key to use for storing the pane state in local storage.\n * The key is based on the pane order and constraints.\n */\nfunction getPaneKey(panes) {\n    const sortedPaneIds = panes\n        .map((pane) => {\n        return pane.opts.order.current\n            ? `${pane.opts.order.current}:${JSON.stringify(pane.constraints)}`\n            : JSON.stringify(pane.constraints);\n    })\n        .sort()\n        .join(\",\");\n    return sortedPaneIds;\n}\n/**\n * Loads the serialized pane group state from local storage.\n * If the state is not found, returns null.\n */\nfunction loadSerializedPaneGroupState(autoSaveId, storage) {\n    try {\n        const paneGroupKey = getPaneGroupKey(autoSaveId);\n        const serialized = storage.getItem(paneGroupKey);\n        const parsed = JSON.parse(serialized || \"\");\n        if (typeof parsed === \"object\" && parsed !== null) {\n            return parsed;\n        }\n    }\n    catch {\n        // noop\n    }\n    return null;\n}\n/**\n * Loads the pane group state from local storage.\n * If the state is not found, returns null.\n */\nexport function loadPaneGroupState(autoSaveId, panesArray, storage) {\n    const state = loadSerializedPaneGroupState(autoSaveId, storage) || {};\n    const paneKey = getPaneKey(panesArray);\n    return state[paneKey] || null;\n}\n/**\n * Saves the pane group state to local storage.\n */\nexport function savePaneGroupState(autoSaveId, panesArray, paneSizesBeforeCollapse, sizes, storage) {\n    const paneGroupKey = getPaneGroupKey(autoSaveId);\n    const paneKey = getPaneKey(panesArray);\n    const state = loadSerializedPaneGroupState(autoSaveId, storage) || {};\n    state[paneKey] = {\n        expandToSizes: Object.fromEntries(paneSizesBeforeCollapse.entries()),\n        layout: sizes,\n    };\n    try {\n        storage.setItem(paneGroupKey, JSON.stringify(state));\n    }\n    catch (error) {\n        console.error(error);\n    }\n}\nconst debounceMap = {};\n/**\n * Returns a debounced version of the given function.\n */\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction debounce(callback, durationMs = 10) {\n    let timeoutId = null;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const callable = (...args) => {\n        if (timeoutId !== null) {\n            clearTimeout(timeoutId);\n        }\n        timeoutId = setTimeout(() => {\n            callback(...args);\n        }, durationMs);\n    };\n    return callable;\n}\n/**\n * Updates the values in local storage based on the current state of\n * the pane group.\n * This function is debounced to limit the frequency of local storage writes.\n */\nexport function updateStorageValues({ autoSaveId, layout, storage, panesArray, paneSizeBeforeCollapse, }) {\n    // If this pane has been configured to persist sizing\n    // information, save sizes to local storage.\n    if (layout.length === 0 || layout.length !== panesArray.length)\n        return;\n    let debouncedSave = debounceMap[autoSaveId];\n    // Limit frequency of local storage writes.\n    if (debouncedSave == null) {\n        debouncedSave = debounce(savePaneGroupState, LOCAL_STORAGE_DEBOUNCE_INTERVAL);\n        debounceMap[autoSaveId] = debouncedSave;\n    }\n    // Clone mutable data before passing to the debounced function,\n    // else we run the risk of saving an incorrect combination of mutable and immutable values to state.\n    const clonedPanesArray = [...panesArray];\n    const clonedPaneSizesBeforeCollapse = new Map(paneSizeBeforeCollapse);\n    debouncedSave(autoSaveId, clonedPanesArray, clonedPaneSizesBeforeCollapse, layout, storage);\n}\n", "import { addEventListener, executeCallbacks, useRefById, afterTick, } from \"svelte-toolbelt\";\nimport { onMount, untrack } from \"svelte\";\nimport { Context, watch } from \"runed\";\nimport { callPaneCallbacks, findPaneDataIndex, getDeltaPercentage, getPivotIndices, getResizeEventCursorPosition, getResizeHandleElement, getResizeHandleElementIndex, getResizeHandleElementsForGroup, getResizeHandlePaneIds, getUnsafeDefaultLayout, noop, paneDataHelper, updateResizeHandleAriaValues, validatePaneGroupLayout, } from \"./internal/helpers.js\";\nimport { isKeyDown, isMouseEvent, isTouchEvent } from \"./internal/utils/is.js\";\nimport { adjustLayoutByDelta } from \"./internal/utils/adjust-layout.js\";\nimport { areArraysEqual, areNumbersAlmostEqual } from \"./internal/utils/compare.js\";\nimport { computePaneFlexBoxStyle, getCursorStyle, resetGlobalCursorStyle, setGlobalCursorStyle, } from \"./internal/utils/style.js\";\nimport { assert } from \"./internal/utils/assert.js\";\nimport { initializeStorage, loadPaneGroupState, updateStorageValues, } from \"./internal/utils/storage.js\";\nimport { on } from \"svelte/events\";\nexport const defaultStorage = {\n    getItem: (name) => {\n        initializeStorage(defaultStorage);\n        return defaultStorage.getItem(name);\n    },\n    setItem: (name, value) => {\n        initializeStorage(defaultStorage);\n        defaultStorage.setItem(name, value);\n    },\n};\nclass PaneGroupState {\n    opts;\n    dragState = $state.raw(null);\n    layout = $state.raw([]);\n    panesArray = $state.raw([]);\n    panesArrayChanged = $state(false);\n    paneIdToLastNotifiedSizeMap = $state({});\n    paneSizeBeforeCollapseMap = new Map();\n    prevDelta = $state(0);\n    constructor(opts) {\n        this.opts = opts;\n        useRefById(opts);\n        watch([() => this.opts.id.current, () => this.layout, () => this.panesArray], () => {\n            return updateResizeHandleAriaValues({\n                groupId: this.opts.id.current,\n                layout: this.layout,\n                panesArray: this.panesArray,\n            });\n        });\n        $effect(() => {\n            return untrack(() => {\n                return this.#setResizeHandlerEventListeners();\n            });\n        });\n        watch([\n            () => this.opts.autoSaveId.current,\n            () => this.layout,\n            () => this.opts.storage.current,\n        ], () => {\n            if (!this.opts.autoSaveId.current)\n                return;\n            updateStorageValues({\n                autoSaveId: this.opts.autoSaveId.current,\n                layout: this.layout,\n                storage: this.opts.storage.current,\n                panesArray: this.panesArray,\n                paneSizeBeforeCollapse: this.paneSizeBeforeCollapseMap,\n            });\n        });\n        watch(() => this.panesArrayChanged, () => {\n            if (!this.panesArrayChanged)\n                return;\n            this.panesArrayChanged = false;\n            // const autoSaveId = this.opts.autoSaveId.current;\n            // const storage = this.opts.storage.current;\n            const prevLayout = this.layout;\n            // const paneDataArray = this.panesArray;\n            let unsafeLayout = null;\n            if (this.opts.autoSaveId.current) {\n                const state = loadPaneGroupState(this.opts.autoSaveId.current, this.panesArray, this.opts.storage.current);\n                if (state) {\n                    this.paneSizeBeforeCollapseMap = new Map(Object.entries(state.expandToSizes));\n                    unsafeLayout = state.layout;\n                }\n            }\n            if (unsafeLayout == null) {\n                unsafeLayout = getUnsafeDefaultLayout({\n                    panesArray: this.panesArray,\n                });\n            }\n            const nextLayout = validatePaneGroupLayout({\n                layout: unsafeLayout,\n                paneConstraints: this.panesArray.map((paneData) => paneData.constraints),\n            });\n            if (areArraysEqual(prevLayout, nextLayout))\n                return;\n            this.layout = nextLayout;\n            this.opts.onLayout.current?.(nextLayout);\n            callPaneCallbacks(this.panesArray, nextLayout, this.paneIdToLastNotifiedSizeMap);\n        });\n    }\n    setLayout = (newLayout) => {\n        this.layout = newLayout;\n    };\n    registerResizeHandle = (dragHandleId) => {\n        return (e) => {\n            e.preventDefault();\n            const direction = this.opts.direction.current;\n            const dragState = this.dragState;\n            const groupId = this.opts.id.current;\n            const keyboardResizeBy = this.opts.keyboardResizeBy.current;\n            const prevLayout = this.layout;\n            const paneDataArray = this.panesArray;\n            const { initialLayout } = dragState ?? {};\n            const pivotIndices = getPivotIndices(groupId, dragHandleId);\n            let delta = getDeltaPercentage(e, dragHandleId, direction, dragState, keyboardResizeBy);\n            if (delta === 0)\n                return;\n            // support RTL\n            const isHorizontal = direction === \"horizontal\";\n            if (document.dir === \"rtl\" && isHorizontal) {\n                delta = -delta;\n            }\n            const paneConstraints = paneDataArray.map((paneData) => paneData.constraints);\n            const nextLayout = adjustLayoutByDelta({\n                delta,\n                layout: initialLayout ?? prevLayout,\n                paneConstraints,\n                pivotIndices,\n                trigger: isKeyDown(e) ? \"keyboard\" : \"mouse-or-touch\",\n            });\n            const layoutChanged = !areArraysEqual(prevLayout, nextLayout);\n            // Only update the cursor for layout changes triggered by touch/mouse events (not keyboard)\n            // Update the cursor even if the layout hasn't changed (we may need to show an invalid cursor state)\n            if (isMouseEvent(e) || isTouchEvent(e)) {\n                // Watch for multiple subsequent deltas; this might occur for tiny cursor movements.\n                // In this case, Pane sizes might not change–\n                // but updating cursor in this scenario would cause a flicker.\n                const prevDelta = this.prevDelta;\n                if (prevDelta !== delta) {\n                    this.prevDelta = delta;\n                    if (!layoutChanged) {\n                        // If the pointer has moved too far to resize the pane any further,\n                        // update the cursor style for a visual clue.\n                        // This mimics VS Code behavior.\n                        if (isHorizontal) {\n                            setGlobalCursorStyle(delta < 0 ? \"horizontal-min\" : \"horizontal-max\");\n                        }\n                        else {\n                            setGlobalCursorStyle(delta < 0 ? \"vertical-min\" : \"vertical-max\");\n                        }\n                    }\n                    else {\n                        setGlobalCursorStyle(isHorizontal ? \"horizontal\" : \"vertical\");\n                    }\n                }\n            }\n            if (layoutChanged) {\n                this.setLayout(nextLayout);\n                this.opts.onLayout.current?.(nextLayout);\n                callPaneCallbacks(paneDataArray, nextLayout, this.paneIdToLastNotifiedSizeMap);\n            }\n        };\n    };\n    resizePane = (paneState, unsafePaneSize) => {\n        const prevLayout = this.layout;\n        const panesArray = this.panesArray;\n        const paneConstraintsArr = panesArray.map((paneData) => paneData.constraints);\n        const { paneSize, pivotIndices } = paneDataHelper(panesArray, paneState, prevLayout);\n        assert(paneSize != null);\n        const isLastPane = findPaneDataIndex(panesArray, paneState) === panesArray.length - 1;\n        const delta = isLastPane ? paneSize - unsafePaneSize : unsafePaneSize - paneSize;\n        const nextLayout = adjustLayoutByDelta({\n            delta,\n            layout: prevLayout,\n            paneConstraints: paneConstraintsArr,\n            pivotIndices,\n            trigger: \"imperative-api\",\n        });\n        if (areArraysEqual(prevLayout, nextLayout))\n            return;\n        this.setLayout(nextLayout);\n        this.opts.onLayout.current?.(nextLayout);\n        callPaneCallbacks(panesArray, nextLayout, this.paneIdToLastNotifiedSizeMap);\n    };\n    startDragging = (dragHandleId, e) => {\n        const direction = this.opts.direction.current;\n        const layout = this.layout;\n        const handleElement = getResizeHandleElement(dragHandleId);\n        assert(handleElement);\n        const initialCursorPosition = getResizeEventCursorPosition(direction, e);\n        this.dragState = {\n            dragHandleId,\n            dragHandleRect: handleElement.getBoundingClientRect(),\n            initialCursorPosition,\n            initialLayout: layout,\n        };\n    };\n    stopDragging = () => {\n        resetGlobalCursorStyle();\n        this.dragState = null;\n    };\n    isPaneCollapsed = (pane) => {\n        const paneDataArray = this.panesArray;\n        const layout = this.layout;\n        const { collapsedSize = 0, collapsible, paneSize, } = paneDataHelper(paneDataArray, pane, layout);\n        return collapsible === true && paneSize === collapsedSize;\n    };\n    expandPane = (pane) => {\n        const prevLayout = this.layout;\n        const paneDataArray = this.panesArray;\n        if (!pane.constraints.collapsible)\n            return;\n        const paneConstraintsArray = paneDataArray.map((paneData) => paneData.constraints);\n        const { collapsedSize = 0, paneSize, minSize = 0, pivotIndices, } = paneDataHelper(paneDataArray, pane, prevLayout);\n        if (paneSize !== collapsedSize)\n            return;\n        // restore this pane to the size it was before it was collapsed, if possible.\n        const prevPaneSize = this.paneSizeBeforeCollapseMap.get(pane.opts.id.current);\n        const baseSize = prevPaneSize != null && prevPaneSize >= minSize ? prevPaneSize : minSize;\n        const isLastPane = findPaneDataIndex(paneDataArray, pane) === paneDataArray.length - 1;\n        const delta = isLastPane ? paneSize - baseSize : baseSize - paneSize;\n        const nextLayout = adjustLayoutByDelta({\n            delta,\n            layout: prevLayout,\n            paneConstraints: paneConstraintsArray,\n            pivotIndices,\n            trigger: \"imperative-api\",\n        });\n        if (areArraysEqual(prevLayout, nextLayout))\n            return;\n        this.setLayout(nextLayout);\n        this.opts.onLayout.current?.(nextLayout);\n        callPaneCallbacks(paneDataArray, nextLayout, this.paneIdToLastNotifiedSizeMap);\n    };\n    collapsePane = (pane) => {\n        const prevLayout = this.layout;\n        const paneDataArray = this.panesArray;\n        if (!pane.constraints.collapsible)\n            return;\n        const paneConstraintsArray = paneDataArray.map((paneData) => paneData.constraints);\n        const { collapsedSize = 0, paneSize, pivotIndices, } = paneDataHelper(paneDataArray, pane, prevLayout);\n        assert(paneSize != null);\n        if (paneSize === collapsedSize)\n            return;\n        // Store the size before collapse, which is returned when `expand()` is called\n        this.paneSizeBeforeCollapseMap.set(pane.opts.id.current, paneSize);\n        const isLastPane = findPaneDataIndex(paneDataArray, pane) === paneDataArray.length - 1;\n        const delta = isLastPane ? paneSize - collapsedSize : collapsedSize - paneSize;\n        const nextLayout = adjustLayoutByDelta({\n            delta,\n            layout: prevLayout,\n            paneConstraints: paneConstraintsArray,\n            pivotIndices,\n            trigger: \"imperative-api\",\n        });\n        if (areArraysEqual(prevLayout, nextLayout))\n            return;\n        this.layout = nextLayout;\n        this.opts.onLayout.current?.(nextLayout);\n        callPaneCallbacks(paneDataArray, nextLayout, this.paneIdToLastNotifiedSizeMap);\n    };\n    getPaneSize = (pane) => {\n        return paneDataHelper(this.panesArray, pane, this.layout).paneSize;\n    };\n    getPaneStyle = (pane, defaultSize) => {\n        const paneDataArray = this.panesArray;\n        const layout = this.layout;\n        const dragState = this.dragState;\n        const paneIndex = findPaneDataIndex(paneDataArray, pane);\n        return computePaneFlexBoxStyle({\n            defaultSize,\n            dragState,\n            layout,\n            panesArray: paneDataArray,\n            paneIndex,\n        });\n    };\n    isPaneExpanded = (pane) => {\n        const { collapsedSize = 0, collapsible, paneSize, } = paneDataHelper(this.panesArray, pane, this.layout);\n        return !collapsible || paneSize > collapsedSize;\n    };\n    registerPane = (pane) => {\n        const newPaneDataArray = [...this.panesArray, pane];\n        newPaneDataArray.sort((paneA, paneB) => {\n            const orderA = paneA.opts.order.current;\n            const orderB = paneB.opts.order.current;\n            if (orderA == null && orderB == null) {\n                return 0;\n            }\n            else if (orderA == null) {\n                return -1;\n            }\n            else if (orderB == null) {\n                return 1;\n            }\n            else {\n                return orderA - orderB;\n            }\n        });\n        this.panesArray = newPaneDataArray;\n        this.panesArrayChanged = true;\n        return () => {\n            const paneDataArray = [...this.panesArray];\n            const index = findPaneDataIndex(this.panesArray, pane);\n            if (index < 0)\n                return;\n            paneDataArray.splice(index, 1);\n            this.panesArray = paneDataArray;\n            delete this.paneIdToLastNotifiedSizeMap[pane.opts.id.current];\n            this.panesArrayChanged = true;\n        };\n    };\n    #setResizeHandlerEventListeners = () => {\n        const groupId = this.opts.id.current;\n        const handles = getResizeHandleElementsForGroup(groupId);\n        const paneDataArray = this.panesArray;\n        const unsubHandlers = handles.map((handle) => {\n            const handleId = handle.getAttribute(\"data-pane-resizer-id\");\n            if (!handleId)\n                return noop;\n            const [idBefore, idAfter] = getResizeHandlePaneIds(groupId, handleId, paneDataArray);\n            if (idBefore == null || idAfter == null)\n                return noop;\n            const onKeydown = (e) => {\n                if (e.defaultPrevented || e.key !== \"Enter\")\n                    return;\n                e.preventDefault();\n                const paneDataArray = this.panesArray;\n                const index = paneDataArray.findIndex((paneData) => paneData.opts.id.current === idBefore);\n                if (index < 0)\n                    return;\n                const paneData = paneDataArray[index];\n                assert(paneData);\n                const layout = this.layout;\n                const size = layout[index];\n                const { collapsedSize = 0, collapsible, minSize = 0 } = paneData.constraints;\n                if (!(size != null && collapsible))\n                    return;\n                const nextLayout = adjustLayoutByDelta({\n                    delta: areNumbersAlmostEqual(size, collapsedSize)\n                        ? minSize - size\n                        : collapsedSize - size,\n                    layout,\n                    paneConstraints: paneDataArray.map((paneData) => paneData.constraints),\n                    pivotIndices: getPivotIndices(groupId, handleId),\n                    trigger: \"keyboard\",\n                });\n                if (layout !== nextLayout) {\n                    this.layout = nextLayout;\n                }\n            };\n            const unsubListener = addEventListener(handle, \"keydown\", onKeydown);\n            return () => {\n                unsubListener();\n            };\n        });\n        return () => {\n            for (const unsub of unsubHandlers) {\n                unsub();\n            }\n        };\n    };\n    props = $derived.by(() => ({\n        id: this.opts.id.current,\n        \"data-pane-group\": \"\",\n        \"data-direction\": this.opts.direction.current,\n        \"data-pane-group-id\": this.opts.id.current,\n        style: {\n            display: \"flex\",\n            flexDirection: this.opts.direction.current === \"horizontal\" ? \"row\" : \"column\",\n            height: \"100%\",\n            overflow: \"hidden\",\n            width: \"100%\",\n        },\n    }));\n}\nconst resizeKeys = [\"ArrowDown\", \"ArrowLeft\", \"ArrowRight\", \"ArrowUp\", \"End\", \"Home\"];\nclass PaneResizerState {\n    opts;\n    group;\n    #isDragging = $derived.by(() => this.group.dragState?.dragHandleId === this.opts.id.current);\n    #isFocused = $state(false);\n    resizeHandler = null;\n    constructor(opts, group) {\n        this.opts = opts;\n        this.group = group;\n        useRefById(opts);\n        $effect(() => {\n            if (this.opts.disabled.current) {\n                this.resizeHandler = null;\n            }\n            else {\n                this.resizeHandler = this.group.registerResizeHandle(this.opts.id.current);\n            }\n        });\n        $effect(() => {\n            const node = this.opts.ref.current;\n            if (!node)\n                return;\n            const disabled = this.opts.disabled.current;\n            const resizeHandler = this.resizeHandler;\n            const isDragging = this.#isDragging;\n            if (disabled || resizeHandler === null || !isDragging)\n                return;\n            const onMove = (e) => {\n                resizeHandler(e);\n            };\n            const onMouseLeave = (e) => {\n                resizeHandler(e);\n            };\n            const stopDraggingAndBlur = () => {\n                node.blur();\n                this.group.stopDragging();\n                this.opts.onDraggingChange.current(false);\n            };\n            return executeCallbacks(on(document.body, \"contextmenu\", stopDraggingAndBlur), on(document.body, \"mousemove\", onMove), on(document.body, \"touchmove\", onMove, { passive: false }), on(document.body, \"mouseleave\", onMouseLeave), on(window, \"mouseup\", stopDraggingAndBlur), on(window, \"touchend\", stopDraggingAndBlur));\n        });\n    }\n    #startDragging = (e) => {\n        e.preventDefault();\n        if (this.opts.disabled.current)\n            return;\n        this.group.startDragging(this.opts.id.current, e);\n        this.opts.onDraggingChange.current(true);\n    };\n    #stopDraggingAndBlur = () => {\n        const node = this.opts.ref.current;\n        if (!node)\n            return;\n        node.blur();\n        this.group.stopDragging();\n        this.opts.onDraggingChange.current(false);\n    };\n    #onkeydown = (e) => {\n        if (this.opts.disabled.current || !this.resizeHandler || e.defaultPrevented)\n            return;\n        if (resizeKeys.includes(e.key)) {\n            e.preventDefault();\n            this.resizeHandler(e);\n            return;\n        }\n        if (e.key !== \"F6\")\n            return;\n        e.preventDefault();\n        const handles = getResizeHandleElementsForGroup(this.group.opts.id.current);\n        const index = getResizeHandleElementIndex(this.group.opts.id.current, this.opts.id.current);\n        if (index === null)\n            return;\n        let nextIndex = 0;\n        if (e.shiftKey) {\n            // Moving backwards\n            if (index > 0) {\n                nextIndex = index - 1;\n            }\n            else {\n                nextIndex = handles.length - 1;\n            }\n        }\n        else {\n            // Moving forwards\n            if (index + 1 < handles.length) {\n                nextIndex = index + 1;\n            }\n            else {\n                nextIndex = 0;\n            }\n        }\n        const nextHandle = handles[nextIndex];\n        nextHandle.focus();\n    };\n    #onblur = () => {\n        this.#isFocused = false;\n    };\n    #onfocus = () => {\n        this.#isFocused = true;\n    };\n    #onmousedown = (e) => {\n        this.#startDragging(e);\n    };\n    #onmouseup = () => {\n        this.#stopDraggingAndBlur();\n    };\n    #ontouchcancel = () => {\n        this.#stopDraggingAndBlur();\n    };\n    #ontouchend = () => {\n        this.#stopDraggingAndBlur();\n    };\n    #ontouchstart = (e) => {\n        this.#startDragging(e);\n    };\n    props = $derived.by(() => ({\n        id: this.opts.id.current,\n        role: \"separator\",\n        \"data-direction\": this.group.opts.direction.current,\n        \"data-pane-group-id\": this.group.opts.id.current,\n        \"data-active\": this.#isDragging\n            ? \"pointer\"\n            : this.#isFocused\n                ? \"keyboard\"\n                : undefined,\n        \"data-enabled\": !this.opts.disabled.current,\n        \"data-pane-resizer-id\": this.opts.id.current,\n        \"data-pane-resizer\": \"\",\n        tabIndex: this.opts.tabIndex.current,\n        style: {\n            cursor: getCursorStyle(this.group.opts.direction.current),\n            touchAction: \"none\",\n            userSelect: \"none\",\n            \"-webkit-user-select\": \"none\",\n            \"-webkit-touch-callout\": \"none\",\n        },\n        onkeydown: this.#onkeydown,\n        onblur: this.#onblur,\n        onfocus: this.#onfocus,\n        onmousedown: this.#onmousedown,\n        onmouseup: this.#onmouseup,\n        ontouchcancel: this.#ontouchcancel,\n        ontouchend: this.#ontouchend,\n        ontouchstart: this.#ontouchstart,\n    }));\n}\nexport class PaneState {\n    opts;\n    group;\n    #paneTransitionState = $state(\"\");\n    callbacks = $derived.by(() => ({\n        onCollapse: this.opts.onCollapse.current,\n        onExpand: this.opts.onExpand.current,\n        onResize: this.opts.onResize.current,\n    }));\n    constraints = $derived.by(() => ({\n        collapsedSize: this.opts.collapsedSize.current,\n        collapsible: this.opts.collapsible.current,\n        defaultSize: this.opts.defaultSize.current,\n        maxSize: this.opts.maxSize.current,\n        minSize: this.opts.minSize.current,\n    }));\n    #handleTransition = (state) => {\n        this.#paneTransitionState = state;\n        afterTick(() => {\n            if (this.opts.ref.current) {\n                const element = this.opts.ref.current;\n                const computedStyle = getComputedStyle(element);\n                const hasTransition = computedStyle.transitionDuration !== \"0s\";\n                if (!hasTransition) {\n                    this.#paneTransitionState = \"\";\n                    return;\n                }\n                const handleTransitionEnd = (event) => {\n                    // Only handle width/flex transitions\n                    if (event.propertyName === \"flex-grow\") {\n                        this.#paneTransitionState = \"\";\n                        element.removeEventListener(\"transitionend\", handleTransitionEnd);\n                    }\n                };\n                // Always add the listener - if there's no transition, it won't fire\n                element.addEventListener(\"transitionend\", handleTransitionEnd);\n            }\n            else {\n                this.#paneTransitionState = \"\";\n            }\n        });\n    };\n    pane = {\n        collapse: () => {\n            this.#handleTransition(\"collapsing\");\n            this.group.collapsePane(this);\n        },\n        expand: () => {\n            this.#handleTransition(\"expanding\");\n            this.group.expandPane(this);\n        },\n        getSize: () => this.group.getPaneSize(this),\n        isCollapsed: () => this.group.isPaneCollapsed(this),\n        isExpanded: () => this.group.isPaneExpanded(this),\n        resize: (size) => this.group.resizePane(this, size),\n        getId: () => this.opts.id.current,\n    };\n    constructor(opts, group) {\n        this.opts = opts;\n        this.group = group;\n        useRefById(opts);\n        onMount(() => {\n            return this.group.registerPane(this);\n        });\n        watch(() => $state.snapshot(this.constraints), () => {\n            this.group.panesArrayChanged = true;\n        });\n    }\n    #isCollapsed = $derived.by(() => this.group.isPaneCollapsed(this));\n    #paneState = $derived.by(() => this.#paneTransitionState !== \"\"\n        ? this.#paneTransitionState\n        : this.#isCollapsed\n            ? \"collapsed\"\n            : \"expanded\");\n    props = $derived.by(() => ({\n        id: this.opts.id.current,\n        style: this.group.getPaneStyle(this, this.opts.defaultSize.current),\n        \"data-pane\": \"\",\n        \"data-pane-id\": this.opts.id.current,\n        \"data-pane-group-id\": this.group.opts.id.current,\n        \"data-collapsed\": this.#isCollapsed ? \"\" : undefined,\n        \"data-expanded\": this.#isCollapsed ? undefined : \"\",\n        \"data-pane-state\": this.#paneState,\n    }));\n}\nconst PaneGroupContext = new Context(\"PaneGroup\");\nexport function usePaneGroup(props) {\n    return PaneGroupContext.set(new PaneGroupState(props));\n}\nexport function usePaneResizer(props) {\n    return new PaneResizerState(props, PaneGroupContext.get());\n}\nexport function usePane(props) {\n    return new PaneState(props, PaneGroupContext.get());\n}\n", null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIO,SAAS,oBAAoB,EAAE,QAAQ,YAAY,aAAc,GAAG;AACvE,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACnB,MAAI,eAAe;AACnB,QAAM,aAAa,aAAa,CAAC;AAEjC,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,UAAM,cAAc,WAAW,CAAC,EAAE;AAClC,UAAM,EAAE,UAAU,KAAK,UAAU,EAAE,IAAI;AACvC,QAAI,MAAM,YAAY;AAClB,uBAAiB;AACjB,uBAAiB;AAAA,IACrB,OACK;AACD,sBAAgB;AAChB,sBAAgB;AAAA,IACpB;AAAA,EACJ;AACA,QAAM,WAAW,KAAK,IAAI,gBAAgB,MAAM,YAAY;AAC5D,QAAM,WAAW,KAAK,IAAI,gBAAgB,MAAM,YAAY;AAC5D,QAAM,WAAW,OAAO,UAAU;AAClC,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;;;AC/BO,SAAS,OAEhB,mBAAmB,UAAU,qBAAqB;AAC9C,MAAI,CAAC,mBAAmB;AACpB,YAAQ,MAAM,OAAO;AACrB,UAAM,IAAI,MAAM,OAAO;AAAA,EAC3B;AACJ;;;ACPO,IAAM,kCAAkC;AACxC,IAAM,YAAY;;;ACGlB,SAAS,sBAAsB,QAAQ,UAAU,iBAAiB,WAAW;AAChF,SAAO,4BAA4B,QAAQ,UAAU,cAAc,MAAM;AAC7E;AAOO,SAAS,4BAA4B,QAAQ,UAAU,iBAAiB,WAAW;AACtF,QAAM,gBAAgB,QAAQ,QAAQ,cAAc;AACpD,QAAM,kBAAkB,QAAQ,UAAU,cAAc;AACxD,SAAO,KAAK,KAAK,gBAAgB,eAAe;AACpD;AAIO,SAAS,eAAe,MAAM,MAAM;AACvC,MAAI,KAAK,WAAW,KAAK;AACrB,WAAO;AACX,WAAS,QAAQ,GAAG,QAAQ,KAAK,QAAQ,SAAS;AAC9C,QAAI,KAAK,KAAK,MAAM,KAAK,KAAK;AAC1B,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAIA,SAAS,QAAQ,OAAO,UAAU;AAC9B,SAAO,OAAO,WAAW,MAAM,QAAQ,QAAQ,CAAC;AACpD;;;ACnCO,IAAM,YAAY,OAAO,aAAa;AACtC,SAAS,cAAcA,UAAS;AACnC,SAAOA,oBAAmB;AAC9B;AACO,SAAS,UAAU,OAAO;AAC7B,SAAO,MAAM,SAAS;AAC1B;AACO,SAAS,aAAa,OAAO;AAChC,SAAO,MAAM,KAAK,WAAW,OAAO;AACxC;AACO,SAAS,aAAa,OAAO;AAChC,SAAO,MAAM,KAAK,WAAW,OAAO;AACxC;;;ACNO,SAAS,WAAW,EAAE,iBAAiB,sBAAsB,WAAW,YAAa,GAAG;AAC3F,QAAM,kBAAkB,qBAAqB,SAAS;AACtD,SAAO,mBAAmB,MAAM,sCAAsC;AACtE,QAAM,EAAE,gBAAgB,GAAG,aAAa,UAAU,KAAK,UAAU,EAAE,IAAI;AACvE,MAAI,UAAU;AACd,MAAI,4BAA4B,SAAS,OAAO,IAAI,GAAG;AACnD,cAAU,8BAA8B,SAAS,aAAa,eAAe,OAAO;AAAA,EACxF;AACA,YAAU,KAAK,IAAI,SAAS,OAAO;AACnC,SAAO,OAAO,WAAW,QAAQ,QAAQ,SAAS,CAAC;AACvD;AAOA,SAAS,8BAA8B,MAAM,aAAa,eAAe,SAAS;AAC9E,MAAI,CAAC;AACD,WAAO;AAEX,QAAM,gBAAgB,gBAAgB,WAAW;AACjD,SAAO,4BAA4B,MAAM,YAAY,IAAI,IAAI,gBAAgB;AACjF;;;ACxBO,SAASC,QAAO;AAAE;AAClB,SAAS,6BAA6B,EAAE,SAAS,QAAQ,WAAY,GAAG;AAC3E,QAAM,uBAAuB,gCAAgC,OAAO;AACpE,WAAS,QAAQ,GAAG,QAAQ,WAAW,SAAS,GAAG,SAAS;AACxD,UAAM,EAAE,UAAU,UAAU,SAAS,IAAI,oBAAoB;AAAA,MACzD;AAAA,MACA;AAAA,MACA,cAAc,CAAC,OAAO,QAAQ,CAAC;AAAA,IACnC,CAAC;AACD,UAAM,iBAAiB,qBAAqB,KAAK;AACjD,QAAI,cAAc,cAAc,GAAG;AAC/B,YAAM,WAAW,WAAW,KAAK;AACjC,qBAAe,aAAa,iBAAiB,SAAS,KAAK,GAAG,OAAO;AACrE,qBAAe,aAAa,iBAAiB,GAAG,KAAK,MAAM,QAAQ,CAAC,EAAE;AACtE,qBAAe,aAAa,iBAAiB,GAAG,KAAK,MAAM,QAAQ,CAAC,EAAE;AACtE,qBAAe,aAAa,iBAAiB,YAAY,OAAO,GAAG,KAAK,MAAM,QAAQ,CAAC,KAAK,EAAE;AAAA,IAClG;AAAA,EACJ;AACA,SAAO,MAAM;AACT,yBAAqB,QAAQ,CAAC,wBAAwB;AAClD,0BAAoB,gBAAgB,eAAe;AACnD,0BAAoB,gBAAgB,eAAe;AACnD,0BAAoB,gBAAgB,eAAe;AACnD,0BAAoB,gBAAgB,eAAe;AAAA,IACvD,CAAC;AAAA,EACL;AACJ;AACO,SAAS,gCAAgC,SAAS;AACrD,MAAI,CAAC;AACD,WAAO,CAAC;AACZ,SAAO,MAAM,KAAK,SAAS,iBAAiB,8CAA8C,OAAO,IAAI,CAAC;AAC1G;AACO,SAAS,4BAA4B,SAAS,IAAI;AACrD,MAAI,CAAC;AACD,WAAO;AACX,QAAM,UAAU,gCAAgC,OAAO;AACvD,QAAM,QAAQ,QAAQ,UAAU,CAAC,WAAW,OAAO,aAAa,sBAAsB,MAAM,EAAE;AAC9F,SAAO,SAAS;AACpB;AACO,SAAS,gBAAgB,SAAS,cAAc;AACnD,QAAM,QAAQ,4BAA4B,SAAS,YAAY;AAC/D,SAAO,SAAS,OAAO,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE;AACvD;AACO,SAAS,eAAe,YAAY,MAAM,QAAQ;AACrD,QAAM,uBAAuB,WAAW,IAAI,CAAC,aAAa,SAAS,WAAW;AAC9E,QAAM,YAAY,kBAAkB,YAAY,IAAI;AACpD,QAAM,kBAAkB,qBAAqB,SAAS;AACtD,QAAM,aAAa,cAAc,WAAW,SAAS;AACrD,QAAM,eAAe,aAAa,CAAC,YAAY,GAAG,SAAS,IAAI,CAAC,WAAW,YAAY,CAAC;AACxF,QAAM,WAAW,OAAO,SAAS;AACjC,SAAO;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AACO,SAAS,kBAAkB,YAAY,MAAM;AAChD,SAAO,WAAW,UAAU,CAAC,iBAAiB,aAAa,KAAK,GAAG,YAAY,KAAK,KAAK,GAAG,OAAO;AACvG;AAEO,SAAS,kBAAkB,YAAY,QAAQ,6BAA6B;AAC/E,WAAS,QAAQ,GAAG,QAAQ,OAAO,QAAQ,SAAS;AAChD,UAAM,OAAO,OAAO,KAAK;AACzB,UAAM,WAAW,WAAW,KAAK;AACjC,WAAO,QAAQ;AACf,UAAM,EAAE,gBAAgB,GAAG,YAAY,IAAI,SAAS;AACpD,UAAM,mBAAmB,4BAA4B,SAAS,KAAK,GAAG,OAAO;AAE7E,QAAI,EAAE,oBAAoB,QAAQ,SAAS;AACvC;AACJ,gCAA4B,SAAS,KAAK,GAAG,OAAO,IAAI;AACxD,UAAM,EAAE,YAAY,UAAU,SAAS,IAAI,SAAS;AACpD,yCAAW,MAAM;AACjB,QAAI,gBAAgB,cAAc,WAAW;AACzC,UAAI,aACC,oBAAoB,QAAQ,qBAAqB,kBAClD,SAAS,eAAe;AACxB,iBAAS;AAAA,MACb;AACA,UAAI,eACC,oBAAoB,QAAQ,qBAAqB,kBAClD,SAAS,eAAe;AACxB,mBAAW;AAAA,MACf;AAAA,IACJ;AAAA,EACJ;AACJ;AACO,SAAS,uBAAuB,EAAE,WAAW,GAAG;AACnD,QAAM,SAAS,MAAM,WAAW,MAAM;AACtC,QAAM,uBAAuB,WAAW,IAAI,CAAC,aAAa,SAAS,WAAW;AAC9E,MAAI,oBAAoB;AACxB,MAAI,gBAAgB;AAEpB,WAAS,QAAQ,GAAG,QAAQ,WAAW,QAAQ,SAAS;AACpD,UAAM,kBAAkB,qBAAqB,KAAK;AAClD,WAAO,eAAe;AACtB,UAAM,EAAE,YAAY,IAAI;AACxB,QAAI,eAAe,MAAM;AACrB;AACA,aAAO,KAAK,IAAI;AAChB,uBAAiB;AAAA,IACrB;AAAA,EACJ;AAEA,WAAS,QAAQ,GAAG,QAAQ,WAAW,QAAQ,SAAS;AACpD,UAAM,kBAAkB,qBAAqB,KAAK;AAClD,WAAO,eAAe;AACtB,UAAM,EAAE,YAAY,IAAI;AACxB,QAAI,eAAe,MAAM;AACrB;AAAA,IACJ;AACA,UAAM,oBAAoB,WAAW,SAAS;AAC9C,UAAM,OAAO,gBAAgB;AAC7B;AACA,WAAO,KAAK,IAAI;AAChB,qBAAiB;AAAA,EACrB;AACA,SAAO;AACX;AAEO,SAAS,wBAAwB,EAAE,QAAQ,YAAY,gBAAiB,GAAG;AAC9E,QAAM,aAAa,CAAC,GAAG,UAAU;AACjC,QAAM,sBAAsB,WAAW,OAAO,CAAC,aAAa,YAAY,cAAc,SAAS,CAAC;AAEhG,MAAI,WAAW,WAAW,gBAAgB,QAAQ;AAC9C,UAAM,IAAI,MAAM,WAAW,gBAAgB,MAAM,iBAAiB,WAC7D,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EACxB,KAAK,IAAI,CAAC,EAAE;AAAA,EACrB,WACS,CAAC,sBAAsB,qBAAqB,GAAG,GAAG;AACvD,aAAS,QAAQ,GAAG,QAAQ,gBAAgB,QAAQ,SAAS;AACzD,YAAM,aAAa,WAAW,KAAK;AACnC,aAAO,cAAc,IAAI;AACzB,YAAM,WAAY,MAAM,sBAAuB;AAC/C,iBAAW,KAAK,IAAI;AAAA,IACxB;AAAA,EACJ;AACA,MAAI,gBAAgB;AAEpB,WAAS,QAAQ,GAAG,QAAQ,gBAAgB,QAAQ,SAAS;AACzD,UAAM,aAAa,WAAW,KAAK;AACnC,WAAO,cAAc,IAAI;AACzB,UAAM,WAAW,WAAW;AAAA,MACxB;AAAA,MACA,WAAW;AAAA,MACX,aAAa;AAAA,IACjB,CAAC;AACD,QAAI,eAAe,UAAU;AACzB,uBAAiB,aAAa;AAC9B,iBAAW,KAAK,IAAI;AAAA,IACxB;AAAA,EACJ;AAGA,MAAI,CAAC,sBAAsB,eAAe,CAAC,GAAG;AAC1C,aAAS,QAAQ,GAAG,QAAQ,gBAAgB,QAAQ,SAAS;AACzD,YAAM,WAAW,WAAW,KAAK;AACjC,aAAO,YAAY,IAAI;AACvB,YAAM,aAAa,WAAW;AAC9B,YAAM,WAAW,WAAW;AAAA,QACxB;AAAA,QACA,WAAW;AAAA,QACX,aAAa;AAAA,MACjB,CAAC;AACD,UAAI,aAAa,UAAU;AACvB,yBAAiB,WAAW;AAC5B,mBAAW,KAAK,IAAI;AAEpB,YAAI,sBAAsB,eAAe,CAAC,GAAG;AACzC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,oBAAoB,IAAI;AACpC,MAAI,CAAC;AACD,WAAO;AACX,QAAMC,WAAU,SAAS,cAAc,yCAAyC,EAAE,IAAI;AACtF,MAAIA,UAAS;AACT,WAAOA;AAAA,EACX;AACA,SAAO;AACX;AACO,SAAS,uBAAuB,IAAI;AACvC,MAAI,CAAC;AACD,WAAO;AACX,QAAMA,WAAU,SAAS,cAAc,0BAA0B,EAAE,IAAI;AACvE,MAAIA,UAAS;AACT,WAAOA;AAAA,EACX;AACA,SAAO;AACX;AACO,SAAS,wBAAwB,GAAG,cAAc,KAAK,kBAAkB;AAC5E,QAAM,eAAe,QAAQ;AAC7B,QAAM,gBAAgB,uBAAuB,YAAY;AACzD,SAAO,aAAa;AACpB,QAAM,UAAU,cAAc,aAAa,oBAAoB;AAC/D,SAAO,OAAO;AACd,QAAM,EAAE,sBAAsB,IAAI;AAClC,QAAM,iBAAiB,6BAA6B,KAAK,CAAC;AAC1D,QAAM,eAAe,oBAAoB,OAAO;AAChD,SAAO,YAAY;AACnB,QAAM,YAAY,aAAa,sBAAsB;AACrD,QAAM,oBAAoB,eAAe,UAAU,QAAQ,UAAU;AACrE,QAAM,eAAe,iBAAiB;AACtC,QAAM,mBAAoB,eAAe,oBAAqB;AAC9D,SAAO;AACX;AAEO,SAAS,mBAAmB,GAAG,cAAc,KAAK,kBAAkB,kBAAkB;AACzF,MAAI,UAAU,CAAC,GAAG;AACd,UAAM,eAAe,QAAQ;AAC7B,QAAI,QAAQ;AACZ,QAAI,EAAE,UAAU;AACZ,cAAQ;AAAA,IACZ,WACS,oBAAoB,MAAM;AAC/B,cAAQ;AAAA,IACZ,OACK;AACD,cAAQ;AAAA,IACZ;AACA,QAAI,WAAW;AACf,YAAQ,EAAE,KAAK;AAAA,MACX,KAAK;AACD,mBAAW,eAAe,IAAI;AAC9B;AAAA,MACJ,KAAK;AACD,mBAAW,eAAe,CAAC,QAAQ;AACnC;AAAA,MACJ,KAAK;AACD,mBAAW,eAAe,QAAQ;AAClC;AAAA,MACJ,KAAK;AACD,mBAAW,eAAe,IAAI,CAAC;AAC/B;AAAA,MACJ,KAAK;AACD,mBAAW;AACX;AAAA,MACJ,KAAK;AACD,mBAAW;AACX;AAAA,IACR;AACA,WAAO;AAAA,EACX,OACK;AACD,QAAI,oBAAoB;AACpB,aAAO;AACX,WAAO,wBAAwB,GAAG,cAAc,KAAK,gBAAgB;AAAA,EACzE;AACJ;AACO,SAAS,6BAA6B,KAAK,GAAG;AACjD,QAAM,eAAe,QAAQ;AAC7B,MAAI,aAAa,CAAC,GAAG;AACjB,WAAO,eAAe,EAAE,UAAU,EAAE;AAAA,EACxC,WACS,aAAa,CAAC,GAAG;AACtB,UAAM,aAAa,EAAE,QAAQ,CAAC;AAC9B,WAAO,UAAU;AACjB,WAAO,eAAe,WAAW,UAAU,WAAW;AAAA,EAC1D,OACK;AACD,UAAM,IAAI,MAAM,2BAA2B,EAAE,IAAI,GAAG;AAAA,EACxD;AACJ;AACO,SAAS,uBAAuB,SAAS,UAAU,YAAY;AAhRtE;AAiRI,QAAM,SAAS,uBAAuB,QAAQ;AAC9C,QAAM,UAAU,gCAAgC,OAAO;AACvD,QAAM,QAAQ,SAAS,QAAQ,QAAQ,MAAM,IAAI;AACjD,QAAM,aAAW,gBAAW,KAAK,MAAhB,mBAAmB,KAAK,GAAG,YAAW;AACvD,QAAM,YAAU,gBAAW,QAAQ,CAAC,MAApB,mBAAuB,KAAK,GAAG,YAAW;AAC1D,SAAO,CAAC,UAAU,OAAO;AAC7B;;;ACvRA,IAAI,QAAQ;AAIL,SAAS,MAAM,SAAS,aAAa;AACxC;AACA,SAAO,GAAG,MAAM,IAAI,KAAK;AAC7B;;;ACEO,SAAS,oBAAoB,EAAE,OAAO,QAAQ,YAAY,iBAAiB,sBAAsB,cAAc,QAAS,GAAG;AAC9H,MAAI,sBAAsB,OAAO,CAAC;AAC9B,WAAO;AACX,QAAM,aAAa,CAAC,GAAG,UAAU;AACjC,QAAM,CAAC,iBAAiB,gBAAgB,IAAI;AAC5C,MAAI,eAAe;AAQnB;AAGI,QAAI,YAAY,YAAY;AACxB;AAEI,cAAM,QAAQ,QAAQ,IAAI,mBAAmB;AAC7C,cAAM,kBAAkB,qBAAqB,KAAK;AAClD,eAAO,eAAe;AACtB,YAAI,gBAAgB,aAAa;AAC7B,gBAAM,WAAW,WAAW,KAAK;AACjC,iBAAO,YAAY,IAAI;AACvB,gBAAMC,mBAAkB,qBAAqB,KAAK;AAClD,iBAAOA,gBAAe;AACtB,gBAAM,EAAE,gBAAgB,GAAG,UAAU,EAAE,IAAIA;AAC3C,cAAI,sBAAsB,UAAU,aAAa,GAAG;AAChD,kBAAM,aAAa,UAAU;AAE7B,gBAAI,4BAA4B,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG;AAC9D,sBAAQ,QAAQ,IAAI,IAAI,aAAa;AAAA,YAEzC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA;AAEI,cAAM,QAAQ,QAAQ,IAAI,kBAAkB;AAC5C,cAAM,kBAAkB,qBAAqB,KAAK;AAClD,eAAO,eAAe;AACtB,cAAM,EAAE,YAAY,IAAI;AACxB,YAAI,aAAa;AACb,gBAAM,WAAW,WAAW,KAAK;AACjC,iBAAO,YAAY,IAAI;AACvB,gBAAMA,mBAAkB,qBAAqB,KAAK;AAClD,iBAAOA,gBAAe;AACtB,gBAAM,EAAE,gBAAgB,GAAG,UAAU,EAAE,IAAIA;AAC3C,cAAI,sBAAsB,UAAU,OAAO,GAAG;AAC1C,kBAAM,aAAa,WAAW;AAC9B,gBAAI,4BAA4B,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,GAAG;AAC9D,sBAAQ,QAAQ,IAAI,IAAI,aAAa;AAAA,YACzC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA;AAMI,UAAM,YAAY,QAAQ,IAAI,IAAI;AAClC,QAAI,QAAQ,QAAQ,IAAI,mBAAmB;AAC3C,QAAI,oBAAoB;AACxB,WAAO,MAAM;AACT,YAAM,WAAW,WAAW,KAAK;AACjC,aAAO,YAAY,IAAI;AACvB,YAAM,cAAc,WAAW;AAAA,QAC3B,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,aAAa;AAAA,MACjB,CAAC;AACD,YAAMC,SAAQ,cAAc;AAC5B,2BAAqBA;AACrB,eAAS;AACT,UAAI,QAAQ,KAAK,SAAS,qBAAqB,QAAQ;AACnD;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,cAAc,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,iBAAiB,CAAC;AACzE,YAAQ,QAAQ,IAAI,IAAI,cAAc;AAAA,EAC1C;AACA;AAEI,UAAM,aAAa,QAAQ,IAAI,kBAAkB;AACjD,QAAI,QAAQ;AACZ,WAAO,SAAS,KAAK,QAAQ,qBAAqB,QAAQ;AACtD,YAAM,iBAAiB,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,YAAY;AAC9D,YAAM,WAAW,WAAW,KAAK;AACjC,aAAO,YAAY,IAAI;AACvB,YAAM,aAAa,WAAW;AAC9B,YAAM,WAAW,WAAW;AAAA,QACxB,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,aAAa;AAAA,MACjB,CAAC;AACD,UAAI,CAAC,sBAAsB,UAAU,QAAQ,GAAG;AAC5C,wBAAgB,WAAW;AAC3B,mBAAW,KAAK,IAAI;AACpB,YAAI,aACC,YAAY,CAAC,EACb,cAAc,KAAK,IAAI,KAAK,EAAE,YAAY,CAAC,GAAG,QAAW;AAAA,UAC1D,SAAS;AAAA,QACb,CAAC,KAAK,GAAG;AACL;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,QAAQ,GAAG;AACX;AAAA,MACJ,OACK;AACD;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAGA,MAAI,sBAAsB,cAAc,CAAC,GAAG;AACxC,WAAO;AAAA,EACX;AACA;AAEI,UAAM,aAAa,QAAQ,IAAI,mBAAmB;AAClD,UAAM,WAAW,WAAW,UAAU;AACtC,WAAO,YAAY,IAAI;AACvB,UAAM,aAAa,WAAW;AAC9B,UAAM,WAAW,WAAW;AAAA,MACxB,iBAAiB;AAAA,MACjB,WAAW;AAAA,MACX,aAAa;AAAA,IACjB,CAAC;AAED,eAAW,UAAU,IAAI;AAEzB,QAAI,CAAC,sBAAsB,UAAU,UAAU,GAAG;AAC9C,UAAI,iBAAiB,aAAa;AAClC,YAAMC,cAAa,QAAQ,IAAI,mBAAmB;AAClD,UAAI,QAAQA;AACZ,aAAO,SAAS,KAAK,QAAQ,qBAAqB,QAAQ;AACtD,cAAMC,YAAW,WAAW,KAAK;AACjC,eAAOA,aAAY,IAAI;AACvB,cAAMC,cAAaD,YAAW;AAC9B,cAAME,YAAW,WAAW;AAAA,UACxB,iBAAiB;AAAA,UACjB,WAAW;AAAA,UACX,aAAaD;AAAA,QACjB,CAAC;AACD,YAAI,CAAC,sBAAsBD,WAAUE,SAAQ,GAAG;AAC5C,4BAAkBA,YAAWF;AAC7B,qBAAW,KAAK,IAAIE;AAAA,QACxB;AACA,YAAI,sBAAsB,gBAAgB,CAAC;AACvC;AACJ,gBAAQ,IAAI,UAAU;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,YAAY,WAAW,OAAO,CAAC,OAAO,SAAS,OAAO,OAAO,CAAC;AACpE,MAAI,CAAC,sBAAsB,WAAW,GAAG;AACrC,WAAO;AACX,SAAO;AACX;;;AC9KA,IAAI,eAAe;AAEnB,IAAI,UAAU;AAIP,SAAS,eAAeC,QAAO;AAClC,UAAQA,QAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,EACf;AACJ;AAIO,SAAS,yBAAyB;AACrC,MAAI,YAAY;AACZ;AACJ,WAAS,KAAK,YAAY,OAAO;AACjC,iBAAe;AACf,YAAU;AACd;AAIO,SAAS,qBAAqBA,QAAO;AACxC,MAAI,iBAAiBA;AACjB;AACJ,iBAAeA;AACf,QAAM,QAAQ,eAAeA,MAAK;AAClC,MAAI,YAAY,MAAM;AAClB,cAAU,SAAS,cAAc,OAAO;AACxC,aAAS,KAAK,YAAY,OAAO;AAAA,EACrC;AACA,UAAQ,YAAY,aAAa,KAAK;AAC1C;AAIO,SAAS,wBAAwB,EAAE,aAAa,WAAW,QAAQ,YAAY,WAAW,YAAY,EAAG,GAAG;AAC/G,QAAM,OAAO,OAAO,SAAS;AAC7B,MAAI;AACJ,MAAI,QAAQ,MAAM;AAGd,eAAW,eAAe;AAAA,EAC9B,WACS,WAAW,WAAW,GAAG;AAE9B,eAAW;AAAA,EACf,OACK;AACD,eAAW,KAAK,YAAY,SAAS;AAAA,EACzC;AACA,SAAO;AAAA,IACH,WAAW;AAAA,IACX;AAAA,IACA,YAAY;AAAA;AAAA,IAEZ,UAAU;AAAA;AAAA;AAAA,IAGV,eAAe,cAAc,OAAO,SAAS;AAAA,EACjD;AACJ;;;ACtEO,SAAS,kBAAkB,eAAe;AAC7C,MAAI;AACA,QAAI,OAAO,iBAAiB,aAAa;AACrC,YAAM,IAAI,UAAU,mDAAmD;AAAA,IAC3E;AACA,kBAAc,UAAU,CAAC,SAAS,aAAa,QAAQ,IAAI;AAC3D,kBAAc,UAAU,CAAC,MAAM,UAAU,aAAa,QAAQ,MAAM,KAAK;AAAA,EAC7E,SACO,KAAK;AACR,YAAQ,MAAM,GAAG;AACjB,kBAAc,UAAU,MAAM;AAC9B,kBAAc,UAAU,MAAM;AAAA,IAAE;AAAA,EACpC;AACJ;AAIA,SAAS,gBAAgB,YAAY;AACjC,SAAO,aAAa,UAAU;AAClC;AAKA,SAAS,WAAW,OAAO;AACvB,QAAM,gBAAgB,MACjB,IAAI,CAAC,SAAS;AACf,WAAO,KAAK,KAAK,MAAM,UACjB,GAAG,KAAK,KAAK,MAAM,OAAO,IAAI,KAAK,UAAU,KAAK,WAAW,CAAC,KAC9D,KAAK,UAAU,KAAK,WAAW;AAAA,EACzC,CAAC,EACI,KAAK,EACL,KAAK,GAAG;AACb,SAAO;AACX;AAKA,SAAS,6BAA6B,YAAY,SAAS;AACvD,MAAI;AACA,UAAM,eAAe,gBAAgB,UAAU;AAC/C,UAAM,aAAa,QAAQ,QAAQ,YAAY;AAC/C,UAAM,SAAS,KAAK,MAAM,cAAc,EAAE;AAC1C,QAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AAC/C,aAAO;AAAA,IACX;AAAA,EACJ,QACM;AAAA,EAEN;AACA,SAAO;AACX;AAKO,SAAS,mBAAmB,YAAY,YAAY,SAAS;AAChE,QAAMC,SAAQ,6BAA6B,YAAY,OAAO,KAAK,CAAC;AACpE,QAAM,UAAU,WAAW,UAAU;AACrC,SAAOA,OAAM,OAAO,KAAK;AAC7B;AAIO,SAAS,mBAAmB,YAAY,YAAY,yBAAyB,OAAO,SAAS;AAChG,QAAM,eAAe,gBAAgB,UAAU;AAC/C,QAAM,UAAU,WAAW,UAAU;AACrC,QAAMA,SAAQ,6BAA6B,YAAY,OAAO,KAAK,CAAC;AACpE,EAAAA,OAAM,OAAO,IAAI;AAAA,IACb,eAAe,OAAO,YAAY,wBAAwB,QAAQ,CAAC;AAAA,IACnE,QAAQ;AAAA,EACZ;AACA,MAAI;AACA,YAAQ,QAAQ,cAAc,KAAK,UAAUA,MAAK,CAAC;AAAA,EACvD,SACO,OAAO;AACV,YAAQ,MAAM,KAAK;AAAA,EACvB;AACJ;AACA,IAAM,cAAc,CAAC;AAKrB,SAAS,SAAS,UAAU,aAAa,IAAI;AACzC,MAAI,YAAY;AAEhB,QAAM,WAAW,IAAI,SAAS;AAC1B,QAAI,cAAc,MAAM;AACpB,mBAAa,SAAS;AAAA,IAC1B;AACA,gBAAY,WAAW,MAAM;AACzB,eAAS,GAAG,IAAI;AAAA,IACpB,GAAG,UAAU;AAAA,EACjB;AACA,SAAO;AACX;AAMO,SAAS,oBAAoB,EAAE,YAAY,QAAQ,SAAS,YAAY,uBAAwB,GAAG;AAGtG,MAAI,OAAO,WAAW,KAAK,OAAO,WAAW,WAAW;AACpD;AACJ,MAAI,gBAAgB,YAAY,UAAU;AAE1C,MAAI,iBAAiB,MAAM;AACvB,oBAAgB,SAAS,oBAAoB,+BAA+B;AAC5E,gBAAY,UAAU,IAAI;AAAA,EAC9B;AAGA,QAAM,mBAAmB,CAAC,GAAG,UAAU;AACvC,QAAM,gCAAgC,IAAI,IAAI,sBAAsB;AACpE,gBAAc,YAAY,kBAAkB,+BAA+B,QAAQ,OAAO;AAC9F;;;ICjHa,iBAAc;EACvB,SAAO,CAAG,SAAS;AACf,sBAAkB,cAAc;WACzB,eAAe,QAAQ,IAAI;EACtC;EACA,SAAO,CAAG,MAAM,UAAU;AACtB,sBAAkB,cAAc;AAChC,mBAAe,QAAQ,MAAM,KAAK;EACtC;;;IAEE,uBAAe;EASjB,YAAY,MAAM;AARlB;yCACuB,IAAI;;;iDAGA,KAAK;;AAEhC,qDAAyB,oBAAO,IAAG;yCAChB,CAAC;AA+DpB,qCAAS,CAAI,cAAc;AACvB,WAAK,SAAS;IAClB;AACA,gDAAoB,CAAI,iBAAiB;cAC7B,MAAM;;AACV,UAAE,eAAc;cACV,YAAY,KAAK,KAAK,UAAU;cAChC,YAAY,KAAK;cACjB,UAAU,KAAK,KAAK,GAAG;cACvB,mBAAmB,KAAK,KAAK,iBAAiB;cAC9C,aAAa,KAAK;cAClB,gBAAgB,KAAK;gBACnB,cAAa,IAAK,aAAS,CAAA;cAC7B,eAAe,gBAAgB,SAAS,YAAY;YACtD,QAAQ,mBAAmB,GAAG,cAAc,WAAW,WAAW,gBAAgB;0BAClF,OAAU,CAAC,EAAA;cAGT,eAAY,cAAG,WAAc,YAAY;0BAC3C,SAAS,KAAQ,KAAK,KAAI,cAAc;AACxC,kBAAK,CAAI;QACb;cACM,kBAAkB,cAAc,IAAG,CAAE,aAAa,SAAS,WAAW;cACtE,aAAa,oBAAmB;UAClC;UACA,QAAQ,iBAAiB;UACzB;UACA;UACA,SAAS,UAAU,CAAC,IAAI,aAAa;;cAEnC,gBAAa,CAAI,eAAe,YAAY,UAAU;YAGxD,aAAa,CAAC,KAAK,aAAa,CAAC,GAAG;gBAI9B,YAAY,KAAK;4BACnB,WAAc,OAAK,KAAA,GAAE;AACrB,iBAAK,YAAY;iBACZ,eAAe;kBAIZ,cAAc;AACd,qCAAqB,QAAQ,IAAI,mBAAmB,gBAAgB;cACxE,OACK;AACD,qCAAqB,QAAQ,IAAI,iBAAiB,cAAc;cACpE;YACJ,OACK;AACD,mCAAqB,eAAe,eAAe,UAAU;YACjE;UACJ;QACJ;YACI,eAAe;AACf,eAAK,UAAU,UAAU;AACzB,2BAAK,KAAK,UAAS,YAAnB,4BAA6B;AAC7B,4BAAkB,eAAe,YAAY,KAAK,2BAA2B;QACjF;MACJ;IACJ;AACA,sCAAU,CAAI,WAAW,mBAAmB;;YAClC,aAAa,KAAK;YAClB,aAAa,KAAK;YAClB,qBAAqB,WAAW,IAAG,CAAE,aAAa,SAAS,WAAW;cACpE,UAAU,aAAY,IAAK,eAAe,YAAY,WAAW,UAAU;AACnF,aAAM,OAAC,UAAY,MAAI,KAAA,CAAA;YACjB,aAAU,cAAG,kBAAkB,YAAY,SAAS,GAAM,WAAW,SAAS,CAAC;YAC/E,QAAQ,aAAa,WAAW,iBAAiB,iBAAiB;YAClE,aAAa,oBAAmB;QAClC;QACA,QAAQ;QACR,iBAAiB;QACjB;QACA,SAAS;;UAET,eAAe,YAAY,UAAU,EAAA;AAEzC,WAAK,UAAU,UAAU;AACzB,uBAAK,KAAK,UAAS,YAAnB,4BAA6B;AAC7B,wBAAkB,YAAY,YAAY,KAAK,2BAA2B;IAC9E;AACA,yCAAa,CAAI,cAAc,MAAM;YAC3B,YAAY,KAAK,KAAK,UAAU;YAChC,SAAS,KAAK;YACd,gBAAgB,uBAAuB,YAAY;AACzD,aAAO,aAAa;YACd,wBAAwB,6BAA6B,WAAW,CAAC;AACvE,WAAK,YAAS;QACV;QACA,gBAAgB,cAAc,sBAAqB;QACnD;QACA,eAAe;;IAEvB;AACA,wCAAY,MAAS;AACjB,6BAAsB;AACtB,WAAK,YAAY;IACrB;AACA,2CAAe,CAAI,SAAS;YAClB,gBAAgB,KAAK;YACrB,SAAS,KAAK;cACZ,gBAAgB,GAAG,aAAa,SAAQ,IAAM,eAAe,eAAe,MAAM,MAAM;2BACzF,aAAgB,IAAI,KAAA,cAAI,UAAa,aAAa;IAC7D;AACA,sCAAU,CAAI,SAAS;;YACb,aAAa,KAAK;YAClB,gBAAgB,KAAK;WACtB,KAAK,YAAY,YAAW;YAE3B,uBAAuB,cAAc,IAAG,CAAE,aAAa,SAAS,WAAW;;QACzE,gBAAgB;QAAG;QAAU,UAAU;QAAG;UAAkB,eAAe,eAAe,MAAM,UAAU;wBAC9G,UAAa,eAAa,KAAA,EAAA;YAGxB,eAAe,KAAK,0BAA0B,IAAI,KAAK,KAAK,GAAG,OAAO;YACtE,WAAQ,OAAG,cAAgB,MAAI,KAAA,KAAI,gBAAgB,UAAU,eAAe;YAC5E,aAAU,cAAG,kBAAkB,eAAe,IAAI,GAAM,cAAc,SAAS,CAAC;YAChF,QAAQ,aAAa,WAAW,WAAW,WAAW;YACtD,aAAa,oBAAmB;QAClC;QACA,QAAQ;QACR,iBAAiB;QACjB;QACA,SAAS;;UAET,eAAe,YAAY,UAAU,EAAA;AAEzC,WAAK,UAAU,UAAU;AACzB,uBAAK,KAAK,UAAS,YAAnB,4BAA6B;AAC7B,wBAAkB,eAAe,YAAY,KAAK,2BAA2B;IACjF;AACA,wCAAY,CAAI,SAAS;;YACf,aAAa,KAAK;YAClB,gBAAgB,KAAK;WACtB,KAAK,YAAY,YAAW;YAE3B,uBAAuB,cAAc,IAAG,CAAE,aAAa,SAAS,WAAW;cACzE,gBAAgB,GAAG,UAAU,aAAY,IAAM,eAAe,eAAe,MAAM,UAAU;AACrG,aAAM,OAAC,UAAY,MAAI,KAAA,CAAA;wBACnB,UAAa,aAAa,EAAA;AAG9B,WAAK,0BAA0B,IAAI,KAAK,KAAK,GAAG,SAAS,QAAQ;YAC3D,aAAU,cAAG,kBAAkB,eAAe,IAAI,GAAM,cAAc,SAAS,CAAC;YAChF,QAAQ,aAAa,WAAW,gBAAgB,gBAAgB;YAChE,aAAa,oBAAmB;QAClC;QACA,QAAQ;QACR,iBAAiB;QACjB;QACA,SAAS;;UAET,eAAe,YAAY,UAAU,EAAA;AAEzC,WAAK,SAAS;AACd,uBAAK,KAAK,UAAS,YAAnB,4BAA6B;AAC7B,wBAAkB,eAAe,YAAY,KAAK,2BAA2B;IACjF;AACA,uCAAW,CAAI,SAAS;aACb,eAAe,KAAK,YAAY,MAAM,KAAK,MAAM,EAAE;IAC9D;AACA,wCAAY,CAAI,MAAM,gBAAgB;YAC5B,gBAAgB,KAAK;YACrB,SAAS,KAAK;YACd,YAAY,KAAK;YACjB,YAAY,kBAAkB,eAAe,IAAI;aAChD,wBAAuB;QAC1B;QACA;QACA;QACA,YAAY;QACZ;;IAER;AACA,0CAAc,CAAI,SAAS;cACf,gBAAgB,GAAG,aAAa,SAAQ,IAAM,eAAe,KAAK,YAAY,MAAM,KAAK,MAAM;cAC/F,eAAe,WAAW;IACtC;AACA,wCAAY,CAAI,SAAS;YACf,mBAAgB,CAAA,GAAO,KAAK,YAAY,IAAI;AAClD,uBAAiB,KAAI,CAAE,OAAO,UAAU;cAC9B,SAAS,MAAM,KAAK,MAAM;cAC1B,SAAS,MAAM,KAAK,MAAM;mBAC5B,QAAU,IAAI,KAAA,OAAI,QAAU,IAAI,GAAE;iBAC3B;QACX,WAAC,OACQ,QAAU,IAAI,GAAE;;QAEzB,WAAC,OACQ,QAAU,IAAI,GAAE;iBACd;QACX,OACK;iBACM,SAAS;QACpB;MACJ,CAAC;AACD,WAAK,aAAa;AAClB,WAAK,oBAAoB;mBACZ;cACH,gBAAa,CAAA,GAAO,KAAK,UAAU;cACnC,QAAQ,kBAAkB,KAAK,YAAY,IAAI;YACjD,QAAQ,EAAC;AAEb,sBAAc,OAAO,OAAO,CAAC;AAC7B,aAAK,aAAa;eACX,KAAK,4BAA4B,KAAK,KAAK,GAAG,OAAO;AAC5D,aAAK,oBAAoB;MAC7B;IACJ;wDAC+B,MAAS;YAC9B,UAAU,KAAK,KAAK,GAAG;YACvB,UAAU,gCAAgC,OAAO;YACjD,gBAAgB,KAAK;YACrB,gBAAgB,QAAQ,IAAG,CAAE,WAAW;cACpC,WAAW,OAAO,aAAa,sBAAsB;aACtD,SAAQ,QACFC;eACJ,UAAU,OAAO,IAAI,uBAAuB,SAAS,UAAU,aAAa;mBAC/E,UAAY,IAAI,KAAA,OAAI,SAAW,IAAI,EAAA,QAC5BA;cACL,YAAS,CAAI,MAAM;cACjB,EAAE,oBAAgB,cAAI,EAAE,KAAQ,SAAO,KAAA,EAAA;AAE3C,YAAE,eAAc;gBACVC,iBAAgB,KAAK;gBACrB,QAAQA,eAAc,UAAS,CAAEC,cAAQ,cAAKA,UAAS,KAAK,GAAG,SAAY,QAAQ,CAAA;cACrF,QAAQ,EAAC;gBAEP,WAAWD,eAAc,KAAK;AACpC,iBAAO,QAAQ;gBACT,SAAS,KAAK;gBACd,OAAO,OAAO,KAAK;kBACjB,gBAAgB,GAAG,aAAa,UAAU,EAAC,IAAK,SAAS;uBAC3D,MAAQ,MAAI,KAAA,KAAI,aAAW;gBAE3B,aAAa,oBAAmB;YAClC,OAAO,sBAAsB,MAAM,aAAa,IAC1C,UAAU,OACV,gBAAgB;YACtB;YACA,iBAAiBA,eAAc,IAAG,CAAEC,cAAaA,UAAS,WAAW;YACrE,cAAc,gBAAgB,SAAS,QAAQ;YAC/C,SAAS;;4BAET,QAAW,YAAU,KAAA,GAAE;AACvB,iBAAK,SAAS;UAClB;QACJ;cACM,gBAAgB,iBAAiB,QAAQ,WAAW,SAAS;qBACtD;AACT,wBAAa;QACjB;MACJ,CAAC;mBACY;mBACE,SAAS,eAAe;AAC/B,gBAAK;QACT;MACJ;IACJ;;MAEI,IAAI,KAAK,KAAK,GAAG;MACjB,mBAAmB;MACnB,kBAAkB,KAAK,KAAK,UAAU;MACtC,sBAAsB,KAAK,KAAK,GAAG;MACnC,OAAK;QACD,SAAS;QACT,eAAa,cAAE,KAAK,KAAK,UAAU,SAAY,YAAY,IAAG,QAAQ;QACtE,QAAQ;QACR,UAAU;QACV,OAAO;;;AA7UX,SAAK,OAAO;AACZ,eAAW,IAAI;AACf;;cAAa,KAAK,KAAK,GAAG;cAAe,KAAK;cAAc,KAAK;;YAAmB;eACzE,6BAA4B;UAC/B,SAAS,KAAK,KAAK,GAAG;UACtB,QAAQ,KAAK;UACb,YAAY,KAAK;;MAEzB;;AACA,IAAA,YAAO,MAAO;aACH,QAAO,MAAO;eACV,mBAAI,iCAAJ;MACX,CAAC;IACL,CAAC;AACD;;cACU,KAAK,KAAK,WAAW;cACrB,KAAK;cACL,KAAK,KAAK,QAAQ;;YACnB;aACA,KAAK,KAAK,WAAW,QAAO;AAEjC,4BAAmB;UACf,YAAY,KAAK,KAAK,WAAW;UACjC,QAAQ,KAAK;UACb,SAAS,KAAK,KAAK,QAAQ;UAC3B,YAAY,KAAK;UACjB,wBAAwB,KAAK;;MAErC;;AACA,UAAK,MAAO,KAAK,mBAAiB,MAAQ;;WACjC,KAAK,kBAAiB;AAE3B,WAAK,oBAAoB;YAGnB,aAAa,KAAK;UAEpB,eAAe;UACf,KAAK,KAAK,WAAW,SAAS;cACxBC,SAAQ,mBAAmB,KAAK,KAAK,WAAW,SAAS,KAAK,YAAY,KAAK,KAAK,QAAQ,OAAO;YACrGA,QAAO;AACP,eAAK,4BAAyB,IAAO,IAAI,OAAO,QAAQA,OAAM,aAAa,CAAA;AAC3E,yBAAeA,OAAM;QACzB;MACJ;iBACI,cAAgB,IAAI,GAAE;AACtB,uBAAe,uBAAsB,EACjC,YAAY,KAAK,WAAU,CAAA;MAEnC;YACM,aAAa,wBAAuB;QACtC,QAAQ;QACR,iBAAiB,KAAK,WAAW,IAAG,CAAE,aAAa,SAAS,WAAW;;UAEvE,eAAe,YAAY,UAAU,EAAA;AAEzC,WAAK,SAAS;AACd,uBAAK,KAAK,UAAS,YAAnB,4BAA6B;AAC7B,wBAAkB,KAAK,YAAY,YAAY,KAAK,2BAA2B;IACnF,CAAC;EACL;MApEA,YAAS;;;MAAT,UAAS,OAAA;;;MACT,SAAM;;;MAAN,OAAM,OAAA;;;MACN,aAAU;;;MAAV,WAAU,OAAA;;;MACV,oBAAiB;;;MAAjB,kBAAiB,OAAA;;;MACjB,8BAA2B;;;MAA3B,4BAA2B,OAAA;;;MAE3B,YAAS;;;MAAT,UAAS,OAAA;;;MAqUT,QAAK;;;MAAL,MAAK,OAAA;;;AAaT;;;;;;;;;IACM,aAAU;EAAI;EAAa;EAAa;EAAc;EAAW;EAAO;;;IACxE,yBAAiB;EAMnB,YAAY,MAAM,OAAO;AALzB;AACA;oCACW,aAAA,MAAA;;AAAA,aAAA,eAAqB,UAAK,MAAM,cAAX,mBAAsB,cAAiB,KAAK,KAAK,GAAG,OAAO;KAAA;mCACjF,MAAU,KAAK;AACzB,yCAAgB;uCAoCF,CAAI,MAAM;AACpB,QAAE,eAAc;UACZ,KAAK,KAAK,SAAS,QAAO;AAE9B,WAAK,MAAM,cAAc,KAAK,KAAK,GAAG,SAAS,CAAC;AAChD,WAAK,KAAK,iBAAiB,QAAQ,IAAI;IAC3C;6CACoB,MAAS;YACnB,OAAO,KAAK,KAAK,IAAI;WACtB,KAAI;AAET,WAAK,KAAI;AACT,WAAK,MAAM,aAAY;AACvB,WAAK,KAAK,iBAAiB,QAAQ,KAAK;IAC5C;mCACU,CAAI,MAAM;UACZ,KAAK,KAAK,SAAS,WAAO,CAAK,KAAK,iBAAiB,EAAE,iBAAgB;UAEvE,WAAW,SAAS,EAAE,GAAG,GAAG;AAC5B,UAAE,eAAc;AAChB,aAAK,cAAc,CAAC;;MAExB;wBACI,EAAE,KAAQ,MAAI,KAAA,EAAA;AAElB,QAAE,eAAc;YACV,UAAU,gCAAgC,KAAK,MAAM,KAAK,GAAG,OAAO;YACpE,QAAQ,4BAA4B,KAAK,MAAM,KAAK,GAAG,SAAS,KAAK,KAAK,GAAG,OAAO;wBACtF,OAAU,IAAI,EAAA;UAEd,YAAY;UACZ,EAAE,UAAU;YAER,QAAQ,GAAG;AACX,sBAAY,QAAQ;QACxB,OACK;AACD,sBAAY,QAAQ,SAAS;QACjC;MACJ,OACK;YAEG,QAAQ,IAAI,QAAQ,QAAQ;AAC5B,sBAAY,QAAQ;QACxB,OACK;AACD,sBAAY;QAChB;MACJ;YACM,aAAa,QAAQ,SAAS;AACpC,iBAAW,MAAK;IACpB;gCACO,MAAS;UACZ,mBAAI,aAAc,KAAK;IAC3B;iCACQ,MAAS;UACb,mBAAI,aAAc,IAAI;IAC1B;qCACY,CAAI,MAAM;AAClB,yBAAI,gBAAJ,WAAoB;IACxB;mCACU,MAAS;AACf,yBAAI,sBAAJ;IACJ;uCACc,MAAS;AACnB,yBAAI,sBAAJ;IACJ;oCACW,MAAS;AAChB,yBAAI,sBAAJ;IACJ;sCACa,CAAI,MAAM;AACnB,yBAAI,gBAAJ,WAAoB;IACxB;;MAEI,IAAI,KAAK,KAAK,GAAG;MACjB,MAAM;MACN,kBAAkB,KAAK,MAAM,KAAK,UAAU;MAC5C,sBAAsB,KAAK,MAAM,KAAK,GAAG;MACzC,eAAa,IAAE,mBAAI,YAAY,IACzB,YAAS,IACT,mBAAI,WAAW,IACX,aACA;MACV,gBAAc,CAAG,KAAK,KAAK,SAAS;MACpC,wBAAwB,KAAK,KAAK,GAAG;MACrC,qBAAqB;MACrB,UAAU,KAAK,KAAK,SAAS;MAC7B,OAAK;QACD,QAAQ,eAAe,KAAK,MAAM,KAAK,UAAU,OAAO;QACxD,aAAa;QACb,YAAY;QACZ,uBAAuB;QACvB,yBAAyB;;MAE7B,WAAW,mBAAI;MACf,QAAQ,mBAAI;MACZ,SAAS,mBAAI;MACb,aAAa,mBAAI;MACjB,WAAW,mBAAI;MACf,eAAe,mBAAI;MACnB,YAAY,mBAAI;MAChB,cAAc,mBAAI;;AAvIlB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,eAAW,IAAI;AACf,IAAA,YAAO,MAAO;UACN,KAAK,KAAK,SAAS,SAAS;AAC5B,aAAK,gBAAgB;MACzB,OACK;AACD,aAAK,gBAAgB,KAAK,MAAM,qBAAqB,KAAK,KAAK,GAAG,OAAO;MAC7E;IACJ,CAAC;AACD,IAAA,YAAO,MAAO;YACJ,OAAO,KAAK,KAAK,IAAI;WACtB,KAAI;YAEH,WAAW,KAAK,KAAK,SAAS;YAC9B,gBAAgB,KAAK;YACrB,aAAU,IAAG,mBAAI,YAAY;UAC/B,YAAQ,cAAI,eAAkB,IAAI,KAAA,CAAK,WAAU;YAE/C,SAAM,CAAI,MAAM;AAClB,sBAAc,CAAC;MACnB;YACM,eAAY,CAAI,MAAM;AACxB,sBAAc,CAAC;MACnB;YACM,sBAAmB,MAAS;AAC9B,aAAK,KAAI;AACT,aAAK,MAAM,aAAY;AACvB,aAAK,KAAK,iBAAiB,QAAQ,KAAK;MAC5C;aACO,iBAAiB,GAAG,SAAS,MAAM,eAAe,mBAAmB,GAAG,GAAG,SAAS,MAAM,aAAa,MAAM,GAAG,GAAG,SAAS,MAAM,aAAa,QAAM,EAAI,SAAS,MAAK,CAAA,GAAK,GAAG,SAAS,MAAM,cAAc,YAAY,GAAG,GAAG,QAAQ,WAAW,mBAAmB,GAAG,GAAG,QAAQ,YAAY,mBAAmB,CAAA;IAC5T,CAAC;EACL;MA0EA,QAAK;;;MAAL,MAAK,OAAA;;;AA8BT;;;;;;;;;;;;;;;IACa,kBAAU;EAyDnB,YAAY,MAAM,OAAO;AAxDzB;AACA;6CACoB,MAAU,EAAE;;MAE5B,YAAY,KAAK,KAAK,WAAW;MACjC,UAAU,KAAK,KAAK,SAAS;MAC7B,UAAU,KAAK,KAAK,SAAS;;;MAG7B,eAAe,KAAK,KAAK,cAAc;MACvC,aAAa,KAAK,KAAK,YAAY;MACnC,aAAa,KAAK,KAAK,YAAY;MACnC,SAAS,KAAK,KAAK,QAAQ;MAC3B,SAAS,KAAK,KAAK,QAAQ;;0CAEd,CAAIA,WAAU;UAC3B,mBAAI,uBAAwBA,QAAK,IAAA;AACjC,gBAAS,MAAO;YACR,KAAK,KAAK,IAAI,SAAS;gBACjBC,WAAU,KAAK,KAAK,IAAI;gBACxB,gBAAgB,iBAAiBA,QAAO;gBACxC,gBAAa,cAAG,cAAc,oBAAuB,MAAI,KAAA;eAC1D,eAAe;gBAChB,mBAAI,uBAAwB,EAAE;;UAElC;gBACM,sBAAmB,CAAI,UAAU;8BAE/B,MAAM,cAAiB,WAAW,GAAE;kBACpC,mBAAI,uBAAwB,EAAE;AAC9B,cAAAA,SAAQ,oBAAoB,iBAAiB,mBAAmB;YACpE;UACJ;AAEA,UAAAA,SAAQ,iBAAiB,iBAAiB,mBAAmB;QACjE,OACK;cACD,mBAAI,uBAAwB,EAAE;QAClC;MACJ,CAAC;IACL;AACA,gCAAI;MACA,UAAQ,MAAQ;AACZ,2BAAI,mBAAJ,WAAuB;AACvB,aAAK,MAAM,aAAa,IAAI;MAChC;MACA,QAAM,MAAQ;AACV,2BAAI,mBAAJ,WAAuB;AACvB,aAAK,MAAM,WAAW,IAAI;MAC9B;MACA,SAAO,MAAQ,KAAK,MAAM,YAAY,IAAI;MAC1C,aAAW,MAAQ,KAAK,MAAM,gBAAgB,IAAI;MAClD,YAAU,MAAQ,KAAK,MAAM,eAAe,IAAI;MAChD,QAAM,CAAG,SAAS,KAAK,MAAM,WAAW,MAAM,IAAI;MAClD,OAAK,MAAQ,KAAK,KAAK,GAAG;;qCAalB,aAAA,MAAqB,KAAK,MAAM,gBAAgB,IAAI,CAAA;mCACtD,aAAA,MAAA,cAAA,IAAqB,mBAAI,qBAAqB,GAAK,IAAE,KAAA,IAAA,IACzD,mBAAI,qBAAqB,IAAA,IACzB,mBAAI,aAAa,IACb,cACA,UAAU;;MAEhB,IAAI,KAAK,KAAK,GAAG;MACjB,OAAO,KAAK,MAAM,aAAa,MAAM,KAAK,KAAK,YAAY,OAAO;MAClE,aAAa;MACb,gBAAgB,KAAK,KAAK,GAAG;MAC7B,sBAAsB,KAAK,MAAM,KAAK,GAAG;MACzC,kBAAgB,IAAE,mBAAI,aAAa,IAAG,KAAK;MAC3C,iBAAe,IAAE,mBAAI,aAAa,IAAG,SAAY;MACjD,mBAAiB,IAAE,mBAAI,WAAW;;AAxBlC,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,eAAW,IAAI;AACf,YAAO,MAAO;aACH,KAAK,MAAM,aAAa,IAAI;IACvC,CAAC;AACD,UAAK,MAAA,SAAuB,KAAK,WAAW,GAAA,MAAS;AACjD,WAAK,MAAM,oBAAoB;IACnC,CAAC;EACL;MA/DA,YAAS;;;MAAT,UAAS,OAAA;;;MAKT,cAAW;;;MAAX,YAAW,OAAA;;;MAiEX,QAAK;;;MAAL,MAAK,OAAA;;;AAUT;;;;;;;;IACM,mBAAgB,IAAO,QAAQ,WAAW;SAChC,aAAa,OAAO;SACzB,iBAAiB,IAAG,IAAK,eAAe,KAAK,CAAA;AACxD;SACgB,eAAe,OAAO;aACvB,iBAAiB,OAAO,iBAAiB,IAAG,CAAA;AAC3D;SACgB,QAAQ,OAAO;aAChB,UAAU,OAAO,iBAAiB,IAAG,CAAA;AACpD;;;;;;;;MCxlBE,aAAU,KAAA,SAAA,cAAA,GAAG,IAAI,GAEjB,KAAE,KAAA,SAAA,MAAA,IAAG,KAAK,GACV,mBAAgB,KAAA,SAAA,oBAAA,GAAG,IAAI,GACvB,iBAAc,KAAA,SAAA,kBAAA,GAAGC,KAAI,GACrB,UAAO,KAAA,SAAA,WAAA,GAAG,cAAc,GACxB,MAAG,KAAA,SAAA,OAAA,IAAa,IAAI,GAGjB,YAAA;;;;;;;;;;;;;;;;;;QAGE,iBAAiB,aAAY;IAClC,IAAI,IAAI,KAAI,MAAO,GAAE,KAAI,MAAK,CAAA;IAC9B,KAAK,IAAI,KAAI,MACN,IAAG,GAAA,CACR,MAAO,IAAM,CAAC,CAAA;IAEhB,YAAY,IAAI,KAAI,MAAO,WAAU,CAAA;IACrC,WAAW,IAAI,KAAI,MAAA,QAAA,SAAA;IACnB,kBAAkB,IAAI,KAAI,MAAO,iBAAgB,CAAA;IACjD,UAAU,IAAI,KAAI,MAAO,eAAc,CAAA;IACvC,SAAS,IAAI,KAAI,MAAO,QAAO,CAAA;;QAGnB,YAAS,MAAS,eAAe;QACjC,YAAY,eAAe;QAC3B,QAAK,MAAS,eAAe,KAAK,GAAG;QAE5C,cAAW,aAAA,MAAY,WAAW,WAAW,eAAe,KAAK,CAAA;;;;;;;oDAItD,OAAK,IAAE,WAAW,EAAA,EAAA;;;;;;;;;kFAE1B,WAAW,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCnCnB,KAAE,KAAA,SAAA,MAAA,IAAG,KAAK,GACV,MAAG,KAAA,SAAA,OAAA,IAAa,IAAI,GAMpB,aAAU,KAAA,SAAA,cAAA,GAAGC,KAAI,GACjB,WAAQ,KAAA,SAAA,YAAA,GAAGA,KAAI,GACf,WAAQ,KAAA,SAAA,YAAA,GAAGA,KAAI,GAIZ,YAAA;;;;;;;;;;;;;;;;;;;;;;QAGE,YAAY,QAAO;IACxB,IAAI,IAAI,KAAI,MAAO,GAAE,CAAA;IACrB,KAAK,IAAI,KAAI,MACN,IAAG,GAAA,CACR,MAAO,IAAM,CAAC,CAAA;IAEhB,eAAe,IAAI,KAAI,MAAA,QAAA,aAAA;IACvB,aAAa,IAAI,KAAI,MAAA,QAAA,WAAA;IACrB,aAAa,IAAI,KAAI,MAAA,QAAA,WAAA;IACrB,SAAS,IAAI,KAAI,MAAA,QAAA,OAAA;IACjB,SAAS,IAAI,KAAI,MAAA,QAAA,OAAA;IACjB,YAAY,IAAI,KAAI,MAAO,WAAU,CAAA;IACrC,UAAU,IAAI,KAAI,MAAO,SAAQ,CAAA;IACjC,UAAU,IAAI,KAAI,MAAO,SAAQ,CAAA;IACjC,OAAO,IAAI,KAAI,MAAA,QAAA,KAAA;;QAGH,WAAW,UAAU,KAAK;QAC1B,SAAS,UAAU,KAAK;QACxB,UAAU,UAAU,KAAK;QACzB,cAAc,UAAU,KAAK;QAC7B,aAAa,UAAU,KAAK;QAC5B,SAAS,UAAU,KAAK;QACxB,QAAQ,UAAU,KAAK;QAE9B,cAAW,aAAA,MAAY,WAAW,WAAW,UAAU,KAAK,CAAA;;;;;;;oDAIjD,OAAK,IAAE,WAAW,EAAA,EAAA;;;;;;;;;kFAE1B,WAAW,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MC/CnB,KAAE,KAAA,SAAA,MAAA,IAAG,KAAK,GACV,MAAG,KAAA,SAAA,OAAA,IAAa,IAAI,GACpB,WAAQ,KAAA,SAAA,YAAA,GAAG,KAAK,GAChB,mBAAgB,KAAA,SAAA,oBAAA,GAAGC,KAAI,GACvB,WAAQ,KAAA,SAAA,YAAA,GAAG,CAAC,GAGT,YAAA;;;;;;;;;;;;;;;;QAGE,eAAe,eAAc;IAClC,IAAI,IAAI,KAAI,MAAO,GAAE,CAAA;IACrB,KAAK,IAAI,KAAI,MACN,IAAG,GAAA,CACR,MAAO,IAAM,CAAC,CAAA;IAEhB,UAAU,IAAI,KAAI,MAAO,SAAQ,CAAA;IACjC,kBAAkB,IAAI,KAAI,MAAO,iBAAgB,CAAA;IACjD,UAAU,IAAI,KAAI,MAAO,SAAQ,CAAA;;QAG5B,cAAW,aAAA,MAAY,WAAW,WAAW,aAAa,KAAK,CAAA;;;;;;;oDAIpD,OAAK,IAAE,WAAW,EAAA,EAAA;;;;;;;;;kFAE1B,WAAW,EAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;", "names": ["element", "noop", "element", "paneConstraints", "delta", "pivotIndex", "prevSize", "unsafeSize", "safeSize", "state", "state", "noop", "paneDataArray", "paneData", "state", "element", "noop", "noop", "noop"]}