{"version": 3, "sources": ["../../d3-interpolate/src/basis.js", "../../d3-interpolate/src/basisClosed.js", "../../d3-interpolate/src/constant.js", "../../d3-interpolate/src/color.js", "../../d3-interpolate/src/rgb.js", "../../d3-interpolate/src/numberArray.js", "../../d3-interpolate/src/array.js", "../../d3-interpolate/src/date.js", "../../d3-interpolate/src/number.js", "../../d3-interpolate/src/object.js", "../../d3-interpolate/src/string.js", "../../d3-interpolate/src/value.js", "../../d3-interpolate/src/discrete.js", "../../d3-interpolate/src/hue.js", "../../d3-interpolate/src/round.js", "../../d3-interpolate/src/transform/decompose.js", "../../d3-interpolate/src/transform/parse.js", "../../d3-interpolate/src/transform/index.js", "../../d3-interpolate/src/zoom.js", "../../d3-interpolate/src/hsl.js", "../../d3-interpolate/src/lab.js", "../../d3-interpolate/src/hcl.js", "../../d3-interpolate/src/cubehelix.js", "../../d3-interpolate/src/piecewise.js", "../../d3-interpolate/src/quantize.js"], "sourcesContent": ["export function basis(t1, v0, v1, v2, v3) {\n  var t2 = t1 * t1, t3 = t2 * t1;\n  return ((1 - 3 * t1 + 3 * t2 - t3) * v0\n      + (4 - 6 * t2 + 3 * t3) * v1\n      + (1 + 3 * t1 + 3 * t2 - 3 * t3) * v2\n      + t3 * v3) / 6;\n}\n\nexport default function(values) {\n  var n = values.length - 1;\n  return function(t) {\n    var i = t <= 0 ? (t = 0) : t >= 1 ? (t = 1, n - 1) : Math.floor(t * n),\n        v1 = values[i],\n        v2 = values[i + 1],\n        v0 = i > 0 ? values[i - 1] : 2 * v1 - v2,\n        v3 = i < n - 1 ? values[i + 2] : 2 * v2 - v1;\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "import {basis} from \"./basis.js\";\n\nexport default function(values) {\n  var n = values.length;\n  return function(t) {\n    var i = Math.floor(((t %= 1) < 0 ? ++t : t) * n),\n        v0 = values[(i + n - 1) % n],\n        v1 = values[i % n],\n        v2 = values[(i + 1) % n],\n        v3 = values[(i + 2) % n];\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n", "export default x => () => x;\n", "import constant from \"./constant.js\";\n\nfunction linear(a, d) {\n  return function(t) {\n    return a + t * d;\n  };\n}\n\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function(t) {\n    return Math.pow(a + t * b, y);\n  };\n}\n\nexport function hue(a, b) {\n  var d = b - a;\n  return d ? linear(a, d > 180 || d < -180 ? d - 360 * Math.round(d / 360) : d) : constant(isNaN(a) ? b : a);\n}\n\nexport function gamma(y) {\n  return (y = +y) === 1 ? nogamma : function(a, b) {\n    return b - a ? exponential(a, b, y) : constant(isNaN(a) ? b : a);\n  };\n}\n\nexport default function nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : constant(isNaN(a) ? b : a);\n}\n", "import {rgb as colorRgb} from \"d3-color\";\nimport basis from \"./basis.js\";\nimport basisClosed from \"./basisClosed.js\";\nimport nogamma, {gamma} from \"./color.js\";\n\nexport default (function rgbGamma(y) {\n  var color = gamma(y);\n\n  function rgb(start, end) {\n    var r = color((start = colorRgb(start)).r, (end = colorRgb(end)).r),\n        g = color(start.g, end.g),\n        b = color(start.b, end.b),\n        opacity = nogamma(start.opacity, end.opacity);\n    return function(t) {\n      start.r = r(t);\n      start.g = g(t);\n      start.b = b(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n\n  rgb.gamma = rgbGamma;\n\n  return rgb;\n})(1);\n\nfunction rgbSpline(spline) {\n  return function(colors) {\n    var n = colors.length,\n        r = new Array(n),\n        g = new Array(n),\n        b = new Array(n),\n        i, color;\n    for (i = 0; i < n; ++i) {\n      color = colorRgb(colors[i]);\n      r[i] = color.r || 0;\n      g[i] = color.g || 0;\n      b[i] = color.b || 0;\n    }\n    r = spline(r);\n    g = spline(g);\n    b = spline(b);\n    color.opacity = 1;\n    return function(t) {\n      color.r = r(t);\n      color.g = g(t);\n      color.b = b(t);\n      return color + \"\";\n    };\n  };\n}\n\nexport var rgbBasis = rgbSpline(basis);\nexport var rgbBasisClosed = rgbSpline(basisClosed);\n", "export default function(a, b) {\n  if (!b) b = [];\n  var n = a ? Math.min(b.length, a.length) : 0,\n      c = b.slice(),\n      i;\n  return function(t) {\n    for (i = 0; i < n; ++i) c[i] = a[i] * (1 - t) + b[i] * t;\n    return c;\n  };\n}\n\nexport function isNumberArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\n", "import value from \"./value.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  return (isNumberArray(b) ? numberArray : genericArray)(a, b);\n}\n\nexport function genericArray(a, b) {\n  var nb = b ? b.length : 0,\n      na = a ? Math.min(nb, a.length) : 0,\n      x = new Array(na),\n      c = new Array(nb),\n      i;\n\n  for (i = 0; i < na; ++i) x[i] = value(a[i], b[i]);\n  for (; i < nb; ++i) c[i] = b[i];\n\n  return function(t) {\n    for (i = 0; i < na; ++i) c[i] = x[i](t);\n    return c;\n  };\n}\n", "export default function(a, b) {\n  var d = new Date;\n  return a = +a, b = +b, function(t) {\n    return d.setTime(a * (1 - t) + b * t), d;\n  };\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return a * (1 - t) + b * t;\n  };\n}\n", "import value from \"./value.js\";\n\nexport default function(a, b) {\n  var i = {},\n      c = {},\n      k;\n\n  if (a === null || typeof a !== \"object\") a = {};\n  if (b === null || typeof b !== \"object\") b = {};\n\n  for (k in b) {\n    if (k in a) {\n      i[k] = value(a[k], b[k]);\n    } else {\n      c[k] = b[k];\n    }\n  }\n\n  return function(t) {\n    for (k in i) c[k] = i[k](t);\n    return c;\n  };\n}\n", "import number from \"./number.js\";\n\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g,\n    reB = new RegExp(reA.source, \"g\");\n\nfunction zero(b) {\n  return function() {\n    return b;\n  };\n}\n\nfunction one(b) {\n  return function(t) {\n    return b(t) + \"\";\n  };\n}\n\nexport default function(a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0, // scan index for next number in b\n      am, // current match in a\n      bm, // current match in b\n      bs, // string preceding current number in b, if any\n      i = -1, // index in s\n      s = [], // string constants and placeholders\n      q = []; // number interpolators\n\n  // Coerce inputs to strings.\n  a = a + \"\", b = b + \"\";\n\n  // Interpolate pairs of numbers in a & b.\n  while ((am = reA.exec(a))\n      && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) { // a string precedes the next number in b\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs; // coalesce with previous string\n      else s[++i] = bs;\n    }\n    if ((am = am[0]) === (bm = bm[0])) { // numbers in a & b match\n      if (s[i]) s[i] += bm; // coalesce with previous string\n      else s[++i] = bm;\n    } else { // interpolate non-matching numbers\n      s[++i] = null;\n      q.push({i: i, x: number(am, bm)});\n    }\n    bi = reB.lastIndex;\n  }\n\n  // Add remains of b.\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs; // coalesce with previous string\n    else s[++i] = bs;\n  }\n\n  // Special optimization for only a single match.\n  // Otherwise, interpolate each of the numbers and rejoin the string.\n  return s.length < 2 ? (q[0]\n      ? one(q[0].x)\n      : zero(b))\n      : (b = q.length, function(t) {\n          for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);\n          return s.join(\"\");\n        });\n}\n", "import {color} from \"d3-color\";\nimport rgb from \"./rgb.js\";\nimport {genericArray} from \"./array.js\";\nimport date from \"./date.js\";\nimport number from \"./number.js\";\nimport object from \"./object.js\";\nimport string from \"./string.js\";\nimport constant from \"./constant.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  var t = typeof b, c;\n  return b == null || t === \"boolean\" ? constant(b)\n      : (t === \"number\" ? number\n      : t === \"string\" ? ((c = color(b)) ? (b = c, rgb) : string)\n      : b instanceof color ? rgb\n      : b instanceof Date ? date\n      : isNumberArray(b) ? numberArray\n      : Array.isArray(b) ? genericArray\n      : typeof b.valueOf !== \"function\" && typeof b.toString !== \"function\" || isNaN(b) ? object\n      : number)(a, b);\n}\n", "export default function(range) {\n  var n = range.length;\n  return function(t) {\n    return range[Math.max(0, Math.min(n - 1, Math.floor(t * n)))];\n  };\n}\n", "import {hue} from \"./color.js\";\n\nexport default function(a, b) {\n  var i = hue(+a, +b);\n  return function(t) {\n    var x = i(t);\n    return x - 360 * Math.floor(x / 360);\n  };\n}\n", "export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return Math.round(a * (1 - t) + b * t);\n  };\n}\n", "var degrees = 180 / Math.PI;\n\nexport var identity = {\n  translateX: 0,\n  translateY: 0,\n  rotate: 0,\n  skewX: 0,\n  scaleX: 1,\n  scaleY: 1\n};\n\nexport default function(a, b, c, d, e, f) {\n  var scaleX, scaleY, skewX;\n  if (scaleX = Math.sqrt(a * a + b * b)) a /= scaleX, b /= scaleX;\n  if (skewX = a * c + b * d) c -= a * skewX, d -= b * skewX;\n  if (scaleY = Math.sqrt(c * c + d * d)) c /= scaleY, d /= scaleY, skewX /= scaleY;\n  if (a * d < b * c) a = -a, b = -b, skewX = -skewX, scaleX = -scaleX;\n  return {\n    translateX: e,\n    translateY: f,\n    rotate: Math.atan2(b, a) * degrees,\n    skewX: Math.atan(skewX) * degrees,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}\n", "import decompose, {identity} from \"./decompose.js\";\n\nvar svgNode;\n\n/* eslint-disable no-undef */\nexport function parseCss(value) {\n  const m = new (typeof DOMMatrix === \"function\" ? DOMMatrix : WebKitCSSMatrix)(value + \"\");\n  return m.isIdentity ? identity : decompose(m.a, m.b, m.c, m.d, m.e, m.f);\n}\n\nexport function parseSvg(value) {\n  if (value == null) return identity;\n  if (!svgNode) svgNode = document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n  svgNode.setAttribute(\"transform\", value);\n  if (!(value = svgNode.transform.baseVal.consolidate())) return identity;\n  value = value.matrix;\n  return decompose(value.a, value.b, value.c, value.d, value.e, value.f);\n}\n", "import number from \"../number.js\";\nimport {parseCss, parseSvg} from \"./parse.js\";\n\nfunction interpolateTransform(parse, pxComma, pxParen, degParen) {\n\n  function pop(s) {\n    return s.length ? s.pop() + \" \" : \"\";\n  }\n\n  function translate(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(\"translate(\", null, pxComma, null, pxParen);\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb || yb) {\n      s.push(\"translate(\" + xb + pxComma + yb + pxParen);\n    }\n  }\n\n  function rotate(a, b, s, q) {\n    if (a !== b) {\n      if (a - b > 180) b += 360; else if (b - a > 180) a += 360; // shortest path\n      q.push({i: s.push(pop(s) + \"rotate(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"rotate(\" + b + degParen);\n    }\n  }\n\n  function skewX(a, b, s, q) {\n    if (a !== b) {\n      q.push({i: s.push(pop(s) + \"skewX(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"skewX(\" + b + degParen);\n    }\n  }\n\n  function scale(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(pop(s) + \"scale(\", null, \",\", null, \")\");\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb !== 1 || yb !== 1) {\n      s.push(pop(s) + \"scale(\" + xb + \",\" + yb + \")\");\n    }\n  }\n\n  return function(a, b) {\n    var s = [], // string constants and placeholders\n        q = []; // number interpolators\n    a = parse(a), b = parse(b);\n    translate(a.translateX, a.translateY, b.translateX, b.translateY, s, q);\n    rotate(a.rotate, b.rotate, s, q);\n    skewX(a.skewX, b.skewX, s, q);\n    scale(a.scaleX, a.scaleY, b.scaleX, b.scaleY, s, q);\n    a = b = null; // gc\n    return function(t) {\n      var i = -1, n = q.length, o;\n      while (++i < n) s[(o = q[i]).i] = o.x(t);\n      return s.join(\"\");\n    };\n  };\n}\n\nexport var interpolateTransformCss = interpolateTransform(parseCss, \"px, \", \"px)\", \"deg)\");\nexport var interpolateTransformSvg = interpolateTransform(parseSvg, \", \", \")\", \")\");\n", "var epsilon2 = 1e-12;\n\nfunction cosh(x) {\n  return ((x = Math.exp(x)) + 1 / x) / 2;\n}\n\nfunction sinh(x) {\n  return ((x = Math.exp(x)) - 1 / x) / 2;\n}\n\nfunction tanh(x) {\n  return ((x = Math.exp(2 * x)) - 1) / (x + 1);\n}\n\nexport default (function zoomRho(rho, rho2, rho4) {\n\n  // p0 = [ux0, uy0, w0]\n  // p1 = [ux1, uy1, w1]\n  function zoom(p0, p1) {\n    var ux0 = p0[0], uy0 = p0[1], w0 = p0[2],\n        ux1 = p1[0], uy1 = p1[1], w1 = p1[2],\n        dx = ux1 - ux0,\n        dy = uy1 - uy0,\n        d2 = dx * dx + dy * dy,\n        i,\n        S;\n\n    // Special case for u0 ≅ u1.\n    if (d2 < epsilon2) {\n      S = Math.log(w1 / w0) / rho;\n      i = function(t) {\n        return [\n          ux0 + t * dx,\n          uy0 + t * dy,\n          w0 * Math.exp(rho * t * S)\n        ];\n      }\n    }\n\n    // General case.\n    else {\n      var d1 = Math.sqrt(d2),\n          b0 = (w1 * w1 - w0 * w0 + rho4 * d2) / (2 * w0 * rho2 * d1),\n          b1 = (w1 * w1 - w0 * w0 - rho4 * d2) / (2 * w1 * rho2 * d1),\n          r0 = Math.log(Math.sqrt(b0 * b0 + 1) - b0),\n          r1 = Math.log(Math.sqrt(b1 * b1 + 1) - b1);\n      S = (r1 - r0) / rho;\n      i = function(t) {\n        var s = t * S,\n            coshr0 = cosh(r0),\n            u = w0 / (rho2 * d1) * (coshr0 * tanh(rho * s + r0) - sinh(r0));\n        return [\n          ux0 + u * dx,\n          uy0 + u * dy,\n          w0 * coshr0 / cosh(rho * s + r0)\n        ];\n      }\n    }\n\n    i.duration = S * 1000 * rho / Math.SQRT2;\n\n    return i;\n  }\n\n  zoom.rho = function(_) {\n    var _1 = Math.max(1e-3, +_), _2 = _1 * _1, _4 = _2 * _2;\n    return zoomRho(_1, _2, _4);\n  };\n\n  return zoom;\n})(Math.SQRT2, 2, 4);\n", "import {hsl as colorHsl} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction hsl(hue) {\n  return function(start, end) {\n    var h = hue((start = colorHsl(start)).h, (end = colorHsl(end)).h),\n        s = color(start.s, end.s),\n        l = color(start.l, end.l),\n        opacity = color(start.opacity, end.opacity);\n    return function(t) {\n      start.h = h(t);\n      start.s = s(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n}\n\nexport default hsl(hue);\nexport var hslLong = hsl(color);\n", "import {lab as colorLab} from \"d3-color\";\nimport color from \"./color.js\";\n\nexport default function lab(start, end) {\n  var l = color((start = colorLab(start)).l, (end = colorLab(end)).l),\n      a = color(start.a, end.a),\n      b = color(start.b, end.b),\n      opacity = color(start.opacity, end.opacity);\n  return function(t) {\n    start.l = l(t);\n    start.a = a(t);\n    start.b = b(t);\n    start.opacity = opacity(t);\n    return start + \"\";\n  };\n}\n", "import {hcl as colorHcl} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction hcl(hue) {\n  return function(start, end) {\n    var h = hue((start = colorHcl(start)).h, (end = colorHcl(end)).h),\n        c = color(start.c, end.c),\n        l = color(start.l, end.l),\n        opacity = color(start.opacity, end.opacity);\n    return function(t) {\n      start.h = h(t);\n      start.c = c(t);\n      start.l = l(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n}\n\nexport default hcl(hue);\nexport var hclLong = hcl(color);\n", "import {cubehelix as colorCubehelix} from \"d3-color\";\nimport color, {hue} from \"./color.js\";\n\nfunction cubehelix(hue) {\n  return (function cubehelixGamma(y) {\n    y = +y;\n\n    function cubehelix(start, end) {\n      var h = hue((start = colorCubehelix(start)).h, (end = colorCubehelix(end)).h),\n          s = color(start.s, end.s),\n          l = color(start.l, end.l),\n          opacity = color(start.opacity, end.opacity);\n      return function(t) {\n        start.h = h(t);\n        start.s = s(t);\n        start.l = l(Math.pow(t, y));\n        start.opacity = opacity(t);\n        return start + \"\";\n      };\n    }\n\n    cubehelix.gamma = cubehelixGamma;\n\n    return cubehelix;\n  })(1);\n}\n\nexport default cubehelix(hue);\nexport var cubehelixLong = cubehelix(color);\n", "import {default as value} from \"./value.js\";\n\nexport default function piecewise(interpolate, values) {\n  if (values === undefined) values = interpolate, interpolate = value;\n  var i = 0, n = values.length - 1, v = values[0], I = new Array(n < 0 ? 0 : n);\n  while (i < n) I[i] = interpolate(v, v = values[++i]);\n  return function(t) {\n    var i = Math.max(0, Math.min(n - 1, Math.floor(t *= n)));\n    return I[i](t - i);\n  };\n}\n", "export default function(interpolator, n) {\n  var samples = new Array(n);\n  for (var i = 0; i < n; ++i) samples[i] = interpolator(i / (n - 1));\n  return samples;\n}\n"], "mappings": ";;;;;;;;;;AAAO,SAAS,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI;AACxC,MAAI,KAAK,KAAK,IAAI,KAAK,KAAK;AAC5B,WAAS,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,MAC9B,IAAI,IAAI,KAAK,IAAI,MAAM,MACvB,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,KACjC,KAAK,MAAM;AACnB;AAEe,SAAR,cAAiB,QAAQ;AAC9B,MAAI,IAAI,OAAO,SAAS;AACxB,SAAO,SAAS,GAAG;AACjB,QAAI,IAAI,KAAK,IAAK,IAAI,IAAK,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK,KAAK,MAAM,IAAI,CAAC,GACjE,KAAK,OAAO,CAAC,GACb,KAAK,OAAO,IAAI,CAAC,GACjB,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,KAAK,IACtC,KAAK,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,KAAK;AAC9C,WAAO,OAAO,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,EAC9C;AACF;;;AChBe,SAAR,oBAAiB,QAAQ;AAC9B,MAAI,IAAI,OAAO;AACf,SAAO,SAAS,GAAG;AACjB,QAAI,IAAI,KAAK,QAAQ,KAAK,KAAK,IAAI,EAAE,IAAI,KAAK,CAAC,GAC3C,KAAK,QAAQ,IAAI,IAAI,KAAK,CAAC,GAC3B,KAAK,OAAO,IAAI,CAAC,GACjB,KAAK,QAAQ,IAAI,KAAK,CAAC,GACvB,KAAK,QAAQ,IAAI,KAAK,CAAC;AAC3B,WAAO,OAAO,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,EAC9C;AACF;;;ACZA,IAAO,mBAAQ,OAAK,MAAM;;;ACE1B,SAAS,OAAO,GAAG,GAAG;AACpB,SAAO,SAAS,GAAG;AACjB,WAAO,IAAI,IAAI;AAAA,EACjB;AACF;AAEA,SAAS,YAAY,GAAG,GAAG,GAAG;AAC5B,SAAO,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,IAAI,GAAG,SAAS,GAAG;AACxE,WAAO,KAAK,IAAI,IAAI,IAAI,GAAG,CAAC;AAAA,EAC9B;AACF;AAEO,SAAS,IAAI,GAAG,GAAG;AACxB,MAAI,IAAI,IAAI;AACZ,SAAO,IAAI,OAAO,GAAG,IAAI,OAAO,IAAI,OAAO,IAAI,MAAM,KAAK,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,iBAAS,MAAM,CAAC,IAAI,IAAI,CAAC;AAC3G;AAEO,SAAS,MAAM,GAAG;AACvB,UAAQ,IAAI,CAAC,OAAO,IAAI,UAAU,SAAS,GAAG,GAAG;AAC/C,WAAO,IAAI,IAAI,YAAY,GAAG,GAAG,CAAC,IAAI,iBAAS,MAAM,CAAC,IAAI,IAAI,CAAC;AAAA,EACjE;AACF;AAEe,SAAR,QAAyB,GAAG,GAAG;AACpC,MAAI,IAAI,IAAI;AACZ,SAAO,IAAI,OAAO,GAAG,CAAC,IAAI,iBAAS,MAAM,CAAC,IAAI,IAAI,CAAC;AACrD;;;ACvBA,IAAO,cAAS,SAAS,SAAS,GAAG;AACnC,MAAIA,SAAQ,MAAM,CAAC;AAEnB,WAASC,KAAI,OAAO,KAAK;AACvB,QAAI,IAAID,QAAO,QAAQ,IAAS,KAAK,GAAG,IAAI,MAAM,IAAS,GAAG,GAAG,CAAC,GAC9D,IAAIA,OAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAIA,OAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAQ,MAAM,SAAS,IAAI,OAAO;AAChD,WAAO,SAAS,GAAG;AACjB,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,UAAU,QAAQ,CAAC;AACzB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AAEA,EAAAC,KAAI,QAAQ;AAEZ,SAAOA;AACT,EAAG,CAAC;AAEJ,SAAS,UAAU,QAAQ;AACzB,SAAO,SAAS,QAAQ;AACtB,QAAI,IAAI,OAAO,QACX,IAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC,GACf,GAAGD;AACP,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,MAAAA,SAAQ,IAAS,OAAO,CAAC,CAAC;AAC1B,QAAE,CAAC,IAAIA,OAAM,KAAK;AAClB,QAAE,CAAC,IAAIA,OAAM,KAAK;AAClB,QAAE,CAAC,IAAIA,OAAM,KAAK;AAAA,IACpB;AACA,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,CAAC;AACZ,QAAI,OAAO,CAAC;AACZ,IAAAA,OAAM,UAAU;AAChB,WAAO,SAAS,GAAG;AACjB,MAAAA,OAAM,IAAI,EAAE,CAAC;AACb,MAAAA,OAAM,IAAI,EAAE,CAAC;AACb,MAAAA,OAAM,IAAI,EAAE,CAAC;AACb,aAAOA,SAAQ;AAAA,IACjB;AAAA,EACF;AACF;AAEO,IAAI,WAAW,UAAU,aAAK;AAC9B,IAAI,iBAAiB,UAAU,mBAAW;;;ACtDlC,SAAR,oBAAiB,GAAG,GAAG;AAC5B,MAAI,CAAC,EAAG,KAAI,CAAC;AACb,MAAI,IAAI,IAAI,KAAK,IAAI,EAAE,QAAQ,EAAE,MAAM,IAAI,GACvC,IAAI,EAAE,MAAM,GACZ;AACJ,SAAO,SAAS,GAAG;AACjB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,IAAI;AACvD,WAAO;AAAA,EACT;AACF;AAEO,SAAS,cAAc,GAAG;AAC/B,SAAO,YAAY,OAAO,CAAC,KAAK,EAAE,aAAa;AACjD;;;ACVe,SAAR,cAAiB,GAAG,GAAG;AAC5B,UAAQ,cAAc,CAAC,IAAI,sBAAc,cAAc,GAAG,CAAC;AAC7D;AAEO,SAAS,aAAa,GAAG,GAAG;AACjC,MAAI,KAAK,IAAI,EAAE,SAAS,GACpB,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,MAAM,IAAI,GAClC,IAAI,IAAI,MAAM,EAAE,GAChB,IAAI,IAAI,MAAM,EAAE,GAChB;AAEJ,OAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG,GAAE,CAAC,IAAI,cAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAChD,SAAO,IAAI,IAAI,EAAE,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAE9B,SAAO,SAAS,GAAG;AACjB,SAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AACtC,WAAO;AAAA,EACT;AACF;;;ACrBe,SAAR,aAAiB,GAAG,GAAG;AAC5B,MAAI,IAAI,oBAAI;AACZ,SAAO,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG;AACjC,WAAO,EAAE,QAAQ,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG;AAAA,EACzC;AACF;;;ACLe,SAAR,eAAiB,GAAG,GAAG;AAC5B,SAAO,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG;AACjC,WAAO,KAAK,IAAI,KAAK,IAAI;AAAA,EAC3B;AACF;;;ACFe,SAAR,eAAiB,GAAG,GAAG;AAC5B,MAAI,IAAI,CAAC,GACL,IAAI,CAAC,GACL;AAEJ,MAAI,MAAM,QAAQ,OAAO,MAAM,SAAU,KAAI,CAAC;AAC9C,MAAI,MAAM,QAAQ,OAAO,MAAM,SAAU,KAAI,CAAC;AAE9C,OAAK,KAAK,GAAG;AACX,QAAI,KAAK,GAAG;AACV,QAAE,CAAC,IAAI,cAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACzB,OAAO;AACL,QAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IACZ;AAAA,EACF;AAEA,SAAO,SAAS,GAAG;AACjB,SAAK,KAAK,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAC1B,WAAO;AAAA,EACT;AACF;;;ACpBA,IAAI,MAAM;AAAV,IACI,MAAM,IAAI,OAAO,IAAI,QAAQ,GAAG;AAEpC,SAAS,KAAK,GAAG;AACf,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,IAAI,GAAG;AACd,SAAO,SAAS,GAAG;AACjB,WAAO,EAAE,CAAC,IAAI;AAAA,EAChB;AACF;AAEe,SAAR,eAAiB,GAAG,GAAG;AAC5B,MAAI,KAAK,IAAI,YAAY,IAAI,YAAY,GACrC,IACA,IACA,IACA,IAAI,IACJ,IAAI,CAAC,GACL,IAAI,CAAC;AAGT,MAAI,IAAI,IAAI,IAAI,IAAI;AAGpB,UAAQ,KAAK,IAAI,KAAK,CAAC,OACf,KAAK,IAAI,KAAK,CAAC,IAAI;AACzB,SAAK,KAAK,GAAG,SAAS,IAAI;AACxB,WAAK,EAAE,MAAM,IAAI,EAAE;AACnB,UAAI,EAAE,CAAC,EAAG,GAAE,CAAC,KAAK;AAAA,UACb,GAAE,EAAE,CAAC,IAAI;AAAA,IAChB;AACA,SAAK,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,IAAI;AACjC,UAAI,EAAE,CAAC,EAAG,GAAE,CAAC,KAAK;AAAA,UACb,GAAE,EAAE,CAAC,IAAI;AAAA,IAChB,OAAO;AACL,QAAE,EAAE,CAAC,IAAI;AACT,QAAE,KAAK,EAAC,GAAM,GAAG,eAAO,IAAI,EAAE,EAAC,CAAC;AAAA,IAClC;AACA,SAAK,IAAI;AAAA,EACX;AAGA,MAAI,KAAK,EAAE,QAAQ;AACjB,SAAK,EAAE,MAAM,EAAE;AACf,QAAI,EAAE,CAAC,EAAG,GAAE,CAAC,KAAK;AAAA,QACb,GAAE,EAAE,CAAC,IAAI;AAAA,EAChB;AAIA,SAAO,EAAE,SAAS,IAAK,EAAE,CAAC,IACpB,IAAI,EAAE,CAAC,EAAE,CAAC,IACV,KAAK,CAAC,KACL,IAAI,EAAE,QAAQ,SAAS,GAAG;AACzB,aAASE,KAAI,GAAG,GAAGA,KAAI,GAAG,EAAEA,GAAG,IAAG,IAAI,EAAEA,EAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;AACtD,WAAO,EAAE,KAAK,EAAE;AAAA,EAClB;AACR;;;ACrDe,SAAR,cAAiB,GAAG,GAAG;AAC5B,MAAI,IAAI,OAAO,GAAG;AAClB,SAAO,KAAK,QAAQ,MAAM,YAAY,iBAAS,CAAC,KACzC,MAAM,WAAW,iBAClB,MAAM,YAAa,IAAI,MAAM,CAAC,MAAM,IAAI,GAAG,eAAO,iBAClD,aAAa,QAAQ,cACrB,aAAa,OAAO,eACpB,cAAc,CAAC,IAAI,sBACnB,MAAM,QAAQ,CAAC,IAAI,eACnB,OAAO,EAAE,YAAY,cAAc,OAAO,EAAE,aAAa,cAAc,MAAM,CAAC,IAAI,iBAClF,gBAAQ,GAAG,CAAC;AACpB;;;ACrBe,SAAR,iBAAiB,OAAO;AAC7B,MAAI,IAAI,MAAM;AACd,SAAO,SAAS,GAAG;AACjB,WAAO,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;AAAA,EAC9D;AACF;;;ACHe,SAAR,YAAiB,GAAG,GAAG;AAC5B,MAAI,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AAClB,SAAO,SAAS,GAAG;AACjB,QAAI,IAAI,EAAE,CAAC;AACX,WAAO,IAAI,MAAM,KAAK,MAAM,IAAI,GAAG;AAAA,EACrC;AACF;;;ACRe,SAAR,cAAiB,GAAG,GAAG;AAC5B,SAAO,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG;AACjC,WAAO,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,CAAC;AAAA,EACvC;AACF;;;ACJA,IAAI,UAAU,MAAM,KAAK;AAElB,IAAI,WAAW;AAAA,EACpB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AACV;AAEe,SAAR,kBAAiB,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACxC,MAAI,QAAQ,QAAQ;AACpB,MAAI,SAAS,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,EAAG,MAAK,QAAQ,KAAK;AACzD,MAAI,QAAQ,IAAI,IAAI,IAAI,EAAG,MAAK,IAAI,OAAO,KAAK,IAAI;AACpD,MAAI,SAAS,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,EAAG,MAAK,QAAQ,KAAK,QAAQ,SAAS;AAC1E,MAAI,IAAI,IAAI,IAAI,EAAG,KAAI,CAAC,GAAG,IAAI,CAAC,GAAG,QAAQ,CAAC,OAAO,SAAS,CAAC;AAC7D,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,QAAQ,KAAK,MAAM,GAAG,CAAC,IAAI;AAAA,IAC3B,OAAO,KAAK,KAAK,KAAK,IAAI;AAAA,IAC1B;AAAA,IACA;AAAA,EACF;AACF;;;ACvBA,IAAI;AAGG,SAAS,SAAS,OAAO;AAC9B,QAAM,IAAI,KAAK,OAAO,cAAc,aAAa,YAAY,iBAAiB,QAAQ,EAAE;AACxF,SAAO,EAAE,aAAa,WAAW,kBAAU,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACzE;AAEO,SAAS,SAAS,OAAO;AAC9B,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,CAAC,QAAS,WAAU,SAAS,gBAAgB,8BAA8B,GAAG;AAClF,UAAQ,aAAa,aAAa,KAAK;AACvC,MAAI,EAAE,QAAQ,QAAQ,UAAU,QAAQ,YAAY,GAAI,QAAO;AAC/D,UAAQ,MAAM;AACd,SAAO,kBAAU,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACvE;;;ACdA,SAAS,qBAAqB,OAAO,SAAS,SAAS,UAAU;AAE/D,WAAS,IAAI,GAAG;AACd,WAAO,EAAE,SAAS,EAAE,IAAI,IAAI,MAAM;AAAA,EACpC;AAEA,WAAS,UAAU,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AACvC,QAAI,OAAO,MAAM,OAAO,IAAI;AAC1B,UAAI,IAAI,EAAE,KAAK,cAAc,MAAM,SAAS,MAAM,OAAO;AACzD,QAAE,KAAK,EAAC,GAAG,IAAI,GAAG,GAAG,eAAO,IAAI,EAAE,EAAC,GAAG,EAAC,GAAG,IAAI,GAAG,GAAG,eAAO,IAAI,EAAE,EAAC,CAAC;AAAA,IACrE,WAAW,MAAM,IAAI;AACnB,QAAE,KAAK,eAAe,KAAK,UAAU,KAAK,OAAO;AAAA,IACnD;AAAA,EACF;AAEA,WAAS,OAAO,GAAG,GAAG,GAAG,GAAG;AAC1B,QAAI,MAAM,GAAG;AACX,UAAI,IAAI,IAAI,IAAK,MAAK;AAAA,eAAc,IAAI,IAAI,IAAK,MAAK;AACtD,QAAE,KAAK,EAAC,GAAG,EAAE,KAAK,IAAI,CAAC,IAAI,WAAW,MAAM,QAAQ,IAAI,GAAG,GAAG,eAAO,GAAG,CAAC,EAAC,CAAC;AAAA,IAC7E,WAAW,GAAG;AACZ,QAAE,KAAK,IAAI,CAAC,IAAI,YAAY,IAAI,QAAQ;AAAA,IAC1C;AAAA,EACF;AAEA,WAAS,MAAM,GAAG,GAAG,GAAG,GAAG;AACzB,QAAI,MAAM,GAAG;AACX,QAAE,KAAK,EAAC,GAAG,EAAE,KAAK,IAAI,CAAC,IAAI,UAAU,MAAM,QAAQ,IAAI,GAAG,GAAG,eAAO,GAAG,CAAC,EAAC,CAAC;AAAA,IAC5E,WAAW,GAAG;AACZ,QAAE,KAAK,IAAI,CAAC,IAAI,WAAW,IAAI,QAAQ;AAAA,IACzC;AAAA,EACF;AAEA,WAAS,MAAM,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AACnC,QAAI,OAAO,MAAM,OAAO,IAAI;AAC1B,UAAI,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,UAAU,MAAM,KAAK,MAAM,GAAG;AACtD,QAAE,KAAK,EAAC,GAAG,IAAI,GAAG,GAAG,eAAO,IAAI,EAAE,EAAC,GAAG,EAAC,GAAG,IAAI,GAAG,GAAG,eAAO,IAAI,EAAE,EAAC,CAAC;AAAA,IACrE,WAAW,OAAO,KAAK,OAAO,GAAG;AAC/B,QAAE,KAAK,IAAI,CAAC,IAAI,WAAW,KAAK,MAAM,KAAK,GAAG;AAAA,IAChD;AAAA,EACF;AAEA,SAAO,SAAS,GAAG,GAAG;AACpB,QAAI,IAAI,CAAC,GACL,IAAI,CAAC;AACT,QAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC;AACzB,cAAU,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,GAAG,CAAC;AACtE,WAAO,EAAE,QAAQ,EAAE,QAAQ,GAAG,CAAC;AAC/B,UAAM,EAAE,OAAO,EAAE,OAAO,GAAG,CAAC;AAC5B,UAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,GAAG,CAAC;AAClD,QAAI,IAAI;AACR,WAAO,SAAS,GAAG;AACjB,UAAI,IAAI,IAAI,IAAI,EAAE,QAAQ;AAC1B,aAAO,EAAE,IAAI,EAAG,IAAG,IAAI,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;AACvC,aAAO,EAAE,KAAK,EAAE;AAAA,IAClB;AAAA,EACF;AACF;AAEO,IAAI,0BAA0B,qBAAqB,UAAU,QAAQ,OAAO,MAAM;AAClF,IAAI,0BAA0B,qBAAqB,UAAU,MAAM,KAAK,GAAG;;;AC9DlF,IAAI,WAAW;AAEf,SAAS,KAAK,GAAG;AACf,WAAS,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;AACvC;AAEA,SAAS,KAAK,GAAG;AACf,WAAS,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,KAAK;AACvC;AAEA,SAAS,KAAK,GAAG;AACf,WAAS,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,MAAM,IAAI;AAC5C;AAEA,IAAO,eAAS,SAAS,QAAQ,KAAK,MAAM,MAAM;AAIhD,WAAS,KAAK,IAAI,IAAI;AACpB,QAAI,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GACnC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GACnC,KAAK,MAAM,KACX,KAAK,MAAM,KACX,KAAK,KAAK,KAAK,KAAK,IACpB,GACA;AAGJ,QAAI,KAAK,UAAU;AACjB,UAAI,KAAK,IAAI,KAAK,EAAE,IAAI;AACxB,UAAI,SAAS,GAAG;AACd,eAAO;AAAA,UACL,MAAM,IAAI;AAAA,UACV,MAAM,IAAI;AAAA,UACV,KAAK,KAAK,IAAI,MAAM,IAAI,CAAC;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,OAGK;AACH,UAAI,KAAK,KAAK,KAAK,EAAE,GACjB,MAAM,KAAK,KAAK,KAAK,KAAK,OAAO,OAAO,IAAI,KAAK,OAAO,KACxD,MAAM,KAAK,KAAK,KAAK,KAAK,OAAO,OAAO,IAAI,KAAK,OAAO,KACxD,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,GACzC,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE;AAC7C,WAAK,KAAK,MAAM;AAChB,UAAI,SAAS,GAAG;AACd,YAAI,IAAI,IAAI,GACR,SAAS,KAAK,EAAE,GAChB,IAAI,MAAM,OAAO,OAAO,SAAS,KAAK,MAAM,IAAI,EAAE,IAAI,KAAK,EAAE;AACjE,eAAO;AAAA,UACL,MAAM,IAAI;AAAA,UACV,MAAM,IAAI;AAAA,UACV,KAAK,SAAS,KAAK,MAAM,IAAI,EAAE;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AAEA,MAAE,WAAW,IAAI,MAAO,MAAM,KAAK;AAEnC,WAAO;AAAA,EACT;AAEA,OAAK,MAAM,SAAS,GAAG;AACrB,QAAI,KAAK,KAAK,IAAI,MAAM,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AACrD,WAAO,QAAQ,IAAI,IAAI,EAAE;AAAA,EAC3B;AAEA,SAAO;AACT,EAAG,KAAK,OAAO,GAAG,CAAC;;;ACnEnB,SAASC,KAAIC,MAAK;AAChB,SAAO,SAAS,OAAO,KAAK;AAC1B,QAAI,IAAIA,MAAK,QAAQ,IAAS,KAAK,GAAG,IAAI,MAAM,IAAS,GAAG,GAAG,CAAC,GAC5D,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAM,MAAM,SAAS,IAAI,OAAO;AAC9C,WAAO,SAAS,GAAG;AACjB,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,UAAU,QAAQ,CAAC;AACzB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACF;AAEA,IAAO,cAAQD,KAAI,GAAG;AACf,IAAI,UAAUA,KAAI,OAAK;;;ACjBf,SAARE,KAAqB,OAAO,KAAK;AACtC,MAAI,IAAI,SAAO,QAAQ,IAAS,KAAK,GAAG,IAAI,MAAM,IAAS,GAAG,GAAG,CAAC,GAC9D,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAM,MAAM,SAAS,IAAI,OAAO;AAC9C,SAAO,SAAS,GAAG;AACjB,UAAM,IAAI,EAAE,CAAC;AACb,UAAM,IAAI,EAAE,CAAC;AACb,UAAM,IAAI,EAAE,CAAC;AACb,UAAM,UAAU,QAAQ,CAAC;AACzB,WAAO,QAAQ;AAAA,EACjB;AACF;;;ACZA,SAASC,KAAIC,MAAK;AAChB,SAAO,SAAS,OAAO,KAAK;AAC1B,QAAI,IAAIA,MAAK,QAAQ,IAAS,KAAK,GAAG,IAAI,MAAM,IAAS,GAAG,GAAG,CAAC,GAC5D,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAM,MAAM,SAAS,IAAI,OAAO;AAC9C,WAAO,SAAS,GAAG;AACjB,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,UAAU,QAAQ,CAAC;AACzB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACF;AAEA,IAAO,cAAQD,KAAI,GAAG;AACf,IAAI,UAAUA,KAAI,OAAK;;;ACjB9B,SAASE,WAAUC,MAAK;AACtB,SAAQ,SAAS,eAAe,GAAG;AACjC,QAAI,CAAC;AAEL,aAASD,WAAU,OAAO,KAAK;AAC7B,UAAI,IAAIC,MAAK,QAAQ,UAAe,KAAK,GAAG,IAAI,MAAM,UAAe,GAAG,GAAG,CAAC,GACxE,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,IAAI,QAAM,MAAM,GAAG,IAAI,CAAC,GACxB,UAAU,QAAM,MAAM,SAAS,IAAI,OAAO;AAC9C,aAAO,SAAS,GAAG;AACjB,cAAM,IAAI,EAAE,CAAC;AACb,cAAM,IAAI,EAAE,CAAC;AACb,cAAM,IAAI,EAAE,KAAK,IAAI,GAAG,CAAC,CAAC;AAC1B,cAAM,UAAU,QAAQ,CAAC;AACzB,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF;AAEA,IAAAD,WAAU,QAAQ;AAElB,WAAOA;AAAA,EACT,EAAG,CAAC;AACN;AAEA,IAAO,oBAAQA,WAAU,GAAG;AACrB,IAAI,gBAAgBA,WAAU,OAAK;;;AC1B3B,SAAR,UAA2B,aAAa,QAAQ;AACrD,MAAI,WAAW,OAAW,UAAS,aAAa,cAAc;AAC9D,MAAI,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC;AAC5E,SAAO,IAAI,EAAG,GAAE,CAAC,IAAI,YAAY,GAAG,IAAI,OAAO,EAAE,CAAC,CAAC;AACnD,SAAO,SAAS,GAAG;AACjB,QAAIE,KAAI,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,MAAM,KAAK,CAAC,CAAC,CAAC;AACvD,WAAO,EAAEA,EAAC,EAAE,IAAIA,EAAC;AAAA,EACnB;AACF;;;ACVe,SAAR,iBAAiB,cAAc,GAAG;AACvC,MAAI,UAAU,IAAI,MAAM,CAAC;AACzB,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,SAAQ,CAAC,IAAI,aAAa,KAAK,IAAI,EAAE;AACjE,SAAO;AACT;", "names": ["color", "rgb", "i", "hsl", "hue", "lab", "hcl", "hue", "cubehelix", "hue", "i"]}