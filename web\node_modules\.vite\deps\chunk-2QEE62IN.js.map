{"version": 3, "sources": ["../../@sinclair/typebox/build/esm/type/registry/format.mjs", "../../@sinclair/typebox/build/esm/type/registry/type.mjs", "../../@sinclair/typebox/build/esm/type/guard/value.mjs", "../../@sinclair/typebox/build/esm/type/clone/value.mjs", "../../@sinclair/typebox/build/esm/value/guard/guard.mjs", "../../@sinclair/typebox/build/esm/system/policy.mjs", "../../@sinclair/typebox/build/esm/type/create/immutable.mjs", "../../@sinclair/typebox/build/esm/type/create/type.mjs", "../../@sinclair/typebox/build/esm/type/symbols/symbols.mjs", "../../@sinclair/typebox/build/esm/type/unsafe/unsafe.mjs", "../../@sinclair/typebox/build/esm/type/error/error.mjs", "../../@sinclair/typebox/build/esm/type/mapped/mapped-key.mjs", "../../@sinclair/typebox/build/esm/type/mapped/mapped-result.mjs", "../../@sinclair/typebox/build/esm/type/array/array.mjs", "../../@sinclair/typebox/build/esm/type/async-iterator/async-iterator.mjs", "../../@sinclair/typebox/build/esm/type/constructor/constructor.mjs", "../../@sinclair/typebox/build/esm/type/function/function.mjs", "../../@sinclair/typebox/build/esm/type/never/never.mjs", "../../@sinclair/typebox/build/esm/type/guard/kind.mjs", "../../@sinclair/typebox/build/esm/type/discard/discard.mjs", "../../@sinclair/typebox/build/esm/type/computed/computed.mjs", "../../@sinclair/typebox/build/esm/type/intersect/intersect-create.mjs", "../../@sinclair/typebox/build/esm/type/intersect/intersect-evaluated.mjs", "../../@sinclair/typebox/build/esm/type/intersect/intersect.mjs", "../../@sinclair/typebox/build/esm/type/union/union-create.mjs", "../../@sinclair/typebox/build/esm/type/union/union-evaluated.mjs", "../../@sinclair/typebox/build/esm/type/union/union.mjs", "../../@sinclair/typebox/build/esm/type/template-literal/parse.mjs", "../../@sinclair/typebox/build/esm/type/template-literal/finite.mjs", "../../@sinclair/typebox/build/esm/type/template-literal/generate.mjs", "../../@sinclair/typebox/build/esm/type/literal/literal.mjs", "../../@sinclair/typebox/build/esm/type/boolean/boolean.mjs", "../../@sinclair/typebox/build/esm/type/bigint/bigint.mjs", "../../@sinclair/typebox/build/esm/type/number/number.mjs", "../../@sinclair/typebox/build/esm/type/string/string.mjs", "../../@sinclair/typebox/build/esm/type/template-literal/syntax.mjs", "../../@sinclair/typebox/build/esm/type/patterns/patterns.mjs", "../../@sinclair/typebox/build/esm/type/template-literal/pattern.mjs", "../../@sinclair/typebox/build/esm/type/template-literal/union.mjs", "../../@sinclair/typebox/build/esm/type/template-literal/template-literal.mjs", "../../@sinclair/typebox/build/esm/type/indexed/indexed-property-keys.mjs", "../../@sinclair/typebox/build/esm/type/indexed/indexed-from-mapped-result.mjs", "../../@sinclair/typebox/build/esm/type/indexed/indexed.mjs", "../../@sinclair/typebox/build/esm/type/indexed/indexed-from-mapped-key.mjs", "../../@sinclair/typebox/build/esm/type/iterator/iterator.mjs", "../../@sinclair/typebox/build/esm/type/object/object.mjs", "../../@sinclair/typebox/build/esm/type/promise/promise.mjs", "../../@sinclair/typebox/build/esm/type/readonly/readonly.mjs", "../../@sinclair/typebox/build/esm/type/readonly/readonly-from-mapped-result.mjs", "../../@sinclair/typebox/build/esm/type/tuple/tuple.mjs", "../../@sinclair/typebox/build/esm/type/sets/set.mjs", "../../@sinclair/typebox/build/esm/type/mapped/mapped.mjs", "../../@sinclair/typebox/build/esm/type/optional/optional-from-mapped-result.mjs", "../../@sinclair/typebox/build/esm/type/optional/optional.mjs", "../../@sinclair/typebox/build/esm/type/ref/ref.mjs", "../../@sinclair/typebox/build/esm/type/keyof/keyof-property-keys.mjs", "../../@sinclair/typebox/build/esm/type/keyof/keyof-from-mapped-result.mjs", "../../@sinclair/typebox/build/esm/type/keyof/keyof.mjs", "../../@sinclair/typebox/build/esm/type/keyof/keyof-property-entries.mjs", "../../@sinclair/typebox/build/esm/type/extends/extends-undefined.mjs", "../../@sinclair/typebox/build/esm/type/any/any.mjs", "../../@sinclair/typebox/build/esm/type/unknown/unknown.mjs", "../../@sinclair/typebox/build/esm/type/guard/type.mjs", "../../@sinclair/typebox/build/esm/type/extends/extends-check.mjs", "../../@sinclair/typebox/build/esm/type/extends/extends-from-mapped-key.mjs", "../../@sinclair/typebox/build/esm/type/extends/extends.mjs", "../../@sinclair/typebox/build/esm/type/extends/extends-from-mapped-result.mjs"], "sourcesContent": ["/** A registry for user defined string formats */\nconst map = new Map();\n/** Returns the entries in this registry */\nexport function Entries() {\n    return new Map(map);\n}\n/** Clears all user defined string formats */\nexport function Clear() {\n    return map.clear();\n}\n/** Deletes a registered format */\nexport function Delete(format) {\n    return map.delete(format);\n}\n/** Returns true if the user defined string format exists */\nexport function Has(format) {\n    return map.has(format);\n}\n/** Sets a validation function for a user defined string format */\nexport function Set(format, func) {\n    map.set(format, func);\n}\n/** Gets a validation function for a user defined string format */\nexport function Get(format) {\n    return map.get(format);\n}\n", "/** A registry for user defined types */\nconst map = new Map();\n/** Returns the entries in this registry */\nexport function Entries() {\n    return new Map(map);\n}\n/** Clears all user defined types */\nexport function Clear() {\n    return map.clear();\n}\n/** Deletes a registered type */\nexport function Delete(kind) {\n    return map.delete(kind);\n}\n/** Returns true if this registry contains this kind */\nexport function Has(kind) {\n    return map.has(kind);\n}\n/** Sets a validation function for a user defined type */\nexport function Set(kind, func) {\n    map.set(kind, func);\n}\n/** Gets a custom validation function for a user defined type */\nexport function Get(kind) {\n    return map.get(kind);\n}\n", "// --------------------------------------------------------------------------\n// PropertyKey\n// --------------------------------------------------------------------------\n/** Returns true if this value has this property key */\nexport function HasPropertyKey(value, key) {\n    return key in value;\n}\n// --------------------------------------------------------------------------\n// Object Instances\n// --------------------------------------------------------------------------\n/** Returns true if this value is an async iterator */\nexport function IsAsyncIterator(value) {\n    return IsObject(value) && !IsArray(value) && !IsUint8Array(value) && Symbol.asyncIterator in value;\n}\n/** Returns true if this value is an array */\nexport function IsArray(value) {\n    return Array.isArray(value);\n}\n/** Returns true if this value is bigint */\nexport function IsBigInt(value) {\n    return typeof value === 'bigint';\n}\n/** Returns true if this value is a boolean */\nexport function IsBoolean(value) {\n    return typeof value === 'boolean';\n}\n/** Returns true if this value is a Date object */\nexport function IsDate(value) {\n    return value instanceof globalThis.Date;\n}\n/** Returns true if this value is a function */\nexport function IsFunction(value) {\n    return typeof value === 'function';\n}\n/** Returns true if this value is an iterator */\nexport function IsIterator(value) {\n    return IsObject(value) && !IsArray(value) && !IsUint8Array(value) && Symbol.iterator in value;\n}\n/** Returns true if this value is null */\nexport function IsNull(value) {\n    return value === null;\n}\n/** Returns true if this value is number */\nexport function IsNumber(value) {\n    return typeof value === 'number';\n}\n/** Returns true if this value is an object */\nexport function IsObject(value) {\n    return typeof value === 'object' && value !== null;\n}\n/** Returns true if this value is RegExp */\nexport function IsRegExp(value) {\n    return value instanceof globalThis.RegExp;\n}\n/** Returns true if this value is string */\nexport function IsString(value) {\n    return typeof value === 'string';\n}\n/** Returns true if this value is symbol */\nexport function IsSymbol(value) {\n    return typeof value === 'symbol';\n}\n/** Returns true if this value is a Uint8Array */\nexport function IsUint8Array(value) {\n    return value instanceof globalThis.Uint8Array;\n}\n/** Returns true if this value is undefined */\nexport function IsUndefined(value) {\n    return value === undefined;\n}\n", "import * as ValueGuard from '../guard/value.mjs';\nfunction ArrayType(value) {\n    return value.map((value) => Visit(value));\n}\nfunction DateType(value) {\n    return new Date(value.getTime());\n}\nfunction Uint8ArrayType(value) {\n    return new Uint8Array(value);\n}\nfunction RegExpType(value) {\n    return new RegExp(value.source, value.flags);\n}\nfunction ObjectType(value) {\n    const result = {};\n    for (const key of Object.getOwnPropertyNames(value)) {\n        result[key] = Visit(value[key]);\n    }\n    for (const key of Object.getOwnPropertySymbols(value)) {\n        result[key] = Visit(value[key]);\n    }\n    return result;\n}\n// prettier-ignore\nfunction Visit(value) {\n    return (ValueGuard.IsArray(value) ? ArrayType(value) :\n        ValueGuard.IsDate(value) ? DateType(value) :\n            ValueGuard.IsUint8Array(value) ? Uint8ArrayType(value) :\n                ValueGuard.IsRegExp(value) ? RegExpType(value) :\n                    ValueGuard.IsObject(value) ? ObjectType(value) :\n                        value);\n}\n/** Clones a value */\nexport function Clone(value) {\n    return Visit(value);\n}\n", "// --------------------------------------------------------------------------\n// Iterators\n// --------------------------------------------------------------------------\n/** Returns true if this value is an async iterator */\nexport function IsAsyncIterator(value) {\n    return IsObject(value) && globalThis.Symbol.asyncIterator in value;\n}\n/** Returns true if this value is an iterator */\nexport function IsIterator(value) {\n    return IsObject(value) && globalThis.Symbol.iterator in value;\n}\n// --------------------------------------------------------------------------\n// Object Instances\n// --------------------------------------------------------------------------\n/** Returns true if this value is not an instance of a class */\nexport function IsStandardObject(value) {\n    return IsObject(value) && (globalThis.Object.getPrototypeOf(value) === Object.prototype || globalThis.Object.getPrototypeOf(value) === null);\n}\n/** Returns true if this value is an instance of a class */\nexport function IsInstanceObject(value) {\n    return IsObject(value) && !IsArray(value) && IsFunction(value.constructor) && value.constructor.name !== 'Object';\n}\n// --------------------------------------------------------------------------\n// JavaScript\n// --------------------------------------------------------------------------\n/** Returns true if this value is a Promise */\nexport function IsPromise(value) {\n    return value instanceof globalThis.Promise;\n}\n/** Returns true if this value is a Date */\nexport function IsDate(value) {\n    return value instanceof Date && globalThis.Number.isFinite(value.getTime());\n}\n/** Returns true if this value is an instance of Map<K, T> */\nexport function IsMap(value) {\n    return value instanceof globalThis.Map;\n}\n/** Returns true if this value is an instance of Set<T> */\nexport function IsSet(value) {\n    return value instanceof globalThis.Set;\n}\n/** Returns true if this value is RegExp */\nexport function IsRegExp(value) {\n    return value instanceof globalThis.RegExp;\n}\n/** Returns true if this value is a typed array */\nexport function IsTypedArray(value) {\n    return globalThis.ArrayBuffer.isView(value);\n}\n/** Returns true if the value is a Int8Array */\nexport function IsInt8Array(value) {\n    return value instanceof globalThis.Int8Array;\n}\n/** Returns true if the value is a Uint8Array */\nexport function IsUint8Array(value) {\n    return value instanceof globalThis.Uint8Array;\n}\n/** Returns true if the value is a Uint8ClampedArray */\nexport function IsUint8ClampedArray(value) {\n    return value instanceof globalThis.Uint8ClampedArray;\n}\n/** Returns true if the value is a Int16Array */\nexport function IsInt16Array(value) {\n    return value instanceof globalThis.Int16Array;\n}\n/** Returns true if the value is a Uint16Array */\nexport function IsUint16Array(value) {\n    return value instanceof globalThis.Uint16Array;\n}\n/** Returns true if the value is a Int32Array */\nexport function IsInt32Array(value) {\n    return value instanceof globalThis.Int32Array;\n}\n/** Returns true if the value is a Uint32Array */\nexport function IsUint32Array(value) {\n    return value instanceof globalThis.Uint32Array;\n}\n/** Returns true if the value is a Float32Array */\nexport function IsFloat32Array(value) {\n    return value instanceof globalThis.Float32Array;\n}\n/** Returns true if the value is a Float64Array */\nexport function IsFloat64Array(value) {\n    return value instanceof globalThis.Float64Array;\n}\n/** Returns true if the value is a BigInt64Array */\nexport function IsBigInt64Array(value) {\n    return value instanceof globalThis.BigInt64Array;\n}\n/** Returns true if the value is a BigUint64Array */\nexport function IsBigUint64Array(value) {\n    return value instanceof globalThis.BigUint64Array;\n}\n// --------------------------------------------------------------------------\n// PropertyKey\n// --------------------------------------------------------------------------\n/** Returns true if this value has this property key */\nexport function HasPropertyKey(value, key) {\n    return key in value;\n}\n// --------------------------------------------------------------------------\n// Standard\n// --------------------------------------------------------------------------\n/** Returns true of this value is an object type */\nexport function IsObject(value) {\n    return value !== null && typeof value === 'object';\n}\n/** Returns true if this value is an array, but not a typed array */\nexport function IsArray(value) {\n    return globalThis.Array.isArray(value) && !globalThis.ArrayBuffer.isView(value);\n}\n/** Returns true if this value is an undefined */\nexport function IsUndefined(value) {\n    return value === undefined;\n}\n/** Returns true if this value is an null */\nexport function IsNull(value) {\n    return value === null;\n}\n/** Returns true if this value is an boolean */\nexport function IsBoolean(value) {\n    return typeof value === 'boolean';\n}\n/** Returns true if this value is an number */\nexport function IsNumber(value) {\n    return typeof value === 'number';\n}\n/** Returns true if this value is an integer */\nexport function IsInteger(value) {\n    return globalThis.Number.isInteger(value);\n}\n/** Returns true if this value is bigint */\nexport function IsBigInt(value) {\n    return typeof value === 'bigint';\n}\n/** Returns true if this value is string */\nexport function IsString(value) {\n    return typeof value === 'string';\n}\n/** Returns true if this value is a function */\nexport function IsFunction(value) {\n    return typeof value === 'function';\n}\n/** Returns true if this value is a symbol */\nexport function IsSymbol(value) {\n    return typeof value === 'symbol';\n}\n/** Returns true if this value is a value type such as number, string, boolean */\nexport function IsValueType(value) {\n    // prettier-ignore\n    return (IsBigInt(value) ||\n        IsBoolean(value) ||\n        IsNull(value) ||\n        IsNumber(value) ||\n        IsString(value) ||\n        IsSymbol(value) ||\n        IsUndefined(value));\n}\n", "import { IsO<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUndefined } from '../value/guard/index.mjs';\nexport var TypeSystemPolicy;\n(function (TypeSystemPolicy) {\n    // ------------------------------------------------------------------\n    // TypeSystemPolicy: Instancing\n    // ------------------------------------------------------------------\n    /**\n     * Configures the instantiation behavior of TypeBox types. The `default` option assigns raw JavaScript\n     * references for embedded types, which may cause side effects if type properties are explicitly updated\n     * outside the TypeBox type builder. The `clone` option creates copies of any shared types upon creation,\n     * preventing unintended side effects. The `freeze` option applies `Object.freeze()` to the type, making\n     * it fully readonly and immutable. Implementations should use `default` whenever possible, as it is the\n     * fastest way to instantiate types. The default setting is `default`.\n     */\n    TypeSystemPolicy.InstanceMode = 'default';\n    // ------------------------------------------------------------------\n    // TypeSystemPolicy: Checking\n    // ------------------------------------------------------------------\n    /** Sets whether TypeBox should assert optional properties using the TypeScript `exactOptionalPropertyTypes` assertion policy. The default is `false` */\n    TypeSystemPolicy.ExactOptionalPropertyTypes = false;\n    /** Sets whether arrays should be treated as a kind of objects. The default is `false` */\n    TypeSystemPolicy.AllowArrayObject = false;\n    /** Sets whether `NaN` or `Infinity` should be treated as valid numeric values. The default is `false` */\n    TypeSystemPolicy.AllowNaN = false;\n    /** Sets whether `null` should validate for void types. The default is `false` */\n    TypeSystemPolicy.AllowNullVoid = false;\n    /** Checks this value using the ExactOptionalPropertyTypes policy */\n    function IsExactOptionalProperty(value, key) {\n        return TypeSystemPolicy.ExactOptionalPropertyTypes ? key in value : value[key] !== undefined;\n    }\n    TypeSystemPolicy.IsExactOptionalProperty = IsExactOptionalProperty;\n    /** Checks this value using the AllowArrayObjects policy */\n    function IsObjectLike(value) {\n        const isObject = IsObject(value);\n        return TypeSystemPolicy.AllowArrayObject ? isObject : isObject && !IsArray(value);\n    }\n    TypeSystemPolicy.IsObjectLike = IsObjectLike;\n    /** Checks this value as a record using the AllowArrayObjects policy */\n    function IsRecordLike(value) {\n        return IsObjectLike(value) && !(value instanceof Date) && !(value instanceof Uint8Array);\n    }\n    TypeSystemPolicy.IsRecordLike = IsRecordLike;\n    /** Checks this value using the AllowNaN policy */\n    function IsNumberLike(value) {\n        return TypeSystemPolicy.AllowNaN ? IsNumber(value) : Number.isFinite(value);\n    }\n    TypeSystemPolicy.IsNumberLike = IsNumberLike;\n    /** Checks this value using the AllowVoidNull policy */\n    function IsVoidLike(value) {\n        const isUndefined = IsUndefined(value);\n        return TypeSystemPolicy.AllowNullVoid ? isUndefined || value === null : isUndefined;\n    }\n    TypeSystemPolicy.IsVoidLike = IsVoidLike;\n})(TypeSystemPolicy || (TypeSystemPolicy = {}));\n", "import * as ValueGuard from '../guard/value.mjs';\nfunction ImmutableArray(value) {\n    return globalThis.Object.freeze(value).map((value) => Immutable(value));\n}\nfunction ImmutableDate(value) {\n    return value;\n}\nfunction ImmutableUint8Array(value) {\n    return value;\n}\nfunction ImmutableRegExp(value) {\n    return value;\n}\nfunction ImmutableObject(value) {\n    const result = {};\n    for (const key of Object.getOwnPropertyNames(value)) {\n        result[key] = Immutable(value[key]);\n    }\n    for (const key of Object.getOwnPropertySymbols(value)) {\n        result[key] = Immutable(value[key]);\n    }\n    return globalThis.Object.freeze(result);\n}\n/** Specialized deep immutable value. Applies freeze recursively to the given value */\n// prettier-ignore\nexport function Immutable(value) {\n    return (ValueGuard.IsArray(value) ? ImmutableArray(value) :\n        ValueGuard.IsDate(value) ? ImmutableDate(value) :\n            ValueGuard.IsUint8Array(value) ? ImmutableUint8Array(value) :\n                ValueGuard.IsRegExp(value) ? ImmutableRegExp(value) :\n                    ValueGuard.IsObject(value) ? ImmutableObject(value) :\n                        value);\n}\n", "import { TypeSystemPolicy } from '../../system/policy.mjs';\nimport { Immutable } from './immutable.mjs';\nimport { Clone } from '../clone/value.mjs';\n/** Creates TypeBox schematics using the configured InstanceMode */\nexport function CreateType(schema, options) {\n    const result = options !== undefined ? { ...options, ...schema } : schema;\n    switch (TypeSystemPolicy.InstanceMode) {\n        case 'freeze':\n            return Immutable(result);\n        case 'clone':\n            return Clone(result);\n        default:\n            return result;\n    }\n}\n", "/** Symbol key applied to transform types */\nexport const TransformKind = Symbol.for('TypeBox.Transform');\n/** Symbol key applied to readonly types */\nexport const ReadonlyKind = Symbol.for('TypeBox.Readonly');\n/** Symbol key applied to optional types */\nexport const OptionalKind = Symbol.for('TypeBox.Optional');\n/** Symbol key applied to types */\nexport const Hint = Symbol.for('TypeBox.Hint');\n/** Symbol key applied to types */\nexport const Kind = Symbol.for('TypeBox.Kind');\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates a Unsafe type that will infers as the generic argument T */\nexport function Unsafe(options = {}) {\n    return CreateType({ [Kind]: options[Kind] ?? 'Unsafe' }, options);\n}\n", "/** The base Error type thrown for all TypeBox exceptions  */\nexport class TypeBoxError extends Error {\n    constructor(message) {\n        super(message);\n    }\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n// prettier-ignore\nexport function MappedKey(T) {\n    return CreateType({\n        [Kind]: 'MappedKey',\n        keys: T\n    });\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n// prettier-ignore\nexport function MappedResult(properties) {\n    return CreateType({\n        [Kind]: 'MappedResult',\n        properties\n    });\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates an Array type */\nexport function Array(items, options) {\n    return CreateType({ [Kind]: 'Array', type: 'array', items }, options);\n}\n", "import { Kind } from '../symbols/index.mjs';\nimport { CreateType } from '../create/type.mjs';\n/** `[JavaScript]` Creates a AsyncIterator type */\nexport function AsyncIterator(items, options) {\n    return CreateType({ [Kind]: 'AsyncIterator', type: 'AsyncIterator', items }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[JavaScript]` Creates a Constructor type */\nexport function Constructor(parameters, returns, options) {\n    return CreateType({ [Kind]: 'Constructor', type: 'Constructor', parameters, returns }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[JavaScript]` Creates a Function type */\nexport function Function(parameters, returns, options) {\n    return CreateType({ [Kind]: 'Function', type: 'Function', parameters, returns }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates a Never type */\nexport function Never(options) {\n    return CreateType({ [Kind]: 'Never', not: {} }, options);\n}\n", "import * as ValueGuard from './value.mjs';\nimport { Kind, Hint, TransformKind, ReadonlyKind, OptionalKind } from '../symbols/index.mjs';\n/** `[Kind-Only]` Returns true if this value has a Readonly symbol */\nexport function IsReadonly(value) {\n    return ValueGuard.IsObject(value) && value[ReadonlyKind] === 'Readonly';\n}\n/** `[Kind-Only]` Returns true if this value has a Optional symbol */\nexport function IsOptional(value) {\n    return ValueGuard.IsObject(value) && value[OptionalKind] === 'Optional';\n}\n/** `[Kind-Only]` Returns true if the given value is TAny */\nexport function IsAny(value) {\n    return IsKindOf(value, 'Any');\n}\n/** `[Kind-Only]` Returns true if the given value is TArgument */\nexport function IsArgument(value) {\n    return IsKindOf(value, 'Argument');\n}\n/** `[Kind-Only]` Returns true if the given value is TArray */\nexport function IsArray(value) {\n    return IsKindOf(value, 'Array');\n}\n/** `[Kind-Only]` Returns true if the given value is TAsyncIterator */\nexport function IsAsyncIterator(value) {\n    return IsKindOf(value, 'AsyncIterator');\n}\n/** `[Kind-Only]` Returns true if the given value is TBigInt */\nexport function IsBigInt(value) {\n    return IsKindOf(value, 'BigInt');\n}\n/** `[Kind-Only]` Returns true if the given value is TBoolean */\nexport function IsBoolean(value) {\n    return IsKindOf(value, 'Boolean');\n}\n/** `[Kind-Only]` Returns true if the given value is TComputed */\nexport function IsComputed(value) {\n    return IsKindOf(value, 'Computed');\n}\n/** `[Kind-Only]` Returns true if the given value is TConstructor */\nexport function IsConstructor(value) {\n    return IsKindOf(value, 'Constructor');\n}\n/** `[Kind-Only]` Returns true if the given value is TDate */\nexport function IsDate(value) {\n    return IsKindOf(value, 'Date');\n}\n/** `[Kind-Only]` Returns true if the given value is TFunction */\nexport function IsFunction(value) {\n    return IsKindOf(value, 'Function');\n}\n/** `[Kind-Only]` Returns true if the given value is TInteger */\nexport function IsImport(value) {\n    return IsKindOf(value, 'Import');\n}\n/** `[Kind-Only]` Returns true if the given value is TInteger */\nexport function IsInteger(value) {\n    return IsKindOf(value, 'Integer');\n}\n/** `[Kind-Only]` Returns true if the given schema is TProperties */\nexport function IsProperties(value) {\n    return ValueGuard.IsObject(value);\n}\n/** `[Kind-Only]` Returns true if the given value is TIntersect */\nexport function IsIntersect(value) {\n    return IsKindOf(value, 'Intersect');\n}\n/** `[Kind-Only]` Returns true if the given value is TIterator */\nexport function IsIterator(value) {\n    return IsKindOf(value, 'Iterator');\n}\n/** `[Kind-Only]` Returns true if the given value is a TKind with the given name. */\nexport function IsKindOf(value, kind) {\n    return ValueGuard.IsObject(value) && Kind in value && value[Kind] === kind;\n}\n/** `[Kind-Only]` Returns true if the given value is TLiteral<string> */\nexport function IsLiteralString(value) {\n    return IsLiteral(value) && ValueGuard.IsString(value.const);\n}\n/** `[Kind-Only]` Returns true if the given value is TLiteral<number> */\nexport function IsLiteralNumber(value) {\n    return IsLiteral(value) && ValueGuard.IsNumber(value.const);\n}\n/** `[Kind-Only]` Returns true if the given value is TLiteral<boolean> */\nexport function IsLiteralBoolean(value) {\n    return IsLiteral(value) && ValueGuard.IsBoolean(value.const);\n}\n/** `[Kind-Only]` Returns true if the given value is TLiteralValue */\nexport function IsLiteralValue(value) {\n    return ValueGuard.IsBoolean(value) || ValueGuard.IsNumber(value) || ValueGuard.IsString(value);\n}\n/** `[Kind-Only]` Returns true if the given value is TLiteral */\nexport function IsLiteral(value) {\n    return IsKindOf(value, 'Literal');\n}\n/** `[Kind-Only]` Returns true if the given value is a TMappedKey */\nexport function IsMappedKey(value) {\n    return IsKindOf(value, 'MappedKey');\n}\n/** `[Kind-Only]` Returns true if the given value is TMappedResult */\nexport function IsMappedResult(value) {\n    return IsKindOf(value, 'MappedResult');\n}\n/** `[Kind-Only]` Returns true if the given value is TNever */\nexport function IsNever(value) {\n    return IsKindOf(value, 'Never');\n}\n/** `[Kind-Only]` Returns true if the given value is TNot */\nexport function IsNot(value) {\n    return IsKindOf(value, 'Not');\n}\n/** `[Kind-Only]` Returns true if the given value is TNull */\nexport function IsNull(value) {\n    return IsKindOf(value, 'Null');\n}\n/** `[Kind-Only]` Returns true if the given value is TNumber */\nexport function IsNumber(value) {\n    return IsKindOf(value, 'Number');\n}\n/** `[Kind-Only]` Returns true if the given value is TObject */\nexport function IsObject(value) {\n    return IsKindOf(value, 'Object');\n}\n/** `[Kind-Only]` Returns true if the given value is TPromise */\nexport function IsPromise(value) {\n    return IsKindOf(value, 'Promise');\n}\n/** `[Kind-Only]` Returns true if the given value is TRecord */\nexport function IsRecord(value) {\n    return IsKindOf(value, 'Record');\n}\n/** `[Kind-Only]` Returns true if this value is TRecursive */\nexport function IsRecursive(value) {\n    return ValueGuard.IsObject(value) && Hint in value && value[Hint] === 'Recursive';\n}\n/** `[Kind-Only]` Returns true if the given value is TRef */\nexport function IsRef(value) {\n    return IsKindOf(value, 'Ref');\n}\n/** `[Kind-Only]` Returns true if the given value is TRegExp */\nexport function IsRegExp(value) {\n    return IsKindOf(value, 'RegExp');\n}\n/** `[Kind-Only]` Returns true if the given value is TString */\nexport function IsString(value) {\n    return IsKindOf(value, 'String');\n}\n/** `[Kind-Only]` Returns true if the given value is TSymbol */\nexport function IsSymbol(value) {\n    return IsKindOf(value, 'Symbol');\n}\n/** `[Kind-Only]` Returns true if the given value is TTemplateLiteral */\nexport function IsTemplateLiteral(value) {\n    return IsKindOf(value, 'TemplateLiteral');\n}\n/** `[Kind-Only]` Returns true if the given value is TThis */\nexport function IsThis(value) {\n    return IsKindOf(value, 'This');\n}\n/** `[Kind-Only]` Returns true of this value is TTransform */\nexport function IsTransform(value) {\n    return ValueGuard.IsObject(value) && TransformKind in value;\n}\n/** `[Kind-Only]` Returns true if the given value is TTuple */\nexport function IsTuple(value) {\n    return IsKindOf(value, 'Tuple');\n}\n/** `[Kind-Only]` Returns true if the given value is TUndefined */\nexport function IsUndefined(value) {\n    return IsKindOf(value, 'Undefined');\n}\n/** `[Kind-Only]` Returns true if the given value is TUnion */\nexport function IsUnion(value) {\n    return IsKindOf(value, 'Union');\n}\n/** `[Kind-Only]` Returns true if the given value is TUint8Array */\nexport function IsUint8Array(value) {\n    return IsKindOf(value, 'Uint8Array');\n}\n/** `[Kind-Only]` Returns true if the given value is TUnknown */\nexport function IsUnknown(value) {\n    return IsKindOf(value, 'Unknown');\n}\n/** `[Kind-Only]` Returns true if the given value is a raw TUnsafe */\nexport function IsUnsafe(value) {\n    return IsKindOf(value, 'Unsafe');\n}\n/** `[Kind-Only]` Returns true if the given value is TVoid */\nexport function IsVoid(value) {\n    return IsKindOf(value, 'Void');\n}\n/** `[Kind-Only]` Returns true if the given value is TKind */\nexport function IsKind(value) {\n    return ValueGuard.IsObject(value) && Kind in value && ValueGuard.IsString(value[Kind]);\n}\n/** `[Kind-Only]` Returns true if the given value is TSchema */\nexport function IsSchema(value) {\n    // prettier-ignore\n    return (IsAny(value) ||\n        IsArgument(value) ||\n        IsArray(value) ||\n        IsBoolean(value) ||\n        IsBigInt(value) ||\n        IsAsyncIterator(value) ||\n        IsComputed(value) ||\n        IsConstructor(value) ||\n        IsDate(value) ||\n        IsFunction(value) ||\n        IsInteger(value) ||\n        IsIntersect(value) ||\n        IsIterator(value) ||\n        IsLiteral(value) ||\n        IsMappedKey(value) ||\n        IsMappedResult(value) ||\n        IsNever(value) ||\n        IsNot(value) ||\n        IsNull(value) ||\n        IsNumber(value) ||\n        IsObject(value) ||\n        IsPromise(value) ||\n        IsRecord(value) ||\n        IsRef(value) ||\n        IsRegExp(value) ||\n        IsString(value) ||\n        IsSymbol(value) ||\n        IsTemplateLiteral(value) ||\n        IsThis(value) ||\n        IsTuple(value) ||\n        IsUndefined(value) ||\n        IsUnion(value) ||\n        IsUint8Array(value) ||\n        IsUnknown(value) ||\n        IsUnsafe(value) ||\n        IsVoid(value) ||\n        IsKind(value));\n}\n", "function DiscardKey(value, key) {\n    const { [key]: _, ...rest } = value;\n    return rest;\n}\n/** Discards property keys from the given value. This function returns a shallow Clone. */\nexport function Discard(value, keys) {\n    return keys.reduce((acc, key) => DiscardKey(acc, key), value);\n}\n", "import { CreateType } from '../create/index.mjs';\nimport { Kind } from '../symbols/symbols.mjs';\n/** `[Internal]` Creates a deferred computed type. This type is used exclusively in modules to defer resolution of computable types that contain interior references  */\nexport function Computed(target, parameters, options) {\n    return CreateType({ [Kind]: 'Computed', target, parameters }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsObject, IsSchema } from '../guard/kind.mjs';\n// ------------------------------------------------------------------\n// IntersectCreate\n// ------------------------------------------------------------------\n// prettier-ignore\nexport function IntersectCreate(T, options = {}) {\n    const allObjects = T.every((schema) => IsObject(schema));\n    const clonedUnevaluatedProperties = IsSchema(options.unevaluatedProperties)\n        ? { unevaluatedProperties: options.unevaluatedProperties }\n        : {};\n    return CreateType((options.unevaluatedProperties === false || IsSchema(options.unevaluatedProperties) || allObjects\n        ? { ...clonedUnevaluatedProperties, [Kind]: 'Intersect', type: 'object', allOf: T }\n        : { ...clonedUnevaluatedProperties, [Kind]: 'Intersect', allOf: T }), options);\n}\n", "import { OptionalKind } from '../symbols/index.mjs';\nimport { CreateType } from '../create/type.mjs';\nimport { Discard } from '../discard/index.mjs';\nimport { Never } from '../never/index.mjs';\nimport { Optional } from '../optional/index.mjs';\nimport { IntersectCreate } from './intersect-create.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsOptional, IsTransform } from '../guard/kind.mjs';\n// prettier-ignore\nfunction IsIntersectOptional(types) {\n    return types.every(left => IsOptional(left));\n}\n// prettier-ignore\nfunction RemoveOptionalFromType(type) {\n    return (Discard(type, [OptionalKind]));\n}\n// prettier-ignore\nfunction RemoveOptionalFromRest(types) {\n    return types.map(left => IsOptional(left) ? RemoveOptionalFromType(left) : left);\n}\n// prettier-ignore\nfunction ResolveIntersect(types, options) {\n    return (IsIntersectOptional(types)\n        ? Optional(IntersectCreate(RemoveOptionalFromRest(types), options))\n        : IntersectCreate(RemoveOptionalFromRest(types), options));\n}\n/** `[Json]` Creates an evaluated Intersect type */\nexport function IntersectEvaluated(types, options = {}) {\n    if (types.length === 1)\n        return CreateType(types[0], options);\n    if (types.length === 0)\n        return Never(options);\n    if (types.some((schema) => IsTransform(schema)))\n        throw new Error('Cannot intersect transform types');\n    return ResolveIntersect(types, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Never } from '../never/index.mjs';\nimport { IntersectCreate } from './intersect-create.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsTransform } from '../guard/kind.mjs';\n/** `[<PERSON><PERSON>]` Creates an evaluated Intersect type */\nexport function Intersect(types, options) {\n    if (types.length === 1)\n        return CreateType(types[0], options);\n    if (types.length === 0)\n        return Never(options);\n    if (types.some((schema) => IsTransform(schema)))\n        throw new Error('Cannot intersect transform types');\n    return IntersectCreate(types, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\nexport function UnionCreate(T, options) {\n    return CreateType({ [Kind]: 'Union', anyOf: T }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { OptionalKind } from '../symbols/index.mjs';\nimport { Discard } from '../discard/index.mjs';\nimport { Never } from '../never/index.mjs';\nimport { Optional } from '../optional/index.mjs';\nimport { UnionCreate } from './union-create.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsOptional } from '../guard/kind.mjs';\n// prettier-ignore\nfunction IsUnionOptional(types) {\n    return types.some(type => IsOptional(type));\n}\n// prettier-ignore\nfunction RemoveOptionalFromRest(types) {\n    return types.map(left => IsOptional(left) ? RemoveOptionalFromType(left) : left);\n}\n// prettier-ignore\nfunction RemoveOptionalFromType(T) {\n    return (Discard(T, [OptionalKind]));\n}\n// prettier-ignore\nfunction ResolveUnion(types, options) {\n    const isOptional = IsUnionOptional(types);\n    return (isOptional\n        ? Optional(UnionCreate(RemoveOptionalFromRest(types), options))\n        : UnionCreate(RemoveOptionalFromRest(types), options));\n}\n/** `[Json]` Creates an evaluated Union type */\nexport function UnionEvaluated(T, options) {\n    // prettier-ignore\n    return (T.length === 1 ? CreateType(T[0], options) :\n        T.length === 0 ? Never(options) :\n            ResolveUnion(T, options));\n}\n", "import { Never } from '../never/index.mjs';\nimport { CreateType } from '../create/type.mjs';\nimport { UnionCreate } from './union-create.mjs';\n/** `[<PERSON><PERSON>]` Creates a Union type */\nexport function Union(types, options) {\n    // prettier-ignore\n    return (types.length === 0 ? Never(options) :\n        types.length === 1 ? CreateType(types[0], options) :\n            UnionCreate(types, options));\n}\n", "import { TypeBoxError } from '../error/index.mjs';\n// ------------------------------------------------------------------\n// TemplateLiteralParserError\n// ------------------------------------------------------------------\nexport class TemplateLiteralParserError extends TypeBoxError {\n}\n// -------------------------------------------------------------------\n// Unescape\n//\n// Unescape for these control characters specifically. Note that this\n// function is only called on non union group content, and where we\n// still want to allow the user to embed control characters in that\n// content. For review.\n// -------------------------------------------------------------------\n// prettier-ignore\nfunction Unescape(pattern) {\n    return pattern\n        .replace(/\\\\\\$/g, '$')\n        .replace(/\\\\\\*/g, '*')\n        .replace(/\\\\\\^/g, '^')\n        .replace(/\\\\\\|/g, '|')\n        .replace(/\\\\\\(/g, '(')\n        .replace(/\\\\\\)/g, ')');\n}\n// -------------------------------------------------------------------\n// Control Characters\n// -------------------------------------------------------------------\nfunction IsNonEscaped(pattern, index, char) {\n    return pattern[index] === char && pattern.charCodeAt(index - 1) !== 92;\n}\nfunction IsOpenParen(pattern, index) {\n    return IsNonEscaped(pattern, index, '(');\n}\nfunction IsCloseParen(pattern, index) {\n    return IsNonEscaped(pattern, index, ')');\n}\nfunction IsSeparator(pattern, index) {\n    return IsNonEscaped(pattern, index, '|');\n}\n// -------------------------------------------------------------------\n// Control Groups\n// -------------------------------------------------------------------\nfunction IsGroup(pattern) {\n    if (!(IsOpenParen(pattern, 0) && IsCloseParen(pattern, pattern.length - 1)))\n        return false;\n    let count = 0;\n    for (let index = 0; index < pattern.length; index++) {\n        if (IsOpenParen(pattern, index))\n            count += 1;\n        if (IsCloseParen(pattern, index))\n            count -= 1;\n        if (count === 0 && index !== pattern.length - 1)\n            return false;\n    }\n    return true;\n}\n// prettier-ignore\nfunction InGroup(pattern) {\n    return pattern.slice(1, pattern.length - 1);\n}\n// prettier-ignore\nfunction IsPrecedenceOr(pattern) {\n    let count = 0;\n    for (let index = 0; index < pattern.length; index++) {\n        if (IsOpenParen(pattern, index))\n            count += 1;\n        if (IsCloseParen(pattern, index))\n            count -= 1;\n        if (IsSeparator(pattern, index) && count === 0)\n            return true;\n    }\n    return false;\n}\n// prettier-ignore\nfunction IsPrecedenceAnd(pattern) {\n    for (let index = 0; index < pattern.length; index++) {\n        if (IsOpenParen(pattern, index))\n            return true;\n    }\n    return false;\n}\n// prettier-ignore\nfunction Or(pattern) {\n    let [count, start] = [0, 0];\n    const expressions = [];\n    for (let index = 0; index < pattern.length; index++) {\n        if (IsOpenParen(pattern, index))\n            count += 1;\n        if (IsCloseParen(pattern, index))\n            count -= 1;\n        if (IsSeparator(pattern, index) && count === 0) {\n            const range = pattern.slice(start, index);\n            if (range.length > 0)\n                expressions.push(TemplateLiteralParse(range));\n            start = index + 1;\n        }\n    }\n    const range = pattern.slice(start);\n    if (range.length > 0)\n        expressions.push(TemplateLiteralParse(range));\n    if (expressions.length === 0)\n        return { type: 'const', const: '' };\n    if (expressions.length === 1)\n        return expressions[0];\n    return { type: 'or', expr: expressions };\n}\n// prettier-ignore\nfunction And(pattern) {\n    function Group(value, index) {\n        if (!IsOpenParen(value, index))\n            throw new TemplateLiteralParserError(`TemplateLiteralParser: Index must point to open parens`);\n        let count = 0;\n        for (let scan = index; scan < value.length; scan++) {\n            if (IsOpenParen(value, scan))\n                count += 1;\n            if (IsCloseParen(value, scan))\n                count -= 1;\n            if (count === 0)\n                return [index, scan];\n        }\n        throw new TemplateLiteralParserError(`TemplateLiteralParser: Unclosed group parens in expression`);\n    }\n    function Range(pattern, index) {\n        for (let scan = index; scan < pattern.length; scan++) {\n            if (IsOpenParen(pattern, scan))\n                return [index, scan];\n        }\n        return [index, pattern.length];\n    }\n    const expressions = [];\n    for (let index = 0; index < pattern.length; index++) {\n        if (IsOpenParen(pattern, index)) {\n            const [start, end] = Group(pattern, index);\n            const range = pattern.slice(start, end + 1);\n            expressions.push(TemplateLiteralParse(range));\n            index = end;\n        }\n        else {\n            const [start, end] = Range(pattern, index);\n            const range = pattern.slice(start, end);\n            if (range.length > 0)\n                expressions.push(TemplateLiteralParse(range));\n            index = end - 1;\n        }\n    }\n    return ((expressions.length === 0) ? { type: 'const', const: '' } :\n        (expressions.length === 1) ? expressions[0] :\n            { type: 'and', expr: expressions });\n}\n// ------------------------------------------------------------------\n// TemplateLiteralParse\n// ------------------------------------------------------------------\n/** Parses a pattern and returns an expression tree */\nexport function TemplateLiteralParse(pattern) {\n    // prettier-ignore\n    return (IsGroup(pattern) ? TemplateLiteralParse(InGroup(pattern)) :\n        IsPrecedenceOr(pattern) ? Or(pattern) :\n            IsPrecedenceAnd(pattern) ? And(pattern) :\n                { type: 'const', const: Unescape(pattern) });\n}\n// ------------------------------------------------------------------\n// TemplateLiteralParseExact\n// ------------------------------------------------------------------\n/** Parses a pattern and strips forward and trailing ^ and $ */\nexport function TemplateLiteralParseExact(pattern) {\n    return TemplateLiteralParse(pattern.slice(1, pattern.length - 1));\n}\n", "import { TemplateLiteralParseExact } from './parse.mjs';\nimport { TypeBoxError } from '../error/index.mjs';\n// ------------------------------------------------------------------\n// TemplateLiteralFiniteError\n// ------------------------------------------------------------------\nexport class TemplateLiteralFiniteError extends TypeBoxError {\n}\n// ------------------------------------------------------------------\n// IsTemplateLiteralFiniteCheck\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction IsNumberExpression(expression) {\n    return (expression.type === 'or' &&\n        expression.expr.length === 2 &&\n        expression.expr[0].type === 'const' &&\n        expression.expr[0].const === '0' &&\n        expression.expr[1].type === 'const' &&\n        expression.expr[1].const === '[1-9][0-9]*');\n}\n// prettier-ignore\nfunction IsBooleanExpression(expression) {\n    return (expression.type === 'or' &&\n        expression.expr.length === 2 &&\n        expression.expr[0].type === 'const' &&\n        expression.expr[0].const === 'true' &&\n        expression.expr[1].type === 'const' &&\n        expression.expr[1].const === 'false');\n}\n// prettier-ignore\nfunction IsStringExpression(expression) {\n    return expression.type === 'const' && expression.const === '.*';\n}\n// ------------------------------------------------------------------\n// IsTemplateLiteralExpressionFinite\n// ------------------------------------------------------------------\n// prettier-ignore\nexport function IsTemplateLiteralExpressionFinite(expression) {\n    return (IsNumberExpression(expression) || IsStringExpression(expression) ? false :\n        IsBooleanExpression(expression) ? true :\n            (expression.type === 'and') ? expression.expr.every((expr) => IsTemplateLiteralExpressionFinite(expr)) :\n                (expression.type === 'or') ? expression.expr.every((expr) => IsTemplateLiteralExpressionFinite(expr)) :\n                    (expression.type === 'const') ? true :\n                        (() => { throw new TemplateLiteralFiniteError(`Unknown expression type`); })());\n}\n/** Returns true if this TemplateLiteral resolves to a finite set of values */\nexport function IsTemplateLiteralFinite(schema) {\n    const expression = TemplateLiteralParseExact(schema.pattern);\n    return IsTemplateLiteralExpressionFinite(expression);\n}\n", "import { IsTemplateLiteralExpressionFinite } from './finite.mjs';\nimport { TemplateLiteralParseExact } from './parse.mjs';\nimport { TypeBoxError } from '../error/index.mjs';\n// ------------------------------------------------------------------\n// TemplateLiteralGenerateError\n// ------------------------------------------------------------------\nexport class TemplateLiteralGenerateError extends TypeBoxError {\n}\n// ------------------------------------------------------------------\n// TemplateLiteralExpressionGenerate\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction* GenerateReduce(buffer) {\n    if (buffer.length === 1)\n        return yield* buffer[0];\n    for (const left of buffer[0]) {\n        for (const right of GenerateReduce(buffer.slice(1))) {\n            yield `${left}${right}`;\n        }\n    }\n}\n// prettier-ignore\nfunction* GenerateAnd(expression) {\n    return yield* GenerateReduce(expression.expr.map((expr) => [...TemplateLiteralExpressionGenerate(expr)]));\n}\n// prettier-ignore\nfunction* GenerateOr(expression) {\n    for (const expr of expression.expr)\n        yield* TemplateLiteralExpressionGenerate(expr);\n}\n// prettier-ignore\nfunction* GenerateConst(expression) {\n    return yield expression.const;\n}\nexport function* TemplateLiteralExpressionGenerate(expression) {\n    return expression.type === 'and'\n        ? yield* GenerateAnd(expression)\n        : expression.type === 'or'\n            ? yield* GenerateOr(expression)\n            : expression.type === 'const'\n                ? yield* GenerateConst(expression)\n                : (() => {\n                    throw new TemplateLiteralGenerateError('Unknown expression');\n                })();\n}\n/** Generates a tuple of strings from the given TemplateLiteral. Returns an empty tuple if infinite. */\nexport function TemplateLiteralGenerate(schema) {\n    const expression = TemplateLiteralParseExact(schema.pattern);\n    // prettier-ignore\n    return (IsTemplateLiteralExpressionFinite(expression)\n        ? [...TemplateLiteralExpressionGenerate(expression)]\n        : []);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates a Literal type */\nexport function Literal(value, options) {\n    return CreateType({\n        [Kind]: 'Literal',\n        const: value,\n        type: typeof value,\n    }, options);\n}\n", "import { Kind } from '../symbols/index.mjs';\nimport { CreateType } from '../create/index.mjs';\n/** `[<PERSON><PERSON>]` Creates a Boolean type */\nexport function Boolean(options) {\n    return CreateType({ [Kind]: 'Boolean', type: 'boolean' }, options);\n}\n", "import { Kind } from '../symbols/index.mjs';\nimport { CreateType } from '../create/index.mjs';\n/** `[JavaScript]` Creates a BigInt type */\nexport function BigInt(options) {\n    return CreateType({ [Kind]: 'BigInt', type: 'bigint' }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates a Number type */\nexport function Number(options) {\n    return CreateType({ [Kind]: 'Number', type: 'number' }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates a String type */\nexport function String(options) {\n    return CreateType({ [Kind]: 'String', type: 'string' }, options);\n}\n", "import { Literal } from '../literal/index.mjs';\nimport { Boolean } from '../boolean/index.mjs';\nimport { BigInt } from '../bigint/index.mjs';\nimport { Number } from '../number/index.mjs';\nimport { String } from '../string/index.mjs';\nimport { UnionEvaluated } from '../union/index.mjs';\nimport { Never } from '../never/index.mjs';\n// ------------------------------------------------------------------\n// SyntaxParsers\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction* FromUnion(syntax) {\n    const trim = syntax.trim().replace(/\"|'/g, '');\n    return (trim === 'boolean' ? yield Boolean() :\n        trim === 'number' ? yield Number() :\n            trim === 'bigint' ? yield BigInt() :\n                trim === 'string' ? yield String() :\n                    yield (() => {\n                        const literals = trim.split('|').map((literal) => Literal(literal.trim()));\n                        return (literals.length === 0 ? Never() :\n                            literals.length === 1 ? literals[0] :\n                                UnionEvaluated(literals));\n                    })());\n}\n// prettier-ignore\nfunction* FromTerminal(syntax) {\n    if (syntax[1] !== '{') {\n        const L = Literal('$');\n        const R = FromSyntax(syntax.slice(1));\n        return yield* [L, ...R];\n    }\n    for (let i = 2; i < syntax.length; i++) {\n        if (syntax[i] === '}') {\n            const L = FromUnion(syntax.slice(2, i));\n            const R = FromSyntax(syntax.slice(i + 1));\n            return yield* [...L, ...R];\n        }\n    }\n    yield Literal(syntax);\n}\n// prettier-ignore\nfunction* FromSyntax(syntax) {\n    for (let i = 0; i < syntax.length; i++) {\n        if (syntax[i] === '$') {\n            const L = Literal(syntax.slice(0, i));\n            const R = FromTerminal(syntax.slice(i));\n            return yield* [L, ...R];\n        }\n    }\n    yield Literal(syntax);\n}\n/** Parses TemplateLiteralSyntax and returns a tuple of TemplateLiteralKinds */\nexport function TemplateLiteralSyntax(syntax) {\n    return [...FromSyntax(syntax)];\n}\n", "export const PatternBoolean = '(true|false)';\nexport const PatternNumber = '(0|[1-9][0-9]*)';\nexport const PatternString = '(.*)';\nexport const PatternNever = '(?!.*)';\nexport const PatternBooleanExact = `^${PatternBoolean}$`;\nexport const PatternNumberExact = `^${PatternNumber}$`;\nexport const PatternStringExact = `^${PatternString}$`;\nexport const PatternNeverExact = `^${PatternNever}$`;\n", "import { Pattern<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, PatternBoolean } from '../patterns/index.mjs';\nimport { Kind } from '../symbols/index.mjs';\nimport { TypeBoxError } from '../error/index.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsTemplateLiteral, IsUnion, IsNumber, IsInteger, IsBigInt, IsString, IsLiteral, IsBoolean } from '../guard/kind.mjs';\n// ------------------------------------------------------------------\n// TemplateLiteralPatternError\n// ------------------------------------------------------------------\nexport class TemplateLiteralPatternError extends TypeBoxError {\n}\n// ------------------------------------------------------------------\n// TemplateLiteralPattern\n// ------------------------------------------------------------------\nfunction Escape(value) {\n    return value.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n// prettier-ignore\nfunction Visit(schema, acc) {\n    return (IsTemplateLiteral(schema) ? schema.pattern.slice(1, schema.pattern.length - 1) :\n        IsUnion(schema) ? `(${schema.anyOf.map((schema) => Visit(schema, acc)).join('|')})` :\n            IsNumber(schema) ? `${acc}${PatternNumber}` :\n                IsInteger(schema) ? `${acc}${PatternNumber}` :\n                    IsBigInt(schema) ? `${acc}${PatternNumber}` :\n                        IsString(schema) ? `${acc}${PatternString}` :\n                            IsLiteral(schema) ? `${acc}${Escape(schema.const.toString())}` :\n                                IsBoolean(schema) ? `${acc}${PatternBoolean}` :\n                                    (() => { throw new TemplateLiteralPatternError(`Unexpected Kind '${schema[Kind]}'`); })());\n}\nexport function TemplateLiteralPattern(kinds) {\n    return `^${kinds.map((schema) => Visit(schema, '')).join('')}\\$`;\n}\n", "import { UnionEvaluated } from '../union/index.mjs';\nimport { Literal } from '../literal/index.mjs';\nimport { TemplateLiteralGenerate } from './generate.mjs';\n/** Returns a Union from the given TemplateLiteral */\nexport function TemplateLiteralToUnion(schema) {\n    const R = TemplateLiteralGenerate(schema);\n    const L = R.map((S) => Literal(S));\n    return UnionEvaluated(L);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { TemplateLiteralSyntax } from './syntax.mjs';\nimport { TemplateLiteralPattern } from './pattern.mjs';\nimport { IsString } from '../guard/value.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates a TemplateLiteral type */\n// prettier-ignore\nexport function TemplateLiteral(unresolved, options) {\n    const pattern = IsString(unresolved)\n        ? TemplateLiteralPattern(TemplateLiteralSyntax(unresolved))\n        : TemplateLiteralPattern(unresolved);\n    return CreateType({ [Kind]: 'TemplateLiteral', type: 'string', pattern }, options);\n}\n", "import { TemplateLiteralGenerate } from '../template-literal/index.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsTemplateLiteral, IsUnion, IsLiteral, IsNumber, IsInteger } from '../guard/kind.mjs';\n// prettier-ignore\nfunction FromTemplateLiteral(templateLiteral) {\n    const keys = TemplateLiteralGenerate(templateLiteral);\n    return keys.map(key => key.toString());\n}\n// prettier-ignore\nfunction FromUnion(types) {\n    const result = [];\n    for (const type of types)\n        result.push(...IndexPropertyKeys(type));\n    return result;\n}\n// prettier-ignore\nfunction FromLiteral(literalValue) {\n    return ([literalValue.toString()] // TS 5.4 observes TLiteralValue as not having a toString()\n    );\n}\n/** Returns a tuple of PropertyKeys derived from the given TSchema */\n// prettier-ignore\nexport function IndexPropertyKeys(type) {\n    return [...new Set((IsTemplateLiteral(type) ? FromTemplateLiteral(type) :\n            IsUnion(type) ? FromUnion(type.anyOf) :\n                IsLiteral(type) ? FromLiteral(type.const) :\n                    IsNumber(type) ? ['[number]'] :\n                        IsInteger(type) ? ['[number]'] :\n                            []))];\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { IndexPropertyKeys } from './indexed-property-keys.mjs';\nimport { Index } from './index.mjs';\n// prettier-ignore\nfunction FromProperties(type, properties, options) {\n    const result = {};\n    for (const K2 of Object.getOwnPropertyNames(properties)) {\n        result[K2] = Index(type, IndexPropertyKeys(properties[K2]), options);\n    }\n    return result;\n}\n// prettier-ignore\nfunction FromMappedResult(type, mappedResult, options) {\n    return FromProperties(type, mappedResult.properties, options);\n}\n// prettier-ignore\nexport function IndexFromMappedResult(type, mappedResult, options) {\n    const properties = FromMappedResult(type, mappedResult, options);\n    return MappedResult(properties);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { TypeBoxError } from '../error/index.mjs';\nimport { Computed } from '../computed/index.mjs';\nimport { Never } from '../never/index.mjs';\nimport { IntersectEvaluated } from '../intersect/index.mjs';\nimport { UnionEvaluated } from '../union/index.mjs';\nimport { IndexPropertyKeys } from './indexed-property-keys.mjs';\nimport { IndexFromMappedKey } from './indexed-from-mapped-key.mjs';\nimport { IndexFromMappedResult } from './indexed-from-mapped-result.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsArray, IsIntersect, IsObject, IsMappedKey, IsMappedResult, IsNever, IsSchema, IsTuple, IsUnion, IsRef } from '../guard/kind.mjs';\n// prettier-ignore\nfunction FromRest(types, key) {\n    return types.map(type => IndexFromPropertyKey(type, key));\n}\n// prettier-ignore\nfunction FromIntersectRest(types) {\n    return types.filter(type => !IsNever(type));\n}\n// prettier-ignore\nfunction FromIntersect(types, key) {\n    return (IntersectEvaluated(FromIntersectRest(FromRest(types, key))));\n}\n// prettier-ignore\nfunction FromUnionRest(types) {\n    return (types.some(L => IsNever(L))\n        ? []\n        : types);\n}\n// prettier-ignore\nfunction FromUnion(types, key) {\n    return (UnionEvaluated(FromUnionRest(FromRest(types, key))));\n}\n// prettier-ignore\nfunction FromTuple(types, key) {\n    return (key in types ? types[key] :\n        key === '[number]' ? UnionEvaluated(types) :\n            Never());\n}\n// prettier-ignore\nfunction FromArray(type, key) {\n    return (key === '[number]'\n        ? type\n        : Never());\n}\n// prettier-ignore\nfunction FromProperty(properties, propertyKey) {\n    return (propertyKey in properties ? properties[propertyKey] : Never());\n}\n// prettier-ignore\nexport function IndexFromPropertyKey(type, propertyKey) {\n    return (IsIntersect(type) ? FromIntersect(type.allOf, propertyKey) :\n        IsUnion(type) ? FromUnion(type.anyOf, propertyKey) :\n            IsTuple(type) ? FromTuple(type.items ?? [], propertyKey) :\n                IsArray(type) ? FromArray(type.items, propertyKey) :\n                    IsObject(type) ? FromProperty(type.properties, propertyKey) :\n                        Never());\n}\n// prettier-ignore\nexport function IndexFromPropertyKeys(type, propertyKeys) {\n    return propertyKeys.map(propertyKey => IndexFromPropertyKey(type, propertyKey));\n}\n// prettier-ignore\nfunction FromSchema(type, propertyKeys) {\n    return (UnionEvaluated(IndexFromPropertyKeys(type, propertyKeys)));\n}\n// prettier-ignore\nexport function IndexFromComputed(type, key) {\n    return Computed('Index', [type, key]);\n}\n/** `[Json]` Returns an Indexed property type for the given keys */\nexport function Index(type, key, options) {\n    // computed-type\n    if (IsRef(type) || IsRef(key)) {\n        const error = `Index types using Ref parameters require both Type and Key to be of TSchema`;\n        if (!IsSchema(type) || !IsSchema(key))\n            throw new TypeBoxError(error);\n        return Computed('Index', [type, key]);\n    }\n    // mapped-types\n    if (IsMappedResult(key))\n        return IndexFromMappedResult(type, key, options);\n    if (IsMappedKey(key))\n        return IndexFromMappedKey(type, key, options);\n    // prettier-ignore\n    return CreateType(IsSchema(key)\n        ? FromSchema(type, IndexPropertyKeys(key))\n        : FromSchema(type, key), options);\n}\n", "import { Index } from './indexed.mjs';\nimport { MappedResult } from '../mapped/index.mjs';\nimport { Clone } from '../clone/value.mjs';\n// prettier-ignore\nfunction MappedIndexPropertyKey(type, key, options) {\n    return { [key]: Index(type, [key], Clone(options)) };\n}\n// prettier-ignore\nfunction MappedIndexPropertyKeys(type, propertyKeys, options) {\n    return propertyKeys.reduce((result, left) => {\n        return { ...result, ...MappedIndexPropertyKey(type, left, options) };\n    }, {});\n}\n// prettier-ignore\nfunction MappedIndexProperties(type, mappedKey, options) {\n    return MappedIndexPropertyKeys(type, mappedKey.keys, options);\n}\n// prettier-ignore\nexport function IndexFromMappedKey(type, mappedKey, options) {\n    const properties = MappedIndexProperties(type, mappedKey, options);\n    return MappedResult(properties);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[JavaScript]` Creates an Iterator type */\nexport function Iterator(items, options) {\n    return CreateType({ [Kind]: 'Iterator', type: 'Iterator', items }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsOptional } from '../guard/kind.mjs';\nfunction RequiredKeys(properties) {\n    const keys = [];\n    for (let key in properties) {\n        if (!IsOptional(properties[key]))\n            keys.push(key);\n    }\n    return keys;\n}\n/** `[<PERSON><PERSON>]` Creates an Object type */\nfunction _Object(properties, options) {\n    const required = RequiredKeys(properties);\n    const schematic = required.length > 0 ? { [Kind]: 'Object', type: 'object', properties, required } : { [Kind]: 'Object', type: 'object', properties };\n    return CreateType(schematic, options);\n}\n/** `[<PERSON><PERSON>]` Creates an Object type */\nexport var Object = _Object;\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[JavaScript]` Creates a Promise type */\nexport function Promise(item, options) {\n    return CreateType({ [Kind]: 'Promise', type: 'Promise', item }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { ReadonlyKind } from '../symbols/index.mjs';\nimport { Discard } from '../discard/index.mjs';\nimport { ReadonlyFromMappedResult } from './readonly-from-mapped-result.mjs';\nimport { IsMappedResult } from '../guard/kind.mjs';\nfunction RemoveReadonly(schema) {\n    return CreateType(Discard(schema, [ReadonlyKind]));\n}\nfunction AddReadonly(schema) {\n    return CreateType({ ...schema, [ReadonlyKind]: 'Readonly' });\n}\n// prettier-ignore\nfunction ReadonlyWithFlag(schema, F) {\n    return (F === false\n        ? RemoveReadonly(schema)\n        : AddReadonly(schema));\n}\n/** `[Json]` Creates a Readonly property */\nexport function Readonly(schema, enable) {\n    const F = enable ?? true;\n    return IsMappedResult(schema) ? ReadonlyFromMappedResult(schema, F) : ReadonlyWithFlag(schema, F);\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { Readonly } from './readonly.mjs';\n// prettier-ignore\nfunction FromProperties(K, F) {\n    const Acc = {};\n    for (const K2 of globalThis.Object.getOwnPropertyNames(K))\n        Acc[K2] = Readonly(K[K2], F);\n    return Acc;\n}\n// prettier-ignore\nfunction FromMappedResult(R, F) {\n    return FromProperties(R.properties, F);\n}\n// prettier-ignore\nexport function ReadonlyFromMappedResult(R, F) {\n    const P = FromMappedResult(R, F);\n    return MappedResult(P);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates a Tuple type */\nexport function Tuple(types, options) {\n    // prettier-ignore\n    return CreateType(types.length > 0 ?\n        { [Kind]: 'Tuple', type: 'array', items: types, additionalItems: false, minItems: types.length, maxItems: types.length } :\n        { [Kind]: 'Tuple', type: 'array', minItems: types.length, maxItems: types.length }, options);\n}\n", "/** Returns true if element right is in the set of left */\n// prettier-ignore\nexport function SetIncludes(T, S) {\n    return T.includes(S);\n}\n/** Returns true if left is a subset of right */\nexport function SetIsSubset(T, S) {\n    return T.every((L) => SetIncludes(S, L));\n}\n/** Returns a distinct set of elements */\nexport function SetDistinct(T) {\n    return [...new Set(T)];\n}\n/** Returns the Intersect of the given sets */\nexport function SetIntersect(T, S) {\n    return T.filter((L) => S.includes(L));\n}\n/** Returns the Union of the given sets */\nexport function SetUnion(T, S) {\n    return [...T, ...S];\n}\n/** Returns the Complement by omitting elements in T that are in S */\n// prettier-ignore\nexport function SetComplement(T, S) {\n    return T.filter(L => !S.includes(L));\n}\n// prettier-ignore\nfunction SetIntersectManyResolve(T, Init) {\n    return T.reduce((Acc, L) => {\n        return SetIntersect(Acc, L);\n    }, Init);\n}\n// prettier-ignore\nexport function SetIntersectMany(T) {\n    return (T.length === 1\n        ? T[0]\n        // Use left to initialize the accumulator for resolve\n        : T.length > 1\n            ? SetIntersectManyResolve(T.slice(1), T[0])\n            : []);\n}\n/** Returns the Union of multiple sets */\nexport function SetUnionMany(T) {\n    const Acc = [];\n    for (const L of T)\n        Acc.push(...L);\n    return Acc;\n}\n", "import { Kind, OptionalKind, <PERSON>onlyKind } from '../symbols/index.mjs';\nimport { Discard } from '../discard/index.mjs';\n// evaluation types\nimport { Array } from '../array/index.mjs';\nimport { AsyncIterator } from '../async-iterator/index.mjs';\nimport { Constructor } from '../constructor/index.mjs';\nimport { Function as FunctionType } from '../function/index.mjs';\nimport { IndexPropertyKeys } from '../indexed/index.mjs';\nimport { Intersect } from '../intersect/index.mjs';\nimport { Iterator } from '../iterator/index.mjs';\nimport { Literal } from '../literal/index.mjs';\nimport { Object } from '../object/index.mjs';\nimport { Optional } from '../optional/index.mjs';\nimport { Promise } from '../promise/index.mjs';\nimport { Readonly } from '../readonly/index.mjs';\nimport { Tuple } from '../tuple/index.mjs';\nimport { Union } from '../union/index.mjs';\n// operator\nimport { SetIncludes } from '../sets/index.mjs';\n// mapping types\nimport { MappedResult } from './mapped-result.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsArray, IsAsyncIterator, IsConstructor, IsFunction, IsIntersect, IsIterator, IsReadonly, IsMappedResult, IsMappedKey, IsObject, IsOptional, IsPromise, IsSchema, IsTuple, IsUnion } from '../guard/kind.mjs';\n// prettier-ignore\nfunction FromMappedResult(K, P) {\n    return (K in P\n        ? FromSchemaType(K, P[K])\n        : MappedResult(P));\n}\n// prettier-ignore\nfunction MappedKeyToKnownMappedResultProperties(K) {\n    return { [K]: Literal(K) };\n}\n// prettier-ignore\nfunction MappedKeyToUnknownMappedResultProperties(P) {\n    const Acc = {};\n    for (const L of P)\n        Acc[L] = Literal(L);\n    return Acc;\n}\n// prettier-ignore\nfunction MappedKeyToMappedResultProperties(K, P) {\n    return (SetIncludes(P, K)\n        ? MappedKeyToKnownMappedResultProperties(K)\n        : MappedKeyToUnknownMappedResultProperties(P));\n}\n// prettier-ignore\nfunction FromMappedKey(K, P) {\n    const R = MappedKeyToMappedResultProperties(K, P);\n    return FromMappedResult(K, R);\n}\n// prettier-ignore\nfunction FromRest(K, T) {\n    return T.map(L => FromSchemaType(K, L));\n}\n// prettier-ignore\nfunction FromProperties(K, T) {\n    const Acc = {};\n    for (const K2 of globalThis.Object.getOwnPropertyNames(T))\n        Acc[K2] = FromSchemaType(K, T[K2]);\n    return Acc;\n}\n// prettier-ignore\nfunction FromSchemaType(K, T) {\n    // required to retain user defined options for mapped type\n    const options = { ...T };\n    return (\n    // unevaluated modifier types\n    IsOptional(T) ? Optional(FromSchemaType(K, Discard(T, [OptionalKind]))) :\n        IsReadonly(T) ? Readonly(FromSchemaType(K, Discard(T, [ReadonlyKind]))) :\n            // unevaluated mapped types\n            IsMappedResult(T) ? FromMappedResult(K, T.properties) :\n                IsMappedKey(T) ? FromMappedKey(K, T.keys) :\n                    // unevaluated types\n                    IsConstructor(T) ? Constructor(FromRest(K, T.parameters), FromSchemaType(K, T.returns), options) :\n                        IsFunction(T) ? FunctionType(FromRest(K, T.parameters), FromSchemaType(K, T.returns), options) :\n                            IsAsyncIterator(T) ? AsyncIterator(FromSchemaType(K, T.items), options) :\n                                IsIterator(T) ? Iterator(FromSchemaType(K, T.items), options) :\n                                    IsIntersect(T) ? Intersect(FromRest(K, T.allOf), options) :\n                                        IsUnion(T) ? Union(FromRest(K, T.anyOf), options) :\n                                            IsTuple(T) ? Tuple(FromRest(K, T.items ?? []), options) :\n                                                IsObject(T) ? Object(FromProperties(K, T.properties), options) :\n                                                    IsArray(T) ? Array(FromSchemaType(K, T.items), options) :\n                                                        IsPromise(T) ? Promise(FromSchemaType(K, T.item), options) :\n                                                            T);\n}\n// prettier-ignore\nexport function MappedFunctionReturnType(K, T) {\n    const Acc = {};\n    for (const L of K)\n        Acc[L] = FromSchemaType(L, T);\n    return Acc;\n}\n/** `[Json]` Creates a Mapped object type */\nexport function Mapped(key, map, options) {\n    const K = IsSchema(key) ? IndexPropertyKeys(key) : key;\n    const RT = map({ [Kind]: 'MappedKey', keys: K });\n    const R = MappedFunctionReturnType(K, RT);\n    return Object(R, options);\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { Optional } from './optional.mjs';\n// prettier-ignore\nfunction FromProperties(P, F) {\n    const Acc = {};\n    for (const K2 of globalThis.Object.getOwnPropertyNames(P))\n        Acc[K2] = Optional(P[K2], F);\n    return Acc;\n}\n// prettier-ignore\nfunction FromMappedResult(R, F) {\n    return FromProperties(R.properties, F);\n}\n// prettier-ignore\nexport function OptionalFromMappedResult(R, F) {\n    const P = FromMappedResult(R, F);\n    return MappedResult(P);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { OptionalKind } from '../symbols/index.mjs';\nimport { Discard } from '../discard/index.mjs';\nimport { OptionalFromMappedResult } from './optional-from-mapped-result.mjs';\nimport { IsMappedResult } from '../guard/kind.mjs';\nfunction RemoveOptional(schema) {\n    return CreateType(Discard(schema, [OptionalKind]));\n}\nfunction AddOptional(schema) {\n    return CreateType({ ...schema, [OptionalKind]: 'Optional' });\n}\n// prettier-ignore\nfunction OptionalWithFlag(schema, F) {\n    return (F === false\n        ? RemoveOptional(schema)\n        : AddOptional(schema));\n}\n/** `[<PERSON><PERSON>]` Creates a Optional property */\nexport function Optional(schema, enable) {\n    const F = enable ?? true;\n    return IsMappedResult(schema) ? OptionalFromMappedResult(schema, F) : OptionalWithFlag(schema, F);\n}\n", "import { TypeBoxError } from '../error/index.mjs';\nimport { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates a Ref type. The referenced type must contain a $id */\nexport function Ref(...args) {\n    const [$ref, options] = typeof args[0] === 'string' ? [args[0], args[1]] : [args[0].$id, args[1]];\n    if (typeof $ref !== 'string')\n        throw new TypeBoxError('Ref: $ref must be a string');\n    return CreateType({ [Kind]: 'Ref', $ref }, options);\n}\n", "import { SetUnionMany, SetIntersectMany } from '../sets/index.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsIntersect, IsUnion, IsTuple, IsArray, IsObject, IsRecord } from '../guard/kind.mjs';\n// prettier-ignore\nfunction FromRest(types) {\n    const result = [];\n    for (const L of types)\n        result.push(KeyOfPropertyKeys(L));\n    return result;\n}\n// prettier-ignore\nfunction FromIntersect(types) {\n    const propertyKeysArray = FromRest(types);\n    const propertyKeys = SetUnionMany(propertyKeysArray);\n    return propertyKeys;\n}\n// prettier-ignore\nfunction FromUnion(types) {\n    const propertyKeysArray = FromRest(types);\n    const propertyKeys = SetIntersectMany(propertyKeysArray);\n    return propertyKeys;\n}\n// prettier-ignore\nfunction FromTuple(types) {\n    return types.map((_, indexer) => indexer.toString());\n}\n// prettier-ignore\nfunction FromArray(_) {\n    return (['[number]']);\n}\n// prettier-ignore\nfunction FromProperties(T) {\n    return (globalThis.Object.getOwnPropertyNames(T));\n}\n// ------------------------------------------------------------------\n// FromPatternProperties\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromPatternProperties(patternProperties) {\n    if (!includePatternProperties)\n        return [];\n    const patternPropertyKeys = globalThis.Object.getOwnPropertyNames(patternProperties);\n    return patternPropertyKeys.map(key => {\n        return (key[0] === '^' && key[key.length - 1] === '$')\n            ? key.slice(1, key.length - 1)\n            : key;\n    });\n}\n/** Returns a tuple of PropertyKeys derived from the given TSchema. */\n// prettier-ignore\nexport function KeyOfPropertyKeys(type) {\n    return (IsIntersect(type) ? FromIntersect(type.allOf) :\n        IsUnion(type) ? FromUnion(type.anyOf) :\n            IsTuple(type) ? FromTuple(type.items ?? []) :\n                IsArray(type) ? FromArray(type.items) :\n                    IsObject(type) ? FromProperties(type.properties) :\n                        IsRecord(type) ? FromPatternProperties(type.patternProperties) :\n                            []);\n}\n// ----------------------------------------------------------------\n// KeyOfPattern\n// ----------------------------------------------------------------\nlet includePatternProperties = false;\n/** Returns a regular expression pattern derived from the given TSchema */\nexport function KeyOfPattern(schema) {\n    includePatternProperties = true;\n    const keys = KeyOfPropertyKeys(schema);\n    includePatternProperties = false;\n    const pattern = keys.map((key) => `(${key})`);\n    return `^(${pattern.join('|')})$`;\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { KeyOf } from './keyof.mjs';\nimport { Clone } from '../clone/value.mjs';\n// prettier-ignore\nfunction FromProperties(properties, options) {\n    const result = {};\n    for (const K2 of globalThis.Object.getOwnPropertyNames(properties))\n        result[K2] = KeyOf(properties[K2], Clone(options));\n    return result;\n}\n// prettier-ignore\nfunction FromMappedResult(mappedResult, options) {\n    return FromProperties(mappedResult.properties, options);\n}\n// prettier-ignore\nexport function KeyOfFromMappedResult(mappedResult, options) {\n    const properties = FromMappedResult(mappedResult, options);\n    return MappedResult(properties);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Literal } from '../literal/index.mjs';\nimport { Number } from '../number/index.mjs';\nimport { Computed } from '../computed/index.mjs';\nimport { Ref } from '../ref/index.mjs';\nimport { KeyOfPropertyKeys } from './keyof-property-keys.mjs';\nimport { UnionEvaluated } from '../union/index.mjs';\nimport { KeyOfFromMappedResult } from './keyof-from-mapped-result.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsMappedResult, IsRef, IsComputed } from '../guard/kind.mjs';\n// prettier-ignore\nfunction FromComputed(target, parameters) {\n    return Computed('KeyOf', [Computed(target, parameters)]);\n}\n// prettier-ignore\nfunction FromRef($ref) {\n    return Computed('KeyOf', [Ref($ref)]);\n}\n// prettier-ignore\nfunction KeyOfFromType(type, options) {\n    const propertyKeys = KeyOfPropertyKeys(type);\n    const propertyKeyTypes = KeyOfPropertyKeysToRest(propertyKeys);\n    const result = UnionEvaluated(propertyKeyTypes);\n    return CreateType(result, options);\n}\n// prettier-ignore\nexport function KeyOfPropertyKeysToRest(propertyKeys) {\n    return propertyKeys.map(L => L === '[number]' ? Number() : Literal(L));\n}\n/** `[Json]` Creates a KeyOf type */\nexport function KeyOf(type, options) {\n    return (IsComputed(type) ? FromComputed(type.target, type.parameters) : IsRef(type) ? FromRef(type.$ref) : IsMappedResult(type) ? KeyOfFromMappedResult(type, options) : KeyOfFromType(type, options));\n}\n", "import { IndexFromPropertyKeys } from '../indexed/indexed.mjs';\nimport { KeyOfPropertyKeys } from './keyof-property-keys.mjs';\n/**\n * `[Utility]` Resolves an array of keys and schemas from the given schema. This method is faster\n * than obtaining the keys and resolving each individually via indexing. This method was written\n * accellerate Intersect and Union encoding.\n */\nexport function KeyOfPropertyEntries(schema) {\n    const keys = KeyOfPropertyKeys(schema);\n    const schemas = IndexFromPropertyKeys(schema, keys);\n    return keys.map((_, index) => [keys[index], schemas[index]]);\n}\n", "import { Kind } from '../symbols/index.mjs';\n/** Fast undefined check used for properties of type undefined */\nfunction Intersect(schema) {\n    return schema.allOf.every((schema) => ExtendsUndefinedCheck(schema));\n}\nfunction Union(schema) {\n    return schema.anyOf.some((schema) => ExtendsUndefinedCheck(schema));\n}\nfunction Not(schema) {\n    return !ExtendsUndefinedCheck(schema.not);\n}\n/** Fast undefined check used for properties of type undefined */\n// prettier-ignore\nexport function ExtendsUndefinedCheck(schema) {\n    return (schema[Kind] === 'Intersect' ? Intersect(schema) :\n        schema[Kind] === 'Union' ? Union(schema) :\n            schema[Kind] === 'Not' ? Not(schema) :\n                schema[Kind] === 'Undefined' ? true :\n                    false);\n}\n", "import { CreateType } from '../create/index.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates an Any type */\nexport function Any(options) {\n    return CreateType({ [Kind]: 'Any' }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates an Unknown type */\nexport function Unknown(options) {\n    return CreateType({ [Kind]: 'Unknown' }, options);\n}\n", "import * as ValueGuard from './value.mjs';\nimport { Kind, Hint, TransformKind, ReadonlyKind, OptionalKind } from '../symbols/index.mjs';\nimport { TypeBoxError } from '../error/index.mjs';\nexport class TypeGuardUnknownTypeError extends TypeBoxError {\n}\nconst KnownTypes = [\n    'Argument',\n    'Any',\n    'Array',\n    'AsyncIterator',\n    'BigInt',\n    'Boolean',\n    'Computed',\n    'Constructor',\n    'Date',\n    'Enum',\n    'Function',\n    'Integer',\n    'Intersect',\n    'Iterator',\n    'Literal',\n    'MappedKey',\n    'MappedResult',\n    'Not',\n    'Null',\n    'Number',\n    'Object',\n    'Promise',\n    'Record',\n    'Ref',\n    'RegExp',\n    'String',\n    'Symbol',\n    'TemplateLiteral',\n    'This',\n    'Tuple',\n    'Undefined',\n    'Union',\n    'Uint8Array',\n    'Unknown',\n    'Void',\n];\nfunction IsPattern(value) {\n    try {\n        new RegExp(value);\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\nfunction IsControlCharacterFree(value) {\n    if (!ValueGuard.IsString(value))\n        return false;\n    for (let i = 0; i < value.length; i++) {\n        const code = value.charCodeAt(i);\n        if ((code >= 7 && code <= 13) || code === 27 || code === 127) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction IsAdditionalProperties(value) {\n    return IsOptionalBoolean(value) || IsSchema(value);\n}\nfunction IsOptionalBigInt(value) {\n    return ValueGuard.IsUndefined(value) || ValueGuard.IsBigInt(value);\n}\nfunction IsOptionalNumber(value) {\n    return ValueGuard.IsUndefined(value) || ValueGuard.IsNumber(value);\n}\nfunction IsOptionalBoolean(value) {\n    return ValueGuard.IsUndefined(value) || ValueGuard.IsBoolean(value);\n}\nfunction IsOptionalString(value) {\n    return ValueGuard.IsUndefined(value) || ValueGuard.IsString(value);\n}\nfunction IsOptionalPattern(value) {\n    return ValueGuard.IsUndefined(value) || (ValueGuard.IsString(value) && IsControlCharacterFree(value) && IsPattern(value));\n}\nfunction IsOptionalFormat(value) {\n    return ValueGuard.IsUndefined(value) || (ValueGuard.IsString(value) && IsControlCharacterFree(value));\n}\nfunction IsOptionalSchema(value) {\n    return ValueGuard.IsUndefined(value) || IsSchema(value);\n}\n// ------------------------------------------------------------------\n// Modifiers\n// ------------------------------------------------------------------\n/** Returns true if this value has a Readonly symbol */\nexport function IsReadonly(value) {\n    return ValueGuard.IsObject(value) && value[ReadonlyKind] === 'Readonly';\n}\n/** Returns true if this value has a Optional symbol */\nexport function IsOptional(value) {\n    return ValueGuard.IsObject(value) && value[OptionalKind] === 'Optional';\n}\n// ------------------------------------------------------------------\n// Types\n// ------------------------------------------------------------------\n/** Returns true if the given value is TAny */\nexport function IsAny(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Any') &&\n        IsOptionalString(value.$id));\n}\n/** Returns true if the given value is TArgument */\nexport function IsArgument(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Argument') &&\n        ValueGuard.IsNumber(value.index));\n}\n/** Returns true if the given value is TArray */\nexport function IsArray(value) {\n    return (IsKindOf(value, 'Array') &&\n        value.type === 'array' &&\n        IsOptionalString(value.$id) &&\n        IsSchema(value.items) &&\n        IsOptionalNumber(value.minItems) &&\n        IsOptionalNumber(value.maxItems) &&\n        IsOptionalBoolean(value.uniqueItems) &&\n        IsOptionalSchema(value.contains) &&\n        IsOptionalNumber(value.minContains) &&\n        IsOptionalNumber(value.maxContains));\n}\n/** Returns true if the given value is TAsyncIterator */\nexport function IsAsyncIterator(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'AsyncIterator') &&\n        value.type === 'AsyncIterator' &&\n        IsOptionalString(value.$id) &&\n        IsSchema(value.items));\n}\n/** Returns true if the given value is TBigInt */\nexport function IsBigInt(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'BigInt') &&\n        value.type === 'bigint' &&\n        IsOptionalString(value.$id) &&\n        IsOptionalBigInt(value.exclusiveMaximum) &&\n        IsOptionalBigInt(value.exclusiveMinimum) &&\n        IsOptionalBigInt(value.maximum) &&\n        IsOptionalBigInt(value.minimum) &&\n        IsOptionalBigInt(value.multipleOf));\n}\n/** Returns true if the given value is TBoolean */\nexport function IsBoolean(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Boolean') &&\n        value.type === 'boolean' &&\n        IsOptionalString(value.$id));\n}\n/** Returns true if the given value is TComputed */\nexport function IsComputed(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Computed') &&\n        ValueGuard.IsString(value.target) &&\n        ValueGuard.IsArray(value.parameters) &&\n        value.parameters.every((schema) => IsSchema(schema)));\n}\n/** Returns true if the given value is TConstructor */\nexport function IsConstructor(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Constructor') &&\n        value.type === 'Constructor' &&\n        IsOptionalString(value.$id) &&\n        ValueGuard.IsArray(value.parameters) &&\n        value.parameters.every(schema => IsSchema(schema)) &&\n        IsSchema(value.returns));\n}\n/** Returns true if the given value is TDate */\nexport function IsDate(value) {\n    return (IsKindOf(value, 'Date') &&\n        value.type === 'Date' &&\n        IsOptionalString(value.$id) &&\n        IsOptionalNumber(value.exclusiveMaximumTimestamp) &&\n        IsOptionalNumber(value.exclusiveMinimumTimestamp) &&\n        IsOptionalNumber(value.maximumTimestamp) &&\n        IsOptionalNumber(value.minimumTimestamp) &&\n        IsOptionalNumber(value.multipleOfTimestamp));\n}\n/** Returns true if the given value is TFunction */\nexport function IsFunction(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Function') &&\n        value.type === 'Function' &&\n        IsOptionalString(value.$id) &&\n        ValueGuard.IsArray(value.parameters) &&\n        value.parameters.every(schema => IsSchema(schema)) &&\n        IsSchema(value.returns));\n}\n/** Returns true if the given value is TImport */\nexport function IsImport(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Import') &&\n        ValueGuard.HasPropertyKey(value, '$defs') &&\n        ValueGuard.IsObject(value.$defs) &&\n        IsProperties(value.$defs) &&\n        ValueGuard.HasPropertyKey(value, '$ref') &&\n        ValueGuard.IsString(value.$ref) &&\n        value.$ref in value.$defs // required\n    );\n}\n/** Returns true if the given value is TInteger */\nexport function IsInteger(value) {\n    return (IsKindOf(value, 'Integer') &&\n        value.type === 'integer' &&\n        IsOptionalString(value.$id) &&\n        IsOptionalNumber(value.exclusiveMaximum) &&\n        IsOptionalNumber(value.exclusiveMinimum) &&\n        IsOptionalNumber(value.maximum) &&\n        IsOptionalNumber(value.minimum) &&\n        IsOptionalNumber(value.multipleOf));\n}\n/** Returns true if the given schema is TProperties */\nexport function IsProperties(value) {\n    // prettier-ignore\n    return (ValueGuard.IsObject(value) &&\n        Object.entries(value).every(([key, schema]) => IsControlCharacterFree(key) && IsSchema(schema)));\n}\n/** Returns true if the given value is TIntersect */\nexport function IsIntersect(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Intersect') &&\n        (ValueGuard.IsString(value.type) && value.type !== 'object' ? false : true) &&\n        ValueGuard.IsArray(value.allOf) &&\n        value.allOf.every(schema => IsSchema(schema) && !IsTransform(schema)) &&\n        IsOptionalString(value.type) &&\n        (IsOptionalBoolean(value.unevaluatedProperties) || IsOptionalSchema(value.unevaluatedProperties)) &&\n        IsOptionalString(value.$id));\n}\n/** Returns true if the given value is TIterator */\nexport function IsIterator(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Iterator') &&\n        value.type === 'Iterator' &&\n        IsOptionalString(value.$id) &&\n        IsSchema(value.items));\n}\n/** Returns true if the given value is a TKind with the given name. */\nexport function IsKindOf(value, kind) {\n    return ValueGuard.IsObject(value) && Kind in value && value[Kind] === kind;\n}\n/** Returns true if the given value is TLiteral<string> */\nexport function IsLiteralString(value) {\n    return IsLiteral(value) && ValueGuard.IsString(value.const);\n}\n/** Returns true if the given value is TLiteral<number> */\nexport function IsLiteralNumber(value) {\n    return IsLiteral(value) && ValueGuard.IsNumber(value.const);\n}\n/** Returns true if the given value is TLiteral<boolean> */\nexport function IsLiteralBoolean(value) {\n    return IsLiteral(value) && ValueGuard.IsBoolean(value.const);\n}\n/** Returns true if the given value is TLiteral */\nexport function IsLiteral(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Literal') &&\n        IsOptionalString(value.$id) && IsLiteralValue(value.const));\n}\n/** Returns true if the given value is a TLiteralValue */\nexport function IsLiteralValue(value) {\n    return ValueGuard.IsBoolean(value) || ValueGuard.IsNumber(value) || ValueGuard.IsString(value);\n}\n/** Returns true if the given value is a TMappedKey */\nexport function IsMappedKey(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'MappedKey') &&\n        ValueGuard.IsArray(value.keys) &&\n        value.keys.every(key => ValueGuard.IsNumber(key) || ValueGuard.IsString(key)));\n}\n/** Returns true if the given value is TMappedResult */\nexport function IsMappedResult(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'MappedResult') &&\n        IsProperties(value.properties));\n}\n/** Returns true if the given value is TNever */\nexport function IsNever(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Never') &&\n        ValueGuard.IsObject(value.not) &&\n        Object.getOwnPropertyNames(value.not).length === 0);\n}\n/** Returns true if the given value is TNot */\nexport function IsNot(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Not') &&\n        IsSchema(value.not));\n}\n/** Returns true if the given value is TNull */\nexport function IsNull(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Null') &&\n        value.type === 'null' &&\n        IsOptionalString(value.$id));\n}\n/** Returns true if the given value is TNumber */\nexport function IsNumber(value) {\n    return (IsKindOf(value, 'Number') &&\n        value.type === 'number' &&\n        IsOptionalString(value.$id) &&\n        IsOptionalNumber(value.exclusiveMaximum) &&\n        IsOptionalNumber(value.exclusiveMinimum) &&\n        IsOptionalNumber(value.maximum) &&\n        IsOptionalNumber(value.minimum) &&\n        IsOptionalNumber(value.multipleOf));\n}\n/** Returns true if the given value is TObject */\nexport function IsObject(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Object') &&\n        value.type === 'object' &&\n        IsOptionalString(value.$id) &&\n        IsProperties(value.properties) &&\n        IsAdditionalProperties(value.additionalProperties) &&\n        IsOptionalNumber(value.minProperties) &&\n        IsOptionalNumber(value.maxProperties));\n}\n/** Returns true if the given value is TPromise */\nexport function IsPromise(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Promise') &&\n        value.type === 'Promise' &&\n        IsOptionalString(value.$id) &&\n        IsSchema(value.item));\n}\n/** Returns true if the given value is TRecord */\nexport function IsRecord(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Record') &&\n        value.type === 'object' &&\n        IsOptionalString(value.$id) &&\n        IsAdditionalProperties(value.additionalProperties) &&\n        ValueGuard.IsObject(value.patternProperties) &&\n        ((schema) => {\n            const keys = Object.getOwnPropertyNames(schema.patternProperties);\n            return (keys.length === 1 &&\n                IsPattern(keys[0]) &&\n                ValueGuard.IsObject(schema.patternProperties) &&\n                IsSchema(schema.patternProperties[keys[0]]));\n        })(value));\n}\n/** Returns true if this value is TRecursive */\nexport function IsRecursive(value) {\n    return ValueGuard.IsObject(value) && Hint in value && value[Hint] === 'Recursive';\n}\n/** Returns true if the given value is TRef */\nexport function IsRef(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Ref') &&\n        IsOptionalString(value.$id) &&\n        ValueGuard.IsString(value.$ref));\n}\n/** Returns true if the given value is TRegExp */\nexport function IsRegExp(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'RegExp') &&\n        IsOptionalString(value.$id) &&\n        ValueGuard.IsString(value.source) &&\n        ValueGuard.IsString(value.flags) &&\n        IsOptionalNumber(value.maxLength) &&\n        IsOptionalNumber(value.minLength));\n}\n/** Returns true if the given value is TString */\nexport function IsString(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'String') &&\n        value.type === 'string' &&\n        IsOptionalString(value.$id) &&\n        IsOptionalNumber(value.minLength) &&\n        IsOptionalNumber(value.maxLength) &&\n        IsOptionalPattern(value.pattern) &&\n        IsOptionalFormat(value.format));\n}\n/** Returns true if the given value is TSymbol */\nexport function IsSymbol(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Symbol') &&\n        value.type === 'symbol' &&\n        IsOptionalString(value.$id));\n}\n/** Returns true if the given value is TTemplateLiteral */\nexport function IsTemplateLiteral(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'TemplateLiteral') &&\n        value.type === 'string' &&\n        ValueGuard.IsString(value.pattern) &&\n        value.pattern[0] === '^' &&\n        value.pattern[value.pattern.length - 1] === '$');\n}\n/** Returns true if the given value is TThis */\nexport function IsThis(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'This') &&\n        IsOptionalString(value.$id) &&\n        ValueGuard.IsString(value.$ref));\n}\n/** Returns true of this value is TTransform */\nexport function IsTransform(value) {\n    return ValueGuard.IsObject(value) && TransformKind in value;\n}\n/** Returns true if the given value is TTuple */\nexport function IsTuple(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Tuple') &&\n        value.type === 'array' &&\n        IsOptionalString(value.$id) &&\n        ValueGuard.IsNumber(value.minItems) &&\n        ValueGuard.IsNumber(value.maxItems) &&\n        value.minItems === value.maxItems &&\n        (( // empty\n        ValueGuard.IsUndefined(value.items) &&\n            ValueGuard.IsUndefined(value.additionalItems) &&\n            value.minItems === 0) || (ValueGuard.IsArray(value.items) &&\n            value.items.every(schema => IsSchema(schema)))));\n}\n/** Returns true if the given value is TUndefined */\nexport function IsUndefined(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Undefined') &&\n        value.type === 'undefined' &&\n        IsOptionalString(value.$id));\n}\n/** Returns true if the given value is TUnion<Literal<string | number>[]> */\nexport function IsUnionLiteral(value) {\n    return IsUnion(value) && value.anyOf.every((schema) => IsLiteralString(schema) || IsLiteralNumber(schema));\n}\n/** Returns true if the given value is TUnion */\nexport function IsUnion(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Union') &&\n        IsOptionalString(value.$id) &&\n        ValueGuard.IsObject(value) &&\n        ValueGuard.IsArray(value.anyOf) &&\n        value.anyOf.every(schema => IsSchema(schema)));\n}\n/** Returns true if the given value is TUint8Array */\nexport function IsUint8Array(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Uint8Array') &&\n        value.type === 'Uint8Array' &&\n        IsOptionalString(value.$id) &&\n        IsOptionalNumber(value.minByteLength) &&\n        IsOptionalNumber(value.maxByteLength));\n}\n/** Returns true if the given value is TUnknown */\nexport function IsUnknown(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Unknown') &&\n        IsOptionalString(value.$id));\n}\n/** Returns true if the given value is a raw TUnsafe */\nexport function IsUnsafe(value) {\n    return IsKindOf(value, 'Unsafe');\n}\n/** Returns true if the given value is TVoid */\nexport function IsVoid(value) {\n    // prettier-ignore\n    return (IsKindOf(value, 'Void') &&\n        value.type === 'void' &&\n        IsOptionalString(value.$id));\n}\n/** Returns true if the given value is TKind */\nexport function IsKind(value) {\n    return ValueGuard.IsObject(value) && Kind in value && ValueGuard.IsString(value[Kind]) && !KnownTypes.includes(value[Kind]);\n}\n/** Returns true if the given value is TSchema */\nexport function IsSchema(value) {\n    // prettier-ignore\n    return (ValueGuard.IsObject(value)) && (IsAny(value) ||\n        IsArgument(value) ||\n        IsArray(value) ||\n        IsBoolean(value) ||\n        IsBigInt(value) ||\n        IsAsyncIterator(value) ||\n        IsComputed(value) ||\n        IsConstructor(value) ||\n        IsDate(value) ||\n        IsFunction(value) ||\n        IsInteger(value) ||\n        IsIntersect(value) ||\n        IsIterator(value) ||\n        IsLiteral(value) ||\n        IsMappedKey(value) ||\n        IsMappedResult(value) ||\n        IsNever(value) ||\n        IsNot(value) ||\n        IsNull(value) ||\n        IsNumber(value) ||\n        IsObject(value) ||\n        IsPromise(value) ||\n        IsRecord(value) ||\n        IsRef(value) ||\n        IsRegExp(value) ||\n        IsString(value) ||\n        IsSymbol(value) ||\n        IsTemplateLiteral(value) ||\n        IsThis(value) ||\n        IsTuple(value) ||\n        IsUndefined(value) ||\n        IsUnion(value) ||\n        IsUint8Array(value) ||\n        IsUnknown(value) ||\n        IsUnsafe(value) ||\n        IsVoid(value) ||\n        IsKind(value));\n}\n", "import { Any } from '../any/index.mjs';\nimport { Function as FunctionType } from '../function/index.mjs';\nimport { Number } from '../number/index.mjs';\nimport { String } from '../string/index.mjs';\nimport { Unknown } from '../unknown/index.mjs';\nimport { TemplateLiteralToUnion } from '../template-literal/index.mjs';\nimport { PatternNumberExact, PatternStringExact } from '../patterns/index.mjs';\nimport { Kind, Hint } from '../symbols/index.mjs';\nimport { TypeBoxError } from '../error/index.mjs';\nimport { TypeGuard, ValueGuard } from '../guard/index.mjs';\nexport class ExtendsResolverError extends TypeBoxError {\n}\nexport var ExtendsResult;\n(function (ExtendsResult) {\n    ExtendsResult[ExtendsResult[\"Union\"] = 0] = \"Union\";\n    ExtendsResult[ExtendsResult[\"True\"] = 1] = \"True\";\n    ExtendsResult[ExtendsResult[\"False\"] = 2] = \"False\";\n})(ExtendsResult || (ExtendsResult = {}));\n// ------------------------------------------------------------------\n// IntoBooleanResult\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction IntoBooleanResult(result) {\n    return result === ExtendsResult.False ? result : ExtendsResult.True;\n}\n// ------------------------------------------------------------------\n// Throw\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction Throw(message) {\n    throw new ExtendsResolverError(message);\n}\n// ------------------------------------------------------------------\n// StructuralRight\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction IsStructuralRight(right) {\n    return (TypeGuard.IsNever(right) ||\n        TypeGuard.IsIntersect(right) ||\n        TypeGuard.IsUnion(right) ||\n        TypeGuard.IsUnknown(right) ||\n        TypeGuard.IsAny(right));\n}\n// prettier-ignore\nfunction StructuralRight(left, right) {\n    return (TypeGuard.IsNever(right) ? FromNeverRight(left, right) :\n        TypeGuard.IsIntersect(right) ? FromIntersectRight(left, right) :\n            TypeGuard.IsUnion(right) ? FromUnionRight(left, right) :\n                TypeGuard.IsUnknown(right) ? FromUnknownRight(left, right) :\n                    TypeGuard.IsAny(right) ? FromAnyRight(left, right) :\n                        Throw('StructuralRight'));\n}\n// ------------------------------------------------------------------\n// Any\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromAnyRight(left, right) {\n    return ExtendsResult.True;\n}\n// prettier-ignore\nfunction FromAny(left, right) {\n    return (TypeGuard.IsIntersect(right) ? FromIntersectRight(left, right) :\n        (TypeGuard.IsUnion(right) && right.anyOf.some((schema) => TypeGuard.IsAny(schema) || TypeGuard.IsUnknown(schema))) ? ExtendsResult.True :\n            TypeGuard.IsUnion(right) ? ExtendsResult.Union :\n                TypeGuard.IsUnknown(right) ? ExtendsResult.True :\n                    TypeGuard.IsAny(right) ? ExtendsResult.True :\n                        ExtendsResult.Union);\n}\n// ------------------------------------------------------------------\n// Array\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromArrayRight(left, right) {\n    return (TypeGuard.IsUnknown(left) ? ExtendsResult.False :\n        TypeGuard.IsAny(left) ? ExtendsResult.Union :\n            TypeGuard.IsNever(left) ? ExtendsResult.True :\n                ExtendsResult.False);\n}\n// prettier-ignore\nfunction FromArray(left, right) {\n    return (TypeGuard.IsObject(right) && IsObjectArrayLike(right) ? ExtendsResult.True :\n        IsStructuralRight(right) ? StructuralRight(left, right) :\n            !TypeGuard.IsArray(right) ? ExtendsResult.False :\n                IntoBooleanResult(Visit(left.items, right.items)));\n}\n// ------------------------------------------------------------------\n// AsyncIterator\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromAsyncIterator(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        !TypeGuard.IsAsyncIterator(right) ? ExtendsResult.False :\n            IntoBooleanResult(Visit(left.items, right.items)));\n}\n// ------------------------------------------------------------------\n// BigInt\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromBigInt(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n            TypeGuard.IsRecord(right) ? FromRecordRight(left, right) :\n                TypeGuard.IsBigInt(right) ? ExtendsResult.True :\n                    ExtendsResult.False);\n}\n// ------------------------------------------------------------------\n// Boolean\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromBooleanRight(left, right) {\n    return (TypeGuard.IsLiteralBoolean(left) ? ExtendsResult.True :\n        TypeGuard.IsBoolean(left) ? ExtendsResult.True :\n            ExtendsResult.False);\n}\n// prettier-ignore\nfunction FromBoolean(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n            TypeGuard.IsRecord(right) ? FromRecordRight(left, right) :\n                TypeGuard.IsBoolean(right) ? ExtendsResult.True :\n                    ExtendsResult.False);\n}\n// ------------------------------------------------------------------\n// Constructor\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromConstructor(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n            !TypeGuard.IsConstructor(right) ? ExtendsResult.False :\n                left.parameters.length > right.parameters.length ? ExtendsResult.False :\n                    (!left.parameters.every((schema, index) => IntoBooleanResult(Visit(right.parameters[index], schema)) === ExtendsResult.True)) ? ExtendsResult.False :\n                        IntoBooleanResult(Visit(left.returns, right.returns)));\n}\n// ------------------------------------------------------------------\n// Date\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromDate(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n            TypeGuard.IsRecord(right) ? FromRecordRight(left, right) :\n                TypeGuard.IsDate(right) ? ExtendsResult.True :\n                    ExtendsResult.False);\n}\n// ------------------------------------------------------------------\n// Function\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromFunction(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n            !TypeGuard.IsFunction(right) ? ExtendsResult.False :\n                left.parameters.length > right.parameters.length ? ExtendsResult.False :\n                    (!left.parameters.every((schema, index) => IntoBooleanResult(Visit(right.parameters[index], schema)) === ExtendsResult.True)) ? ExtendsResult.False :\n                        IntoBooleanResult(Visit(left.returns, right.returns)));\n}\n// ------------------------------------------------------------------\n// Integer\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromIntegerRight(left, right) {\n    return (TypeGuard.IsLiteral(left) && ValueGuard.IsNumber(left.const) ? ExtendsResult.True :\n        TypeGuard.IsNumber(left) || TypeGuard.IsInteger(left) ? ExtendsResult.True :\n            ExtendsResult.False);\n}\n// prettier-ignore\nfunction FromInteger(left, right) {\n    return (TypeGuard.IsInteger(right) || TypeGuard.IsNumber(right) ? ExtendsResult.True :\n        IsStructuralRight(right) ? StructuralRight(left, right) :\n            TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n                TypeGuard.IsRecord(right) ? FromRecordRight(left, right) :\n                    ExtendsResult.False);\n}\n// ------------------------------------------------------------------\n// Intersect\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromIntersectRight(left, right) {\n    return right.allOf.every((schema) => Visit(left, schema) === ExtendsResult.True)\n        ? ExtendsResult.True\n        : ExtendsResult.False;\n}\n// prettier-ignore\nfunction FromIntersect(left, right) {\n    return left.allOf.some((schema) => Visit(schema, right) === ExtendsResult.True)\n        ? ExtendsResult.True\n        : ExtendsResult.False;\n}\n// ------------------------------------------------------------------\n// Iterator\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromIterator(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        !TypeGuard.IsIterator(right) ? ExtendsResult.False :\n            IntoBooleanResult(Visit(left.items, right.items)));\n}\n// ------------------------------------------------------------------\n// Literal\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromLiteral(left, right) {\n    return (TypeGuard.IsLiteral(right) && right.const === left.const ? ExtendsResult.True :\n        IsStructuralRight(right) ? StructuralRight(left, right) :\n            TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n                TypeGuard.IsRecord(right) ? FromRecordRight(left, right) :\n                    TypeGuard.IsString(right) ? FromStringRight(left, right) :\n                        TypeGuard.IsNumber(right) ? FromNumberRight(left, right) :\n                            TypeGuard.IsInteger(right) ? FromIntegerRight(left, right) :\n                                TypeGuard.IsBoolean(right) ? FromBooleanRight(left, right) :\n                                    ExtendsResult.False);\n}\n// ------------------------------------------------------------------\n// Never\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromNeverRight(left, right) {\n    return ExtendsResult.False;\n}\n// prettier-ignore\nfunction FromNever(left, right) {\n    return ExtendsResult.True;\n}\n// ------------------------------------------------------------------\n// Not\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction UnwrapTNot(schema) {\n    let [current, depth] = [schema, 0];\n    while (true) {\n        if (!TypeGuard.IsNot(current))\n            break;\n        current = current.not;\n        depth += 1;\n    }\n    return depth % 2 === 0 ? current : Unknown();\n}\n// prettier-ignore\nfunction FromNot(left, right) {\n    // TypeScript has no concept of negated types, and attempts to correctly check the negated\n    // type at runtime would put TypeBox at odds with TypeScripts ability to statically infer\n    // the type. Instead we unwrap to either unknown or T and continue evaluating.\n    // prettier-ignore\n    return (TypeGuard.IsNot(left) ? Visit(UnwrapTNot(left), right) :\n        TypeGuard.IsNot(right) ? Visit(left, UnwrapTNot(right)) :\n            Throw('Invalid fallthrough for Not'));\n}\n// ------------------------------------------------------------------\n// Null\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromNull(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n            TypeGuard.IsRecord(right) ? FromRecordRight(left, right) :\n                TypeGuard.IsNull(right) ? ExtendsResult.True :\n                    ExtendsResult.False);\n}\n// ------------------------------------------------------------------\n// Number\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromNumberRight(left, right) {\n    return (TypeGuard.IsLiteralNumber(left) ? ExtendsResult.True :\n        TypeGuard.IsNumber(left) || TypeGuard.IsInteger(left) ? ExtendsResult.True :\n            ExtendsResult.False);\n}\n// prettier-ignore\nfunction FromNumber(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n            TypeGuard.IsRecord(right) ? FromRecordRight(left, right) :\n                TypeGuard.IsInteger(right) || TypeGuard.IsNumber(right) ? ExtendsResult.True :\n                    ExtendsResult.False);\n}\n// ------------------------------------------------------------------\n// Object\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction IsObjectPropertyCount(schema, count) {\n    return Object.getOwnPropertyNames(schema.properties).length === count;\n}\n// prettier-ignore\nfunction IsObjectStringLike(schema) {\n    return IsObjectArrayLike(schema);\n}\n// prettier-ignore\nfunction IsObjectSymbolLike(schema) {\n    return IsObjectPropertyCount(schema, 0) || (IsObjectPropertyCount(schema, 1) && 'description' in schema.properties && TypeGuard.IsUnion(schema.properties.description) && schema.properties.description.anyOf.length === 2 && ((TypeGuard.IsString(schema.properties.description.anyOf[0]) &&\n        TypeGuard.IsUndefined(schema.properties.description.anyOf[1])) || (TypeGuard.IsString(schema.properties.description.anyOf[1]) &&\n        TypeGuard.IsUndefined(schema.properties.description.anyOf[0]))));\n}\n// prettier-ignore\nfunction IsObjectNumberLike(schema) {\n    return IsObjectPropertyCount(schema, 0);\n}\n// prettier-ignore\nfunction IsObjectBooleanLike(schema) {\n    return IsObjectPropertyCount(schema, 0);\n}\n// prettier-ignore\nfunction IsObjectBigIntLike(schema) {\n    return IsObjectPropertyCount(schema, 0);\n}\n// prettier-ignore\nfunction IsObjectDateLike(schema) {\n    return IsObjectPropertyCount(schema, 0);\n}\n// prettier-ignore\nfunction IsObjectUint8ArrayLike(schema) {\n    return IsObjectArrayLike(schema);\n}\n// prettier-ignore\nfunction IsObjectFunctionLike(schema) {\n    const length = Number();\n    return IsObjectPropertyCount(schema, 0) || (IsObjectPropertyCount(schema, 1) && 'length' in schema.properties && IntoBooleanResult(Visit(schema.properties['length'], length)) === ExtendsResult.True);\n}\n// prettier-ignore\nfunction IsObjectConstructorLike(schema) {\n    return IsObjectPropertyCount(schema, 0);\n}\n// prettier-ignore\nfunction IsObjectArrayLike(schema) {\n    const length = Number();\n    return IsObjectPropertyCount(schema, 0) || (IsObjectPropertyCount(schema, 1) && 'length' in schema.properties && IntoBooleanResult(Visit(schema.properties['length'], length)) === ExtendsResult.True);\n}\n// prettier-ignore\nfunction IsObjectPromiseLike(schema) {\n    const then = FunctionType([Any()], Any());\n    return IsObjectPropertyCount(schema, 0) || (IsObjectPropertyCount(schema, 1) && 'then' in schema.properties && IntoBooleanResult(Visit(schema.properties['then'], then)) === ExtendsResult.True);\n}\n// ------------------------------------------------------------------\n// Property\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction Property(left, right) {\n    return (Visit(left, right) === ExtendsResult.False ? ExtendsResult.False :\n        TypeGuard.IsOptional(left) && !TypeGuard.IsOptional(right) ? ExtendsResult.False :\n            ExtendsResult.True);\n}\n// prettier-ignore\nfunction FromObjectRight(left, right) {\n    return (TypeGuard.IsUnknown(left) ? ExtendsResult.False :\n        TypeGuard.IsAny(left) ? ExtendsResult.Union : (TypeGuard.IsNever(left) ||\n            (TypeGuard.IsLiteralString(left) && IsObjectStringLike(right)) ||\n            (TypeGuard.IsLiteralNumber(left) && IsObjectNumberLike(right)) ||\n            (TypeGuard.IsLiteralBoolean(left) && IsObjectBooleanLike(right)) ||\n            (TypeGuard.IsSymbol(left) && IsObjectSymbolLike(right)) ||\n            (TypeGuard.IsBigInt(left) && IsObjectBigIntLike(right)) ||\n            (TypeGuard.IsString(left) && IsObjectStringLike(right)) ||\n            (TypeGuard.IsSymbol(left) && IsObjectSymbolLike(right)) ||\n            (TypeGuard.IsNumber(left) && IsObjectNumberLike(right)) ||\n            (TypeGuard.IsInteger(left) && IsObjectNumberLike(right)) ||\n            (TypeGuard.IsBoolean(left) && IsObjectBooleanLike(right)) ||\n            (TypeGuard.IsUint8Array(left) && IsObjectUint8ArrayLike(right)) ||\n            (TypeGuard.IsDate(left) && IsObjectDateLike(right)) ||\n            (TypeGuard.IsConstructor(left) && IsObjectConstructorLike(right)) ||\n            (TypeGuard.IsFunction(left) && IsObjectFunctionLike(right))) ? ExtendsResult.True :\n            (TypeGuard.IsRecord(left) && TypeGuard.IsString(RecordKey(left))) ? (() => {\n                // When expressing a Record with literal key values, the Record is converted into a Object with\n                // the Hint assigned as `Record`. This is used to invert the extends logic.\n                return right[Hint] === 'Record' ? ExtendsResult.True : ExtendsResult.False;\n            })() :\n                (TypeGuard.IsRecord(left) && TypeGuard.IsNumber(RecordKey(left))) ? (() => {\n                    return IsObjectPropertyCount(right, 0) ? ExtendsResult.True : ExtendsResult.False;\n                })() :\n                    ExtendsResult.False);\n}\n// prettier-ignore\nfunction FromObject(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsRecord(right) ? FromRecordRight(left, right) :\n            !TypeGuard.IsObject(right) ? ExtendsResult.False :\n                (() => {\n                    for (const key of Object.getOwnPropertyNames(right.properties)) {\n                        if (!(key in left.properties) && !TypeGuard.IsOptional(right.properties[key])) {\n                            return ExtendsResult.False;\n                        }\n                        if (TypeGuard.IsOptional(right.properties[key])) {\n                            return ExtendsResult.True;\n                        }\n                        if (Property(left.properties[key], right.properties[key]) === ExtendsResult.False) {\n                            return ExtendsResult.False;\n                        }\n                    }\n                    return ExtendsResult.True;\n                })());\n}\n// ------------------------------------------------------------------\n// Promise\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromPromise(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) && IsObjectPromiseLike(right) ? ExtendsResult.True :\n            !TypeGuard.IsPromise(right) ? ExtendsResult.False :\n                IntoBooleanResult(Visit(left.item, right.item)));\n}\n// ------------------------------------------------------------------\n// Record\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction RecordKey(schema) {\n    return (PatternNumberExact in schema.patternProperties ? Number() :\n        PatternStringExact in schema.patternProperties ? String() :\n            Throw('Unknown record key pattern'));\n}\n// prettier-ignore\nfunction RecordValue(schema) {\n    return (PatternNumberExact in schema.patternProperties ? schema.patternProperties[PatternNumberExact] :\n        PatternStringExact in schema.patternProperties ? schema.patternProperties[PatternStringExact] :\n            Throw('Unable to get record value schema'));\n}\n// prettier-ignore\nfunction FromRecordRight(left, right) {\n    const [Key, Value] = [RecordKey(right), RecordValue(right)];\n    return ((TypeGuard.IsLiteralString(left) && TypeGuard.IsNumber(Key) && IntoBooleanResult(Visit(left, Value)) === ExtendsResult.True) ? ExtendsResult.True :\n        TypeGuard.IsUint8Array(left) && TypeGuard.IsNumber(Key) ? Visit(left, Value) :\n            TypeGuard.IsString(left) && TypeGuard.IsNumber(Key) ? Visit(left, Value) :\n                TypeGuard.IsArray(left) && TypeGuard.IsNumber(Key) ? Visit(left, Value) :\n                    TypeGuard.IsObject(left) ? (() => {\n                        for (const key of Object.getOwnPropertyNames(left.properties)) {\n                            if (Property(Value, left.properties[key]) === ExtendsResult.False) {\n                                return ExtendsResult.False;\n                            }\n                        }\n                        return ExtendsResult.True;\n                    })() :\n                        ExtendsResult.False);\n}\n// prettier-ignore\nfunction FromRecord(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n            !TypeGuard.IsRecord(right) ? ExtendsResult.False :\n                Visit(RecordValue(left), RecordValue(right)));\n}\n// ------------------------------------------------------------------\n// RegExp\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromRegExp(left, right) {\n    // Note: RegExp types evaluate as strings, not RegExp objects.\n    // Here we remap either into string and continue evaluating.\n    const L = TypeGuard.IsRegExp(left) ? String() : left;\n    const R = TypeGuard.IsRegExp(right) ? String() : right;\n    return Visit(L, R);\n}\n// ------------------------------------------------------------------\n// String\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromStringRight(left, right) {\n    return (TypeGuard.IsLiteral(left) && ValueGuard.IsString(left.const) ? ExtendsResult.True :\n        TypeGuard.IsString(left) ? ExtendsResult.True :\n            ExtendsResult.False);\n}\n// prettier-ignore\nfunction FromString(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n            TypeGuard.IsRecord(right) ? FromRecordRight(left, right) :\n                TypeGuard.IsString(right) ? ExtendsResult.True :\n                    ExtendsResult.False);\n}\n// ------------------------------------------------------------------\n// Symbol\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromSymbol(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n            TypeGuard.IsRecord(right) ? FromRecordRight(left, right) :\n                TypeGuard.IsSymbol(right) ? ExtendsResult.True :\n                    ExtendsResult.False);\n}\n// ------------------------------------------------------------------\n// TemplateLiteral\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromTemplateLiteral(left, right) {\n    // TemplateLiteral types are resolved to either unions for finite expressions or string\n    // for infinite expressions. Here we call to TemplateLiteralResolver to resolve for\n    // either type and continue evaluating.\n    return (TypeGuard.IsTemplateLiteral(left) ? Visit(TemplateLiteralToUnion(left), right) :\n        TypeGuard.IsTemplateLiteral(right) ? Visit(left, TemplateLiteralToUnion(right)) :\n            Throw('Invalid fallthrough for TemplateLiteral'));\n}\n// ------------------------------------------------------------------\n// Tuple\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction IsArrayOfTuple(left, right) {\n    return (TypeGuard.IsArray(right) &&\n        left.items !== undefined &&\n        left.items.every((schema) => Visit(schema, right.items) === ExtendsResult.True));\n}\n// prettier-ignore\nfunction FromTupleRight(left, right) {\n    return (TypeGuard.IsNever(left) ? ExtendsResult.True :\n        TypeGuard.IsUnknown(left) ? ExtendsResult.False :\n            TypeGuard.IsAny(left) ? ExtendsResult.Union :\n                ExtendsResult.False);\n}\n// prettier-ignore\nfunction FromTuple(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) && IsObjectArrayLike(right) ? ExtendsResult.True :\n            TypeGuard.IsArray(right) && IsArrayOfTuple(left, right) ? ExtendsResult.True :\n                !TypeGuard.IsTuple(right) ? ExtendsResult.False :\n                    (ValueGuard.IsUndefined(left.items) && !ValueGuard.IsUndefined(right.items)) || (!ValueGuard.IsUndefined(left.items) && ValueGuard.IsUndefined(right.items)) ? ExtendsResult.False :\n                        (ValueGuard.IsUndefined(left.items) && !ValueGuard.IsUndefined(right.items)) ? ExtendsResult.True :\n                            left.items.every((schema, index) => Visit(schema, right.items[index]) === ExtendsResult.True) ? ExtendsResult.True :\n                                ExtendsResult.False);\n}\n// ------------------------------------------------------------------\n// Uint8Array\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromUint8Array(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n            TypeGuard.IsRecord(right) ? FromRecordRight(left, right) :\n                TypeGuard.IsUint8Array(right) ? ExtendsResult.True :\n                    ExtendsResult.False);\n}\n// ------------------------------------------------------------------\n// Undefined\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromUndefined(left, right) {\n    return (IsStructuralRight(right) ? StructuralRight(left, right) :\n        TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n            TypeGuard.IsRecord(right) ? FromRecordRight(left, right) :\n                TypeGuard.IsVoid(right) ? FromVoidRight(left, right) :\n                    TypeGuard.IsUndefined(right) ? ExtendsResult.True :\n                        ExtendsResult.False);\n}\n// ------------------------------------------------------------------\n// Union\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromUnionRight(left, right) {\n    return right.anyOf.some((schema) => Visit(left, schema) === ExtendsResult.True)\n        ? ExtendsResult.True\n        : ExtendsResult.False;\n}\n// prettier-ignore\nfunction FromUnion(left, right) {\n    return left.anyOf.every((schema) => Visit(schema, right) === ExtendsResult.True)\n        ? ExtendsResult.True\n        : ExtendsResult.False;\n}\n// ------------------------------------------------------------------\n// Unknown\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromUnknownRight(left, right) {\n    return ExtendsResult.True;\n}\n// prettier-ignore\nfunction FromUnknown(left, right) {\n    return (TypeGuard.IsNever(right) ? FromNeverRight(left, right) :\n        TypeGuard.IsIntersect(right) ? FromIntersectRight(left, right) :\n            TypeGuard.IsUnion(right) ? FromUnionRight(left, right) :\n                TypeGuard.IsAny(right) ? FromAnyRight(left, right) :\n                    TypeGuard.IsString(right) ? FromStringRight(left, right) :\n                        TypeGuard.IsNumber(right) ? FromNumberRight(left, right) :\n                            TypeGuard.IsInteger(right) ? FromIntegerRight(left, right) :\n                                TypeGuard.IsBoolean(right) ? FromBooleanRight(left, right) :\n                                    TypeGuard.IsArray(right) ? FromArrayRight(left, right) :\n                                        TypeGuard.IsTuple(right) ? FromTupleRight(left, right) :\n                                            TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n                                                TypeGuard.IsUnknown(right) ? ExtendsResult.True :\n                                                    ExtendsResult.False);\n}\n// ------------------------------------------------------------------\n// Void\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromVoidRight(left, right) {\n    return (TypeGuard.IsUndefined(left) ? ExtendsResult.True :\n        TypeGuard.IsUndefined(left) ? ExtendsResult.True :\n            ExtendsResult.False);\n}\n// prettier-ignore\nfunction FromVoid(left, right) {\n    return (TypeGuard.IsIntersect(right) ? FromIntersectRight(left, right) :\n        TypeGuard.IsUnion(right) ? FromUnionRight(left, right) :\n            TypeGuard.IsUnknown(right) ? FromUnknownRight(left, right) :\n                TypeGuard.IsAny(right) ? FromAnyRight(left, right) :\n                    TypeGuard.IsObject(right) ? FromObjectRight(left, right) :\n                        TypeGuard.IsVoid(right) ? ExtendsResult.True :\n                            ExtendsResult.False);\n}\n// prettier-ignore\nfunction Visit(left, right) {\n    return (\n    // resolvable\n    (TypeGuard.IsTemplateLiteral(left) || TypeGuard.IsTemplateLiteral(right)) ? FromTemplateLiteral(left, right) :\n        (TypeGuard.IsRegExp(left) || TypeGuard.IsRegExp(right)) ? FromRegExp(left, right) :\n            (TypeGuard.IsNot(left) || TypeGuard.IsNot(right)) ? FromNot(left, right) :\n                // standard\n                TypeGuard.IsAny(left) ? FromAny(left, right) :\n                    TypeGuard.IsArray(left) ? FromArray(left, right) :\n                        TypeGuard.IsBigInt(left) ? FromBigInt(left, right) :\n                            TypeGuard.IsBoolean(left) ? FromBoolean(left, right) :\n                                TypeGuard.IsAsyncIterator(left) ? FromAsyncIterator(left, right) :\n                                    TypeGuard.IsConstructor(left) ? FromConstructor(left, right) :\n                                        TypeGuard.IsDate(left) ? FromDate(left, right) :\n                                            TypeGuard.IsFunction(left) ? FromFunction(left, right) :\n                                                TypeGuard.IsInteger(left) ? FromInteger(left, right) :\n                                                    TypeGuard.IsIntersect(left) ? FromIntersect(left, right) :\n                                                        TypeGuard.IsIterator(left) ? FromIterator(left, right) :\n                                                            TypeGuard.IsLiteral(left) ? FromLiteral(left, right) :\n                                                                TypeGuard.IsNever(left) ? FromNever(left, right) :\n                                                                    TypeGuard.IsNull(left) ? FromNull(left, right) :\n                                                                        TypeGuard.IsNumber(left) ? FromNumber(left, right) :\n                                                                            TypeGuard.IsObject(left) ? FromObject(left, right) :\n                                                                                TypeGuard.IsRecord(left) ? FromRecord(left, right) :\n                                                                                    TypeGuard.IsString(left) ? FromString(left, right) :\n                                                                                        TypeGuard.IsSymbol(left) ? FromSymbol(left, right) :\n                                                                                            TypeGuard.IsTuple(left) ? FromTuple(left, right) :\n                                                                                                TypeGuard.IsPromise(left) ? FromPromise(left, right) :\n                                                                                                    TypeGuard.IsUint8Array(left) ? FromUint8Array(left, right) :\n                                                                                                        TypeGuard.IsUndefined(left) ? FromUndefined(left, right) :\n                                                                                                            TypeGuard.IsUnion(left) ? FromUnion(left, right) :\n                                                                                                                TypeGuard.IsUnknown(left) ? FromUnknown(left, right) :\n                                                                                                                    TypeGuard.IsVoid(left) ? FromVoid(left, right) :\n                                                                                                                        Throw(`Unknown left type operand '${left[Kind]}'`));\n}\nexport function ExtendsCheck(left, right) {\n    return Visit(left, right);\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { Literal } from '../literal/index.mjs';\nimport { Extends } from './extends.mjs';\nimport { Clone } from '../clone/value.mjs';\n// prettier-ignore\nfunction FromPropertyKey(K, U, L, R, options) {\n    return {\n        [K]: Extends(Literal(K), U, L, R, Clone(options))\n    };\n}\n// prettier-ignore\nfunction FromPropertyKeys(K, U, L, R, options) {\n    return K.reduce((Acc, LK) => {\n        return { ...Acc, ...FromPropertyKey(LK, U, L, R, options) };\n    }, {});\n}\n// prettier-ignore\nfunction FromMappedKey(K, U, L, R, options) {\n    return FromPropertyKeys(K.keys, U, L, R, options);\n}\n// prettier-ignore\nexport function ExtendsFromMappedKey(T, U, L, R, options) {\n    const P = FromMappedKey(T, U, L, R, options);\n    return MappedResult(P);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Union } from '../union/index.mjs';\nimport { ExtendsCheck, ExtendsResult } from './extends-check.mjs';\nimport { ExtendsFromMappedKey } from './extends-from-mapped-key.mjs';\nimport { ExtendsFromMappedResult } from './extends-from-mapped-result.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsMappedKey, IsMappedResult } from '../guard/kind.mjs';\n// prettier-ignore\nfunction ExtendsResolve(left, right, trueType, falseType) {\n    const R = ExtendsCheck(left, right);\n    return (R === ExtendsResult.Union ? Union([trueType, falseType]) :\n        R === ExtendsResult.True ? trueType :\n            falseType);\n}\n/** `[Json]` Creates a Conditional type */\nexport function Extends(L, R, T, F, options) {\n    // prettier-ignore\n    return (IsMappedResult(L) ? ExtendsFromMappedResult(L, R, T, F, options) :\n        IsMappedKey(L) ? CreateType(ExtendsFromMappedKey(L, R, T, F, options)) :\n            CreateType(ExtendsResolve(L, R, T, F), options));\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { Extends } from './extends.mjs';\nimport { Clone } from '../clone/value.mjs';\n// prettier-ignore\nfunction FromProperties(P, Right, True, False, options) {\n    const Acc = {};\n    for (const K2 of globalThis.Object.getOwnPropertyNames(P))\n        Acc[K2] = Extends(P[K2], Right, True, False, Clone(options));\n    return Acc;\n}\n// prettier-ignore\nfunction FromMappedResult(Left, Right, True, False, options) {\n    return FromProperties(Left.properties, Right, True, False, options);\n}\n// prettier-ignore\nexport function ExtendsFromMappedResult(Left, Right, True, False, options) {\n    const P = FromMappedResult(Left, Right, True, False, options);\n    return MappedResult(P);\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAAAA;AAAA;AACA,IAAM,MAAM,oBAAI,IAAI;AAEb,SAAS,UAAU;AACtB,SAAO,IAAI,IAAI,GAAG;AACtB;AAEO,SAAS,QAAQ;AACpB,SAAO,IAAI,MAAM;AACrB;AAEO,SAAS,OAAO,QAAQ;AAC3B,SAAO,IAAI,OAAO,MAAM;AAC5B;AAEO,SAAS,IAAI,QAAQ;AACxB,SAAO,IAAI,IAAI,MAAM;AACzB;AAEO,SAASA,KAAI,QAAQ,MAAM;AAC9B,MAAI,IAAI,QAAQ,IAAI;AACxB;AAEO,SAAS,IAAI,QAAQ;AACxB,SAAO,IAAI,IAAI,MAAM;AACzB;;;ACzBA;AAAA;AAAA,eAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA,EAAA,WAAAC;AAAA;AACA,IAAMC,OAAM,oBAAI,IAAI;AAEb,SAASJ,WAAU;AACtB,SAAO,IAAI,IAAII,IAAG;AACtB;AAEO,SAASN,SAAQ;AACpB,SAAOM,KAAI,MAAM;AACrB;AAEO,SAASL,QAAO,MAAM;AACzB,SAAOK,KAAI,OAAO,IAAI;AAC1B;AAEO,SAASF,KAAI,MAAM;AACtB,SAAOE,KAAI,IAAI,IAAI;AACvB;AAEO,SAASD,KAAI,MAAM,MAAM;AAC5B,EAAAC,KAAI,IAAI,MAAM,IAAI;AACtB;AAEO,SAASH,KAAI,MAAM;AACtB,SAAOG,KAAI,IAAI,IAAI;AACvB;;;ACzBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIO,SAAS,eAAe,OAAO,KAAK;AACvC,SAAO,OAAO;AAClB;AAKO,SAAS,gBAAgB,OAAO;AACnC,SAAO,SAAS,KAAK,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,aAAa,KAAK,KAAK,OAAO,iBAAiB;AACjG;AAEO,SAAS,QAAQ,OAAO;AAC3B,SAAO,MAAM,QAAQ,KAAK;AAC9B;AAEO,SAAS,SAAS,OAAO;AAC5B,SAAO,OAAO,UAAU;AAC5B;AAEO,SAAS,UAAU,OAAO;AAC7B,SAAO,OAAO,UAAU;AAC5B;AAEO,SAAS,OAAO,OAAO;AAC1B,SAAO,iBAAiB,WAAW;AACvC;AAEO,SAAS,WAAW,OAAO;AAC9B,SAAO,OAAO,UAAU;AAC5B;AAEO,SAAS,WAAW,OAAO;AAC9B,SAAO,SAAS,KAAK,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,aAAa,KAAK,KAAK,OAAO,YAAY;AAC5F;AAEO,SAAS,OAAO,OAAO;AAC1B,SAAO,UAAU;AACrB;AAEO,SAAS,SAAS,OAAO;AAC5B,SAAO,OAAO,UAAU;AAC5B;AAEO,SAAS,SAAS,OAAO;AAC5B,SAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AAEO,SAAS,SAAS,OAAO;AAC5B,SAAO,iBAAiB,WAAW;AACvC;AAEO,SAAS,SAAS,OAAO;AAC5B,SAAO,OAAO,UAAU;AAC5B;AAEO,SAAS,SAAS,OAAO;AAC5B,SAAO,OAAO,UAAU;AAC5B;AAEO,SAAS,aAAa,OAAO;AAChC,SAAO,iBAAiB,WAAW;AACvC;AAEO,SAAS,YAAY,OAAO;AAC/B,SAAO,UAAU;AACrB;;;ACpEA,SAAS,UAAU,OAAO;AACtB,SAAO,MAAM,IAAI,CAACC,WAAU,MAAMA,MAAK,CAAC;AAC5C;AACA,SAAS,SAAS,OAAO;AACrB,SAAO,IAAI,KAAK,MAAM,QAAQ,CAAC;AACnC;AACA,SAAS,eAAe,OAAO;AAC3B,SAAO,IAAI,WAAW,KAAK;AAC/B;AACA,SAAS,WAAW,OAAO;AACvB,SAAO,IAAI,OAAO,MAAM,QAAQ,MAAM,KAAK;AAC/C;AACA,SAAS,WAAW,OAAO;AACvB,QAAM,SAAS,CAAC;AAChB,aAAW,OAAO,OAAO,oBAAoB,KAAK,GAAG;AACjD,WAAO,GAAG,IAAI,MAAM,MAAM,GAAG,CAAC;AAAA,EAClC;AACA,aAAW,OAAO,OAAO,sBAAsB,KAAK,GAAG;AACnD,WAAO,GAAG,IAAI,MAAM,MAAM,GAAG,CAAC;AAAA,EAClC;AACA,SAAO;AACX;AAEA,SAAS,MAAM,OAAO;AAClB,SAAmB,QAAQ,KAAK,IAAI,UAAU,KAAK,IACpC,OAAO,KAAK,IAAI,SAAS,KAAK,IAC1B,aAAa,KAAK,IAAI,eAAe,KAAK,IACtC,SAAS,KAAK,IAAI,WAAW,KAAK,IAC9B,SAAS,KAAK,IAAI,WAAW,KAAK,IACzC;AACxB;AAEO,SAAS,MAAM,OAAO;AACzB,SAAO,MAAM,KAAK;AACtB;;;AC/BO,SAASC,iBAAgB,OAAO;AACnC,SAAOC,UAAS,KAAK,KAAK,WAAW,OAAO,iBAAiB;AACjE;AAEO,SAASC,YAAW,OAAO;AAC9B,SAAOD,UAAS,KAAK,KAAK,WAAW,OAAO,YAAY;AAC5D;AAgBO,SAAS,UAAU,OAAO;AAC7B,SAAO,iBAAiB,WAAW;AACvC;AAEO,SAASE,QAAO,OAAO;AAC1B,SAAO,iBAAiB,QAAQ,WAAW,OAAO,SAAS,MAAM,QAAQ,CAAC;AAC9E;AAsBO,SAASC,cAAa,OAAO;AAChC,SAAO,iBAAiB,WAAW;AACvC;AAyCO,SAASC,gBAAe,OAAO,KAAK;AACvC,SAAO,OAAO;AAClB;AAKO,SAASC,UAAS,OAAO;AAC5B,SAAO,UAAU,QAAQ,OAAO,UAAU;AAC9C;AAEO,SAASC,SAAQ,OAAO;AAC3B,SAAO,WAAW,MAAM,QAAQ,KAAK,KAAK,CAAC,WAAW,YAAY,OAAO,KAAK;AAClF;AAEO,SAASC,aAAY,OAAO;AAC/B,SAAO,UAAU;AACrB;AAEO,SAASC,QAAO,OAAO;AAC1B,SAAO,UAAU;AACrB;AAEO,SAASC,WAAU,OAAO;AAC7B,SAAO,OAAO,UAAU;AAC5B;AAEO,SAASC,UAAS,OAAO;AAC5B,SAAO,OAAO,UAAU;AAC5B;AAEO,SAAS,UAAU,OAAO;AAC7B,SAAO,WAAW,OAAO,UAAU,KAAK;AAC5C;AAEO,SAASC,UAAS,OAAO;AAC5B,SAAO,OAAO,UAAU;AAC5B;AAEO,SAASC,UAAS,OAAO;AAC5B,SAAO,OAAO,UAAU;AAC5B;AAEO,SAASC,YAAW,OAAO;AAC9B,SAAO,OAAO,UAAU;AAC5B;AAEO,SAASC,UAAS,OAAO;AAC5B,SAAO,OAAO,UAAU;AAC5B;AAEO,SAAS,YAAY,OAAO;AAE/B,SAAQH,UAAS,KAAK,KAClBF,WAAU,KAAK,KACfD,QAAO,KAAK,KACZE,UAAS,KAAK,KACdE,UAAS,KAAK,KACdE,UAAS,KAAK,KACdP,aAAY,KAAK;AACzB;;;AC5JO,IAAI;AAAA,CACV,SAAUQ,mBAAkB;AAYzB,EAAAA,kBAAiB,eAAe;AAKhC,EAAAA,kBAAiB,6BAA6B;AAE9C,EAAAA,kBAAiB,mBAAmB;AAEpC,EAAAA,kBAAiB,WAAW;AAE5B,EAAAA,kBAAiB,gBAAgB;AAEjC,WAAS,wBAAwB,OAAO,KAAK;AACzC,WAAOA,kBAAiB,6BAA6B,OAAO,QAAQ,MAAM,GAAG,MAAM;AAAA,EACvF;AACA,EAAAA,kBAAiB,0BAA0B;AAE3C,WAAS,aAAa,OAAO;AACzB,UAAM,WAAWC,UAAS,KAAK;AAC/B,WAAOD,kBAAiB,mBAAmB,WAAW,YAAY,CAACE,SAAQ,KAAK;AAAA,EACpF;AACA,EAAAF,kBAAiB,eAAe;AAEhC,WAAS,aAAa,OAAO;AACzB,WAAO,aAAa,KAAK,KAAK,EAAE,iBAAiB,SAAS,EAAE,iBAAiB;AAAA,EACjF;AACA,EAAAA,kBAAiB,eAAe;AAEhC,WAAS,aAAa,OAAO;AACzB,WAAOA,kBAAiB,WAAWG,UAAS,KAAK,IAAI,OAAO,SAAS,KAAK;AAAA,EAC9E;AACA,EAAAH,kBAAiB,eAAe;AAEhC,WAAS,WAAW,OAAO;AACvB,UAAM,cAAcI,aAAY,KAAK;AACrC,WAAOJ,kBAAiB,gBAAgB,eAAe,UAAU,OAAO;AAAA,EAC5E;AACA,EAAAA,kBAAiB,aAAa;AAClC,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;;;ACpD9C,SAAS,eAAe,OAAO;AAC3B,SAAO,WAAW,OAAO,OAAO,KAAK,EAAE,IAAI,CAACK,WAAU,UAAUA,MAAK,CAAC;AAC1E;AACA,SAAS,cAAc,OAAO;AAC1B,SAAO;AACX;AACA,SAAS,oBAAoB,OAAO;AAChC,SAAO;AACX;AACA,SAAS,gBAAgB,OAAO;AAC5B,SAAO;AACX;AACA,SAAS,gBAAgB,OAAO;AAC5B,QAAM,SAAS,CAAC;AAChB,aAAW,OAAO,OAAO,oBAAoB,KAAK,GAAG;AACjD,WAAO,GAAG,IAAI,UAAU,MAAM,GAAG,CAAC;AAAA,EACtC;AACA,aAAW,OAAO,OAAO,sBAAsB,KAAK,GAAG;AACnD,WAAO,GAAG,IAAI,UAAU,MAAM,GAAG,CAAC;AAAA,EACtC;AACA,SAAO,WAAW,OAAO,OAAO,MAAM;AAC1C;AAGO,SAAS,UAAU,OAAO;AAC7B,SAAmB,QAAQ,KAAK,IAAI,eAAe,KAAK,IACzC,OAAO,KAAK,IAAI,cAAc,KAAK,IAC/B,aAAa,KAAK,IAAI,oBAAoB,KAAK,IAC3C,SAAS,KAAK,IAAI,gBAAgB,KAAK,IACnC,SAAS,KAAK,IAAI,gBAAgB,KAAK,IAC9C;AACxB;;;AC5BO,SAAS,WAAW,QAAQ,SAAS;AACxC,QAAM,SAAS,YAAY,SAAY,EAAE,GAAG,SAAS,GAAG,OAAO,IAAI;AACnE,UAAQ,iBAAiB,cAAc;AAAA,IACnC,KAAK;AACD,aAAO,UAAU,MAAM;AAAA,IAC3B,KAAK;AACD,aAAO,MAAM,MAAM;AAAA,IACvB;AACI,aAAO;AAAA,EACf;AACJ;;;ACbO,IAAM,gBAAgB,OAAO,IAAI,mBAAmB;AAEpD,IAAM,eAAe,OAAO,IAAI,kBAAkB;AAElD,IAAM,eAAe,OAAO,IAAI,kBAAkB;AAElD,IAAM,OAAO,OAAO,IAAI,cAAc;AAEtC,IAAM,OAAO,OAAO,IAAI,cAAc;;;ACNtC,SAAS,OAAO,UAAU,CAAC,GAAG;AACjC,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,QAAQ,IAAI,KAAK,SAAS,GAAG,OAAO;AACpE;;;ACJO,IAAM,eAAN,cAA2B,MAAM;AAAA,EACpC,YAAY,SAAS;AACjB,UAAM,OAAO;AAAA,EACjB;AACJ;;;ACFO,SAAS,UAAU,GAAG;AACzB,SAAO,WAAW;AAAA,IACd,CAAC,IAAI,GAAG;AAAA,IACR,MAAM;AAAA,EACV,CAAC;AACL;;;ACLO,SAAS,aAAa,YAAY;AACrC,SAAO,WAAW;AAAA,IACd,CAAC,IAAI,GAAG;AAAA,IACR;AAAA,EACJ,CAAC;AACL;;;ACLO,SAASC,OAAM,OAAO,SAAS;AAClC,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,SAAS,MAAM,SAAS,MAAM,GAAG,OAAO;AACxE;;;ACFO,SAAS,cAAc,OAAO,SAAS;AAC1C,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,iBAAiB,MAAM,iBAAiB,MAAM,GAAG,OAAO;AACxF;;;ACFO,SAAS,YAAY,YAAY,SAAS,SAAS;AACtD,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,eAAe,MAAM,eAAe,YAAY,QAAQ,GAAG,OAAO;AAClG;;;ACFO,SAAS,SAAS,YAAY,SAAS,SAAS;AACnD,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,YAAY,MAAM,YAAY,YAAY,QAAQ,GAAG,OAAO;AAC5F;;;ACFO,SAAS,MAAM,SAAS;AAC3B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,SAAS,KAAK,CAAC,EAAE,GAAG,OAAO;AAC3D;;;ACLA;AAAA;AAAA;AAAA;AAAA,iBAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA;AAAA;AAAA,gBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA;AAAA,mBAAAC;AAAA,EAAA;AAAA,oBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA;AAAA,mBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAAC;AAAA,EAAA;AAAA,kBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA,sBAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAGO,SAAS,WAAW,OAAO;AAC9B,SAAkB,SAAS,KAAK,KAAK,MAAM,YAAY,MAAM;AACjE;AAEO,SAAS,WAAW,OAAO;AAC9B,SAAkB,SAAS,KAAK,KAAK,MAAM,YAAY,MAAM;AACjE;AAEO,SAAS,MAAM,OAAO;AACzB,SAAO,SAAS,OAAO,KAAK;AAChC;AAEO,SAAS,WAAW,OAAO;AAC9B,SAAO,SAAS,OAAO,UAAU;AACrC;AAEO,SAASC,SAAQ,OAAO;AAC3B,SAAO,SAAS,OAAO,OAAO;AAClC;AAEO,SAASC,iBAAgB,OAAO;AACnC,SAAO,SAAS,OAAO,eAAe;AAC1C;AAEO,SAASC,UAAS,OAAO;AAC5B,SAAO,SAAS,OAAO,QAAQ;AACnC;AAEO,SAASC,WAAU,OAAO;AAC7B,SAAO,SAAS,OAAO,SAAS;AACpC;AAEO,SAAS,WAAW,OAAO;AAC9B,SAAO,SAAS,OAAO,UAAU;AACrC;AAEO,SAAS,cAAc,OAAO;AACjC,SAAO,SAAS,OAAO,aAAa;AACxC;AAEO,SAASC,QAAO,OAAO;AAC1B,SAAO,SAAS,OAAO,MAAM;AACjC;AAEO,SAASC,YAAW,OAAO;AAC9B,SAAO,SAAS,OAAO,UAAU;AACrC;AAEO,SAAS,SAAS,OAAO;AAC5B,SAAO,SAAS,OAAO,QAAQ;AACnC;AAEO,SAASC,WAAU,OAAO;AAC7B,SAAO,SAAS,OAAO,SAAS;AACpC;AAEO,SAAS,aAAa,OAAO;AAChC,SAAkB,SAAS,KAAK;AACpC;AAEO,SAAS,YAAY,OAAO;AAC/B,SAAO,SAAS,OAAO,WAAW;AACtC;AAEO,SAASC,YAAW,OAAO;AAC9B,SAAO,SAAS,OAAO,UAAU;AACrC;AAEO,SAAS,SAAS,OAAO,MAAM;AAClC,SAAkB,SAAS,KAAK,KAAK,QAAQ,SAAS,MAAM,IAAI,MAAM;AAC1E;AAEO,SAAS,gBAAgB,OAAO;AACnC,SAAO,UAAU,KAAK,KAAgB,SAAS,MAAM,KAAK;AAC9D;AAEO,SAAS,gBAAgB,OAAO;AACnC,SAAO,UAAU,KAAK,KAAgB,SAAS,MAAM,KAAK;AAC9D;AAEO,SAAS,iBAAiB,OAAO;AACpC,SAAO,UAAU,KAAK,KAAgB,UAAU,MAAM,KAAK;AAC/D;AAEO,SAAS,eAAe,OAAO;AAClC,SAAkB,UAAU,KAAK,KAAgB,SAAS,KAAK,KAAgB,SAAS,KAAK;AACjG;AAEO,SAAS,UAAU,OAAO;AAC7B,SAAO,SAAS,OAAO,SAAS;AACpC;AAEO,SAAS,YAAY,OAAO;AAC/B,SAAO,SAAS,OAAO,WAAW;AACtC;AAEO,SAAS,eAAe,OAAO;AAClC,SAAO,SAAS,OAAO,cAAc;AACzC;AAEO,SAAS,QAAQ,OAAO;AAC3B,SAAO,SAAS,OAAO,OAAO;AAClC;AAEO,SAAS,MAAM,OAAO;AACzB,SAAO,SAAS,OAAO,KAAK;AAChC;AAEO,SAASC,QAAO,OAAO;AAC1B,SAAO,SAAS,OAAO,MAAM;AACjC;AAEO,SAASC,UAAS,OAAO;AAC5B,SAAO,SAAS,OAAO,QAAQ;AACnC;AAEO,SAASC,UAAS,OAAO;AAC5B,SAAO,SAAS,OAAO,QAAQ;AACnC;AAEO,SAASC,WAAU,OAAO;AAC7B,SAAO,SAAS,OAAO,SAAS;AACpC;AAEO,SAAS,SAAS,OAAO;AAC5B,SAAO,SAAS,OAAO,QAAQ;AACnC;AAEO,SAAS,YAAY,OAAO;AAC/B,SAAkB,SAAS,KAAK,KAAK,QAAQ,SAAS,MAAM,IAAI,MAAM;AAC1E;AAEO,SAAS,MAAM,OAAO;AACzB,SAAO,SAAS,OAAO,KAAK;AAChC;AAEO,SAASC,UAAS,OAAO;AAC5B,SAAO,SAAS,OAAO,QAAQ;AACnC;AAEO,SAASC,UAAS,OAAO;AAC5B,SAAO,SAAS,OAAO,QAAQ;AACnC;AAEO,SAASC,UAAS,OAAO;AAC5B,SAAO,SAAS,OAAO,QAAQ;AACnC;AAEO,SAAS,kBAAkB,OAAO;AACrC,SAAO,SAAS,OAAO,iBAAiB;AAC5C;AAEO,SAAS,OAAO,OAAO;AAC1B,SAAO,SAAS,OAAO,MAAM;AACjC;AAEO,SAAS,YAAY,OAAO;AAC/B,SAAkB,SAAS,KAAK,KAAK,iBAAiB;AAC1D;AAEO,SAAS,QAAQ,OAAO;AAC3B,SAAO,SAAS,OAAO,OAAO;AAClC;AAEO,SAASC,aAAY,OAAO;AAC/B,SAAO,SAAS,OAAO,WAAW;AACtC;AAEO,SAAS,QAAQ,OAAO;AAC3B,SAAO,SAAS,OAAO,OAAO;AAClC;AAEO,SAASC,cAAa,OAAO;AAChC,SAAO,SAAS,OAAO,YAAY;AACvC;AAEO,SAAS,UAAU,OAAO;AAC7B,SAAO,SAAS,OAAO,SAAS;AACpC;AAEO,SAAS,SAAS,OAAO;AAC5B,SAAO,SAAS,OAAO,QAAQ;AACnC;AAEO,SAAS,OAAO,OAAO;AAC1B,SAAO,SAAS,OAAO,MAAM;AACjC;AAEO,SAAS,OAAO,OAAO;AAC1B,SAAkB,SAAS,KAAK,KAAK,QAAQ,SAAoB,SAAS,MAAM,IAAI,CAAC;AACzF;AAEO,SAAS,SAAS,OAAO;AAE5B,SAAQ,MAAM,KAAK,KACf,WAAW,KAAK,KAChBhB,SAAQ,KAAK,KACbG,WAAU,KAAK,KACfD,UAAS,KAAK,KACdD,iBAAgB,KAAK,KACrB,WAAW,KAAK,KAChB,cAAc,KAAK,KACnBG,QAAO,KAAK,KACZC,YAAW,KAAK,KAChBC,WAAU,KAAK,KACf,YAAY,KAAK,KACjBC,YAAW,KAAK,KAChB,UAAU,KAAK,KACf,YAAY,KAAK,KACjB,eAAe,KAAK,KACpB,QAAQ,KAAK,KACb,MAAM,KAAK,KACXC,QAAO,KAAK,KACZC,UAAS,KAAK,KACdC,UAAS,KAAK,KACdC,WAAU,KAAK,KACf,SAAS,KAAK,KACd,MAAM,KAAK,KACXC,UAAS,KAAK,KACdC,UAAS,KAAK,KACdC,UAAS,KAAK,KACd,kBAAkB,KAAK,KACvB,OAAO,KAAK,KACZ,QAAQ,KAAK,KACbC,aAAY,KAAK,KACjB,QAAQ,KAAK,KACbC,cAAa,KAAK,KAClB,UAAU,KAAK,KACf,SAAS,KAAK,KACd,OAAO,KAAK,KACZ,OAAO,KAAK;AACpB;;;AC1OA,SAAS,WAAW,OAAO,KAAK;AAC5B,QAAM,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,IAAI;AAC9B,SAAO;AACX;AAEO,SAAS,QAAQ,OAAO,MAAM;AACjC,SAAO,KAAK,OAAO,CAAC,KAAK,QAAQ,WAAW,KAAK,GAAG,GAAG,KAAK;AAChE;;;ACJO,SAAS,SAAS,QAAQ,YAAY,SAAS;AAClD,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,YAAY,QAAQ,WAAW,GAAG,OAAO;AACzE;;;ACKO,SAAS,gBAAgB,GAAG,UAAU,CAAC,GAAG;AAC7C,QAAM,aAAa,EAAE,MAAM,CAAC,WAAWC,UAAS,MAAM,CAAC;AACvD,QAAM,8BAA8B,SAAS,QAAQ,qBAAqB,IACpE,EAAE,uBAAuB,QAAQ,sBAAsB,IACvD,CAAC;AACP,SAAO,WAAY,QAAQ,0BAA0B,SAAS,SAAS,QAAQ,qBAAqB,KAAK,aACnG,EAAE,GAAG,6BAA6B,CAAC,IAAI,GAAG,aAAa,MAAM,UAAU,OAAO,EAAE,IAChF,EAAE,GAAG,6BAA6B,CAAC,IAAI,GAAG,aAAa,OAAO,EAAE,GAAI,OAAO;AACrF;;;ACPA,SAAS,oBAAoB,OAAO;AAChC,SAAO,MAAM,MAAM,UAAQ,WAAW,IAAI,CAAC;AAC/C;AAEA,SAAS,uBAAuB,MAAM;AAClC,SAAQ,QAAQ,MAAM,CAAC,YAAY,CAAC;AACxC;AAEA,SAAS,uBAAuB,OAAO;AACnC,SAAO,MAAM,IAAI,UAAQ,WAAW,IAAI,IAAI,uBAAuB,IAAI,IAAI,IAAI;AACnF;AAEA,SAAS,iBAAiB,OAAO,SAAS;AACtC,SAAQ,oBAAoB,KAAK,IAC3B,SAAS,gBAAgB,uBAAuB,KAAK,GAAG,OAAO,CAAC,IAChE,gBAAgB,uBAAuB,KAAK,GAAG,OAAO;AAChE;AAEO,SAAS,mBAAmB,OAAO,UAAU,CAAC,GAAG;AACpD,MAAI,MAAM,WAAW;AACjB,WAAO,WAAW,MAAM,CAAC,GAAG,OAAO;AACvC,MAAI,MAAM,WAAW;AACjB,WAAO,MAAM,OAAO;AACxB,MAAI,MAAM,KAAK,CAAC,WAAW,YAAY,MAAM,CAAC;AAC1C,UAAM,IAAI,MAAM,kCAAkC;AACtD,SAAO,iBAAiB,OAAO,OAAO;AAC1C;;;AC7BO,SAAS,UAAU,OAAO,SAAS;AACtC,MAAI,MAAM,WAAW;AACjB,WAAO,WAAW,MAAM,CAAC,GAAG,OAAO;AACvC,MAAI,MAAM,WAAW;AACjB,WAAO,MAAM,OAAO;AACxB,MAAI,MAAM,KAAK,CAAC,WAAW,YAAY,MAAM,CAAC;AAC1C,UAAM,IAAI,MAAM,kCAAkC;AACtD,SAAO,gBAAgB,OAAO,OAAO;AACzC;;;ACdO,SAAS,YAAY,GAAG,SAAS;AACpC,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,SAAS,OAAO,EAAE,GAAG,OAAO;AAC5D;;;ACOA,SAAS,gBAAgB,OAAO;AAC5B,SAAO,MAAM,KAAK,UAAQ,WAAW,IAAI,CAAC;AAC9C;AAEA,SAASC,wBAAuB,OAAO;AACnC,SAAO,MAAM,IAAI,UAAQ,WAAW,IAAI,IAAIC,wBAAuB,IAAI,IAAI,IAAI;AACnF;AAEA,SAASA,wBAAuB,GAAG;AAC/B,SAAQ,QAAQ,GAAG,CAAC,YAAY,CAAC;AACrC;AAEA,SAAS,aAAa,OAAO,SAAS;AAClC,QAAM,aAAa,gBAAgB,KAAK;AACxC,SAAQ,aACF,SAAS,YAAYD,wBAAuB,KAAK,GAAG,OAAO,CAAC,IAC5D,YAAYA,wBAAuB,KAAK,GAAG,OAAO;AAC5D;AAEO,SAAS,eAAe,GAAG,SAAS;AAEvC,SAAQ,EAAE,WAAW,IAAI,WAAW,EAAE,CAAC,GAAG,OAAO,IAC7C,EAAE,WAAW,IAAI,MAAM,OAAO,IAC1B,aAAa,GAAG,OAAO;AACnC;;;AC/BO,SAAS,MAAM,OAAO,SAAS;AAElC,SAAQ,MAAM,WAAW,IAAI,MAAM,OAAO,IACtC,MAAM,WAAW,IAAI,WAAW,MAAM,CAAC,GAAG,OAAO,IAC7C,YAAY,OAAO,OAAO;AACtC;;;ACLO,IAAM,6BAAN,cAAyC,aAAa;AAC7D;AAUA,SAAS,SAAS,SAAS;AACvB,SAAO,QACF,QAAQ,SAAS,GAAG,EACpB,QAAQ,SAAS,GAAG,EACpB,QAAQ,SAAS,GAAG,EACpB,QAAQ,SAAS,GAAG,EACpB,QAAQ,SAAS,GAAG,EACpB,QAAQ,SAAS,GAAG;AAC7B;AAIA,SAAS,aAAa,SAAS,OAAO,MAAM;AACxC,SAAO,QAAQ,KAAK,MAAM,QAAQ,QAAQ,WAAW,QAAQ,CAAC,MAAM;AACxE;AACA,SAAS,YAAY,SAAS,OAAO;AACjC,SAAO,aAAa,SAAS,OAAO,GAAG;AAC3C;AACA,SAAS,aAAa,SAAS,OAAO;AAClC,SAAO,aAAa,SAAS,OAAO,GAAG;AAC3C;AACA,SAAS,YAAY,SAAS,OAAO;AACjC,SAAO,aAAa,SAAS,OAAO,GAAG;AAC3C;AAIA,SAAS,QAAQ,SAAS;AACtB,MAAI,EAAE,YAAY,SAAS,CAAC,KAAK,aAAa,SAAS,QAAQ,SAAS,CAAC;AACrE,WAAO;AACX,MAAI,QAAQ;AACZ,WAAS,QAAQ,GAAG,QAAQ,QAAQ,QAAQ,SAAS;AACjD,QAAI,YAAY,SAAS,KAAK;AAC1B,eAAS;AACb,QAAI,aAAa,SAAS,KAAK;AAC3B,eAAS;AACb,QAAI,UAAU,KAAK,UAAU,QAAQ,SAAS;AAC1C,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAEA,SAAS,QAAQ,SAAS;AACtB,SAAO,QAAQ,MAAM,GAAG,QAAQ,SAAS,CAAC;AAC9C;AAEA,SAAS,eAAe,SAAS;AAC7B,MAAI,QAAQ;AACZ,WAAS,QAAQ,GAAG,QAAQ,QAAQ,QAAQ,SAAS;AACjD,QAAI,YAAY,SAAS,KAAK;AAC1B,eAAS;AACb,QAAI,aAAa,SAAS,KAAK;AAC3B,eAAS;AACb,QAAI,YAAY,SAAS,KAAK,KAAK,UAAU;AACzC,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAEA,SAAS,gBAAgB,SAAS;AAC9B,WAAS,QAAQ,GAAG,QAAQ,QAAQ,QAAQ,SAAS;AACjD,QAAI,YAAY,SAAS,KAAK;AAC1B,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAEA,SAAS,GAAG,SAAS;AACjB,MAAI,CAAC,OAAO,KAAK,IAAI,CAAC,GAAG,CAAC;AAC1B,QAAM,cAAc,CAAC;AACrB,WAAS,QAAQ,GAAG,QAAQ,QAAQ,QAAQ,SAAS;AACjD,QAAI,YAAY,SAAS,KAAK;AAC1B,eAAS;AACb,QAAI,aAAa,SAAS,KAAK;AAC3B,eAAS;AACb,QAAI,YAAY,SAAS,KAAK,KAAK,UAAU,GAAG;AAC5C,YAAME,SAAQ,QAAQ,MAAM,OAAO,KAAK;AACxC,UAAIA,OAAM,SAAS;AACf,oBAAY,KAAK,qBAAqBA,MAAK,CAAC;AAChD,cAAQ,QAAQ;AAAA,IACpB;AAAA,EACJ;AACA,QAAM,QAAQ,QAAQ,MAAM,KAAK;AACjC,MAAI,MAAM,SAAS;AACf,gBAAY,KAAK,qBAAqB,KAAK,CAAC;AAChD,MAAI,YAAY,WAAW;AACvB,WAAO,EAAE,MAAM,SAAS,OAAO,GAAG;AACtC,MAAI,YAAY,WAAW;AACvB,WAAO,YAAY,CAAC;AACxB,SAAO,EAAE,MAAM,MAAM,MAAM,YAAY;AAC3C;AAEA,SAAS,IAAI,SAAS;AAClB,WAAS,MAAM,OAAO,OAAO;AACzB,QAAI,CAAC,YAAY,OAAO,KAAK;AACzB,YAAM,IAAI,2BAA2B,wDAAwD;AACjG,QAAI,QAAQ;AACZ,aAAS,OAAO,OAAO,OAAO,MAAM,QAAQ,QAAQ;AAChD,UAAI,YAAY,OAAO,IAAI;AACvB,iBAAS;AACb,UAAI,aAAa,OAAO,IAAI;AACxB,iBAAS;AACb,UAAI,UAAU;AACV,eAAO,CAAC,OAAO,IAAI;AAAA,IAC3B;AACA,UAAM,IAAI,2BAA2B,4DAA4D;AAAA,EACrG;AACA,WAAS,MAAMC,UAAS,OAAO;AAC3B,aAAS,OAAO,OAAO,OAAOA,SAAQ,QAAQ,QAAQ;AAClD,UAAI,YAAYA,UAAS,IAAI;AACzB,eAAO,CAAC,OAAO,IAAI;AAAA,IAC3B;AACA,WAAO,CAAC,OAAOA,SAAQ,MAAM;AAAA,EACjC;AACA,QAAM,cAAc,CAAC;AACrB,WAAS,QAAQ,GAAG,QAAQ,QAAQ,QAAQ,SAAS;AACjD,QAAI,YAAY,SAAS,KAAK,GAAG;AAC7B,YAAM,CAAC,OAAO,GAAG,IAAI,MAAM,SAAS,KAAK;AACzC,YAAM,QAAQ,QAAQ,MAAM,OAAO,MAAM,CAAC;AAC1C,kBAAY,KAAK,qBAAqB,KAAK,CAAC;AAC5C,cAAQ;AAAA,IACZ,OACK;AACD,YAAM,CAAC,OAAO,GAAG,IAAI,MAAM,SAAS,KAAK;AACzC,YAAM,QAAQ,QAAQ,MAAM,OAAO,GAAG;AACtC,UAAI,MAAM,SAAS;AACf,oBAAY,KAAK,qBAAqB,KAAK,CAAC;AAChD,cAAQ,MAAM;AAAA,IAClB;AAAA,EACJ;AACA,SAAS,YAAY,WAAW,IAAK,EAAE,MAAM,SAAS,OAAO,GAAG,IAC3D,YAAY,WAAW,IAAK,YAAY,CAAC,IACtC,EAAE,MAAM,OAAO,MAAM,YAAY;AAC7C;AAKO,SAAS,qBAAqB,SAAS;AAE1C,SAAQ,QAAQ,OAAO,IAAI,qBAAqB,QAAQ,OAAO,CAAC,IAC5D,eAAe,OAAO,IAAI,GAAG,OAAO,IAChC,gBAAgB,OAAO,IAAI,IAAI,OAAO,IAClC,EAAE,MAAM,SAAS,OAAO,SAAS,OAAO,EAAE;AAC1D;AAKO,SAAS,0BAA0B,SAAS;AAC/C,SAAO,qBAAqB,QAAQ,MAAM,GAAG,QAAQ,SAAS,CAAC,CAAC;AACpE;;;ACjKO,IAAM,6BAAN,cAAyC,aAAa;AAC7D;AAKA,SAAS,mBAAmB,YAAY;AACpC,SAAQ,WAAW,SAAS,QACxB,WAAW,KAAK,WAAW,KAC3B,WAAW,KAAK,CAAC,EAAE,SAAS,WAC5B,WAAW,KAAK,CAAC,EAAE,UAAU,OAC7B,WAAW,KAAK,CAAC,EAAE,SAAS,WAC5B,WAAW,KAAK,CAAC,EAAE,UAAU;AACrC;AAEA,SAAS,oBAAoB,YAAY;AACrC,SAAQ,WAAW,SAAS,QACxB,WAAW,KAAK,WAAW,KAC3B,WAAW,KAAK,CAAC,EAAE,SAAS,WAC5B,WAAW,KAAK,CAAC,EAAE,UAAU,UAC7B,WAAW,KAAK,CAAC,EAAE,SAAS,WAC5B,WAAW,KAAK,CAAC,EAAE,UAAU;AACrC;AAEA,SAAS,mBAAmB,YAAY;AACpC,SAAO,WAAW,SAAS,WAAW,WAAW,UAAU;AAC/D;AAKO,SAAS,kCAAkC,YAAY;AAC1D,SAAQ,mBAAmB,UAAU,KAAK,mBAAmB,UAAU,IAAI,QACvE,oBAAoB,UAAU,IAAI,OAC7B,WAAW,SAAS,QAAS,WAAW,KAAK,MAAM,CAAC,SAAS,kCAAkC,IAAI,CAAC,IAChG,WAAW,SAAS,OAAQ,WAAW,KAAK,MAAM,CAAC,SAAS,kCAAkC,IAAI,CAAC,IAC/F,WAAW,SAAS,UAAW,QAC3B,MAAM;AAAE,UAAM,IAAI,2BAA2B,yBAAyB;AAAA,EAAG,GAAG;AACrG;AAEO,SAAS,wBAAwB,QAAQ;AAC5C,QAAM,aAAa,0BAA0B,OAAO,OAAO;AAC3D,SAAO,kCAAkC,UAAU;AACvD;;;AC1CO,IAAM,+BAAN,cAA2C,aAAa;AAC/D;AAKA,UAAU,eAAe,QAAQ;AAC7B,MAAI,OAAO,WAAW;AAClB,WAAO,OAAO,OAAO,CAAC;AAC1B,aAAW,QAAQ,OAAO,CAAC,GAAG;AAC1B,eAAW,SAAS,eAAe,OAAO,MAAM,CAAC,CAAC,GAAG;AACjD,YAAM,GAAG,IAAI,GAAG,KAAK;AAAA,IACzB;AAAA,EACJ;AACJ;AAEA,UAAU,YAAY,YAAY;AAC9B,SAAO,OAAO,eAAe,WAAW,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,kCAAkC,IAAI,CAAC,CAAC,CAAC;AAC5G;AAEA,UAAU,WAAW,YAAY;AAC7B,aAAW,QAAQ,WAAW;AAC1B,WAAO,kCAAkC,IAAI;AACrD;AAEA,UAAU,cAAc,YAAY;AAChC,SAAO,MAAM,WAAW;AAC5B;AACO,UAAU,kCAAkC,YAAY;AAC3D,SAAO,WAAW,SAAS,QACrB,OAAO,YAAY,UAAU,IAC7B,WAAW,SAAS,OAChB,OAAO,WAAW,UAAU,IAC5B,WAAW,SAAS,UAChB,OAAO,cAAc,UAAU,KAC9B,MAAM;AACL,UAAM,IAAI,6BAA6B,oBAAoB;AAAA,EAC/D,GAAG;AACnB;AAEO,SAAS,wBAAwB,QAAQ;AAC5C,QAAM,aAAa,0BAA0B,OAAO,OAAO;AAE3D,SAAQ,kCAAkC,UAAU,IAC9C,CAAC,GAAG,kCAAkC,UAAU,CAAC,IACjD,CAAC;AACX;;;ACjDO,SAAS,QAAQ,OAAO,SAAS;AACpC,SAAO,WAAW;AAAA,IACd,CAAC,IAAI,GAAG;AAAA,IACR,OAAO;AAAA,IACP,MAAM,OAAO;AAAA,EACjB,GAAG,OAAO;AACd;;;ACNO,SAAS,QAAQ,SAAS;AAC7B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,WAAW,MAAM,UAAU,GAAG,OAAO;AACrE;;;ACFO,SAAS,OAAO,SAAS;AAC5B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,UAAU,MAAM,SAAS,GAAG,OAAO;AACnE;;;ACFO,SAASC,QAAO,SAAS;AAC5B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,UAAU,MAAM,SAAS,GAAG,OAAO;AACnE;;;ACFO,SAAS,OAAO,SAAS;AAC5B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,UAAU,MAAM,SAAS,GAAG,OAAO;AACnE;;;ACMA,UAAU,UAAU,QAAQ;AACxB,QAAM,OAAO,OAAO,KAAK,EAAE,QAAQ,QAAQ,EAAE;AAC7C,SAAQ,SAAS,YAAY,MAAM,QAAQ,IACvC,SAAS,WAAW,MAAMC,QAAO,IAC7B,SAAS,WAAW,MAAM,OAAO,IAC7B,SAAS,WAAW,MAAM,OAAO,IAC7B,OAAO,MAAM;AACT,UAAM,WAAW,KAAK,MAAM,GAAG,EAAE,IAAI,CAAC,YAAY,QAAQ,QAAQ,KAAK,CAAC,CAAC;AACzE,WAAQ,SAAS,WAAW,IAAI,MAAM,IAClC,SAAS,WAAW,IAAI,SAAS,CAAC,IAC9B,eAAe,QAAQ;AAAA,EACnC,GAAG;AACvB;AAEA,UAAU,aAAa,QAAQ;AAC3B,MAAI,OAAO,CAAC,MAAM,KAAK;AACnB,UAAM,IAAI,QAAQ,GAAG;AACrB,UAAM,IAAI,WAAW,OAAO,MAAM,CAAC,CAAC;AACpC,WAAO,OAAO,CAAC,GAAG,GAAG,CAAC;AAAA,EAC1B;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,QAAI,OAAO,CAAC,MAAM,KAAK;AACnB,YAAM,IAAI,UAAU,OAAO,MAAM,GAAG,CAAC,CAAC;AACtC,YAAM,IAAI,WAAW,OAAO,MAAM,IAAI,CAAC,CAAC;AACxC,aAAO,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IAC7B;AAAA,EACJ;AACA,QAAM,QAAQ,MAAM;AACxB;AAEA,UAAU,WAAW,QAAQ;AACzB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,QAAI,OAAO,CAAC,MAAM,KAAK;AACnB,YAAM,IAAI,QAAQ,OAAO,MAAM,GAAG,CAAC,CAAC;AACpC,YAAM,IAAI,aAAa,OAAO,MAAM,CAAC,CAAC;AACtC,aAAO,OAAO,CAAC,GAAG,GAAG,CAAC;AAAA,IAC1B;AAAA,EACJ;AACA,QAAM,QAAQ,MAAM;AACxB;AAEO,SAAS,sBAAsB,QAAQ;AAC1C,SAAO,CAAC,GAAG,WAAW,MAAM,CAAC;AACjC;;;ACtDO,IAAM,iBAAiB;AACvB,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AACtB,IAAM,eAAe;AACrB,IAAM,sBAAsB,IAAI,cAAc;AAC9C,IAAM,qBAAqB,IAAI,aAAa;AAC5C,IAAM,qBAAqB,IAAI,aAAa;AAC5C,IAAM,oBAAoB,IAAI,YAAY;;;ACG1C,IAAM,8BAAN,cAA0C,aAAa;AAC9D;AAIA,SAAS,OAAO,OAAO;AACnB,SAAO,MAAM,QAAQ,uBAAuB,MAAM;AACtD;AAEA,SAASC,OAAM,QAAQ,KAAK;AACxB,SAAQ,kBAAkB,MAAM,IAAI,OAAO,QAAQ,MAAM,GAAG,OAAO,QAAQ,SAAS,CAAC,IACjF,QAAQ,MAAM,IAAI,IAAI,OAAO,MAAM,IAAI,CAACC,YAAWD,OAAMC,SAAQ,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,MAC5EC,UAAS,MAAM,IAAI,GAAG,GAAG,GAAG,aAAa,KACrCC,WAAU,MAAM,IAAI,GAAG,GAAG,GAAG,aAAa,KACtCC,UAAS,MAAM,IAAI,GAAG,GAAG,GAAG,aAAa,KACrCC,UAAS,MAAM,IAAI,GAAG,GAAG,GAAG,aAAa,KACrC,UAAU,MAAM,IAAI,GAAG,GAAG,GAAG,OAAO,OAAO,MAAM,SAAS,CAAC,CAAC,KACxDC,WAAU,MAAM,IAAI,GAAG,GAAG,GAAG,cAAc,MACtC,MAAM;AAAE,UAAM,IAAI,4BAA4B,oBAAoB,OAAO,IAAI,CAAC,GAAG;AAAA,EAAG,GAAG;AAC5H;AACO,SAAS,uBAAuB,OAAO;AAC1C,SAAO,IAAI,MAAM,IAAI,CAAC,WAAWN,OAAM,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC;AAChE;;;AC5BO,SAAS,uBAAuB,QAAQ;AAC3C,QAAM,IAAI,wBAAwB,MAAM;AACxC,QAAM,IAAI,EAAE,IAAI,CAAC,MAAM,QAAQ,CAAC,CAAC;AACjC,SAAO,eAAe,CAAC;AAC3B;;;ACDO,SAAS,gBAAgB,YAAY,SAAS;AACjD,QAAM,UAAU,SAAS,UAAU,IAC7B,uBAAuB,sBAAsB,UAAU,CAAC,IACxD,uBAAuB,UAAU;AACvC,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,mBAAmB,MAAM,UAAU,QAAQ,GAAG,OAAO;AACrF;;;ACNA,SAAS,oBAAoB,iBAAiB;AAC1C,QAAM,OAAO,wBAAwB,eAAe;AACpD,SAAO,KAAK,IAAI,SAAO,IAAI,SAAS,CAAC;AACzC;AAEA,SAASO,WAAU,OAAO;AACtB,QAAM,SAAS,CAAC;AAChB,aAAW,QAAQ;AACf,WAAO,KAAK,GAAG,kBAAkB,IAAI,CAAC;AAC1C,SAAO;AACX;AAEA,SAAS,YAAY,cAAc;AAC/B,SAAQ,CAAC,aAAa,SAAS,CAAC;AAEpC;AAGO,SAAS,kBAAkB,MAAM;AACpC,SAAO,CAAC,GAAG,IAAI,IAAK,kBAAkB,IAAI,IAAI,oBAAoB,IAAI,IAC9D,QAAQ,IAAI,IAAIA,WAAU,KAAK,KAAK,IAChC,UAAU,IAAI,IAAI,YAAY,KAAK,KAAK,IACpCC,UAAS,IAAI,IAAI,CAAC,UAAU,IACxBC,WAAU,IAAI,IAAI,CAAC,UAAU,IACzB,CAAC,CAAE,CAAC;AAChC;;;AC3BA,SAAS,eAAe,MAAM,YAAY,SAAS;AAC/C,QAAM,SAAS,CAAC;AAChB,aAAW,MAAM,OAAO,oBAAoB,UAAU,GAAG;AACrD,WAAO,EAAE,IAAI,MAAM,MAAM,kBAAkB,WAAW,EAAE,CAAC,GAAG,OAAO;AAAA,EACvE;AACA,SAAO;AACX;AAEA,SAAS,iBAAiB,MAAM,cAAc,SAAS;AACnD,SAAO,eAAe,MAAM,aAAa,YAAY,OAAO;AAChE;AAEO,SAAS,sBAAsB,MAAM,cAAc,SAAS;AAC/D,QAAM,aAAa,iBAAiB,MAAM,cAAc,OAAO;AAC/D,SAAO,aAAa,UAAU;AAClC;;;ACLA,SAAS,SAAS,OAAO,KAAK;AAC1B,SAAO,MAAM,IAAI,UAAQ,qBAAqB,MAAM,GAAG,CAAC;AAC5D;AAEA,SAAS,kBAAkB,OAAO;AAC9B,SAAO,MAAM,OAAO,UAAQ,CAAC,QAAQ,IAAI,CAAC;AAC9C;AAEA,SAAS,cAAc,OAAO,KAAK;AAC/B,SAAQ,mBAAmB,kBAAkB,SAAS,OAAO,GAAG,CAAC,CAAC;AACtE;AAEA,SAAS,cAAc,OAAO;AAC1B,SAAQ,MAAM,KAAK,OAAK,QAAQ,CAAC,CAAC,IAC5B,CAAC,IACD;AACV;AAEA,SAASC,WAAU,OAAO,KAAK;AAC3B,SAAQ,eAAe,cAAc,SAAS,OAAO,GAAG,CAAC,CAAC;AAC9D;AAEA,SAAS,UAAU,OAAO,KAAK;AAC3B,SAAQ,OAAO,QAAQ,MAAM,GAAG,IAC5B,QAAQ,aAAa,eAAe,KAAK,IACrC,MAAM;AAClB;AAEA,SAAS,UAAU,MAAM,KAAK;AAC1B,SAAQ,QAAQ,aACV,OACA,MAAM;AAChB;AAEA,SAAS,aAAa,YAAY,aAAa;AAC3C,SAAQ,eAAe,aAAa,WAAW,WAAW,IAAI,MAAM;AACxE;AAEO,SAAS,qBAAqB,MAAM,aAAa;AACpD,SAAQ,YAAY,IAAI,IAAI,cAAc,KAAK,OAAO,WAAW,IAC7D,QAAQ,IAAI,IAAIA,WAAU,KAAK,OAAO,WAAW,IAC7C,QAAQ,IAAI,IAAI,UAAU,KAAK,SAAS,CAAC,GAAG,WAAW,IACnDC,SAAQ,IAAI,IAAI,UAAU,KAAK,OAAO,WAAW,IAC7CC,UAAS,IAAI,IAAI,aAAa,KAAK,YAAY,WAAW,IACtD,MAAM;AAC9B;AAEO,SAAS,sBAAsB,MAAM,cAAc;AACtD,SAAO,aAAa,IAAI,iBAAe,qBAAqB,MAAM,WAAW,CAAC;AAClF;AAEA,SAAS,WAAW,MAAM,cAAc;AACpC,SAAQ,eAAe,sBAAsB,MAAM,YAAY,CAAC;AACpE;AAEO,SAAS,kBAAkB,MAAM,KAAK;AACzC,SAAO,SAAS,SAAS,CAAC,MAAM,GAAG,CAAC;AACxC;AAEO,SAAS,MAAM,MAAM,KAAK,SAAS;AAEtC,MAAI,MAAM,IAAI,KAAK,MAAM,GAAG,GAAG;AAC3B,UAAM,QAAQ;AACd,QAAI,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,GAAG;AAChC,YAAM,IAAI,aAAa,KAAK;AAChC,WAAO,SAAS,SAAS,CAAC,MAAM,GAAG,CAAC;AAAA,EACxC;AAEA,MAAI,eAAe,GAAG;AAClB,WAAO,sBAAsB,MAAM,KAAK,OAAO;AACnD,MAAI,YAAY,GAAG;AACf,WAAO,mBAAmB,MAAM,KAAK,OAAO;AAEhD,SAAO,WAAW,SAAS,GAAG,IACxB,WAAW,MAAM,kBAAkB,GAAG,CAAC,IACvC,WAAW,MAAM,GAAG,GAAG,OAAO;AACxC;;;ACtFA,SAAS,uBAAuB,MAAM,KAAK,SAAS;AAChD,SAAO,EAAE,CAAC,GAAG,GAAG,MAAM,MAAM,CAAC,GAAG,GAAG,MAAM,OAAO,CAAC,EAAE;AACvD;AAEA,SAAS,wBAAwB,MAAM,cAAc,SAAS;AAC1D,SAAO,aAAa,OAAO,CAAC,QAAQ,SAAS;AACzC,WAAO,EAAE,GAAG,QAAQ,GAAG,uBAAuB,MAAM,MAAM,OAAO,EAAE;AAAA,EACvE,GAAG,CAAC,CAAC;AACT;AAEA,SAAS,sBAAsB,MAAM,WAAW,SAAS;AACrD,SAAO,wBAAwB,MAAM,UAAU,MAAM,OAAO;AAChE;AAEO,SAAS,mBAAmB,MAAM,WAAW,SAAS;AACzD,QAAM,aAAa,sBAAsB,MAAM,WAAW,OAAO;AACjE,SAAO,aAAa,UAAU;AAClC;;;AClBO,SAAS,SAAS,OAAO,SAAS;AACrC,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,YAAY,MAAM,YAAY,MAAM,GAAG,OAAO;AAC9E;;;ACCA,SAAS,aAAa,YAAY;AAC9B,QAAM,OAAO,CAAC;AACd,WAAS,OAAO,YAAY;AACxB,QAAI,CAAC,WAAW,WAAW,GAAG,CAAC;AAC3B,WAAK,KAAK,GAAG;AAAA,EACrB;AACA,SAAO;AACX;AAEA,SAAS,QAAQ,YAAY,SAAS;AAClC,QAAM,WAAW,aAAa,UAAU;AACxC,QAAM,YAAY,SAAS,SAAS,IAAI,EAAE,CAAC,IAAI,GAAG,UAAU,MAAM,UAAU,YAAY,SAAS,IAAI,EAAE,CAAC,IAAI,GAAG,UAAU,MAAM,UAAU,WAAW;AACpJ,SAAO,WAAW,WAAW,OAAO;AACxC;AAEO,IAAIC,UAAS;;;AClBb,SAASC,SAAQ,MAAM,SAAS;AACnC,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,WAAW,MAAM,WAAW,KAAK,GAAG,OAAO;AAC3E;;;ACAA,SAAS,eAAe,QAAQ;AAC5B,SAAO,WAAW,QAAQ,QAAQ,CAAC,YAAY,CAAC,CAAC;AACrD;AACA,SAAS,YAAY,QAAQ;AACzB,SAAO,WAAW,EAAE,GAAG,QAAQ,CAAC,YAAY,GAAG,WAAW,CAAC;AAC/D;AAEA,SAAS,iBAAiB,QAAQ,GAAG;AACjC,SAAQ,MAAM,QACR,eAAe,MAAM,IACrB,YAAY,MAAM;AAC5B;AAEO,SAAS,SAAS,QAAQ,QAAQ;AACrC,QAAM,IAAI,UAAU;AACpB,SAAO,eAAe,MAAM,IAAI,yBAAyB,QAAQ,CAAC,IAAI,iBAAiB,QAAQ,CAAC;AACpG;;;AClBA,SAASC,gBAAe,GAAG,GAAG;AAC1B,QAAM,MAAM,CAAC;AACb,aAAW,MAAM,WAAW,OAAO,oBAAoB,CAAC;AACpD,QAAI,EAAE,IAAI,SAAS,EAAE,EAAE,GAAG,CAAC;AAC/B,SAAO;AACX;AAEA,SAASC,kBAAiB,GAAG,GAAG;AAC5B,SAAOD,gBAAe,EAAE,YAAY,CAAC;AACzC;AAEO,SAAS,yBAAyB,GAAG,GAAG;AAC3C,QAAM,IAAIC,kBAAiB,GAAG,CAAC;AAC/B,SAAO,aAAa,CAAC;AACzB;;;ACdO,SAAS,MAAM,OAAO,SAAS;AAElC,SAAO,WAAW,MAAM,SAAS,IAC7B,EAAE,CAAC,IAAI,GAAG,SAAS,MAAM,SAAS,OAAO,OAAO,iBAAiB,OAAO,UAAU,MAAM,QAAQ,UAAU,MAAM,OAAO,IACvH,EAAE,CAAC,IAAI,GAAG,SAAS,MAAM,SAAS,UAAU,MAAM,QAAQ,UAAU,MAAM,OAAO,GAAG,OAAO;AACnG;;;ACNO,SAAS,YAAY,GAAG,GAAG;AAC9B,SAAO,EAAE,SAAS,CAAC;AACvB;AAEO,SAAS,YAAY,GAAG,GAAG;AAC9B,SAAO,EAAE,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,CAAC;AAC3C;AAEO,SAAS,YAAY,GAAG;AAC3B,SAAO,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;AACzB;AAEO,SAAS,aAAa,GAAG,GAAG;AAC/B,SAAO,EAAE,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACxC;AAEO,SAAS,SAAS,GAAG,GAAG;AAC3B,SAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AACtB;AAGO,SAAS,cAAc,GAAG,GAAG;AAChC,SAAO,EAAE,OAAO,OAAK,CAAC,EAAE,SAAS,CAAC,CAAC;AACvC;AAEA,SAAS,wBAAwB,GAAG,MAAM;AACtC,SAAO,EAAE,OAAO,CAAC,KAAK,MAAM;AACxB,WAAO,aAAa,KAAK,CAAC;AAAA,EAC9B,GAAG,IAAI;AACX;AAEO,SAAS,iBAAiB,GAAG;AAChC,SAAQ,EAAE,WAAW,IACf,EAAE,CAAC,IAEH,EAAE,SAAS,IACP,wBAAwB,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,IACxC,CAAC;AACf;AAEO,SAAS,aAAa,GAAG;AAC5B,QAAM,MAAM,CAAC;AACb,aAAW,KAAK;AACZ,QAAI,KAAK,GAAG,CAAC;AACjB,SAAO;AACX;;;ACrBA,SAASC,kBAAiB,GAAG,GAAG;AAC5B,SAAQ,KAAK,IACP,eAAe,GAAG,EAAE,CAAC,CAAC,IACtB,aAAa,CAAC;AACxB;AAEA,SAAS,uCAAuC,GAAG;AAC/C,SAAO,EAAE,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE;AAC7B;AAEA,SAAS,yCAAyC,GAAG;AACjD,QAAM,MAAM,CAAC;AACb,aAAW,KAAK;AACZ,QAAI,CAAC,IAAI,QAAQ,CAAC;AACtB,SAAO;AACX;AAEA,SAAS,kCAAkC,GAAG,GAAG;AAC7C,SAAQ,YAAY,GAAG,CAAC,IAClB,uCAAuC,CAAC,IACxC,yCAAyC,CAAC;AACpD;AAEA,SAAS,cAAc,GAAG,GAAG;AACzB,QAAM,IAAI,kCAAkC,GAAG,CAAC;AAChD,SAAOA,kBAAiB,GAAG,CAAC;AAChC;AAEA,SAASC,UAAS,GAAG,GAAG;AACpB,SAAO,EAAE,IAAI,OAAK,eAAe,GAAG,CAAC,CAAC;AAC1C;AAEA,SAASC,gBAAe,GAAG,GAAG;AAC1B,QAAM,MAAM,CAAC;AACb,aAAW,MAAM,WAAW,OAAO,oBAAoB,CAAC;AACpD,QAAI,EAAE,IAAI,eAAe,GAAG,EAAE,EAAE,CAAC;AACrC,SAAO;AACX;AAEA,SAAS,eAAe,GAAG,GAAG;AAE1B,QAAM,UAAU,EAAE,GAAG,EAAE;AACvB;AAAA;AAAA,IAEA,WAAW,CAAC,IAAI,SAAS,eAAe,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,IAClE,WAAW,CAAC,IAAI,SAAS,eAAe,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;AAAA;AAAA,MAElE,eAAe,CAAC,IAAIF,kBAAiB,GAAG,EAAE,UAAU,IAChD,YAAY,CAAC,IAAI,cAAc,GAAG,EAAE,IAAI;AAAA;AAAA,QAEpC,cAAc,CAAC,IAAI,YAAYC,UAAS,GAAG,EAAE,UAAU,GAAG,eAAe,GAAG,EAAE,OAAO,GAAG,OAAO,IAC3FE,YAAW,CAAC,IAAI,SAAaF,UAAS,GAAG,EAAE,UAAU,GAAG,eAAe,GAAG,EAAE,OAAO,GAAG,OAAO,IACzFG,iBAAgB,CAAC,IAAI,cAAc,eAAe,GAAG,EAAE,KAAK,GAAG,OAAO,IAClEC,YAAW,CAAC,IAAI,SAAS,eAAe,GAAG,EAAE,KAAK,GAAG,OAAO,IACxD,YAAY,CAAC,IAAI,UAAUJ,UAAS,GAAG,EAAE,KAAK,GAAG,OAAO,IACpD,QAAQ,CAAC,IAAI,MAAMA,UAAS,GAAG,EAAE,KAAK,GAAG,OAAO,IAC5C,QAAQ,CAAC,IAAI,MAAMA,UAAS,GAAG,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO,IAClDK,UAAS,CAAC,IAAIC,QAAOL,gBAAe,GAAG,EAAE,UAAU,GAAG,OAAO,IACzDM,SAAQ,CAAC,IAAIC,OAAM,eAAe,GAAG,EAAE,KAAK,GAAG,OAAO,IAClDC,WAAU,CAAC,IAAIC,SAAQ,eAAe,GAAG,EAAE,IAAI,GAAG,OAAO,IACrD;AAAA;AAAA;AAAA;AAC5D;AAEO,SAAS,yBAAyB,GAAG,GAAG;AAC3C,QAAM,MAAM,CAAC;AACb,aAAW,KAAK;AACZ,QAAI,CAAC,IAAI,eAAe,GAAG,CAAC;AAChC,SAAO;AACX;AAEO,SAAS,OAAO,KAAKC,MAAK,SAAS;AACtC,QAAM,IAAI,SAAS,GAAG,IAAI,kBAAkB,GAAG,IAAI;AACnD,QAAM,KAAKA,KAAI,EAAE,CAAC,IAAI,GAAG,aAAa,MAAM,EAAE,CAAC;AAC/C,QAAM,IAAI,yBAAyB,GAAG,EAAE;AACxC,SAAOL,QAAO,GAAG,OAAO;AAC5B;;;AClGA,SAASM,gBAAe,GAAG,GAAG;AAC1B,QAAM,MAAM,CAAC;AACb,aAAW,MAAM,WAAW,OAAO,oBAAoB,CAAC;AACpD,QAAI,EAAE,IAAI,SAAS,EAAE,EAAE,GAAG,CAAC;AAC/B,SAAO;AACX;AAEA,SAASC,kBAAiB,GAAG,GAAG;AAC5B,SAAOD,gBAAe,EAAE,YAAY,CAAC;AACzC;AAEO,SAAS,yBAAyB,GAAG,GAAG;AAC3C,QAAM,IAAIC,kBAAiB,GAAG,CAAC;AAC/B,SAAO,aAAa,CAAC;AACzB;;;ACZA,SAAS,eAAe,QAAQ;AAC5B,SAAO,WAAW,QAAQ,QAAQ,CAAC,YAAY,CAAC,CAAC;AACrD;AACA,SAAS,YAAY,QAAQ;AACzB,SAAO,WAAW,EAAE,GAAG,QAAQ,CAAC,YAAY,GAAG,WAAW,CAAC;AAC/D;AAEA,SAAS,iBAAiB,QAAQ,GAAG;AACjC,SAAQ,MAAM,QACR,eAAe,MAAM,IACrB,YAAY,MAAM;AAC5B;AAEO,SAAS,SAAS,QAAQ,QAAQ;AACrC,QAAM,IAAI,UAAU;AACpB,SAAO,eAAe,MAAM,IAAI,yBAAyB,QAAQ,CAAC,IAAI,iBAAiB,QAAQ,CAAC;AACpG;;;ACjBO,SAAS,OAAO,MAAM;AACzB,QAAM,CAAC,MAAM,OAAO,IAAI,OAAO,KAAK,CAAC,MAAM,WAAW,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAChG,MAAI,OAAO,SAAS;AAChB,UAAM,IAAI,aAAa,4BAA4B;AACvD,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,OAAO,KAAK,GAAG,OAAO;AACtD;;;ACHA,SAASC,UAAS,OAAO;AACrB,QAAM,SAAS,CAAC;AAChB,aAAW,KAAK;AACZ,WAAO,KAAK,kBAAkB,CAAC,CAAC;AACpC,SAAO;AACX;AAEA,SAASC,eAAc,OAAO;AAC1B,QAAM,oBAAoBD,UAAS,KAAK;AACxC,QAAM,eAAe,aAAa,iBAAiB;AACnD,SAAO;AACX;AAEA,SAASE,WAAU,OAAO;AACtB,QAAM,oBAAoBF,UAAS,KAAK;AACxC,QAAM,eAAe,iBAAiB,iBAAiB;AACvD,SAAO;AACX;AAEA,SAASG,WAAU,OAAO;AACtB,SAAO,MAAM,IAAI,CAAC,GAAG,YAAY,QAAQ,SAAS,CAAC;AACvD;AAEA,SAASC,WAAU,GAAG;AAClB,SAAQ,CAAC,UAAU;AACvB;AAEA,SAASC,gBAAe,GAAG;AACvB,SAAQ,WAAW,OAAO,oBAAoB,CAAC;AACnD;AAKA,SAAS,sBAAsB,mBAAmB;AAC9C,MAAI,CAAC;AACD,WAAO,CAAC;AACZ,QAAM,sBAAsB,WAAW,OAAO,oBAAoB,iBAAiB;AACnF,SAAO,oBAAoB,IAAI,SAAO;AAClC,WAAQ,IAAI,CAAC,MAAM,OAAO,IAAI,IAAI,SAAS,CAAC,MAAM,MAC5C,IAAI,MAAM,GAAG,IAAI,SAAS,CAAC,IAC3B;AAAA,EACV,CAAC;AACL;AAGO,SAAS,kBAAkB,MAAM;AACpC,SAAQ,YAAY,IAAI,IAAIJ,eAAc,KAAK,KAAK,IAChD,QAAQ,IAAI,IAAIC,WAAU,KAAK,KAAK,IAChC,QAAQ,IAAI,IAAIC,WAAU,KAAK,SAAS,CAAC,CAAC,IACtCG,SAAQ,IAAI,IAAIF,WAAU,KAAK,KAAK,IAChCG,UAAS,IAAI,IAAIF,gBAAe,KAAK,UAAU,IAC3C,SAAS,IAAI,IAAI,sBAAsB,KAAK,iBAAiB,IACzD,CAAC;AAC7B;AAIA,IAAI,2BAA2B;AAExB,SAAS,aAAa,QAAQ;AACjC,6BAA2B;AAC3B,QAAM,OAAO,kBAAkB,MAAM;AACrC,6BAA2B;AAC3B,QAAM,UAAU,KAAK,IAAI,CAAC,QAAQ,IAAI,GAAG,GAAG;AAC5C,SAAO,KAAK,QAAQ,KAAK,GAAG,CAAC;AACjC;;;ACpEA,SAASG,gBAAe,YAAY,SAAS;AACzC,QAAM,SAAS,CAAC;AAChB,aAAW,MAAM,WAAW,OAAO,oBAAoB,UAAU;AAC7D,WAAO,EAAE,IAAI,MAAM,WAAW,EAAE,GAAG,MAAM,OAAO,CAAC;AACrD,SAAO;AACX;AAEA,SAASC,kBAAiB,cAAc,SAAS;AAC7C,SAAOD,gBAAe,aAAa,YAAY,OAAO;AAC1D;AAEO,SAAS,sBAAsB,cAAc,SAAS;AACzD,QAAM,aAAaC,kBAAiB,cAAc,OAAO;AACzD,SAAO,aAAa,UAAU;AAClC;;;ACLA,SAAS,aAAa,QAAQ,YAAY;AACtC,SAAO,SAAS,SAAS,CAAC,SAAS,QAAQ,UAAU,CAAC,CAAC;AAC3D;AAEA,SAAS,QAAQ,MAAM;AACnB,SAAO,SAAS,SAAS,CAAC,IAAI,IAAI,CAAC,CAAC;AACxC;AAEA,SAAS,cAAc,MAAM,SAAS;AAClC,QAAM,eAAe,kBAAkB,IAAI;AAC3C,QAAM,mBAAmB,wBAAwB,YAAY;AAC7D,QAAM,SAAS,eAAe,gBAAgB;AAC9C,SAAO,WAAW,QAAQ,OAAO;AACrC;AAEO,SAAS,wBAAwB,cAAc;AAClD,SAAO,aAAa,IAAI,OAAK,MAAM,aAAaC,QAAO,IAAI,QAAQ,CAAC,CAAC;AACzE;AAEO,SAAS,MAAM,MAAM,SAAS;AACjC,SAAQ,WAAW,IAAI,IAAI,aAAa,KAAK,QAAQ,KAAK,UAAU,IAAI,MAAM,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,eAAe,IAAI,IAAI,sBAAsB,MAAM,OAAO,IAAI,cAAc,MAAM,OAAO;AACxM;;;AC3BO,SAAS,qBAAqB,QAAQ;AACzC,QAAM,OAAO,kBAAkB,MAAM;AACrC,QAAM,UAAU,sBAAsB,QAAQ,IAAI;AAClD,SAAO,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,KAAK,GAAG,QAAQ,KAAK,CAAC,CAAC;AAC/D;;;ACTA,SAASC,WAAU,QAAQ;AACvB,SAAO,OAAO,MAAM,MAAM,CAACC,YAAW,sBAAsBA,OAAM,CAAC;AACvE;AACA,SAASC,OAAM,QAAQ;AACnB,SAAO,OAAO,MAAM,KAAK,CAACD,YAAW,sBAAsBA,OAAM,CAAC;AACtE;AACA,SAAS,IAAI,QAAQ;AACjB,SAAO,CAAC,sBAAsB,OAAO,GAAG;AAC5C;AAGO,SAAS,sBAAsB,QAAQ;AAC1C,SAAQ,OAAO,IAAI,MAAM,cAAcD,WAAU,MAAM,IACnD,OAAO,IAAI,MAAM,UAAUE,OAAM,MAAM,IACnC,OAAO,IAAI,MAAM,QAAQ,IAAI,MAAM,IAC/B,OAAO,IAAI,MAAM,cAAc,OAC3B;AACpB;;;AChBO,SAAS,IAAI,SAAS;AACzB,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,MAAM,GAAG,OAAO;AAChD;;;ACFO,SAAS,QAAQ,SAAS;AAC7B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,UAAU,GAAG,OAAO;AACpD;;;ACLA,IAAAC,gBAAA;AAAA,SAAAA,eAAA;AAAA,eAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,qBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA,wBAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,uBAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,sBAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,iBAAAC;AAAA,EAAA,oBAAAC;AAAA,EAAA,kBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,yBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA,oBAAAC;AAAA,EAAA,mBAAAC;AAAA,EAAA,eAAAC;AAAA,EAAA;AAAA,mBAAAC;AAAA,EAAA,gBAAAC;AAAA,EAAA,cAAAC;AAAA,EAAA;AAAA;AAGO,IAAM,4BAAN,cAAwC,aAAa;AAC5D;AACA,IAAM,aAAa;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACA,SAAS,UAAU,OAAO;AACtB,MAAI;AACA,QAAI,OAAO,KAAK;AAChB,WAAO;AAAA,EACX,QACM;AACF,WAAO;AAAA,EACX;AACJ;AACA,SAAS,uBAAuB,OAAO;AACnC,MAAI,CAAY,SAAS,KAAK;AAC1B,WAAO;AACX,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAM,OAAO,MAAM,WAAW,CAAC;AAC/B,QAAK,QAAQ,KAAK,QAAQ,MAAO,SAAS,MAAM,SAAS,KAAK;AAC1D,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,uBAAuB,OAAO;AACnC,SAAO,kBAAkB,KAAK,KAAKC,UAAS,KAAK;AACrD;AACA,SAAS,iBAAiB,OAAO;AAC7B,SAAkB,YAAY,KAAK,KAAgB,SAAS,KAAK;AACrE;AACA,SAAS,iBAAiB,OAAO;AAC7B,SAAkB,YAAY,KAAK,KAAgB,SAAS,KAAK;AACrE;AACA,SAAS,kBAAkB,OAAO;AAC9B,SAAkB,YAAY,KAAK,KAAgB,UAAU,KAAK;AACtE;AACA,SAAS,iBAAiB,OAAO;AAC7B,SAAkB,YAAY,KAAK,KAAgB,SAAS,KAAK;AACrE;AACA,SAAS,kBAAkB,OAAO;AAC9B,SAAkB,YAAY,KAAK,KAAiB,SAAS,KAAK,KAAK,uBAAuB,KAAK,KAAK,UAAU,KAAK;AAC3H;AACA,SAAS,iBAAiB,OAAO;AAC7B,SAAkB,YAAY,KAAK,KAAiB,SAAS,KAAK,KAAK,uBAAuB,KAAK;AACvG;AACA,SAAS,iBAAiB,OAAO;AAC7B,SAAkB,YAAY,KAAK,KAAKA,UAAS,KAAK;AAC1D;AAKO,SAASC,YAAW,OAAO;AAC9B,SAAkB,SAAS,KAAK,KAAK,MAAM,YAAY,MAAM;AACjE;AAEO,SAASC,YAAW,OAAO;AAC9B,SAAkB,SAAS,KAAK,KAAK,MAAM,YAAY,MAAM;AACjE;AAKO,SAASC,OAAM,OAAO;AAEzB,SAAQC,UAAS,OAAO,KAAK,KACzB,iBAAiB,MAAM,GAAG;AAClC;AAEO,SAASC,YAAW,OAAO;AAE9B,SAAQD,UAAS,OAAO,UAAU,KACnB,SAAS,MAAM,KAAK;AACvC;AAEO,SAASE,SAAQ,OAAO;AAC3B,SAAQF,UAAS,OAAO,OAAO,KAC3B,MAAM,SAAS,WACf,iBAAiB,MAAM,GAAG,KAC1BJ,UAAS,MAAM,KAAK,KACpB,iBAAiB,MAAM,QAAQ,KAC/B,iBAAiB,MAAM,QAAQ,KAC/B,kBAAkB,MAAM,WAAW,KACnC,iBAAiB,MAAM,QAAQ,KAC/B,iBAAiB,MAAM,WAAW,KAClC,iBAAiB,MAAM,WAAW;AAC1C;AAEO,SAASO,iBAAgB,OAAO;AAEnC,SAAQH,UAAS,OAAO,eAAe,KACnC,MAAM,SAAS,mBACf,iBAAiB,MAAM,GAAG,KAC1BJ,UAAS,MAAM,KAAK;AAC5B;AAEO,SAASQ,UAAS,OAAO;AAE5B,SAAQJ,UAAS,OAAO,QAAQ,KAC5B,MAAM,SAAS,YACf,iBAAiB,MAAM,GAAG,KAC1B,iBAAiB,MAAM,gBAAgB,KACvC,iBAAiB,MAAM,gBAAgB,KACvC,iBAAiB,MAAM,OAAO,KAC9B,iBAAiB,MAAM,OAAO,KAC9B,iBAAiB,MAAM,UAAU;AACzC;AAEO,SAASK,WAAU,OAAO;AAE7B,SAAQL,UAAS,OAAO,SAAS,KAC7B,MAAM,SAAS,aACf,iBAAiB,MAAM,GAAG;AAClC;AAEO,SAASM,YAAW,OAAO;AAE9B,SAAQN,UAAS,OAAO,UAAU,KACnB,SAAS,MAAM,MAAM,KACrB,QAAQ,MAAM,UAAU,KACnC,MAAM,WAAW,MAAM,CAAC,WAAWJ,UAAS,MAAM,CAAC;AAC3D;AAEO,SAASW,eAAc,OAAO;AAEjC,SAAQP,UAAS,OAAO,aAAa,KACjC,MAAM,SAAS,iBACf,iBAAiB,MAAM,GAAG,KACf,QAAQ,MAAM,UAAU,KACnC,MAAM,WAAW,MAAM,YAAUJ,UAAS,MAAM,CAAC,KACjDA,UAAS,MAAM,OAAO;AAC9B;AAEO,SAASY,QAAO,OAAO;AAC1B,SAAQR,UAAS,OAAO,MAAM,KAC1B,MAAM,SAAS,UACf,iBAAiB,MAAM,GAAG,KAC1B,iBAAiB,MAAM,yBAAyB,KAChD,iBAAiB,MAAM,yBAAyB,KAChD,iBAAiB,MAAM,gBAAgB,KACvC,iBAAiB,MAAM,gBAAgB,KACvC,iBAAiB,MAAM,mBAAmB;AAClD;AAEO,SAASS,YAAW,OAAO;AAE9B,SAAQT,UAAS,OAAO,UAAU,KAC9B,MAAM,SAAS,cACf,iBAAiB,MAAM,GAAG,KACf,QAAQ,MAAM,UAAU,KACnC,MAAM,WAAW,MAAM,YAAUJ,UAAS,MAAM,CAAC,KACjDA,UAAS,MAAM,OAAO;AAC9B;AAEO,SAASc,UAAS,OAAO;AAE5B,SAAQV,UAAS,OAAO,QAAQ,KACjB,eAAe,OAAO,OAAO,KAC7B,SAAS,MAAM,KAAK,KAC/BW,cAAa,MAAM,KAAK,KACb,eAAe,OAAO,MAAM,KAC5B,SAAS,MAAM,IAAI,KAC9B,MAAM,QAAQ,MAAM;AAE5B;AAEO,SAASC,WAAU,OAAO;AAC7B,SAAQZ,UAAS,OAAO,SAAS,KAC7B,MAAM,SAAS,aACf,iBAAiB,MAAM,GAAG,KAC1B,iBAAiB,MAAM,gBAAgB,KACvC,iBAAiB,MAAM,gBAAgB,KACvC,iBAAiB,MAAM,OAAO,KAC9B,iBAAiB,MAAM,OAAO,KAC9B,iBAAiB,MAAM,UAAU;AACzC;AAEO,SAASW,cAAa,OAAO;AAEhC,SAAmB,SAAS,KAAK,KAC7B,OAAO,QAAQ,KAAK,EAAE,MAAM,CAAC,CAAC,KAAK,MAAM,MAAM,uBAAuB,GAAG,KAAKf,UAAS,MAAM,CAAC;AACtG;AAEO,SAASiB,aAAY,OAAO;AAE/B,SAAQb,UAAS,OAAO,WAAW,MACnB,SAAS,MAAM,IAAI,KAAK,MAAM,SAAS,WAAW,QAAQ,SAC3D,QAAQ,MAAM,KAAK,KAC9B,MAAM,MAAM,MAAM,YAAUJ,UAAS,MAAM,KAAK,CAACkB,aAAY,MAAM,CAAC,KACpE,iBAAiB,MAAM,IAAI,MAC1B,kBAAkB,MAAM,qBAAqB,KAAK,iBAAiB,MAAM,qBAAqB,MAC/F,iBAAiB,MAAM,GAAG;AAClC;AAEO,SAASC,YAAW,OAAO;AAE9B,SAAQf,UAAS,OAAO,UAAU,KAC9B,MAAM,SAAS,cACf,iBAAiB,MAAM,GAAG,KAC1BJ,UAAS,MAAM,KAAK;AAC5B;AAEO,SAASI,UAAS,OAAO,MAAM;AAClC,SAAkB,SAAS,KAAK,KAAK,QAAQ,SAAS,MAAM,IAAI,MAAM;AAC1E;AAEO,SAASgB,iBAAgB,OAAO;AACnC,SAAOC,WAAU,KAAK,KAAgB,SAAS,MAAM,KAAK;AAC9D;AAEO,SAASC,iBAAgB,OAAO;AACnC,SAAOD,WAAU,KAAK,KAAgB,SAAS,MAAM,KAAK;AAC9D;AAEO,SAASE,kBAAiB,OAAO;AACpC,SAAOF,WAAU,KAAK,KAAgB,UAAU,MAAM,KAAK;AAC/D;AAEO,SAASA,WAAU,OAAO;AAE7B,SAAQjB,UAAS,OAAO,SAAS,KAC7B,iBAAiB,MAAM,GAAG,KAAKoB,gBAAe,MAAM,KAAK;AACjE;AAEO,SAASA,gBAAe,OAAO;AAClC,SAAkB,UAAU,KAAK,KAAgB,SAAS,KAAK,KAAgB,SAAS,KAAK;AACjG;AAEO,SAASC,aAAY,OAAO;AAE/B,SAAQrB,UAAS,OAAO,WAAW,KACpB,QAAQ,MAAM,IAAI,KAC7B,MAAM,KAAK,MAAM,SAAkB,SAAS,GAAG,KAAgB,SAAS,GAAG,CAAC;AACpF;AAEO,SAASsB,gBAAe,OAAO;AAElC,SAAQtB,UAAS,OAAO,cAAc,KAClCW,cAAa,MAAM,UAAU;AACrC;AAEO,SAASY,SAAQ,OAAO;AAE3B,SAAQvB,UAAS,OAAO,OAAO,KAChB,SAAS,MAAM,GAAG,KAC7B,OAAO,oBAAoB,MAAM,GAAG,EAAE,WAAW;AACzD;AAEO,SAASwB,OAAM,OAAO;AAEzB,SAAQxB,UAAS,OAAO,KAAK,KACzBJ,UAAS,MAAM,GAAG;AAC1B;AAEO,SAAS6B,QAAO,OAAO;AAE1B,SAAQzB,UAAS,OAAO,MAAM,KAC1B,MAAM,SAAS,UACf,iBAAiB,MAAM,GAAG;AAClC;AAEO,SAAS0B,UAAS,OAAO;AAC5B,SAAQ1B,UAAS,OAAO,QAAQ,KAC5B,MAAM,SAAS,YACf,iBAAiB,MAAM,GAAG,KAC1B,iBAAiB,MAAM,gBAAgB,KACvC,iBAAiB,MAAM,gBAAgB,KACvC,iBAAiB,MAAM,OAAO,KAC9B,iBAAiB,MAAM,OAAO,KAC9B,iBAAiB,MAAM,UAAU;AACzC;AAEO,SAAS2B,UAAS,OAAO;AAE5B,SAAQ3B,UAAS,OAAO,QAAQ,KAC5B,MAAM,SAAS,YACf,iBAAiB,MAAM,GAAG,KAC1BW,cAAa,MAAM,UAAU,KAC7B,uBAAuB,MAAM,oBAAoB,KACjD,iBAAiB,MAAM,aAAa,KACpC,iBAAiB,MAAM,aAAa;AAC5C;AAEO,SAASiB,WAAU,OAAO;AAE7B,SAAQ5B,UAAS,OAAO,SAAS,KAC7B,MAAM,SAAS,aACf,iBAAiB,MAAM,GAAG,KAC1BJ,UAAS,MAAM,IAAI;AAC3B;AAEO,SAASiC,UAAS,OAAO;AAE5B,SAAQ7B,UAAS,OAAO,QAAQ,KAC5B,MAAM,SAAS,YACf,iBAAiB,MAAM,GAAG,KAC1B,uBAAuB,MAAM,oBAAoB,KACtC,SAAS,MAAM,iBAAiB,MAC1C,CAAC,WAAW;AACT,UAAM,OAAO,OAAO,oBAAoB,OAAO,iBAAiB;AAChE,WAAQ,KAAK,WAAW,KACpB,UAAU,KAAK,CAAC,CAAC,KACN,SAAS,OAAO,iBAAiB,KAC5CJ,UAAS,OAAO,kBAAkB,KAAK,CAAC,CAAC,CAAC;AAAA,EAClD,GAAG,KAAK;AAChB;AAEO,SAASkC,aAAY,OAAO;AAC/B,SAAkB,SAAS,KAAK,KAAK,QAAQ,SAAS,MAAM,IAAI,MAAM;AAC1E;AAEO,SAASC,OAAM,OAAO;AAEzB,SAAQ/B,UAAS,OAAO,KAAK,KACzB,iBAAiB,MAAM,GAAG,KACf,SAAS,MAAM,IAAI;AACtC;AAEO,SAASgC,UAAS,OAAO;AAE5B,SAAQhC,UAAS,OAAO,QAAQ,KAC5B,iBAAiB,MAAM,GAAG,KACf,SAAS,MAAM,MAAM,KACrB,SAAS,MAAM,KAAK,KAC/B,iBAAiB,MAAM,SAAS,KAChC,iBAAiB,MAAM,SAAS;AACxC;AAEO,SAASiC,UAAS,OAAO;AAE5B,SAAQjC,UAAS,OAAO,QAAQ,KAC5B,MAAM,SAAS,YACf,iBAAiB,MAAM,GAAG,KAC1B,iBAAiB,MAAM,SAAS,KAChC,iBAAiB,MAAM,SAAS,KAChC,kBAAkB,MAAM,OAAO,KAC/B,iBAAiB,MAAM,MAAM;AACrC;AAEO,SAASkC,UAAS,OAAO;AAE5B,SAAQlC,UAAS,OAAO,QAAQ,KAC5B,MAAM,SAAS,YACf,iBAAiB,MAAM,GAAG;AAClC;AAEO,SAASmC,mBAAkB,OAAO;AAErC,SAAQnC,UAAS,OAAO,iBAAiB,KACrC,MAAM,SAAS,YACJ,SAAS,MAAM,OAAO,KACjC,MAAM,QAAQ,CAAC,MAAM,OACrB,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC,MAAM;AACpD;AAEO,SAASoC,QAAO,OAAO;AAE1B,SAAQpC,UAAS,OAAO,MAAM,KAC1B,iBAAiB,MAAM,GAAG,KACf,SAAS,MAAM,IAAI;AACtC;AAEO,SAASc,aAAY,OAAO;AAC/B,SAAkB,SAAS,KAAK,KAAK,iBAAiB;AAC1D;AAEO,SAASuB,SAAQ,OAAO;AAE3B,SAAQrC,UAAS,OAAO,OAAO,KAC3B,MAAM,SAAS,WACf,iBAAiB,MAAM,GAAG,KACf,SAAS,MAAM,QAAQ,KACvB,SAAS,MAAM,QAAQ,KAClC,MAAM,aAAa,MAAM;AAAA,GAEd,YAAY,MAAM,KAAK,KACnB,YAAY,MAAM,eAAe,KAC5C,MAAM,aAAa,KAAkB,QAAQ,MAAM,KAAK,KACxD,MAAM,MAAM,MAAM,YAAUJ,UAAS,MAAM,CAAC;AACxD;AAEO,SAAS0C,aAAY,OAAO;AAE/B,SAAQtC,UAAS,OAAO,WAAW,KAC/B,MAAM,SAAS,eACf,iBAAiB,MAAM,GAAG;AAClC;AAEO,SAAS,eAAe,OAAO;AAClC,SAAOuC,SAAQ,KAAK,KAAK,MAAM,MAAM,MAAM,CAAC,WAAWvB,iBAAgB,MAAM,KAAKE,iBAAgB,MAAM,CAAC;AAC7G;AAEO,SAASqB,SAAQ,OAAO;AAE3B,SAAQvC,UAAS,OAAO,OAAO,KAC3B,iBAAiB,MAAM,GAAG,KACf,SAAS,KAAK,KACd,QAAQ,MAAM,KAAK,KAC9B,MAAM,MAAM,MAAM,YAAUJ,UAAS,MAAM,CAAC;AACpD;AAEO,SAAS4C,cAAa,OAAO;AAEhC,SAAQxC,UAAS,OAAO,YAAY,KAChC,MAAM,SAAS,gBACf,iBAAiB,MAAM,GAAG,KAC1B,iBAAiB,MAAM,aAAa,KACpC,iBAAiB,MAAM,aAAa;AAC5C;AAEO,SAASyC,WAAU,OAAO;AAE7B,SAAQzC,UAAS,OAAO,SAAS,KAC7B,iBAAiB,MAAM,GAAG;AAClC;AAEO,SAAS0C,UAAS,OAAO;AAC5B,SAAO1C,UAAS,OAAO,QAAQ;AACnC;AAEO,SAAS2C,QAAO,OAAO;AAE1B,SAAQ3C,UAAS,OAAO,MAAM,KAC1B,MAAM,SAAS,UACf,iBAAiB,MAAM,GAAG;AAClC;AAEO,SAAS4C,QAAO,OAAO;AAC1B,SAAkB,SAAS,KAAK,KAAK,QAAQ,SAAoB,SAAS,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW,SAAS,MAAM,IAAI,CAAC;AAC9H;AAEO,SAAShD,UAAS,OAAO;AAE5B,SAAmB,SAAS,KAAK,MAAOG,OAAM,KAAK,KAC/CE,YAAW,KAAK,KAChBC,SAAQ,KAAK,KACbG,WAAU,KAAK,KACfD,UAAS,KAAK,KACdD,iBAAgB,KAAK,KACrBG,YAAW,KAAK,KAChBC,eAAc,KAAK,KACnBC,QAAO,KAAK,KACZC,YAAW,KAAK,KAChBG,WAAU,KAAK,KACfC,aAAY,KAAK,KACjBE,YAAW,KAAK,KAChBE,WAAU,KAAK,KACfI,aAAY,KAAK,KACjBC,gBAAe,KAAK,KACpBC,SAAQ,KAAK,KACbC,OAAM,KAAK,KACXC,QAAO,KAAK,KACZC,UAAS,KAAK,KACdC,UAAS,KAAK,KACdC,WAAU,KAAK,KACfC,UAAS,KAAK,KACdE,OAAM,KAAK,KACXC,UAAS,KAAK,KACdC,UAAS,KAAK,KACdC,UAAS,KAAK,KACdC,mBAAkB,KAAK,KACvBC,QAAO,KAAK,KACZC,SAAQ,KAAK,KACbC,aAAY,KAAK,KACjBC,SAAQ,KAAK,KACbC,cAAa,KAAK,KAClBC,WAAU,KAAK,KACfC,UAAS,KAAK,KACdC,QAAO,KAAK,KACZC,QAAO,KAAK;AACpB;;;AClfO,IAAM,uBAAN,cAAmC,aAAa;AACvD;AACO,IAAI;AAAA,CACV,SAAUC,gBAAe;AACtB,EAAAA,eAAcA,eAAc,OAAO,IAAI,CAAC,IAAI;AAC5C,EAAAA,eAAcA,eAAc,MAAM,IAAI,CAAC,IAAI;AAC3C,EAAAA,eAAcA,eAAc,OAAO,IAAI,CAAC,IAAI;AAChD,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAKxC,SAAS,kBAAkB,QAAQ;AAC/B,SAAO,WAAW,cAAc,QAAQ,SAAS,cAAc;AACnE;AAKA,SAAS,MAAM,SAAS;AACpB,QAAM,IAAI,qBAAqB,OAAO;AAC1C;AAKA,SAAS,kBAAkB,OAAO;AAC9B,SAAQC,cAAU,QAAQ,KAAK,KAC3BA,cAAU,YAAY,KAAK,KAC3BA,cAAU,QAAQ,KAAK,KACvBA,cAAU,UAAU,KAAK,KACzBA,cAAU,MAAM,KAAK;AAC7B;AAEA,SAAS,gBAAgB,MAAM,OAAO;AAClC,SAAQA,cAAU,QAAQ,KAAK,IAAI,eAAe,MAAM,KAAK,IACzDA,cAAU,YAAY,KAAK,IAAI,mBAAmB,MAAM,KAAK,IACzDA,cAAU,QAAQ,KAAK,IAAI,eAAe,MAAM,KAAK,IACjDA,cAAU,UAAU,KAAK,IAAI,iBAAiB,MAAM,KAAK,IACrDA,cAAU,MAAM,KAAK,IAAI,aAAa,MAAM,KAAK,IAC7C,MAAM,iBAAiB;AAC/C;AAKA,SAAS,aAAa,MAAM,OAAO;AAC/B,SAAO,cAAc;AACzB;AAEA,SAAS,QAAQ,MAAM,OAAO;AAC1B,SAAQA,cAAU,YAAY,KAAK,IAAI,mBAAmB,MAAM,KAAK,IAChEA,cAAU,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK,CAAC,WAAWA,cAAU,MAAM,MAAM,KAAKA,cAAU,UAAU,MAAM,CAAC,IAAK,cAAc,OAC/HA,cAAU,QAAQ,KAAK,IAAI,cAAc,QACrCA,cAAU,UAAU,KAAK,IAAI,cAAc,OACvCA,cAAU,MAAM,KAAK,IAAI,cAAc,OACnC,cAAc;AACtC;AAKA,SAAS,eAAe,MAAM,OAAO;AACjC,SAAQA,cAAU,UAAU,IAAI,IAAI,cAAc,QAC9CA,cAAU,MAAM,IAAI,IAAI,cAAc,QAClCA,cAAU,QAAQ,IAAI,IAAI,cAAc,OACpC,cAAc;AAC9B;AAEA,SAASC,WAAU,MAAM,OAAO;AAC5B,SAAQD,cAAU,SAAS,KAAK,KAAK,kBAAkB,KAAK,IAAI,cAAc,OAC1E,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAClD,CAACA,cAAU,QAAQ,KAAK,IAAI,cAAc,QACtC,kBAAkBE,OAAM,KAAK,OAAO,MAAM,KAAK,CAAC;AAChE;AAKA,SAAS,kBAAkB,MAAM,OAAO;AACpC,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1D,CAACF,cAAU,gBAAgB,KAAK,IAAI,cAAc,QAC9C,kBAAkBE,OAAM,KAAK,OAAO,MAAM,KAAK,CAAC;AAC5D;AAKA,SAAS,WAAW,MAAM,OAAO;AAC7B,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DF,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,cAAc,OACtC,cAAc;AAClC;AAKA,SAAS,iBAAiB,MAAM,OAAO;AACnC,SAAQA,cAAU,iBAAiB,IAAI,IAAI,cAAc,OACrDA,cAAU,UAAU,IAAI,IAAI,cAAc,OACtC,cAAc;AAC1B;AAEA,SAAS,YAAY,MAAM,OAAO;AAC9B,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,UAAU,KAAK,IAAI,cAAc,OACvC,cAAc;AAClC;AAKA,SAAS,gBAAgB,MAAM,OAAO;AAClC,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnD,CAACA,cAAU,cAAc,KAAK,IAAI,cAAc,QAC5C,KAAK,WAAW,SAAS,MAAM,WAAW,SAAS,cAAc,QAC5D,CAAC,KAAK,WAAW,MAAM,CAAC,QAAQ,UAAU,kBAAkBE,OAAM,MAAM,WAAW,KAAK,GAAG,MAAM,CAAC,MAAM,cAAc,IAAI,IAAK,cAAc,QAC1I,kBAAkBA,OAAM,KAAK,SAAS,MAAM,OAAO,CAAC;AAC5E;AAKA,SAAS,SAAS,MAAM,OAAO;AAC3B,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DF,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,OAAO,KAAK,IAAI,cAAc,OACpC,cAAc;AAClC;AAKA,SAAS,aAAa,MAAM,OAAO;AAC/B,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnD,CAACA,cAAU,WAAW,KAAK,IAAI,cAAc,QACzC,KAAK,WAAW,SAAS,MAAM,WAAW,SAAS,cAAc,QAC5D,CAAC,KAAK,WAAW,MAAM,CAAC,QAAQ,UAAU,kBAAkBE,OAAM,MAAM,WAAW,KAAK,GAAG,MAAM,CAAC,MAAM,cAAc,IAAI,IAAK,cAAc,QAC1I,kBAAkBA,OAAM,KAAK,SAAS,MAAM,OAAO,CAAC;AAC5E;AAKA,SAAS,iBAAiB,MAAM,OAAO;AACnC,SAAQF,cAAU,UAAU,IAAI,KAAK,cAAW,SAAS,KAAK,KAAK,IAAI,cAAc,OACjFA,cAAU,SAAS,IAAI,KAAKA,cAAU,UAAU,IAAI,IAAI,cAAc,OAClE,cAAc;AAC1B;AAEA,SAAS,YAAY,MAAM,OAAO;AAC9B,SAAQA,cAAU,UAAU,KAAK,KAAKA,cAAU,SAAS,KAAK,IAAI,cAAc,OAC5E,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAClDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnD,cAAc;AAClC;AAKA,SAAS,mBAAmB,MAAM,OAAO;AACrC,SAAO,MAAM,MAAM,MAAM,CAAC,WAAWE,OAAM,MAAM,MAAM,MAAM,cAAc,IAAI,IACzE,cAAc,OACd,cAAc;AACxB;AAEA,SAASC,eAAc,MAAM,OAAO;AAChC,SAAO,KAAK,MAAM,KAAK,CAAC,WAAWD,OAAM,QAAQ,KAAK,MAAM,cAAc,IAAI,IACxE,cAAc,OACd,cAAc;AACxB;AAKA,SAAS,aAAa,MAAM,OAAO;AAC/B,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1D,CAACF,cAAU,WAAW,KAAK,IAAI,cAAc,QACzC,kBAAkBE,OAAM,KAAK,OAAO,MAAM,KAAK,CAAC;AAC5D;AAKA,SAASE,aAAY,MAAM,OAAO;AAC9B,SAAQJ,cAAU,UAAU,KAAK,KAAK,MAAM,UAAU,KAAK,QAAQ,cAAc,OAC7E,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAClDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,UAAU,KAAK,IAAI,iBAAiB,MAAM,KAAK,IACrDA,cAAU,UAAU,KAAK,IAAI,iBAAiB,MAAM,KAAK,IACrD,cAAc;AAClD;AAKA,SAAS,eAAe,MAAM,OAAO;AACjC,SAAO,cAAc;AACzB;AAEA,SAAS,UAAU,MAAM,OAAO;AAC5B,SAAO,cAAc;AACzB;AAKA,SAAS,WAAW,QAAQ;AACxB,MAAI,CAAC,SAAS,KAAK,IAAI,CAAC,QAAQ,CAAC;AACjC,SAAO,MAAM;AACT,QAAI,CAACA,cAAU,MAAM,OAAO;AACxB;AACJ,cAAU,QAAQ;AAClB,aAAS;AAAA,EACb;AACA,SAAO,QAAQ,MAAM,IAAI,UAAU,QAAQ;AAC/C;AAEA,SAAS,QAAQ,MAAM,OAAO;AAK1B,SAAQA,cAAU,MAAM,IAAI,IAAIE,OAAM,WAAW,IAAI,GAAG,KAAK,IACzDF,cAAU,MAAM,KAAK,IAAIE,OAAM,MAAM,WAAW,KAAK,CAAC,IAClD,MAAM,6BAA6B;AAC/C;AAKA,SAAS,SAAS,MAAM,OAAO;AAC3B,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DF,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,OAAO,KAAK,IAAI,cAAc,OACpC,cAAc;AAClC;AAKA,SAAS,gBAAgB,MAAM,OAAO;AAClC,SAAQA,cAAU,gBAAgB,IAAI,IAAI,cAAc,OACpDA,cAAU,SAAS,IAAI,KAAKA,cAAU,UAAU,IAAI,IAAI,cAAc,OAClE,cAAc;AAC1B;AAEA,SAAS,WAAW,MAAM,OAAO;AAC7B,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,UAAU,KAAK,KAAKA,cAAU,SAAS,KAAK,IAAI,cAAc,OACpE,cAAc;AAClC;AAKA,SAAS,sBAAsB,QAAQ,OAAO;AAC1C,SAAO,OAAO,oBAAoB,OAAO,UAAU,EAAE,WAAW;AACpE;AAEA,SAAS,mBAAmB,QAAQ;AAChC,SAAO,kBAAkB,MAAM;AACnC;AAEA,SAAS,mBAAmB,QAAQ;AAChC,SAAO,sBAAsB,QAAQ,CAAC,KAAM,sBAAsB,QAAQ,CAAC,KAAK,iBAAiB,OAAO,cAAcA,cAAU,QAAQ,OAAO,WAAW,WAAW,KAAK,OAAO,WAAW,YAAY,MAAM,WAAW,MAAOA,cAAU,SAAS,OAAO,WAAW,YAAY,MAAM,CAAC,CAAC,KACrRA,cAAU,YAAY,OAAO,WAAW,YAAY,MAAM,CAAC,CAAC,KAAOA,cAAU,SAAS,OAAO,WAAW,YAAY,MAAM,CAAC,CAAC,KAC5HA,cAAU,YAAY,OAAO,WAAW,YAAY,MAAM,CAAC,CAAC;AACpE;AAEA,SAAS,mBAAmB,QAAQ;AAChC,SAAO,sBAAsB,QAAQ,CAAC;AAC1C;AAEA,SAAS,oBAAoB,QAAQ;AACjC,SAAO,sBAAsB,QAAQ,CAAC;AAC1C;AAEA,SAAS,mBAAmB,QAAQ;AAChC,SAAO,sBAAsB,QAAQ,CAAC;AAC1C;AAEA,SAAS,iBAAiB,QAAQ;AAC9B,SAAO,sBAAsB,QAAQ,CAAC;AAC1C;AAEA,SAAS,uBAAuB,QAAQ;AACpC,SAAO,kBAAkB,MAAM;AACnC;AAEA,SAAS,qBAAqB,QAAQ;AAClC,QAAM,SAASK,QAAO;AACtB,SAAO,sBAAsB,QAAQ,CAAC,KAAM,sBAAsB,QAAQ,CAAC,KAAK,YAAY,OAAO,cAAc,kBAAkBH,OAAM,OAAO,WAAW,QAAQ,GAAG,MAAM,CAAC,MAAM,cAAc;AACrM;AAEA,SAAS,wBAAwB,QAAQ;AACrC,SAAO,sBAAsB,QAAQ,CAAC;AAC1C;AAEA,SAAS,kBAAkB,QAAQ;AAC/B,QAAM,SAASG,QAAO;AACtB,SAAO,sBAAsB,QAAQ,CAAC,KAAM,sBAAsB,QAAQ,CAAC,KAAK,YAAY,OAAO,cAAc,kBAAkBH,OAAM,OAAO,WAAW,QAAQ,GAAG,MAAM,CAAC,MAAM,cAAc;AACrM;AAEA,SAAS,oBAAoB,QAAQ;AACjC,QAAM,OAAO,SAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACxC,SAAO,sBAAsB,QAAQ,CAAC,KAAM,sBAAsB,QAAQ,CAAC,KAAK,UAAU,OAAO,cAAc,kBAAkBA,OAAM,OAAO,WAAW,MAAM,GAAG,IAAI,CAAC,MAAM,cAAc;AAC/L;AAKA,SAAS,SAAS,MAAM,OAAO;AAC3B,SAAQA,OAAM,MAAM,KAAK,MAAM,cAAc,QAAQ,cAAc,QAC/DF,cAAU,WAAW,IAAI,KAAK,CAACA,cAAU,WAAW,KAAK,IAAI,cAAc,QACvE,cAAc;AAC1B;AAEA,SAAS,gBAAgB,MAAM,OAAO;AAClC,SAAQA,cAAU,UAAU,IAAI,IAAI,cAAc,QAC9CA,cAAU,MAAM,IAAI,IAAI,cAAc,QAASA,cAAU,QAAQ,IAAI,KAChEA,cAAU,gBAAgB,IAAI,KAAK,mBAAmB,KAAK,KAC3DA,cAAU,gBAAgB,IAAI,KAAK,mBAAmB,KAAK,KAC3DA,cAAU,iBAAiB,IAAI,KAAK,oBAAoB,KAAK,KAC7DA,cAAU,SAAS,IAAI,KAAK,mBAAmB,KAAK,KACpDA,cAAU,SAAS,IAAI,KAAK,mBAAmB,KAAK,KACpDA,cAAU,SAAS,IAAI,KAAK,mBAAmB,KAAK,KACpDA,cAAU,SAAS,IAAI,KAAK,mBAAmB,KAAK,KACpDA,cAAU,SAAS,IAAI,KAAK,mBAAmB,KAAK,KACpDA,cAAU,UAAU,IAAI,KAAK,mBAAmB,KAAK,KACrDA,cAAU,UAAU,IAAI,KAAK,oBAAoB,KAAK,KACtDA,cAAU,aAAa,IAAI,KAAK,uBAAuB,KAAK,KAC5DA,cAAU,OAAO,IAAI,KAAK,iBAAiB,KAAK,KAChDA,cAAU,cAAc,IAAI,KAAK,wBAAwB,KAAK,KAC9DA,cAAU,WAAW,IAAI,KAAK,qBAAqB,KAAK,IAAM,cAAc,OAC5EA,cAAU,SAAS,IAAI,KAAKA,cAAU,SAAS,UAAU,IAAI,CAAC,KAAM,MAAM;AAGvE,WAAO,MAAM,IAAI,MAAM,WAAW,cAAc,OAAO,cAAc;AAAA,EACzE,GAAG,IACEA,cAAU,SAAS,IAAI,KAAKA,cAAU,SAAS,UAAU,IAAI,CAAC,KAAM,MAAM;AACvE,WAAO,sBAAsB,OAAO,CAAC,IAAI,cAAc,OAAO,cAAc;AAAA,EAChF,GAAG,IACC,cAAc;AAClC;AAEA,SAAS,WAAW,MAAM,OAAO;AAC7B,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnD,CAACA,cAAU,SAAS,KAAK,IAAI,cAAc,SACtC,MAAM;AACH,eAAW,OAAO,OAAO,oBAAoB,MAAM,UAAU,GAAG;AAC5D,UAAI,EAAE,OAAO,KAAK,eAAe,CAACA,cAAU,WAAW,MAAM,WAAW,GAAG,CAAC,GAAG;AAC3E,eAAO,cAAc;AAAA,MACzB;AACA,UAAIA,cAAU,WAAW,MAAM,WAAW,GAAG,CAAC,GAAG;AAC7C,eAAO,cAAc;AAAA,MACzB;AACA,UAAI,SAAS,KAAK,WAAW,GAAG,GAAG,MAAM,WAAW,GAAG,CAAC,MAAM,cAAc,OAAO;AAC/E,eAAO,cAAc;AAAA,MACzB;AAAA,IACJ;AACA,WAAO,cAAc;AAAA,EACzB,GAAG;AACnB;AAKA,SAAS,YAAY,MAAM,OAAO;AAC9B,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DA,cAAU,SAAS,KAAK,KAAK,oBAAoB,KAAK,IAAI,cAAc,OACpE,CAACA,cAAU,UAAU,KAAK,IAAI,cAAc,QACxC,kBAAkBE,OAAM,KAAK,MAAM,MAAM,IAAI,CAAC;AAC9D;AAKA,SAAS,UAAU,QAAQ;AACvB,SAAQ,sBAAsB,OAAO,oBAAoBG,QAAO,IAC5D,sBAAsB,OAAO,oBAAoB,OAAO,IACpD,MAAM,4BAA4B;AAC9C;AAEA,SAAS,YAAY,QAAQ;AACzB,SAAQ,sBAAsB,OAAO,oBAAoB,OAAO,kBAAkB,kBAAkB,IAChG,sBAAsB,OAAO,oBAAoB,OAAO,kBAAkB,kBAAkB,IACxF,MAAM,mCAAmC;AACrD;AAEA,SAAS,gBAAgB,MAAM,OAAO;AAClC,QAAM,CAAC,KAAK,KAAK,IAAI,CAAC,UAAU,KAAK,GAAG,YAAY,KAAK,CAAC;AAC1D,SAASL,cAAU,gBAAgB,IAAI,KAAKA,cAAU,SAAS,GAAG,KAAK,kBAAkBE,OAAM,MAAM,KAAK,CAAC,MAAM,cAAc,OAAQ,cAAc,OACjJF,cAAU,aAAa,IAAI,KAAKA,cAAU,SAAS,GAAG,IAAIE,OAAM,MAAM,KAAK,IACvEF,cAAU,SAAS,IAAI,KAAKA,cAAU,SAAS,GAAG,IAAIE,OAAM,MAAM,KAAK,IACnEF,cAAU,QAAQ,IAAI,KAAKA,cAAU,SAAS,GAAG,IAAIE,OAAM,MAAM,KAAK,IAClEF,cAAU,SAAS,IAAI,KAAK,MAAM;AAC9B,eAAW,OAAO,OAAO,oBAAoB,KAAK,UAAU,GAAG;AAC3D,UAAI,SAAS,OAAO,KAAK,WAAW,GAAG,CAAC,MAAM,cAAc,OAAO;AAC/D,eAAO,cAAc;AAAA,MACzB;AAAA,IACJ;AACA,WAAO,cAAc;AAAA,EACzB,GAAG,IACC,cAAc;AACtC;AAEA,SAAS,WAAW,MAAM,OAAO;AAC7B,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnD,CAACA,cAAU,SAAS,KAAK,IAAI,cAAc,QACvCE,OAAM,YAAY,IAAI,GAAG,YAAY,KAAK,CAAC;AAC3D;AAKA,SAAS,WAAW,MAAM,OAAO;AAG7B,QAAM,IAAIF,cAAU,SAAS,IAAI,IAAI,OAAO,IAAI;AAChD,QAAM,IAAIA,cAAU,SAAS,KAAK,IAAI,OAAO,IAAI;AACjD,SAAOE,OAAM,GAAG,CAAC;AACrB;AAKA,SAAS,gBAAgB,MAAM,OAAO;AAClC,SAAQF,cAAU,UAAU,IAAI,KAAK,cAAW,SAAS,KAAK,KAAK,IAAI,cAAc,OACjFA,cAAU,SAAS,IAAI,IAAI,cAAc,OACrC,cAAc;AAC1B;AAEA,SAAS,WAAW,MAAM,OAAO;AAC7B,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,cAAc,OACtC,cAAc;AAClC;AAKA,SAAS,WAAW,MAAM,OAAO;AAC7B,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,cAAc,OACtC,cAAc;AAClC;AAKA,SAASM,qBAAoB,MAAM,OAAO;AAItC,SAAQN,cAAU,kBAAkB,IAAI,IAAIE,OAAM,uBAAuB,IAAI,GAAG,KAAK,IACjFF,cAAU,kBAAkB,KAAK,IAAIE,OAAM,MAAM,uBAAuB,KAAK,CAAC,IAC1E,MAAM,yCAAyC;AAC3D;AAKA,SAAS,eAAe,MAAM,OAAO;AACjC,SAAQF,cAAU,QAAQ,KAAK,KAC3B,KAAK,UAAU,UACf,KAAK,MAAM,MAAM,CAAC,WAAWE,OAAM,QAAQ,MAAM,KAAK,MAAM,cAAc,IAAI;AACtF;AAEA,SAAS,eAAe,MAAM,OAAO;AACjC,SAAQF,cAAU,QAAQ,IAAI,IAAI,cAAc,OAC5CA,cAAU,UAAU,IAAI,IAAI,cAAc,QACtCA,cAAU,MAAM,IAAI,IAAI,cAAc,QAClC,cAAc;AAC9B;AAEA,SAASO,WAAU,MAAM,OAAO;AAC5B,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DP,cAAU,SAAS,KAAK,KAAK,kBAAkB,KAAK,IAAI,cAAc,OAClEA,cAAU,QAAQ,KAAK,KAAK,eAAe,MAAM,KAAK,IAAI,cAAc,OACpE,CAACA,cAAU,QAAQ,KAAK,IAAI,cAAc,QACrC,cAAW,YAAY,KAAK,KAAK,KAAK,CAAC,cAAW,YAAY,MAAM,KAAK,KAAO,CAAC,cAAW,YAAY,KAAK,KAAK,KAAK,cAAW,YAAY,MAAM,KAAK,IAAK,cAAc,QACxK,cAAW,YAAY,KAAK,KAAK,KAAK,CAAC,cAAW,YAAY,MAAM,KAAK,IAAK,cAAc,OACzF,KAAK,MAAM,MAAM,CAAC,QAAQ,UAAUE,OAAM,QAAQ,MAAM,MAAM,KAAK,CAAC,MAAM,cAAc,IAAI,IAAI,cAAc,OAC1G,cAAc;AAC9C;AAKA,SAAS,eAAe,MAAM,OAAO;AACjC,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DF,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,aAAa,KAAK,IAAI,cAAc,OAC1C,cAAc;AAClC;AAKA,SAAS,cAAc,MAAM,OAAO;AAChC,SAAQ,kBAAkB,KAAK,IAAI,gBAAgB,MAAM,KAAK,IAC1DA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,OAAO,KAAK,IAAI,cAAc,MAAM,KAAK,IAC/CA,cAAU,YAAY,KAAK,IAAI,cAAc,OACzC,cAAc;AACtC;AAKA,SAAS,eAAe,MAAM,OAAO;AACjC,SAAO,MAAM,MAAM,KAAK,CAAC,WAAWE,OAAM,MAAM,MAAM,MAAM,cAAc,IAAI,IACxE,cAAc,OACd,cAAc;AACxB;AAEA,SAASM,WAAU,MAAM,OAAO;AAC5B,SAAO,KAAK,MAAM,MAAM,CAAC,WAAWN,OAAM,QAAQ,KAAK,MAAM,cAAc,IAAI,IACzE,cAAc,OACd,cAAc;AACxB;AAKA,SAAS,iBAAiB,MAAM,OAAO;AACnC,SAAO,cAAc;AACzB;AAEA,SAAS,YAAY,MAAM,OAAO;AAC9B,SAAQF,cAAU,QAAQ,KAAK,IAAI,eAAe,MAAM,KAAK,IACzDA,cAAU,YAAY,KAAK,IAAI,mBAAmB,MAAM,KAAK,IACzDA,cAAU,QAAQ,KAAK,IAAI,eAAe,MAAM,KAAK,IACjDA,cAAU,MAAM,KAAK,IAAI,aAAa,MAAM,KAAK,IAC7CA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,UAAU,KAAK,IAAI,iBAAiB,MAAM,KAAK,IACrDA,cAAU,UAAU,KAAK,IAAI,iBAAiB,MAAM,KAAK,IACrDA,cAAU,QAAQ,KAAK,IAAI,eAAe,MAAM,KAAK,IACjDA,cAAU,QAAQ,KAAK,IAAI,eAAe,MAAM,KAAK,IACjDA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,UAAU,KAAK,IAAI,cAAc,OACvC,cAAc;AAClE;AAKA,SAAS,cAAc,MAAM,OAAO;AAChC,SAAQA,cAAU,YAAY,IAAI,IAAI,cAAc,OAChDA,cAAU,YAAY,IAAI,IAAI,cAAc,OACxC,cAAc;AAC1B;AAEA,SAAS,SAAS,MAAM,OAAO;AAC3B,SAAQA,cAAU,YAAY,KAAK,IAAI,mBAAmB,MAAM,KAAK,IACjEA,cAAU,QAAQ,KAAK,IAAI,eAAe,MAAM,KAAK,IACjDA,cAAU,UAAU,KAAK,IAAI,iBAAiB,MAAM,KAAK,IACrDA,cAAU,MAAM,KAAK,IAAI,aAAa,MAAM,KAAK,IAC7CA,cAAU,SAAS,KAAK,IAAI,gBAAgB,MAAM,KAAK,IACnDA,cAAU,OAAO,KAAK,IAAI,cAAc,OACpC,cAAc;AAC1C;AAEA,SAASE,OAAM,MAAM,OAAO;AACxB;AAAA;AAAA,IAECF,cAAU,kBAAkB,IAAI,KAAKA,cAAU,kBAAkB,KAAK,IAAKM,qBAAoB,MAAM,KAAK,IACtGN,cAAU,SAAS,IAAI,KAAKA,cAAU,SAAS,KAAK,IAAK,WAAW,MAAM,KAAK,IAC3EA,cAAU,MAAM,IAAI,KAAKA,cAAU,MAAM,KAAK,IAAK,QAAQ,MAAM,KAAK;AAAA;AAAA,MAEnEA,cAAU,MAAM,IAAI,IAAI,QAAQ,MAAM,KAAK,IACvCA,cAAU,QAAQ,IAAI,IAAIC,WAAU,MAAM,KAAK,IAC3CD,cAAU,SAAS,IAAI,IAAI,WAAW,MAAM,KAAK,IAC7CA,cAAU,UAAU,IAAI,IAAI,YAAY,MAAM,KAAK,IAC/CA,cAAU,gBAAgB,IAAI,IAAI,kBAAkB,MAAM,KAAK,IAC3DA,cAAU,cAAc,IAAI,IAAI,gBAAgB,MAAM,KAAK,IACvDA,cAAU,OAAO,IAAI,IAAI,SAAS,MAAM,KAAK,IACzCA,cAAU,WAAW,IAAI,IAAI,aAAa,MAAM,KAAK,IACjDA,cAAU,UAAU,IAAI,IAAI,YAAY,MAAM,KAAK,IAC/CA,cAAU,YAAY,IAAI,IAAIG,eAAc,MAAM,KAAK,IACnDH,cAAU,WAAW,IAAI,IAAI,aAAa,MAAM,KAAK,IACjDA,cAAU,UAAU,IAAI,IAAII,aAAY,MAAM,KAAK,IAC/CJ,cAAU,QAAQ,IAAI,IAAI,UAAU,MAAM,KAAK,IAC3CA,cAAU,OAAO,IAAI,IAAI,SAAS,MAAM,KAAK,IACzCA,cAAU,SAAS,IAAI,IAAI,WAAW,MAAM,KAAK,IAC7CA,cAAU,SAAS,IAAI,IAAI,WAAW,MAAM,KAAK,IAC7CA,cAAU,SAAS,IAAI,IAAI,WAAW,MAAM,KAAK,IAC7CA,cAAU,SAAS,IAAI,IAAI,WAAW,MAAM,KAAK,IAC7CA,cAAU,SAAS,IAAI,IAAI,WAAW,MAAM,KAAK,IAC7CA,cAAU,QAAQ,IAAI,IAAIO,WAAU,MAAM,KAAK,IAC3CP,cAAU,UAAU,IAAI,IAAI,YAAY,MAAM,KAAK,IAC/CA,cAAU,aAAa,IAAI,IAAI,eAAe,MAAM,KAAK,IACrDA,cAAU,YAAY,IAAI,IAAI,cAAc,MAAM,KAAK,IACnDA,cAAU,QAAQ,IAAI,IAAIQ,WAAU,MAAM,KAAK,IAC3CR,cAAU,UAAU,IAAI,IAAI,YAAY,MAAM,KAAK,IAC/CA,cAAU,OAAO,IAAI,IAAI,SAAS,MAAM,KAAK,IACzC,MAAM,8BAA8B,KAAK,IAAI,CAAC,GAAG;AAAA;AAAA;AACzK;AACO,SAAS,aAAa,MAAM,OAAO;AACtC,SAAOE,OAAM,MAAM,KAAK;AAC5B;;;ACrnBA,SAAS,gBAAgB,GAAG,GAAG,GAAG,GAAG,SAAS;AAC1C,SAAO;AAAA,IACH,CAAC,CAAC,GAAG,QAAQ,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,MAAM,OAAO,CAAC;AAAA,EACpD;AACJ;AAEA,SAAS,iBAAiB,GAAG,GAAG,GAAG,GAAG,SAAS;AAC3C,SAAO,EAAE,OAAO,CAAC,KAAK,OAAO;AACzB,WAAO,EAAE,GAAG,KAAK,GAAG,gBAAgB,IAAI,GAAG,GAAG,GAAG,OAAO,EAAE;AAAA,EAC9D,GAAG,CAAC,CAAC;AACT;AAEA,SAASO,eAAc,GAAG,GAAG,GAAG,GAAG,SAAS;AACxC,SAAO,iBAAiB,EAAE,MAAM,GAAG,GAAG,GAAG,OAAO;AACpD;AAEO,SAAS,qBAAqB,GAAG,GAAG,GAAG,GAAG,SAAS;AACtD,QAAM,IAAIA,eAAc,GAAG,GAAG,GAAG,GAAG,OAAO;AAC3C,SAAO,aAAa,CAAC;AACzB;;;ACdA,SAAS,eAAe,MAAM,OAAO,UAAU,WAAW;AACtD,QAAM,IAAI,aAAa,MAAM,KAAK;AAClC,SAAQ,MAAM,cAAc,QAAQ,MAAM,CAAC,UAAU,SAAS,CAAC,IAC3D,MAAM,cAAc,OAAO,WACvB;AACZ;AAEO,SAAS,QAAQ,GAAG,GAAG,GAAG,GAAG,SAAS;AAEzC,SAAQ,eAAe,CAAC,IAAI,wBAAwB,GAAG,GAAG,GAAG,GAAG,OAAO,IACnE,YAAY,CAAC,IAAI,WAAW,qBAAqB,GAAG,GAAG,GAAG,GAAG,OAAO,CAAC,IACjE,WAAW,eAAe,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO;AAC1D;;;AClBA,SAASC,gBAAe,GAAG,OAAO,MAAM,OAAO,SAAS;AACpD,QAAM,MAAM,CAAC;AACb,aAAW,MAAM,WAAW,OAAO,oBAAoB,CAAC;AACpD,QAAI,EAAE,IAAI,QAAQ,EAAE,EAAE,GAAG,OAAO,MAAM,OAAO,MAAM,OAAO,CAAC;AAC/D,SAAO;AACX;AAEA,SAASC,kBAAiB,MAAM,OAAO,MAAM,OAAO,SAAS;AACzD,SAAOD,gBAAe,KAAK,YAAY,OAAO,MAAM,OAAO,OAAO;AACtE;AAEO,SAAS,wBAAwB,MAAM,OAAO,MAAM,OAAO,SAAS;AACvE,QAAM,IAAIC,kBAAiB,MAAM,OAAO,MAAM,OAAO,OAAO;AAC5D,SAAO,aAAa,CAAC;AACzB;", "names": ["Set", "Clear", "Delete", "Entries", "Get", "Has", "Set", "map", "value", "IsAsyncIterator", "IsObject", "IsIterator", "IsDate", "IsUint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IsObject", "IsArray", "IsUndefined", "IsNull", "IsBoolean", "IsNumber", "IsBigInt", "IsString", "IsFunction", "IsSymbol", "TypeSystemPolicy", "IsObject", "IsArray", "IsNumber", "IsUndefined", "value", "Array", "IsArray", "IsAsyncIterator", "IsBigInt", "IsBoolean", "IsDate", "IsFunction", "IsInteger", "IsIterator", "IsNull", "IsNumber", "IsObject", "IsPromise", "IsRegExp", "IsString", "IsSymbol", "IsUint8Array", "IsUndefined", "IsArray", "IsAsyncIterator", "IsBigInt", "IsBoolean", "IsDate", "IsFunction", "IsInteger", "IsIterator", "IsNull", "IsNumber", "IsObject", "IsPromise", "IsRegExp", "IsString", "IsSymbol", "IsUndefined", "IsUint8Array", "IsObject", "RemoveOptionalFromRest", "RemoveOptionalFromType", "range", "pattern", "Number", "Number", "Visit", "schema", "IsNumber", "IsInteger", "IsBigInt", "IsString", "IsBoolean", "FromUnion", "IsNumber", "IsInteger", "FromUnion", "IsArray", "IsObject", "Object", "Promise", "FromProperties", "FromMappedResult", "FromMappedResult", "FromRest", "FromProperties", "IsFunction", "IsAsyncIterator", "IsIterator", "IsObject", "Object", "IsArray", "Array", "IsPromise", "Promise", "map", "FromProperties", "FromMappedResult", "FromRest", "FromIntersect", "FromUnion", "FromTuple", "FromArray", "FromProperties", "IsArray", "IsObject", "FromProperties", "FromMappedResult", "Number", "Intersect", "schema", "Union", "type_exports", "IsAny", "IsArgument", "IsArray", "IsAsyncIterator", "IsBigInt", "IsBoolean", "IsComputed", "IsConstructor", "IsDate", "IsFunction", "IsImport", "IsInteger", "IsIntersect", "IsIterator", "IsKind", "IsKindOf", "IsLiteral", "IsLiteralBoolean", "IsLiteralNumber", "IsLiteralString", "IsLiteralValue", "IsMappedKey", "IsMappedResult", "IsNever", "IsNot", "IsNull", "IsNumber", "IsObject", "IsOptional", "IsPromise", "IsProperties", "Is<PERSON><PERSON>only", "IsRecord", "IsRecursive", "IsRef", "IsRegExp", "IsSchema", "IsString", "IsSymbol", "IsTemplateLiteral", "IsThis", "IsTransform", "Is<PERSON><PERSON>le", "IsUint8Array", "IsUndefined", "IsUnion", "IsUnknown", "IsUnsafe", "IsVoid", "IsSchema", "Is<PERSON><PERSON>only", "IsOptional", "IsAny", "IsKindOf", "IsArgument", "IsArray", "IsAsyncIterator", "IsBigInt", "IsBoolean", "IsComputed", "IsConstructor", "IsDate", "IsFunction", "IsImport", "IsProperties", "IsInteger", "IsIntersect", "IsTransform", "IsIterator", "IsLiteralString", "IsLiteral", "IsLiteralNumber", "IsLiteralBoolean", "IsLiteralValue", "IsMappedKey", "IsMappedResult", "IsNever", "IsNot", "IsNull", "IsNumber", "IsObject", "IsPromise", "IsRecord", "IsRecursive", "IsRef", "IsRegExp", "IsString", "IsSymbol", "IsTemplateLiteral", "IsThis", "Is<PERSON><PERSON>le", "IsUndefined", "IsUnion", "IsUint8Array", "IsUnknown", "IsUnsafe", "IsVoid", "IsKind", "ExtendsResult", "type_exports", "FromArray", "Visit", "FromIntersect", "FromLiteral", "Number", "FromTemplateLiteral", "FromTuple", "FromUnion", "FromMappedKey", "FromProperties", "FromMappedResult"]}