{"version": 3, "sources": ["../../@stripe/stripe-js/dist/index.js", "../../@stripe/stripe-js/lib/index.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nvar V3_URL = 'https://js.stripe.com/v3';\nvar V3_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/v3\\/?(\\?.*)?$/;\nvar EXISTING_SCRIPT_MESSAGE = 'loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used';\nvar findScript = function findScript() {\n  var scripts = document.querySelectorAll(\"script[src^=\\\"\".concat(V3_URL, \"\\\"]\"));\n\n  for (var i = 0; i < scripts.length; i++) {\n    var script = scripts[i];\n\n    if (!V3_URL_REGEX.test(script.src)) {\n      continue;\n    }\n\n    return script;\n  }\n\n  return null;\n};\n\nvar injectScript = function injectScript(params) {\n  var queryString = params && !params.advancedFraudSignals ? '?advancedFraudSignals=false' : '';\n  var script = document.createElement('script');\n  script.src = \"\".concat(V3_URL).concat(queryString);\n  var headOrBody = document.head || document.body;\n\n  if (!headOrBody) {\n    throw new Error('Expected document.body not to be null. Stripe.js requires a <body> element.');\n  }\n\n  headOrBody.appendChild(script);\n  return script;\n};\n\nvar registerWrapper = function registerWrapper(stripe, startTime) {\n  if (!stripe || !stripe._registerWrapper) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'stripe-js',\n    version: \"4.6.0\",\n    startTime: startTime\n  });\n};\n\nvar stripePromise = null;\nvar onErrorListener = null;\nvar onLoadListener = null;\n\nvar onError = function onError(reject) {\n  return function () {\n    reject(new Error('Failed to load Stripe.js'));\n  };\n};\n\nvar onLoad = function onLoad(resolve, reject) {\n  return function () {\n    if (window.Stripe) {\n      resolve(window.Stripe);\n    } else {\n      reject(new Error('Stripe.js not available'));\n    }\n  };\n};\n\nvar loadScript = function loadScript(params) {\n  // Ensure that we only attempt to load Stripe.js at most once\n  if (stripePromise !== null) {\n    return stripePromise;\n  }\n\n  stripePromise = new Promise(function (resolve, reject) {\n    if (typeof window === 'undefined' || typeof document === 'undefined') {\n      // Resolve to null when imported server side. This makes the module\n      // safe to import in an isomorphic code base.\n      resolve(null);\n      return;\n    }\n\n    if (window.Stripe && params) {\n      console.warn(EXISTING_SCRIPT_MESSAGE);\n    }\n\n    if (window.Stripe) {\n      resolve(window.Stripe);\n      return;\n    }\n\n    try {\n      var script = findScript();\n\n      if (script && params) {\n        console.warn(EXISTING_SCRIPT_MESSAGE);\n      } else if (!script) {\n        script = injectScript(params);\n      } else if (script && onLoadListener !== null && onErrorListener !== null) {\n        var _script$parentNode;\n\n        // remove event listeners\n        script.removeEventListener('load', onLoadListener);\n        script.removeEventListener('error', onErrorListener); // if script exists, but we are reloading due to an error,\n        // reload script to trigger 'load' event\n\n        (_script$parentNode = script.parentNode) === null || _script$parentNode === void 0 ? void 0 : _script$parentNode.removeChild(script);\n        script = injectScript(params);\n      }\n\n      onLoadListener = onLoad(resolve, reject);\n      onErrorListener = onError(reject);\n      script.addEventListener('load', onLoadListener);\n      script.addEventListener('error', onErrorListener);\n    } catch (error) {\n      reject(error);\n      return;\n    }\n  }); // Resets stripePromise on error\n\n  return stripePromise[\"catch\"](function (error) {\n    stripePromise = null;\n    return Promise.reject(error);\n  });\n};\nvar initStripe = function initStripe(maybeStripe, args, startTime) {\n  if (maybeStripe === null) {\n    return null;\n  }\n\n  var stripe = maybeStripe.apply(undefined, args);\n  registerWrapper(stripe, startTime);\n  return stripe;\n}; // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n\nvar stripePromise$1;\nvar loadCalled = false;\n\nvar getStripePromise = function getStripePromise() {\n  if (stripePromise$1) {\n    return stripePromise$1;\n  }\n\n  stripePromise$1 = loadScript(null)[\"catch\"](function (error) {\n    // clear cache on error\n    stripePromise$1 = null;\n    return Promise.reject(error);\n  });\n  return stripePromise$1;\n}; // Execute our own script injection after a tick to give users time to do their\n// own script injection.\n\n\nPromise.resolve().then(function () {\n  return getStripePromise();\n})[\"catch\"](function (error) {\n  if (!loadCalled) {\n    console.warn(error);\n  }\n});\nvar loadStripe = function loadStripe() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  loadCalled = true;\n  var startTime = Date.now(); // if previous attempts are unsuccessful, will re-load script\n\n  return getStripePromise().then(function (maybeStripe) {\n    return initStripe(maybeStripe, args, startTime);\n  });\n};\n\nexports.loadStripe = loadStripe;\n", "// eslint-disable-next-line no-undef\nmodule.exports = require('../dist');\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,QAAI,SAAS;AACb,QAAI,eAAe;AACnB,QAAI,0BAA0B;AAC9B,QAAI,aAAa,SAASA,cAAa;AACrC,UAAI,UAAU,SAAS,iBAAiB,gBAAiB,OAAO,QAAQ,IAAK,CAAC;AAE9E,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAI,SAAS,QAAQ,CAAC;AAEtB,YAAI,CAAC,aAAa,KAAK,OAAO,GAAG,GAAG;AAClC;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,eAAe,SAASC,cAAa,QAAQ;AAC/C,UAAI,cAAc,UAAU,CAAC,OAAO,uBAAuB,gCAAgC;AAC3F,UAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,aAAO,MAAM,GAAG,OAAO,MAAM,EAAE,OAAO,WAAW;AACjD,UAAI,aAAa,SAAS,QAAQ,SAAS;AAE3C,UAAI,CAAC,YAAY;AACf,cAAM,IAAI,MAAM,6EAA6E;AAAA,MAC/F;AAEA,iBAAW,YAAY,MAAM;AAC7B,aAAO;AAAA,IACT;AAEA,QAAI,kBAAkB,SAASC,iBAAgB,QAAQ,WAAW;AAChE,UAAI,CAAC,UAAU,CAAC,OAAO,kBAAkB;AACvC;AAAA,MACF;AAEA,aAAO,iBAAiB;AAAA,QACtB,MAAM;AAAA,QACN,SAAS;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAI,gBAAgB;AACpB,QAAI,kBAAkB;AACtB,QAAI,iBAAiB;AAErB,QAAI,UAAU,SAASC,SAAQ,QAAQ;AACrC,aAAO,WAAY;AACjB,eAAO,IAAI,MAAM,0BAA0B,CAAC;AAAA,MAC9C;AAAA,IACF;AAEA,QAAI,SAAS,SAASC,QAAO,SAAS,QAAQ;AAC5C,aAAO,WAAY;AACjB,YAAI,OAAO,QAAQ;AACjB,kBAAQ,OAAO,MAAM;AAAA,QACvB,OAAO;AACL,iBAAO,IAAI,MAAM,yBAAyB,CAAC;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAEA,QAAI,aAAa,SAASC,YAAW,QAAQ;AAE3C,UAAI,kBAAkB,MAAM;AAC1B,eAAO;AAAA,MACT;AAEA,sBAAgB,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACrD,YAAI,OAAO,WAAW,eAAe,OAAO,aAAa,aAAa;AAGpE,kBAAQ,IAAI;AACZ;AAAA,QACF;AAEA,YAAI,OAAO,UAAU,QAAQ;AAC3B,kBAAQ,KAAK,uBAAuB;AAAA,QACtC;AAEA,YAAI,OAAO,QAAQ;AACjB,kBAAQ,OAAO,MAAM;AACrB;AAAA,QACF;AAEA,YAAI;AACF,cAAI,SAAS,WAAW;AAExB,cAAI,UAAU,QAAQ;AACpB,oBAAQ,KAAK,uBAAuB;AAAA,UACtC,WAAW,CAAC,QAAQ;AAClB,qBAAS,aAAa,MAAM;AAAA,UAC9B,WAAW,UAAU,mBAAmB,QAAQ,oBAAoB,MAAM;AACxE,gBAAI;AAGJ,mBAAO,oBAAoB,QAAQ,cAAc;AACjD,mBAAO,oBAAoB,SAAS,eAAe;AAGnD,aAAC,qBAAqB,OAAO,gBAAgB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,YAAY,MAAM;AACnI,qBAAS,aAAa,MAAM;AAAA,UAC9B;AAEA,2BAAiB,OAAO,SAAS,MAAM;AACvC,4BAAkB,QAAQ,MAAM;AAChC,iBAAO,iBAAiB,QAAQ,cAAc;AAC9C,iBAAO,iBAAiB,SAAS,eAAe;AAAA,QAClD,SAAS,OAAO;AACd,iBAAO,KAAK;AACZ;AAAA,QACF;AAAA,MACF,CAAC;AAED,aAAO,cAAc,OAAO,EAAE,SAAU,OAAO;AAC7C,wBAAgB;AAChB,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,QAAI,aAAa,SAASC,YAAW,aAAa,MAAM,WAAW;AACjE,UAAI,gBAAgB,MAAM;AACxB,eAAO;AAAA,MACT;AAEA,UAAI,SAAS,YAAY,MAAM,QAAW,IAAI;AAC9C,sBAAgB,QAAQ,SAAS;AACjC,aAAO;AAAA,IACT;AAEA,QAAI;AACJ,QAAI,aAAa;AAEjB,QAAI,mBAAmB,SAASC,oBAAmB;AACjD,UAAI,iBAAiB;AACnB,eAAO;AAAA,MACT;AAEA,wBAAkB,WAAW,IAAI,EAAE,OAAO,EAAE,SAAU,OAAO;AAE3D,0BAAkB;AAClB,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC7B,CAAC;AACD,aAAO;AAAA,IACT;AAIA,YAAQ,QAAQ,EAAE,KAAK,WAAY;AACjC,aAAO,iBAAiB;AAAA,IAC1B,CAAC,EAAE,OAAO,EAAE,SAAU,OAAO;AAC3B,UAAI,CAAC,YAAY;AACf,gBAAQ,KAAK,KAAK;AAAA,MACpB;AAAA,IACF,CAAC;AACD,QAAI,aAAa,SAASC,cAAa;AACrC,eAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,aAAK,IAAI,IAAI,UAAU,IAAI;AAAA,MAC7B;AAEA,mBAAa;AACb,UAAI,YAAY,KAAK,IAAI;AAEzB,aAAO,iBAAiB,EAAE,KAAK,SAAU,aAAa;AACpD,eAAO,WAAW,aAAa,MAAM,SAAS;AAAA,MAChD,CAAC;AAAA,IACH;AAEA,YAAQ,aAAa;AAAA;AAAA;;;AC9KrB;AAAA;AACA,WAAO,UAAU;AAAA;AAAA;", "names": ["findScript", "injectScript", "registerWrapper", "onError", "onLoad", "loadScript", "initStripe", "getStripePromise", "loadStripe"]}