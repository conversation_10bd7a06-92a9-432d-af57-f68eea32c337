import "./chunk-KWPVD4H7.js";

// node_modules/@typeschema/core/dist/index.mjs
function memoize(fn) {
  let cache = void 0;
  const memoizedFn = async () => {
    if (cache === void 0) {
      cache = await fn();
    }
    return cache;
  };
  memoizedFn.clear = () => cache = void 0;
  return memoizedFn;
}
function memoizeWithKey(fn) {
  const cache = /* @__PURE__ */ new Map();
  const memoizedFn = async (key) => {
    if (!cache.has(key)) {
      cache.set(key, await fn(key));
    }
    return cache.get(key);
  };
  memoizedFn.clear = () => cache.clear();
  return memoizedFn;
}
function createValidate(validationAdapter2) {
  const memoizedValidationAdapter = memoizeWithKey(
    (schema) => validationAdapter2(schema)
  );
  return async (schema, data) => {
    const validateSchema = await memoizedValidationAdapter(schema);
    return validateSchema(data);
  };
}
function createAssert(validate2) {
  return async (schema, data) => {
    const result = await validate2(schema, data);
    if (result.success) {
      return result.data;
    }
    throw new AggregateError(result.issues, "Assertion failed");
  };
}
function createWrap(assert2, validate2) {
  return (schema) => ({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    _input: void 0,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    _output: void 0,
    assert: (data) => assert2(schema, data),
    parse: (data) => assert2(schema, data),
    validate: (data) => validate2(schema, data)
  });
}

// node_modules/@typeschema/class-validator/dist/index.mjs
var importValidationModule = memoize(async () => {
  try {
    var dynamicallyImportedModule = await import(
      /* webpackIgnore: true */
      "./cjs-DFQOWV6N.js"
    );
  } catch (moduleImportError) {
    throw moduleImportError;
  }
  const { validate: validate2 } = dynamicallyImportedModule;
  return { validate: validate2 };
});
function getIssues(error, parentPath) {
  var _a;
  const path = [
    ...parentPath,
    Number.isInteger(+error.property) ? +error.property : error.property
  ];
  return Object.values(error.constraints ?? {}).map((message) => ({ message, path })).concat(
    ((_a = error.children) == null ? void 0 : _a.flatMap((childError) => getIssues(childError, path))) ?? []
  );
}
var validationAdapter = async (schema) => {
  const { validate: validate2 } = await importValidationModule();
  return async (data) => {
    const errors = await validate2(Object.assign(new schema(), data));
    if (errors.length === 0) {
      return {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        data,
        success: true
      };
    }
    return {
      issues: errors.flatMap((error) => getIssues(error, [])),
      success: false
    };
  };
};
var validate = createValidate(validationAdapter);
var assert = createAssert(validate);
var wrap = createWrap(assert, validate);
export {
  assert,
  validate,
  validationAdapter,
  wrap
};
//# sourceMappingURL=@typeschema_class-validator.js.map
