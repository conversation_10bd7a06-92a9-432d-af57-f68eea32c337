{"version": 3, "sources": ["../../agent-base/src/promisify.ts", "../../agent-base/src/index.ts"], "sourcesContent": ["import {\n\tAgent,\n\tClientRequest,\n\tRequestOptions,\n\tAgentCallbackCallback,\n\tAgentCallbackPromise,\n\tAgentCallbackReturn\n} from './index';\n\ntype LegacyCallback = (\n\treq: ClientRequest,\n\topts: RequestOptions,\n\tfn: AgentCallbackCallback\n) => void;\n\nexport default function promisify(fn: LegacyCallback): AgentCallbackPromise {\n\treturn function(this: Agent, req: ClientRequest, opts: RequestOptions) {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tfn.call(\n\t\t\t\tthis,\n\t\t\t\treq,\n\t\t\t\topts,\n\t\t\t\t(err: Error | null | undefined, rtn?: AgentCallbackReturn) => {\n\t\t\t\t\tif (err) {\n\t\t\t\t\t\treject(err);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresolve(rtn);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t);\n\t\t});\n\t};\n}\n", "import net from 'net';\nimport http from 'http';\nimport https from 'https';\nimport { Duplex } from 'stream';\nimport { EventEmitter } from 'events';\nimport createDebug from 'debug';\nimport promisify from './promisify';\n\nconst debug = createDebug('agent-base');\n\nfunction isAgent(v: any): v is createAgent.AgentLike {\n\treturn Boolean(v) && typeof v.addRequest === 'function';\n}\n\nfunction isSecureEndpoint(): boolean {\n\tconst { stack } = new Error();\n\tif (typeof stack !== 'string') return false;\n\treturn stack.split('\\n').some(l => l.indexOf('(https.js:') !== -1  || l.indexOf('node:https:') !== -1);\n}\n\nfunction createAgent(opts?: createAgent.AgentOptions): createAgent.Agent;\nfunction createAgent(\n\tcallback: createAgent.AgentCallback,\n\topts?: createAgent.AgentOptions\n): createAgent.Agent;\nfunction createAgent(\n\tcallback?: createAgent.AgentCallback | createAgent.AgentOptions,\n\topts?: createAgent.AgentOptions\n) {\n\treturn new createAgent.Agent(callback, opts);\n}\n\nnamespace createAgent {\n\texport interface ClientRequest extends http.ClientRequest {\n\t\t_last?: boolean;\n\t\t_hadError?: boolean;\n\t\tmethod: string;\n\t}\n\n\texport interface AgentRequestOptions {\n\t\thost?: string;\n\t\tpath?: string;\n\t\t// `port` on `http.RequestOptions` can be a string or undefined,\n\t\t// but `net.TcpNetConnectOpts` expects only a number\n\t\tport: number;\n\t}\n\n\texport interface HttpRequestOptions\n\t\textends AgentRequestOptions,\n\t\t\tOmit<http.RequestOptions, keyof AgentRequestOptions> {\n\t\tsecureEndpoint: false;\n\t}\n\n\texport interface HttpsRequestOptions\n\t\textends AgentRequestOptions,\n\t\t\tOmit<https.RequestOptions, keyof AgentRequestOptions> {\n\t\tsecureEndpoint: true;\n\t}\n\n\texport type RequestOptions = HttpRequestOptions | HttpsRequestOptions;\n\n\texport type AgentLike = Pick<createAgent.Agent, 'addRequest'> | http.Agent;\n\n\texport type AgentCallbackReturn = Duplex | AgentLike;\n\n\texport type AgentCallbackCallback = (\n\t\terr?: Error | null,\n\t\tsocket?: createAgent.AgentCallbackReturn\n\t) => void;\n\n\texport type AgentCallbackPromise = (\n\t\treq: createAgent.ClientRequest,\n\t\topts: createAgent.RequestOptions\n\t) =>\n\t\t| createAgent.AgentCallbackReturn\n\t\t| Promise<createAgent.AgentCallbackReturn>;\n\n\texport type AgentCallback = typeof Agent.prototype.callback;\n\n\texport type AgentOptions = {\n\t\ttimeout?: number;\n\t};\n\n\t/**\n\t * Base `http.Agent` implementation.\n\t * No pooling/keep-alive is implemented by default.\n\t *\n\t * @param {Function} callback\n\t * @api public\n\t */\n\texport class Agent extends EventEmitter {\n\t\tpublic timeout: number | null;\n\t\tpublic maxFreeSockets: number;\n\t\tpublic maxTotalSockets: number;\n\t\tpublic maxSockets: number;\n\t\tpublic sockets: {\n\t\t\t[key: string]: net.Socket[];\n\t\t};\n\t\tpublic freeSockets: {\n\t\t\t[key: string]: net.Socket[];\n\t\t};\n\t\tpublic requests: {\n\t\t\t[key: string]: http.IncomingMessage[];\n\t\t};\n\t\tpublic options: https.AgentOptions;\n\t\tprivate promisifiedCallback?: createAgent.AgentCallbackPromise;\n\t\tprivate explicitDefaultPort?: number;\n\t\tprivate explicitProtocol?: string;\n\n\t\tconstructor(\n\t\t\tcallback?: createAgent.AgentCallback | createAgent.AgentOptions,\n\t\t\t_opts?: createAgent.AgentOptions\n\t\t) {\n\t\t\tsuper();\n\n\t\t\tlet opts = _opts;\n\t\t\tif (typeof callback === 'function') {\n\t\t\t\tthis.callback = callback;\n\t\t\t} else if (callback) {\n\t\t\t\topts = callback;\n\t\t\t}\n\n\t\t\t// Timeout for the socket to be returned from the callback\n\t\t\tthis.timeout = null;\n\t\t\tif (opts && typeof opts.timeout === 'number') {\n\t\t\t\tthis.timeout = opts.timeout;\n\t\t\t}\n\n\t\t\t// These aren't actually used by `agent-base`, but are required\n\t\t\t// for the TypeScript definition files in `@types/node` :/\n\t\t\tthis.maxFreeSockets = 1;\n\t\t\tthis.maxSockets = 1;\n\t\t\tthis.maxTotalSockets = Infinity;\n\t\t\tthis.sockets = {};\n\t\t\tthis.freeSockets = {};\n\t\t\tthis.requests = {};\n\t\t\tthis.options = {};\n\t\t}\n\n\t\tget defaultPort(): number {\n\t\t\tif (typeof this.explicitDefaultPort === 'number') {\n\t\t\t\treturn this.explicitDefaultPort;\n\t\t\t}\n\t\t\treturn isSecureEndpoint() ? 443 : 80;\n\t\t}\n\n\t\tset defaultPort(v: number) {\n\t\t\tthis.explicitDefaultPort = v;\n\t\t}\n\n\t\tget protocol(): string {\n\t\t\tif (typeof this.explicitProtocol === 'string') {\n\t\t\t\treturn this.explicitProtocol;\n\t\t\t}\n\t\t\treturn isSecureEndpoint() ? 'https:' : 'http:';\n\t\t}\n\n\t\tset protocol(v: string) {\n\t\t\tthis.explicitProtocol = v;\n\t\t}\n\n\t\tcallback(\n\t\t\treq: createAgent.ClientRequest,\n\t\t\topts: createAgent.RequestOptions,\n\t\t\tfn: createAgent.AgentCallbackCallback\n\t\t): void;\n\t\tcallback(\n\t\t\treq: createAgent.ClientRequest,\n\t\t\topts: createAgent.RequestOptions\n\t\t):\n\t\t\t| createAgent.AgentCallbackReturn\n\t\t\t| Promise<createAgent.AgentCallbackReturn>;\n\t\tcallback(\n\t\t\treq: createAgent.ClientRequest,\n\t\t\topts: createAgent.AgentOptions,\n\t\t\tfn?: createAgent.AgentCallbackCallback\n\t\t):\n\t\t\t| createAgent.AgentCallbackReturn\n\t\t\t| Promise<createAgent.AgentCallbackReturn>\n\t\t\t| void {\n\t\t\tthrow new Error(\n\t\t\t\t'\"agent-base\" has no default implementation, you must subclass and override `callback()`'\n\t\t\t);\n\t\t}\n\n\t\t/**\n\t\t * Called by node-core's \"_http_client.js\" module when creating\n\t\t * a new HTTP request with this Agent instance.\n\t\t *\n\t\t * @api public\n\t\t */\n\t\taddRequest(req: ClientRequest, _opts: RequestOptions): void {\n\t\t\tconst opts: RequestOptions = { ..._opts };\n\n\t\t\tif (typeof opts.secureEndpoint !== 'boolean') {\n\t\t\t\topts.secureEndpoint = isSecureEndpoint();\n\t\t\t}\n\n\t\t\tif (opts.host == null) {\n\t\t\t\topts.host = 'localhost';\n\t\t\t}\n\n\t\t\tif (opts.port == null) {\n\t\t\t\topts.port = opts.secureEndpoint ? 443 : 80;\n\t\t\t}\n\n\t\t\tif (opts.protocol == null) {\n\t\t\t\topts.protocol = opts.secureEndpoint ? 'https:' : 'http:';\n\t\t\t}\n\n\t\t\tif (opts.host && opts.path) {\n\t\t\t\t// If both a `host` and `path` are specified then it's most\n\t\t\t\t// likely the result of a `url.parse()` call... we need to\n\t\t\t\t// remove the `path` portion so that `net.connect()` doesn't\n\t\t\t\t// attempt to open that as a unix socket file.\n\t\t\t\tdelete opts.path;\n\t\t\t}\n\n\t\t\tdelete opts.agent;\n\t\t\tdelete opts.hostname;\n\t\t\tdelete opts._defaultAgent;\n\t\t\tdelete opts.defaultPort;\n\t\t\tdelete opts.createConnection;\n\n\t\t\t// Hint to use \"Connection: close\"\n\t\t\t// XXX: non-documented `http` module API :(\n\t\t\treq._last = true;\n\t\t\treq.shouldKeepAlive = false;\n\n\t\t\tlet timedOut = false;\n\t\t\tlet timeoutId: ReturnType<typeof setTimeout> | null = null;\n\t\t\tconst timeoutMs = opts.timeout || this.timeout;\n\n\t\t\tconst onerror = (err: NodeJS.ErrnoException) => {\n\t\t\t\tif (req._hadError) return;\n\t\t\t\treq.emit('error', err);\n\t\t\t\t// For Safety. Some additional errors might fire later on\n\t\t\t\t// and we need to make sure we don't double-fire the error event.\n\t\t\t\treq._hadError = true;\n\t\t\t};\n\n\t\t\tconst ontimeout = () => {\n\t\t\t\ttimeoutId = null;\n\t\t\t\ttimedOut = true;\n\t\t\t\tconst err: NodeJS.ErrnoException = new Error(\n\t\t\t\t\t`A \"socket\" was not created for HTTP request before ${timeoutMs}ms`\n\t\t\t\t);\n\t\t\t\terr.code = 'ETIMEOUT';\n\t\t\t\tonerror(err);\n\t\t\t};\n\n\t\t\tconst callbackError = (err: NodeJS.ErrnoException) => {\n\t\t\t\tif (timedOut) return;\n\t\t\t\tif (timeoutId !== null) {\n\t\t\t\t\tclearTimeout(timeoutId);\n\t\t\t\t\ttimeoutId = null;\n\t\t\t\t}\n\t\t\t\tonerror(err);\n\t\t\t};\n\n\t\t\tconst onsocket = (socket: AgentCallbackReturn) => {\n\t\t\t\tif (timedOut) return;\n\t\t\t\tif (timeoutId != null) {\n\t\t\t\t\tclearTimeout(timeoutId);\n\t\t\t\t\ttimeoutId = null;\n\t\t\t\t}\n\n\t\t\t\tif (isAgent(socket)) {\n\t\t\t\t\t// `socket` is actually an `http.Agent` instance, so\n\t\t\t\t\t// relinquish responsibility for this `req` to the Agent\n\t\t\t\t\t// from here on\n\t\t\t\t\tdebug(\n\t\t\t\t\t\t'Callback returned another Agent instance %o',\n\t\t\t\t\t\tsocket.constructor.name\n\t\t\t\t\t);\n\t\t\t\t\t(socket as createAgent.Agent).addRequest(req, opts);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (socket) {\n\t\t\t\t\tsocket.once('free', () => {\n\t\t\t\t\t\tthis.freeSocket(socket as net.Socket, opts);\n\t\t\t\t\t});\n\t\t\t\t\treq.onSocket(socket as net.Socket);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconst err = new Error(\n\t\t\t\t\t`no Duplex stream was returned to agent-base for \\`${req.method} ${req.path}\\``\n\t\t\t\t);\n\t\t\t\tonerror(err);\n\t\t\t};\n\n\t\t\tif (typeof this.callback !== 'function') {\n\t\t\t\tonerror(new Error('`callback` is not defined'));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (!this.promisifiedCallback) {\n\t\t\t\tif (this.callback.length >= 3) {\n\t\t\t\t\tdebug('Converting legacy callback function to promise');\n\t\t\t\t\tthis.promisifiedCallback = promisify(this.callback);\n\t\t\t\t} else {\n\t\t\t\t\tthis.promisifiedCallback = this.callback;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (typeof timeoutMs === 'number' && timeoutMs > 0) {\n\t\t\t\ttimeoutId = setTimeout(ontimeout, timeoutMs);\n\t\t\t}\n\n\t\t\tif ('port' in opts && typeof opts.port !== 'number') {\n\t\t\t\topts.port = Number(opts.port);\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\tdebug(\n\t\t\t\t\t'Resolving socket for %o request: %o',\n\t\t\t\t\topts.protocol,\n\t\t\t\t\t`${req.method} ${req.path}`\n\t\t\t\t);\n\t\t\t\tPromise.resolve(this.promisifiedCallback(req, opts)).then(\n\t\t\t\t\tonsocket,\n\t\t\t\t\tcallbackError\n\t\t\t\t);\n\t\t\t} catch (err) {\n\t\t\t\tPromise.reject(err).catch(callbackError);\n\t\t\t}\n\t\t}\n\n\t\tfreeSocket(socket: net.Socket, opts: AgentOptions) {\n\t\t\tdebug('Freeing socket %o %o', socket.constructor.name, opts);\n\t\t\tsocket.destroy();\n\t\t}\n\n\t\tdestroy() {\n\t\t\tdebug('Destroying agent %o', this.constructor.name);\n\t\t}\n\t}\n\n\t// So that `instanceof` works correctly\n\tcreateAgent.prototype = createAgent.Agent.prototype;\n}\n\nexport = createAgent;\n"], "mappings": ";;;;;;;;;;;;;;;AAeA,aAAwB,UAAU,IAAkB;AACnD,aAAO,SAAsB,KAAoB,MAAoB;AACpE,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACtC,aAAG,KACF,MACA,KACA,MACA,CAAC,KAA+B,QAA6B;AAC5D,gBAAI,KAAK;AACR,qBAAO,GAAG;mBACJ;AACN,sBAAQ,GAAG;;UAEb,CAAC;QAEH,CAAC;MACF;IACD;AAjBA,YAAA,UAAA;;;;;;;;;;ACXA,QAAA,WAAA;AACA,QAAA,UAAA,gBAAA,aAAA;AACA,QAAA,cAAA,gBAAA,mBAAA;AAEA,QAAM,QAAQ,QAAA,QAAY,YAAY;AAEtC,aAAS,QAAQ,GAAM;AACtB,aAAO,QAAQ,CAAC,KAAK,OAAO,EAAE,eAAe;IAC9C;AAEA,aAAS,mBAAgB;AACxB,YAAM,EAAE,MAAK,IAAK,IAAI,MAAK;AAC3B,UAAI,OAAO,UAAU;AAAU,eAAO;AACtC,aAAO,MAAM,MAAM,IAAI,EAAE,KAAK,OAAK,EAAE,QAAQ,YAAY,MAAM,MAAO,EAAE,QAAQ,aAAa,MAAM,EAAE;IACtG;AAOA,aAAS,YACR,UACA,MAA+B;AAE/B,aAAO,IAAI,YAAY,MAAM,UAAU,IAAI;IAC5C;AAEA,KAAA,SAAUA,cAAW;MA0DpB,MAAa,cAAc,SAAA,aAAY;QAmBtC,YACC,UACA,OAAgC;AAEhC,gBAAK;AAEL,cAAI,OAAO;AACX,cAAI,OAAO,aAAa,YAAY;AACnC,iBAAK,WAAW;qBACN,UAAU;AACpB,mBAAO;;AAIR,eAAK,UAAU;AACf,cAAI,QAAQ,OAAO,KAAK,YAAY,UAAU;AAC7C,iBAAK,UAAU,KAAK;;AAKrB,eAAK,iBAAiB;AACtB,eAAK,aAAa;AAClB,eAAK,kBAAkB;AACvB,eAAK,UAAU,CAAA;AACf,eAAK,cAAc,CAAA;AACnB,eAAK,WAAW,CAAA;AAChB,eAAK,UAAU,CAAA;QAChB;QAEA,IAAI,cAAW;AACd,cAAI,OAAO,KAAK,wBAAwB,UAAU;AACjD,mBAAO,KAAK;;AAEb,iBAAO,iBAAgB,IAAK,MAAM;QACnC;QAEA,IAAI,YAAY,GAAS;AACxB,eAAK,sBAAsB;QAC5B;QAEA,IAAI,WAAQ;AACX,cAAI,OAAO,KAAK,qBAAqB,UAAU;AAC9C,mBAAO,KAAK;;AAEb,iBAAO,iBAAgB,IAAK,WAAW;QACxC;QAEA,IAAI,SAAS,GAAS;AACrB,eAAK,mBAAmB;QACzB;QAaA,SACC,KACA,MACA,IAAsC;AAKtC,gBAAM,IAAI,MACT,yFAAyF;QAE3F;;;;;;;QAQA,WAAW,KAAoB,OAAqB;AACnD,gBAAM,OAAI,OAAA,OAAA,CAAA,GAAwB,KAAK;AAEvC,cAAI,OAAO,KAAK,mBAAmB,WAAW;AAC7C,iBAAK,iBAAiB,iBAAgB;;AAGvC,cAAI,KAAK,QAAQ,MAAM;AACtB,iBAAK,OAAO;;AAGb,cAAI,KAAK,QAAQ,MAAM;AACtB,iBAAK,OAAO,KAAK,iBAAiB,MAAM;;AAGzC,cAAI,KAAK,YAAY,MAAM;AAC1B,iBAAK,WAAW,KAAK,iBAAiB,WAAW;;AAGlD,cAAI,KAAK,QAAQ,KAAK,MAAM;AAK3B,mBAAO,KAAK;;AAGb,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,iBAAO,KAAK;AAIZ,cAAI,QAAQ;AACZ,cAAI,kBAAkB;AAEtB,cAAI,WAAW;AACf,cAAI,YAAkD;AACtD,gBAAM,YAAY,KAAK,WAAW,KAAK;AAEvC,gBAAM,UAAU,CAAC,QAA8B;AAC9C,gBAAI,IAAI;AAAW;AACnB,gBAAI,KAAK,SAAS,GAAG;AAGrB,gBAAI,YAAY;UACjB;AAEA,gBAAM,YAAY,MAAK;AACtB,wBAAY;AACZ,uBAAW;AACX,kBAAM,MAA6B,IAAI,MACtC,sDAAsD,SAAS,IAAI;AAEpE,gBAAI,OAAO;AACX,oBAAQ,GAAG;UACZ;AAEA,gBAAM,gBAAgB,CAAC,QAA8B;AACpD,gBAAI;AAAU;AACd,gBAAI,cAAc,MAAM;AACvB,2BAAa,SAAS;AACtB,0BAAY;;AAEb,oBAAQ,GAAG;UACZ;AAEA,gBAAM,WAAW,CAAC,WAA+B;AAChD,gBAAI;AAAU;AACd,gBAAI,aAAa,MAAM;AACtB,2BAAa,SAAS;AACtB,0BAAY;;AAGb,gBAAI,QAAQ,MAAM,GAAG;AAIpB,oBACC,+CACA,OAAO,YAAY,IAAI;AAEvB,qBAA6B,WAAW,KAAK,IAAI;AAClD;;AAGD,gBAAI,QAAQ;AACX,qBAAO,KAAK,QAAQ,MAAK;AACxB,qBAAK,WAAW,QAAsB,IAAI;cAC3C,CAAC;AACD,kBAAI,SAAS,MAAoB;AACjC;;AAGD,kBAAM,MAAM,IAAI,MACf,qDAAqD,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI;AAEhF,oBAAQ,GAAG;UACZ;AAEA,cAAI,OAAO,KAAK,aAAa,YAAY;AACxC,oBAAQ,IAAI,MAAM,2BAA2B,CAAC;AAC9C;;AAGD,cAAI,CAAC,KAAK,qBAAqB;AAC9B,gBAAI,KAAK,SAAS,UAAU,GAAG;AAC9B,oBAAM,gDAAgD;AACtD,mBAAK,sBAAsB,YAAA,QAAU,KAAK,QAAQ;mBAC5C;AACN,mBAAK,sBAAsB,KAAK;;;AAIlC,cAAI,OAAO,cAAc,YAAY,YAAY,GAAG;AACnD,wBAAY,WAAW,WAAW,SAAS;;AAG5C,cAAI,UAAU,QAAQ,OAAO,KAAK,SAAS,UAAU;AACpD,iBAAK,OAAO,OAAO,KAAK,IAAI;;AAG7B,cAAI;AACH,kBACC,uCACA,KAAK,UACL,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,EAAE;AAE5B,oBAAQ,QAAQ,KAAK,oBAAoB,KAAK,IAAI,CAAC,EAAE,KACpD,UACA,aAAa;mBAEN,KAAK;AACb,oBAAQ,OAAO,GAAG,EAAE,MAAM,aAAa;;QAEzC;QAEA,WAAW,QAAoB,MAAkB;AAChD,gBAAM,wBAAwB,OAAO,YAAY,MAAM,IAAI;AAC3D,iBAAO,QAAO;QACf;QAEA,UAAO;AACN,gBAAM,uBAAuB,KAAK,YAAY,IAAI;QACnD;;AAvPY,MAAAA,aAAA,QAAK;AA2PlB,MAAAA,aAAY,YAAYA,aAAY,MAAM;IAC3C,GAtTU,gBAAA,cAAW,CAAA,EAAA;AAwTrB,WAAA,UAAS;;;", "names": ["createAgent"]}