{"version": 3, "sources": ["../../@typeschema/core/dist/index.mjs", "../../@typeschema/class-validator/dist/index.mjs"], "sourcesContent": ["// src/utils.ts\n// @__NO_SIDE_EFFECTS__\nfunction memoize(fn) {\n  let cache = void 0;\n  const memoizedFn = async () => {\n    if (cache === void 0) {\n      cache = await fn();\n    }\n    return cache;\n  };\n  memoizedFn.clear = () => cache = void 0;\n  return memoizedFn;\n}\n// @__NO_SIDE_EFFECTS__\nfunction memoizeWithKey(fn) {\n  const cache = /* @__PURE__ */ new Map();\n  const memoizedFn = async (key) => {\n    if (!cache.has(key)) {\n      cache.set(key, await fn(key));\n    }\n    return cache.get(key);\n  };\n  memoizedFn.clear = () => cache.clear();\n  return memoizedFn;\n}\n// @__NO_SIDE_EFFECTS__\nfunction unsupportedAdapter(adapterName) {\n  return async () => {\n    throw new Error(`This feature is unsupported for ${adapterName}`);\n  };\n}\n\n// src/serialization.ts\n// @__NO_SIDE_EFFECTS__\nfunction createToJSONSchema(serializationAdapter) {\n  const memoizedSerializationAdapter = memoizeWithKey(\n    (schema) => serializationAdapter(schema)\n  );\n  return async (schema) => {\n    const serializedSchema = await memoizedSerializationAdapter(schema);\n    return serializedSchema;\n  };\n}\n\n// src/validation.ts\n// @__NO_SIDE_EFFECTS__\nfunction createValidate(validationAdapter) {\n  const memoizedValidationAdapter = memoizeWithKey(\n    (schema) => validationAdapter(schema)\n  );\n  return async (schema, data) => {\n    const validateSchema = await memoizedValidationAdapter(schema);\n    return validateSchema(data);\n  };\n}\n// @__NO_SIDE_EFFECTS__\nfunction createAssert(validate) {\n  return async (schema, data) => {\n    const result = await validate(schema, data);\n    if (result.success) {\n      return result.data;\n    }\n    throw new AggregateError(result.issues, \"Assertion failed\");\n  };\n}\n\n// src/wrap.ts\n// @__NO_SIDE_EFFECTS__\nfunction createWrap(assert, validate) {\n  return (schema) => ({\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    _input: void 0,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    _output: void 0,\n    assert: (data) => assert(schema, data),\n    parse: (data) => assert(schema, data),\n    validate: (data) => validate(schema, data)\n  });\n}\nexport {\n  createAssert,\n  createToJSONSchema,\n  createValidate,\n  createWrap,\n  memoize,\n  memoizeWithKey,\n  unsupportedAdapter\n};\n", "// src/index.ts\nimport {\n  createAssert,\n  createValidate,\n  createWrap\n} from \"@typeschema/core\";\n\n// src/validation.ts\nimport { memoize } from \"@typeschema/core\";\nvar importValidationModule = memoize(async () => {\n  try {\n    var dynamicallyImportedModule = await import(\n      /* webpackIgnore: true */\n      \"class-validator\"\n    );\n  } catch (moduleImportError) {\n    throw moduleImportError;\n  }\n  const { validate: validate2 } = dynamicallyImportedModule;\n  return { validate: validate2 };\n});\nfunction getIssues(error, parentPath) {\n  const path = [\n    ...parentPath,\n    Number.isInteger(+error.property) ? +error.property : error.property\n  ];\n  return Object.values(error.constraints ?? {}).map((message) => ({ message, path })).concat(\n    error.children?.flatMap((childError) => getIssues(childError, path)) ?? []\n  );\n}\nvar validationAdapter = async (schema) => {\n  const { validate: validate2 } = await importValidationModule();\n  return async (data) => {\n    const errors = await validate2(Object.assign(new schema(), data));\n    if (errors.length === 0) {\n      return {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        data,\n        success: true\n      };\n    }\n    return {\n      issues: errors.flatMap((error) => getIssues(error, [])),\n      success: false\n    };\n  };\n};\n\n// src/index.ts\nvar validate = createValidate(validationAdapter);\nvar assert = createAssert(validate);\nvar wrap = createWrap(assert, validate);\nexport {\n  assert,\n  validate,\n  validationAdapter,\n  wrap\n};\n"], "mappings": ";;;AAEA,SAAS,QAAQ,IAAI;AACnB,MAAI,QAAQ;AACZ,QAAM,aAAa,YAAY;AAC7B,QAAI,UAAU,QAAQ;AACpB,cAAQ,MAAM,GAAG;AAAA,IACnB;AACA,WAAO;AAAA,EACT;AACA,aAAW,QAAQ,MAAM,QAAQ;AACjC,SAAO;AACT;AAEA,SAAS,eAAe,IAAI;AAC1B,QAAM,QAAwB,oBAAI,IAAI;AACtC,QAAM,aAAa,OAAO,QAAQ;AAChC,QAAI,CAAC,MAAM,IAAI,GAAG,GAAG;AACnB,YAAM,IAAI,KAAK,MAAM,GAAG,GAAG,CAAC;AAAA,IAC9B;AACA,WAAO,MAAM,IAAI,GAAG;AAAA,EACtB;AACA,aAAW,QAAQ,MAAM,MAAM,MAAM;AACrC,SAAO;AACT;AAsBA,SAAS,eAAeA,oBAAmB;AACzC,QAAM,4BAA4B;AAAA,IAChC,CAAC,WAAWA,mBAAkB,MAAM;AAAA,EACtC;AACA,SAAO,OAAO,QAAQ,SAAS;AAC7B,UAAM,iBAAiB,MAAM,0BAA0B,MAAM;AAC7D,WAAO,eAAe,IAAI;AAAA,EAC5B;AACF;AAEA,SAAS,aAAaC,WAAU;AAC9B,SAAO,OAAO,QAAQ,SAAS;AAC7B,UAAM,SAAS,MAAMA,UAAS,QAAQ,IAAI;AAC1C,QAAI,OAAO,SAAS;AAClB,aAAO,OAAO;AAAA,IAChB;AACA,UAAM,IAAI,eAAe,OAAO,QAAQ,kBAAkB;AAAA,EAC5D;AACF;AAIA,SAAS,WAAWC,SAAQD,WAAU;AACpC,SAAO,CAAC,YAAY;AAAA;AAAA,IAElB,QAAQ;AAAA;AAAA,IAER,SAAS;AAAA,IACT,QAAQ,CAAC,SAASC,QAAO,QAAQ,IAAI;AAAA,IACrC,OAAO,CAAC,SAASA,QAAO,QAAQ,IAAI;AAAA,IACpC,UAAU,CAAC,SAASD,UAAS,QAAQ,IAAI;AAAA,EAC3C;AACF;;;ACrEA,IAAI,yBAAyB,QAAQ,YAAY;AAC/C,MAAI;AACF,QAAI,4BAA4B,MAAM;AAAA;AAAA,MAEpC;AAAA,IACF;AAAA,EACF,SAAS,mBAAmB;AAC1B,UAAM;AAAA,EACR;AACA,QAAM,EAAE,UAAU,UAAU,IAAI;AAChC,SAAO,EAAE,UAAU,UAAU;AAC/B,CAAC;AACD,SAAS,UAAU,OAAO,YAAY;AArBtC;AAsBE,QAAM,OAAO;AAAA,IACX,GAAG;AAAA,IACH,OAAO,UAAU,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,WAAW,MAAM;AAAA,EAC9D;AACA,SAAO,OAAO,OAAO,MAAM,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,SAAS,KAAK,EAAE,EAAE;AAAA,MAClF,WAAM,aAAN,mBAAgB,QAAQ,CAAC,eAAe,UAAU,YAAY,IAAI,OAAM,CAAC;AAAA,EAC3E;AACF;AACA,IAAI,oBAAoB,OAAO,WAAW;AACxC,QAAM,EAAE,UAAU,UAAU,IAAI,MAAM,uBAAuB;AAC7D,SAAO,OAAO,SAAS;AACrB,UAAM,SAAS,MAAM,UAAU,OAAO,OAAO,IAAI,OAAO,GAAG,IAAI,CAAC;AAChE,QAAI,OAAO,WAAW,GAAG;AACvB,aAAO;AAAA;AAAA,QAEL;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AACA,WAAO;AAAA,MACL,QAAQ,OAAO,QAAQ,CAAC,UAAU,UAAU,OAAO,CAAC,CAAC,CAAC;AAAA,MACtD,SAAS;AAAA,IACX;AAAA,EACF;AACF;AAGA,IAAI,WAAW,eAAe,iBAAiB;AAC/C,IAAI,SAAS,aAAa,QAAQ;AAClC,IAAI,OAAO,WAAW,QAAQ,QAAQ;", "names": ["validationAdapter", "validate", "assert"]}