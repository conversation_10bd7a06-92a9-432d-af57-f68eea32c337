{"version": 3, "sources": ["../../d3-path/src/path.js"], "sourcesContent": ["const pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction append(strings) {\n  this._ += strings[0];\n  for (let i = 1, n = strings.length; i < n; ++i) {\n    this._ += arguments[i] + strings[i];\n  }\n}\n\nfunction appendRound(digits) {\n  let d = Math.floor(digits);\n  if (!(d >= 0)) throw new Error(`invalid digits: ${digits}`);\n  if (d > 15) return append;\n  const k = 10 ** d;\n  return function(strings) {\n    this._ += strings[0];\n    for (let i = 1, n = strings.length; i < n; ++i) {\n      this._ += Math.round(arguments[i] * k) / k + strings[i];\n    }\n  };\n}\n\nexport class Path {\n  constructor(digits) {\n    this._x0 = this._y0 = // start of current subpath\n    this._x1 = this._y1 = null; // end of current subpath\n    this._ = \"\";\n    this._append = digits == null ? append : appendRound(digits);\n  }\n  moveTo(x, y) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}`;\n  }\n  closePath() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._append`Z`;\n    }\n  }\n  lineTo(x, y) {\n    this._append`L${this._x1 = +x},${this._y1 = +y}`;\n  }\n  quadraticCurveTo(x1, y1, x, y) {\n    this._append`Q${+x1},${+y1},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  bezierCurveTo(x1, y1, x2, y2, x, y) {\n    this._append`C${+x1},${+y1},${+x2},${+y2},${this._x1 = +x},${this._y1 = +y}`;\n  }\n  arcTo(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._append`M${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._append`L${this._x1 = x1},${this._y1 = y1}`;\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      let x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._append`L${x1 + t01 * x01},${y1 + t01 * y01}`;\n      }\n\n      this._append`A${r},${r},0,0,${+(y01 * x20 > x01 * y20)},${this._x1 = x1 + t21 * x21},${this._y1 = y1 + t21 * y21}`;\n    }\n  }\n  arc(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(`negative radius: ${r}`);\n\n    let dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._append`M${x0},${y0}`;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._append`L${x0},${y0}`;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._append`A${r},${r},0,1,${cw},${x - dx},${y - dy}A${r},${r},0,1,${cw},${this._x1 = x0},${this._y1 = y0}`;\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._append`A${r},${r},0,${+(da >= pi)},${cw},${this._x1 = x + r * Math.cos(a1)},${this._y1 = y + r * Math.sin(a1)}`;\n    }\n  }\n  rect(x, y, w, h) {\n    this._append`M${this._x0 = this._x1 = +x},${this._y0 = this._y1 = +y}h${w = +w}v${+h}h${-w}Z`;\n  }\n  toString() {\n    return this._;\n  }\n}\n\nexport function path() {\n  return new Path;\n}\n\n// Allow instanceof d3.path\npath.prototype = Path.prototype;\n\nexport function pathRound(digits = 3) {\n  return new Path(+digits);\n}\n"], "mappings": ";AAAA,IAAM,KAAK,KAAK;AAAhB,IACI,MAAM,IAAI;AADd,IAEI,UAAU;AAFd,IAGI,aAAa,MAAM;AAEvB,SAAS,OAAO,SAAS;AACvB,OAAK,KAAK,QAAQ,CAAC;AACnB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,SAAK,KAAK,UAAU,CAAC,IAAI,QAAQ,CAAC;AAAA,EACpC;AACF;AAEA,SAAS,YAAY,QAAQ;AAC3B,MAAI,IAAI,KAAK,MAAM,MAAM;AACzB,MAAI,EAAE,KAAK,GAAI,OAAM,IAAI,MAAM,mBAAmB,MAAM,EAAE;AAC1D,MAAI,IAAI,GAAI,QAAO;AACnB,QAAM,IAAI,MAAM;AAChB,SAAO,SAAS,SAAS;AACvB,SAAK,KAAK,QAAQ,CAAC;AACnB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,WAAK,KAAK,KAAK,MAAM,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC;AAAA,IACxD;AAAA,EACF;AACF;AAEO,IAAM,OAAN,MAAW;AAAA,EAChB,YAAY,QAAQ;AAClB,SAAK,MAAM,KAAK;AAAA,IAChB,KAAK,MAAM,KAAK,MAAM;AACtB,SAAK,IAAI;AACT,SAAK,UAAU,UAAU,OAAO,SAAS,YAAY,MAAM;AAAA,EAC7D;AAAA,EACA,OAAO,GAAG,GAAG;AACX,SAAK,WAAW,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC;AAAA,EACtE;AAAA,EACA,YAAY;AACV,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK;AACrC,WAAK;AAAA,IACP;AAAA,EACF;AAAA,EACA,OAAO,GAAG,GAAG;AACX,SAAK,WAAW,KAAK,MAAM,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EAChD;AAAA,EACA,iBAAiB,IAAI,IAAI,GAAG,GAAG;AAC7B,SAAK,WAAW,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,KAAK,MAAM,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EAC9D;AAAA,EACA,cAAc,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AAClC,SAAK,WAAW,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,KAAK,MAAM,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,EAC5E;AAAA,EACA,MAAM,IAAI,IAAI,IAAI,IAAI,GAAG;AACvB,SAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC;AAG7C,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,oBAAoB,CAAC,EAAE;AAElD,QAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACV,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM;AAG9B,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,WAAW,KAAK,MAAM,EAAE,IAAI,KAAK,MAAM,EAAE;AAAA,IAChD,WAGS,EAAE,QAAQ,SAAS;AAAA,aAKnB,EAAE,KAAK,IAAI,MAAM,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,GAAG;AAC3D,WAAK,WAAW,KAAK,MAAM,EAAE,IAAI,KAAK,MAAM,EAAE;AAAA,IAChD,OAGK;AACH,UAAI,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM,KAC1B,QAAQ,MAAM,MAAM,MAAM,KAC1B,MAAM,KAAK,KAAK,KAAK,GACrB,MAAM,KAAK,KAAK,KAAK,GACrB,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,QAAQ,QAAQ,UAAU,IAAI,MAAM,IAAI,KAAK,CAAC,GAChF,MAAM,IAAI,KACV,MAAM,IAAI;AAGd,UAAI,KAAK,IAAI,MAAM,CAAC,IAAI,SAAS;AAC/B,aAAK,WAAW,KAAK,MAAM,GAAG,IAAI,KAAK,MAAM,GAAG;AAAA,MAClD;AAEA,WAAK,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,MAAM,MAAM,IAAI,IAAI,KAAK,MAAM,KAAK,MAAM,GAAG,IAAI,KAAK,MAAM,KAAK,MAAM,GAAG;AAAA,IAClH;AAAA,EACF;AAAA,EACA,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,KAAK;AACxB,QAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AAGhC,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,oBAAoB,CAAC,EAAE;AAElD,QAAI,KAAK,IAAI,KAAK,IAAI,EAAE,GACpB,KAAK,IAAI,KAAK,IAAI,EAAE,GACpB,KAAK,IAAI,IACT,KAAK,IAAI,IACT,KAAK,IAAI,KACT,KAAK,MAAM,KAAK,KAAK,KAAK;AAG9B,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,WAAW,EAAE,IAAI,EAAE;AAAA,IAC1B,WAGS,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,WAAW,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,SAAS;AAC/E,WAAK,WAAW,EAAE,IAAI,EAAE;AAAA,IAC1B;AAGA,QAAI,CAAC,EAAG;AAGR,QAAI,KAAK,EAAG,MAAK,KAAK,MAAM;AAG5B,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,KAAK,MAAM,EAAE,IAAI,KAAK,MAAM,EAAE;AAAA,IAC5G,WAGS,KAAK,SAAS;AACrB,WAAK,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;AAAA,IACrH;AAAA,EACF;AAAA,EACA,KAAK,GAAG,GAAG,GAAG,GAAG;AACf,SAAK,WAAW,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC,IAAI,KAAK,MAAM,KAAK,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AAAA,EAC5F;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AACF;AAEO,SAAS,OAAO;AACrB,SAAO,IAAI;AACb;AAGA,KAAK,YAAY,KAAK;AAEf,SAAS,UAAU,SAAS,GAAG;AACpC,SAAO,IAAI,KAAK,CAAC,MAAM;AACzB;", "names": []}