{"version": 3, "sources": ["../../d3-sankey/node_modules/d3-array/dist/d3-array.js", "../../d3-sankey/node_modules/d3-path/dist/d3-path.js", "../../d3-sankey/node_modules/d3-shape/dist/d3-shape.js", "../../d3-sankey/dist/d3-sankey.js"], "sourcesContent": ["// https://d3js.org/d3-array/ v2.12.1 Copyright 2021 <PERSON>\n(function (global, factory) {\ntypeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\ntypeof define === 'function' && define.amd ? define(['exports'], factory) :\n(global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.d3 = global.d3 || {}));\n}(this, (function (exports) { 'use strict';\n\nfunction ascending(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n\nfunction bisector(f) {\n  let delta = f;\n  let compare = f;\n\n  if (f.length === 1) {\n    delta = (d, x) => f(d) - x;\n    compare = ascendingComparator(f);\n  }\n\n  function left(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    while (lo < hi) {\n      const mid = (lo + hi) >>> 1;\n      if (compare(a[mid], x) < 0) lo = mid + 1;\n      else hi = mid;\n    }\n    return lo;\n  }\n\n  function right(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    while (lo < hi) {\n      const mid = (lo + hi) >>> 1;\n      if (compare(a[mid], x) > 0) hi = mid;\n      else lo = mid + 1;\n    }\n    return lo;\n  }\n\n  function center(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n\nfunction ascendingComparator(f) {\n  return (d, x) => ascending(f(d), x);\n}\n\nfunction number(x) {\n  return x === null ? NaN : +x;\n}\n\nfunction* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}\n\nconst ascendingBisect = bisector(ascending);\nconst bisectRight = ascendingBisect.right;\nconst bisectLeft = ascendingBisect.left;\nconst bisectCenter = bisector(number).center;\n\nfunction count(values, valueof) {\n  let count = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  }\n  return count;\n}\n\nfunction length$1(array) {\n  return array.length | 0;\n}\n\nfunction empty(length) {\n  return !(length > 0);\n}\n\nfunction arrayify(values) {\n  return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\n\nfunction reducer(reduce) {\n  return values => reduce(...values);\n}\n\nfunction cross(...values) {\n  const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n  values = values.map(arrayify);\n  const lengths = values.map(length$1);\n  const j = values.length - 1;\n  const index = new Array(j + 1).fill(0);\n  const product = [];\n  if (j < 0 || lengths.some(empty)) return product;\n  while (true) {\n    product.push(index.map((j, i) => values[i][j]));\n    let i = j;\n    while (++index[i] === lengths[i]) {\n      if (i === 0) return reduce ? product.map(reduce) : product;\n      index[i--] = 0;\n    }\n  }\n}\n\nfunction cumsum(values, valueof) {\n  var sum = 0, index = 0;\n  return Float64Array.from(values, valueof === undefined\n    ? v => (sum += +v || 0)\n    : v => (sum += +valueof(v, index++, values) || 0));\n}\n\nfunction descending(a, b) {\n  return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}\n\nfunction variance(values, valueof) {\n  let count = 0;\n  let delta;\n  let mean = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  }\n  if (count > 1) return sum / (count - 1);\n}\n\nfunction deviation(values, valueof) {\n  const v = variance(values, valueof);\n  return v ? Math.sqrt(v) : v;\n}\n\nfunction extent(values, valueof) {\n  let min;\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  }\n  return [min, max];\n}\n\n// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nclass Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n, x, y, lo, hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && ((lo < 0 && p[n - 1] < 0) || (lo > 0 && p[n - 1] > 0))) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\n\nfunction fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\n\nfunction fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined\n      ? v => adder.add(+v || 0)\n      : v => adder.add(+valueof(v, ++index, values) || 0)\n  );\n}\n\nclass InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\n\nclass InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\n\nfunction intern_get({_intern, _key}, value) {\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\n\nfunction intern_set({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\n\nfunction intern_delete({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(value);\n    _intern.delete(key);\n  }\n  return value;\n}\n\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n\nfunction identity(x) {\n  return x;\n}\n\nfunction group(values, ...keys) {\n  return nest(values, identity, identity, keys);\n}\n\nfunction groups(values, ...keys) {\n  return nest(values, Array.from, identity, keys);\n}\n\nfunction rollup(values, reduce, ...keys) {\n  return nest(values, identity, reduce, keys);\n}\n\nfunction rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\n\nfunction index(values, ...keys) {\n  return nest(values, identity, unique, keys);\n}\n\nfunction indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\n\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\n\nfunction nest(values, map, reduce, keys) {\n  return (function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);\n      else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  })(values, 0);\n}\n\nfunction permute(source, keys) {\n  return Array.from(keys, key => source[key]);\n}\n\nfunction sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f = ascending] = F;\n  if (f.length === 1 || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascending(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascending(f[i], f[j]));\n    }\n    return permute(values, index);\n  }\n  return values.sort(f);\n}\n\nfunction groupSort(values, reduce, key) {\n  return (reduce.length === 1\n    ? sort(rollup(values, reduce, key), (([ak, av], [bk, bv]) => ascending(av, bv) || ascending(ak, bk)))\n    : sort(group(values, key), (([ak, av], [bk, bv]) => reduce(av, bv) || ascending(ak, bk))))\n    .map(([key]) => key);\n}\n\nvar array = Array.prototype;\n\nvar slice = array.slice;\n\nfunction constant(x) {\n  return function() {\n    return x;\n  };\n}\n\nvar e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nfunction ticks(start, stop, count) {\n  var reverse,\n      i = -1,\n      n,\n      ticks,\n      step;\n\n  stop = +stop, start = +start, count = +count;\n  if (start === stop && count > 0) return [start];\n  if (reverse = stop < start) n = start, start = stop, stop = n;\n  if ((step = tickIncrement(start, stop, count)) === 0 || !isFinite(step)) return [];\n\n  if (step > 0) {\n    let r0 = Math.round(start / step), r1 = Math.round(stop / step);\n    if (r0 * step < start) ++r0;\n    if (r1 * step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n    while (++i < n) ticks[i] = (r0 + i) * step;\n  } else {\n    step = -step;\n    let r0 = Math.round(start * step), r1 = Math.round(stop * step);\n    if (r0 / step < start) ++r0;\n    if (r1 / step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n    while (++i < n) ticks[i] = (r0 + i) / step;\n  }\n\n  if (reverse) ticks.reverse();\n\n  return ticks;\n}\n\nfunction tickIncrement(start, stop, count) {\n  var step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log(step) / Math.LN10),\n      error = step / Math.pow(10, power);\n  return power >= 0\n      ? (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1) * Math.pow(10, power)\n      : -Math.pow(10, -power) / (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1);\n}\n\nfunction tickStep(start, stop, count) {\n  var step0 = Math.abs(stop - start) / Math.max(0, count),\n      step1 = Math.pow(10, Math.floor(Math.log(step0) / Math.LN10)),\n      error = step0 / step1;\n  if (error >= e10) step1 *= 10;\n  else if (error >= e5) step1 *= 5;\n  else if (error >= e2) step1 *= 2;\n  return stop < start ? -step1 : step1;\n}\n\nfunction nice(start, stop, count) {\n  let prestep;\n  while (true) {\n    const step = tickIncrement(start, stop, count);\n    if (step === prestep || step === 0 || !isFinite(step)) {\n      return [start, stop];\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    }\n    prestep = step;\n  }\n}\n\nfunction sturges(values) {\n  return Math.ceil(Math.log(count(values)) / Math.LN2) + 1;\n}\n\nfunction bin() {\n  var value = identity,\n      domain = extent,\n      threshold = sturges;\n\n  function histogram(data) {\n    if (!Array.isArray(data)) data = Array.from(data);\n\n    var i,\n        n = data.length,\n        x,\n        values = new Array(n);\n\n    for (i = 0; i < n; ++i) {\n      values[i] = value(data[i], i, data);\n    }\n\n    var xz = domain(values),\n        x0 = xz[0],\n        x1 = xz[1],\n        tz = threshold(values, x0, x1);\n\n    // Convert number of thresholds into uniform thresholds, and nice the\n    // default domain accordingly.\n    if (!Array.isArray(tz)) {\n      const max = x1, tn = +tz;\n      if (domain === extent) [x0, x1] = nice(x0, x1, tn);\n      tz = ticks(x0, x1, tn);\n\n      // If the last threshold is coincident with the domain’s upper bound, the\n      // last bin will be zero-width. If the default domain is used, and this\n      // last threshold is coincident with the maximum input value, we can\n      // extend the niced upper bound by one tick to ensure uniform bin widths;\n      // otherwise, we simply remove the last threshold. Note that we don’t\n      // coerce values or the domain to numbers, and thus must be careful to\n      // compare order (>=) rather than strict equality (===)!\n      if (tz[tz.length - 1] >= x1) {\n        if (max >= x1 && domain === extent) {\n          const step = tickIncrement(x0, x1, tn);\n          if (isFinite(step)) {\n            if (step > 0) {\n              x1 = (Math.floor(x1 / step) + 1) * step;\n            } else if (step < 0) {\n              x1 = (Math.ceil(x1 * -step) + 1) / -step;\n            }\n          }\n        } else {\n          tz.pop();\n        }\n      }\n    }\n\n    // Remove any thresholds outside the domain.\n    var m = tz.length;\n    while (tz[0] <= x0) tz.shift(), --m;\n    while (tz[m - 1] > x1) tz.pop(), --m;\n\n    var bins = new Array(m + 1),\n        bin;\n\n    // Initialize bins.\n    for (i = 0; i <= m; ++i) {\n      bin = bins[i] = [];\n      bin.x0 = i > 0 ? tz[i - 1] : x0;\n      bin.x1 = i < m ? tz[i] : x1;\n    }\n\n    // Assign data to bins by value, ignoring any outside the domain.\n    for (i = 0; i < n; ++i) {\n      x = values[i];\n      if (x0 <= x && x <= x1) {\n        bins[bisectRight(tz, x, 0, m)].push(data[i]);\n      }\n    }\n\n    return bins;\n  }\n\n  histogram.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(_), histogram) : value;\n  };\n\n  histogram.domain = function(_) {\n    return arguments.length ? (domain = typeof _ === \"function\" ? _ : constant([_[0], _[1]]), histogram) : domain;\n  };\n\n  histogram.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? constant(slice.call(_)) : constant(_), histogram) : threshold;\n  };\n\n  return histogram;\n}\n\nfunction max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n\nfunction min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nfunction quickselect(array, k, left = 0, right = array.length - 1, compare = ascending) {\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n\n    const t = array[k];\n    let i = left;\n    let j = right;\n\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n\n    if (compare(array[left], t) === 0) swap(array, left, j);\n    else ++j, swap(array, j, right);\n\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n  return array;\n}\n\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}\n\nfunction quantile(values, p, valueof) {\n  values = Float64Array.from(numbers(values, valueof));\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return min(values);\n  if (p >= 1) return max(values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = max(quickselect(values, i0).subarray(0, i0 + 1)),\n      value1 = min(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nfunction quantileSorted(values, p, valueof = number) {\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = +valueof(values[i0], i0, values),\n      value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nfunction freedmanDiaconis(values, min, max) {\n  return Math.ceil((max - min) / (2 * (quantile(values, 0.75) - quantile(values, 0.25)) * Math.pow(count(values), -1 / 3)));\n}\n\nfunction scott(values, min, max) {\n  return Math.ceil((max - min) / (3.5 * deviation(values) * Math.pow(count(values), -1 / 3)));\n}\n\nfunction maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n  return maxIndex;\n}\n\nfunction mean(values, valueof) {\n  let count = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  }\n  if (count) return sum / count;\n}\n\nfunction median(values, valueof) {\n  return quantile(values, 0.5, valueof);\n}\n\nfunction* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\n\nfunction merge(arrays) {\n  return Array.from(flatten(arrays));\n}\n\nfunction minIndex(values, valueof) {\n  let min;\n  let minIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  }\n  return minIndex;\n}\n\nfunction pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\n\nfunction pair(a, b) {\n  return [a, b];\n}\n\nfunction range(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n\n  var i = -1,\n      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n      range = new Array(n);\n\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n\n  return range;\n}\n\nfunction least(values, compare = ascending) {\n  let min;\n  let defined = false;\n  if (compare.length === 1) {\n    let minValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, minValue) < 0\n          : ascending(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, min) < 0\n          : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n  return min;\n}\n\nfunction leastIndex(values, compare = ascending) {\n  if (compare.length === 1) return minIndex(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (min < 0\n        ? compare(value, value) === 0\n        : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n  return min;\n}\n\nfunction greatest(values, compare = ascending) {\n  let max;\n  let defined = false;\n  if (compare.length === 1) {\n    let maxValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, maxValue) > 0\n          : ascending(value, value) === 0) {\n        max = element;\n        maxValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, max) > 0\n          : compare(value, value) === 0) {\n        max = value;\n        defined = true;\n      }\n    }\n  }\n  return max;\n}\n\nfunction greatestIndex(values, compare = ascending) {\n  if (compare.length === 1) return maxIndex(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (max < 0\n        ? compare(value, value) === 0\n        : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n  return max;\n}\n\nfunction scan(values, compare) {\n  const index = leastIndex(values, compare);\n  return index < 0 ? undefined : index;\n}\n\nvar shuffle = shuffler(Math.random);\n\nfunction shuffler(random) {\n  return function shuffle(array, i0 = 0, i1 = array.length) {\n    let m = i1 - (i0 = +i0);\n    while (m) {\n      const i = random() * m-- | 0, t = array[m + i0];\n      array[m + i0] = array[i + i0];\n      array[i + i0] = t;\n    }\n    return array;\n  };\n}\n\nfunction sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n\nfunction transpose(matrix) {\n  if (!(n = matrix.length)) return [];\n  for (var i = -1, m = min(matrix, length), transpose = new Array(m); ++i < m;) {\n    for (var j = -1, n, row = transpose[i] = new Array(n); ++j < n;) {\n      row[j] = matrix[j][i];\n    }\n  }\n  return transpose;\n}\n\nfunction length(d) {\n  return d.length;\n}\n\nfunction zip() {\n  return transpose(arguments);\n}\n\nfunction every(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (!test(value, ++index, values)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction some(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction filter(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  const array = [];\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      array.push(value);\n    }\n  }\n  return array;\n}\n\nfunction map(values, mapper) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n  return Array.from(values, (value, index) => mapper(value, index, values));\n}\n\nfunction reduce(values, reducer, value) {\n  if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n  const iterator = values[Symbol.iterator]();\n  let done, next, index = -1;\n  if (arguments.length < 3) {\n    ({done, value} = iterator.next());\n    if (done) return;\n    ++index;\n  }\n  while (({done, value: next} = iterator.next()), !done) {\n    value = reducer(value, next, ++index, values);\n  }\n  return value;\n}\n\nfunction reverse(values) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  return Array.from(values).reverse();\n}\n\nfunction difference(values, ...others) {\n  values = new Set(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}\n\nfunction disjoint(values, other) {\n  const iterator = other[Symbol.iterator](), set = new Set();\n  for (const v of values) {\n    if (set.has(v)) return false;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) break;\n      if (Object.is(v, value)) return false;\n      set.add(value);\n    }\n  }\n  return true;\n}\n\nfunction set(values) {\n  return values instanceof Set ? values : new Set(values);\n}\n\nfunction intersection(values, ...others) {\n  values = new Set(values);\n  others = others.map(set);\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n  return values;\n}\n\nfunction superset(values, other) {\n  const iterator = values[Symbol.iterator](), set = new Set();\n  for (const o of other) {\n    if (set.has(o)) continue;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) return false;\n      set.add(value);\n      if (Object.is(o, value)) break;\n    }\n  }\n  return true;\n}\n\nfunction subset(values, other) {\n  return superset(other, values);\n}\n\nfunction union(...others) {\n  const set = new Set();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}\n\nexports.Adder = Adder;\nexports.InternMap = InternMap;\nexports.InternSet = InternSet;\nexports.ascending = ascending;\nexports.bin = bin;\nexports.bisect = bisectRight;\nexports.bisectCenter = bisectCenter;\nexports.bisectLeft = bisectLeft;\nexports.bisectRight = bisectRight;\nexports.bisector = bisector;\nexports.count = count;\nexports.cross = cross;\nexports.cumsum = cumsum;\nexports.descending = descending;\nexports.deviation = deviation;\nexports.difference = difference;\nexports.disjoint = disjoint;\nexports.every = every;\nexports.extent = extent;\nexports.fcumsum = fcumsum;\nexports.filter = filter;\nexports.fsum = fsum;\nexports.greatest = greatest;\nexports.greatestIndex = greatestIndex;\nexports.group = group;\nexports.groupSort = groupSort;\nexports.groups = groups;\nexports.histogram = bin;\nexports.index = index;\nexports.indexes = indexes;\nexports.intersection = intersection;\nexports.least = least;\nexports.leastIndex = leastIndex;\nexports.map = map;\nexports.max = max;\nexports.maxIndex = maxIndex;\nexports.mean = mean;\nexports.median = median;\nexports.merge = merge;\nexports.min = min;\nexports.minIndex = minIndex;\nexports.nice = nice;\nexports.pairs = pairs;\nexports.permute = permute;\nexports.quantile = quantile;\nexports.quantileSorted = quantileSorted;\nexports.quickselect = quickselect;\nexports.range = range;\nexports.reduce = reduce;\nexports.reverse = reverse;\nexports.rollup = rollup;\nexports.rollups = rollups;\nexports.scan = scan;\nexports.shuffle = shuffle;\nexports.shuffler = shuffler;\nexports.some = some;\nexports.sort = sort;\nexports.subset = subset;\nexports.sum = sum;\nexports.superset = superset;\nexports.thresholdFreedmanDiaconis = freedmanDiaconis;\nexports.thresholdScott = scott;\nexports.thresholdSturges = sturges;\nexports.tickIncrement = tickIncrement;\nexports.tickStep = tickStep;\nexports.ticks = ticks;\nexports.transpose = transpose;\nexports.union = union;\nexports.variance = variance;\nexports.zip = zip;\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\n})));\n", "// https://d3js.org/d3-path/ v1.0.9 Copyright 2019 <PERSON>\n(function (global, factory) {\ntypeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\ntypeof define === 'function' && define.amd ? define(['exports'], factory) :\n(global = global || self, factory(global.d3 = global.d3 || {}));\n}(this, function (exports) { 'use strict';\n\nvar pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction Path() {\n  this._x0 = this._y0 = // start of current subpath\n  this._x1 = this._y1 = null; // end of current subpath\n  this._ = \"\";\n}\n\nfunction path() {\n  return new Path;\n}\n\nPath.prototype = path.prototype = {\n  constructor: Path,\n  moveTo: function(x, y) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y);\n  },\n  closePath: function() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  },\n  lineTo: function(x, y) {\n    this._ += \"L\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  quadraticCurveTo: function(x1, y1, x, y) {\n    this._ += \"Q\" + (+x1) + \",\" + (+y1) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) {\n    this._ += \"C\" + (+x1) + \",\" + (+y1) + \",\" + (+x2) + \",\" + (+y2) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  arcTo: function(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n    var x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._ += \"M\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._ += \"L\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      var x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._ += \"L\" + (x1 + t01 * x01) + \",\" + (y1 + t01 * y01);\n      }\n\n      this._ += \"A\" + r + \",\" + r + \",0,0,\" + (+(y01 * x20 > x01 * y20)) + \",\" + (this._x1 = x1 + t21 * x21) + \",\" + (this._y1 = y1 + t21 * y21);\n    }\n  },\n  arc: function(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n    var dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._ += \"M\" + x0 + \",\" + y0;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._ += \"L\" + x0 + \",\" + y0;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (x - dx) + \",\" + (y - dy) + \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (this._x1 = x0) + \",\" + (this._y1 = y0);\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,\" + (+(da >= pi)) + \",\" + cw + \",\" + (this._x1 = x + r * Math.cos(a1)) + \",\" + (this._y1 = y + r * Math.sin(a1));\n    }\n  },\n  rect: function(x, y, w, h) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y) + \"h\" + (+w) + \"v\" + (+h) + \"h\" + (-w) + \"Z\";\n  },\n  toString: function() {\n    return this._;\n  }\n};\n\nexports.path = path;\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\n}));\n", "// https://d3js.org/d3-shape/ v1.3.7 Copyright 2019 <PERSON>\n(function (global, factory) {\ntypeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('d3-path')) :\ntypeof define === 'function' && define.amd ? define(['exports', 'd3-path'], factory) :\n(global = global || self, factory(global.d3 = global.d3 || {}, global.d3));\n}(this, function (exports, d3Path) { 'use strict';\n\nfunction constant(x) {\n  return function constant() {\n    return x;\n  };\n}\n\nvar abs = Math.abs;\nvar atan2 = Math.atan2;\nvar cos = Math.cos;\nvar max = Math.max;\nvar min = Math.min;\nvar sin = Math.sin;\nvar sqrt = Math.sqrt;\n\nvar epsilon = 1e-12;\nvar pi = Math.PI;\nvar halfPi = pi / 2;\nvar tau = 2 * pi;\n\nfunction acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nfunction asin(x) {\n  return x >= 1 ? halfPi : x <= -1 ? -halfPi : Math.asin(x);\n}\n\nfunction arcInnerRadius(d) {\n  return d.innerRadius;\n}\n\nfunction arcOuterRadius(d) {\n  return d.outerRadius;\n}\n\nfunction arcStartAngle(d) {\n  return d.startAngle;\n}\n\nfunction arcEndAngle(d) {\n  return d.endAngle;\n}\n\nfunction arcPadAngle(d) {\n  return d && d.padAngle; // Note: optional!\n}\n\nfunction intersect(x0, y0, x1, y1, x2, y2, x3, y3) {\n  var x10 = x1 - x0, y10 = y1 - y0,\n      x32 = x3 - x2, y32 = y3 - y2,\n      t = y32 * x10 - x32 * y10;\n  if (t * t < epsilon) return;\n  t = (x32 * (y0 - y2) - y32 * (x0 - x2)) / t;\n  return [x0 + t * x10, y0 + t * y10];\n}\n\n// Compute perpendicular offset line of length rc.\n// http://mathworld.wolfram.com/Circle-LineIntersection.html\nfunction cornerTangents(x0, y0, x1, y1, r1, rc, cw) {\n  var x01 = x0 - x1,\n      y01 = y0 - y1,\n      lo = (cw ? rc : -rc) / sqrt(x01 * x01 + y01 * y01),\n      ox = lo * y01,\n      oy = -lo * x01,\n      x11 = x0 + ox,\n      y11 = y0 + oy,\n      x10 = x1 + ox,\n      y10 = y1 + oy,\n      x00 = (x11 + x10) / 2,\n      y00 = (y11 + y10) / 2,\n      dx = x10 - x11,\n      dy = y10 - y11,\n      d2 = dx * dx + dy * dy,\n      r = r1 - rc,\n      D = x11 * y10 - x10 * y11,\n      d = (dy < 0 ? -1 : 1) * sqrt(max(0, r * r * d2 - D * D)),\n      cx0 = (D * dy - dx * d) / d2,\n      cy0 = (-D * dx - dy * d) / d2,\n      cx1 = (D * dy + dx * d) / d2,\n      cy1 = (-D * dx + dy * d) / d2,\n      dx0 = cx0 - x00,\n      dy0 = cy0 - y00,\n      dx1 = cx1 - x00,\n      dy1 = cy1 - y00;\n\n  // Pick the closer of the two intersection points.\n  // TODO Is there a faster way to determine which intersection to use?\n  if (dx0 * dx0 + dy0 * dy0 > dx1 * dx1 + dy1 * dy1) cx0 = cx1, cy0 = cy1;\n\n  return {\n    cx: cx0,\n    cy: cy0,\n    x01: -ox,\n    y01: -oy,\n    x11: cx0 * (r1 / r - 1),\n    y11: cy0 * (r1 / r - 1)\n  };\n}\n\nfunction arc() {\n  var innerRadius = arcInnerRadius,\n      outerRadius = arcOuterRadius,\n      cornerRadius = constant(0),\n      padRadius = null,\n      startAngle = arcStartAngle,\n      endAngle = arcEndAngle,\n      padAngle = arcPadAngle,\n      context = null;\n\n  function arc() {\n    var buffer,\n        r,\n        r0 = +innerRadius.apply(this, arguments),\n        r1 = +outerRadius.apply(this, arguments),\n        a0 = startAngle.apply(this, arguments) - halfPi,\n        a1 = endAngle.apply(this, arguments) - halfPi,\n        da = abs(a1 - a0),\n        cw = a1 > a0;\n\n    if (!context) context = buffer = d3Path.path();\n\n    // Ensure that the outer radius is always larger than the inner radius.\n    if (r1 < r0) r = r1, r1 = r0, r0 = r;\n\n    // Is it a point?\n    if (!(r1 > epsilon)) context.moveTo(0, 0);\n\n    // Or is it a circle or annulus?\n    else if (da > tau - epsilon) {\n      context.moveTo(r1 * cos(a0), r1 * sin(a0));\n      context.arc(0, 0, r1, a0, a1, !cw);\n      if (r0 > epsilon) {\n        context.moveTo(r0 * cos(a1), r0 * sin(a1));\n        context.arc(0, 0, r0, a1, a0, cw);\n      }\n    }\n\n    // Or is it a circular or annular sector?\n    else {\n      var a01 = a0,\n          a11 = a1,\n          a00 = a0,\n          a10 = a1,\n          da0 = da,\n          da1 = da,\n          ap = padAngle.apply(this, arguments) / 2,\n          rp = (ap > epsilon) && (padRadius ? +padRadius.apply(this, arguments) : sqrt(r0 * r0 + r1 * r1)),\n          rc = min(abs(r1 - r0) / 2, +cornerRadius.apply(this, arguments)),\n          rc0 = rc,\n          rc1 = rc,\n          t0,\n          t1;\n\n      // Apply padding? Note that since r1 ≥ r0, da1 ≥ da0.\n      if (rp > epsilon) {\n        var p0 = asin(rp / r0 * sin(ap)),\n            p1 = asin(rp / r1 * sin(ap));\n        if ((da0 -= p0 * 2) > epsilon) p0 *= (cw ? 1 : -1), a00 += p0, a10 -= p0;\n        else da0 = 0, a00 = a10 = (a0 + a1) / 2;\n        if ((da1 -= p1 * 2) > epsilon) p1 *= (cw ? 1 : -1), a01 += p1, a11 -= p1;\n        else da1 = 0, a01 = a11 = (a0 + a1) / 2;\n      }\n\n      var x01 = r1 * cos(a01),\n          y01 = r1 * sin(a01),\n          x10 = r0 * cos(a10),\n          y10 = r0 * sin(a10);\n\n      // Apply rounded corners?\n      if (rc > epsilon) {\n        var x11 = r1 * cos(a11),\n            y11 = r1 * sin(a11),\n            x00 = r0 * cos(a00),\n            y00 = r0 * sin(a00),\n            oc;\n\n        // Restrict the corner radius according to the sector angle.\n        if (da < pi && (oc = intersect(x01, y01, x00, y00, x11, y11, x10, y10))) {\n          var ax = x01 - oc[0],\n              ay = y01 - oc[1],\n              bx = x11 - oc[0],\n              by = y11 - oc[1],\n              kc = 1 / sin(acos((ax * bx + ay * by) / (sqrt(ax * ax + ay * ay) * sqrt(bx * bx + by * by))) / 2),\n              lc = sqrt(oc[0] * oc[0] + oc[1] * oc[1]);\n          rc0 = min(rc, (r0 - lc) / (kc - 1));\n          rc1 = min(rc, (r1 - lc) / (kc + 1));\n        }\n      }\n\n      // Is the sector collapsed to a line?\n      if (!(da1 > epsilon)) context.moveTo(x01, y01);\n\n      // Does the sector’s outer ring have rounded corners?\n      else if (rc1 > epsilon) {\n        t0 = cornerTangents(x00, y00, x01, y01, r1, rc1, cw);\n        t1 = cornerTangents(x11, y11, x10, y10, r1, rc1, cw);\n\n        context.moveTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc1 < rc) context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc1, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r1, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), !cw);\n          context.arc(t1.cx, t1.cy, rc1, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the outer ring just a circular arc?\n      else context.moveTo(x01, y01), context.arc(0, 0, r1, a01, a11, !cw);\n\n      // Is there no inner ring, and it’s a circular sector?\n      // Or perhaps it’s an annular sector collapsed due to padding?\n      if (!(r0 > epsilon) || !(da0 > epsilon)) context.lineTo(x10, y10);\n\n      // Does the sector’s inner ring (or point) have rounded corners?\n      else if (rc0 > epsilon) {\n        t0 = cornerTangents(x10, y10, x11, y11, r0, -rc0, cw);\n        t1 = cornerTangents(x01, y01, x00, y00, r0, -rc0, cw);\n\n        context.lineTo(t0.cx + t0.x01, t0.cy + t0.y01);\n\n        // Have the corners merged?\n        if (rc0 < rc) context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t1.y01, t1.x01), !cw);\n\n        // Otherwise, draw the two corners and the ring.\n        else {\n          context.arc(t0.cx, t0.cy, rc0, atan2(t0.y01, t0.x01), atan2(t0.y11, t0.x11), !cw);\n          context.arc(0, 0, r0, atan2(t0.cy + t0.y11, t0.cx + t0.x11), atan2(t1.cy + t1.y11, t1.cx + t1.x11), cw);\n          context.arc(t1.cx, t1.cy, rc0, atan2(t1.y11, t1.x11), atan2(t1.y01, t1.x01), !cw);\n        }\n      }\n\n      // Or is the inner ring just a circular arc?\n      else context.arc(0, 0, r0, a10, a00, cw);\n    }\n\n    context.closePath();\n\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  arc.centroid = function() {\n    var r = (+innerRadius.apply(this, arguments) + +outerRadius.apply(this, arguments)) / 2,\n        a = (+startAngle.apply(this, arguments) + +endAngle.apply(this, arguments)) / 2 - pi / 2;\n    return [cos(a) * r, sin(a) * r];\n  };\n\n  arc.innerRadius = function(_) {\n    return arguments.length ? (innerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : innerRadius;\n  };\n\n  arc.outerRadius = function(_) {\n    return arguments.length ? (outerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : outerRadius;\n  };\n\n  arc.cornerRadius = function(_) {\n    return arguments.length ? (cornerRadius = typeof _ === \"function\" ? _ : constant(+_), arc) : cornerRadius;\n  };\n\n  arc.padRadius = function(_) {\n    return arguments.length ? (padRadius = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), arc) : padRadius;\n  };\n\n  arc.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : startAngle;\n  };\n\n  arc.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : endAngle;\n  };\n\n  arc.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), arc) : padAngle;\n  };\n\n  arc.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), arc) : context;\n  };\n\n  return arc;\n}\n\nfunction Linear(context) {\n  this._context = context;\n}\n\nLinear.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; // proceed\n      default: this._context.lineTo(x, y); break;\n    }\n  }\n};\n\nfunction curveLinear(context) {\n  return new Linear(context);\n}\n\nfunction x(p) {\n  return p[0];\n}\n\nfunction y(p) {\n  return p[1];\n}\n\nfunction line() {\n  var x$1 = x,\n      y$1 = y,\n      defined = constant(true),\n      context = null,\n      curve = curveLinear,\n      output = null;\n\n  function line(data) {\n    var i,\n        n = data.length,\n        d,\n        defined0 = false,\n        buffer;\n\n    if (context == null) output = curve(buffer = d3Path.path());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) output.lineStart();\n        else output.lineEnd();\n      }\n      if (defined0) output.point(+x$1(d, i, data), +y$1(d, i, data));\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  line.x = function(_) {\n    return arguments.length ? (x$1 = typeof _ === \"function\" ? _ : constant(+_), line) : x$1;\n  };\n\n  line.y = function(_) {\n    return arguments.length ? (y$1 = typeof _ === \"function\" ? _ : constant(+_), line) : y$1;\n  };\n\n  line.defined = function(_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), line) : defined;\n  };\n\n  line.curve = function(_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), line) : curve;\n  };\n\n  line.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), line) : context;\n  };\n\n  return line;\n}\n\nfunction area() {\n  var x0 = x,\n      x1 = null,\n      y0 = constant(0),\n      y1 = y,\n      defined = constant(true),\n      context = null,\n      curve = curveLinear,\n      output = null;\n\n  function area(data) {\n    var i,\n        j,\n        k,\n        n = data.length,\n        d,\n        defined0 = false,\n        buffer,\n        x0z = new Array(n),\n        y0z = new Array(n);\n\n    if (context == null) output = curve(buffer = d3Path.path());\n\n    for (i = 0; i <= n; ++i) {\n      if (!(i < n && defined(d = data[i], i, data)) === defined0) {\n        if (defined0 = !defined0) {\n          j = i;\n          output.areaStart();\n          output.lineStart();\n        } else {\n          output.lineEnd();\n          output.lineStart();\n          for (k = i - 1; k >= j; --k) {\n            output.point(x0z[k], y0z[k]);\n          }\n          output.lineEnd();\n          output.areaEnd();\n        }\n      }\n      if (defined0) {\n        x0z[i] = +x0(d, i, data), y0z[i] = +y0(d, i, data);\n        output.point(x1 ? +x1(d, i, data) : x0z[i], y1 ? +y1(d, i, data) : y0z[i]);\n      }\n    }\n\n    if (buffer) return output = null, buffer + \"\" || null;\n  }\n\n  function arealine() {\n    return line().defined(defined).curve(curve).context(context);\n  }\n\n  area.x = function(_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), x1 = null, area) : x0;\n  };\n\n  area.x0 = function(_) {\n    return arguments.length ? (x0 = typeof _ === \"function\" ? _ : constant(+_), area) : x0;\n  };\n\n  area.x1 = function(_) {\n    return arguments.length ? (x1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : x1;\n  };\n\n  area.y = function(_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), y1 = null, area) : y0;\n  };\n\n  area.y0 = function(_) {\n    return arguments.length ? (y0 = typeof _ === \"function\" ? _ : constant(+_), area) : y0;\n  };\n\n  area.y1 = function(_) {\n    return arguments.length ? (y1 = _ == null ? null : typeof _ === \"function\" ? _ : constant(+_), area) : y1;\n  };\n\n  area.lineX0 =\n  area.lineY0 = function() {\n    return arealine().x(x0).y(y0);\n  };\n\n  area.lineY1 = function() {\n    return arealine().x(x0).y(y1);\n  };\n\n  area.lineX1 = function() {\n    return arealine().x(x1).y(y0);\n  };\n\n  area.defined = function(_) {\n    return arguments.length ? (defined = typeof _ === \"function\" ? _ : constant(!!_), area) : defined;\n  };\n\n  area.curve = function(_) {\n    return arguments.length ? (curve = _, context != null && (output = curve(context)), area) : curve;\n  };\n\n  area.context = function(_) {\n    return arguments.length ? (_ == null ? context = output = null : output = curve(context = _), area) : context;\n  };\n\n  return area;\n}\n\nfunction descending(a, b) {\n  return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}\n\nfunction identity(d) {\n  return d;\n}\n\nfunction pie() {\n  var value = identity,\n      sortValues = descending,\n      sort = null,\n      startAngle = constant(0),\n      endAngle = constant(tau),\n      padAngle = constant(0);\n\n  function pie(data) {\n    var i,\n        n = data.length,\n        j,\n        k,\n        sum = 0,\n        index = new Array(n),\n        arcs = new Array(n),\n        a0 = +startAngle.apply(this, arguments),\n        da = Math.min(tau, Math.max(-tau, endAngle.apply(this, arguments) - a0)),\n        a1,\n        p = Math.min(Math.abs(da) / n, padAngle.apply(this, arguments)),\n        pa = p * (da < 0 ? -1 : 1),\n        v;\n\n    for (i = 0; i < n; ++i) {\n      if ((v = arcs[index[i] = i] = +value(data[i], i, data)) > 0) {\n        sum += v;\n      }\n    }\n\n    // Optionally sort the arcs by previously-computed values or by data.\n    if (sortValues != null) index.sort(function(i, j) { return sortValues(arcs[i], arcs[j]); });\n    else if (sort != null) index.sort(function(i, j) { return sort(data[i], data[j]); });\n\n    // Compute the arcs! They are stored in the original data's order.\n    for (i = 0, k = sum ? (da - n * pa) / sum : 0; i < n; ++i, a0 = a1) {\n      j = index[i], v = arcs[j], a1 = a0 + (v > 0 ? v * k : 0) + pa, arcs[j] = {\n        data: data[j],\n        index: i,\n        value: v,\n        startAngle: a0,\n        endAngle: a1,\n        padAngle: p\n      };\n    }\n\n    return arcs;\n  }\n\n  pie.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), pie) : value;\n  };\n\n  pie.sortValues = function(_) {\n    return arguments.length ? (sortValues = _, sort = null, pie) : sortValues;\n  };\n\n  pie.sort = function(_) {\n    return arguments.length ? (sort = _, sortValues = null, pie) : sort;\n  };\n\n  pie.startAngle = function(_) {\n    return arguments.length ? (startAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : startAngle;\n  };\n\n  pie.endAngle = function(_) {\n    return arguments.length ? (endAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : endAngle;\n  };\n\n  pie.padAngle = function(_) {\n    return arguments.length ? (padAngle = typeof _ === \"function\" ? _ : constant(+_), pie) : padAngle;\n  };\n\n  return pie;\n}\n\nvar curveRadialLinear = curveRadial(curveLinear);\n\nfunction Radial(curve) {\n  this._curve = curve;\n}\n\nRadial.prototype = {\n  areaStart: function() {\n    this._curve.areaStart();\n  },\n  areaEnd: function() {\n    this._curve.areaEnd();\n  },\n  lineStart: function() {\n    this._curve.lineStart();\n  },\n  lineEnd: function() {\n    this._curve.lineEnd();\n  },\n  point: function(a, r) {\n    this._curve.point(r * Math.sin(a), r * -Math.cos(a));\n  }\n};\n\nfunction curveRadial(curve) {\n\n  function radial(context) {\n    return new Radial(curve(context));\n  }\n\n  radial._curve = curve;\n\n  return radial;\n}\n\nfunction lineRadial(l) {\n  var c = l.curve;\n\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n\n  l.curve = function(_) {\n    return arguments.length ? c(curveRadial(_)) : c()._curve;\n  };\n\n  return l;\n}\n\nfunction lineRadial$1() {\n  return lineRadial(line().curve(curveRadialLinear));\n}\n\nfunction areaRadial() {\n  var a = area().curve(curveRadialLinear),\n      c = a.curve,\n      x0 = a.lineX0,\n      x1 = a.lineX1,\n      y0 = a.lineY0,\n      y1 = a.lineY1;\n\n  a.angle = a.x, delete a.x;\n  a.startAngle = a.x0, delete a.x0;\n  a.endAngle = a.x1, delete a.x1;\n  a.radius = a.y, delete a.y;\n  a.innerRadius = a.y0, delete a.y0;\n  a.outerRadius = a.y1, delete a.y1;\n  a.lineStartAngle = function() { return lineRadial(x0()); }, delete a.lineX0;\n  a.lineEndAngle = function() { return lineRadial(x1()); }, delete a.lineX1;\n  a.lineInnerRadius = function() { return lineRadial(y0()); }, delete a.lineY0;\n  a.lineOuterRadius = function() { return lineRadial(y1()); }, delete a.lineY1;\n\n  a.curve = function(_) {\n    return arguments.length ? c(curveRadial(_)) : c()._curve;\n  };\n\n  return a;\n}\n\nfunction pointRadial(x, y) {\n  return [(y = +y) * Math.cos(x -= Math.PI / 2), y * Math.sin(x)];\n}\n\nvar slice = Array.prototype.slice;\n\nfunction linkSource(d) {\n  return d.source;\n}\n\nfunction linkTarget(d) {\n  return d.target;\n}\n\nfunction link(curve) {\n  var source = linkSource,\n      target = linkTarget,\n      x$1 = x,\n      y$1 = y,\n      context = null;\n\n  function link() {\n    var buffer, argv = slice.call(arguments), s = source.apply(this, argv), t = target.apply(this, argv);\n    if (!context) context = buffer = d3Path.path();\n    curve(context, +x$1.apply(this, (argv[0] = s, argv)), +y$1.apply(this, argv), +x$1.apply(this, (argv[0] = t, argv)), +y$1.apply(this, argv));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  link.source = function(_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n\n  link.target = function(_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n\n  link.x = function(_) {\n    return arguments.length ? (x$1 = typeof _ === \"function\" ? _ : constant(+_), link) : x$1;\n  };\n\n  link.y = function(_) {\n    return arguments.length ? (y$1 = typeof _ === \"function\" ? _ : constant(+_), link) : y$1;\n  };\n\n  link.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), link) : context;\n  };\n\n  return link;\n}\n\nfunction curveHorizontal(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0 = (x0 + x1) / 2, y0, x0, y1, x1, y1);\n}\n\nfunction curveVertical(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0, y0 = (y0 + y1) / 2, x1, y0, x1, y1);\n}\n\nfunction curveRadial$1(context, x0, y0, x1, y1) {\n  var p0 = pointRadial(x0, y0),\n      p1 = pointRadial(x0, y0 = (y0 + y1) / 2),\n      p2 = pointRadial(x1, y0),\n      p3 = pointRadial(x1, y1);\n  context.moveTo(p0[0], p0[1]);\n  context.bezierCurveTo(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);\n}\n\nfunction linkHorizontal() {\n  return link(curveHorizontal);\n}\n\nfunction linkVertical() {\n  return link(curveVertical);\n}\n\nfunction linkRadial() {\n  var l = link(curveRadial$1);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}\n\nvar circle = {\n  draw: function(context, size) {\n    var r = Math.sqrt(size / pi);\n    context.moveTo(r, 0);\n    context.arc(0, 0, r, 0, tau);\n  }\n};\n\nvar cross = {\n  draw: function(context, size) {\n    var r = Math.sqrt(size / 5) / 2;\n    context.moveTo(-3 * r, -r);\n    context.lineTo(-r, -r);\n    context.lineTo(-r, -3 * r);\n    context.lineTo(r, -3 * r);\n    context.lineTo(r, -r);\n    context.lineTo(3 * r, -r);\n    context.lineTo(3 * r, r);\n    context.lineTo(r, r);\n    context.lineTo(r, 3 * r);\n    context.lineTo(-r, 3 * r);\n    context.lineTo(-r, r);\n    context.lineTo(-3 * r, r);\n    context.closePath();\n  }\n};\n\nvar tan30 = Math.sqrt(1 / 3),\n    tan30_2 = tan30 * 2;\n\nvar diamond = {\n  draw: function(context, size) {\n    var y = Math.sqrt(size / tan30_2),\n        x = y * tan30;\n    context.moveTo(0, -y);\n    context.lineTo(x, 0);\n    context.lineTo(0, y);\n    context.lineTo(-x, 0);\n    context.closePath();\n  }\n};\n\nvar ka = 0.89081309152928522810,\n    kr = Math.sin(pi / 10) / Math.sin(7 * pi / 10),\n    kx = Math.sin(tau / 10) * kr,\n    ky = -Math.cos(tau / 10) * kr;\n\nvar star = {\n  draw: function(context, size) {\n    var r = Math.sqrt(size * ka),\n        x = kx * r,\n        y = ky * r;\n    context.moveTo(0, -r);\n    context.lineTo(x, y);\n    for (var i = 1; i < 5; ++i) {\n      var a = tau * i / 5,\n          c = Math.cos(a),\n          s = Math.sin(a);\n      context.lineTo(s * r, -c * r);\n      context.lineTo(c * x - s * y, s * x + c * y);\n    }\n    context.closePath();\n  }\n};\n\nvar square = {\n  draw: function(context, size) {\n    var w = Math.sqrt(size),\n        x = -w / 2;\n    context.rect(x, x, w, w);\n  }\n};\n\nvar sqrt3 = Math.sqrt(3);\n\nvar triangle = {\n  draw: function(context, size) {\n    var y = -Math.sqrt(size / (sqrt3 * 3));\n    context.moveTo(0, y * 2);\n    context.lineTo(-sqrt3 * y, -y);\n    context.lineTo(sqrt3 * y, -y);\n    context.closePath();\n  }\n};\n\nvar c = -0.5,\n    s = Math.sqrt(3) / 2,\n    k = 1 / Math.sqrt(12),\n    a = (k / 2 + 1) * 3;\n\nvar wye = {\n  draw: function(context, size) {\n    var r = Math.sqrt(size / a),\n        x0 = r / 2,\n        y0 = r * k,\n        x1 = x0,\n        y1 = r * k + r,\n        x2 = -x1,\n        y2 = y1;\n    context.moveTo(x0, y0);\n    context.lineTo(x1, y1);\n    context.lineTo(x2, y2);\n    context.lineTo(c * x0 - s * y0, s * x0 + c * y0);\n    context.lineTo(c * x1 - s * y1, s * x1 + c * y1);\n    context.lineTo(c * x2 - s * y2, s * x2 + c * y2);\n    context.lineTo(c * x0 + s * y0, c * y0 - s * x0);\n    context.lineTo(c * x1 + s * y1, c * y1 - s * x1);\n    context.lineTo(c * x2 + s * y2, c * y2 - s * x2);\n    context.closePath();\n  }\n};\n\nvar symbols = [\n  circle,\n  cross,\n  diamond,\n  square,\n  star,\n  triangle,\n  wye\n];\n\nfunction symbol() {\n  var type = constant(circle),\n      size = constant(64),\n      context = null;\n\n  function symbol() {\n    var buffer;\n    if (!context) context = buffer = d3Path.path();\n    type.apply(this, arguments).draw(context, +size.apply(this, arguments));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  symbol.type = function(_) {\n    return arguments.length ? (type = typeof _ === \"function\" ? _ : constant(_), symbol) : type;\n  };\n\n  symbol.size = function(_) {\n    return arguments.length ? (size = typeof _ === \"function\" ? _ : constant(+_), symbol) : size;\n  };\n\n  symbol.context = function(_) {\n    return arguments.length ? (context = _ == null ? null : _, symbol) : context;\n  };\n\n  return symbol;\n}\n\nfunction noop() {}\n\nfunction point(that, x, y) {\n  that._context.bezierCurveTo(\n    (2 * that._x0 + that._x1) / 3,\n    (2 * that._y0 + that._y1) / 3,\n    (that._x0 + 2 * that._x1) / 3,\n    (that._y0 + 2 * that._y1) / 3,\n    (that._x0 + 4 * that._x1 + x) / 6,\n    (that._y0 + 4 * that._y1 + y) / 6\n  );\n}\n\nfunction Basis(context) {\n  this._context = context;\n}\n\nBasis.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 3: point(this, this._x1, this._y1); // proceed\n      case 2: this._context.lineTo(this._x1, this._y1); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._context.lineTo((5 * this._x0 + this._x1) / 6, (5 * this._y0 + this._y1) / 6); // proceed\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nfunction basis(context) {\n  return new Basis(context);\n}\n\nfunction BasisClosed(context) {\n  this._context = context;\n}\n\nBasisClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x2, this._y2);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.moveTo((this._x2 + 2 * this._x3) / 3, (this._y2 + 2 * this._y3) / 3);\n        this._context.lineTo((this._x3 + 2 * this._x2) / 3, (this._y3 + 2 * this._y2) / 3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x2, this._y2);\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._x2 = x, this._y2 = y; break;\n      case 1: this._point = 2; this._x3 = x, this._y3 = y; break;\n      case 2: this._point = 3; this._x4 = x, this._y4 = y; this._context.moveTo((this._x0 + 4 * this._x1 + x) / 6, (this._y0 + 4 * this._y1 + y) / 6); break;\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nfunction basisClosed(context) {\n  return new BasisClosed(context);\n}\n\nfunction BasisOpen(context) {\n  this._context = context;\n}\n\nBasisOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; var x0 = (this._x0 + 4 * this._x1 + x) / 6, y0 = (this._y0 + 4 * this._y1 + y) / 6; this._line ? this._context.lineTo(x0, y0) : this._context.moveTo(x0, y0); break;\n      case 3: this._point = 4; // proceed\n      default: point(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n  }\n};\n\nfunction basisOpen(context) {\n  return new BasisOpen(context);\n}\n\nfunction Bundle(context, beta) {\n  this._basis = new Basis(context);\n  this._beta = beta;\n}\n\nBundle.prototype = {\n  lineStart: function() {\n    this._x = [];\n    this._y = [];\n    this._basis.lineStart();\n  },\n  lineEnd: function() {\n    var x = this._x,\n        y = this._y,\n        j = x.length - 1;\n\n    if (j > 0) {\n      var x0 = x[0],\n          y0 = y[0],\n          dx = x[j] - x0,\n          dy = y[j] - y0,\n          i = -1,\n          t;\n\n      while (++i <= j) {\n        t = i / j;\n        this._basis.point(\n          this._beta * x[i] + (1 - this._beta) * (x0 + t * dx),\n          this._beta * y[i] + (1 - this._beta) * (y0 + t * dy)\n        );\n      }\n    }\n\n    this._x = this._y = null;\n    this._basis.lineEnd();\n  },\n  point: function(x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\nvar bundle = (function custom(beta) {\n\n  function bundle(context) {\n    return beta === 1 ? new Basis(context) : new Bundle(context, beta);\n  }\n\n  bundle.beta = function(beta) {\n    return custom(+beta);\n  };\n\n  return bundle;\n})(0.85);\n\nfunction point$1(that, x, y) {\n  that._context.bezierCurveTo(\n    that._x1 + that._k * (that._x2 - that._x0),\n    that._y1 + that._k * (that._y2 - that._y0),\n    that._x2 + that._k * (that._x1 - x),\n    that._y2 + that._k * (that._y1 - y),\n    that._x2,\n    that._y2\n  );\n}\n\nfunction Cardinal(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinal.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x2, this._y2); break;\n      case 3: point$1(this, this._x1, this._y1); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; this._x1 = x, this._y1 = y; break;\n      case 2: this._point = 3; // proceed\n      default: point$1(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nvar cardinal = (function custom(tension) {\n\n  function cardinal(context) {\n    return new Cardinal(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n\nfunction CardinalClosed(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinalClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.lineTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        this.point(this._x5, this._y5);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._x3 = x, this._y3 = y; break;\n      case 1: this._point = 2; this._context.moveTo(this._x4 = x, this._y4 = y); break;\n      case 2: this._point = 3; this._x5 = x, this._y5 = y; break;\n      default: point$1(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nvar cardinalClosed = (function custom(tension) {\n\n  function cardinal(context) {\n    return new CardinalClosed(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n\nfunction CardinalOpen(context, tension) {\n  this._context = context;\n  this._k = (1 - tension) / 6;\n}\n\nCardinalOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2); break;\n      case 3: this._point = 4; // proceed\n      default: point$1(this, x, y); break;\n    }\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nvar cardinalOpen = (function custom(tension) {\n\n  function cardinal(context) {\n    return new CardinalOpen(context, tension);\n  }\n\n  cardinal.tension = function(tension) {\n    return custom(+tension);\n  };\n\n  return cardinal;\n})(0);\n\nfunction point$2(that, x, y) {\n  var x1 = that._x1,\n      y1 = that._y1,\n      x2 = that._x2,\n      y2 = that._y2;\n\n  if (that._l01_a > epsilon) {\n    var a = 2 * that._l01_2a + 3 * that._l01_a * that._l12_a + that._l12_2a,\n        n = 3 * that._l01_a * (that._l01_a + that._l12_a);\n    x1 = (x1 * a - that._x0 * that._l12_2a + that._x2 * that._l01_2a) / n;\n    y1 = (y1 * a - that._y0 * that._l12_2a + that._y2 * that._l01_2a) / n;\n  }\n\n  if (that._l23_a > epsilon) {\n    var b = 2 * that._l23_2a + 3 * that._l23_a * that._l12_a + that._l12_2a,\n        m = 3 * that._l23_a * (that._l23_a + that._l12_a);\n    x2 = (x2 * b + that._x1 * that._l23_2a - x * that._l12_2a) / m;\n    y2 = (y2 * b + that._y1 * that._l23_2a - y * that._l12_2a) / m;\n  }\n\n  that._context.bezierCurveTo(x1, y1, x2, y2, that._x2, that._y2);\n}\n\nfunction CatmullRom(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRom.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x2, this._y2); break;\n      case 3: this.point(this._x2, this._y2); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; // proceed\n      default: point$2(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nvar catmullRom = (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRom(context, alpha) : new Cardinal(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n\nfunction CatmullRomClosed(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRomClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 =\n    this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 1: {\n        this._context.moveTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 2: {\n        this._context.lineTo(this._x3, this._y3);\n        this._context.closePath();\n        break;\n      }\n      case 3: {\n        this.point(this._x3, this._y3);\n        this.point(this._x4, this._y4);\n        this.point(this._x5, this._y5);\n        break;\n      }\n    }\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; this._x3 = x, this._y3 = y; break;\n      case 1: this._point = 2; this._context.moveTo(this._x4 = x, this._y4 = y); break;\n      case 2: this._point = 3; this._x5 = x, this._y5 = y; break;\n      default: point$2(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nvar catmullRomClosed = (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRomClosed(context, alpha) : new CardinalClosed(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n\nfunction CatmullRomOpen(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRomOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2); break;\n      case 3: this._point = 4; // proceed\n      default: point$2(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nvar catmullRomOpen = (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRomOpen(context, alpha) : new CardinalOpen(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n\nfunction LinearClosed(context) {\n  this._context = context;\n}\n\nLinearClosed.prototype = {\n  areaStart: noop,\n  areaEnd: noop,\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._point) this._context.closePath();\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    if (this._point) this._context.lineTo(x, y);\n    else this._point = 1, this._context.moveTo(x, y);\n  }\n};\n\nfunction linearClosed(context) {\n  return new LinearClosed(context);\n}\n\nfunction sign(x) {\n  return x < 0 ? -1 : 1;\n}\n\n// Calculate the slopes of the tangents (Hermite-type interpolation) based on\n// the following paper: Steffen, M. 1990. A Simple Method for Monotonic\n// Interpolation in One Dimension. Astronomy and Astrophysics, Vol. 239, NO.\n// NOV(II), P. 443, 1990.\nfunction slope3(that, x2, y2) {\n  var h0 = that._x1 - that._x0,\n      h1 = x2 - that._x1,\n      s0 = (that._y1 - that._y0) / (h0 || h1 < 0 && -0),\n      s1 = (y2 - that._y1) / (h1 || h0 < 0 && -0),\n      p = (s0 * h1 + s1 * h0) / (h0 + h1);\n  return (sign(s0) + sign(s1)) * Math.min(Math.abs(s0), Math.abs(s1), 0.5 * Math.abs(p)) || 0;\n}\n\n// Calculate a one-sided slope.\nfunction slope2(that, t) {\n  var h = that._x1 - that._x0;\n  return h ? (3 * (that._y1 - that._y0) / h - t) / 2 : t;\n}\n\n// According to https://en.wikipedia.org/wiki/Cubic_Hermite_spline#Representations\n// \"you can express cubic Hermite interpolation in terms of cubic Bézier curves\n// with respect to the four values p0, p0 + m0 / 3, p1 - m1 / 3, p1\".\nfunction point$3(that, t0, t1) {\n  var x0 = that._x0,\n      y0 = that._y0,\n      x1 = that._x1,\n      y1 = that._y1,\n      dx = (x1 - x0) / 3;\n  that._context.bezierCurveTo(x0 + dx, y0 + dx * t0, x1 - dx, y1 - dx * t1, x1, y1);\n}\n\nfunction MonotoneX(context) {\n  this._context = context;\n}\n\nMonotoneX.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 =\n    this._y0 = this._y1 =\n    this._t0 = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x1, this._y1); break;\n      case 3: point$3(this, this._t0, slope2(this, this._t0)); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    var t1 = NaN;\n\n    x = +x, y = +y;\n    if (x === this._x1 && y === this._y1) return; // Ignore coincident points.\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; point$3(this, slope2(this, t1 = slope3(this, x, y)), t1); break;\n      default: point$3(this, this._t0, t1 = slope3(this, x, y)); break;\n    }\n\n    this._x0 = this._x1, this._x1 = x;\n    this._y0 = this._y1, this._y1 = y;\n    this._t0 = t1;\n  }\n};\n\nfunction MonotoneY(context) {\n  this._context = new ReflectContext(context);\n}\n\n(MonotoneY.prototype = Object.create(MonotoneX.prototype)).point = function(x, y) {\n  MonotoneX.prototype.point.call(this, y, x);\n};\n\nfunction ReflectContext(context) {\n  this._context = context;\n}\n\nReflectContext.prototype = {\n  moveTo: function(x, y) { this._context.moveTo(y, x); },\n  closePath: function() { this._context.closePath(); },\n  lineTo: function(x, y) { this._context.lineTo(y, x); },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) { this._context.bezierCurveTo(y1, x1, y2, x2, y, x); }\n};\n\nfunction monotoneX(context) {\n  return new MonotoneX(context);\n}\n\nfunction monotoneY(context) {\n  return new MonotoneY(context);\n}\n\nfunction Natural(context) {\n  this._context = context;\n}\n\nNatural.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x = [];\n    this._y = [];\n  },\n  lineEnd: function() {\n    var x = this._x,\n        y = this._y,\n        n = x.length;\n\n    if (n) {\n      this._line ? this._context.lineTo(x[0], y[0]) : this._context.moveTo(x[0], y[0]);\n      if (n === 2) {\n        this._context.lineTo(x[1], y[1]);\n      } else {\n        var px = controlPoints(x),\n            py = controlPoints(y);\n        for (var i0 = 0, i1 = 1; i1 < n; ++i0, ++i1) {\n          this._context.bezierCurveTo(px[0][i0], py[0][i0], px[1][i0], py[1][i0], x[i1], y[i1]);\n        }\n      }\n    }\n\n    if (this._line || (this._line !== 0 && n === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n    this._x = this._y = null;\n  },\n  point: function(x, y) {\n    this._x.push(+x);\n    this._y.push(+y);\n  }\n};\n\n// See https://www.particleincell.com/2012/bezier-splines/ for derivation.\nfunction controlPoints(x) {\n  var i,\n      n = x.length - 1,\n      m,\n      a = new Array(n),\n      b = new Array(n),\n      r = new Array(n);\n  a[0] = 0, b[0] = 2, r[0] = x[0] + 2 * x[1];\n  for (i = 1; i < n - 1; ++i) a[i] = 1, b[i] = 4, r[i] = 4 * x[i] + 2 * x[i + 1];\n  a[n - 1] = 2, b[n - 1] = 7, r[n - 1] = 8 * x[n - 1] + x[n];\n  for (i = 1; i < n; ++i) m = a[i] / b[i - 1], b[i] -= m, r[i] -= m * r[i - 1];\n  a[n - 1] = r[n - 1] / b[n - 1];\n  for (i = n - 2; i >= 0; --i) a[i] = (r[i] - a[i + 1]) / b[i];\n  b[n - 1] = (x[n] + a[n - 1]) / 2;\n  for (i = 0; i < n - 1; ++i) b[i] = 2 * x[i + 1] - a[i + 1];\n  return [a, b];\n}\n\nfunction natural(context) {\n  return new Natural(context);\n}\n\nfunction Step(context, t) {\n  this._context = context;\n  this._t = t;\n}\n\nStep.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x = this._y = NaN;\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (0 < this._t && this._t < 1 && this._point === 2) this._context.lineTo(this._x, this._y);\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    if (this._line >= 0) this._t = 1 - this._t, this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; // proceed\n      default: {\n        if (this._t <= 0) {\n          this._context.lineTo(this._x, y);\n          this._context.lineTo(x, y);\n        } else {\n          var x1 = this._x * (1 - this._t) + x * this._t;\n          this._context.lineTo(x1, this._y);\n          this._context.lineTo(x1, y);\n        }\n        break;\n      }\n    }\n    this._x = x, this._y = y;\n  }\n};\n\nfunction step(context) {\n  return new Step(context, 0.5);\n}\n\nfunction stepBefore(context) {\n  return new Step(context, 0);\n}\n\nfunction stepAfter(context) {\n  return new Step(context, 1);\n}\n\nfunction none(series, order) {\n  if (!((n = series.length) > 1)) return;\n  for (var i = 1, j, s0, s1 = series[order[0]], n, m = s1.length; i < n; ++i) {\n    s0 = s1, s1 = series[order[i]];\n    for (j = 0; j < m; ++j) {\n      s1[j][1] += s1[j][0] = isNaN(s0[j][1]) ? s0[j][0] : s0[j][1];\n    }\n  }\n}\n\nfunction none$1(series) {\n  var n = series.length, o = new Array(n);\n  while (--n >= 0) o[n] = n;\n  return o;\n}\n\nfunction stackValue(d, key) {\n  return d[key];\n}\n\nfunction stack() {\n  var keys = constant([]),\n      order = none$1,\n      offset = none,\n      value = stackValue;\n\n  function stack(data) {\n    var kz = keys.apply(this, arguments),\n        i,\n        m = data.length,\n        n = kz.length,\n        sz = new Array(n),\n        oz;\n\n    for (i = 0; i < n; ++i) {\n      for (var ki = kz[i], si = sz[i] = new Array(m), j = 0, sij; j < m; ++j) {\n        si[j] = sij = [0, +value(data[j], ki, j, data)];\n        sij.data = data[j];\n      }\n      si.key = ki;\n    }\n\n    for (i = 0, oz = order(sz); i < n; ++i) {\n      sz[oz[i]].index = i;\n    }\n\n    offset(sz, oz);\n    return sz;\n  }\n\n  stack.keys = function(_) {\n    return arguments.length ? (keys = typeof _ === \"function\" ? _ : constant(slice.call(_)), stack) : keys;\n  };\n\n  stack.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(+_), stack) : value;\n  };\n\n  stack.order = function(_) {\n    return arguments.length ? (order = _ == null ? none$1 : typeof _ === \"function\" ? _ : constant(slice.call(_)), stack) : order;\n  };\n\n  stack.offset = function(_) {\n    return arguments.length ? (offset = _ == null ? none : _, stack) : offset;\n  };\n\n  return stack;\n}\n\nfunction expand(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var i, n, j = 0, m = series[0].length, y; j < m; ++j) {\n    for (y = i = 0; i < n; ++i) y += series[i][j][1] || 0;\n    if (y) for (i = 0; i < n; ++i) series[i][j][1] /= y;\n  }\n  none(series, order);\n}\n\nfunction diverging(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var i, j = 0, d, dy, yp, yn, n, m = series[order[0]].length; j < m; ++j) {\n    for (yp = yn = 0, i = 0; i < n; ++i) {\n      if ((dy = (d = series[order[i]][j])[1] - d[0]) > 0) {\n        d[0] = yp, d[1] = yp += dy;\n      } else if (dy < 0) {\n        d[1] = yn, d[0] = yn += dy;\n      } else {\n        d[0] = 0, d[1] = dy;\n      }\n    }\n  }\n}\n\nfunction silhouette(series, order) {\n  if (!((n = series.length) > 0)) return;\n  for (var j = 0, s0 = series[order[0]], n, m = s0.length; j < m; ++j) {\n    for (var i = 0, y = 0; i < n; ++i) y += series[i][j][1] || 0;\n    s0[j][1] += s0[j][0] = -y / 2;\n  }\n  none(series, order);\n}\n\nfunction wiggle(series, order) {\n  if (!((n = series.length) > 0) || !((m = (s0 = series[order[0]]).length) > 0)) return;\n  for (var y = 0, j = 1, s0, m, n; j < m; ++j) {\n    for (var i = 0, s1 = 0, s2 = 0; i < n; ++i) {\n      var si = series[order[i]],\n          sij0 = si[j][1] || 0,\n          sij1 = si[j - 1][1] || 0,\n          s3 = (sij0 - sij1) / 2;\n      for (var k = 0; k < i; ++k) {\n        var sk = series[order[k]],\n            skj0 = sk[j][1] || 0,\n            skj1 = sk[j - 1][1] || 0;\n        s3 += skj0 - skj1;\n      }\n      s1 += sij0, s2 += s3 * sij0;\n    }\n    s0[j - 1][1] += s0[j - 1][0] = y;\n    if (s1) y -= s2 / s1;\n  }\n  s0[j - 1][1] += s0[j - 1][0] = y;\n  none(series, order);\n}\n\nfunction appearance(series) {\n  var peaks = series.map(peak);\n  return none$1(series).sort(function(a, b) { return peaks[a] - peaks[b]; });\n}\n\nfunction peak(series) {\n  var i = -1, j = 0, n = series.length, vi, vj = -Infinity;\n  while (++i < n) if ((vi = +series[i][1]) > vj) vj = vi, j = i;\n  return j;\n}\n\nfunction ascending(series) {\n  var sums = series.map(sum);\n  return none$1(series).sort(function(a, b) { return sums[a] - sums[b]; });\n}\n\nfunction sum(series) {\n  var s = 0, i = -1, n = series.length, v;\n  while (++i < n) if (v = +series[i][1]) s += v;\n  return s;\n}\n\nfunction descending$1(series) {\n  return ascending(series).reverse();\n}\n\nfunction insideOut(series) {\n  var n = series.length,\n      i,\n      j,\n      sums = series.map(sum),\n      order = appearance(series),\n      top = 0,\n      bottom = 0,\n      tops = [],\n      bottoms = [];\n\n  for (i = 0; i < n; ++i) {\n    j = order[i];\n    if (top < bottom) {\n      top += sums[j];\n      tops.push(j);\n    } else {\n      bottom += sums[j];\n      bottoms.push(j);\n    }\n  }\n\n  return bottoms.reverse().concat(tops);\n}\n\nfunction reverse(series) {\n  return none$1(series).reverse();\n}\n\nexports.arc = arc;\nexports.area = area;\nexports.areaRadial = areaRadial;\nexports.curveBasis = basis;\nexports.curveBasisClosed = basisClosed;\nexports.curveBasisOpen = basisOpen;\nexports.curveBundle = bundle;\nexports.curveCardinal = cardinal;\nexports.curveCardinalClosed = cardinalClosed;\nexports.curveCardinalOpen = cardinalOpen;\nexports.curveCatmullRom = catmullRom;\nexports.curveCatmullRomClosed = catmullRomClosed;\nexports.curveCatmullRomOpen = catmullRomOpen;\nexports.curveLinear = curveLinear;\nexports.curveLinearClosed = linearClosed;\nexports.curveMonotoneX = monotoneX;\nexports.curveMonotoneY = monotoneY;\nexports.curveNatural = natural;\nexports.curveStep = step;\nexports.curveStepAfter = stepAfter;\nexports.curveStepBefore = stepBefore;\nexports.line = line;\nexports.lineRadial = lineRadial$1;\nexports.linkHorizontal = linkHorizontal;\nexports.linkRadial = linkRadial;\nexports.linkVertical = linkVertical;\nexports.pie = pie;\nexports.pointRadial = pointRadial;\nexports.radialArea = areaRadial;\nexports.radialLine = lineRadial$1;\nexports.stack = stack;\nexports.stackOffsetDiverging = diverging;\nexports.stackOffsetExpand = expand;\nexports.stackOffsetNone = none;\nexports.stackOffsetSilhouette = silhouette;\nexports.stackOffsetWiggle = wiggle;\nexports.stackOrderAppearance = appearance;\nexports.stackOrderAscending = ascending;\nexports.stackOrderDescending = descending$1;\nexports.stackOrderInsideOut = insideOut;\nexports.stackOrderNone = none$1;\nexports.stackOrderReverse = reverse;\nexports.symbol = symbol;\nexports.symbolCircle = circle;\nexports.symbolCross = cross;\nexports.symbolDiamond = diamond;\nexports.symbolSquare = square;\nexports.symbolStar = star;\nexports.symbolTriangle = triangle;\nexports.symbolWye = wye;\nexports.symbols = symbols;\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\n}));\n", "// https://github.com/d3/d3-sankey v0.12.3 Copyright 2019 <PERSON>\n(function (global, factory) {\ntypeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('d3-array'), require('d3-shape')) :\ntypeof define === 'function' && define.amd ? define(['exports', 'd3-array', 'd3-shape'], factory) :\n(global = global || self, factory(global.d3 = global.d3 || {}, global.d3, global.d3));\n}(this, function (exports, d3Array, d3Shape) { 'use strict';\n\nfunction targetDepth(d) {\n  return d.target.depth;\n}\n\nfunction left(node) {\n  return node.depth;\n}\n\nfunction right(node, n) {\n  return n - 1 - node.height;\n}\n\nfunction justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\n\nfunction center(node) {\n  return node.targetLinks.length ? node.depth\n      : node.sourceLinks.length ? d3Array.min(node.sourceLinks, targetDepth) - 1\n      : 0;\n}\n\nfunction constant(x) {\n  return function() {\n    return x;\n  };\n}\n\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\n\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\n\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\n\nfunction value(d) {\n  return d.value;\n}\n\nfunction defaultId(d) {\n  return d.index;\n}\n\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\n\nfunction defaultLinks(graph) {\n  return graph.links;\n}\n\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error(\"missing: \" + id);\n  return node;\n}\n\nfunction computeLinkBreadths({nodes}) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\n\nfunction Sankey() {\n  let x0 = 0, y0 = 0, x1 = 1, y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8, py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n\n  function sankey() {\n    const graph = {nodes: nodes.apply(null, arguments), links: links.apply(null, arguments)};\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n\n  sankey.update = function(graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n\n  sankey.nodeId = function(_) {\n    return arguments.length ? (id = typeof _ === \"function\" ? _ : constant(_), sankey) : id;\n  };\n\n  sankey.nodeAlign = function(_) {\n    return arguments.length ? (align = typeof _ === \"function\" ? _ : constant(_), sankey) : align;\n  };\n\n  sankey.nodeSort = function(_) {\n    return arguments.length ? (sort = _, sankey) : sort;\n  };\n\n  sankey.nodeWidth = function(_) {\n    return arguments.length ? (dx = +_, sankey) : dx;\n  };\n\n  sankey.nodePadding = function(_) {\n    return arguments.length ? (dy = py = +_, sankey) : dy;\n  };\n\n  sankey.nodes = function(_) {\n    return arguments.length ? (nodes = typeof _ === \"function\" ? _ : constant(_), sankey) : nodes;\n  };\n\n  sankey.links = function(_) {\n    return arguments.length ? (links = typeof _ === \"function\" ? _ : constant(_), sankey) : links;\n  };\n\n  sankey.linkSort = function(_) {\n    return arguments.length ? (linkSort = _, sankey) : linkSort;\n  };\n\n  sankey.size = function(_) {\n    return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], sankey) : [x1 - x0, y1 - y0];\n  };\n\n  sankey.extent = function(_) {\n    return arguments.length ? (x0 = +_[0][0], x1 = +_[1][0], y0 = +_[0][1], y1 = +_[1][1], sankey) : [[x0, y0], [x1, y1]];\n  };\n\n  sankey.iterations = function(_) {\n    return arguments.length ? (iterations = +_, sankey) : iterations;\n  };\n\n  function computeNodeLinks({nodes, links}) {\n    for (const [i, node] of nodes.entries()) {\n      node.index = i;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    }\n    const nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d]));\n    for (const [i, link] of links.entries()) {\n      link.index = i;\n      let {source, target} = link;\n      if (typeof source !== \"object\") source = link.source = find(nodeById, source);\n      if (typeof target !== \"object\") target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    }\n    if (linkSort != null) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n\n  function computeNodeValues({nodes}) {\n    for (const node of nodes) {\n      node.value = node.fixedValue === undefined\n          ? Math.max(d3Array.sum(node.sourceLinks, value), d3Array.sum(node.targetLinks, value))\n          : node.fixedValue;\n    }\n  }\n\n  function computeNodeDepths({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.depth = x;\n        for (const {target} of node.sourceLinks) {\n          next.add(target);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeHeights({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.height = x;\n        for (const {source} of node.targetLinks) {\n          next.add(source);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeLayers({nodes}) {\n    const x = d3Array.max(nodes, d => d.depth) + 1;\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x);\n    for (const node of nodes) {\n      const i = Math.max(0, Math.min(x - 1, Math.floor(align.call(null, node, x))));\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);\n      else columns[i] = [node];\n    }\n    if (sort) for (const column of columns) {\n      column.sort(sort);\n    }\n    return columns;\n  }\n\n  function initializeNodeBreadths(columns) {\n    const ky = d3Array.min(columns, c => (y1 - y0 - (c.length - 1) * py) / d3Array.sum(c, value));\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(dy, (y1 - y0) / (d3Array.max(columns, c => c.length) - 1));\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const {source, value} of target.targetLinks) {\n          let v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const {target, value} of source.sourceLinks) {\n          let v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) node.y0 += dy, node.y1 += dy;\n      y = node.y1 + py;\n    }\n  }\n\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) node.y0 -= dy, node.y1 -= dy;\n      y = node.y0 - py;\n    }\n  }\n\n  function reorderNodeLinks({sourceLinks, targetLinks}) {\n    if (linkSort === undefined) {\n      for (const {source: {sourceLinks}} of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {target: {targetLinks}} of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - (source.sourceLinks.length - 1) * py / 2;\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - (target.targetLinks.length - 1) * py / 2;\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  return sankey;\n}\n\nfunction horizontalSource(d) {\n  return [d.source.x1, d.y0];\n}\n\nfunction horizontalTarget(d) {\n  return [d.target.x0, d.y1];\n}\n\nfunction sankeyLinkHorizontal() {\n  return d3Shape.linkHorizontal()\n      .source(horizontalSource)\n      .target(horizontalTarget);\n}\n\nexports.sankey = Sankey;\nexports.sankeyCenter = center;\nexports.sankeyJustify = justify;\nexports.sankeyLeft = left;\nexports.sankeyLinkHorizontal = sankeyLinkHorizontal;\nexports.sankeyRight = right;\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\n}));\n"], "mappings": ";;;;;AAAA;AAAA;AACA,KAAC,SAAU,QAAQ,SAAS;AAC5B,aAAO,YAAY,YAAY,OAAO,WAAW,cAAc,QAAQ,OAAO,IAC9E,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,CAAC,SAAS,GAAG,OAAO,KACvE,SAAS,OAAO,eAAe,cAAc,aAAa,UAAU,MAAM,QAAQ,OAAO,KAAK,OAAO,MAAM,CAAC,CAAC;AAAA,IAC9G,GAAE,SAAO,SAAUA,UAAS;AAAE;AAE9B,eAAS,UAAU,GAAG,GAAG;AACvB,eAAO,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAAA,MAC/C;AAEA,eAAS,SAAS,GAAG;AACnB,YAAI,QAAQ;AACZ,YAAI,UAAU;AAEd,YAAI,EAAE,WAAW,GAAG;AAClB,kBAAQ,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI;AACzB,oBAAU,oBAAoB,CAAC;AAAA,QACjC;AAEA,iBAAS,KAAK,GAAG,GAAG,IAAI,IAAI;AAC1B,cAAI,MAAM,KAAM,MAAK;AACrB,cAAI,MAAM,KAAM,MAAK,EAAE;AACvB,iBAAO,KAAK,IAAI;AACd,kBAAM,MAAO,KAAK,OAAQ;AAC1B,gBAAI,QAAQ,EAAE,GAAG,GAAG,CAAC,IAAI,EAAG,MAAK,MAAM;AAAA,gBAClC,MAAK;AAAA,UACZ;AACA,iBAAO;AAAA,QACT;AAEA,iBAAS,MAAM,GAAG,GAAG,IAAI,IAAI;AAC3B,cAAI,MAAM,KAAM,MAAK;AACrB,cAAI,MAAM,KAAM,MAAK,EAAE;AACvB,iBAAO,KAAK,IAAI;AACd,kBAAM,MAAO,KAAK,OAAQ;AAC1B,gBAAI,QAAQ,EAAE,GAAG,GAAG,CAAC,IAAI,EAAG,MAAK;AAAA,gBAC5B,MAAK,MAAM;AAAA,UAClB;AACA,iBAAO;AAAA,QACT;AAEA,iBAAS,OAAO,GAAG,GAAG,IAAI,IAAI;AAC5B,cAAI,MAAM,KAAM,MAAK;AACrB,cAAI,MAAM,KAAM,MAAK,EAAE;AACvB,gBAAM,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC;AAC/B,iBAAO,IAAI,MAAM,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI;AAAA,QAClE;AAEA,eAAO,EAAC,MAAM,QAAQ,MAAK;AAAA,MAC7B;AAEA,eAAS,oBAAoB,GAAG;AAC9B,eAAO,CAAC,GAAG,MAAM,UAAU,EAAE,CAAC,GAAG,CAAC;AAAA,MACpC;AAEA,eAAS,OAAO,GAAG;AACjB,eAAO,MAAM,OAAO,MAAM,CAAC;AAAA,MAC7B;AAEA,gBAAU,QAAQ,QAAQ,SAAS;AACjC,YAAI,YAAY,QAAW;AACzB,mBAAS,SAAS,QAAQ;AACxB,gBAAI,SAAS,SAAS,QAAQ,CAAC,UAAU,OAAO;AAC9C,oBAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAIC,SAAQ;AACZ,mBAAS,SAAS,QAAQ;AACxB,iBAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SAAS,QAAQ,CAAC,UAAU,OAAO;AAClF,oBAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,kBAAkB,SAAS,SAAS;AAC1C,YAAM,cAAc,gBAAgB;AACpC,YAAM,aAAa,gBAAgB;AACnC,YAAM,eAAe,SAAS,MAAM,EAAE;AAEtC,eAAS,MAAM,QAAQ,SAAS;AAC9B,YAAIC,SAAQ;AACZ,YAAI,YAAY,QAAW;AACzB,mBAAS,SAAS,QAAQ;AACxB,gBAAI,SAAS,SAAS,QAAQ,CAAC,UAAU,OAAO;AAC9C,gBAAEA;AAAA,YACJ;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAID,SAAQ;AACZ,mBAAS,SAAS,QAAQ;AACxB,iBAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SAAS,QAAQ,CAAC,UAAU,OAAO;AAClF,gBAAEC;AAAA,YACJ;AAAA,UACF;AAAA,QACF;AACA,eAAOA;AAAA,MACT;AAEA,eAAS,SAASC,QAAO;AACvB,eAAOA,OAAM,SAAS;AAAA,MACxB;AAEA,eAAS,MAAMC,SAAQ;AACrB,eAAO,EAAEA,UAAS;AAAA,MACpB;AAEA,eAAS,SAAS,QAAQ;AACxB,eAAO,OAAO,WAAW,YAAY,YAAY,SAAS,SAAS,MAAM,KAAK,MAAM;AAAA,MACtF;AAEA,eAAS,QAAQC,SAAQ;AACvB,eAAO,YAAUA,QAAO,GAAG,MAAM;AAAA,MACnC;AAEA,eAAS,SAAS,QAAQ;AACxB,cAAMA,UAAS,OAAO,OAAO,OAAO,SAAS,CAAC,MAAM,cAAc,QAAQ,OAAO,IAAI,CAAC;AACtF,iBAAS,OAAO,IAAI,QAAQ;AAC5B,cAAM,UAAU,OAAO,IAAI,QAAQ;AACnC,cAAM,IAAI,OAAO,SAAS;AAC1B,cAAMJ,SAAQ,IAAI,MAAM,IAAI,CAAC,EAAE,KAAK,CAAC;AACrC,cAAM,UAAU,CAAC;AACjB,YAAI,IAAI,KAAK,QAAQ,KAAK,KAAK,EAAG,QAAO;AACzC,eAAO,MAAM;AACX,kBAAQ,KAAKA,OAAM,IAAI,CAACK,IAAGC,OAAM,OAAOA,EAAC,EAAED,EAAC,CAAC,CAAC;AAC9C,cAAI,IAAI;AACR,iBAAO,EAAEL,OAAM,CAAC,MAAM,QAAQ,CAAC,GAAG;AAChC,gBAAI,MAAM,EAAG,QAAOI,UAAS,QAAQ,IAAIA,OAAM,IAAI;AACnD,YAAAJ,OAAM,GAAG,IAAI;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAEA,eAAS,OAAO,QAAQ,SAAS;AAC/B,YAAIO,OAAM,GAAGP,SAAQ;AACrB,eAAO,aAAa,KAAK,QAAQ,YAAY,SACzC,OAAMO,QAAO,CAAC,KAAK,IACnB,OAAMA,QAAO,CAAC,QAAQ,GAAGP,UAAS,MAAM,KAAK,CAAE;AAAA,MACrD;AAEA,eAAS,WAAW,GAAG,GAAG;AACxB,eAAO,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAAA,MAC/C;AAEA,eAAS,SAAS,QAAQ,SAAS;AACjC,YAAIC,SAAQ;AACZ,YAAI;AACJ,YAAIO,QAAO;AACX,YAAID,OAAM;AACV,YAAI,YAAY,QAAW;AACzB,mBAAS,SAAS,QAAQ;AACxB,gBAAI,SAAS,SAAS,QAAQ,CAAC,UAAU,OAAO;AAC9C,sBAAQ,QAAQC;AAChB,cAAAA,SAAQ,QAAQ,EAAEP;AAClB,cAAAM,QAAO,SAAS,QAAQC;AAAA,YAC1B;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAIR,SAAQ;AACZ,mBAAS,SAAS,QAAQ;AACxB,iBAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SAAS,QAAQ,CAAC,UAAU,OAAO;AAClF,sBAAQ,QAAQQ;AAChB,cAAAA,SAAQ,QAAQ,EAAEP;AAClB,cAAAM,QAAO,SAAS,QAAQC;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AACA,YAAIP,SAAQ,EAAG,QAAOM,QAAON,SAAQ;AAAA,MACvC;AAEA,eAAS,UAAU,QAAQ,SAAS;AAClC,cAAM,IAAI,SAAS,QAAQ,OAAO;AAClC,eAAO,IAAI,KAAK,KAAK,CAAC,IAAI;AAAA,MAC5B;AAEA,eAAS,OAAO,QAAQ,SAAS;AAC/B,YAAIQ;AACJ,YAAIC;AACJ,YAAI,YAAY,QAAW;AACzB,qBAAW,SAAS,QAAQ;AAC1B,gBAAI,SAAS,MAAM;AACjB,kBAAID,SAAQ,QAAW;AACrB,oBAAI,SAAS,MAAO,CAAAA,OAAMC,OAAM;AAAA,cAClC,OAAO;AACL,oBAAID,OAAM,MAAO,CAAAA,OAAM;AACvB,oBAAIC,OAAM,MAAO,CAAAA,OAAM;AAAA,cACzB;AAAA,YACF;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAIV,SAAQ;AACZ,mBAAS,SAAS,QAAQ;AACxB,iBAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,MAAM;AACrD,kBAAIS,SAAQ,QAAW;AACrB,oBAAI,SAAS,MAAO,CAAAA,OAAMC,OAAM;AAAA,cAClC,OAAO;AACL,oBAAID,OAAM,MAAO,CAAAA,OAAM;AACvB,oBAAIC,OAAM,MAAO,CAAAA,OAAM;AAAA,cACzB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,eAAO,CAACD,MAAKC,IAAG;AAAA,MAClB;AAAA,MAGA,MAAM,MAAM;AAAA,QACV,cAAc;AACZ,eAAK,YAAY,IAAI,aAAa,EAAE;AACpC,eAAK,KAAK;AAAA,QACZ;AAAA,QACA,IAAI,GAAG;AACL,gBAAM,IAAI,KAAK;AACf,cAAI,IAAI;AACR,mBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK;AAC1C,kBAAM,IAAI,EAAE,CAAC,GACX,KAAK,IAAI,GACT,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5D,gBAAI,GAAI,GAAE,GAAG,IAAI;AACjB,gBAAI;AAAA,UACN;AACA,YAAE,CAAC,IAAI;AACP,eAAK,KAAK,IAAI;AACd,iBAAO;AAAA,QACT;AAAA,QACA,UAAU;AACR,gBAAM,IAAI,KAAK;AACf,cAAI,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI,KAAK;AAChC,cAAI,IAAI,GAAG;AACT,iBAAK,EAAE,EAAE,CAAC;AACV,mBAAO,IAAI,GAAG;AACZ,kBAAI;AACJ,kBAAI,EAAE,EAAE,CAAC;AACT,mBAAK,IAAI;AACT,mBAAK,KAAK,KAAK;AACf,kBAAI,GAAI;AAAA,YACV;AACA,gBAAI,IAAI,MAAO,KAAK,KAAK,EAAE,IAAI,CAAC,IAAI,KAAO,KAAK,KAAK,EAAE,IAAI,CAAC,IAAI,IAAK;AACnE,kBAAI,KAAK;AACT,kBAAI,KAAK;AACT,kBAAI,KAAK,IAAI,GAAI,MAAK;AAAA,YACxB;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,KAAK,QAAQ,SAAS;AAC7B,cAAM,QAAQ,IAAI,MAAM;AACxB,YAAI,YAAY,QAAW;AACzB,mBAAS,SAAS,QAAQ;AACxB,gBAAI,QAAQ,CAAC,OAAO;AAClB,oBAAM,IAAI,KAAK;AAAA,YACjB;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAIV,SAAQ;AACZ,mBAAS,SAAS,QAAQ;AACxB,gBAAI,QAAQ,CAAC,QAAQ,OAAO,EAAEA,QAAO,MAAM,GAAG;AAC5C,oBAAM,IAAI,KAAK;AAAA,YACjB;AAAA,UACF;AAAA,QACF;AACA,eAAO,CAAC;AAAA,MACV;AAEA,eAAS,QAAQ,QAAQ,SAAS;AAChC,cAAM,QAAQ,IAAI,MAAM;AACxB,YAAIA,SAAQ;AACZ,eAAO,aAAa;AAAA,UAAK;AAAA,UAAQ,YAAY,SACvC,OAAK,MAAM,IAAI,CAAC,KAAK,CAAC,IACtB,OAAK,MAAM,IAAI,CAAC,QAAQ,GAAG,EAAEA,QAAO,MAAM,KAAK,CAAC;AAAA,QACtD;AAAA,MACF;AAAA,MAEA,MAAM,kBAAkB,IAAI;AAAA,QAC1B,YAAY,SAAS,MAAM,OAAO;AAChC,gBAAM;AACN,iBAAO,iBAAiB,MAAM,EAAC,SAAS,EAAC,OAAO,oBAAI,IAAI,EAAC,GAAG,MAAM,EAAC,OAAO,IAAG,EAAC,CAAC;AAC/E,cAAI,WAAW,KAAM,YAAW,CAACW,MAAK,KAAK,KAAK,QAAS,MAAK,IAAIA,MAAK,KAAK;AAAA,QAC9E;AAAA,QACA,IAAI,KAAK;AACP,iBAAO,MAAM,IAAI,WAAW,MAAM,GAAG,CAAC;AAAA,QACxC;AAAA,QACA,IAAI,KAAK;AACP,iBAAO,MAAM,IAAI,WAAW,MAAM,GAAG,CAAC;AAAA,QACxC;AAAA,QACA,IAAI,KAAK,OAAO;AACd,iBAAO,MAAM,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK;AAAA,QAC/C;AAAA,QACA,OAAO,KAAK;AACV,iBAAO,MAAM,OAAO,cAAc,MAAM,GAAG,CAAC;AAAA,QAC9C;AAAA,MACF;AAAA,MAEA,MAAM,kBAAkB,IAAI;AAAA,QAC1B,YAAY,QAAQ,MAAM,OAAO;AAC/B,gBAAM;AACN,iBAAO,iBAAiB,MAAM,EAAC,SAAS,EAAC,OAAO,oBAAI,IAAI,EAAC,GAAG,MAAM,EAAC,OAAO,IAAG,EAAC,CAAC;AAC/E,cAAI,UAAU,KAAM,YAAW,SAAS,OAAQ,MAAK,IAAI,KAAK;AAAA,QAChE;AAAA,QACA,IAAI,OAAO;AACT,iBAAO,MAAM,IAAI,WAAW,MAAM,KAAK,CAAC;AAAA,QAC1C;AAAA,QACA,IAAI,OAAO;AACT,iBAAO,MAAM,IAAI,WAAW,MAAM,KAAK,CAAC;AAAA,QAC1C;AAAA,QACA,OAAO,OAAO;AACZ,iBAAO,MAAM,OAAO,cAAc,MAAM,KAAK,CAAC;AAAA,QAChD;AAAA,MACF;AAEA,eAAS,WAAW,EAAC,SAAS,KAAI,GAAG,OAAO;AAC1C,cAAM,MAAM,KAAK,KAAK;AACtB,eAAO,QAAQ,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,IAAI;AAAA,MAC/C;AAEA,eAAS,WAAW,EAAC,SAAS,KAAI,GAAG,OAAO;AAC1C,cAAM,MAAM,KAAK,KAAK;AACtB,YAAI,QAAQ,IAAI,GAAG,EAAG,QAAO,QAAQ,IAAI,GAAG;AAC5C,gBAAQ,IAAI,KAAK,KAAK;AACtB,eAAO;AAAA,MACT;AAEA,eAAS,cAAc,EAAC,SAAS,KAAI,GAAG,OAAO;AAC7C,cAAM,MAAM,KAAK,KAAK;AACtB,YAAI,QAAQ,IAAI,GAAG,GAAG;AACpB,kBAAQ,QAAQ,IAAI,KAAK;AACzB,kBAAQ,OAAO,GAAG;AAAA,QACpB;AACA,eAAO;AAAA,MACT;AAEA,eAAS,MAAM,OAAO;AACpB,eAAO,UAAU,QAAQ,OAAO,UAAU,WAAW,MAAM,QAAQ,IAAI;AAAA,MACzE;AAEA,eAAS,SAAS,GAAG;AACnB,eAAO;AAAA,MACT;AAEA,eAAS,MAAM,WAAW,MAAM;AAC9B,eAAO,KAAK,QAAQ,UAAU,UAAU,IAAI;AAAA,MAC9C;AAEA,eAAS,OAAO,WAAW,MAAM;AAC/B,eAAO,KAAK,QAAQ,MAAM,MAAM,UAAU,IAAI;AAAA,MAChD;AAEA,eAAS,OAAO,QAAQP,YAAW,MAAM;AACvC,eAAO,KAAK,QAAQ,UAAUA,SAAQ,IAAI;AAAA,MAC5C;AAEA,eAAS,QAAQ,QAAQA,YAAW,MAAM;AACxC,eAAO,KAAK,QAAQ,MAAM,MAAMA,SAAQ,IAAI;AAAA,MAC9C;AAEA,eAAS,MAAM,WAAW,MAAM;AAC9B,eAAO,KAAK,QAAQ,UAAU,QAAQ,IAAI;AAAA,MAC5C;AAEA,eAAS,QAAQ,WAAW,MAAM;AAChC,eAAO,KAAK,QAAQ,MAAM,MAAM,QAAQ,IAAI;AAAA,MAC9C;AAEA,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,WAAW,EAAG,OAAM,IAAI,MAAM,eAAe;AACxD,eAAO,OAAO,CAAC;AAAA,MACjB;AAEA,eAAS,KAAK,QAAQQ,MAAKR,SAAQ,MAAM;AACvC,eAAQ,SAAS,QAAQS,SAAQ,GAAG;AAClC,cAAI,KAAK,KAAK,OAAQ,QAAOT,QAAOS,OAAM;AAC1C,gBAAMC,UAAS,IAAI,UAAU;AAC7B,gBAAMC,SAAQ,KAAK,GAAG;AACtB,cAAIf,SAAQ;AACZ,qBAAW,SAASa,SAAQ;AAC1B,kBAAM,MAAME,OAAM,OAAO,EAAEf,QAAOa,OAAM;AACxC,kBAAMG,SAAQF,QAAO,IAAI,GAAG;AAC5B,gBAAIE,OAAO,CAAAA,OAAM,KAAK,KAAK;AAAA,gBACtB,CAAAF,QAAO,IAAI,KAAK,CAAC,KAAK,CAAC;AAAA,UAC9B;AACA,qBAAW,CAAC,KAAKD,OAAM,KAAKC,SAAQ;AAClC,YAAAA,QAAO,IAAI,KAAK,QAAQD,SAAQ,CAAC,CAAC;AAAA,UACpC;AACA,iBAAOD,KAAIE,OAAM;AAAA,QACnB,EAAG,QAAQ,CAAC;AAAA,MACd;AAEA,eAAS,QAAQ,QAAQ,MAAM;AAC7B,eAAO,MAAM,KAAK,MAAM,SAAO,OAAO,GAAG,CAAC;AAAA,MAC5C;AAEA,eAAS,KAAK,WAAW,GAAG;AAC1B,YAAI,OAAO,OAAO,OAAO,QAAQ,MAAM,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/F,iBAAS,MAAM,KAAK,MAAM;AAC1B,YAAI,CAAC,IAAI,SAAS,IAAI;AACtB,YAAI,EAAE,WAAW,KAAK,EAAE,SAAS,GAAG;AAClC,gBAAMd,SAAQ,YAAY,KAAK,QAAQ,CAAC,GAAG,MAAM,CAAC;AAClD,cAAI,EAAE,SAAS,GAAG;AAChB,gBAAI,EAAE,IAAI,CAAAiB,OAAK,OAAO,IAAIA,EAAC,CAAC;AAC5B,YAAAjB,OAAM,KAAK,CAAC,GAAG,MAAM;AACnB,yBAAWiB,MAAK,GAAG;AACjB,sBAAM,IAAI,UAAUA,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AAC9B,oBAAI,EAAG,QAAO;AAAA,cAChB;AAAA,YACF,CAAC;AAAA,UACH,OAAO;AACL,gBAAI,OAAO,IAAI,CAAC;AAChB,YAAAjB,OAAM,KAAK,CAAC,GAAG,MAAM,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAAA,UAC5C;AACA,iBAAO,QAAQ,QAAQA,MAAK;AAAA,QAC9B;AACA,eAAO,OAAO,KAAK,CAAC;AAAA,MACtB;AAEA,eAAS,UAAU,QAAQI,SAAQ,KAAK;AACtC,gBAAQA,QAAO,WAAW,IACtB,KAAK,OAAO,QAAQA,SAAQ,GAAG,GAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM,UAAU,IAAI,EAAE,KAAK,UAAU,IAAI,EAAE,CAAE,IAClG,KAAK,MAAM,QAAQ,GAAG,GAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAMA,QAAO,IAAI,EAAE,KAAK,UAAU,IAAI,EAAE,CAAE,GACvF,IAAI,CAAC,CAACO,IAAG,MAAMA,IAAG;AAAA,MACvB;AAEA,UAAI,QAAQ,MAAM;AAElB,UAAI,QAAQ,MAAM;AAElB,eAAS,SAAS,GAAG;AACnB,eAAO,WAAW;AAChB,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,MAAM,KAAK,KAAK,EAAE,GAClB,KAAK,KAAK,KAAK,EAAE,GACjB,KAAK,KAAK,KAAK,CAAC;AAEpB,eAAS,MAAM,OAAO,MAAMV,QAAO;AACjC,YAAIiB,UACA,IAAI,IACJ,GACAC,QACA;AAEJ,eAAO,CAAC,MAAM,QAAQ,CAAC,OAAOlB,SAAQ,CAACA;AACvC,YAAI,UAAU,QAAQA,SAAQ,EAAG,QAAO,CAAC,KAAK;AAC9C,YAAIiB,WAAU,OAAO,MAAO,KAAI,OAAO,QAAQ,MAAM,OAAO;AAC5D,aAAK,OAAO,cAAc,OAAO,MAAMjB,MAAK,OAAO,KAAK,CAAC,SAAS,IAAI,EAAG,QAAO,CAAC;AAEjF,YAAI,OAAO,GAAG;AACZ,cAAI,KAAK,KAAK,MAAM,QAAQ,IAAI,GAAG,KAAK,KAAK,MAAM,OAAO,IAAI;AAC9D,cAAI,KAAK,OAAO,MAAO,GAAE;AACzB,cAAI,KAAK,OAAO,KAAM,GAAE;AACxB,UAAAkB,SAAQ,IAAI,MAAM,IAAI,KAAK,KAAK,CAAC;AACjC,iBAAO,EAAE,IAAI,EAAG,CAAAA,OAAM,CAAC,KAAK,KAAK,KAAK;AAAA,QACxC,OAAO;AACL,iBAAO,CAAC;AACR,cAAI,KAAK,KAAK,MAAM,QAAQ,IAAI,GAAG,KAAK,KAAK,MAAM,OAAO,IAAI;AAC9D,cAAI,KAAK,OAAO,MAAO,GAAE;AACzB,cAAI,KAAK,OAAO,KAAM,GAAE;AACxB,UAAAA,SAAQ,IAAI,MAAM,IAAI,KAAK,KAAK,CAAC;AACjC,iBAAO,EAAE,IAAI,EAAG,CAAAA,OAAM,CAAC,KAAK,KAAK,KAAK;AAAA,QACxC;AAEA,YAAID,SAAS,CAAAC,OAAM,QAAQ;AAE3B,eAAOA;AAAA,MACT;AAEA,eAAS,cAAc,OAAO,MAAMlB,QAAO;AACzC,YAAI,QAAQ,OAAO,SAAS,KAAK,IAAI,GAAGA,MAAK,GACzC,QAAQ,KAAK,MAAM,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,GAC7C,QAAQ,OAAO,KAAK,IAAI,IAAI,KAAK;AACrC,eAAO,SAAS,KACT,SAAS,MAAM,KAAK,SAAS,KAAK,IAAI,SAAS,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAChF,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,MAAM,KAAK,SAAS,KAAK,IAAI,SAAS,KAAK,IAAI;AAAA,MACzF;AAEA,eAAS,SAAS,OAAO,MAAMA,QAAO;AACpC,YAAI,QAAQ,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,IAAI,GAAGA,MAAK,GAClD,QAAQ,KAAK,IAAI,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,GAC5D,QAAQ,QAAQ;AACpB,YAAI,SAAS,IAAK,UAAS;AAAA,iBAClB,SAAS,GAAI,UAAS;AAAA,iBACtB,SAAS,GAAI,UAAS;AAC/B,eAAO,OAAO,QAAQ,CAAC,QAAQ;AAAA,MACjC;AAEA,eAAS,KAAK,OAAO,MAAMA,QAAO;AAChC,YAAI;AACJ,eAAO,MAAM;AACX,gBAAM,OAAO,cAAc,OAAO,MAAMA,MAAK;AAC7C,cAAI,SAAS,WAAW,SAAS,KAAK,CAAC,SAAS,IAAI,GAAG;AACrD,mBAAO,CAAC,OAAO,IAAI;AAAA,UACrB,WAAW,OAAO,GAAG;AACnB,oBAAQ,KAAK,MAAM,QAAQ,IAAI,IAAI;AACnC,mBAAO,KAAK,KAAK,OAAO,IAAI,IAAI;AAAA,UAClC,WAAW,OAAO,GAAG;AACnB,oBAAQ,KAAK,KAAK,QAAQ,IAAI,IAAI;AAClC,mBAAO,KAAK,MAAM,OAAO,IAAI,IAAI;AAAA,UACnC;AACA,oBAAU;AAAA,QACZ;AAAA,MACF;AAEA,eAAS,QAAQ,QAAQ;AACvB,eAAO,KAAK,KAAK,KAAK,IAAI,MAAM,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI;AAAA,MACzD;AAEA,eAAS,MAAM;AACb,YAAI,QAAQ,UACR,SAAS,QACT,YAAY;AAEhB,iBAAS,UAAU,MAAM;AACvB,cAAI,CAAC,MAAM,QAAQ,IAAI,EAAG,QAAO,MAAM,KAAK,IAAI;AAEhD,cAAI,GACA,IAAI,KAAK,QACT,GACA,SAAS,IAAI,MAAM,CAAC;AAExB,eAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,mBAAO,CAAC,IAAI,MAAM,KAAK,CAAC,GAAG,GAAG,IAAI;AAAA,UACpC;AAEA,cAAI,KAAK,OAAO,MAAM,GAClB,KAAK,GAAG,CAAC,GACT,KAAK,GAAG,CAAC,GACT,KAAK,UAAU,QAAQ,IAAI,EAAE;AAIjC,cAAI,CAAC,MAAM,QAAQ,EAAE,GAAG;AACtB,kBAAMS,OAAM,IAAI,KAAK,CAAC;AACtB,gBAAI,WAAW,OAAQ,EAAC,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI,EAAE;AACjD,iBAAK,MAAM,IAAI,IAAI,EAAE;AASrB,gBAAI,GAAG,GAAG,SAAS,CAAC,KAAK,IAAI;AAC3B,kBAAIA,QAAO,MAAM,WAAW,QAAQ;AAClC,sBAAM,OAAO,cAAc,IAAI,IAAI,EAAE;AACrC,oBAAI,SAAS,IAAI,GAAG;AAClB,sBAAI,OAAO,GAAG;AACZ,0BAAM,KAAK,MAAM,KAAK,IAAI,IAAI,KAAK;AAAA,kBACrC,WAAW,OAAO,GAAG;AACnB,0BAAM,KAAK,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC;AAAA,kBACtC;AAAA,gBACF;AAAA,cACF,OAAO;AACL,mBAAG,IAAI;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAGA,cAAI,IAAI,GAAG;AACX,iBAAO,GAAG,CAAC,KAAK,GAAI,IAAG,MAAM,GAAG,EAAE;AAClC,iBAAO,GAAG,IAAI,CAAC,IAAI,GAAI,IAAG,IAAI,GAAG,EAAE;AAEnC,cAAI,OAAO,IAAI,MAAM,IAAI,CAAC,GACtBU;AAGJ,eAAK,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACvB,YAAAA,OAAM,KAAK,CAAC,IAAI,CAAC;AACjB,YAAAA,KAAI,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;AAC7B,YAAAA,KAAI,KAAK,IAAI,IAAI,GAAG,CAAC,IAAI;AAAA,UAC3B;AAGA,eAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,gBAAI,OAAO,CAAC;AACZ,gBAAI,MAAM,KAAK,KAAK,IAAI;AACtB,mBAAK,YAAY,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAAA,YAC7C;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,kBAAU,QAAQ,SAAS,GAAG;AAC5B,iBAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,aAAa;AAAA,QAC7F;AAEA,kBAAU,SAAS,SAAS,GAAG;AAC7B,iBAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,aAAa;AAAA,QACzG;AAEA,kBAAU,aAAa,SAAS,GAAG;AACjC,iBAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAI,MAAM,QAAQ,CAAC,IAAI,SAAS,MAAM,KAAK,CAAC,CAAC,IAAI,SAAS,CAAC,GAAG,aAAa;AAAA,QAC9I;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,IAAI,QAAQ,SAAS;AAC5B,YAAIV;AACJ,YAAI,YAAY,QAAW;AACzB,qBAAW,SAAS,QAAQ;AAC1B,gBAAI,SAAS,SACLA,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,cAAAA,OAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAIV,SAAQ;AACZ,mBAAS,SAAS,QAAQ;AACxB,iBAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SACzCU,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,cAAAA,OAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AACA,eAAOA;AAAA,MACT;AAEA,eAAS,IAAI,QAAQ,SAAS;AAC5B,YAAID;AACJ,YAAI,YAAY,QAAW;AACzB,qBAAW,SAAS,QAAQ;AAC1B,gBAAI,SAAS,SACLA,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,cAAAA,OAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAIT,SAAQ;AACZ,mBAAS,SAAS,QAAQ;AACxB,iBAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SACzCS,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,cAAAA,OAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AACA,eAAOA;AAAA,MACT;AAIA,eAAS,YAAYP,QAAO,GAAG,OAAO,GAAG,QAAQA,OAAM,SAAS,GAAG,UAAU,WAAW;AACtF,eAAO,QAAQ,MAAM;AACnB,cAAI,QAAQ,OAAO,KAAK;AACtB,kBAAM,IAAI,QAAQ,OAAO;AACzB,kBAAM,IAAI,IAAI,OAAO;AACrB,kBAAM,IAAI,KAAK,IAAI,CAAC;AACpB,kBAAM,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC;AAClC,kBAAM,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACxE,kBAAM,UAAU,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AAC7D,kBAAM,WAAW,KAAK,IAAI,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AACrE,wBAAYA,QAAO,GAAG,SAAS,UAAU,OAAO;AAAA,UAClD;AAEA,gBAAM,IAAIA,OAAM,CAAC;AACjB,cAAI,IAAI;AACR,cAAI,IAAI;AAER,eAAKA,QAAO,MAAM,CAAC;AACnB,cAAI,QAAQA,OAAM,KAAK,GAAG,CAAC,IAAI,EAAG,MAAKA,QAAO,MAAM,KAAK;AAEzD,iBAAO,IAAI,GAAG;AACZ,iBAAKA,QAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE;AAC1B,mBAAO,QAAQA,OAAM,CAAC,GAAG,CAAC,IAAI,EAAG,GAAE;AACnC,mBAAO,QAAQA,OAAM,CAAC,GAAG,CAAC,IAAI,EAAG,GAAE;AAAA,UACrC;AAEA,cAAI,QAAQA,OAAM,IAAI,GAAG,CAAC,MAAM,EAAG,MAAKA,QAAO,MAAM,CAAC;AAAA,cACjD,GAAE,GAAG,KAAKA,QAAO,GAAG,KAAK;AAE9B,cAAI,KAAK,EAAG,QAAO,IAAI;AACvB,cAAI,KAAK,EAAG,SAAQ,IAAI;AAAA,QAC1B;AACA,eAAOA;AAAA,MACT;AAEA,eAAS,KAAKA,QAAO,GAAG,GAAG;AACzB,cAAM,IAAIA,OAAM,CAAC;AACjB,QAAAA,OAAM,CAAC,IAAIA,OAAM,CAAC;AAClB,QAAAA,OAAM,CAAC,IAAI;AAAA,MACb;AAEA,eAAS,SAAS,QAAQ,GAAG,SAAS;AACpC,iBAAS,aAAa,KAAK,QAAQ,QAAQ,OAAO,CAAC;AACnD,YAAI,EAAE,IAAI,OAAO,QAAS;AAC1B,aAAK,IAAI,CAAC,MAAM,KAAK,IAAI,EAAG,QAAO,IAAI,MAAM;AAC7C,YAAI,KAAK,EAAG,QAAO,IAAI,MAAM;AAC7B,YAAI,GACA,KAAK,IAAI,KAAK,GACd,KAAK,KAAK,MAAM,CAAC,GACjB,SAAS,IAAI,YAAY,QAAQ,EAAE,EAAE,SAAS,GAAG,KAAK,CAAC,CAAC,GACxD,SAAS,IAAI,OAAO,SAAS,KAAK,CAAC,CAAC;AACxC,eAAO,UAAU,SAAS,WAAW,IAAI;AAAA,MAC3C;AAEA,eAAS,eAAe,QAAQ,GAAG,UAAU,QAAQ;AACnD,YAAI,EAAE,IAAI,OAAO,QAAS;AAC1B,aAAK,IAAI,CAAC,MAAM,KAAK,IAAI,EAAG,QAAO,CAAC,QAAQ,OAAO,CAAC,GAAG,GAAG,MAAM;AAChE,YAAI,KAAK,EAAG,QAAO,CAAC,QAAQ,OAAO,IAAI,CAAC,GAAG,IAAI,GAAG,MAAM;AACxD,YAAI,GACA,KAAK,IAAI,KAAK,GACd,KAAK,KAAK,MAAM,CAAC,GACjB,SAAS,CAAC,QAAQ,OAAO,EAAE,GAAG,IAAI,MAAM,GACxC,SAAS,CAAC,QAAQ,OAAO,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;AACpD,eAAO,UAAU,SAAS,WAAW,IAAI;AAAA,MAC3C;AAEA,eAAS,iBAAiB,QAAQO,MAAKC,MAAK;AAC1C,eAAO,KAAK,MAAMA,OAAMD,SAAQ,KAAK,SAAS,QAAQ,IAAI,IAAI,SAAS,QAAQ,IAAI,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,EAAE;AAAA,MAC1H;AAEA,eAAS,MAAM,QAAQA,MAAKC,MAAK;AAC/B,eAAO,KAAK,MAAMA,OAAMD,SAAQ,MAAM,UAAU,MAAM,IAAI,KAAK,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,EAAE;AAAA,MAC5F;AAEA,eAAS,SAAS,QAAQ,SAAS;AACjC,YAAIC;AACJ,YAAIW,YAAW;AACf,YAAIrB,SAAQ;AACZ,YAAI,YAAY,QAAW;AACzB,qBAAW,SAAS,QAAQ;AAC1B,cAAEA;AACF,gBAAI,SAAS,SACLU,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,cAAAA,OAAM,OAAOW,YAAWrB;AAAA,YAC1B;AAAA,UACF;AAAA,QACF,OAAO;AACL,mBAAS,SAAS,QAAQ;AACxB,iBAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SACzCU,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,cAAAA,OAAM,OAAOW,YAAWrB;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AACA,eAAOqB;AAAA,MACT;AAEA,eAAS,KAAK,QAAQ,SAAS;AAC7B,YAAIpB,SAAQ;AACZ,YAAIM,OAAM;AACV,YAAI,YAAY,QAAW;AACzB,mBAAS,SAAS,QAAQ;AACxB,gBAAI,SAAS,SAAS,QAAQ,CAAC,UAAU,OAAO;AAC9C,gBAAEN,QAAOM,QAAO;AAAA,YAClB;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAIP,SAAQ;AACZ,mBAAS,SAAS,QAAQ;AACxB,iBAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SAAS,QAAQ,CAAC,UAAU,OAAO;AAClF,gBAAEC,QAAOM,QAAO;AAAA,YAClB;AAAA,UACF;AAAA,QACF;AACA,YAAIN,OAAO,QAAOM,OAAMN;AAAA,MAC1B;AAEA,eAAS,OAAO,QAAQ,SAAS;AAC/B,eAAO,SAAS,QAAQ,KAAK,OAAO;AAAA,MACtC;AAEA,gBAAU,QAAQ,QAAQ;AACxB,mBAAWC,UAAS,QAAQ;AAC1B,iBAAOA;AAAA,QACT;AAAA,MACF;AAEA,eAAS,MAAM,QAAQ;AACrB,eAAO,MAAM,KAAK,QAAQ,MAAM,CAAC;AAAA,MACnC;AAEA,eAAS,SAAS,QAAQ,SAAS;AACjC,YAAIO;AACJ,YAAIa,YAAW;AACf,YAAItB,SAAQ;AACZ,YAAI,YAAY,QAAW;AACzB,qBAAW,SAAS,QAAQ;AAC1B,cAAEA;AACF,gBAAI,SAAS,SACLS,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,cAAAA,OAAM,OAAOa,YAAWtB;AAAA,YAC1B;AAAA,UACF;AAAA,QACF,OAAO;AACL,mBAAS,SAAS,QAAQ;AACxB,iBAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SACzCS,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,cAAAA,OAAM,OAAOa,YAAWtB;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AACA,eAAOsB;AAAA,MACT;AAEA,eAAS,MAAM,QAAQ,SAAS,MAAM;AACpC,cAAMC,SAAQ,CAAC;AACf,YAAI;AACJ,YAAI,QAAQ;AACZ,mBAAW,SAAS,QAAQ;AAC1B,cAAI,MAAO,CAAAA,OAAM,KAAK,OAAO,UAAU,KAAK,CAAC;AAC7C,qBAAW;AACX,kBAAQ;AAAA,QACV;AACA,eAAOA;AAAA,MACT;AAEA,eAAS,KAAK,GAAG,GAAG;AAClB,eAAO,CAAC,GAAG,CAAC;AAAA,MACd;AAEA,eAAS,MAAM,OAAO,MAAM,MAAM;AAChC,gBAAQ,CAAC,OAAO,OAAO,CAAC,MAAM,QAAQ,IAAI,UAAU,UAAU,KAAK,OAAO,OAAO,QAAQ,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC;AAE9G,YAAI,IAAI,IACJ,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI,GACpDC,SAAQ,IAAI,MAAM,CAAC;AAEvB,eAAO,EAAE,IAAI,GAAG;AACd,UAAAA,OAAM,CAAC,IAAI,QAAQ,IAAI;AAAA,QACzB;AAEA,eAAOA;AAAA,MACT;AAEA,eAAS,MAAM,QAAQ,UAAU,WAAW;AAC1C,YAAIf;AACJ,YAAI,UAAU;AACd,YAAI,QAAQ,WAAW,GAAG;AACxB,cAAI;AACJ,qBAAW,WAAW,QAAQ;AAC5B,kBAAM,QAAQ,QAAQ,OAAO;AAC7B,gBAAI,UACE,UAAU,OAAO,QAAQ,IAAI,IAC7B,UAAU,OAAO,KAAK,MAAM,GAAG;AACnC,cAAAA,OAAM;AACN,yBAAW;AACX,wBAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF,OAAO;AACL,qBAAW,SAAS,QAAQ;AAC1B,gBAAI,UACE,QAAQ,OAAOA,IAAG,IAAI,IACtB,QAAQ,OAAO,KAAK,MAAM,GAAG;AACjC,cAAAA,OAAM;AACN,wBAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AACA,eAAOA;AAAA,MACT;AAEA,eAAS,WAAW,QAAQ,UAAU,WAAW;AAC/C,YAAI,QAAQ,WAAW,EAAG,QAAO,SAAS,QAAQ,OAAO;AACzD,YAAI;AACJ,YAAIA,OAAM;AACV,YAAIT,SAAQ;AACZ,mBAAW,SAAS,QAAQ;AAC1B,YAAEA;AACF,cAAIS,OAAM,IACJ,QAAQ,OAAO,KAAK,MAAM,IAC1B,QAAQ,OAAO,QAAQ,IAAI,GAAG;AAClC,uBAAW;AACX,YAAAA,OAAMT;AAAA,UACR;AAAA,QACF;AACA,eAAOS;AAAA,MACT;AAEA,eAAS,SAAS,QAAQ,UAAU,WAAW;AAC7C,YAAIC;AACJ,YAAI,UAAU;AACd,YAAI,QAAQ,WAAW,GAAG;AACxB,cAAI;AACJ,qBAAW,WAAW,QAAQ;AAC5B,kBAAM,QAAQ,QAAQ,OAAO;AAC7B,gBAAI,UACE,UAAU,OAAO,QAAQ,IAAI,IAC7B,UAAU,OAAO,KAAK,MAAM,GAAG;AACnC,cAAAA,OAAM;AACN,yBAAW;AACX,wBAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF,OAAO;AACL,qBAAW,SAAS,QAAQ;AAC1B,gBAAI,UACE,QAAQ,OAAOA,IAAG,IAAI,IACtB,QAAQ,OAAO,KAAK,MAAM,GAAG;AACjC,cAAAA,OAAM;AACN,wBAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AACA,eAAOA;AAAA,MACT;AAEA,eAAS,cAAc,QAAQ,UAAU,WAAW;AAClD,YAAI,QAAQ,WAAW,EAAG,QAAO,SAAS,QAAQ,OAAO;AACzD,YAAI;AACJ,YAAIA,OAAM;AACV,YAAIV,SAAQ;AACZ,mBAAW,SAAS,QAAQ;AAC1B,YAAEA;AACF,cAAIU,OAAM,IACJ,QAAQ,OAAO,KAAK,MAAM,IAC1B,QAAQ,OAAO,QAAQ,IAAI,GAAG;AAClC,uBAAW;AACX,YAAAA,OAAMV;AAAA,UACR;AAAA,QACF;AACA,eAAOU;AAAA,MACT;AAEA,eAAS,KAAK,QAAQ,SAAS;AAC7B,cAAMV,SAAQ,WAAW,QAAQ,OAAO;AACxC,eAAOA,SAAQ,IAAI,SAAYA;AAAA,MACjC;AAEA,UAAI,UAAU,SAAS,KAAK,MAAM;AAElC,eAAS,SAAS,QAAQ;AACxB,eAAO,SAASyB,SAAQvB,QAAO,KAAK,GAAG,KAAKA,OAAM,QAAQ;AACxD,cAAI,IAAI,MAAM,KAAK,CAAC;AACpB,iBAAO,GAAG;AACR,kBAAM,IAAI,OAAO,IAAI,MAAM,GAAG,IAAIA,OAAM,IAAI,EAAE;AAC9C,YAAAA,OAAM,IAAI,EAAE,IAAIA,OAAM,IAAI,EAAE;AAC5B,YAAAA,OAAM,IAAI,EAAE,IAAI;AAAA,UAClB;AACA,iBAAOA;AAAA,QACT;AAAA,MACF;AAEA,eAAS,IAAI,QAAQ,SAAS;AAC5B,YAAIK,OAAM;AACV,YAAI,YAAY,QAAW;AACzB,mBAAS,SAAS,QAAQ;AACxB,gBAAI,QAAQ,CAAC,OAAO;AAClB,cAAAA,QAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAIP,SAAQ;AACZ,mBAAS,SAAS,QAAQ;AACxB,gBAAI,QAAQ,CAAC,QAAQ,OAAO,EAAEA,QAAO,MAAM,GAAG;AAC5C,cAAAO,QAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AACA,eAAOA;AAAA,MACT;AAEA,eAAS,UAAU,QAAQ;AACzB,YAAI,EAAE,IAAI,OAAO,QAAS,QAAO,CAAC;AAClC,iBAAS,IAAI,IAAI,IAAI,IAAI,QAAQ,MAAM,GAAGmB,aAAY,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,KAAI;AAC5E,mBAAS,IAAI,IAAI,GAAG,MAAMA,WAAU,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,KAAI;AAC/D,gBAAI,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,UACtB;AAAA,QACF;AACA,eAAOA;AAAA,MACT;AAEA,eAAS,OAAO,GAAG;AACjB,eAAO,EAAE;AAAA,MACX;AAEA,eAAS,MAAM;AACb,eAAO,UAAU,SAAS;AAAA,MAC5B;AAEA,eAAS,MAAM,QAAQ,MAAM;AAC3B,YAAI,OAAO,SAAS,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC5E,YAAI1B,SAAQ;AACZ,mBAAW,SAAS,QAAQ;AAC1B,cAAI,CAAC,KAAK,OAAO,EAAEA,QAAO,MAAM,GAAG;AACjC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,eAAS,KAAK,QAAQ,MAAM;AAC1B,YAAI,OAAO,SAAS,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC5E,YAAIA,SAAQ;AACZ,mBAAW,SAAS,QAAQ;AAC1B,cAAI,KAAK,OAAO,EAAEA,QAAO,MAAM,GAAG;AAChC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,eAAS,OAAO,QAAQ,MAAM;AAC5B,YAAI,OAAO,SAAS,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC5E,cAAME,SAAQ,CAAC;AACf,YAAIF,SAAQ;AACZ,mBAAW,SAAS,QAAQ;AAC1B,cAAI,KAAK,OAAO,EAAEA,QAAO,MAAM,GAAG;AAChC,YAAAE,OAAM,KAAK,KAAK;AAAA,UAClB;AAAA,QACF;AACA,eAAOA;AAAA,MACT;AAEA,eAAS,IAAI,QAAQ,QAAQ;AAC3B,YAAI,OAAO,OAAO,OAAO,QAAQ,MAAM,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/F,YAAI,OAAO,WAAW,WAAY,OAAM,IAAI,UAAU,0BAA0B;AAChF,eAAO,MAAM,KAAK,QAAQ,CAAC,OAAOF,WAAU,OAAO,OAAOA,QAAO,MAAM,CAAC;AAAA,MAC1E;AAEA,eAAS,OAAO,QAAQ2B,UAAS,OAAO;AACtC,YAAI,OAAOA,aAAY,WAAY,OAAM,IAAI,UAAU,2BAA2B;AAClF,cAAM,WAAW,OAAO,OAAO,QAAQ,EAAE;AACzC,YAAI,MAAM,MAAM3B,SAAQ;AACxB,YAAI,UAAU,SAAS,GAAG;AACxB,WAAC,EAAC,MAAM,MAAK,IAAI,SAAS,KAAK;AAC/B,cAAI,KAAM;AACV,YAAEA;AAAA,QACJ;AACA,eAAQ,EAAC,MAAM,OAAO,KAAI,IAAI,SAAS,KAAK,GAAI,CAAC,MAAM;AACrD,kBAAQ2B,SAAQ,OAAO,MAAM,EAAE3B,QAAO,MAAM;AAAA,QAC9C;AACA,eAAO;AAAA,MACT;AAEA,eAAS,QAAQ,QAAQ;AACvB,YAAI,OAAO,OAAO,OAAO,QAAQ,MAAM,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/F,eAAO,MAAM,KAAK,MAAM,EAAE,QAAQ;AAAA,MACpC;AAEA,eAAS,WAAW,WAAW,QAAQ;AACrC,iBAAS,IAAI,IAAI,MAAM;AACvB,mBAAW,SAAS,QAAQ;AAC1B,qBAAW,SAAS,OAAO;AACzB,mBAAO,OAAO,KAAK;AAAA,UACrB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,eAAS,SAAS,QAAQ,OAAO;AAC/B,cAAM,WAAW,MAAM,OAAO,QAAQ,EAAE,GAAG4B,OAAM,oBAAI,IAAI;AACzD,mBAAW,KAAK,QAAQ;AACtB,cAAIA,KAAI,IAAI,CAAC,EAAG,QAAO;AACvB,cAAI,OAAO;AACX,iBAAQ,EAAC,OAAO,KAAI,IAAI,SAAS,KAAK,GAAI;AACxC,gBAAI,KAAM;AACV,gBAAI,OAAO,GAAG,GAAG,KAAK,EAAG,QAAO;AAChC,YAAAA,KAAI,IAAI,KAAK;AAAA,UACf;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,eAAS,IAAI,QAAQ;AACnB,eAAO,kBAAkB,MAAM,SAAS,IAAI,IAAI,MAAM;AAAA,MACxD;AAEA,eAAS,aAAa,WAAW,QAAQ;AACvC,iBAAS,IAAI,IAAI,MAAM;AACvB,iBAAS,OAAO,IAAI,GAAG;AACvB,YAAK,YAAW,SAAS,QAAQ;AAC/B,qBAAW,SAAS,QAAQ;AAC1B,gBAAI,CAAC,MAAM,IAAI,KAAK,GAAG;AACrB,qBAAO,OAAO,KAAK;AACnB,uBAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,eAAS,SAAS,QAAQ,OAAO;AAC/B,cAAM,WAAW,OAAO,OAAO,QAAQ,EAAE,GAAGA,OAAM,oBAAI,IAAI;AAC1D,mBAAW,KAAK,OAAO;AACrB,cAAIA,KAAI,IAAI,CAAC,EAAG;AAChB,cAAI,OAAO;AACX,iBAAQ,EAAC,OAAO,KAAI,IAAI,SAAS,KAAK,GAAI;AACxC,gBAAI,KAAM,QAAO;AACjB,YAAAA,KAAI,IAAI,KAAK;AACb,gBAAI,OAAO,GAAG,GAAG,KAAK,EAAG;AAAA,UAC3B;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,eAAS,OAAO,QAAQ,OAAO;AAC7B,eAAO,SAAS,OAAO,MAAM;AAAA,MAC/B;AAEA,eAAS,SAAS,QAAQ;AACxB,cAAMA,OAAM,oBAAI,IAAI;AACpB,mBAAW,SAAS,QAAQ;AAC1B,qBAAW,KAAK,OAAO;AACrB,YAAAA,KAAI,IAAI,CAAC;AAAA,UACX;AAAA,QACF;AACA,eAAOA;AAAA,MACT;AAEA,MAAA7B,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,MAAM;AACd,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,eAAe;AACvB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,cAAc;AACtB,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,UAAU;AAClB,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,OAAO;AACf,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,gBAAgB;AACxB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,UAAU;AAClB,MAAAA,SAAQ,eAAe;AACvB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,MAAM;AACd,MAAAA,SAAQ,MAAM;AACd,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,OAAO;AACf,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,MAAM;AACd,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,OAAO;AACf,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,UAAU;AAClB,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,cAAc;AACtB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,UAAU;AAClB,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,UAAU;AAClB,MAAAA,SAAQ,OAAO;AACf,MAAAA,SAAQ,UAAU;AAClB,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,OAAO;AACf,MAAAA,SAAQ,OAAO;AACf,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,MAAM;AACd,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,4BAA4B;AACpC,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,mBAAmB;AAC3B,MAAAA,SAAQ,gBAAgB;AACxB,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,MAAM;AAEd,aAAO,eAAeA,UAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA,IAE5D,CAAE;AAAA;AAAA;;;AC7pCF;AAAA;AACA,KAAC,SAAU,QAAQ,SAAS;AAC5B,aAAO,YAAY,YAAY,OAAO,WAAW,cAAc,QAAQ,OAAO,IAC9E,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,CAAC,SAAS,GAAG,OAAO,KACvE,SAAS,UAAU,MAAM,QAAQ,OAAO,KAAK,OAAO,MAAM,CAAC,CAAC;AAAA,IAC7D,GAAE,SAAM,SAAU8B,UAAS;AAAE;AAE7B,UAAI,KAAK,KAAK,IACV,MAAM,IAAI,IACV,UAAU,MACV,aAAa,MAAM;AAEvB,eAAS,OAAO;AACd,aAAK,MAAM,KAAK;AAAA,QAChB,KAAK,MAAM,KAAK,MAAM;AACtB,aAAK,IAAI;AAAA,MACX;AAEA,eAAS,OAAO;AACd,eAAO,IAAI;AAAA,MACb;AAEA,WAAK,YAAY,KAAK,YAAY;AAAA,QAChC,aAAa;AAAA,QACb,QAAQ,SAAS,GAAG,GAAG;AACrB,eAAK,KAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAAC,KAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAAC;AAAA,QAC7E;AAAA,QACA,WAAW,WAAW;AACpB,cAAI,KAAK,QAAQ,MAAM;AACrB,iBAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK;AACrC,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF;AAAA,QACA,QAAQ,SAAS,GAAG,GAAG;AACrB,eAAK,KAAK,OAAO,KAAK,MAAM,CAAC,KAAK,OAAO,KAAK,MAAM,CAAC;AAAA,QACvD;AAAA,QACA,kBAAkB,SAAS,IAAI,IAAI,GAAG,GAAG;AACvC,eAAK,KAAK,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,OAAO,KAAK,MAAM,CAAC,KAAK,OAAO,KAAK,MAAM,CAAC;AAAA,QACnF;AAAA,QACA,eAAe,SAAS,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG;AAC5C,eAAK,KAAK,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,MAAO,CAAC,KAAM,OAAO,KAAK,MAAM,CAAC,KAAK,OAAO,KAAK,MAAM,CAAC;AAAA,QAC/G;AAAA,QACA,OAAO,SAAS,IAAI,IAAI,IAAI,IAAI,GAAG;AACjC,eAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC;AAC7C,cAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACV,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM;AAG9B,cAAI,IAAI,EAAG,OAAM,IAAI,MAAM,sBAAsB,CAAC;AAGlD,cAAI,KAAK,QAAQ,MAAM;AACrB,iBAAK,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;AAAA,UACtD,WAGS,EAAE,QAAQ,SAAS;AAAA,mBAKnB,EAAE,KAAK,IAAI,MAAM,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,GAAG;AAC3D,iBAAK,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;AAAA,UACtD,OAGK;AACH,gBAAI,MAAM,KAAK,IACX,MAAM,KAAK,IACX,QAAQ,MAAM,MAAM,MAAM,KAC1B,QAAQ,MAAM,MAAM,MAAM,KAC1B,MAAM,KAAK,KAAK,KAAK,GACrB,MAAM,KAAK,KAAK,KAAK,GACrB,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,QAAQ,QAAQ,UAAU,IAAI,MAAM,IAAI,KAAK,CAAC,GAChF,MAAM,IAAI,KACV,MAAM,IAAI;AAGd,gBAAI,KAAK,IAAI,MAAM,CAAC,IAAI,SAAS;AAC/B,mBAAK,KAAK,OAAO,KAAK,MAAM,OAAO,OAAO,KAAK,MAAM;AAAA,YACvD;AAEA,iBAAK,KAAK,MAAM,IAAI,MAAM,IAAI,UAAW,EAAE,MAAM,MAAM,MAAM,OAAQ,OAAO,KAAK,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,MAAM,KAAK,MAAM;AAAA,UACxI;AAAA,QACF;AAAA,QACA,KAAK,SAAS,GAAG,GAAG,GAAG,IAAI,IAAI,KAAK;AAClC,cAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AAChC,cAAI,KAAK,IAAI,KAAK,IAAI,EAAE,GACpB,KAAK,IAAI,KAAK,IAAI,EAAE,GACpB,KAAK,IAAI,IACT,KAAK,IAAI,IACT,KAAK,IAAI,KACT,KAAK,MAAM,KAAK,KAAK,KAAK;AAG9B,cAAI,IAAI,EAAG,OAAM,IAAI,MAAM,sBAAsB,CAAC;AAGlD,cAAI,KAAK,QAAQ,MAAM;AACrB,iBAAK,KAAK,MAAM,KAAK,MAAM;AAAA,UAC7B,WAGS,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,WAAW,KAAK,IAAI,KAAK,MAAM,EAAE,IAAI,SAAS;AAC/E,iBAAK,KAAK,MAAM,KAAK,MAAM;AAAA,UAC7B;AAGA,cAAI,CAAC,EAAG;AAGR,cAAI,KAAK,EAAG,MAAK,KAAK,MAAM;AAG5B,cAAI,KAAK,YAAY;AACnB,iBAAK,KAAK,MAAM,IAAI,MAAM,IAAI,UAAU,KAAK,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI,UAAU,KAAK,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK,MAAM;AAAA,UAC9J,WAGS,KAAK,SAAS;AACrB,iBAAK,KAAK,MAAM,IAAI,MAAM,IAAI,QAAS,EAAE,MAAM,MAAO,MAAM,KAAK,OAAO,KAAK,MAAM,IAAI,IAAI,KAAK,IAAI,EAAE,KAAK,OAAO,KAAK,MAAM,IAAI,IAAI,KAAK,IAAI,EAAE;AAAA,UAClJ;AAAA,QACF;AAAA,QACA,MAAM,SAAS,GAAG,GAAG,GAAG,GAAG;AACzB,eAAK,KAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAAC,KAAK,OAAO,KAAK,MAAM,KAAK,MAAM,CAAC,KAAK,MAAO,CAAC,IAAK,MAAO,CAAC,IAAK,MAAO,CAAC,IAAK;AAAA,QACzH;AAAA,QACA,UAAU,WAAW;AACnB,iBAAO,KAAK;AAAA,QACd;AAAA,MACF;AAEA,MAAAA,SAAQ,OAAO;AAEf,aAAO,eAAeA,UAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA,IAE5D,CAAC;AAAA;AAAA;;;AC5ID;AAAA;AACA,KAAC,SAAU,QAAQ,SAAS;AAC5B,aAAO,YAAY,YAAY,OAAO,WAAW,cAAc,QAAQ,SAAS,iBAAkB,IAClG,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,CAAC,WAAW,SAAS,GAAG,OAAO,KAClF,SAAS,UAAU,MAAM,QAAQ,OAAO,KAAK,OAAO,MAAM,CAAC,GAAG,OAAO,EAAE;AAAA,IACxE,GAAE,SAAM,SAAUC,UAAS,QAAQ;AAAE;AAErC,eAAS,SAASC,IAAG;AACnB,eAAO,SAASC,YAAW;AACzB,iBAAOD;AAAA,QACT;AAAA,MACF;AAEA,UAAI,MAAM,KAAK;AACf,UAAI,QAAQ,KAAK;AACjB,UAAI,MAAM,KAAK;AACf,UAAI,MAAM,KAAK;AACf,UAAI,MAAM,KAAK;AACf,UAAI,MAAM,KAAK;AACf,UAAI,OAAO,KAAK;AAEhB,UAAI,UAAU;AACd,UAAI,KAAK,KAAK;AACd,UAAI,SAAS,KAAK;AAClB,UAAI,MAAM,IAAI;AAEd,eAAS,KAAKA,IAAG;AACf,eAAOA,KAAI,IAAI,IAAIA,KAAI,KAAK,KAAK,KAAK,KAAKA,EAAC;AAAA,MAC9C;AAEA,eAAS,KAAKA,IAAG;AACf,eAAOA,MAAK,IAAI,SAASA,MAAK,KAAK,CAAC,SAAS,KAAK,KAAKA,EAAC;AAAA,MAC1D;AAEA,eAAS,eAAe,GAAG;AACzB,eAAO,EAAE;AAAA,MACX;AAEA,eAAS,eAAe,GAAG;AACzB,eAAO,EAAE;AAAA,MACX;AAEA,eAAS,cAAc,GAAG;AACxB,eAAO,EAAE;AAAA,MACX;AAEA,eAAS,YAAY,GAAG;AACtB,eAAO,EAAE;AAAA,MACX;AAEA,eAAS,YAAY,GAAG;AACtB,eAAO,KAAK,EAAE;AAAA,MAChB;AAEA,eAAS,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACjD,YAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAC1B,MAAM,KAAK,IAAI,MAAM,KAAK,IAC1B,IAAI,MAAM,MAAM,MAAM;AAC1B,YAAI,IAAI,IAAI,QAAS;AACrB,aAAK,OAAO,KAAK,MAAM,OAAO,KAAK,OAAO;AAC1C,eAAO,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG;AAAA,MACpC;AAIA,eAAS,eAAe,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAClD,YAAI,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,KAAK,CAAC,MAAM,KAAK,MAAM,MAAM,MAAM,GAAG,GACjD,KAAK,KAAK,KACV,KAAK,CAAC,KAAK,KACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,MAAM,KAAK,IACX,OAAO,MAAM,OAAO,GACpB,OAAO,MAAM,OAAO,GACpB,KAAK,MAAM,KACX,KAAK,MAAM,KACX,KAAK,KAAK,KAAK,KAAK,IACpB,IAAI,KAAK,IACT,IAAI,MAAM,MAAM,MAAM,KACtB,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,GACvD,OAAO,IAAI,KAAK,KAAK,KAAK,IAC1B,OAAO,CAAC,IAAI,KAAK,KAAK,KAAK,IAC3B,OAAO,IAAI,KAAK,KAAK,KAAK,IAC1B,OAAO,CAAC,IAAI,KAAK,KAAK,KAAK,IAC3B,MAAM,MAAM,KACZ,MAAM,MAAM,KACZ,MAAM,MAAM,KACZ,MAAM,MAAM;AAIhB,YAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAK,OAAM,KAAK,MAAM;AAEpE,eAAO;AAAA,UACL,IAAI;AAAA,UACJ,IAAI;AAAA,UACJ,KAAK,CAAC;AAAA,UACN,KAAK,CAAC;AAAA,UACN,KAAK,OAAO,KAAK,IAAI;AAAA,UACrB,KAAK,OAAO,KAAK,IAAI;AAAA,QACvB;AAAA,MACF;AAEA,eAAS,MAAM;AACb,YAAI,cAAc,gBACd,cAAc,gBACd,eAAe,SAAS,CAAC,GACzB,YAAY,MACZ,aAAa,eACb,WAAW,aACX,WAAW,aACX,UAAU;AAEd,iBAASE,OAAM;AACb,cAAI,QACA,GACA,KAAK,CAAC,YAAY,MAAM,MAAM,SAAS,GACvC,KAAK,CAAC,YAAY,MAAM,MAAM,SAAS,GACvC,KAAK,WAAW,MAAM,MAAM,SAAS,IAAI,QACzC,KAAK,SAAS,MAAM,MAAM,SAAS,IAAI,QACvC,KAAK,IAAI,KAAK,EAAE,GAChB,KAAK,KAAK;AAEd,cAAI,CAAC,QAAS,WAAU,SAAS,OAAO,KAAK;AAG7C,cAAI,KAAK,GAAI,KAAI,IAAI,KAAK,IAAI,KAAK;AAGnC,cAAI,EAAE,KAAK,SAAU,SAAQ,OAAO,GAAG,CAAC;AAAA,mBAG/B,KAAK,MAAM,SAAS;AAC3B,oBAAQ,OAAO,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AACzC,oBAAQ,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE;AACjC,gBAAI,KAAK,SAAS;AAChB,sBAAQ,OAAO,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;AACzC,sBAAQ,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,YAClC;AAAA,UACF,OAGK;AACH,gBAAI,MAAM,IACN,MAAM,IACN,MAAM,IACN,MAAM,IACN,MAAM,IACN,MAAM,IACN,KAAK,SAAS,MAAM,MAAM,SAAS,IAAI,GACvC,KAAM,KAAK,YAAa,YAAY,CAAC,UAAU,MAAM,MAAM,SAAS,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE,IAC9F,KAAK,IAAI,IAAI,KAAK,EAAE,IAAI,GAAG,CAAC,aAAa,MAAM,MAAM,SAAS,CAAC,GAC/D,MAAM,IACN,MAAM,IACN,IACA;AAGJ,gBAAI,KAAK,SAAS;AAChB,kBAAI,KAAK,KAAK,KAAK,KAAK,IAAI,EAAE,CAAC,GAC3B,KAAK,KAAK,KAAK,KAAK,IAAI,EAAE,CAAC;AAC/B,mBAAK,OAAO,KAAK,KAAK,QAAS,OAAO,KAAK,IAAI,IAAK,OAAO,IAAI,OAAO;AAAA,kBACjE,OAAM,GAAG,MAAM,OAAO,KAAK,MAAM;AACtC,mBAAK,OAAO,KAAK,KAAK,QAAS,OAAO,KAAK,IAAI,IAAK,OAAO,IAAI,OAAO;AAAA,kBACjE,OAAM,GAAG,MAAM,OAAO,KAAK,MAAM;AAAA,YACxC;AAEA,gBAAI,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG;AAGtB,gBAAI,KAAK,SAAS;AAChB,kBAAI,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB,MAAM,KAAK,IAAI,GAAG,GAClB;AAGJ,kBAAI,KAAK,OAAO,KAAK,UAAU,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAI;AACvE,oBAAI,KAAK,MAAM,GAAG,CAAC,GACf,KAAK,MAAM,GAAG,CAAC,GACf,KAAK,MAAM,GAAG,CAAC,GACf,KAAK,MAAM,GAAG,CAAC,GACf,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE,EAAE,IAAI,CAAC,GAChG,KAAK,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;AAC3C,sBAAM,IAAI,KAAK,KAAK,OAAO,KAAK,EAAE;AAClC,sBAAM,IAAI,KAAK,KAAK,OAAO,KAAK,EAAE;AAAA,cACpC;AAAA,YACF;AAGA,gBAAI,EAAE,MAAM,SAAU,SAAQ,OAAO,KAAK,GAAG;AAAA,qBAGpC,MAAM,SAAS;AACtB,mBAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,EAAE;AACnD,mBAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,EAAE;AAEnD,sBAAQ,OAAO,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG;AAG7C,kBAAI,MAAM,GAAI,SAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,mBAGzF;AACH,wBAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;AAChF,wBAAQ,IAAI,GAAG,GAAG,IAAI,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;AACvG,wBAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,cAClF;AAAA,YACF,MAGK,SAAQ,OAAO,KAAK,GAAG,GAAG,QAAQ,IAAI,GAAG,GAAG,IAAI,KAAK,KAAK,CAAC,EAAE;AAIlE,gBAAI,EAAE,KAAK,YAAY,EAAE,MAAM,SAAU,SAAQ,OAAO,KAAK,GAAG;AAAA,qBAGvD,MAAM,SAAS;AACtB,mBAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AACpD,mBAAK,eAAe,KAAK,KAAK,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AAEpD,sBAAQ,OAAO,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG;AAG7C,kBAAI,MAAM,GAAI,SAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,mBAGzF;AACH,wBAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;AAChF,wBAAQ,IAAI,GAAG,GAAG,IAAI,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,EAAE;AACtG,wBAAQ,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,cAClF;AAAA,YACF,MAGK,SAAQ,IAAI,GAAG,GAAG,IAAI,KAAK,KAAK,EAAE;AAAA,UACzC;AAEA,kBAAQ,UAAU;AAElB,cAAI,OAAQ,QAAO,UAAU,MAAM,SAAS,MAAM;AAAA,QACpD;AAEA,QAAAA,KAAI,WAAW,WAAW;AACxB,cAAI,KAAK,CAAC,YAAY,MAAM,MAAM,SAAS,IAAI,CAAC,YAAY,MAAM,MAAM,SAAS,KAAK,GAClFC,MAAK,CAAC,WAAW,MAAM,MAAM,SAAS,IAAI,CAAC,SAAS,MAAM,MAAM,SAAS,KAAK,IAAI,KAAK;AAC3F,iBAAO,CAAC,IAAIA,EAAC,IAAI,GAAG,IAAIA,EAAC,IAAI,CAAC;AAAA,QAChC;AAEA,QAAAD,KAAI,cAAc,SAAS,GAAG;AAC5B,iBAAO,UAAU,UAAU,cAAc,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,QAAO;AAAA,QAC9F;AAEA,QAAAA,KAAI,cAAc,SAAS,GAAG;AAC5B,iBAAO,UAAU,UAAU,cAAc,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,QAAO;AAAA,QAC9F;AAEA,QAAAA,KAAI,eAAe,SAAS,GAAG;AAC7B,iBAAO,UAAU,UAAU,eAAe,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,QAAO;AAAA,QAC/F;AAEA,QAAAA,KAAI,YAAY,SAAS,GAAG;AAC1B,iBAAO,UAAU,UAAU,YAAY,KAAK,OAAO,OAAO,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,QAAO;AAAA,QAC/G;AAEA,QAAAA,KAAI,aAAa,SAAS,GAAG;AAC3B,iBAAO,UAAU,UAAU,aAAa,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,QAAO;AAAA,QAC7F;AAEA,QAAAA,KAAI,WAAW,SAAS,GAAG;AACzB,iBAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,QAAO;AAAA,QAC3F;AAEA,QAAAA,KAAI,WAAW,SAAS,GAAG;AACzB,iBAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,QAAO;AAAA,QAC3F;AAEA,QAAAA,KAAI,UAAU,SAAS,GAAG;AACxB,iBAAO,UAAU,UAAW,UAAU,KAAK,OAAO,OAAO,GAAIA,QAAO;AAAA,QACtE;AAEA,eAAOA;AAAA,MACT;AAEA,eAAS,OAAO,SAAS;AACvB,aAAK,WAAW;AAAA,MAClB;AAEA,aAAO,YAAY;AAAA,QACjB,WAAW,WAAW;AACpB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,SAAS,WAAW;AAClB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,WAAW,WAAW;AACpB,eAAK,SAAS;AAAA,QAChB;AAAA,QACA,SAAS,WAAW;AAClB,cAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,eAAK,QAAQ,IAAI,KAAK;AAAA,QACxB;AAAA,QACA,OAAO,SAASF,IAAGI,IAAG;AACpB,UAAAJ,KAAI,CAACA,IAAGI,KAAI,CAACA;AACb,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,QAAQ,KAAK,SAAS,OAAOJ,IAAGI,EAAC,IAAI,KAAK,SAAS,OAAOJ,IAAGI,EAAC;AAAG;AAAA,YAC/F,KAAK;AAAG,mBAAK,SAAS;AAAA,YACtB;AAAS,mBAAK,SAAS,OAAOJ,IAAGI,EAAC;AAAG;AAAA,UACvC;AAAA,QACF;AAAA,MACF;AAEA,eAAS,YAAY,SAAS;AAC5B,eAAO,IAAI,OAAO,OAAO;AAAA,MAC3B;AAEA,eAAS,EAAE,GAAG;AACZ,eAAO,EAAE,CAAC;AAAA,MACZ;AAEA,eAAS,EAAE,GAAG;AACZ,eAAO,EAAE,CAAC;AAAA,MACZ;AAEA,eAAS,OAAO;AACd,YAAI,MAAM,GACN,MAAM,GACN,UAAU,SAAS,IAAI,GACvB,UAAU,MACV,QAAQ,aACR,SAAS;AAEb,iBAASC,MAAK,MAAM;AAClB,cAAI,GACA,IAAI,KAAK,QACT,GACA,WAAW,OACX;AAEJ,cAAI,WAAW,KAAM,UAAS,MAAM,SAAS,OAAO,KAAK,CAAC;AAE1D,eAAK,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACvB,gBAAI,EAAE,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,OAAO,UAAU;AAC1D,kBAAI,WAAW,CAAC,SAAU,QAAO,UAAU;AAAA,kBACtC,QAAO,QAAQ;AAAA,YACtB;AACA,gBAAI,SAAU,QAAO,MAAM,CAAC,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC;AAAA,UAC/D;AAEA,cAAI,OAAQ,QAAO,SAAS,MAAM,SAAS,MAAM;AAAA,QACnD;AAEA,QAAAA,MAAK,IAAI,SAAS,GAAG;AACnB,iBAAO,UAAU,UAAU,MAAM,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,SAAQ;AAAA,QACvF;AAEA,QAAAA,MAAK,IAAI,SAAS,GAAG;AACnB,iBAAO,UAAU,UAAU,MAAM,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,SAAQ;AAAA,QACvF;AAEA,QAAAA,MAAK,UAAU,SAAS,GAAG;AACzB,iBAAO,UAAU,UAAU,UAAU,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAQ;AAAA,QAC5F;AAEA,QAAAA,MAAK,QAAQ,SAAS,GAAG;AACvB,iBAAO,UAAU,UAAU,QAAQ,GAAG,WAAW,SAAS,SAAS,MAAM,OAAO,IAAIA,SAAQ;AAAA,QAC9F;AAEA,QAAAA,MAAK,UAAU,SAAS,GAAG;AACzB,iBAAO,UAAU,UAAU,KAAK,OAAO,UAAU,SAAS,OAAO,SAAS,MAAM,UAAU,CAAC,GAAGA,SAAQ;AAAA,QACxG;AAEA,eAAOA;AAAA,MACT;AAEA,eAAS,OAAO;AACd,YAAI,KAAK,GACL,KAAK,MACL,KAAK,SAAS,CAAC,GACf,KAAK,GACL,UAAU,SAAS,IAAI,GACvB,UAAU,MACV,QAAQ,aACR,SAAS;AAEb,iBAASC,MAAK,MAAM;AAClB,cAAI,GACA,GACAC,IACA,IAAI,KAAK,QACT,GACA,WAAW,OACX,QACA,MAAM,IAAI,MAAM,CAAC,GACjB,MAAM,IAAI,MAAM,CAAC;AAErB,cAAI,WAAW,KAAM,UAAS,MAAM,SAAS,OAAO,KAAK,CAAC;AAE1D,eAAK,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACvB,gBAAI,EAAE,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,OAAO,UAAU;AAC1D,kBAAI,WAAW,CAAC,UAAU;AACxB,oBAAI;AACJ,uBAAO,UAAU;AACjB,uBAAO,UAAU;AAAA,cACnB,OAAO;AACL,uBAAO,QAAQ;AACf,uBAAO,UAAU;AACjB,qBAAKA,KAAI,IAAI,GAAGA,MAAK,GAAG,EAAEA,IAAG;AAC3B,yBAAO,MAAM,IAAIA,EAAC,GAAG,IAAIA,EAAC,CAAC;AAAA,gBAC7B;AACA,uBAAO,QAAQ;AACf,uBAAO,QAAQ;AAAA,cACjB;AAAA,YACF;AACA,gBAAI,UAAU;AACZ,kBAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,IAAI;AACjD,qBAAO,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC;AAAA,YAC3E;AAAA,UACF;AAEA,cAAI,OAAQ,QAAO,SAAS,MAAM,SAAS,MAAM;AAAA,QACnD;AAEA,iBAAS,WAAW;AAClB,iBAAO,KAAK,EAAE,QAAQ,OAAO,EAAE,MAAM,KAAK,EAAE,QAAQ,OAAO;AAAA,QAC7D;AAEA,QAAAD,MAAK,IAAI,SAAS,GAAG;AACnB,iBAAO,UAAU,UAAU,KAAK,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAG,KAAK,MAAMA,SAAQ;AAAA,QACjG;AAEA,QAAAA,MAAK,KAAK,SAAS,GAAG;AACpB,iBAAO,UAAU,UAAU,KAAK,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,SAAQ;AAAA,QACtF;AAEA,QAAAA,MAAK,KAAK,SAAS,GAAG;AACpB,iBAAO,UAAU,UAAU,KAAK,KAAK,OAAO,OAAO,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,SAAQ;AAAA,QACzG;AAEA,QAAAA,MAAK,IAAI,SAAS,GAAG;AACnB,iBAAO,UAAU,UAAU,KAAK,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAG,KAAK,MAAMA,SAAQ;AAAA,QACjG;AAEA,QAAAA,MAAK,KAAK,SAAS,GAAG;AACpB,iBAAO,UAAU,UAAU,KAAK,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,SAAQ;AAAA,QACtF;AAEA,QAAAA,MAAK,KAAK,SAAS,GAAG;AACpB,iBAAO,UAAU,UAAU,KAAK,KAAK,OAAO,OAAO,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,SAAQ;AAAA,QACzG;AAEA,QAAAA,MAAK,SACLA,MAAK,SAAS,WAAW;AACvB,iBAAO,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAAA,QAC9B;AAEA,QAAAA,MAAK,SAAS,WAAW;AACvB,iBAAO,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAAA,QAC9B;AAEA,QAAAA,MAAK,SAAS,WAAW;AACvB,iBAAO,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAAA,QAC9B;AAEA,QAAAA,MAAK,UAAU,SAAS,GAAG;AACzB,iBAAO,UAAU,UAAU,UAAU,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAQ;AAAA,QAC5F;AAEA,QAAAA,MAAK,QAAQ,SAAS,GAAG;AACvB,iBAAO,UAAU,UAAU,QAAQ,GAAG,WAAW,SAAS,SAAS,MAAM,OAAO,IAAIA,SAAQ;AAAA,QAC9F;AAEA,QAAAA,MAAK,UAAU,SAAS,GAAG;AACzB,iBAAO,UAAU,UAAU,KAAK,OAAO,UAAU,SAAS,OAAO,SAAS,MAAM,UAAU,CAAC,GAAGA,SAAQ;AAAA,QACxG;AAEA,eAAOA;AAAA,MACT;AAEA,eAAS,WAAWH,IAAG,GAAG;AACxB,eAAO,IAAIA,KAAI,KAAK,IAAIA,KAAI,IAAI,KAAKA,KAAI,IAAI;AAAA,MAC/C;AAEA,eAAS,SAAS,GAAG;AACnB,eAAO;AAAA,MACT;AAEA,eAAS,MAAM;AACb,YAAI,QAAQ,UACR,aAAa,YACb,OAAO,MACP,aAAa,SAAS,CAAC,GACvB,WAAW,SAAS,GAAG,GACvB,WAAW,SAAS,CAAC;AAEzB,iBAASK,KAAI,MAAM;AACjB,cAAI,GACA,IAAI,KAAK,QACT,GACAD,IACAE,OAAM,GACN,QAAQ,IAAI,MAAM,CAAC,GACnB,OAAO,IAAI,MAAM,CAAC,GAClB,KAAK,CAAC,WAAW,MAAM,MAAM,SAAS,GACtC,KAAK,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,SAAS,MAAM,MAAM,SAAS,IAAI,EAAE,CAAC,GACvE,IACA,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,IAAI,GAAG,SAAS,MAAM,MAAM,SAAS,CAAC,GAC9D,KAAK,KAAK,KAAK,IAAI,KAAK,IACxB;AAEJ,eAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,iBAAK,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,GAAG,IAAI,KAAK,GAAG;AAC3D,cAAAA,QAAO;AAAA,YACT;AAAA,UACF;AAGA,cAAI,cAAc,KAAM,OAAM,KAAK,SAASC,IAAGC,IAAG;AAAE,mBAAO,WAAW,KAAKD,EAAC,GAAG,KAAKC,EAAC,CAAC;AAAA,UAAG,CAAC;AAAA,mBACjF,QAAQ,KAAM,OAAM,KAAK,SAASD,IAAGC,IAAG;AAAE,mBAAO,KAAK,KAAKD,EAAC,GAAG,KAAKC,EAAC,CAAC;AAAA,UAAG,CAAC;AAGnF,eAAK,IAAI,GAAGJ,KAAIE,QAAO,KAAK,IAAI,MAAMA,OAAM,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,IAAI;AAClE,gBAAI,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,KAAK,MAAM,IAAI,IAAI,IAAIF,KAAI,KAAK,IAAI,KAAK,CAAC,IAAI;AAAA,cACvE,MAAM,KAAK,CAAC;AAAA,cACZ,OAAO;AAAA,cACP,OAAO;AAAA,cACP,YAAY;AAAA,cACZ,UAAU;AAAA,cACV,UAAU;AAAA,YACZ;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,QAAAC,KAAI,QAAQ,SAAS,GAAG;AACtB,iBAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,QAAO;AAAA,QACxF;AAEA,QAAAA,KAAI,aAAa,SAAS,GAAG;AAC3B,iBAAO,UAAU,UAAU,aAAa,GAAG,OAAO,MAAMA,QAAO;AAAA,QACjE;AAEA,QAAAA,KAAI,OAAO,SAAS,GAAG;AACrB,iBAAO,UAAU,UAAU,OAAO,GAAG,aAAa,MAAMA,QAAO;AAAA,QACjE;AAEA,QAAAA,KAAI,aAAa,SAAS,GAAG;AAC3B,iBAAO,UAAU,UAAU,aAAa,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,QAAO;AAAA,QAC7F;AAEA,QAAAA,KAAI,WAAW,SAAS,GAAG;AACzB,iBAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,QAAO;AAAA,QAC3F;AAEA,QAAAA,KAAI,WAAW,SAAS,GAAG;AACzB,iBAAO,UAAU,UAAU,WAAW,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,QAAO;AAAA,QAC3F;AAEA,eAAOA;AAAA,MACT;AAEA,UAAI,oBAAoB,YAAY,WAAW;AAE/C,eAAS,OAAO,OAAO;AACrB,aAAK,SAAS;AAAA,MAChB;AAEA,aAAO,YAAY;AAAA,QACjB,WAAW,WAAW;AACpB,eAAK,OAAO,UAAU;AAAA,QACxB;AAAA,QACA,SAAS,WAAW;AAClB,eAAK,OAAO,QAAQ;AAAA,QACtB;AAAA,QACA,WAAW,WAAW;AACpB,eAAK,OAAO,UAAU;AAAA,QACxB;AAAA,QACA,SAAS,WAAW;AAClB,eAAK,OAAO,QAAQ;AAAA,QACtB;AAAA,QACA,OAAO,SAASL,IAAG,GAAG;AACpB,eAAK,OAAO,MAAM,IAAI,KAAK,IAAIA,EAAC,GAAG,IAAI,CAAC,KAAK,IAAIA,EAAC,CAAC;AAAA,QACrD;AAAA,MACF;AAEA,eAAS,YAAY,OAAO;AAE1B,iBAAS,OAAO,SAAS;AACvB,iBAAO,IAAI,OAAO,MAAM,OAAO,CAAC;AAAA,QAClC;AAEA,eAAO,SAAS;AAEhB,eAAO;AAAA,MACT;AAEA,eAAS,WAAW,GAAG;AACrB,YAAIS,KAAI,EAAE;AAEV,UAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;AACxB,UAAE,SAAS,EAAE,GAAG,OAAO,EAAE;AAEzB,UAAE,QAAQ,SAAS,GAAG;AACpB,iBAAO,UAAU,SAASA,GAAE,YAAY,CAAC,CAAC,IAAIA,GAAE,EAAE;AAAA,QACpD;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,eAAe;AACtB,eAAO,WAAW,KAAK,EAAE,MAAM,iBAAiB,CAAC;AAAA,MACnD;AAEA,eAAS,aAAa;AACpB,YAAIT,KAAI,KAAK,EAAE,MAAM,iBAAiB,GAClCS,KAAIT,GAAE,OACN,KAAKA,GAAE,QACP,KAAKA,GAAE,QACP,KAAKA,GAAE,QACP,KAAKA,GAAE;AAEX,QAAAA,GAAE,QAAQA,GAAE,GAAG,OAAOA,GAAE;AACxB,QAAAA,GAAE,aAAaA,GAAE,IAAI,OAAOA,GAAE;AAC9B,QAAAA,GAAE,WAAWA,GAAE,IAAI,OAAOA,GAAE;AAC5B,QAAAA,GAAE,SAASA,GAAE,GAAG,OAAOA,GAAE;AACzB,QAAAA,GAAE,cAAcA,GAAE,IAAI,OAAOA,GAAE;AAC/B,QAAAA,GAAE,cAAcA,GAAE,IAAI,OAAOA,GAAE;AAC/B,QAAAA,GAAE,iBAAiB,WAAW;AAAE,iBAAO,WAAW,GAAG,CAAC;AAAA,QAAG,GAAG,OAAOA,GAAE;AACrE,QAAAA,GAAE,eAAe,WAAW;AAAE,iBAAO,WAAW,GAAG,CAAC;AAAA,QAAG,GAAG,OAAOA,GAAE;AACnE,QAAAA,GAAE,kBAAkB,WAAW;AAAE,iBAAO,WAAW,GAAG,CAAC;AAAA,QAAG,GAAG,OAAOA,GAAE;AACtE,QAAAA,GAAE,kBAAkB,WAAW;AAAE,iBAAO,WAAW,GAAG,CAAC;AAAA,QAAG,GAAG,OAAOA,GAAE;AAEtE,QAAAA,GAAE,QAAQ,SAAS,GAAG;AACpB,iBAAO,UAAU,SAASS,GAAE,YAAY,CAAC,CAAC,IAAIA,GAAE,EAAE;AAAA,QACpD;AAEA,eAAOT;AAAA,MACT;AAEA,eAAS,YAAYH,IAAGI,IAAG;AACzB,eAAO,EAAEA,KAAI,CAACA,MAAK,KAAK,IAAIJ,MAAK,KAAK,KAAK,CAAC,GAAGI,KAAI,KAAK,IAAIJ,EAAC,CAAC;AAAA,MAChE;AAEA,UAAI,QAAQ,MAAM,UAAU;AAE5B,eAAS,WAAW,GAAG;AACrB,eAAO,EAAE;AAAA,MACX;AAEA,eAAS,WAAW,GAAG;AACrB,eAAO,EAAE;AAAA,MACX;AAEA,eAAS,KAAK,OAAO;AACnB,YAAI,SAAS,YACT,SAAS,YACT,MAAM,GACN,MAAM,GACN,UAAU;AAEd,iBAASa,QAAO;AACd,cAAI,QAAQ,OAAO,MAAM,KAAK,SAAS,GAAGC,KAAI,OAAO,MAAM,MAAM,IAAI,GAAG,IAAI,OAAO,MAAM,MAAM,IAAI;AACnG,cAAI,CAAC,QAAS,WAAU,SAAS,OAAO,KAAK;AAC7C,gBAAM,SAAS,CAAC,IAAI,MAAM,OAAO,KAAK,CAAC,IAAIA,IAAG,KAAK,GAAG,CAAC,IAAI,MAAM,MAAM,IAAI,GAAG,CAAC,IAAI,MAAM,OAAO,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,IAAI,MAAM,MAAM,IAAI,CAAC;AAC3I,cAAI,OAAQ,QAAO,UAAU,MAAM,SAAS,MAAM;AAAA,QACpD;AAEA,QAAAD,MAAK,SAAS,SAAS,GAAG;AACxB,iBAAO,UAAU,UAAU,SAAS,GAAGA,SAAQ;AAAA,QACjD;AAEA,QAAAA,MAAK,SAAS,SAAS,GAAG;AACxB,iBAAO,UAAU,UAAU,SAAS,GAAGA,SAAQ;AAAA,QACjD;AAEA,QAAAA,MAAK,IAAI,SAAS,GAAG;AACnB,iBAAO,UAAU,UAAU,MAAM,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,SAAQ;AAAA,QACvF;AAEA,QAAAA,MAAK,IAAI,SAAS,GAAG;AACnB,iBAAO,UAAU,UAAU,MAAM,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,SAAQ;AAAA,QACvF;AAEA,QAAAA,MAAK,UAAU,SAAS,GAAG;AACzB,iBAAO,UAAU,UAAW,UAAU,KAAK,OAAO,OAAO,GAAIA,SAAQ;AAAA,QACvE;AAEA,eAAOA;AAAA,MACT;AAEA,eAAS,gBAAgB,SAAS,IAAI,IAAI,IAAI,IAAI;AAChD,gBAAQ,OAAO,IAAI,EAAE;AACrB,gBAAQ,cAAc,MAAM,KAAK,MAAM,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,MAC9D;AAEA,eAAS,cAAc,SAAS,IAAI,IAAI,IAAI,IAAI;AAC9C,gBAAQ,OAAO,IAAI,EAAE;AACrB,gBAAQ,cAAc,IAAI,MAAM,KAAK,MAAM,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,MAC9D;AAEA,eAAS,cAAc,SAAS,IAAI,IAAI,IAAI,IAAI;AAC9C,YAAI,KAAK,YAAY,IAAI,EAAE,GACvB,KAAK,YAAY,IAAI,MAAM,KAAK,MAAM,CAAC,GACvC,KAAK,YAAY,IAAI,EAAE,GACvB,KAAK,YAAY,IAAI,EAAE;AAC3B,gBAAQ,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAC3B,gBAAQ,cAAc,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,MAChE;AAEA,eAAS,iBAAiB;AACxB,eAAO,KAAK,eAAe;AAAA,MAC7B;AAEA,eAAS,eAAe;AACtB,eAAO,KAAK,aAAa;AAAA,MAC3B;AAEA,eAAS,aAAa;AACpB,YAAI,IAAI,KAAK,aAAa;AAC1B,UAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;AACxB,UAAE,SAAS,EAAE,GAAG,OAAO,EAAE;AACzB,eAAO;AAAA,MACT;AAEA,UAAI,SAAS;AAAA,QACX,MAAM,SAAS,SAAS,MAAM;AAC5B,cAAI,IAAI,KAAK,KAAK,OAAO,EAAE;AAC3B,kBAAQ,OAAO,GAAG,CAAC;AACnB,kBAAQ,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG;AAAA,QAC7B;AAAA,MACF;AAEA,UAAI,QAAQ;AAAA,QACV,MAAM,SAAS,SAAS,MAAM;AAC5B,cAAI,IAAI,KAAK,KAAK,OAAO,CAAC,IAAI;AAC9B,kBAAQ,OAAO,KAAK,GAAG,CAAC,CAAC;AACzB,kBAAQ,OAAO,CAAC,GAAG,CAAC,CAAC;AACrB,kBAAQ,OAAO,CAAC,GAAG,KAAK,CAAC;AACzB,kBAAQ,OAAO,GAAG,KAAK,CAAC;AACxB,kBAAQ,OAAO,GAAG,CAAC,CAAC;AACpB,kBAAQ,OAAO,IAAI,GAAG,CAAC,CAAC;AACxB,kBAAQ,OAAO,IAAI,GAAG,CAAC;AACvB,kBAAQ,OAAO,GAAG,CAAC;AACnB,kBAAQ,OAAO,GAAG,IAAI,CAAC;AACvB,kBAAQ,OAAO,CAAC,GAAG,IAAI,CAAC;AACxB,kBAAQ,OAAO,CAAC,GAAG,CAAC;AACpB,kBAAQ,OAAO,KAAK,GAAG,CAAC;AACxB,kBAAQ,UAAU;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,QAAQ,KAAK,KAAK,IAAI,CAAC,GACvB,UAAU,QAAQ;AAEtB,UAAI,UAAU;AAAA,QACZ,MAAM,SAAS,SAAS,MAAM;AAC5B,cAAIT,KAAI,KAAK,KAAK,OAAO,OAAO,GAC5BJ,KAAII,KAAI;AACZ,kBAAQ,OAAO,GAAG,CAACA,EAAC;AACpB,kBAAQ,OAAOJ,IAAG,CAAC;AACnB,kBAAQ,OAAO,GAAGI,EAAC;AACnB,kBAAQ,OAAO,CAACJ,IAAG,CAAC;AACpB,kBAAQ,UAAU;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,KAAK,oBACL,KAAK,KAAK,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI,IAAI,KAAK,EAAE,GAC7C,KAAK,KAAK,IAAI,MAAM,EAAE,IAAI,IAC1B,KAAK,CAAC,KAAK,IAAI,MAAM,EAAE,IAAI;AAE/B,UAAI,OAAO;AAAA,QACT,MAAM,SAAS,SAAS,MAAM;AAC5B,cAAI,IAAI,KAAK,KAAK,OAAO,EAAE,GACvBA,KAAI,KAAK,GACTI,KAAI,KAAK;AACb,kBAAQ,OAAO,GAAG,CAAC,CAAC;AACpB,kBAAQ,OAAOJ,IAAGI,EAAC;AACnB,mBAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,gBAAID,KAAI,MAAM,IAAI,GACdS,KAAI,KAAK,IAAIT,EAAC,GACdW,KAAI,KAAK,IAAIX,EAAC;AAClB,oBAAQ,OAAOW,KAAI,GAAG,CAACF,KAAI,CAAC;AAC5B,oBAAQ,OAAOA,KAAIZ,KAAIc,KAAIV,IAAGU,KAAId,KAAIY,KAAIR,EAAC;AAAA,UAC7C;AACA,kBAAQ,UAAU;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,SAAS;AAAA,QACX,MAAM,SAAS,SAAS,MAAM;AAC5B,cAAI,IAAI,KAAK,KAAK,IAAI,GAClBJ,KAAI,CAAC,IAAI;AACb,kBAAQ,KAAKA,IAAGA,IAAG,GAAG,CAAC;AAAA,QACzB;AAAA,MACF;AAEA,UAAI,QAAQ,KAAK,KAAK,CAAC;AAEvB,UAAI,WAAW;AAAA,QACb,MAAM,SAAS,SAAS,MAAM;AAC5B,cAAII,KAAI,CAAC,KAAK,KAAK,QAAQ,QAAQ,EAAE;AACrC,kBAAQ,OAAO,GAAGA,KAAI,CAAC;AACvB,kBAAQ,OAAO,CAAC,QAAQA,IAAG,CAACA,EAAC;AAC7B,kBAAQ,OAAO,QAAQA,IAAG,CAACA,EAAC;AAC5B,kBAAQ,UAAU;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,IAAI,MACJ,IAAI,KAAK,KAAK,CAAC,IAAI,GACnB,IAAI,IAAI,KAAK,KAAK,EAAE,GACpB,KAAK,IAAI,IAAI,KAAK;AAEtB,UAAI,MAAM;AAAA,QACR,MAAM,SAAS,SAAS,MAAM;AAC5B,cAAI,IAAI,KAAK,KAAK,OAAO,CAAC,GACtB,KAAK,IAAI,GACT,KAAK,IAAI,GACT,KAAK,IACL,KAAK,IAAI,IAAI,GACb,KAAK,CAAC,IACN,KAAK;AACT,kBAAQ,OAAO,IAAI,EAAE;AACrB,kBAAQ,OAAO,IAAI,EAAE;AACrB,kBAAQ,OAAO,IAAI,EAAE;AACrB,kBAAQ,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/C,kBAAQ,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/C,kBAAQ,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/C,kBAAQ,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/C,kBAAQ,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/C,kBAAQ,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/C,kBAAQ,UAAU;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,UAAU;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,eAAS,SAAS;AAChB,YAAI,OAAO,SAAS,MAAM,GACtB,OAAO,SAAS,EAAE,GAClB,UAAU;AAEd,iBAASW,UAAS;AAChB,cAAI;AACJ,cAAI,CAAC,QAAS,WAAU,SAAS,OAAO,KAAK;AAC7C,eAAK,MAAM,MAAM,SAAS,EAAE,KAAK,SAAS,CAAC,KAAK,MAAM,MAAM,SAAS,CAAC;AACtE,cAAI,OAAQ,QAAO,UAAU,MAAM,SAAS,MAAM;AAAA,QACpD;AAEA,QAAAA,QAAO,OAAO,SAAS,GAAG;AACxB,iBAAO,UAAU,UAAU,OAAO,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAGA,WAAU;AAAA,QACzF;AAEA,QAAAA,QAAO,OAAO,SAAS,GAAG;AACxB,iBAAO,UAAU,UAAU,OAAO,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,WAAU;AAAA,QAC1F;AAEA,QAAAA,QAAO,UAAU,SAAS,GAAG;AAC3B,iBAAO,UAAU,UAAU,UAAU,KAAK,OAAO,OAAO,GAAGA,WAAU;AAAA,QACvE;AAEA,eAAOA;AAAA,MACT;AAEA,eAAS,OAAO;AAAA,MAAC;AAEjB,eAAS,MAAM,MAAMf,IAAGI,IAAG;AACzB,aAAK,SAAS;AAAA,WACX,IAAI,KAAK,MAAM,KAAK,OAAO;AAAA,WAC3B,IAAI,KAAK,MAAM,KAAK,OAAO;AAAA,WAC3B,KAAK,MAAM,IAAI,KAAK,OAAO;AAAA,WAC3B,KAAK,MAAM,IAAI,KAAK,OAAO;AAAA,WAC3B,KAAK,MAAM,IAAI,KAAK,MAAMJ,MAAK;AAAA,WAC/B,KAAK,MAAM,IAAI,KAAK,MAAMI,MAAK;AAAA,QAClC;AAAA,MACF;AAEA,eAAS,MAAM,SAAS;AACtB,aAAK,WAAW;AAAA,MAClB;AAEA,YAAM,YAAY;AAAA,QAChB,WAAW,WAAW;AACpB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,SAAS,WAAW;AAClB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,WAAW,WAAW;AACpB,eAAK,MAAM,KAAK,MAChB,KAAK,MAAM,KAAK,MAAM;AACtB,eAAK,SAAS;AAAA,QAChB;AAAA,QACA,SAAS,WAAW;AAClB,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,oBAAM,MAAM,KAAK,KAAK,KAAK,GAAG;AAAA,YACtC,KAAK;AAAG,mBAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,UACpD;AACA,cAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,eAAK,QAAQ,IAAI,KAAK;AAAA,QACxB;AAAA,QACA,OAAO,SAASJ,IAAGI,IAAG;AACpB,UAAAJ,KAAI,CAACA,IAAGI,KAAI,CAACA;AACb,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,QAAQ,KAAK,SAAS,OAAOJ,IAAGI,EAAC,IAAI,KAAK,SAAS,OAAOJ,IAAGI,EAAC;AAAG;AAAA,YAC/F,KAAK;AAAG,mBAAK,SAAS;AAAG;AAAA,YACzB,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,SAAS,QAAQ,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,IAAI,KAAK,MAAM,KAAK,OAAO,CAAC;AAAA,YAC1G;AAAS,oBAAM,MAAMJ,IAAGI,EAAC;AAAG;AAAA,UAC9B;AACA,eAAK,MAAM,KAAK,KAAK,KAAK,MAAMJ;AAChC,eAAK,MAAM,KAAK,KAAK,KAAK,MAAMI;AAAA,QAClC;AAAA,MACF;AAEA,eAAS,MAAM,SAAS;AACtB,eAAO,IAAI,MAAM,OAAO;AAAA,MAC1B;AAEA,eAAS,YAAY,SAAS;AAC5B,aAAK,WAAW;AAAA,MAClB;AAEA,kBAAY,YAAY;AAAA,QACtB,WAAW;AAAA,QACX,SAAS;AAAA,QACT,WAAW,WAAW;AACpB,eAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MACjD,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACvD,eAAK,SAAS;AAAA,QAChB;AAAA,QACA,SAAS,WAAW;AAClB,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK,GAAG;AACN,mBAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,mBAAK,SAAS,UAAU;AACxB;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,CAAC;AACjF,mBAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,MAAM,IAAI,KAAK,OAAO,CAAC;AACjF,mBAAK,SAAS,UAAU;AACxB;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,mBAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,mBAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,OAAO,SAASJ,IAAGI,IAAG;AACpB,UAAAJ,KAAI,CAACA,IAAGI,KAAI,CAACA;AACb,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,MAAMJ,IAAG,KAAK,MAAMI;AAAG;AAAA,YACrD,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,MAAMJ,IAAG,KAAK,MAAMI;AAAG;AAAA,YACrD,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,MAAMJ,IAAG,KAAK,MAAMI;AAAG,mBAAK,SAAS,QAAQ,KAAK,MAAM,IAAI,KAAK,MAAMJ,MAAK,IAAI,KAAK,MAAM,IAAI,KAAK,MAAMI,MAAK,CAAC;AAAG;AAAA,YACjJ;AAAS,oBAAM,MAAMJ,IAAGI,EAAC;AAAG;AAAA,UAC9B;AACA,eAAK,MAAM,KAAK,KAAK,KAAK,MAAMJ;AAChC,eAAK,MAAM,KAAK,KAAK,KAAK,MAAMI;AAAA,QAClC;AAAA,MACF;AAEA,eAAS,YAAY,SAAS;AAC5B,eAAO,IAAI,YAAY,OAAO;AAAA,MAChC;AAEA,eAAS,UAAU,SAAS;AAC1B,aAAK,WAAW;AAAA,MAClB;AAEA,gBAAU,YAAY;AAAA,QACpB,WAAW,WAAW;AACpB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,SAAS,WAAW;AAClB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,WAAW,WAAW;AACpB,eAAK,MAAM,KAAK,MAChB,KAAK,MAAM,KAAK,MAAM;AACtB,eAAK,SAAS;AAAA,QAChB;AAAA,QACA,SAAS,WAAW;AAClB,cAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,eAAK,QAAQ,IAAI,KAAK;AAAA,QACxB;AAAA,QACA,OAAO,SAASJ,IAAGI,IAAG;AACpB,UAAAJ,KAAI,CAACA,IAAGI,KAAI,CAACA;AACb,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS;AAAG;AAAA,YACzB,KAAK;AAAG,mBAAK,SAAS;AAAG;AAAA,YACzB,KAAK;AAAG,mBAAK,SAAS;AAAG,kBAAI,MAAM,KAAK,MAAM,IAAI,KAAK,MAAMJ,MAAK,GAAG,MAAM,KAAK,MAAM,IAAI,KAAK,MAAMI,MAAK;AAAG,mBAAK,QAAQ,KAAK,SAAS,OAAO,IAAI,EAAE,IAAI,KAAK,SAAS,OAAO,IAAI,EAAE;AAAG;AAAA,YACvL,KAAK;AAAG,mBAAK,SAAS;AAAA,YACtB;AAAS,oBAAM,MAAMJ,IAAGI,EAAC;AAAG;AAAA,UAC9B;AACA,eAAK,MAAM,KAAK,KAAK,KAAK,MAAMJ;AAChC,eAAK,MAAM,KAAK,KAAK,KAAK,MAAMI;AAAA,QAClC;AAAA,MACF;AAEA,eAAS,UAAU,SAAS;AAC1B,eAAO,IAAI,UAAU,OAAO;AAAA,MAC9B;AAEA,eAAS,OAAO,SAAS,MAAM;AAC7B,aAAK,SAAS,IAAI,MAAM,OAAO;AAC/B,aAAK,QAAQ;AAAA,MACf;AAEA,aAAO,YAAY;AAAA,QACjB,WAAW,WAAW;AACpB,eAAK,KAAK,CAAC;AACX,eAAK,KAAK,CAAC;AACX,eAAK,OAAO,UAAU;AAAA,QACxB;AAAA,QACA,SAAS,WAAW;AAClB,cAAIJ,KAAI,KAAK,IACTI,KAAI,KAAK,IACT,IAAIJ,GAAE,SAAS;AAEnB,cAAI,IAAI,GAAG;AACT,gBAAI,KAAKA,GAAE,CAAC,GACR,KAAKI,GAAE,CAAC,GACR,KAAKJ,GAAE,CAAC,IAAI,IACZ,KAAKI,GAAE,CAAC,IAAI,IACZ,IAAI,IACJ;AAEJ,mBAAO,EAAE,KAAK,GAAG;AACf,kBAAI,IAAI;AACR,mBAAK,OAAO;AAAA,gBACV,KAAK,QAAQJ,GAAE,CAAC,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI;AAAA,gBACjD,KAAK,QAAQI,GAAE,CAAC,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI;AAAA,cACnD;AAAA,YACF;AAAA,UACF;AAEA,eAAK,KAAK,KAAK,KAAK;AACpB,eAAK,OAAO,QAAQ;AAAA,QACtB;AAAA,QACA,OAAO,SAASJ,IAAGI,IAAG;AACpB,eAAK,GAAG,KAAK,CAACJ,EAAC;AACf,eAAK,GAAG,KAAK,CAACI,EAAC;AAAA,QACjB;AAAA,MACF;AAEA,UAAI,SAAU,SAAS,OAAO,MAAM;AAElC,iBAASY,QAAO,SAAS;AACvB,iBAAO,SAAS,IAAI,IAAI,MAAM,OAAO,IAAI,IAAI,OAAO,SAAS,IAAI;AAAA,QACnE;AAEA,QAAAA,QAAO,OAAO,SAASC,OAAM;AAC3B,iBAAO,OAAO,CAACA,KAAI;AAAA,QACrB;AAEA,eAAOD;AAAA,MACT,EAAG,IAAI;AAEP,eAAS,QAAQ,MAAMhB,IAAGI,IAAG;AAC3B,aAAK,SAAS;AAAA,UACZ,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAAA,UACtC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK;AAAA,UACtC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAMJ;AAAA,UACjC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAMI;AAAA,UACjC,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAEA,eAAS,SAAS,SAAS,SAAS;AAClC,aAAK,WAAW;AAChB,aAAK,MAAM,IAAI,WAAW;AAAA,MAC5B;AAEA,eAAS,YAAY;AAAA,QACnB,WAAW,WAAW;AACpB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,SAAS,WAAW;AAClB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,WAAW,WAAW;AACpB,eAAK,MAAM,KAAK,MAAM,KAAK,MAC3B,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACjC,eAAK,SAAS;AAAA,QAChB;AAAA,QACA,SAAS,WAAW;AAClB,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,YAClD,KAAK;AAAG,sBAAQ,MAAM,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,UAC7C;AACA,cAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,eAAK,QAAQ,IAAI,KAAK;AAAA,QACxB;AAAA,QACA,OAAO,SAASJ,IAAGI,IAAG;AACpB,UAAAJ,KAAI,CAACA,IAAGI,KAAI,CAACA;AACb,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,QAAQ,KAAK,SAAS,OAAOJ,IAAGI,EAAC,IAAI,KAAK,SAAS,OAAOJ,IAAGI,EAAC;AAAG;AAAA,YAC/F,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,MAAMJ,IAAG,KAAK,MAAMI;AAAG;AAAA,YACrD,KAAK;AAAG,mBAAK,SAAS;AAAA,YACtB;AAAS,sBAAQ,MAAMJ,IAAGI,EAAC;AAAG;AAAA,UAChC;AACA,eAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMJ;AACrD,eAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMI;AAAA,QACvD;AAAA,MACF;AAEA,UAAI,WAAY,SAAS,OAAO,SAAS;AAEvC,iBAASc,UAAS,SAAS;AACzB,iBAAO,IAAI,SAAS,SAAS,OAAO;AAAA,QACtC;AAEA,QAAAA,UAAS,UAAU,SAASC,UAAS;AACnC,iBAAO,OAAO,CAACA,QAAO;AAAA,QACxB;AAEA,eAAOD;AAAA,MACT,EAAG,CAAC;AAEJ,eAAS,eAAe,SAAS,SAAS;AACxC,aAAK,WAAW;AAChB,aAAK,MAAM,IAAI,WAAW;AAAA,MAC5B;AAEA,qBAAe,YAAY;AAAA,QACzB,WAAW;AAAA,QACX,SAAS;AAAA,QACT,WAAW,WAAW;AACpB,eAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAC5D,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAClE,eAAK,SAAS;AAAA,QAChB;AAAA,QACA,SAAS,WAAW;AAClB,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK,GAAG;AACN,mBAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,mBAAK,SAAS,UAAU;AACxB;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,mBAAK,SAAS,UAAU;AACxB;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,mBAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,mBAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,OAAO,SAASlB,IAAGI,IAAG;AACpB,UAAAJ,KAAI,CAACA,IAAGI,KAAI,CAACA;AACb,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,MAAMJ,IAAG,KAAK,MAAMI;AAAG;AAAA,YACrD,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,SAAS,OAAO,KAAK,MAAMJ,IAAG,KAAK,MAAMI,EAAC;AAAG;AAAA,YAC3E,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,MAAMJ,IAAG,KAAK,MAAMI;AAAG;AAAA,YACrD;AAAS,sBAAQ,MAAMJ,IAAGI,EAAC;AAAG;AAAA,UAChC;AACA,eAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMJ;AACrD,eAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMI;AAAA,QACvD;AAAA,MACF;AAEA,UAAI,iBAAkB,SAAS,OAAO,SAAS;AAE7C,iBAASc,UAAS,SAAS;AACzB,iBAAO,IAAI,eAAe,SAAS,OAAO;AAAA,QAC5C;AAEA,QAAAA,UAAS,UAAU,SAASC,UAAS;AACnC,iBAAO,OAAO,CAACA,QAAO;AAAA,QACxB;AAEA,eAAOD;AAAA,MACT,EAAG,CAAC;AAEJ,eAAS,aAAa,SAAS,SAAS;AACtC,aAAK,WAAW;AAChB,aAAK,MAAM,IAAI,WAAW;AAAA,MAC5B;AAEA,mBAAa,YAAY;AAAA,QACvB,WAAW,WAAW;AACpB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,SAAS,WAAW;AAClB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,WAAW,WAAW;AACpB,eAAK,MAAM,KAAK,MAAM,KAAK,MAC3B,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACjC,eAAK,SAAS;AAAA,QAChB;AAAA,QACA,SAAS,WAAW;AAClB,cAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,eAAK,QAAQ,IAAI,KAAK;AAAA,QACxB;AAAA,QACA,OAAO,SAASlB,IAAGI,IAAG;AACpB,UAAAJ,KAAI,CAACA,IAAGI,KAAI,CAACA;AACb,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS;AAAG;AAAA,YACzB,KAAK;AAAG,mBAAK,SAAS;AAAG;AAAA,YACzB,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,QAAQ,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,YAC3H,KAAK;AAAG,mBAAK,SAAS;AAAA,YACtB;AAAS,sBAAQ,MAAMJ,IAAGI,EAAC;AAAG;AAAA,UAChC;AACA,eAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMJ;AACrD,eAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMI;AAAA,QACvD;AAAA,MACF;AAEA,UAAI,eAAgB,SAAS,OAAO,SAAS;AAE3C,iBAASc,UAAS,SAAS;AACzB,iBAAO,IAAI,aAAa,SAAS,OAAO;AAAA,QAC1C;AAEA,QAAAA,UAAS,UAAU,SAASC,UAAS;AACnC,iBAAO,OAAO,CAACA,QAAO;AAAA,QACxB;AAEA,eAAOD;AAAA,MACT,EAAG,CAAC;AAEJ,eAAS,QAAQ,MAAMlB,IAAGI,IAAG;AAC3B,YAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACVgB,MAAK,KAAK,KACVC,MAAK,KAAK;AAEd,YAAI,KAAK,SAAS,SAAS;AACzB,cAAIlB,KAAI,IAAI,KAAK,UAAU,IAAI,KAAK,SAAS,KAAK,SAAS,KAAK,SAC5D,IAAI,IAAI,KAAK,UAAU,KAAK,SAAS,KAAK;AAC9C,gBAAM,KAAKA,KAAI,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,WAAW;AACpE,gBAAM,KAAKA,KAAI,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,WAAW;AAAA,QACtE;AAEA,YAAI,KAAK,SAAS,SAAS;AACzB,cAAI,IAAI,IAAI,KAAK,UAAU,IAAI,KAAK,SAAS,KAAK,SAAS,KAAK,SAC5D,IAAI,IAAI,KAAK,UAAU,KAAK,SAAS,KAAK;AAC9C,UAAAiB,OAAMA,MAAK,IAAI,KAAK,MAAM,KAAK,UAAUpB,KAAI,KAAK,WAAW;AAC7D,UAAAqB,OAAMA,MAAK,IAAI,KAAK,MAAM,KAAK,UAAUjB,KAAI,KAAK,WAAW;AAAA,QAC/D;AAEA,aAAK,SAAS,cAAc,IAAI,IAAIgB,KAAIC,KAAI,KAAK,KAAK,KAAK,GAAG;AAAA,MAChE;AAEA,eAAS,WAAW,SAAS,OAAO;AAClC,aAAK,WAAW;AAChB,aAAK,SAAS;AAAA,MAChB;AAEA,iBAAW,YAAY;AAAA,QACrB,WAAW,WAAW;AACpB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,SAAS,WAAW;AAClB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,WAAW,WAAW;AACpB,eAAK,MAAM,KAAK,MAAM,KAAK,MAC3B,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACjC,eAAK,SAAS,KAAK,SAAS,KAAK,SACjC,KAAK,UAAU,KAAK,UAAU,KAAK,UACnC,KAAK,SAAS;AAAA,QAChB;AAAA,QACA,SAAS,WAAW;AAClB,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,YAClD,KAAK;AAAG,mBAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,UAC1C;AACA,cAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,eAAK,QAAQ,IAAI,KAAK;AAAA,QACxB;AAAA,QACA,OAAO,SAASrB,IAAGI,IAAG;AACpB,UAAAJ,KAAI,CAACA,IAAGI,KAAI,CAACA;AAEb,cAAI,KAAK,QAAQ;AACf,gBAAI,MAAM,KAAK,MAAMJ,IACjB,MAAM,KAAK,MAAMI;AACrB,iBAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,CAAC;AAAA,UACrF;AAEA,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,QAAQ,KAAK,SAAS,OAAOJ,IAAGI,EAAC,IAAI,KAAK,SAAS,OAAOJ,IAAGI,EAAC;AAAG;AAAA,YAC/F,KAAK;AAAG,mBAAK,SAAS;AAAG;AAAA,YACzB,KAAK;AAAG,mBAAK,SAAS;AAAA,YACtB;AAAS,sBAAQ,MAAMJ,IAAGI,EAAC;AAAG;AAAA,UAChC;AAEA,eAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK;AAC9C,eAAK,UAAU,KAAK,SAAS,KAAK,UAAU,KAAK;AACjD,eAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMJ;AACrD,eAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMI;AAAA,QACvD;AAAA,MACF;AAEA,UAAI,aAAc,SAAS,OAAO,OAAO;AAEvC,iBAASkB,YAAW,SAAS;AAC3B,iBAAO,QAAQ,IAAI,WAAW,SAAS,KAAK,IAAI,IAAI,SAAS,SAAS,CAAC;AAAA,QACzE;AAEA,QAAAA,YAAW,QAAQ,SAASC,QAAO;AACjC,iBAAO,OAAO,CAACA,MAAK;AAAA,QACtB;AAEA,eAAOD;AAAA,MACT,EAAG,GAAG;AAEN,eAAS,iBAAiB,SAAS,OAAO;AACxC,aAAK,WAAW;AAChB,aAAK,SAAS;AAAA,MAChB;AAEA,uBAAiB,YAAY;AAAA,QAC3B,WAAW;AAAA,QACX,SAAS;AAAA,QACT,WAAW,WAAW;AACpB,eAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAC5D,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AAClE,eAAK,SAAS,KAAK,SAAS,KAAK,SACjC,KAAK,UAAU,KAAK,UAAU,KAAK,UACnC,KAAK,SAAS;AAAA,QAChB;AAAA,QACA,SAAS,WAAW;AAClB,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK,GAAG;AACN,mBAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,mBAAK,SAAS,UAAU;AACxB;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AACvC,mBAAK,SAAS,UAAU;AACxB;AAAA,YACF;AAAA,YACA,KAAK,GAAG;AACN,mBAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,mBAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B,mBAAK,MAAM,KAAK,KAAK,KAAK,GAAG;AAC7B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,OAAO,SAAStB,IAAGI,IAAG;AACpB,UAAAJ,KAAI,CAACA,IAAGI,KAAI,CAACA;AAEb,cAAI,KAAK,QAAQ;AACf,gBAAI,MAAM,KAAK,MAAMJ,IACjB,MAAM,KAAK,MAAMI;AACrB,iBAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,CAAC;AAAA,UACrF;AAEA,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,MAAMJ,IAAG,KAAK,MAAMI;AAAG;AAAA,YACrD,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,SAAS,OAAO,KAAK,MAAMJ,IAAG,KAAK,MAAMI,EAAC;AAAG;AAAA,YAC3E,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,MAAMJ,IAAG,KAAK,MAAMI;AAAG;AAAA,YACrD;AAAS,sBAAQ,MAAMJ,IAAGI,EAAC;AAAG;AAAA,UAChC;AAEA,eAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK;AAC9C,eAAK,UAAU,KAAK,SAAS,KAAK,UAAU,KAAK;AACjD,eAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMJ;AACrD,eAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMI;AAAA,QACvD;AAAA,MACF;AAEA,UAAI,mBAAoB,SAAS,OAAO,OAAO;AAE7C,iBAASkB,YAAW,SAAS;AAC3B,iBAAO,QAAQ,IAAI,iBAAiB,SAAS,KAAK,IAAI,IAAI,eAAe,SAAS,CAAC;AAAA,QACrF;AAEA,QAAAA,YAAW,QAAQ,SAASC,QAAO;AACjC,iBAAO,OAAO,CAACA,MAAK;AAAA,QACtB;AAEA,eAAOD;AAAA,MACT,EAAG,GAAG;AAEN,eAAS,eAAe,SAAS,OAAO;AACtC,aAAK,WAAW;AAChB,aAAK,SAAS;AAAA,MAChB;AAEA,qBAAe,YAAY;AAAA,QACzB,WAAW,WAAW;AACpB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,SAAS,WAAW;AAClB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,WAAW,WAAW;AACpB,eAAK,MAAM,KAAK,MAAM,KAAK,MAC3B,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM;AACjC,eAAK,SAAS,KAAK,SAAS,KAAK,SACjC,KAAK,UAAU,KAAK,UAAU,KAAK,UACnC,KAAK,SAAS;AAAA,QAChB;AAAA,QACA,SAAS,WAAW;AAClB,cAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,eAAK,QAAQ,IAAI,KAAK;AAAA,QACxB;AAAA,QACA,OAAO,SAAStB,IAAGI,IAAG;AACpB,UAAAJ,KAAI,CAACA,IAAGI,KAAI,CAACA;AAEb,cAAI,KAAK,QAAQ;AACf,gBAAI,MAAM,KAAK,MAAMJ,IACjB,MAAM,KAAK,MAAMI;AACrB,iBAAK,SAAS,KAAK,KAAK,KAAK,UAAU,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM,CAAC;AAAA,UACrF;AAEA,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS;AAAG;AAAA,YACzB,KAAK;AAAG,mBAAK,SAAS;AAAG;AAAA,YACzB,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,QAAQ,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,YAC3H,KAAK;AAAG,mBAAK,SAAS;AAAA,YACtB;AAAS,sBAAQ,MAAMJ,IAAGI,EAAC;AAAG;AAAA,UAChC;AAEA,eAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,KAAK;AAC9C,eAAK,UAAU,KAAK,SAAS,KAAK,UAAU,KAAK;AACjD,eAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMJ;AACrD,eAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAMI;AAAA,QACvD;AAAA,MACF;AAEA,UAAI,iBAAkB,SAAS,OAAO,OAAO;AAE3C,iBAASkB,YAAW,SAAS;AAC3B,iBAAO,QAAQ,IAAI,eAAe,SAAS,KAAK,IAAI,IAAI,aAAa,SAAS,CAAC;AAAA,QACjF;AAEA,QAAAA,YAAW,QAAQ,SAASC,QAAO;AACjC,iBAAO,OAAO,CAACA,MAAK;AAAA,QACtB;AAEA,eAAOD;AAAA,MACT,EAAG,GAAG;AAEN,eAAS,aAAa,SAAS;AAC7B,aAAK,WAAW;AAAA,MAClB;AAEA,mBAAa,YAAY;AAAA,QACvB,WAAW;AAAA,QACX,SAAS;AAAA,QACT,WAAW,WAAW;AACpB,eAAK,SAAS;AAAA,QAChB;AAAA,QACA,SAAS,WAAW;AAClB,cAAI,KAAK,OAAQ,MAAK,SAAS,UAAU;AAAA,QAC3C;AAAA,QACA,OAAO,SAAStB,IAAGI,IAAG;AACpB,UAAAJ,KAAI,CAACA,IAAGI,KAAI,CAACA;AACb,cAAI,KAAK,OAAQ,MAAK,SAAS,OAAOJ,IAAGI,EAAC;AAAA,cACrC,MAAK,SAAS,GAAG,KAAK,SAAS,OAAOJ,IAAGI,EAAC;AAAA,QACjD;AAAA,MACF;AAEA,eAAS,aAAa,SAAS;AAC7B,eAAO,IAAI,aAAa,OAAO;AAAA,MACjC;AAEA,eAAS,KAAKJ,IAAG;AACf,eAAOA,KAAI,IAAI,KAAK;AAAA,MACtB;AAMA,eAAS,OAAO,MAAM,IAAI,IAAI;AAC5B,YAAI,KAAK,KAAK,MAAM,KAAK,KACrB,KAAK,KAAK,KAAK,KACf,MAAM,KAAK,MAAM,KAAK,QAAQ,MAAM,KAAK,KAAK,KAC9C,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,KAAK,KACxC,KAAK,KAAK,KAAK,KAAK,OAAO,KAAK;AACpC,gBAAQ,KAAK,EAAE,IAAI,KAAK,EAAE,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG,MAAM,KAAK,IAAI,CAAC,CAAC,KAAK;AAAA,MAC5F;AAGA,eAAS,OAAO,MAAM,GAAG;AACvB,YAAI,IAAI,KAAK,MAAM,KAAK;AACxB,eAAO,KAAK,KAAK,KAAK,MAAM,KAAK,OAAO,IAAI,KAAK,IAAI;AAAA,MACvD;AAKA,eAAS,QAAQ,MAAM,IAAI,IAAI;AAC7B,YAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,MAAM,KAAK,MAAM;AACrB,aAAK,SAAS,cAAc,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,EAAE;AAAA,MAClF;AAEA,eAAS,UAAU,SAAS;AAC1B,aAAK,WAAW;AAAA,MAClB;AAEA,gBAAU,YAAY;AAAA,QACpB,WAAW,WAAW;AACpB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,SAAS,WAAW;AAClB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,WAAW,WAAW;AACpB,eAAK,MAAM,KAAK,MAChB,KAAK,MAAM,KAAK,MAChB,KAAK,MAAM;AACX,eAAK,SAAS;AAAA,QAChB;AAAA,QACA,SAAS,WAAW;AAClB,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS,OAAO,KAAK,KAAK,KAAK,GAAG;AAAG;AAAA,YAClD,KAAK;AAAG,sBAAQ,MAAM,KAAK,KAAK,OAAO,MAAM,KAAK,GAAG,CAAC;AAAG;AAAA,UAC3D;AACA,cAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,eAAK,QAAQ,IAAI,KAAK;AAAA,QACxB;AAAA,QACA,OAAO,SAASA,IAAGI,IAAG;AACpB,cAAI,KAAK;AAET,UAAAJ,KAAI,CAACA,IAAGI,KAAI,CAACA;AACb,cAAIJ,OAAM,KAAK,OAAOI,OAAM,KAAK,IAAK;AACtC,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,QAAQ,KAAK,SAAS,OAAOJ,IAAGI,EAAC,IAAI,KAAK,SAAS,OAAOJ,IAAGI,EAAC;AAAG;AAAA,YAC/F,KAAK;AAAG,mBAAK,SAAS;AAAG;AAAA,YACzB,KAAK;AAAG,mBAAK,SAAS;AAAG,sBAAQ,MAAM,OAAO,MAAM,KAAK,OAAO,MAAMJ,IAAGI,EAAC,CAAC,GAAG,EAAE;AAAG;AAAA,YACnF;AAAS,sBAAQ,MAAM,KAAK,KAAK,KAAK,OAAO,MAAMJ,IAAGI,EAAC,CAAC;AAAG;AAAA,UAC7D;AAEA,eAAK,MAAM,KAAK,KAAK,KAAK,MAAMJ;AAChC,eAAK,MAAM,KAAK,KAAK,KAAK,MAAMI;AAChC,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAEA,eAAS,UAAU,SAAS;AAC1B,aAAK,WAAW,IAAI,eAAe,OAAO;AAAA,MAC5C;AAEA,OAAC,UAAU,YAAY,OAAO,OAAO,UAAU,SAAS,GAAG,QAAQ,SAASJ,IAAGI,IAAG;AAChF,kBAAU,UAAU,MAAM,KAAK,MAAMA,IAAGJ,EAAC;AAAA,MAC3C;AAEA,eAAS,eAAe,SAAS;AAC/B,aAAK,WAAW;AAAA,MAClB;AAEA,qBAAe,YAAY;AAAA,QACzB,QAAQ,SAASA,IAAGI,IAAG;AAAE,eAAK,SAAS,OAAOA,IAAGJ,EAAC;AAAA,QAAG;AAAA,QACrD,WAAW,WAAW;AAAE,eAAK,SAAS,UAAU;AAAA,QAAG;AAAA,QACnD,QAAQ,SAASA,IAAGI,IAAG;AAAE,eAAK,SAAS,OAAOA,IAAGJ,EAAC;AAAA,QAAG;AAAA,QACrD,eAAe,SAAS,IAAI,IAAI,IAAI,IAAIA,IAAGI,IAAG;AAAE,eAAK,SAAS,cAAc,IAAI,IAAI,IAAI,IAAIA,IAAGJ,EAAC;AAAA,QAAG;AAAA,MACrG;AAEA,eAAS,UAAU,SAAS;AAC1B,eAAO,IAAI,UAAU,OAAO;AAAA,MAC9B;AAEA,eAAS,UAAU,SAAS;AAC1B,eAAO,IAAI,UAAU,OAAO;AAAA,MAC9B;AAEA,eAAS,QAAQ,SAAS;AACxB,aAAK,WAAW;AAAA,MAClB;AAEA,cAAQ,YAAY;AAAA,QAClB,WAAW,WAAW;AACpB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,SAAS,WAAW;AAClB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,WAAW,WAAW;AACpB,eAAK,KAAK,CAAC;AACX,eAAK,KAAK,CAAC;AAAA,QACb;AAAA,QACA,SAAS,WAAW;AAClB,cAAIA,KAAI,KAAK,IACTI,KAAI,KAAK,IACT,IAAIJ,GAAE;AAEV,cAAI,GAAG;AACL,iBAAK,QAAQ,KAAK,SAAS,OAAOA,GAAE,CAAC,GAAGI,GAAE,CAAC,CAAC,IAAI,KAAK,SAAS,OAAOJ,GAAE,CAAC,GAAGI,GAAE,CAAC,CAAC;AAC/E,gBAAI,MAAM,GAAG;AACX,mBAAK,SAAS,OAAOJ,GAAE,CAAC,GAAGI,GAAE,CAAC,CAAC;AAAA,YACjC,OAAO;AACL,kBAAI,KAAK,cAAcJ,EAAC,GACpB,KAAK,cAAcI,EAAC;AACxB,uBAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,EAAE,IAAI,EAAE,IAAI;AAC3C,qBAAK,SAAS,cAAc,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,GAAGJ,GAAE,EAAE,GAAGI,GAAE,EAAE,CAAC;AAAA,cACtF;AAAA,YACF;AAAA,UACF;AAEA,cAAI,KAAK,SAAU,KAAK,UAAU,KAAK,MAAM,EAAI,MAAK,SAAS,UAAU;AACzE,eAAK,QAAQ,IAAI,KAAK;AACtB,eAAK,KAAK,KAAK,KAAK;AAAA,QACtB;AAAA,QACA,OAAO,SAASJ,IAAGI,IAAG;AACpB,eAAK,GAAG,KAAK,CAACJ,EAAC;AACf,eAAK,GAAG,KAAK,CAACI,EAAC;AAAA,QACjB;AAAA,MACF;AAGA,eAAS,cAAcJ,IAAG;AACxB,YAAI,GACA,IAAIA,GAAE,SAAS,GACf,GACAG,KAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC,GACf,IAAI,IAAI,MAAM,CAAC;AACnB,QAAAA,GAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAIH,GAAE,CAAC,IAAI,IAAIA,GAAE,CAAC;AACzC,aAAK,IAAI,GAAG,IAAI,IAAI,GAAG,EAAE,EAAG,CAAAG,GAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,IAAIH,GAAE,CAAC,IAAI,IAAIA,GAAE,IAAI,CAAC;AAC7E,QAAAG,GAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,IAAIH,GAAE,IAAI,CAAC,IAAIA,GAAE,CAAC;AACzD,aAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,KAAIG,GAAE,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,IAAI,CAAC;AAC3E,QAAAA,GAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AAC7B,aAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG,CAAAA,GAAE,CAAC,KAAK,EAAE,CAAC,IAAIA,GAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AAC3D,UAAE,IAAI,CAAC,KAAKH,GAAE,CAAC,IAAIG,GAAE,IAAI,CAAC,KAAK;AAC/B,aAAK,IAAI,GAAG,IAAI,IAAI,GAAG,EAAE,EAAG,GAAE,CAAC,IAAI,IAAIH,GAAE,IAAI,CAAC,IAAIG,GAAE,IAAI,CAAC;AACzD,eAAO,CAACA,IAAG,CAAC;AAAA,MACd;AAEA,eAAS,QAAQ,SAAS;AACxB,eAAO,IAAI,QAAQ,OAAO;AAAA,MAC5B;AAEA,eAAS,KAAK,SAAS,GAAG;AACxB,aAAK,WAAW;AAChB,aAAK,KAAK;AAAA,MACZ;AAEA,WAAK,YAAY;AAAA,QACf,WAAW,WAAW;AACpB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,SAAS,WAAW;AAClB,eAAK,QAAQ;AAAA,QACf;AAAA,QACA,WAAW,WAAW;AACpB,eAAK,KAAK,KAAK,KAAK;AACpB,eAAK,SAAS;AAAA,QAChB;AAAA,QACA,SAAS,WAAW;AAClB,cAAI,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,WAAW,EAAG,MAAK,SAAS,OAAO,KAAK,IAAI,KAAK,EAAE;AAC1F,cAAI,KAAK,SAAU,KAAK,UAAU,KAAK,KAAK,WAAW,EAAI,MAAK,SAAS,UAAU;AACnF,cAAI,KAAK,SAAS,EAAG,MAAK,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,IAAI,KAAK;AAAA,QACpE;AAAA,QACA,OAAO,SAASH,IAAGI,IAAG;AACpB,UAAAJ,KAAI,CAACA,IAAGI,KAAI,CAACA;AACb,kBAAQ,KAAK,QAAQ;AAAA,YACnB,KAAK;AAAG,mBAAK,SAAS;AAAG,mBAAK,QAAQ,KAAK,SAAS,OAAOJ,IAAGI,EAAC,IAAI,KAAK,SAAS,OAAOJ,IAAGI,EAAC;AAAG;AAAA,YAC/F,KAAK;AAAG,mBAAK,SAAS;AAAA,YACtB,SAAS;AACP,kBAAI,KAAK,MAAM,GAAG;AAChB,qBAAK,SAAS,OAAO,KAAK,IAAIA,EAAC;AAC/B,qBAAK,SAAS,OAAOJ,IAAGI,EAAC;AAAA,cAC3B,OAAO;AACL,oBAAI,KAAK,KAAK,MAAM,IAAI,KAAK,MAAMJ,KAAI,KAAK;AAC5C,qBAAK,SAAS,OAAO,IAAI,KAAK,EAAE;AAChC,qBAAK,SAAS,OAAO,IAAII,EAAC;AAAA,cAC5B;AACA;AAAA,YACF;AAAA,UACF;AACA,eAAK,KAAKJ,IAAG,KAAK,KAAKI;AAAA,QACzB;AAAA,MACF;AAEA,eAAS,KAAK,SAAS;AACrB,eAAO,IAAI,KAAK,SAAS,GAAG;AAAA,MAC9B;AAEA,eAAS,WAAW,SAAS;AAC3B,eAAO,IAAI,KAAK,SAAS,CAAC;AAAA,MAC5B;AAEA,eAAS,UAAU,SAAS;AAC1B,eAAO,IAAI,KAAK,SAAS,CAAC;AAAA,MAC5B;AAEA,eAAS,KAAK,QAAQ,OAAO;AAC3B,YAAI,GAAG,IAAI,OAAO,UAAU,GAAI;AAChC,iBAAS,IAAI,GAAG,GAAG,IAAI,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC1E,eAAK,IAAI,KAAK,OAAO,MAAM,CAAC,CAAC;AAC7B,eAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,eAAG,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;AAAA,UAC7D;AAAA,QACF;AAAA,MACF;AAEA,eAAS,OAAO,QAAQ;AACtB,YAAI,IAAI,OAAO,QAAQ,IAAI,IAAI,MAAM,CAAC;AACtC,eAAO,EAAE,KAAK,EAAG,GAAE,CAAC,IAAI;AACxB,eAAO;AAAA,MACT;AAEA,eAAS,WAAW,GAAG,KAAK;AAC1B,eAAO,EAAE,GAAG;AAAA,MACd;AAEA,eAAS,QAAQ;AACf,YAAI,OAAO,SAAS,CAAC,CAAC,GAClB,QAAQ,QACR,SAAS,MACT,QAAQ;AAEZ,iBAASoB,OAAM,MAAM;AACnB,cAAI,KAAK,KAAK,MAAM,MAAM,SAAS,GAC/B,GACA,IAAI,KAAK,QACT,IAAI,GAAG,QACP,KAAK,IAAI,MAAM,CAAC,GAChB;AAEJ,eAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,qBAAS,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,EAAE,GAAG;AACtE,iBAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;AAC9C,kBAAI,OAAO,KAAK,CAAC;AAAA,YACnB;AACA,eAAG,MAAM;AAAA,UACX;AAEA,eAAK,IAAI,GAAG,KAAK,MAAM,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG;AACtC,eAAG,GAAG,CAAC,CAAC,EAAE,QAAQ;AAAA,UACpB;AAEA,iBAAO,IAAI,EAAE;AACb,iBAAO;AAAA,QACT;AAEA,QAAAA,OAAM,OAAO,SAAS,GAAG;AACvB,iBAAO,UAAU,UAAU,OAAO,OAAO,MAAM,aAAa,IAAI,SAAS,MAAM,KAAK,CAAC,CAAC,GAAGA,UAAS;AAAA,QACpG;AAEA,QAAAA,OAAM,QAAQ,SAAS,GAAG;AACxB,iBAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,UAAS;AAAA,QAC1F;AAEA,QAAAA,OAAM,QAAQ,SAAS,GAAG;AACxB,iBAAO,UAAU,UAAU,QAAQ,KAAK,OAAO,SAAS,OAAO,MAAM,aAAa,IAAI,SAAS,MAAM,KAAK,CAAC,CAAC,GAAGA,UAAS;AAAA,QAC1H;AAEA,QAAAA,OAAM,SAAS,SAAS,GAAG;AACzB,iBAAO,UAAU,UAAU,SAAS,KAAK,OAAO,OAAO,GAAGA,UAAS;AAAA,QACrE;AAEA,eAAOA;AAAA,MACT;AAEA,eAAS,OAAO,QAAQ,OAAO;AAC7B,YAAI,GAAG,IAAI,OAAO,UAAU,GAAI;AAChC,iBAAS,GAAG,GAAG,IAAI,GAAG,IAAI,OAAO,CAAC,EAAE,QAAQpB,IAAG,IAAI,GAAG,EAAE,GAAG;AACzD,eAAKA,KAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,MAAK,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;AACpD,cAAIA,GAAG,MAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,QAAO,CAAC,EAAE,CAAC,EAAE,CAAC,KAAKA;AAAA,QACpD;AACA,aAAK,QAAQ,KAAK;AAAA,MACpB;AAEA,eAAS,UAAU,QAAQ,OAAO;AAChC,YAAI,GAAG,IAAI,OAAO,UAAU,GAAI;AAChC,iBAAS,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,CAAC,CAAC,EAAE,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC5E,eAAK,KAAK,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACnC,iBAAK,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG;AAClD,gBAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,MAAM;AAAA,YAC1B,WAAW,KAAK,GAAG;AACjB,gBAAE,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,MAAM;AAAA,YAC1B,OAAO;AACL,gBAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI;AAAA,YACnB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,eAAS,WAAW,QAAQ,OAAO;AACjC,YAAI,GAAG,IAAI,OAAO,UAAU,GAAI;AAChC,iBAAS,IAAI,GAAG,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,GAAG;AACnE,mBAAS,IAAI,GAAGA,KAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,MAAK,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;AAC3D,aAAG,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAACA,KAAI;AAAA,QAC9B;AACA,aAAK,QAAQ,KAAK;AAAA,MACpB;AAEA,eAAS,OAAO,QAAQ,OAAO;AAC7B,YAAI,GAAG,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,KAAK,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU,GAAI;AAC/E,iBAASA,KAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG;AAC3C,mBAAS,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1C,gBAAI,KAAK,OAAO,MAAM,CAAC,CAAC,GACpB,OAAO,GAAG,CAAC,EAAE,CAAC,KAAK,GACnB,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,GACvB,MAAM,OAAO,QAAQ;AACzB,qBAASG,KAAI,GAAGA,KAAI,GAAG,EAAEA,IAAG;AAC1B,kBAAI,KAAK,OAAO,MAAMA,EAAC,CAAC,GACpB,OAAO,GAAG,CAAC,EAAE,CAAC,KAAK,GACnB,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK;AAC3B,oBAAM,OAAO;AAAA,YACf;AACA,kBAAM,MAAM,MAAM,KAAK;AAAA,UACzB;AACA,aAAG,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,IAAIH;AAC/B,cAAI,GAAI,CAAAA,MAAK,KAAK;AAAA,QACpB;AACA,WAAG,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,IAAIA;AAC/B,aAAK,QAAQ,KAAK;AAAA,MACpB;AAEA,eAAS,WAAW,QAAQ;AAC1B,YAAI,QAAQ,OAAO,IAAI,IAAI;AAC3B,eAAO,OAAO,MAAM,EAAE,KAAK,SAASD,IAAG,GAAG;AAAE,iBAAO,MAAMA,EAAC,IAAI,MAAM,CAAC;AAAA,QAAG,CAAC;AAAA,MAC3E;AAEA,eAAS,KAAK,QAAQ;AACpB,YAAI,IAAI,IAAI,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,KAAK;AAC/C,eAAO,EAAE,IAAI,EAAG,MAAK,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,GAAI,MAAK,IAAI,IAAI;AAC5D,eAAO;AAAA,MACT;AAEA,eAAS,UAAU,QAAQ;AACzB,YAAI,OAAO,OAAO,IAAI,GAAG;AACzB,eAAO,OAAO,MAAM,EAAE,KAAK,SAASA,IAAG,GAAG;AAAE,iBAAO,KAAKA,EAAC,IAAI,KAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MACzE;AAEA,eAAS,IAAI,QAAQ;AACnB,YAAIW,KAAI,GAAG,IAAI,IAAI,IAAI,OAAO,QAAQ;AACtC,eAAO,EAAE,IAAI,EAAG,KAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAG,CAAAA,MAAK;AAC5C,eAAOA;AAAA,MACT;AAEA,eAAS,aAAa,QAAQ;AAC5B,eAAO,UAAU,MAAM,EAAE,QAAQ;AAAA,MACnC;AAEA,eAAS,UAAU,QAAQ;AACzB,YAAI,IAAI,OAAO,QACX,GACA,GACA,OAAO,OAAO,IAAI,GAAG,GACrB,QAAQ,WAAW,MAAM,GACzB,MAAM,GACN,SAAS,GACT,OAAO,CAAC,GACR,UAAU,CAAC;AAEf,aAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,cAAI,MAAM,CAAC;AACX,cAAI,MAAM,QAAQ;AAChB,mBAAO,KAAK,CAAC;AACb,iBAAK,KAAK,CAAC;AAAA,UACb,OAAO;AACL,sBAAU,KAAK,CAAC;AAChB,oBAAQ,KAAK,CAAC;AAAA,UAChB;AAAA,QACF;AAEA,eAAO,QAAQ,QAAQ,EAAE,OAAO,IAAI;AAAA,MACtC;AAEA,eAAS,QAAQ,QAAQ;AACvB,eAAO,OAAO,MAAM,EAAE,QAAQ;AAAA,MAChC;AAEA,MAAAf,SAAQ,MAAM;AACd,MAAAA,SAAQ,OAAO;AACf,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,mBAAmB;AAC3B,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,cAAc;AACtB,MAAAA,SAAQ,gBAAgB;AACxB,MAAAA,SAAQ,sBAAsB;AAC9B,MAAAA,SAAQ,oBAAoB;AAC5B,MAAAA,SAAQ,kBAAkB;AAC1B,MAAAA,SAAQ,wBAAwB;AAChC,MAAAA,SAAQ,sBAAsB;AAC9B,MAAAA,SAAQ,cAAc;AACtB,MAAAA,SAAQ,oBAAoB;AAC5B,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,eAAe;AACvB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,kBAAkB;AAC1B,MAAAA,SAAQ,OAAO;AACf,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,eAAe;AACvB,MAAAA,SAAQ,MAAM;AACd,MAAAA,SAAQ,cAAc;AACtB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,uBAAuB;AAC/B,MAAAA,SAAQ,oBAAoB;AAC5B,MAAAA,SAAQ,kBAAkB;AAC1B,MAAAA,SAAQ,wBAAwB;AAChC,MAAAA,SAAQ,oBAAoB;AAC5B,MAAAA,SAAQ,uBAAuB;AAC/B,MAAAA,SAAQ,sBAAsB;AAC9B,MAAAA,SAAQ,uBAAuB;AAC/B,MAAAA,SAAQ,sBAAsB;AAC9B,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,oBAAoB;AAC5B,MAAAA,SAAQ,SAAS;AACjB,MAAAA,SAAQ,eAAe;AACvB,MAAAA,SAAQ,cAAc;AACtB,MAAAA,SAAQ,gBAAgB;AACxB,MAAAA,SAAQ,eAAe;AACvB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,UAAU;AAElB,aAAO,eAAeA,UAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA,IAE5D,CAAC;AAAA;AAAA;;;AC55DD;AAAA;AACA,KAAC,SAAU,QAAQ,SAAS;AAC5B,aAAO,YAAY,YAAY,OAAO,WAAW,cAAc,QAAQ,SAAS,oBAAqB,kBAAmB,IACxH,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,CAAC,WAAW,YAAY,UAAU,GAAG,OAAO,KAC/F,SAAS,UAAU,MAAM,QAAQ,OAAO,KAAK,OAAO,MAAM,CAAC,GAAG,OAAO,IAAI,OAAO,EAAE;AAAA,IACnF,GAAE,SAAM,SAAU0B,UAAS,SAAS,SAAS;AAAE;AAE/C,eAAS,YAAY,GAAG;AACtB,eAAO,EAAE,OAAO;AAAA,MAClB;AAEA,eAAS,KAAK,MAAM;AAClB,eAAO,KAAK;AAAA,MACd;AAEA,eAAS,MAAM,MAAM,GAAG;AACtB,eAAO,IAAI,IAAI,KAAK;AAAA,MACtB;AAEA,eAAS,QAAQ,MAAM,GAAG;AACxB,eAAO,KAAK,YAAY,SAAS,KAAK,QAAQ,IAAI;AAAA,MACpD;AAEA,eAAS,OAAO,MAAM;AACpB,eAAO,KAAK,YAAY,SAAS,KAAK,QAChC,KAAK,YAAY,SAAS,QAAQ,IAAI,KAAK,aAAa,WAAW,IAAI,IACvE;AAAA,MACR;AAEA,eAAS,SAAS,GAAG;AACnB,eAAO,WAAW;AAChB,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,uBAAuB,GAAG,GAAG;AACpC,eAAO,iBAAiB,EAAE,QAAQ,EAAE,MAAM,KAAK,EAAE,QAAQ,EAAE;AAAA,MAC7D;AAEA,eAAS,uBAAuB,GAAG,GAAG;AACpC,eAAO,iBAAiB,EAAE,QAAQ,EAAE,MAAM,KAAK,EAAE,QAAQ,EAAE;AAAA,MAC7D;AAEA,eAAS,iBAAiB,GAAG,GAAG;AAC9B,eAAO,EAAE,KAAK,EAAE;AAAA,MAClB;AAEA,eAAS,MAAM,GAAG;AAChB,eAAO,EAAE;AAAA,MACX;AAEA,eAAS,UAAU,GAAG;AACpB,eAAO,EAAE;AAAA,MACX;AAEA,eAAS,aAAa,OAAO;AAC3B,eAAO,MAAM;AAAA,MACf;AAEA,eAAS,aAAa,OAAO;AAC3B,eAAO,MAAM;AAAA,MACf;AAEA,eAAS,KAAK,UAAU,IAAI;AAC1B,cAAM,OAAO,SAAS,IAAI,EAAE;AAC5B,YAAI,CAAC,KAAM,OAAM,IAAI,MAAM,cAAc,EAAE;AAC3C,eAAO;AAAA,MACT;AAEA,eAAS,oBAAoB,EAAC,MAAK,GAAG;AACpC,mBAAW,QAAQ,OAAO;AACxB,cAAI,KAAK,KAAK;AACd,cAAI,KAAK;AACT,qBAAW,QAAQ,KAAK,aAAa;AACnC,iBAAK,KAAK,KAAK,KAAK,QAAQ;AAC5B,kBAAM,KAAK;AAAA,UACb;AACA,qBAAW,QAAQ,KAAK,aAAa;AACnC,iBAAK,KAAK,KAAK,KAAK,QAAQ;AAC5B,kBAAM,KAAK;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAEA,eAAS,SAAS;AAChB,YAAI,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;AACjC,YAAI,KAAK;AACT,YAAI,KAAK,GAAG;AACZ,YAAI,KAAK;AACT,YAAI,QAAQ;AACZ,YAAI;AACJ,YAAI;AACJ,YAAI,QAAQ;AACZ,YAAI,QAAQ;AACZ,YAAI,aAAa;AAEjB,iBAAS,SAAS;AAChB,gBAAM,QAAQ,EAAC,OAAO,MAAM,MAAM,MAAM,SAAS,GAAG,OAAO,MAAM,MAAM,MAAM,SAAS,EAAC;AACvF,2BAAiB,KAAK;AACtB,4BAAkB,KAAK;AACvB,4BAAkB,KAAK;AACvB,6BAAmB,KAAK;AACxB,8BAAoB,KAAK;AACzB,8BAAoB,KAAK;AACzB,iBAAO;AAAA,QACT;AAEA,eAAO,SAAS,SAAS,OAAO;AAC9B,8BAAoB,KAAK;AACzB,iBAAO;AAAA,QACT;AAEA,eAAO,SAAS,SAAS,GAAG;AAC1B,iBAAO,UAAU,UAAU,KAAK,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAU;AAAA,QACvF;AAEA,eAAO,YAAY,SAAS,GAAG;AAC7B,iBAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAU;AAAA,QAC1F;AAEA,eAAO,WAAW,SAAS,GAAG;AAC5B,iBAAO,UAAU,UAAU,OAAO,GAAG,UAAU;AAAA,QACjD;AAEA,eAAO,YAAY,SAAS,GAAG;AAC7B,iBAAO,UAAU,UAAU,KAAK,CAAC,GAAG,UAAU;AAAA,QAChD;AAEA,eAAO,cAAc,SAAS,GAAG;AAC/B,iBAAO,UAAU,UAAU,KAAK,KAAK,CAAC,GAAG,UAAU;AAAA,QACrD;AAEA,eAAO,QAAQ,SAAS,GAAG;AACzB,iBAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAU;AAAA,QAC1F;AAEA,eAAO,QAAQ,SAAS,GAAG;AACzB,iBAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,UAAU;AAAA,QAC1F;AAEA,eAAO,WAAW,SAAS,GAAG;AAC5B,iBAAO,UAAU,UAAU,WAAW,GAAG,UAAU;AAAA,QACrD;AAEA,eAAO,OAAO,SAAS,GAAG;AACxB,iBAAO,UAAU,UAAU,KAAK,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,KAAK,IAAI,KAAK,EAAE;AAAA,QAC7F;AAEA,eAAO,SAAS,SAAS,GAAG;AAC1B,iBAAO,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AAAA,QACtH;AAEA,eAAO,aAAa,SAAS,GAAG;AAC9B,iBAAO,UAAU,UAAU,aAAa,CAAC,GAAG,UAAU;AAAA,QACxD;AAEA,iBAAS,iBAAiB,EAAC,OAAAC,QAAO,OAAAC,OAAK,GAAG;AACxC,qBAAW,CAAC,GAAG,IAAI,KAAKD,OAAM,QAAQ,GAAG;AACvC,iBAAK,QAAQ;AACb,iBAAK,cAAc,CAAC;AACpB,iBAAK,cAAc,CAAC;AAAA,UACtB;AACA,gBAAM,WAAW,IAAI,IAAIA,OAAM,IAAI,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,GAAGA,MAAK,GAAG,CAAC,CAAC,CAAC;AAClE,qBAAW,CAAC,GAAG,IAAI,KAAKC,OAAM,QAAQ,GAAG;AACvC,iBAAK,QAAQ;AACb,gBAAI,EAAC,QAAQ,OAAM,IAAI;AACvB,gBAAI,OAAO,WAAW,SAAU,UAAS,KAAK,SAAS,KAAK,UAAU,MAAM;AAC5E,gBAAI,OAAO,WAAW,SAAU,UAAS,KAAK,SAAS,KAAK,UAAU,MAAM;AAC5E,mBAAO,YAAY,KAAK,IAAI;AAC5B,mBAAO,YAAY,KAAK,IAAI;AAAA,UAC9B;AACA,cAAI,YAAY,MAAM;AACpB,uBAAW,EAAC,aAAa,YAAW,KAAKD,QAAO;AAC9C,0BAAY,KAAK,QAAQ;AACzB,0BAAY,KAAK,QAAQ;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,kBAAkB,EAAC,OAAAA,OAAK,GAAG;AAClC,qBAAW,QAAQA,QAAO;AACxB,iBAAK,QAAQ,KAAK,eAAe,SAC3B,KAAK,IAAI,QAAQ,IAAI,KAAK,aAAa,KAAK,GAAG,QAAQ,IAAI,KAAK,aAAa,KAAK,CAAC,IACnF,KAAK;AAAA,UACb;AAAA,QACF;AAEA,iBAAS,kBAAkB,EAAC,OAAAA,OAAK,GAAG;AAClC,gBAAM,IAAIA,OAAM;AAChB,cAAI,UAAU,IAAI,IAAIA,MAAK;AAC3B,cAAI,OAAO,oBAAI;AACf,cAAI,IAAI;AACR,iBAAO,QAAQ,MAAM;AACnB,uBAAW,QAAQ,SAAS;AAC1B,mBAAK,QAAQ;AACb,yBAAW,EAAC,OAAM,KAAK,KAAK,aAAa;AACvC,qBAAK,IAAI,MAAM;AAAA,cACjB;AAAA,YACF;AACA,gBAAI,EAAE,IAAI,EAAG,OAAM,IAAI,MAAM,eAAe;AAC5C,sBAAU;AACV,mBAAO,oBAAI;AAAA,UACb;AAAA,QACF;AAEA,iBAAS,mBAAmB,EAAC,OAAAA,OAAK,GAAG;AACnC,gBAAM,IAAIA,OAAM;AAChB,cAAI,UAAU,IAAI,IAAIA,MAAK;AAC3B,cAAI,OAAO,oBAAI;AACf,cAAI,IAAI;AACR,iBAAO,QAAQ,MAAM;AACnB,uBAAW,QAAQ,SAAS;AAC1B,mBAAK,SAAS;AACd,yBAAW,EAAC,OAAM,KAAK,KAAK,aAAa;AACvC,qBAAK,IAAI,MAAM;AAAA,cACjB;AAAA,YACF;AACA,gBAAI,EAAE,IAAI,EAAG,OAAM,IAAI,MAAM,eAAe;AAC5C,sBAAU;AACV,mBAAO,oBAAI;AAAA,UACb;AAAA,QACF;AAEA,iBAAS,kBAAkB,EAAC,OAAAA,OAAK,GAAG;AAClC,gBAAM,IAAI,QAAQ,IAAIA,QAAO,OAAK,EAAE,KAAK,IAAI;AAC7C,gBAAM,MAAM,KAAK,KAAK,OAAO,IAAI;AACjC,gBAAM,UAAU,IAAI,MAAM,CAAC;AAC3B,qBAAW,QAAQA,QAAO;AACxB,kBAAM,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,MAAM,MAAM,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC;AAC5E,iBAAK,QAAQ;AACb,iBAAK,KAAK,KAAK,IAAI;AACnB,iBAAK,KAAK,KAAK,KAAK;AACpB,gBAAI,QAAQ,CAAC,EAAG,SAAQ,CAAC,EAAE,KAAK,IAAI;AAAA,gBAC/B,SAAQ,CAAC,IAAI,CAAC,IAAI;AAAA,UACzB;AACA,cAAI,KAAM,YAAW,UAAU,SAAS;AACtC,mBAAO,KAAK,IAAI;AAAA,UAClB;AACA,iBAAO;AAAA,QACT;AAEA,iBAAS,uBAAuB,SAAS;AACvC,gBAAM,KAAK,QAAQ,IAAI,SAAS,QAAM,KAAK,MAAM,EAAE,SAAS,KAAK,MAAM,QAAQ,IAAI,GAAG,KAAK,CAAC;AAC5F,qBAAWA,UAAS,SAAS;AAC3B,gBAAI,IAAI;AACR,uBAAW,QAAQA,QAAO;AACxB,mBAAK,KAAK;AACV,mBAAK,KAAK,IAAI,KAAK,QAAQ;AAC3B,kBAAI,KAAK,KAAK;AACd,yBAAW,QAAQ,KAAK,aAAa;AACnC,qBAAK,QAAQ,KAAK,QAAQ;AAAA,cAC5B;AAAA,YACF;AACA,iBAAK,KAAK,IAAI,OAAOA,OAAM,SAAS;AACpC,qBAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,EAAE,GAAG;AACrC,oBAAM,OAAOA,OAAM,CAAC;AACpB,mBAAK,MAAM,KAAK,IAAI;AACpB,mBAAK,MAAM,KAAK,IAAI;AAAA,YACtB;AACA,yBAAaA,MAAK;AAAA,UACpB;AAAA,QACF;AAEA,iBAAS,oBAAoB,OAAO;AAClC,gBAAM,UAAU,kBAAkB,KAAK;AACvC,eAAK,KAAK,IAAI,KAAK,KAAK,OAAO,QAAQ,IAAI,SAAS,OAAK,EAAE,MAAM,IAAI,EAAE;AACvE,iCAAuB,OAAO;AAC9B,mBAAS,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AACnC,kBAAM,QAAQ,KAAK,IAAI,MAAM,CAAC;AAC9B,kBAAM,OAAO,KAAK,IAAI,IAAI,QAAQ,IAAI,KAAK,UAAU;AACrD,6BAAiB,SAAS,OAAO,IAAI;AACrC,6BAAiB,SAAS,OAAO,IAAI;AAAA,UACvC;AAAA,QACF;AAGA,iBAAS,iBAAiB,SAAS,OAAO,MAAM;AAC9C,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,kBAAM,SAAS,QAAQ,CAAC;AACxB,uBAAW,UAAU,QAAQ;AAC3B,kBAAI,IAAI;AACR,kBAAI,IAAI;AACR,yBAAW,EAAC,QAAQ,OAAAE,OAAK,KAAK,OAAO,aAAa;AAChD,oBAAI,IAAIA,UAAS,OAAO,QAAQ,OAAO;AACvC,qBAAK,UAAU,QAAQ,MAAM,IAAI;AACjC,qBAAK;AAAA,cACP;AACA,kBAAI,EAAE,IAAI,GAAI;AACd,kBAAIC,OAAM,IAAI,IAAI,OAAO,MAAM;AAC/B,qBAAO,MAAMA;AACb,qBAAO,MAAMA;AACb,+BAAiB,MAAM;AAAA,YACzB;AACA,gBAAI,SAAS,OAAW,QAAO,KAAK,gBAAgB;AACpD,8BAAkB,QAAQ,IAAI;AAAA,UAChC;AAAA,QACF;AAGA,iBAAS,iBAAiB,SAAS,OAAO,MAAM;AAC9C,mBAAS,IAAI,QAAQ,QAAQ,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACnD,kBAAM,SAAS,QAAQ,CAAC;AACxB,uBAAW,UAAU,QAAQ;AAC3B,kBAAI,IAAI;AACR,kBAAI,IAAI;AACR,yBAAW,EAAC,QAAQ,OAAAD,OAAK,KAAK,OAAO,aAAa;AAChD,oBAAI,IAAIA,UAAS,OAAO,QAAQ,OAAO;AACvC,qBAAK,UAAU,QAAQ,MAAM,IAAI;AACjC,qBAAK;AAAA,cACP;AACA,kBAAI,EAAE,IAAI,GAAI;AACd,kBAAIC,OAAM,IAAI,IAAI,OAAO,MAAM;AAC/B,qBAAO,MAAMA;AACb,qBAAO,MAAMA;AACb,+BAAiB,MAAM;AAAA,YACzB;AACA,gBAAI,SAAS,OAAW,QAAO,KAAK,gBAAgB;AACpD,8BAAkB,QAAQ,IAAI;AAAA,UAChC;AAAA,QACF;AAEA,iBAAS,kBAAkBH,QAAO,OAAO;AACvC,gBAAM,IAAIA,OAAM,UAAU;AAC1B,gBAAM,UAAUA,OAAM,CAAC;AACvB,uCAA6BA,QAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,KAAK;AACjE,uCAA6BA,QAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,KAAK;AACjE,uCAA6BA,QAAO,IAAIA,OAAM,SAAS,GAAG,KAAK;AAC/D,uCAA6BA,QAAO,IAAI,GAAG,KAAK;AAAA,QAClD;AAGA,iBAAS,6BAA6BA,QAAO,GAAG,GAAG,OAAO;AACxD,iBAAO,IAAIA,OAAM,QAAQ,EAAE,GAAG;AAC5B,kBAAM,OAAOA,OAAM,CAAC;AACpB,kBAAMG,OAAM,IAAI,KAAK,MAAM;AAC3B,gBAAIA,MAAK,KAAM,MAAK,MAAMA,KAAI,KAAK,MAAMA;AACzC,gBAAI,KAAK,KAAK;AAAA,UAChB;AAAA,QACF;AAGA,iBAAS,6BAA6BH,QAAO,GAAG,GAAG,OAAO;AACxD,iBAAO,KAAK,GAAG,EAAE,GAAG;AAClB,kBAAM,OAAOA,OAAM,CAAC;AACpB,kBAAMG,OAAM,KAAK,KAAK,KAAK;AAC3B,gBAAIA,MAAK,KAAM,MAAK,MAAMA,KAAI,KAAK,MAAMA;AACzC,gBAAI,KAAK,KAAK;AAAA,UAChB;AAAA,QACF;AAEA,iBAAS,iBAAiB,EAAC,aAAa,YAAW,GAAG;AACpD,cAAI,aAAa,QAAW;AAC1B,uBAAW,EAAC,QAAQ,EAAC,aAAAC,aAAW,EAAC,KAAK,aAAa;AACjD,cAAAA,aAAY,KAAK,sBAAsB;AAAA,YACzC;AACA,uBAAW,EAAC,QAAQ,EAAC,aAAAC,aAAW,EAAC,KAAK,aAAa;AACjD,cAAAA,aAAY,KAAK,sBAAsB;AAAA,YACzC;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,aAAaL,QAAO;AAC3B,cAAI,aAAa,QAAW;AAC1B,uBAAW,EAAC,aAAa,YAAW,KAAKA,QAAO;AAC9C,0BAAY,KAAK,sBAAsB;AACvC,0BAAY,KAAK,sBAAsB;AAAA,YACzC;AAAA,UACF;AAAA,QACF;AAGA,iBAAS,UAAU,QAAQ,QAAQ;AACjC,cAAI,IAAI,OAAO,MAAM,OAAO,YAAY,SAAS,KAAK,KAAK;AAC3D,qBAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,gBAAI,SAAS,OAAQ;AACrB,iBAAK,QAAQ;AAAA,UACf;AACA,qBAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,gBAAI,SAAS,OAAQ;AACrB,iBAAK;AAAA,UACP;AACA,iBAAO;AAAA,QACT;AAGA,iBAAS,UAAU,QAAQ,QAAQ;AACjC,cAAI,IAAI,OAAO,MAAM,OAAO,YAAY,SAAS,KAAK,KAAK;AAC3D,qBAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,gBAAI,SAAS,OAAQ;AACrB,iBAAK,QAAQ;AAAA,UACf;AACA,qBAAW,EAAC,QAAQ,MAAM,MAAK,KAAK,OAAO,aAAa;AACtD,gBAAI,SAAS,OAAQ;AACrB,iBAAK;AAAA,UACP;AACA,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,iBAAiB,GAAG;AAC3B,eAAO,CAAC,EAAE,OAAO,IAAI,EAAE,EAAE;AAAA,MAC3B;AAEA,eAAS,iBAAiB,GAAG;AAC3B,eAAO,CAAC,EAAE,OAAO,IAAI,EAAE,EAAE;AAAA,MAC3B;AAEA,eAAS,uBAAuB;AAC9B,eAAO,QAAQ,eAAe,EACzB,OAAO,gBAAgB,EACvB,OAAO,gBAAgB;AAAA,MAC9B;AAEA,MAAAD,SAAQ,SAAS;AACjB,MAAAA,SAAQ,eAAe;AACvB,MAAAA,SAAQ,gBAAgB;AACxB,MAAAA,SAAQ,aAAa;AACrB,MAAAA,SAAQ,uBAAuB;AAC/B,MAAAA,SAAQ,cAAc;AAEtB,aAAO,eAAeA,UAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA,IAE5D,CAAC;AAAA;AAAA;", "names": ["exports", "index", "count", "array", "length", "reduce", "j", "i", "sum", "mean", "min", "max", "key", "map", "values", "groups", "keyof", "group", "f", "reverse", "ticks", "bin", "maxIndex", "minIndex", "pairs", "range", "shuffle", "transpose", "reducer", "set", "exports", "exports", "x", "constant", "arc", "a", "y", "line", "area", "k", "pie", "sum", "i", "j", "c", "link", "s", "symbol", "bundle", "beta", "cardinal", "tension", "x2", "y2", "catmullRom", "alpha", "stack", "exports", "nodes", "links", "value", "dy", "sourceLinks", "targetLinks"]}