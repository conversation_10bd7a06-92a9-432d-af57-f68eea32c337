{"version": 3, "sources": ["../../@sinclair/typebox/build/esm/type/clone/type.mjs", "../../@sinclair/typebox/build/esm/type/helpers/helpers.mjs", "../../@sinclair/typebox/build/esm/type/argument/argument.mjs", "../../@sinclair/typebox/build/esm/type/awaited/awaited.mjs", "../../@sinclair/typebox/build/esm/type/composite/composite.mjs", "../../@sinclair/typebox/build/esm/type/date/date.mjs", "../../@sinclair/typebox/build/esm/type/null/null.mjs", "../../@sinclair/typebox/build/esm/type/symbol/symbol.mjs", "../../@sinclair/typebox/build/esm/type/undefined/undefined.mjs", "../../@sinclair/typebox/build/esm/type/uint8array/uint8array.mjs", "../../@sinclair/typebox/build/esm/type/const/const.mjs", "../../@sinclair/typebox/build/esm/type/constructor-parameters/constructor-parameters.mjs", "../../@sinclair/typebox/build/esm/type/enum/enum.mjs", "../../@sinclair/typebox/build/esm/type/exclude/exclude-from-template-literal.mjs", "../../@sinclair/typebox/build/esm/type/exclude/exclude.mjs", "../../@sinclair/typebox/build/esm/type/exclude/exclude-from-mapped-result.mjs", "../../@sinclair/typebox/build/esm/type/extract/extract-from-template-literal.mjs", "../../@sinclair/typebox/build/esm/type/extract/extract.mjs", "../../@sinclair/typebox/build/esm/type/extract/extract-from-mapped-result.mjs", "../../@sinclair/typebox/build/esm/type/instance-type/instance-type.mjs", "../../@sinclair/typebox/build/esm/type/readonly-optional/readonly-optional.mjs", "../../@sinclair/typebox/build/esm/type/record/record.mjs", "../../@sinclair/typebox/build/esm/type/instantiate/instantiate.mjs", "../../@sinclair/typebox/build/esm/type/integer/integer.mjs", "../../@sinclair/typebox/build/esm/type/intrinsic/intrinsic-from-mapped-key.mjs", "../../@sinclair/typebox/build/esm/type/intrinsic/intrinsic.mjs", "../../@sinclair/typebox/build/esm/type/intrinsic/capitalize.mjs", "../../@sinclair/typebox/build/esm/type/intrinsic/lowercase.mjs", "../../@sinclair/typebox/build/esm/type/intrinsic/uncapitalize.mjs", "../../@sinclair/typebox/build/esm/type/intrinsic/uppercase.mjs", "../../@sinclair/typebox/build/esm/type/omit/omit-from-mapped-result.mjs", "../../@sinclair/typebox/build/esm/type/omit/omit.mjs", "../../@sinclair/typebox/build/esm/type/omit/omit-from-mapped-key.mjs", "../../@sinclair/typebox/build/esm/type/pick/pick-from-mapped-result.mjs", "../../@sinclair/typebox/build/esm/type/pick/pick.mjs", "../../@sinclair/typebox/build/esm/type/pick/pick-from-mapped-key.mjs", "../../@sinclair/typebox/build/esm/type/partial/partial.mjs", "../../@sinclair/typebox/build/esm/type/partial/partial-from-mapped-result.mjs", "../../@sinclair/typebox/build/esm/type/required/required.mjs", "../../@sinclair/typebox/build/esm/type/required/required-from-mapped-result.mjs", "../../@sinclair/typebox/build/esm/type/module/compute.mjs", "../../@sinclair/typebox/build/esm/type/module/module.mjs", "../../@sinclair/typebox/build/esm/type/not/not.mjs", "../../@sinclair/typebox/build/esm/type/parameters/parameters.mjs", "../../@sinclair/typebox/build/esm/type/recursive/recursive.mjs", "../../@sinclair/typebox/build/esm/type/regexp/regexp.mjs", "../../@sinclair/typebox/build/esm/type/rest/rest.mjs", "../../@sinclair/typebox/build/esm/type/return-type/return-type.mjs", "../../@sinclair/typebox/build/esm/type/transform/transform.mjs", "../../@sinclair/typebox/build/esm/type/void/void.mjs", "../../@sinclair/typebox/build/esm/type/type/json.mjs", "../../@sinclair/typebox/build/esm/type/type/type.mjs", "../../@sinclair/typebox/build/esm/type/type/javascript.mjs", "../../@sinclair/typebox/build/esm/type/type/index.mjs"], "sourcesContent": ["import { Clone } from './value.mjs';\n/** Clones a Rest */\nexport function CloneRest(schemas) {\n    return schemas.map((schema) => CloneType(schema));\n}\n/** Clones a Type */\nexport function CloneType(schema, options) {\n    return options === undefined ? Clone(schema) : Clone({ ...options, ...schema });\n}\n", "/** Increments the given string value + 1 */\nexport function Increment(T) {\n    return (parseInt(T) + 1).toString();\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[JavaScript]` Creates an Argument Type. */\nexport function Argument(index) {\n    return CreateType({ [Kind]: 'Argument', index });\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Computed } from '../computed/index.mjs';\nimport { Intersect } from '../intersect/index.mjs';\nimport { Union } from '../union/index.mjs';\nimport { Ref } from '../ref/index.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsIntersect, IsUnion, IsPromise, IsRef, IsComputed } from '../guard/kind.mjs';\n// prettier-ignore\nfunction FromComputed(target, parameters) {\n    return Computed('Awaited', [Computed(target, parameters)]);\n}\n// prettier-ignore\nfunction FromRef($ref) {\n    return Computed('Awaited', [Ref($ref)]);\n}\n// prettier-ignore\nfunction FromIntersect(types) {\n    return Intersect(FromRest(types));\n}\n// prettier-ignore\nfunction FromUnion(types) {\n    return Union(FromRest(types));\n}\n// prettier-ignore\nfunction FromPromise(type) {\n    return Awaited(type);\n}\n// prettier-ignore\nfunction FromRest(types) {\n    return types.map(type => Awaited(type));\n}\n/** `[JavaScript]` Constructs a type by recursively unwrapping Promise types */\nexport function Awaited(type, options) {\n    return CreateType(IsComputed(type) ? FromComputed(type.target, type.parameters) : IsIntersect(type) ? FromIntersect(type.allOf) : IsUnion(type) ? FromUnion(type.anyOf) : IsPromise(type) ? FromPromise(type.item) : IsRef(type) ? FromRef(type.$ref) : type, options);\n}\n", "import { IntersectEvaluated } from '../intersect/index.mjs';\nimport { IndexFromPropertyKeys } from '../indexed/index.mjs';\nimport { KeyOfPropertyKeys } from '../keyof/index.mjs';\nimport { Object } from '../object/index.mjs';\nimport { SetDistinct } from '../sets/index.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsNever } from '../guard/kind.mjs';\n// prettier-ignore\nfunction CompositeKeys(T) {\n    const Acc = [];\n    for (const L of T)\n        Acc.push(...KeyOfPropertyKeys(L));\n    return SetDistinct(Acc);\n}\n// prettier-ignore\nfunction FilterNever(T) {\n    return T.filter(L => !IsNever(L));\n}\n// prettier-ignore\nfunction CompositeProperty(T, K) {\n    const Acc = [];\n    for (const L of T)\n        Acc.push(...IndexFromPropertyKeys(L, [K]));\n    return FilterNever(Acc);\n}\n// prettier-ignore\nfunction CompositeProperties(T, K) {\n    const Acc = {};\n    for (const L of K) {\n        Acc[L] = IntersectEvaluated(CompositeProperty(T, L));\n    }\n    return Acc;\n}\n// prettier-ignore\nexport function Composite(T, options) {\n    const K = CompositeKeys(T);\n    const P = CompositeProperties(T, K);\n    const R = Object(P, options);\n    return R;\n}\n", "import { Kind } from '../symbols/index.mjs';\nimport { CreateType } from '../create/type.mjs';\n/** `[JavaScript]` Creates a Date type */\nexport function Date(options) {\n    return CreateType({ [Kind]: 'Date', type: 'Date' }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates a Null type */\nexport function Null(options) {\n    return CreateType({ [Kind]: 'Null', type: 'null' }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[JavaScript]` Creates a Symbol type */\nexport function Symbol(options) {\n    return CreateType({ [Kind]: 'Symbol', type: 'symbol' }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[JavaScript]` Creates a Undefined type */\nexport function Undefined(options) {\n    return CreateType({ [Kind]: 'Undefined', type: 'undefined' }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[JavaScript]` Creates a Uint8Array type */\nexport function Uint8Array(options) {\n    return CreateType({ [Kind]: 'Uint8Array', type: 'Uint8Array' }, options);\n}\n", "import { Any } from '../any/index.mjs';\nimport { BigInt } from '../bigint/index.mjs';\nimport { Date } from '../date/index.mjs';\nimport { Function as FunctionType } from '../function/index.mjs';\nimport { Literal } from '../literal/index.mjs';\nimport { Null } from '../null/index.mjs';\nimport { Object } from '../object/index.mjs';\nimport { Symbol } from '../symbol/index.mjs';\nimport { Tuple } from '../tuple/index.mjs';\nimport { Readonly } from '../readonly/index.mjs';\nimport { Undefined } from '../undefined/index.mjs';\nimport { Uint8Array } from '../uint8array/index.mjs';\nimport { Unknown } from '../unknown/index.mjs';\nimport { CreateType } from '../create/index.mjs';\n// ------------------------------------------------------------------\n// ValueGuard\n// ------------------------------------------------------------------\nimport { IsArray, IsNumber, IsBigInt, IsUint8Array, IsDate, IsIterator, IsObject, IsAsyncIterator, IsFunction, IsUndefined, IsNull, IsSymbol, IsBoolean, IsString } from '../guard/value.mjs';\n// prettier-ignore\nfunction FromArray(T) {\n    return T.map(L => FromValue(L, false));\n}\n// prettier-ignore\nfunction FromProperties(value) {\n    const Acc = {};\n    for (const K of globalThis.Object.getOwnPropertyNames(value))\n        Acc[K] = Readonly(FromValue(value[K], false));\n    return Acc;\n}\nfunction ConditionalReadonly(T, root) {\n    return (root === true ? T : Readonly(T));\n}\n// prettier-ignore\nfunction FromValue(value, root) {\n    return (IsAsyncIterator(value) ? ConditionalReadonly(Any(), root) :\n        IsIterator(value) ? ConditionalReadonly(Any(), root) :\n            IsArray(value) ? Readonly(Tuple(FromArray(value))) :\n                IsUint8Array(value) ? Uint8Array() :\n                    IsDate(value) ? Date() :\n                        IsObject(value) ? ConditionalReadonly(Object(FromProperties(value)), root) :\n                            IsFunction(value) ? ConditionalReadonly(FunctionType([], Unknown()), root) :\n                                IsUndefined(value) ? Undefined() :\n                                    IsNull(value) ? Null() :\n                                        IsSymbol(value) ? Symbol() :\n                                            IsBigInt(value) ? BigInt() :\n                                                IsNumber(value) ? Literal(value) :\n                                                    IsBoolean(value) ? Literal(value) :\n                                                        IsString(value) ? Literal(value) :\n                                                            Object({}));\n}\n/** `[JavaScript]` Creates a readonly const type from the given value. */\nexport function Const(T, options) {\n    return CreateType(FromValue(T, true), options);\n}\n", "import { <PERSON>ple } from '../tuple/index.mjs';\nimport { Never } from '../never/index.mjs';\nimport * as KindGuard from '../guard/kind.mjs';\n/** `[JavaScript]` Extracts the ConstructorParameters from the given Constructor type */\nexport function ConstructorParameters(schema, options) {\n    return (KindGuard.IsConstructor(schema) ? Tuple(schema.parameters, options) : Never(options));\n}\n", "import { Literal } from '../literal/index.mjs';\nimport { Kind, Hint } from '../symbols/index.mjs';\nimport { Union } from '../union/index.mjs';\n// ------------------------------------------------------------------\n// ValueGuard\n// ------------------------------------------------------------------\nimport { IsUndefined } from '../guard/value.mjs';\n/** `[<PERSON><PERSON>]` Creates a Enum type */\nexport function Enum(item, options) {\n    if (IsUndefined(item))\n        throw new Error('Enum undefined or empty');\n    const values1 = globalThis.Object.getOwnPropertyNames(item)\n        .filter((key) => isNaN(key))\n        .map((key) => item[key]);\n    const values2 = [...new Set(values1)];\n    const anyOf = values2.map((value) => Literal(value));\n    return Union(anyOf, { ...options, [Hint]: 'Enum' });\n}\n", "import { Exclude } from './exclude.mjs';\nimport { TemplateLiteralToUnion } from '../template-literal/index.mjs';\nexport function ExcludeFromTemplateLiteral(L, R) {\n    return Exclude(TemplateLiteralToUnion(L), R);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Union } from '../union/index.mjs';\nimport { Never } from '../never/index.mjs';\nimport { ExtendsCheck, ExtendsResult } from '../extends/index.mjs';\nimport { ExcludeFromMappedResult } from './exclude-from-mapped-result.mjs';\nimport { ExcludeFromTemplateLiteral } from './exclude-from-template-literal.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsMappedResult, IsTemplateLiteral, IsUnion } from '../guard/kind.mjs';\nfunction ExcludeRest(L, R) {\n    const excluded = L.filter((inner) => ExtendsCheck(inner, R) === ExtendsResult.False);\n    return excluded.length === 1 ? excluded[0] : Union(excluded);\n}\n/** `[Json]` Constructs a type by excluding from unionType all union members that are assignable to excludedMembers */\nexport function Exclude(L, R, options = {}) {\n    // overloads\n    if (IsTemplateLiteral(L))\n        return CreateType(ExcludeFromTemplateLiteral(L, R), options);\n    if (IsMappedResult(L))\n        return CreateType(ExcludeFromMappedResult(L, R), options);\n    // prettier-ignore\n    return CreateType(IsUnion(L) ? ExcludeRest(L.anyOf, R) :\n        ExtendsCheck(L, R) !== ExtendsResult.False ? Never() : L, options);\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { Exclude } from './exclude.mjs';\n// prettier-ignore\nfunction FromProperties(P, U) {\n    const Acc = {};\n    for (const K2 of globalThis.Object.getOwnPropertyNames(P))\n        Acc[K2] = Exclude(P[K2], U);\n    return Acc;\n}\n// prettier-ignore\nfunction FromMappedResult(R, T) {\n    return FromProperties(R.properties, T);\n}\n// prettier-ignore\nexport function ExcludeFromMappedResult(R, T) {\n    const P = FromMappedResult(R, T);\n    return MappedResult(P);\n}\n", "import { Extract } from './extract.mjs';\nimport { TemplateLiteralToUnion } from '../template-literal/index.mjs';\nexport function ExtractFromTemplateLiteral(L, R) {\n    return Extract(TemplateLiteralToUnion(L), R);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Union } from '../union/index.mjs';\nimport { Never } from '../never/index.mjs';\nimport { ExtendsCheck, ExtendsResult } from '../extends/index.mjs';\nimport { ExtractFromMappedResult } from './extract-from-mapped-result.mjs';\nimport { ExtractFromTemplateLiteral } from './extract-from-template-literal.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsMappedResult, IsTemplateLiteral, IsUnion } from '../guard/kind.mjs';\nfunction ExtractRest(L, R) {\n    const extracted = L.filter((inner) => ExtendsCheck(inner, R) !== ExtendsResult.False);\n    return extracted.length === 1 ? extracted[0] : Union(extracted);\n}\n/** `[Json]` Constructs a type by extracting from type all union members that are assignable to union */\nexport function Extract(L, R, options) {\n    // overloads\n    if (IsTemplateLiteral(L))\n        return CreateType(ExtractFromTemplateLiteral(L, R), options);\n    if (IsMappedResult(L))\n        return CreateType(ExtractFromMappedResult(L, R), options);\n    // prettier-ignore\n    return CreateType(IsUnion(L) ? ExtractRest(L.anyOf, R) :\n        ExtendsCheck(L, R) !== ExtendsResult.False ? L : Never(), options);\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { Extract } from './extract.mjs';\n// prettier-ignore\nfunction FromProperties(P, T) {\n    const Acc = {};\n    for (const K2 of globalThis.Object.getOwnPropertyNames(P))\n        Acc[K2] = Extract(P[K2], T);\n    return Acc;\n}\n// prettier-ignore\nfunction FromMappedResult(R, T) {\n    return FromProperties(R.properties, T);\n}\n// prettier-ignore\nexport function ExtractFromMappedResult(R, T) {\n    const P = FromMappedResult(R, T);\n    return MappedResult(P);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Never } from '../never/index.mjs';\nimport * as KindGuard from '../guard/kind.mjs';\n/** `[JavaScript]` Extracts the InstanceType from the given Constructor type */\nexport function InstanceType(schema, options) {\n    return (KindGuard.IsConstructor(schema) ? CreateType(schema.returns, options) : Never(options));\n}\n", "import { Readonly } from '../readonly/index.mjs';\nimport { Optional } from '../optional/index.mjs';\n/** `[<PERSON><PERSON>]` Creates a Readonly and Optional property */\nexport function ReadonlyOptional(schema) {\n    return Readonly(Optional(schema));\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind, Hint } from '../symbols/index.mjs';\nimport { Never } from '../never/index.mjs';\nimport { Number } from '../number/index.mjs';\nimport { Object } from '../object/index.mjs';\nimport { String } from '../string/index.mjs';\nimport { Union } from '../union/index.mjs';\nimport { IsTemplateLiteralFinite } from '../template-literal/index.mjs';\nimport { PatternStringExact, PatternNumberExact, PatternNeverExact } from '../patterns/index.mjs';\nimport { IndexPropertyKeys } from '../indexed/index.mjs';\n// ------------------------------------------------------------------\n// ValueGuard\n// ------------------------------------------------------------------\nimport { IsUndefined } from '../guard/value.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsInteger, IsLiteral, IsAny, IsBoolean, IsNever, IsNumber, IsString, IsRegExp, IsTemplateLiteral, IsUnion } from '../guard/kind.mjs';\n// ------------------------------------------------------------------\n// RecordCreateFromPattern\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction RecordCreateFromPattern(pattern, T, options) {\n    return CreateType({ [Kind]: 'Record', type: 'object', patternProperties: { [pattern]: T } }, options);\n}\n// ------------------------------------------------------------------\n// RecordCreateFromKeys\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction RecordCreateFromKeys(K, T, options) {\n    const result = {};\n    for (const K2 of K)\n        result[K2] = T;\n    return Object(result, { ...options, [Hint]: 'Record' });\n}\n// prettier-ignore\nfunction FromTemplateLiteralKey(K, T, options) {\n    return (IsTemplateLiteralFinite(K)\n        ? RecordCreateFromKeys(IndexPropertyKeys(K), T, options)\n        : RecordCreateFromPattern(K.pattern, T, options));\n}\n// prettier-ignore\nfunction FromUnionKey(key, type, options) {\n    return RecordCreateFromKeys(IndexPropertyKeys(Union(key)), type, options);\n}\n// prettier-ignore\nfunction FromLiteralKey(key, type, options) {\n    return RecordCreateFromKeys([key.toString()], type, options);\n}\n// prettier-ignore\nfunction FromRegExpKey(key, type, options) {\n    return RecordCreateFromPattern(key.source, type, options);\n}\n// prettier-ignore\nfunction FromStringKey(key, type, options) {\n    const pattern = IsUndefined(key.pattern) ? PatternStringExact : key.pattern;\n    return RecordCreateFromPattern(pattern, type, options);\n}\n// prettier-ignore\nfunction FromAnyKey(_, type, options) {\n    return RecordCreateFromPattern(PatternStringExact, type, options);\n}\n// prettier-ignore\nfunction FromNeverKey(_key, type, options) {\n    return RecordCreateFromPattern(PatternNeverExact, type, options);\n}\n// prettier-ignore\nfunction FromBooleanKey(_key, type, options) {\n    return Object({ true: type, false: type }, options);\n}\n// prettier-ignore\nfunction FromIntegerKey(_key, type, options) {\n    return RecordCreateFromPattern(PatternNumberExact, type, options);\n}\n// prettier-ignore\nfunction FromNumberKey(_, type, options) {\n    return RecordCreateFromPattern(PatternNumberExact, type, options);\n}\n// ------------------------------------------------------------------\n// TRecordOrObject\n// ------------------------------------------------------------------\n/** `[Json]` Creates a Record type */\nexport function Record(key, type, options = {}) {\n    // prettier-ignore\n    return (IsUnion(key) ? FromUnionKey(key.anyOf, type, options) :\n        IsTemplateLiteral(key) ? FromTemplateLiteralKey(key, type, options) :\n            IsLiteral(key) ? FromLiteralKey(key.const, type, options) :\n                IsBoolean(key) ? FromBooleanKey(key, type, options) :\n                    IsInteger(key) ? FromIntegerKey(key, type, options) :\n                        IsNumber(key) ? FromNumberKey(key, type, options) :\n                            IsRegExp(key) ? FromRegExpKey(key, type, options) :\n                                IsString(key) ? FromStringKey(key, type, options) :\n                                    IsAny(key) ? FromAnyKey(key, type, options) :\n                                        IsNever(key) ? FromNeverKey(key, type, options) :\n                                            Never(options));\n}\n// ------------------------------------------------------------------\n// Record Utilities\n// ------------------------------------------------------------------\n/** Gets the Records Pattern */\nexport function RecordPattern(record) {\n    return globalThis.Object.getOwnPropertyNames(record.patternProperties)[0];\n}\n/** Gets the Records Key Type */\n// prettier-ignore\nexport function RecordKey(type) {\n    const pattern = RecordPattern(type);\n    return (pattern === PatternStringExact ? String() :\n        pattern === PatternNumberExact ? Number() :\n            String({ pattern }));\n}\n/** Gets a Record Value Type */\n// prettier-ignore\nexport function RecordValue(type) {\n    return type.patternProperties[RecordPattern(type)];\n}\n", "import { CloneType } from '../clone/type.mjs';\nimport { Unknown } from '../unknown/index.mjs';\nimport { ReadonlyOptional } from '../readonly-optional/index.mjs';\nimport { Readonly } from '../readonly/index.mjs';\nimport { Optional } from '../optional/index.mjs';\nimport { Object } from '../object/index.mjs';\nimport { Record, RecordKey, RecordValue } from '../record/index.mjs';\nimport * as ValueGuard from '../guard/value.mjs';\nimport * as KindGuard from '../guard/kind.mjs';\n// prettier-ignore\nfunction FromConstructor(args, type) {\n    type.parameters = FromTypes(args, type.parameters);\n    type.returns = FromType(args, type.returns);\n    return type;\n}\n// prettier-ignore\nfunction FromFunction(args, type) {\n    type.parameters = FromTypes(args, type.parameters);\n    type.returns = FromType(args, type.returns);\n    return type;\n}\n// prettier-ignore\nfunction FromIntersect(args, type) {\n    type.allOf = FromTypes(args, type.allOf);\n    return type;\n}\n// prettier-ignore\nfunction FromUnion(args, type) {\n    type.anyOf = FromTypes(args, type.anyOf);\n    return type;\n}\n// prettier-ignore\nfunction FromTuple(args, type) {\n    if (ValueGuard.IsUndefined(type.items))\n        return type;\n    type.items = FromTypes(args, type.items);\n    return type;\n}\n// prettier-ignore\nfunction FromArray(args, type) {\n    type.items = FromType(args, type.items);\n    return type;\n}\n// prettier-ignore\nfunction FromAsyncIterator(args, type) {\n    type.items = FromType(args, type.items);\n    return type;\n}\n// prettier-ignore\nfunction FromIterator(args, type) {\n    type.items = FromType(args, type.items);\n    return type;\n}\n// prettier-ignore\nfunction FromPromise(args, type) {\n    type.item = FromType(args, type.item);\n    return type;\n}\n// prettier-ignore\nfunction FromObject(args, type) {\n    const mappedProperties = FromProperties(args, type.properties);\n    return { ...type, ...Object(mappedProperties) }; // retain options\n}\n// prettier-ignore\nfunction FromRecord(args, type) {\n    const mappedKey = FromType(args, RecordKey(type));\n    const mappedValue = FromType(args, RecordValue(type));\n    const result = Record(mappedKey, mappedValue);\n    return { ...type, ...result }; // retain options\n}\n// prettier-ignore\nfunction FromArgument(args, argument) {\n    return argument.index in args ? args[argument.index] : Unknown();\n}\n// prettier-ignore\nfunction FromProperty(args, type) {\n    const isReadonly = KindGuard.IsReadonly(type);\n    const isOptional = KindGuard.IsOptional(type);\n    const mapped = FromType(args, type);\n    return (isReadonly && isOptional ? ReadonlyOptional(mapped) :\n        isReadonly && !isOptional ? Readonly(mapped) :\n            !isReadonly && isOptional ? Optional(mapped) :\n                mapped);\n}\n// prettier-ignore\nfunction FromProperties(args, properties) {\n    return globalThis.Object.getOwnPropertyNames(properties).reduce((result, key) => {\n        return { ...result, [key]: FromProperty(args, properties[key]) };\n    }, {});\n}\n// prettier-ignore\nexport function FromTypes(args, types) {\n    return types.map(type => FromType(args, type));\n}\n// prettier-ignore\nfunction FromType(args, type) {\n    return (KindGuard.IsConstructor(type) ? FromConstructor(args, type) :\n        KindGuard.IsFunction(type) ? FromFunction(args, type) :\n            KindGuard.IsIntersect(type) ? FromIntersect(args, type) :\n                KindGuard.IsUnion(type) ? FromUnion(args, type) :\n                    KindGuard.IsTuple(type) ? FromTuple(args, type) :\n                        KindGuard.IsArray(type) ? FromArray(args, type) :\n                            KindGuard.IsAsyncIterator(type) ? FromAsyncIterator(args, type) :\n                                KindGuard.IsIterator(type) ? FromIterator(args, type) :\n                                    KindGuard.IsPromise(type) ? FromPromise(args, type) :\n                                        KindGuard.IsObject(type) ? FromObject(args, type) :\n                                            KindGuard.IsRecord(type) ? FromRecord(args, type) :\n                                                KindGuard.IsArgument(type) ? FromArgument(args, type) :\n                                                    type);\n}\n/** `[JavaScript]` Instantiates a type with the given parameters */\n// prettier-ignore\nexport function Instantiate(type, args) {\n    return FromType(args, CloneType(type));\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates an Integer type */\nexport function Integer(options) {\n    return CreateType({ [Kind]: 'Integer', type: 'integer' }, options);\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { Intrinsic } from './intrinsic.mjs';\nimport { Literal } from '../literal/index.mjs';\nimport { Clone } from '../clone/value.mjs';\n// prettier-ignore\nfunction MappedIntrinsicPropertyKey(K, M, options) {\n    return {\n        [K]: Intrinsic(Literal(K), M, Clone(options))\n    };\n}\n// prettier-ignore\nfunction MappedIntrinsicPropertyKeys(K, M, options) {\n    const result = K.reduce((Acc, L) => {\n        return { ...Acc, ...MappedIntrinsicPropertyKey(L, M, options) };\n    }, {});\n    return result;\n}\n// prettier-ignore\nfunction MappedIntrinsicProperties(T, M, options) {\n    return MappedIntrinsicPropertyKeys(T['keys'], M, options);\n}\n// prettier-ignore\nexport function IntrinsicFromMappedKey(T, M, options) {\n    const P = MappedIntrinsicProperties(T, M, options);\n    return MappedResult(P);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { TemplateLiteral, TemplateLiteralParseExact, IsTemplateLiteralExpressionFinite, TemplateLiteralExpressionGenerate } from '../template-literal/index.mjs';\nimport { IntrinsicFromMappedKey } from './intrinsic-from-mapped-key.mjs';\nimport { Literal } from '../literal/index.mjs';\nimport { Union } from '../union/index.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsMap<PERSON><PERSON>ey, IsTemplateLiteral, IsUnion, Is<PERSON><PERSON><PERSON> } from '../guard/kind.mjs';\n// ------------------------------------------------------------------\n// Apply\n// ------------------------------------------------------------------\nfunction ApplyUncapitalize(value) {\n    const [first, rest] = [value.slice(0, 1), value.slice(1)];\n    return [first.toLowerCase(), rest].join('');\n}\nfunction ApplyCapitalize(value) {\n    const [first, rest] = [value.slice(0, 1), value.slice(1)];\n    return [first.toUpperCase(), rest].join('');\n}\nfunction ApplyUppercase(value) {\n    return value.toUpperCase();\n}\nfunction ApplyLowercase(value) {\n    return value.toLowerCase();\n}\nfunction FromTemplateLiteral(schema, mode, options) {\n    // note: template literals require special runtime handling as they are encoded in string patterns.\n    // This diverges from the mapped type which would otherwise map on the template literal kind.\n    const expression = TemplateLiteralParseExact(schema.pattern);\n    const finite = IsTemplateLiteralExpressionFinite(expression);\n    if (!finite)\n        return { ...schema, pattern: FromLiteralValue(schema.pattern, mode) };\n    const strings = [...TemplateLiteralExpressionGenerate(expression)];\n    const literals = strings.map((value) => Literal(value));\n    const mapped = FromRest(literals, mode);\n    const union = Union(mapped);\n    return TemplateLiteral([union], options);\n}\n// prettier-ignore\nfunction FromLiteralValue(value, mode) {\n    return (typeof value === 'string' ? (mode === 'Uncapitalize' ? ApplyUncapitalize(value) :\n        mode === 'Capitalize' ? ApplyCapitalize(value) :\n            mode === 'Uppercase' ? ApplyUppercase(value) :\n                mode === 'Lowercase' ? ApplyLowercase(value) :\n                    value) : value.toString());\n}\n// prettier-ignore\nfunction FromRest(T, M) {\n    return T.map(L => Intrinsic(L, M));\n}\n/** Applies an intrinsic string manipulation to the given type. */\nexport function Intrinsic(schema, mode, options = {}) {\n    // prettier-ignore\n    return (\n    // Intrinsic-Mapped-Inference\n    IsMappedKey(schema) ? IntrinsicFromMappedKey(schema, mode, options) :\n        // Standard-Inference\n        IsTemplateLiteral(schema) ? FromTemplateLiteral(schema, mode, options) :\n            IsUnion(schema) ? Union(FromRest(schema.anyOf, mode), options) :\n                IsLiteral(schema) ? Literal(FromLiteralValue(schema.const, mode), options) :\n                    // Default Type\n                    CreateType(schema, options));\n}\n", "import { Intrinsic } from './intrinsic.mjs';\n/** `[<PERSON><PERSON>]` Intrinsic function to Capitalize LiteralString types */\nexport function Capitalize(T, options = {}) {\n    return Intrinsic(T, 'Capitalize', options);\n}\n", "import { Intrinsic } from './intrinsic.mjs';\n/** `[<PERSON><PERSON>]` Intrinsic function to Lowercase LiteralString types */\nexport function Lowercase(T, options = {}) {\n    return Intrinsic(T, 'Lowercase', options);\n}\n", "import { Intrinsic } from './intrinsic.mjs';\n/** `[<PERSON><PERSON>]` Intrinsic function to Uncapitalize LiteralString types */\nexport function Uncapitalize(T, options = {}) {\n    return Intrinsic(T, 'Uncapitalize', options);\n}\n", "import { Intrinsic } from './intrinsic.mjs';\n/** `[<PERSON><PERSON>]` Intrinsic function to Uppercase LiteralString types */\nexport function Uppercase(T, options = {}) {\n    return Intrinsic(T, 'Uppercase', options);\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { Omit } from './omit.mjs';\nimport { Clone } from '../clone/value.mjs';\n// prettier-ignore\nfunction FromProperties(properties, propertyKeys, options) {\n    const result = {};\n    for (const K2 of globalThis.Object.getOwnPropertyNames(properties))\n        result[K2] = Omit(properties[K2], propertyKeys, Clone(options));\n    return result;\n}\n// prettier-ignore\nfunction FromMappedResult(mappedResult, propertyKeys, options) {\n    return FromProperties(mappedResult.properties, propertyKeys, options);\n}\n// prettier-ignore\nexport function OmitFromMappedResult(mappedResult, propertyKeys, options) {\n    const properties = FromMappedResult(mappedResult, propertyKeys, options);\n    return MappedResult(properties);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Discard } from '../discard/discard.mjs';\nimport { TransformKind } from '../symbols/symbols.mjs';\nimport { Computed } from '../computed/index.mjs';\nimport { Literal } from '../literal/index.mjs';\nimport { IndexPropertyKeys } from '../indexed/index.mjs';\nimport { Intersect } from '../intersect/index.mjs';\nimport { Union } from '../union/index.mjs';\nimport { Object } from '../object/index.mjs';\n// ------------------------------------------------------------------\n// Mapped\n// ------------------------------------------------------------------\nimport { OmitFromMappedKey } from './omit-from-mapped-key.mjs';\nimport { OmitFromMappedResult } from './omit-from-mapped-result.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsMappedKey, IsIntersect, IsUnion, IsObject, IsSchema, IsMappedResult, IsLiteralValue, IsRef } from '../guard/kind.mjs';\nimport { IsArray as IsArrayValue } from '../guard/value.mjs';\n// prettier-ignore\nfunction FromIntersect(types, propertyKeys) {\n    return types.map((type) => OmitResolve(type, propertyKeys));\n}\n// prettier-ignore\nfunction FromUnion(types, propertyKeys) {\n    return types.map((type) => OmitResolve(type, propertyKeys));\n}\n// ------------------------------------------------------------------\n// FromProperty\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction FromProperty(properties, key) {\n    const { [key]: _, ...R } = properties;\n    return R;\n}\n// prettier-ignore\nfunction FromProperties(properties, propertyKeys) {\n    return propertyKeys.reduce((T, K2) => FromProperty(T, K2), properties);\n}\n// prettier-ignore\nfunction FromObject(properties, propertyKeys) {\n    const options = Discard(properties, [TransformKind, '$id', 'required', 'properties']);\n    const omittedProperties = FromProperties(properties['properties'], propertyKeys);\n    return Object(omittedProperties, options);\n}\n// prettier-ignore\nfunction UnionFromPropertyKeys(propertyKeys) {\n    const result = propertyKeys.reduce((result, key) => IsLiteralValue(key) ? [...result, Literal(key)] : result, []);\n    return Union(result);\n}\n// prettier-ignore\nfunction OmitResolve(properties, propertyKeys) {\n    return (IsIntersect(properties) ? Intersect(FromIntersect(properties.allOf, propertyKeys)) :\n        IsUnion(properties) ? Union(FromUnion(properties.anyOf, propertyKeys)) :\n            IsObject(properties) ? FromObject(properties, propertyKeys) :\n                Object({}));\n}\n/** `[Json]` Constructs a type whose keys are picked from the given type */\n// prettier-ignore\nexport function Omit(type, key, options) {\n    const typeKey = IsArrayValue(key) ? UnionFromPropertyKeys(key) : key;\n    const propertyKeys = IsSchema(key) ? IndexPropertyKeys(key) : key;\n    const isTypeRef = IsRef(type);\n    const isKeyRef = IsRef(key);\n    return (IsMappedResult(type) ? OmitFromMappedResult(type, propertyKeys, options) :\n        IsMappedKey(key) ? OmitFromMappedKey(type, key, options) :\n            (isTypeRef && isKeyRef) ? Computed('Omit', [type, typeKey], options) :\n                (!isTypeRef && isKeyRef) ? Computed('Omit', [type, typeKey], options) :\n                    (isTypeRef && !isKeyRef) ? Computed('Omit', [type, typeKey], options) :\n                        CreateType({ ...OmitResolve(type, propertyKeys), ...options }));\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { Omit } from './omit.mjs';\nimport { Clone } from '../clone/value.mjs';\n// prettier-ignore\nfunction FromPropertyKey(type, key, options) {\n    return { [key]: Omit(type, [key], Clone(options)) };\n}\n// prettier-ignore\nfunction FromPropertyKeys(type, propertyKeys, options) {\n    return propertyKeys.reduce((Acc, LK) => {\n        return { ...Acc, ...FromPropertyKey(type, LK, options) };\n    }, {});\n}\n// prettier-ignore\nfunction FromMappedKey(type, mappedKey, options) {\n    return FromPropertyKeys(type, mappedKey.keys, options);\n}\n// prettier-ignore\nexport function OmitFromMappedKey(type, mappedKey, options) {\n    const properties = FromMappedKey(type, mappedKey, options);\n    return MappedResult(properties);\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { Pick } from './pick.mjs';\nimport { Clone } from '../clone/value.mjs';\n// prettier-ignore\nfunction FromProperties(properties, propertyKeys, options) {\n    const result = {};\n    for (const K2 of globalThis.Object.getOwnPropertyNames(properties))\n        result[K2] = Pick(properties[K2], propertyKeys, Clone(options));\n    return result;\n}\n// prettier-ignore\nfunction FromMappedResult(mappedResult, propertyKeys, options) {\n    return FromProperties(mappedResult.properties, propertyKeys, options);\n}\n// prettier-ignore\nexport function PickFromMappedResult(mappedResult, propertyKeys, options) {\n    const properties = FromMappedResult(mappedResult, propertyKeys, options);\n    return MappedResult(properties);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Discard } from '../discard/discard.mjs';\nimport { Computed } from '../computed/index.mjs';\nimport { Intersect } from '../intersect/index.mjs';\nimport { Literal } from '../literal/index.mjs';\nimport { Object } from '../object/index.mjs';\nimport { Union } from '../union/index.mjs';\nimport { IndexPropertyKeys } from '../indexed/index.mjs';\nimport { TransformKind } from '../symbols/symbols.mjs';\n// ------------------------------------------------------------------\n// Guards\n// ------------------------------------------------------------------\nimport { IsMappedKey, IsMappedResult, IsIntersect, IsUnion, IsObject, IsSchema, IsLiteralValue, IsRef } from '../guard/kind.mjs';\nimport { IsArray as IsArrayValue } from '../guard/value.mjs';\n// ------------------------------------------------------------------\n// Infrastructure\n// ------------------------------------------------------------------\nimport { PickFromMappedKey } from './pick-from-mapped-key.mjs';\nimport { PickFromMappedResult } from './pick-from-mapped-result.mjs';\nfunction FromIntersect(types, propertyKeys) {\n    return types.map((type) => PickResolve(type, propertyKeys));\n}\n// prettier-ignore\nfunction FromUnion(types, propertyKeys) {\n    return types.map((type) => PickResolve(type, propertyKeys));\n}\n// prettier-ignore\nfunction FromProperties(properties, propertyKeys) {\n    const result = {};\n    for (const K2 of propertyKeys)\n        if (K2 in properties)\n            result[K2] = properties[K2];\n    return result;\n}\n// prettier-ignore\nfunction FromObject(T, K) {\n    const options = Discard(T, [TransformKind, '$id', 'required', 'properties']);\n    const properties = FromProperties(T['properties'], K);\n    return Object(properties, options);\n}\n// prettier-ignore\nfunction UnionFromPropertyKeys(propertyKeys) {\n    const result = propertyKeys.reduce((result, key) => IsLiteralValue(key) ? [...result, Literal(key)] : result, []);\n    return Union(result);\n}\n// prettier-ignore\nfunction PickResolve(properties, propertyKeys) {\n    return (IsIntersect(properties) ? Intersect(FromIntersect(properties.allOf, propertyKeys)) :\n        IsUnion(properties) ? Union(FromUnion(properties.anyOf, propertyKeys)) :\n            IsObject(properties) ? FromObject(properties, propertyKeys) :\n                Object({}));\n}\n/** `[Json]` Constructs a type whose keys are picked from the given type */\n// prettier-ignore\nexport function Pick(type, key, options) {\n    const typeKey = IsArrayValue(key) ? UnionFromPropertyKeys(key) : key;\n    const propertyKeys = IsSchema(key) ? IndexPropertyKeys(key) : key;\n    const isTypeRef = IsRef(type);\n    const isKeyRef = IsRef(key);\n    return (IsMappedResult(type) ? PickFromMappedResult(type, propertyKeys, options) :\n        IsMappedKey(key) ? PickFromMappedKey(type, key, options) :\n            (isTypeRef && isKeyRef) ? Computed('Pick', [type, typeKey], options) :\n                (!isTypeRef && isKeyRef) ? Computed('Pick', [type, typeKey], options) :\n                    (isTypeRef && !isKeyRef) ? Computed('Pick', [type, typeKey], options) :\n                        CreateType({ ...PickResolve(type, propertyKeys), ...options }));\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { Pick } from './pick.mjs';\nimport { Clone } from '../clone/value.mjs';\n// prettier-ignore\nfunction FromPropertyKey(type, key, options) {\n    return {\n        [key]: Pick(type, [key], <PERSON><PERSON>(options))\n    };\n}\n// prettier-ignore\nfunction FromPropertyKeys(type, propertyKeys, options) {\n    return propertyKeys.reduce((result, leftKey) => {\n        return { ...result, ...FromPropertyKey(type, leftKey, options) };\n    }, {});\n}\n// prettier-ignore\nfunction FromMappedKey(type, mappedKey, options) {\n    return FromPropertyKeys(type, mappedKey.keys, options);\n}\n// prettier-ignore\nexport function PickFromMappedKey(type, mappedKey, options) {\n    const properties = FromMappedKey(type, mappedKey, options);\n    return MappedResult(properties);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Computed } from '../computed/index.mjs';\nimport { Optional } from '../optional/index.mjs';\nimport { Object } from '../object/index.mjs';\nimport { Intersect } from '../intersect/index.mjs';\nimport { Union } from '../union/index.mjs';\nimport { Ref } from '../ref/index.mjs';\nimport { Discard } from '../discard/index.mjs';\nimport { TransformKind } from '../symbols/index.mjs';\nimport { PartialFromMappedResult } from './partial-from-mapped-result.mjs';\n// ------------------------------------------------------------------\n// KindGuard\n// ------------------------------------------------------------------\nimport * as KindGuard from '../guard/kind.mjs';\n// prettier-ignore\nfunction FromComputed(target, parameters) {\n    return Computed('Partial', [Computed(target, parameters)]);\n}\n// prettier-ignore\nfunction FromRef($ref) {\n    return Computed('Partial', [Ref($ref)]);\n}\n// prettier-ignore\nfunction FromProperties(properties) {\n    const partialProperties = {};\n    for (const K of globalThis.Object.getOwnPropertyNames(properties))\n        partialProperties[K] = Optional(properties[K]);\n    return partialProperties;\n}\n// prettier-ignore\nfunction FromObject(type) {\n    const options = Discard(type, [TransformKind, '$id', 'required', 'properties']);\n    const properties = FromProperties(type['properties']);\n    return Object(properties, options);\n}\n// prettier-ignore\nfunction FromRest(types) {\n    return types.map(type => PartialResolve(type));\n}\n// ------------------------------------------------------------------\n// PartialResolve\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction PartialResolve(type) {\n    return (\n    // Mappable\n    KindGuard.IsComputed(type) ? FromComputed(type.target, type.parameters) :\n        KindGuard.IsRef(type) ? FromRef(type.$ref) :\n            KindGuard.IsIntersect(type) ? Intersect(FromRest(type.allOf)) :\n                KindGuard.IsUnion(type) ? Union(FromRest(type.anyOf)) :\n                    KindGuard.IsObject(type) ? FromObject(type) :\n                        // Intrinsic\n                        KindGuard.IsBigInt(type) ? type :\n                            KindGuard.IsBoolean(type) ? type :\n                                KindGuard.IsInteger(type) ? type :\n                                    KindGuard.IsLiteral(type) ? type :\n                                        KindGuard.IsNull(type) ? type :\n                                            KindGuard.IsNumber(type) ? type :\n                                                KindGuard.IsString(type) ? type :\n                                                    KindGuard.IsSymbol(type) ? type :\n                                                        KindGuard.IsUndefined(type) ? type :\n                                                            // Passthrough\n                                                            Object({}));\n}\n/** `[Json]` Constructs a type where all properties are optional */\nexport function Partial(type, options) {\n    if (KindGuard.IsMappedResult(type)) {\n        return PartialFromMappedResult(type, options);\n    }\n    else {\n        // special: mapping types require overridable options\n        return CreateType({ ...PartialResolve(type), ...options });\n    }\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { Partial } from './partial.mjs';\nimport { Clone } from '../clone/value.mjs';\n// prettier-ignore\nfunction FromProperties(K, options) {\n    const Acc = {};\n    for (const K2 of globalThis.Object.getOwnPropertyNames(K))\n        Acc[K2] = Partial(K[K2], Clone(options));\n    return Acc;\n}\n// prettier-ignore\nfunction FromMappedResult(R, options) {\n    return FromProperties(R.properties, options);\n}\n// prettier-ignore\nexport function PartialFromMappedResult(R, options) {\n    const P = FromMappedResult(R, options);\n    return MappedResult(P);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Computed } from '../computed/index.mjs';\nimport { Object } from '../object/index.mjs';\nimport { Intersect } from '../intersect/index.mjs';\nimport { Union } from '../union/index.mjs';\nimport { Ref } from '../ref/index.mjs';\nimport { OptionalKind, TransformKind } from '../symbols/index.mjs';\nimport { Discard } from '../discard/index.mjs';\nimport { RequiredFromMappedResult } from './required-from-mapped-result.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport * as KindGuard from '../guard/kind.mjs';\n// prettier-ignore\nfunction FromComputed(target, parameters) {\n    return Computed('Required', [Computed(target, parameters)]);\n}\n// prettier-ignore\nfunction FromRef($ref) {\n    return Computed('Required', [Ref($ref)]);\n}\n// prettier-ignore\nfunction FromProperties(properties) {\n    const requiredProperties = {};\n    for (const K of globalThis.Object.getOwnPropertyNames(properties))\n        requiredProperties[K] = Discard(properties[K], [OptionalKind]);\n    return requiredProperties;\n}\n// prettier-ignore\nfunction FromObject(type) {\n    const options = Discard(type, [TransformKind, '$id', 'required', 'properties']);\n    const properties = FromProperties(type['properties']);\n    return Object(properties, options);\n}\n// prettier-ignore\nfunction FromRest(types) {\n    return types.map(type => RequiredResolve(type));\n}\n// ------------------------------------------------------------------\n// RequiredResolve\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction RequiredResolve(type) {\n    return (\n    // Mappable\n    KindGuard.IsComputed(type) ? FromComputed(type.target, type.parameters) :\n        KindGuard.IsRef(type) ? FromRef(type.$ref) :\n            KindGuard.IsIntersect(type) ? Intersect(FromRest(type.allOf)) :\n                KindGuard.IsUnion(type) ? Union(FromRest(type.anyOf)) :\n                    KindGuard.IsObject(type) ? FromObject(type) :\n                        // Intrinsic\n                        KindGuard.IsBigInt(type) ? type :\n                            KindGuard.IsBoolean(type) ? type :\n                                KindGuard.IsInteger(type) ? type :\n                                    KindGuard.IsLiteral(type) ? type :\n                                        KindGuard.IsNull(type) ? type :\n                                            KindGuard.IsNumber(type) ? type :\n                                                KindGuard.IsString(type) ? type :\n                                                    KindGuard.IsSymbol(type) ? type :\n                                                        KindGuard.IsUndefined(type) ? type :\n                                                            // Passthrough\n                                                            Object({}));\n}\n/** `[Json]` Constructs a type where all properties are required */\nexport function Required(type, options) {\n    if (KindGuard.IsMappedResult(type)) {\n        return RequiredFromMappedResult(type, options);\n    }\n    else {\n        // special: mapping types require overridable options\n        return CreateType({ ...RequiredResolve(type), ...options });\n    }\n}\n", "import { MappedResult } from '../mapped/index.mjs';\nimport { Required } from './required.mjs';\n// prettier-ignore\nfunction FromProperties(P, options) {\n    const Acc = {};\n    for (const K2 of globalThis.Object.getOwnPropertyNames(P))\n        Acc[K2] = Required(P[K2], options);\n    return Acc;\n}\n// prettier-ignore\nfunction FromMappedResult(R, options) {\n    return FromProperties(R.properties, options);\n}\n// prettier-ignore\nexport function RequiredFromMappedResult(R, options) {\n    const P = FromMappedResult(R, options);\n    return MappedResult(P);\n}\n", "import { CreateType } from '../create/index.mjs';\nimport { CloneType } from '../clone/index.mjs';\nimport { Discard } from '../discard/index.mjs';\nimport { Array } from '../array/index.mjs';\nimport { Awaited } from '../awaited/index.mjs';\nimport { AsyncIterator } from '../async-iterator/index.mjs';\nimport { Constructor } from '../constructor/index.mjs';\nimport { Index } from '../indexed/index.mjs';\nimport { Function as FunctionType } from '../function/index.mjs';\nimport { Intersect } from '../intersect/index.mjs';\nimport { Iterator } from '../iterator/index.mjs';\nimport { KeyOf } from '../keyof/index.mjs';\nimport { Object } from '../object/index.mjs';\nimport { Omit } from '../omit/index.mjs';\nimport { Pick } from '../pick/index.mjs';\nimport { Never } from '../never/index.mjs';\nimport { Partial } from '../partial/index.mjs';\nimport { RecordValue, RecordPattern } from '../record/index.mjs';\nimport { Required } from '../required/index.mjs';\nimport { Tuple } from '../tuple/index.mjs';\nimport { Union } from '../union/index.mjs';\n// ------------------------------------------------------------------\n// Symbols\n// ------------------------------------------------------------------\nimport { TransformKind, OptionalKind, ReadonlyKind } from '../symbols/index.mjs';\n// ------------------------------------------------------------------\n// KindGuard\n// ------------------------------------------------------------------\nimport * as KindGuard from '../guard/kind.mjs';\n// prettier-ignore\nfunction DereferenceParameters(moduleProperties, types) {\n    return types.map((type) => {\n        return KindGuard.IsRef(type)\n            ? Dereference(moduleProperties, type.$ref)\n            : FromType(moduleProperties, type);\n    });\n}\n// prettier-ignore\nfunction Dereference(moduleProperties, ref) {\n    return (ref in moduleProperties\n        ? KindGuard.IsRef(moduleProperties[ref])\n            ? Dereference(moduleProperties, moduleProperties[ref].$ref)\n            : FromType(moduleProperties, moduleProperties[ref])\n        : Never());\n}\n// prettier-ignore\nfunction FromAwaited(parameters) {\n    return Awaited(parameters[0]);\n}\n// prettier-ignore\nfunction FromIndex(parameters) {\n    return Index(parameters[0], parameters[1]);\n}\n// prettier-ignore\nfunction FromKeyOf(parameters) {\n    return KeyOf(parameters[0]);\n}\n// prettier-ignore\nfunction FromPartial(parameters) {\n    return Partial(parameters[0]);\n}\n// prettier-ignore\nfunction FromOmit(parameters) {\n    return Omit(parameters[0], parameters[1]);\n}\n// prettier-ignore\nfunction FromPick(parameters) {\n    return Pick(parameters[0], parameters[1]);\n}\n// prettier-ignore\nfunction FromRequired(parameters) {\n    return Required(parameters[0]);\n}\n// prettier-ignore\nfunction FromComputed(moduleProperties, target, parameters) {\n    const dereferenced = DereferenceParameters(moduleProperties, parameters);\n    return (target === 'Awaited' ? FromAwaited(dereferenced) :\n        target === 'Index' ? FromIndex(dereferenced) :\n            target === 'KeyOf' ? FromKeyOf(dereferenced) :\n                target === 'Partial' ? FromPartial(dereferenced) :\n                    target === 'Omit' ? FromOmit(dereferenced) :\n                        target === 'Pick' ? FromPick(dereferenced) :\n                            target === 'Required' ? FromRequired(dereferenced) :\n                                Never());\n}\nfunction FromArray(moduleProperties, type) {\n    return Array(FromType(moduleProperties, type));\n}\nfunction FromAsyncIterator(moduleProperties, type) {\n    return AsyncIterator(FromType(moduleProperties, type));\n}\n// prettier-ignore\nfunction FromConstructor(moduleProperties, parameters, instanceType) {\n    return Constructor(FromTypes(moduleProperties, parameters), FromType(moduleProperties, instanceType));\n}\n// prettier-ignore\nfunction FromFunction(moduleProperties, parameters, returnType) {\n    return FunctionType(FromTypes(moduleProperties, parameters), FromType(moduleProperties, returnType));\n}\nfunction FromIntersect(moduleProperties, types) {\n    return Intersect(FromTypes(moduleProperties, types));\n}\nfunction FromIterator(moduleProperties, type) {\n    return Iterator(FromType(moduleProperties, type));\n}\nfunction FromObject(moduleProperties, properties) {\n    return Object(globalThis.Object.keys(properties).reduce((result, key) => {\n        return { ...result, [key]: FromType(moduleProperties, properties[key]) };\n    }, {}));\n}\n// prettier-ignore\nfunction FromRecord(moduleProperties, type) {\n    const [value, pattern] = [FromType(moduleProperties, RecordValue(type)), RecordPattern(type)];\n    const result = CloneType(type);\n    result.patternProperties[pattern] = value;\n    return result;\n}\n// prettier-ignore\nfunction FromTransform(moduleProperties, transform) {\n    return (KindGuard.IsRef(transform))\n        ? { ...Dereference(moduleProperties, transform.$ref), [TransformKind]: transform[TransformKind] }\n        : transform;\n}\nfunction FromTuple(moduleProperties, types) {\n    return Tuple(FromTypes(moduleProperties, types));\n}\nfunction FromUnion(moduleProperties, types) {\n    return Union(FromTypes(moduleProperties, types));\n}\nfunction FromTypes(moduleProperties, types) {\n    return types.map((type) => FromType(moduleProperties, type));\n}\n// prettier-ignore\nexport function FromType(moduleProperties, type) {\n    return (\n    // Modifiers\n    KindGuard.IsOptional(type) ? CreateType(FromType(moduleProperties, Discard(type, [OptionalKind])), type) :\n        KindGuard.IsReadonly(type) ? CreateType(FromType(moduleProperties, Discard(type, [ReadonlyKind])), type) :\n            // Transform\n            KindGuard.IsTransform(type) ? CreateType(FromTransform(moduleProperties, type), type) :\n                // Types\n                KindGuard.IsArray(type) ? CreateType(FromArray(moduleProperties, type.items), type) :\n                    KindGuard.IsAsyncIterator(type) ? CreateType(FromAsyncIterator(moduleProperties, type.items), type) :\n                        KindGuard.IsComputed(type) ? CreateType(FromComputed(moduleProperties, type.target, type.parameters)) :\n                            KindGuard.IsConstructor(type) ? CreateType(FromConstructor(moduleProperties, type.parameters, type.returns), type) :\n                                KindGuard.IsFunction(type) ? CreateType(FromFunction(moduleProperties, type.parameters, type.returns), type) :\n                                    KindGuard.IsIntersect(type) ? CreateType(FromIntersect(moduleProperties, type.allOf), type) :\n                                        KindGuard.IsIterator(type) ? CreateType(FromIterator(moduleProperties, type.items), type) :\n                                            KindGuard.IsObject(type) ? CreateType(FromObject(moduleProperties, type.properties), type) :\n                                                KindGuard.IsRecord(type) ? CreateType(FromRecord(moduleProperties, type)) :\n                                                    KindGuard.IsTuple(type) ? CreateType(FromTuple(moduleProperties, type.items || []), type) :\n                                                        KindGuard.IsUnion(type) ? CreateType(FromUnion(moduleProperties, type.anyOf), type) :\n                                                            type);\n}\n// prettier-ignore\nexport function ComputeType(moduleProperties, key) {\n    return (key in moduleProperties\n        ? FromType(moduleProperties, moduleProperties[key])\n        : Never());\n}\n// prettier-ignore\nexport function ComputeModuleProperties(moduleProperties) {\n    return globalThis.Object.getOwnPropertyNames(moduleProperties).reduce((result, key) => {\n        return { ...result, [key]: ComputeType(moduleProperties, key) };\n    }, {});\n}\n", "import { CreateType } from '../create/index.mjs';\nimport { Kind } from '../symbols/index.mjs';\n// ------------------------------------------------------------------\n// Module Infrastructure Types\n// ------------------------------------------------------------------\nimport { ComputeModuleProperties } from './compute.mjs';\n// ------------------------------------------------------------------\n// Module\n// ------------------------------------------------------------------\n// prettier-ignore\nexport class TModule {\n    constructor($defs) {\n        const computed = ComputeModuleProperties($defs);\n        const identified = this.WithIdentifiers(computed);\n        this.$defs = identified;\n    }\n    /** `[Json]` Imports a Type by Key. */\n    Import(key, options) {\n        const $defs = { ...this.$defs, [key]: CreateType(this.$defs[key], options) };\n        return CreateType({ [Kind]: 'Import', $defs, $ref: key });\n    }\n    // prettier-ignore\n    WithIdentifiers($defs) {\n        return globalThis.Object.getOwnPropertyNames($defs).reduce((result, key) => {\n            return { ...result, [key]: { ...$defs[key], $id: key } };\n        }, {});\n    }\n}\n/** `[Json]` Creates a Type Definition Module. */\nexport function Module(properties) {\n    return new TModule(properties);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[<PERSON><PERSON>]` Creates a Not type */\nexport function Not(type, options) {\n    return CreateType({ [Kind]: 'Not', not: type }, options);\n}\n", "import { <PERSON><PERSON> } from '../tuple/index.mjs';\nimport { Never } from '../never/index.mjs';\nimport * as KindGuard from '../guard/kind.mjs';\n/** `[JavaScript]` Extracts the Parameters from the given Function type */\nexport function Parameters(schema, options) {\n    return (KindGuard.IsFunction(schema) ? Tuple(schema.parameters, options) : Never());\n}\n", "import { CloneType } from '../clone/type.mjs';\nimport { CreateType } from '../create/type.mjs';\nimport { IsUndefined } from '../guard/value.mjs';\nimport { Kind, Hint } from '../symbols/index.mjs';\n// Auto Tracked For Recursive Types without ID's\nlet Ordinal = 0;\n/** `[<PERSON><PERSON>]` Creates a Recursive type */\nexport function Recursive(callback, options = {}) {\n    if (IsUndefined(options.$id))\n        options.$id = `T${Ordinal++}`;\n    const thisType = CloneType(callback({ [Kind]: 'This', $ref: `${options.$id}` }));\n    thisType.$id = options.$id;\n    // prettier-ignore\n    return CreateType({ [Hint]: 'Recursive', ...thisType }, options);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { IsString } from '../guard/value.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[JavaScript]` Creates a RegExp type */\nexport function RegExp(unresolved, options) {\n    const expr = IsString(unresolved) ? new globalThis.RegExp(unresolved) : unresolved;\n    return CreateType({ [Kind]: 'RegExp', type: 'RegExp', source: expr.source, flags: expr.flags }, options);\n}\n", "// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsIntersect, IsUnion, IsTuple } from '../guard/kind.mjs';\n// prettier-ignore\nfunction RestResolve(T) {\n    return (IsIntersect(T) ? T.allOf :\n        IsUnion(T) ? T.anyOf :\n            IsTuple(T) ? T.items ?? [] :\n                []);\n}\n/** `[<PERSON><PERSON>]` Extracts interior Rest elements from Tuple, Intersect and Union types */\nexport function Rest(T) {\n    return RestResolve(T);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Never } from '../never/index.mjs';\nimport * as KindGuard from '../guard/kind.mjs';\n/** `[JavaScript]` Extracts the ReturnType from the given Function type */\nexport function ReturnType(schema, options) {\n    return (KindGuard.IsFunction(schema) ? CreateType(schema.returns, options) : Never(options));\n}\n", "import { TransformKind } from '../symbols/index.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsTransform } from '../guard/kind.mjs';\n// ------------------------------------------------------------------\n// TransformBuilders\n// ------------------------------------------------------------------\nexport class TransformDecodeBuilder {\n    constructor(schema) {\n        this.schema = schema;\n    }\n    Decode(decode) {\n        return new TransformEncodeBuilder(this.schema, decode);\n    }\n}\n// prettier-ignore\nexport class TransformEncodeBuilder {\n    constructor(schema, decode) {\n        this.schema = schema;\n        this.decode = decode;\n    }\n    EncodeTransform(encode, schema) {\n        const Encode = (value) => schema[TransformKind].Encode(encode(value));\n        const Decode = (value) => this.decode(schema[TransformKind].Decode(value));\n        const Codec = { Encode: Encode, Decode: Decode };\n        return { ...schema, [TransformKind]: Codec };\n    }\n    EncodeSchema(encode, schema) {\n        const Codec = { Decode: this.decode, Encode: encode };\n        return { ...schema, [TransformKind]: Codec };\n    }\n    Encode(encode) {\n        return (IsTransform(this.schema) ? this.EncodeTransform(encode, this.schema) : this.EncodeSchema(encode, this.schema));\n    }\n}\n/** `[Json]` Creates a Transform type */\nexport function Transform(schema) {\n    return new TransformDecodeBuilder(schema);\n}\n", "import { CreateType } from '../create/type.mjs';\nimport { Kind } from '../symbols/index.mjs';\n/** `[JavaScript]` Creates a Void type */\nexport function Void(options) {\n    return CreateType({ [Kind]: 'Void', type: 'void' }, options);\n}\n", "import { Any } from '../any/index.mjs';\nimport { Array } from '../array/index.mjs';\nimport { <PERSON>olean } from '../boolean/index.mjs';\nimport { Composite } from '../composite/index.mjs';\nimport { Const } from '../const/index.mjs';\nimport { Enum } from '../enum/index.mjs';\nimport { Exclude } from '../exclude/index.mjs';\nimport { Extends } from '../extends/index.mjs';\nimport { Extract } from '../extract/index.mjs';\nimport { Index } from '../indexed/index.mjs';\nimport { Integer } from '../integer/index.mjs';\nimport { Intersect } from '../intersect/index.mjs';\nimport { Capitalize, Uncapitalize, Lowercase, Uppercase } from '../intrinsic/index.mjs';\nimport { KeyOf } from '../keyof/index.mjs';\nimport { Literal } from '../literal/index.mjs';\nimport { Mapped } from '../mapped/index.mjs';\nimport { Never } from '../never/index.mjs';\nimport { Not } from '../not/index.mjs';\nimport { Null } from '../null/index.mjs';\nimport { Module } from '../module/index.mjs';\nimport { Number } from '../number/index.mjs';\nimport { Object } from '../object/index.mjs';\nimport { Omit } from '../omit/index.mjs';\nimport { Optional } from '../optional/index.mjs';\nimport { Partial } from '../partial/index.mjs';\nimport { Pick } from '../pick/index.mjs';\nimport { Readonly } from '../readonly/index.mjs';\nimport { ReadonlyOptional } from '../readonly-optional/index.mjs';\nimport { Record } from '../record/index.mjs';\nimport { Recursive } from '../recursive/index.mjs';\nimport { Ref } from '../ref/index.mjs';\nimport { Required } from '../required/index.mjs';\nimport { Rest } from '../rest/index.mjs';\nimport { String } from '../string/index.mjs';\nimport { TemplateLiteral } from '../template-literal/index.mjs';\nimport { Transform } from '../transform/index.mjs';\nimport { Tuple } from '../tuple/index.mjs';\nimport { Union } from '../union/index.mjs';\nimport { Unknown } from '../unknown/index.mjs';\nimport { Unsafe } from '../unsafe/index.mjs';\n/** Json Type Builder with Static Resolution for TypeScript */\nexport class JsonTypeBuilder {\n    // ------------------------------------------------------------------------\n    // Modifiers\n    // ------------------------------------------------------------------------\n    /** `[Json]` Creates a Readonly and Optional property */\n    ReadonlyOptional(type) {\n        return ReadonlyOptional(type);\n    }\n    /** `[Json]` Creates a Readonly property */\n    Readonly(type, enable) {\n        return Readonly(type, enable ?? true);\n    }\n    /** `[Json]` Creates a Optional property */\n    Optional(type, enable) {\n        return Optional(type, enable ?? true);\n    }\n    // ------------------------------------------------------------------------\n    // Types\n    // ------------------------------------------------------------------------\n    /** `[Json]` Creates an Any type */\n    Any(options) {\n        return Any(options);\n    }\n    /** `[Json]` Creates an Array type */\n    Array(items, options) {\n        return Array(items, options);\n    }\n    /** `[Json]` Creates a Boolean type */\n    Boolean(options) {\n        return Boolean(options);\n    }\n    /** `[Json]` Intrinsic function to Capitalize LiteralString types */\n    Capitalize(schema, options) {\n        return Capitalize(schema, options);\n    }\n    /** `[Json]` Creates a Composite object type */\n    Composite(schemas, options) {\n        return Composite(schemas, options); // (error) TS 5.4.0-dev - review TComposite implementation\n    }\n    /** `[JavaScript]` Creates a readonly const type from the given value. */\n    Const(value, options) {\n        return Const(value, options);\n    }\n    /** `[Json]` Creates a Enum type */\n    Enum(item, options) {\n        return Enum(item, options);\n    }\n    /** `[Json]` Constructs a type by excluding from unionType all union members that are assignable to excludedMembers */\n    Exclude(unionType, excludedMembers, options) {\n        return Exclude(unionType, excludedMembers, options);\n    }\n    /** `[Json]` Creates a Conditional type */\n    Extends(L, R, T, F, options) {\n        return Extends(L, R, T, F, options);\n    }\n    /** `[Json]` Constructs a type by extracting from type all union members that are assignable to union */\n    Extract(type, union, options) {\n        return Extract(type, union, options);\n    }\n    /** `[Json]` Returns an Indexed property type for the given keys */\n    Index(type, key, options) {\n        return Index(type, key, options);\n    }\n    /** `[Json]` Creates an Integer type */\n    Integer(options) {\n        return Integer(options);\n    }\n    /** `[Json]` Creates an Intersect type */\n    Intersect(types, options) {\n        return Intersect(types, options);\n    }\n    /** `[Json]` Creates a KeyOf type */\n    KeyOf(type, options) {\n        return KeyOf(type, options);\n    }\n    /** `[Json]` Creates a Literal type */\n    Literal(literalValue, options) {\n        return Literal(literalValue, options);\n    }\n    /** `[Json]` Intrinsic function to Lowercase LiteralString types */\n    Lowercase(type, options) {\n        return Lowercase(type, options);\n    }\n    /** `[Json]` Creates a Mapped object type */\n    Mapped(key, map, options) {\n        return Mapped(key, map, options);\n    }\n    /** `[Json]` Creates a Type Definition Module. */\n    Module(properties) {\n        return Module(properties);\n    }\n    /** `[Json]` Creates a Never type */\n    Never(options) {\n        return Never(options);\n    }\n    /** `[Json]` Creates a Not type */\n    Not(type, options) {\n        return Not(type, options);\n    }\n    /** `[Json]` Creates a Null type */\n    Null(options) {\n        return Null(options);\n    }\n    /** `[Json]` Creates a Number type */\n    Number(options) {\n        return Number(options);\n    }\n    /** `[Json]` Creates an Object type */\n    Object(properties, options) {\n        return Object(properties, options);\n    }\n    /** `[Json]` Constructs a type whose keys are omitted from the given type */\n    Omit(schema, selector, options) {\n        return Omit(schema, selector, options);\n    }\n    /** `[Json]` Constructs a type where all properties are optional */\n    Partial(type, options) {\n        return Partial(type, options);\n    }\n    /** `[Json]` Constructs a type whose keys are picked from the given type */\n    Pick(type, key, options) {\n        return Pick(type, key, options);\n    }\n    /** `[Json]` Creates a Record type */\n    Record(key, value, options) {\n        return Record(key, value, options);\n    }\n    /** `[Json]` Creates a Recursive type */\n    Recursive(callback, options) {\n        return Recursive(callback, options);\n    }\n    /** `[Json]` Creates a Ref type. The referenced type must contain a $id */\n    Ref(...args) {\n        return Ref(args[0], args[1]);\n    }\n    /** `[Json]` Constructs a type where all properties are required */\n    Required(type, options) {\n        return Required(type, options);\n    }\n    /** `[Json]` Extracts interior Rest elements from Tuple, Intersect and Union types */\n    Rest(type) {\n        return Rest(type);\n    }\n    /** `[Json]` Creates a String type */\n    String(options) {\n        return String(options);\n    }\n    /** `[Json]` Creates a TemplateLiteral type */\n    TemplateLiteral(unresolved, options) {\n        return TemplateLiteral(unresolved, options);\n    }\n    /** `[Json]` Creates a Transform type */\n    Transform(type) {\n        return Transform(type);\n    }\n    /** `[Json]` Creates a Tuple type */\n    Tuple(types, options) {\n        return Tuple(types, options);\n    }\n    /** `[Json]` Intrinsic function to Uncapitalize LiteralString types */\n    Uncapitalize(type, options) {\n        return Uncapitalize(type, options);\n    }\n    /** `[Json]` Creates a Union type */\n    Union(types, options) {\n        return Union(types, options);\n    }\n    /** `[Json]` Creates an Unknown type */\n    Unknown(options) {\n        return Unknown(options);\n    }\n    /** `[Json]` Creates a Unsafe type that will infers as the generic argument T */\n    Unsafe(options) {\n        return Unsafe(options);\n    }\n    /** `[Json]` Intrinsic function to Uppercase LiteralString types */\n    Uppercase(schema, options) {\n        return Uppercase(schema, options);\n    }\n}\n", "// ------------------------------------------------------------------\n// Type: Module\n// ------------------------------------------------------------------\nexport { Any } from '../any/index.mjs';\nexport { Argument } from '../argument/index.mjs';\nexport { Array } from '../array/index.mjs';\nexport { AsyncIterator } from '../async-iterator/index.mjs';\nexport { Awaited } from '../awaited/index.mjs';\nexport { BigInt } from '../bigint/index.mjs';\nexport { Boolean } from '../boolean/index.mjs';\nexport { Composite } from '../composite/index.mjs';\nexport { Const } from '../const/index.mjs';\nexport { Constructor } from '../constructor/index.mjs';\nexport { ConstructorParameters } from '../constructor-parameters/index.mjs';\nexport { Date } from '../date/index.mjs';\nexport { Enum } from '../enum/index.mjs';\nexport { Exclude } from '../exclude/index.mjs';\nexport { Extends } from '../extends/index.mjs';\nexport { Extract } from '../extract/index.mjs';\nexport { Function } from '../function/index.mjs';\nexport { Index } from '../indexed/index.mjs';\nexport { InstanceType } from '../instance-type/index.mjs';\nexport { Instantiate } from '../instantiate/index.mjs';\nexport { Integer } from '../integer/index.mjs';\nexport { Intersect } from '../intersect/index.mjs';\nexport { Capitalize, Uncapitalize, Lowercase, Uppercase } from '../intrinsic/index.mjs';\nexport { Iterator } from '../iterator/index.mjs';\nexport { KeyOf } from '../keyof/index.mjs';\nexport { Literal } from '../literal/index.mjs';\nexport { Mapped } from '../mapped/index.mjs';\nexport { Module } from '../module/index.mjs';\nexport { Never } from '../never/index.mjs';\nexport { Not } from '../not/index.mjs';\nexport { Null } from '../null/index.mjs';\nexport { Number } from '../number/index.mjs';\nexport { Object } from '../object/index.mjs';\nexport { Omit } from '../omit/index.mjs';\nexport { Optional } from '../optional/index.mjs';\nexport { Parameters } from '../parameters/index.mjs';\nexport { Partial } from '../partial/index.mjs';\nexport { Pick } from '../pick/index.mjs';\nexport { Promise } from '../promise/index.mjs';\nexport { Readonly } from '../readonly/index.mjs';\nexport { ReadonlyOptional } from '../readonly-optional/index.mjs';\nexport { Record } from '../record/index.mjs';\nexport { Recursive } from '../recursive/index.mjs';\nexport { Ref } from '../ref/index.mjs';\nexport { RegExp } from '../regexp/index.mjs';\nexport { Required } from '../required/index.mjs';\nexport { Rest } from '../rest/index.mjs';\nexport { ReturnType } from '../return-type/index.mjs';\nexport { String } from '../string/index.mjs';\nexport { Symbol } from '../symbol/index.mjs';\nexport { TemplateLiteral } from '../template-literal/index.mjs';\nexport { Transform } from '../transform/index.mjs';\nexport { Tuple } from '../tuple/index.mjs';\nexport { Uint8Array } from '../uint8array/index.mjs';\nexport { Undefined } from '../undefined/index.mjs';\nexport { Union } from '../union/index.mjs';\nexport { Unknown } from '../unknown/index.mjs';\nexport { Unsafe } from '../unsafe/index.mjs';\nexport { Void } from '../void/index.mjs';\n", "import { JsonTypeBuilder } from './json.mjs';\nimport { Argument } from '../argument/index.mjs';\nimport { AsyncIterator } from '../async-iterator/index.mjs';\nimport { Awaited } from '../awaited/index.mjs';\nimport { BigInt } from '../bigint/index.mjs';\nimport { Constructor } from '../constructor/index.mjs';\nimport { ConstructorParameters } from '../constructor-parameters/index.mjs';\nimport { Date } from '../date/index.mjs';\nimport { Function as FunctionType } from '../function/index.mjs';\nimport { InstanceType } from '../instance-type/index.mjs';\nimport { Instantiate } from '../instantiate/index.mjs';\nimport { Iterator } from '../iterator/index.mjs';\nimport { Parameters } from '../parameters/index.mjs';\nimport { Promise } from '../promise/index.mjs';\nimport { RegExp } from '../regexp/index.mjs';\nimport { ReturnType } from '../return-type/index.mjs';\nimport { Symbol } from '../symbol/index.mjs';\nimport { Uint8Array } from '../uint8array/index.mjs';\nimport { Undefined } from '../undefined/index.mjs';\nimport { Void } from '../void/index.mjs';\n/** JavaScript Type Builder with Static Resolution for TypeScript */\nexport class JavaScriptTypeBuilder extends JsonTypeBuilder {\n    /** `[JavaScript]` Creates a Generic Argument Type */\n    Argument(index) {\n        return Argument(index);\n    }\n    /** `[JavaScript]` Creates a AsyncIterator type */\n    AsyncIterator(items, options) {\n        return AsyncIterator(items, options);\n    }\n    /** `[JavaScript]` Constructs a type by recursively unwrapping Promise types */\n    Awaited(schema, options) {\n        return Awaited(schema, options);\n    }\n    /** `[JavaScript]` Creates a BigInt type */\n    BigInt(options) {\n        return BigInt(options);\n    }\n    /** `[JavaScript]` Extracts the ConstructorParameters from the given Constructor type */\n    ConstructorParameters(schema, options) {\n        return ConstructorParameters(schema, options);\n    }\n    /** `[JavaScript]` Creates a Constructor type */\n    Constructor(parameters, instanceType, options) {\n        return Constructor(parameters, instanceType, options);\n    }\n    /** `[JavaScript]` Creates a Date type */\n    Date(options = {}) {\n        return Date(options);\n    }\n    /** `[JavaScript]` Creates a Function type */\n    Function(parameters, returnType, options) {\n        return FunctionType(parameters, returnType, options);\n    }\n    /** `[JavaScript]` Extracts the InstanceType from the given Constructor type */\n    InstanceType(schema, options) {\n        return InstanceType(schema, options);\n    }\n    /** `[JavaScript]` Instantiates a type with the given parameters */\n    Instantiate(schema, parameters) {\n        return Instantiate(schema, parameters);\n    }\n    /** `[JavaScript]` Creates an Iterator type */\n    Iterator(items, options) {\n        return Iterator(items, options);\n    }\n    /** `[JavaScript]` Extracts the Parameters from the given Function type */\n    Parameters(schema, options) {\n        return Parameters(schema, options);\n    }\n    /** `[JavaScript]` Creates a Promise type */\n    Promise(item, options) {\n        return Promise(item, options);\n    }\n    /** `[JavaScript]` Creates a RegExp type */\n    RegExp(unresolved, options) {\n        return RegExp(unresolved, options);\n    }\n    /** `[JavaScript]` Extracts the ReturnType from the given Function type */\n    ReturnType(type, options) {\n        return ReturnType(type, options);\n    }\n    /** `[JavaScript]` Creates a Symbol type */\n    Symbol(options) {\n        return Symbol(options);\n    }\n    /** `[JavaScript]` Creates a Undefined type */\n    Undefined(options) {\n        return Undefined(options);\n    }\n    /** `[JavaScript]` Creates a Uint8Array type */\n    Uint8Array(options) {\n        return Uint8Array(options);\n    }\n    /** `[JavaScript]` Creates a Void type */\n    Void(options) {\n        return Void(options);\n    }\n}\n", "// ------------------------------------------------------------------\n// JsonTypeBuilder\n// ------------------------------------------------------------------\nexport { JsonTypeBuilder } from './json.mjs';\n// ------------------------------------------------------------------\n// JavaScriptTypeBuilder\n// ------------------------------------------------------------------\nimport * as TypeBuilder from './type.mjs';\nimport { JavaScriptTypeBuilder } from './javascript.mjs';\n/** JavaScript Type Builder with Static Resolution for TypeScript */\nconst Type = TypeBuilder;\nexport { JavaScriptTypeBuilder };\nexport { Type };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,SAAS,UAAU,SAAS;AAC/B,SAAO,QAAQ,IAAI,CAAC,WAAW,UAAU,MAAM,CAAC;AACpD;AAEO,SAAS,UAAU,QAAQ,SAAS;AACvC,SAAO,YAAY,SAAY,MAAM,MAAM,IAAI,MAAM,EAAE,GAAG,SAAS,GAAG,OAAO,CAAC;AAClF;;;ACPO,SAAS,UAAU,GAAG;AACzB,UAAQ,SAAS,CAAC,IAAI,GAAG,SAAS;AACtC;;;ACAO,SAAS,SAAS,OAAO;AAC5B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,YAAY,MAAM,CAAC;AACnD;;;ACKA,SAAS,aAAa,QAAQ,YAAY;AACtC,SAAO,SAAS,WAAW,CAAC,SAAS,QAAQ,UAAU,CAAC,CAAC;AAC7D;AAEA,SAAS,QAAQ,MAAM;AACnB,SAAO,SAAS,WAAW,CAAC,IAAI,IAAI,CAAC,CAAC;AAC1C;AAEA,SAAS,cAAc,OAAO;AAC1B,SAAO,UAAU,SAAS,KAAK,CAAC;AACpC;AAEA,SAAS,UAAU,OAAO;AACtB,SAAO,MAAM,SAAS,KAAK,CAAC;AAChC;AAEA,SAAS,YAAY,MAAM;AACvB,SAAO,QAAQ,IAAI;AACvB;AAEA,SAAS,SAAS,OAAO;AACrB,SAAO,MAAM,IAAI,UAAQ,QAAQ,IAAI,CAAC;AAC1C;AAEO,SAAS,QAAQ,MAAM,SAAS;AACnC,SAAO,WAAW,WAAW,IAAI,IAAI,aAAa,KAAK,QAAQ,KAAK,UAAU,IAAI,YAAY,IAAI,IAAI,cAAc,KAAK,KAAK,IAAI,QAAQ,IAAI,IAAI,UAAU,KAAK,KAAK,IAAI,UAAU,IAAI,IAAI,YAAY,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,MAAM,OAAO;AACzQ;;;AC1BA,SAAS,cAAc,GAAG;AACtB,QAAM,MAAM,CAAC;AACb,aAAW,KAAK;AACZ,QAAI,KAAK,GAAG,kBAAkB,CAAC,CAAC;AACpC,SAAO,YAAY,GAAG;AAC1B;AAEA,SAAS,YAAY,GAAG;AACpB,SAAO,EAAE,OAAO,OAAK,CAAC,QAAQ,CAAC,CAAC;AACpC;AAEA,SAAS,kBAAkB,GAAG,GAAG;AAC7B,QAAM,MAAM,CAAC;AACb,aAAW,KAAK;AACZ,QAAI,KAAK,GAAG,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,SAAO,YAAY,GAAG;AAC1B;AAEA,SAAS,oBAAoB,GAAG,GAAG;AAC/B,QAAM,MAAM,CAAC;AACb,aAAW,KAAK,GAAG;AACf,QAAI,CAAC,IAAI,mBAAmB,kBAAkB,GAAG,CAAC,CAAC;AAAA,EACvD;AACA,SAAO;AACX;AAEO,SAAS,UAAU,GAAG,SAAS;AAClC,QAAM,IAAI,cAAc,CAAC;AACzB,QAAM,IAAI,oBAAoB,GAAG,CAAC;AAClC,QAAM,IAAI,OAAO,GAAG,OAAO;AAC3B,SAAO;AACX;;;ACtCO,SAAS,KAAK,SAAS;AAC1B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,QAAQ,MAAM,OAAO,GAAG,OAAO;AAC/D;;;ACFO,SAAS,KAAK,SAAS;AAC1B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,QAAQ,MAAM,OAAO,GAAG,OAAO;AAC/D;;;ACFO,SAAS,OAAO,SAAS;AAC5B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,UAAU,MAAM,SAAS,GAAG,OAAO;AACnE;;;ACFO,SAAS,UAAU,SAAS;AAC/B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,aAAa,MAAM,YAAY,GAAG,OAAO;AACzE;;;ACFO,SAAS,WAAW,SAAS;AAChC,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,cAAc,MAAM,aAAa,GAAG,OAAO;AAC3E;;;ACcA,SAAS,UAAU,GAAG;AAClB,SAAO,EAAE,IAAI,OAAK,UAAU,GAAG,KAAK,CAAC;AACzC;AAEA,SAAS,eAAe,OAAO;AAC3B,QAAM,MAAM,CAAC;AACb,aAAW,KAAK,WAAW,OAAO,oBAAoB,KAAK;AACvD,QAAI,CAAC,IAAI,SAAS,UAAU,MAAM,CAAC,GAAG,KAAK,CAAC;AAChD,SAAO;AACX;AACA,SAAS,oBAAoB,GAAG,MAAM;AAClC,SAAQ,SAAS,OAAO,IAAI,SAAS,CAAC;AAC1C;AAEA,SAAS,UAAU,OAAO,MAAM;AAC5B,SAAQ,gBAAgB,KAAK,IAAI,oBAAoB,IAAI,GAAG,IAAI,IAC5D,WAAW,KAAK,IAAI,oBAAoB,IAAI,GAAG,IAAI,IAC/C,QAAQ,KAAK,IAAI,SAAS,MAAM,UAAU,KAAK,CAAC,CAAC,IAC7C,aAAa,KAAK,IAAI,WAAW,IAC7B,OAAO,KAAK,IAAI,KAAK,IACjB,SAAS,KAAK,IAAI,oBAAoB,OAAO,eAAe,KAAK,CAAC,GAAG,IAAI,IACrE,WAAW,KAAK,IAAI,oBAAoB,SAAa,CAAC,GAAG,QAAQ,CAAC,GAAG,IAAI,IACrE,YAAY,KAAK,IAAI,UAAU,IAC3B,OAAO,KAAK,IAAI,KAAK,IACjB,SAAS,KAAK,IAAI,OAAO,IACrB,SAAS,KAAK,IAAI,OAAO,IACrB,SAAS,KAAK,IAAI,QAAQ,KAAK,IAC3B,UAAU,KAAK,IAAI,QAAQ,KAAK,IAC5B,SAAS,KAAK,IAAI,QAAQ,KAAK,IAC3B,OAAO,CAAC,CAAC;AACrE;AAEO,SAAS,MAAM,GAAG,SAAS;AAC9B,SAAO,WAAW,UAAU,GAAG,IAAI,GAAG,OAAO;AACjD;;;ACjDO,SAAS,sBAAsB,QAAQ,SAAS;AACnD,SAAkB,cAAc,MAAM,IAAI,MAAM,OAAO,YAAY,OAAO,IAAI,MAAM,OAAO;AAC/F;;;ACEO,SAAS,KAAK,MAAM,SAAS;AAChC,MAAI,YAAY,IAAI;AAChB,UAAM,IAAI,MAAM,yBAAyB;AAC7C,QAAM,UAAU,WAAW,OAAO,oBAAoB,IAAI,EACrD,OAAO,CAAC,QAAQ,MAAM,GAAG,CAAC,EAC1B,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC;AAC3B,QAAM,UAAU,CAAC,GAAG,IAAI,IAAI,OAAO,CAAC;AACpC,QAAM,QAAQ,QAAQ,IAAI,CAAC,UAAU,QAAQ,KAAK,CAAC;AACnD,SAAO,MAAM,OAAO,EAAE,GAAG,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC;AACtD;;;ACfO,SAAS,2BAA2B,GAAG,GAAG;AAC7C,SAAO,QAAQ,uBAAuB,CAAC,GAAG,CAAC;AAC/C;;;ACMA,SAAS,YAAY,GAAG,GAAG;AACvB,QAAM,WAAW,EAAE,OAAO,CAAC,UAAU,aAAa,OAAO,CAAC,MAAM,cAAc,KAAK;AACnF,SAAO,SAAS,WAAW,IAAI,SAAS,CAAC,IAAI,MAAM,QAAQ;AAC/D;AAEO,SAAS,QAAQ,GAAG,GAAG,UAAU,CAAC,GAAG;AAExC,MAAI,kBAAkB,CAAC;AACnB,WAAO,WAAW,2BAA2B,GAAG,CAAC,GAAG,OAAO;AAC/D,MAAI,eAAe,CAAC;AAChB,WAAO,WAAW,wBAAwB,GAAG,CAAC,GAAG,OAAO;AAE5D,SAAO,WAAW,QAAQ,CAAC,IAAI,YAAY,EAAE,OAAO,CAAC,IACjD,aAAa,GAAG,CAAC,MAAM,cAAc,QAAQ,MAAM,IAAI,GAAG,OAAO;AACzE;;;ACrBA,SAASA,gBAAe,GAAG,GAAG;AAC1B,QAAM,MAAM,CAAC;AACb,aAAW,MAAM,WAAW,OAAO,oBAAoB,CAAC;AACpD,QAAI,EAAE,IAAI,QAAQ,EAAE,EAAE,GAAG,CAAC;AAC9B,SAAO;AACX;AAEA,SAAS,iBAAiB,GAAG,GAAG;AAC5B,SAAOA,gBAAe,EAAE,YAAY,CAAC;AACzC;AAEO,SAAS,wBAAwB,GAAG,GAAG;AAC1C,QAAM,IAAI,iBAAiB,GAAG,CAAC;AAC/B,SAAO,aAAa,CAAC;AACzB;;;ACfO,SAAS,2BAA2B,GAAG,GAAG;AAC7C,SAAO,QAAQ,uBAAuB,CAAC,GAAG,CAAC;AAC/C;;;ACMA,SAAS,YAAY,GAAG,GAAG;AACvB,QAAM,YAAY,EAAE,OAAO,CAAC,UAAU,aAAa,OAAO,CAAC,MAAM,cAAc,KAAK;AACpF,SAAO,UAAU,WAAW,IAAI,UAAU,CAAC,IAAI,MAAM,SAAS;AAClE;AAEO,SAAS,QAAQ,GAAG,GAAG,SAAS;AAEnC,MAAI,kBAAkB,CAAC;AACnB,WAAO,WAAW,2BAA2B,GAAG,CAAC,GAAG,OAAO;AAC/D,MAAI,eAAe,CAAC;AAChB,WAAO,WAAW,wBAAwB,GAAG,CAAC,GAAG,OAAO;AAE5D,SAAO,WAAW,QAAQ,CAAC,IAAI,YAAY,EAAE,OAAO,CAAC,IACjD,aAAa,GAAG,CAAC,MAAM,cAAc,QAAQ,IAAI,MAAM,GAAG,OAAO;AACzE;;;ACrBA,SAASC,gBAAe,GAAG,GAAG;AAC1B,QAAM,MAAM,CAAC;AACb,aAAW,MAAM,WAAW,OAAO,oBAAoB,CAAC;AACpD,QAAI,EAAE,IAAI,QAAQ,EAAE,EAAE,GAAG,CAAC;AAC9B,SAAO;AACX;AAEA,SAASC,kBAAiB,GAAG,GAAG;AAC5B,SAAOD,gBAAe,EAAE,YAAY,CAAC;AACzC;AAEO,SAAS,wBAAwB,GAAG,GAAG;AAC1C,QAAM,IAAIC,kBAAiB,GAAG,CAAC;AAC/B,SAAO,aAAa,CAAC;AACzB;;;ACbO,SAAS,aAAa,QAAQ,SAAS;AAC1C,SAAkB,cAAc,MAAM,IAAI,WAAW,OAAO,SAAS,OAAO,IAAI,MAAM,OAAO;AACjG;;;ACHO,SAAS,iBAAiB,QAAQ;AACrC,SAAO,SAAS,SAAS,MAAM,CAAC;AACpC;;;ACiBA,SAAS,wBAAwB,SAAS,GAAG,SAAS;AAClD,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,UAAU,MAAM,UAAU,mBAAmB,EAAE,CAAC,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO;AACxG;AAKA,SAAS,qBAAqB,GAAG,GAAG,SAAS;AACzC,QAAM,SAAS,CAAC;AAChB,aAAW,MAAM;AACb,WAAO,EAAE,IAAI;AACjB,SAAO,OAAO,QAAQ,EAAE,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC;AAC1D;AAEA,SAAS,uBAAuB,GAAG,GAAG,SAAS;AAC3C,SAAQ,wBAAwB,CAAC,IAC3B,qBAAqB,kBAAkB,CAAC,GAAG,GAAG,OAAO,IACrD,wBAAwB,EAAE,SAAS,GAAG,OAAO;AACvD;AAEA,SAAS,aAAa,KAAK,MAAM,SAAS;AACtC,SAAO,qBAAqB,kBAAkB,MAAM,GAAG,CAAC,GAAG,MAAM,OAAO;AAC5E;AAEA,SAAS,eAAe,KAAK,MAAM,SAAS;AACxC,SAAO,qBAAqB,CAAC,IAAI,SAAS,CAAC,GAAG,MAAM,OAAO;AAC/D;AAEA,SAAS,cAAc,KAAK,MAAM,SAAS;AACvC,SAAO,wBAAwB,IAAI,QAAQ,MAAM,OAAO;AAC5D;AAEA,SAAS,cAAc,KAAK,MAAM,SAAS;AACvC,QAAM,UAAU,YAAY,IAAI,OAAO,IAAI,qBAAqB,IAAI;AACpE,SAAO,wBAAwB,SAAS,MAAM,OAAO;AACzD;AAEA,SAAS,WAAW,GAAG,MAAM,SAAS;AAClC,SAAO,wBAAwB,oBAAoB,MAAM,OAAO;AACpE;AAEA,SAAS,aAAa,MAAM,MAAM,SAAS;AACvC,SAAO,wBAAwB,mBAAmB,MAAM,OAAO;AACnE;AAEA,SAAS,eAAe,MAAM,MAAM,SAAS;AACzC,SAAO,OAAO,EAAE,MAAM,MAAM,OAAO,KAAK,GAAG,OAAO;AACtD;AAEA,SAAS,eAAe,MAAM,MAAM,SAAS;AACzC,SAAO,wBAAwB,oBAAoB,MAAM,OAAO;AACpE;AAEA,SAAS,cAAc,GAAG,MAAM,SAAS;AACrC,SAAO,wBAAwB,oBAAoB,MAAM,OAAO;AACpE;AAKO,SAAS,OAAO,KAAK,MAAM,UAAU,CAAC,GAAG;AAE5C,SAAQ,QAAQ,GAAG,IAAI,aAAa,IAAI,OAAO,MAAM,OAAO,IACxD,kBAAkB,GAAG,IAAI,uBAAuB,KAAK,MAAM,OAAO,IAC9D,UAAU,GAAG,IAAI,eAAe,IAAI,OAAO,MAAM,OAAO,IACpDC,WAAU,GAAG,IAAI,eAAe,KAAK,MAAM,OAAO,IAC9C,UAAU,GAAG,IAAI,eAAe,KAAK,MAAM,OAAO,IAC9CC,UAAS,GAAG,IAAI,cAAc,KAAK,MAAM,OAAO,IAC5C,SAAS,GAAG,IAAI,cAAc,KAAK,MAAM,OAAO,IAC5CC,UAAS,GAAG,IAAI,cAAc,KAAK,MAAM,OAAO,IAC5C,MAAM,GAAG,IAAI,WAAW,KAAK,MAAM,OAAO,IACtC,QAAQ,GAAG,IAAI,aAAa,KAAK,MAAM,OAAO,IAC1C,MAAM,OAAO;AACzD;AAKO,SAAS,cAAc,QAAQ;AAClC,SAAO,WAAW,OAAO,oBAAoB,OAAO,iBAAiB,EAAE,CAAC;AAC5E;AAGO,SAAS,UAAU,MAAM;AAC5B,QAAM,UAAU,cAAc,IAAI;AAClC,SAAQ,YAAY,qBAAqB,OAAO,IAC5C,YAAY,qBAAqB,OAAO,IACpC,OAAO,EAAE,QAAQ,CAAC;AAC9B;AAGO,SAAS,YAAY,MAAM;AAC9B,SAAO,KAAK,kBAAkB,cAAc,IAAI,CAAC;AACrD;;;ACzGA,SAAS,gBAAgB,MAAM,MAAM;AACjC,OAAK,aAAa,UAAU,MAAM,KAAK,UAAU;AACjD,OAAK,UAAU,SAAS,MAAM,KAAK,OAAO;AAC1C,SAAO;AACX;AAEA,SAAS,aAAa,MAAM,MAAM;AAC9B,OAAK,aAAa,UAAU,MAAM,KAAK,UAAU;AACjD,OAAK,UAAU,SAAS,MAAM,KAAK,OAAO;AAC1C,SAAO;AACX;AAEA,SAASC,eAAc,MAAM,MAAM;AAC/B,OAAK,QAAQ,UAAU,MAAM,KAAK,KAAK;AACvC,SAAO;AACX;AAEA,SAASC,WAAU,MAAM,MAAM;AAC3B,OAAK,QAAQ,UAAU,MAAM,KAAK,KAAK;AACvC,SAAO;AACX;AAEA,SAAS,UAAU,MAAM,MAAM;AAC3B,MAAe,YAAY,KAAK,KAAK;AACjC,WAAO;AACX,OAAK,QAAQ,UAAU,MAAM,KAAK,KAAK;AACvC,SAAO;AACX;AAEA,SAASC,WAAU,MAAM,MAAM;AAC3B,OAAK,QAAQ,SAAS,MAAM,KAAK,KAAK;AACtC,SAAO;AACX;AAEA,SAAS,kBAAkB,MAAM,MAAM;AACnC,OAAK,QAAQ,SAAS,MAAM,KAAK,KAAK;AACtC,SAAO;AACX;AAEA,SAAS,aAAa,MAAM,MAAM;AAC9B,OAAK,QAAQ,SAAS,MAAM,KAAK,KAAK;AACtC,SAAO;AACX;AAEA,SAASC,aAAY,MAAM,MAAM;AAC7B,OAAK,OAAO,SAAS,MAAM,KAAK,IAAI;AACpC,SAAO;AACX;AAEA,SAAS,WAAW,MAAM,MAAM;AAC5B,QAAM,mBAAmBC,gBAAe,MAAM,KAAK,UAAU;AAC7D,SAAO,EAAE,GAAG,MAAM,GAAG,OAAO,gBAAgB,EAAE;AAClD;AAEA,SAAS,WAAW,MAAM,MAAM;AAC5B,QAAM,YAAY,SAAS,MAAM,UAAU,IAAI,CAAC;AAChD,QAAM,cAAc,SAAS,MAAM,YAAY,IAAI,CAAC;AACpD,QAAM,SAAS,OAAO,WAAW,WAAW;AAC5C,SAAO,EAAE,GAAG,MAAM,GAAG,OAAO;AAChC;AAEA,SAAS,aAAa,MAAM,UAAU;AAClC,SAAO,SAAS,SAAS,OAAO,KAAK,SAAS,KAAK,IAAI,QAAQ;AACnE;AAEA,SAAS,aAAa,MAAM,MAAM;AAC9B,QAAM,aAAuB,WAAW,IAAI;AAC5C,QAAM,aAAuB,WAAW,IAAI;AAC5C,QAAM,SAAS,SAAS,MAAM,IAAI;AAClC,SAAQ,cAAc,aAAa,iBAAiB,MAAM,IACtD,cAAc,CAAC,aAAa,SAAS,MAAM,IACvC,CAAC,cAAc,aAAa,SAAS,MAAM,IACvC;AAChB;AAEA,SAASA,gBAAe,MAAM,YAAY;AACtC,SAAO,WAAW,OAAO,oBAAoB,UAAU,EAAE,OAAO,CAAC,QAAQ,QAAQ;AAC7E,WAAO,EAAE,GAAG,QAAQ,CAAC,GAAG,GAAG,aAAa,MAAM,WAAW,GAAG,CAAC,EAAE;AAAA,EACnE,GAAG,CAAC,CAAC;AACT;AAEO,SAAS,UAAU,MAAM,OAAO;AACnC,SAAO,MAAM,IAAI,UAAQ,SAAS,MAAM,IAAI,CAAC;AACjD;AAEA,SAAS,SAAS,MAAM,MAAM;AAC1B,SAAkB,cAAc,IAAI,IAAI,gBAAgB,MAAM,IAAI,IACpDC,YAAW,IAAI,IAAI,aAAa,MAAM,IAAI,IACtC,YAAY,IAAI,IAAIL,eAAc,MAAM,IAAI,IACxC,QAAQ,IAAI,IAAIC,WAAU,MAAM,IAAI,IAChC,QAAQ,IAAI,IAAI,UAAU,MAAM,IAAI,IAChCK,SAAQ,IAAI,IAAIJ,WAAU,MAAM,IAAI,IAChCK,iBAAgB,IAAI,IAAI,kBAAkB,MAAM,IAAI,IAChDC,YAAW,IAAI,IAAI,aAAa,MAAM,IAAI,IACtC,UAAU,IAAI,IAAIL,aAAY,MAAM,IAAI,IACpCM,UAAS,IAAI,IAAI,WAAW,MAAM,IAAI,IAClC,SAAS,IAAI,IAAI,WAAW,MAAM,IAAI,IAClC,WAAW,IAAI,IAAI,aAAa,MAAM,IAAI,IAChD;AACpD;AAGO,SAAS,YAAY,MAAM,MAAM;AACpC,SAAO,SAAS,MAAM,UAAU,IAAI,CAAC;AACzC;;;AC/GO,SAAS,QAAQ,SAAS;AAC7B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,WAAW,MAAM,UAAU,GAAG,OAAO;AACrE;;;ACAA,SAAS,2BAA2B,GAAG,GAAG,SAAS;AAC/C,SAAO;AAAA,IACH,CAAC,CAAC,GAAG,UAAU,QAAQ,CAAC,GAAG,GAAG,MAAM,OAAO,CAAC;AAAA,EAChD;AACJ;AAEA,SAAS,4BAA4B,GAAG,GAAG,SAAS;AAChD,QAAM,SAAS,EAAE,OAAO,CAAC,KAAK,MAAM;AAChC,WAAO,EAAE,GAAG,KAAK,GAAG,2BAA2B,GAAG,GAAG,OAAO,EAAE;AAAA,EAClE,GAAG,CAAC,CAAC;AACL,SAAO;AACX;AAEA,SAAS,0BAA0B,GAAG,GAAG,SAAS;AAC9C,SAAO,4BAA4B,EAAE,MAAM,GAAG,GAAG,OAAO;AAC5D;AAEO,SAAS,uBAAuB,GAAG,GAAG,SAAS;AAClD,QAAM,IAAI,0BAA0B,GAAG,GAAG,OAAO;AACjD,SAAO,aAAa,CAAC;AACzB;;;ACbA,SAAS,kBAAkB,OAAO;AAC9B,QAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,MAAM,GAAG,CAAC,GAAG,MAAM,MAAM,CAAC,CAAC;AACxD,SAAO,CAAC,MAAM,YAAY,GAAG,IAAI,EAAE,KAAK,EAAE;AAC9C;AACA,SAAS,gBAAgB,OAAO;AAC5B,QAAM,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,MAAM,GAAG,CAAC,GAAG,MAAM,MAAM,CAAC,CAAC;AACxD,SAAO,CAAC,MAAM,YAAY,GAAG,IAAI,EAAE,KAAK,EAAE;AAC9C;AACA,SAAS,eAAe,OAAO;AAC3B,SAAO,MAAM,YAAY;AAC7B;AACA,SAAS,eAAe,OAAO;AAC3B,SAAO,MAAM,YAAY;AAC7B;AACA,SAAS,oBAAoB,QAAQ,MAAM,SAAS;AAGhD,QAAM,aAAa,0BAA0B,OAAO,OAAO;AAC3D,QAAM,SAAS,kCAAkC,UAAU;AAC3D,MAAI,CAAC;AACD,WAAO,EAAE,GAAG,QAAQ,SAAS,iBAAiB,OAAO,SAAS,IAAI,EAAE;AACxE,QAAM,UAAU,CAAC,GAAG,kCAAkC,UAAU,CAAC;AACjE,QAAM,WAAW,QAAQ,IAAI,CAAC,UAAU,QAAQ,KAAK,CAAC;AACtD,QAAM,SAASC,UAAS,UAAU,IAAI;AACtC,QAAM,QAAQ,MAAM,MAAM;AAC1B,SAAO,gBAAgB,CAAC,KAAK,GAAG,OAAO;AAC3C;AAEA,SAAS,iBAAiB,OAAO,MAAM;AACnC,SAAQ,OAAO,UAAU,WAAY,SAAS,iBAAiB,kBAAkB,KAAK,IAClF,SAAS,eAAe,gBAAgB,KAAK,IACzC,SAAS,cAAc,eAAe,KAAK,IACvC,SAAS,cAAc,eAAe,KAAK,IACvC,QAAS,MAAM,SAAS;AAC5C;AAEA,SAASA,UAAS,GAAG,GAAG;AACpB,SAAO,EAAE,IAAI,OAAK,UAAU,GAAG,CAAC,CAAC;AACrC;AAEO,SAAS,UAAU,QAAQ,MAAM,UAAU,CAAC,GAAG;AAElD;AAAA;AAAA,IAEA,YAAY,MAAM,IAAI,uBAAuB,QAAQ,MAAM,OAAO;AAAA;AAAA,MAE9D,kBAAkB,MAAM,IAAI,oBAAoB,QAAQ,MAAM,OAAO,IACjE,QAAQ,MAAM,IAAI,MAAMA,UAAS,OAAO,OAAO,IAAI,GAAG,OAAO,IACzD,UAAU,MAAM,IAAI,QAAQ,iBAAiB,OAAO,OAAO,IAAI,GAAG,OAAO;AAAA;AAAA,QAErE,WAAW,QAAQ,OAAO;AAAA;AAAA;AAAA;AAC9C;;;AC7DO,SAAS,WAAW,GAAG,UAAU,CAAC,GAAG;AACxC,SAAO,UAAU,GAAG,cAAc,OAAO;AAC7C;;;ACFO,SAAS,UAAU,GAAG,UAAU,CAAC,GAAG;AACvC,SAAO,UAAU,GAAG,aAAa,OAAO;AAC5C;;;ACFO,SAAS,aAAa,GAAG,UAAU,CAAC,GAAG;AAC1C,SAAO,UAAU,GAAG,gBAAgB,OAAO;AAC/C;;;ACFO,SAAS,UAAU,GAAG,UAAU,CAAC,GAAG;AACvC,SAAO,UAAU,GAAG,aAAa,OAAO;AAC5C;;;ACAA,SAASC,gBAAe,YAAY,cAAc,SAAS;AACvD,QAAM,SAAS,CAAC;AAChB,aAAW,MAAM,WAAW,OAAO,oBAAoB,UAAU;AAC7D,WAAO,EAAE,IAAI,KAAK,WAAW,EAAE,GAAG,cAAc,MAAM,OAAO,CAAC;AAClE,SAAO;AACX;AAEA,SAASC,kBAAiB,cAAc,cAAc,SAAS;AAC3D,SAAOD,gBAAe,aAAa,YAAY,cAAc,OAAO;AACxE;AAEO,SAAS,qBAAqB,cAAc,cAAc,SAAS;AACtE,QAAM,aAAaC,kBAAiB,cAAc,cAAc,OAAO;AACvE,SAAO,aAAa,UAAU;AAClC;;;ACEA,SAASC,eAAc,OAAO,cAAc;AACxC,SAAO,MAAM,IAAI,CAAC,SAAS,YAAY,MAAM,YAAY,CAAC;AAC9D;AAEA,SAASC,WAAU,OAAO,cAAc;AACpC,SAAO,MAAM,IAAI,CAAC,SAAS,YAAY,MAAM,YAAY,CAAC;AAC9D;AAKA,SAASC,cAAa,YAAY,KAAK;AACnC,QAAM,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI;AAC3B,SAAO;AACX;AAEA,SAASC,gBAAe,YAAY,cAAc;AAC9C,SAAO,aAAa,OAAO,CAAC,GAAG,OAAOD,cAAa,GAAG,EAAE,GAAG,UAAU;AACzE;AAEA,SAASE,YAAW,YAAY,cAAc;AAC1C,QAAM,UAAU,QAAQ,YAAY,CAAC,eAAe,OAAO,YAAY,YAAY,CAAC;AACpF,QAAM,oBAAoBD,gBAAe,WAAW,YAAY,GAAG,YAAY;AAC/E,SAAO,OAAO,mBAAmB,OAAO;AAC5C;AAEA,SAAS,sBAAsB,cAAc;AACzC,QAAM,SAAS,aAAa,OAAO,CAACE,SAAQ,QAAQ,eAAe,GAAG,IAAI,CAAC,GAAGA,SAAQ,QAAQ,GAAG,CAAC,IAAIA,SAAQ,CAAC,CAAC;AAChH,SAAO,MAAM,MAAM;AACvB;AAEA,SAAS,YAAY,YAAY,cAAc;AAC3C,SAAQ,YAAY,UAAU,IAAI,UAAUL,eAAc,WAAW,OAAO,YAAY,CAAC,IACrF,QAAQ,UAAU,IAAI,MAAMC,WAAU,WAAW,OAAO,YAAY,CAAC,IACjEK,UAAS,UAAU,IAAIF,YAAW,YAAY,YAAY,IACtD,OAAO,CAAC,CAAC;AACzB;AAGO,SAAS,KAAK,MAAM,KAAK,SAAS;AACrC,QAAM,UAAU,QAAa,GAAG,IAAI,sBAAsB,GAAG,IAAI;AACjE,QAAM,eAAe,SAAS,GAAG,IAAI,kBAAkB,GAAG,IAAI;AAC9D,QAAM,YAAY,MAAM,IAAI;AAC5B,QAAM,WAAW,MAAM,GAAG;AAC1B,SAAQ,eAAe,IAAI,IAAI,qBAAqB,MAAM,cAAc,OAAO,IAC3E,YAAY,GAAG,IAAI,kBAAkB,MAAM,KAAK,OAAO,IAClD,aAAa,WAAY,SAAS,QAAQ,CAAC,MAAM,OAAO,GAAG,OAAO,IAC9D,CAAC,aAAa,WAAY,SAAS,QAAQ,CAAC,MAAM,OAAO,GAAG,OAAO,IAC/D,aAAa,CAAC,WAAY,SAAS,QAAQ,CAAC,MAAM,OAAO,GAAG,OAAO,IAChE,WAAW,EAAE,GAAG,YAAY,MAAM,YAAY,GAAG,GAAG,QAAQ,CAAC;AACrF;;;AClEA,SAAS,gBAAgB,MAAM,KAAK,SAAS;AACzC,SAAO,EAAE,CAAC,GAAG,GAAG,KAAK,MAAM,CAAC,GAAG,GAAG,MAAM,OAAO,CAAC,EAAE;AACtD;AAEA,SAAS,iBAAiB,MAAM,cAAc,SAAS;AACnD,SAAO,aAAa,OAAO,CAAC,KAAK,OAAO;AACpC,WAAO,EAAE,GAAG,KAAK,GAAG,gBAAgB,MAAM,IAAI,OAAO,EAAE;AAAA,EAC3D,GAAG,CAAC,CAAC;AACT;AAEA,SAAS,cAAc,MAAM,WAAW,SAAS;AAC7C,SAAO,iBAAiB,MAAM,UAAU,MAAM,OAAO;AACzD;AAEO,SAAS,kBAAkB,MAAM,WAAW,SAAS;AACxD,QAAM,aAAa,cAAc,MAAM,WAAW,OAAO;AACzD,SAAO,aAAa,UAAU;AAClC;;;ACjBA,SAASG,gBAAe,YAAY,cAAc,SAAS;AACvD,QAAM,SAAS,CAAC;AAChB,aAAW,MAAM,WAAW,OAAO,oBAAoB,UAAU;AAC7D,WAAO,EAAE,IAAI,KAAK,WAAW,EAAE,GAAG,cAAc,MAAM,OAAO,CAAC;AAClE,SAAO;AACX;AAEA,SAASC,kBAAiB,cAAc,cAAc,SAAS;AAC3D,SAAOD,gBAAe,aAAa,YAAY,cAAc,OAAO;AACxE;AAEO,SAAS,qBAAqB,cAAc,cAAc,SAAS;AACtE,QAAM,aAAaC,kBAAiB,cAAc,cAAc,OAAO;AACvE,SAAO,aAAa,UAAU;AAClC;;;ACCA,SAASC,eAAc,OAAO,cAAc;AACxC,SAAO,MAAM,IAAI,CAAC,SAAS,YAAY,MAAM,YAAY,CAAC;AAC9D;AAEA,SAASC,WAAU,OAAO,cAAc;AACpC,SAAO,MAAM,IAAI,CAAC,SAAS,YAAY,MAAM,YAAY,CAAC;AAC9D;AAEA,SAASC,gBAAe,YAAY,cAAc;AAC9C,QAAM,SAAS,CAAC;AAChB,aAAW,MAAM;AACb,QAAI,MAAM;AACN,aAAO,EAAE,IAAI,WAAW,EAAE;AAClC,SAAO;AACX;AAEA,SAASC,YAAW,GAAG,GAAG;AACtB,QAAM,UAAU,QAAQ,GAAG,CAAC,eAAe,OAAO,YAAY,YAAY,CAAC;AAC3E,QAAM,aAAaD,gBAAe,EAAE,YAAY,GAAG,CAAC;AACpD,SAAO,OAAO,YAAY,OAAO;AACrC;AAEA,SAASE,uBAAsB,cAAc;AACzC,QAAM,SAAS,aAAa,OAAO,CAACC,SAAQ,QAAQ,eAAe,GAAG,IAAI,CAAC,GAAGA,SAAQ,QAAQ,GAAG,CAAC,IAAIA,SAAQ,CAAC,CAAC;AAChH,SAAO,MAAM,MAAM;AACvB;AAEA,SAAS,YAAY,YAAY,cAAc;AAC3C,SAAQ,YAAY,UAAU,IAAI,UAAUL,eAAc,WAAW,OAAO,YAAY,CAAC,IACrF,QAAQ,UAAU,IAAI,MAAMC,WAAU,WAAW,OAAO,YAAY,CAAC,IACjEK,UAAS,UAAU,IAAIH,YAAW,YAAY,YAAY,IACtD,OAAO,CAAC,CAAC;AACzB;AAGO,SAAS,KAAK,MAAM,KAAK,SAAS;AACrC,QAAM,UAAU,QAAa,GAAG,IAAIC,uBAAsB,GAAG,IAAI;AACjE,QAAM,eAAe,SAAS,GAAG,IAAI,kBAAkB,GAAG,IAAI;AAC9D,QAAM,YAAY,MAAM,IAAI;AAC5B,QAAM,WAAW,MAAM,GAAG;AAC1B,SAAQ,eAAe,IAAI,IAAI,qBAAqB,MAAM,cAAc,OAAO,IAC3E,YAAY,GAAG,IAAI,kBAAkB,MAAM,KAAK,OAAO,IAClD,aAAa,WAAY,SAAS,QAAQ,CAAC,MAAM,OAAO,GAAG,OAAO,IAC9D,CAAC,aAAa,WAAY,SAAS,QAAQ,CAAC,MAAM,OAAO,GAAG,OAAO,IAC/D,aAAa,CAAC,WAAY,SAAS,QAAQ,CAAC,MAAM,OAAO,GAAG,OAAO,IAChE,WAAW,EAAE,GAAG,YAAY,MAAM,YAAY,GAAG,GAAG,QAAQ,CAAC;AACrF;;;AC7DA,SAASG,iBAAgB,MAAM,KAAK,SAAS;AACzC,SAAO;AAAA,IACH,CAAC,GAAG,GAAG,KAAK,MAAM,CAAC,GAAG,GAAG,MAAM,OAAO,CAAC;AAAA,EAC3C;AACJ;AAEA,SAASC,kBAAiB,MAAM,cAAc,SAAS;AACnD,SAAO,aAAa,OAAO,CAAC,QAAQ,YAAY;AAC5C,WAAO,EAAE,GAAG,QAAQ,GAAGD,iBAAgB,MAAM,SAAS,OAAO,EAAE;AAAA,EACnE,GAAG,CAAC,CAAC;AACT;AAEA,SAASE,eAAc,MAAM,WAAW,SAAS;AAC7C,SAAOD,kBAAiB,MAAM,UAAU,MAAM,OAAO;AACzD;AAEO,SAAS,kBAAkB,MAAM,WAAW,SAAS;AACxD,QAAM,aAAaC,eAAc,MAAM,WAAW,OAAO;AACzD,SAAO,aAAa,UAAU;AAClC;;;ACRA,SAASC,cAAa,QAAQ,YAAY;AACtC,SAAO,SAAS,WAAW,CAAC,SAAS,QAAQ,UAAU,CAAC,CAAC;AAC7D;AAEA,SAASC,SAAQ,MAAM;AACnB,SAAO,SAAS,WAAW,CAAC,IAAI,IAAI,CAAC,CAAC;AAC1C;AAEA,SAASC,gBAAe,YAAY;AAChC,QAAM,oBAAoB,CAAC;AAC3B,aAAW,KAAK,WAAW,OAAO,oBAAoB,UAAU;AAC5D,sBAAkB,CAAC,IAAI,SAAS,WAAW,CAAC,CAAC;AACjD,SAAO;AACX;AAEA,SAASC,YAAW,MAAM;AACtB,QAAM,UAAU,QAAQ,MAAM,CAAC,eAAe,OAAO,YAAY,YAAY,CAAC;AAC9E,QAAM,aAAaD,gBAAe,KAAK,YAAY,CAAC;AACpD,SAAO,OAAO,YAAY,OAAO;AACrC;AAEA,SAASE,UAAS,OAAO;AACrB,SAAO,MAAM,IAAI,UAAQ,eAAe,IAAI,CAAC;AACjD;AAKA,SAAS,eAAe,MAAM;AAC1B;AAAA;AAAA,IAEU,WAAW,IAAI,IAAIJ,cAAa,KAAK,QAAQ,KAAK,UAAU,IACxD,MAAM,IAAI,IAAIC,SAAQ,KAAK,IAAI,IAC3B,YAAY,IAAI,IAAI,UAAUG,UAAS,KAAK,KAAK,CAAC,IAC9C,QAAQ,IAAI,IAAI,MAAMA,UAAS,KAAK,KAAK,CAAC,IACtCC,UAAS,IAAI,IAAIF,YAAW,IAAI;AAAA;AAAA,MAE5BG,UAAS,IAAI,IAAI,OACbC,WAAU,IAAI,IAAI,OACd,UAAU,IAAI,IAAI,OACd,UAAU,IAAI,IAAI,OACdC,QAAO,IAAI,IAAI,OACXC,UAAS,IAAI,IAAI,OACbC,UAAS,IAAI,IAAI,OACbC,UAAS,IAAI,IAAI,OACbC,aAAY,IAAI,IAAI;AAAA;AAAA,QAE1B,OAAO,CAAC,CAAC;AAAA;AAAA;AAAA;AACrE;AAEO,SAAS,QAAQ,MAAM,SAAS;AACnC,MAAc,eAAe,IAAI,GAAG;AAChC,WAAO,wBAAwB,MAAM,OAAO;AAAA,EAChD,OACK;AAED,WAAO,WAAW,EAAE,GAAG,eAAe,IAAI,GAAG,GAAG,QAAQ,CAAC;AAAA,EAC7D;AACJ;;;ACrEA,SAASC,iBAAe,GAAG,SAAS;AAChC,QAAM,MAAM,CAAC;AACb,aAAW,MAAM,WAAW,OAAO,oBAAoB,CAAC;AACpD,QAAI,EAAE,IAAI,QAAQ,EAAE,EAAE,GAAG,MAAM,OAAO,CAAC;AAC3C,SAAO;AACX;AAEA,SAASC,kBAAiB,GAAG,SAAS;AAClC,SAAOD,iBAAe,EAAE,YAAY,OAAO;AAC/C;AAEO,SAAS,wBAAwB,GAAG,SAAS;AAChD,QAAM,IAAIC,kBAAiB,GAAG,OAAO;AACrC,SAAO,aAAa,CAAC;AACzB;;;ACJA,SAASC,cAAa,QAAQ,YAAY;AACtC,SAAO,SAAS,YAAY,CAAC,SAAS,QAAQ,UAAU,CAAC,CAAC;AAC9D;AAEA,SAASC,SAAQ,MAAM;AACnB,SAAO,SAAS,YAAY,CAAC,IAAI,IAAI,CAAC,CAAC;AAC3C;AAEA,SAASC,iBAAe,YAAY;AAChC,QAAM,qBAAqB,CAAC;AAC5B,aAAW,KAAK,WAAW,OAAO,oBAAoB,UAAU;AAC5D,uBAAmB,CAAC,IAAI,QAAQ,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC;AACjE,SAAO;AACX;AAEA,SAASC,YAAW,MAAM;AACtB,QAAM,UAAU,QAAQ,MAAM,CAAC,eAAe,OAAO,YAAY,YAAY,CAAC;AAC9E,QAAM,aAAaD,iBAAe,KAAK,YAAY,CAAC;AACpD,SAAO,OAAO,YAAY,OAAO;AACrC;AAEA,SAASE,UAAS,OAAO;AACrB,SAAO,MAAM,IAAI,UAAQ,gBAAgB,IAAI,CAAC;AAClD;AAKA,SAAS,gBAAgB,MAAM;AAC3B;AAAA;AAAA,IAEU,WAAW,IAAI,IAAIJ,cAAa,KAAK,QAAQ,KAAK,UAAU,IACxD,MAAM,IAAI,IAAIC,SAAQ,KAAK,IAAI,IAC3B,YAAY,IAAI,IAAI,UAAUG,UAAS,KAAK,KAAK,CAAC,IAC9C,QAAQ,IAAI,IAAI,MAAMA,UAAS,KAAK,KAAK,CAAC,IACtCC,UAAS,IAAI,IAAIF,YAAW,IAAI;AAAA;AAAA,MAE5BG,UAAS,IAAI,IAAI,OACbC,WAAU,IAAI,IAAI,OACd,UAAU,IAAI,IAAI,OACd,UAAU,IAAI,IAAI,OACdC,QAAO,IAAI,IAAI,OACXC,UAAS,IAAI,IAAI,OACbC,UAAS,IAAI,IAAI,OACbC,UAAS,IAAI,IAAI,OACbC,aAAY,IAAI,IAAI;AAAA;AAAA,QAE1B,OAAO,CAAC,CAAC;AAAA;AAAA;AAAA;AACrE;AAEO,SAAS,SAAS,MAAM,SAAS;AACpC,MAAc,eAAe,IAAI,GAAG;AAChC,WAAO,yBAAyB,MAAM,OAAO;AAAA,EACjD,OACK;AAED,WAAO,WAAW,EAAE,GAAG,gBAAgB,IAAI,GAAG,GAAG,QAAQ,CAAC;AAAA,EAC9D;AACJ;;;ACrEA,SAASC,iBAAe,GAAG,SAAS;AAChC,QAAM,MAAM,CAAC;AACb,aAAW,MAAM,WAAW,OAAO,oBAAoB,CAAC;AACpD,QAAI,EAAE,IAAI,SAAS,EAAE,EAAE,GAAG,OAAO;AACrC,SAAO;AACX;AAEA,SAASC,kBAAiB,GAAG,SAAS;AAClC,SAAOD,iBAAe,EAAE,YAAY,OAAO;AAC/C;AAEO,SAAS,yBAAyB,GAAG,SAAS;AACjD,QAAM,IAAIC,kBAAiB,GAAG,OAAO;AACrC,SAAO,aAAa,CAAC;AACzB;;;ACaA,SAAS,sBAAsB,kBAAkB,OAAO;AACpD,SAAO,MAAM,IAAI,CAAC,SAAS;AACvB,WAAiB,MAAM,IAAI,IACrB,YAAY,kBAAkB,KAAK,IAAI,IACvCC,UAAS,kBAAkB,IAAI;AAAA,EACzC,CAAC;AACL;AAEA,SAAS,YAAY,kBAAkB,KAAK;AACxC,SAAQ,OAAO,mBACC,MAAM,iBAAiB,GAAG,CAAC,IACjC,YAAY,kBAAkB,iBAAiB,GAAG,EAAE,IAAI,IACxDA,UAAS,kBAAkB,iBAAiB,GAAG,CAAC,IACpD,MAAM;AAChB;AAEA,SAAS,YAAY,YAAY;AAC7B,SAAO,QAAQ,WAAW,CAAC,CAAC;AAChC;AAEA,SAAS,UAAU,YAAY;AAC3B,SAAO,MAAM,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AAC7C;AAEA,SAAS,UAAU,YAAY;AAC3B,SAAO,MAAM,WAAW,CAAC,CAAC;AAC9B;AAEA,SAAS,YAAY,YAAY;AAC7B,SAAO,QAAQ,WAAW,CAAC,CAAC;AAChC;AAEA,SAAS,SAAS,YAAY;AAC1B,SAAO,KAAK,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AAC5C;AAEA,SAAS,SAAS,YAAY;AAC1B,SAAO,KAAK,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AAC5C;AAEA,SAAS,aAAa,YAAY;AAC9B,SAAO,SAAS,WAAW,CAAC,CAAC;AACjC;AAEA,SAASC,cAAa,kBAAkB,QAAQ,YAAY;AACxD,QAAM,eAAe,sBAAsB,kBAAkB,UAAU;AACvE,SAAQ,WAAW,YAAY,YAAY,YAAY,IACnD,WAAW,UAAU,UAAU,YAAY,IACvC,WAAW,UAAU,UAAU,YAAY,IACvC,WAAW,YAAY,YAAY,YAAY,IAC3C,WAAW,SAAS,SAAS,YAAY,IACrC,WAAW,SAAS,SAAS,YAAY,IACrC,WAAW,aAAa,aAAa,YAAY,IAC7C,MAAM;AACtC;AACA,SAASC,WAAU,kBAAkB,MAAM;AACvC,SAAO,MAAMF,UAAS,kBAAkB,IAAI,CAAC;AACjD;AACA,SAASG,mBAAkB,kBAAkB,MAAM;AAC/C,SAAO,cAAcH,UAAS,kBAAkB,IAAI,CAAC;AACzD;AAEA,SAASI,iBAAgB,kBAAkB,YAAY,cAAc;AACjE,SAAO,YAAYC,WAAU,kBAAkB,UAAU,GAAGL,UAAS,kBAAkB,YAAY,CAAC;AACxG;AAEA,SAASM,cAAa,kBAAkB,YAAY,YAAY;AAC5D,SAAO,SAAaD,WAAU,kBAAkB,UAAU,GAAGL,UAAS,kBAAkB,UAAU,CAAC;AACvG;AACA,SAASO,eAAc,kBAAkB,OAAO;AAC5C,SAAO,UAAUF,WAAU,kBAAkB,KAAK,CAAC;AACvD;AACA,SAASG,cAAa,kBAAkB,MAAM;AAC1C,SAAO,SAASR,UAAS,kBAAkB,IAAI,CAAC;AACpD;AACA,SAASS,YAAW,kBAAkB,YAAY;AAC9C,SAAO,OAAO,WAAW,OAAO,KAAK,UAAU,EAAE,OAAO,CAAC,QAAQ,QAAQ;AACrE,WAAO,EAAE,GAAG,QAAQ,CAAC,GAAG,GAAGT,UAAS,kBAAkB,WAAW,GAAG,CAAC,EAAE;AAAA,EAC3E,GAAG,CAAC,CAAC,CAAC;AACV;AAEA,SAASU,YAAW,kBAAkB,MAAM;AACxC,QAAM,CAAC,OAAO,OAAO,IAAI,CAACV,UAAS,kBAAkB,YAAY,IAAI,CAAC,GAAG,cAAc,IAAI,CAAC;AAC5F,QAAM,SAAS,UAAU,IAAI;AAC7B,SAAO,kBAAkB,OAAO,IAAI;AACpC,SAAO;AACX;AAEA,SAAS,cAAc,kBAAkB,WAAW;AAChD,SAAkB,MAAM,SAAS,IAC3B,EAAE,GAAG,YAAY,kBAAkB,UAAU,IAAI,GAAG,CAAC,aAAa,GAAG,UAAU,aAAa,EAAE,IAC9F;AACV;AACA,SAASW,WAAU,kBAAkB,OAAO;AACxC,SAAO,MAAMN,WAAU,kBAAkB,KAAK,CAAC;AACnD;AACA,SAASO,WAAU,kBAAkB,OAAO;AACxC,SAAO,MAAMP,WAAU,kBAAkB,KAAK,CAAC;AACnD;AACA,SAASA,WAAU,kBAAkB,OAAO;AACxC,SAAO,MAAM,IAAI,CAAC,SAASL,UAAS,kBAAkB,IAAI,CAAC;AAC/D;AAEO,SAASA,UAAS,kBAAkB,MAAM;AAC7C;AAAA;AAAA,IAEU,WAAW,IAAI,IAAI,WAAWA,UAAS,kBAAkB,QAAQ,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,IACzF,WAAW,IAAI,IAAI,WAAWA,UAAS,kBAAkB,QAAQ,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI;AAAA;AAAA,MAEzF,YAAY,IAAI,IAAI,WAAW,cAAc,kBAAkB,IAAI,GAAG,IAAI;AAAA;AAAA,QAEtEa,SAAQ,IAAI,IAAI,WAAWX,WAAU,kBAAkB,KAAK,KAAK,GAAG,IAAI,IACpEY,iBAAgB,IAAI,IAAI,WAAWX,mBAAkB,kBAAkB,KAAK,KAAK,GAAG,IAAI,IACpF,WAAW,IAAI,IAAI,WAAWF,cAAa,kBAAkB,KAAK,QAAQ,KAAK,UAAU,CAAC,IACtF,cAAc,IAAI,IAAI,WAAWG,iBAAgB,kBAAkB,KAAK,YAAY,KAAK,OAAO,GAAG,IAAI,IACnGW,YAAW,IAAI,IAAI,WAAWT,cAAa,kBAAkB,KAAK,YAAY,KAAK,OAAO,GAAG,IAAI,IAC7F,YAAY,IAAI,IAAI,WAAWC,eAAc,kBAAkB,KAAK,KAAK,GAAG,IAAI,IAC5ES,YAAW,IAAI,IAAI,WAAWR,cAAa,kBAAkB,KAAK,KAAK,GAAG,IAAI,IAC1ES,UAAS,IAAI,IAAI,WAAWR,YAAW,kBAAkB,KAAK,UAAU,GAAG,IAAI,IAC3E,SAAS,IAAI,IAAI,WAAWC,YAAW,kBAAkB,IAAI,CAAC,IAC1D,QAAQ,IAAI,IAAI,WAAWC,WAAU,kBAAkB,KAAK,SAAS,CAAC,CAAC,GAAG,IAAI,IAC1E,QAAQ,IAAI,IAAI,WAAWC,WAAU,kBAAkB,KAAK,KAAK,GAAG,IAAI,IAC9E;AAAA;AAAA;AAAA;AAC5D;AAEO,SAAS,YAAY,kBAAkB,KAAK;AAC/C,SAAQ,OAAO,mBACTZ,UAAS,kBAAkB,iBAAiB,GAAG,CAAC,IAChD,MAAM;AAChB;AAEO,SAAS,wBAAwB,kBAAkB;AACtD,SAAO,WAAW,OAAO,oBAAoB,gBAAgB,EAAE,OAAO,CAAC,QAAQ,QAAQ;AACnF,WAAO,EAAE,GAAG,QAAQ,CAAC,GAAG,GAAG,YAAY,kBAAkB,GAAG,EAAE;AAAA,EAClE,GAAG,CAAC,CAAC;AACT;;;AC3JO,IAAM,UAAN,MAAc;AAAA,EACjB,YAAY,OAAO;AACf,UAAM,WAAW,wBAAwB,KAAK;AAC9C,UAAM,aAAa,KAAK,gBAAgB,QAAQ;AAChD,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA,EAEA,OAAO,KAAK,SAAS;AACjB,UAAM,QAAQ,EAAE,GAAG,KAAK,OAAO,CAAC,GAAG,GAAG,WAAW,KAAK,MAAM,GAAG,GAAG,OAAO,EAAE;AAC3E,WAAO,WAAW,EAAE,CAAC,IAAI,GAAG,UAAU,OAAO,MAAM,IAAI,CAAC;AAAA,EAC5D;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACnB,WAAO,WAAW,OAAO,oBAAoB,KAAK,EAAE,OAAO,CAAC,QAAQ,QAAQ;AACxE,aAAO,EAAE,GAAG,QAAQ,CAAC,GAAG,GAAG,EAAE,GAAG,MAAM,GAAG,GAAG,KAAK,IAAI,EAAE;AAAA,IAC3D,GAAG,CAAC,CAAC;AAAA,EACT;AACJ;AAEO,SAAS,OAAO,YAAY;AAC/B,SAAO,IAAI,QAAQ,UAAU;AACjC;;;AC5BO,SAAS,IAAI,MAAM,SAAS;AAC/B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,OAAO,KAAK,KAAK,GAAG,OAAO;AAC3D;;;ACDO,SAAS,WAAW,QAAQ,SAAS;AACxC,SAAkBkB,YAAW,MAAM,IAAI,MAAM,OAAO,YAAY,OAAO,IAAI,MAAM;AACrF;;;ACDA,IAAI,UAAU;AAEP,SAAS,UAAU,UAAU,UAAU,CAAC,GAAG;AAC9C,MAAI,YAAY,QAAQ,GAAG;AACvB,YAAQ,MAAM,IAAI,SAAS;AAC/B,QAAM,WAAW,UAAU,SAAS,EAAE,CAAC,IAAI,GAAG,QAAQ,MAAM,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC;AAC/E,WAAS,MAAM,QAAQ;AAEvB,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,aAAa,GAAG,SAAS,GAAG,OAAO;AACnE;;;ACVO,SAAS,OAAO,YAAY,SAAS;AACxC,QAAM,OAAO,SAAS,UAAU,IAAI,IAAI,WAAW,OAAO,UAAU,IAAI;AACxE,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,UAAU,MAAM,UAAU,QAAQ,KAAK,QAAQ,OAAO,KAAK,MAAM,GAAG,OAAO;AAC3G;;;ACFA,SAAS,YAAY,GAAG;AACpB,SAAQ,YAAY,CAAC,IAAI,EAAE,QACvB,QAAQ,CAAC,IAAI,EAAE,QACX,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,IACrB,CAAC;AACjB;AAEO,SAAS,KAAK,GAAG;AACpB,SAAO,YAAY,CAAC;AACxB;;;ACVO,SAAS,WAAW,QAAQ,SAAS;AACxC,SAAkBC,YAAW,MAAM,IAAI,WAAW,OAAO,SAAS,OAAO,IAAI,MAAM,OAAO;AAC9F;;;ACEO,IAAM,yBAAN,MAA6B;AAAA,EAChC,YAAY,QAAQ;AAChB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,OAAO,QAAQ;AACX,WAAO,IAAI,uBAAuB,KAAK,QAAQ,MAAM;AAAA,EACzD;AACJ;AAEO,IAAM,yBAAN,MAA6B;AAAA,EAChC,YAAY,QAAQ,QAAQ;AACxB,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,gBAAgB,QAAQ,QAAQ;AAC5B,UAAM,SAAS,CAAC,UAAU,OAAO,aAAa,EAAE,OAAO,OAAO,KAAK,CAAC;AACpE,UAAM,SAAS,CAAC,UAAU,KAAK,OAAO,OAAO,aAAa,EAAE,OAAO,KAAK,CAAC;AACzE,UAAM,QAAQ,EAAE,QAAgB,OAAe;AAC/C,WAAO,EAAE,GAAG,QAAQ,CAAC,aAAa,GAAG,MAAM;AAAA,EAC/C;AAAA,EACA,aAAa,QAAQ,QAAQ;AACzB,UAAM,QAAQ,EAAE,QAAQ,KAAK,QAAQ,QAAQ,OAAO;AACpD,WAAO,EAAE,GAAG,QAAQ,CAAC,aAAa,GAAG,MAAM;AAAA,EAC/C;AAAA,EACA,OAAO,QAAQ;AACX,WAAQ,YAAY,KAAK,MAAM,IAAI,KAAK,gBAAgB,QAAQ,KAAK,MAAM,IAAI,KAAK,aAAa,QAAQ,KAAK,MAAM;AAAA,EACxH;AACJ;AAEO,SAAS,UAAU,QAAQ;AAC9B,SAAO,IAAI,uBAAuB,MAAM;AAC5C;;;ACpCO,SAAS,KAAK,SAAS;AAC1B,SAAO,WAAW,EAAE,CAAC,IAAI,GAAG,QAAQ,MAAM,OAAO,GAAG,OAAO;AAC/D;;;ACoCO,IAAM,kBAAN,MAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,iBAAiB,MAAM;AACnB,WAAO,iBAAiB,IAAI;AAAA,EAChC;AAAA;AAAA,EAEA,SAAS,MAAM,QAAQ;AACnB,WAAO,SAAS,MAAM,UAAU,IAAI;AAAA,EACxC;AAAA;AAAA,EAEA,SAAS,MAAM,QAAQ;AACnB,WAAO,SAAS,MAAM,UAAU,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACT,WAAO,IAAI,OAAO;AAAA,EACtB;AAAA;AAAA,EAEA,MAAM,OAAO,SAAS;AAClB,WAAO,MAAM,OAAO,OAAO;AAAA,EAC/B;AAAA;AAAA,EAEA,QAAQ,SAAS;AACb,WAAO,QAAQ,OAAO;AAAA,EAC1B;AAAA;AAAA,EAEA,WAAW,QAAQ,SAAS;AACxB,WAAO,WAAW,QAAQ,OAAO;AAAA,EACrC;AAAA;AAAA,EAEA,UAAU,SAAS,SAAS;AACxB,WAAO,UAAU,SAAS,OAAO;AAAA,EACrC;AAAA;AAAA,EAEA,MAAM,OAAO,SAAS;AAClB,WAAO,MAAM,OAAO,OAAO;AAAA,EAC/B;AAAA;AAAA,EAEA,KAAK,MAAM,SAAS;AAChB,WAAO,KAAK,MAAM,OAAO;AAAA,EAC7B;AAAA;AAAA,EAEA,QAAQ,WAAW,iBAAiB,SAAS;AACzC,WAAO,QAAQ,WAAW,iBAAiB,OAAO;AAAA,EACtD;AAAA;AAAA,EAEA,QAAQ,GAAG,GAAG,GAAG,GAAG,SAAS;AACzB,WAAO,QAAQ,GAAG,GAAG,GAAG,GAAG,OAAO;AAAA,EACtC;AAAA;AAAA,EAEA,QAAQ,MAAM,OAAO,SAAS;AAC1B,WAAO,QAAQ,MAAM,OAAO,OAAO;AAAA,EACvC;AAAA;AAAA,EAEA,MAAM,MAAM,KAAK,SAAS;AACtB,WAAO,MAAM,MAAM,KAAK,OAAO;AAAA,EACnC;AAAA;AAAA,EAEA,QAAQ,SAAS;AACb,WAAO,QAAQ,OAAO;AAAA,EAC1B;AAAA;AAAA,EAEA,UAAU,OAAO,SAAS;AACtB,WAAO,UAAU,OAAO,OAAO;AAAA,EACnC;AAAA;AAAA,EAEA,MAAM,MAAM,SAAS;AACjB,WAAO,MAAM,MAAM,OAAO;AAAA,EAC9B;AAAA;AAAA,EAEA,QAAQ,cAAc,SAAS;AAC3B,WAAO,QAAQ,cAAc,OAAO;AAAA,EACxC;AAAA;AAAA,EAEA,UAAU,MAAM,SAAS;AACrB,WAAO,UAAU,MAAM,OAAO;AAAA,EAClC;AAAA;AAAA,EAEA,OAAO,KAAK,KAAK,SAAS;AACtB,WAAO,OAAO,KAAK,KAAK,OAAO;AAAA,EACnC;AAAA;AAAA,EAEA,OAAO,YAAY;AACf,WAAO,OAAO,UAAU;AAAA,EAC5B;AAAA;AAAA,EAEA,MAAM,SAAS;AACX,WAAO,MAAM,OAAO;AAAA,EACxB;AAAA;AAAA,EAEA,IAAI,MAAM,SAAS;AACf,WAAO,IAAI,MAAM,OAAO;AAAA,EAC5B;AAAA;AAAA,EAEA,KAAK,SAAS;AACV,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA;AAAA,EAEA,OAAO,SAAS;AACZ,WAAO,OAAO,OAAO;AAAA,EACzB;AAAA;AAAA,EAEA,OAAO,YAAY,SAAS;AACxB,WAAO,OAAO,YAAY,OAAO;AAAA,EACrC;AAAA;AAAA,EAEA,KAAK,QAAQ,UAAU,SAAS;AAC5B,WAAO,KAAK,QAAQ,UAAU,OAAO;AAAA,EACzC;AAAA;AAAA,EAEA,QAAQ,MAAM,SAAS;AACnB,WAAO,QAAQ,MAAM,OAAO;AAAA,EAChC;AAAA;AAAA,EAEA,KAAK,MAAM,KAAK,SAAS;AACrB,WAAO,KAAK,MAAM,KAAK,OAAO;AAAA,EAClC;AAAA;AAAA,EAEA,OAAO,KAAK,OAAO,SAAS;AACxB,WAAO,OAAO,KAAK,OAAO,OAAO;AAAA,EACrC;AAAA;AAAA,EAEA,UAAU,UAAU,SAAS;AACzB,WAAO,UAAU,UAAU,OAAO;AAAA,EACtC;AAAA;AAAA,EAEA,OAAO,MAAM;AACT,WAAO,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,EAC/B;AAAA;AAAA,EAEA,SAAS,MAAM,SAAS;AACpB,WAAO,SAAS,MAAM,OAAO;AAAA,EACjC;AAAA;AAAA,EAEA,KAAK,MAAM;AACP,WAAO,KAAK,IAAI;AAAA,EACpB;AAAA;AAAA,EAEA,OAAO,SAAS;AACZ,WAAO,OAAO,OAAO;AAAA,EACzB;AAAA;AAAA,EAEA,gBAAgB,YAAY,SAAS;AACjC,WAAO,gBAAgB,YAAY,OAAO;AAAA,EAC9C;AAAA;AAAA,EAEA,UAAU,MAAM;AACZ,WAAO,UAAU,IAAI;AAAA,EACzB;AAAA;AAAA,EAEA,MAAM,OAAO,SAAS;AAClB,WAAO,MAAM,OAAO,OAAO;AAAA,EAC/B;AAAA;AAAA,EAEA,aAAa,MAAM,SAAS;AACxB,WAAO,aAAa,MAAM,OAAO;AAAA,EACrC;AAAA;AAAA,EAEA,MAAM,OAAO,SAAS;AAClB,WAAO,MAAM,OAAO,OAAO;AAAA,EAC/B;AAAA;AAAA,EAEA,QAAQ,SAAS;AACb,WAAO,QAAQ,OAAO;AAAA,EAC1B;AAAA;AAAA,EAEA,OAAO,SAAS;AACZ,WAAO,OAAO,OAAO;AAAA,EACzB;AAAA;AAAA,EAEA,UAAU,QAAQ,SAAS;AACvB,WAAO,UAAU,QAAQ,OAAO;AAAA,EACpC;AACJ;;;AC5NA,IAAAC,gBAAA;AAAA,SAAAA,eAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACqBO,IAAM,wBAAN,cAAoC,gBAAgB;AAAA;AAAA,EAEvD,SAAS,OAAO;AACZ,WAAO,SAAS,KAAK;AAAA,EACzB;AAAA;AAAA,EAEA,cAAc,OAAO,SAAS;AAC1B,WAAO,cAAc,OAAO,OAAO;AAAA,EACvC;AAAA;AAAA,EAEA,QAAQ,QAAQ,SAAS;AACrB,WAAO,QAAQ,QAAQ,OAAO;AAAA,EAClC;AAAA;AAAA,EAEA,OAAO,SAAS;AACZ,WAAO,OAAO,OAAO;AAAA,EACzB;AAAA;AAAA,EAEA,sBAAsB,QAAQ,SAAS;AACnC,WAAO,sBAAsB,QAAQ,OAAO;AAAA,EAChD;AAAA;AAAA,EAEA,YAAY,YAAY,cAAc,SAAS;AAC3C,WAAO,YAAY,YAAY,cAAc,OAAO;AAAA,EACxD;AAAA;AAAA,EAEA,KAAK,UAAU,CAAC,GAAG;AACf,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA;AAAA,EAEA,SAAS,YAAY,YAAY,SAAS;AACtC,WAAO,SAAa,YAAY,YAAY,OAAO;AAAA,EACvD;AAAA;AAAA,EAEA,aAAa,QAAQ,SAAS;AAC1B,WAAO,aAAa,QAAQ,OAAO;AAAA,EACvC;AAAA;AAAA,EAEA,YAAY,QAAQ,YAAY;AAC5B,WAAO,YAAY,QAAQ,UAAU;AAAA,EACzC;AAAA;AAAA,EAEA,SAAS,OAAO,SAAS;AACrB,WAAO,SAAS,OAAO,OAAO;AAAA,EAClC;AAAA;AAAA,EAEA,WAAW,QAAQ,SAAS;AACxB,WAAO,WAAW,QAAQ,OAAO;AAAA,EACrC;AAAA;AAAA,EAEA,QAAQ,MAAM,SAAS;AACnB,WAAOC,SAAQ,MAAM,OAAO;AAAA,EAChC;AAAA;AAAA,EAEA,OAAO,YAAY,SAAS;AACxB,WAAO,OAAO,YAAY,OAAO;AAAA,EACrC;AAAA;AAAA,EAEA,WAAW,MAAM,SAAS;AACtB,WAAO,WAAW,MAAM,OAAO;AAAA,EACnC;AAAA;AAAA,EAEA,OAAO,SAAS;AACZ,WAAO,OAAO,OAAO;AAAA,EACzB;AAAA;AAAA,EAEA,UAAU,SAAS;AACf,WAAO,UAAU,OAAO;AAAA,EAC5B;AAAA;AAAA,EAEA,WAAW,SAAS;AAChB,WAAO,WAAW,OAAO;AAAA,EAC7B;AAAA;AAAA,EAEA,KAAK,SAAS;AACV,WAAO,KAAK,OAAO;AAAA,EACvB;AACJ;;;ACxFA,IAAM,OAAOC;", "names": ["FromProperties", "FromProperties", "FromMappedResult", "IsBoolean", "IsNumber", "IsString", "FromIntersect", "FromUnion", "FromArray", "FromPromise", "FromProperties", "IsFunction", "IsArray", "IsAsyncIterator", "IsIterator", "IsObject", "FromRest", "FromProperties", "FromMappedResult", "FromIntersect", "FromUnion", "FromProperty", "FromProperties", "FromObject", "result", "IsObject", "FromProperties", "FromMappedResult", "FromIntersect", "FromUnion", "FromProperties", "FromObject", "UnionFromPropertyKeys", "result", "IsObject", "FromPropertyKey", "FromPropertyKeys", "FromMappedKey", "FromComputed", "FromRef", "FromProperties", "FromObject", "FromRest", "IsObject", "IsBigInt", "IsBoolean", "IsNull", "IsNumber", "IsString", "IsSymbol", "IsUndefined", "FromProperties", "FromMappedResult", "FromComputed", "FromRef", "FromProperties", "FromObject", "FromRest", "IsObject", "IsBigInt", "IsBoolean", "IsNull", "IsNumber", "IsString", "IsSymbol", "IsUndefined", "FromProperties", "FromMappedResult", "FromType", "FromComputed", "FromArray", "FromAsyncIterator", "FromConstructor", "FromTypes", "FromFunction", "FromIntersect", "FromIterator", "FromObject", "FromRecord", "FromTuple", "FromUnion", "IsArray", "IsAsyncIterator", "IsFunction", "IsIterator", "IsObject", "IsFunction", "IsFunction", "type_exports", "Promise", "Promise", "type_exports"]}