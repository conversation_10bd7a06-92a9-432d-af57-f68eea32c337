<script lang="ts">
  import * as Chart from '$lib/components/ui/chart';
  import { BarChart } from 'layerchart';

  // Props
  let {
    historyData = [],
    metric = 'successRate',
    title = 'Last 30 Days',
    height = 100,
  }: {
    historyData?: any[];
    metric?: string;
    title?: string;
    height?: number;
  } = $props();

  // Generate mock data for 30 days if not provided
  const chartData = $derived(() => {
    if (!historyData || historyData.length === 0) {
      const mockData = [];
      const today = new Date();

      for (let i = 29; i >= 0; i--) {
        const date = new Date();
        date.setDate(today.getDate() - i);

        // Generate random status with higher probability of operational
        const rand = Math.random();
        let status = 'operational';
        if (rand > 0.9) status = 'outage';
        else if (rand > 0.8) status = 'degraded';
        else if (rand > 0.7) status = 'maintenance';

        mockData.push({
          date: formatDate(date.toISOString()),
          status,
          value:
            status === 'operational'
              ? 100
              : status === 'degraded'
                ? 80
                : status === 'maintenance'
                  ? 60
                  : 0,
          statusColor: getStatusColor(status),
        });
      }

      return mockData;
    }

    return historyData.map((item) => ({
      date: formatDate(item.date),
      status: item.status,
      value: item[metric] || 0,
      statusColor: getStatusColor(item.status),
    }));
  });

  // Format date for display
  function formatDate(dateStr: string): string {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }

  // Get status color
  function getStatusColor(status: string): string {
    switch (status) {
      case 'operational':
        return 'var(--chart-3)'; // Green
      case 'degraded':
        return 'var(--chart-4)'; // Yellow
      case 'outage':
        return 'var(--chart-5)'; // Red
      case 'maintenance':
        return 'var(--chart-2)'; // Blue
      default:
        return 'hsl(var(--muted-foreground))'; // Gray
    }
  }

  // Chart configuration
  const chartConfig = {
    operational: {
      label: 'Operational',
      color: 'var(--chart-3)',
    },
    degraded: {
      label: 'Degraded',
      color: 'var(--chart-4)',
    },
    outage: {
      label: 'Outage',
      color: 'var(--chart-5)',
    },
    maintenance: {
      label: 'Maintenance',
      color: 'var(--chart-2)',
    },
  } satisfies Chart.ChartConfig;
</script>

<div class="w-full">
  <h4 class="mb-2 text-sm font-medium">{title}</h4>

  {#if chartData.length === 0}
    <div class="flex items-center justify-center" style="height: {height}px;">
      <div class="text-muted-foreground text-sm">No data available</div>
    </div>
  {:else}
    <Chart.Container config={chartConfig} class="w-full" style="height: {height}px;">
      <BarChart
        data={chartData()}
        x="date"
        axis="x"
        series={[
          {
            key: 'value',
            label: 'Status',
            color: 'var(--chart-1)',
          },
        ]}
        props={{
          xAxis: {
            format: (d: string) => d.slice(0, 3),
          },
        }}>
        {#snippet tooltip()}
          <Chart.Tooltip />
        {/snippet}
      </BarChart>
    </Chart.Container>

    {#if historyData && historyData.length > 0}
      <div class="text-muted-foreground mt-1 flex justify-between text-xs">
        <span>{formatDate(historyData[0].date)}</span>
        <span>{formatDate(historyData[historyData.length - 1].date)}</span>
      </div>
    {/if}
  {/if}
</div>
