{"version": 3, "sources": ["../../@gcornut/valibot-json-schema/node_modules/valibot/dist/index.js", "../../@gcornut/valibot-json-schema/dist/index.mjs"], "sourcesContent": ["// src/regex.ts\nvar BIC_REGEX = /^[A-Z]{6}(?!00)[A-Z\\d]{2}(?:[A-Z\\d]{3})?$/u;\nvar CUID2_REGEX = /^[a-z][\\da-z]*$/u;\nvar DECIMAL_REGEX = /^\\d+$/u;\nvar EMAIL_REGEX = /^[\\w+-]+(?:\\.[\\w+-]+)*@[\\da-z]+(?:[.-][\\da-z]+)*\\.[a-z]{2,}$/iu;\nvar EMOJI_REGEX = /^[\\p{Extended_Pictographic}\\p{Emoji_Component}]+$/u;\nvar HEXADECIMAL_REGEX = /^(?:0h|0x)?[\\da-f]+$/iu;\nvar HEX_COLOR_REGEX = /^#(?:[\\da-f]{3,4}|[\\da-f]{6}|[\\da-f]{8})$/iu;\nvar IMEI_REGEX = /^\\d{15}$|^\\d{2}-\\d{6}-\\d{6}-\\d$/u;\nvar IPV4_REGEX = (\n  // eslint-disable-next-line redos-detector/no-unsafe-regex -- false positive\n  /^(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])(?:\\.(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])){3}$/u\n);\nvar IPV6_REGEX = /^(?:(?:[\\da-f]{1,4}:){7}[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,7}:|(?:[\\da-f]{1,4}:){1,6}:[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,5}(?::[\\da-f]{1,4}){1,2}|(?:[\\da-f]{1,4}:){1,4}(?::[\\da-f]{1,4}){1,3}|(?:[\\da-f]{1,4}:){1,3}(?::[\\da-f]{1,4}){1,4}|(?:[\\da-f]{1,4}:){1,2}(?::[\\da-f]{1,4}){1,5}|[\\da-f]{1,4}:(?::[\\da-f]{1,4}){1,6}|:(?:(?::[\\da-f]{1,4}){1,7}|:)|fe80:(?::[\\da-f]{0,4}){0,4}%[\\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)|(?:[\\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d))$/iu;\nvar IP_REGEX = /^(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])(?:\\.(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])){3}$|^(?:(?:[\\da-f]{1,4}:){7}[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,7}:|(?:[\\da-f]{1,4}:){1,6}:[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,5}(?::[\\da-f]{1,4}){1,2}|(?:[\\da-f]{1,4}:){1,4}(?::[\\da-f]{1,4}){1,3}|(?:[\\da-f]{1,4}:){1,3}(?::[\\da-f]{1,4}){1,4}|(?:[\\da-f]{1,4}:){1,2}(?::[\\da-f]{1,4}){1,5}|[\\da-f]{1,4}:(?::[\\da-f]{1,4}){1,6}|:(?:(?::[\\da-f]{1,4}){1,7}|:)|fe80:(?::[\\da-f]{0,4}){0,4}%[\\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)|(?:[\\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d))$/iu;\nvar ISO_DATE_REGEX = /^\\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\\d|0[1-9]|3[01])$/u;\nvar ISO_DATE_TIME_REGEX = /^\\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\\d|0[1-9]|3[01])T(?:0\\d|1\\d|2[0-3]):[0-5]\\d$/u;\nvar ISO_TIME_REGEX = /^(?:0\\d|1\\d|2[0-3]):[0-5]\\d$/u;\nvar ISO_TIME_SECOND_REGEX = /^(?:0\\d|1\\d|2[0-3])(?::[0-5]\\d){2}$/u;\nvar ISO_TIMESTAMP_REGEX = /^\\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\\d|0[1-9]|3[01])T(?:0\\d|1\\d|2[0-3])(?::[0-5]\\d){2}(?:\\.\\d{1,9})?(?:Z|[+-](?:0\\d|1\\d|2[0-3])(?::?[0-5]\\d)?)$/u;\nvar ISO_WEEK_REGEX = /^\\d{4}-W(?:0[1-9]|[1-4]\\d|5[0-3])$/u;\nvar MAC48_REGEX = /^(?:[\\da-f]{2}:){5}[\\da-f]{2}$|^(?:[\\da-f]{2}-){5}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){2}[\\da-f]{4}$/iu;\nvar MAC64_REGEX = /^(?:[\\da-f]{2}:){7}[\\da-f]{2}$|^(?:[\\da-f]{2}-){7}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){3}[\\da-f]{4}$|^(?:[\\da-f]{4}:){3}[\\da-f]{4}$/iu;\nvar MAC_REGEX = /^(?:[\\da-f]{2}:){5}[\\da-f]{2}$|^(?:[\\da-f]{2}-){5}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){2}[\\da-f]{4}$|^(?:[\\da-f]{2}:){7}[\\da-f]{2}$|^(?:[\\da-f]{2}-){7}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){3}[\\da-f]{4}$|^(?:[\\da-f]{4}:){3}[\\da-f]{4}$/iu;\nvar OCTAL_REGEX = /^(?:0o)?[0-7]+$/iu;\nvar ULID_REGEX = /^[\\da-hjkmnp-tv-z]{26}$/iu;\nvar UUID_REGEX = /^[\\da-f]{8}(?:-[\\da-f]{4}){3}-[\\da-f]{12}$/iu;\n\n// src/storages/globalConfig/globalConfig.ts\nvar store;\nfunction setGlobalConfig(config2) {\n  store = { ...store, ...config2 };\n}\nfunction getGlobalConfig(config2) {\n  return {\n    lang: config2?.lang ?? store?.lang,\n    message: config2?.message,\n    abortEarly: config2?.abortEarly ?? store?.abortEarly,\n    abortPipeEarly: config2?.abortPipeEarly ?? store?.abortPipeEarly,\n    skipPipe: config2?.skipPipe\n  };\n}\nfunction deleteGlobalConfig() {\n  store = void 0;\n}\n\n// src/storages/globalMessage/globalMessage.ts\nvar store2;\nfunction setGlobalMessage(message, lang) {\n  if (!store2)\n    store2 = /* @__PURE__ */ new Map();\n  store2.set(lang, message);\n}\nfunction getGlobalMessage(lang) {\n  return store2?.get(lang);\n}\nfunction deleteGlobalMessage(lang) {\n  store2?.delete(lang);\n}\n\n// src/storages/schemaMessage/schemaMessage.ts\nvar store3;\nfunction setSchemaMessage(message, lang) {\n  if (!store3)\n    store3 = /* @__PURE__ */ new Map();\n  store3.set(lang, message);\n}\nfunction getSchemaMessage(lang) {\n  return store3?.get(lang);\n}\nfunction deleteSchemaMessage(lang) {\n  store3?.delete(lang);\n}\n\n// src/storages/specificMessage/specificMessage.ts\nvar store4;\nfunction setSpecificMessage(reference, message, lang) {\n  if (!store4)\n    store4 = /* @__PURE__ */ new Map();\n  if (!store4.get(reference))\n    store4.set(reference, /* @__PURE__ */ new Map());\n  store4.get(reference).set(lang, message);\n}\nfunction getSpecificMessage(reference, lang) {\n  return store4?.get(reference)?.get(lang);\n}\nfunction deleteSpecificMessage(reference, lang) {\n  store4?.get(reference)?.delete(lang);\n}\n\n// src/utils/_stringify/_stringify.ts\nfunction _stringify(input) {\n  let type = typeof input;\n  if (type === \"object\") {\n    type = (input && Object.getPrototypeOf(input)?.constructor?.name) ?? \"null\";\n  }\n  return type === \"string\" ? `\"${input}\"` : type === \"number\" || type === \"bigint\" || type === \"boolean\" ? `${input}` : type;\n}\n\n// src/utils/_addIssue/_addIssue.ts\nfunction _addIssue(context, label, dataset, config2, other) {\n  const input = other && \"input\" in other ? other.input : dataset.value;\n  const expected = other?.expected ?? context.expects;\n  const received = other?.received ?? _stringify(input);\n  const issue = {\n    kind: context.kind,\n    type: context.type,\n    input,\n    expected,\n    received,\n    message: `Invalid ${label}: ${expected ? `Expected ${expected} but r` : \"R\"}eceived ${received}`,\n    // @ts-expect-error\n    requirement: context.requirement,\n    path: other?.path,\n    issues: other?.issues,\n    lang: config2.lang,\n    abortEarly: config2.abortEarly,\n    abortPipeEarly: config2.abortPipeEarly,\n    skipPipe: config2.skipPipe\n  };\n  const isSchema = context.kind === \"schema\";\n  const message = (\n    // @ts-expect-error\n    context.message ?? getSpecificMessage(context.reference, issue.lang) ?? (isSchema ? getSchemaMessage(issue.lang) : null) ?? config2.message ?? getGlobalMessage(issue.lang)\n  );\n  if (message) {\n    issue.message = typeof message === \"function\" ? message(issue) : message;\n  }\n  if (isSchema) {\n    dataset.typed = false;\n  }\n  if (dataset.issues) {\n    dataset.issues.push(issue);\n  } else {\n    dataset.issues = [issue];\n  }\n}\n\n// src/utils/_isAllowedObjectKey/_isAllowedObjectKey.ts\nfunction _isAllowedObjectKey(key) {\n  return key !== \"__proto__\" && key !== \"prototype\" && key !== \"constructor\";\n}\n\n// src/utils/_isLuhnAlgo/_isLuhnAlgo.ts\nvar NON_DIGIT_REGEX = /\\D/gu;\nfunction _isLuhnAlgo(input) {\n  const number2 = input.replace(NON_DIGIT_REGEX, \"\");\n  let length2 = number2.length;\n  let bit = 1;\n  let sum = 0;\n  while (length2) {\n    const value2 = +number2[--length2];\n    bit ^= 1;\n    sum += bit ? [0, 2, 4, 6, 8, 1, 3, 5, 7, 9][value2] : value2;\n  }\n  return sum % 10 === 0;\n}\n\n// src/utils/entriesFromList/entriesFromList.ts\nfunction entriesFromList(list, schema) {\n  const entries = {};\n  for (const key of list) {\n    entries[key] = schema;\n  }\n  return entries;\n}\n\n// src/utils/getDotPath/getDotPath.ts\nfunction getDotPath(issue) {\n  if (issue.path) {\n    let key = \"\";\n    for (const item of issue.path) {\n      if (\"key\" in item && (typeof item.key === \"string\" || typeof item.key === \"number\")) {\n        if (key) {\n          key += `.${item.key}`;\n        } else {\n          key += item.key;\n        }\n      } else {\n        return null;\n      }\n    }\n    return key;\n  }\n  return null;\n}\n\n// src/utils/isOfKind/isOfKind.ts\nfunction isOfKind(kind, object2) {\n  return object2.kind === kind;\n}\n\n// src/utils/isOfType/isOfType.ts\nfunction isOfType(type, object2) {\n  return object2.type === type;\n}\n\n// src/utils/isValiError/isValiError.ts\nfunction isValiError(error) {\n  return error instanceof ValiError;\n}\n\n// src/utils/ValiError/ValiError.ts\nvar ValiError = class extends Error {\n  /**\n   * The error issues.\n   */\n  issues;\n  /**\n   * Creates a Valibot error with useful information.\n   *\n   * @param issues The error issues.\n   */\n  constructor(issues) {\n    super(issues[0].message);\n    this.name = \"ValiError\";\n    this.issues = issues;\n  }\n};\n\n// src/actions/bic/bic.ts\nfunction bic(message) {\n  return {\n    kind: \"validation\",\n    type: \"bic\",\n    reference: bic,\n    async: false,\n    expects: null,\n    requirement: BIC_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"BIC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/bytes/bytes.ts\nfunction bytes(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"bytes\",\n    reference: bytes,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = new TextEncoder().encode(dataset.value).length;\n        if (length2 !== this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/brand/brand.ts\nfunction brand(name) {\n  return {\n    kind: \"transformation\",\n    type: \"brand\",\n    reference: brand,\n    async: false,\n    name,\n    _run(dataset) {\n      return dataset;\n    }\n  };\n}\n\n// src/actions/check/check.ts\nfunction check(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"check\",\n    reference: check,\n    async: false,\n    expects: null,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"input\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/check/checkAsync.ts\nfunction checkAsync(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"check\",\n    reference: checkAsync,\n    async: true,\n    expects: null,\n    requirement,\n    message,\n    async _run(dataset, config2) {\n      if (dataset.typed && !await this.requirement(dataset.value)) {\n        _addIssue(this, \"input\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/creditCard/creditCard.ts\nvar CREDIT_CARD_REGEX = /^(?:\\d{14,19}|\\d{4}(?: \\d{3,6}){2,4}|\\d{4}(?:-\\d{3,6}){2,4})$/u;\nvar SANITIZE_REGEX = /[- ]/gu;\nvar PROVIDER_REGEX_LIST = [\n  // American Express\n  /^3[47]\\d{13}$/u,\n  // Diners Club\n  /^3(?:0[0-5]|[68]\\d)\\d{11,13}$/u,\n  // Discover\n  /^6(?:011|5\\d{2})\\d{12,15}$/u,\n  // JCB\n  /^(?:2131|1800|35\\d{3})\\d{11}$/u,\n  // Mastercard\n  /^5[1-5]\\d{2}|(?:222\\d|22[3-9]\\d|2[3-6]\\d{2}|27[01]\\d|2720)\\d{12}$/u,\n  // UnionPay\n  /^(?:6[27]\\d{14,17}|81\\d{14,17})$/u,\n  // Visa\n  /^4\\d{12}(?:\\d{3,6})?$/u\n];\nfunction creditCard(message) {\n  return {\n    kind: \"validation\",\n    type: \"credit_card\",\n    reference: creditCard,\n    async: false,\n    expects: null,\n    requirement(input) {\n      let sanitized;\n      return CREDIT_CARD_REGEX.test(input) && // Remove any hyphens and blanks\n      (sanitized = input.replace(SANITIZE_REGEX, \"\")) && // Check if it matches a provider\n      PROVIDER_REGEX_LIST.some((regex2) => regex2.test(sanitized)) && // Check if passes luhn algorithm\n      _isLuhnAlgo(sanitized);\n    },\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"credit card\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/cuid2/cuid2.ts\nfunction cuid2(message) {\n  return {\n    kind: \"validation\",\n    type: \"cuid2\",\n    reference: cuid2,\n    async: false,\n    expects: null,\n    requirement: CUID2_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"Cuid2\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/decimal/decimal.ts\nfunction decimal(message) {\n  return {\n    kind: \"validation\",\n    type: \"decimal\",\n    reference: decimal,\n    async: false,\n    expects: null,\n    requirement: DECIMAL_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"decimal\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/email/email.ts\nfunction email(message) {\n  return {\n    kind: \"validation\",\n    type: \"email\",\n    reference: email,\n    expects: null,\n    async: false,\n    requirement: EMAIL_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"email\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/emoji/emoji.ts\nfunction emoji(message) {\n  return {\n    kind: \"validation\",\n    type: \"emoji\",\n    reference: emoji,\n    async: false,\n    expects: null,\n    requirement: EMOJI_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"emoji\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/empty/empty.ts\nfunction empty(message) {\n  return {\n    kind: \"validation\",\n    type: \"empty\",\n    reference: empty,\n    async: false,\n    expects: \"0\",\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length > 0) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/endsWith/endsWith.ts\nfunction endsWith(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"ends_with\",\n    reference: endsWith,\n    async: false,\n    expects: `\"${requirement}\"`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.endsWith(this.requirement)) {\n        _addIssue(this, \"end\", dataset, config2, {\n          received: `\"${dataset.value.slice(-this.requirement.length)}\"`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/every/every.ts\nfunction every(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"every\",\n    reference: every,\n    async: false,\n    expects: null,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.every(this.requirement)) {\n        _addIssue(this, \"content\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/excludes/excludes.ts\nfunction excludes(requirement, message) {\n  const received = _stringify(requirement);\n  return {\n    kind: \"validation\",\n    type: \"excludes\",\n    reference: excludes,\n    async: false,\n    expects: `!${received}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.includes(this.requirement)) {\n        _addIssue(this, \"content\", dataset, config2, { received });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/finite/finite.ts\nfunction finite(message) {\n  return {\n    kind: \"validation\",\n    type: \"finite\",\n    reference: finite,\n    async: false,\n    expects: null,\n    requirement: Number.isFinite,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"finite\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/hash/hash.ts\nvar HASH_LENGTHS = {\n  md4: 32,\n  md5: 32,\n  sha1: 40,\n  sha256: 64,\n  sha384: 96,\n  sha512: 128,\n  ripemd128: 32,\n  ripemd160: 40,\n  tiger128: 32,\n  tiger160: 40,\n  tiger192: 48,\n  crc32: 8,\n  crc32b: 8,\n  adler32: 8\n};\nfunction hash(types, message) {\n  return {\n    kind: \"validation\",\n    type: \"hash\",\n    reference: hash,\n    expects: null,\n    async: false,\n    requirement: RegExp(\n      types.map((type) => `^[a-f0-9]{${HASH_LENGTHS[type]}}$`).join(\"|\"),\n      \"iu\"\n    ),\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"hash\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/hexadecimal/hexadecimal.ts\nfunction hexadecimal(message) {\n  return {\n    kind: \"validation\",\n    type: \"hexadecimal\",\n    reference: hexadecimal,\n    async: false,\n    expects: null,\n    requirement: HEXADECIMAL_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"hexadecimal\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/hexColor/hexColor.ts\nfunction hexColor(message) {\n  return {\n    kind: \"validation\",\n    type: \"hex_color\",\n    reference: hexColor,\n    async: false,\n    expects: null,\n    requirement: HEX_COLOR_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"hex color\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/imei/imei.ts\nfunction imei(message) {\n  return {\n    kind: \"validation\",\n    type: \"imei\",\n    reference: imei,\n    async: false,\n    expects: null,\n    requirement(input) {\n      return IMEI_REGEX.test(input) && _isLuhnAlgo(input);\n    },\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"IMEI\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/includes/includes.ts\nfunction includes(requirement, message) {\n  const expects = _stringify(requirement);\n  return {\n    kind: \"validation\",\n    type: \"includes\",\n    reference: includes,\n    async: false,\n    expects,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.includes(this.requirement)) {\n        _addIssue(this, \"content\", dataset, config2, {\n          received: `!${expects}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/integer/integer.ts\nfunction integer(message) {\n  return {\n    kind: \"validation\",\n    type: \"integer\",\n    reference: integer,\n    async: false,\n    expects: null,\n    requirement: Number.isInteger,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"integer\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ip/ip.ts\nfunction ip(message) {\n  return {\n    kind: \"validation\",\n    type: \"ip\",\n    reference: ip,\n    async: false,\n    expects: null,\n    requirement: IP_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"IP\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ipv4/ipv4.ts\nfunction ipv4(message) {\n  return {\n    kind: \"validation\",\n    type: \"ipv4\",\n    reference: ipv4,\n    async: false,\n    expects: null,\n    requirement: IPV4_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"IPv4\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ipv6/ipv6.ts\nfunction ipv6(message) {\n  return {\n    kind: \"validation\",\n    type: \"ipv6\",\n    reference: ipv6,\n    async: false,\n    expects: null,\n    requirement: IPV6_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"IPv6\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoDate/isoDate.ts\nfunction isoDate(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_date\",\n    reference: isoDate,\n    async: false,\n    expects: null,\n    requirement: ISO_DATE_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"date\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoDateTime/isoDateTime.ts\nfunction isoDateTime(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_date_time\",\n    reference: isoDateTime,\n    async: false,\n    expects: null,\n    requirement: ISO_DATE_TIME_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"date-time\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoTime/isoTime.ts\nfunction isoTime(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_time\",\n    reference: isoTime,\n    async: false,\n    expects: null,\n    requirement: ISO_TIME_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"time\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoTimeSecond/isoTimeSecond.ts\nfunction isoTimeSecond(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_time_second\",\n    reference: isoTimeSecond,\n    async: false,\n    expects: null,\n    requirement: ISO_TIME_SECOND_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"time-second\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoTimestamp/isoTimestamp.ts\nfunction isoTimestamp(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_timestamp\",\n    reference: isoTimestamp,\n    async: false,\n    expects: null,\n    requirement: ISO_TIMESTAMP_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"timestamp\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoWeek/isoWeek.ts\nfunction isoWeek(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_week\",\n    reference: isoWeek,\n    async: false,\n    expects: null,\n    requirement: ISO_WEEK_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"week\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/length/length.ts\nfunction length(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"length\",\n    reference: length,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length !== this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mac/mac.ts\nfunction mac(message) {\n  return {\n    kind: \"validation\",\n    type: \"mac\",\n    reference: mac,\n    async: false,\n    expects: null,\n    requirement: MAC_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"MAC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mac48/mac48.ts\nfunction mac48(message) {\n  return {\n    kind: \"validation\",\n    type: \"mac48\",\n    reference: mac48,\n    async: false,\n    expects: null,\n    requirement: MAC48_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"48-bit MAC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mac64/mac64.ts\nfunction mac64(message) {\n  return {\n    kind: \"validation\",\n    type: \"mac64\",\n    reference: mac64,\n    async: false,\n    expects: null,\n    requirement: MAC64_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"64-bit MAC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxBytes/maxBytes.ts\nfunction maxBytes(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"max_bytes\",\n    reference: maxBytes,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = new TextEncoder().encode(dataset.value).length;\n        if (length2 > this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxLength/maxLength.ts\nfunction maxLength(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"max_length\",\n    reference: maxLength,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length > this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxSize/maxSize.ts\nfunction maxSize(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"max_size\",\n    reference: maxSize,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.size > this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxValue/maxValue.ts\nfunction maxValue(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"max_value\",\n    reference: maxValue,\n    async: false,\n    expects: `<=${requirement instanceof Date ? requirement.toJSON() : _stringify(requirement)}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value > this.requirement) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mimeType/mimeType.ts\nfunction mimeType(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"mime_type\",\n    reference: mimeType,\n    async: false,\n    expects: requirement.map((option) => `\"${option}\"`).join(\" | \") || \"never\",\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.includes(dataset.value.type)) {\n        _addIssue(this, \"MIME type\", dataset, config2, {\n          received: `\"${dataset.value.type}\"`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minBytes/minBytes.ts\nfunction minBytes(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"min_bytes\",\n    reference: minBytes,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = new TextEncoder().encode(dataset.value).length;\n        if (length2 < this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minLength/minLength.ts\nfunction minLength(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"min_length\",\n    reference: minLength,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length < this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minSize/minSize.ts\nfunction minSize(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"min_size\",\n    reference: minSize,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.size < this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minValue/minValue.ts\nfunction minValue(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"min_value\",\n    reference: minValue,\n    async: false,\n    expects: `>=${requirement instanceof Date ? requirement.toJSON() : _stringify(requirement)}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value < this.requirement) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/multipleOf/multipleOf.ts\nfunction multipleOf(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"multiple_of\",\n    reference: multipleOf,\n    async: false,\n    expects: `%${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value % this.requirement !== 0) {\n        _addIssue(this, \"multiple\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/nonEmpty/nonEmpty.ts\nfunction nonEmpty(message) {\n  return {\n    kind: \"validation\",\n    type: \"non_empty\",\n    reference: nonEmpty,\n    async: false,\n    expects: \"!0\",\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length === 0) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: \"0\"\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notBytes/notBytes.ts\nfunction notBytes(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"not_bytes\",\n    reference: notBytes,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = new TextEncoder().encode(dataset.value).length;\n        if (length2 === this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notLength/notLength.ts\nfunction notLength(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"not_length\",\n    reference: notLength,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length === this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notSize/notSize.ts\nfunction notSize(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"not_size\",\n    reference: notSize,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.size === this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notValue/notValue.ts\nfunction notValue(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"not_value\",\n    reference: notValue,\n    async: false,\n    expects: requirement instanceof Date ? `!${requirement.toJSON()}` : `!${_stringify(requirement)}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && this.requirement <= dataset.value && this.requirement >= dataset.value) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/octal/octal.ts\nfunction octal(message) {\n  return {\n    kind: \"validation\",\n    type: \"octal\",\n    reference: octal,\n    async: false,\n    expects: null,\n    requirement: OCTAL_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"octal\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/readonly/readonly.ts\nfunction readonly() {\n  return {\n    kind: \"transformation\",\n    type: \"readonly\",\n    reference: readonly,\n    async: false,\n    _run(dataset) {\n      return dataset;\n    }\n  };\n}\n\n// src/actions/regex/regex.ts\nfunction regex(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"regex\",\n    reference: regex,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"format\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/safeInteger/safeInteger.ts\nfunction safeInteger(message) {\n  return {\n    kind: \"validation\",\n    type: \"safe_integer\",\n    reference: safeInteger,\n    async: false,\n    expects: null,\n    requirement: Number.isSafeInteger,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"safe integer\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/size/size.ts\nfunction size(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"size\",\n    reference: size,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.size !== this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/some/some.ts\nfunction some(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"some\",\n    reference: some,\n    async: false,\n    expects: null,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.some(this.requirement)) {\n        _addIssue(this, \"content\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/startsWith/startsWith.ts\nfunction startsWith(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"starts_with\",\n    reference: startsWith,\n    async: false,\n    expects: `\"${requirement}\"`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.startsWith(this.requirement)) {\n        _addIssue(this, \"start\", dataset, config2, {\n          received: `\"${dataset.value.slice(0, this.requirement.length)}\"`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toLowerCase/toLowerCase.ts\nfunction toLowerCase() {\n  return {\n    kind: \"transformation\",\n    type: \"to_lower_case\",\n    reference: toLowerCase,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.toLowerCase();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toMaxValue/toMaxValue.ts\nfunction toMaxValue(requirement) {\n  return {\n    kind: \"transformation\",\n    type: \"to_max_value\",\n    reference: toMaxValue,\n    async: false,\n    requirement,\n    _run(dataset) {\n      dataset.value = dataset.value > this.requirement ? this.requirement : dataset.value;\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toMinValue/toMinValue.ts\nfunction toMinValue(requirement) {\n  return {\n    kind: \"transformation\",\n    type: \"to_min_value\",\n    reference: toMinValue,\n    async: false,\n    requirement,\n    _run(dataset) {\n      dataset.value = dataset.value < this.requirement ? this.requirement : dataset.value;\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toUpperCase/toUpperCase.ts\nfunction toUpperCase() {\n  return {\n    kind: \"transformation\",\n    type: \"to_upper_case\",\n    reference: toUpperCase,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.toUpperCase();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/transform/transform.ts\nfunction transform(action) {\n  return {\n    kind: \"transformation\",\n    type: \"transform\",\n    reference: transform,\n    async: false,\n    action,\n    _run(dataset) {\n      dataset.value = action(dataset.value);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/transform/transformAsync.ts\nfunction transformAsync(action) {\n  return {\n    kind: \"transformation\",\n    type: \"transform\",\n    reference: transformAsync,\n    async: true,\n    action,\n    async _run(dataset) {\n      dataset.value = await action(dataset.value);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/trim/trim.ts\nfunction trim() {\n  return {\n    kind: \"transformation\",\n    type: \"trim\",\n    reference: trim,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.trim();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/trimEnd/trimEnd.ts\nfunction trimEnd() {\n  return {\n    kind: \"transformation\",\n    type: \"trim_end\",\n    reference: trimEnd,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.trimEnd();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/trimStart/trimStart.ts\nfunction trimStart() {\n  return {\n    kind: \"transformation\",\n    type: \"trim_start\",\n    reference: trimStart,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.trimStart();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ulid/ulid.ts\nfunction ulid(message) {\n  return {\n    kind: \"validation\",\n    type: \"ulid\",\n    reference: ulid,\n    async: false,\n    expects: null,\n    requirement: ULID_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"ULID\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/url/url.ts\nfunction url(message) {\n  return {\n    kind: \"validation\",\n    type: \"url\",\n    reference: url,\n    async: false,\n    expects: null,\n    requirement(input) {\n      try {\n        new URL(input);\n        return true;\n      } catch {\n        return false;\n      }\n    },\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"URL\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/uuid/uuid.ts\nfunction uuid(message) {\n  return {\n    kind: \"validation\",\n    type: \"uuid\",\n    reference: uuid,\n    async: false,\n    expects: null,\n    requirement: UUID_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"UUID\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/value/value.ts\nfunction value(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"value\",\n    reference: value,\n    async: false,\n    expects: requirement instanceof Date ? requirement.toJSON() : _stringify(requirement),\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !(this.requirement <= dataset.value && this.requirement >= dataset.value)) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/config/config.ts\nfunction config(schema, config2) {\n  return {\n    ...schema,\n    _run(dataset, config_) {\n      return schema._run(dataset, { ...config_, ...config2 });\n    }\n  };\n}\n\n// src/methods/getFallback/getFallback.ts\nfunction getFallback(schema, dataset, config2) {\n  return typeof schema.fallback === \"function\" ? (\n    // @ts-expect-error\n    schema.fallback(dataset, config2)\n  ) : (\n    // @ts-expect-error\n    schema.fallback\n  );\n}\n\n// src/methods/fallback/fallback.ts\nfunction fallback(schema, fallback2) {\n  return {\n    ...schema,\n    fallback: fallback2,\n    _run(dataset, config2) {\n      schema._run(dataset, config2);\n      return dataset.issues ? { typed: true, value: getFallback(this, dataset, config2) } : dataset;\n    }\n  };\n}\n\n// src/methods/fallback/fallbackAsync.ts\nfunction fallbackAsync(schema, fallback2) {\n  return {\n    ...schema,\n    fallback: fallback2,\n    async: true,\n    async _run(dataset, config2) {\n      schema._run(dataset, config2);\n      return dataset.issues ? (\n        // @ts-expect-error\n        { typed: true, value: await getFallback(this, dataset, config2) }\n      ) : dataset;\n    }\n  };\n}\n\n// src/methods/flatten/flatten.ts\nfunction flatten(issues) {\n  const flatErrors = {};\n  for (const issue of issues) {\n    if (issue.path) {\n      const dotPath = getDotPath(issue);\n      if (dotPath) {\n        if (!flatErrors.nested) {\n          flatErrors.nested = {};\n        }\n        if (flatErrors.nested[dotPath]) {\n          flatErrors.nested[dotPath].push(issue.message);\n        } else {\n          flatErrors.nested[dotPath] = [issue.message];\n        }\n      } else {\n        if (flatErrors.other) {\n          flatErrors.other.push(issue.message);\n        } else {\n          flatErrors.other = [issue.message];\n        }\n      }\n    } else {\n      if (flatErrors.root) {\n        flatErrors.root.push(issue.message);\n      } else {\n        flatErrors.root = [issue.message];\n      }\n    }\n  }\n  return flatErrors;\n}\n\n// src/methods/forward/forward.ts\nfunction forward(action, pathKeys) {\n  return {\n    ...action,\n    _run(dataset, config2) {\n      const prevIssues = dataset.issues && [...dataset.issues];\n      action._run(dataset, config2);\n      if (dataset.issues) {\n        for (const issue of dataset.issues) {\n          if (!prevIssues?.includes(issue)) {\n            let pathInput = dataset.value;\n            for (const key of pathKeys) {\n              const pathValue = pathInput[key];\n              const pathItem = {\n                type: \"unknown\",\n                origin: \"value\",\n                input: pathInput,\n                key,\n                value: pathValue\n              };\n              if (issue.path) {\n                issue.path.push(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              if (!pathValue) {\n                break;\n              }\n              pathInput = pathValue;\n            }\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/forward/forwardAsync.ts\nfunction forwardAsync(action, pathKeys) {\n  return {\n    ...action,\n    async: true,\n    async _run(dataset, config2) {\n      const prevIssues = dataset.issues && [...dataset.issues];\n      await action._run(dataset, config2);\n      if (dataset.issues) {\n        for (const issue of dataset.issues) {\n          if (!prevIssues?.includes(issue)) {\n            let pathInput = dataset.value;\n            for (const key of pathKeys) {\n              const pathValue = pathInput[key];\n              const pathItem = {\n                type: \"unknown\",\n                origin: \"value\",\n                input: pathInput,\n                key,\n                value: pathValue\n              };\n              if (issue.path) {\n                issue.path.push(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              if (!pathValue) {\n                break;\n              }\n              pathInput = pathValue;\n            }\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/getDefault/getDefault.ts\nfunction getDefault(schema, dataset, config2) {\n  return typeof schema.default === \"function\" ? (\n    // @ts-expect-error\n    schema.default(dataset, config2)\n  ) : (\n    // @ts-expect-error\n    schema.default\n  );\n}\n\n// src/methods/getDefaults/getDefaults.ts\nfunction getDefaults(schema) {\n  if (\"entries\" in schema) {\n    const object2 = {};\n    for (const key in schema.entries) {\n      object2[key] = getDefaults(schema.entries[key]);\n    }\n    return object2;\n  }\n  if (\"items\" in schema) {\n    return schema.items.map(getDefaults);\n  }\n  return getDefault(schema);\n}\n\n// src/methods/getDefaults/getDefaultsAsync.ts\nasync function getDefaultsAsync(schema) {\n  if (\"entries\" in schema) {\n    return Object.fromEntries(\n      await Promise.all(\n        Object.entries(schema.entries).map(async ([key, value2]) => [\n          key,\n          await getDefaultsAsync(value2)\n        ])\n      )\n    );\n  }\n  if (\"items\" in schema) {\n    return Promise.all(schema.items.map(getDefaultsAsync));\n  }\n  return getDefault(schema);\n}\n\n// src/methods/getFallbacks/getFallbacks.ts\nfunction getFallbacks(schema) {\n  if (\"entries\" in schema) {\n    const object2 = {};\n    for (const key in schema.entries) {\n      object2[key] = getFallbacks(schema.entries[key]);\n    }\n    return object2;\n  }\n  if (\"items\" in schema) {\n    return schema.items.map(getFallbacks);\n  }\n  return getFallback(schema);\n}\n\n// src/methods/getFallbacks/getFallbacksAsync.ts\nasync function getFallbacksAsync(schema) {\n  if (\"entries\" in schema) {\n    return Object.fromEntries(\n      await Promise.all(\n        Object.entries(schema.entries).map(async ([key, value2]) => [\n          key,\n          await getFallbacksAsync(value2)\n        ])\n      )\n    );\n  }\n  if (\"items\" in schema) {\n    return Promise.all(schema.items.map(getFallbacksAsync));\n  }\n  return getFallback(schema);\n}\n\n// src/methods/is/is.ts\nfunction is(schema, input) {\n  return !schema._run({ typed: false, value: input }, { abortEarly: true }).issues;\n}\n\n// src/schemas/any/any.ts\nfunction any() {\n  return {\n    kind: \"schema\",\n    type: \"any\",\n    reference: any,\n    expects: \"any\",\n    async: false,\n    _run(dataset) {\n      dataset.typed = true;\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/array/array.ts\nfunction array(item, message) {\n  return {\n    kind: \"schema\",\n    type: \"array\",\n    reference: array,\n    expects: \"Array\",\n    async: false,\n    item,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < input.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.item._run({ typed: false, value: value2 }, config2);\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/array/arrayAsync.ts\nfunction arrayAsync(item, message) {\n  return {\n    kind: \"schema\",\n    type: \"array\",\n    reference: arrayAsync,\n    expects: \"Array\",\n    async: true,\n    item,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          input.map((value2) => this.item._run({ typed: false, value: value2 }, config2))\n        );\n        for (let key = 0; key < itemDatasets.length; key++) {\n          const itemDataset = itemDatasets[key];\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: input[key]\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/bigint/bigint.ts\nfunction bigint(message) {\n  return {\n    kind: \"schema\",\n    type: \"bigint\",\n    reference: bigint,\n    expects: \"bigint\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"bigint\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/blob/blob.ts\nfunction blob(message) {\n  return {\n    kind: \"schema\",\n    type: \"blob\",\n    reference: blob,\n    expects: \"Blob\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value instanceof Blob) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/boolean/boolean.ts\nfunction boolean(message) {\n  return {\n    kind: \"schema\",\n    type: \"boolean\",\n    reference: boolean,\n    expects: \"boolean\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"boolean\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/custom/custom.ts\nfunction custom(check2, message) {\n  return {\n    kind: \"schema\",\n    type: \"custom\",\n    reference: custom,\n    expects: \"unknown\",\n    async: false,\n    check: check2,\n    message,\n    _run(dataset, config2) {\n      if (this.check(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/custom/customAsync.ts\nfunction customAsync(check2, message) {\n  return {\n    kind: \"schema\",\n    type: \"custom\",\n    reference: customAsync,\n    expects: \"unknown\",\n    async: true,\n    check: check2,\n    message,\n    async _run(dataset, config2) {\n      if (await this.check(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/date/date.ts\nfunction date(message) {\n  return {\n    kind: \"schema\",\n    type: \"date\",\n    reference: date,\n    expects: \"Date\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value instanceof Date && !isNaN(dataset.value.getTime())) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/enum/enum.ts\nfunction enum_(enum__, message) {\n  const options = Object.entries(enum__).filter(([key]) => isNaN(+key)).map(([, value2]) => value2);\n  return {\n    kind: \"schema\",\n    type: \"enum\",\n    reference: enum_,\n    expects: options.map(_stringify).join(\" | \") || \"never\",\n    async: false,\n    enum: enum__,\n    options,\n    message,\n    _run(dataset, config2) {\n      if (this.options.includes(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/instance/instance.ts\nfunction instance(class_, message) {\n  return {\n    kind: \"schema\",\n    type: \"instance\",\n    reference: instance,\n    expects: class_.name,\n    async: false,\n    class: class_,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value instanceof this.class) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/intersect/utils/_merge/_merge.ts\nfunction _merge(value1, value2) {\n  if (typeof value1 === typeof value2) {\n    if (value1 === value2 || value1 instanceof Date && value2 instanceof Date && +value1 === +value2) {\n      return { value: value1 };\n    }\n    if (value1 && value2 && value1.constructor === Object && value2.constructor === Object) {\n      for (const key in value2) {\n        if (key in value1) {\n          const dataset = _merge(value1[key], value2[key]);\n          if (dataset.issue) {\n            return dataset;\n          }\n          value1[key] = dataset.value;\n        } else {\n          value1[key] = value2[key];\n        }\n      }\n      return { value: value1 };\n    }\n    if (Array.isArray(value1) && Array.isArray(value2)) {\n      if (value1.length === value2.length) {\n        for (let index = 0; index < value1.length; index++) {\n          const dataset = _merge(value1[index], value2[index]);\n          if (dataset.issue) {\n            return dataset;\n          }\n          value1[index] = dataset.value;\n        }\n        return { value: value1 };\n      }\n    }\n  }\n  return { issue: true };\n}\n\n// src/schemas/intersect/intersect.ts\nfunction intersect(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"intersect\",\n    reference: intersect,\n    expects: [...new Set(options.map((option) => option.expects))].join(\" & \") || \"never\",\n    async: false,\n    options,\n    message,\n    _run(dataset, config2) {\n      if (this.options.length) {\n        const input = dataset.value;\n        let outputs;\n        dataset.typed = true;\n        for (const schema of this.options) {\n          const optionDataset = schema._run(\n            { typed: false, value: input },\n            config2\n          );\n          if (optionDataset.issues) {\n            if (dataset.issues) {\n              dataset.issues.push(...optionDataset.issues);\n            } else {\n              dataset.issues = optionDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!optionDataset.typed) {\n            dataset.typed = false;\n          }\n          if (dataset.typed) {\n            if (outputs) {\n              outputs.push(optionDataset.value);\n            } else {\n              outputs = [optionDataset.value];\n            }\n          }\n        }\n        if (dataset.typed) {\n          dataset.value = outputs[0];\n          for (let index = 1; index < outputs.length; index++) {\n            const mergeDataset = _merge(dataset.value, outputs[index]);\n            if (mergeDataset.issue) {\n              _addIssue(this, \"type\", dataset, config2, {\n                received: \"unknown\"\n              });\n              break;\n            }\n            dataset.value = mergeDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/intersect/intersectAsync.ts\nfunction intersectAsync(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"intersect\",\n    reference: intersectAsync,\n    expects: [...new Set(options.map((option) => option.expects))].join(\" & \") || \"never\",\n    async: true,\n    options,\n    message,\n    async _run(dataset, config2) {\n      if (this.options.length) {\n        const input = dataset.value;\n        let outputs;\n        dataset.typed = true;\n        const optionDatasets = await Promise.all(\n          this.options.map(\n            (schema) => schema._run({ typed: false, value: input }, config2)\n          )\n        );\n        for (const optionDataset of optionDatasets) {\n          if (optionDataset.issues) {\n            if (dataset.issues) {\n              dataset.issues.push(...optionDataset.issues);\n            } else {\n              dataset.issues = optionDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!optionDataset.typed) {\n            dataset.typed = false;\n          }\n          if (dataset.typed) {\n            if (outputs) {\n              outputs.push(optionDataset.value);\n            } else {\n              outputs = [optionDataset.value];\n            }\n          }\n        }\n        if (dataset.typed) {\n          dataset.value = outputs[0];\n          for (let index = 1; index < outputs.length; index++) {\n            const mergeDataset = _merge(dataset.value, outputs[index]);\n            if (mergeDataset.issue) {\n              _addIssue(this, \"type\", dataset, config2, {\n                received: \"unknown\"\n              });\n              break;\n            }\n            dataset.value = mergeDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/lazy/lazy.ts\nfunction lazy(getter) {\n  return {\n    kind: \"schema\",\n    type: \"lazy\",\n    reference: lazy,\n    expects: \"unknown\",\n    async: false,\n    getter,\n    _run(dataset, config2) {\n      return this.getter(dataset.value)._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/lazy/lazyAsync.ts\nfunction lazyAsync(getter) {\n  return {\n    kind: \"schema\",\n    type: \"lazy\",\n    reference: lazyAsync,\n    expects: \"unknown\",\n    async: true,\n    getter,\n    async _run(dataset, config2) {\n      return (await this.getter(dataset.value))._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/literal/literal.ts\nfunction literal(literal_, message) {\n  return {\n    kind: \"schema\",\n    type: \"literal\",\n    reference: literal,\n    expects: _stringify(literal_),\n    async: false,\n    literal: literal_,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === this.literal) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseObject/looseObject.ts\nfunction looseObject(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"loose_object\",\n    reference: looseObject,\n    expects: \"Object\",\n    async: false,\n    entries,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const value2 = input[key];\n          const valueDataset = this.entries[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (_isAllowedObjectKey(key) && !(key in this.entries)) {\n              dataset.value[key] = input[key];\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseObject/looseObjectAsync.ts\nfunction looseObjectAsync(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"loose_object\",\n    reference: looseObjectAsync,\n    expects: \"Object\",\n    async: true,\n    entries,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const valueDatasets = await Promise.all(\n          Object.entries(this.entries).map(async ([key, schema]) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await schema._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, valueDataset] of valueDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (_isAllowedObjectKey(key) && !(key in this.entries)) {\n              dataset.value[key] = input[key];\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseTuple/looseTuple.ts\nfunction looseTuple(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"loose_tuple\",\n    reference: looseTuple,\n    expects: \"Array\",\n    async: false,\n    items,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (let key = items.length; key < input.length; key++) {\n            dataset.value.push(input[key]);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseTuple/looseTupleAsync.ts\nfunction looseTupleAsync(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"loose_tuple\",\n    reference: looseTupleAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          items.map(async (item, key) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await item._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, itemDataset] of itemDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (let key = items.length; key < input.length; key++) {\n            dataset.value.push(input[key]);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/map/map.ts\nfunction map(key, value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"map\",\n    reference: map,\n    expects: \"Map\",\n    async: false,\n    key,\n    value: value2,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Map) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Map();\n        for (const [inputKey, inputValue] of input) {\n          const keyDataset = this.key._run(\n            { typed: false, value: inputKey },\n            config2\n          );\n          if (keyDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"key\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of keyDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = keyDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          const valueDataset = this.value._run(\n            { typed: false, value: inputValue },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"value\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!keyDataset.typed || !valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.set(keyDataset.value, valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/map/mapAsync.ts\nfunction mapAsync(key, value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"map\",\n    reference: mapAsync,\n    expects: \"Map\",\n    async: true,\n    key,\n    value: value2,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Map) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Map();\n        const datasets = await Promise.all(\n          [...input].map(\n            ([inputKey, inputValue]) => Promise.all([\n              inputKey,\n              inputValue,\n              this.key._run({ typed: false, value: inputKey }, config2),\n              this.value._run({ typed: false, value: inputValue }, config2)\n            ])\n          )\n        );\n        for (const [\n          inputKey,\n          inputValue,\n          keyDataset,\n          valueDataset\n        ] of datasets) {\n          if (keyDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"key\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of keyDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = keyDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"value\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!keyDataset.typed || !valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.set(keyDataset.value, valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nan/nan.ts\nfunction nan(message) {\n  return {\n    kind: \"schema\",\n    type: \"nan\",\n    reference: nan,\n    expects: \"NaN\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (Number.isNaN(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/never/never.ts\nfunction never(message) {\n  return {\n    kind: \"schema\",\n    type: \"never\",\n    reference: never,\n    expects: \"never\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      _addIssue(this, \"type\", dataset, config2);\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nonNullable/nonNullable.ts\nfunction nonNullable(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullable\",\n    reference: nonNullable,\n    expects: \"!null\",\n    async: false,\n    wrapped,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === null) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonNullable/nonNullableAsync.ts\nfunction nonNullableAsync(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullable\",\n    reference: nonNullableAsync,\n    expects: \"!null\",\n    async: true,\n    wrapped,\n    message,\n    async _run(dataset, config2) {\n      if (dataset.value === null) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonNullish/nonNullish.ts\nfunction nonNullish(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullish\",\n    reference: nonNullish,\n    expects: \"!null & !undefined\",\n    async: false,\n    wrapped,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonNullish/nonNullishAsync.ts\nfunction nonNullishAsync(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullish\",\n    reference: nonNullishAsync,\n    expects: \"!null & !undefined\",\n    async: true,\n    wrapped,\n    message,\n    async _run(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonOptional/nonOptional.ts\nfunction nonOptional(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_optional\",\n    reference: nonOptional,\n    expects: \"!undefined\",\n    async: false,\n    wrapped,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonOptional/nonOptionalAsync.ts\nfunction nonOptionalAsync(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_optional\",\n    reference: nonOptionalAsync,\n    expects: \"!undefined\",\n    async: true,\n    wrapped,\n    message,\n    async _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/null/null.ts\nfunction null_(message) {\n  return {\n    kind: \"schema\",\n    type: \"null\",\n    reference: null_,\n    expects: \"null\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === null) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nullable/nullable.ts\nfunction nullable(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"nullable\",\n    reference: nullable,\n    expects: `${wrapped.expects} | null`,\n    async: false,\n    wrapped,\n    _run(dataset, config2) {\n      if (dataset.value === null) {\n        if (\"default\" in this) {\n          dataset.value = getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === null) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/nullable/nullableAsync.ts\nfunction nullableAsync(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"nullable\",\n    reference: nullableAsync,\n    expects: `${wrapped.expects} | null`,\n    async: true,\n    wrapped,\n    async _run(dataset, config2) {\n      if (dataset.value === null) {\n        if (\"default\" in this) {\n          dataset.value = await getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === null) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/nullish/nullish.ts\nfunction nullish(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"nullish\",\n    reference: nullish,\n    expects: `${wrapped.expects} | null | undefined`,\n    async: false,\n    wrapped,\n    _run(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        if (\"default\" in this) {\n          dataset.value = getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === null || dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/nullish/nullishAsync.ts\nfunction nullishAsync(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"nullish\",\n    reference: nullishAsync,\n    expects: `${wrapped.expects} | null | undefined`,\n    async: true,\n    wrapped,\n    async _run(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        if (\"default\" in this) {\n          dataset.value = await getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === null || dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/number/number.ts\nfunction number(message) {\n  return {\n    kind: \"schema\",\n    type: \"number\",\n    reference: number,\n    expects: \"number\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"number\" && !isNaN(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/object/object.ts\nfunction object(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"object\",\n    reference: object,\n    expects: \"Object\",\n    async: false,\n    entries,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const value2 = input[key];\n          const valueDataset = this.entries[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/object/objectAsync.ts\nfunction objectAsync(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"object\",\n    reference: objectAsync,\n    expects: \"Object\",\n    async: true,\n    entries,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const valueDatasets = await Promise.all(\n          Object.entries(this.entries).map(async ([key, schema]) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await schema._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, valueDataset] of valueDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/objectWithRest/objectWithRest.ts\nfunction objectWithRest(entries, rest, message) {\n  return {\n    kind: \"schema\",\n    type: \"object_with_rest\",\n    reference: objectWithRest,\n    expects: \"Object\",\n    async: false,\n    entries,\n    rest,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const value2 = input[key];\n          const valueDataset = this.entries[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (_isAllowedObjectKey(key) && !(key in this.entries)) {\n              const value2 = input[key];\n              const valueDataset = this.rest._run(\n                { typed: false, value: value2 },\n                config2\n              );\n              if (valueDataset.issues) {\n                const pathItem = {\n                  type: \"object\",\n                  origin: \"value\",\n                  input,\n                  key,\n                  value: value2\n                };\n                for (const issue of valueDataset.issues) {\n                  if (issue.path) {\n                    issue.path.unshift(pathItem);\n                  } else {\n                    issue.path = [pathItem];\n                  }\n                  dataset.issues?.push(issue);\n                }\n                if (!dataset.issues) {\n                  dataset.issues = valueDataset.issues;\n                }\n                if (config2.abortEarly) {\n                  dataset.typed = false;\n                  break;\n                }\n              }\n              if (!valueDataset.typed) {\n                dataset.typed = false;\n              }\n              dataset.value[key] = valueDataset.value;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/objectWithRest/objectWithRestAsync.ts\nfunction objectWithRestAsync(entries, rest, message) {\n  return {\n    kind: \"schema\",\n    type: \"object_with_rest\",\n    reference: objectWithRestAsync,\n    expects: \"Object\",\n    async: true,\n    entries,\n    rest,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const [normalDatasets, restDatasets] = await Promise.all([\n          // Parse schema of each normal entry\n          Promise.all(\n            Object.entries(this.entries).map(async ([key, schema]) => {\n              const value2 = input[key];\n              return [\n                key,\n                value2,\n                await schema._run({ typed: false, value: value2 }, config2)\n              ];\n            })\n          ),\n          // Parse other entries with rest schema\n          Promise.all(\n            Object.entries(input).filter(\n              ([key]) => _isAllowedObjectKey(key) && !(key in this.entries)\n            ).map(\n              async ([key, value2]) => [\n                key,\n                value2,\n                await this.rest._run({ typed: false, value: value2 }, config2)\n              ]\n            )\n          )\n        ]);\n        for (const [key, value2, valueDataset] of normalDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const [key, value2, valueDataset] of restDatasets) {\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!valueDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/optional/optional.ts\nfunction optional(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"optional\",\n    reference: optional,\n    expects: `${wrapped.expects} | undefined`,\n    async: false,\n    wrapped,\n    _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        if (\"default\" in this) {\n          dataset.value = getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/optional/optionalAsync.ts\nfunction optionalAsync(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"optional\",\n    reference: optionalAsync,\n    expects: `${wrapped.expects} | undefined`,\n    async: true,\n    wrapped,\n    async _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        if (\"default\" in this) {\n          dataset.value = await getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/picklist/picklist.ts\nfunction picklist(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"picklist\",\n    reference: picklist,\n    expects: options.map(_stringify).join(\" | \") || \"never\",\n    async: false,\n    options,\n    message,\n    _run(dataset, config2) {\n      if (this.options.includes(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/record/record.ts\nfunction record(key, value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"record\",\n    reference: record,\n    expects: \"Object\",\n    async: false,\n    key,\n    value: value2,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const entryKey in input) {\n          if (_isAllowedObjectKey(entryKey)) {\n            const entryValue = input[entryKey];\n            const keyDataset = this.key._run(\n              { typed: false, value: entryKey },\n              config2\n            );\n            if (keyDataset.issues) {\n              const pathItem = {\n                type: \"record\",\n                origin: \"key\",\n                input,\n                key: entryKey,\n                value: entryValue\n              };\n              for (const issue of keyDataset.issues) {\n                issue.path = [pathItem];\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = keyDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            const valueDataset = this.value._run(\n              { typed: false, value: entryValue },\n              config2\n            );\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"record\",\n                origin: \"value\",\n                input,\n                key: entryKey,\n                value: entryValue\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!keyDataset.typed || !valueDataset.typed) {\n              dataset.typed = false;\n            }\n            if (keyDataset.typed) {\n              dataset.value[keyDataset.value] = valueDataset.value;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/record/recordAsync.ts\nfunction recordAsync(key, value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"record\",\n    reference: recordAsync,\n    expects: \"Object\",\n    async: true,\n    key,\n    value: value2,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const datasets = await Promise.all(\n          Object.entries(input).filter(([key2]) => _isAllowedObjectKey(key2)).map(\n            ([entryKey, entryValue]) => Promise.all([\n              entryKey,\n              entryValue,\n              this.key._run({ typed: false, value: entryKey }, config2),\n              this.value._run({ typed: false, value: entryValue }, config2)\n            ])\n          )\n        );\n        for (const [\n          entryKey,\n          entryValue,\n          keyDataset,\n          valueDataset\n        ] of datasets) {\n          if (keyDataset.issues) {\n            const pathItem = {\n              type: \"record\",\n              origin: \"key\",\n              input,\n              key: entryKey,\n              value: entryValue\n            };\n            for (const issue of keyDataset.issues) {\n              issue.path = [pathItem];\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = keyDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"record\",\n              origin: \"value\",\n              input,\n              key: entryKey,\n              value: entryValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!keyDataset.typed || !valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (keyDataset.typed) {\n            dataset.value[keyDataset.value] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/set/set.ts\nfunction set(value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"set\",\n    reference: set,\n    expects: \"Set\",\n    async: false,\n    value: value2,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Set) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Set();\n        for (const inputValue of input) {\n          const valueDataset = this.value._run(\n            { typed: false, value: inputValue },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"set\",\n              origin: \"value\",\n              input,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.add(valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/set/setAsync.ts\nfunction setAsync(value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"set\",\n    reference: setAsync,\n    expects: \"Set\",\n    async: true,\n    value: value2,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Set) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Set();\n        const valueDatasets = await Promise.all(\n          [...input].map(\n            async (inputValue) => [\n              inputValue,\n              await this.value._run(\n                { typed: false, value: inputValue },\n                config2\n              )\n            ]\n          )\n        );\n        for (const [inputValue, valueDataset] of valueDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"set\",\n              origin: \"value\",\n              input,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.add(valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictObject/strictObject.ts\nfunction strictObject(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"strict_object\",\n    reference: strictObject,\n    expects: \"Object\",\n    async: false,\n    entries,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const value2 = input[key];\n          const valueDataset = this.entries[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (!(key in this.entries)) {\n              const value2 = input[key];\n              _addIssue(this, \"type\", dataset, config2, {\n                input: value2,\n                expected: \"never\",\n                path: [\n                  {\n                    type: \"object\",\n                    origin: \"value\",\n                    input,\n                    key,\n                    value: value2\n                  }\n                ]\n              });\n              break;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictObject/strictObjectAsync.ts\nfunction strictObjectAsync(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"strict_object\",\n    reference: strictObjectAsync,\n    expects: \"Object\",\n    async: true,\n    entries,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const valueDatasets = await Promise.all(\n          Object.entries(this.entries).map(async ([key, schema]) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await schema._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, valueDataset] of valueDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (!(key in this.entries)) {\n              const value2 = input[key];\n              _addIssue(this, \"type\", dataset, config2, {\n                input: value2,\n                expected: \"never\",\n                path: [\n                  {\n                    type: \"object\",\n                    origin: \"value\",\n                    input,\n                    key,\n                    value: value2\n                  }\n                ]\n              });\n              break;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictTuple/strictTuple.ts\nfunction strictTuple(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"strict_tuple\",\n    reference: strictTuple,\n    expects: \"Array\",\n    async: false,\n    items,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!(dataset.issues && config2.abortEarly) && items.length < input.length) {\n          const value2 = input[items.length];\n          _addIssue(this, \"type\", dataset, config2, {\n            input: value2,\n            expected: \"never\",\n            path: [\n              {\n                type: \"tuple\",\n                origin: \"value\",\n                input,\n                key: items.length,\n                value: value2\n              }\n            ]\n          });\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictTuple/strictTupleAsync.ts\nfunction strictTupleAsync(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"strict_tuple\",\n    reference: strictTupleAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          items.map(async (item, key) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await item._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, itemDataset] of itemDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!(dataset.issues && config2.abortEarly) && items.length < input.length) {\n          const value2 = input[items.length];\n          _addIssue(this, \"type\", dataset, config2, {\n            input: value2,\n            expected: \"never\",\n            path: [\n              {\n                type: \"tuple\",\n                origin: \"value\",\n                input,\n                key: items.length,\n                value: value2\n              }\n            ]\n          });\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/string/string.ts\nfunction string(message) {\n  return {\n    kind: \"schema\",\n    type: \"string\",\n    reference: string,\n    expects: \"string\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"string\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/symbol/symbol.ts\nfunction symbol(message) {\n  return {\n    kind: \"schema\",\n    type: \"symbol\",\n    reference: symbol,\n    expects: \"symbol\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"symbol\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tuple/tuple.ts\nfunction tuple(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"tuple\",\n    reference: tuple,\n    expects: \"Array\",\n    async: false,\n    items,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tuple/tupleAsync.ts\nfunction tupleAsync(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"tuple\",\n    reference: tupleAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          items.map(async (item, key) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await item._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, itemDataset] of itemDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tupleWithRest/tupleWithRest.ts\nfunction tupleWithRest(items, rest, message) {\n  return {\n    kind: \"schema\",\n    type: \"tuple_with_rest\",\n    reference: tupleWithRest,\n    expects: \"Array\",\n    async: false,\n    items,\n    rest,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (let key = items.length; key < input.length; key++) {\n            const value2 = input[key];\n            const itemDataset = this.rest._run({ typed: false, value: value2 }, config2);\n            if (itemDataset.issues) {\n              const pathItem = {\n                type: \"tuple\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of itemDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = itemDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!itemDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value.push(itemDataset.value);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tupleWithRest/tupleWithRestAsync.ts\nfunction tupleWithRestAsync(items, rest, message) {\n  return {\n    kind: \"schema\",\n    type: \"tuple_with_rest\",\n    reference: tupleWithRestAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    rest,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const [normalDatasets, restDatasets] = await Promise.all([\n          // Parse schema of each normal item\n          Promise.all(\n            items.map(async (item, key) => {\n              const value2 = input[key];\n              return [\n                key,\n                value2,\n                await item._run({ typed: false, value: value2 }, config2)\n              ];\n            })\n          ),\n          // Parse other items with rest schema\n          Promise.all(\n            input.slice(items.length).map(async (value2, key) => {\n              return [\n                key + items.length,\n                value2,\n                await rest._run({ typed: false, value: value2 }, config2)\n              ];\n            })\n          )\n        ]);\n        for (const [key, value2, itemDataset] of normalDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const [key, value2, itemDataset] of restDatasets) {\n            if (itemDataset.issues) {\n              const pathItem = {\n                type: \"tuple\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of itemDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = itemDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!itemDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value.push(itemDataset.value);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/undefined/undefined.ts\nfunction undefined_(message) {\n  return {\n    kind: \"schema\",\n    type: \"undefined\",\n    reference: undefined_,\n    expects: \"undefined\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/union/utils/_subIssues/_subIssues.ts\nfunction _subIssues(datasets) {\n  let issues;\n  if (datasets) {\n    for (const dataset of datasets) {\n      if (issues) {\n        issues.push(...dataset.issues);\n      } else {\n        issues = dataset.issues;\n      }\n    }\n  }\n  return issues;\n}\n\n// src/schemas/union/union.ts\nfunction union(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"union\",\n    reference: union,\n    expects: [...new Set(options.map((option) => option.expects))].join(\" | \") || \"never\",\n    async: false,\n    options,\n    message,\n    _run(dataset, config2) {\n      let validDataset;\n      let typedDatasets;\n      let untypedDatasets;\n      for (const schema of this.options) {\n        const optionDataset = schema._run(\n          { typed: false, value: dataset.value },\n          config2\n        );\n        if (optionDataset.typed) {\n          if (optionDataset.issues) {\n            if (typedDatasets) {\n              typedDatasets.push(optionDataset);\n            } else {\n              typedDatasets = [optionDataset];\n            }\n          } else {\n            validDataset = optionDataset;\n            break;\n          }\n        } else {\n          if (untypedDatasets) {\n            untypedDatasets.push(optionDataset);\n          } else {\n            untypedDatasets = [optionDataset];\n          }\n        }\n      }\n      if (validDataset) {\n        return validDataset;\n      }\n      if (typedDatasets) {\n        if (typedDatasets.length === 1) {\n          return typedDatasets[0];\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(typedDatasets)\n        });\n        dataset.typed = true;\n      } else if (untypedDatasets?.length === 1) {\n        return untypedDatasets[0];\n      } else {\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(untypedDatasets)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/union/unionAsync.ts\nfunction unionAsync(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"union\",\n    reference: unionAsync,\n    expects: [...new Set(options.map((option) => option.expects))].join(\" | \") || \"never\",\n    async: true,\n    options,\n    message,\n    async _run(dataset, config2) {\n      let validDataset;\n      let typedDatasets;\n      let untypedDatasets;\n      for (const schema of this.options) {\n        const optionDataset = await schema._run(\n          { typed: false, value: dataset.value },\n          config2\n        );\n        if (optionDataset.typed) {\n          if (optionDataset.issues) {\n            if (typedDatasets) {\n              typedDatasets.push(optionDataset);\n            } else {\n              typedDatasets = [optionDataset];\n            }\n          } else {\n            validDataset = optionDataset;\n            break;\n          }\n        } else {\n          if (untypedDatasets) {\n            untypedDatasets.push(optionDataset);\n          } else {\n            untypedDatasets = [optionDataset];\n          }\n        }\n      }\n      if (validDataset) {\n        return validDataset;\n      }\n      if (typedDatasets) {\n        if (typedDatasets.length === 1) {\n          return typedDatasets[0];\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(typedDatasets)\n        });\n        dataset.typed = true;\n      } else if (untypedDatasets?.length === 1) {\n        return untypedDatasets[0];\n      } else {\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(untypedDatasets)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/unknown/unknown.ts\nfunction unknown() {\n  return {\n    kind: \"schema\",\n    type: \"unknown\",\n    reference: unknown,\n    expects: \"unknown\",\n    async: false,\n    _run(dataset) {\n      dataset.typed = true;\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/variant/utils/_discriminators/_discriminators.ts\nfunction _discriminators(key, options, set2 = /* @__PURE__ */ new Set()) {\n  for (const schema of options) {\n    if (schema.type === \"variant\") {\n      _discriminators(key, schema.options, set2);\n    } else {\n      set2.add(schema.entries[key].expects);\n    }\n  }\n  return set2;\n}\n\n// src/schemas/variant/variant.ts\nfunction variant(key, options, message) {\n  let expectedDiscriminators;\n  return {\n    kind: \"schema\",\n    type: \"variant\",\n    reference: variant,\n    expects: \"Object\",\n    async: false,\n    key,\n    options,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        const discriminator = input[this.key];\n        if (this.key in input) {\n          let outputDataset;\n          for (const schema of this.options) {\n            if (schema.type === \"variant\" || !schema.entries[this.key]._run(\n              { typed: false, value: discriminator },\n              config2\n            ).issues) {\n              const optionDataset = schema._run(\n                { typed: false, value: input },\n                config2\n              );\n              if (!optionDataset.issues) {\n                return optionDataset;\n              }\n              if (!outputDataset || !outputDataset.typed && optionDataset.typed) {\n                outputDataset = optionDataset;\n              }\n            }\n          }\n          if (outputDataset) {\n            return outputDataset;\n          }\n        }\n        if (!expectedDiscriminators) {\n          expectedDiscriminators = [..._discriminators(this.key, this.options)].join(\" | \") || \"never\";\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          input: discriminator,\n          expected: expectedDiscriminators,\n          path: [\n            {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key: this.key,\n              value: discriminator\n            }\n          ]\n        });\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/variant/variantAsync.ts\nfunction variantAsync(key, options, message) {\n  let expectedDiscriminators;\n  return {\n    kind: \"schema\",\n    type: \"variant\",\n    reference: variantAsync,\n    expects: \"Object\",\n    async: true,\n    key,\n    options,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        const discriminator = input[this.key];\n        if (this.key in input) {\n          let outputDataset;\n          for (const schema of this.options) {\n            if (schema.type === \"variant\" || !(await schema.entries[this.key]._run(\n              { typed: false, value: discriminator },\n              config2\n            )).issues) {\n              const optionDataset = await schema._run(\n                { typed: false, value: input },\n                config2\n              );\n              if (!optionDataset.issues) {\n                return optionDataset;\n              }\n              if (!outputDataset || !outputDataset.typed && optionDataset.typed) {\n                outputDataset = optionDataset;\n              }\n            }\n          }\n          if (outputDataset) {\n            return outputDataset;\n          }\n        }\n        if (!expectedDiscriminators) {\n          expectedDiscriminators = [..._discriminators(this.key, this.options)].join(\" | \") || \"never\";\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          input: discriminator,\n          expected: expectedDiscriminators,\n          path: [\n            {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key: this.key,\n              value: discriminator\n            }\n          ]\n        });\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/void/void.ts\nfunction void_(message) {\n  return {\n    kind: \"schema\",\n    type: \"void\",\n    reference: void_,\n    expects: \"void\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/keyof/keyof.ts\nfunction keyof(schema, message) {\n  return picklist(Object.keys(schema.entries), message);\n}\n\n// src/methods/omit/omit.ts\nfunction omit(schema, keys) {\n  const entries = {\n    ...schema.entries\n  };\n  for (const key of keys) {\n    delete entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/parse/parse.ts\nfunction parse(schema, input, config2) {\n  const dataset = schema._run(\n    { typed: false, value: input },\n    getGlobalConfig(config2)\n  );\n  if (dataset.issues) {\n    throw new ValiError(dataset.issues);\n  }\n  return dataset.value;\n}\n\n// src/methods/parse/parseAsync.ts\nasync function parseAsync(schema, input, config2) {\n  const dataset = await schema._run(\n    { typed: false, value: input },\n    getGlobalConfig(config2)\n  );\n  if (dataset.issues) {\n    throw new ValiError(dataset.issues);\n  }\n  return dataset.value;\n}\n\n// src/methods/parser/parser.ts\nfunction parser(schema, config2) {\n  const func = (input) => parse(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/parser/parserAsync.ts\nfunction parserAsync(schema, config2) {\n  const func = (input) => parseAsync(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/partial/partial.ts\nfunction partial(schema, keys) {\n  const entries = {};\n  for (const key in schema.entries) {\n    entries[key] = !keys || keys.includes(key) ? optional(schema.entries[key]) : schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/partial/partialAsync.ts\nfunction partialAsync(schema, keys) {\n  const entries = {};\n  for (const key in schema.entries) {\n    entries[key] = !keys || keys.includes(key) ? optionalAsync(schema.entries[key]) : schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/pick/pick.ts\nfunction pick(schema, keys) {\n  const entries = {};\n  for (const key of keys) {\n    entries[key] = schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/pipe/pipe.ts\nfunction pipe(...pipe2) {\n  return {\n    ...pipe2[0],\n    pipe: pipe2,\n    _run(dataset, config2) {\n      for (let index = 0; index < pipe2.length; index++) {\n        dataset = pipe2[index]._run(dataset, config2);\n        const nextAction = pipe2[index + 1];\n        if (config2.skipPipe || dataset.issues && (config2.abortEarly || config2.abortPipeEarly || // TODO: This behavior must be documented!\n        nextAction?.kind === \"schema\" || nextAction?.kind === \"transformation\")) {\n          dataset.typed = false;\n          break;\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/pipe/pipeAsync.ts\nfunction pipeAsync(...pipe2) {\n  return {\n    ...pipe2[0],\n    pipe: pipe2,\n    async: true,\n    async _run(dataset, config2) {\n      for (let index = 0; index < pipe2.length; index++) {\n        dataset = await pipe2[index]._run(dataset, config2);\n        const nextAction = pipe2[index + 1];\n        if (config2.skipPipe || dataset.issues && (config2.abortEarly || config2.abortPipeEarly || // TODO: This behavior must be documented!\n        nextAction?.kind === \"schema\" || nextAction?.kind === \"transformation\")) {\n          dataset.typed = false;\n          break;\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/required/required.ts\nfunction required(schema, arg2, arg3) {\n  const keys = Array.isArray(arg2) ? arg2 : void 0;\n  const message = Array.isArray(arg2) ? arg3 : arg2;\n  const entries = {};\n  for (const key in schema.entries) {\n    entries[key] = !keys || keys.includes(key) ? nonOptional(schema.entries[key], message) : schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/required/requiredAsync.ts\nfunction requiredAsync(schema, arg2, arg3) {\n  const keys = Array.isArray(arg2) ? arg2 : void 0;\n  const message = Array.isArray(arg2) ? arg3 : arg2;\n  const entries = {};\n  for (const key in schema.entries) {\n    entries[key] = !keys || keys.includes(key) ? nonOptionalAsync(schema.entries[key], message) : schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/safeParse/safeParse.ts\nfunction safeParse(schema, input, config2) {\n  const dataset = schema._run(\n    { typed: false, value: input },\n    getGlobalConfig(config2)\n  );\n  return {\n    typed: dataset.typed,\n    success: !dataset.issues,\n    output: dataset.value,\n    issues: dataset.issues\n  };\n}\n\n// src/methods/safeParse/safeParseAsync.ts\nasync function safeParseAsync(schema, input, config2) {\n  const dataset = await schema._run(\n    { typed: false, value: input },\n    getGlobalConfig(config2)\n  );\n  return {\n    typed: dataset.typed,\n    success: !dataset.issues,\n    output: dataset.value,\n    issues: dataset.issues\n  };\n}\n\n// src/methods/safeParser/safeParser.ts\nfunction safeParser(schema, config2) {\n  const func = (input) => safeParse(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/safeParser/safeParserAsync.ts\nfunction safeParserAsync(schema, config2) {\n  const func = (input) => safeParseAsync(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/unwrap/unwrap.ts\nfunction unwrap(schema) {\n  return schema.wrapped;\n}\nexport {\n  BIC_REGEX,\n  CUID2_REGEX,\n  DECIMAL_REGEX,\n  EMAIL_REGEX,\n  EMOJI_REGEX,\n  HEXADECIMAL_REGEX,\n  HEX_COLOR_REGEX,\n  IMEI_REGEX,\n  IPV4_REGEX,\n  IPV6_REGEX,\n  IP_REGEX,\n  ISO_DATE_REGEX,\n  ISO_DATE_TIME_REGEX,\n  ISO_TIMESTAMP_REGEX,\n  ISO_TIME_REGEX,\n  ISO_TIME_SECOND_REGEX,\n  ISO_WEEK_REGEX,\n  MAC48_REGEX,\n  MAC64_REGEX,\n  MAC_REGEX,\n  OCTAL_REGEX,\n  ULID_REGEX,\n  UUID_REGEX,\n  ValiError,\n  _addIssue,\n  _isAllowedObjectKey,\n  _isLuhnAlgo,\n  _stringify,\n  any,\n  array,\n  arrayAsync,\n  bic,\n  bigint,\n  blob,\n  boolean,\n  brand,\n  bytes,\n  check,\n  checkAsync,\n  config,\n  creditCard,\n  cuid2,\n  custom,\n  customAsync,\n  date,\n  decimal,\n  deleteGlobalConfig,\n  deleteGlobalMessage,\n  deleteSchemaMessage,\n  deleteSpecificMessage,\n  email,\n  emoji,\n  empty,\n  endsWith,\n  entriesFromList,\n  enum_,\n  every,\n  excludes,\n  fallback,\n  fallbackAsync,\n  finite,\n  flatten,\n  forward,\n  forwardAsync,\n  getDefault,\n  getDefaults,\n  getDefaultsAsync,\n  getDotPath,\n  getFallback,\n  getFallbacks,\n  getFallbacksAsync,\n  getGlobalConfig,\n  getGlobalMessage,\n  getSchemaMessage,\n  getSpecificMessage,\n  hash,\n  hexColor,\n  hexadecimal,\n  imei,\n  includes,\n  instance,\n  integer,\n  intersect,\n  intersectAsync,\n  ip,\n  ipv4,\n  ipv6,\n  is,\n  isOfKind,\n  isOfType,\n  isValiError,\n  isoDate,\n  isoDateTime,\n  isoTime,\n  isoTimeSecond,\n  isoTimestamp,\n  isoWeek,\n  keyof,\n  lazy,\n  lazyAsync,\n  length,\n  literal,\n  looseObject,\n  looseObjectAsync,\n  looseTuple,\n  looseTupleAsync,\n  mac,\n  mac48,\n  mac64,\n  map,\n  mapAsync,\n  maxBytes,\n  maxLength,\n  maxSize,\n  maxValue,\n  mimeType,\n  minBytes,\n  minLength,\n  minSize,\n  minValue,\n  multipleOf,\n  nan,\n  never,\n  nonEmpty,\n  nonNullable,\n  nonNullableAsync,\n  nonNullish,\n  nonNullishAsync,\n  nonOptional,\n  nonOptionalAsync,\n  notBytes,\n  notLength,\n  notSize,\n  notValue,\n  null_,\n  nullable,\n  nullableAsync,\n  nullish,\n  nullishAsync,\n  number,\n  object,\n  objectAsync,\n  objectWithRest,\n  objectWithRestAsync,\n  octal,\n  omit,\n  optional,\n  optionalAsync,\n  parse,\n  parseAsync,\n  parser,\n  parserAsync,\n  partial,\n  partialAsync,\n  pick,\n  picklist,\n  pipe,\n  pipeAsync,\n  readonly,\n  record,\n  recordAsync,\n  regex,\n  required,\n  requiredAsync,\n  safeInteger,\n  safeParse,\n  safeParseAsync,\n  safeParser,\n  safeParserAsync,\n  set,\n  setAsync,\n  setGlobalConfig,\n  setGlobalMessage,\n  setSchemaMessage,\n  setSpecificMessage,\n  size,\n  some,\n  startsWith,\n  strictObject,\n  strictObjectAsync,\n  strictTuple,\n  strictTupleAsync,\n  string,\n  symbol,\n  toLowerCase,\n  toMaxValue,\n  toMinValue,\n  toUpperCase,\n  transform,\n  transformAsync,\n  trim,\n  trimEnd,\n  trimStart,\n  tuple,\n  tupleAsync,\n  tupleWithRest,\n  tupleWithRestAsync,\n  ulid,\n  undefined_,\n  union,\n  unionAsync,\n  unknown,\n  unwrap,\n  url,\n  uuid,\n  value,\n  variant,\n  variantAsync,\n  void_\n};\n", "// src/extension/withJSONSchemaFeatures.ts\nvar JSON_SCHEMA_FEATURES_KEY = \"__json_schema_features\";\nfunction withJSONSchemaFeatures(schema, features) {\n  return Object.assign(schema, { [JSON_SCHEMA_FEATURES_KEY]: features });\n}\nfunction getJSONSchemaFeatures(schema) {\n  return schema[JSON_SCHEMA_FEATURES_KEY];\n}\n\n// src/extension/assignExtraJSONSchemaFeatures.ts\nfunction assignExtraJSONSchemaFeatures(schema, converted) {\n  const jsonSchemaFeatures = getJSONSchemaFeatures(schema);\n  if (jsonSchemaFeatures) {\n    Object.assign(converted, jsonSchemaFeatures);\n  }\n}\n\n// src/utils/assert.ts\nfunction assert(value, predicate, message) {\n  if (!predicate(value)) throw new Error(message.replace(\"%\", String(value)));\n  return value;\n}\n\n// src/utils/json-schema.ts\nvar $schema = \"http://json-schema.org/draft-07/schema#\";\nfunction isJSONLiteral(value) {\n  return typeof value === \"number\" && !Number.isNaN(value) || typeof value === \"string\" || typeof value === \"boolean\" || value === null;\n}\nvar assertJSONLiteral = (v) => assert(v, isJSONLiteral, \"Unsupported literal value type: %\");\n\n// src/toJSONSchema/schemas.ts\nimport {\n  getDefault,\n  never\n} from \"valibot\";\n\n// src/utils/isEqual.ts\nfunction isEqual(obj1, obj2) {\n  if (obj1 === obj2) return true;\n  if (typeof obj1 === \"object\" && typeof obj2 === \"object\") {\n    const keys1 = Object.keys(obj1);\n    const keys2 = Object.keys(obj2);\n    if (keys1.length !== keys2.length) return false;\n    return keys1.every((key1) => isEqual(obj1[key1], obj2[key1]));\n  }\n  return false;\n}\n\n// src/utils/valibot.ts\nfunction isSchemaType(type) {\n  return (schema) => {\n    return !!schema && schema.type === type;\n  };\n}\nvar isNullishSchema = isSchemaType(\"nullish\");\nvar isOptionalSchema = isSchemaType(\"optional\");\nvar isStringSchema = isSchemaType(\"string\");\nvar isNeverSchema = isSchemaType(\"never\");\n\n// src/toJSONSchema/toDefinitionURI.ts\nvar toDefinitionURI = (name) => `#/definitions/${name}`;\n\n// src/toJSONSchema/schemas.ts\nvar SCHEMA_CONVERTERS = {\n  any: () => ({}),\n  // Core types\n  null: () => ({ const: null }),\n  literal: ({ literal }) => ({ const: assertJSONLiteral(literal) }),\n  number: () => ({ type: \"number\" }),\n  string: () => ({ type: \"string\" }),\n  boolean: () => ({ type: \"boolean\" }),\n  // Compositions\n  optional: (schema, convert) => {\n    const output = convert(schema.wrapped);\n    const defaultValue = getDefault(schema);\n    if (defaultValue !== void 0) output.default = defaultValue;\n    return output;\n  },\n  nullish: (schema, convert) => {\n    const output = { anyOf: [{ const: null }, convert(schema.wrapped)] };\n    const defaultValue = getDefault(schema);\n    if (defaultValue !== void 0) output.default = defaultValue;\n    return output;\n  },\n  nullable: (schema, convert) => {\n    const output = { anyOf: [{ const: null }, convert(schema.wrapped)] };\n    const defaultValue = getDefault(schema);\n    if (defaultValue !== void 0) output.default = defaultValue;\n    return output;\n  },\n  picklist: ({ options }) => ({ enum: options.map(assertJSONLiteral) }),\n  enum: (options) => ({ enum: Object.values(options.enum).map(assertJSONLiteral) }),\n  union: ({ options }, convert) => ({ anyOf: options.map(convert) }),\n  intersect: ({ options }, convert) => ({ allOf: options.map(convert) }),\n  // Complex types\n  array: ({ item }, convert) => ({ type: \"array\", items: convert(item) }),\n  tuple_with_rest({ items: originalItems, rest }, convert) {\n    const minItems = originalItems.length;\n    let maxItems;\n    let items = originalItems.map(convert);\n    let additionalItems;\n    if (isNeverSchema(rest)) {\n      maxItems = minItems;\n    } else if (rest) {\n      const restItems = convert(rest);\n      if (items.length === 1 && isEqual(items[0], restItems)) {\n        items = items[0];\n      } else {\n        additionalItems = restItems;\n      }\n    }\n    return {\n      type: \"array\",\n      items,\n      ...additionalItems && { additionalItems },\n      ...minItems && { minItems },\n      ...maxItems && { maxItems }\n    };\n  },\n  strict_tuple({ items: originalItems }, convert) {\n    const items = originalItems.map(convert);\n    return { type: \"array\", items, minItems: items.length, maxItems: items.length };\n  },\n  tuple({ items: originalItems }, convert, context) {\n    const items = originalItems.map(convert);\n    return { type: \"array\", items, minItems: items.length };\n  },\n  object_with_rest({ entries, rest }, convert, context) {\n    const properties = {};\n    const required = [];\n    for (const [propKey, propValue] of Object.entries(entries)) {\n      const propSchema = propValue;\n      if (!isOptionalSchema(propSchema) && !isNullishSchema(propSchema)) {\n        required.push(propKey);\n      }\n      properties[propKey] = convert(propSchema);\n      assignExtraJSONSchemaFeatures(propValue, properties[propKey]);\n    }\n    let additionalProperties;\n    if (rest) {\n      additionalProperties = isNeverSchema(rest) ? false : convert(rest);\n    } else if (context.strictObjectTypes) {\n      additionalProperties = false;\n    }\n    const output = { type: \"object\", properties };\n    if (additionalProperties !== void 0) output.additionalProperties = additionalProperties;\n    if (required.length) output.required = required;\n    return output;\n  },\n  object(schema, convert, context) {\n    return SCHEMA_CONVERTERS.object_with_rest(schema, convert, context);\n  },\n  strict_object(schema, convert, context) {\n    return SCHEMA_CONVERTERS.object_with_rest({ ...schema, rest: never() }, convert, context);\n  },\n  record({ key, value }, convert) {\n    assert(key, isStringSchema, \"Unsupported record key type: %\");\n    return { type: \"object\", additionalProperties: convert(value) };\n  },\n  lazy(schema, _, context) {\n    const nested = schema.getter({});\n    const defName = context.defNameMap.get(nested);\n    if (!defName) {\n      throw new Error(\"Type inside lazy schema must be provided in the definitions\");\n    }\n    return { $ref: toDefinitionURI(defName) };\n  },\n  date(_, __, context) {\n    if (!context.dateStrategy) {\n      throw new Error('The \"dateStrategy\" option must be set to handle date validators');\n    }\n    switch (context.dateStrategy) {\n      case \"integer\":\n        return { type: \"integer\", format: \"unix-time\" };\n      case \"string\":\n        return { type: \"string\", format: \"date-time\" };\n    }\n  },\n  undefined(_, __, context) {\n    if (!context.undefinedStrategy) {\n      throw new Error('The \"undefinedStrategy\" option must be set to handle the `undefined` schema');\n    }\n    switch (context.undefinedStrategy) {\n      case \"any\":\n        return {};\n      case \"null\":\n        return { type: \"null\" };\n    }\n  },\n  bigint(_, __, context) {\n    if (!context.bigintStrategy) {\n      throw new Error('The \"bigintStrategy\" option must be set to handle `bigint` validators');\n    }\n    switch (context.bigintStrategy) {\n      case \"integer\":\n        return { type: \"integer\", format: \"int64\" };\n      case \"string\":\n        return { type: \"string\" };\n    }\n  },\n  variant({ options }, ...args) {\n    return SCHEMA_CONVERTERS.union({ options }, ...args);\n  }\n};\n\n// src/toJSONSchema/validations.ts\nvar VALIDATION_BY_SCHEMA = {\n  array: {\n    length: ({ requirement }) => ({ minItems: requirement, maxItems: requirement }),\n    min_length: ({ requirement }) => ({ minItems: requirement }),\n    max_length: ({ requirement }) => ({ maxItems: requirement })\n  },\n  string: {\n    value: ({ requirement }) => ({ const: requirement }),\n    length: ({ requirement }) => ({ minLength: requirement, maxLength: requirement }),\n    min_length: ({ requirement }) => ({ minLength: requirement }),\n    max_length: ({ requirement }) => ({ maxLength: requirement }),\n    // TODO: validate RegExp features are compatible with json schema ?\n    regex: ({ requirement }) => ({ pattern: requirement.source }),\n    email: () => ({ format: \"email\" }),\n    iso_date: () => ({ format: \"date\" }),\n    iso_timestamp: () => ({ format: \"date-time\" }),\n    ipv4: () => ({ format: \"ipv4\" }),\n    ipv6: () => ({ format: \"ipv6\" }),\n    uuid: () => ({ format: \"uuid\" })\n  },\n  number: {\n    value: ({ requirement }) => ({ const: requirement }),\n    min_value: ({ requirement }) => ({ minimum: requirement }),\n    max_value: ({ requirement }) => ({ maximum: requirement }),\n    multiple_of: ({ requirement }) => ({ multipleOf: requirement }),\n    integer: () => ({ type: \"integer\" })\n  },\n  boolean: {\n    value: ({ requirement }) => ({ const: requirement })\n  },\n  date: {\n    value: ({ requirement }, context) => ({ const: asDateRequirement(\"value\", requirement, context) }),\n    min_value: ({ requirement }, context) => ({ minimum: asDateRequirement(\"minValue\", requirement, context) }),\n    max_value: ({ requirement }, context) => ({ maximum: asDateRequirement(\"maxValue\", requirement, context) })\n  }\n};\nfunction asDateRequirement(type, requirement, context) {\n  assert(requirement, () => context.dateStrategy === \"integer\", `${type} validation is only available with 'integer' date strategy`);\n  assert(requirement, (r) => r instanceof Date, `Non-date value used for ${type} validation`);\n  return requirement.getTime();\n}\nfunction convertPipe(schemaType, pipe, context) {\n  const [schema, ...pipeItems] = pipe || [];\n  if (!schema) return {};\n  const childPipe = convertPipe(schemaType, schema == null ? void 0 : schema.pipe, context);\n  function convertPipeItem(def, validation) {\n    var _a, _b, _c;\n    const validationType = validation.type;\n    const validationConverter = ((_b = (_a = context.customValidationConversion) == null ? void 0 : _a[schemaType]) == null ? void 0 : _b[validationType]) || ((_c = VALIDATION_BY_SCHEMA[schemaType]) == null ? void 0 : _c[validationType]);\n    if (!validationConverter && context.ignoreUnknownValidation) return {};\n    assert(validationConverter, Boolean, `Unsupported valibot validation \\`${validationType}\\` for schema \\`${schemaType}\\``);\n    const converted = validationConverter(validation, context);\n    return Object.assign(def, converted);\n  }\n  return pipeItems.reduce(convertPipeItem, childPipe);\n}\n\n// src/toJSONSchema/index.ts\nfunction getDefNameMap(definitions = {}) {\n  const map = /* @__PURE__ */ new Map();\n  for (const [name, definition] of Object.entries(definitions)) {\n    map.set(definition, name);\n  }\n  return map;\n}\nfunction createConverter(context) {\n  const definitions = {};\n  function converter(schema) {\n    var _a;\n    const defName = context.defNameMap.get(schema);\n    const defURI = defName && toDefinitionURI(defName);\n    if (defURI && defURI in definitions) {\n      return { $ref: defURI };\n    }\n    const schemaConverter = ((_a = context.customSchemaConversion) == null ? void 0 : _a[schema.type]) || SCHEMA_CONVERTERS[schema.type];\n    assert(schemaConverter, Boolean, `Unsupported valibot schema: ${(schema == null ? void 0 : schema.type) || schema}`);\n    let converted = schemaConverter(schema, converter, context) || {};\n    const convertedValidation = convertPipe(schema.type, schema.pipe, context);\n    converted = { ...converted, ...convertedValidation };\n    assignExtraJSONSchemaFeatures(schema, converted);\n    if (defURI) {\n      definitions[defName] = converted;\n      return { $ref: defURI };\n    }\n    return converted;\n  }\n  return { definitions, converter };\n}\nfunction toJSONSchema(options) {\n  const { schema, definitions: inputDefinitions, ...more } = options;\n  const defNameMap = getDefNameMap(inputDefinitions);\n  const { definitions, converter } = createConverter({ defNameMap, ...more });\n  if (!schema && !inputDefinitions) {\n    throw new Error(\"No main schema or definitions provided.\");\n  }\n  if (inputDefinitions) {\n    Object.values(inputDefinitions).forEach(converter);\n  }\n  const mainConverted = schema && converter(schema);\n  const mainDefName = schema && defNameMap.get(schema);\n  const out = { $schema };\n  if (mainDefName) {\n    out.$ref = toDefinitionURI(mainDefName);\n  } else {\n    Object.assign(out, mainConverted);\n  }\n  if (Object.keys(definitions).length) {\n    out.definitions = definitions;\n  }\n  return out;\n}\nexport {\n  toJSONSchema,\n  withJSONSchemaFeatures\n};\n"], "mappings": ";AA+CA,IAAI;AAMJ,SAAS,iBAAiB,MAAM;AAC9B,SAAO,iCAAQ,IAAI;AACrB;AAMA,IAAI;AAMJ,SAAS,iBAAiB,MAAM;AAC9B,SAAO,iCAAQ,IAAI;AACrB;AAMA,IAAI;AAQJ,SAAS,mBAAmB,WAAW,MAAM;AAnF7C;AAoFE,UAAO,sCAAQ,IAAI,eAAZ,mBAAwB,IAAI;AACrC;AAMA,SAAS,WAAW,OAAO;AA3F3B;AA4FE,MAAI,OAAO,OAAO;AAClB,MAAI,SAAS,UAAU;AACrB,YAAQ,WAAS,kBAAO,eAAe,KAAK,MAA3B,mBAA8B,gBAA9B,mBAA2C,UAAS;AAAA,EACvE;AACA,SAAO,SAAS,WAAW,IAAI,KAAK,MAAM,SAAS,YAAY,SAAS,YAAY,SAAS,YAAY,GAAG,KAAK,KAAK;AACxH;AAGA,SAAS,UAAU,SAAS,OAAO,SAAS,SAAS,OAAO;AAC1D,QAAM,QAAQ,SAAS,WAAW,QAAQ,MAAM,QAAQ,QAAQ;AAChE,QAAM,YAAW,+BAAO,aAAY,QAAQ;AAC5C,QAAM,YAAW,+BAAO,aAAY,WAAW,KAAK;AACpD,QAAM,QAAQ;AAAA,IACZ,MAAM,QAAQ;AAAA,IACd,MAAM,QAAQ;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,WAAW,KAAK,KAAK,WAAW,YAAY,QAAQ,WAAW,GAAG,WAAW,QAAQ;AAAA;AAAA,IAE9F,aAAa,QAAQ;AAAA,IACrB,MAAM,+BAAO;AAAA,IACb,QAAQ,+BAAO;AAAA,IACf,MAAM,QAAQ;AAAA,IACd,YAAY,QAAQ;AAAA,IACpB,gBAAgB,QAAQ;AAAA,IACxB,UAAU,QAAQ;AAAA,EACpB;AACA,QAAM,WAAW,QAAQ,SAAS;AAClC,QAAM;AAAA;AAAA,IAEJ,QAAQ,WAAW,mBAAmB,QAAQ,WAAW,MAAM,IAAI,MAAM,WAAW,iBAAiB,MAAM,IAAI,IAAI,SAAS,QAAQ,WAAW,iBAAiB,MAAM,IAAI;AAAA;AAE5K,MAAI,SAAS;AACX,UAAM,UAAU,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AAAA,EACnE;AACA,MAAI,UAAU;AACZ,YAAQ,QAAQ;AAAA,EAClB;AACA,MAAI,QAAQ,QAAQ;AAClB,YAAQ,OAAO,KAAK,KAAK;AAAA,EAC3B,OAAO;AACL,YAAQ,SAAS,CAAC,KAAK;AAAA,EACzB;AACF;AA2kDA,SAAS,WAAW,QAAQ,SAAS,SAAS;AAC5C,SAAO,OAAO,OAAO,YAAY;AAAA;AAAA,IAE/B,OAAO,QAAQ,SAAS,OAAO;AAAA;AAAA;AAAA,IAG/B,OAAO;AAAA;AAEX;AA0hCA,SAAS,MAAM,SAAS;AACtB,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,IACP;AAAA,IACA,KAAK,SAAS,SAAS;AACrB,gBAAU,MAAM,QAAQ,SAAS,OAAO;AACxC,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;ACjwFA,IAAI,2BAA2B;AAC/B,SAAS,uBAAuB,QAAQ,UAAU;AAChD,SAAO,OAAO,OAAO,QAAQ,EAAE,CAAC,wBAAwB,GAAG,SAAS,CAAC;AACvE;AACA,SAAS,sBAAsB,QAAQ;AACrC,SAAO,OAAO,wBAAwB;AACxC;AAGA,SAAS,8BAA8B,QAAQ,WAAW;AACxD,QAAM,qBAAqB,sBAAsB,MAAM;AACvD,MAAI,oBAAoB;AACtB,WAAO,OAAO,WAAW,kBAAkB;AAAA,EAC7C;AACF;AAGA,SAAS,OAAO,OAAO,WAAW,SAAS;AACzC,MAAI,CAAC,UAAU,KAAK,EAAG,OAAM,IAAI,MAAM,QAAQ,QAAQ,KAAK,OAAO,KAAK,CAAC,CAAC;AAC1E,SAAO;AACT;AAGA,IAAI,UAAU;AACd,SAAS,cAAc,OAAO;AAC5B,SAAO,OAAO,UAAU,YAAY,CAAC,OAAO,MAAM,KAAK,KAAK,OAAO,UAAU,YAAY,OAAO,UAAU,aAAa,UAAU;AACnI;AACA,IAAI,oBAAoB,CAAC,MAAM,OAAO,GAAG,eAAe,mCAAmC;AAS3F,SAAS,QAAQ,MAAM,MAAM;AAC3B,MAAI,SAAS,KAAM,QAAO;AAC1B,MAAI,OAAO,SAAS,YAAY,OAAO,SAAS,UAAU;AACxD,UAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,UAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,QAAI,MAAM,WAAW,MAAM,OAAQ,QAAO;AAC1C,WAAO,MAAM,MAAM,CAAC,SAAS,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;AAAA,EAC9D;AACA,SAAO;AACT;AAGA,SAAS,aAAa,MAAM;AAC1B,SAAO,CAAC,WAAW;AACjB,WAAO,CAAC,CAAC,UAAU,OAAO,SAAS;AAAA,EACrC;AACF;AACA,IAAI,kBAAkB,aAAa,SAAS;AAC5C,IAAI,mBAAmB,aAAa,UAAU;AAC9C,IAAI,iBAAiB,aAAa,QAAQ;AAC1C,IAAI,gBAAgB,aAAa,OAAO;AAGxC,IAAI,kBAAkB,CAAC,SAAS,iBAAiB,IAAI;AAGrD,IAAI,oBAAoB;AAAA,EACtB,KAAK,OAAO,CAAC;AAAA;AAAA,EAEb,MAAM,OAAO,EAAE,OAAO,KAAK;AAAA,EAC3B,SAAS,CAAC,EAAE,QAAQ,OAAO,EAAE,OAAO,kBAAkB,OAAO,EAAE;AAAA,EAC/D,QAAQ,OAAO,EAAE,MAAM,SAAS;AAAA,EAChC,QAAQ,OAAO,EAAE,MAAM,SAAS;AAAA,EAChC,SAAS,OAAO,EAAE,MAAM,UAAU;AAAA;AAAA,EAElC,UAAU,CAAC,QAAQ,YAAY;AAC7B,UAAM,SAAS,QAAQ,OAAO,OAAO;AACrC,UAAM,eAAe,WAAW,MAAM;AACtC,QAAI,iBAAiB,OAAQ,QAAO,UAAU;AAC9C,WAAO;AAAA,EACT;AAAA,EACA,SAAS,CAAC,QAAQ,YAAY;AAC5B,UAAM,SAAS,EAAE,OAAO,CAAC,EAAE,OAAO,KAAK,GAAG,QAAQ,OAAO,OAAO,CAAC,EAAE;AACnE,UAAM,eAAe,WAAW,MAAM;AACtC,QAAI,iBAAiB,OAAQ,QAAO,UAAU;AAC9C,WAAO;AAAA,EACT;AAAA,EACA,UAAU,CAAC,QAAQ,YAAY;AAC7B,UAAM,SAAS,EAAE,OAAO,CAAC,EAAE,OAAO,KAAK,GAAG,QAAQ,OAAO,OAAO,CAAC,EAAE;AACnE,UAAM,eAAe,WAAW,MAAM;AACtC,QAAI,iBAAiB,OAAQ,QAAO,UAAU;AAC9C,WAAO;AAAA,EACT;AAAA,EACA,UAAU,CAAC,EAAE,QAAQ,OAAO,EAAE,MAAM,QAAQ,IAAI,iBAAiB,EAAE;AAAA,EACnE,MAAM,CAAC,aAAa,EAAE,MAAM,OAAO,OAAO,QAAQ,IAAI,EAAE,IAAI,iBAAiB,EAAE;AAAA,EAC/E,OAAO,CAAC,EAAE,QAAQ,GAAG,aAAa,EAAE,OAAO,QAAQ,IAAI,OAAO,EAAE;AAAA,EAChE,WAAW,CAAC,EAAE,QAAQ,GAAG,aAAa,EAAE,OAAO,QAAQ,IAAI,OAAO,EAAE;AAAA;AAAA,EAEpE,OAAO,CAAC,EAAE,KAAK,GAAG,aAAa,EAAE,MAAM,SAAS,OAAO,QAAQ,IAAI,EAAE;AAAA,EACrE,gBAAgB,EAAE,OAAO,eAAe,KAAK,GAAG,SAAS;AACvD,UAAM,WAAW,cAAc;AAC/B,QAAI;AACJ,QAAI,QAAQ,cAAc,IAAI,OAAO;AACrC,QAAI;AACJ,QAAI,cAAc,IAAI,GAAG;AACvB,iBAAW;AAAA,IACb,WAAW,MAAM;AACf,YAAM,YAAY,QAAQ,IAAI;AAC9B,UAAI,MAAM,WAAW,KAAK,QAAQ,MAAM,CAAC,GAAG,SAAS,GAAG;AACtD,gBAAQ,MAAM,CAAC;AAAA,MACjB,OAAO;AACL,0BAAkB;AAAA,MACpB;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA,GAAG,mBAAmB,EAAE,gBAAgB;AAAA,MACxC,GAAG,YAAY,EAAE,SAAS;AAAA,MAC1B,GAAG,YAAY,EAAE,SAAS;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,aAAa,EAAE,OAAO,cAAc,GAAG,SAAS;AAC9C,UAAM,QAAQ,cAAc,IAAI,OAAO;AACvC,WAAO,EAAE,MAAM,SAAS,OAAO,UAAU,MAAM,QAAQ,UAAU,MAAM,OAAO;AAAA,EAChF;AAAA,EACA,MAAM,EAAE,OAAO,cAAc,GAAG,SAAS,SAAS;AAChD,UAAM,QAAQ,cAAc,IAAI,OAAO;AACvC,WAAO,EAAE,MAAM,SAAS,OAAO,UAAU,MAAM,OAAO;AAAA,EACxD;AAAA,EACA,iBAAiB,EAAE,SAAS,KAAK,GAAG,SAAS,SAAS;AACpD,UAAM,aAAa,CAAC;AACpB,UAAM,WAAW,CAAC;AAClB,eAAW,CAAC,SAAS,SAAS,KAAK,OAAO,QAAQ,OAAO,GAAG;AAC1D,YAAM,aAAa;AACnB,UAAI,CAAC,iBAAiB,UAAU,KAAK,CAAC,gBAAgB,UAAU,GAAG;AACjE,iBAAS,KAAK,OAAO;AAAA,MACvB;AACA,iBAAW,OAAO,IAAI,QAAQ,UAAU;AACxC,oCAA8B,WAAW,WAAW,OAAO,CAAC;AAAA,IAC9D;AACA,QAAI;AACJ,QAAI,MAAM;AACR,6BAAuB,cAAc,IAAI,IAAI,QAAQ,QAAQ,IAAI;AAAA,IACnE,WAAW,QAAQ,mBAAmB;AACpC,6BAAuB;AAAA,IACzB;AACA,UAAM,SAAS,EAAE,MAAM,UAAU,WAAW;AAC5C,QAAI,yBAAyB,OAAQ,QAAO,uBAAuB;AACnE,QAAI,SAAS,OAAQ,QAAO,WAAW;AACvC,WAAO;AAAA,EACT;AAAA,EACA,OAAO,QAAQ,SAAS,SAAS;AAC/B,WAAO,kBAAkB,iBAAiB,QAAQ,SAAS,OAAO;AAAA,EACpE;AAAA,EACA,cAAc,QAAQ,SAAS,SAAS;AACtC,WAAO,kBAAkB,iBAAiB,EAAE,GAAG,QAAQ,MAAM,MAAM,EAAE,GAAG,SAAS,OAAO;AAAA,EAC1F;AAAA,EACA,OAAO,EAAE,KAAK,MAAM,GAAG,SAAS;AAC9B,WAAO,KAAK,gBAAgB,gCAAgC;AAC5D,WAAO,EAAE,MAAM,UAAU,sBAAsB,QAAQ,KAAK,EAAE;AAAA,EAChE;AAAA,EACA,KAAK,QAAQ,GAAG,SAAS;AACvB,UAAM,SAAS,OAAO,OAAO,CAAC,CAAC;AAC/B,UAAM,UAAU,QAAQ,WAAW,IAAI,MAAM;AAC7C,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,6DAA6D;AAAA,IAC/E;AACA,WAAO,EAAE,MAAM,gBAAgB,OAAO,EAAE;AAAA,EAC1C;AAAA,EACA,KAAK,GAAG,IAAI,SAAS;AACnB,QAAI,CAAC,QAAQ,cAAc;AACzB,YAAM,IAAI,MAAM,iEAAiE;AAAA,IACnF;AACA,YAAQ,QAAQ,cAAc;AAAA,MAC5B,KAAK;AACH,eAAO,EAAE,MAAM,WAAW,QAAQ,YAAY;AAAA,MAChD,KAAK;AACH,eAAO,EAAE,MAAM,UAAU,QAAQ,YAAY;AAAA,IACjD;AAAA,EACF;AAAA,EACA,UAAU,GAAG,IAAI,SAAS;AACxB,QAAI,CAAC,QAAQ,mBAAmB;AAC9B,YAAM,IAAI,MAAM,6EAA6E;AAAA,IAC/F;AACA,YAAQ,QAAQ,mBAAmB;AAAA,MACjC,KAAK;AACH,eAAO,CAAC;AAAA,MACV,KAAK;AACH,eAAO,EAAE,MAAM,OAAO;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO,GAAG,IAAI,SAAS;AACrB,QAAI,CAAC,QAAQ,gBAAgB;AAC3B,YAAM,IAAI,MAAM,uEAAuE;AAAA,IACzF;AACA,YAAQ,QAAQ,gBAAgB;AAAA,MAC9B,KAAK;AACH,eAAO,EAAE,MAAM,WAAW,QAAQ,QAAQ;AAAA,MAC5C,KAAK;AACH,eAAO,EAAE,MAAM,SAAS;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,QAAQ,EAAE,QAAQ,MAAM,MAAM;AAC5B,WAAO,kBAAkB,MAAM,EAAE,QAAQ,GAAG,GAAG,IAAI;AAAA,EACrD;AACF;AAGA,IAAI,uBAAuB;AAAA,EACzB,OAAO;AAAA,IACL,QAAQ,CAAC,EAAE,YAAY,OAAO,EAAE,UAAU,aAAa,UAAU,YAAY;AAAA,IAC7E,YAAY,CAAC,EAAE,YAAY,OAAO,EAAE,UAAU,YAAY;AAAA,IAC1D,YAAY,CAAC,EAAE,YAAY,OAAO,EAAE,UAAU,YAAY;AAAA,EAC5D;AAAA,EACA,QAAQ;AAAA,IACN,OAAO,CAAC,EAAE,YAAY,OAAO,EAAE,OAAO,YAAY;AAAA,IAClD,QAAQ,CAAC,EAAE,YAAY,OAAO,EAAE,WAAW,aAAa,WAAW,YAAY;AAAA,IAC/E,YAAY,CAAC,EAAE,YAAY,OAAO,EAAE,WAAW,YAAY;AAAA,IAC3D,YAAY,CAAC,EAAE,YAAY,OAAO,EAAE,WAAW,YAAY;AAAA;AAAA,IAE3D,OAAO,CAAC,EAAE,YAAY,OAAO,EAAE,SAAS,YAAY,OAAO;AAAA,IAC3D,OAAO,OAAO,EAAE,QAAQ,QAAQ;AAAA,IAChC,UAAU,OAAO,EAAE,QAAQ,OAAO;AAAA,IAClC,eAAe,OAAO,EAAE,QAAQ,YAAY;AAAA,IAC5C,MAAM,OAAO,EAAE,QAAQ,OAAO;AAAA,IAC9B,MAAM,OAAO,EAAE,QAAQ,OAAO;AAAA,IAC9B,MAAM,OAAO,EAAE,QAAQ,OAAO;AAAA,EAChC;AAAA,EACA,QAAQ;AAAA,IACN,OAAO,CAAC,EAAE,YAAY,OAAO,EAAE,OAAO,YAAY;AAAA,IAClD,WAAW,CAAC,EAAE,YAAY,OAAO,EAAE,SAAS,YAAY;AAAA,IACxD,WAAW,CAAC,EAAE,YAAY,OAAO,EAAE,SAAS,YAAY;AAAA,IACxD,aAAa,CAAC,EAAE,YAAY,OAAO,EAAE,YAAY,YAAY;AAAA,IAC7D,SAAS,OAAO,EAAE,MAAM,UAAU;AAAA,EACpC;AAAA,EACA,SAAS;AAAA,IACP,OAAO,CAAC,EAAE,YAAY,OAAO,EAAE,OAAO,YAAY;AAAA,EACpD;AAAA,EACA,MAAM;AAAA,IACJ,OAAO,CAAC,EAAE,YAAY,GAAG,aAAa,EAAE,OAAO,kBAAkB,SAAS,aAAa,OAAO,EAAE;AAAA,IAChG,WAAW,CAAC,EAAE,YAAY,GAAG,aAAa,EAAE,SAAS,kBAAkB,YAAY,aAAa,OAAO,EAAE;AAAA,IACzG,WAAW,CAAC,EAAE,YAAY,GAAG,aAAa,EAAE,SAAS,kBAAkB,YAAY,aAAa,OAAO,EAAE;AAAA,EAC3G;AACF;AACA,SAAS,kBAAkB,MAAM,aAAa,SAAS;AACrD,SAAO,aAAa,MAAM,QAAQ,iBAAiB,WAAW,GAAG,IAAI,4DAA4D;AACjI,SAAO,aAAa,CAAC,MAAM,aAAa,MAAM,2BAA2B,IAAI,aAAa;AAC1F,SAAO,YAAY,QAAQ;AAC7B;AACA,SAAS,YAAY,YAAY,MAAM,SAAS;AAC9C,QAAM,CAAC,QAAQ,GAAG,SAAS,IAAI,QAAQ,CAAC;AACxC,MAAI,CAAC,OAAQ,QAAO,CAAC;AACrB,QAAM,YAAY,YAAY,YAAY,UAAU,OAAO,SAAS,OAAO,MAAM,OAAO;AACxF,WAAS,gBAAgB,KAAK,YAAY;AACxC,QAAI,IAAI,IAAI;AACZ,UAAM,iBAAiB,WAAW;AAClC,UAAM,wBAAwB,MAAM,KAAK,QAAQ,+BAA+B,OAAO,SAAS,GAAG,UAAU,MAAM,OAAO,SAAS,GAAG,cAAc,QAAQ,KAAK,qBAAqB,UAAU,MAAM,OAAO,SAAS,GAAG,cAAc;AACvO,QAAI,CAAC,uBAAuB,QAAQ,wBAAyB,QAAO,CAAC;AACrE,WAAO,qBAAqB,SAAS,oCAAoC,cAAc,mBAAmB,UAAU,IAAI;AACxH,UAAM,YAAY,oBAAoB,YAAY,OAAO;AACzD,WAAO,OAAO,OAAO,KAAK,SAAS;AAAA,EACrC;AACA,SAAO,UAAU,OAAO,iBAAiB,SAAS;AACpD;AAGA,SAAS,cAAc,cAAc,CAAC,GAAG;AACvC,QAAM,MAAsB,oBAAI,IAAI;AACpC,aAAW,CAAC,MAAM,UAAU,KAAK,OAAO,QAAQ,WAAW,GAAG;AAC5D,QAAI,IAAI,YAAY,IAAI;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,SAAS;AAChC,QAAM,cAAc,CAAC;AACrB,WAAS,UAAU,QAAQ;AACzB,QAAI;AACJ,UAAM,UAAU,QAAQ,WAAW,IAAI,MAAM;AAC7C,UAAM,SAAS,WAAW,gBAAgB,OAAO;AACjD,QAAI,UAAU,UAAU,aAAa;AACnC,aAAO,EAAE,MAAM,OAAO;AAAA,IACxB;AACA,UAAM,oBAAoB,KAAK,QAAQ,2BAA2B,OAAO,SAAS,GAAG,OAAO,IAAI,MAAM,kBAAkB,OAAO,IAAI;AACnI,WAAO,iBAAiB,SAAS,gCAAgC,UAAU,OAAO,SAAS,OAAO,SAAS,MAAM,EAAE;AACnH,QAAI,YAAY,gBAAgB,QAAQ,WAAW,OAAO,KAAK,CAAC;AAChE,UAAM,sBAAsB,YAAY,OAAO,MAAM,OAAO,MAAM,OAAO;AACzE,gBAAY,EAAE,GAAG,WAAW,GAAG,oBAAoB;AACnD,kCAA8B,QAAQ,SAAS;AAC/C,QAAI,QAAQ;AACV,kBAAY,OAAO,IAAI;AACvB,aAAO,EAAE,MAAM,OAAO;AAAA,IACxB;AACA,WAAO;AAAA,EACT;AACA,SAAO,EAAE,aAAa,UAAU;AAClC;AACA,SAAS,aAAa,SAAS;AAC7B,QAAM,EAAE,QAAQ,aAAa,kBAAkB,GAAG,KAAK,IAAI;AAC3D,QAAM,aAAa,cAAc,gBAAgB;AACjD,QAAM,EAAE,aAAa,UAAU,IAAI,gBAAgB,EAAE,YAAY,GAAG,KAAK,CAAC;AAC1E,MAAI,CAAC,UAAU,CAAC,kBAAkB;AAChC,UAAM,IAAI,MAAM,yCAAyC;AAAA,EAC3D;AACA,MAAI,kBAAkB;AACpB,WAAO,OAAO,gBAAgB,EAAE,QAAQ,SAAS;AAAA,EACnD;AACA,QAAM,gBAAgB,UAAU,UAAU,MAAM;AAChD,QAAM,cAAc,UAAU,WAAW,IAAI,MAAM;AACnD,QAAM,MAAM,EAAE,QAAQ;AACtB,MAAI,aAAa;AACf,QAAI,OAAO,gBAAgB,WAAW;AAAA,EACxC,OAAO;AACL,WAAO,OAAO,KAAK,aAAa;AAAA,EAClC;AACA,MAAI,OAAO,KAAK,WAAW,EAAE,QAAQ;AACnC,QAAI,cAAc;AAAA,EACpB;AACA,SAAO;AACT;", "names": []}