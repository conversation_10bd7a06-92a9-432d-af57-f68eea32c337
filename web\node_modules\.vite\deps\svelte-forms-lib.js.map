{"version": 3, "sources": ["../../svelte-forms-lib/lib/components/key.js", "../../dequal/lite/index.mjs", "../../svelte-forms-lib/lib/util.js", "../../svelte-forms-lib/lib/create-form.js", "../../svelte-forms-lib/lib/components/Form.svelte", "../../svelte-forms-lib/lib/components/Textarea.svelte", "../../svelte-forms-lib/lib/components/Field.svelte", "../../svelte-forms-lib/lib/components/Select.svelte", "../../svelte-forms-lib/lib/components/ErrorMessage.svelte"], "sourcesContent": ["export const key = {};\n", "var has = Object.prototype.hasOwnProperty;\n\nexport function dequal(foo, bar) {\n\tvar ctor, len;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n", "import {dequal as isEqual} from 'dequal/lite';\n\nfunction subscribeOnce(observable) {\n  return new Promise((resolve) => {\n    observable.subscribe(resolve)(); // immediately invoke to unsubscribe\n  });\n}\n\nfunction update(object, path, value) {\n  object.update((o) => {\n    set(o, path, value);\n    return o;\n  });\n}\n\nfunction cloneDeep(object) {\n  return JSON.parse(JSON.stringify(object));\n}\n\nfunction isNullish(value) {\n  return value === undefined || value === null;\n}\n\nfunction isEmpty(object) {\n  return isNullish(object) || Object.keys(object).length <= 0;\n}\n\nfunction getValues(object) {\n  let results = [];\n\n  for (const [, value] of Object.entries(object)) {\n    const values = typeof value === 'object' ? getValues(value) : [value];\n    results = [...results, ...values];\n  }\n\n  return results;\n}\n\n// TODO: refactor this so as not to rely directly on yup's API\n// This should use dependency injection, with a default callback which may assume\n// yup as the validation schema\nfunction getErrorsFromSchema(initialValues, schema, errors = {}) {\n  for (const key in schema) {\n    switch (true) {\n      case schema[key].type === 'object' && !isEmpty(schema[key].fields): {\n        errors[key] = getErrorsFromSchema(\n          initialValues[key],\n          schema[key].fields,\n          {...errors[key]},\n        );\n        break;\n      }\n\n      case schema[key].type === 'array': {\n        const values =\n          initialValues && initialValues[key] ? initialValues[key] : [];\n        errors[key] = values.map((value) => {\n          const innerError = getErrorsFromSchema(\n            value,\n            schema[key].innerType.fields,\n            {...errors[key]},\n          );\n\n          return Object.keys(innerError).length > 0 ? innerError : '';\n        });\n        break;\n      }\n\n      default: {\n        errors[key] = '';\n      }\n    }\n  }\n\n  return errors;\n}\n\nconst deepEqual = isEqual;\n\nfunction assignDeep(object, value) {\n  if (Array.isArray(object)) {\n    return object.map((o) => assignDeep(o, value));\n  }\n  const copy = {};\n  for (const key in object) {\n    copy[key] =\n      typeof object[key] === 'object' && !isNullish(object[key]) ? assignDeep(object[key], value) : value;\n  }\n  return copy;\n}\n\nfunction set(object, path, value) {\n  if (new Object(object) !== object) return object;\n\n  if (!Array.isArray(path)) {\n    path = path.toString().match(/[^.[\\]]+/g) || [];\n  }\n\n  const result = path\n    .slice(0, -1)\n    // TODO: replace this reduce with something more readable\n    // eslint-disable-next-line unicorn/no-array-reduce\n    .reduce(\n      (accumulator, key, index) =>\n        new Object(accumulator[key]) === accumulator[key]\n          ? accumulator[key]\n          : (accumulator[key] =\n              Math.trunc(Math.abs(path[index + 1])) === +path[index + 1]\n                ? []\n                : {}),\n      object,\n    );\n\n  result[path[path.length - 1]] = value;\n\n  return object;\n}\n\nexport const util = {\n  assignDeep,\n  cloneDeep,\n  deepEqual,\n  getErrorsFromSchema,\n  getValues,\n  isEmpty,\n  isNullish,\n  set,\n  subscribeOnce,\n  update,\n};\n", "import {derived, writable, get} from 'svelte/store';\nimport {util} from './util';\n\nconst NO_ERROR = '';\nconst IS_TOUCHED = true;\n\nfunction isCheckbox(element) {\n  return element.getAttribute && element.getAttribute('type') === 'checkbox';\n}\n\nfunction isFileInput(element) {\n  return element.getAttribute && element.getAttribute('type') === 'file';\n}\n\nfunction resolveValue(element) {\n  if (isFileInput(element)) {\n    return element.files;\n  } else if (isCheckbox(element)) {\n    return element.checked;\n  } else {\n    return element.value;\n  }\n}\n\nexport const createForm = (config) => {\n  let initialValues = config.initialValues || {};\n\n  const validationSchema = config.validationSchema;\n  const validateFunction = config.validate;\n  const onSubmit = config.onSubmit;\n\n  const getInitial = {\n    values: () => util.cloneDeep(initialValues),\n    errors: () =>\n      validationSchema\n        ? util.getErrorsFromSchema(initialValues, validationSchema.fields)\n        : util.assignDeep(initialValues, NO_ERROR),\n    touched: () => util.assignDeep(initialValues, !IS_TOUCHED),\n  };\n\n  const form = writable(getInitial.values());\n  const errors = writable(getInitial.errors());\n  const touched = writable(getInitial.touched());\n\n  const isSubmitting = writable(false);\n  const isValidating = writable(false);\n\n  const isValid = derived(errors, ($errors) => {\n    const noErrors = util\n      .getValues($errors)\n      .every((field) => field === NO_ERROR);\n    return noErrors;\n  });\n\n  const modified = derived(form, ($form) => {\n    const object = util.assignDeep($form, false);\n\n    for (let key in $form) {\n      object[key] = !util.deepEqual($form[key], initialValues[key]);\n    }\n\n    return object;\n  });\n\n  const isModified = derived(modified, ($modified) => {\n    return util.getValues($modified).includes(true);\n  });\n\n  function validateField(field) {\n    return util\n      .subscribeOnce(form)\n      .then((values) => validateFieldValue(field, values[field]));\n  }\n\n  function validateFieldValue(field, value) {\n    updateTouched(field, true);\n\n    if (validationSchema) {\n      isValidating.set(true);\n\n      return validationSchema\n        .validateAt(field, get(form))\n        .then(() => util.update(errors, field, ''))\n        .catch((error) => util.update(errors, field, error.message))\n        .finally(() => {\n          isValidating.set(false);\n        });\n    }\n\n    if (validateFunction) {\n      isValidating.set(true);\n      return Promise.resolve()\n        .then(() => validateFunction({[field]: value}))\n        .then((errs) =>\n          util.update(errors, field, !util.isNullish(errs) ? errs[field] : ''),\n        )\n        .finally(() => {\n          isValidating.set(false);\n        });\n    }\n\n    return Promise.resolve();\n  }\n\n  function updateValidateField(field, value) {\n    updateField(field, value);\n    return validateFieldValue(field, value);\n  }\n\n  function handleChange(event) {\n    const element = event.target;\n    const field = element.name || element.id;\n    const value = resolveValue(element);\n\n    return updateValidateField(field, value);\n  }\n\n  function handleSubmit(event) {\n    if (event && event.preventDefault) {\n      event.preventDefault();\n    }\n\n    isSubmitting.set(true);\n\n    return util.subscribeOnce(form).then((values) => {\n      if (typeof validateFunction === 'function') {\n        isValidating.set(true);\n\n        return Promise.resolve()\n          .then(() => validateFunction(values))\n          .then((error) => {\n            if (util.isNullish(error) || util.getValues(error).length === 0) {\n              return clearErrorsAndSubmit(values);\n            } else {\n              errors.set(error);\n              isSubmitting.set(false);\n            }\n          })\n          .finally(() => isValidating.set(false));\n      }\n\n      if (validationSchema) {\n        isValidating.set(true);\n\n        return (\n          validationSchema\n            .validate(values, {abortEarly: false})\n            .then(() => clearErrorsAndSubmit(values))\n            // eslint-disable-next-line unicorn/catch-error-name\n            .catch((yupErrors) => {\n              if (yupErrors && yupErrors.inner) {\n                const updatedErrors = getInitial.errors();\n\n                yupErrors.inner.map((error) =>\n                  util.set(updatedErrors, error.path, error.message),\n                );\n\n                errors.set(updatedErrors);\n              }\n              isSubmitting.set(false);\n            })\n            .finally(() => isValidating.set(false))\n        );\n      }\n\n      return clearErrorsAndSubmit(values);\n    });\n  }\n\n  function handleReset() {\n    form.set(getInitial.values());\n    errors.set(getInitial.errors());\n    touched.set(getInitial.touched());\n  }\n\n  function clearErrorsAndSubmit(values) {\n    return Promise.resolve()\n      .then(() => errors.set(getInitial.errors()))\n      .then(() => onSubmit(values, form, errors))\n      .finally(() => isSubmitting.set(false));\n  }\n\n  /**\n   * Handler to imperatively update the value of a form field\n   */\n  function updateField(field, value) {\n    util.update(form, field, value);\n  }\n\n  /**\n   * Handler to imperatively update the touched value of a form field\n   */\n  function updateTouched(field, value) {\n    util.update(touched, field, value);\n  }\n\n  /**\n   * Update the initial values and reset form. Used to dynamically display new form values\n   */\n  function updateInitialValues(newValues) {\n    initialValues = newValues;\n\n    handleReset();\n  }\n\n  return {\n    form,\n    errors,\n    touched,\n    modified,\n    isValid,\n    isSubmitting,\n    isValidating,\n    isModified,\n    handleChange,\n    handleSubmit,\n    handleReset,\n    updateField,\n    updateValidateField,\n    updateTouched,\n    validateField,\n    updateInitialValues,\n    state: derived(\n      [\n        form,\n        errors,\n        touched,\n        modified,\n        isValid,\n        isValidating,\n        isSubmitting,\n        isModified,\n      ],\n      ([\n        $form,\n        $errors,\n        $touched,\n        $modified,\n        $isValid,\n        $isValidating,\n        $isSubmitting,\n        $isModified,\n      ]) => ({\n        form: $form,\n        errors: $errors,\n        touched: $touched,\n        modified: $modified,\n        isValid: $isValid,\n        isSubmitting: $isSubmitting,\n        isValidating: $isValidating,\n        isModified: $isModified,\n      }),\n    ),\n  };\n};\n", null, null, null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,IAAM,MAAM,CAAC;;;ACApB,IAAI,MAAM,OAAO,UAAU;AAEpB,SAAS,OAAO,KAAK,KAAK;AAChC,MAAI,MAAM;AACV,MAAI,QAAQ,IAAK,QAAO;AAExB,MAAI,OAAO,QAAQ,OAAK,IAAI,iBAAiB,IAAI,aAAa;AAC7D,QAAI,SAAS,KAAM,QAAO,IAAI,QAAQ,MAAM,IAAI,QAAQ;AACxD,QAAI,SAAS,OAAQ,QAAO,IAAI,SAAS,MAAM,IAAI,SAAS;AAE5D,QAAI,SAAS,OAAO;AACnB,WAAK,MAAI,IAAI,YAAY,IAAI,QAAQ;AACpC,eAAO,SAAS,OAAO,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,EAAE;AAAA,MAC5C;AACA,aAAO,QAAQ;AAAA,IAChB;AAEA,QAAI,CAAC,QAAQ,OAAO,QAAQ,UAAU;AACrC,YAAM;AACN,WAAK,QAAQ,KAAK;AACjB,YAAI,IAAI,KAAK,KAAK,IAAI,KAAK,EAAE,OAAO,CAAC,IAAI,KAAK,KAAK,IAAI,EAAG,QAAO;AACjE,YAAI,EAAE,QAAQ,QAAQ,CAAC,OAAO,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,EAAG,QAAO;AAAA,MAC7D;AACA,aAAO,OAAO,KAAK,GAAG,EAAE,WAAW;AAAA,IACpC;AAAA,EACD;AAEA,SAAO,QAAQ,OAAO,QAAQ;AAC/B;;;AC1BA,SAAS,cAAc,YAAY;AACjC,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,eAAW,UAAU,OAAO,EAAE;AAAA,EAChC,CAAC;AACH;AAEA,SAAS,OAAO,QAAQ,MAAM,OAAO;AACnC,SAAO,OAAO,CAAC,MAAM;AACnB,IAAAA,KAAI,GAAG,MAAM,KAAK;AAClB,WAAO;AAAA,EACT,CAAC;AACH;AAEA,SAAS,UAAU,QAAQ;AACzB,SAAO,KAAK,MAAM,KAAK,UAAU,MAAM,CAAC;AAC1C;AAEA,SAAS,UAAU,OAAO;AACxB,SAAO,UAAU,UAAa,UAAU;AAC1C;AAEA,SAAS,QAAQ,QAAQ;AACvB,SAAO,UAAU,MAAM,KAAK,OAAO,KAAK,MAAM,EAAE,UAAU;AAC5D;AAEA,SAAS,UAAU,QAAQ;AACzB,MAAI,UAAU,CAAC;AAEf,aAAW,CAAC,EAAE,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AAC9C,UAAM,SAAS,OAAO,UAAU,WAAW,UAAU,KAAK,IAAI,CAAC,KAAK;AACpE,cAAU,CAAC,GAAG,SAAS,GAAG,MAAM;AAAA,EAClC;AAEA,SAAO;AACT;AAKA,SAAS,oBAAoB,eAAe,QAAQ,SAAS,CAAC,GAAG;AAC/D,aAAWC,QAAO,QAAQ;AACxB,YAAQ,MAAM;AAAA,MACZ,MAAK,OAAOA,IAAG,EAAE,SAAS,YAAY,CAAC,QAAQ,OAAOA,IAAG,EAAE,MAAM,IAAG;AAClE,eAAOA,IAAG,IAAI;AAAA,UACZ,cAAcA,IAAG;AAAA,UACjB,OAAOA,IAAG,EAAE;AAAA,UACZ,EAAC,GAAG,OAAOA,IAAG,EAAC;AAAA,QACjB;AACA;AAAA,MACF;AAAA,MAEA,KAAK,OAAOA,IAAG,EAAE,SAAS,SAAS;AACjC,cAAM,SACJ,iBAAiB,cAAcA,IAAG,IAAI,cAAcA,IAAG,IAAI,CAAC;AAC9D,eAAOA,IAAG,IAAI,OAAO,IAAI,CAAC,UAAU;AAClC,gBAAM,aAAa;AAAA,YACjB;AAAA,YACA,OAAOA,IAAG,EAAE,UAAU;AAAA,YACtB,EAAC,GAAG,OAAOA,IAAG,EAAC;AAAA,UACjB;AAEA,iBAAO,OAAO,KAAK,UAAU,EAAE,SAAS,IAAI,aAAa;AAAA,QAC3D,CAAC;AACD;AAAA,MACF;AAAA,MAEA,SAAS;AACP,eAAOA,IAAG,IAAI;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,IAAM,YAAY;AAElB,SAAS,WAAW,QAAQ,OAAO;AACjC,MAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,WAAO,OAAO,IAAI,CAAC,MAAM,WAAW,GAAG,KAAK,CAAC;AAAA,EAC/C;AACA,QAAM,OAAO,CAAC;AACd,aAAWA,QAAO,QAAQ;AACxB,SAAKA,IAAG,IACN,OAAO,OAAOA,IAAG,MAAM,YAAY,CAAC,UAAU,OAAOA,IAAG,CAAC,IAAI,WAAW,OAAOA,IAAG,GAAG,KAAK,IAAI;AAAA,EAClG;AACA,SAAO;AACT;AAEA,SAASD,KAAI,QAAQ,MAAM,OAAO;AAChC,MAAI,IAAI,OAAO,MAAM,MAAM,OAAQ,QAAO;AAE1C,MAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACxB,WAAO,KAAK,SAAS,EAAE,MAAM,WAAW,KAAK,CAAC;AAAA,EAChD;AAEA,QAAM,SAAS,KACZ,MAAM,GAAG,EAAE,EAGX;AAAA,IACC,CAAC,aAAaC,MAAK,UACjB,IAAI,OAAO,YAAYA,IAAG,CAAC,MAAM,YAAYA,IAAG,IAC5C,YAAYA,IAAG,IACd,YAAYA,IAAG,IACd,KAAK,MAAM,KAAK,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,QAAQ,CAAC,IACrD,CAAC,IACD,CAAC;AAAA,IACb;AAAA,EACF;AAEF,SAAO,KAAK,KAAK,SAAS,CAAC,CAAC,IAAI;AAEhC,SAAO;AACT;AAEO,IAAM,OAAO;AAAA,EAClB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,KAAAD;AAAA,EACA;AAAA,EACA;AACF;;;AC9HA,IAAM,WAAW;AACjB,IAAM,aAAa;AAEnB,SAAS,WAAW,SAAS;AAC3B,SAAO,QAAQ,gBAAgB,QAAQ,aAAa,MAAM,MAAM;AAClE;AAEA,SAAS,YAAY,SAAS;AAC5B,SAAO,QAAQ,gBAAgB,QAAQ,aAAa,MAAM,MAAM;AAClE;AAEA,SAAS,aAAa,SAAS;AAC7B,MAAI,YAAY,OAAO,GAAG;AACxB,WAAO,QAAQ;AAAA,EACjB,WAAW,WAAW,OAAO,GAAG;AAC9B,WAAO,QAAQ;AAAA,EACjB,OAAO;AACL,WAAO,QAAQ;AAAA,EACjB;AACF;AAEO,IAAM,aAAa,CAAC,WAAW;AACpC,MAAI,gBAAgB,OAAO,iBAAiB,CAAC;AAE7C,QAAM,mBAAmB,OAAO;AAChC,QAAM,mBAAmB,OAAO;AAChC,QAAM,WAAW,OAAO;AAExB,QAAM,aAAa;AAAA,IACjB,QAAQ,MAAM,KAAK,UAAU,aAAa;AAAA,IAC1C,QAAQ,MACN,mBACI,KAAK,oBAAoB,eAAe,iBAAiB,MAAM,IAC/D,KAAK,WAAW,eAAe,QAAQ;AAAA,IAC7C,SAAS,MAAM,KAAK,WAAW,eAAe,CAAC,UAAU;AAAA,EAC3D;AAEA,QAAM,OAAO,SAAS,WAAW,OAAO,CAAC;AACzC,QAAM,SAAS,SAAS,WAAW,OAAO,CAAC;AAC3C,QAAM,UAAU,SAAS,WAAW,QAAQ,CAAC;AAE7C,QAAM,eAAe,SAAS,KAAK;AACnC,QAAM,eAAe,SAAS,KAAK;AAEnC,QAAM,UAAU,QAAQ,QAAQ,CAAC,YAAY;AAC3C,UAAM,WAAW,KACd,UAAU,OAAO,EACjB,MAAM,CAAC,UAAU,UAAU,QAAQ;AACtC,WAAO;AAAA,EACT,CAAC;AAED,QAAM,WAAW,QAAQ,MAAM,CAAC,UAAU;AACxC,UAAM,SAAS,KAAK,WAAW,OAAO,KAAK;AAE3C,aAASE,QAAO,OAAO;AACrB,aAAOA,IAAG,IAAI,CAAC,KAAK,UAAU,MAAMA,IAAG,GAAG,cAAcA,IAAG,CAAC;AAAA,IAC9D;AAEA,WAAO;AAAA,EACT,CAAC;AAED,QAAM,aAAa,QAAQ,UAAU,CAAC,cAAc;AAClD,WAAO,KAAK,UAAU,SAAS,EAAE,SAAS,IAAI;AAAA,EAChD,CAAC;AAED,WAAS,cAAc,OAAO;AAC5B,WAAO,KACJ,cAAc,IAAI,EAClB,KAAK,CAAC,WAAW,mBAAmB,OAAO,OAAO,KAAK,CAAC,CAAC;AAAA,EAC9D;AAEA,WAAS,mBAAmB,OAAO,OAAO;AACxC,kBAAc,OAAO,IAAI;AAEzB,QAAI,kBAAkB;AACpB,mBAAa,IAAI,IAAI;AAErB,aAAO,iBACJ,WAAW,OAAO,IAAI,IAAI,CAAC,EAC3B,KAAK,MAAM,KAAK,OAAO,QAAQ,OAAO,EAAE,CAAC,EACzC,MAAM,CAAC,UAAU,KAAK,OAAO,QAAQ,OAAO,MAAM,OAAO,CAAC,EAC1D,QAAQ,MAAM;AACb,qBAAa,IAAI,KAAK;AAAA,MACxB,CAAC;AAAA,IACL;AAEA,QAAI,kBAAkB;AACpB,mBAAa,IAAI,IAAI;AACrB,aAAO,QAAQ,QAAQ,EACpB,KAAK,MAAM,iBAAiB,EAAC,CAAC,KAAK,GAAG,MAAK,CAAC,CAAC,EAC7C;AAAA,QAAK,CAAC,SACL,KAAK,OAAO,QAAQ,OAAO,CAAC,KAAK,UAAU,IAAI,IAAI,KAAK,KAAK,IAAI,EAAE;AAAA,MACrE,EACC,QAAQ,MAAM;AACb,qBAAa,IAAI,KAAK;AAAA,MACxB,CAAC;AAAA,IACL;AAEA,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAEA,WAAS,oBAAoB,OAAO,OAAO;AACzC,gBAAY,OAAO,KAAK;AACxB,WAAO,mBAAmB,OAAO,KAAK;AAAA,EACxC;AAEA,WAAS,aAAaC,QAAO;AAC3B,UAAM,UAAUA,OAAM;AACtB,UAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,UAAM,QAAQ,aAAa,OAAO;AAElC,WAAO,oBAAoB,OAAO,KAAK;AAAA,EACzC;AAEA,WAAS,aAAaA,QAAO;AAC3B,QAAIA,UAASA,OAAM,gBAAgB;AACjC,MAAAA,OAAM,eAAe;AAAA,IACvB;AAEA,iBAAa,IAAI,IAAI;AAErB,WAAO,KAAK,cAAc,IAAI,EAAE,KAAK,CAAC,WAAW;AAC/C,UAAI,OAAO,qBAAqB,YAAY;AAC1C,qBAAa,IAAI,IAAI;AAErB,eAAO,QAAQ,QAAQ,EACpB,KAAK,MAAM,iBAAiB,MAAM,CAAC,EACnC,KAAK,CAAC,UAAU;AACf,cAAI,KAAK,UAAU,KAAK,KAAK,KAAK,UAAU,KAAK,EAAE,WAAW,GAAG;AAC/D,mBAAO,qBAAqB,MAAM;AAAA,UACpC,OAAO;AACL,mBAAO,IAAI,KAAK;AAChB,yBAAa,IAAI,KAAK;AAAA,UACxB;AAAA,QACF,CAAC,EACA,QAAQ,MAAM,aAAa,IAAI,KAAK,CAAC;AAAA,MAC1C;AAEA,UAAI,kBAAkB;AACpB,qBAAa,IAAI,IAAI;AAErB,eACE,iBACG,SAAS,QAAQ,EAAC,YAAY,MAAK,CAAC,EACpC,KAAK,MAAM,qBAAqB,MAAM,CAAC,EAEvC,MAAM,CAAC,cAAc;AACpB,cAAI,aAAa,UAAU,OAAO;AAChC,kBAAM,gBAAgB,WAAW,OAAO;AAExC,sBAAU,MAAM;AAAA,cAAI,CAAC,UACnB,KAAK,IAAI,eAAe,MAAM,MAAM,MAAM,OAAO;AAAA,YACnD;AAEA,mBAAO,IAAI,aAAa;AAAA,UAC1B;AACA,uBAAa,IAAI,KAAK;AAAA,QACxB,CAAC,EACA,QAAQ,MAAM,aAAa,IAAI,KAAK,CAAC;AAAA,MAE5C;AAEA,aAAO,qBAAqB,MAAM;AAAA,IACpC,CAAC;AAAA,EACH;AAEA,WAAS,cAAc;AACrB,SAAK,IAAI,WAAW,OAAO,CAAC;AAC5B,WAAO,IAAI,WAAW,OAAO,CAAC;AAC9B,YAAQ,IAAI,WAAW,QAAQ,CAAC;AAAA,EAClC;AAEA,WAAS,qBAAqB,QAAQ;AACpC,WAAO,QAAQ,QAAQ,EACpB,KAAK,MAAM,OAAO,IAAI,WAAW,OAAO,CAAC,CAAC,EAC1C,KAAK,MAAM,SAAS,QAAQ,MAAM,MAAM,CAAC,EACzC,QAAQ,MAAM,aAAa,IAAI,KAAK,CAAC;AAAA,EAC1C;AAKA,WAAS,YAAY,OAAO,OAAO;AACjC,SAAK,OAAO,MAAM,OAAO,KAAK;AAAA,EAChC;AAKA,WAAS,cAAc,OAAO,OAAO;AACnC,SAAK,OAAO,SAAS,OAAO,KAAK;AAAA,EACnC;AAKA,WAAS,oBAAoB,WAAW;AACtC,oBAAgB;AAEhB,gBAAY;AAAA,EACd;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,MACL;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,CAAC;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,OAAO;AAAA,QACL,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS;AAAA,QACT,cAAc;AAAA,QACd,cAAc;AAAA,QACd,YAAY;AAAA,MACd;AAAA,IACF;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;MCzPa,gBAAa,KAAA,SAAA,iBAAA,IAAA,OAAA,CAAA,EAAA;MACb,WAAQ,KAAA,SAAA,YAAA,GAAG,IAAI;MACf,mBAAgB,KAAA,SAAA,oBAAA,GAAG,IAAI;MACvB,WAAQ,KAAA,SAAA,YAAA,GAAA,MAAS;cAChB,MACR,6EAA6E;EAEjF,CAAC;MACU,UAAO,KAAA,SAAA,WAAA,IAAA,MAAG,WAAU;IAC7B,eAAA,cAAa;IACb,UAAA,SAAQ;IACR,UAAA,SAAQ;IACR,kBAAA,iBAAgB;;;IAIhB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACE,QAAO;AAEX,aAAW,KAAG;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;6EAI+B,YAAW,CAAA,CAAA;;gBAA7B,cAAY,MAAA,QAAA,MAAA,CAAA,IAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;MC7ChB,OAAI,KAAA,SAAA,QAAA,CAAA;UAER,MAAM,aAAY,IAAI,WAAW,GAAG;;;;;;;;WAIkC,MAAK,EAAC,KAAI,CAAA;;;gBAA7D,cAAY,MAAA,QAAA,UAAA,CAAA,IAAA,EAAA,CAAA;;;gBAAW,cAAY,MAAA,QAAA,UAAA,CAAA,IAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCNlD,OAAI,KAAA,SAAA,QAAA,CAAA;MACJ,OAAI,KAAA,SAAA,QAAA,GAAG,MAAM;UAEjB,MAAM,aAAY,IAAI,WAAW,GAAG;;;;;;;;WAMpC,MAAK,EAAC,KAAI,CAAA;;;;gBACN,cAAY,MAAA,QAAA,OAAA,CAAA,IAAA,EAAA,CAAA;;;gBACd,cAAY,MAAA,QAAA,OAAA,CAAA,IAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCXV,OAAI,KAAA,SAAA,QAAA,CAAA;UAER,MAAM,aAAY,IAAI,WAAW,GAAG;;;;;;;;;;;aAKpC,MAAK,EAAC,KAAI,CAAA;;;;;;;;gBACN,cAAY,MAAA,QAAA,QAAA,CAAA,IAAA,EAAA,CAAA;;;gBACd,cAAY,MAAA,QAAA,QAAA,CAAA,IAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MCTV,OAAI,KAAA,SAAA,QAAA,CAAA;UAER,OAAM,IAAI,WAAW,GAAG;;;;;;;;;;;;uBAIV,QAAO,EAAC,KAAI,CAAA,CAAA;;;;;UAD9B,QAAO,EAAC,KAAI,CAAA,EAAA,UAAA,UAAA;;;;;;;;;;;;;;;;", "names": ["set", "key", "key", "event"]}