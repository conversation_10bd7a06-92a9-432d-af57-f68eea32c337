{"version": 3, "sources": ["../../d3-quadtree/src/add.js", "../../d3-quadtree/src/cover.js", "../../d3-quadtree/src/data.js", "../../d3-quadtree/src/extent.js", "../../d3-quadtree/src/quad.js", "../../d3-quadtree/src/find.js", "../../d3-quadtree/src/remove.js", "../../d3-quadtree/src/root.js", "../../d3-quadtree/src/size.js", "../../d3-quadtree/src/visit.js", "../../d3-quadtree/src/visitAfter.js", "../../d3-quadtree/src/x.js", "../../d3-quadtree/src/y.js", "../../d3-quadtree/src/quadtree.js"], "sourcesContent": ["export default function(d) {\n  const x = +this._x.call(null, d),\n      y = +this._y.call(null, d);\n  return add(this.cover(x, y), x, y, d);\n}\n\nfunction add(tree, x, y, d) {\n  if (isNaN(x) || isNaN(y)) return tree; // ignore invalid points\n\n  var parent,\n      node = tree._root,\n      leaf = {data: d},\n      x0 = tree._x0,\n      y0 = tree._y0,\n      x1 = tree._x1,\n      y1 = tree._y1,\n      xm,\n      ym,\n      xp,\n      yp,\n      right,\n      bottom,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return tree._root = leaf, tree;\n\n  // Find the existing leaf for the new point, or add it.\n  while (node.length) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n    if (parent = node, !(node = node[i = bottom << 1 | right])) return parent[i] = leaf, tree;\n  }\n\n  // Is the new point is exactly coincident with the existing point?\n  xp = +tree._x.call(null, node.data);\n  yp = +tree._y.call(null, node.data);\n  if (x === xp && y === yp) return leaf.next = node, parent ? parent[i] = leaf : tree._root = leaf, tree;\n\n  // Otherwise, split the leaf node until the old and new point are separated.\n  do {\n    parent = parent ? parent[i] = new Array(4) : tree._root = new Array(4);\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n  } while ((i = bottom << 1 | right) === (j = (yp >= ym) << 1 | (xp >= xm)));\n  return parent[j] = node, parent[i] = leaf, tree;\n}\n\nexport function addAll(data) {\n  var d, i, n = data.length,\n      x,\n      y,\n      xz = new Array(n),\n      yz = new Array(n),\n      x0 = Infinity,\n      y0 = Infinity,\n      x1 = -Infinity,\n      y1 = -Infinity;\n\n  // Compute the points and their extent.\n  for (i = 0; i < n; ++i) {\n    if (isNaN(x = +this._x.call(null, d = data[i])) || isNaN(y = +this._y.call(null, d))) continue;\n    xz[i] = x;\n    yz[i] = y;\n    if (x < x0) x0 = x;\n    if (x > x1) x1 = x;\n    if (y < y0) y0 = y;\n    if (y > y1) y1 = y;\n  }\n\n  // If there were no (valid) points, abort.\n  if (x0 > x1 || y0 > y1) return this;\n\n  // Expand the tree to cover the new points.\n  this.cover(x0, y0).cover(x1, y1);\n\n  // Add the new points.\n  for (i = 0; i < n; ++i) {\n    add(this, xz[i], yz[i], data[i]);\n  }\n\n  return this;\n}\n", "export default function(x, y) {\n  if (isNaN(x = +x) || isNaN(y = +y)) return this; // ignore invalid points\n\n  var x0 = this._x0,\n      y0 = this._y0,\n      x1 = this._x1,\n      y1 = this._y1;\n\n  // If the quadtree has no extent, initialize them.\n  // Integer extent are necessary so that if we later double the extent,\n  // the existing quadrant boundaries don’t change due to floating point error!\n  if (isNaN(x0)) {\n    x1 = (x0 = Math.floor(x)) + 1;\n    y1 = (y0 = Math.floor(y)) + 1;\n  }\n\n  // Otherwise, double repeatedly to cover.\n  else {\n    var z = x1 - x0 || 1,\n        node = this._root,\n        parent,\n        i;\n\n    while (x0 > x || x >= x1 || y0 > y || y >= y1) {\n      i = (y < y0) << 1 | (x < x0);\n      parent = new Array(4), parent[i] = node, node = parent, z *= 2;\n      switch (i) {\n        case 0: x1 = x0 + z, y1 = y0 + z; break;\n        case 1: x0 = x1 - z, y1 = y0 + z; break;\n        case 2: x1 = x0 + z, y0 = y1 - z; break;\n        case 3: x0 = x1 - z, y0 = y1 - z; break;\n      }\n    }\n\n    if (this._root && this._root.length) this._root = node;\n  }\n\n  this._x0 = x0;\n  this._y0 = y0;\n  this._x1 = x1;\n  this._y1 = y1;\n  return this;\n}\n", "export default function() {\n  var data = [];\n  this.visit(function(node) {\n    if (!node.length) do data.push(node.data); while (node = node.next)\n  });\n  return data;\n}\n", "export default function(_) {\n  return arguments.length\n      ? this.cover(+_[0][0], +_[0][1]).cover(+_[1][0], +_[1][1])\n      : isNaN(this._x0) ? undefined : [[this._x0, this._y0], [this._x1, this._y1]];\n}\n", "export default function(node, x0, y0, x1, y1) {\n  this.node = node;\n  this.x0 = x0;\n  this.y0 = y0;\n  this.x1 = x1;\n  this.y1 = y1;\n}\n", "import Quad from \"./quad.js\";\n\nexport default function(x, y, radius) {\n  var data,\n      x0 = this._x0,\n      y0 = this._y0,\n      x1,\n      y1,\n      x2,\n      y2,\n      x3 = this._x1,\n      y3 = this._y1,\n      quads = [],\n      node = this._root,\n      q,\n      i;\n\n  if (node) quads.push(new Quad(node, x0, y0, x3, y3));\n  if (radius == null) radius = Infinity;\n  else {\n    x0 = x - radius, y0 = y - radius;\n    x3 = x + radius, y3 = y + radius;\n    radius *= radius;\n  }\n\n  while (q = quads.pop()) {\n\n    // Stop searching if this quadrant can’t contain a closer node.\n    if (!(node = q.node)\n        || (x1 = q.x0) > x3\n        || (y1 = q.y0) > y3\n        || (x2 = q.x1) < x0\n        || (y2 = q.y1) < y0) continue;\n\n    // Bisect the current quadrant.\n    if (node.length) {\n      var xm = (x1 + x2) / 2,\n          ym = (y1 + y2) / 2;\n\n      quads.push(\n        new Quad(node[3], xm, ym, x2, y2),\n        new Quad(node[2], x1, ym, xm, y2),\n        new Quad(node[1], xm, y1, x2, ym),\n        new Quad(node[0], x1, y1, xm, ym)\n      );\n\n      // Visit the closest quadrant first.\n      if (i = (y >= ym) << 1 | (x >= xm)) {\n        q = quads[quads.length - 1];\n        quads[quads.length - 1] = quads[quads.length - 1 - i];\n        quads[quads.length - 1 - i] = q;\n      }\n    }\n\n    // Visit this point. (Visiting coincident points isn’t necessary!)\n    else {\n      var dx = x - +this._x.call(null, node.data),\n          dy = y - +this._y.call(null, node.data),\n          d2 = dx * dx + dy * dy;\n      if (d2 < radius) {\n        var d = Math.sqrt(radius = d2);\n        x0 = x - d, y0 = y - d;\n        x3 = x + d, y3 = y + d;\n        data = node.data;\n      }\n    }\n  }\n\n  return data;\n}\n", "export default function(d) {\n  if (isNaN(x = +this._x.call(null, d)) || isNaN(y = +this._y.call(null, d))) return this; // ignore invalid points\n\n  var parent,\n      node = this._root,\n      retainer,\n      previous,\n      next,\n      x0 = this._x0,\n      y0 = this._y0,\n      x1 = this._x1,\n      y1 = this._y1,\n      x,\n      y,\n      xm,\n      ym,\n      right,\n      bottom,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return this;\n\n  // Find the leaf node for the point.\n  // While descending, also retain the deepest parent with a non-removed sibling.\n  if (node.length) while (true) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n    if (!(parent = node, node = node[i = bottom << 1 | right])) return this;\n    if (!node.length) break;\n    if (parent[(i + 1) & 3] || parent[(i + 2) & 3] || parent[(i + 3) & 3]) retainer = parent, j = i;\n  }\n\n  // Find the point to remove.\n  while (node.data !== d) if (!(previous = node, node = node.next)) return this;\n  if (next = node.next) delete node.next;\n\n  // If there are multiple coincident points, remove just the point.\n  if (previous) return (next ? previous.next = next : delete previous.next), this;\n\n  // If this is the root point, remove it.\n  if (!parent) return this._root = next, this;\n\n  // Remove this leaf.\n  next ? parent[i] = next : delete parent[i];\n\n  // If the parent now contains exactly one leaf, collapse superfluous parents.\n  if ((node = parent[0] || parent[1] || parent[2] || parent[3])\n      && node === (parent[3] || parent[2] || parent[1] || parent[0])\n      && !node.length) {\n    if (retainer) retainer[j] = node;\n    else this._root = node;\n  }\n\n  return this;\n}\n\nexport function removeAll(data) {\n  for (var i = 0, n = data.length; i < n; ++i) this.remove(data[i]);\n  return this;\n}\n", "export default function() {\n  return this._root;\n}\n", "export default function() {\n  var size = 0;\n  this.visit(function(node) {\n    if (!node.length) do ++size; while (node = node.next)\n  });\n  return size;\n}\n", "import Quad from \"./quad.js\";\n\nexport default function(callback) {\n  var quads = [], q, node = this._root, child, x0, y0, x1, y1;\n  if (node) quads.push(new Quad(node, this._x0, this._y0, this._x1, this._y1));\n  while (q = quads.pop()) {\n    if (!callback(node = q.node, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1) && node.length) {\n      var xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n      if (child = node[3]) quads.push(new Quad(child, xm, ym, x1, y1));\n      if (child = node[2]) quads.push(new Quad(child, x0, ym, xm, y1));\n      if (child = node[1]) quads.push(new Quad(child, xm, y0, x1, ym));\n      if (child = node[0]) quads.push(new Quad(child, x0, y0, xm, ym));\n    }\n  }\n  return this;\n}\n", "import Quad from \"./quad.js\";\n\nexport default function(callback) {\n  var quads = [], next = [], q;\n  if (this._root) quads.push(new Quad(this._root, this._x0, this._y0, this._x1, this._y1));\n  while (q = quads.pop()) {\n    var node = q.node;\n    if (node.length) {\n      var child, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1, xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n      if (child = node[0]) quads.push(new Quad(child, x0, y0, xm, ym));\n      if (child = node[1]) quads.push(new Quad(child, xm, y0, x1, ym));\n      if (child = node[2]) quads.push(new Quad(child, x0, ym, xm, y1));\n      if (child = node[3]) quads.push(new Quad(child, xm, ym, x1, y1));\n    }\n    next.push(q);\n  }\n  while (q = next.pop()) {\n    callback(q.node, q.x0, q.y0, q.x1, q.y1);\n  }\n  return this;\n}\n", "export function defaultX(d) {\n  return d[0];\n}\n\nexport default function(_) {\n  return arguments.length ? (this._x = _, this) : this._x;\n}\n", "export function defaultY(d) {\n  return d[1];\n}\n\nexport default function(_) {\n  return arguments.length ? (this._y = _, this) : this._y;\n}\n", "import tree_add, {addAll as tree_addAll} from \"./add.js\";\nimport tree_cover from \"./cover.js\";\nimport tree_data from \"./data.js\";\nimport tree_extent from \"./extent.js\";\nimport tree_find from \"./find.js\";\nimport tree_remove, {removeAll as tree_removeAll} from \"./remove.js\";\nimport tree_root from \"./root.js\";\nimport tree_size from \"./size.js\";\nimport tree_visit from \"./visit.js\";\nimport tree_visitAfter from \"./visitAfter.js\";\nimport tree_x, {defaultX} from \"./x.js\";\nimport tree_y, {defaultY} from \"./y.js\";\n\nexport default function quadtree(nodes, x, y) {\n  var tree = new Quadtree(x == null ? defaultX : x, y == null ? defaultY : y, NaN, NaN, NaN, NaN);\n  return nodes == null ? tree : tree.addAll(nodes);\n}\n\nfunction Quadtree(x, y, x0, y0, x1, y1) {\n  this._x = x;\n  this._y = y;\n  this._x0 = x0;\n  this._y0 = y0;\n  this._x1 = x1;\n  this._y1 = y1;\n  this._root = undefined;\n}\n\nfunction leaf_copy(leaf) {\n  var copy = {data: leaf.data}, next = copy;\n  while (leaf = leaf.next) next = next.next = {data: leaf.data};\n  return copy;\n}\n\nvar treeProto = quadtree.prototype = Quadtree.prototype;\n\ntreeProto.copy = function() {\n  var copy = new Quadtree(this._x, this._y, this._x0, this._y0, this._x1, this._y1),\n      node = this._root,\n      nodes,\n      child;\n\n  if (!node) return copy;\n\n  if (!node.length) return copy._root = leaf_copy(node), copy;\n\n  nodes = [{source: node, target: copy._root = new Array(4)}];\n  while (node = nodes.pop()) {\n    for (var i = 0; i < 4; ++i) {\n      if (child = node.source[i]) {\n        if (child.length) nodes.push({source: child, target: node.target[i] = new Array(4)});\n        else node.target[i] = leaf_copy(child);\n      }\n    }\n  }\n\n  return copy;\n};\n\ntreeProto.add = tree_add;\ntreeProto.addAll = tree_addAll;\ntreeProto.cover = tree_cover;\ntreeProto.data = tree_data;\ntreeProto.extent = tree_extent;\ntreeProto.find = tree_find;\ntreeProto.remove = tree_remove;\ntreeProto.removeAll = tree_removeAll;\ntreeProto.root = tree_root;\ntreeProto.size = tree_size;\ntreeProto.visit = tree_visit;\ntreeProto.visitAfter = tree_visitAfter;\ntreeProto.x = tree_x;\ntreeProto.y = tree_y;\n"], "mappings": ";AAAe,SAAR,YAAiB,GAAG;AACzB,QAAM,IAAI,CAAC,KAAK,GAAG,KAAK,MAAM,CAAC,GAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,MAAM,CAAC;AAC7B,SAAO,IAAI,KAAK,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;AACtC;AAEA,SAAS,IAAI,MAAM,GAAG,GAAG,GAAG;AAC1B,MAAI,MAAM,CAAC,KAAK,MAAM,CAAC,EAAG,QAAO;AAEjC,MAAI,QACA,OAAO,KAAK,OACZ,OAAO,EAAC,MAAM,EAAC,GACf,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,IACA,IACA,IACA,IACA,OACA,QACA,GACA;AAGJ,MAAI,CAAC,KAAM,QAAO,KAAK,QAAQ,MAAM;AAGrC,SAAO,KAAK,QAAQ;AAClB,QAAI,QAAQ,MAAM,MAAM,KAAK,MAAM,GAAI,MAAK;AAAA,QAAS,MAAK;AAC1D,QAAI,SAAS,MAAM,MAAM,KAAK,MAAM,GAAI,MAAK;AAAA,QAAS,MAAK;AAC3D,QAAI,SAAS,MAAM,EAAE,OAAO,KAAK,IAAI,UAAU,IAAI,KAAK,GAAI,QAAO,OAAO,CAAC,IAAI,MAAM;AAAA,EACvF;AAGA,OAAK,CAAC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI;AAClC,OAAK,CAAC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI;AAClC,MAAI,MAAM,MAAM,MAAM,GAAI,QAAO,KAAK,OAAO,MAAM,SAAS,OAAO,CAAC,IAAI,OAAO,KAAK,QAAQ,MAAM;AAGlG,KAAG;AACD,aAAS,SAAS,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC;AACrE,QAAI,QAAQ,MAAM,MAAM,KAAK,MAAM,GAAI,MAAK;AAAA,QAAS,MAAK;AAC1D,QAAI,SAAS,MAAM,MAAM,KAAK,MAAM,GAAI,MAAK;AAAA,QAAS,MAAK;AAAA,EAC7D,UAAU,IAAI,UAAU,IAAI,YAAY,KAAK,MAAM,OAAO,IAAK,MAAM;AACrE,SAAO,OAAO,CAAC,IAAI,MAAM,OAAO,CAAC,IAAI,MAAM;AAC7C;AAEO,SAAS,OAAO,MAAM;AAC3B,MAAI,GAAG,GAAG,IAAI,KAAK,QACf,GACA,GACA,KAAK,IAAI,MAAM,CAAC,GAChB,KAAK,IAAI,MAAM,CAAC,GAChB,KAAK,UACL,KAAK,UACL,KAAK,WACL,KAAK;AAGT,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,QAAI,MAAM,IAAI,CAAC,KAAK,GAAG,KAAK,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,GAAG,KAAK,MAAM,CAAC,CAAC,EAAG;AACtF,OAAG,CAAC,IAAI;AACR,OAAG,CAAC,IAAI;AACR,QAAI,IAAI,GAAI,MAAK;AACjB,QAAI,IAAI,GAAI,MAAK;AACjB,QAAI,IAAI,GAAI,MAAK;AACjB,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB;AAGA,MAAI,KAAK,MAAM,KAAK,GAAI,QAAO;AAG/B,OAAK,MAAM,IAAI,EAAE,EAAE,MAAM,IAAI,EAAE;AAG/B,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,QAAI,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,EACjC;AAEA,SAAO;AACT;;;ACnFe,SAAR,cAAiB,GAAG,GAAG;AAC5B,MAAI,MAAM,IAAI,CAAC,CAAC,KAAK,MAAM,IAAI,CAAC,CAAC,EAAG,QAAO;AAE3C,MAAI,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK;AAKd,MAAI,MAAM,EAAE,GAAG;AACb,UAAM,KAAK,KAAK,MAAM,CAAC,KAAK;AAC5B,UAAM,KAAK,KAAK,MAAM,CAAC,KAAK;AAAA,EAC9B,OAGK;AACH,QAAI,IAAI,KAAK,MAAM,GACf,OAAO,KAAK,OACZ,QACA;AAEJ,WAAO,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,IAAI;AAC7C,WAAK,IAAI,OAAO,IAAK,IAAI;AACzB,eAAS,IAAI,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,MAAM,OAAO,QAAQ,KAAK;AAC7D,cAAQ,GAAG;AAAA,QACT,KAAK;AAAG,eAAK,KAAK,GAAG,KAAK,KAAK;AAAG;AAAA,QAClC,KAAK;AAAG,eAAK,KAAK,GAAG,KAAK,KAAK;AAAG;AAAA,QAClC,KAAK;AAAG,eAAK,KAAK,GAAG,KAAK,KAAK;AAAG;AAAA,QAClC,KAAK;AAAG,eAAK,KAAK,GAAG,KAAK,KAAK;AAAG;AAAA,MACpC;AAAA,IACF;AAEA,QAAI,KAAK,SAAS,KAAK,MAAM,OAAQ,MAAK,QAAQ;AAAA,EACpD;AAEA,OAAK,MAAM;AACX,OAAK,MAAM;AACX,OAAK,MAAM;AACX,OAAK,MAAM;AACX,SAAO;AACT;;;AC1Ce,SAAR,eAAmB;AACxB,MAAI,OAAO,CAAC;AACZ,OAAK,MAAM,SAAS,MAAM;AACxB,QAAI,CAAC,KAAK,OAAQ;AAAG,WAAK,KAAK,KAAK,IAAI;AAAA,WAAU,OAAO,KAAK;AAAA,EAChE,CAAC;AACD,SAAO;AACT;;;ACNe,SAAR,eAAiB,GAAG;AACzB,SAAO,UAAU,SACX,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,IACvD,MAAM,KAAK,GAAG,IAAI,SAAY,CAAC,CAAC,KAAK,KAAK,KAAK,GAAG,GAAG,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC;AACjF;;;ACJe,SAAR,aAAiB,MAAM,IAAI,IAAI,IAAI,IAAI;AAC5C,OAAK,OAAO;AACZ,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,KAAK;AACZ;;;ACJe,SAAR,aAAiB,GAAG,GAAG,QAAQ;AACpC,MAAI,MACA,KAAK,KAAK,KACV,KAAK,KAAK,KACV,IACA,IACA,IACA,IACA,KAAK,KAAK,KACV,KAAK,KAAK,KACV,QAAQ,CAAC,GACT,OAAO,KAAK,OACZ,GACA;AAEJ,MAAI,KAAM,OAAM,KAAK,IAAI,aAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;AACnD,MAAI,UAAU,KAAM,UAAS;AAAA,OACxB;AACH,SAAK,IAAI,QAAQ,KAAK,IAAI;AAC1B,SAAK,IAAI,QAAQ,KAAK,IAAI;AAC1B,cAAU;AAAA,EACZ;AAEA,SAAO,IAAI,MAAM,IAAI,GAAG;AAGtB,QAAI,EAAE,OAAO,EAAE,UACP,KAAK,EAAE,MAAM,OACb,KAAK,EAAE,MAAM,OACb,KAAK,EAAE,MAAM,OACb,KAAK,EAAE,MAAM,GAAI;AAGzB,QAAI,KAAK,QAAQ;AACf,UAAI,MAAM,KAAK,MAAM,GACjB,MAAM,KAAK,MAAM;AAErB,YAAM;AAAA,QACJ,IAAI,aAAK,KAAK,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,QAChC,IAAI,aAAK,KAAK,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,QAChC,IAAI,aAAK,KAAK,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,QAChC,IAAI,aAAK,KAAK,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE;AAAA,MAClC;AAGA,UAAI,KAAK,KAAK,OAAO,IAAK,KAAK,IAAK;AAClC,YAAI,MAAM,MAAM,SAAS,CAAC;AAC1B,cAAM,MAAM,SAAS,CAAC,IAAI,MAAM,MAAM,SAAS,IAAI,CAAC;AACpD,cAAM,MAAM,SAAS,IAAI,CAAC,IAAI;AAAA,MAChC;AAAA,IACF,OAGK;AACH,UAAI,KAAK,IAAI,CAAC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,GACtC,KAAK,IAAI,CAAC,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,GACtC,KAAK,KAAK,KAAK,KAAK;AACxB,UAAI,KAAK,QAAQ;AACf,YAAI,IAAI,KAAK,KAAK,SAAS,EAAE;AAC7B,aAAK,IAAI,GAAG,KAAK,IAAI;AACrB,aAAK,IAAI,GAAG,KAAK,IAAI;AACrB,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACrEe,SAAR,eAAiB,GAAG;AACzB,MAAI,MAAM,IAAI,CAAC,KAAK,GAAG,KAAK,MAAM,CAAC,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,GAAG,KAAK,MAAM,CAAC,CAAC,EAAG,QAAO;AAEnF,MAAI,QACA,OAAO,KAAK,OACZ,UACA,UACA,MACA,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,KAAK,KAAK,KACV,GACA,GACA,IACA,IACA,OACA,QACA,GACA;AAGJ,MAAI,CAAC,KAAM,QAAO;AAIlB,MAAI,KAAK,OAAQ,QAAO,MAAM;AAC5B,QAAI,QAAQ,MAAM,MAAM,KAAK,MAAM,GAAI,MAAK;AAAA,QAAS,MAAK;AAC1D,QAAI,SAAS,MAAM,MAAM,KAAK,MAAM,GAAI,MAAK;AAAA,QAAS,MAAK;AAC3D,QAAI,EAAE,SAAS,MAAM,OAAO,KAAK,IAAI,UAAU,IAAI,KAAK,GAAI,QAAO;AACnE,QAAI,CAAC,KAAK,OAAQ;AAClB,QAAI,OAAQ,IAAI,IAAK,CAAC,KAAK,OAAQ,IAAI,IAAK,CAAC,KAAK,OAAQ,IAAI,IAAK,CAAC,EAAG,YAAW,QAAQ,IAAI;AAAA,EAChG;AAGA,SAAO,KAAK,SAAS,EAAG,KAAI,EAAE,WAAW,MAAM,OAAO,KAAK,MAAO,QAAO;AACzE,MAAI,OAAO,KAAK,KAAM,QAAO,KAAK;AAGlC,MAAI,SAAU,QAAQ,OAAO,SAAS,OAAO,OAAO,OAAO,SAAS,MAAO;AAG3E,MAAI,CAAC,OAAQ,QAAO,KAAK,QAAQ,MAAM;AAGvC,SAAO,OAAO,CAAC,IAAI,OAAO,OAAO,OAAO,CAAC;AAGzC,OAAK,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,MACpD,UAAU,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,MACzD,CAAC,KAAK,QAAQ;AACnB,QAAI,SAAU,UAAS,CAAC,IAAI;AAAA,QACvB,MAAK,QAAQ;AAAA,EACpB;AAEA,SAAO;AACT;AAEO,SAAS,UAAU,MAAM;AAC9B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,EAAG,MAAK,OAAO,KAAK,CAAC,CAAC;AAChE,SAAO;AACT;;;AC7De,SAAR,eAAmB;AACxB,SAAO,KAAK;AACd;;;ACFe,SAAR,eAAmB;AACxB,MAAI,OAAO;AACX,OAAK,MAAM,SAAS,MAAM;AACxB,QAAI,CAAC,KAAK,OAAQ;AAAG,QAAE;AAAA,WAAa,OAAO,KAAK;AAAA,EAClD,CAAC;AACD,SAAO;AACT;;;ACJe,SAAR,cAAiB,UAAU;AAChC,MAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,OAAO,OAAO,IAAI,IAAI,IAAI;AACzD,MAAI,KAAM,OAAM,KAAK,IAAI,aAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AAC3E,SAAO,IAAI,MAAM,IAAI,GAAG;AACtB,QAAI,CAAC,SAAS,OAAO,EAAE,MAAM,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,EAAE,KAAK,KAAK,QAAQ;AACvF,UAAI,MAAM,KAAK,MAAM,GAAG,MAAM,KAAK,MAAM;AACzC,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAC/D,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAC/D,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAC/D,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAAA,IACjE;AAAA,EACF;AACA,SAAO;AACT;;;ACbe,SAAR,mBAAiB,UAAU;AAChC,MAAI,QAAQ,CAAC,GAAG,OAAO,CAAC,GAAG;AAC3B,MAAI,KAAK,MAAO,OAAM,KAAK,IAAI,aAAK,KAAK,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AACvF,SAAO,IAAI,MAAM,IAAI,GAAG;AACtB,QAAI,OAAO,EAAE;AACb,QAAI,KAAK,QAAQ;AACf,UAAI,OAAO,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,MAAM,KAAK,MAAM,GAAG,MAAM,KAAK,MAAM;AAC5F,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAC/D,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAC/D,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAC/D,UAAI,QAAQ,KAAK,CAAC,EAAG,OAAM,KAAK,IAAI,aAAK,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC;AAAA,IACjE;AACA,SAAK,KAAK,CAAC;AAAA,EACb;AACA,SAAO,IAAI,KAAK,IAAI,GAAG;AACrB,aAAS,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;AAAA,EACzC;AACA,SAAO;AACT;;;ACpBO,SAAS,SAAS,GAAG;AAC1B,SAAO,EAAE,CAAC;AACZ;AAEe,SAAR,UAAiB,GAAG;AACzB,SAAO,UAAU,UAAU,KAAK,KAAK,GAAG,QAAQ,KAAK;AACvD;;;ACNO,SAAS,SAAS,GAAG;AAC1B,SAAO,EAAE,CAAC;AACZ;AAEe,SAAR,UAAiB,GAAG;AACzB,SAAO,UAAU,UAAU,KAAK,KAAK,GAAG,QAAQ,KAAK;AACvD;;;ACOe,SAAR,SAA0B,OAAO,GAAG,GAAG;AAC5C,MAAI,OAAO,IAAI,SAAS,KAAK,OAAO,WAAW,GAAG,KAAK,OAAO,WAAW,GAAG,KAAK,KAAK,KAAK,GAAG;AAC9F,SAAO,SAAS,OAAO,OAAO,KAAK,OAAO,KAAK;AACjD;AAEA,SAAS,SAAS,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI;AACtC,OAAK,KAAK;AACV,OAAK,KAAK;AACV,OAAK,MAAM;AACX,OAAK,MAAM;AACX,OAAK,MAAM;AACX,OAAK,MAAM;AACX,OAAK,QAAQ;AACf;AAEA,SAAS,UAAU,MAAM;AACvB,MAAI,OAAO,EAAC,MAAM,KAAK,KAAI,GAAG,OAAO;AACrC,SAAO,OAAO,KAAK,KAAM,QAAO,KAAK,OAAO,EAAC,MAAM,KAAK,KAAI;AAC5D,SAAO;AACT;AAEA,IAAI,YAAY,SAAS,YAAY,SAAS;AAE9C,UAAU,OAAO,WAAW;AAC1B,MAAI,OAAO,IAAI,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,GAC5E,OAAO,KAAK,OACZ,OACA;AAEJ,MAAI,CAAC,KAAM,QAAO;AAElB,MAAI,CAAC,KAAK,OAAQ,QAAO,KAAK,QAAQ,UAAU,IAAI,GAAG;AAEvD,UAAQ,CAAC,EAAC,QAAQ,MAAM,QAAQ,KAAK,QAAQ,IAAI,MAAM,CAAC,EAAC,CAAC;AAC1D,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1B,UAAI,QAAQ,KAAK,OAAO,CAAC,GAAG;AAC1B,YAAI,MAAM,OAAQ,OAAM,KAAK,EAAC,QAAQ,OAAO,QAAQ,KAAK,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,EAAC,CAAC;AAAA,YAC9E,MAAK,OAAO,CAAC,IAAI,UAAU,KAAK;AAAA,MACvC;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEA,UAAU,MAAM;AAChB,UAAU,SAAS;AACnB,UAAU,QAAQ;AAClB,UAAU,OAAO;AACjB,UAAU,SAAS;AACnB,UAAU,OAAO;AACjB,UAAU,SAAS;AACnB,UAAU,YAAY;AACtB,UAAU,OAAO;AACjB,UAAU,OAAO;AACjB,UAAU,QAAQ;AAClB,UAAU,aAAa;AACvB,UAAU,IAAI;AACd,UAAU,IAAI;", "names": []}