{"version": 3, "sources": ["../../memoize-weak/lib/memoize.js", "../../memoize-weak/index.js"], "sourcesContent": ["function isPrimitive(value) {\n  return ((typeof value !== 'object') && (typeof value !== 'function')) || (value === null);\n}\n\nfunction MapTree() {\n  this.childBranches = new WeakMap();\n  this.primitiveKeys = new Map();\n  this.hasValue = false;\n  this.value = undefined;\n}\n\nMapTree.prototype.has = function has(key) {\n  var keyObject = (isPrimitive(key) ? this.primitiveKeys.get(key) : key);\n  return (keyObject ? this.childBranches.has(keyObject) : false);\n};\n\nMapTree.prototype.get = function get(key) {\n  var keyObject = (isPrimitive(key) ? this.primitiveKeys.get(key) : key);\n  return (keyObject ? this.childBranches.get(keyObject) : undefined);\n};\n\nMapTree.prototype.resolveBranch = function resolveBranch(key) {\n  if (this.has(key)) { return this.get(key); }\n  var newBranch = new MapTree();\n  var keyObject = this.createKey(key);\n  this.childBranches.set(keyObject, newBranch);\n  return newBranch;\n};\n\nMapTree.prototype.setValue = function setValue(value) {\n  this.hasValue = true;\n  return (this.value = value);\n};\n\nMapTree.prototype.createKey = function createKey(key) {\n  if (isPrimitive(key)) {\n    var keyObject = {};\n    this.primitiveKeys.set(key, keyObject);\n    return keyObject;\n  }\n  return key;\n};\n\nMapTree.prototype.clear = function clear() {\n  if (arguments.length === 0) {\n    this.childBranches = new WeakMap();\n    this.primitiveKeys.clear();\n    this.hasValue = false;\n    this.value = undefined;\n  } else if (arguments.length === 1) {\n    var key = arguments[0];\n    if (isPrimitive(key)) {\n      var keyObject = this.primitiveKeys.get(key);\n      if (keyObject) {\n        this.childBranches.delete(keyObject);\n        this.primitiveKeys.delete(key);\n      }\n    } else {\n      this.childBranches.delete(key);\n    }\n  } else {\n    var childKey = arguments[0];\n    if (this.has(childKey)) {\n      var childBranch = this.get(childKey);\n      childBranch.clear.apply(childBranch, Array.prototype.slice.call(arguments, 1));\n    }\n  }\n};\n\nmodule.exports = function memoize(fn) {\n  var argsTree = new MapTree();\n\n  function memoized() {\n    var args = Array.prototype.slice.call(arguments);\n    var argNode = args.reduce(function getBranch(parentBranch, arg) {\n      return parentBranch.resolveBranch(arg);\n    }, argsTree);\n    if (argNode.hasValue) { return argNode.value; }\n    var value = fn.apply(null, args);\n    return argNode.setValue(value);\n  }\n\n  memoized.clear = argsTree.clear.bind(argsTree);\n\n  return memoized;\n};\n", "module.exports = require('./lib/memoize');\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,aAAS,YAAY,OAAO;AAC1B,aAAS,OAAO,UAAU,YAAc,OAAO,UAAU,cAAiB,UAAU;AAAA,IACtF;AAEA,aAAS,UAAU;AACjB,WAAK,gBAAgB,oBAAI,QAAQ;AACjC,WAAK,gBAAgB,oBAAI,IAAI;AAC7B,WAAK,WAAW;AAChB,WAAK,QAAQ;AAAA,IACf;AAEA,YAAQ,UAAU,MAAM,SAAS,IAAI,KAAK;AACxC,UAAI,YAAa,YAAY,GAAG,IAAI,KAAK,cAAc,IAAI,GAAG,IAAI;AAClE,aAAQ,YAAY,KAAK,cAAc,IAAI,SAAS,IAAI;AAAA,IAC1D;AAEA,YAAQ,UAAU,MAAM,SAAS,IAAI,KAAK;AACxC,UAAI,YAAa,YAAY,GAAG,IAAI,KAAK,cAAc,IAAI,GAAG,IAAI;AAClE,aAAQ,YAAY,KAAK,cAAc,IAAI,SAAS,IAAI;AAAA,IAC1D;AAEA,YAAQ,UAAU,gBAAgB,SAAS,cAAc,KAAK;AAC5D,UAAI,KAAK,IAAI,GAAG,GAAG;AAAE,eAAO,KAAK,IAAI,GAAG;AAAA,MAAG;AAC3C,UAAI,YAAY,IAAI,QAAQ;AAC5B,UAAI,YAAY,KAAK,UAAU,GAAG;AAClC,WAAK,cAAc,IAAI,WAAW,SAAS;AAC3C,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,WAAW,SAAS,SAAS,OAAO;AACpD,WAAK,WAAW;AAChB,aAAQ,KAAK,QAAQ;AAAA,IACvB;AAEA,YAAQ,UAAU,YAAY,SAAS,UAAU,KAAK;AACpD,UAAI,YAAY,GAAG,GAAG;AACpB,YAAI,YAAY,CAAC;AACjB,aAAK,cAAc,IAAI,KAAK,SAAS;AACrC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,QAAQ,SAAS,QAAQ;AACzC,UAAI,UAAU,WAAW,GAAG;AAC1B,aAAK,gBAAgB,oBAAI,QAAQ;AACjC,aAAK,cAAc,MAAM;AACzB,aAAK,WAAW;AAChB,aAAK,QAAQ;AAAA,MACf,WAAW,UAAU,WAAW,GAAG;AACjC,YAAI,MAAM,UAAU,CAAC;AACrB,YAAI,YAAY,GAAG,GAAG;AACpB,cAAI,YAAY,KAAK,cAAc,IAAI,GAAG;AAC1C,cAAI,WAAW;AACb,iBAAK,cAAc,OAAO,SAAS;AACnC,iBAAK,cAAc,OAAO,GAAG;AAAA,UAC/B;AAAA,QACF,OAAO;AACL,eAAK,cAAc,OAAO,GAAG;AAAA,QAC/B;AAAA,MACF,OAAO;AACL,YAAI,WAAW,UAAU,CAAC;AAC1B,YAAI,KAAK,IAAI,QAAQ,GAAG;AACtB,cAAI,cAAc,KAAK,IAAI,QAAQ;AACnC,sBAAY,MAAM,MAAM,aAAa,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC,CAAC;AAAA,QAC/E;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU,SAAS,QAAQ,IAAI;AACpC,UAAI,WAAW,IAAI,QAAQ;AAE3B,eAAS,WAAW;AAClB,YAAI,OAAO,MAAM,UAAU,MAAM,KAAK,SAAS;AAC/C,YAAI,UAAU,KAAK,OAAO,SAAS,UAAU,cAAc,KAAK;AAC9D,iBAAO,aAAa,cAAc,GAAG;AAAA,QACvC,GAAG,QAAQ;AACX,YAAI,QAAQ,UAAU;AAAE,iBAAO,QAAQ;AAAA,QAAO;AAC9C,YAAI,QAAQ,GAAG,MAAM,MAAM,IAAI;AAC/B,eAAO,QAAQ,SAAS,KAAK;AAAA,MAC/B;AAEA,eAAS,QAAQ,SAAS,MAAM,KAAK,QAAQ;AAE7C,aAAO;AAAA,IACT;AAAA;AAAA;;;ACrFA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;", "names": []}