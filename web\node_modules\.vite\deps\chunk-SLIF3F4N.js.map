{"version": 3, "sources": ["../../sveltekit-superforms/dist/client/SuperDebug.svelte", "../../sveltekit-superforms/dist/client/clipboardCopy.js"], "sourcesContent": [null, "/*! clipboard-copy. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\n\nfunction makeError() {\n\treturn new DOMException('The request is not allowed', 'NotAllowedError');\n}\n\n/**\n * @param {string} text\n */\nasync function copyClipboardApi(text) {\n\t// Use the Async Clipboard API when available. Requires a secure browsing\n\t// context (i.e. HTTPS)\n\tif (!navigator.clipboard) {\n\t\tthrow makeError();\n\t}\n\treturn navigator.clipboard.writeText(text);\n}\n\n/**\n * @param {string} text\n */\nasync function copyExecCommand(text) {\n\t// Put the text to copy into a <span>\n\tconst span = document.createElement('span');\n\tspan.textContent = text;\n\n\t// Preserve consecutive spaces and newlines\n\tspan.style.whiteSpace = 'pre';\n\tspan.style.webkitUserSelect = 'auto';\n\tspan.style.userSelect = 'all';\n\n\t// Add the <span> to the page\n\tdocument.body.appendChild(span);\n\n\t// Make a selection object representing the range of text selected by the user\n\tconst selection = window.getSelection();\n\tconst range = window.document.createRange();\n\tselection?.removeAllRanges();\n\trange.selectNode(span);\n\tselection?.addRange(range);\n\n\t// Copy text to the clipboard\n\tlet success = false;\n\ttry {\n\t\tsuccess = window.document.execCommand('copy');\n\t} finally {\n\t\t// Cleanup\n\t\tselection?.removeAllRanges();\n\t\twindow.document.body.removeChild(span);\n\t}\n\n\tif (!success) throw makeError();\n}\n\n/**\n * @param {string} text\n */\nexport async function clipboardCopy(text) {\n\ttry {\n\t\tawait copyClipboardApi(text);\n\t} catch (err) {\n\t\t// ...Otherwise, use document.execCommand() fallback\n\t\ttry {\n\t\t\tawait copyExecCommand(text);\n\t\t} catch (err2) {\n\t\t\tthrow err2 || err || makeError();\n\t\t}\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SACU,eAAe;SACf,YAAY;;;ACAtB,SAAS,YAAY;AACpB,SAAO,IAAI,aAAa,8BAA8B,iBAAiB;AACxE;AAKA,eAAe,iBAAiB,MAAM;AAGrC,MAAI,CAAC,UAAU,WAAW;AACzB,UAAM,UAAU;AAAA,EACjB;AACA,SAAO,UAAU,UAAU,UAAU,IAAI;AAC1C;AAKA,eAAe,gBAAgB,MAAM;AAEpC,QAAM,OAAO,SAAS,cAAc,MAAM;AAC1C,OAAK,cAAc;AAGnB,OAAK,MAAM,aAAa;AACxB,OAAK,MAAM,mBAAmB;AAC9B,OAAK,MAAM,aAAa;AAGxB,WAAS,KAAK,YAAY,IAAI;AAG9B,QAAM,YAAY,OAAO,aAAa;AACtC,QAAM,QAAQ,OAAO,SAAS,YAAY;AAC1C,yCAAW;AACX,QAAM,WAAW,IAAI;AACrB,yCAAW,SAAS;AAGpB,MAAI,UAAU;AACd,MAAI;AACH,cAAU,OAAO,SAAS,YAAY,MAAM;AAAA,EAC7C,UAAE;AAED,2CAAW;AACX,WAAO,SAAS,KAAK,YAAY,IAAI;AAAA,EACtC;AAEA,MAAI,CAAC,QAAS,OAAM,UAAU;AAC/B;AAKA,eAAsB,cAAc,MAAM;AACzC,MAAI;AACH,UAAM,iBAAiB,IAAI;AAAA,EAC5B,SAAS,KAAK;AAEb,QAAI;AACH,YAAM,gBAAgB,IAAI;AAAA,IAC3B,SAAS,MAAM;AACd,YAAM,QAAQ,OAAO,UAAU;AAAA,IAChC;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MD9DK,YAAS,eAAG,KAAK;MAcV,OAAI,KAAA,SAAA,QAAA,CAAA;MAMJ,UAAO,KAAA,SAAA,WAAA,GAAG,IAAI;MAMd,SAAM,KAAA,SAAA,UAAA,GAAG,IAAI;MAIb,QAAK,KAAA,SAAA,SAAA,GAAG,EAAE;MAMV,iBAAc,KAAA,SAAA,kBAAA,GAAG,GAAG;MAMpB,MAAG,KAAA,SAAA,OAAA,IAAG,MAAS;MAOf,UAAO,KAAA,SAAA,WAAA,GAAG,KAAK;MAMf,MAAG,KAAA,SAAA,OAAA,GAAG,KAAK;MAMX,YAAS,KAAA,SAAA,aAAA,GAAG,KAAK;MAoCjB,QAAK,KAAA,SAAA,SAAA,GAAG,SAAS;MAQjB,cAAW,KAAA,SAAA,eAAA,GAAG,KAAK;MAMnB,YAAS,KAAA,SAAA,aAAA,IAAG,KAAK;MAExB,WAAW,YAAW,EAAE,aAAW;WAK9B,YAAYA,UAAS,QAAW;QACpCC;UAEE,QAAQ,MAAK,EAAC,MAAM,MAAM;QAE5B;UACC,eAAe,YAAY;AAC9B,QAAAA,QAAO,KAAK,MAAM,eAAe,UAAU;MAC5C;AAEA,MAAAA,QAAI;QAAK,WAAWA,SAAQA,MAAK,YAAYA,MAAK,YAAS,CAAA;;AAE3D,MAAAA,MAAK,UAAU,KAAK,IAAA,cAAID,SAAW,MAAS,IAAIC,MAAK,UAAU,KAAK,KAAK,UAAS,IAAID;IACvF,QAAQ;AACP,MAAAC,QAAI,EACH,WAAS,EAAA,CACP,KAAK,GAAG,UAAA,EAAA,EAAA;IAGZ;sBAEID,SAAW,QAAS,KAAA,GAAE;AACzB,qBAAe,aAAa,KAAK,UAAUC,KAAI;IAChD;AAEA,cAAYA,MAAK,UAAU,KAAK,CAAA;EACjC;MAKI,SAAM,eAAA;iBAKK,YAAY,GAAG;SACxB,EAAE,OAAM;UACP;;MAAqC,EAAE,OAAQ,QAAQ,cAAc;;SACtE,OAAM;UAEL;;MAAwC,OAAO,cAAc,oBAAoB;;SAClF,OAAM;AAEX,iBAAY,IAAC,MAAM,CAAA;UACb,cAAc,OAAO,SAAS;QACpC,QAAS,WAAU,MAAA,IAAQ,QAAS,MAAS,GAAG,GAAG,CAAA;EACpD;WAKS,WAAW,MAAM;;MAExB,MAAM,KAAK;MACX,MAAM,KAAK;MACX,MAAM,KAAK;MACX,cAAY,IAAM,KAAK,KAAK,YAAY;;EAE1C;WAQS,gBAAgB,MAAM;mBACf,MAAI;WACb,YAAY;mDAC2B,KAAK,QAAQ,SAAS;MAClE;WACK,UAAU;uCACiB,KAAK,SAAQ,CAAA;MAC7C;;UAGK,gBAAgB,KAAK;MAC1B;eACU,KAAK,OAAO;0BACjB,OAAU,MAAS,GAAE;iBACjB;QACR;iCACW,MAAS,QAAQ,KAAI,KAAK,GAAG,aAAa,MAAM;iBAEnD,UAAU,MAAM,KAAK,GAAG,CAAA,IAAK,iBAAiB;QACtD;iCACW,OAAU,QAAQ,GAAE;qBAC1B,OAAS,OAAO,iBAAiB,EAAA,QAAS;qBAC1C,OAAS,OAAO,iBAAiB,EAAA,QAAS;cAC1C,MAAM,KAAK,EAAA,QAAU;QAC1B;iCACW,OAAU,QAAQ,GAAE;iBACvB,UAAU;QAClB;iCACW,OAAU,UAAU,KAAI,UAAS,GAAE;iBACtC,iBAAsB,MAAM,IAAI;QACxC;YACI,iBAAiB,OAAO;iBACpB,OAAY,MAAM,IAAI,KAAK,MAAM,WAAW,MAAM,SAAS,oBAAoB;QACvF;YACI,iBAAiB,KAAK;iBAClB,MAAM,KAAK,KAAK;QACxB;YACI,iBAAiB,KAAK;iBAClB,MAAM,KAAK,MAAM,QAAO,CAAA;QAChC;iCAEQ,MAAS,QAAQ,KAAA,OAAA,OACjB,KAAK,GAAG,GAAK,QAAQ,KAC5B,KAAK,GAAG,KACR,mBAAmB,KAAK,GAAG,GAC1B;iBACM,UAAU,KAAK,GAAG,EAAE,SAAQ;QACpC;YACI,WAAO,cAAA,OAAW,MAAS,QAAQ,KAAI,KAAK,GAAG,aAAa,MAAM;iBAC9D,WAAW,KAAK,GAAG,CAAA;QAC3B;YACI,WAAO,cAAA,OAAW,MAAS,QAAQ,KAAI,KAAK,GAAG,aAAa,UAAU;gBAEnE,OAAO,KAAK,GAAG;gBACf,SAAM,CAAA;mBACH,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;kBAC/B,OAAO,KAAK,KAAK,CAAC;gBACpB,KAAM,QAAO,KAAK,WAAW,IAAI,CAAA;UACtC;iBACO;QACR;eACO;MACR;MACA;MAEC,QAAQ,MAAM,OAAO,EACrB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM;WAEf,cAAc,QACpB,yGAAuG,SAC7F,OAAO;UACZ,MAAM;UACN,KAAK,KAAK,KAAK,GAAG;YACjB,KAAK,KAAK,KAAK,GAAG;AACrB,gBAAM;AACN,kBAAQ,MAAM,MAAM,GAAC,EAAI,IAAI;QAC9B,OAAO;AACN,gBAAM;AACN,kBACC,eAAc,IAAG,KAAK,MAAM,SAAS,eAAA,IAClC,MAAM,MAAM,GAAG,eAAc,IAAG,CAAC,IAAA,MAC5B,MAAM,SAAS,eAAc,CAAA,IAAI,MAAM,MAAM,QACnD,MAAM,MAAK,CAAE,eAAc,IAAG,CAAC,IAC9B;qBAEA,OAAS,gBAAgB,GAAE;AAC9B,kBAAM;AACN,oBAAQ;UACT,WAAW,MAAM,WAAW,OAAO,GAAG;AACrC,kBAAM;AACN,oBAAQ,MAAM,MAAM,GAAC,EAAI;UAC1B,WAAC,OAAU,OAAS,UAAU,GAAE;AAC/B,kBAAM;AACN,oBAAQ;UACT,WAAC,OAAU,OAAS,UAAU,GAAE;AAC/B,kBAAM;AACN,oBAAQ;UACT,WAAC,OAAU,OAAS,WAAW,GAAE;AAChC,kBAAM;AACN,oBAAQ;UACT,WAAW,MAAM,WAAW,QAAQ,GAAG;AACtC,kBAAM;AACN,oBAAQ,MAAM,MAAM,GAAC,EAAI,IAAI;UAC9B,WAAW,MAAM,WAAW,OAAO,GAAG;AACrC,kBAAM;AACN,oBAAQ,MAAM,MAAM,GAAC,EAAI;UAC1B,WAAW,MAAM,WAAW,OAAO,GAAG;AACrC,kBAAM;AACN,oBAAQ,MAAM,MAAM,GAAC,EAAI;UAC1B,WAAW,MAAM,WAAW,QAAQ,GAAG;AACtC,kBAAM;AACN,oBAAQ,MAAM,MAAM,GAAC,EAAI;UAC1B;QACD;MACD,WAAW,aAAa,KAAK,KAAK,GAAG;AACpC,cAAM;MACP,WAAW,OAAO,KAAK,KAAK,GAAG;AAC9B,cAAM;MACP;aACO,kBAAkB,MAAM,OAAO,QAAQ;IAC/C,CAAA;EAEF;WAQS,cAAcA,OAAMC,MAAKC,UAAS;QACtCD,MAAK;aACD;IACR;WAECC,YAAO,cAAA,OACCF,OAAS,QAAQ,KAAA,cACxBA,OAAS,MAAI,KAAA,KACb,UAAUA,SAAI,cAAA,OACPA,MAAK,MAAM,GAAM,UAAU;EAErC;WAOS,YAAYA,OAAMC,MAAK;QAC3BA,MAAK;aACD;IACR;gCAEQD,OAAS,QAAQ,KAAA,cACxBA,OAAS,MAAI,KAAA,KACb,eAAeA,SAAI,cAAA,OACZA,MAAK,WAAW,GAAM,UAAS;EAExC;;QAEG,YAAU,cACZ,MAAK,GAAK,QAAO;;;;;;;;;;;;;;QAgBd,MAAS;;;;;sBAGV,WAAY,YAAY,KAAI,GAAE,IAAG,CAAA,IAAI,KAAI,IAAG,SAAS,KAAI,CAAA,CAAA,GAAA,cAAA,QAAA;;;;;;;;;;YA4NpDG,QAAI,mBAAA,MAAA,IAAG,WAAY,IAAI,CAAA;UAAvBA,KAAI;;;;eAzNP,SAAS,EAAA,UAAA,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;mBA4OJ,MAAM,EAAA,UAAA,YAAA;cAAA,UAAA,WAAA,KAAA;;;;;;;;;;;;;;+BA0CV,MAAK,EAAC,MAAM;;;;qCALY,MAAK,EAAC,SAAS;wCACZ,MAAK,EAAC,UAAU,OAAO,MAAK,EAAC,SAAS;yCACrC,MAAK,EAAC,UAAU,OAAO,MAAK,EAAC,SAAS;sCACzC,MAAK,EAAC,UAAU;;;;;;;;cALvC,OAAM,EAAA,UAAA,YAAA;;;;;;;;;;;;;;;;;;cAkB4C;;;;;0BACc,WAAM;;;mCAAQ,gBAC/E,YAAW,IAAC,MAAM,GAAE,IAAG,CAAA,IAAIC,KAAG,IAAC,MAAM,CAAA,IAAA,IAAI,MAAA,CAAA,GAAA,OAAA,KAAA;;;0BAChC,UAAK;;;mCACN,gBAAe,IAAC,KAAK,CAAA,GAAA,OAAA,KAAA;;;;;;;;;+BAAwB,gBACtD,WAAC,CAAA,GAAA,OAAA,KAAA;;;;gBALG,cAAc,WAAU,GAAE,IAAG,GAAE,QAAO,CAAA,EAAA,UAAA,YAAA;gBAAA,UAAA,aAAA,KAAA;;;;;;;kCAFnC,IAAG,OAAA,GAAA,MAAH,IAAG,CAAA;;;;;;;;;;;+CAuBgB,UAAS,EAAA;;;;wDATP,YAAW,CAAE,UAAS,CAAA,CAAA,CAAA;;;;cAHlD,YAAW,EAAA,UAAA,YAAA;;;;;;;6BAzET,UAAU,CAAA;oEAIY,MAAK,GAAK,EAAC,IACpC,oEACA,OAAE,EAAA,EAAA;yBAE4B,MAAK,CAAA;;;;;+CATP,YAAW,EAAA;;uCA6DX,MAAK;mCACT,UAAS;;;;;6BAnDuB,WAAW;;;;UAdpE,QAAO,EAAA,UAAA,YAAA;;;;;;;;;;;;;;;;", "names": ["status", "data", "raw", "promise", "init", "get"]}