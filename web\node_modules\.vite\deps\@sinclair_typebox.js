import {
  Any,
  Array,
  <PERSON>ync<PERSON><PERSON><PERSON>,
  BigInt,
  <PERSON>olean,
  <PERSON><PERSON>,
  Computed,
  Constructor,
  <PERSON>reateType,
  Discard,
  Extends,
  ExtendsCheck,
  ExtendsFromMappedKey,
  ExtendsFromMappedResult,
  ExtendsResolverError,
  ExtendsResult,
  ExtendsUndefinedCheck,
  Function,
  Hint,
  Index,
  IndexFromComputed,
  IndexFromMappedKey,
  IndexFromMappedResult,
  IndexFromPropertyKey,
  IndexFromPropertyKeys,
  IndexPropertyKeys,
  Intersect,
  IntersectEvaluated,
  IsAny,
  IsArgument,
  IsArray2 as IsArray,
  IsArray3 as IsArray2,
  IsAsyncIterator2 as IsAsyncIterator,
  IsAsyncIterator3 as IsAsyncIterator2,
  IsBigInt2 as IsBigInt,
  IsBigInt3 as IsBigInt2,
  IsBoolean2 as IsBoolean,
  IsBoolean3 as IsBoolean2,
  IsComputed,
  IsConstructor,
  IsDate2 as IsDate,
  IsFunction2 as IsFunction,
  IsFunction3 as IsFunction2,
  IsInteger2 as Is<PERSON><PERSON>ger,
  IsIntersect,
  IsIterator2 as IsIterator,
  IsIterator3 as IsIterator2,
  IsLiteral,
  IsLiteralValue,
  IsMappedKey,
  IsMappedResult,
  IsNever,
  IsNull2 as IsNull,
  IsNull3 as IsNull2,
  IsNumber2 as IsNumber,
  IsNumber3 as IsNumber2,
  IsObject2 as IsObject,
  IsObject3 as IsObject2,
  IsOptional,
  IsPromise2 as IsPromise,
  IsReadonly,
  IsRecord,
  IsRef,
  IsRegExp,
  IsSchema,
  IsString2 as IsString,
  IsString3 as IsString2,
  IsSymbol2 as IsSymbol,
  IsSymbol3 as IsSymbol2,
  IsTemplateLiteral,
  IsTemplateLiteralExpressionFinite,
  IsTemplateLiteralFinite,
  IsTransform,
  IsTuple,
  IsUint8Array2 as IsUint8Array,
  IsUndefined2 as IsUndefined,
  IsUndefined3 as IsUndefined2,
  IsUnion,
  Iterator,
  KeyOf,
  KeyOfFromMappedResult,
  KeyOfPattern,
  KeyOfPropertyEntries,
  KeyOfPropertyKeys,
  KeyOfPropertyKeysToRest,
  Kind,
  Literal,
  Mapped,
  MappedFunctionReturnType,
  MappedKey,
  MappedResult,
  Never,
  Number,
  Object,
  Optional,
  OptionalFromMappedResult,
  OptionalKind,
  PatternBoolean,
  PatternBooleanExact,
  PatternNever,
  PatternNeverExact,
  PatternNumber,
  PatternNumberExact,
  PatternString,
  PatternStringExact,
  Promise as Promise2,
  Readonly,
  ReadonlyFromMappedResult,
  ReadonlyKind,
  Ref,
  SetComplement,
  SetDistinct,
  SetIncludes,
  SetIntersect,
  SetIntersectMany,
  SetIsSubset,
  SetUnion,
  SetUnionMany,
  String,
  TemplateLiteral,
  TemplateLiteralExpressionGenerate,
  TemplateLiteralFiniteError,
  TemplateLiteralGenerate,
  TemplateLiteralGenerateError,
  TemplateLiteralParse,
  TemplateLiteralParseExact,
  TemplateLiteralParserError,
  TemplateLiteralPattern,
  TemplateLiteralPatternError,
  TemplateLiteralSyntax,
  TemplateLiteralToUnion,
  TransformKind,
  Tuple,
  TypeBoxError,
  Union,
  UnionEvaluated,
  Unknown,
  Unsafe,
  format_exports,
  kind_exports,
  type_exports,
  type_exports2,
  value_exports
} from "./chunk-2QEE62IN.js";
import {
  __export
} from "./chunk-KWPVD4H7.js";

// node_modules/@sinclair/typebox/build/esm/type/clone/type.mjs
function CloneRest(schemas) {
  return schemas.map((schema) => CloneType(schema));
}
function CloneType(schema, options) {
  return options === void 0 ? Clone(schema) : Clone({ ...options, ...schema });
}

// node_modules/@sinclair/typebox/build/esm/type/helpers/helpers.mjs
function Increment(T) {
  return (parseInt(T) + 1).toString();
}

// node_modules/@sinclair/typebox/build/esm/type/argument/argument.mjs
function Argument(index) {
  return CreateType({ [Kind]: "Argument", index });
}

// node_modules/@sinclair/typebox/build/esm/type/awaited/awaited.mjs
function FromComputed(target, parameters) {
  return Computed("Awaited", [Computed(target, parameters)]);
}
function FromRef($ref) {
  return Computed("Awaited", [Ref($ref)]);
}
function FromIntersect(types) {
  return Intersect(FromRest(types));
}
function FromUnion(types) {
  return Union(FromRest(types));
}
function FromPromise(type) {
  return Awaited(type);
}
function FromRest(types) {
  return types.map((type) => Awaited(type));
}
function Awaited(type, options) {
  return CreateType(IsComputed(type) ? FromComputed(type.target, type.parameters) : IsIntersect(type) ? FromIntersect(type.allOf) : IsUnion(type) ? FromUnion(type.anyOf) : IsPromise(type) ? FromPromise(type.item) : IsRef(type) ? FromRef(type.$ref) : type, options);
}

// node_modules/@sinclair/typebox/build/esm/type/composite/composite.mjs
function CompositeKeys(T) {
  const Acc = [];
  for (const L of T)
    Acc.push(...KeyOfPropertyKeys(L));
  return SetDistinct(Acc);
}
function FilterNever(T) {
  return T.filter((L) => !IsNever(L));
}
function CompositeProperty(T, K) {
  const Acc = [];
  for (const L of T)
    Acc.push(...IndexFromPropertyKeys(L, [K]));
  return FilterNever(Acc);
}
function CompositeProperties(T, K) {
  const Acc = {};
  for (const L of K) {
    Acc[L] = IntersectEvaluated(CompositeProperty(T, L));
  }
  return Acc;
}
function Composite(T, options) {
  const K = CompositeKeys(T);
  const P = CompositeProperties(T, K);
  const R = Object(P, options);
  return R;
}

// node_modules/@sinclair/typebox/build/esm/type/date/date.mjs
function Date(options) {
  return CreateType({ [Kind]: "Date", type: "Date" }, options);
}

// node_modules/@sinclair/typebox/build/esm/type/null/null.mjs
function Null(options) {
  return CreateType({ [Kind]: "Null", type: "null" }, options);
}

// node_modules/@sinclair/typebox/build/esm/type/symbol/symbol.mjs
function Symbol(options) {
  return CreateType({ [Kind]: "Symbol", type: "symbol" }, options);
}

// node_modules/@sinclair/typebox/build/esm/type/undefined/undefined.mjs
function Undefined(options) {
  return CreateType({ [Kind]: "Undefined", type: "undefined" }, options);
}

// node_modules/@sinclair/typebox/build/esm/type/uint8array/uint8array.mjs
function Uint8Array(options) {
  return CreateType({ [Kind]: "Uint8Array", type: "Uint8Array" }, options);
}

// node_modules/@sinclair/typebox/build/esm/type/const/const.mjs
function FromArray(T) {
  return T.map((L) => FromValue(L, false));
}
function FromProperties(value) {
  const Acc = {};
  for (const K of globalThis.Object.getOwnPropertyNames(value))
    Acc[K] = Readonly(FromValue(value[K], false));
  return Acc;
}
function ConditionalReadonly(T, root) {
  return root === true ? T : Readonly(T);
}
function FromValue(value, root) {
  return IsAsyncIterator(value) ? ConditionalReadonly(Any(), root) : IsIterator(value) ? ConditionalReadonly(Any(), root) : IsArray(value) ? Readonly(Tuple(FromArray(value))) : IsUint8Array(value) ? Uint8Array() : IsDate(value) ? Date() : IsObject(value) ? ConditionalReadonly(Object(FromProperties(value)), root) : IsFunction(value) ? ConditionalReadonly(Function([], Unknown()), root) : IsUndefined(value) ? Undefined() : IsNull(value) ? Null() : IsSymbol(value) ? Symbol() : IsBigInt(value) ? BigInt() : IsNumber(value) ? Literal(value) : IsBoolean(value) ? Literal(value) : IsString(value) ? Literal(value) : Object({});
}
function Const(T, options) {
  return CreateType(FromValue(T, true), options);
}

// node_modules/@sinclair/typebox/build/esm/type/constructor-parameters/constructor-parameters.mjs
function ConstructorParameters(schema, options) {
  return IsConstructor(schema) ? Tuple(schema.parameters, options) : Never(options);
}

// node_modules/@sinclair/typebox/build/esm/type/enum/enum.mjs
function Enum(item, options) {
  if (IsUndefined(item))
    throw new Error("Enum undefined or empty");
  const values1 = globalThis.Object.getOwnPropertyNames(item).filter((key) => isNaN(key)).map((key) => item[key]);
  const values2 = [...new Set(values1)];
  const anyOf = values2.map((value) => Literal(value));
  return Union(anyOf, { ...options, [Hint]: "Enum" });
}

// node_modules/@sinclair/typebox/build/esm/type/exclude/exclude-from-template-literal.mjs
function ExcludeFromTemplateLiteral(L, R) {
  return Exclude(TemplateLiteralToUnion(L), R);
}

// node_modules/@sinclair/typebox/build/esm/type/exclude/exclude.mjs
function ExcludeRest(L, R) {
  const excluded = L.filter((inner) => ExtendsCheck(inner, R) === ExtendsResult.False);
  return excluded.length === 1 ? excluded[0] : Union(excluded);
}
function Exclude(L, R, options = {}) {
  if (IsTemplateLiteral(L))
    return CreateType(ExcludeFromTemplateLiteral(L, R), options);
  if (IsMappedResult(L))
    return CreateType(ExcludeFromMappedResult(L, R), options);
  return CreateType(IsUnion(L) ? ExcludeRest(L.anyOf, R) : ExtendsCheck(L, R) !== ExtendsResult.False ? Never() : L, options);
}

// node_modules/@sinclair/typebox/build/esm/type/exclude/exclude-from-mapped-result.mjs
function FromProperties2(P, U) {
  const Acc = {};
  for (const K2 of globalThis.Object.getOwnPropertyNames(P))
    Acc[K2] = Exclude(P[K2], U);
  return Acc;
}
function FromMappedResult(R, T) {
  return FromProperties2(R.properties, T);
}
function ExcludeFromMappedResult(R, T) {
  const P = FromMappedResult(R, T);
  return MappedResult(P);
}

// node_modules/@sinclair/typebox/build/esm/type/extract/extract-from-template-literal.mjs
function ExtractFromTemplateLiteral(L, R) {
  return Extract(TemplateLiteralToUnion(L), R);
}

// node_modules/@sinclair/typebox/build/esm/type/extract/extract.mjs
function ExtractRest(L, R) {
  const extracted = L.filter((inner) => ExtendsCheck(inner, R) !== ExtendsResult.False);
  return extracted.length === 1 ? extracted[0] : Union(extracted);
}
function Extract(L, R, options) {
  if (IsTemplateLiteral(L))
    return CreateType(ExtractFromTemplateLiteral(L, R), options);
  if (IsMappedResult(L))
    return CreateType(ExtractFromMappedResult(L, R), options);
  return CreateType(IsUnion(L) ? ExtractRest(L.anyOf, R) : ExtendsCheck(L, R) !== ExtendsResult.False ? L : Never(), options);
}

// node_modules/@sinclair/typebox/build/esm/type/extract/extract-from-mapped-result.mjs
function FromProperties3(P, T) {
  const Acc = {};
  for (const K2 of globalThis.Object.getOwnPropertyNames(P))
    Acc[K2] = Extract(P[K2], T);
  return Acc;
}
function FromMappedResult2(R, T) {
  return FromProperties3(R.properties, T);
}
function ExtractFromMappedResult(R, T) {
  const P = FromMappedResult2(R, T);
  return MappedResult(P);
}

// node_modules/@sinclair/typebox/build/esm/type/instance-type/instance-type.mjs
function InstanceType(schema, options) {
  return IsConstructor(schema) ? CreateType(schema.returns, options) : Never(options);
}

// node_modules/@sinclair/typebox/build/esm/type/readonly-optional/readonly-optional.mjs
function ReadonlyOptional(schema) {
  return Readonly(Optional(schema));
}

// node_modules/@sinclair/typebox/build/esm/type/record/record.mjs
function RecordCreateFromPattern(pattern, T, options) {
  return CreateType({ [Kind]: "Record", type: "object", patternProperties: { [pattern]: T } }, options);
}
function RecordCreateFromKeys(K, T, options) {
  const result = {};
  for (const K2 of K)
    result[K2] = T;
  return Object(result, { ...options, [Hint]: "Record" });
}
function FromTemplateLiteralKey(K, T, options) {
  return IsTemplateLiteralFinite(K) ? RecordCreateFromKeys(IndexPropertyKeys(K), T, options) : RecordCreateFromPattern(K.pattern, T, options);
}
function FromUnionKey(key, type, options) {
  return RecordCreateFromKeys(IndexPropertyKeys(Union(key)), type, options);
}
function FromLiteralKey(key, type, options) {
  return RecordCreateFromKeys([key.toString()], type, options);
}
function FromRegExpKey(key, type, options) {
  return RecordCreateFromPattern(key.source, type, options);
}
function FromStringKey(key, type, options) {
  const pattern = IsUndefined(key.pattern) ? PatternStringExact : key.pattern;
  return RecordCreateFromPattern(pattern, type, options);
}
function FromAnyKey(_, type, options) {
  return RecordCreateFromPattern(PatternStringExact, type, options);
}
function FromNeverKey(_key, type, options) {
  return RecordCreateFromPattern(PatternNeverExact, type, options);
}
function FromBooleanKey(_key, type, options) {
  return Object({ true: type, false: type }, options);
}
function FromIntegerKey(_key, type, options) {
  return RecordCreateFromPattern(PatternNumberExact, type, options);
}
function FromNumberKey(_, type, options) {
  return RecordCreateFromPattern(PatternNumberExact, type, options);
}
function Record(key, type, options = {}) {
  return IsUnion(key) ? FromUnionKey(key.anyOf, type, options) : IsTemplateLiteral(key) ? FromTemplateLiteralKey(key, type, options) : IsLiteral(key) ? FromLiteralKey(key.const, type, options) : IsBoolean2(key) ? FromBooleanKey(key, type, options) : IsInteger(key) ? FromIntegerKey(key, type, options) : IsNumber2(key) ? FromNumberKey(key, type, options) : IsRegExp(key) ? FromRegExpKey(key, type, options) : IsString2(key) ? FromStringKey(key, type, options) : IsAny(key) ? FromAnyKey(key, type, options) : IsNever(key) ? FromNeverKey(key, type, options) : Never(options);
}
function RecordPattern(record) {
  return globalThis.Object.getOwnPropertyNames(record.patternProperties)[0];
}
function RecordKey(type) {
  const pattern = RecordPattern(type);
  return pattern === PatternStringExact ? String() : pattern === PatternNumberExact ? Number() : String({ pattern });
}
function RecordValue(type) {
  return type.patternProperties[RecordPattern(type)];
}

// node_modules/@sinclair/typebox/build/esm/type/instantiate/instantiate.mjs
function FromConstructor(args, type) {
  type.parameters = FromTypes(args, type.parameters);
  type.returns = FromType(args, type.returns);
  return type;
}
function FromFunction(args, type) {
  type.parameters = FromTypes(args, type.parameters);
  type.returns = FromType(args, type.returns);
  return type;
}
function FromIntersect2(args, type) {
  type.allOf = FromTypes(args, type.allOf);
  return type;
}
function FromUnion2(args, type) {
  type.anyOf = FromTypes(args, type.anyOf);
  return type;
}
function FromTuple(args, type) {
  if (IsUndefined(type.items))
    return type;
  type.items = FromTypes(args, type.items);
  return type;
}
function FromArray2(args, type) {
  type.items = FromType(args, type.items);
  return type;
}
function FromAsyncIterator(args, type) {
  type.items = FromType(args, type.items);
  return type;
}
function FromIterator(args, type) {
  type.items = FromType(args, type.items);
  return type;
}
function FromPromise2(args, type) {
  type.item = FromType(args, type.item);
  return type;
}
function FromObject(args, type) {
  const mappedProperties = FromProperties4(args, type.properties);
  return { ...type, ...Object(mappedProperties) };
}
function FromRecord(args, type) {
  const mappedKey = FromType(args, RecordKey(type));
  const mappedValue = FromType(args, RecordValue(type));
  const result = Record(mappedKey, mappedValue);
  return { ...type, ...result };
}
function FromArgument(args, argument) {
  return argument.index in args ? args[argument.index] : Unknown();
}
function FromProperty(args, type) {
  const isReadonly = IsReadonly(type);
  const isOptional = IsOptional(type);
  const mapped = FromType(args, type);
  return isReadonly && isOptional ? ReadonlyOptional(mapped) : isReadonly && !isOptional ? Readonly(mapped) : !isReadonly && isOptional ? Optional(mapped) : mapped;
}
function FromProperties4(args, properties) {
  return globalThis.Object.getOwnPropertyNames(properties).reduce((result, key) => {
    return { ...result, [key]: FromProperty(args, properties[key]) };
  }, {});
}
function FromTypes(args, types) {
  return types.map((type) => FromType(args, type));
}
function FromType(args, type) {
  return IsConstructor(type) ? FromConstructor(args, type) : IsFunction2(type) ? FromFunction(args, type) : IsIntersect(type) ? FromIntersect2(args, type) : IsUnion(type) ? FromUnion2(args, type) : IsTuple(type) ? FromTuple(args, type) : IsArray2(type) ? FromArray2(args, type) : IsAsyncIterator2(type) ? FromAsyncIterator(args, type) : IsIterator2(type) ? FromIterator(args, type) : IsPromise(type) ? FromPromise2(args, type) : IsObject2(type) ? FromObject(args, type) : IsRecord(type) ? FromRecord(args, type) : IsArgument(type) ? FromArgument(args, type) : type;
}
function Instantiate(type, args) {
  return FromType(args, CloneType(type));
}

// node_modules/@sinclair/typebox/build/esm/type/integer/integer.mjs
function Integer(options) {
  return CreateType({ [Kind]: "Integer", type: "integer" }, options);
}

// node_modules/@sinclair/typebox/build/esm/type/intrinsic/intrinsic-from-mapped-key.mjs
function MappedIntrinsicPropertyKey(K, M, options) {
  return {
    [K]: Intrinsic(Literal(K), M, Clone(options))
  };
}
function MappedIntrinsicPropertyKeys(K, M, options) {
  const result = K.reduce((Acc, L) => {
    return { ...Acc, ...MappedIntrinsicPropertyKey(L, M, options) };
  }, {});
  return result;
}
function MappedIntrinsicProperties(T, M, options) {
  return MappedIntrinsicPropertyKeys(T["keys"], M, options);
}
function IntrinsicFromMappedKey(T, M, options) {
  const P = MappedIntrinsicProperties(T, M, options);
  return MappedResult(P);
}

// node_modules/@sinclair/typebox/build/esm/type/intrinsic/intrinsic.mjs
function ApplyUncapitalize(value) {
  const [first, rest] = [value.slice(0, 1), value.slice(1)];
  return [first.toLowerCase(), rest].join("");
}
function ApplyCapitalize(value) {
  const [first, rest] = [value.slice(0, 1), value.slice(1)];
  return [first.toUpperCase(), rest].join("");
}
function ApplyUppercase(value) {
  return value.toUpperCase();
}
function ApplyLowercase(value) {
  return value.toLowerCase();
}
function FromTemplateLiteral(schema, mode, options) {
  const expression = TemplateLiteralParseExact(schema.pattern);
  const finite = IsTemplateLiteralExpressionFinite(expression);
  if (!finite)
    return { ...schema, pattern: FromLiteralValue(schema.pattern, mode) };
  const strings = [...TemplateLiteralExpressionGenerate(expression)];
  const literals = strings.map((value) => Literal(value));
  const mapped = FromRest2(literals, mode);
  const union = Union(mapped);
  return TemplateLiteral([union], options);
}
function FromLiteralValue(value, mode) {
  return typeof value === "string" ? mode === "Uncapitalize" ? ApplyUncapitalize(value) : mode === "Capitalize" ? ApplyCapitalize(value) : mode === "Uppercase" ? ApplyUppercase(value) : mode === "Lowercase" ? ApplyLowercase(value) : value : value.toString();
}
function FromRest2(T, M) {
  return T.map((L) => Intrinsic(L, M));
}
function Intrinsic(schema, mode, options = {}) {
  return (
    // Intrinsic-Mapped-Inference
    IsMappedKey(schema) ? IntrinsicFromMappedKey(schema, mode, options) : (
      // Standard-Inference
      IsTemplateLiteral(schema) ? FromTemplateLiteral(schema, mode, options) : IsUnion(schema) ? Union(FromRest2(schema.anyOf, mode), options) : IsLiteral(schema) ? Literal(FromLiteralValue(schema.const, mode), options) : (
        // Default Type
        CreateType(schema, options)
      )
    )
  );
}

// node_modules/@sinclair/typebox/build/esm/type/intrinsic/capitalize.mjs
function Capitalize(T, options = {}) {
  return Intrinsic(T, "Capitalize", options);
}

// node_modules/@sinclair/typebox/build/esm/type/intrinsic/lowercase.mjs
function Lowercase(T, options = {}) {
  return Intrinsic(T, "Lowercase", options);
}

// node_modules/@sinclair/typebox/build/esm/type/intrinsic/uncapitalize.mjs
function Uncapitalize(T, options = {}) {
  return Intrinsic(T, "Uncapitalize", options);
}

// node_modules/@sinclair/typebox/build/esm/type/intrinsic/uppercase.mjs
function Uppercase(T, options = {}) {
  return Intrinsic(T, "Uppercase", options);
}

// node_modules/@sinclair/typebox/build/esm/type/omit/omit-from-mapped-result.mjs
function FromProperties5(properties, propertyKeys, options) {
  const result = {};
  for (const K2 of globalThis.Object.getOwnPropertyNames(properties))
    result[K2] = Omit(properties[K2], propertyKeys, Clone(options));
  return result;
}
function FromMappedResult3(mappedResult, propertyKeys, options) {
  return FromProperties5(mappedResult.properties, propertyKeys, options);
}
function OmitFromMappedResult(mappedResult, propertyKeys, options) {
  const properties = FromMappedResult3(mappedResult, propertyKeys, options);
  return MappedResult(properties);
}

// node_modules/@sinclair/typebox/build/esm/type/omit/omit.mjs
function FromIntersect3(types, propertyKeys) {
  return types.map((type) => OmitResolve(type, propertyKeys));
}
function FromUnion3(types, propertyKeys) {
  return types.map((type) => OmitResolve(type, propertyKeys));
}
function FromProperty2(properties, key) {
  const { [key]: _, ...R } = properties;
  return R;
}
function FromProperties6(properties, propertyKeys) {
  return propertyKeys.reduce((T, K2) => FromProperty2(T, K2), properties);
}
function FromObject2(properties, propertyKeys) {
  const options = Discard(properties, [TransformKind, "$id", "required", "properties"]);
  const omittedProperties = FromProperties6(properties["properties"], propertyKeys);
  return Object(omittedProperties, options);
}
function UnionFromPropertyKeys(propertyKeys) {
  const result = propertyKeys.reduce((result2, key) => IsLiteralValue(key) ? [...result2, Literal(key)] : result2, []);
  return Union(result);
}
function OmitResolve(properties, propertyKeys) {
  return IsIntersect(properties) ? Intersect(FromIntersect3(properties.allOf, propertyKeys)) : IsUnion(properties) ? Union(FromUnion3(properties.anyOf, propertyKeys)) : IsObject2(properties) ? FromObject2(properties, propertyKeys) : Object({});
}
function Omit(type, key, options) {
  const typeKey = IsArray(key) ? UnionFromPropertyKeys(key) : key;
  const propertyKeys = IsSchema(key) ? IndexPropertyKeys(key) : key;
  const isTypeRef = IsRef(type);
  const isKeyRef = IsRef(key);
  return IsMappedResult(type) ? OmitFromMappedResult(type, propertyKeys, options) : IsMappedKey(key) ? OmitFromMappedKey(type, key, options) : isTypeRef && isKeyRef ? Computed("Omit", [type, typeKey], options) : !isTypeRef && isKeyRef ? Computed("Omit", [type, typeKey], options) : isTypeRef && !isKeyRef ? Computed("Omit", [type, typeKey], options) : CreateType({ ...OmitResolve(type, propertyKeys), ...options });
}

// node_modules/@sinclair/typebox/build/esm/type/omit/omit-from-mapped-key.mjs
function FromPropertyKey(type, key, options) {
  return { [key]: Omit(type, [key], Clone(options)) };
}
function FromPropertyKeys(type, propertyKeys, options) {
  return propertyKeys.reduce((Acc, LK) => {
    return { ...Acc, ...FromPropertyKey(type, LK, options) };
  }, {});
}
function FromMappedKey(type, mappedKey, options) {
  return FromPropertyKeys(type, mappedKey.keys, options);
}
function OmitFromMappedKey(type, mappedKey, options) {
  const properties = FromMappedKey(type, mappedKey, options);
  return MappedResult(properties);
}

// node_modules/@sinclair/typebox/build/esm/type/pick/pick-from-mapped-result.mjs
function FromProperties7(properties, propertyKeys, options) {
  const result = {};
  for (const K2 of globalThis.Object.getOwnPropertyNames(properties))
    result[K2] = Pick(properties[K2], propertyKeys, Clone(options));
  return result;
}
function FromMappedResult4(mappedResult, propertyKeys, options) {
  return FromProperties7(mappedResult.properties, propertyKeys, options);
}
function PickFromMappedResult(mappedResult, propertyKeys, options) {
  const properties = FromMappedResult4(mappedResult, propertyKeys, options);
  return MappedResult(properties);
}

// node_modules/@sinclair/typebox/build/esm/type/pick/pick.mjs
function FromIntersect4(types, propertyKeys) {
  return types.map((type) => PickResolve(type, propertyKeys));
}
function FromUnion4(types, propertyKeys) {
  return types.map((type) => PickResolve(type, propertyKeys));
}
function FromProperties8(properties, propertyKeys) {
  const result = {};
  for (const K2 of propertyKeys)
    if (K2 in properties)
      result[K2] = properties[K2];
  return result;
}
function FromObject3(T, K) {
  const options = Discard(T, [TransformKind, "$id", "required", "properties"]);
  const properties = FromProperties8(T["properties"], K);
  return Object(properties, options);
}
function UnionFromPropertyKeys2(propertyKeys) {
  const result = propertyKeys.reduce((result2, key) => IsLiteralValue(key) ? [...result2, Literal(key)] : result2, []);
  return Union(result);
}
function PickResolve(properties, propertyKeys) {
  return IsIntersect(properties) ? Intersect(FromIntersect4(properties.allOf, propertyKeys)) : IsUnion(properties) ? Union(FromUnion4(properties.anyOf, propertyKeys)) : IsObject2(properties) ? FromObject3(properties, propertyKeys) : Object({});
}
function Pick(type, key, options) {
  const typeKey = IsArray(key) ? UnionFromPropertyKeys2(key) : key;
  const propertyKeys = IsSchema(key) ? IndexPropertyKeys(key) : key;
  const isTypeRef = IsRef(type);
  const isKeyRef = IsRef(key);
  return IsMappedResult(type) ? PickFromMappedResult(type, propertyKeys, options) : IsMappedKey(key) ? PickFromMappedKey(type, key, options) : isTypeRef && isKeyRef ? Computed("Pick", [type, typeKey], options) : !isTypeRef && isKeyRef ? Computed("Pick", [type, typeKey], options) : isTypeRef && !isKeyRef ? Computed("Pick", [type, typeKey], options) : CreateType({ ...PickResolve(type, propertyKeys), ...options });
}

// node_modules/@sinclair/typebox/build/esm/type/pick/pick-from-mapped-key.mjs
function FromPropertyKey2(type, key, options) {
  return {
    [key]: Pick(type, [key], Clone(options))
  };
}
function FromPropertyKeys2(type, propertyKeys, options) {
  return propertyKeys.reduce((result, leftKey) => {
    return { ...result, ...FromPropertyKey2(type, leftKey, options) };
  }, {});
}
function FromMappedKey2(type, mappedKey, options) {
  return FromPropertyKeys2(type, mappedKey.keys, options);
}
function PickFromMappedKey(type, mappedKey, options) {
  const properties = FromMappedKey2(type, mappedKey, options);
  return MappedResult(properties);
}

// node_modules/@sinclair/typebox/build/esm/type/partial/partial.mjs
function FromComputed2(target, parameters) {
  return Computed("Partial", [Computed(target, parameters)]);
}
function FromRef2($ref) {
  return Computed("Partial", [Ref($ref)]);
}
function FromProperties9(properties) {
  const partialProperties = {};
  for (const K of globalThis.Object.getOwnPropertyNames(properties))
    partialProperties[K] = Optional(properties[K]);
  return partialProperties;
}
function FromObject4(type) {
  const options = Discard(type, [TransformKind, "$id", "required", "properties"]);
  const properties = FromProperties9(type["properties"]);
  return Object(properties, options);
}
function FromRest3(types) {
  return types.map((type) => PartialResolve(type));
}
function PartialResolve(type) {
  return (
    // Mappable
    IsComputed(type) ? FromComputed2(type.target, type.parameters) : IsRef(type) ? FromRef2(type.$ref) : IsIntersect(type) ? Intersect(FromRest3(type.allOf)) : IsUnion(type) ? Union(FromRest3(type.anyOf)) : IsObject2(type) ? FromObject4(type) : (
      // Intrinsic
      IsBigInt2(type) ? type : IsBoolean2(type) ? type : IsInteger(type) ? type : IsLiteral(type) ? type : IsNull2(type) ? type : IsNumber2(type) ? type : IsString2(type) ? type : IsSymbol2(type) ? type : IsUndefined2(type) ? type : (
        // Passthrough
        Object({})
      )
    )
  );
}
function Partial(type, options) {
  if (IsMappedResult(type)) {
    return PartialFromMappedResult(type, options);
  } else {
    return CreateType({ ...PartialResolve(type), ...options });
  }
}

// node_modules/@sinclair/typebox/build/esm/type/partial/partial-from-mapped-result.mjs
function FromProperties10(K, options) {
  const Acc = {};
  for (const K2 of globalThis.Object.getOwnPropertyNames(K))
    Acc[K2] = Partial(K[K2], Clone(options));
  return Acc;
}
function FromMappedResult5(R, options) {
  return FromProperties10(R.properties, options);
}
function PartialFromMappedResult(R, options) {
  const P = FromMappedResult5(R, options);
  return MappedResult(P);
}

// node_modules/@sinclair/typebox/build/esm/type/required/required.mjs
function FromComputed3(target, parameters) {
  return Computed("Required", [Computed(target, parameters)]);
}
function FromRef3($ref) {
  return Computed("Required", [Ref($ref)]);
}
function FromProperties11(properties) {
  const requiredProperties = {};
  for (const K of globalThis.Object.getOwnPropertyNames(properties))
    requiredProperties[K] = Discard(properties[K], [OptionalKind]);
  return requiredProperties;
}
function FromObject5(type) {
  const options = Discard(type, [TransformKind, "$id", "required", "properties"]);
  const properties = FromProperties11(type["properties"]);
  return Object(properties, options);
}
function FromRest4(types) {
  return types.map((type) => RequiredResolve(type));
}
function RequiredResolve(type) {
  return (
    // Mappable
    IsComputed(type) ? FromComputed3(type.target, type.parameters) : IsRef(type) ? FromRef3(type.$ref) : IsIntersect(type) ? Intersect(FromRest4(type.allOf)) : IsUnion(type) ? Union(FromRest4(type.anyOf)) : IsObject2(type) ? FromObject5(type) : (
      // Intrinsic
      IsBigInt2(type) ? type : IsBoolean2(type) ? type : IsInteger(type) ? type : IsLiteral(type) ? type : IsNull2(type) ? type : IsNumber2(type) ? type : IsString2(type) ? type : IsSymbol2(type) ? type : IsUndefined2(type) ? type : (
        // Passthrough
        Object({})
      )
    )
  );
}
function Required(type, options) {
  if (IsMappedResult(type)) {
    return RequiredFromMappedResult(type, options);
  } else {
    return CreateType({ ...RequiredResolve(type), ...options });
  }
}

// node_modules/@sinclair/typebox/build/esm/type/required/required-from-mapped-result.mjs
function FromProperties12(P, options) {
  const Acc = {};
  for (const K2 of globalThis.Object.getOwnPropertyNames(P))
    Acc[K2] = Required(P[K2], options);
  return Acc;
}
function FromMappedResult6(R, options) {
  return FromProperties12(R.properties, options);
}
function RequiredFromMappedResult(R, options) {
  const P = FromMappedResult6(R, options);
  return MappedResult(P);
}

// node_modules/@sinclair/typebox/build/esm/type/module/compute.mjs
function DereferenceParameters(moduleProperties, types) {
  return types.map((type) => {
    return IsRef(type) ? Dereference(moduleProperties, type.$ref) : FromType2(moduleProperties, type);
  });
}
function Dereference(moduleProperties, ref) {
  return ref in moduleProperties ? IsRef(moduleProperties[ref]) ? Dereference(moduleProperties, moduleProperties[ref].$ref) : FromType2(moduleProperties, moduleProperties[ref]) : Never();
}
function FromAwaited(parameters) {
  return Awaited(parameters[0]);
}
function FromIndex(parameters) {
  return Index(parameters[0], parameters[1]);
}
function FromKeyOf(parameters) {
  return KeyOf(parameters[0]);
}
function FromPartial(parameters) {
  return Partial(parameters[0]);
}
function FromOmit(parameters) {
  return Omit(parameters[0], parameters[1]);
}
function FromPick(parameters) {
  return Pick(parameters[0], parameters[1]);
}
function FromRequired(parameters) {
  return Required(parameters[0]);
}
function FromComputed4(moduleProperties, target, parameters) {
  const dereferenced = DereferenceParameters(moduleProperties, parameters);
  return target === "Awaited" ? FromAwaited(dereferenced) : target === "Index" ? FromIndex(dereferenced) : target === "KeyOf" ? FromKeyOf(dereferenced) : target === "Partial" ? FromPartial(dereferenced) : target === "Omit" ? FromOmit(dereferenced) : target === "Pick" ? FromPick(dereferenced) : target === "Required" ? FromRequired(dereferenced) : Never();
}
function FromArray3(moduleProperties, type) {
  return Array(FromType2(moduleProperties, type));
}
function FromAsyncIterator2(moduleProperties, type) {
  return AsyncIterator(FromType2(moduleProperties, type));
}
function FromConstructor2(moduleProperties, parameters, instanceType) {
  return Constructor(FromTypes2(moduleProperties, parameters), FromType2(moduleProperties, instanceType));
}
function FromFunction2(moduleProperties, parameters, returnType) {
  return Function(FromTypes2(moduleProperties, parameters), FromType2(moduleProperties, returnType));
}
function FromIntersect5(moduleProperties, types) {
  return Intersect(FromTypes2(moduleProperties, types));
}
function FromIterator2(moduleProperties, type) {
  return Iterator(FromType2(moduleProperties, type));
}
function FromObject6(moduleProperties, properties) {
  return Object(globalThis.Object.keys(properties).reduce((result, key) => {
    return { ...result, [key]: FromType2(moduleProperties, properties[key]) };
  }, {}));
}
function FromRecord2(moduleProperties, type) {
  const [value, pattern] = [FromType2(moduleProperties, RecordValue(type)), RecordPattern(type)];
  const result = CloneType(type);
  result.patternProperties[pattern] = value;
  return result;
}
function FromTransform(moduleProperties, transform) {
  return IsRef(transform) ? { ...Dereference(moduleProperties, transform.$ref), [TransformKind]: transform[TransformKind] } : transform;
}
function FromTuple2(moduleProperties, types) {
  return Tuple(FromTypes2(moduleProperties, types));
}
function FromUnion5(moduleProperties, types) {
  return Union(FromTypes2(moduleProperties, types));
}
function FromTypes2(moduleProperties, types) {
  return types.map((type) => FromType2(moduleProperties, type));
}
function FromType2(moduleProperties, type) {
  return (
    // Modifiers
    IsOptional(type) ? CreateType(FromType2(moduleProperties, Discard(type, [OptionalKind])), type) : IsReadonly(type) ? CreateType(FromType2(moduleProperties, Discard(type, [ReadonlyKind])), type) : (
      // Transform
      IsTransform(type) ? CreateType(FromTransform(moduleProperties, type), type) : (
        // Types
        IsArray2(type) ? CreateType(FromArray3(moduleProperties, type.items), type) : IsAsyncIterator2(type) ? CreateType(FromAsyncIterator2(moduleProperties, type.items), type) : IsComputed(type) ? CreateType(FromComputed4(moduleProperties, type.target, type.parameters)) : IsConstructor(type) ? CreateType(FromConstructor2(moduleProperties, type.parameters, type.returns), type) : IsFunction2(type) ? CreateType(FromFunction2(moduleProperties, type.parameters, type.returns), type) : IsIntersect(type) ? CreateType(FromIntersect5(moduleProperties, type.allOf), type) : IsIterator2(type) ? CreateType(FromIterator2(moduleProperties, type.items), type) : IsObject2(type) ? CreateType(FromObject6(moduleProperties, type.properties), type) : IsRecord(type) ? CreateType(FromRecord2(moduleProperties, type)) : IsTuple(type) ? CreateType(FromTuple2(moduleProperties, type.items || []), type) : IsUnion(type) ? CreateType(FromUnion5(moduleProperties, type.anyOf), type) : type
      )
    )
  );
}
function ComputeType(moduleProperties, key) {
  return key in moduleProperties ? FromType2(moduleProperties, moduleProperties[key]) : Never();
}
function ComputeModuleProperties(moduleProperties) {
  return globalThis.Object.getOwnPropertyNames(moduleProperties).reduce((result, key) => {
    return { ...result, [key]: ComputeType(moduleProperties, key) };
  }, {});
}

// node_modules/@sinclair/typebox/build/esm/type/module/module.mjs
var TModule = class {
  constructor($defs) {
    const computed = ComputeModuleProperties($defs);
    const identified = this.WithIdentifiers(computed);
    this.$defs = identified;
  }
  /** `[Json]` Imports a Type by Key. */
  Import(key, options) {
    const $defs = { ...this.$defs, [key]: CreateType(this.$defs[key], options) };
    return CreateType({ [Kind]: "Import", $defs, $ref: key });
  }
  // prettier-ignore
  WithIdentifiers($defs) {
    return globalThis.Object.getOwnPropertyNames($defs).reduce((result, key) => {
      return { ...result, [key]: { ...$defs[key], $id: key } };
    }, {});
  }
};
function Module(properties) {
  return new TModule(properties);
}

// node_modules/@sinclair/typebox/build/esm/type/not/not.mjs
function Not(type, options) {
  return CreateType({ [Kind]: "Not", not: type }, options);
}

// node_modules/@sinclair/typebox/build/esm/type/parameters/parameters.mjs
function Parameters(schema, options) {
  return IsFunction2(schema) ? Tuple(schema.parameters, options) : Never();
}

// node_modules/@sinclair/typebox/build/esm/type/recursive/recursive.mjs
var Ordinal = 0;
function Recursive(callback, options = {}) {
  if (IsUndefined(options.$id))
    options.$id = `T${Ordinal++}`;
  const thisType = CloneType(callback({ [Kind]: "This", $ref: `${options.$id}` }));
  thisType.$id = options.$id;
  return CreateType({ [Hint]: "Recursive", ...thisType }, options);
}

// node_modules/@sinclair/typebox/build/esm/type/regexp/regexp.mjs
function RegExp(unresolved, options) {
  const expr = IsString(unresolved) ? new globalThis.RegExp(unresolved) : unresolved;
  return CreateType({ [Kind]: "RegExp", type: "RegExp", source: expr.source, flags: expr.flags }, options);
}

// node_modules/@sinclair/typebox/build/esm/type/rest/rest.mjs
function RestResolve(T) {
  return IsIntersect(T) ? T.allOf : IsUnion(T) ? T.anyOf : IsTuple(T) ? T.items ?? [] : [];
}
function Rest(T) {
  return RestResolve(T);
}

// node_modules/@sinclair/typebox/build/esm/type/return-type/return-type.mjs
function ReturnType(schema, options) {
  return IsFunction2(schema) ? CreateType(schema.returns, options) : Never(options);
}

// node_modules/@sinclair/typebox/build/esm/type/transform/transform.mjs
var TransformDecodeBuilder = class {
  constructor(schema) {
    this.schema = schema;
  }
  Decode(decode) {
    return new TransformEncodeBuilder(this.schema, decode);
  }
};
var TransformEncodeBuilder = class {
  constructor(schema, decode) {
    this.schema = schema;
    this.decode = decode;
  }
  EncodeTransform(encode, schema) {
    const Encode = (value) => schema[TransformKind].Encode(encode(value));
    const Decode = (value) => this.decode(schema[TransformKind].Decode(value));
    const Codec = { Encode, Decode };
    return { ...schema, [TransformKind]: Codec };
  }
  EncodeSchema(encode, schema) {
    const Codec = { Decode: this.decode, Encode: encode };
    return { ...schema, [TransformKind]: Codec };
  }
  Encode(encode) {
    return IsTransform(this.schema) ? this.EncodeTransform(encode, this.schema) : this.EncodeSchema(encode, this.schema);
  }
};
function Transform(schema) {
  return new TransformDecodeBuilder(schema);
}

// node_modules/@sinclair/typebox/build/esm/type/void/void.mjs
function Void(options) {
  return CreateType({ [Kind]: "Void", type: "void" }, options);
}

// node_modules/@sinclair/typebox/build/esm/type/type/json.mjs
var JsonTypeBuilder = class {
  // ------------------------------------------------------------------------
  // Modifiers
  // ------------------------------------------------------------------------
  /** `[Json]` Creates a Readonly and Optional property */
  ReadonlyOptional(type) {
    return ReadonlyOptional(type);
  }
  /** `[Json]` Creates a Readonly property */
  Readonly(type, enable) {
    return Readonly(type, enable ?? true);
  }
  /** `[Json]` Creates a Optional property */
  Optional(type, enable) {
    return Optional(type, enable ?? true);
  }
  // ------------------------------------------------------------------------
  // Types
  // ------------------------------------------------------------------------
  /** `[Json]` Creates an Any type */
  Any(options) {
    return Any(options);
  }
  /** `[Json]` Creates an Array type */
  Array(items, options) {
    return Array(items, options);
  }
  /** `[Json]` Creates a Boolean type */
  Boolean(options) {
    return Boolean(options);
  }
  /** `[Json]` Intrinsic function to Capitalize LiteralString types */
  Capitalize(schema, options) {
    return Capitalize(schema, options);
  }
  /** `[Json]` Creates a Composite object type */
  Composite(schemas, options) {
    return Composite(schemas, options);
  }
  /** `[JavaScript]` Creates a readonly const type from the given value. */
  Const(value, options) {
    return Const(value, options);
  }
  /** `[Json]` Creates a Enum type */
  Enum(item, options) {
    return Enum(item, options);
  }
  /** `[Json]` Constructs a type by excluding from unionType all union members that are assignable to excludedMembers */
  Exclude(unionType, excludedMembers, options) {
    return Exclude(unionType, excludedMembers, options);
  }
  /** `[Json]` Creates a Conditional type */
  Extends(L, R, T, F, options) {
    return Extends(L, R, T, F, options);
  }
  /** `[Json]` Constructs a type by extracting from type all union members that are assignable to union */
  Extract(type, union, options) {
    return Extract(type, union, options);
  }
  /** `[Json]` Returns an Indexed property type for the given keys */
  Index(type, key, options) {
    return Index(type, key, options);
  }
  /** `[Json]` Creates an Integer type */
  Integer(options) {
    return Integer(options);
  }
  /** `[Json]` Creates an Intersect type */
  Intersect(types, options) {
    return Intersect(types, options);
  }
  /** `[Json]` Creates a KeyOf type */
  KeyOf(type, options) {
    return KeyOf(type, options);
  }
  /** `[Json]` Creates a Literal type */
  Literal(literalValue, options) {
    return Literal(literalValue, options);
  }
  /** `[Json]` Intrinsic function to Lowercase LiteralString types */
  Lowercase(type, options) {
    return Lowercase(type, options);
  }
  /** `[Json]` Creates a Mapped object type */
  Mapped(key, map, options) {
    return Mapped(key, map, options);
  }
  /** `[Json]` Creates a Type Definition Module. */
  Module(properties) {
    return Module(properties);
  }
  /** `[Json]` Creates a Never type */
  Never(options) {
    return Never(options);
  }
  /** `[Json]` Creates a Not type */
  Not(type, options) {
    return Not(type, options);
  }
  /** `[Json]` Creates a Null type */
  Null(options) {
    return Null(options);
  }
  /** `[Json]` Creates a Number type */
  Number(options) {
    return Number(options);
  }
  /** `[Json]` Creates an Object type */
  Object(properties, options) {
    return Object(properties, options);
  }
  /** `[Json]` Constructs a type whose keys are omitted from the given type */
  Omit(schema, selector, options) {
    return Omit(schema, selector, options);
  }
  /** `[Json]` Constructs a type where all properties are optional */
  Partial(type, options) {
    return Partial(type, options);
  }
  /** `[Json]` Constructs a type whose keys are picked from the given type */
  Pick(type, key, options) {
    return Pick(type, key, options);
  }
  /** `[Json]` Creates a Record type */
  Record(key, value, options) {
    return Record(key, value, options);
  }
  /** `[Json]` Creates a Recursive type */
  Recursive(callback, options) {
    return Recursive(callback, options);
  }
  /** `[Json]` Creates a Ref type. The referenced type must contain a $id */
  Ref(...args) {
    return Ref(args[0], args[1]);
  }
  /** `[Json]` Constructs a type where all properties are required */
  Required(type, options) {
    return Required(type, options);
  }
  /** `[Json]` Extracts interior Rest elements from Tuple, Intersect and Union types */
  Rest(type) {
    return Rest(type);
  }
  /** `[Json]` Creates a String type */
  String(options) {
    return String(options);
  }
  /** `[Json]` Creates a TemplateLiteral type */
  TemplateLiteral(unresolved, options) {
    return TemplateLiteral(unresolved, options);
  }
  /** `[Json]` Creates a Transform type */
  Transform(type) {
    return Transform(type);
  }
  /** `[Json]` Creates a Tuple type */
  Tuple(types, options) {
    return Tuple(types, options);
  }
  /** `[Json]` Intrinsic function to Uncapitalize LiteralString types */
  Uncapitalize(type, options) {
    return Uncapitalize(type, options);
  }
  /** `[Json]` Creates a Union type */
  Union(types, options) {
    return Union(types, options);
  }
  /** `[Json]` Creates an Unknown type */
  Unknown(options) {
    return Unknown(options);
  }
  /** `[Json]` Creates a Unsafe type that will infers as the generic argument T */
  Unsafe(options) {
    return Unsafe(options);
  }
  /** `[Json]` Intrinsic function to Uppercase LiteralString types */
  Uppercase(schema, options) {
    return Uppercase(schema, options);
  }
};

// node_modules/@sinclair/typebox/build/esm/type/type/type.mjs
var type_exports3 = {};
__export(type_exports3, {
  Any: () => Any,
  Argument: () => Argument,
  Array: () => Array,
  AsyncIterator: () => AsyncIterator,
  Awaited: () => Awaited,
  BigInt: () => BigInt,
  Boolean: () => Boolean,
  Capitalize: () => Capitalize,
  Composite: () => Composite,
  Const: () => Const,
  Constructor: () => Constructor,
  ConstructorParameters: () => ConstructorParameters,
  Date: () => Date,
  Enum: () => Enum,
  Exclude: () => Exclude,
  Extends: () => Extends,
  Extract: () => Extract,
  Function: () => Function,
  Index: () => Index,
  InstanceType: () => InstanceType,
  Instantiate: () => Instantiate,
  Integer: () => Integer,
  Intersect: () => Intersect,
  Iterator: () => Iterator,
  KeyOf: () => KeyOf,
  Literal: () => Literal,
  Lowercase: () => Lowercase,
  Mapped: () => Mapped,
  Module: () => Module,
  Never: () => Never,
  Not: () => Not,
  Null: () => Null,
  Number: () => Number,
  Object: () => Object,
  Omit: () => Omit,
  Optional: () => Optional,
  Parameters: () => Parameters,
  Partial: () => Partial,
  Pick: () => Pick,
  Promise: () => Promise2,
  Readonly: () => Readonly,
  ReadonlyOptional: () => ReadonlyOptional,
  Record: () => Record,
  Recursive: () => Recursive,
  Ref: () => Ref,
  RegExp: () => RegExp,
  Required: () => Required,
  Rest: () => Rest,
  ReturnType: () => ReturnType,
  String: () => String,
  Symbol: () => Symbol,
  TemplateLiteral: () => TemplateLiteral,
  Transform: () => Transform,
  Tuple: () => Tuple,
  Uint8Array: () => Uint8Array,
  Uncapitalize: () => Uncapitalize,
  Undefined: () => Undefined,
  Union: () => Union,
  Unknown: () => Unknown,
  Unsafe: () => Unsafe,
  Uppercase: () => Uppercase,
  Void: () => Void
});

// node_modules/@sinclair/typebox/build/esm/type/type/javascript.mjs
var JavaScriptTypeBuilder = class extends JsonTypeBuilder {
  /** `[JavaScript]` Creates a Generic Argument Type */
  Argument(index) {
    return Argument(index);
  }
  /** `[JavaScript]` Creates a AsyncIterator type */
  AsyncIterator(items, options) {
    return AsyncIterator(items, options);
  }
  /** `[JavaScript]` Constructs a type by recursively unwrapping Promise types */
  Awaited(schema, options) {
    return Awaited(schema, options);
  }
  /** `[JavaScript]` Creates a BigInt type */
  BigInt(options) {
    return BigInt(options);
  }
  /** `[JavaScript]` Extracts the ConstructorParameters from the given Constructor type */
  ConstructorParameters(schema, options) {
    return ConstructorParameters(schema, options);
  }
  /** `[JavaScript]` Creates a Constructor type */
  Constructor(parameters, instanceType, options) {
    return Constructor(parameters, instanceType, options);
  }
  /** `[JavaScript]` Creates a Date type */
  Date(options = {}) {
    return Date(options);
  }
  /** `[JavaScript]` Creates a Function type */
  Function(parameters, returnType, options) {
    return Function(parameters, returnType, options);
  }
  /** `[JavaScript]` Extracts the InstanceType from the given Constructor type */
  InstanceType(schema, options) {
    return InstanceType(schema, options);
  }
  /** `[JavaScript]` Instantiates a type with the given parameters */
  Instantiate(schema, parameters) {
    return Instantiate(schema, parameters);
  }
  /** `[JavaScript]` Creates an Iterator type */
  Iterator(items, options) {
    return Iterator(items, options);
  }
  /** `[JavaScript]` Extracts the Parameters from the given Function type */
  Parameters(schema, options) {
    return Parameters(schema, options);
  }
  /** `[JavaScript]` Creates a Promise type */
  Promise(item, options) {
    return Promise2(item, options);
  }
  /** `[JavaScript]` Creates a RegExp type */
  RegExp(unresolved, options) {
    return RegExp(unresolved, options);
  }
  /** `[JavaScript]` Extracts the ReturnType from the given Function type */
  ReturnType(type, options) {
    return ReturnType(type, options);
  }
  /** `[JavaScript]` Creates a Symbol type */
  Symbol(options) {
    return Symbol(options);
  }
  /** `[JavaScript]` Creates a Undefined type */
  Undefined(options) {
    return Undefined(options);
  }
  /** `[JavaScript]` Creates a Uint8Array type */
  Uint8Array(options) {
    return Uint8Array(options);
  }
  /** `[JavaScript]` Creates a Void type */
  Void(options) {
    return Void(options);
  }
};

// node_modules/@sinclair/typebox/build/esm/type/type/index.mjs
var Type = type_exports3;
export {
  Any,
  Argument,
  Array,
  AsyncIterator,
  Awaited,
  BigInt,
  Boolean,
  Capitalize,
  Clone,
  CloneRest,
  CloneType,
  Composite,
  Const,
  Constructor,
  ConstructorParameters,
  CreateType,
  Date,
  Enum,
  Exclude,
  ExcludeFromMappedResult,
  ExcludeFromTemplateLiteral,
  Extends,
  ExtendsCheck,
  ExtendsFromMappedKey,
  ExtendsFromMappedResult,
  ExtendsResolverError,
  ExtendsResult,
  ExtendsUndefinedCheck,
  Extract,
  ExtractFromMappedResult,
  ExtractFromTemplateLiteral,
  format_exports as FormatRegistry,
  FromTypes,
  Function,
  Hint,
  Increment,
  Index,
  IndexFromComputed,
  IndexFromMappedKey,
  IndexFromMappedResult,
  IndexFromPropertyKey,
  IndexFromPropertyKeys,
  IndexPropertyKeys,
  InstanceType,
  Instantiate,
  Integer,
  Intersect,
  IntersectEvaluated,
  Intrinsic,
  IntrinsicFromMappedKey,
  IsTemplateLiteralExpressionFinite,
  IsTemplateLiteralFinite,
  Iterator,
  JavaScriptTypeBuilder,
  JsonTypeBuilder,
  KeyOf,
  KeyOfFromMappedResult,
  KeyOfPattern,
  KeyOfPropertyEntries,
  KeyOfPropertyKeys,
  KeyOfPropertyKeysToRest,
  Kind,
  kind_exports as KindGuard,
  Literal,
  Lowercase,
  Mapped,
  MappedFunctionReturnType,
  MappedKey,
  MappedResult,
  Module,
  Never,
  Not,
  Null,
  Number,
  Object,
  Omit,
  OmitFromMappedKey,
  OmitFromMappedResult,
  Optional,
  OptionalFromMappedResult,
  OptionalKind,
  Parameters,
  Partial,
  PartialFromMappedResult,
  PatternBoolean,
  PatternBooleanExact,
  PatternNever,
  PatternNeverExact,
  PatternNumber,
  PatternNumberExact,
  PatternString,
  PatternStringExact,
  Pick,
  PickFromMappedKey,
  PickFromMappedResult,
  Promise2 as Promise,
  Readonly,
  ReadonlyFromMappedResult,
  ReadonlyKind,
  ReadonlyOptional,
  Record,
  RecordKey,
  RecordPattern,
  RecordValue,
  Recursive,
  Ref,
  RegExp,
  Required,
  RequiredFromMappedResult,
  Rest,
  ReturnType,
  SetComplement,
  SetDistinct,
  SetIncludes,
  SetIntersect,
  SetIntersectMany,
  SetIsSubset,
  SetUnion,
  SetUnionMany,
  String,
  Symbol,
  TModule,
  TemplateLiteral,
  TemplateLiteralExpressionGenerate,
  TemplateLiteralFiniteError,
  TemplateLiteralGenerate,
  TemplateLiteralGenerateError,
  TemplateLiteralParse,
  TemplateLiteralParseExact,
  TemplateLiteralParserError,
  TemplateLiteralPattern,
  TemplateLiteralPatternError,
  TemplateLiteralSyntax,
  TemplateLiteralToUnion,
  Transform,
  TransformDecodeBuilder,
  TransformEncodeBuilder,
  TransformKind,
  Tuple,
  Type,
  TypeBoxError,
  type_exports2 as TypeGuard,
  type_exports as TypeRegistry,
  Uint8Array,
  Uncapitalize,
  Undefined,
  Union,
  UnionEvaluated,
  Unknown,
  Unsafe,
  Uppercase,
  value_exports as ValueGuard,
  Void
};
//# sourceMappingURL=@sinclair_typebox.js.map
