import {
  __commonJS
} from "./chunk-KWPVD4H7.js";

// browser-external:node:dns/promises
var require_promises = __commonJS({
  "browser-external:node:dns/promises"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:dns/promises" has been externalized for browser compatibility. Cannot access "node:dns/promises.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});
export default require_promises();
//# sourceMappingURL=promises-BIUHY4SO.js.map
