import {
  add_locations,
  await_block,
  bind_this,
  check_target,
  get as get2,
  hmr,
  html,
  if_block,
  init,
  legacy_api,
  prop,
  readable,
  set_class,
  set_style,
  setup_stores,
  slot,
  store_get,
  store_unsub,
  validate_store
} from "./chunk-CRCQ7E27.js";
import {
  append,
  comment,
  ns_template,
  preventDefault,
  set_text,
  template
} from "./chunk-OSNF6FE7.js";
import {
  FILENAME,
  HMR,
  child,
  deep_read_state,
  derived_safe_equal,
  equals,
  event,
  first_child,
  get,
  legacy_pre_effect,
  legacy_pre_effect_reset,
  mutable_source,
  pop,
  push,
  reset,
  set,
  sibling,
  strict_equals,
  template_effect
} from "./chunk-PJ2X7CWE.js";

// node_modules/sveltekit-superforms/dist/client/SuperDebug.svelte
import { browser } from "$app/environment";
import { page } from "$app/stores";

// node_modules/sveltekit-superforms/dist/client/clipboardCopy.js
function makeError() {
  return new DOMException("The request is not allowed", "NotAllowedError");
}
async function copyClipboardApi(text) {
  if (!navigator.clipboard) {
    throw makeError();
  }
  return navigator.clipboard.writeText(text);
}
async function copyExecCommand(text) {
  const span = document.createElement("span");
  span.textContent = text;
  span.style.whiteSpace = "pre";
  span.style.webkitUserSelect = "auto";
  span.style.userSelect = "all";
  document.body.appendChild(span);
  const selection = window.getSelection();
  const range = window.document.createRange();
  selection == null ? void 0 : selection.removeAllRanges();
  range.selectNode(span);
  selection == null ? void 0 : selection.addRange(range);
  let success = false;
  try {
    success = window.document.execCommand("copy");
  } finally {
    selection == null ? void 0 : selection.removeAllRanges();
    window.document.body.removeChild(span);
  }
  if (!success) throw makeError();
}
async function clipboardCopy(text) {
  try {
    await copyClipboardApi(text);
  } catch (err) {
    try {
      await copyExecCommand(text);
    } catch (err2) {
      throw err2 || err || makeError();
    }
  }
}

// node_modules/sveltekit-superforms/dist/client/SuperDebug.svelte
SuperDebug[FILENAME] = "node_modules/sveltekit-superforms/dist/client/SuperDebug.svelte";
var root_1 = add_locations(
  template(`<style>.super-debug--absolute {
			position: absolute;
		}

		.super-debug--top-0 {
			top: 0;
		}

		.super-debug--inset-x-0 {
			left: 0px;
			right: 0px;
		}

		.super-debug--hidden {
			height: 0;
			overflow: hidden;
		}

		.super-debug--hidden:not(.super-debug--with-label) {
			height: 1.5em;
		}

		.super-debug--rotated {
			transform: rotate(180deg);
		}

		.super-debug {
			--_sd-bg-color: var(--sd-bg-color, var(--sd-vscode-bg-color, rgb(30, 41, 59)));
			position: relative;
			background-color: var(--_sd-bg-color);
			border-radius: 0.5rem;
			overflow: hidden;
		}

		.super-debug--pre {
			overflow-x: auto;
		}

		.super-debug--collapse {
			display: block;
			width: 100%;
			color: rgba(255, 255, 255, 0.25);
			background-color: rgba(255, 255, 255, 0.15);
			padding: 5px 0;
			display: flex;
			justify-content: center;
			border-color: transparent;
			margin: 0;
			padding: 3px 0;
		}

		.super-debug--collapse:focus {
			color: #fafafa;
			background-color: rgba(255, 255, 255, 0.25);
		}

		.super-debug--collapse:is(:hover) {
			color: rgba(255, 255, 255, 0.35);
			background-color: rgba(255, 255, 255, 0.25);
		}

		.super-debug--status {
			display: flex;
			padding: 1em;
			padding-bottom: 0;
			justify-content: space-between;
			font-family:
				Inconsolata, Monaco, Consolas, 'Lucida Console', 'Courier New', Courier, monospace;
		}

		.super-debug--right-status {
			display: flex;
			gap: 0.55em;
		}

		.super-debug--copy {
			margin: 0;
			padding: 0;
			padding-top: 2px;
			background-color: transparent;
			border: 0;
			color: #666;
			cursor: pointer;
		}

		.super-debug--copy:hover {
			background-color: transparent;
			color: #666;
		}

		.super-debug--copy:focus {
			background-color: transparent;
			color: #666;
		}

		.super-debug--label {
			color: var(--sd-label-color, var(--sd-vscode-label-color, white));
		}

		.super-debug--promise-loading {
			color: var(--sd-promise-loading-color, var(--sd-vscode-promise-loading-color, #999));
		}

		.super-debug--promise-rejected {
			color: var(--sd-promise-rejected-color, var(--sd-vscode-promise-rejected-color, #ff475d));
		}

		.super-debug pre {
			color: var(--sd-code-default, var(--sd-vscode-code-default, #999));
			background-color: var(--_sd-bg-color);
			font-size: 1em;
			margin-bottom: 0;
			padding: 1em 0 1em 1em;
		}

		.super-debug--info {
			color: var(--sd-info, var(--sd-vscode-info, rgb(85, 85, 255)));
		}

		.super-debug--success {
			color: var(--sd-success, var(--sd-vscode-success, #2cd212));
		}

		.super-debug--redirect {
			color: var(--sd-redirect, var(--sd-vscode-redirect, #03cae5));
		}

		.super-debug--error {
			color: var(--sd-error, var(--sd-vscode-error, #ff475d));
		}

		.super-debug--code .key {
			color: var(--sd-code-key, var(--sd-vscode-code-key, #eab308));
		}

		.super-debug--code .string {
			color: var(--sd-code-string, var(--sd-vscode-code-string, #6ec687));
		}

		.super-debug--code .date {
			color: var(--sd-code-date, var(--sd-vscode-code-date, #f06962));
		}

		.super-debug--code .boolean {
			color: var(--sd-code-boolean, var(--sd-vscode-code-boolean, #79b8ff));
		}

		.super-debug--code .number {
			color: var(--sd-code-number, var(--sd-vscode-code-number, #af77e9));
		}

		.super-debug--code .bigint {
			color: var(--sd-code-bigint, var(--sd-vscode-code-bigint, #af77e9));
		}

		.super-debug--code .null {
			color: var(--sd-code-null, var(--sd-vscode-code-null, #238afe));
		}

		.super-debug--code .nan {
			color: var(--sd-code-nan, var(--sd-vscode-code-nan, #af77e9));
		}

		.super-debug--code .undefined {
			color: var(--sd-code-undefined, var(--sd-vscode-code-undefined, #238afe));
		}

		.super-debug--code .function {
			color: var(--sd-code-function, var(--sd-vscode-code-function, #f06962));
		}

		.super-debug--code .symbol {
			color: var(--sd-code-symbol, var(--sd-vscode-code-symbol, #4de0c5));
		}

		.super-debug--code .error {
			color: var(--sd-code-error, var(--sd-vscode-code-error, #ff475d));
		}

		.super-debug pre::-webkit-scrollbar {
			width: var(--sd-sb-width, var(--sd-vscode-sb-width, 1rem));
			height: var(--sd-sb-height, var(--sd-vscode-sb-height, 1rem));
		}

		.super-debug pre::-webkit-scrollbar-track {
			border-radius: 12px;
			background-color: var(
				--sd-sb-track-color,
				var(--sd-vscode-sb-track-color, hsl(0, 0%, 40%, 0.2))
			);
		}
		.super-debug:is(:focus-within, :hover) pre::-webkit-scrollbar-track {
			border-radius: 12px;
			background-color: var(
				--sd-sb-track-color-focus,
				var(--sd-vscode-sb-track-color-focus, hsl(0, 0%, 50%, 0.2))
			);
		}

		.super-debug pre::-webkit-scrollbar-thumb {
			border-radius: 12px;
			background-color: var(
				--sd-sb-thumb-color,
				var(--sd-vscode-sb-thumb-color, hsl(217, 50%, 50%, 0.5))
			);
		}
		.super-debug:is(:focus-within, :hover) pre::-webkit-scrollbar-thumb {
			border-radius: 12px;
			background-color: var(
				--sd-sb-thumb-color-focus,
				var(--sd-vscode-sb-thumb-color-focus, hsl(217, 50%, 50%))
			);
		}</style>`),
  SuperDebug[FILENAME],
  [[377, 1]]
);
var root_3 = add_locations(ns_template(`<svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M7 9.667A2.667 2.667 0 0 1 9.667 7h8.666A2.667 2.667 0 0 1 21 9.667v8.666A2.667 2.667 0 0 1 18.333 21H9.667A2.667 2.667 0 0 1 7 18.333z"></path><path d="M4.012 16.737A2.005 2.005 0 0 1 3 15V5c0-1.1.9-2 2-2h10c.75 0 1.158.385 1.5 1"></path></g></svg>`), SuperDebug[FILENAME], [
  [
    613,
    6,
    [
      [614, 8, [[620, 9], [622, 10]]]
    ]
  ]
]);
var root_4 = add_locations(ns_template(`<svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M15 12v6m-3-3h6"></path><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></g></svg>`), SuperDebug[FILENAME], [
  [
    628,
    6,
    [
      [
        629,
        8,
        [[635, 9], [635, 37], [642, 10]]
      ]
    ]
  ]
]);
var root_5 = add_locations(template(`<div> </div>`), SuperDebug[FILENAME], [[648, 5]]);
var root_9 = add_locations(template(`<span class="super-debug--promise-rejected">Rejected:</span> <!>`, 1), SuperDebug[FILENAME], [[668, 23]]);
var root_10 = add_locations(template(`<div class="super-debug--promise-loading">Loading data...</div>`), SuperDebug[FILENAME], [[665, 70]]);
var root_12 = add_locations(template(`<button type="button" class="super-debug--collapse" aria-label="Collapse"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" d="M4.08 11.92L12 4l7.92 7.92l-1.42 1.41l-5.5-5.5V22h-2V7.83l-5.5 5.5l-1.42-1.41M12 4h10V2H2v2h10Z"></path></svg></button>`), SuperDebug[FILENAME], [
  [
    675,
    3,
    [[681, 4, [[687, 6]]]]
  ]
]);
var root_2 = add_locations(template(`<div dir="ltr"><div><div class="super-debug--label"> </div> <div class="super-debug--right-status"><button type="button" class="super-debug--copy"><!></button> <!></div></div> <pre><code class="super-debug--code"><!></code></pre> <!></div>`), SuperDebug[FILENAME], [
  [
    598,
    1,
    [
      [
        604,
        2,
        [
          [609, 3],
          [610, 3, [[611, 4]]]
        ]
      ],
      [659, 2, [[663, 19]]]
    ]
  ]
]);
var root = add_locations(template(`<!> <!>`, 1), SuperDebug[FILENAME], []);
function SuperDebug($$anchor, $$props) {
  check_target(new.target);
  push($$props, false, SuperDebug);
  const [$$stores, $$cleanup] = setup_stores();
  const $page = () => (validate_store(page, "page"), store_get(page, "$page", $$stores));
  const $debugData = () => (validate_store(get(debugData), "debugData"), store_get(get(debugData), "$debugData", $$stores));
  const themeStyle = mutable_source();
  const debugData = mutable_source();
  let styleInit = mutable_source(false);
  let data = prop($$props, "data", 8);
  let display = prop($$props, "display", 8, true);
  let status = prop($$props, "status", 8, true);
  let label = prop($$props, "label", 8, "");
  let stringTruncate = prop($$props, "stringTruncate", 8, 120);
  let ref = prop($$props, "ref", 12, void 0);
  let promise = prop($$props, "promise", 8, false);
  let raw = prop($$props, "raw", 8, false);
  let functions = prop($$props, "functions", 8, false);
  let theme = prop($$props, "theme", 8, "default");
  let collapsible = prop($$props, "collapsible", 8, false);
  let collapsed = prop($$props, "collapsed", 12, false);
  if (browser && collapsible()) setCollapse();
  function setCollapse(status2 = void 0) {
    let data2;
    const route = $page().route.id ?? "";
    try {
      if (sessionStorage.SuperDebug) {
        data2 = JSON.parse(sessionStorage.SuperDebug);
      }
      data2 = {
        collapsed: data2 && data2.collapsed ? data2.collapsed : {}
      };
      data2.collapsed[route] = strict_equals(status2, void 0) ? data2.collapsed[route] ?? collapsed() : status2;
    } catch {
      data2 = { collapsed: { [route]: collapsed() } };
    }
    if (strict_equals(status2, void 0, false)) {
      sessionStorage.SuperDebug = JSON.stringify(data2);
    }
    collapsed(data2.collapsed[route]);
  }
  let copied = mutable_source();
  async function copyContent(e) {
    if (!e.target) return;
    const parent = (
      /** @type {HTMLElement} */
      e.target.closest(".super-debug")
    );
    if (!parent) return;
    const codeEl = (
      /** @type {HTMLPreElement} */
      parent.querySelector(".super-debug--code")
    );
    if (!codeEl) return;
    clearTimeout(get(copied));
    await clipboardCopy(codeEl.innerText);
    set(copied, setTimeout(() => set(copied, void 0), 900));
  }
  function fileToJSON(file) {
    return {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: new Date(file.lastModified)
    };
  }
  function syntaxHighlight(json) {
    switch (typeof json) {
      case "function": {
        return `<span class="function">[function ${json.name ?? "unnamed"}]</span>`;
      }
      case "symbol": {
        return `<span class="symbol">${json.toString()}</span>`;
      }
    }
    const encodedString = JSON.stringify(
      json,
      function(key, value) {
        if (strict_equals(value, void 0)) {
          return "#}#undefined";
        }
        if (strict_equals(typeof this, "object") && this[key] instanceof Date) {
          return "#}D#" + (isNaN(this[key]) ? "Invalid Date" : value);
        }
        if (strict_equals(typeof value, "number")) {
          if (equals(value, Number.POSITIVE_INFINITY)) return "#}#Inf";
          if (equals(value, Number.NEGATIVE_INFINITY)) return "#}#-Inf";
          if (isNaN(value)) return "#}#NaN";
        }
        if (strict_equals(typeof value, "bigint")) {
          return "#}BI#" + value;
        }
        if (strict_equals(typeof value, "function") && functions()) {
          return `#}F#[function ${value.name}]`;
        }
        if (value instanceof Error) {
          return `#}E#${value.name}: ${value.message || value.cause || "(No error message)"}`;
        }
        if (value instanceof Set) {
          return Array.from(value);
        }
        if (value instanceof Map) {
          return Array.from(value.entries());
        }
        if (strict_equals(typeof this, "object") && equals(typeof this[key], "object") && this[key] && "toExponential" in this[key]) {
          return "#}DE#" + this[key].toString();
        }
        if (browser && strict_equals(typeof this, "object") && this[key] instanceof File) {
          return fileToJSON(this[key]);
        }
        if (browser && strict_equals(typeof this, "object") && this[key] instanceof FileList) {
          const list = this[key];
          const output = [];
          for (let i = 0; i < list.length; i++) {
            const file = list.item(i);
            if (file) output.push(fileToJSON(file));
          }
          return output;
        }
        return value;
      },
      2
    ).replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;");
    return encodedString.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+-]?\d+)?)/g, function(match) {
      let cls = "number";
      if (/^"/.test(match)) {
        if (/:$/.test(match)) {
          cls = "key";
          match = match.slice(1, -2) + ":";
        } else {
          cls = "string";
          match = stringTruncate() > 0 && match.length > stringTruncate() ? match.slice(0, stringTruncate() / 2) + `[..${match.length - stringTruncate()}/${match.length}..]` + match.slice(-stringTruncate() / 2) : match;
          if (equals(match, '"#}#undefined"')) {
            cls = "undefined";
            match = "undefined";
          } else if (match.startsWith('"#}D#')) {
            cls = "date";
            match = match.slice(5, -1);
          } else if (equals(match, '"#}#NaN"')) {
            cls = "nan";
            match = "NaN";
          } else if (equals(match, '"#}#Inf"')) {
            cls = "nan";
            match = "Infinity";
          } else if (equals(match, '"#}#-Inf"')) {
            cls = "nan";
            match = "-Infinity";
          } else if (match.startsWith('"#}BI#')) {
            cls = "bigint";
            match = match.slice(6, -1) + "n";
          } else if (match.startsWith('"#}F#')) {
            cls = "function";
            match = match.slice(5, -1);
          } else if (match.startsWith('"#}E#')) {
            cls = "error";
            match = match.slice(5, -1);
          } else if (match.startsWith('"#}DE#')) {
            cls = "number";
            match = match.slice(6, -1);
          }
        }
      } else if (/true|false/.test(match)) {
        cls = "boolean";
      } else if (/null/.test(match)) {
        cls = "null";
      }
      return '<span class="' + cls + '">' + match + "</span>";
    });
  }
  function assertPromise(data2, raw2, promise2) {
    if (raw2) {
      return false;
    }
    return promise2 || strict_equals(typeof data2, "object") && strict_equals(data2, null, false) && "then" in data2 && strict_equals(typeof data2["then"], "function");
  }
  function assertStore(data2, raw2) {
    if (raw2) {
      return false;
    }
    return strict_equals(typeof data2, "object") && strict_equals(data2, null, false) && "subscribe" in data2 && strict_equals(typeof data2["subscribe"], "function");
  }
  legacy_pre_effect(() => deep_read_state(theme()), () => {
    set(themeStyle, strict_equals(theme(), "vscode") ? `
      --sd-vscode-bg-color: #1f1f1f;
      --sd-vscode-label-color: #cccccc;
      --sd-vscode-code-default: #8c8a89;
      --sd-vscode-code-key: #9cdcfe;
      --sd-vscode-code-string: #ce9171;
      --sd-vscode-code-number: #b5c180;
      --sd-vscode-code-boolean: #4a9cd6;
      --sd-vscode-code-null: #4a9cd6;
      --sd-vscode-code-undefined: #4a9cd6;
      --sd-vscode-code-nan: #4a9cd6;
      --sd-vscode-code-symbol: #4de0c5;
      --sd-vscode-sb-thumb-color: #35373a;
      --sd-vscode-sb-thumb-color-focus: #4b4d50;
    ` : void 0);
  });
  legacy_pre_effect(
    () => (deep_read_state(data()), deep_read_state(raw()), readable),
    () => {
      store_unsub(set(debugData, assertStore(data(), raw()) ? data() : readable(data())), "$debugData", $$stores);
    }
  );
  legacy_pre_effect_reset();
  init();
  var fragment = root();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var style = root_1();
      const init2 = derived_safe_equal(() => set(styleInit, true));
      get(init2);
      append($$anchor2, style);
    };
    if_block(node, ($$render) => {
      if (!get(styleInit)) $$render(consequent);
    });
  }
  var node_1 = sibling(node, 2);
  {
    var consequent_5 = ($$anchor2) => {
      var div = root_2();
      let classes;
      var div_1 = child(div);
      var div_2 = child(div_1);
      var text = child(div_2, true);
      reset(div_2);
      var div_3 = sibling(div_2, 2);
      var button = child(div_3);
      var node_2 = child(button);
      {
        var consequent_1 = ($$anchor3) => {
          var svg = root_3();
          append($$anchor3, svg);
        };
        var alternate = ($$anchor3) => {
          var svg_1 = root_4();
          append($$anchor3, svg_1);
        };
        if_block(node_2, ($$render) => {
          if (!get(copied)) $$render(consequent_1);
          else $$render(alternate, false);
        });
      }
      reset(button);
      var node_3 = sibling(button, 2);
      {
        var consequent_2 = ($$anchor3) => {
          var div_4 = root_5();
          let classes_1;
          var text_1 = child(div_4, true);
          reset(div_4);
          template_effect(
            ($0) => {
              classes_1 = set_class(div_4, 1, "", null, classes_1, $0);
              set_text(text_1, $page().status);
            },
            [
              () => ({
                "super-debug--info": $page().status < 200,
                "super-debug--success": $page().status >= 200 && $page().status < 300,
                "super-debug--redirect": $page().status >= 300 && $page().status < 400,
                "super-debug--error": $page().status >= 400
              })
            ],
            derived_safe_equal
          );
          append($$anchor3, div_4);
        };
        if_block(node_3, ($$render) => {
          if (status()) $$render(consequent_2);
        });
      }
      reset(div_3);
      reset(div_1);
      var pre = sibling(div_1, 2);
      let classes_2;
      var code = child(pre);
      var node_4 = child(code);
      slot(node_4, $$props, "default", {}, ($$anchor3) => {
        var fragment_1 = comment();
        var node_5 = first_child(fragment_1);
        {
          var consequent_3 = ($$anchor4) => {
            var fragment_2 = comment();
            var node_6 = first_child(fragment_2);
            await_block(
              node_6,
              $debugData,
              ($$anchor5) => {
                var div_5 = root_10();
                append($$anchor5, div_5);
              },
              ($$anchor5, result) => {
                var fragment_3 = comment();
                var node_7 = first_child(fragment_3);
                html(node_7, () => syntaxHighlight(assertStore(get(result), raw()) ? get2(get(result)) : get(result)), false, false);
                append($$anchor5, fragment_3);
              },
              ($$anchor5, error) => {
                var fragment_4 = root_9();
                var node_8 = sibling(first_child(fragment_4), 2);
                html(node_8, () => syntaxHighlight(get(error)), false, false);
                append($$anchor5, fragment_4);
              }
            );
            append($$anchor4, fragment_2);
          };
          var alternate_1 = ($$anchor4) => {
            var fragment_5 = comment();
            var node_9 = first_child(fragment_5);
            html(node_9, () => syntaxHighlight($debugData()), false, false);
            append($$anchor4, fragment_5);
          };
          if_block(node_5, ($$render) => {
            if (assertPromise($debugData(), raw(), promise())) $$render(consequent_3);
            else $$render(alternate_1, false);
          });
        }
        append($$anchor3, fragment_1);
      });
      reset(code);
      reset(pre);
      bind_this(pre, ($$value) => ref($$value), () => ref());
      var node_10 = sibling(pre, 2);
      {
        var consequent_4 = ($$anchor3) => {
          var button_1 = root_12();
          var svg_2 = child(button_1);
          let classes_3;
          reset(button_1);
          template_effect(
            ($0) => classes_3 = set_class(svg_2, 0, "", null, classes_3, $0),
            [
              () => ({ "super-debug--rotated": collapsed() })
            ],
            derived_safe_equal
          );
          event("click", button_1, preventDefault(() => setCollapse(!collapsed())));
          append($$anchor3, button_1);
        };
        if_block(node_10, ($$render) => {
          if (collapsible()) $$render(consequent_4);
        });
      }
      reset(div);
      template_effect(
        ($0, $1) => {
          classes = set_class(div, 1, "super-debug", null, classes, $0);
          set_style(div, get(themeStyle));
          set_class(div_1, 1, `super-debug--status ${(strict_equals(label(), "") ? "super-debug--absolute super-debug--inset-x-0 super-debug--top-0" : "") ?? ""}`);
          set_text(text, label());
          classes_2 = set_class(pre, 1, "super-debug--pre", null, classes_2, $1);
          div.dir = div.dir;
        },
        [
          () => ({ "super-debug--collapsible": collapsible() }),
          () => ({
            "super-debug--with-label": label(),
            "super-debug--hidden": collapsed()
          })
        ],
        derived_safe_equal
      );
      event("click", button, copyContent);
      append($$anchor2, div);
    };
    if_block(node_1, ($$render) => {
      if (display()) $$render(consequent_5);
    });
  }
  append($$anchor, fragment);
  var $$pop = pop({ ...legacy_api() });
  $$cleanup();
  return $$pop;
}
if (import.meta.hot) {
  SuperDebug = hmr(SuperDebug, () => SuperDebug[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = SuperDebug[HMR].source;
    set(SuperDebug[HMR].source, module.default[HMR].original);
  });
}
var SuperDebug_default = SuperDebug;

export {
  SuperDebug_default
};
/*! Bundled license information:

sveltekit-superforms/dist/client/clipboardCopy.js:
  (*! clipboard-copy. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> *)
*/
//# sourceMappingURL=chunk-SLIF3F4N.js.map
