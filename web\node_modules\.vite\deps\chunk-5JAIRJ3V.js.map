{"version": 3, "sources": ["../../embla-carousel-reactive-utils/src/components/utils.ts"], "sourcesContent": ["import { EmblaPluginType } from 'embla-carousel'\n\nexport function isObject(subject: unknown): subject is Record<string, unknown> {\n  return Object.prototype.toString.call(subject) === '[object Object]'\n}\n\nexport function isRecord(\n  subject: unknown\n): subject is Record<string | number, unknown> {\n  return isObject(subject) || Array.isArray(subject)\n}\n\nexport function canUseDOM(): boolean {\n  return !!(\n    typeof window !== 'undefined' &&\n    window.document &&\n    window.document.createElement\n  )\n}\n\nexport function areOptionsEqual(\n  optionsA: Record<string, unknown>,\n  optionsB: Record<string, unknown>\n): boolean {\n  const optionsAKeys = Object.keys(optionsA)\n  const optionsBKeys = Object.keys(optionsB)\n\n  if (optionsAKeys.length !== optionsBKeys.length) return false\n\n  const breakpointsA = JSON.stringify(Object.keys(optionsA.breakpoints || {}))\n  const breakpointsB = JSON.stringify(Object.keys(optionsB.breakpoints || {}))\n\n  if (breakpointsA !== breakpointsB) return false\n\n  return optionsAKeys.every((key) => {\n    const valueA = optionsA[key]\n    const valueB = optionsB[key]\n    if (typeof valueA === 'function') return `${valueA}` === `${valueB}`\n    if (!isRecord(valueA) || !isRecord(valueB)) return valueA === valueB\n    return areOptionsEqual(valueA, valueB)\n  })\n}\n\nexport function sortAndMapPluginToOptions(\n  plugins: EmblaPluginType[]\n): EmblaPluginType['options'][] {\n  return plugins\n    .concat()\n    .sort((a, b) => (a.name > b.name ? 1 : -1))\n    .map((plugin) => plugin.options)\n}\n\nexport function arePluginsEqual(\n  pluginsA: EmblaPluginType[],\n  pluginsB: EmblaPluginType[]\n): boolean {\n  if (pluginsA.length !== pluginsB.length) return false\n\n  const optionsA = sortAndMapPluginToOptions(pluginsA)\n  const optionsB = sortAndMapPluginToOptions(pluginsB)\n\n  return optionsA.every((optionA, index) => {\n    const optionB = optionsB[index]\n    return areOptionsEqual(optionA, optionB)\n  })\n}\n"], "mappings": ";AAEM,SAAUA,SAASC,SAAgB;AACvC,SAAOC,OAAOC,UAAUC,SAASC,KAAKJ,OAAO,MAAM;AACrD;AAEM,SAAUK,SACdL,SAAgB;AAEhB,SAAOD,SAASC,OAAO,KAAKM,MAAMC,QAAQP,OAAO;AACnD;SAEgBQ,YAAS;AACvB,SAAO,CAAC,EACN,OAAOC,WAAW,eAClBA,OAAOC,YACPD,OAAOC,SAASC;AAEpB;AAEgB,SAAAC,gBACdC,UACAC,UAAiC;AAEjC,QAAMC,eAAed,OAAOe,KAAKH,QAAQ;AACzC,QAAMI,eAAehB,OAAOe,KAAKF,QAAQ;AAEzC,MAAIC,aAAaG,WAAWD,aAAaC,OAAQ,QAAO;AAExD,QAAMC,eAAeC,KAAKC,UAAUpB,OAAOe,KAAKH,SAASS,eAAe,CAAA,CAAE,CAAC;AAC3E,QAAMC,eAAeH,KAAKC,UAAUpB,OAAOe,KAAKF,SAASQ,eAAe,CAAA,CAAE,CAAC;AAE3E,MAAIH,iBAAiBI,aAAc,QAAO;AAE1C,SAAOR,aAAaS,MAAOC,SAAO;AAChC,UAAMC,SAASb,SAASY,GAAG;AAC3B,UAAME,SAASb,SAASW,GAAG;AAC3B,QAAI,OAAOC,WAAW,WAAY,QAAO,GAAGA,MAAM,OAAO,GAAGC,MAAM;AAClE,QAAI,CAACtB,SAASqB,MAAM,KAAK,CAACrB,SAASsB,MAAM,EAAG,QAAOD,WAAWC;AAC9D,WAAOf,gBAAgBc,QAAQC,MAAM;EACvC,CAAC;AACH;AAEM,SAAUC,0BACdC,SAA0B;AAE1B,SAAOA,QACJC,OAAM,EACNC,KAAK,CAACC,GAAGC,MAAOD,EAAEE,OAAOD,EAAEC,OAAO,IAAI,EAAG,EACzCC,IAAKC,YAAWA,OAAOC,OAAO;AACnC;AAEgB,SAAAC,gBACdC,UACAC,UAA2B;AAE3B,MAAID,SAASrB,WAAWsB,SAAStB,OAAQ,QAAO;AAEhD,QAAML,WAAWe,0BAA0BW,QAAQ;AACnD,QAAMzB,WAAWc,0BAA0BY,QAAQ;AAEnD,SAAO3B,SAASW,MAAM,CAACiB,SAASC,UAAS;AACvC,UAAMC,UAAU7B,SAAS4B,KAAK;AAC9B,WAAO9B,gBAAgB6B,SAASE,OAAO;EACzC,CAAC;AACH;", "names": ["isObject", "subject", "Object", "prototype", "toString", "call", "isRecord", "Array", "isArray", "canUseDOM", "window", "document", "createElement", "areOptionsEqual", "optionsA", "optionsB", "optionsAKeys", "keys", "optionsBKeys", "length", "breakpointsA", "JSON", "stringify", "breakpoints", "breakpointsB", "every", "key", "valueA", "valueB", "sortAndMapPluginToOptions", "plugins", "concat", "sort", "a", "b", "name", "map", "plugin", "options", "arePluginsEqual", "pluginsA", "pluginsB", "optionA", "index", "optionB"]}