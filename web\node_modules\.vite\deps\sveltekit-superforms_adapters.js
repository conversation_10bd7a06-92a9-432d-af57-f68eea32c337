import {
  require_memoize_weak
} from "./chunk-BZE7UB6G.js";
import {
  toJSONSchema
} from "./chunk-FQSVQ22F.js";
import {
  safeParseAsync
} from "./chunk-UUWKUH7N.js";
import {
  zodToJsonSchema
} from "./chunk-QL3R5NW6.js";
import {
  JSONSchema_exports,
  Schema_exports
} from "./chunk-KSJAPN47.js";
import {
  ArrayFormatter,
  Either_exports
} from "./chunk-JUBHK7DH.js";
import "./chunk-WGQNECLP.js";
import {
  createAdapter,
  createJsonSchema,
  pathExists,
  splitPath,
  traversePath,
  traversePaths
} from "./chunk-KNIUJTOO.js";
import "./chunk-FQ32N6ZP.js";
import {
  __toESM
} from "./chunk-KWPVD4H7.js";

// node_modules/sveltekit-superforms/dist/memoize.js
var import_memoize_weak = __toESM(require_memoize_weak(), 1);
var memoize = import_memoize_weak.default;

// node_modules/sveltekit-superforms/dist/adapters/arktype.js
async function modules() {
  const { type } = await import(
    /* webpackIgnore: true */
    "./arktype.js"
  );
  return { type };
}
var fetchModule = memoize(modules);
async function _validate(schema, data) {
  const { type } = await fetchModule();
  const result = schema(data);
  if (!(result instanceof type.errors)) {
    return {
      data: result,
      success: true
    };
  }
  const issues = [];
  for (const error of result) {
    issues.push({ message: error.problem, path: Array.from(error.path) });
  }
  return {
    issues,
    success: false
  };
}
function _arktype(schema, options) {
  return createAdapter({
    superFormValidationLibrary: "arktype",
    defaults: options.defaults,
    jsonSchema: createJsonSchema(options),
    validate: async (data) => _validate(schema, data)
  });
}
function _arktypeClient(schema) {
  return {
    superFormValidationLibrary: "arktype",
    validate: async (data) => _validate(schema, data)
  };
}
var arktype = memoize(_arktype);
var arktypeClient = memoize(_arktypeClient);

// node_modules/sveltekit-superforms/dist/adapters/classvalidator.js
async function modules2() {
  const { validate: validate9 } = await import(
    /* webpackIgnore: true */
    "./@typeschema_class-validator.js"
  );
  return { validate: validate9 };
}
var fetchModule2 = memoize(modules2);
async function validate(schema, data) {
  const { validate: validate9 } = await fetchModule2();
  const result = await validate9(schema, data);
  if (result.success) {
    return {
      data: result.data,
      success: true
    };
  }
  return {
    issues: result.issues.map(({ message, path }) => ({
      message,
      path
    })),
    success: false
  };
}
function _classvalidator(schema, options) {
  return createAdapter({
    superFormValidationLibrary: "classvalidator",
    validate: async (data) => validate(schema, data),
    jsonSchema: createJsonSchema(options),
    defaults: options.defaults
  });
}
function _classvalidatorClient(schema) {
  return {
    superFormValidationLibrary: "classvalidator",
    validate: async (data) => validate(schema, data)
  };
}
var classvalidator = memoize(_classvalidator);
var classvalidatorClient = memoize(_classvalidatorClient);

// node_modules/sveltekit-superforms/dist/adapters/effect.js
var effectToJSONSchema = (schema) => {
  return JSONSchema_exports.make(schema);
};
async function validate2(schema, data, options) {
  const result = Schema_exports.decodeUnknownEither(schema, { errors: "all" })(data, options == null ? void 0 : options.parseOptions);
  if (Either_exports.isRight(result)) {
    return {
      data: result.right,
      success: true
    };
  }
  return {
    // get rid of the _tag property
    issues: ArrayFormatter.formatErrorSync(result.left).map(({ message, path }) => ({
      message,
      path: [...path]
      // path is readonly array so we have to copy it
    })),
    success: false
  };
}
function _effect(schema, options) {
  return createAdapter({
    superFormValidationLibrary: "effect",
    validate: async (data) => validate2(schema, data, options),
    jsonSchema: (options == null ? void 0 : options.jsonSchema) ?? effectToJSONSchema(schema),
    defaults: options == null ? void 0 : options.defaults
  });
}
function _effectClient(schema, options) {
  return {
    superFormValidationLibrary: "effect",
    validate: async (data) => validate2(schema, data, options)
  };
}
var effect = memoize(_effect);
var effectClient = memoize(_effectClient);

// node_modules/sveltekit-superforms/dist/adapters/joi-to-json-schema/index.js
function assert(condition, errorMessage) {
  if (!condition)
    throw new Error(errorMessage);
}
var TYPES = {
  alternatives: (schema, joi2, transformer) => {
    const result = schema.oneOf = [];
    joi2.matches.forEach(function(match) {
      if (match.schema) {
        return result.push(convert(match.schema, transformer));
      }
      if (!match.is) {
        throw new Error('joi.when requires an "is"');
      }
      if (!(match.then || match.otherwise)) {
        throw new Error('joi.when requires one or both of "then" and "otherwise"');
      }
      if (match.then) {
        result.push(convert(match.then, transformer));
      }
      if (match.otherwise) {
        result.push(convert(match.otherwise, transformer));
      }
    });
    return schema;
  },
  date: (schema) => {
    schema.type = "Date";
    return schema;
  },
  any: (schema) => {
    delete schema.type;
    return schema;
  },
  array: (schema, joi2, transformer) => {
    var _a;
    schema.type = "array";
    (_a = joi2._rules) == null ? void 0 : _a.forEach((test) => {
      switch (test.name) {
        case "unique":
          schema.uniqueItems = true;
          break;
        case "length":
          schema.minItems = schema.maxItems = test.args.limit;
          break;
        case "min":
          schema.minItems = test.args.limit;
          break;
        case "max":
          schema.maxItems = test.args.limit;
          break;
      }
    });
    if (joi2.$_terms) {
      let list;
      if (joi2.$_terms._inclusions.length) {
        list = joi2.$_terms._inclusions;
      } else if (joi2.$_terms._requireds.length) {
        list = joi2.$_terms._requireds;
      }
      if (list) {
        schema.items = convert(list[0], transformer);
      }
    }
    return schema;
  },
  binary: (schema, joi2) => {
    schema.type = "string";
    schema.contentMediaType = joi2._meta.length > 0 && joi2._meta[0].contentMediaType ? joi2._meta[0].contentMediaType : "text/plain";
    schema.contentEncoding = joi2._flags.encoding ? joi2._flags.encoding : "binary";
    return schema;
  },
  boolean: (schema) => {
    schema.type = "boolean";
    return schema;
  },
  number: (schema, joi2) => {
    var _a;
    schema.type = "number";
    (_a = joi2._rules) == null ? void 0 : _a.forEach((test) => {
      switch (test.name) {
        case "integer":
          schema.type = "integer";
          break;
        case "less":
          schema.exclusiveMaximum = test.args.limit;
          break;
        case "greater":
          schema.exclusiveMinimum = test.args.limit;
          break;
        case "min":
          schema.minimum = test.args.limit;
          break;
        case "max":
          schema.maximum = test.args.limit;
          break;
        case "precision": {
          let multipleOf;
          if (test.args.limit && test.args.limit > 1) {
            multipleOf = JSON.parse("0." + "0".repeat(test.args.limit - 1) + "1");
          } else {
            multipleOf = 1;
          }
          schema.multipleOf = multipleOf;
          break;
        }
      }
    });
    return schema;
  },
  string: (schema, joi2) => {
    schema.type = "string";
    joi2._rules.forEach((test) => {
      switch (test.name) {
        case "email":
          schema.format = "email";
          break;
        case "pattern":
        case "regex": {
          const arg = test.args;
          const pattern = arg && arg.regex ? arg.regex : arg;
          schema.pattern = String(pattern).replace(/^\//, "").replace(/\/$/, "");
          break;
        }
        case "min":
          schema.minLength = test.args.limit;
          break;
        case "max":
          schema.maxLength = test.args.limit;
          break;
        case "length":
          schema.minLength = schema.maxLength = test.args.limit;
          break;
        case "uri":
          schema.format = "uri";
          break;
      }
    });
    return schema;
  },
  object: (schema, joi2, transformer) => {
    var _a, _b;
    schema.type = "object";
    schema.properties = {};
    schema.additionalProperties = Boolean(joi2._flags.allowUnknown || !joi2._inner.children);
    schema.pattern = // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ((_a = joi2.patterns) == null ? void 0 : _a.map((pattern) => {
      return { regex: pattern.regex, rule: convert(pattern.rule, transformer) };
    })) ?? [];
    if (!((_b = joi2.$_terms.keys) == null ? void 0 : _b.length)) {
      return schema;
    }
    joi2.$_terms.keys.forEach((property) => {
      if (property.schema._flags.presence !== "forbidden") {
        if (!schema.properties)
          schema.properties = {};
        schema.properties[property.key] = convert(property.schema, transformer);
        if (property.schema._flags.presence === "required" || property.schema._settings && property.schema._settings.presence === "required" && property.schema._flags.presence !== "optional") {
          schema.required = schema.required || [];
          schema.required.push(property.key);
        }
      }
    });
    return schema;
  }
};
function convert(joi2, transformer) {
  var _a;
  assert("object" === typeof joi2 && "type" in joi2, "requires a joi schema object");
  if (!TYPES[joi2.type]) {
    throw new Error(`sorry, do not know how to convert unknown joi type: "${joi2.type}"`);
  }
  if (transformer) {
    assert("function" === typeof transformer, "transformer must be a function");
  }
  const schema = {};
  if (joi2._description) {
    schema.description = joi2._description;
  }
  if (joi2._examples && joi2._examples.length > 0) {
    schema.examples = joi2._examples.map((e) => e.value);
  }
  if (joi2._examples && joi2._examples.length === 1) {
    schema.examples = joi2._examples[0].value;
  }
  if (joi2._settings && joi2._settings.language && joi2._settings.language.label) {
    schema.title = joi2._settings.language.label;
  } else if (joi2._flags && joi2._flags.label) {
    schema.title = joi2._flags.label;
  }
  if (joi2._flags && joi2._flags.default !== void 0 && joi2._flags.default !== null) {
    schema["default"] = joi2._flags.default;
  }
  if (joi2._valids && joi2._valids._set && (joi2._valids._set.size || joi2._valids._set.length)) {
    if (Array.isArray(joi2.children) || !joi2._flags.allowOnly) {
      return {
        anyOf: [
          {
            type: joi2.type,
            enum: [...joi2._valids._set]
          },
          TYPES[joi2.type](schema, joi2, transformer)
        ]
      };
    }
    schema["enum"] = [...joi2._valids._set];
  }
  let result = TYPES[joi2.type](schema, joi2, transformer);
  if (transformer) {
    result = transformer(result, joi2);
  }
  if (((_a = joi2._valids) == null ? void 0 : _a._values) && joi2._valids._values.size && !joi2._flags.allowOnly) {
    const constants = Array.from(joi2._valids._values).map((v) => ({
      const: v
    }));
    if (result.anyOf) {
      result.anyOf = [...constants, ...result.anyOf];
    } else {
      result = { anyOf: [...constants, result] };
    }
  }
  return result;
}
convert.TYPES = TYPES;

// node_modules/sveltekit-superforms/dist/adapters/joi.js
async function validate3(schema, data) {
  const result = schema.validate(data, { abortEarly: false });
  if (result.error == null) {
    return {
      data: result.value,
      success: true
    };
  }
  return {
    issues: result.error.details.map(({ message, path }) => ({
      message,
      path
    })),
    success: false
  };
}
function _joi(schema, options) {
  return createAdapter({
    superFormValidationLibrary: "joi",
    jsonSchema: (options == null ? void 0 : options.jsonSchema) ?? convert(schema),
    defaults: options == null ? void 0 : options.defaults,
    validate: async (data) => validate3(schema, data)
  });
}
function _joiClient(schema) {
  return {
    superFormValidationLibrary: "joi",
    validate: async (data) => validate3(schema, data)
  };
}
var joi = memoize(_joi);
var joiClient = memoize(_joiClient);

// node_modules/sveltekit-superforms/dist/adapters/superform.js
function _superform(schema) {
  return {
    superFormValidationLibrary: "superform",
    async validate(data) {
      if (!data || typeof data !== "object")
        data = {};
      else
        data = { ...data };
      const newData = data;
      for (const [key, value] of Object.entries(schema)) {
        if (typeof value === "function" && !(key in newData)) {
          newData[key] = void 0;
        }
      }
      const output = [];
      function mapErrors(path, errors2) {
        if (!errors2)
          return;
        if (typeof errors2 === "string")
          errors2 = [errors2];
        errors2.forEach((message) => {
          output.push({
            path,
            message
          });
        });
      }
      const queue = [];
      traversePaths(newData, async ({ value, path }) => {
        const validationPath = path.filter((p) => /\D/.test(String(p)));
        const maybeValidator = traversePath(schema, validationPath);
        if (typeof (maybeValidator == null ? void 0 : maybeValidator.value) === "function") {
          const check = maybeValidator.value;
          queue.push({ path, errors: check(value) });
        }
      });
      const errors = await Promise.all(queue.map((check) => check.errors));
      for (let i = 0; i < errors.length; i++) {
        mapErrors(queue[i].path, errors[i]);
      }
      return output.length ? {
        success: false,
        issues: output
      } : {
        success: true,
        data
      };
    }
  };
}
var superformClient = memoize(_superform);

// node_modules/sveltekit-superforms/dist/adapters/typebox.js
var Email = /^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i;
async function modules3() {
  const { TypeCompiler } = await import(
    /* webpackIgnore: true */
    "./@sinclair_typebox_compiler.js"
  );
  const { FormatRegistry } = await import(
    /* webpackIgnore: true */
    "./@sinclair_typebox.js"
  );
  return { TypeCompiler, FormatRegistry };
}
var fetchModule3 = memoize(modules3);
async function validate4(schema, data) {
  const { TypeCompiler, FormatRegistry } = await fetchModule3();
  if (!compiled.has(schema)) {
    compiled.set(schema, TypeCompiler.Compile(schema));
  }
  if (!FormatRegistry.Has("email")) {
    FormatRegistry.Set("email", (value) => Email.test(value));
  }
  const validator = compiled.get(schema);
  const errors = [...(validator == null ? void 0 : validator.Errors(data)) ?? []];
  if (!errors.length) {
    return { success: true, data };
  }
  return {
    success: false,
    issues: errors.map((issue) => ({
      path: issue.path.substring(1).split("/"),
      message: issue.message
    }))
  };
}
function _typebox(schema) {
  return createAdapter({
    superFormValidationLibrary: "typebox",
    validate: async (data) => validate4(schema, data),
    jsonSchema: schema
  });
}
function _typeboxClient(schema) {
  return {
    superFormValidationLibrary: "typebox",
    validate: async (data) => validate4(schema, data)
  };
}
var typebox = memoize(_typebox);
var typeboxClient = memoize(_typeboxClient);
var compiled = /* @__PURE__ */ new WeakMap();

// node_modules/sveltekit-superforms/dist/adapters/valibot.js
var defaultOptions = {
  strictObjectTypes: true,
  dateStrategy: "integer",
  ignoreUnknownValidation: true,
  customSchemaConversion: {
    custom: () => ({}),
    instance: () => ({}),
    file: () => ({}),
    blob: () => ({})
  }
};
var valibotToJSONSchema = (options) => {
  return toJSONSchema({ ...defaultOptions, ...options });
};
async function _validate2(schema, data, config) {
  const result = await safeParseAsync(schema, data, config);
  if (result.success) {
    return {
      data: result.output,
      success: true
    };
  }
  return {
    issues: result.issues.map(({ message, path }) => ({
      message,
      path: path == null ? void 0 : path.map(({ key }) => key)
    })),
    success: false
  };
}
function _valibot(schema, options = {}) {
  return createAdapter({
    superFormValidationLibrary: "valibot",
    validate: async (data) => _validate2(schema, data, options == null ? void 0 : options.config),
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    jsonSchema: (options == null ? void 0 : options.jsonSchema) ?? valibotToJSONSchema({ schema, ...options }),
    defaults: "defaults" in options ? options.defaults : void 0
  });
}
function _valibotClient(schema) {
  return {
    superFormValidationLibrary: "valibot",
    validate: async (data) => _validate2(schema, data)
  };
}
var valibot = memoize(_valibot);
var valibotClient = memoize(_valibotClient);

// node_modules/sveltekit-superforms/dist/adapters/yup-to-json-schema/converters/common.js
var commonConverter = (description, converters) => {
  var _a, _b;
  const jsonSchema = {};
  jsonSchema.type = description.type;
  if (description.nullable) {
    jsonSchema.type = [jsonSchema.type, "null"];
  }
  if (((_a = description.oneOf) == null ? void 0 : _a.length) > 0) {
    jsonSchema.enum = description.oneOf;
  }
  if (((_b = description.notOneOf) == null ? void 0 : _b.length) > 0) {
    jsonSchema.not = {
      enum: description.notOneOf
    };
  }
  if (description.label) {
    jsonSchema.title = description.label;
  }
  if (description.default !== void 0) {
    jsonSchema.default = description.default;
  }
  return jsonSchema;
};
var common_default = commonConverter;

// node_modules/sveltekit-superforms/dist/adapters/yup-to-json-schema/converters/string.js
var uuidRegExPattern = "^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$";
var stringConverter = (description, converters) => {
  const jsonSchema = common_default(description, converters);
  const meta = description.meta || {};
  description.tests.forEach((test) => {
    var _a, _b, _c, _d;
    switch (test.name) {
      case "length":
        if (((_a = test.params) == null ? void 0 : _a.length) !== void 0) {
          jsonSchema.minLength = Number(test.params.length);
          jsonSchema.maxLength = Number(test.params.length);
        }
        break;
      case "min":
        if (((_b = test.params) == null ? void 0 : _b.min) !== void 0) {
          jsonSchema.minLength = Number(test.params.min);
        }
        break;
      case "max":
        if (((_c = test.params) == null ? void 0 : _c.max) !== void 0) {
          jsonSchema.maxLength = Number(test.params.max);
        }
        break;
      case "matches":
        if ((_d = test.params) == null ? void 0 : _d.regex) {
          jsonSchema.pattern = test.params.regex.toString().replace(/^\/(.*)\/[gimusy]*$/, "$1");
        }
        break;
      case "email":
        jsonSchema.format = "email";
        break;
      case "url":
        jsonSchema.format = "uri";
        break;
      case "uuid":
        jsonSchema.format = "uuid";
        jsonSchema.pattern = uuidRegExPattern;
        break;
    }
  });
  return Object.assign(jsonSchema, meta.jsonSchema);
};
var string_default = stringConverter;

// node_modules/sveltekit-superforms/dist/adapters/yup-to-json-schema/converters/number.js
var numberConverter = (description, converters) => {
  const jsonSchema = common_default(description, converters);
  const meta = description.meta || {};
  description.tests.forEach((test) => {
    var _a, _b, _c, _d;
    switch (test.name) {
      case "min":
        if (((_a = test.params) == null ? void 0 : _a.min) !== void 0) {
          jsonSchema.minimum = Number(test.params.min);
        }
        if (((_b = test.params) == null ? void 0 : _b.more) !== void 0) {
          jsonSchema.exclusiveMinimum = Number(test.params.more);
        }
        break;
      case "max":
        if (((_c = test.params) == null ? void 0 : _c.max) !== void 0) {
          jsonSchema.maximum = Number(test.params.max);
        }
        if (((_d = test.params) == null ? void 0 : _d.less) !== void 0) {
          jsonSchema.exclusiveMaximum = Number(test.params.less);
        }
        break;
      case "integer":
        if (jsonSchema.type === "number") {
          jsonSchema.type = "integer";
        } else {
          jsonSchema.type = [...jsonSchema.type, "integer"].filter((type) => type !== "number");
        }
    }
  });
  return Object.assign(jsonSchema, meta.jsonSchema);
};
var number_default = numberConverter;

// node_modules/sveltekit-superforms/dist/adapters/yup-to-json-schema/converters/boolean.js
var booleanConverter = (description, converters) => {
  const jsonSchema = common_default(description, converters);
  const meta = description.meta || {};
  return Object.assign(jsonSchema, meta.jsonSchema);
};
var boolean_default = booleanConverter;

// node_modules/sveltekit-superforms/dist/adapters/yup-to-json-schema/converters/date.js
var dateConverter = (description, converters) => {
  const jsonSchema = common_default(description, converters);
  const meta = description.meta || {};
  jsonSchema.type = "string";
  jsonSchema.format = "date-time";
  return Object.assign(jsonSchema, meta.jsonSchema);
};
var date_default = dateConverter;

// node_modules/sveltekit-superforms/dist/adapters/yup-to-json-schema/converters/array.js
var arrayConverter = (description, converters) => {
  const jsonSchema = common_default(description, converters);
  const meta = description.meta || {};
  const { innerType } = description;
  if (innerType) {
    const converter = converters[innerType.type];
    jsonSchema.items = converter(innerType, converters);
  }
  description.tests.forEach((test) => {
    var _a, _b, _c;
    switch (test.name) {
      case "length":
        if (((_a = test.params) == null ? void 0 : _a.length) !== void 0) {
          jsonSchema.minItems = jsonSchema.maxItems = Number(test.params.length);
        }
        break;
      case "min":
        if (((_b = test.params) == null ? void 0 : _b.min) !== void 0) {
          jsonSchema.minItems = Number(test.params.min);
        }
        break;
      case "max":
        if (((_c = test.params) == null ? void 0 : _c.max) !== void 0) {
          jsonSchema.maxItems = Number(test.params.max);
        }
        break;
    }
  });
  return Object.assign(jsonSchema, meta.jsonSchema);
};
var array_default = arrayConverter;

// node_modules/sveltekit-superforms/dist/adapters/yup-to-json-schema/converters/object.js
var objectConverter = (description, converters) => {
  const jsonSchema = common_default(description, converters);
  const meta = description.meta || {};
  const properties = {};
  const required = [];
  Object.keys(description.fields).forEach((fieldName) => {
    const fieldDescription = description.fields[fieldName];
    const converter = converters[fieldDescription.type];
    properties[fieldName] = converter(fieldDescription, converters);
    if (!fieldDescription.optional) {
      required.push(fieldName);
    }
  });
  if (Object.keys(properties).length > 0) {
    jsonSchema.properties = properties;
  }
  if (Object.keys(required).length > 0) {
    jsonSchema.required = required;
  }
  return Object.assign(jsonSchema, meta.jsonSchema);
};
var object_default = objectConverter;

// node_modules/sveltekit-superforms/dist/adapters/yup-to-json-schema/converters/tuple.js
var tupleConverter = (description, converters) => {
  const jsonSchema = common_default(description, converters);
  const meta = description.meta || {};
  jsonSchema.type = "array";
  jsonSchema.items = description.innerType.map((description2) => {
    const converter = converters[description2.type];
    return converter(description2, converters);
  });
  jsonSchema.minItems = jsonSchema.items.length;
  jsonSchema.maxItems = jsonSchema.items.length;
  return Object.assign(jsonSchema, meta.jsonSchema);
};
var tuple_default = tupleConverter;

// node_modules/sveltekit-superforms/dist/adapters/yup-to-json-schema/converters/mixed.js
var getType = (item) => {
  switch (typeof item) {
    case "string":
      return "string";
    case "number":
      return "number";
    case "boolean":
      return "boolean";
    case "object":
      if (Array.isArray(item)) {
        return "array";
      } else if (item === null) {
        return "null";
      } else if (item instanceof Date) {
        return "string";
      } else {
        return "object";
      }
    default:
      return "null";
  }
};
var mixedConverter = (description, converters) => {
  var _a;
  const jsonSchema = common_default(description, converters);
  const meta = description.meta || {};
  let types = Array.isArray(description.type) ? description.type : [description.type];
  types = types.filter((type) => type !== "mixed");
  if (((_a = description.oneOf) == null ? void 0 : _a.length) > 0) {
    description.oneOf.forEach((item) => {
      types.push(getType(item));
    });
  }
  if (description.default !== void 0) {
    types.push(getType(description.default));
  }
  types = types.filter((type, index, self) => self.indexOf(type) === index);
  jsonSchema.type = types;
  return Object.assign(jsonSchema, meta.jsonSchema);
};
var mixed_default = mixedConverter;

// node_modules/sveltekit-superforms/dist/adapters/yup-to-json-schema/converters/lazy.js
var lazyConverter = (description, converters) => {
  const jsonSchema = common_default(description, converters);
  const meta = description.meta || {};
  return Object.assign(jsonSchema, meta.jsonSchema);
};
var lazy_default = lazyConverter;

// node_modules/sveltekit-superforms/dist/adapters/yup-to-json-schema/converters/index.js
function convertSchema(yupSchema, options) {
  const { converters, ...resolveOptions } = options || {};
  const allConverters = {
    string: string_default,
    number: number_default,
    boolean: boolean_default,
    date: date_default,
    array: array_default,
    object: object_default,
    tuple: tuple_default,
    mixed: mixed_default,
    lazy: lazy_default,
    ...converters
  };
  const description = yupSchema.describe(resolveOptions);
  const converter = allConverters[description.type];
  return converter(description, allConverters);
}

// node_modules/sveltekit-superforms/dist/adapters/yup.js
var modules4 = async () => {
  const { ValidationError } = await import(
    /* webpackIgnore: true */
    "./yup.js"
  );
  return { ValidationError };
};
var fetchModule4 = memoize(modules4);
function yupToJSONSchema(schema) {
  return convertSchema(schema, {
    converters: {
      date: (desc, options) => {
        return options.string(desc, options);
      }
    }
  });
}
async function validate5(schema, data) {
  const { ValidationError } = await fetchModule4();
  try {
    return {
      success: true,
      data: await schema.validate(data, { strict: true, abortEarly: false })
    };
  } catch (error) {
    if (!(error instanceof ValidationError))
      throw error;
    return {
      success: false,
      issues: error.inner.map((error2) => ({
        message: error2.message,
        path: error2.path !== null && error2.path !== void 0 ? splitPath(error2.path) : void 0
      }))
    };
  }
}
function _yup(schema, options) {
  return createAdapter({
    superFormValidationLibrary: "yup",
    validate: async (data) => validate5(schema, data),
    jsonSchema: (options == null ? void 0 : options.jsonSchema) ?? yupToJSONSchema(schema),
    defaults: options == null ? void 0 : options.defaults
  });
}
function _yupClient(schema) {
  return {
    superFormValidationLibrary: "yup",
    validate: async (data) => validate5(schema, data)
  };
}
var yup = memoize(_yup);
var yupClient = memoize(_yupClient);

// node_modules/sveltekit-superforms/dist/adapters/zod.js
var defaultOptions2 = {
  dateStrategy: "integer",
  pipeStrategy: "output",
  $refStrategy: "none"
};
var zodToJSONSchema = (...params) => {
  params[1] = typeof params[1] == "object" ? { ...defaultOptions2, ...params[1] } : defaultOptions2;
  return zodToJsonSchema(...params);
};
async function validate6(schema, data, errorMap) {
  const result = await schema.safeParseAsync(data, { errorMap });
  if (result.success) {
    return {
      data: result.data,
      success: true
    };
  }
  return {
    issues: result.error.issues.map(({ message, path }) => ({ message, path })),
    success: false
  };
}
function _zod(schema, options) {
  return createAdapter({
    superFormValidationLibrary: "zod",
    validate: async (data) => {
      return validate6(schema, data, options == null ? void 0 : options.errorMap);
    },
    jsonSchema: (options == null ? void 0 : options.jsonSchema) ?? zodToJSONSchema(schema, options == null ? void 0 : options.config),
    defaults: options == null ? void 0 : options.defaults
  });
}
function _zodClient(schema, options) {
  return {
    superFormValidationLibrary: "zod",
    validate: async (data) => validate6(schema, data, options == null ? void 0 : options.errorMap)
  };
}
var zod = memoize(_zod);
var zodClient = memoize(_zodClient);

// node_modules/sveltekit-superforms/dist/adapters/vine.js
async function modules5() {
  const { Vine, errors } = await import(
    /* webpackIgnore: true */
    "./@vinejs_vine.js"
  );
  return { Vine, errors };
}
var fetchModule5 = memoize(modules5);
async function validate7(schema, data) {
  const { Vine, errors } = await fetchModule5();
  try {
    const output = await new Vine().validate({ schema, data });
    return {
      success: true,
      data: output
    };
  } catch (e) {
    if (e instanceof errors.E_VALIDATION_ERROR) {
      return {
        success: false,
        issues: e.messages.map((m) => ({
          path: m.field.split("."),
          message: m.message
        }))
      };
    } else {
      return { success: false, issues: [] };
    }
  }
}
function _vine(schema, options) {
  return createAdapter({
    superFormValidationLibrary: "vine",
    validate: async (data) => validate7(schema, data),
    jsonSchema: createJsonSchema(options),
    defaults: options.defaults
  });
}
function _vineClient(schema) {
  return {
    superFormValidationLibrary: "vine",
    validate: async (data) => validate7(schema, data)
  };
}
var vine = memoize(_vine);
var vineClient = memoize(_vineClient);

// node_modules/sveltekit-superforms/dist/adapters/schemasafe.js
async function modules6() {
  const { validator } = await import(
    /* webpackIgnore: true */
    "./@exodus_schemasafe.js"
  );
  return { validator };
}
var fetchModule6 = memoize(modules6);
var Email2 = /^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i;
var defaultOptions3 = {
  formats: {
    email: (str) => Email2.test(str)
  },
  includeErrors: true,
  allErrors: true
};
async function cachedValidator(currentSchema, config) {
  const { validator } = await fetchModule6();
  if (!cache.has(currentSchema)) {
    cache.set(currentSchema, validator(currentSchema, {
      ...defaultOptions3,
      ...config
    }));
  }
  return cache.get(currentSchema);
}
function _schemasafe(schema, options) {
  return createAdapter({
    superFormValidationLibrary: "schemasafe",
    jsonSchema: schema,
    defaults: options == null ? void 0 : options.defaults,
    async validate(data) {
      const validator = await cachedValidator(schema, options == null ? void 0 : options.config);
      const isValid = validator(data);
      if (isValid) {
        return {
          data,
          success: true
        };
      }
      return {
        issues: (validator.errors ?? []).map(({ instanceLocation, keywordLocation }) => ({
          message: (options == null ? void 0 : options.descriptionAsErrors) ? errorDescription(schema, keywordLocation) : keywordLocation,
          path: instanceLocation.split("/").slice(1)
        })),
        success: false
      };
    }
  });
}
function _schemasafeClient(schema, options) {
  return {
    superFormValidationLibrary: "schemasafe",
    async validate(data) {
      const validator = await cachedValidator(schema, options == null ? void 0 : options.config);
      const isValid = validator(data);
      if (isValid) {
        return {
          data,
          success: true
        };
      }
      return {
        issues: (validator.errors ?? []).map(({ instanceLocation, keywordLocation }) => ({
          message: keywordLocation,
          path: instanceLocation.split("/").slice(1)
        })),
        success: false
      };
    }
  };
}
var schemasafe = memoize(_schemasafe);
var schemasafeClient = memoize(_schemasafeClient);
var cache = /* @__PURE__ */ new WeakMap();
function errorDescription(schema, keywordLocation) {
  if (!keywordLocation.startsWith("#/"))
    return keywordLocation;
  const searchPath = keywordLocation.slice(2).split("/");
  const path = pathExists(schema, searchPath);
  return (path == null ? void 0 : path.parent.description) ?? keywordLocation;
}

// node_modules/sveltekit-superforms/dist/adapters/superstruct.js
async function validate8(schema, data) {
  const result = schema.validate(data, { coerce: true });
  if (!result[0]) {
    return {
      data: result[1],
      success: true
    };
  }
  const errors = result[0];
  return {
    success: false,
    issues: errors.failures().map((error) => ({
      message: error.message,
      path: error.path
    }))
  };
}
function _superstruct(schema, options) {
  return createAdapter({
    superFormValidationLibrary: "superstruct",
    defaults: options.defaults,
    jsonSchema: createJsonSchema(options),
    validate: async (data) => validate8(schema, data)
  });
}
function _superstructClient(schema) {
  return {
    superFormValidationLibrary: "superstruct",
    validate: async (data) => validate8(schema, data)
  };
}
var superstruct = memoize(_superstruct);
var superstructClient = memoize(_superstructClient);
export {
  arktype,
  arktypeClient,
  classvalidator,
  classvalidatorClient,
  effect,
  effectClient,
  joi,
  joiClient,
  schemasafe,
  schemasafeClient,
  superformClient,
  superstruct,
  superstructClient,
  typebox,
  typeboxClient,
  valibot,
  valibotClient,
  vine,
  vineClient,
  yup,
  yupClient,
  zod,
  zodClient
};
//# sourceMappingURL=sveltekit-superforms_adapters.js.map
