{"version": 3, "sources": ["../../@layerstack/utils/dist/object.js", "../../@layerstack/utils/dist/typeHelpers.js"], "sourcesContent": ["import { get, camelCase, mergeWith } from 'lodash-es';\nimport { entries, fromEntries, keys } from './typeHelpers.js';\nexport function isLiteralObject(obj) {\n    return obj && typeof obj === 'object' && obj.constructor === Object;\n}\nexport function isEmptyObject(obj) {\n    return isLiteralObject(obj) && keys(obj).length === 0;\n}\nexport function camelCaseKeys(obj) {\n    return keys(obj).reduce((acc, key) => ((acc[camelCase(key ? String(key) : undefined)] = obj[key]), acc), {});\n}\n// https://codereview.stackexchange.com/questions/73714/find-a-nested-property-in-an-object\n// https://github.com/dominik791/obj-traverse\nexport function nestedFindByPredicate(obj, predicate, childrenProp) {\n    const getChildrenProp = propAccessor(childrenProp ?? 'children');\n    if (predicate(obj)) {\n        return obj;\n    }\n    else {\n        const children = getChildrenProp(obj);\n        if (children) {\n            for (let o of children) {\n                const match = nestedFindByPredicate(o, predicate, childrenProp);\n                if (match) {\n                    return match;\n                }\n            }\n        }\n    }\n}\nexport function propAccessor(prop) {\n    return typeof prop === 'function'\n        ? prop\n        : typeof prop === 'string'\n            ? (d) => get(d, prop)\n            : (x) => x;\n}\n/**\n * Produce a unique Id for an object (helpful for debugging)\n * See: https://stackoverflow.com/a/35306050/191902\n */\nvar objIdMap = new WeakMap(), objectCount = 0;\nexport function objectId(object) {\n    if (!objIdMap.has(object))\n        objIdMap.set(object, ++objectCount);\n    return objIdMap.get(object);\n}\nexport function distinctKeys(...objs) {\n    return [...new Set(flatten(objs.map((x) => keys(x))))];\n}\n// Copied from `array.ts` to remove circular dependency\nfunction flatten(items) {\n    return items.reduce((prev, next) => prev.concat(next), []);\n}\n/**\n * Recursive merge objects\n * @param object The destination object\n * @param source  The source object\n * @returns\n */\nexport function merge(object, source) {\n    return mergeWith(object, source, (objValue, srcValue) => {\n        if (Array.isArray(srcValue)) {\n            // Overwrite instead of merging by index with objValue (like standard lodash `merge` does)\n            return srcValue;\n        }\n    });\n}\n/**\n * Remove properties from object based on expiration\n */\nexport function expireObject(object, expiry) {\n    const now = new Date();\n    if (expiry instanceof Date || typeof object !== 'object' || object == null) {\n        // Expired\n        if (expiry < now) {\n            return null;\n        }\n        // Not expired\n        return object;\n    }\n    // LoopIterate over the properties in `object`\n    for (let [prop, propExpiry] of entries(expiry)) {\n        if (propExpiry instanceof Date) {\n            // Check if expired\n            if (propExpiry < now) {\n                if (prop === '$default') {\n                    // Delete all properties which do not have explicit expiry to check\n                    for (let objProp of keys(object)) {\n                        if (!(objProp in expiry)) {\n                            delete object[objProp];\n                        }\n                    }\n                    // Remove expired `$default` property\n                    // @ts-expect-error it's fine if the property doesn't exist in object\n                    delete object[prop];\n                }\n                else {\n                    // Remove expired property\n                    // @ts-expect-error it's fine if the property doesn't exist in object\n                    delete object[prop];\n                }\n            }\n            else {\n                // Keep value\n            }\n        }\n        else {\n            // Check expiry for each property in object.  Skip if prop not in object (expiry only)\n            const value = object[prop];\n            if (value && typeof value === 'object') {\n                expireObject(value, propExpiry);\n                // Remove property if empty object (all properties removed)\n                if (isEmptyObject(value)) {\n                    delete object[prop];\n                }\n            }\n        }\n    }\n    return isEmptyObject(object) ? null : object;\n}\n/**\n * Remove properties from an object.  See also lodash `_.omit()`\n */\nexport function omit(obj, keys) {\n    if (keys.length === 0) {\n        return obj;\n    }\n    else {\n        return fromEntries(entries(obj).filter(([key]) => !keys.includes(key)));\n    }\n}\n/**\n * Remove `null` or `undefined` properties from an object\n */\nexport function omitNil(obj) {\n    if (keys.length === 0) {\n        return obj;\n    }\n    else {\n        return fromEntries(entries(obj).filter(([key, value]) => value != null));\n    }\n}\n/**\n * Pick properties from an object.  See also lodash `_.pick()`\n */\nexport function pick(obj, keys) {\n    if (keys.length === 0) {\n        return obj;\n    }\n    else {\n        return fromEntries(keys.filter((key) => key in obj).map((key) => [key, obj[key]]));\n    }\n}\n/**\n * Create new object with keys and values swapped.  Last value's key is used if duplicated\n */\nexport function keysByValues(obj) {\n    return fromEntries(entries(obj).map(([key, value]) => [String(value), key]));\n}\n/**\n * Map keys of an object\n */\nexport function mapKeys(obj, fn) {\n    return fromEntries(entries(obj).map(([key, value]) => [fn(key), value]));\n}\n/**\n * Map values of an object\n */\nexport function mapValues(obj, fn) {\n    return fromEntries(entries(obj).map(([key, value]) => [key, fn(value)]));\n}\n", "// https://basarat.gitbooks.io/typescript/docs/types/never.html#use-case-exhaustive-checks\n// https://www.typescriptlang.org/docs/handbook/basic-types.html#never\nexport function fail(message) {\n    throw new Error(message);\n}\n// Get keys of object (strongly-typed)\n// Reason Object.keys() isn't like this by default due to runtime properties: https://github.com/Microsoft/TypeScript/pull/12253#issuecomment-263132208\nexport function keys(o) {\n    return Object.keys(o);\n}\n// @ts-expect-error\nexport function entries(o) {\n    if (o instanceof Map)\n        return Array.from(o.entries());\n    return Object.entries(o); // TODO: Improve based on key/value pair - https://stackoverflow.com/questions/60141960/typescript-key-value-relation-preserving-object-entries-type\n}\n// Get object from entries (array of [key, value] arrays) (strongly-typed)\nexport function fromEntries(entries) {\n    return Object.fromEntries(entries);\n}\n// https://github.com/Microsoft/TypeScript/issues/17198#issuecomment-315400819\nexport function enumKeys(E) {\n    return keys(E).filter((k) => typeof E[k] === 'number'); // [\"A\", \"B\"]\n}\nexport function enumValues(E) {\n    const keys = enumKeys(E);\n    return keys.map((k) => E[k]); // [0, 1]\n}\n/**\n * util to make sure we have handled all enum cases in a switch statement\n * Just add at the end of the switch statement a `default` like this:\n *\n * ```ts\n * switch (periodType) {\n *   case xxx:\n *     ...\n *\n *   default:\n *     assertNever(periodType); // This will now report unhandled cases\n * }\n * ```\n */\nexport function assertNever(x) {\n    throw new Error(`Unhandled enum case: ${x}`);\n}\n"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACEO,SAAS,KAAK,SAAS;AAC1B,QAAM,IAAI,MAAM,OAAO;AAC3B;AAGO,SAAS,KAAK,GAAG;AACpB,SAAO,OAAO,KAAK,CAAC;AACxB;AAEO,SAAS,QAAQ,GAAG;AACvB,MAAI,aAAa;AACb,WAAO,MAAM,KAAK,EAAE,QAAQ,CAAC;AACjC,SAAO,OAAO,QAAQ,CAAC;AAC3B;AAEO,SAAS,YAAYA,UAAS;AACjC,SAAO,OAAO,YAAYA,QAAO;AACrC;AAEO,SAAS,SAAS,GAAG;AACxB,SAAO,KAAK,CAAC,EAAE,OAAO,CAAC,MAAM,OAAO,EAAE,CAAC,MAAM,QAAQ;AACzD;AACO,SAAS,WAAW,GAAG;AAC1B,QAAMC,QAAO,SAAS,CAAC;AACvB,SAAOA,MAAK,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;AAC/B;AAeO,SAAS,YAAY,GAAG;AAC3B,QAAM,IAAI,MAAM,wBAAwB,CAAC,EAAE;AAC/C;;;AD1CO,SAAS,gBAAgB,KAAK;AACjC,SAAO,OAAO,OAAO,QAAQ,YAAY,IAAI,gBAAgB;AACjE;AACO,SAAS,cAAc,KAAK;AAC/B,SAAO,gBAAgB,GAAG,KAAK,KAAK,GAAG,EAAE,WAAW;AACxD;AACO,SAAS,cAAc,KAAK;AAC/B,SAAO,KAAK,GAAG,EAAE,OAAO,CAAC,KAAK,SAAU,IAAI,kBAAU,MAAM,OAAO,GAAG,IAAI,MAAS,CAAC,IAAI,IAAI,GAAG,GAAI,MAAM,CAAC,CAAC;AAC/G;AAGO,SAAS,sBAAsB,KAAK,WAAW,cAAc;AAChE,QAAM,kBAAkB,aAAa,gBAAgB,UAAU;AAC/D,MAAI,UAAU,GAAG,GAAG;AAChB,WAAO;AAAA,EACX,OACK;AACD,UAAM,WAAW,gBAAgB,GAAG;AACpC,QAAI,UAAU;AACV,eAAS,KAAK,UAAU;AACpB,cAAM,QAAQ,sBAAsB,GAAG,WAAW,YAAY;AAC9D,YAAI,OAAO;AACP,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACO,SAAS,aAAa,MAAM;AAC/B,SAAO,OAAO,SAAS,aACjB,OACA,OAAO,SAAS,WACZ,CAAC,MAAM,YAAI,GAAG,IAAI,IAClB,CAAC,MAAM;AACrB;AAKA,IAAI,WAAW,oBAAI,QAAQ;AAA3B,IAA8B,cAAc;AACrC,SAAS,SAAS,QAAQ;AAC7B,MAAI,CAAC,SAAS,IAAI,MAAM;AACpB,aAAS,IAAI,QAAQ,EAAE,WAAW;AACtC,SAAO,SAAS,IAAI,MAAM;AAC9B;AACO,SAAS,gBAAgB,MAAM;AAClC,SAAO,CAAC,GAAG,IAAI,IAAI,QAAQ,KAAK,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD;AAEA,SAAS,QAAQ,OAAO;AACpB,SAAO,MAAM,OAAO,CAAC,MAAM,SAAS,KAAK,OAAO,IAAI,GAAG,CAAC,CAAC;AAC7D;AAOO,SAAS,MAAM,QAAQ,QAAQ;AAClC,SAAO,kBAAU,QAAQ,QAAQ,CAAC,UAAU,aAAa;AACrD,QAAI,MAAM,QAAQ,QAAQ,GAAG;AAEzB,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AAIO,SAAS,aAAa,QAAQ,QAAQ;AACzC,QAAM,MAAM,oBAAI,KAAK;AACrB,MAAI,kBAAkB,QAAQ,OAAO,WAAW,YAAY,UAAU,MAAM;AAExE,QAAI,SAAS,KAAK;AACd,aAAO;AAAA,IACX;AAEA,WAAO;AAAA,EACX;AAEA,WAAS,CAAC,MAAM,UAAU,KAAK,QAAQ,MAAM,GAAG;AAC5C,QAAI,sBAAsB,MAAM;AAE5B,UAAI,aAAa,KAAK;AAClB,YAAI,SAAS,YAAY;AAErB,mBAAS,WAAW,KAAK,MAAM,GAAG;AAC9B,gBAAI,EAAE,WAAW,SAAS;AACtB,qBAAO,OAAO,OAAO;AAAA,YACzB;AAAA,UACJ;AAGA,iBAAO,OAAO,IAAI;AAAA,QACtB,OACK;AAGD,iBAAO,OAAO,IAAI;AAAA,QACtB;AAAA,MACJ,OACK;AAAA,MAEL;AAAA,IACJ,OACK;AAED,YAAM,QAAQ,OAAO,IAAI;AACzB,UAAI,SAAS,OAAO,UAAU,UAAU;AACpC,qBAAa,OAAO,UAAU;AAE9B,YAAI,cAAc,KAAK,GAAG;AACtB,iBAAO,OAAO,IAAI;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,cAAc,MAAM,IAAI,OAAO;AAC1C;AAIO,SAAS,KAAK,KAAKC,OAAM;AAC5B,MAAIA,MAAK,WAAW,GAAG;AACnB,WAAO;AAAA,EACX,OACK;AACD,WAAO,YAAY,QAAQ,GAAG,EAAE,OAAO,CAAC,CAAC,GAAG,MAAM,CAACA,MAAK,SAAS,GAAG,CAAC,CAAC;AAAA,EAC1E;AACJ;AAIO,SAAS,QAAQ,KAAK;AACzB,MAAI,KAAK,WAAW,GAAG;AACnB,WAAO;AAAA,EACX,OACK;AACD,WAAO,YAAY,QAAQ,GAAG,EAAE,OAAO,CAAC,CAAC,KAAK,KAAK,MAAM,SAAS,IAAI,CAAC;AAAA,EAC3E;AACJ;AAIO,SAAS,KAAK,KAAKA,OAAM;AAC5B,MAAIA,MAAK,WAAW,GAAG;AACnB,WAAO;AAAA,EACX,OACK;AACD,WAAO,YAAYA,MAAK,OAAO,CAAC,QAAQ,OAAO,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC;AAAA,EACrF;AACJ;AAIO,SAAS,aAAa,KAAK;AAC9B,SAAO,YAAY,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,OAAO,KAAK,GAAG,GAAG,CAAC,CAAC;AAC/E;AAIO,SAAS,QAAQ,KAAK,IAAI;AAC7B,SAAO,YAAY,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;AAC3E;AAIO,SAAS,UAAU,KAAK,IAAI;AAC/B,SAAO,YAAY,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;AAC3E;", "names": ["entries", "keys", "keys"]}