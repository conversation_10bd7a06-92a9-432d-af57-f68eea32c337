import {
  extendTailwindMerge
} from "./chunk-YCMJBG7T.js";
import {
  addDays,
  addMonths,
  addQuarters,
  addWeeks,
  addYears,
  differenceInDays,
  differenceInMonths,
  differenceInQuarters,
  differenceInWeeks,
  differenceInYears,
  endOfDay,
  endOfMonth,
  endOfQuarter,
  endOfWeek,
  endOfYear,
  formatISO,
  isAfter,
  isBefore,
  isLeapYear,
  isSameDay,
  isSameMonth,
  isSameQuarter,
  isSameWeek,
  isSameYear,
  max,
  min,
  parseISO,
  startOfDay,
  startOfMonth,
  startOfQuarter,
  startOfWeek,
  startOfYear,
  subYears
} from "./chunk-LKELDSZT.js";
import {
  clsx_default
} from "./chunk-U7P2NEEE.js";
import {
  camelCase_default,
  defaultsDeep_default,
  get_default,
  isFunction_default,
  mergeWith_default
} from "./chunk-LK7GAOJV.js";
import {
  greatest,
  range,
  rollup
} from "./chunk-3VW5CGFU.js";
import {
  __export
} from "./chunk-KWPVD4H7.js";

// node_modules/culori/src/rgb/parseNumber.js
var parseNumber = (color, len) => {
  if (typeof color !== "number") return;
  if (len === 3) {
    return {
      mode: "rgb",
      r: (color >> 8 & 15 | color >> 4 & 240) / 255,
      g: (color >> 4 & 15 | color & 240) / 255,
      b: (color & 15 | color << 4 & 240) / 255
    };
  }
  if (len === 4) {
    return {
      mode: "rgb",
      r: (color >> 12 & 15 | color >> 8 & 240) / 255,
      g: (color >> 8 & 15 | color >> 4 & 240) / 255,
      b: (color >> 4 & 15 | color & 240) / 255,
      alpha: (color & 15 | color << 4 & 240) / 255
    };
  }
  if (len === 6) {
    return {
      mode: "rgb",
      r: (color >> 16 & 255) / 255,
      g: (color >> 8 & 255) / 255,
      b: (color & 255) / 255
    };
  }
  if (len === 8) {
    return {
      mode: "rgb",
      r: (color >> 24 & 255) / 255,
      g: (color >> 16 & 255) / 255,
      b: (color >> 8 & 255) / 255,
      alpha: (color & 255) / 255
    };
  }
};
var parseNumber_default = parseNumber;

// node_modules/culori/src/colors/named.js
var named = {
  aliceblue: 15792383,
  antiquewhite: 16444375,
  aqua: 65535,
  aquamarine: 8388564,
  azure: 15794175,
  beige: 16119260,
  bisque: 16770244,
  black: 0,
  blanchedalmond: 16772045,
  blue: 255,
  blueviolet: 9055202,
  brown: 10824234,
  burlywood: 14596231,
  cadetblue: 6266528,
  chartreuse: 8388352,
  chocolate: 13789470,
  coral: 16744272,
  cornflowerblue: 6591981,
  cornsilk: 16775388,
  crimson: 14423100,
  cyan: 65535,
  darkblue: 139,
  darkcyan: 35723,
  darkgoldenrod: 12092939,
  darkgray: 11119017,
  darkgreen: 25600,
  darkgrey: 11119017,
  darkkhaki: 12433259,
  darkmagenta: 9109643,
  darkolivegreen: 5597999,
  darkorange: 16747520,
  darkorchid: 10040012,
  darkred: 9109504,
  darksalmon: 15308410,
  darkseagreen: 9419919,
  darkslateblue: 4734347,
  darkslategray: 3100495,
  darkslategrey: 3100495,
  darkturquoise: 52945,
  darkviolet: 9699539,
  deeppink: 16716947,
  deepskyblue: 49151,
  dimgray: 6908265,
  dimgrey: 6908265,
  dodgerblue: 2003199,
  firebrick: 11674146,
  floralwhite: 16775920,
  forestgreen: 2263842,
  fuchsia: 16711935,
  gainsboro: 14474460,
  ghostwhite: 16316671,
  gold: 16766720,
  goldenrod: 14329120,
  gray: 8421504,
  green: 32768,
  greenyellow: 11403055,
  grey: 8421504,
  honeydew: 15794160,
  hotpink: 16738740,
  indianred: 13458524,
  indigo: 4915330,
  ivory: 16777200,
  khaki: 15787660,
  lavender: 15132410,
  lavenderblush: 16773365,
  lawngreen: 8190976,
  lemonchiffon: 16775885,
  lightblue: 11393254,
  lightcoral: 15761536,
  lightcyan: 14745599,
  lightgoldenrodyellow: 16448210,
  lightgray: 13882323,
  lightgreen: 9498256,
  lightgrey: 13882323,
  lightpink: 16758465,
  lightsalmon: 16752762,
  lightseagreen: 2142890,
  lightskyblue: 8900346,
  lightslategray: 7833753,
  lightslategrey: 7833753,
  lightsteelblue: 11584734,
  lightyellow: 16777184,
  lime: 65280,
  limegreen: 3329330,
  linen: 16445670,
  magenta: 16711935,
  maroon: 8388608,
  mediumaquamarine: 6737322,
  mediumblue: 205,
  mediumorchid: 12211667,
  mediumpurple: 9662683,
  mediumseagreen: 3978097,
  mediumslateblue: 8087790,
  mediumspringgreen: 64154,
  mediumturquoise: 4772300,
  mediumvioletred: 13047173,
  midnightblue: 1644912,
  mintcream: 16121850,
  mistyrose: 16770273,
  moccasin: 16770229,
  navajowhite: 16768685,
  navy: 128,
  oldlace: 16643558,
  olive: 8421376,
  olivedrab: 7048739,
  orange: 16753920,
  orangered: 16729344,
  orchid: 14315734,
  palegoldenrod: 15657130,
  palegreen: 10025880,
  paleturquoise: 11529966,
  palevioletred: 14381203,
  papayawhip: 16773077,
  peachpuff: 16767673,
  peru: 13468991,
  pink: 16761035,
  plum: 14524637,
  powderblue: 11591910,
  purple: 8388736,
  // Added in CSS Colors Level 4:
  // https://drafts.csswg.org/css-color/#changes-from-3
  rebeccapurple: 6697881,
  red: 16711680,
  rosybrown: 12357519,
  royalblue: 4286945,
  saddlebrown: 9127187,
  salmon: 16416882,
  sandybrown: 16032864,
  seagreen: 3050327,
  seashell: 16774638,
  sienna: 10506797,
  silver: 12632256,
  skyblue: 8900331,
  slateblue: 6970061,
  slategray: 7372944,
  slategrey: 7372944,
  snow: 16775930,
  springgreen: 65407,
  steelblue: 4620980,
  tan: 13808780,
  teal: 32896,
  thistle: 14204888,
  tomato: 16737095,
  turquoise: 4251856,
  violet: 15631086,
  wheat: 16113331,
  white: 16777215,
  whitesmoke: 16119285,
  yellow: 16776960,
  yellowgreen: 10145074
};
var named_default = named;

// node_modules/culori/src/rgb/parseNamed.js
var parseNamed = (color) => {
  return parseNumber_default(named_default[color.toLowerCase()], 6);
};
var parseNamed_default = parseNamed;

// node_modules/culori/src/rgb/parseHex.js
var hex = /^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i;
var parseHex = (color) => {
  let match;
  return (match = color.match(hex)) ? parseNumber_default(parseInt(match[1], 16), match[1].length) : void 0;
};
var parseHex_default = parseHex;

// node_modules/culori/src/util/regex.js
var num = "([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)";
var num_none = `(?:${num}|none)`;
var per = `${num}%`;
var per_none = `(?:${num}%|none)`;
var num_per = `(?:${num}%|${num})`;
var num_per_none = `(?:${num}%|${num}|none)`;
var hue = `(?:${num}(deg|grad|rad|turn)|${num})`;
var hue_none = `(?:${num}(deg|grad|rad|turn)|${num}|none)`;
var c = `\\s*,\\s*`;
var rx_num_per_none = new RegExp("^" + num_per_none + "$");

// node_modules/culori/src/rgb/parseRgbLegacy.js
var rgb_num_old = new RegExp(
  `^rgba?\\(\\s*${num}${c}${num}${c}${num}\\s*(?:,\\s*${num_per}\\s*)?\\)$`
);
var rgb_per_old = new RegExp(
  `^rgba?\\(\\s*${per}${c}${per}${c}${per}\\s*(?:,\\s*${num_per}\\s*)?\\)$`
);
var parseRgbLegacy = (color) => {
  let res = { mode: "rgb" };
  let match;
  if (match = color.match(rgb_num_old)) {
    if (match[1] !== void 0) {
      res.r = match[1] / 255;
    }
    if (match[2] !== void 0) {
      res.g = match[2] / 255;
    }
    if (match[3] !== void 0) {
      res.b = match[3] / 255;
    }
  } else if (match = color.match(rgb_per_old)) {
    if (match[1] !== void 0) {
      res.r = match[1] / 100;
    }
    if (match[2] !== void 0) {
      res.g = match[2] / 100;
    }
    if (match[3] !== void 0) {
      res.b = match[3] / 100;
    }
  } else {
    return void 0;
  }
  if (match[4] !== void 0) {
    res.alpha = Math.max(0, Math.min(1, match[4] / 100));
  } else if (match[5] !== void 0) {
    res.alpha = Math.max(0, Math.min(1, +match[5]));
  }
  return res;
};
var parseRgbLegacy_default = parseRgbLegacy;

// node_modules/culori/src/_prepare.js
var prepare = (color, mode) => color === void 0 ? void 0 : typeof color !== "object" ? parse_default(color) : color.mode !== void 0 ? color : mode ? { ...color, mode } : void 0;
var prepare_default = prepare;

// node_modules/culori/src/converter.js
var converter = (target_mode = "rgb") => (color) => (color = prepare_default(color, target_mode)) !== void 0 ? (
  // if the color's mode corresponds to our target mode
  color.mode === target_mode ? (
    // then just return the color
    color
  ) : (
    // otherwise check to see if we have a dedicated
    // converter for the target mode
    converters[color.mode][target_mode] ? (
      // and return its result...
      converters[color.mode][target_mode](color)
    ) : (
      // ...otherwise pass through RGB as an intermediary step.
      // if the target mode is RGB...
      target_mode === "rgb" ? (
        // just return the RGB
        converters[color.mode].rgb(color)
      ) : (
        // otherwise convert color.mode -> RGB -> target_mode
        converters.rgb[target_mode](converters[color.mode].rgb(color))
      )
    )
  )
) : void 0;
var converter_default = converter;

// node_modules/culori/src/modes.js
var converters = {};
var modes = {};
var parsers = [];
var colorProfiles = {};
var identity = (v) => v;
var useMode = (definition29) => {
  converters[definition29.mode] = {
    ...converters[definition29.mode],
    ...definition29.toMode
  };
  Object.keys(definition29.fromMode || {}).forEach((k4) => {
    if (!converters[k4]) {
      converters[k4] = {};
    }
    converters[k4][definition29.mode] = definition29.fromMode[k4];
  });
  if (!definition29.ranges) {
    definition29.ranges = {};
  }
  if (!definition29.difference) {
    definition29.difference = {};
  }
  definition29.channels.forEach((channel) => {
    if (definition29.ranges[channel] === void 0) {
      definition29.ranges[channel] = [0, 1];
    }
    if (!definition29.interpolate[channel]) {
      throw new Error(`Missing interpolator for: ${channel}`);
    }
    if (typeof definition29.interpolate[channel] === "function") {
      definition29.interpolate[channel] = {
        use: definition29.interpolate[channel]
      };
    }
    if (!definition29.interpolate[channel].fixup) {
      definition29.interpolate[channel].fixup = identity;
    }
  });
  modes[definition29.mode] = definition29;
  (definition29.parse || []).forEach((parser) => {
    useParser(parser, definition29.mode);
  });
  return converter_default(definition29.mode);
};
var getMode = (mode) => modes[mode];
var useParser = (parser, mode) => {
  if (typeof parser === "string") {
    if (!mode) {
      throw new Error(`'mode' required when 'parser' is a string`);
    }
    colorProfiles[parser] = mode;
  } else if (typeof parser === "function") {
    if (parsers.indexOf(parser) < 0) {
      parsers.push(parser);
    }
  }
};

// node_modules/culori/src/parse.js
var IdentStartCodePoint = /[^\x00-\x7F]|[a-zA-Z_]/;
var IdentCodePoint = /[^\x00-\x7F]|[-\w]/;
var Tok = {
  Function: "function",
  Ident: "ident",
  Number: "number",
  Percentage: "percentage",
  ParenClose: ")",
  None: "none",
  Hue: "hue",
  Alpha: "alpha"
};
var _i = 0;
function is_num(chars) {
  let ch = chars[_i];
  let ch1 = chars[_i + 1];
  if (ch === "-" || ch === "+") {
    return /\d/.test(ch1) || ch1 === "." && /\d/.test(chars[_i + 2]);
  }
  if (ch === ".") {
    return /\d/.test(ch1);
  }
  return /\d/.test(ch);
}
function is_ident(chars) {
  if (_i >= chars.length) {
    return false;
  }
  let ch = chars[_i];
  if (IdentStartCodePoint.test(ch)) {
    return true;
  }
  if (ch === "-") {
    if (chars.length - _i < 2) {
      return false;
    }
    let ch1 = chars[_i + 1];
    if (ch1 === "-" || IdentStartCodePoint.test(ch1)) {
      return true;
    }
    return false;
  }
  return false;
}
var huenits = {
  deg: 1,
  rad: 180 / Math.PI,
  grad: 9 / 10,
  turn: 360
};
function num2(chars) {
  let value = "";
  if (chars[_i] === "-" || chars[_i] === "+") {
    value += chars[_i++];
  }
  value += digits(chars);
  if (chars[_i] === "." && /\d/.test(chars[_i + 1])) {
    value += chars[_i++] + digits(chars);
  }
  if (chars[_i] === "e" || chars[_i] === "E") {
    if ((chars[_i + 1] === "-" || chars[_i + 1] === "+") && /\d/.test(chars[_i + 2])) {
      value += chars[_i++] + chars[_i++] + digits(chars);
    } else if (/\d/.test(chars[_i + 1])) {
      value += chars[_i++] + digits(chars);
    }
  }
  if (is_ident(chars)) {
    let id = ident(chars);
    if (id === "deg" || id === "rad" || id === "turn" || id === "grad") {
      return { type: Tok.Hue, value: value * huenits[id] };
    }
    return void 0;
  }
  if (chars[_i] === "%") {
    _i++;
    return { type: Tok.Percentage, value: +value };
  }
  return { type: Tok.Number, value: +value };
}
function digits(chars) {
  let v = "";
  while (/\d/.test(chars[_i])) {
    v += chars[_i++];
  }
  return v;
}
function ident(chars) {
  let v = "";
  while (_i < chars.length && IdentCodePoint.test(chars[_i])) {
    v += chars[_i++];
  }
  return v;
}
function identlike(chars) {
  let v = ident(chars);
  if (chars[_i] === "(") {
    _i++;
    return { type: Tok.Function, value: v };
  }
  if (v === "none") {
    return { type: Tok.None, value: void 0 };
  }
  return { type: Tok.Ident, value: v };
}
function tokenize(str = "") {
  let chars = str.trim();
  let tokens = [];
  let ch;
  _i = 0;
  while (_i < chars.length) {
    ch = chars[_i++];
    if (ch === "\n" || ch === "	" || ch === " ") {
      while (_i < chars.length && (chars[_i] === "\n" || chars[_i] === "	" || chars[_i] === " ")) {
        _i++;
      }
      continue;
    }
    if (ch === ",") {
      return void 0;
    }
    if (ch === ")") {
      tokens.push({ type: Tok.ParenClose });
      continue;
    }
    if (ch === "+") {
      _i--;
      if (is_num(chars)) {
        tokens.push(num2(chars));
        continue;
      }
      return void 0;
    }
    if (ch === "-") {
      _i--;
      if (is_num(chars)) {
        tokens.push(num2(chars));
        continue;
      }
      if (is_ident(chars)) {
        tokens.push({ type: Tok.Ident, value: ident(chars) });
        continue;
      }
      return void 0;
    }
    if (ch === ".") {
      _i--;
      if (is_num(chars)) {
        tokens.push(num2(chars));
        continue;
      }
      return void 0;
    }
    if (ch === "/") {
      while (_i < chars.length && (chars[_i] === "\n" || chars[_i] === "	" || chars[_i] === " ")) {
        _i++;
      }
      let alpha;
      if (is_num(chars)) {
        alpha = num2(chars);
        if (alpha.type !== Tok.Hue) {
          tokens.push({ type: Tok.Alpha, value: alpha });
          continue;
        }
      }
      if (is_ident(chars)) {
        if (ident(chars) === "none") {
          tokens.push({
            type: Tok.Alpha,
            value: { type: Tok.None, value: void 0 }
          });
          continue;
        }
      }
      return void 0;
    }
    if (/\d/.test(ch)) {
      _i--;
      tokens.push(num2(chars));
      continue;
    }
    if (IdentStartCodePoint.test(ch)) {
      _i--;
      tokens.push(identlike(chars));
      continue;
    }
    return void 0;
  }
  return tokens;
}
function parseColorSyntax(tokens) {
  tokens._i = 0;
  let token = tokens[tokens._i++];
  if (!token || token.type !== Tok.Function || token.value !== "color") {
    return void 0;
  }
  token = tokens[tokens._i++];
  if (token.type !== Tok.Ident) {
    return void 0;
  }
  const mode = colorProfiles[token.value];
  if (!mode) {
    return void 0;
  }
  const res = { mode };
  const coords = consumeCoords(tokens, false);
  if (!coords) {
    return void 0;
  }
  const channels = getMode(mode).channels;
  for (let ii = 0, c2, ch; ii < channels.length; ii++) {
    c2 = coords[ii];
    ch = channels[ii];
    if (c2.type !== Tok.None) {
      res[ch] = c2.type === Tok.Number ? c2.value : c2.value / 100;
      if (ch === "alpha") {
        res[ch] = Math.max(0, Math.min(1, res[ch]));
      }
    }
  }
  return res;
}
function consumeCoords(tokens, includeHue) {
  const coords = [];
  let token;
  while (tokens._i < tokens.length) {
    token = tokens[tokens._i++];
    if (token.type === Tok.None || token.type === Tok.Number || token.type === Tok.Alpha || token.type === Tok.Percentage || includeHue && token.type === Tok.Hue) {
      coords.push(token);
      continue;
    }
    if (token.type === Tok.ParenClose) {
      if (tokens._i < tokens.length) {
        return void 0;
      }
      continue;
    }
    return void 0;
  }
  if (coords.length < 3 || coords.length > 4) {
    return void 0;
  }
  if (coords.length === 4) {
    if (coords[3].type !== Tok.Alpha) {
      return void 0;
    }
    coords[3] = coords[3].value;
  }
  if (coords.length === 3) {
    coords.push({ type: Tok.None, value: void 0 });
  }
  return coords.every((c2) => c2.type !== Tok.Alpha) ? coords : void 0;
}
function parseModernSyntax(tokens, includeHue) {
  tokens._i = 0;
  let token = tokens[tokens._i++];
  if (!token || token.type !== Tok.Function) {
    return void 0;
  }
  let coords = consumeCoords(tokens, includeHue);
  if (!coords) {
    return void 0;
  }
  coords.unshift(token.value);
  return coords;
}
var parse = (color) => {
  if (typeof color !== "string") {
    return void 0;
  }
  const tokens = tokenize(color);
  const parsed = tokens ? parseModernSyntax(tokens, true) : void 0;
  let result = void 0;
  let i = 0;
  let len = parsers.length;
  while (i < len) {
    if ((result = parsers[i++](color, parsed)) !== void 0) {
      return result;
    }
  }
  return tokens ? parseColorSyntax(tokens) : void 0;
};
var parse_default = parse;

// node_modules/culori/src/rgb/parseRgb.js
function parseRgb(color, parsed) {
  if (!parsed || parsed[0] !== "rgb" && parsed[0] !== "rgba") {
    return void 0;
  }
  const res = { mode: "rgb" };
  const [, r2, g, b, alpha] = parsed;
  if (r2.type === Tok.Hue || g.type === Tok.Hue || b.type === Tok.Hue) {
    return void 0;
  }
  if (r2.type !== Tok.None) {
    res.r = r2.type === Tok.Number ? r2.value / 255 : r2.value / 100;
  }
  if (g.type !== Tok.None) {
    res.g = g.type === Tok.Number ? g.value / 255 : g.value / 100;
  }
  if (b.type !== Tok.None) {
    res.b = b.type === Tok.Number ? b.value / 255 : b.value / 100;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
var parseRgb_default = parseRgb;

// node_modules/culori/src/rgb/parseTransparent.js
var parseTransparent = (c2) => c2 === "transparent" ? { mode: "rgb", r: 0, g: 0, b: 0, alpha: 0 } : void 0;
var parseTransparent_default = parseTransparent;

// node_modules/culori/src/interpolate/lerp.js
var lerp = (a, b, t) => a + t * (b - a);

// node_modules/culori/src/interpolate/piecewise.js
var get_classes = (arr) => {
  let classes = [];
  for (let i = 0; i < arr.length - 1; i++) {
    let a = arr[i];
    let b = arr[i + 1];
    if (a === void 0 && b === void 0) {
      classes.push(void 0);
    } else if (a !== void 0 && b !== void 0) {
      classes.push([a, b]);
    } else {
      classes.push(a !== void 0 ? [a, a] : [b, b]);
    }
  }
  return classes;
};
var interpolatorPiecewise = (interpolator) => (arr) => {
  let classes = get_classes(arr);
  return (t) => {
    let cls2 = t * classes.length;
    let idx = t >= 1 ? classes.length - 1 : Math.max(Math.floor(cls2), 0);
    let pair = classes[idx];
    return pair === void 0 ? void 0 : interpolator(pair[0], pair[1], cls2 - idx);
  };
};

// node_modules/culori/src/interpolate/linear.js
var interpolatorLinear = interpolatorPiecewise(lerp);

// node_modules/culori/src/fixup/alpha.js
var fixupAlpha = (arr) => {
  let some_defined = false;
  let res = arr.map((v) => {
    if (v !== void 0) {
      some_defined = true;
      return v;
    }
    return 1;
  });
  return some_defined ? res : arr;
};

// node_modules/culori/src/rgb/definition.js
var definition = {
  mode: "rgb",
  channels: ["r", "g", "b", "alpha"],
  parse: [
    parseRgb_default,
    parseHex_default,
    parseRgbLegacy_default,
    parseNamed_default,
    parseTransparent_default,
    "srgb"
  ],
  serialize: "srgb",
  interpolate: {
    r: interpolatorLinear,
    g: interpolatorLinear,
    b: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  gamut: true,
  white: { r: 1, g: 1, b: 1 },
  black: { r: 0, g: 0, b: 0 }
};
var definition_default = definition;

// node_modules/culori/src/a98/convertA98ToXyz65.js
var linearize = (v = 0) => Math.pow(Math.abs(v), 563 / 256) * Math.sign(v);
var convertA98ToXyz65 = (a982) => {
  let r2 = linearize(a982.r);
  let g = linearize(a982.g);
  let b = linearize(a982.b);
  let res = {
    mode: "xyz65",
    x: 0.5766690429101305 * r2 + 0.1855582379065463 * g + 0.1882286462349947 * b,
    y: 0.297344975250536 * r2 + 0.6273635662554661 * g + 0.0752914584939979 * b,
    z: 0.0270313613864123 * r2 + 0.0706888525358272 * g + 0.9913375368376386 * b
  };
  if (a982.alpha !== void 0) {
    res.alpha = a982.alpha;
  }
  return res;
};
var convertA98ToXyz65_default = convertA98ToXyz65;

// node_modules/culori/src/a98/convertXyz65ToA98.js
var gamma = (v) => Math.pow(Math.abs(v), 256 / 563) * Math.sign(v);
var convertXyz65ToA98 = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = {
    mode: "a98",
    r: gamma(
      x * 2.0415879038107465 - y * 0.5650069742788597 - 0.3447313507783297 * z
    ),
    g: gamma(
      x * -0.9692436362808798 + y * 1.8759675015077206 + 0.0415550574071756 * z
    ),
    b: gamma(
      x * 0.0134442806320312 - y * 0.1183623922310184 + 1.0151749943912058 * z
    )
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertXyz65ToA98_default = convertXyz65ToA98;

// node_modules/culori/src/lrgb/convertRgbToLrgb.js
var fn = (c2 = 0) => {
  const abs2 = Math.abs(c2);
  if (abs2 <= 0.04045) {
    return c2 / 12.92;
  }
  return (Math.sign(c2) || 1) * Math.pow((abs2 + 0.055) / 1.055, 2.4);
};
var convertRgbToLrgb = ({ r: r2, g, b, alpha }) => {
  let res = {
    mode: "lrgb",
    r: fn(r2),
    g: fn(g),
    b: fn(b)
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
var convertRgbToLrgb_default = convertRgbToLrgb;

// node_modules/culori/src/xyz65/convertRgbToXyz65.js
var convertRgbToXyz65 = (rgb5) => {
  let { r: r2, g, b, alpha } = convertRgbToLrgb_default(rgb5);
  let res = {
    mode: "xyz65",
    x: 0.4123907992659593 * r2 + 0.357584339383878 * g + 0.1804807884018343 * b,
    y: 0.2126390058715102 * r2 + 0.715168678767756 * g + 0.0721923153607337 * b,
    z: 0.0193308187155918 * r2 + 0.119194779794626 * g + 0.9505321522496607 * b
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertRgbToXyz65_default = convertRgbToXyz65;

// node_modules/culori/src/lrgb/convertLrgbToRgb.js
var fn2 = (c2 = 0) => {
  const abs2 = Math.abs(c2);
  if (abs2 > 31308e-7) {
    return (Math.sign(c2) || 1) * (1.055 * Math.pow(abs2, 1 / 2.4) - 0.055);
  }
  return c2 * 12.92;
};
var convertLrgbToRgb = ({ r: r2, g, b, alpha }, mode = "rgb") => {
  let res = {
    mode,
    r: fn2(r2),
    g: fn2(g),
    b: fn2(b)
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
var convertLrgbToRgb_default = convertLrgbToRgb;

// node_modules/culori/src/xyz65/convertXyz65ToRgb.js
var convertXyz65ToRgb = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = convertLrgbToRgb_default({
    r: x * 3.2409699419045226 - y * 1.537383177570094 - 0.4986107602930034 * z,
    g: x * -0.9692436362808796 + y * 1.8759675015077204 + 0.0415550574071756 * z,
    b: x * 0.0556300796969936 - y * 0.2039769588889765 + 1.0569715142428784 * z
  });
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertXyz65ToRgb_default = convertXyz65ToRgb;

// node_modules/culori/src/a98/definition.js
var definition2 = {
  ...definition_default,
  mode: "a98",
  parse: ["a98-rgb"],
  serialize: "a98-rgb",
  fromMode: {
    rgb: (color) => convertXyz65ToA98_default(convertRgbToXyz65_default(color)),
    xyz65: convertXyz65ToA98_default
  },
  toMode: {
    rgb: (color) => convertXyz65ToRgb_default(convertA98ToXyz65_default(color)),
    xyz65: convertA98ToXyz65_default
  }
};
var definition_default2 = definition2;

// node_modules/culori/src/util/normalizeHue.js
var normalizeHue = (hue3) => (hue3 = hue3 % 360) < 0 ? hue3 + 360 : hue3;
var normalizeHue_default = normalizeHue;

// node_modules/culori/src/fixup/hue.js
var hue2 = (hues, fn5) => {
  return hues.map((hue3, idx, arr) => {
    if (hue3 === void 0) {
      return hue3;
    }
    let normalized = normalizeHue_default(hue3);
    if (idx === 0 || hues[idx - 1] === void 0) {
      return normalized;
    }
    return fn5(normalized - normalizeHue_default(arr[idx - 1]));
  }).reduce((acc, curr) => {
    if (!acc.length || curr === void 0 || acc[acc.length - 1] === void 0) {
      acc.push(curr);
      return acc;
    }
    acc.push(curr + acc[acc.length - 1]);
    return acc;
  }, []);
};
var fixupHueShorter = (arr) => hue2(arr, (d) => Math.abs(d) <= 180 ? d : d - 360 * Math.sign(d));

// node_modules/culori/src/cubehelix/constants.js
var M = [-0.14861, 1.78277, -0.29227, -0.90649, 1.97294, 0];
var degToRad = Math.PI / 180;
var radToDeg = 180 / Math.PI;

// node_modules/culori/src/cubehelix/convertRgbToCubehelix.js
var DE = M[3] * M[4];
var BE = M[1] * M[4];
var BCAD = M[1] * M[2] - M[0] * M[3];
var convertRgbToCubehelix = ({ r: r2, g, b, alpha }) => {
  if (r2 === void 0) r2 = 0;
  if (g === void 0) g = 0;
  if (b === void 0) b = 0;
  let l = (BCAD * b + r2 * DE - g * BE) / (BCAD + DE - BE);
  let x = b - l;
  let y = (M[4] * (g - l) - M[2] * x) / M[3];
  let res = {
    mode: "cubehelix",
    l,
    s: l === 0 || l === 1 ? void 0 : Math.sqrt(x * x + y * y) / (M[4] * l * (1 - l))
  };
  if (res.s) res.h = Math.atan2(y, x) * radToDeg - 120;
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
var convertRgbToCubehelix_default = convertRgbToCubehelix;

// node_modules/culori/src/cubehelix/convertCubehelixToRgb.js
var convertCubehelixToRgb = ({ h, s, l, alpha }) => {
  let res = { mode: "rgb" };
  h = (h === void 0 ? 0 : h + 120) * degToRad;
  if (l === void 0) l = 0;
  let amp = s === void 0 ? 0 : s * l * (1 - l);
  let cosh = Math.cos(h);
  let sinh = Math.sin(h);
  res.r = l + amp * (M[0] * cosh + M[1] * sinh);
  res.g = l + amp * (M[2] * cosh + M[3] * sinh);
  res.b = l + amp * (M[4] * cosh + M[5] * sinh);
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
var convertCubehelixToRgb_default = convertCubehelixToRgb;

// node_modules/culori/src/difference.js
var differenceHueSaturation = (std, smp) => {
  if (std.h === void 0 || smp.h === void 0 || !std.s || !smp.s) {
    return 0;
  }
  let std_h = normalizeHue_default(std.h);
  let smp_h = normalizeHue_default(smp.h);
  let dH = Math.sin((smp_h - std_h + 360) / 2 * Math.PI / 180);
  return 2 * Math.sqrt(std.s * smp.s) * dH;
};
var differenceHueNaive = (std, smp) => {
  if (std.h === void 0 || smp.h === void 0) {
    return 0;
  }
  let std_h = normalizeHue_default(std.h);
  let smp_h = normalizeHue_default(smp.h);
  if (Math.abs(smp_h - std_h) > 180) {
    return std_h - (smp_h - 360 * Math.sign(smp_h - std_h));
  }
  return smp_h - std_h;
};
var differenceHueChroma = (std, smp) => {
  if (std.h === void 0 || smp.h === void 0 || !std.c || !smp.c) {
    return 0;
  }
  let std_h = normalizeHue_default(std.h);
  let smp_h = normalizeHue_default(smp.h);
  let dH = Math.sin((smp_h - std_h + 360) / 2 * Math.PI / 180);
  return 2 * Math.sqrt(std.c * smp.c) * dH;
};

// node_modules/culori/src/average.js
var averageAngle = (val) => {
  let sum2 = val.reduce(
    (sum3, val2) => {
      if (val2 !== void 0) {
        let rad = val2 * Math.PI / 180;
        sum3.sin += Math.sin(rad);
        sum3.cos += Math.cos(rad);
      }
      return sum3;
    },
    { sin: 0, cos: 0 }
  );
  let angle = Math.atan2(sum2.sin, sum2.cos) * 180 / Math.PI;
  return angle < 0 ? 360 + angle : angle;
};

// node_modules/culori/src/cubehelix/definition.js
var definition3 = {
  mode: "cubehelix",
  channels: ["h", "s", "l", "alpha"],
  parse: ["--cubehelix"],
  serialize: "--cubehelix",
  ranges: {
    h: [0, 360],
    s: [0, 4.614],
    l: [0, 1]
  },
  fromMode: {
    rgb: convertRgbToCubehelix_default
  },
  toMode: {
    rgb: convertCubehelixToRgb_default
  },
  interpolate: {
    h: {
      use: interpolatorLinear,
      fixup: fixupHueShorter
    },
    s: interpolatorLinear,
    l: interpolatorLinear,
    alpha: {
      use: interpolatorLinear,
      fixup: fixupAlpha
    }
  },
  difference: {
    h: differenceHueSaturation
  },
  average: {
    h: averageAngle
  }
};
var definition_default3 = definition3;

// node_modules/culori/src/lch/convertLabToLch.js
var convertLabToLch = ({ l, a, b, alpha }, mode = "lch") => {
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let c2 = Math.sqrt(a * a + b * b);
  let res = { mode, l, c: c2 };
  if (c2) res.h = normalizeHue_default(Math.atan2(b, a) * 180 / Math.PI);
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
var convertLabToLch_default = convertLabToLch;

// node_modules/culori/src/lch/convertLchToLab.js
var convertLchToLab = ({ l, c: c2, h, alpha }, mode = "lab") => {
  if (h === void 0) h = 0;
  let res = {
    mode,
    l,
    a: c2 ? c2 * Math.cos(h / 180 * Math.PI) : 0,
    b: c2 ? c2 * Math.sin(h / 180 * Math.PI) : 0
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
var convertLchToLab_default = convertLchToLab;

// node_modules/culori/src/xyz65/constants.js
var k = Math.pow(29, 3) / Math.pow(3, 3);
var e = Math.pow(6, 3) / Math.pow(29, 3);

// node_modules/culori/src/constants.js
var D50 = {
  X: 0.3457 / 0.3585,
  Y: 1,
  Z: (1 - 0.3457 - 0.3585) / 0.3585
};
var D65 = {
  X: 0.3127 / 0.329,
  Y: 1,
  Z: (1 - 0.3127 - 0.329) / 0.329
};
var k2 = Math.pow(29, 3) / Math.pow(3, 3);
var e2 = Math.pow(6, 3) / Math.pow(29, 3);

// node_modules/culori/src/lab65/convertLab65ToXyz65.js
var fn3 = (v) => Math.pow(v, 3) > e ? Math.pow(v, 3) : (116 * v - 16) / k;
var convertLab65ToXyz65 = ({ l, a, b, alpha }) => {
  if (l === void 0) l = 0;
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let fy = (l + 16) / 116;
  let fx = a / 500 + fy;
  let fz = fy - b / 200;
  let res = {
    mode: "xyz65",
    x: fn3(fx) * D65.X,
    y: fn3(fy) * D65.Y,
    z: fn3(fz) * D65.Z
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertLab65ToXyz65_default = convertLab65ToXyz65;

// node_modules/culori/src/lab65/convertLab65ToRgb.js
var convertLab65ToRgb = (lab2) => convertXyz65ToRgb_default(convertLab65ToXyz65_default(lab2));
var convertLab65ToRgb_default = convertLab65ToRgb;

// node_modules/culori/src/lab65/convertXyz65ToLab65.js
var f = (value) => value > e ? Math.cbrt(value) : (k * value + 16) / 116;
var convertXyz65ToLab65 = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let f0 = f(x / D65.X);
  let f1 = f(y / D65.Y);
  let f22 = f(z / D65.Z);
  let res = {
    mode: "lab65",
    l: 116 * f1 - 16,
    a: 500 * (f0 - f1),
    b: 200 * (f1 - f22)
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertXyz65ToLab65_default = convertXyz65ToLab65;

// node_modules/culori/src/lab65/convertRgbToLab65.js
var convertRgbToLab65 = (rgb5) => {
  let res = convertXyz65ToLab65_default(convertRgbToXyz65_default(rgb5));
  if (rgb5.r === rgb5.b && rgb5.b === rgb5.g) {
    res.a = res.b = 0;
  }
  return res;
};
var convertRgbToLab65_default = convertRgbToLab65;

// node_modules/culori/src/dlch/constants.js
var kE = 1;
var kCH = 1;
var θ = 26 / 180 * Math.PI;
var cosθ = Math.cos(θ);
var sinθ = Math.sin(θ);
var factor = 100 / Math.log(139 / 100);

// node_modules/culori/src/dlch/convertDlchToLab65.js
var convertDlchToLab65 = ({ l, c: c2, h, alpha }) => {
  if (l === void 0) l = 0;
  if (c2 === void 0) c2 = 0;
  if (h === void 0) h = 0;
  let res = {
    mode: "lab65",
    l: (Math.exp(l * kE / factor) - 1) / 39e-4
  };
  let G = (Math.exp(0.0435 * c2 * kCH * kE) - 1) / 0.075;
  let e4 = G * Math.cos(h / 180 * Math.PI - θ);
  let f3 = G * Math.sin(h / 180 * Math.PI - θ);
  res.a = e4 * cosθ - f3 / 0.83 * sinθ;
  res.b = e4 * sinθ + f3 / 0.83 * cosθ;
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
var convertDlchToLab65_default = convertDlchToLab65;

// node_modules/culori/src/dlch/convertLab65ToDlch.js
var convertLab65ToDlch = ({ l, a, b, alpha }) => {
  if (l === void 0) l = 0;
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let e4 = a * cosθ + b * sinθ;
  let f3 = 0.83 * (b * cosθ - a * sinθ);
  let G = Math.sqrt(e4 * e4 + f3 * f3);
  let res = {
    mode: "dlch",
    l: factor / kE * Math.log(1 + 39e-4 * l),
    c: Math.log(1 + 0.075 * G) / (0.0435 * kCH * kE)
  };
  if (res.c) {
    res.h = normalizeHue_default((Math.atan2(f3, e4) + θ) / Math.PI * 180);
  }
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
var convertLab65ToDlch_default = convertLab65ToDlch;

// node_modules/culori/src/dlab/definition.js
var convertDlabToLab65 = (c2) => convertDlchToLab65_default(convertLabToLch_default(c2, "dlch"));
var convertLab65ToDlab = (c2) => convertLchToLab_default(convertLab65ToDlch_default(c2), "dlab");
var definition4 = {
  mode: "dlab",
  parse: ["--din99o-lab"],
  serialize: "--din99o-lab",
  toMode: {
    lab65: convertDlabToLab65,
    rgb: (c2) => convertLab65ToRgb_default(convertDlabToLab65(c2))
  },
  fromMode: {
    lab65: convertLab65ToDlab,
    rgb: (c2) => convertLab65ToDlab(convertRgbToLab65_default(c2))
  },
  channels: ["l", "a", "b", "alpha"],
  ranges: {
    l: [0, 100],
    a: [-40.09, 45.501],
    b: [-40.469, 44.344]
  },
  interpolate: {
    l: interpolatorLinear,
    a: interpolatorLinear,
    b: interpolatorLinear,
    alpha: {
      use: interpolatorLinear,
      fixup: fixupAlpha
    }
  }
};
var definition_default4 = definition4;

// node_modules/culori/src/dlch/definition.js
var definition5 = {
  mode: "dlch",
  parse: ["--din99o-lch"],
  serialize: "--din99o-lch",
  toMode: {
    lab65: convertDlchToLab65_default,
    dlab: (c2) => convertLchToLab_default(c2, "dlab"),
    rgb: (c2) => convertLab65ToRgb_default(convertDlchToLab65_default(c2))
  },
  fromMode: {
    lab65: convertLab65ToDlch_default,
    dlab: (c2) => convertLabToLch_default(c2, "dlch"),
    rgb: (c2) => convertLab65ToDlch_default(convertRgbToLab65_default(c2))
  },
  channels: ["l", "c", "h", "alpha"],
  ranges: {
    l: [0, 100],
    c: [0, 51.484],
    h: [0, 360]
  },
  interpolate: {
    l: interpolatorLinear,
    c: interpolatorLinear,
    h: {
      use: interpolatorLinear,
      fixup: fixupHueShorter
    },
    alpha: {
      use: interpolatorLinear,
      fixup: fixupAlpha
    }
  },
  difference: {
    h: differenceHueChroma
  },
  average: {
    h: averageAngle
  }
};
var definition_default5 = definition5;

// node_modules/culori/src/hsi/convertHsiToRgb.js
function convertHsiToRgb({ h, s, i, alpha }) {
  h = normalizeHue_default(h !== void 0 ? h : 0);
  if (s === void 0) s = 0;
  if (i === void 0) i = 0;
  let f3 = Math.abs(h / 60 % 2 - 1);
  let res;
  switch (Math.floor(h / 60)) {
    case 0:
      res = {
        r: i * (1 + s * (3 / (2 - f3) - 1)),
        g: i * (1 + s * (3 * (1 - f3) / (2 - f3) - 1)),
        b: i * (1 - s)
      };
      break;
    case 1:
      res = {
        r: i * (1 + s * (3 * (1 - f3) / (2 - f3) - 1)),
        g: i * (1 + s * (3 / (2 - f3) - 1)),
        b: i * (1 - s)
      };
      break;
    case 2:
      res = {
        r: i * (1 - s),
        g: i * (1 + s * (3 / (2 - f3) - 1)),
        b: i * (1 + s * (3 * (1 - f3) / (2 - f3) - 1))
      };
      break;
    case 3:
      res = {
        r: i * (1 - s),
        g: i * (1 + s * (3 * (1 - f3) / (2 - f3) - 1)),
        b: i * (1 + s * (3 / (2 - f3) - 1))
      };
      break;
    case 4:
      res = {
        r: i * (1 + s * (3 * (1 - f3) / (2 - f3) - 1)),
        g: i * (1 - s),
        b: i * (1 + s * (3 / (2 - f3) - 1))
      };
      break;
    case 5:
      res = {
        r: i * (1 + s * (3 / (2 - f3) - 1)),
        g: i * (1 - s),
        b: i * (1 + s * (3 * (1 - f3) / (2 - f3) - 1))
      };
      break;
    default:
      res = { r: i * (1 - s), g: i * (1 - s), b: i * (1 - s) };
  }
  res.mode = "rgb";
  if (alpha !== void 0) res.alpha = alpha;
  return res;
}

// node_modules/culori/src/hsi/convertRgbToHsi.js
function convertRgbToHsi({ r: r2, g, b, alpha }) {
  if (r2 === void 0) r2 = 0;
  if (g === void 0) g = 0;
  if (b === void 0) b = 0;
  let M3 = Math.max(r2, g, b), m = Math.min(r2, g, b);
  let res = {
    mode: "hsi",
    s: r2 + g + b === 0 ? 0 : 1 - 3 * m / (r2 + g + b),
    i: (r2 + g + b) / 3
  };
  if (M3 - m !== 0)
    res.h = (M3 === r2 ? (g - b) / (M3 - m) + (g < b) * 6 : M3 === g ? (b - r2) / (M3 - m) + 2 : (r2 - g) / (M3 - m) + 4) * 60;
  if (alpha !== void 0) res.alpha = alpha;
  return res;
}

// node_modules/culori/src/hsi/definition.js
var definition6 = {
  mode: "hsi",
  toMode: {
    rgb: convertHsiToRgb
  },
  parse: ["--hsi"],
  serialize: "--hsi",
  fromMode: {
    rgb: convertRgbToHsi
  },
  channels: ["h", "s", "i", "alpha"],
  ranges: {
    h: [0, 360]
  },
  gamut: "rgb",
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    s: interpolatorLinear,
    i: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueSaturation
  },
  average: {
    h: averageAngle
  }
};
var definition_default6 = definition6;

// node_modules/culori/src/hsl/convertHslToRgb.js
function convertHslToRgb({ h, s, l, alpha }) {
  h = normalizeHue_default(h !== void 0 ? h : 0);
  if (s === void 0) s = 0;
  if (l === void 0) l = 0;
  let m1 = l + s * (l < 0.5 ? l : 1 - l);
  let m2 = m1 - (m1 - l) * 2 * Math.abs(h / 60 % 2 - 1);
  let res;
  switch (Math.floor(h / 60)) {
    case 0:
      res = { r: m1, g: m2, b: 2 * l - m1 };
      break;
    case 1:
      res = { r: m2, g: m1, b: 2 * l - m1 };
      break;
    case 2:
      res = { r: 2 * l - m1, g: m1, b: m2 };
      break;
    case 3:
      res = { r: 2 * l - m1, g: m2, b: m1 };
      break;
    case 4:
      res = { r: m2, g: 2 * l - m1, b: m1 };
      break;
    case 5:
      res = { r: m1, g: 2 * l - m1, b: m2 };
      break;
    default:
      res = { r: 2 * l - m1, g: 2 * l - m1, b: 2 * l - m1 };
  }
  res.mode = "rgb";
  if (alpha !== void 0) res.alpha = alpha;
  return res;
}

// node_modules/culori/src/hsl/convertRgbToHsl.js
function convertRgbToHsl({ r: r2, g, b, alpha }) {
  if (r2 === void 0) r2 = 0;
  if (g === void 0) g = 0;
  if (b === void 0) b = 0;
  let M3 = Math.max(r2, g, b), m = Math.min(r2, g, b);
  let res = {
    mode: "hsl",
    s: M3 === m ? 0 : (M3 - m) / (1 - Math.abs(M3 + m - 1)),
    l: 0.5 * (M3 + m)
  };
  if (M3 - m !== 0)
    res.h = (M3 === r2 ? (g - b) / (M3 - m) + (g < b) * 6 : M3 === g ? (b - r2) / (M3 - m) + 2 : (r2 - g) / (M3 - m) + 4) * 60;
  if (alpha !== void 0) res.alpha = alpha;
  return res;
}

// node_modules/culori/src/util/hue.js
var hueToDeg = (val, unit) => {
  switch (unit) {
    case "deg":
      return +val;
    case "rad":
      return val / Math.PI * 180;
    case "grad":
      return val / 10 * 9;
    case "turn":
      return val * 360;
  }
};
var hue_default = hueToDeg;

// node_modules/culori/src/hsl/parseHslLegacy.js
var hsl_old = new RegExp(
  `^hsla?\\(\\s*${hue}${c}${per}${c}${per}\\s*(?:,\\s*${num_per}\\s*)?\\)$`
);
var parseHslLegacy = (color) => {
  let match = color.match(hsl_old);
  if (!match) return;
  let res = { mode: "hsl" };
  if (match[3] !== void 0) {
    res.h = +match[3];
  } else if (match[1] !== void 0 && match[2] !== void 0) {
    res.h = hue_default(match[1], match[2]);
  }
  if (match[4] !== void 0) {
    res.s = Math.min(Math.max(0, match[4] / 100), 1);
  }
  if (match[5] !== void 0) {
    res.l = Math.min(Math.max(0, match[5] / 100), 1);
  }
  if (match[6] !== void 0) {
    res.alpha = Math.max(0, Math.min(1, match[6] / 100));
  } else if (match[7] !== void 0) {
    res.alpha = Math.max(0, Math.min(1, +match[7]));
  }
  return res;
};
var parseHslLegacy_default = parseHslLegacy;

// node_modules/culori/src/hsl/parseHsl.js
function parseHsl(color, parsed) {
  if (!parsed || parsed[0] !== "hsl" && parsed[0] !== "hsla") {
    return void 0;
  }
  const res = { mode: "hsl" };
  const [, h, s, l, alpha] = parsed;
  if (h.type !== Tok.None) {
    if (h.type === Tok.Percentage) {
      return void 0;
    }
    res.h = h.value;
  }
  if (s.type !== Tok.None) {
    if (s.type === Tok.Hue) {
      return void 0;
    }
    res.s = s.value / 100;
  }
  if (l.type !== Tok.None) {
    if (l.type === Tok.Hue) {
      return void 0;
    }
    res.l = l.value / 100;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
var parseHsl_default = parseHsl;

// node_modules/culori/src/hsl/definition.js
var definition7 = {
  mode: "hsl",
  toMode: {
    rgb: convertHslToRgb
  },
  fromMode: {
    rgb: convertRgbToHsl
  },
  channels: ["h", "s", "l", "alpha"],
  ranges: {
    h: [0, 360]
  },
  gamut: "rgb",
  parse: [parseHsl_default, parseHslLegacy_default],
  serialize: (c2) => `hsl(${c2.h !== void 0 ? c2.h : "none"} ${c2.s !== void 0 ? c2.s * 100 + "%" : "none"} ${c2.l !== void 0 ? c2.l * 100 + "%" : "none"}${c2.alpha < 1 ? ` / ${c2.alpha}` : ""})`,
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    s: interpolatorLinear,
    l: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueSaturation
  },
  average: {
    h: averageAngle
  }
};
var definition_default7 = definition7;

// node_modules/culori/src/hsv/convertHsvToRgb.js
function convertHsvToRgb({ h, s, v, alpha }) {
  h = normalizeHue_default(h !== void 0 ? h : 0);
  if (s === void 0) s = 0;
  if (v === void 0) v = 0;
  let f3 = Math.abs(h / 60 % 2 - 1);
  let res;
  switch (Math.floor(h / 60)) {
    case 0:
      res = { r: v, g: v * (1 - s * f3), b: v * (1 - s) };
      break;
    case 1:
      res = { r: v * (1 - s * f3), g: v, b: v * (1 - s) };
      break;
    case 2:
      res = { r: v * (1 - s), g: v, b: v * (1 - s * f3) };
      break;
    case 3:
      res = { r: v * (1 - s), g: v * (1 - s * f3), b: v };
      break;
    case 4:
      res = { r: v * (1 - s * f3), g: v * (1 - s), b: v };
      break;
    case 5:
      res = { r: v, g: v * (1 - s), b: v * (1 - s * f3) };
      break;
    default:
      res = { r: v * (1 - s), g: v * (1 - s), b: v * (1 - s) };
  }
  res.mode = "rgb";
  if (alpha !== void 0) res.alpha = alpha;
  return res;
}

// node_modules/culori/src/hsv/convertRgbToHsv.js
function convertRgbToHsv({ r: r2, g, b, alpha }) {
  if (r2 === void 0) r2 = 0;
  if (g === void 0) g = 0;
  if (b === void 0) b = 0;
  let M3 = Math.max(r2, g, b), m = Math.min(r2, g, b);
  let res = {
    mode: "hsv",
    s: M3 === 0 ? 0 : 1 - m / M3,
    v: M3
  };
  if (M3 - m !== 0)
    res.h = (M3 === r2 ? (g - b) / (M3 - m) + (g < b) * 6 : M3 === g ? (b - r2) / (M3 - m) + 2 : (r2 - g) / (M3 - m) + 4) * 60;
  if (alpha !== void 0) res.alpha = alpha;
  return res;
}

// node_modules/culori/src/hsv/definition.js
var definition8 = {
  mode: "hsv",
  toMode: {
    rgb: convertHsvToRgb
  },
  parse: ["--hsv"],
  serialize: "--hsv",
  fromMode: {
    rgb: convertRgbToHsv
  },
  channels: ["h", "s", "v", "alpha"],
  ranges: {
    h: [0, 360]
  },
  gamut: "rgb",
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    s: interpolatorLinear,
    v: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueSaturation
  },
  average: {
    h: averageAngle
  }
};
var definition_default8 = definition8;

// node_modules/culori/src/hwb/convertHwbToRgb.js
function convertHwbToRgb({ h, w, b, alpha }) {
  if (w === void 0) w = 0;
  if (b === void 0) b = 0;
  if (w + b > 1) {
    let s = w + b;
    w /= s;
    b /= s;
  }
  return convertHsvToRgb({
    h,
    s: b === 1 ? 1 : 1 - w / (1 - b),
    v: 1 - b,
    alpha
  });
}

// node_modules/culori/src/hwb/convertRgbToHwb.js
function convertRgbToHwb(rgba) {
  let hsv2 = convertRgbToHsv(rgba);
  if (hsv2 === void 0) return void 0;
  let s = hsv2.s !== void 0 ? hsv2.s : 0;
  let v = hsv2.v !== void 0 ? hsv2.v : 0;
  let res = {
    mode: "hwb",
    w: (1 - s) * v,
    b: 1 - v
  };
  if (hsv2.h !== void 0) res.h = hsv2.h;
  if (hsv2.alpha !== void 0) res.alpha = hsv2.alpha;
  return res;
}

// node_modules/culori/src/hwb/parseHwb.js
function ParseHwb(color, parsed) {
  if (!parsed || parsed[0] !== "hwb") {
    return void 0;
  }
  const res = { mode: "hwb" };
  const [, h, w, b, alpha] = parsed;
  if (h.type !== Tok.None) {
    if (h.type === Tok.Percentage) {
      return void 0;
    }
    res.h = h.value;
  }
  if (w.type !== Tok.None) {
    if (w.type === Tok.Hue) {
      return void 0;
    }
    res.w = w.value / 100;
  }
  if (b.type !== Tok.None) {
    if (b.type === Tok.Hue) {
      return void 0;
    }
    res.b = b.value / 100;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
var parseHwb_default = ParseHwb;

// node_modules/culori/src/hwb/definition.js
var definition9 = {
  mode: "hwb",
  toMode: {
    rgb: convertHwbToRgb
  },
  fromMode: {
    rgb: convertRgbToHwb
  },
  channels: ["h", "w", "b", "alpha"],
  ranges: {
    h: [0, 360]
  },
  gamut: "rgb",
  parse: [parseHwb_default],
  serialize: (c2) => `hwb(${c2.h !== void 0 ? c2.h : "none"} ${c2.w !== void 0 ? c2.w * 100 + "%" : "none"} ${c2.b !== void 0 ? c2.b * 100 + "%" : "none"}${c2.alpha < 1 ? ` / ${c2.alpha}` : ""})`,
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    w: interpolatorLinear,
    b: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueNaive
  },
  average: {
    h: averageAngle
  }
};
var definition_default9 = definition9;

// node_modules/culori/src/hdr/constants.js
var YW = 203;

// node_modules/culori/src/hdr/transfer.js
var M1 = 0.1593017578125;
var M2 = 78.84375;
var C1 = 0.8359375;
var C2 = 18.8515625;
var C3 = 18.6875;
function transferPqDecode(v) {
  if (v < 0) return 0;
  const c2 = Math.pow(v, 1 / M2);
  return 1e4 * Math.pow(Math.max(0, c2 - C1) / (C2 - C3 * c2), 1 / M1);
}
function transferPqEncode(v) {
  if (v < 0) return 0;
  const c2 = Math.pow(v / 1e4, M1);
  return Math.pow((C1 + C2 * c2) / (1 + C3 * c2), M2);
}

// node_modules/culori/src/itp/convertItpToXyz65.js
var toRel = (c2) => Math.max(c2 / YW, 0);
var convertItpToXyz65 = ({ i, t, p: p4, alpha }) => {
  if (i === void 0) i = 0;
  if (t === void 0) t = 0;
  if (p4 === void 0) p4 = 0;
  const l = transferPqDecode(
    i + 0.008609037037932761 * t + 0.11102962500302593 * p4
  );
  const m = transferPqDecode(
    i - 0.00860903703793275 * t - 0.11102962500302599 * p4
  );
  const s = transferPqDecode(
    i + 0.5600313357106791 * t - 0.32062717498731885 * p4
  );
  const res = {
    mode: "xyz65",
    x: toRel(
      2.070152218389422 * l - 1.3263473389671556 * m + 0.2066510476294051 * s
    ),
    y: toRel(
      0.3647385209748074 * l + 0.680566024947227 * m - 0.0453045459220346 * s
    ),
    z: toRel(
      -0.049747207535812 * l - 0.0492609666966138 * m + 1.1880659249923042 * s
    )
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertItpToXyz65_default = convertItpToXyz65;

// node_modules/culori/src/itp/convertXyz65ToItp.js
var toAbs = (c2 = 0) => Math.max(c2 * YW, 0);
var convertXyz65ToItp = ({ x, y, z, alpha }) => {
  const absX = toAbs(x);
  const absY = toAbs(y);
  const absZ = toAbs(z);
  const l = transferPqEncode(
    0.3592832590121217 * absX + 0.6976051147779502 * absY - 0.0358915932320289 * absZ
  );
  const m = transferPqEncode(
    -0.1920808463704995 * absX + 1.1004767970374323 * absY + 0.0753748658519118 * absZ
  );
  const s = transferPqEncode(
    0.0070797844607477 * absX + 0.0748396662186366 * absY + 0.8433265453898765 * absZ
  );
  const i = 0.5 * l + 0.5 * m;
  const t = 1.61376953125 * l - 3.323486328125 * m + 1.709716796875 * s;
  const p4 = 4.378173828125 * l - 4.24560546875 * m - 0.132568359375 * s;
  const res = { mode: "itp", i, t, p: p4 };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertXyz65ToItp_default = convertXyz65ToItp;

// node_modules/culori/src/itp/definition.js
var definition10 = {
  mode: "itp",
  channels: ["i", "t", "p", "alpha"],
  parse: ["--ictcp"],
  serialize: "--ictcp",
  toMode: {
    xyz65: convertItpToXyz65_default,
    rgb: (color) => convertXyz65ToRgb_default(convertItpToXyz65_default(color))
  },
  fromMode: {
    xyz65: convertXyz65ToItp_default,
    rgb: (color) => convertXyz65ToItp_default(convertRgbToXyz65_default(color))
  },
  ranges: {
    i: [0, 0.581],
    t: [-0.369, 0.272],
    p: [-0.164, 0.331]
  },
  interpolate: {
    i: interpolatorLinear,
    t: interpolatorLinear,
    p: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
var definition_default10 = definition10;

// node_modules/culori/src/jab/convertXyz65ToJab.js
var p = 134.03437499999998;
var d0 = 16295499532821565e-27;
var jabPqEncode = (v) => {
  if (v < 0) return 0;
  let vn3 = Math.pow(v / 1e4, M1);
  return Math.pow((C1 + C2 * vn3) / (1 + C3 * vn3), p);
};
var abs = (v = 0) => Math.max(v * 203, 0);
var convertXyz65ToJab = ({ x, y, z, alpha }) => {
  x = abs(x);
  y = abs(y);
  z = abs(z);
  let xp = 1.15 * x - 0.15 * z;
  let yp = 0.66 * y + 0.34 * x;
  let l = jabPqEncode(0.41478972 * xp + 0.579999 * yp + 0.014648 * z);
  let m = jabPqEncode(-0.20151 * xp + 1.120649 * yp + 0.0531008 * z);
  let s = jabPqEncode(-0.0166008 * xp + 0.2648 * yp + 0.6684799 * z);
  let i = (l + m) / 2;
  let res = {
    mode: "jab",
    j: 0.44 * i / (1 - 0.56 * i) - d0,
    a: 3.524 * l - 4.066708 * m + 0.542708 * s,
    b: 0.199076 * l + 1.096799 * m - 1.295875 * s
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertXyz65ToJab_default = convertXyz65ToJab;

// node_modules/culori/src/jab/convertJabToXyz65.js
var p2 = 134.03437499999998;
var d02 = 16295499532821565e-27;
var jabPqDecode = (v) => {
  if (v < 0) return 0;
  let vp = Math.pow(v, 1 / p2);
  return 1e4 * Math.pow((C1 - vp) / (C3 * vp - C2), 1 / M1);
};
var rel = (v) => v / 203;
var convertJabToXyz65 = ({ j, a, b, alpha }) => {
  if (j === void 0) j = 0;
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let i = (j + d02) / (0.44 + 0.56 * (j + d02));
  let l = jabPqDecode(i + 0.13860504 * a + 0.058047316 * b);
  let m = jabPqDecode(i - 0.13860504 * a - 0.058047316 * b);
  let s = jabPqDecode(i - 0.096019242 * a - 0.8118919 * b);
  let res = {
    mode: "xyz65",
    x: rel(
      1.661373024652174 * l - 0.914523081304348 * m + 0.23136208173913045 * s
    ),
    y: rel(
      -0.3250758611844533 * l + 1.571847026732543 * m - 0.21825383453227928 * s
    ),
    z: rel(-0.090982811 * l - 0.31272829 * m + 1.5227666 * s)
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertJabToXyz65_default = convertJabToXyz65;

// node_modules/culori/src/jab/convertRgbToJab.js
var convertRgbToJab = (rgb5) => {
  let res = convertXyz65ToJab_default(convertRgbToXyz65_default(rgb5));
  if (rgb5.r === rgb5.b && rgb5.b === rgb5.g) {
    res.a = res.b = 0;
  }
  return res;
};
var convertRgbToJab_default = convertRgbToJab;

// node_modules/culori/src/jab/convertJabToRgb.js
var convertJabToRgb = (color) => convertXyz65ToRgb_default(convertJabToXyz65_default(color));
var convertJabToRgb_default = convertJabToRgb;

// node_modules/culori/src/jab/definition.js
var definition11 = {
  mode: "jab",
  channels: ["j", "a", "b", "alpha"],
  parse: ["--jzazbz"],
  serialize: "--jzazbz",
  fromMode: {
    rgb: convertRgbToJab_default,
    xyz65: convertXyz65ToJab_default
  },
  toMode: {
    rgb: convertJabToRgb_default,
    xyz65: convertJabToXyz65_default
  },
  ranges: {
    j: [0, 0.222],
    a: [-0.109, 0.129],
    b: [-0.185, 0.134]
  },
  interpolate: {
    j: interpolatorLinear,
    a: interpolatorLinear,
    b: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
var definition_default11 = definition11;

// node_modules/culori/src/jch/convertJabToJch.js
var convertJabToJch = ({ j, a, b, alpha }) => {
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let c2 = Math.sqrt(a * a + b * b);
  let res = {
    mode: "jch",
    j,
    c: c2
  };
  if (c2) {
    res.h = normalizeHue_default(Math.atan2(b, a) * 180 / Math.PI);
  }
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertJabToJch_default = convertJabToJch;

// node_modules/culori/src/jch/convertJchToJab.js
var convertJchToJab = ({ j, c: c2, h, alpha }) => {
  if (h === void 0) h = 0;
  let res = {
    mode: "jab",
    j,
    a: c2 ? c2 * Math.cos(h / 180 * Math.PI) : 0,
    b: c2 ? c2 * Math.sin(h / 180 * Math.PI) : 0
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
var convertJchToJab_default = convertJchToJab;

// node_modules/culori/src/jch/definition.js
var definition12 = {
  mode: "jch",
  parse: ["--jzczhz"],
  serialize: "--jzczhz",
  toMode: {
    jab: convertJchToJab_default,
    rgb: (c2) => convertJabToRgb_default(convertJchToJab_default(c2))
  },
  fromMode: {
    rgb: (c2) => convertJabToJch_default(convertRgbToJab_default(c2)),
    jab: convertJabToJch_default
  },
  channels: ["j", "c", "h", "alpha"],
  ranges: {
    j: [0, 0.221],
    c: [0, 0.19],
    h: [0, 360]
  },
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    c: interpolatorLinear,
    j: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueChroma
  },
  average: {
    h: averageAngle
  }
};
var definition_default12 = definition12;

// node_modules/culori/src/xyz50/constants.js
var k3 = Math.pow(29, 3) / Math.pow(3, 3);
var e3 = Math.pow(6, 3) / Math.pow(29, 3);

// node_modules/culori/src/lab/convertLabToXyz50.js
var fn4 = (v) => Math.pow(v, 3) > e3 ? Math.pow(v, 3) : (116 * v - 16) / k3;
var convertLabToXyz50 = ({ l, a, b, alpha }) => {
  if (l === void 0) l = 0;
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let fy = (l + 16) / 116;
  let fx = a / 500 + fy;
  let fz = fy - b / 200;
  let res = {
    mode: "xyz50",
    x: fn4(fx) * D50.X,
    y: fn4(fy) * D50.Y,
    z: fn4(fz) * D50.Z
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertLabToXyz50_default = convertLabToXyz50;

// node_modules/culori/src/xyz50/convertXyz50ToRgb.js
var convertXyz50ToRgb = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = convertLrgbToRgb_default({
    r: x * 3.1341359569958707 - y * 1.6173863321612538 - 0.4906619460083532 * z,
    g: x * -0.978795502912089 + y * 1.916254567259524 + 0.03344273116131949 * z,
    b: x * 0.07195537988411677 - y * 0.2289768264158322 + 1.405386058324125 * z
  });
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertXyz50ToRgb_default = convertXyz50ToRgb;

// node_modules/culori/src/lab/convertLabToRgb.js
var convertLabToRgb = (lab2) => convertXyz50ToRgb_default(convertLabToXyz50_default(lab2));
var convertLabToRgb_default = convertLabToRgb;

// node_modules/culori/src/xyz50/convertRgbToXyz50.js
var convertRgbToXyz50 = (rgb5) => {
  let { r: r2, g, b, alpha } = convertRgbToLrgb_default(rgb5);
  let res = {
    mode: "xyz50",
    x: 0.436065742824811 * r2 + 0.3851514688337912 * g + 0.14307845442264197 * b,
    y: 0.22249319175623702 * r2 + 0.7168870538238823 * g + 0.06061979053616537 * b,
    z: 0.013923904500943465 * r2 + 0.09708128566574634 * g + 0.7140993584005155 * b
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertRgbToXyz50_default = convertRgbToXyz50;

// node_modules/culori/src/lab/convertXyz50ToLab.js
var f2 = (value) => value > e3 ? Math.cbrt(value) : (k3 * value + 16) / 116;
var convertXyz50ToLab = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let f0 = f2(x / D50.X);
  let f1 = f2(y / D50.Y);
  let f22 = f2(z / D50.Z);
  let res = {
    mode: "lab",
    l: 116 * f1 - 16,
    a: 500 * (f0 - f1),
    b: 200 * (f1 - f22)
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertXyz50ToLab_default = convertXyz50ToLab;

// node_modules/culori/src/lab/convertRgbToLab.js
var convertRgbToLab = (rgb5) => {
  let res = convertXyz50ToLab_default(convertRgbToXyz50_default(rgb5));
  if (rgb5.r === rgb5.b && rgb5.b === rgb5.g) {
    res.a = res.b = 0;
  }
  return res;
};
var convertRgbToLab_default = convertRgbToLab;

// node_modules/culori/src/lab/parseLab.js
function parseLab(color, parsed) {
  if (!parsed || parsed[0] !== "lab") {
    return void 0;
  }
  const res = { mode: "lab" };
  const [, l, a, b, alpha] = parsed;
  if (l.type === Tok.Hue || a.type === Tok.Hue || b.type === Tok.Hue) {
    return void 0;
  }
  if (l.type !== Tok.None) {
    res.l = Math.min(Math.max(0, l.value), 100);
  }
  if (a.type !== Tok.None) {
    res.a = a.type === Tok.Number ? a.value : a.value * 125 / 100;
  }
  if (b.type !== Tok.None) {
    res.b = b.type === Tok.Number ? b.value : b.value * 125 / 100;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
var parseLab_default = parseLab;

// node_modules/culori/src/lab/definition.js
var definition13 = {
  mode: "lab",
  toMode: {
    xyz50: convertLabToXyz50_default,
    rgb: convertLabToRgb_default
  },
  fromMode: {
    xyz50: convertXyz50ToLab_default,
    rgb: convertRgbToLab_default
  },
  channels: ["l", "a", "b", "alpha"],
  ranges: {
    l: [0, 100],
    a: [-100, 100],
    b: [-100, 100]
  },
  parse: [parseLab_default],
  serialize: (c2) => `lab(${c2.l !== void 0 ? c2.l : "none"} ${c2.a !== void 0 ? c2.a : "none"} ${c2.b !== void 0 ? c2.b : "none"}${c2.alpha < 1 ? ` / ${c2.alpha}` : ""})`,
  interpolate: {
    l: interpolatorLinear,
    a: interpolatorLinear,
    b: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
var definition_default13 = definition13;

// node_modules/culori/src/lab65/definition.js
var definition14 = {
  ...definition_default13,
  mode: "lab65",
  parse: ["--lab-d65"],
  serialize: "--lab-d65",
  toMode: {
    xyz65: convertLab65ToXyz65_default,
    rgb: convertLab65ToRgb_default
  },
  fromMode: {
    xyz65: convertXyz65ToLab65_default,
    rgb: convertRgbToLab65_default
  },
  ranges: {
    l: [0, 100],
    a: [-86.182, 98.234],
    b: [-107.86, 94.477]
  }
};
var definition_default14 = definition14;

// node_modules/culori/src/lch/parseLch.js
function parseLch(color, parsed) {
  if (!parsed || parsed[0] !== "lch") {
    return void 0;
  }
  const res = { mode: "lch" };
  const [, l, c2, h, alpha] = parsed;
  if (l.type !== Tok.None) {
    if (l.type === Tok.Hue) {
      return void 0;
    }
    res.l = Math.min(Math.max(0, l.value), 100);
  }
  if (c2.type !== Tok.None) {
    res.c = Math.max(
      0,
      c2.type === Tok.Number ? c2.value : c2.value * 150 / 100
    );
  }
  if (h.type !== Tok.None) {
    if (h.type === Tok.Percentage) {
      return void 0;
    }
    res.h = h.value;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
var parseLch_default = parseLch;

// node_modules/culori/src/lch/definition.js
var definition15 = {
  mode: "lch",
  toMode: {
    lab: convertLchToLab_default,
    rgb: (c2) => convertLabToRgb_default(convertLchToLab_default(c2))
  },
  fromMode: {
    rgb: (c2) => convertLabToLch_default(convertRgbToLab_default(c2)),
    lab: convertLabToLch_default
  },
  channels: ["l", "c", "h", "alpha"],
  ranges: {
    l: [0, 100],
    c: [0, 150],
    h: [0, 360]
  },
  parse: [parseLch_default],
  serialize: (c2) => `lch(${c2.l !== void 0 ? c2.l : "none"} ${c2.c !== void 0 ? c2.c : "none"} ${c2.h !== void 0 ? c2.h : "none"}${c2.alpha < 1 ? ` / ${c2.alpha}` : ""})`,
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    c: interpolatorLinear,
    l: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueChroma
  },
  average: {
    h: averageAngle
  }
};
var definition_default15 = definition15;

// node_modules/culori/src/lch65/definition.js
var definition16 = {
  ...definition_default15,
  mode: "lch65",
  parse: ["--lch-d65"],
  serialize: "--lch-d65",
  toMode: {
    lab65: (c2) => convertLchToLab_default(c2, "lab65"),
    rgb: (c2) => convertLab65ToRgb_default(convertLchToLab_default(c2, "lab65"))
  },
  fromMode: {
    rgb: (c2) => convertLabToLch_default(convertRgbToLab65_default(c2), "lch65"),
    lab65: (c2) => convertLabToLch_default(c2, "lch65")
  },
  ranges: {
    l: [0, 100],
    c: [0, 133.807],
    h: [0, 360]
  }
};
var definition_default16 = definition16;

// node_modules/culori/src/lchuv/convertLuvToLchuv.js
var convertLuvToLchuv = ({ l, u, v, alpha }) => {
  if (u === void 0) u = 0;
  if (v === void 0) v = 0;
  let c2 = Math.sqrt(u * u + v * v);
  let res = {
    mode: "lchuv",
    l,
    c: c2
  };
  if (c2) {
    res.h = normalizeHue_default(Math.atan2(v, u) * 180 / Math.PI);
  }
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertLuvToLchuv_default = convertLuvToLchuv;

// node_modules/culori/src/lchuv/convertLchuvToLuv.js
var convertLchuvToLuv = ({ l, c: c2, h, alpha }) => {
  if (h === void 0) h = 0;
  let res = {
    mode: "luv",
    l,
    u: c2 ? c2 * Math.cos(h / 180 * Math.PI) : 0,
    v: c2 ? c2 * Math.sin(h / 180 * Math.PI) : 0
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertLchuvToLuv_default = convertLchuvToLuv;

// node_modules/culori/src/luv/convertXyz50ToLuv.js
var u_fn = (x, y, z) => 4 * x / (x + 15 * y + 3 * z);
var v_fn = (x, y, z) => 9 * y / (x + 15 * y + 3 * z);
var un = u_fn(D50.X, D50.Y, D50.Z);
var vn = v_fn(D50.X, D50.Y, D50.Z);
var l_fn = (value) => value <= e3 ? k3 * value : 116 * Math.cbrt(value) - 16;
var convertXyz50ToLuv = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let l = l_fn(y / D50.Y);
  let u = u_fn(x, y, z);
  let v = v_fn(x, y, z);
  if (!isFinite(u) || !isFinite(v)) {
    l = u = v = 0;
  } else {
    u = 13 * l * (u - un);
    v = 13 * l * (v - vn);
  }
  let res = {
    mode: "luv",
    l,
    u,
    v
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertXyz50ToLuv_default = convertXyz50ToLuv;

// node_modules/culori/src/luv/convertLuvToXyz50.js
var u_fn2 = (x, y, z) => 4 * x / (x + 15 * y + 3 * z);
var v_fn2 = (x, y, z) => 9 * y / (x + 15 * y + 3 * z);
var un2 = u_fn2(D50.X, D50.Y, D50.Z);
var vn2 = v_fn2(D50.X, D50.Y, D50.Z);
var convertLuvToXyz50 = ({ l, u, v, alpha }) => {
  if (l === void 0) l = 0;
  if (l === 0) {
    return { mode: "xyz50", x: 0, y: 0, z: 0 };
  }
  if (u === void 0) u = 0;
  if (v === void 0) v = 0;
  let up = u / (13 * l) + un2;
  let vp = v / (13 * l) + vn2;
  let y = D50.Y * (l <= 8 ? l / k3 : Math.pow((l + 16) / 116, 3));
  let x = y * (9 * up) / (4 * vp);
  let z = y * (12 - 3 * up - 20 * vp) / (4 * vp);
  let res = { mode: "xyz50", x, y, z };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertLuvToXyz50_default = convertLuvToXyz50;

// node_modules/culori/src/lchuv/definition.js
var convertRgbToLchuv = (rgb5) => convertLuvToLchuv_default(convertXyz50ToLuv_default(convertRgbToXyz50_default(rgb5)));
var convertLchuvToRgb = (lchuv2) => convertXyz50ToRgb_default(convertLuvToXyz50_default(convertLchuvToLuv_default(lchuv2)));
var definition17 = {
  mode: "lchuv",
  toMode: {
    luv: convertLchuvToLuv_default,
    rgb: convertLchuvToRgb
  },
  fromMode: {
    rgb: convertRgbToLchuv,
    luv: convertLuvToLchuv_default
  },
  channels: ["l", "c", "h", "alpha"],
  parse: ["--lchuv"],
  serialize: "--lchuv",
  ranges: {
    l: [0, 100],
    c: [0, 176.956],
    h: [0, 360]
  },
  interpolate: {
    h: { use: interpolatorLinear, fixup: fixupHueShorter },
    c: interpolatorLinear,
    l: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  },
  difference: {
    h: differenceHueChroma
  },
  average: {
    h: averageAngle
  }
};
var definition_default17 = definition17;

// node_modules/culori/src/lrgb/definition.js
var definition18 = {
  ...definition_default,
  mode: "lrgb",
  toMode: {
    rgb: convertLrgbToRgb_default
  },
  fromMode: {
    rgb: convertRgbToLrgb_default
  },
  parse: ["srgb-linear"],
  serialize: "srgb-linear"
};
var definition_default18 = definition18;

// node_modules/culori/src/luv/definition.js
var definition19 = {
  mode: "luv",
  toMode: {
    xyz50: convertLuvToXyz50_default,
    rgb: (luv2) => convertXyz50ToRgb_default(convertLuvToXyz50_default(luv2))
  },
  fromMode: {
    xyz50: convertXyz50ToLuv_default,
    rgb: (rgb5) => convertXyz50ToLuv_default(convertRgbToXyz50_default(rgb5))
  },
  channels: ["l", "u", "v", "alpha"],
  parse: ["--luv"],
  serialize: "--luv",
  ranges: {
    l: [0, 100],
    u: [-84.936, 175.042],
    v: [-125.882, 87.243]
  },
  interpolate: {
    l: interpolatorLinear,
    u: interpolatorLinear,
    v: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
var definition_default19 = definition19;

// node_modules/culori/src/oklab/convertLrgbToOklab.js
var convertLrgbToOklab = ({ r: r2, g, b, alpha }) => {
  if (r2 === void 0) r2 = 0;
  if (g === void 0) g = 0;
  if (b === void 0) b = 0;
  let L = Math.cbrt(
    0.41222147079999993 * r2 + 0.5363325363 * g + 0.0514459929 * b
  );
  let M3 = Math.cbrt(
    0.2119034981999999 * r2 + 0.6806995450999999 * g + 0.1073969566 * b
  );
  let S = Math.cbrt(
    0.08830246189999998 * r2 + 0.2817188376 * g + 0.6299787005000002 * b
  );
  let res = {
    mode: "oklab",
    l: 0.2104542553 * L + 0.793617785 * M3 - 0.0040720468 * S,
    a: 1.9779984951 * L - 2.428592205 * M3 + 0.4505937099 * S,
    b: 0.0259040371 * L + 0.7827717662 * M3 - 0.808675766 * S
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertLrgbToOklab_default = convertLrgbToOklab;

// node_modules/culori/src/oklab/convertRgbToOklab.js
var convertRgbToOklab = (rgb5) => {
  let res = convertLrgbToOklab_default(convertRgbToLrgb_default(rgb5));
  if (rgb5.r === rgb5.b && rgb5.b === rgb5.g) {
    res.a = res.b = 0;
  }
  return res;
};
var convertRgbToOklab_default = convertRgbToOklab;

// node_modules/culori/src/oklab/convertOklabToLrgb.js
var convertOklabToLrgb = ({ l, a, b, alpha }) => {
  if (l === void 0) l = 0;
  if (a === void 0) a = 0;
  if (b === void 0) b = 0;
  let L = Math.pow(
    l * 0.9999999984505198 + 0.39633779217376786 * a + 0.2158037580607588 * b,
    3
  );
  let M3 = Math.pow(
    l * 1.0000000088817609 - 0.10556134232365635 * a - 0.06385417477170591 * b,
    3
  );
  let S = Math.pow(
    l * 1.0000000546724108 - 0.08948418209496575 * a - 1.2914855378640917 * b,
    3
  );
  let res = {
    mode: "lrgb",
    r: 4.076741661347994 * L - 3.307711590408193 * M3 + 0.230969928729428 * S,
    g: -1.2684380040921763 * L + 2.6097574006633715 * M3 - 0.3413193963102197 * S,
    b: -0.004196086541837188 * L - 0.7034186144594493 * M3 + 1.7076147009309444 * S
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertOklabToLrgb_default = convertOklabToLrgb;

// node_modules/culori/src/oklab/convertOklabToRgb.js
var convertOklabToRgb = (c2) => convertLrgbToRgb_default(convertOklabToLrgb_default(c2));
var convertOklabToRgb_default = convertOklabToRgb;

// node_modules/culori/src/okhsl/helpers.js
function toe(x) {
  const k_1 = 0.206;
  const k_2 = 0.03;
  const k_3 = (1 + k_1) / (1 + k_2);
  return 0.5 * (k_3 * x - k_1 + Math.sqrt((k_3 * x - k_1) * (k_3 * x - k_1) + 4 * k_2 * k_3 * x));
}
function toe_inv(x) {
  const k_1 = 0.206;
  const k_2 = 0.03;
  const k_3 = (1 + k_1) / (1 + k_2);
  return (x * x + k_1 * x) / (k_3 * (x + k_2));
}
function compute_max_saturation(a, b) {
  let k0, k1, k22, k32, k4, wl, wm, ws;
  if (-1.88170328 * a - 0.80936493 * b > 1) {
    k0 = 1.19086277;
    k1 = 1.76576728;
    k22 = 0.59662641;
    k32 = 0.75515197;
    k4 = 0.56771245;
    wl = 4.0767416621;
    wm = -3.3077115913;
    ws = 0.2309699292;
  } else if (1.81444104 * a - 1.19445276 * b > 1) {
    k0 = 0.73956515;
    k1 = -0.45954404;
    k22 = 0.08285427;
    k32 = 0.1254107;
    k4 = 0.14503204;
    wl = -1.2684380046;
    wm = 2.6097574011;
    ws = -0.3413193965;
  } else {
    k0 = 1.35733652;
    k1 = -915799e-8;
    k22 = -1.1513021;
    k32 = -0.50559606;
    k4 = 692167e-8;
    wl = -0.0041960863;
    wm = -0.7034186147;
    ws = 1.707614701;
  }
  let S = k0 + k1 * a + k22 * b + k32 * a * a + k4 * a * b;
  let k_l = 0.3963377774 * a + 0.2158037573 * b;
  let k_m = -0.1055613458 * a - 0.0638541728 * b;
  let k_s = -0.0894841775 * a - 1.291485548 * b;
  {
    let l_ = 1 + S * k_l;
    let m_ = 1 + S * k_m;
    let s_ = 1 + S * k_s;
    let l = l_ * l_ * l_;
    let m = m_ * m_ * m_;
    let s = s_ * s_ * s_;
    let l_dS = 3 * k_l * l_ * l_;
    let m_dS = 3 * k_m * m_ * m_;
    let s_dS = 3 * k_s * s_ * s_;
    let l_dS2 = 6 * k_l * k_l * l_;
    let m_dS2 = 6 * k_m * k_m * m_;
    let s_dS2 = 6 * k_s * k_s * s_;
    let f3 = wl * l + wm * m + ws * s;
    let f1 = wl * l_dS + wm * m_dS + ws * s_dS;
    let f22 = wl * l_dS2 + wm * m_dS2 + ws * s_dS2;
    S = S - f3 * f1 / (f1 * f1 - 0.5 * f3 * f22);
  }
  return S;
}
function find_cusp(a, b) {
  let S_cusp = compute_max_saturation(a, b);
  let rgb5 = convertOklabToLrgb_default({ l: 1, a: S_cusp * a, b: S_cusp * b });
  let L_cusp = Math.cbrt(1 / Math.max(rgb5.r, rgb5.g, rgb5.b));
  let C_cusp = L_cusp * S_cusp;
  return [L_cusp, C_cusp];
}
function find_gamut_intersection(a, b, L1, C12, L0, cusp = null) {
  if (!cusp) {
    cusp = find_cusp(a, b);
  }
  let t;
  if ((L1 - L0) * cusp[1] - (cusp[0] - L0) * C12 <= 0) {
    t = cusp[1] * L0 / (C12 * cusp[0] + cusp[1] * (L0 - L1));
  } else {
    t = cusp[1] * (L0 - 1) / (C12 * (cusp[0] - 1) + cusp[1] * (L0 - L1));
    {
      let dL = L1 - L0;
      let dC = C12;
      let k_l = 0.3963377774 * a + 0.2158037573 * b;
      let k_m = -0.1055613458 * a - 0.0638541728 * b;
      let k_s = -0.0894841775 * a - 1.291485548 * b;
      let l_dt = dL + dC * k_l;
      let m_dt = dL + dC * k_m;
      let s_dt = dL + dC * k_s;
      {
        let L = L0 * (1 - t) + t * L1;
        let C = t * C12;
        let l_ = L + C * k_l;
        let m_ = L + C * k_m;
        let s_ = L + C * k_s;
        let l = l_ * l_ * l_;
        let m = m_ * m_ * m_;
        let s = s_ * s_ * s_;
        let ldt = 3 * l_dt * l_ * l_;
        let mdt = 3 * m_dt * m_ * m_;
        let sdt = 3 * s_dt * s_ * s_;
        let ldt2 = 6 * l_dt * l_dt * l_;
        let mdt2 = 6 * m_dt * m_dt * m_;
        let sdt2 = 6 * s_dt * s_dt * s_;
        let r2 = 4.0767416621 * l - 3.3077115913 * m + 0.2309699292 * s - 1;
        let r1 = 4.0767416621 * ldt - 3.3077115913 * mdt + 0.2309699292 * sdt;
        let r22 = 4.0767416621 * ldt2 - 3.3077115913 * mdt2 + 0.2309699292 * sdt2;
        let u_r = r1 / (r1 * r1 - 0.5 * r2 * r22);
        let t_r = -r2 * u_r;
        let g = -1.2684380046 * l + 2.6097574011 * m - 0.3413193965 * s - 1;
        let g1 = -1.2684380046 * ldt + 2.6097574011 * mdt - 0.3413193965 * sdt;
        let g2 = -1.2684380046 * ldt2 + 2.6097574011 * mdt2 - 0.3413193965 * sdt2;
        let u_g = g1 / (g1 * g1 - 0.5 * g * g2);
        let t_g = -g * u_g;
        let b2 = -0.0041960863 * l - 0.7034186147 * m + 1.707614701 * s - 1;
        let b1 = -0.0041960863 * ldt - 0.7034186147 * mdt + 1.707614701 * sdt;
        let b22 = -0.0041960863 * ldt2 - 0.7034186147 * mdt2 + 1.707614701 * sdt2;
        let u_b = b1 / (b1 * b1 - 0.5 * b2 * b22);
        let t_b = -b2 * u_b;
        t_r = u_r >= 0 ? t_r : 1e6;
        t_g = u_g >= 0 ? t_g : 1e6;
        t_b = u_b >= 0 ? t_b : 1e6;
        t += Math.min(t_r, Math.min(t_g, t_b));
      }
    }
  }
  return t;
}
function get_ST_max(a_, b_, cusp = null) {
  if (!cusp) {
    cusp = find_cusp(a_, b_);
  }
  let L = cusp[0];
  let C = cusp[1];
  return [C / L, C / (1 - L)];
}
function get_Cs(L, a_, b_) {
  let cusp = find_cusp(a_, b_);
  let C_max = find_gamut_intersection(a_, b_, L, 1, L, cusp);
  let ST_max = get_ST_max(a_, b_, cusp);
  let S_mid = 0.11516993 + 1 / (7.4477897 + 4.1590124 * b_ + a_ * (-2.19557347 + 1.75198401 * b_ + a_ * (-2.13704948 - 10.02301043 * b_ + a_ * (-4.24894561 + 5.38770819 * b_ + 4.69891013 * a_))));
  let T_mid = 0.11239642 + 1 / (1.6132032 - 0.68124379 * b_ + a_ * (0.40370612 + 0.90148123 * b_ + a_ * (-0.27087943 + 0.6122399 * b_ + a_ * (299215e-8 - 0.45399568 * b_ - 0.14661872 * a_))));
  let k4 = C_max / Math.min(L * ST_max[0], (1 - L) * ST_max[1]);
  let C_a = L * S_mid;
  let C_b = (1 - L) * T_mid;
  let C_mid = 0.9 * k4 * Math.sqrt(
    Math.sqrt(
      1 / (1 / (C_a * C_a * C_a * C_a) + 1 / (C_b * C_b * C_b * C_b))
    )
  );
  C_a = L * 0.4;
  C_b = (1 - L) * 0.8;
  let C_0 = Math.sqrt(1 / (1 / (C_a * C_a) + 1 / (C_b * C_b)));
  return [C_0, C_mid, C_max];
}

// node_modules/culori/src/okhsl/convertOklabToOkhsl.js
function convertOklabToOkhsl(lab2) {
  const l = lab2.l !== void 0 ? lab2.l : 0;
  const a = lab2.a !== void 0 ? lab2.a : 0;
  const b = lab2.b !== void 0 ? lab2.b : 0;
  const ret = { mode: "okhsl", l: toe(l) };
  if (lab2.alpha !== void 0) {
    ret.alpha = lab2.alpha;
  }
  let c2 = Math.sqrt(a * a + b * b);
  if (!c2) {
    ret.s = 0;
    return ret;
  }
  let [C_0, C_mid, C_max] = get_Cs(l, a / c2, b / c2);
  let s;
  if (c2 < C_mid) {
    let k_0 = 0;
    let k_1 = 0.8 * C_0;
    let k_2 = 1 - k_1 / C_mid;
    let t = (c2 - k_0) / (k_1 + k_2 * (c2 - k_0));
    s = t * 0.8;
  } else {
    let k_0 = C_mid;
    let k_1 = 0.2 * C_mid * C_mid * 1.25 * 1.25 / C_0;
    let k_2 = 1 - k_1 / (C_max - C_mid);
    let t = (c2 - k_0) / (k_1 + k_2 * (c2 - k_0));
    s = 0.8 + 0.2 * t;
  }
  if (s) {
    ret.s = s;
    ret.h = normalizeHue_default(Math.atan2(b, a) * 180 / Math.PI);
  }
  return ret;
}

// node_modules/culori/src/okhsl/convertOkhslToOklab.js
function convertOkhslToOklab(hsl3) {
  let h = hsl3.h !== void 0 ? hsl3.h : 0;
  let s = hsl3.s !== void 0 ? hsl3.s : 0;
  let l = hsl3.l !== void 0 ? hsl3.l : 0;
  const ret = { mode: "oklab", l: toe_inv(l) };
  if (hsl3.alpha !== void 0) {
    ret.alpha = hsl3.alpha;
  }
  if (!s || l === 1) {
    ret.a = ret.b = 0;
    return ret;
  }
  let a_ = Math.cos(h / 180 * Math.PI);
  let b_ = Math.sin(h / 180 * Math.PI);
  let [C_0, C_mid, C_max] = get_Cs(ret.l, a_, b_);
  let t, k_0, k_1, k_2;
  if (s < 0.8) {
    t = 1.25 * s;
    k_0 = 0;
    k_1 = 0.8 * C_0;
    k_2 = 1 - k_1 / C_mid;
  } else {
    t = 5 * (s - 0.8);
    k_0 = C_mid;
    k_1 = 0.2 * C_mid * C_mid * 1.25 * 1.25 / C_0;
    k_2 = 1 - k_1 / (C_max - C_mid);
  }
  let C = k_0 + t * k_1 / (1 - k_2 * t);
  ret.a = C * a_;
  ret.b = C * b_;
  return ret;
}

// node_modules/culori/src/okhsl/modeOkhsl.js
var modeOkhsl = {
  ...definition_default7,
  mode: "okhsl",
  channels: ["h", "s", "l", "alpha"],
  parse: ["--okhsl"],
  serialize: "--okhsl",
  fromMode: {
    oklab: convertOklabToOkhsl,
    rgb: (c2) => convertOklabToOkhsl(convertRgbToOklab_default(c2))
  },
  toMode: {
    oklab: convertOkhslToOklab,
    rgb: (c2) => convertOklabToRgb_default(convertOkhslToOklab(c2))
  }
};
var modeOkhsl_default = modeOkhsl;

// node_modules/culori/src/okhsv/convertOklabToOkhsv.js
function convertOklabToOkhsv(lab2) {
  let l = lab2.l !== void 0 ? lab2.l : 0;
  let a = lab2.a !== void 0 ? lab2.a : 0;
  let b = lab2.b !== void 0 ? lab2.b : 0;
  let c2 = Math.sqrt(a * a + b * b);
  let a_ = c2 ? a / c2 : 1;
  let b_ = c2 ? b / c2 : 1;
  let [S_max, T] = get_ST_max(a_, b_);
  let S_0 = 0.5;
  let k4 = 1 - S_0 / S_max;
  let t = T / (c2 + l * T);
  let L_v = t * l;
  let C_v = t * c2;
  let L_vt = toe_inv(L_v);
  let C_vt = C_v * L_vt / L_v;
  let rgb_scale = convertOklabToLrgb_default({ l: L_vt, a: a_ * C_vt, b: b_ * C_vt });
  let scale_L = Math.cbrt(
    1 / Math.max(rgb_scale.r, rgb_scale.g, rgb_scale.b, 0)
  );
  l = l / scale_L;
  c2 = c2 / scale_L * toe(l) / l;
  l = toe(l);
  const ret = {
    mode: "okhsv",
    s: c2 ? (S_0 + T) * C_v / (T * S_0 + T * k4 * C_v) : 0,
    v: l ? l / L_v : 0
  };
  if (ret.s) {
    ret.h = normalizeHue_default(Math.atan2(b, a) * 180 / Math.PI);
  }
  if (lab2.alpha !== void 0) {
    ret.alpha = lab2.alpha;
  }
  return ret;
}

// node_modules/culori/src/okhsv/convertOkhsvToOklab.js
function convertOkhsvToOklab(hsv2) {
  const ret = { mode: "oklab" };
  if (hsv2.alpha !== void 0) {
    ret.alpha = hsv2.alpha;
  }
  const h = hsv2.h !== void 0 ? hsv2.h : 0;
  const s = hsv2.s !== void 0 ? hsv2.s : 0;
  const v = hsv2.v !== void 0 ? hsv2.v : 0;
  const a_ = Math.cos(h / 180 * Math.PI);
  const b_ = Math.sin(h / 180 * Math.PI);
  const [S_max, T] = get_ST_max(a_, b_);
  const S_0 = 0.5;
  const k4 = 1 - S_0 / S_max;
  const L_v = 1 - s * S_0 / (S_0 + T - T * k4 * s);
  const C_v = s * T * S_0 / (S_0 + T - T * k4 * s);
  const L_vt = toe_inv(L_v);
  const C_vt = C_v * L_vt / L_v;
  const rgb_scale = convertOklabToLrgb_default({
    l: L_vt,
    a: a_ * C_vt,
    b: b_ * C_vt
  });
  const scale_L = Math.cbrt(
    1 / Math.max(rgb_scale.r, rgb_scale.g, rgb_scale.b, 0)
  );
  const L_new = toe_inv(v * L_v);
  const C = C_v * L_new / L_v;
  ret.l = L_new * scale_L;
  ret.a = C * a_ * scale_L;
  ret.b = C * b_ * scale_L;
  return ret;
}

// node_modules/culori/src/okhsv/modeOkhsv.js
var modeOkhsv = {
  ...definition_default8,
  mode: "okhsv",
  channels: ["h", "s", "v", "alpha"],
  parse: ["--okhsv"],
  serialize: "--okhsv",
  fromMode: {
    oklab: convertOklabToOkhsv,
    rgb: (c2) => convertOklabToOkhsv(convertRgbToOklab_default(c2))
  },
  toMode: {
    oklab: convertOkhsvToOklab,
    rgb: (c2) => convertOklabToRgb_default(convertOkhsvToOklab(c2))
  }
};
var modeOkhsv_default = modeOkhsv;

// node_modules/culori/src/oklab/parseOklab.js
function parseOklab(color, parsed) {
  if (!parsed || parsed[0] !== "oklab") {
    return void 0;
  }
  const res = { mode: "oklab" };
  const [, l, a, b, alpha] = parsed;
  if (l.type === Tok.Hue || a.type === Tok.Hue || b.type === Tok.Hue) {
    return void 0;
  }
  if (l.type !== Tok.None) {
    res.l = Math.min(
      Math.max(0, l.type === Tok.Number ? l.value : l.value / 100),
      1
    );
  }
  if (a.type !== Tok.None) {
    res.a = a.type === Tok.Number ? a.value : a.value * 0.4 / 100;
  }
  if (b.type !== Tok.None) {
    res.b = b.type === Tok.Number ? b.value : b.value * 0.4 / 100;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
var parseOklab_default = parseOklab;

// node_modules/culori/src/oklab/definition.js
var definition20 = {
  ...definition_default13,
  mode: "oklab",
  toMode: {
    lrgb: convertOklabToLrgb_default,
    rgb: convertOklabToRgb_default
  },
  fromMode: {
    lrgb: convertLrgbToOklab_default,
    rgb: convertRgbToOklab_default
  },
  ranges: {
    l: [0, 1],
    a: [-0.4, 0.4],
    b: [-0.4, 0.4]
  },
  parse: [parseOklab_default],
  serialize: (c2) => `oklab(${c2.l !== void 0 ? c2.l : "none"} ${c2.a !== void 0 ? c2.a : "none"} ${c2.b !== void 0 ? c2.b : "none"}${c2.alpha < 1 ? ` / ${c2.alpha}` : ""})`
};
var definition_default20 = definition20;

// node_modules/culori/src/oklch/parseOklch.js
function parseOklch(color, parsed) {
  if (!parsed || parsed[0] !== "oklch") {
    return void 0;
  }
  const res = { mode: "oklch" };
  const [, l, c2, h, alpha] = parsed;
  if (l.type !== Tok.None) {
    if (l.type === Tok.Hue) {
      return void 0;
    }
    res.l = Math.min(
      Math.max(0, l.type === Tok.Number ? l.value : l.value / 100),
      1
    );
  }
  if (c2.type !== Tok.None) {
    res.c = Math.max(
      0,
      c2.type === Tok.Number ? c2.value : c2.value * 0.4 / 100
    );
  }
  if (h.type !== Tok.None) {
    if (h.type === Tok.Percentage) {
      return void 0;
    }
    res.h = h.value;
  }
  if (alpha.type !== Tok.None) {
    res.alpha = Math.min(
      1,
      Math.max(
        0,
        alpha.type === Tok.Number ? alpha.value : alpha.value / 100
      )
    );
  }
  return res;
}
var parseOklch_default = parseOklch;

// node_modules/culori/src/oklch/definition.js
var definition21 = {
  ...definition_default15,
  mode: "oklch",
  toMode: {
    oklab: (c2) => convertLchToLab_default(c2, "oklab"),
    rgb: (c2) => convertOklabToRgb_default(convertLchToLab_default(c2, "oklab"))
  },
  fromMode: {
    rgb: (c2) => convertLabToLch_default(convertRgbToOklab_default(c2), "oklch"),
    oklab: (c2) => convertLabToLch_default(c2, "oklch")
  },
  parse: [parseOklch_default],
  serialize: (c2) => `oklch(${c2.l !== void 0 ? c2.l : "none"} ${c2.c !== void 0 ? c2.c : "none"} ${c2.h !== void 0 ? c2.h : "none"}${c2.alpha < 1 ? ` / ${c2.alpha}` : ""})`,
  ranges: {
    l: [0, 1],
    c: [0, 0.4],
    h: [0, 360]
  }
};
var definition_default21 = definition21;

// node_modules/culori/src/p3/convertP3ToXyz65.js
var convertP3ToXyz65 = (rgb5) => {
  let { r: r2, g, b, alpha } = convertRgbToLrgb_default(rgb5);
  let res = {
    mode: "xyz65",
    x: 0.486570948648216 * r2 + 0.265667693169093 * g + 0.1982172852343625 * b,
    y: 0.2289745640697487 * r2 + 0.6917385218365062 * g + 0.079286914093745 * b,
    z: 0 * r2 + 0.0451133818589026 * g + 1.043944368900976 * b
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertP3ToXyz65_default = convertP3ToXyz65;

// node_modules/culori/src/p3/convertXyz65ToP3.js
var convertXyz65ToP3 = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = convertLrgbToRgb_default(
    {
      r: x * 2.4934969119414263 - y * 0.9313836179191242 - 0.402710784450717 * z,
      g: x * -0.8294889695615749 + y * 1.7626640603183465 + 0.0236246858419436 * z,
      b: x * 0.0358458302437845 - y * 0.0761723892680418 + 0.9568845240076871 * z
    },
    "p3"
  );
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertXyz65ToP3_default = convertXyz65ToP3;

// node_modules/culori/src/p3/definition.js
var definition22 = {
  ...definition_default,
  mode: "p3",
  parse: ["display-p3"],
  serialize: "display-p3",
  fromMode: {
    rgb: (color) => convertXyz65ToP3_default(convertRgbToXyz65_default(color)),
    xyz65: convertXyz65ToP3_default
  },
  toMode: {
    rgb: (color) => convertXyz65ToRgb_default(convertP3ToXyz65_default(color)),
    xyz65: convertP3ToXyz65_default
  }
};
var definition_default22 = definition22;

// node_modules/culori/src/prophoto/convertXyz50ToProphoto.js
var gamma2 = (v) => {
  let abs2 = Math.abs(v);
  if (abs2 >= 1 / 512) {
    return Math.sign(v) * Math.pow(abs2, 1 / 1.8);
  }
  return 16 * v;
};
var convertXyz50ToProphoto = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = {
    mode: "prophoto",
    r: gamma2(
      x * 1.3457868816471585 - y * 0.2555720873797946 - 0.0511018649755453 * z
    ),
    g: gamma2(
      x * -0.5446307051249019 + y * 1.5082477428451466 + 0.0205274474364214 * z
    ),
    b: gamma2(x * 0 + y * 0 + 1.2119675456389452 * z)
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertXyz50ToProphoto_default = convertXyz50ToProphoto;

// node_modules/culori/src/prophoto/convertProphotoToXyz50.js
var linearize2 = (v = 0) => {
  let abs2 = Math.abs(v);
  if (abs2 >= 16 / 512) {
    return Math.sign(v) * Math.pow(abs2, 1.8);
  }
  return v / 16;
};
var convertProphotoToXyz50 = (prophoto2) => {
  let r2 = linearize2(prophoto2.r);
  let g = linearize2(prophoto2.g);
  let b = linearize2(prophoto2.b);
  let res = {
    mode: "xyz50",
    x: 0.7977666449006423 * r2 + 0.1351812974005331 * g + 0.0313477341283922 * b,
    y: 0.2880748288194013 * r2 + 0.7118352342418731 * g + 899369387256e-16 * b,
    z: 0 * r2 + 0 * g + 0.8251046025104602 * b
  };
  if (prophoto2.alpha !== void 0) {
    res.alpha = prophoto2.alpha;
  }
  return res;
};
var convertProphotoToXyz50_default = convertProphotoToXyz50;

// node_modules/culori/src/prophoto/definition.js
var definition23 = {
  ...definition_default,
  mode: "prophoto",
  parse: ["prophoto-rgb"],
  serialize: "prophoto-rgb",
  fromMode: {
    xyz50: convertXyz50ToProphoto_default,
    rgb: (color) => convertXyz50ToProphoto_default(convertRgbToXyz50_default(color))
  },
  toMode: {
    xyz50: convertProphotoToXyz50_default,
    rgb: (color) => convertXyz50ToRgb_default(convertProphotoToXyz50_default(color))
  }
};
var definition_default23 = definition23;

// node_modules/culori/src/rec2020/convertXyz65ToRec2020.js
var α = 1.09929682680944;
var β = 0.018053968510807;
var gamma3 = (v) => {
  const abs2 = Math.abs(v);
  if (abs2 > β) {
    return (Math.sign(v) || 1) * (α * Math.pow(abs2, 0.45) - (α - 1));
  }
  return 4.5 * v;
};
var convertXyz65ToRec2020 = ({ x, y, z, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = {
    mode: "rec2020",
    r: gamma3(
      x * 1.7166511879712683 - y * 0.3556707837763925 - 0.2533662813736599 * z
    ),
    g: gamma3(
      x * -0.6666843518324893 + y * 1.6164812366349395 + 0.0157685458139111 * z
    ),
    b: gamma3(
      x * 0.0176398574453108 - y * 0.0427706132578085 + 0.9421031212354739 * z
    )
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertXyz65ToRec2020_default = convertXyz65ToRec2020;

// node_modules/culori/src/rec2020/convertRec2020ToXyz65.js
var α2 = 1.09929682680944;
var β2 = 0.018053968510807;
var linearize3 = (v = 0) => {
  let abs2 = Math.abs(v);
  if (abs2 < β2 * 4.5) {
    return v / 4.5;
  }
  return (Math.sign(v) || 1) * Math.pow((abs2 + α2 - 1) / α2, 1 / 0.45);
};
var convertRec2020ToXyz65 = (rec20202) => {
  let r2 = linearize3(rec20202.r);
  let g = linearize3(rec20202.g);
  let b = linearize3(rec20202.b);
  let res = {
    mode: "xyz65",
    x: 0.6369580483012911 * r2 + 0.1446169035862083 * g + 0.1688809751641721 * b,
    y: 0.262700212011267 * r2 + 0.6779980715188708 * g + 0.059301716469862 * b,
    z: 0 * r2 + 0.0280726930490874 * g + 1.0609850577107909 * b
  };
  if (rec20202.alpha !== void 0) {
    res.alpha = rec20202.alpha;
  }
  return res;
};
var convertRec2020ToXyz65_default = convertRec2020ToXyz65;

// node_modules/culori/src/rec2020/definition.js
var definition24 = {
  ...definition_default,
  mode: "rec2020",
  fromMode: {
    xyz65: convertXyz65ToRec2020_default,
    rgb: (color) => convertXyz65ToRec2020_default(convertRgbToXyz65_default(color))
  },
  toMode: {
    xyz65: convertRec2020ToXyz65_default,
    rgb: (color) => convertXyz65ToRgb_default(convertRec2020ToXyz65_default(color))
  },
  parse: ["rec2020"],
  serialize: "rec2020"
};
var definition_default24 = definition24;

// node_modules/culori/src/xyb/constants.js
var bias = 0.0037930732552754493;
var bias_cbrt = Math.cbrt(bias);

// node_modules/culori/src/xyb/convertRgbToXyb.js
var transfer = (v) => Math.cbrt(v) - bias_cbrt;
var convertRgbToXyb = (color) => {
  const { r: r2, g, b, alpha } = convertRgbToLrgb_default(color);
  const l = transfer(0.3 * r2 + 0.622 * g + 0.078 * b + bias);
  const m = transfer(0.23 * r2 + 0.692 * g + 0.078 * b + bias);
  const s = transfer(
    0.2434226892454782 * r2 + 0.2047674442449682 * g + 0.5518098665095535 * b + bias
  );
  const res = {
    mode: "xyb",
    x: (l - m) / 2,
    y: (l + m) / 2,
    /* Apply default chroma from luma (subtract Y from B) */
    b: s - (l + m) / 2
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
var convertRgbToXyb_default = convertRgbToXyb;

// node_modules/culori/src/xyb/convertXybToRgb.js
var transfer2 = (v) => Math.pow(v + bias_cbrt, 3);
var convertXybToRgb = ({ x, y, b, alpha }) => {
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (b === void 0) b = 0;
  const l = transfer2(x + y) - bias;
  const m = transfer2(y - x) - bias;
  const s = transfer2(b + y) - bias;
  const res = convertLrgbToRgb_default({
    r: 11.031566904639861 * l - 9.866943908131562 * m - 0.16462299650829934 * s,
    g: -3.2541473810744237 * l + 4.418770377582723 * m - 0.16462299650829934 * s,
    b: -3.6588512867136815 * l + 2.7129230459360922 * m + 1.9459282407775895 * s
  });
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
var convertXybToRgb_default = convertXybToRgb;

// node_modules/culori/src/xyb/definition.js
var definition25 = {
  mode: "xyb",
  channels: ["x", "y", "b", "alpha"],
  parse: ["--xyb"],
  serialize: "--xyb",
  toMode: {
    rgb: convertXybToRgb_default
  },
  fromMode: {
    rgb: convertRgbToXyb_default
  },
  ranges: {
    x: [-0.0154, 0.0281],
    y: [0, 0.8453],
    b: [-0.2778, 0.388]
  },
  interpolate: {
    x: interpolatorLinear,
    y: interpolatorLinear,
    b: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
var definition_default25 = definition25;

// node_modules/culori/src/xyz50/definition.js
var definition26 = {
  mode: "xyz50",
  parse: ["xyz-d50"],
  serialize: "xyz-d50",
  toMode: {
    rgb: convertXyz50ToRgb_default,
    lab: convertXyz50ToLab_default
  },
  fromMode: {
    rgb: convertRgbToXyz50_default,
    lab: convertLabToXyz50_default
  },
  channels: ["x", "y", "z", "alpha"],
  ranges: {
    x: [0, 0.964],
    y: [0, 0.999],
    z: [0, 0.825]
  },
  interpolate: {
    x: interpolatorLinear,
    y: interpolatorLinear,
    z: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
var definition_default26 = definition26;

// node_modules/culori/src/xyz65/convertXyz65ToXyz50.js
var convertXyz65ToXyz50 = (xyz652) => {
  let { x, y, z, alpha } = xyz652;
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = {
    mode: "xyz50",
    x: 1.0479298208405488 * x + 0.0229467933410191 * y - 0.0501922295431356 * z,
    y: 0.0296278156881593 * x + 0.990434484573249 * y - 0.0170738250293851 * z,
    z: -0.0092430581525912 * x + 0.0150551448965779 * y + 0.7518742899580008 * z
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertXyz65ToXyz50_default = convertXyz65ToXyz50;

// node_modules/culori/src/xyz65/convertXyz50ToXyz65.js
var convertXyz50ToXyz65 = (xyz502) => {
  let { x, y, z, alpha } = xyz502;
  if (x === void 0) x = 0;
  if (y === void 0) y = 0;
  if (z === void 0) z = 0;
  let res = {
    mode: "xyz65",
    x: 0.9554734527042182 * x - 0.0230985368742614 * y + 0.0632593086610217 * z,
    y: -0.0283697069632081 * x + 1.0099954580058226 * y + 0.021041398966943 * z,
    z: 0.0123140016883199 * x - 0.0205076964334779 * y + 1.3303659366080753 * z
  };
  if (alpha !== void 0) {
    res.alpha = alpha;
  }
  return res;
};
var convertXyz50ToXyz65_default = convertXyz50ToXyz65;

// node_modules/culori/src/xyz65/definition.js
var definition27 = {
  mode: "xyz65",
  toMode: {
    rgb: convertXyz65ToRgb_default,
    xyz50: convertXyz65ToXyz50_default
  },
  fromMode: {
    rgb: convertRgbToXyz65_default,
    xyz50: convertXyz50ToXyz65_default
  },
  ranges: {
    x: [0, 0.95],
    y: [0, 1],
    z: [0, 1.088]
  },
  channels: ["x", "y", "z", "alpha"],
  parse: ["xyz", "xyz-d65"],
  serialize: "xyz-d65",
  interpolate: {
    x: interpolatorLinear,
    y: interpolatorLinear,
    z: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
var definition_default27 = definition27;

// node_modules/culori/src/yiq/convertRgbToYiq.js
var convertRgbToYiq = ({ r: r2, g, b, alpha }) => {
  if (r2 === void 0) r2 = 0;
  if (g === void 0) g = 0;
  if (b === void 0) b = 0;
  const res = {
    mode: "yiq",
    y: 0.29889531 * r2 + 0.58662247 * g + 0.11448223 * b,
    i: 0.59597799 * r2 - 0.2741761 * g - 0.32180189 * b,
    q: 0.21147017 * r2 - 0.52261711 * g + 0.31114694 * b
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
var convertRgbToYiq_default = convertRgbToYiq;

// node_modules/culori/src/yiq/convertYiqToRgb.js
var convertYiqToRgb = ({ y, i, q, alpha }) => {
  if (y === void 0) y = 0;
  if (i === void 0) i = 0;
  if (q === void 0) q = 0;
  const res = {
    mode: "rgb",
    r: y + 0.95608445 * i + 0.6208885 * q,
    g: y - 0.27137664 * i - 0.6486059 * q,
    b: y - 1.10561724 * i + 1.70250126 * q
  };
  if (alpha !== void 0) res.alpha = alpha;
  return res;
};
var convertYiqToRgb_default = convertYiqToRgb;

// node_modules/culori/src/yiq/definition.js
var definition28 = {
  mode: "yiq",
  toMode: {
    rgb: convertYiqToRgb_default
  },
  fromMode: {
    rgb: convertRgbToYiq_default
  },
  channels: ["y", "i", "q", "alpha"],
  parse: ["--yiq"],
  serialize: "--yiq",
  ranges: {
    i: [-0.595, 0.595],
    q: [-0.522, 0.522]
  },
  interpolate: {
    y: interpolatorLinear,
    i: interpolatorLinear,
    q: interpolatorLinear,
    alpha: { use: interpolatorLinear, fixup: fixupAlpha }
  }
};
var definition_default28 = definition28;

// node_modules/culori/src/round.js
var r = (value, precision) => Math.round(value * (precision = Math.pow(10, precision))) / precision;
var round = (precision = 4) => (value) => typeof value === "number" ? r(value, precision) : value;
var round_default = round;

// node_modules/culori/src/formatter.js
var twoDecimals = round_default(2);
var rgb = converter_default("rgb");
var hsl = converter_default("hsl");
var formatCss = (c2) => {
  const color = prepare_default(c2);
  if (!color) {
    return void 0;
  }
  const def = getMode(color.mode);
  if (!def.serialize || typeof def.serialize === "string") {
    let res = `color(${def.serialize || `--${color.mode}`} `;
    def.channels.forEach((ch, i) => {
      if (ch !== "alpha") {
        res += (i ? " " : "") + (color[ch] !== void 0 ? color[ch] : "none");
      }
    });
    if (color.alpha !== void 0 && color.alpha < 1) {
      res += ` / ${color.alpha}`;
    }
    return res + ")";
  }
  if (typeof def.serialize === "function") {
    return def.serialize(color);
  }
  return void 0;
};

// node_modules/culori/src/map.js
var mapper = (fn5, mode = "rgb", preserve_mode = false) => {
  let channels = mode ? getMode(mode).channels : null;
  let conv = mode ? converter_default(mode) : prepare_default;
  return (color) => {
    let conv_color = conv(color);
    if (!conv_color) {
      return void 0;
    }
    let res = (channels || getMode(conv_color.mode).channels).reduce(
      (res2, ch) => {
        let v = fn5(conv_color[ch], ch, conv_color, mode);
        if (v !== void 0 && !isNaN(v)) {
          res2[ch] = v;
        }
        return res2;
      },
      { mode: conv_color.mode }
    );
    if (!preserve_mode) {
      return res;
    }
    let prep = prepare_default(color);
    if (prep && prep.mode !== res.mode) {
      return converter_default(prep.mode)(res);
    }
    return res;
  };
};
var mapAlphaMultiply = (v, ch, c2) => {
  if (ch !== "alpha") {
    return (v || 0) * (c2.alpha !== void 0 ? c2.alpha : 1);
  }
  return v;
};
var mapAlphaDivide = (v, ch, c2) => {
  if (ch !== "alpha" && c2.alpha !== 0) {
    return (v || 0) / (c2.alpha !== void 0 ? c2.alpha : 1);
  }
  return v;
};

// node_modules/culori/src/util/normalizePositions.js
var normalizePositions = (arr) => {
  if (arr[0] === void 0) {
    arr[0] = 0;
  }
  if (arr[arr.length - 1] === void 0) {
    arr[arr.length - 1] = 1;
  }
  let i = 1;
  let j;
  let from_idx;
  let from_pos;
  let inc;
  while (i < arr.length) {
    if (arr[i] === void 0) {
      from_idx = i;
      from_pos = arr[i - 1];
      j = i;
      while (arr[j] === void 0) j++;
      inc = (arr[j] - from_pos) / (j - i + 1);
      while (i < j) {
        arr[i] = from_pos + (i + 1 - from_idx) * inc;
        i++;
      }
    } else if (arr[i] < arr[i - 1]) {
      arr[i] = arr[i - 1];
    }
    i++;
  }
  return arr;
};
var normalizePositions_default = normalizePositions;

// node_modules/culori/src/easing/midpoint.js
var midpoint = (H = 0.5) => (t) => H <= 0 ? 1 : H >= 1 ? 0 : Math.pow(t, Math.log(0.5) / Math.log(H));
var midpoint_default = midpoint;

// node_modules/culori/src/interpolate/interpolate.js
var isfn = (o) => typeof o === "function";
var isobj = (o) => o && typeof o === "object";
var isnum = (o) => typeof o === "number";
var interpolate_fn = (colors2, mode = "rgb", overrides, premap) => {
  let def = getMode(mode);
  let conv = converter_default(mode);
  let conv_colors = [];
  let positions = [];
  let fns = {};
  colors2.forEach((val) => {
    if (Array.isArray(val)) {
      conv_colors.push(conv(val[0]));
      positions.push(val[1]);
    } else if (isnum(val) || isfn(val)) {
      fns[positions.length] = val;
    } else {
      conv_colors.push(conv(val));
      positions.push(void 0);
    }
  });
  normalizePositions_default(positions);
  let fixed = def.channels.reduce((res, ch) => {
    let ffn;
    if (isobj(overrides) && isobj(overrides[ch]) && overrides[ch].fixup) {
      ffn = overrides[ch].fixup;
    } else if (isobj(def.interpolate[ch]) && def.interpolate[ch].fixup) {
      ffn = def.interpolate[ch].fixup;
    } else {
      ffn = (v) => v;
    }
    res[ch] = ffn(conv_colors.map((color) => color[ch]));
    return res;
  }, {});
  if (premap) {
    let ccolors = conv_colors.map((color, idx) => {
      return def.channels.reduce(
        (c2, ch) => {
          c2[ch] = fixed[ch][idx];
          return c2;
        },
        { mode }
      );
    });
    fixed = def.channels.reduce((res, ch) => {
      res[ch] = ccolors.map((c2) => {
        let v = premap(c2[ch], ch, c2, mode);
        return isNaN(v) ? void 0 : v;
      });
      return res;
    }, {});
  }
  let interpolators = def.channels.reduce((res, ch) => {
    let ifn;
    if (isfn(overrides)) {
      ifn = overrides;
    } else if (isobj(overrides) && isfn(overrides[ch])) {
      ifn = overrides[ch];
    } else if (isobj(overrides) && isobj(overrides[ch]) && overrides[ch].use) {
      ifn = overrides[ch].use;
    } else if (isfn(def.interpolate[ch])) {
      ifn = def.interpolate[ch];
    } else if (isobj(def.interpolate[ch])) {
      ifn = def.interpolate[ch].use;
    }
    res[ch] = ifn(fixed[ch]);
    return res;
  }, {});
  let n = conv_colors.length - 1;
  return (t) => {
    t = Math.min(Math.max(0, t), 1);
    if (t <= positions[0]) {
      return conv_colors[0];
    }
    if (t > positions[n]) {
      return conv_colors[n];
    }
    let idx = 0;
    while (positions[idx] < t) idx++;
    let start = positions[idx - 1];
    let delta = positions[idx] - start;
    let P = (t - start) / delta;
    let fn5 = fns[idx] || fns[0];
    if (fn5 !== void 0) {
      if (isnum(fn5)) {
        fn5 = midpoint_default((fn5 - start) / delta);
      }
      P = fn5(P);
    }
    let t0 = (idx - 1 + P) / n;
    return def.channels.reduce(
      (res, channel) => {
        let val = interpolators[channel](t0);
        if (val !== void 0) {
          res[channel] = val;
        }
        return res;
      },
      { mode }
    );
  };
};
var interpolate = (colors2, mode = "rgb", overrides) => interpolate_fn(colors2, mode, overrides);
var interpolateWith = (premap, postmap) => (colors2, mode = "rgb", overrides) => {
  let post = postmap ? mapper(postmap, mode) : void 0;
  let it = interpolate_fn(colors2, mode, overrides, premap);
  return post ? (t) => post(it(t)) : it;
};
var interpolateWithPremultipliedAlpha = interpolateWith(
  mapAlphaMultiply,
  mapAlphaDivide
);

// node_modules/culori/src/clamp.js
var rgb2 = converter_default("rgb");
var fixup_rgb = (c2) => {
  const res = {
    mode: c2.mode,
    r: Math.max(0, Math.min(c2.r !== void 0 ? c2.r : 0, 1)),
    g: Math.max(0, Math.min(c2.g !== void 0 ? c2.g : 0, 1)),
    b: Math.max(0, Math.min(c2.b !== void 0 ? c2.b : 0, 1))
  };
  if (c2.alpha !== void 0) {
    res.alpha = c2.alpha;
  }
  return res;
};
var to_displayable_srgb = (c2) => fixup_rgb(rgb2(c2));
var inrange_rgb = (c2) => {
  return c2 !== void 0 && (c2.r === void 0 || c2.r >= 0 && c2.r <= 1) && (c2.g === void 0 || c2.g >= 0 && c2.g <= 1) && (c2.b === void 0 || c2.b >= 0 && c2.b <= 1);
};
function displayable(color) {
  return inrange_rgb(rgb2(color));
}
function clampRgb(color) {
  color = prepare_default(color);
  if (color === void 0 || displayable(color)) return color;
  let conv = converter_default(color.mode);
  return conv(to_displayable_srgb(color));
}

// node_modules/culori/src/deficiency.js
var rgb3 = converter_default("rgb");

// node_modules/culori/src/wcag.js
function luminance(color) {
  let c2 = converter_default("lrgb")(color);
  return 0.2126 * c2.r + 0.7152 * c2.g + 0.0722 * c2.b;
}
function contrast(a, b) {
  let L1 = luminance(a);
  let L2 = luminance(b);
  return (Math.max(L1, L2) + 0.05) / (Math.min(L1, L2) + 0.05);
}

// node_modules/culori/src/index.js
var a98 = useMode(definition_default2);
var cubehelix = useMode(definition_default3);
var dlab = useMode(definition_default4);
var dlch = useMode(definition_default5);
var hsi = useMode(definition_default6);
var hsl2 = useMode(definition_default7);
var hsv = useMode(definition_default8);
var hwb = useMode(definition_default9);
var itp = useMode(definition_default10);
var jab = useMode(definition_default11);
var jch = useMode(definition_default12);
var lab = useMode(definition_default13);
var lab65 = useMode(definition_default14);
var lch = useMode(definition_default15);
var lch65 = useMode(definition_default16);
var lchuv = useMode(definition_default17);
var lrgb = useMode(definition_default18);
var luv = useMode(definition_default19);
var okhsl = useMode(modeOkhsl_default);
var okhsv = useMode(modeOkhsv_default);
var oklab = useMode(definition_default20);
var oklch = useMode(definition_default21);
var p3 = useMode(definition_default22);
var prophoto = useMode(definition_default23);
var rec2020 = useMode(definition_default24);
var rgb4 = useMode(definition_default);
var xyb = useMode(definition_default25);
var xyz50 = useMode(definition_default26);
var xyz65 = useMode(definition_default27);
var yiq = useMode(definition_default28);

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/array.js
var array_exports = {};
__export(array_exports, {
  addItem: () => addItem,
  average: () => average2,
  buildTree: () => buildTree,
  chunk: () => chunk,
  combine: () => combine,
  flatten: () => flatten2,
  flattenTree: () => flattenTree,
  greatestAbs: () => greatestAbs,
  joinValues: () => joinValues,
  moveItem: () => moveItem,
  movingAverage: () => movingAverage,
  nestedFindByPath: () => nestedFindByPath,
  nestedFindByPredicate: () => nestedFindByPredicate2,
  removeItem: () => removeItem,
  samples: () => samples,
  subtract: () => subtract,
  sum: () => sum,
  sumObjects: () => sumObjects,
  unique: () => unique,
  walk: () => walk
});

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/object.js
var object_exports = {};
__export(object_exports, {
  camelCaseKeys: () => camelCaseKeys,
  distinctKeys: () => distinctKeys,
  expireObject: () => expireObject,
  isEmptyObject: () => isEmptyObject,
  isLiteralObject: () => isLiteralObject,
  keysByValues: () => keysByValues,
  mapKeys: () => mapKeys,
  mapValues: () => mapValues,
  merge: () => merge,
  nestedFindByPredicate: () => nestedFindByPredicate,
  objectId: () => objectId,
  omit: () => omit,
  omitNil: () => omitNil,
  pick: () => pick,
  propAccessor: () => propAccessor
});

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/typeHelpers.js
function keys(o) {
  return Object.keys(o);
}
function entries(o) {
  if (o instanceof Map)
    return Array.from(o.entries());
  return Object.entries(o);
}
function fromEntries(entries2) {
  return Object.fromEntries(entries2);
}
function assertNever(x) {
  throw new Error(`Unhandled enum case: ${x}`);
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/object.js
function isLiteralObject(obj) {
  return obj && typeof obj === "object" && obj.constructor === Object;
}
function isEmptyObject(obj) {
  return isLiteralObject(obj) && keys(obj).length === 0;
}
function camelCaseKeys(obj) {
  return keys(obj).reduce((acc, key) => (acc[camelCase_default(key ? String(key) : void 0)] = obj[key], acc), {});
}
function nestedFindByPredicate(obj, predicate, childrenProp) {
  const getChildrenProp = propAccessor(childrenProp ?? "children");
  if (predicate(obj)) {
    return obj;
  } else {
    const children = getChildrenProp(obj);
    if (children) {
      for (let o of children) {
        const match = nestedFindByPredicate(o, predicate, childrenProp);
        if (match) {
          return match;
        }
      }
    }
  }
}
function propAccessor(prop) {
  return typeof prop === "function" ? prop : typeof prop === "string" ? (d) => get_default(d, prop) : (x) => x;
}
var objIdMap = /* @__PURE__ */ new WeakMap();
var objectCount = 0;
function objectId(object) {
  if (!objIdMap.has(object))
    objIdMap.set(object, ++objectCount);
  return objIdMap.get(object);
}
function distinctKeys(...objs) {
  return [...new Set(flatten(objs.map((x) => keys(x))))];
}
function flatten(items) {
  return items.reduce((prev, next) => prev.concat(next), []);
}
function merge(object, source) {
  return mergeWith_default(object, source, (objValue, srcValue) => {
    if (Array.isArray(srcValue)) {
      return srcValue;
    }
  });
}
function expireObject(object, expiry) {
  const now = /* @__PURE__ */ new Date();
  if (expiry instanceof Date || typeof object !== "object" || object == null) {
    if (expiry < now) {
      return null;
    }
    return object;
  }
  for (let [prop, propExpiry] of entries(expiry)) {
    if (propExpiry instanceof Date) {
      if (propExpiry < now) {
        if (prop === "$default") {
          for (let objProp of keys(object)) {
            if (!(objProp in expiry)) {
              delete object[objProp];
            }
          }
          delete object[prop];
        } else {
          delete object[prop];
        }
      } else {
      }
    } else {
      const value = object[prop];
      if (value && typeof value === "object") {
        expireObject(value, propExpiry);
        if (isEmptyObject(value)) {
          delete object[prop];
        }
      }
    }
  }
  return isEmptyObject(object) ? null : object;
}
function omit(obj, keys2) {
  if (keys2.length === 0) {
    return obj;
  } else {
    return fromEntries(entries(obj).filter(([key]) => !keys2.includes(key)));
  }
}
function omitNil(obj) {
  if (keys.length === 0) {
    return obj;
  } else {
    return fromEntries(entries(obj).filter(([key, value]) => value != null));
  }
}
function pick(obj, keys2) {
  if (keys2.length === 0) {
    return obj;
  } else {
    return fromEntries(keys2.filter((key) => key in obj).map((key) => [key, obj[key]]));
  }
}
function keysByValues(obj) {
  return fromEntries(entries(obj).map(([key, value]) => [String(value), key]));
}
function mapKeys(obj, fn5) {
  return fromEntries(entries(obj).map(([key, value]) => [fn5(key), value]));
}
function mapValues(obj, fn5) {
  return fromEntries(entries(obj).map(([key, value]) => [key, fn5(value)]));
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/array.js
function flatten2(items) {
  return items.reduce((prev, next) => prev.concat(next), []);
}
function combine(values, func) {
  if (values.every((x) => x == null)) {
    return null;
  }
  return values.reduce(func);
}
function sum(items, prop) {
  const getProp = propAccessor(prop);
  const values = items.map((x) => getProp(x));
  return combine(values, (total, operand) => (total || 0) + (operand || 0));
}
function sumObjects(items, prop) {
  const getProp = propAccessor(prop);
  const result = rollup(items.flatMap((x) => entries(x ?? {})), (values) => sum(values, (d) => {
    const value = Number(getProp(d[1]));
    return Number.isFinite(value) ? value : 0;
  }), (d) => d[0]);
  return items.every(Array.isArray) ? Array.from(result.values()) : fromEntries(result);
}
function subtract(items, prop) {
  const getProp = propAccessor(prop);
  const values = items.map((x) => getProp(x));
  return combine(values, (total, operand) => (total || 0) - (operand || 0));
}
function average2(items, prop) {
  const total = sum(items, prop);
  return total !== null ? total / items.length : null;
}
function movingAverage(items, windowSize, prop) {
  const getProp = propAccessor(prop);
  let sum2 = 0;
  const means = items.map((item, i) => {
    const value = getProp(item);
    sum2 += value ?? 0;
    if (i >= windowSize - 1) {
      const mean = sum2 / windowSize;
      const oldestValue = getProp(items[i - windowSize + 1]);
      sum2 -= oldestValue ?? 0;
      return mean;
    } else {
      return null;
    }
  });
  return means;
}
function unique(values) {
  return Array.from(new Set(values));
}
function joinValues(values = [], max2 = 3, separator = ", ") {
  const total = values.length;
  if (total <= max2) {
    return values.join(separator);
  } else {
    if (max2 === 0) {
      if (values.length === 1) {
        return values[0];
      } else {
        return `(${total} total)`;
      }
    } else {
      return `${values.slice(0, max2).join(separator)}, ... (${total} total)`;
    }
  }
}
function nestedFindByPath(arr, path, props, depth = 0) {
  const getKeyProp = propAccessor((props == null ? void 0 : props.key) ?? "key");
  const getValuesProp = propAccessor((props == null ? void 0 : props.values) ?? "values");
  const item = arr.find((x) => getKeyProp(x) === path[depth]);
  if (depth === path.length - 1) {
    return item;
  } else {
    const children = getValuesProp(item);
    if (children) {
      return nestedFindByPath(getValuesProp(item), path, props, depth + 1);
    }
  }
}
function nestedFindByPredicate2(arr, predicate, childrenProp) {
  const getChildrenProp = propAccessor(childrenProp ?? "children");
  let match = arr.find(predicate);
  if (match) {
    return match;
  } else {
    for (var item of arr) {
      const children = getChildrenProp(item);
      if (children) {
        match = nestedFindByPredicate2(getChildrenProp(item), predicate, childrenProp);
        if (match) {
          return match;
        }
      }
    }
  }
  return void 0;
}
function buildTree(arr) {
  var levels = [{}];
  arr.forEach((o) => {
    var _a;
    levels.length = o.level;
    levels[o.level - 1].children = levels[o.level - 1].children || [];
    (_a = levels[o.level - 1].children) == null ? void 0 : _a.push(o);
    levels[o.level] = o;
  });
  return levels[0].children ?? [];
}
function walk(arr, children, callback) {
  arr.forEach((item) => {
    callback(item);
    if (children(item)) {
      walk(children(item), children, callback);
    }
  });
}
function flattenTree(arr, children) {
  const flatArray = [];
  walk(arr, children, (item) => flatArray.push(item));
  return flatArray;
}
function chunk(array, size) {
  return array.reduce((acc, item, index) => {
    const bucket = Math.floor(index / size);
    if (!acc[bucket]) {
      acc[bucket] = [];
    }
    acc[bucket].push(item);
    return acc;
  }, []);
}
function samples(array, size) {
  if (!((size = Math.floor(size)) > 0))
    return [];
  const n = array.length;
  if (!(n > size))
    return [...array];
  if (size === 1)
    return [array[n >> 1]];
  return Array.from({ length: size }, (_, i) => array[Math.round(i / (size - 1) * (n - 1))]);
}
function addItem(array, item, index) {
  array.splice(index, 0, item);
  return array;
}
function moveItem(array, from, to) {
  var item = array[from];
  array.splice(from, 1);
  array.splice(to, 0, item);
  return array;
}
function removeItem(array, index) {
  array.splice(index, 1);
  return array;
}
function greatestAbs(array) {
  return greatest(array, (a, b) => Math.abs(a) - Math.abs(b));
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/date.js
var date_exports = {};
__export(date_exports, {
  DateToken: () => DateToken,
  DayOfWeek: () => DayOfWeek,
  PeriodType: () => PeriodType,
  endOfBiWeek: () => endOfBiWeek,
  endOfFiscalYear: () => endOfFiscalYear,
  formatDate: () => formatDate,
  formatDateWithLocale: () => formatDateWithLocale,
  formatISODate: () => formatISODate,
  formatIntl: () => formatIntl,
  getDateFuncsByPeriodType: () => getDateFuncsByPeriodType,
  getDayOfWeek: () => getDayOfWeek,
  getDayOfWeekName: () => getDayOfWeekName,
  getFiscalYear: () => getFiscalYear,
  getFiscalYearRange: () => getFiscalYearRange,
  getMaxSelectedDate: () => getMaxSelectedDate,
  getMinSelectedDate: () => getMinSelectedDate,
  getMonthDaysByWeek: () => getMonthDaysByWeek,
  getMonths: () => getMonths,
  getPeriodTypeByCode: () => getPeriodTypeByCode,
  getPeriodTypeCode: () => getPeriodTypeCode,
  getPeriodTypeName: () => getPeriodTypeName,
  getPeriodTypeNameWithLocale: () => getPeriodTypeNameWithLocale,
  hasDayOfWeek: () => hasDayOfWeek,
  isSameFiscalYear: () => isSameFiscalYear,
  isStringDate: () => isStringDate,
  localToUtcDate: () => localToUtcDate,
  missingDayOfWeek: () => missingDayOfWeek,
  randomDate: () => randomDate,
  replaceDayOfWeek: () => replaceDayOfWeek,
  startOfBiWeek: () => startOfBiWeek,
  startOfFiscalYear: () => startOfFiscalYear,
  updatePeriodTypeWithWeekStartsOn: () => updatePeriodTypeWithWeekStartsOn,
  utcToLocalDate: () => utcToLocalDate
});

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/typeGuards.js
function hasKeyOf(object, key) {
  if (object) {
    return key in object;
  } else {
    return false;
  }
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/date_types.js
var PeriodType;
(function(PeriodType2) {
  PeriodType2[PeriodType2["Custom"] = 1] = "Custom";
  PeriodType2[PeriodType2["Day"] = 10] = "Day";
  PeriodType2[PeriodType2["DayTime"] = 11] = "DayTime";
  PeriodType2[PeriodType2["TimeOnly"] = 15] = "TimeOnly";
  PeriodType2[PeriodType2["Week"] = 20] = "Week";
  PeriodType2[PeriodType2["WeekSun"] = 21] = "WeekSun";
  PeriodType2[PeriodType2["WeekMon"] = 22] = "WeekMon";
  PeriodType2[PeriodType2["WeekTue"] = 23] = "WeekTue";
  PeriodType2[PeriodType2["WeekWed"] = 24] = "WeekWed";
  PeriodType2[PeriodType2["WeekThu"] = 25] = "WeekThu";
  PeriodType2[PeriodType2["WeekFri"] = 26] = "WeekFri";
  PeriodType2[PeriodType2["WeekSat"] = 27] = "WeekSat";
  PeriodType2[PeriodType2["Month"] = 30] = "Month";
  PeriodType2[PeriodType2["MonthYear"] = 31] = "MonthYear";
  PeriodType2[PeriodType2["Quarter"] = 40] = "Quarter";
  PeriodType2[PeriodType2["CalendarYear"] = 50] = "CalendarYear";
  PeriodType2[PeriodType2["FiscalYearOctober"] = 60] = "FiscalYearOctober";
  PeriodType2[PeriodType2["BiWeek1"] = 70] = "BiWeek1";
  PeriodType2[PeriodType2["BiWeek1Sun"] = 71] = "BiWeek1Sun";
  PeriodType2[PeriodType2["BiWeek1Mon"] = 72] = "BiWeek1Mon";
  PeriodType2[PeriodType2["BiWeek1Tue"] = 73] = "BiWeek1Tue";
  PeriodType2[PeriodType2["BiWeek1Wed"] = 74] = "BiWeek1Wed";
  PeriodType2[PeriodType2["BiWeek1Thu"] = 75] = "BiWeek1Thu";
  PeriodType2[PeriodType2["BiWeek1Fri"] = 76] = "BiWeek1Fri";
  PeriodType2[PeriodType2["BiWeek1Sat"] = 77] = "BiWeek1Sat";
  PeriodType2[PeriodType2["BiWeek2"] = 80] = "BiWeek2";
  PeriodType2[PeriodType2["BiWeek2Sun"] = 81] = "BiWeek2Sun";
  PeriodType2[PeriodType2["BiWeek2Mon"] = 82] = "BiWeek2Mon";
  PeriodType2[PeriodType2["BiWeek2Tue"] = 83] = "BiWeek2Tue";
  PeriodType2[PeriodType2["BiWeek2Wed"] = 84] = "BiWeek2Wed";
  PeriodType2[PeriodType2["BiWeek2Thu"] = 85] = "BiWeek2Thu";
  PeriodType2[PeriodType2["BiWeek2Fri"] = 86] = "BiWeek2Fri";
  PeriodType2[PeriodType2["BiWeek2Sat"] = 87] = "BiWeek2Sat";
})(PeriodType || (PeriodType = {}));
var DayOfWeek;
(function(DayOfWeek2) {
  DayOfWeek2[DayOfWeek2["Sunday"] = 0] = "Sunday";
  DayOfWeek2[DayOfWeek2["Monday"] = 1] = "Monday";
  DayOfWeek2[DayOfWeek2["Tuesday"] = 2] = "Tuesday";
  DayOfWeek2[DayOfWeek2["Wednesday"] = 3] = "Wednesday";
  DayOfWeek2[DayOfWeek2["Thursday"] = 4] = "Thursday";
  DayOfWeek2[DayOfWeek2["Friday"] = 5] = "Friday";
  DayOfWeek2[DayOfWeek2["Saturday"] = 6] = "Saturday";
})(DayOfWeek || (DayOfWeek = {}));
var DateToken;
(function(DateToken2) {
  DateToken2["Year_numeric"] = "yyy";
  DateToken2["Year_2Digit"] = "yy";
  DateToken2["Month_long"] = "MMMM";
  DateToken2["Month_short"] = "MMM";
  DateToken2["Month_2Digit"] = "MM";
  DateToken2["Month_numeric"] = "M";
  DateToken2["Hour_numeric"] = "h";
  DateToken2["Hour_2Digit"] = "hh";
  DateToken2["Hour_wAMPM"] = "a";
  DateToken2["Hour_woAMPM"] = "aaaaaa";
  DateToken2["Minute_numeric"] = "m";
  DateToken2["Minute_2Digit"] = "mm";
  DateToken2["Second_numeric"] = "s";
  DateToken2["Second_2Digit"] = "ss";
  DateToken2["MiliSecond_3"] = "SSS";
  DateToken2["DayOfMonth_numeric"] = "d";
  DateToken2["DayOfMonth_2Digit"] = "dd";
  DateToken2["DayOfMonth_withOrdinal"] = "do";
  DateToken2["DayOfWeek_narrow"] = "eeeee";
  DateToken2["DayOfWeek_long"] = "eeee";
  DateToken2["DayOfWeek_short"] = "eee";
})(DateToken || (DateToken = {}));

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/dateInternal.js
function getWeekStartsOnFromIntl(locales) {
  var _a;
  if (!locales) {
    return DayOfWeek.Sunday;
  }
  const locale = new Intl.Locale(locales);
  const weekInfo = locale.weekInfo ?? ((_a = locale.getWeekInfo) == null ? void 0 : _a.call(locale));
  return ((weekInfo == null ? void 0 : weekInfo.firstDay) ?? 0) % 7;
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/locale.js
var defaultLocaleSettings = {
  locale: "en",
  dictionary: {
    Ok: "Ok",
    Cancel: "Cancel",
    Date: {
      Start: "Start",
      End: "End",
      Empty: "Empty",
      Day: "Day",
      DayTime: "Day Time",
      Time: "Time",
      Week: "Week",
      BiWeek: "Bi-Week",
      Month: "Month",
      Quarter: "Quarter",
      CalendarYear: "Calendar Year",
      FiscalYearOct: "Fiscal Year (Oct)",
      PeriodDay: {
        Current: "Today",
        Last: "Yesterday",
        LastX: "Last {0} days"
      },
      PeriodWeek: {
        Current: "This week",
        Last: "Last week",
        LastX: "Last {0} weeks"
      },
      PeriodBiWeek: {
        Current: "This bi-week",
        Last: "Last bi-week",
        LastX: "Last {0} bi-weeks"
      },
      PeriodMonth: {
        Current: "This month",
        Last: "Last month",
        LastX: "Last {0} months"
      },
      PeriodQuarter: {
        Current: "This quarter",
        Last: "Last quarter",
        LastX: "Last {0} quarters"
      },
      PeriodQuarterSameLastyear: "Same quarter last year",
      PeriodYear: {
        Current: "This year",
        Last: "Last year",
        LastX: "Last {0} years"
      },
      PeriodFiscalYear: {
        Current: "This fiscal year",
        Last: "Last fiscal year",
        LastX: "Last {0} fiscal years"
      }
    }
  },
  formats: {
    numbers: {
      defaults: {
        currency: "USD",
        fractionDigits: 2,
        currencyDisplay: "symbol"
      }
    },
    dates: {
      baseParsing: "MM/dd/yyyy",
      weekStartsOn: DayOfWeek.Sunday,
      ordinalSuffixes: {
        one: "st",
        two: "nd",
        few: "rd",
        other: "th"
      },
      presets: {
        day: {
          short: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric],
          default: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric],
          long: [DateToken.DayOfMonth_numeric, DateToken.Month_short, DateToken.Year_numeric]
        },
        dayTime: {
          short: [
            DateToken.DayOfMonth_numeric,
            DateToken.Month_numeric,
            DateToken.Year_numeric,
            DateToken.Hour_numeric,
            DateToken.Minute_numeric
          ],
          default: [
            DateToken.DayOfMonth_numeric,
            DateToken.Month_numeric,
            DateToken.Year_numeric,
            DateToken.Hour_2Digit,
            DateToken.Minute_2Digit
          ],
          long: [
            DateToken.DayOfMonth_numeric,
            DateToken.Month_numeric,
            DateToken.Year_numeric,
            DateToken.Hour_2Digit,
            DateToken.Minute_2Digit,
            DateToken.Second_2Digit
          ]
        },
        timeOnly: {
          short: [DateToken.Hour_numeric, DateToken.Minute_numeric],
          default: [DateToken.Hour_2Digit, DateToken.Minute_2Digit, DateToken.Second_2Digit],
          long: [
            DateToken.Hour_2Digit,
            DateToken.Minute_2Digit,
            DateToken.Second_2Digit,
            DateToken.MiliSecond_3
          ]
        },
        week: {
          short: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric],
          default: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric],
          long: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric]
        },
        month: {
          short: DateToken.Month_short,
          default: DateToken.Month_short,
          long: DateToken.Month_long
        },
        monthsYear: {
          short: [DateToken.Month_short, DateToken.Year_2Digit],
          default: [DateToken.Month_long, DateToken.Year_numeric],
          long: [DateToken.Month_long, DateToken.Year_numeric]
        },
        year: {
          short: DateToken.Year_2Digit,
          default: DateToken.Year_numeric,
          long: DateToken.Year_numeric
        }
      }
    }
  }
};
function createLocaleSettings(localeSettings, base = defaultLocaleSettings) {
  var _a, _b, _c, _d;
  if ((_b = (_a = localeSettings.formats) == null ? void 0 : _a.dates) == null ? void 0 : _b.ordinalSuffixes) {
    localeSettings.formats.dates.ordinalSuffixes = {
      one: "",
      two: "",
      few: "",
      other: "",
      zero: "",
      many: "",
      ...localeSettings.formats.dates.ordinalSuffixes
    };
  }
  if (((_d = (_c = localeSettings.formats) == null ? void 0 : _c.dates) == null ? void 0 : _d.weekStartsOn) === void 0) {
    localeSettings = defaultsDeep_default(localeSettings, {
      formats: { dates: { weekStartsOn: getWeekStartsOnFromIntl(localeSettings.locale) } }
    });
  }
  return defaultsDeep_default(localeSettings, base);
}
var defaultLocale = createLocaleSettings({ locale: "en" });

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/date.js
function getDayOfWeekName(weekStartsOn, locales) {
  const date = new Date(2024, 0, 7 + weekStartsOn);
  const formatter = new Intl.DateTimeFormat(locales, { weekday: "short" });
  return formatter.format(date);
}
function getPeriodTypeName(periodType) {
  return getPeriodTypeNameWithLocale(defaultLocale, periodType);
}
function getPeriodTypeNameWithLocale(settings, periodType) {
  const { locale, dictionary: { Date: dico } } = settings;
  switch (periodType) {
    case PeriodType.Custom:
      return "Custom";
    case PeriodType.Day:
      return dico.Day;
    case PeriodType.DayTime:
      return dico.DayTime;
    case PeriodType.TimeOnly:
      return dico.Time;
    case PeriodType.WeekSun:
      return `${dico.Week} (${getDayOfWeekName(DayOfWeek.Sunday, locale)})`;
    case PeriodType.WeekMon:
      return `${dico.Week} (${getDayOfWeekName(1, locale)})`;
    case PeriodType.WeekTue:
      return `${dico.Week} (${getDayOfWeekName(2, locale)})`;
    case PeriodType.WeekWed:
      return `${dico.Week} (${getDayOfWeekName(3, locale)})`;
    case PeriodType.WeekThu:
      return `${dico.Week} (${getDayOfWeekName(4, locale)})`;
    case PeriodType.WeekFri:
      return `${dico.Week} (${getDayOfWeekName(5, locale)})`;
    case PeriodType.WeekSat:
      return `${dico.Week} (${getDayOfWeekName(6, locale)})`;
    case PeriodType.Week:
      return dico.Week;
    case PeriodType.Month:
      return dico.Month;
    case PeriodType.MonthYear:
      return dico.Month;
    case PeriodType.Quarter:
      return dico.Quarter;
    case PeriodType.CalendarYear:
      return dico.CalendarYear;
    case PeriodType.FiscalYearOctober:
      return dico.FiscalYearOct;
    case PeriodType.BiWeek1Sun:
      return `${dico.BiWeek} (${getDayOfWeekName(0, locale)})`;
    case PeriodType.BiWeek1Mon:
      return `${dico.BiWeek} (${getDayOfWeekName(1, locale)})`;
    case PeriodType.BiWeek1Tue:
      return `${dico.BiWeek} (${getDayOfWeekName(2, locale)})`;
    case PeriodType.BiWeek1Wed:
      return `${dico.BiWeek} (${getDayOfWeekName(3, locale)})`;
    case PeriodType.BiWeek1Thu:
      return `${dico.BiWeek} (${getDayOfWeekName(4, locale)})`;
    case PeriodType.BiWeek1Fri:
      return `${dico.BiWeek} (${getDayOfWeekName(5, locale)})`;
    case PeriodType.BiWeek1Sat:
      return `${dico.BiWeek} (${getDayOfWeekName(6, locale)})`;
    case PeriodType.BiWeek1:
      return dico.BiWeek;
    case PeriodType.BiWeek2Sun:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(0, locale)})`;
    case PeriodType.BiWeek2Mon:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(1, locale)})`;
    case PeriodType.BiWeek2Tue:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(2, locale)})`;
    case PeriodType.BiWeek2Wed:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(3, locale)})`;
    case PeriodType.BiWeek2Thu:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(4, locale)})`;
    case PeriodType.BiWeek2Fri:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(5, locale)})`;
    case PeriodType.BiWeek2Sat:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(6, locale)})`;
    case PeriodType.BiWeek2:
      return `${dico.BiWeek} 2`;
    default:
      assertNever(periodType);
  }
}
var periodTypeMappings = {
  [PeriodType.Custom]: "CUSTOM",
  [PeriodType.Day]: "DAY",
  [PeriodType.DayTime]: "DAY-TIME",
  [PeriodType.TimeOnly]: "TIME",
  [PeriodType.WeekSun]: "WEEK-SUN",
  [PeriodType.WeekMon]: "WEEK-MON",
  [PeriodType.WeekTue]: "WEEK-TUE",
  [PeriodType.WeekWed]: "WEEK-WED",
  [PeriodType.WeekThu]: "WEEK-THU",
  [PeriodType.WeekFri]: "WEEK-FRI",
  [PeriodType.WeekSat]: "WEEK-SAT",
  [PeriodType.Week]: "WEEK",
  [PeriodType.Month]: "MTH",
  [PeriodType.MonthYear]: "MTH-CY",
  [PeriodType.Quarter]: "QTR",
  [PeriodType.CalendarYear]: "CY",
  [PeriodType.FiscalYearOctober]: "FY-OCT",
  [PeriodType.BiWeek1Sun]: "BIWEEK1-SUN",
  [PeriodType.BiWeek1Mon]: "BIWEEK1-MON",
  [PeriodType.BiWeek1Tue]: "BIWEEK1-TUE",
  [PeriodType.BiWeek1Wed]: "BIWEEK1-WED",
  [PeriodType.BiWeek1Thu]: "BIWEEK1-THU",
  [PeriodType.BiWeek1Fri]: "BIWEEK1-FRI",
  [PeriodType.BiWeek1Sat]: "BIWEEK1-SAT",
  [PeriodType.BiWeek1]: "BIWEEK1",
  [PeriodType.BiWeek2Sun]: "BIWEEK2-SUN",
  [PeriodType.BiWeek2Mon]: "BIWEEK2-MON",
  [PeriodType.BiWeek2Tue]: "BIWEEK2-TUE",
  [PeriodType.BiWeek2Wed]: "BIWEEK2-WED",
  [PeriodType.BiWeek2Thu]: "BIWEEK2-THU",
  [PeriodType.BiWeek2Fri]: "BIWEEK2-FRI",
  [PeriodType.BiWeek2Sat]: "BIWEEK2-SAT",
  [PeriodType.BiWeek2]: "BIWEEK2"
};
function getPeriodTypeCode(periodType) {
  return periodTypeMappings[periodType];
}
function getPeriodTypeByCode(code) {
  const element = entries(periodTypeMappings).find((c2) => c2[1] === code);
  return parseInt(String((element == null ? void 0 : element[0]) ?? "1"));
}
function getDayOfWeek(periodType) {
  if (periodType >= PeriodType.WeekSun && periodType <= PeriodType.WeekSat || periodType >= PeriodType.BiWeek1Sun && periodType <= PeriodType.BiWeek1Sat || periodType >= PeriodType.BiWeek2Sun && periodType <= PeriodType.BiWeek2Sat) {
    return periodType % 10 - 1;
  } else {
    return null;
  }
}
function replaceDayOfWeek(periodType, dayOfWeek) {
  if (hasDayOfWeek(periodType)) {
    return periodType - (getDayOfWeek(periodType) ?? 0) + dayOfWeek;
  } else if (missingDayOfWeek(periodType)) {
    return periodType + dayOfWeek + 1;
  } else {
    return periodType;
  }
}
function hasDayOfWeek(periodType) {
  if (periodType >= PeriodType.WeekSun && periodType <= PeriodType.WeekSat) {
    return true;
  }
  if (periodType >= PeriodType.BiWeek1Sun && periodType <= PeriodType.BiWeek1Sat) {
    return true;
  }
  if (periodType >= PeriodType.BiWeek2Sun && periodType <= PeriodType.BiWeek2Sat) {
    return true;
  }
  return false;
}
function missingDayOfWeek(periodType) {
  return [PeriodType.Week, PeriodType.BiWeek1, PeriodType.BiWeek2].includes(periodType);
}
function getMonths(year = (/* @__PURE__ */ new Date()).getFullYear()) {
  return Array.from({ length: 12 }, (_, i) => new Date(year, i, 1));
}
function getMonthDaysByWeek(dateInTheMonth, weekStartsOn = DayOfWeek.Sunday) {
  const startOfFirstWeek = startOfWeek(startOfMonth(dateInTheMonth), { weekStartsOn });
  const endOfLastWeek = endOfWeek(endOfMonth(dateInTheMonth), { weekStartsOn });
  const list = [];
  let valueToAdd = startOfFirstWeek;
  while (valueToAdd <= endOfLastWeek) {
    list.push(valueToAdd);
    valueToAdd = addDays(valueToAdd, 1);
  }
  return chunk(list, 7);
}
function getMinSelectedDate(date) {
  if (date instanceof Date) {
    return date;
  } else if (date instanceof Array) {
    return min(date);
  } else if (hasKeyOf(date, "from")) {
    return date.from;
  } else {
    return null;
  }
}
function getMaxSelectedDate(date) {
  if (date instanceof Date) {
    return date;
  } else if (date instanceof Array) {
    return max(date);
  } else if (hasKeyOf(date, "to")) {
    return date.to;
  } else {
    return null;
  }
}
function getFiscalYear(date = /* @__PURE__ */ new Date(), options) {
  if (date === null) {
    return NaN;
  }
  const startMonth = options && options.startMonth || 10;
  return date.getMonth() >= startMonth - 1 ? date.getFullYear() + 1 : date.getFullYear();
}
function getFiscalYearRange(date = /* @__PURE__ */ new Date(), options) {
  const fiscalYear = getFiscalYear(date, options);
  const startMonth = options && options.startMonth || 10;
  const numberOfMonths = options && options.numberOfMonths || 12;
  const startDate = new Date((fiscalYear || 0) - 1, startMonth - 1, 1);
  const endDate = endOfMonth(addMonths(startDate, numberOfMonths - 1));
  return { startDate, endDate };
}
function startOfFiscalYear(date, options) {
  return getFiscalYearRange(date, options).startDate;
}
function endOfFiscalYear(date, options) {
  return getFiscalYearRange(date, options).endDate;
}
function isSameFiscalYear(dateLeft, dateRight) {
  return getFiscalYear(dateLeft) === getFiscalYear(dateRight);
}
var biweekBaseDates = [/* @__PURE__ */ new Date("1799-12-22T00:00"), /* @__PURE__ */ new Date("1799-12-15T00:00")];
function startOfBiWeek(date, week, startOfWeek2) {
  var weekBaseDate = biweekBaseDates[week - 1];
  var baseDate = addDays(weekBaseDate, startOfWeek2);
  var periodsSince = Math.floor(differenceInDays(date, baseDate) / 14);
  return addDays(baseDate, periodsSince * 14);
}
function endOfBiWeek(date, week, startOfWeek2) {
  return addDays(startOfBiWeek(date, week, startOfWeek2), 13);
}
function getDateFuncsByPeriodType(settings, periodType) {
  if (settings) {
    periodType = updatePeriodTypeWithWeekStartsOn(settings.formats.dates.weekStartsOn, periodType);
  }
  switch (periodType) {
    case PeriodType.Day:
      return {
        start: startOfDay,
        end: endOfDay,
        add: addDays,
        difference: differenceInDays,
        isSame: isSameDay
      };
    case PeriodType.Week:
    case PeriodType.WeekSun:
      return {
        start: startOfWeek,
        end: endOfWeek,
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: isSameWeek
      };
    case PeriodType.WeekMon:
      return {
        start: (date) => startOfWeek(date, { weekStartsOn: 1 }),
        end: (date) => endOfWeek(date, { weekStartsOn: 1 }),
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 1 })
      };
    case PeriodType.WeekTue:
      return {
        start: (date) => startOfWeek(date, { weekStartsOn: 2 }),
        end: (date) => endOfWeek(date, { weekStartsOn: 2 }),
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 2 })
      };
    case PeriodType.WeekWed:
      return {
        start: (date) => startOfWeek(date, { weekStartsOn: 3 }),
        end: (date) => endOfWeek(date, { weekStartsOn: 3 }),
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 3 })
      };
    case PeriodType.WeekThu:
      return {
        start: (date) => startOfWeek(date, { weekStartsOn: 4 }),
        end: (date) => endOfWeek(date, { weekStartsOn: 4 }),
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 4 })
      };
    case PeriodType.WeekFri:
      return {
        start: (date) => startOfWeek(date, { weekStartsOn: 5 }),
        end: (date) => endOfWeek(date, { weekStartsOn: 5 }),
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 5 })
      };
    case PeriodType.WeekSat:
      return {
        start: (date) => startOfWeek(date, { weekStartsOn: 6 }),
        end: (date) => endOfWeek(date, { weekStartsOn: 6 }),
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 6 })
      };
    case PeriodType.Month:
      return {
        start: startOfMonth,
        end: endOfMonth,
        add: addMonths,
        difference: differenceInMonths,
        isSame: isSameMonth
      };
    case PeriodType.Quarter:
      return {
        start: startOfQuarter,
        end: endOfQuarter,
        add: addQuarters,
        difference: differenceInQuarters,
        isSame: isSameQuarter
      };
    case PeriodType.CalendarYear:
      return {
        start: startOfYear,
        end: endOfYear,
        add: addYears,
        difference: differenceInYears,
        isSame: isSameYear
      };
    case PeriodType.FiscalYearOctober:
      return {
        start: startOfFiscalYear,
        end: endOfFiscalYear,
        add: addYears,
        difference: differenceInYears,
        isSame: isSameFiscalYear
      };
    case PeriodType.BiWeek1:
    case PeriodType.BiWeek1Sun:
    case PeriodType.BiWeek1Mon:
    case PeriodType.BiWeek1Tue:
    case PeriodType.BiWeek1Wed:
    case PeriodType.BiWeek1Thu:
    case PeriodType.BiWeek1Fri:
    case PeriodType.BiWeek1Sat:
    case PeriodType.BiWeek2:
    case PeriodType.BiWeek2Sun:
    case PeriodType.BiWeek2Mon:
    case PeriodType.BiWeek2Tue:
    case PeriodType.BiWeek2Wed:
    case PeriodType.BiWeek2Thu:
    case PeriodType.BiWeek2Fri:
    case PeriodType.BiWeek2Sat: {
      const week = getPeriodTypeCode(periodType).startsWith("BIWEEK1") ? 1 : 2;
      const dayOfWeek = getDayOfWeek(periodType);
      return {
        start: (date) => startOfBiWeek(date, week, dayOfWeek),
        end: (date) => endOfBiWeek(date, week, dayOfWeek),
        add: (date, amount) => addWeeks(date, amount * 2),
        difference: (dateLeft, dateRight) => {
          return differenceInWeeks(dateLeft, dateRight) / 2;
        },
        isSame: (dateLeft, dateRight) => {
          return isSameDay(startOfBiWeek(dateLeft, week, dayOfWeek), startOfBiWeek(dateRight, week, dayOfWeek));
        }
      };
    }
    case PeriodType.Custom:
    case PeriodType.DayTime:
    case PeriodType.TimeOnly:
    case PeriodType.MonthYear:
    case null:
    case void 0:
      return {
        start: startOfDay,
        end: endOfDay,
        add: addDays,
        difference: differenceInDays,
        isSame: isSameDay
      };
    default:
      assertNever(periodType);
  }
}
function formatISODate(date, representation = "complete") {
  if (date == null) {
    return "";
  }
  if (typeof date === "string") {
    date = parseISO(date);
  }
  return formatISO(date, { representation });
}
function formatIntl(settings, dt, tokens_or_intlOptions) {
  const { locale, formats: { dates: { ordinalSuffixes: suffixes } } } = settings;
  function formatIntlOrdinal(formatter2, with_ordinal = false) {
    if (with_ordinal) {
      const rules = new Intl.PluralRules(locale, { type: "ordinal" });
      const splited = formatter2.formatToParts(dt);
      return splited.map((c2) => {
        if (c2.type === "day") {
          const ordinal = rules.select(parseInt(c2.value, 10));
          const suffix = suffixes[ordinal];
          return `${c2.value}${suffix}`;
        }
        return c2.value;
      }).join("");
    }
    return formatter2.format(dt);
  }
  if (typeof tokens_or_intlOptions !== "string" && !Array.isArray(tokens_or_intlOptions)) {
    return formatIntlOrdinal(new Intl.DateTimeFormat(locale, tokens_or_intlOptions), tokens_or_intlOptions.withOrdinal);
  }
  const tokens = Array.isArray(tokens_or_intlOptions) ? tokens_or_intlOptions.join("") : tokens_or_intlOptions;
  const formatter = new Intl.DateTimeFormat(locale, {
    year: tokens.includes(DateToken.Year_numeric) ? "numeric" : tokens.includes(DateToken.Year_2Digit) ? "2-digit" : void 0,
    month: tokens.includes(DateToken.Month_long) ? "long" : tokens.includes(DateToken.Month_short) ? "short" : tokens.includes(DateToken.Month_2Digit) ? "2-digit" : tokens.includes(DateToken.Month_numeric) ? "numeric" : void 0,
    day: tokens.includes(DateToken.DayOfMonth_2Digit) ? "2-digit" : tokens.includes(DateToken.DayOfMonth_numeric) ? "numeric" : void 0,
    hour: tokens.includes(DateToken.Hour_2Digit) ? "2-digit" : tokens.includes(DateToken.Hour_numeric) ? "numeric" : void 0,
    hour12: tokens.includes(DateToken.Hour_woAMPM) ? false : tokens.includes(DateToken.Hour_wAMPM) ? true : void 0,
    minute: tokens.includes(DateToken.Minute_2Digit) ? "2-digit" : tokens.includes(DateToken.Minute_numeric) ? "numeric" : void 0,
    second: tokens.includes(DateToken.Second_2Digit) ? "2-digit" : tokens.includes(DateToken.Second_numeric) ? "numeric" : void 0,
    fractionalSecondDigits: tokens.includes(DateToken.MiliSecond_3) ? 3 : void 0,
    weekday: tokens.includes(DateToken.DayOfWeek_narrow) ? "narrow" : tokens.includes(DateToken.DayOfWeek_long) ? "long" : tokens.includes(DateToken.DayOfWeek_short) ? "short" : void 0
  });
  return formatIntlOrdinal(formatter, tokens.includes(DateToken.DayOfMonth_withOrdinal));
}
function range2(settings, date, weekStartsOn, formatToUse, biWeek = void 0) {
  const start = biWeek === void 0 ? startOfWeek(date, { weekStartsOn }) : startOfBiWeek(date, biWeek, weekStartsOn);
  const end = biWeek === void 0 ? endOfWeek(date, { weekStartsOn }) : endOfBiWeek(date, biWeek, weekStartsOn);
  return formatIntl(settings, start, formatToUse) + " - " + formatIntl(settings, end, formatToUse);
}
function formatDate(date, periodType, options = {}) {
  return formatDateWithLocale(defaultLocale, date, periodType, options);
}
function updatePeriodTypeWithWeekStartsOn(weekStartsOn, periodType) {
  if (periodType === PeriodType.Week) {
    periodType = [
      PeriodType.WeekSun,
      PeriodType.WeekMon,
      PeriodType.WeekTue,
      PeriodType.WeekWed,
      PeriodType.WeekThu,
      PeriodType.WeekFri,
      PeriodType.WeekSat
    ][weekStartsOn];
  } else if (periodType === PeriodType.BiWeek1) {
    periodType = [
      PeriodType.BiWeek1Sun,
      PeriodType.BiWeek1Mon,
      PeriodType.BiWeek1Tue,
      PeriodType.BiWeek1Wed,
      PeriodType.BiWeek1Thu,
      PeriodType.BiWeek1Fri,
      PeriodType.BiWeek1Sat
    ][weekStartsOn];
  } else if (periodType === PeriodType.BiWeek2) {
    periodType = [
      PeriodType.BiWeek2Sun,
      PeriodType.BiWeek2Mon,
      PeriodType.BiWeek2Tue,
      PeriodType.BiWeek2Wed,
      PeriodType.BiWeek2Thu,
      PeriodType.BiWeek2Fri,
      PeriodType.BiWeek2Sat
    ][weekStartsOn];
  }
  return periodType;
}
function formatDateWithLocale(settings, date, periodType, options = {}) {
  if (typeof date === "string") {
    date = parseISO(date);
  }
  if (date == null || isNaN(date)) {
    return "";
  }
  const weekStartsOn = options.weekStartsOn ?? settings.formats.dates.weekStartsOn;
  const { day, dayTime, timeOnly, week, month, monthsYear, year } = settings.formats.dates.presets;
  periodType = updatePeriodTypeWithWeekStartsOn(weekStartsOn, periodType) ?? periodType;
  function rv(preset) {
    if (options.variant === "custom") {
      return options.custom ?? preset.default;
    } else if (options.custom && !options.variant) {
      return options.custom;
    }
    return preset[options.variant ?? "default"];
  }
  switch (periodType) {
    case PeriodType.Custom:
      return formatIntl(settings, date, options.custom);
    case PeriodType.Day:
      return formatIntl(settings, date, rv(day));
    case PeriodType.DayTime:
      return formatIntl(settings, date, rv(dayTime));
    case PeriodType.TimeOnly:
      return formatIntl(settings, date, rv(timeOnly));
    case PeriodType.Week:
    case PeriodType.WeekSun:
      return range2(settings, date, 0, rv(week));
    case PeriodType.WeekMon:
      return range2(settings, date, 1, rv(week));
    case PeriodType.WeekTue:
      return range2(settings, date, 2, rv(week));
    case PeriodType.WeekWed:
      return range2(settings, date, 3, rv(week));
    case PeriodType.WeekThu:
      return range2(settings, date, 4, rv(week));
    case PeriodType.WeekFri:
      return range2(settings, date, 5, rv(week));
    case PeriodType.WeekSat:
      return range2(settings, date, 6, rv(week));
    case PeriodType.Month:
      return formatIntl(settings, date, rv(month));
    case PeriodType.MonthYear:
      return formatIntl(settings, date, rv(monthsYear));
    case PeriodType.Quarter:
      return [
        formatIntl(settings, startOfQuarter(date), rv(month)),
        formatIntl(settings, endOfQuarter(date), rv(monthsYear))
      ].join(" - ");
    case PeriodType.CalendarYear:
      return formatIntl(settings, date, rv(year));
    case PeriodType.FiscalYearOctober:
      const fDate = new Date(getFiscalYear(date), 0, 1);
      return formatIntl(settings, fDate, rv(year));
    case PeriodType.BiWeek1:
    case PeriodType.BiWeek1Sun:
      return range2(settings, date, 0, rv(week), 1);
    case PeriodType.BiWeek1Mon:
      return range2(settings, date, 1, rv(week), 1);
    case PeriodType.BiWeek1Tue:
      return range2(settings, date, 2, rv(week), 1);
    case PeriodType.BiWeek1Wed:
      return range2(settings, date, 3, rv(week), 1);
    case PeriodType.BiWeek1Thu:
      return range2(settings, date, 4, rv(week), 1);
    case PeriodType.BiWeek1Fri:
      return range2(settings, date, 5, rv(week), 1);
    case PeriodType.BiWeek1Sat:
      return range2(settings, date, 6, rv(week), 1);
    case PeriodType.BiWeek2:
    case PeriodType.BiWeek2Sun:
      return range2(settings, date, 0, rv(week), 2);
    case PeriodType.BiWeek2Mon:
      return range2(settings, date, 1, rv(week), 2);
    case PeriodType.BiWeek2Tue:
      return range2(settings, date, 2, rv(week), 2);
    case PeriodType.BiWeek2Wed:
      return range2(settings, date, 3, rv(week), 2);
    case PeriodType.BiWeek2Thu:
      return range2(settings, date, 4, rv(week), 2);
    case PeriodType.BiWeek2Fri:
      return range2(settings, date, 5, rv(week), 2);
    case PeriodType.BiWeek2Sat:
      return range2(settings, date, 6, rv(week), 2);
    default:
      return formatISO(date);
  }
}
function utcToLocalDate(date) {
  date = date instanceof Date ? date : typeof date === "string" ? new Date(date) : /* @__PURE__ */ new Date();
  const d = new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds());
  d.setUTCFullYear(date.getUTCFullYear());
  return d;
}
function localToUtcDate(date) {
  date = date instanceof Date ? date : typeof date === "string" ? new Date(date) : /* @__PURE__ */ new Date();
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds()));
  return d;
}
function randomDate(from, to) {
  const fromTime = from.getTime();
  const toTime = to.getTime();
  return new Date(fromTime + Math.random() * (toTime - fromTime));
}
var DATE_FORMAT = /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(.\d+|)(Z|(-|\+)\d{2}:\d{2}))?$/;
function isStringDate(value) {
  return DATE_FORMAT.test(value);
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/duration.js
var duration_exports = {};
__export(duration_exports, {
  DurationUnits: () => DurationUnits,
  getDuration: () => getDuration,
  humanizeDuration: () => humanizeDuration
});
var DurationUnits;
(function(DurationUnits2) {
  DurationUnits2[DurationUnits2["Year"] = 0] = "Year";
  DurationUnits2[DurationUnits2["Day"] = 1] = "Day";
  DurationUnits2[DurationUnits2["Hour"] = 2] = "Hour";
  DurationUnits2[DurationUnits2["Minute"] = 3] = "Minute";
  DurationUnits2[DurationUnits2["Second"] = 4] = "Second";
  DurationUnits2[DurationUnits2["Millisecond"] = 5] = "Millisecond";
})(DurationUnits || (DurationUnits = {}));
function getDuration(start, end, duration) {
  const startDate = typeof start === "string" ? parseISO(start) : start;
  const endDate = typeof end === "string" ? parseISO(end) : end;
  const differenceInMs = startDate ? Math.abs(Number(endDate || /* @__PURE__ */ new Date()) - Number(startDate)) : void 0;
  if (!Number.isFinite(differenceInMs) && duration == null) {
    return null;
  }
  var milliseconds = (duration == null ? void 0 : duration.milliseconds) ?? differenceInMs ?? 0;
  var seconds = (duration == null ? void 0 : duration.seconds) ?? 0;
  var minutes = (duration == null ? void 0 : duration.minutes) ?? 0;
  var hours = (duration == null ? void 0 : duration.hours) ?? 0;
  var days = (duration == null ? void 0 : duration.days) ?? 0;
  var years = (duration == null ? void 0 : duration.years) ?? 0;
  if (milliseconds >= 1e3) {
    const carrySeconds = (milliseconds - milliseconds % 1e3) / 1e3;
    seconds += carrySeconds;
    milliseconds = milliseconds - carrySeconds * 1e3;
  }
  if (seconds >= 60) {
    const carryMinutes = (seconds - seconds % 60) / 60;
    minutes += carryMinutes;
    seconds = seconds - carryMinutes * 60;
  }
  if (minutes >= 60) {
    const carryHours = (minutes - minutes % 60) / 60;
    hours += carryHours;
    minutes = minutes - carryHours * 60;
  }
  if (hours >= 24) {
    const carryDays = (hours - hours % 24) / 24;
    days += carryDays;
    hours = hours - carryDays * 24;
  }
  if (days >= 365) {
    const carryYears = (days - days % 365) / 365;
    years += carryYears;
    days = days - carryYears * 365;
  }
  return {
    milliseconds,
    seconds,
    minutes,
    hours,
    days,
    years
  };
}
function humanizeDuration(config) {
  const { start, end, minUnits, totalUnits = 99, variant = "short" } = config;
  const duration = getDuration(start, end, config.duration);
  if (duration === null) {
    return "unknown";
  }
  var sentenceArr = [];
  var unitNames = variant === "short" ? ["y", "d", "h", "m", "s", "ms"] : ["years", "days", "hours", "minutes", "seconds", "milliseconds"];
  var unitNums = [
    duration.years,
    duration.days,
    duration.hours,
    duration.minutes,
    duration.seconds,
    duration.milliseconds
  ].filter((x, i2) => i2 <= (minUnits ?? 99));
  for (var i in unitNums) {
    if (sentenceArr.length >= totalUnits) {
      break;
    }
    const unitNum = unitNums[i];
    let unitName = unitNames[i];
    if (unitNum !== 0 || sentenceArr.length === 0 && Number(i) === unitNums.length - 1) {
      switch (variant) {
        case "short":
          sentenceArr.push(unitNum + unitName);
          break;
        case "long":
          if (unitNum === 1) {
            unitName = unitName.slice(0, -1);
          }
          sentenceArr.push(unitNum + " " + unitName);
          break;
      }
    }
  }
  const sentence = sentenceArr.join(variant === "long" ? " and " : " ");
  return sentence;
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/number.js
var number_exports = {};
__export(number_exports, {
  clamp: () => clamp,
  decimalCount: () => decimalCount,
  formatNumber: () => formatNumber,
  formatNumberWithLocale: () => formatNumberWithLocale,
  modulo: () => modulo,
  randomInteger: () => randomInteger,
  round: () => round2,
  step: () => step
});
function getFormatNumber(settings, style) {
  const { numbers } = settings.formats;
  const styleSettings = style && style != "none" ? numbers[style] : {};
  return {
    ...numbers.defaults,
    ...styleSettings
  };
}
function formatNumber(number, style, options) {
  return formatNumberWithLocale(defaultLocale, number, style, options);
}
function formatNumberWithLocale(settings, number, style, options = {}) {
  if (number == null) {
    return "";
  }
  if (style === "none") {
    return `${number}`;
  }
  if (style == null) {
    style = Number.isInteger(number) ? "integer" : "decimal";
  }
  const defaults = getFormatNumber(settings, style);
  const formatter = Intl.NumberFormat(settings.locale, {
    // Let's always starts with all defaults
    ...defaults,
    ...style !== "default" && {
      style
    },
    // Let's shorten min / max with fractionDigits
    ...{
      minimumFractionDigits: options.fractionDigits ?? defaults.fractionDigits,
      maximumFractionDigits: options.fractionDigits ?? defaults.fractionDigits
    },
    // now we bring in user specified options
    ...omitNil(options),
    ...style === "currencyRound" && {
      style: "currency",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    },
    // Let's overwrite for style=percentRound
    ...style === "percentRound" && {
      style: "percent",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    },
    // Let's overwrite for style=metric
    ...style === "metric" && {
      style: "decimal",
      notation: "compact",
      minimumFractionDigits: 0
    },
    // Let's overwrite for style=integer
    ...style === "integer" && {
      style: "decimal",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }
  });
  const value = formatter.format(number);
  let suffix = options.suffix ?? "";
  if (suffix && Math.abs(number) >= 2 && options.suffixExtraIfMany !== "") {
    suffix += options.suffixExtraIfMany ?? "s";
  }
  return `${value}${suffix}`;
}
function clamp(value, min2, max2) {
  return value < min2 ? min2 : value > max2 ? max2 : value;
}
function decimalCount(value) {
  var _a;
  return ((_a = value == null ? void 0 : value.toString().split(".")[1]) == null ? void 0 : _a.length) ?? 0;
}
function round2(value, decimals) {
  return Number(value.toFixed(decimals));
}
function step(value, step2) {
  return round2(value + step2, decimalCount(step2));
}
function randomInteger(min2, max2) {
  return Math.floor(Math.random() * (max2 - min2 + 1)) + min2;
}
function modulo(n, m) {
  return (n % m + m) % m;
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/json.js
function stringify(value) {
  return JSON.stringify(value, replacer);
}
function replacer(key, value) {
  if (value instanceof Map) {
    return {
      _type: "Map",
      value: Array.from(value.entries())
    };
  } else if (value instanceof Set) {
    return {
      _type: "Set",
      value: Array.from(value.values())
    };
  } else {
    return value;
  }
}
function parse2(value) {
  let result;
  try {
    result = JSON.parse(value, reviver);
  } catch (e4) {
    result = value;
  }
  return result;
}
function reviver(key, value) {
  if (typeof value === "string" && isStringDate(value)) {
    return parseISO(value);
  } else if (typeof value === "object" && value !== null) {
    if (value._type === "Map") {
      return new Map(value.value);
    } else if (value._type === "Set") {
      return new Set(value.value);
    }
  }
  return value;
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/env.js
var env_exports = {};
__export(env_exports, {
  browser: () => browser,
  ssr: () => ssr
});
var browser = typeof window !== "undefined";
var ssr = typeof window === "undefined";

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/dateRange.js
var dateRange_exports = {};
__export(dateRange_exports, {
  getDateRangePresets: () => getDateRangePresets,
  getPeriodComparisonOffset: () => getPeriodComparisonOffset,
  getPreviousYearPeriodOffset: () => getPreviousYearPeriodOffset
});
function formatMsg(settings, type, lastX) {
  return lastX === 0 ? settings.dictionary.Date[type].Current : lastX === 1 ? settings.dictionary.Date[type].Last : settings.dictionary.Date[type].LastX.replace("{0}", lastX.toString());
}
function getDateRangePresets(settings, periodType) {
  let now = /* @__PURE__ */ new Date();
  const today = startOfDay(now);
  if (settings) {
    periodType = updatePeriodTypeWithWeekStartsOn(settings.formats.dates.weekStartsOn, periodType) ?? periodType;
  }
  const { start, end, add } = getDateFuncsByPeriodType(settings, periodType);
  switch (periodType) {
    case PeriodType.Day: {
      const last = start(add(today, -1));
      return [0, 1, 3, 7, 14, 30].map((lastX) => {
        return {
          label: formatMsg(settings, "PeriodDay", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    case PeriodType.WeekSun:
    case PeriodType.WeekMon:
    case PeriodType.WeekTue:
    case PeriodType.WeekWed:
    case PeriodType.WeekThu:
    case PeriodType.WeekFri:
    case PeriodType.WeekSat: {
      const last = start(add(today, -1));
      return [0, 1, 2, 4, 6].map((lastX) => {
        return {
          label: formatMsg(settings, "PeriodWeek", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    case PeriodType.BiWeek1Sun:
    case PeriodType.BiWeek1Mon:
    case PeriodType.BiWeek1Tue:
    case PeriodType.BiWeek1Wed:
    case PeriodType.BiWeek1Thu:
    case PeriodType.BiWeek1Fri:
    case PeriodType.BiWeek1Sat:
    case PeriodType.BiWeek2Sun:
    case PeriodType.BiWeek2Mon:
    case PeriodType.BiWeek2Tue:
    case PeriodType.BiWeek2Wed:
    case PeriodType.BiWeek2Thu:
    case PeriodType.BiWeek2Fri:
    case PeriodType.BiWeek2Sat: {
      const last = start(add(today, -1));
      return [0, 1, 2, 4, 6].map((lastX) => {
        return {
          label: formatMsg(settings, "PeriodBiWeek", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    case PeriodType.Month: {
      const last = start(add(today, -1));
      return [0, 1, 2, 3, 6, 12].map((lastX) => {
        return {
          label: formatMsg(settings, "PeriodMonth", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    case PeriodType.Quarter: {
      const last = start(add(today, -1));
      return [0, 1, -1, 4, 12].map((lastX) => {
        if (lastX === -1) {
          return {
            label: settings.dictionary.Date.PeriodQuarterSameLastyear,
            value: {
              from: start(add(today, -4)),
              to: end(add(today, -4)),
              periodType
            }
          };
        }
        return {
          label: formatMsg(settings, "PeriodQuarter", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    case PeriodType.CalendarYear: {
      const last = start(add(today, -1));
      return [0, 1, 3, 5].map((lastX) => {
        return {
          label: formatMsg(settings, "PeriodYear", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    case PeriodType.FiscalYearOctober: {
      const last = start(add(today, -1));
      return [0, 1, 3, 5].map((lastX) => {
        return {
          label: formatMsg(settings, "PeriodFiscalYear", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    default: {
      return [];
    }
  }
}
function getPreviousYearPeriodOffset(periodType, options) {
  switch (periodType) {
    case PeriodType.Day:
      const adjustForLeapYear = (options == null ? void 0 : options.referenceDate) ? isLeapYear(options == null ? void 0 : options.referenceDate) && isAfter(options == null ? void 0 : options.referenceDate, new Date(
        options == null ? void 0 : options.referenceDate.getFullYear(),
        /*Feb*/
        1,
        28
      )) || isLeapYear(subYears(options == null ? void 0 : options.referenceDate, 1)) && isBefore(options == null ? void 0 : options.referenceDate, new Date(
        options == null ? void 0 : options.referenceDate.getFullYear(),
        /*Feb*/
        1,
        29
      )) : false;
      return (options == null ? void 0 : options.alignDayOfWeek) ? -364 : adjustForLeapYear ? -366 : -365;
    case PeriodType.WeekSun:
    case PeriodType.WeekMon:
    case PeriodType.WeekTue:
    case PeriodType.WeekWed:
    case PeriodType.WeekThu:
    case PeriodType.WeekFri:
    case PeriodType.WeekSat:
      return -52;
    case PeriodType.BiWeek1Sun:
    case PeriodType.BiWeek1Mon:
    case PeriodType.BiWeek1Tue:
    case PeriodType.BiWeek1Wed:
    case PeriodType.BiWeek1Thu:
    case PeriodType.BiWeek1Fri:
    case PeriodType.BiWeek1Sat:
    case PeriodType.BiWeek2Sun:
    case PeriodType.BiWeek2Mon:
    case PeriodType.BiWeek2Tue:
    case PeriodType.BiWeek2Wed:
    case PeriodType.BiWeek2Thu:
    case PeriodType.BiWeek2Fri:
    case PeriodType.BiWeek2Sat:
      return -26;
    case PeriodType.Month:
      return -12;
    case PeriodType.Quarter:
      return -4;
    case PeriodType.CalendarYear:
    case PeriodType.FiscalYearOctober:
      return -1;
  }
}
function getPeriodComparisonOffset(settings, view, period) {
  if (period == null || period.from == null || period.to == null || period.periodType == null) {
    throw new Error("Period must be defined to calculate offset");
  }
  switch (view) {
    case "prevPeriod":
      const dateFuncs = getDateFuncsByPeriodType(settings, period.periodType);
      return dateFuncs.difference(period.from, period.to) - 1;
    case "prevYear":
      return getPreviousYearPeriodOffset(period.periodType, {
        referenceDate: period.from
      });
    case "fiftyTwoWeeksAgo":
      return getPreviousYearPeriodOffset(period.periodType, {
        alignDayOfWeek: true
      });
    default:
      throw new Error("Unhandled period offset: " + view);
  }
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/map.js
var map_exports = {};
__export(map_exports, {
  get: () => get
});
function get(map, path) {
  let key = void 0;
  let value = map;
  const currentPath = [...path];
  while (key = currentPath.shift()) {
    if (value instanceof Map && value.has(key)) {
      value = value.get(key);
    } else {
      return void 0;
    }
  }
  return value;
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/rollup.js
var rollup_exports = {};
__export(rollup_exports, {
  default: () => rollup_default
});
function rollup_default(data, reduce, keys2 = [], emptyKey = "Unknown") {
  const keyFuncs = keys2.map((key) => {
    if (isFunction_default(key)) {
      return key;
    } else if (typeof key === "string") {
      return (d) => get_default(d, key) || emptyKey;
    } else {
      return () => "Overall";
    }
  });
  return rollup(data, reduce, ...keyFuncs);
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/routing.js
var routing_exports = {};
__export(routing_exports, {
  isActive: () => isActive,
  url: () => url
});
function url(currentUrl, path) {
  if (path == null) {
    return path;
  } else if (path.match(/^\.\.?\//)) {
    let [, breadcrumbs, relativePath] = path.match(/^([\.\/]+)(.*)/);
    let dir = currentUrl.pathname.replace(/\/$/, "");
    const traverse = breadcrumbs.match(/\.\.\//g) || [];
    traverse.forEach(() => dir = dir.replace(/\/[^\/]+\/?$/, ""));
    path = `${dir}/${relativePath}`.replace(/\/$/, "");
    path = path || "/";
  } else if (path.match(/^\//)) {
    return path;
  } else {
    return path;
  }
  return path;
}
function isActive(currentUrl, path) {
  if (path === "/") {
    return currentUrl.pathname === path;
  } else {
    return currentUrl.pathname.match(path + "($|\\/)") != null;
  }
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/serialize.js
var serialize_exports = {};
__export(serialize_exports, {
  decodeArray: () => decodeArray,
  decodeBoolean: () => decodeBoolean,
  decodeDate: () => decodeDate,
  decodeDateTime: () => decodeDateTime,
  decodeDelimitedArray: () => decodeDelimitedArray,
  decodeDelimitedNumericArray: () => decodeDelimitedNumericArray,
  decodeEnum: () => decodeEnum,
  decodeJson: () => decodeJson,
  decodeNumber: () => decodeNumber,
  decodeNumericArray: () => decodeNumericArray,
  decodeNumericObject: () => decodeNumericObject,
  decodeObject: () => decodeObject,
  decodeString: () => decodeString,
  encodeArray: () => encodeArray,
  encodeBoolean: () => encodeBoolean,
  encodeDate: () => encodeDate,
  encodeDateTime: () => encodeDateTime,
  encodeDelimitedArray: () => encodeDelimitedArray,
  encodeDelimitedNumericArray: () => encodeDelimitedNumericArray,
  encodeJson: () => encodeJson,
  encodeNumber: () => encodeNumber,
  encodeNumericArray: () => encodeNumericArray,
  encodeNumericObject: () => encodeNumericObject,
  encodeObject: () => encodeObject,
  encodeString: () => encodeString
});
function getEncodedValue(input, allowEmptyString) {
  if (input == null) {
    return input;
  }
  if (input.length === 0 && (!allowEmptyString || allowEmptyString && input !== "")) {
    return null;
  }
  const str = input instanceof Array ? input[0] : input;
  if (str == null) {
    return str;
  }
  if (!allowEmptyString && str === "") {
    return null;
  }
  return str;
}
function getEncodedValueArray(input) {
  if (input == null) {
    return input;
  }
  return input instanceof Array ? input : input === "" ? [] : [input];
}
function encodeDate(date) {
  if (date == null) {
    return date;
  }
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${year}-${month < 10 ? `0${month}` : month}-${day < 10 ? `0${day}` : day}`;
}
function decodeDate(input) {
  const dateString = getEncodedValue(input);
  if (dateString == null)
    return dateString;
  const parts = dateString.split("-");
  if (parts[1] != null) {
    parts[1] -= 1;
  } else {
    parts[1] = 0;
    parts[2] = 1;
  }
  const decoded = new Date(...parts);
  if (isNaN(decoded.getTime())) {
    return null;
  }
  return decoded;
}
function encodeDateTime(date) {
  if (date == null) {
    return date;
  }
  return date.toISOString();
}
function decodeDateTime(input) {
  const dateString = getEncodedValue(input);
  if (dateString == null)
    return dateString;
  const decoded = new Date(dateString);
  if (isNaN(decoded.getTime())) {
    return null;
  }
  return decoded;
}
function encodeBoolean(bool) {
  if (bool == null) {
    return bool;
  }
  return bool ? "1" : "0";
}
function decodeBoolean(input) {
  const boolStr = getEncodedValue(input);
  if (boolStr == null)
    return boolStr;
  if (boolStr === "1") {
    return true;
  } else if (boolStr === "0") {
    return false;
  }
  return null;
}
function encodeNumber(num3) {
  if (num3 == null) {
    return num3;
  }
  return String(num3);
}
function decodeNumber(input) {
  const numStr = getEncodedValue(input);
  if (numStr == null)
    return numStr;
  if (numStr === "")
    return null;
  const result = +numStr;
  return result;
}
function encodeString(str) {
  if (str == null) {
    return str;
  }
  return String(str);
}
function decodeString(input) {
  const str = getEncodedValue(input, true);
  if (str == null)
    return str;
  return String(str);
}
function decodeEnum(input, enumValues) {
  const str = decodeString(input);
  if (str == null)
    return str;
  return enumValues.includes(str) ? str : void 0;
}
function encodeJson(any) {
  if (any == null) {
    return any;
  }
  return stringify(any);
}
function decodeJson(input) {
  const jsonStr = getEncodedValue(input);
  if (jsonStr == null)
    return jsonStr;
  let result = null;
  try {
    result = parse2(jsonStr);
  } catch (e4) {
  }
  return result;
}
function encodeArray(array) {
  if (array == null) {
    return array;
  }
  return array;
}
function decodeArray(input) {
  const arr = getEncodedValueArray(input);
  if (arr == null)
    return arr;
  return arr;
}
function encodeNumericArray(array) {
  if (array == null) {
    return array;
  }
  return array.map(String);
}
function decodeNumericArray(input) {
  const arr = decodeArray(input);
  if (arr == null)
    return arr;
  return arr.map((d) => d === "" || d == null ? null : +d);
}
function encodeDelimitedArray(array, entrySeparator = "_") {
  if (array == null) {
    return array;
  }
  return array.join(entrySeparator);
}
function decodeDelimitedArray(input, entrySeparator = "_") {
  const arrayStr = getEncodedValue(input, true);
  if (arrayStr == null)
    return arrayStr;
  if (arrayStr === "")
    return [];
  return arrayStr.split(entrySeparator);
}
var encodeDelimitedNumericArray = encodeDelimitedArray;
function decodeDelimitedNumericArray(arrayStr, entrySeparator = "_") {
  const decoded = decodeDelimitedArray(arrayStr, entrySeparator);
  if (decoded == null)
    return decoded;
  return decoded.map((d) => d === "" || d == null ? null : +d);
}
function encodeObject(obj, keyValSeparator = "-", entrySeparator = "_") {
  if (obj == null)
    return obj;
  if (isEmptyObject(obj))
    return "";
  return keys(obj).map((key) => {
    const value = encodeJson(obj[key]);
    return `${key}${keyValSeparator}${value}`;
  }).join(entrySeparator);
}
function decodeObject(input, keyValSeparator = "-", entrySeparator = "_") {
  const objStr = getEncodedValue(input, true);
  if (objStr == null)
    return objStr;
  if (objStr === "")
    return {};
  const obj = {};
  const keyValSeparatorRegExp = new RegExp(`${keyValSeparator}(.*)`);
  objStr.split(entrySeparator).forEach((entryStr) => {
    const [key, value] = entryStr.split(keyValSeparatorRegExp);
    obj[key] = decodeJson(value);
  });
  return obj;
}
var encodeNumericObject = encodeObject;
function decodeNumericObject(input, keyValSeparator = "-", entrySeparator = "_") {
  const decoded = decodeObject(input, keyValSeparator, entrySeparator);
  if (decoded == null)
    return decoded;
  const decodedNumberObj = {};
  for (const key of keys(decoded)) {
    decodedNumberObj[key] = decodeNumber(decoded[key]);
  }
  return decodedNumberObj;
}

// node_modules/@layerstack/tailwind/node_modules/@layerstack/utils/dist/styles.js
var styles_exports = {};
__export(styles_exports, {
  objectToString: () => objectToString
});
function objectToString(styleObj) {
  return entries(styleObj).map(([key, value]) => {
    if (value) {
      const propertyName = key.replace(/([A-Z])/g, "-$1").toLowerCase();
      return `${propertyName}: ${value};`;
    } else {
      return null;
    }
  }).filter((x) => x).join(" ");
}

// node_modules/@layerstack/tailwind/dist/theme.js
var semanticColors = ["primary", "secondary", "accent", "neutral"];
var stateColors = ["info", "success", "warning", "danger"];
var colors = [...semanticColors, ...stateColors];
var shades = [50, ...range(100, 1e3, 100)];
var colorNames = [
  // Semantic & State colors (ex. `priamry`, 'primary-content`, 'primary-100`, ...)
  ...colors.flatMap((color) => [
    color,
    // default
    `${color}-content`,
    // text/content
    ...shades.map((shade) => `${color}-${shade}`)
  ]),
  // Surfaces
  "surface-100",
  "surface-200",
  "surface-300",
  "surface-content"
];
function getThemeNames(cssContent) {
  const themeBlocks = cssContent.split(/\[data-theme=/);
  const light = [];
  const dark = [];
  for (let i = 1; i < themeBlocks.length; i++) {
    const block = themeBlocks[i];
    const nameMatch = block.match(/^"([^"]+)"/);
    if (!nameMatch)
      continue;
    const themeName = nameMatch[1];
    if (block.includes("color-scheme: dark")) {
      dark.push(themeName);
    } else {
      light.push(themeName);
    }
  }
  return { light, dark };
}
function processThemeColors(colors2, colorSpace) {
  var _a, _b;
  colors2["neutral"] ?? (colors2["neutral"] = colors2["neutral-500"] ?? "oklch(.355192 .032071 262.988584)");
  colors2["info"] ?? (colors2["info"] = colors2["info-500"] ?? "oklch(0.7206 0.191 231.6)");
  colors2["success"] ?? (colors2["success"] = colors2["success-500"] ?? "oklch(64.8% 0.150 160)");
  colors2["warning"] ?? (colors2["warning"] = colors2["warning-500"] ?? "oklch(0.8471 0.199 83.87)");
  colors2["danger"] ?? (colors2["danger"] = colors2["danger-500"] ?? "oklch(0.7176 0.221 22.18)");
  for (const color of [...semanticColors, ...stateColors]) {
    colors2[color] ?? (colors2[color] = colors2[`${color}-500`]);
    colors2[_a = `${color}-content`] ?? (colors2[_a] = foregroundColor(colors2[color]));
    for (const shade of shades) {
      const shadeColorName = `${color}-${shade}`;
      if (!(shadeColorName in colors2)) {
        const referenceShade = ((_b = keys(colors2).map((key) => {
          const [c2, s] = String(key).split("-");
          return [c2, Number(s)];
        }).find(([c2, s]) => c2 === color && (s < 500 ? s > shade : s < shade))) == null ? void 0 : _b[1]) ?? 500;
        const referenceColor = colors2[`${color}-${referenceShade}`] ?? colors2[color];
        if (shade < 500) {
          colors2[shadeColorName] ?? (colors2[shadeColorName] = lightenColor(referenceColor, (referenceShade - shade) / 1e3));
        } else if (shade > 500) {
          colors2[shadeColorName] ?? (colors2[shadeColorName] = darkenColor(colors2[color], (shade - referenceShade) / 1e3));
        } else {
          colors2[shadeColorName] ?? (colors2[shadeColorName] = colors2[color]);
        }
      }
    }
  }
  colors2["surface-100"] ?? (colors2["surface-100"] = "oklch(100 0 0)");
  colors2["surface-200"] ?? (colors2["surface-200"] = darkenColor(colors2["surface-100"], 0.07));
  colors2["surface-300"] ?? (colors2["surface-300"] = darkenColor(colors2["surface-200"], 0.07));
  colors2["surface-content"] ?? (colors2["surface-content"] = foregroundColor(colors2["surface-100"]));
  colors2["color-scheme"] ?? (colors2["color-scheme"] = isDark(colors2["surface-content"]) ? "light" : "dark");
  const result = fromEntries(entries(colors2).map(([name, value]) => {
    if (colorNames.includes(String(name))) {
      return [`--color-${name}`, convertColor(value, colorSpace)];
    } else {
      return [name, value];
    }
  }));
  return result;
}
function round3(value, decimals) {
  if (value) {
    return Number(value.toFixed(decimals));
  } else {
    return 0;
  }
}
function isDark(color) {
  try {
    if (contrast(color, "black") < contrast(color, "white")) {
      return true;
    }
    return false;
  } catch (e4) {
    return false;
  }
}
function foregroundColor(color, percentage = 0.8) {
  try {
    return isDark(color) ? lightenColor(color, percentage) : darkenColor(color, percentage);
  } catch (e4) {
  }
}
function lightenColor(color, percentage) {
  try {
    return formatCss(interpolate([color, "white"], "oklch")(percentage));
  } catch (e4) {
  }
}
function darkenColor(color, percentage) {
  try {
    return formatCss(interpolate([color, "black"], "oklch")(percentage));
  } catch (e4) {
  }
}
function convertColor(color, colorSpace, decimals = 4) {
  try {
    if (colorSpace === "rgb") {
      const computedColor = typeof color === "string" ? rgb4(color) : color;
      if (computedColor) {
        const { r: r2, g, b } = computedColor;
        return `rgb(${round3(r2 * 255, decimals)} ${round3(g * 255, decimals)} ${round3(b * 255, decimals)})`;
      }
    } else if (colorSpace === "hsl") {
      const computedColor = typeof color === "string" ? hsl2(clampRgb(color)) : color;
      if (computedColor) {
        const { h, s, l } = computedColor;
        return `hsl(${round3(h ?? 0, decimals)} ${round3(s * 100, decimals)}% ${round3(l * 100, decimals)}%)`;
      }
    } else if (colorSpace === "oklch") {
      const computedColor = typeof color === "string" ? oklch(clampRgb(color)) : color;
      if (computedColor) {
        const { l, c: c2, h } = computedColor;
        return `oklch(${round3(l, decimals)} ${round3(c2, decimals)} ${round3(h ?? 0, decimals)})`;
      }
    }
  } catch (e4) {
  }
}
function themeStylesString(colors2, colorSpace) {
  const styleProperties = processThemeColors(colors2, colorSpace);
  return entries(styleProperties).map(([key, value]) => {
    return `${key}: ${value};`;
  }).join("\n");
}
function createHeadSnippet(darkThemes) {
  const applyInitialStyle = `
  function applyInitialStyle(darkThemes) {
    let theme = localStorage.getItem('theme');
    // Ignore if no dark things registered (default 'dark' removed)
    if (darkThemes.length > 0) {
      if (theme) {
        document.documentElement.dataset.theme = theme;
        if (darkThemes.includes(theme)) {
          document.documentElement.classList.add('dark');
        }
      } else if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
        document.documentElement.classList.add('dark');
      }
    }
  }
  `;
  let darkThemeList = darkThemes.map((theme) => `'${theme}'`).join(", ");
  return `<script>${applyInitialStyle}([${darkThemeList}])<\/script>`;
}

// node_modules/@layerstack/tailwind/dist/utils.js
var twMerge = extendTailwindMerge({
  extend: {
    classGroups: {
      shadow: [
        "shadow-border-l",
        "shadow-border-r",
        "shadow-border-t",
        "shadow-border-b",
        "elevation-none",
        ...range(1, 25).map((x) => `elevation-${x}`)
      ]
    }
  }
});
var cls = (...inputs) => twMerge(clsx_default(...inputs));
var clsMerge = (...inputs) => mergeWith_default({}, ...inputs.filter(Boolean), (a, b) => twMerge(a, b));
var normalizeClasses = (classes) => {
  return classes && typeof classes === "object" ? classes : { root: classes };
};

export {
  semanticColors,
  stateColors,
  colors,
  shades,
  colorNames,
  getThemeNames,
  processThemeColors,
  convertColor,
  themeStylesString,
  createHeadSnippet,
  cls,
  clsMerge,
  normalizeClasses
};
//# sourceMappingURL=chunk-PY3PPNTO.js.map
