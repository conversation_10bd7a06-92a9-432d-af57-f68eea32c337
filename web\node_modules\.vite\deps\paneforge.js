import {
  Context,
  addEventListener,
  afterTick,
  box,
  executeCallbacks,
  mergeProps,
  useRefById,
  watch
} from "./chunk-5JNNSBY3.js";
import "./chunk-ISMEUBKR.js";
import "./chunk-6HTHKKYV.js";
import "./chunk-KPTXDKX7.js";
import "./chunk-7RQDXF5S.js";
import "./chunk-C5KNTEDU.js";
import "./chunk-MZKCMDML.js";
import {
  add_locations,
  check_target,
  hmr,
  if_block,
  legacy_api,
  onMount,
  prop,
  rest_props,
  set_attributes,
  snippet
} from "./chunk-CRCQ7E27.js";
import "./chunk-U7P2NEEE.js";
import {
  append,
  comment,
  template
} from "./chunk-OSNF6FE7.js";
import {
  FILENAME,
  HMR,
  child,
  equals,
  first_child,
  get,
  noop,
  on,
  pop,
  proxy,
  push,
  reset,
  set,
  snapshot,
  state,
  strict_equals,
  template_effect,
  untrack,
  user_derived,
  user_effect
} from "./chunk-PJ2X7CWE.js";
import "./chunk-VIZMNZTH.js";
import "./chunk-HNWPC2PS.js";
import "./chunk-RVAV4ZRS.js";
import {
  __privateAdd,
  __privateGet,
  __publicField
} from "./chunk-KWPVD4H7.js";

// node_modules/paneforge/dist/internal/utils/aria.js
function calculateAriaValues({ layout, panesArray, pivotIndices }) {
  let currentMinSize = 0;
  let currentMaxSize = 100;
  let totalMinSize = 0;
  let totalMaxSize = 0;
  const firstIndex = pivotIndices[0];
  for (let i = 0; i < panesArray.length; i++) {
    const constraints = panesArray[i].constraints;
    const { maxSize = 100, minSize = 0 } = constraints;
    if (i === firstIndex) {
      currentMinSize = minSize;
      currentMaxSize = maxSize;
    } else {
      totalMinSize += minSize;
      totalMaxSize += maxSize;
    }
  }
  const valueMax = Math.min(currentMaxSize, 100 - totalMinSize);
  const valueMin = Math.max(currentMinSize, 100 - totalMaxSize);
  const valueNow = layout[firstIndex];
  return {
    valueMax,
    valueMin,
    valueNow
  };
}

// node_modules/paneforge/dist/internal/utils/assert.js
function assert(expectedCondition, message = "Assertion failed!") {
  if (!expectedCondition) {
    console.error(message);
    throw new Error(message);
  }
}

// node_modules/paneforge/dist/internal/constants.js
var LOCAL_STORAGE_DEBOUNCE_INTERVAL = 100;
var PRECISION = 10;

// node_modules/paneforge/dist/internal/utils/compare.js
function areNumbersAlmostEqual(actual, expected, fractionDigits = PRECISION) {
  return compareNumbersWithTolerance(actual, expected, fractionDigits) === 0;
}
function compareNumbersWithTolerance(actual, expected, fractionDigits = PRECISION) {
  const roundedActual = roundTo(actual, fractionDigits);
  const roundedExpected = roundTo(expected, fractionDigits);
  return Math.sign(roundedActual - roundedExpected);
}
function areArraysEqual(arrA, arrB) {
  if (arrA.length !== arrB.length)
    return false;
  for (let index = 0; index < arrA.length; index++) {
    if (arrA[index] !== arrB[index])
      return false;
  }
  return true;
}
function roundTo(value, decimals) {
  return Number.parseFloat(value.toFixed(decimals));
}

// node_modules/paneforge/dist/internal/utils/is.js
var isBrowser = typeof document !== "undefined";
function isHTMLElement(element2) {
  return element2 instanceof HTMLElement;
}
function isKeyDown(event) {
  return event.type === "keydown";
}
function isMouseEvent(event) {
  return event.type.startsWith("mouse");
}
function isTouchEvent(event) {
  return event.type.startsWith("touch");
}

// node_modules/paneforge/dist/internal/utils/resize.js
function resizePane({ paneConstraints: paneConstraintsArray, paneIndex, initialSize }) {
  const paneConstraints = paneConstraintsArray[paneIndex];
  assert(paneConstraints != null, "Pane constraints should not be null.");
  const { collapsedSize = 0, collapsible, maxSize = 100, minSize = 0 } = paneConstraints;
  let newSize = initialSize;
  if (compareNumbersWithTolerance(newSize, minSize) < 0) {
    newSize = getAdjustedSizeForCollapsible(newSize, collapsible, collapsedSize, minSize);
  }
  newSize = Math.min(maxSize, newSize);
  return Number.parseFloat(newSize.toFixed(PRECISION));
}
function getAdjustedSizeForCollapsible(size, collapsible, collapsedSize, minSize) {
  if (!collapsible)
    return minSize;
  const halfwayPoint = (collapsedSize + minSize) / 2;
  return compareNumbersWithTolerance(size, halfwayPoint) < 0 ? collapsedSize : minSize;
}

// node_modules/paneforge/dist/internal/helpers.js
function noop2() {
}
function updateResizeHandleAriaValues({ groupId, layout, panesArray }) {
  const resizeHandleElements = getResizeHandleElementsForGroup(groupId);
  for (let index = 0; index < panesArray.length - 1; index++) {
    const { valueMax, valueMin, valueNow } = calculateAriaValues({
      layout,
      panesArray,
      pivotIndices: [index, index + 1]
    });
    const resizeHandleEl = resizeHandleElements[index];
    if (isHTMLElement(resizeHandleEl)) {
      const paneData = panesArray[index];
      resizeHandleEl.setAttribute("aria-controls", paneData.opts.id.current);
      resizeHandleEl.setAttribute("aria-valuemax", `${Math.round(valueMax)}`);
      resizeHandleEl.setAttribute("aria-valuemin", `${Math.round(valueMin)}`);
      resizeHandleEl.setAttribute("aria-valuenow", valueNow != null ? `${Math.round(valueNow)}` : "");
    }
  }
  return () => {
    resizeHandleElements.forEach((resizeHandleElement) => {
      resizeHandleElement.removeAttribute("aria-controls");
      resizeHandleElement.removeAttribute("aria-valuemax");
      resizeHandleElement.removeAttribute("aria-valuemin");
      resizeHandleElement.removeAttribute("aria-valuenow");
    });
  };
}
function getResizeHandleElementsForGroup(groupId) {
  if (!isBrowser)
    return [];
  return Array.from(document.querySelectorAll(`[data-pane-resizer-id][data-pane-group-id="${groupId}"]`));
}
function getResizeHandleElementIndex(groupId, id) {
  if (!isBrowser)
    return null;
  const handles = getResizeHandleElementsForGroup(groupId);
  const index = handles.findIndex((handle) => handle.getAttribute("data-pane-resizer-id") === id);
  return index ?? null;
}
function getPivotIndices(groupId, dragHandleId) {
  const index = getResizeHandleElementIndex(groupId, dragHandleId);
  return index != null ? [index, index + 1] : [-1, -1];
}
function paneDataHelper(panesArray, pane, layout) {
  const paneConstraintsArray = panesArray.map((paneData) => paneData.constraints);
  const paneIndex = findPaneDataIndex(panesArray, pane);
  const paneConstraints = paneConstraintsArray[paneIndex];
  const isLastPane = paneIndex === panesArray.length - 1;
  const pivotIndices = isLastPane ? [paneIndex - 1, paneIndex] : [paneIndex, paneIndex + 1];
  const paneSize = layout[paneIndex];
  return {
    ...paneConstraints,
    paneSize,
    pivotIndices
  };
}
function findPaneDataIndex(panesArray, pane) {
  return panesArray.findIndex((prevPaneData) => prevPaneData.opts.id.current === pane.opts.id.current);
}
function callPaneCallbacks(panesArray, layout, paneIdToLastNotifiedSizeMap) {
  for (let index = 0; index < layout.length; index++) {
    const size = layout[index];
    const paneData = panesArray[index];
    assert(paneData);
    const { collapsedSize = 0, collapsible } = paneData.constraints;
    const lastNotifiedSize = paneIdToLastNotifiedSizeMap[paneData.opts.id.current];
    if (!(lastNotifiedSize == null || size !== lastNotifiedSize))
      continue;
    paneIdToLastNotifiedSizeMap[paneData.opts.id.current] = size;
    const { onCollapse, onExpand, onResize } = paneData.callbacks;
    onResize == null ? void 0 : onResize(size, lastNotifiedSize);
    if (collapsible && (onCollapse || onExpand)) {
      if (onExpand && (lastNotifiedSize == null || lastNotifiedSize === collapsedSize) && size !== collapsedSize) {
        onExpand();
      }
      if (onCollapse && (lastNotifiedSize == null || lastNotifiedSize !== collapsedSize) && size === collapsedSize) {
        onCollapse();
      }
    }
  }
}
function getUnsafeDefaultLayout({ panesArray }) {
  const layout = Array(panesArray.length);
  const paneConstraintsArray = panesArray.map((paneData) => paneData.constraints);
  let numPanesWithSizes = 0;
  let remainingSize = 100;
  for (let index = 0; index < panesArray.length; index++) {
    const paneConstraints = paneConstraintsArray[index];
    assert(paneConstraints);
    const { defaultSize } = paneConstraints;
    if (defaultSize != null) {
      numPanesWithSizes++;
      layout[index] = defaultSize;
      remainingSize -= defaultSize;
    }
  }
  for (let index = 0; index < panesArray.length; index++) {
    const paneConstraints = paneConstraintsArray[index];
    assert(paneConstraints);
    const { defaultSize } = paneConstraints;
    if (defaultSize != null) {
      continue;
    }
    const numRemainingPanes = panesArray.length - numPanesWithSizes;
    const size = remainingSize / numRemainingPanes;
    numPanesWithSizes++;
    layout[index] = size;
    remainingSize -= size;
  }
  return layout;
}
function validatePaneGroupLayout({ layout: prevLayout, paneConstraints }) {
  const nextLayout = [...prevLayout];
  const nextLayoutTotalSize = nextLayout.reduce((accumulated, current) => accumulated + current, 0);
  if (nextLayout.length !== paneConstraints.length) {
    throw new Error(`Invalid ${paneConstraints.length} pane layout: ${nextLayout.map((size) => `${size}%`).join(", ")}`);
  } else if (!areNumbersAlmostEqual(nextLayoutTotalSize, 100)) {
    for (let index = 0; index < paneConstraints.length; index++) {
      const unsafeSize = nextLayout[index];
      assert(unsafeSize != null);
      const safeSize = 100 / nextLayoutTotalSize * unsafeSize;
      nextLayout[index] = safeSize;
    }
  }
  let remainingSize = 0;
  for (let index = 0; index < paneConstraints.length; index++) {
    const unsafeSize = nextLayout[index];
    assert(unsafeSize != null);
    const safeSize = resizePane({
      paneConstraints,
      paneIndex: index,
      initialSize: unsafeSize
    });
    if (unsafeSize !== safeSize) {
      remainingSize += unsafeSize - safeSize;
      nextLayout[index] = safeSize;
    }
  }
  if (!areNumbersAlmostEqual(remainingSize, 0)) {
    for (let index = 0; index < paneConstraints.length; index++) {
      const prevSize = nextLayout[index];
      assert(prevSize != null);
      const unsafeSize = prevSize + remainingSize;
      const safeSize = resizePane({
        paneConstraints,
        paneIndex: index,
        initialSize: unsafeSize
      });
      if (prevSize !== safeSize) {
        remainingSize -= safeSize - prevSize;
        nextLayout[index] = safeSize;
        if (areNumbersAlmostEqual(remainingSize, 0)) {
          break;
        }
      }
    }
  }
  return nextLayout;
}
function getPaneGroupElement(id) {
  if (!isBrowser)
    return null;
  const element2 = document.querySelector(`[data-pane-group][data-pane-group-id="${id}"]`);
  if (element2) {
    return element2;
  }
  return null;
}
function getResizeHandleElement(id) {
  if (!isBrowser)
    return null;
  const element2 = document.querySelector(`[data-pane-resizer-id="${id}"]`);
  if (element2) {
    return element2;
  }
  return null;
}
function getDragOffsetPercentage(e, dragHandleId, dir, initialDragState) {
  const isHorizontal = dir === "horizontal";
  const handleElement = getResizeHandleElement(dragHandleId);
  assert(handleElement);
  const groupId = handleElement.getAttribute("data-pane-group-id");
  assert(groupId);
  const { initialCursorPosition } = initialDragState;
  const cursorPosition = getResizeEventCursorPosition(dir, e);
  const groupElement = getPaneGroupElement(groupId);
  assert(groupElement);
  const groupRect = groupElement.getBoundingClientRect();
  const groupSizeInPixels = isHorizontal ? groupRect.width : groupRect.height;
  const offsetPixels = cursorPosition - initialCursorPosition;
  const offsetPercentage = offsetPixels / groupSizeInPixels * 100;
  return offsetPercentage;
}
function getDeltaPercentage(e, dragHandleId, dir, initialDragState, keyboardResizeBy) {
  if (isKeyDown(e)) {
    const isHorizontal = dir === "horizontal";
    let delta = 0;
    if (e.shiftKey) {
      delta = 100;
    } else if (keyboardResizeBy != null) {
      delta = keyboardResizeBy;
    } else {
      delta = 10;
    }
    let movement = 0;
    switch (e.key) {
      case "ArrowDown":
        movement = isHorizontal ? 0 : delta;
        break;
      case "ArrowLeft":
        movement = isHorizontal ? -delta : 0;
        break;
      case "ArrowRight":
        movement = isHorizontal ? delta : 0;
        break;
      case "ArrowUp":
        movement = isHorizontal ? 0 : -delta;
        break;
      case "End":
        movement = 100;
        break;
      case "Home":
        movement = -100;
        break;
    }
    return movement;
  } else {
    if (initialDragState == null)
      return 0;
    return getDragOffsetPercentage(e, dragHandleId, dir, initialDragState);
  }
}
function getResizeEventCursorPosition(dir, e) {
  const isHorizontal = dir === "horizontal";
  if (isMouseEvent(e)) {
    return isHorizontal ? e.clientX : e.clientY;
  } else if (isTouchEvent(e)) {
    const firstTouch = e.touches[0];
    assert(firstTouch);
    return isHorizontal ? firstTouch.screenX : firstTouch.screenY;
  } else {
    throw new Error(`Unsupported event type "${e.type}"`);
  }
}
function getResizeHandlePaneIds(groupId, handleId, panesArray) {
  var _a, _b;
  const handle = getResizeHandleElement(handleId);
  const handles = getResizeHandleElementsForGroup(groupId);
  const index = handle ? handles.indexOf(handle) : -1;
  const idBefore = ((_a = panesArray[index]) == null ? void 0 : _a.opts.id.current) ?? null;
  const idAfter = ((_b = panesArray[index + 1]) == null ? void 0 : _b.opts.id.current) ?? null;
  return [idBefore, idAfter];
}

// node_modules/paneforge/dist/internal/utils/useId.js
var count = 0;
function useId(prefix = "paneforge") {
  count++;
  return `${prefix}-${count}`;
}

// node_modules/paneforge/dist/internal/utils/adjust-layout.js
function adjustLayoutByDelta({ delta, layout: prevLayout, paneConstraints: paneConstraintsArray, pivotIndices, trigger }) {
  if (areNumbersAlmostEqual(delta, 0))
    return prevLayout;
  const nextLayout = [...prevLayout];
  const [firstPivotIndex, secondPivotIndex] = pivotIndices;
  let deltaApplied = 0;
  {
    if (trigger === "keyboard") {
      {
        const index = delta < 0 ? secondPivotIndex : firstPivotIndex;
        const paneConstraints = paneConstraintsArray[index];
        assert(paneConstraints);
        if (paneConstraints.collapsible) {
          const prevSize = prevLayout[index];
          assert(prevSize != null);
          const paneConstraints2 = paneConstraintsArray[index];
          assert(paneConstraints2);
          const { collapsedSize = 0, minSize = 0 } = paneConstraints2;
          if (areNumbersAlmostEqual(prevSize, collapsedSize)) {
            const localDelta = minSize - prevSize;
            if (compareNumbersWithTolerance(localDelta, Math.abs(delta)) > 0) {
              delta = delta < 0 ? 0 - localDelta : localDelta;
            }
          }
        }
      }
      {
        const index = delta < 0 ? firstPivotIndex : secondPivotIndex;
        const paneConstraints = paneConstraintsArray[index];
        assert(paneConstraints);
        const { collapsible } = paneConstraints;
        if (collapsible) {
          const prevSize = prevLayout[index];
          assert(prevSize != null);
          const paneConstraints2 = paneConstraintsArray[index];
          assert(paneConstraints2);
          const { collapsedSize = 0, minSize = 0 } = paneConstraints2;
          if (areNumbersAlmostEqual(prevSize, minSize)) {
            const localDelta = prevSize - collapsedSize;
            if (compareNumbersWithTolerance(localDelta, Math.abs(delta)) > 0) {
              delta = delta < 0 ? 0 - localDelta : localDelta;
            }
          }
        }
      }
    }
  }
  {
    const increment = delta < 0 ? 1 : -1;
    let index = delta < 0 ? secondPivotIndex : firstPivotIndex;
    let maxAvailableDelta = 0;
    while (true) {
      const prevSize = prevLayout[index];
      assert(prevSize != null);
      const maxSafeSize = resizePane({
        paneConstraints: paneConstraintsArray,
        paneIndex: index,
        initialSize: 100
      });
      const delta2 = maxSafeSize - prevSize;
      maxAvailableDelta += delta2;
      index += increment;
      if (index < 0 || index >= paneConstraintsArray.length) {
        break;
      }
    }
    const minAbsDelta = Math.min(Math.abs(delta), Math.abs(maxAvailableDelta));
    delta = delta < 0 ? 0 - minAbsDelta : minAbsDelta;
  }
  {
    const pivotIndex = delta < 0 ? firstPivotIndex : secondPivotIndex;
    let index = pivotIndex;
    while (index >= 0 && index < paneConstraintsArray.length) {
      const deltaRemaining = Math.abs(delta) - Math.abs(deltaApplied);
      const prevSize = prevLayout[index];
      assert(prevSize != null);
      const unsafeSize = prevSize - deltaRemaining;
      const safeSize = resizePane({
        paneConstraints: paneConstraintsArray,
        paneIndex: index,
        initialSize: unsafeSize
      });
      if (!areNumbersAlmostEqual(prevSize, safeSize)) {
        deltaApplied += prevSize - safeSize;
        nextLayout[index] = safeSize;
        if (deltaApplied.toPrecision(3).localeCompare(Math.abs(delta).toPrecision(3), void 0, {
          numeric: true
        }) >= 0) {
          break;
        }
      }
      if (delta < 0) {
        index--;
      } else {
        index++;
      }
    }
  }
  if (areNumbersAlmostEqual(deltaApplied, 0)) {
    return prevLayout;
  }
  {
    const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;
    const prevSize = prevLayout[pivotIndex];
    assert(prevSize != null);
    const unsafeSize = prevSize + deltaApplied;
    const safeSize = resizePane({
      paneConstraints: paneConstraintsArray,
      paneIndex: pivotIndex,
      initialSize: unsafeSize
    });
    nextLayout[pivotIndex] = safeSize;
    if (!areNumbersAlmostEqual(safeSize, unsafeSize)) {
      let deltaRemaining = unsafeSize - safeSize;
      const pivotIndex2 = delta < 0 ? secondPivotIndex : firstPivotIndex;
      let index = pivotIndex2;
      while (index >= 0 && index < paneConstraintsArray.length) {
        const prevSize2 = nextLayout[index];
        assert(prevSize2 != null);
        const unsafeSize2 = prevSize2 + deltaRemaining;
        const safeSize2 = resizePane({
          paneConstraints: paneConstraintsArray,
          paneIndex: index,
          initialSize: unsafeSize2
        });
        if (!areNumbersAlmostEqual(prevSize2, safeSize2)) {
          deltaRemaining -= safeSize2 - prevSize2;
          nextLayout[index] = safeSize2;
        }
        if (areNumbersAlmostEqual(deltaRemaining, 0))
          break;
        delta > 0 ? index-- : index++;
      }
    }
  }
  const totalSize = nextLayout.reduce((total, size) => size + total, 0);
  if (!areNumbersAlmostEqual(totalSize, 100))
    return prevLayout;
  return nextLayout;
}

// node_modules/paneforge/dist/internal/utils/style.js
var currentState = null;
var element = null;
function getCursorStyle(state2) {
  switch (state2) {
    case "horizontal":
      return "ew-resize";
    case "horizontal-max":
      return "w-resize";
    case "horizontal-min":
      return "e-resize";
    case "vertical":
      return "ns-resize";
    case "vertical-max":
      return "n-resize";
    case "vertical-min":
      return "s-resize";
  }
}
function resetGlobalCursorStyle() {
  if (element === null)
    return;
  document.head.removeChild(element);
  currentState = null;
  element = null;
}
function setGlobalCursorStyle(state2) {
  if (currentState === state2)
    return;
  currentState = state2;
  const style = getCursorStyle(state2);
  if (element === null) {
    element = document.createElement("style");
    document.head.appendChild(element);
  }
  element.innerHTML = `*{cursor: ${style}!important;}`;
}
function computePaneFlexBoxStyle({ defaultSize, dragState, layout, panesArray, paneIndex, precision = 3 }) {
  const size = layout[paneIndex];
  let flexGrow;
  if (size == null) {
    flexGrow = defaultSize ?? "1";
  } else if (panesArray.length === 1) {
    flexGrow = "1";
  } else {
    flexGrow = size.toPrecision(precision);
  }
  return {
    flexBasis: 0,
    flexGrow,
    flexShrink: 1,
    // Without this, pane sizes may be unintentionally overridden by their content
    overflow: "hidden",
    // Disable pointer events inside of a pane during resize
    // This avoid edge cases like nested iframes
    pointerEvents: dragState !== null ? "none" : void 0
  };
}

// node_modules/paneforge/dist/internal/utils/storage.js
function initializeStorage(storageObject) {
  try {
    if (typeof localStorage === "undefined") {
      throw new TypeError("localStorage is not supported in this environment");
    }
    storageObject.getItem = (name) => localStorage.getItem(name);
    storageObject.setItem = (name, value) => localStorage.setItem(name, value);
  } catch (err) {
    console.error(err);
    storageObject.getItem = () => null;
    storageObject.setItem = () => {
    };
  }
}
function getPaneGroupKey(autoSaveId) {
  return `paneforge:${autoSaveId}`;
}
function getPaneKey(panes) {
  const sortedPaneIds = panes.map((pane) => {
    return pane.opts.order.current ? `${pane.opts.order.current}:${JSON.stringify(pane.constraints)}` : JSON.stringify(pane.constraints);
  }).sort().join(",");
  return sortedPaneIds;
}
function loadSerializedPaneGroupState(autoSaveId, storage) {
  try {
    const paneGroupKey = getPaneGroupKey(autoSaveId);
    const serialized = storage.getItem(paneGroupKey);
    const parsed = JSON.parse(serialized || "");
    if (typeof parsed === "object" && parsed !== null) {
      return parsed;
    }
  } catch {
  }
  return null;
}
function loadPaneGroupState(autoSaveId, panesArray, storage) {
  const state2 = loadSerializedPaneGroupState(autoSaveId, storage) || {};
  const paneKey = getPaneKey(panesArray);
  return state2[paneKey] || null;
}
function savePaneGroupState(autoSaveId, panesArray, paneSizesBeforeCollapse, sizes, storage) {
  const paneGroupKey = getPaneGroupKey(autoSaveId);
  const paneKey = getPaneKey(panesArray);
  const state2 = loadSerializedPaneGroupState(autoSaveId, storage) || {};
  state2[paneKey] = {
    expandToSizes: Object.fromEntries(paneSizesBeforeCollapse.entries()),
    layout: sizes
  };
  try {
    storage.setItem(paneGroupKey, JSON.stringify(state2));
  } catch (error) {
    console.error(error);
  }
}
var debounceMap = {};
function debounce(callback, durationMs = 10) {
  let timeoutId = null;
  const callable = (...args) => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      callback(...args);
    }, durationMs);
  };
  return callable;
}
function updateStorageValues({ autoSaveId, layout, storage, panesArray, paneSizeBeforeCollapse }) {
  if (layout.length === 0 || layout.length !== panesArray.length)
    return;
  let debouncedSave = debounceMap[autoSaveId];
  if (debouncedSave == null) {
    debouncedSave = debounce(savePaneGroupState, LOCAL_STORAGE_DEBOUNCE_INTERVAL);
    debounceMap[autoSaveId] = debouncedSave;
  }
  const clonedPanesArray = [...panesArray];
  const clonedPaneSizesBeforeCollapse = new Map(paneSizeBeforeCollapse);
  debouncedSave(autoSaveId, clonedPanesArray, clonedPaneSizesBeforeCollapse, layout, storage);
}

// node_modules/paneforge/dist/paneforge.svelte.js
var defaultStorage = {
  getItem: (name) => {
    initializeStorage(defaultStorage);
    return defaultStorage.getItem(name);
  },
  setItem: (name, value) => {
    initializeStorage(defaultStorage);
    defaultStorage.setItem(name, value);
  }
};
var _dragState, _layout, _panesArray, _panesArrayChanged, _paneIdToLastNotifiedSizeMap, _prevDelta, _setResizeHandlerEventListeners, _props;
var PaneGroupState = class {
  constructor(opts) {
    __publicField(this, "opts");
    __privateAdd(this, _dragState, state(null));
    __privateAdd(this, _layout, state([]));
    __privateAdd(this, _panesArray, state([]));
    __privateAdd(this, _panesArrayChanged, state(false));
    __privateAdd(this, _paneIdToLastNotifiedSizeMap, state(proxy({})));
    __publicField(this, "paneSizeBeforeCollapseMap", /* @__PURE__ */ new Map());
    __privateAdd(this, _prevDelta, state(0));
    __publicField(this, "setLayout", (newLayout) => {
      this.layout = newLayout;
    });
    __publicField(this, "registerResizeHandle", (dragHandleId) => {
      return (e) => {
        var _a, _b;
        e.preventDefault();
        const direction = this.opts.direction.current;
        const dragState = this.dragState;
        const groupId = this.opts.id.current;
        const keyboardResizeBy = this.opts.keyboardResizeBy.current;
        const prevLayout = this.layout;
        const paneDataArray = this.panesArray;
        const { initialLayout } = dragState ?? {};
        const pivotIndices = getPivotIndices(groupId, dragHandleId);
        let delta = getDeltaPercentage(e, dragHandleId, direction, dragState, keyboardResizeBy);
        if (strict_equals(delta, 0)) return;
        const isHorizontal = strict_equals(direction, "horizontal");
        if (strict_equals(document.dir, "rtl") && isHorizontal) {
          delta = -delta;
        }
        const paneConstraints = paneDataArray.map((paneData) => paneData.constraints);
        const nextLayout = adjustLayoutByDelta({
          delta,
          layout: initialLayout ?? prevLayout,
          paneConstraints,
          pivotIndices,
          trigger: isKeyDown(e) ? "keyboard" : "mouse-or-touch"
        });
        const layoutChanged = !areArraysEqual(prevLayout, nextLayout);
        if (isMouseEvent(e) || isTouchEvent(e)) {
          const prevDelta = this.prevDelta;
          if (strict_equals(prevDelta, delta, false)) {
            this.prevDelta = delta;
            if (!layoutChanged) {
              if (isHorizontal) {
                setGlobalCursorStyle(delta < 0 ? "horizontal-min" : "horizontal-max");
              } else {
                setGlobalCursorStyle(delta < 0 ? "vertical-min" : "vertical-max");
              }
            } else {
              setGlobalCursorStyle(isHorizontal ? "horizontal" : "vertical");
            }
          }
        }
        if (layoutChanged) {
          this.setLayout(nextLayout);
          (_b = (_a = this.opts.onLayout).current) == null ? void 0 : _b.call(_a, nextLayout);
          callPaneCallbacks(paneDataArray, nextLayout, this.paneIdToLastNotifiedSizeMap);
        }
      };
    });
    __publicField(this, "resizePane", (paneState, unsafePaneSize) => {
      var _a, _b;
      const prevLayout = this.layout;
      const panesArray = this.panesArray;
      const paneConstraintsArr = panesArray.map((paneData) => paneData.constraints);
      const { paneSize, pivotIndices } = paneDataHelper(panesArray, paneState, prevLayout);
      assert(equals(paneSize, null, false));
      const isLastPane = strict_equals(findPaneDataIndex(panesArray, paneState), panesArray.length - 1);
      const delta = isLastPane ? paneSize - unsafePaneSize : unsafePaneSize - paneSize;
      const nextLayout = adjustLayoutByDelta({
        delta,
        layout: prevLayout,
        paneConstraints: paneConstraintsArr,
        pivotIndices,
        trigger: "imperative-api"
      });
      if (areArraysEqual(prevLayout, nextLayout)) return;
      this.setLayout(nextLayout);
      (_b = (_a = this.opts.onLayout).current) == null ? void 0 : _b.call(_a, nextLayout);
      callPaneCallbacks(panesArray, nextLayout, this.paneIdToLastNotifiedSizeMap);
    });
    __publicField(this, "startDragging", (dragHandleId, e) => {
      const direction = this.opts.direction.current;
      const layout = this.layout;
      const handleElement = getResizeHandleElement(dragHandleId);
      assert(handleElement);
      const initialCursorPosition = getResizeEventCursorPosition(direction, e);
      this.dragState = {
        dragHandleId,
        dragHandleRect: handleElement.getBoundingClientRect(),
        initialCursorPosition,
        initialLayout: layout
      };
    });
    __publicField(this, "stopDragging", () => {
      resetGlobalCursorStyle();
      this.dragState = null;
    });
    __publicField(this, "isPaneCollapsed", (pane) => {
      const paneDataArray = this.panesArray;
      const layout = this.layout;
      const { collapsedSize = 0, collapsible, paneSize } = paneDataHelper(paneDataArray, pane, layout);
      return strict_equals(collapsible, true) && strict_equals(paneSize, collapsedSize);
    });
    __publicField(this, "expandPane", (pane) => {
      var _a, _b;
      const prevLayout = this.layout;
      const paneDataArray = this.panesArray;
      if (!pane.constraints.collapsible) return;
      const paneConstraintsArray = paneDataArray.map((paneData) => paneData.constraints);
      const {
        collapsedSize = 0,
        paneSize,
        minSize = 0,
        pivotIndices
      } = paneDataHelper(paneDataArray, pane, prevLayout);
      if (strict_equals(paneSize, collapsedSize, false)) return;
      const prevPaneSize = this.paneSizeBeforeCollapseMap.get(pane.opts.id.current);
      const baseSize = equals(prevPaneSize, null, false) && prevPaneSize >= minSize ? prevPaneSize : minSize;
      const isLastPane = strict_equals(findPaneDataIndex(paneDataArray, pane), paneDataArray.length - 1);
      const delta = isLastPane ? paneSize - baseSize : baseSize - paneSize;
      const nextLayout = adjustLayoutByDelta({
        delta,
        layout: prevLayout,
        paneConstraints: paneConstraintsArray,
        pivotIndices,
        trigger: "imperative-api"
      });
      if (areArraysEqual(prevLayout, nextLayout)) return;
      this.setLayout(nextLayout);
      (_b = (_a = this.opts.onLayout).current) == null ? void 0 : _b.call(_a, nextLayout);
      callPaneCallbacks(paneDataArray, nextLayout, this.paneIdToLastNotifiedSizeMap);
    });
    __publicField(this, "collapsePane", (pane) => {
      var _a, _b;
      const prevLayout = this.layout;
      const paneDataArray = this.panesArray;
      if (!pane.constraints.collapsible) return;
      const paneConstraintsArray = paneDataArray.map((paneData) => paneData.constraints);
      const { collapsedSize = 0, paneSize, pivotIndices } = paneDataHelper(paneDataArray, pane, prevLayout);
      assert(equals(paneSize, null, false));
      if (strict_equals(paneSize, collapsedSize)) return;
      this.paneSizeBeforeCollapseMap.set(pane.opts.id.current, paneSize);
      const isLastPane = strict_equals(findPaneDataIndex(paneDataArray, pane), paneDataArray.length - 1);
      const delta = isLastPane ? paneSize - collapsedSize : collapsedSize - paneSize;
      const nextLayout = adjustLayoutByDelta({
        delta,
        layout: prevLayout,
        paneConstraints: paneConstraintsArray,
        pivotIndices,
        trigger: "imperative-api"
      });
      if (areArraysEqual(prevLayout, nextLayout)) return;
      this.layout = nextLayout;
      (_b = (_a = this.opts.onLayout).current) == null ? void 0 : _b.call(_a, nextLayout);
      callPaneCallbacks(paneDataArray, nextLayout, this.paneIdToLastNotifiedSizeMap);
    });
    __publicField(this, "getPaneSize", (pane) => {
      return paneDataHelper(this.panesArray, pane, this.layout).paneSize;
    });
    __publicField(this, "getPaneStyle", (pane, defaultSize) => {
      const paneDataArray = this.panesArray;
      const layout = this.layout;
      const dragState = this.dragState;
      const paneIndex = findPaneDataIndex(paneDataArray, pane);
      return computePaneFlexBoxStyle({
        defaultSize,
        dragState,
        layout,
        panesArray: paneDataArray,
        paneIndex
      });
    });
    __publicField(this, "isPaneExpanded", (pane) => {
      const { collapsedSize = 0, collapsible, paneSize } = paneDataHelper(this.panesArray, pane, this.layout);
      return !collapsible || paneSize > collapsedSize;
    });
    __publicField(this, "registerPane", (pane) => {
      const newPaneDataArray = [...this.panesArray, pane];
      newPaneDataArray.sort((paneA, paneB) => {
        const orderA = paneA.opts.order.current;
        const orderB = paneB.opts.order.current;
        if (equals(orderA, null) && equals(orderB, null)) {
          return 0;
        } else if (equals(orderA, null)) {
          return -1;
        } else if (equals(orderB, null)) {
          return 1;
        } else {
          return orderA - orderB;
        }
      });
      this.panesArray = newPaneDataArray;
      this.panesArrayChanged = true;
      return () => {
        const paneDataArray = [...this.panesArray];
        const index = findPaneDataIndex(this.panesArray, pane);
        if (index < 0) return;
        paneDataArray.splice(index, 1);
        this.panesArray = paneDataArray;
        delete this.paneIdToLastNotifiedSizeMap[pane.opts.id.current];
        this.panesArrayChanged = true;
      };
    });
    __privateAdd(this, _setResizeHandlerEventListeners, () => {
      const groupId = this.opts.id.current;
      const handles = getResizeHandleElementsForGroup(groupId);
      const paneDataArray = this.panesArray;
      const unsubHandlers = handles.map((handle) => {
        const handleId = handle.getAttribute("data-pane-resizer-id");
        if (!handleId) return noop2;
        const [idBefore, idAfter] = getResizeHandlePaneIds(groupId, handleId, paneDataArray);
        if (equals(idBefore, null) || equals(idAfter, null)) return noop2;
        const onKeydown = (e) => {
          if (e.defaultPrevented || strict_equals(e.key, "Enter", false)) return;
          e.preventDefault();
          const paneDataArray2 = this.panesArray;
          const index = paneDataArray2.findIndex((paneData2) => strict_equals(paneData2.opts.id.current, idBefore));
          if (index < 0) return;
          const paneData = paneDataArray2[index];
          assert(paneData);
          const layout = this.layout;
          const size = layout[index];
          const { collapsedSize = 0, collapsible, minSize = 0 } = paneData.constraints;
          if (!(equals(size, null, false) && collapsible)) return;
          const nextLayout = adjustLayoutByDelta({
            delta: areNumbersAlmostEqual(size, collapsedSize) ? minSize - size : collapsedSize - size,
            layout,
            paneConstraints: paneDataArray2.map((paneData2) => paneData2.constraints),
            pivotIndices: getPivotIndices(groupId, handleId),
            trigger: "keyboard"
          });
          if (strict_equals(layout, nextLayout, false)) {
            this.layout = nextLayout;
          }
        };
        const unsubListener = addEventListener(handle, "keydown", onKeydown);
        return () => {
          unsubListener();
        };
      });
      return () => {
        for (const unsub of unsubHandlers) {
          unsub();
        }
      };
    });
    __privateAdd(this, _props, user_derived(() => ({
      id: this.opts.id.current,
      "data-pane-group": "",
      "data-direction": this.opts.direction.current,
      "data-pane-group-id": this.opts.id.current,
      style: {
        display: "flex",
        flexDirection: strict_equals(this.opts.direction.current, "horizontal") ? "row" : "column",
        height: "100%",
        overflow: "hidden",
        width: "100%"
      }
    })));
    this.opts = opts;
    useRefById(opts);
    watch(
      [
        () => this.opts.id.current,
        () => this.layout,
        () => this.panesArray
      ],
      () => {
        return updateResizeHandleAriaValues({
          groupId: this.opts.id.current,
          layout: this.layout,
          panesArray: this.panesArray
        });
      }
    );
    user_effect(() => {
      return untrack(() => {
        return __privateGet(this, _setResizeHandlerEventListeners).call(this);
      });
    });
    watch(
      [
        () => this.opts.autoSaveId.current,
        () => this.layout,
        () => this.opts.storage.current
      ],
      () => {
        if (!this.opts.autoSaveId.current) return;
        updateStorageValues({
          autoSaveId: this.opts.autoSaveId.current,
          layout: this.layout,
          storage: this.opts.storage.current,
          panesArray: this.panesArray,
          paneSizeBeforeCollapse: this.paneSizeBeforeCollapseMap
        });
      }
    );
    watch(() => this.panesArrayChanged, () => {
      var _a, _b;
      if (!this.panesArrayChanged) return;
      this.panesArrayChanged = false;
      const prevLayout = this.layout;
      let unsafeLayout = null;
      if (this.opts.autoSaveId.current) {
        const state2 = loadPaneGroupState(this.opts.autoSaveId.current, this.panesArray, this.opts.storage.current);
        if (state2) {
          this.paneSizeBeforeCollapseMap = new Map(Object.entries(state2.expandToSizes));
          unsafeLayout = state2.layout;
        }
      }
      if (equals(unsafeLayout, null)) {
        unsafeLayout = getUnsafeDefaultLayout({ panesArray: this.panesArray });
      }
      const nextLayout = validatePaneGroupLayout({
        layout: unsafeLayout,
        paneConstraints: this.panesArray.map((paneData) => paneData.constraints)
      });
      if (areArraysEqual(prevLayout, nextLayout)) return;
      this.layout = nextLayout;
      (_b = (_a = this.opts.onLayout).current) == null ? void 0 : _b.call(_a, nextLayout);
      callPaneCallbacks(this.panesArray, nextLayout, this.paneIdToLastNotifiedSizeMap);
    });
  }
  get dragState() {
    return get(__privateGet(this, _dragState));
  }
  set dragState(value) {
    set(__privateGet(this, _dragState), value);
  }
  get layout() {
    return get(__privateGet(this, _layout));
  }
  set layout(value) {
    set(__privateGet(this, _layout), value);
  }
  get panesArray() {
    return get(__privateGet(this, _panesArray));
  }
  set panesArray(value) {
    set(__privateGet(this, _panesArray), value);
  }
  get panesArrayChanged() {
    return get(__privateGet(this, _panesArrayChanged));
  }
  set panesArrayChanged(value) {
    set(__privateGet(this, _panesArrayChanged), value, true);
  }
  get paneIdToLastNotifiedSizeMap() {
    return get(__privateGet(this, _paneIdToLastNotifiedSizeMap));
  }
  set paneIdToLastNotifiedSizeMap(value) {
    set(__privateGet(this, _paneIdToLastNotifiedSizeMap), value, true);
  }
  get prevDelta() {
    return get(__privateGet(this, _prevDelta));
  }
  set prevDelta(value) {
    set(__privateGet(this, _prevDelta), value, true);
  }
  get props() {
    return get(__privateGet(this, _props));
  }
  set props(value) {
    set(__privateGet(this, _props), value);
  }
};
_dragState = new WeakMap();
_layout = new WeakMap();
_panesArray = new WeakMap();
_panesArrayChanged = new WeakMap();
_paneIdToLastNotifiedSizeMap = new WeakMap();
_prevDelta = new WeakMap();
_setResizeHandlerEventListeners = new WeakMap();
_props = new WeakMap();
var resizeKeys = [
  "ArrowDown",
  "ArrowLeft",
  "ArrowRight",
  "ArrowUp",
  "End",
  "Home"
];
var _isDragging, _isFocused, _startDragging, _stopDraggingAndBlur, _onkeydown, _onblur, _onfocus, _onmousedown, _onmouseup, _ontouchcancel, _ontouchend, _ontouchstart, _props2;
var PaneResizerState = class {
  constructor(opts, group) {
    __publicField(this, "opts");
    __publicField(this, "group");
    __privateAdd(this, _isDragging, user_derived(() => {
      var _a;
      return strict_equals((_a = this.group.dragState) == null ? void 0 : _a.dragHandleId, this.opts.id.current);
    }));
    __privateAdd(this, _isFocused, state(false));
    __publicField(this, "resizeHandler", null);
    __privateAdd(this, _startDragging, (e) => {
      e.preventDefault();
      if (this.opts.disabled.current) return;
      this.group.startDragging(this.opts.id.current, e);
      this.opts.onDraggingChange.current(true);
    });
    __privateAdd(this, _stopDraggingAndBlur, () => {
      const node = this.opts.ref.current;
      if (!node) return;
      node.blur();
      this.group.stopDragging();
      this.opts.onDraggingChange.current(false);
    });
    __privateAdd(this, _onkeydown, (e) => {
      if (this.opts.disabled.current || !this.resizeHandler || e.defaultPrevented) return;
      if (resizeKeys.includes(e.key)) {
        e.preventDefault();
        this.resizeHandler(e);
        return;
      }
      if (strict_equals(e.key, "F6", false)) return;
      e.preventDefault();
      const handles = getResizeHandleElementsForGroup(this.group.opts.id.current);
      const index = getResizeHandleElementIndex(this.group.opts.id.current, this.opts.id.current);
      if (strict_equals(index, null)) return;
      let nextIndex = 0;
      if (e.shiftKey) {
        if (index > 0) {
          nextIndex = index - 1;
        } else {
          nextIndex = handles.length - 1;
        }
      } else {
        if (index + 1 < handles.length) {
          nextIndex = index + 1;
        } else {
          nextIndex = 0;
        }
      }
      const nextHandle = handles[nextIndex];
      nextHandle.focus();
    });
    __privateAdd(this, _onblur, () => {
      set(__privateGet(this, _isFocused), false);
    });
    __privateAdd(this, _onfocus, () => {
      set(__privateGet(this, _isFocused), true);
    });
    __privateAdd(this, _onmousedown, (e) => {
      __privateGet(this, _startDragging).call(this, e);
    });
    __privateAdd(this, _onmouseup, () => {
      __privateGet(this, _stopDraggingAndBlur).call(this);
    });
    __privateAdd(this, _ontouchcancel, () => {
      __privateGet(this, _stopDraggingAndBlur).call(this);
    });
    __privateAdd(this, _ontouchend, () => {
      __privateGet(this, _stopDraggingAndBlur).call(this);
    });
    __privateAdd(this, _ontouchstart, (e) => {
      __privateGet(this, _startDragging).call(this, e);
    });
    __privateAdd(this, _props2, user_derived(() => ({
      id: this.opts.id.current,
      role: "separator",
      "data-direction": this.group.opts.direction.current,
      "data-pane-group-id": this.group.opts.id.current,
      "data-active": get(__privateGet(this, _isDragging)) ? "pointer" : get(__privateGet(this, _isFocused)) ? "keyboard" : void 0,
      "data-enabled": !this.opts.disabled.current,
      "data-pane-resizer-id": this.opts.id.current,
      "data-pane-resizer": "",
      tabIndex: this.opts.tabIndex.current,
      style: {
        cursor: getCursorStyle(this.group.opts.direction.current),
        touchAction: "none",
        userSelect: "none",
        "-webkit-user-select": "none",
        "-webkit-touch-callout": "none"
      },
      onkeydown: __privateGet(this, _onkeydown),
      onblur: __privateGet(this, _onblur),
      onfocus: __privateGet(this, _onfocus),
      onmousedown: __privateGet(this, _onmousedown),
      onmouseup: __privateGet(this, _onmouseup),
      ontouchcancel: __privateGet(this, _ontouchcancel),
      ontouchend: __privateGet(this, _ontouchend),
      ontouchstart: __privateGet(this, _ontouchstart)
    })));
    this.opts = opts;
    this.group = group;
    useRefById(opts);
    user_effect(() => {
      if (this.opts.disabled.current) {
        this.resizeHandler = null;
      } else {
        this.resizeHandler = this.group.registerResizeHandle(this.opts.id.current);
      }
    });
    user_effect(() => {
      const node = this.opts.ref.current;
      if (!node) return;
      const disabled = this.opts.disabled.current;
      const resizeHandler = this.resizeHandler;
      const isDragging = get(__privateGet(this, _isDragging));
      if (disabled || strict_equals(resizeHandler, null) || !isDragging) return;
      const onMove = (e) => {
        resizeHandler(e);
      };
      const onMouseLeave = (e) => {
        resizeHandler(e);
      };
      const stopDraggingAndBlur = () => {
        node.blur();
        this.group.stopDragging();
        this.opts.onDraggingChange.current(false);
      };
      return executeCallbacks(on(document.body, "contextmenu", stopDraggingAndBlur), on(document.body, "mousemove", onMove), on(document.body, "touchmove", onMove, { passive: false }), on(document.body, "mouseleave", onMouseLeave), on(window, "mouseup", stopDraggingAndBlur), on(window, "touchend", stopDraggingAndBlur));
    });
  }
  get props() {
    return get(__privateGet(this, _props2));
  }
  set props(value) {
    set(__privateGet(this, _props2), value);
  }
};
_isDragging = new WeakMap();
_isFocused = new WeakMap();
_startDragging = new WeakMap();
_stopDraggingAndBlur = new WeakMap();
_onkeydown = new WeakMap();
_onblur = new WeakMap();
_onfocus = new WeakMap();
_onmousedown = new WeakMap();
_onmouseup = new WeakMap();
_ontouchcancel = new WeakMap();
_ontouchend = new WeakMap();
_ontouchstart = new WeakMap();
_props2 = new WeakMap();
var _paneTransitionState, _callbacks, _constraints, _handleTransition, _isCollapsed, _paneState, _props3;
var PaneState = class {
  constructor(opts, group) {
    __publicField(this, "opts");
    __publicField(this, "group");
    __privateAdd(this, _paneTransitionState, state(""));
    __privateAdd(this, _callbacks, user_derived(() => ({
      onCollapse: this.opts.onCollapse.current,
      onExpand: this.opts.onExpand.current,
      onResize: this.opts.onResize.current
    })));
    __privateAdd(this, _constraints, user_derived(() => ({
      collapsedSize: this.opts.collapsedSize.current,
      collapsible: this.opts.collapsible.current,
      defaultSize: this.opts.defaultSize.current,
      maxSize: this.opts.maxSize.current,
      minSize: this.opts.minSize.current
    })));
    __privateAdd(this, _handleTransition, (state2) => {
      set(__privateGet(this, _paneTransitionState), state2, true);
      afterTick(() => {
        if (this.opts.ref.current) {
          const element2 = this.opts.ref.current;
          const computedStyle = getComputedStyle(element2);
          const hasTransition = strict_equals(computedStyle.transitionDuration, "0s", false);
          if (!hasTransition) {
            set(__privateGet(this, _paneTransitionState), "");
            return;
          }
          const handleTransitionEnd = (event) => {
            if (strict_equals(event.propertyName, "flex-grow")) {
              set(__privateGet(this, _paneTransitionState), "");
              element2.removeEventListener("transitionend", handleTransitionEnd);
            }
          };
          element2.addEventListener("transitionend", handleTransitionEnd);
        } else {
          set(__privateGet(this, _paneTransitionState), "");
        }
      });
    });
    __publicField(this, "pane", {
      collapse: () => {
        __privateGet(this, _handleTransition).call(this, "collapsing");
        this.group.collapsePane(this);
      },
      expand: () => {
        __privateGet(this, _handleTransition).call(this, "expanding");
        this.group.expandPane(this);
      },
      getSize: () => this.group.getPaneSize(this),
      isCollapsed: () => this.group.isPaneCollapsed(this),
      isExpanded: () => this.group.isPaneExpanded(this),
      resize: (size) => this.group.resizePane(this, size),
      getId: () => this.opts.id.current
    });
    __privateAdd(this, _isCollapsed, user_derived(() => this.group.isPaneCollapsed(this)));
    __privateAdd(this, _paneState, user_derived(() => strict_equals(get(__privateGet(this, _paneTransitionState)), "", false) ? get(__privateGet(this, _paneTransitionState)) : get(__privateGet(this, _isCollapsed)) ? "collapsed" : "expanded"));
    __privateAdd(this, _props3, user_derived(() => ({
      id: this.opts.id.current,
      style: this.group.getPaneStyle(this, this.opts.defaultSize.current),
      "data-pane": "",
      "data-pane-id": this.opts.id.current,
      "data-pane-group-id": this.group.opts.id.current,
      "data-collapsed": get(__privateGet(this, _isCollapsed)) ? "" : void 0,
      "data-expanded": get(__privateGet(this, _isCollapsed)) ? void 0 : "",
      "data-pane-state": get(__privateGet(this, _paneState))
    })));
    this.opts = opts;
    this.group = group;
    useRefById(opts);
    onMount(() => {
      return this.group.registerPane(this);
    });
    watch(() => snapshot(this.constraints), () => {
      this.group.panesArrayChanged = true;
    });
  }
  get callbacks() {
    return get(__privateGet(this, _callbacks));
  }
  set callbacks(value) {
    set(__privateGet(this, _callbacks), value);
  }
  get constraints() {
    return get(__privateGet(this, _constraints));
  }
  set constraints(value) {
    set(__privateGet(this, _constraints), value);
  }
  get props() {
    return get(__privateGet(this, _props3));
  }
  set props(value) {
    set(__privateGet(this, _props3), value);
  }
};
_paneTransitionState = new WeakMap();
_callbacks = new WeakMap();
_constraints = new WeakMap();
_handleTransition = new WeakMap();
_isCollapsed = new WeakMap();
_paneState = new WeakMap();
_props3 = new WeakMap();
var PaneGroupContext = new Context("PaneGroup");
function usePaneGroup(props) {
  return PaneGroupContext.set(new PaneGroupState(props));
}
function usePaneResizer(props) {
  return new PaneResizerState(props, PaneGroupContext.get());
}
function usePane(props) {
  return new PaneState(props, PaneGroupContext.get());
}

// node_modules/paneforge/dist/components/pane-group.svelte
Pane_group[FILENAME] = "node_modules/paneforge/dist/components/pane-group.svelte";
var root_2 = add_locations(template(`<div><!></div>`), Pane_group[FILENAME], [[44, 1]]);
function Pane_group($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Pane_group);
  let autoSaveId = prop($$props, "autoSaveId", 3, null), id = prop($$props, "id", 19, useId), keyboardResizeBy = prop($$props, "keyboardResizeBy", 3, null), onLayoutChange = prop($$props, "onLayoutChange", 3, noop2), storage = prop($$props, "storage", 3, defaultStorage), ref = prop($$props, "ref", 15, null), restProps = rest_props(
    $$props,
    [
      "$$slots",
      "$$events",
      "$$legacy",
      "autoSaveId",
      "direction",
      "id",
      "keyboardResizeBy",
      "onLayoutChange",
      "storage",
      "ref",
      "child",
      "children"
    ],
    "restProps"
  );
  const paneGroupState = usePaneGroup({
    id: box.with(() => id() ?? useId()),
    ref: box.with(() => ref(), (v) => ref(v)),
    autoSaveId: box.with(() => autoSaveId()),
    direction: box.with(() => $$props.direction),
    keyboardResizeBy: box.with(() => keyboardResizeBy()),
    onLayout: box.with(() => onLayoutChange()),
    storage: box.with(() => storage())
  });
  const getLayout = () => paneGroupState.layout;
  const setLayout = paneGroupState.setLayout;
  const getId = () => paneGroupState.opts.id.current;
  const mergedProps = user_derived(() => mergeProps(restProps, paneGroupState.props));
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      snippet(node_1, () => $$props.child, () => ({ props: get(mergedProps) }));
      append($$anchor2, fragment_1);
    };
    var alternate = ($$anchor2) => {
      var div = root_2();
      let attributes;
      var node_2 = child(div);
      snippet(node_2, () => $$props.children ?? noop);
      reset(div);
      template_effect(() => attributes = set_attributes(div, attributes, { ...get(mergedProps) }));
      append($$anchor2, div);
    };
    if_block(node, ($$render) => {
      if ($$props.child) $$render(consequent);
      else $$render(alternate, false);
    });
  }
  append($$anchor, fragment);
  return pop({
    get getLayout() {
      return getLayout;
    },
    get setLayout() {
      return setLayout;
    },
    get getId() {
      return getId;
    },
    ...legacy_api()
  });
}
if (import.meta.hot) {
  Pane_group = hmr(Pane_group, () => Pane_group[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Pane_group[HMR].source;
    set(Pane_group[HMR].source, module.default[HMR].original);
  });
}
var pane_group_default = Pane_group;

// node_modules/paneforge/dist/components/pane.svelte
Pane[FILENAME] = "node_modules/paneforge/dist/components/pane.svelte";
var root_22 = add_locations(template(`<div><!></div>`), Pane[FILENAME], [[56, 1]]);
function Pane($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Pane);
  let id = prop($$props, "id", 19, useId), ref = prop($$props, "ref", 15, null), onCollapse = prop($$props, "onCollapse", 3, noop2), onExpand = prop($$props, "onExpand", 3, noop2), onResize = prop($$props, "onResize", 3, noop2), restProps = rest_props(
    $$props,
    [
      "$$slots",
      "$$events",
      "$$legacy",
      "id",
      "ref",
      "collapsedSize",
      "collapsible",
      "defaultSize",
      "maxSize",
      "minSize",
      "onCollapse",
      "onExpand",
      "onResize",
      "order",
      "child",
      "children"
    ],
    "restProps"
  );
  const paneState = usePane({
    id: box.with(() => id()),
    ref: box.with(() => ref(), (v) => ref(v)),
    collapsedSize: box.with(() => $$props.collapsedSize),
    collapsible: box.with(() => $$props.collapsible),
    defaultSize: box.with(() => $$props.defaultSize),
    maxSize: box.with(() => $$props.maxSize),
    minSize: box.with(() => $$props.minSize),
    onCollapse: box.with(() => onCollapse()),
    onExpand: box.with(() => onExpand()),
    onResize: box.with(() => onResize()),
    order: box.with(() => $$props.order)
  });
  const collapse = paneState.pane.collapse;
  const expand = paneState.pane.expand;
  const getSize = paneState.pane.getSize;
  const isCollapsed = paneState.pane.isCollapsed;
  const isExpanded = paneState.pane.isExpanded;
  const resize = paneState.pane.resize;
  const getId = paneState.pane.getId;
  const mergedProps = user_derived(() => mergeProps(restProps, paneState.props));
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      snippet(node_1, () => $$props.child, () => ({ props: get(mergedProps) }));
      append($$anchor2, fragment_1);
    };
    var alternate = ($$anchor2) => {
      var div = root_22();
      let attributes;
      var node_2 = child(div);
      snippet(node_2, () => $$props.children ?? noop);
      reset(div);
      template_effect(() => attributes = set_attributes(div, attributes, { ...get(mergedProps) }));
      append($$anchor2, div);
    };
    if_block(node, ($$render) => {
      if ($$props.child) $$render(consequent);
      else $$render(alternate, false);
    });
  }
  append($$anchor, fragment);
  return pop({
    get collapse() {
      return collapse;
    },
    get expand() {
      return expand;
    },
    get getSize() {
      return getSize;
    },
    get isCollapsed() {
      return isCollapsed;
    },
    get isExpanded() {
      return isExpanded;
    },
    get resize() {
      return resize;
    },
    get getId() {
      return getId;
    },
    ...legacy_api()
  });
}
if (import.meta.hot) {
  Pane = hmr(Pane, () => Pane[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Pane[HMR].source;
    set(Pane[HMR].source, module.default[HMR].original);
  });
}
var pane_default = Pane;

// node_modules/paneforge/dist/components/pane-resizer.svelte
Pane_resizer[FILENAME] = "node_modules/paneforge/dist/components/pane-resizer.svelte";
var root_23 = add_locations(template(`<div><!></div>`), Pane_resizer[FILENAME], [[36, 1]]);
function Pane_resizer($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Pane_resizer);
  let id = prop($$props, "id", 19, useId), ref = prop($$props, "ref", 15, null), disabled = prop($$props, "disabled", 3, false), onDraggingChange = prop($$props, "onDraggingChange", 3, noop2), tabindex = prop($$props, "tabindex", 3, 0), restProps = rest_props(
    $$props,
    [
      "$$slots",
      "$$events",
      "$$legacy",
      "id",
      "ref",
      "disabled",
      "onDraggingChange",
      "tabindex",
      "child",
      "children"
    ],
    "restProps"
  );
  const resizerState = usePaneResizer({
    id: box.with(() => id()),
    ref: box.with(() => ref(), (v) => ref(v)),
    disabled: box.with(() => disabled()),
    onDraggingChange: box.with(() => onDraggingChange()),
    tabIndex: box.with(() => tabindex())
  });
  const mergedProps = user_derived(() => mergeProps(restProps, resizerState.props));
  var fragment = comment();
  var node = first_child(fragment);
  {
    var consequent = ($$anchor2) => {
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      snippet(node_1, () => $$props.child, () => ({ props: get(mergedProps) }));
      append($$anchor2, fragment_1);
    };
    var alternate = ($$anchor2) => {
      var div = root_23();
      let attributes;
      var node_2 = child(div);
      snippet(node_2, () => $$props.children ?? noop);
      reset(div);
      template_effect(() => attributes = set_attributes(div, attributes, { ...get(mergedProps) }));
      append($$anchor2, div);
    };
    if_block(node, ($$render) => {
      if ($$props.child) $$render(consequent);
      else $$render(alternate, false);
    });
  }
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Pane_resizer = hmr(Pane_resizer, () => Pane_resizer[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Pane_resizer[HMR].source;
    set(Pane_resizer[HMR].source, module.default[HMR].original);
  });
}
var pane_resizer_default = Pane_resizer;
export {
  pane_default as Pane,
  pane_group_default as PaneGroup,
  pane_resizer_default as PaneResizer
};
//# sourceMappingURL=paneforge.js.map
