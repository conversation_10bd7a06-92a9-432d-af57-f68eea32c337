<script lang="ts">
  import * as Card from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { Loader2, TrendingUp, TrendingDown, Minus } from 'lucide-svelte';
  import * as Chart from '$lib/components/ui/chart';
  import { BarChart } from 'layerchart';

  const {
    featureId,
    limitId,
    title = 'Usage History',
    description = 'Track your usage over time',
    periodsToShow = 6,
    initialPeriodType = 'monthly',
  } = $props<{
    featureId: string;
    limitId: string;
    title?: string;
    description?: string;
    periodsToShow?: number;
    initialPeriodType?: 'daily' | 'weekly' | 'monthly';
  }>();

  let periodType = $state<'daily' | 'weekly' | 'monthly'>(initialPeriodType);

  let loading = $state(true);
  let error = $state<string | null>(null);
  let usageData = $state<any[]>([]);

  // Chart configuration
  const chartConfig = {
    used: {
      label: 'Usage',
      color: 'var(--chart-1)',
    },
    limit: {
      label: 'Limit',
      color: 'var(--chart-5)',
    },
  } satisfies Chart.ChartConfig;

  // Transform data for chart
  const chartData = $derived(() => {
    return usageData.map((item) => ({
      period: formatDate(item.date, periodType),
      used: item.used,
      limit: item.limit || 0,
    }));
  });

  // Calculate trend data
  let trendData = $derived(() => {
    if (usageData.length < 2) return null;

    const current = usageData[usageData.length - 1]?.used || 0;
    const previous = usageData[usageData.length - 2]?.used || 0;

    if (previous === 0) return null;

    const change = current - previous;
    const percentChange = Math.round((change / previous) * 100);

    return {
      change,
      percentChange,
      direction: change > 0 ? 'up' : change < 0 ? 'down' : 'stable',
    };
  });

  // Format date based on period type
  function formatDate(date: Date, type: 'daily' | 'weekly' | 'monthly'): string {
    if (type === 'daily') {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    } else if (type === 'weekly') {
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      return `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`;
    } else {
      return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    }
  }

  // Generate periods for the chart
  function generatePeriods(
    type: 'daily' | 'weekly' | 'monthly',
    count: number
  ): { date: Date; period: string }[] {
    const result = [];
    const now = new Date();

    for (let i = 0; i < count; i++) {
      const date = new Date();

      if (type === 'daily') {
        date.setDate(now.getDate() - i);
        const period = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        result.unshift({ date, period });
      } else if (type === 'weekly') {
        date.setDate(now.getDate() - i * 7);
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        const period = `${weekStart.getFullYear()}-W${Math.ceil((weekStart.getDate() + weekStart.getDay()) / 7)}`;
        result.unshift({ date: weekStart, period });
      } else {
        date.setMonth(now.getMonth() - i);
        const period = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        result.unshift({ date, period });
      }
    }

    return result;
  }

  // Fetch usage data
  async function fetchUsageData() {
    try {
      loading = true;
      error = null;

      // Check if required parameters are provided
      if (!featureId || !limitId) {
        usageData = [];
        loading = false;
        return;
      }

      // Generate periods
      const periodsList = generatePeriods(periodType, periodsToShow);

      // Fetch data for each period
      const response = await fetch(`/api/feature-usage?featureId=${featureId}&limitId=${limitId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch usage data');
      }

      const data = await response.json();

      // Map periods to usage data
      usageData = periodsList.map(({ date, period }) => {
        const usage = data.find((d: any) => d.period === period);
        return {
          date,
          period,
          used: usage ? usage.used : 0,
          limit: usage ? usage.limit : null,
        };
      });
    } catch (err) {
      console.error('Error fetching usage data:', err);
      error = err.message;
    } finally {
      loading = false;
    }
  }

  // Fetch data when component mounts and when period type changes
  $effect(() => {
    fetchUsageData();
  });
</script>

<Card.Root>
  <Card.Header>
    <div class="flex items-center justify-between">
      <div>
        <div class="flex items-center gap-3">
          <div>
            <Card.Title>{title}</Card.Title>
            <Card.Description>{description}</Card.Description>
          </div>
          {#if trendData()}
            <div class="flex items-center gap-1 text-sm">
              {#if trendData().direction === 'up'}
                <TrendingUp class="h-4 w-4 text-green-500" />
                <span class="font-medium text-green-600">+{trendData().percentChange}%</span>
              {:else if trendData().direction === 'down'}
                <TrendingDown class="h-4 w-4 text-red-500" />
                <span class="font-medium text-red-600">{trendData().percentChange}%</span>
              {:else}
                <Minus class="h-4 w-4 text-gray-500" />
                <span class="font-medium text-gray-600">No change</span>
              {/if}
            </div>
          {/if}
        </div>
      </div>
      <div class="flex items-center space-x-2">
        <select
          bind:value={periodType}
          class="border-input bg-background ring-offset-background focus-visible:ring-ring h-8 rounded-md border px-3 py-1 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2">
          <option value="daily">Daily</option>
          <option value="weekly">Weekly</option>
          <option value="monthly">Monthly</option>
        </select>
        <Button variant="outline" size="sm" onclick={fetchUsageData} disabled={loading}>
          <Loader2 class={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          <span class="sr-only">Refresh</span>
        </Button>
      </div>
    </div>
  </Card.Header>
  <Card.Content>
    {#if loading}
      <div class="flex justify-center py-8">
        <Loader2 class="text-primary h-8 w-8 animate-spin" />
      </div>
    {:else if error}
      <div class="bg-destructive/10 text-destructive rounded-md p-4 text-sm">
        <p>Error loading usage data: {error}</p>
      </div>
    {:else if usageData.length === 0}
      <div class="rounded-md border border-dashed p-8 text-center">
        <p class="text-muted-foreground">No usage data available.</p>
      </div>
    {:else}
      <Chart.Container config={chartConfig} class="h-60 w-full">
        <BarChart
          data={chartData()}
          x="period"
          axis="x"
          legend
          series={[
            {
              key: 'used',
              label: chartConfig.used.label,
              color: chartConfig.used.color,
            },
          ]}
          props={{
            xAxis: {
              format: (d: string) => d.slice(0, 6),
            },
          }}>
          {#snippet tooltip()}
            <Chart.Tooltip />
          {/snippet}
        </BarChart>
      </Chart.Container>

      <!-- Chart summary -->
      <div class="text-muted-foreground mt-4 flex items-center justify-between text-sm">
        <div class="flex items-center gap-4">
          <div class="flex items-center gap-2">
            <div class="h-3 w-3 rounded" style="background-color: var(--chart-1);"></div>
            <span>Usage</span>
          </div>
          {#if usageData.some((d) => d.limit !== null && d.limit > 0)}
            <div class="flex items-center gap-2">
              <div
                class="h-0.5 w-4 opacity-70"
                style="background-color: var(--chart-5); border-top: 2px dashed;">
              </div>
              <span>Limit</span>
            </div>
          {/if}
        </div>
        <div>
          Total: {usageData.reduce((sum, d) => sum + d.used, 0)} uses
        </div>
      </div>
    {/if}
  </Card.Content>
</Card.Root>
