import {
  __commonJ<PERSON>,
  __privateAdd,
  __publicField,
  __toESM
} from "./chunk-KWPVD4H7.js";

// browser-external:node:module
var require_node_module = __commonJS({
  "browser-external:node:module"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:module" has been externalized for browser compatibility. Cannot access "node:module.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:path
var require_node_path = __commonJS({
  "browser-external:node:path"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:path" has been externalized for browser compatibility. Cannot access "node:path.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:process
var require_node_process = __commonJS({
  "browser-external:node:process"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:process" has been externalized for browser compatibility. Cannot access "node:process.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:url
var require_node_url = __commonJS({
  "browser-external:node:url"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:url" has been externalized for browser compatibility. Cannot access "node:url.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:fs
var require_node_fs = __commonJS({
  "browser-external:node:fs"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:fs" has been externalized for browser compatibility. Cannot access "node:fs.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:child_process
var require_node_child_process = __commonJS({
  "browser-external:node:child_process"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:child_process" has been externalized for browser compatibility. Cannot access "node:child_process.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:fs/promises
var require_promises = __commonJS({
  "browser-external:node:fs/promises"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:fs/promises" has been externalized for browser compatibility. Cannot access "node:fs/promises.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:os
var require_node_os = __commonJS({
  "browser-external:node:os"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:os" has been externalized for browser compatibility. Cannot access "node:os.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:util
var require_node_util = __commonJS({
  "browser-external:node:util"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:util" has been externalized for browser compatibility. Cannot access "node:util.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:async_hooks
var require_node_async_hooks = __commonJS({
  "browser-external:node:async_hooks"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:async_hooks" has been externalized for browser compatibility. Cannot access "node:async_hooks.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// browser-external:node:events
var require_node_events = __commonJS({
  "browser-external:node:events"(exports, module) {
    module.exports = Object.create(new Proxy({}, {
      get(_2, key) {
        if (key !== "__esModule" && key !== "__proto__" && key !== "constructor" && key !== "splice") {
          console.warn(`Module "node:events" has been externalized for browser compatibility. Cannot access "node:events.${key}" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`);
        }
      }
    }));
  }
});

// node_modules/@prisma/client/runtime/library.mjs
var __banner_node_module = __toESM(require_node_module(), 1);
var __banner_node_path = __toESM(require_node_path(), 1);
var process = __toESM(require_node_process(), 1);
var __banner_node_url = __toESM(require_node_url(), 1);
var import_node_fs = __toESM(require_node_fs(), 1);
var import_node_child_process = __toESM(require_node_child_process(), 1);
var import_promises = __toESM(require_promises(), 1);
var import_node_os = __toESM(require_node_os(), 1);
var import_node_util = __toESM(require_node_util(), 1);
var import_node_process = __toESM(require_node_process(), 1);
var import_node_path = __toESM(require_node_path(), 1);
var import_node_fs2 = __toESM(require_node_fs(), 1);
var import_node_path2 = __toESM(require_node_path(), 1);
var import_node_fs3 = __toESM(require_node_fs(), 1);
var import_node_path3 = __toESM(require_node_path(), 1);
var import_node_fs4 = __toESM(require_node_fs(), 1);
var import_node_async_hooks = __toESM(require_node_async_hooks(), 1);
var import_node_events = __toESM(require_node_events(), 1);
var import_node_fs5 = __toESM(require_node_fs(), 1);
var import_node_path4 = __toESM(require_node_path(), 1);
var import_node_fs6 = __toESM(require_node_fs(), 1);
var import_node_path5 = __toESM(require_node_path(), 1);
var import_node_os2 = __toESM(require_node_os(), 1);
var import_node_path6 = __toESM(require_node_path(), 1);
var __filename = __banner_node_url.fileURLToPath(import.meta.url);
var __dirname = __banner_node_path.dirname(__filename);
var require2 = __banner_node_module.createRequire(import.meta.url);
var eu = Object.create;
var Wn = Object.defineProperty;
var ru = Object.getOwnPropertyDescriptor;
var tu = Object.getOwnPropertyNames;
var nu = Object.getPrototypeOf;
var iu = Object.prototype.hasOwnProperty;
var fr = ((e5) => typeof require2 < "u" ? require2 : typeof Proxy < "u" ? new Proxy(e5, { get: (r, t) => (typeof require2 < "u" ? require2 : r)[t] }) : e5)(function(e5) {
  if (typeof require2 < "u") return require2.apply(this, arguments);
  throw Error('Dynamic require of "' + e5 + '" is not supported');
});
var Ro = (e5, r) => () => (e5 && (r = e5(e5 = 0)), r);
var te = (e5, r) => () => (r || e5((r = { exports: {} }).exports, r), r.exports);
var gr = (e5, r) => {
  for (var t in r) Wn(e5, t, { get: r[t], enumerable: true });
};
var ou = (e5, r, t, n) => {
  if (r && typeof r == "object" || typeof r == "function") for (let i of tu(r)) !iu.call(e5, i) && i !== t && Wn(e5, i, { get: () => r[i], enumerable: !(n = ru(r, i)) || n.enumerable });
  return e5;
};
var se = (e5, r, t) => (t = e5 != null ? eu(nu(e5)) : {}, ou(r || !e5 || !e5.__esModule ? Wn(t, "default", { value: e5, enumerable: true }) : t, e5));
var oi = te(($g, Xo) => {
  "use strict";
  Xo.exports = (e5, r = process.argv) => {
    let t = e5.startsWith("-") ? "" : e5.length === 1 ? "-" : "--", n = r.indexOf(t + e5), i = r.indexOf("--");
    return n !== -1 && (i === -1 || n < i);
  };
});
var ts = te((qg, rs) => {
  "use strict";
  var vc = fr("node:os"), es = fr("node:tty"), pe = oi(), { env: U } = process, Ge;
  pe("no-color") || pe("no-colors") || pe("color=false") || pe("color=never") ? Ge = 0 : (pe("color") || pe("colors") || pe("color=true") || pe("color=always")) && (Ge = 1);
  "FORCE_COLOR" in U && (U.FORCE_COLOR === "true" ? Ge = 1 : U.FORCE_COLOR === "false" ? Ge = 0 : Ge = U.FORCE_COLOR.length === 0 ? 1 : Math.min(parseInt(U.FORCE_COLOR, 10), 3));
  function si(e5) {
    return e5 === 0 ? false : { level: e5, hasBasic: true, has256: e5 >= 2, has16m: e5 >= 3 };
  }
  function ai(e5, r) {
    if (Ge === 0) return 0;
    if (pe("color=16m") || pe("color=full") || pe("color=truecolor")) return 3;
    if (pe("color=256")) return 2;
    if (e5 && !r && Ge === void 0) return 0;
    let t = Ge || 0;
    if (U.TERM === "dumb") return t;
    if (process.platform === "win32") {
      let n = vc.release().split(".");
      return Number(n[0]) >= 10 && Number(n[2]) >= 10586 ? Number(n[2]) >= 14931 ? 3 : 2 : 1;
    }
    if ("CI" in U) return ["TRAVIS", "CIRCLECI", "APPVEYOR", "GITLAB_CI", "GITHUB_ACTIONS", "BUILDKITE"].some((n) => n in U) || U.CI_NAME === "codeship" ? 1 : t;
    if ("TEAMCITY_VERSION" in U) return /^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(U.TEAMCITY_VERSION) ? 1 : 0;
    if (U.COLORTERM === "truecolor") return 3;
    if ("TERM_PROGRAM" in U) {
      let n = parseInt((U.TERM_PROGRAM_VERSION || "").split(".")[0], 10);
      switch (U.TERM_PROGRAM) {
        case "iTerm.app":
          return n >= 3 ? 3 : 2;
        case "Apple_Terminal":
          return 2;
      }
    }
    return /-256(color)?$/i.test(U.TERM) ? 2 : /^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(U.TERM) || "COLORTERM" in U ? 1 : t;
  }
  function Pc(e5) {
    let r = ai(e5, e5 && e5.isTTY);
    return si(r);
  }
  rs.exports = { supportsColor: Pc, stdout: si(ai(true, es.isatty(1))), stderr: si(ai(true, es.isatty(2))) };
});
var os = te((jg, is) => {
  "use strict";
  var Tc = ts(), Er = oi();
  function ns(e5) {
    if (/^\d{3,4}$/.test(e5)) {
      let t = /(\d{1,2})(\d{2})/.exec(e5) || [];
      return { major: 0, minor: parseInt(t[1], 10), patch: parseInt(t[2], 10) };
    }
    let r = (e5 || "").split(".").map((t) => parseInt(t, 10));
    return { major: r[0], minor: r[1], patch: r[2] };
  }
  function li(e5) {
    let { CI: r, FORCE_HYPERLINK: t, NETLIFY: n, TEAMCITY_VERSION: i, TERM_PROGRAM: o, TERM_PROGRAM_VERSION: s, VTE_VERSION: a, TERM: l } = process.env;
    if (t) return !(t.length > 0 && parseInt(t, 10) === 0);
    if (Er("no-hyperlink") || Er("no-hyperlinks") || Er("hyperlink=false") || Er("hyperlink=never")) return false;
    if (Er("hyperlink=true") || Er("hyperlink=always") || n) return true;
    if (!Tc.supportsColor(e5) || e5 && !e5.isTTY) return false;
    if ("WT_SESSION" in process.env) return true;
    if (process.platform === "win32" || r || i) return false;
    if (o) {
      let u = ns(s || "");
      switch (o) {
        case "iTerm.app":
          return u.major === 3 ? u.minor >= 1 : u.major > 3;
        case "WezTerm":
          return u.major >= 20200620;
        case "vscode":
          return u.major > 1 || u.major === 1 && u.minor >= 72;
        case "ghostty":
          return true;
      }
    }
    if (a) {
      if (a === "0.50.0") return false;
      let u = ns(a);
      return u.major > 0 || u.minor >= 50;
    }
    switch (l) {
      case "alacritty":
        return true;
    }
    return false;
  }
  is.exports = { supportsHyperlink: li, stdout: li(process.stdout), stderr: li(process.stderr) };
});
var ss = te((Xg, Sc) => {
  Sc.exports = { name: "@prisma/internals", version: "6.8.1", description: "This package is intended for Prisma's internal use", main: "dist/index.js", types: "dist/index.d.ts", repository: { type: "git", url: "https://github.com/prisma/prisma.git", directory: "packages/internals" }, homepage: "https://www.prisma.io", author: "Tim Suchanek <<EMAIL>>", bugs: "https://github.com/prisma/prisma/issues", license: "Apache-2.0", scripts: { dev: "DEV=true tsx helpers/build.ts", build: "tsx helpers/build.ts", test: "dotenv -e ../../.db.env -- jest --silent", prepublishOnly: "pnpm run build" }, files: ["README.md", "dist", "!**/libquery_engine*", "!dist/get-generators/engines/*", "scripts"], devDependencies: { "@babel/helper-validator-identifier": "7.25.9", "@opentelemetry/api": "1.9.0", "@swc/core": "1.11.5", "@swc/jest": "0.2.37", "@types/babel__helper-validator-identifier": "7.15.2", "@types/jest": "29.5.14", "@types/node": "18.19.76", "@types/resolve": "1.20.6", archiver: "6.0.2", "checkpoint-client": "1.1.33", "cli-truncate": "4.0.0", dotenv: "16.5.0", esbuild: "0.25.1", "escape-string-regexp": "5.0.0", execa: "5.1.1", "fast-glob": "3.3.3", "find-up": "7.0.0", "fp-ts": "2.16.9", "fs-extra": "11.3.0", "fs-jetpack": "5.1.0", "global-dirs": "4.0.0", globby: "11.1.0", "identifier-regex": "1.0.0", "indent-string": "4.0.0", "is-windows": "1.0.2", "is-wsl": "3.1.0", jest: "29.7.0", "jest-junit": "16.0.0", kleur: "4.1.5", "mock-stdin": "1.0.0", "new-github-issue-url": "0.2.1", "node-fetch": "3.3.2", "npm-packlist": "5.1.3", open: "7.4.2", "p-map": "4.0.0", "read-package-up": "11.0.0", resolve: "1.22.10", "string-width": "7.2.0", "strip-ansi": "6.0.1", "strip-indent": "4.0.0", "temp-dir": "2.0.0", tempy: "1.0.1", "terminal-link": "4.0.0", tmp: "0.2.3", "ts-node": "10.9.2", "ts-pattern": "5.6.2", "ts-toolbelt": "9.6.0", typescript: "5.4.5", yarn: "1.22.22" }, dependencies: { "@prisma/config": "workspace:*", "@prisma/debug": "workspace:*", "@prisma/dmmf": "workspace:*", "@prisma/driver-adapter-utils": "workspace:*", "@prisma/engines": "workspace:*", "@prisma/fetch-engine": "workspace:*", "@prisma/generator": "workspace:*", "@prisma/generator-helper": "workspace:*", "@prisma/get-platform": "workspace:*", "@prisma/prisma-schema-wasm": "6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e", "@prisma/schema-engine-wasm": "6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e", "@prisma/schema-files-loader": "workspace:*", arg: "5.0.2", prompts: "2.4.2" }, peerDependencies: { typescript: ">=5.1.0" }, peerDependenciesMeta: { typescript: { optional: true } }, sideEffects: false };
});
var pi = te((th, Ic) => {
  Ic.exports = { name: "@prisma/engines-version", version: "6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e", main: "index.js", types: "index.d.ts", license: "Apache-2.0", author: "Tim Suchanek <<EMAIL>>", prisma: { enginesVersion: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e" }, repository: { type: "git", url: "https://github.com/prisma/engines-wrapper.git", directory: "packages/engines-version" }, devDependencies: { "@types/node": "18.19.76", typescript: "4.9.5" }, files: ["index.js", "index.d.ts"], scripts: { build: "tsc -d" } };
});
var di = te((Kt) => {
  "use strict";
  Object.defineProperty(Kt, "__esModule", { value: true });
  Kt.enginesVersion = void 0;
  Kt.enginesVersion = pi().prisma.enginesVersion;
});
var ps = te((Ph, cs) => {
  "use strict";
  cs.exports = (e5) => {
    let r = e5.match(/^[ \t]*(?=\S)/gm);
    return r ? r.reduce((t, n) => Math.min(t, n.length), 1 / 0) : 0;
  };
});
var yi = te((Rh, fs) => {
  "use strict";
  fs.exports = (e5, r = 1, t) => {
    if (t = { indent: " ", includeEmptyLines: false, ...t }, typeof e5 != "string") throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e5}\``);
    if (typeof r != "number") throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof r}\``);
    if (typeof t.indent != "string") throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof t.indent}\``);
    if (r === 0) return e5;
    let n = t.includeEmptyLines ? /^/gm : /^(?!\s*$)/gm;
    return e5.replace(n, t.indent.repeat(r));
  };
});
var bs = te((Ih, ys) => {
  "use strict";
  ys.exports = ({ onlyFirst: e5 = false } = {}) => {
    let r = ["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)", "(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");
    return new RegExp(r, e5 ? void 0 : "g");
  };
});
var xi = te((kh, Es) => {
  "use strict";
  var qc = bs();
  Es.exports = (e5) => typeof e5 == "string" ? e5.replace(qc(), "") : e5;
});
var ws = te((Lh, jc) => {
  jc.exports = { name: "dotenv", version: "16.5.0", description: "Loads environment variables from .env file", main: "lib/main.js", types: "lib/main.d.ts", exports: { ".": { types: "./lib/main.d.ts", require: "./lib/main.js", default: "./lib/main.js" }, "./config": "./config.js", "./config.js": "./config.js", "./lib/env-options": "./lib/env-options.js", "./lib/env-options.js": "./lib/env-options.js", "./lib/cli-options": "./lib/cli-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./package.json": "./package.json" }, scripts: { "dts-check": "tsc --project tests/types/tsconfig.json", lint: "standard", pretest: "npm run lint && npm run dts-check", test: "tap run --allow-empty-coverage --disable-coverage --timeout=60000", "test:coverage": "tap run --show-full-coverage --timeout=60000 --coverage-report=lcov", prerelease: "npm test", release: "standard-version" }, repository: { type: "git", url: "git://github.com/motdotla/dotenv.git" }, homepage: "https://github.com/motdotla/dotenv#readme", funding: "https://dotenvx.com", keywords: ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], readmeFilename: "README.md", license: "BSD-2-Clause", devDependencies: { "@types/node": "^18.11.3", decache: "^4.6.2", sinon: "^14.0.1", standard: "^17.0.0", "standard-version": "^9.5.0", tap: "^19.2.0", typescript: "^4.8.4" }, engines: { node: ">=12" }, browser: { fs: false } };
});
var Ss = te((Fh, Ne) => {
  "use strict";
  var Pi = fr("node:fs"), Ti = fr("node:path"), Vc = fr("node:os"), Bc = fr("node:crypto"), Uc = ws(), vs = Uc.version, Qc = /(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;
  function Gc(e5) {
    let r = {}, t = e5.toString();
    t = t.replace(/\r\n?/mg, `
`);
    let n;
    for (; (n = Qc.exec(t)) != null; ) {
      let i = n[1], o = n[2] || "";
      o = o.trim();
      let s = o[0];
      o = o.replace(/^(['"`])([\s\S]*)\1$/mg, "$2"), s === '"' && (o = o.replace(/\\n/g, `
`), o = o.replace(/\\r/g, "\r")), r[i] = o;
    }
    return r;
  }
  function Wc(e5) {
    let r = Ts(e5), t = V.configDotenv({ path: r });
    if (!t.parsed) {
      let s = new Error(`MISSING_DATA: Cannot parse ${r} for an unknown reason`);
      throw s.code = "MISSING_DATA", s;
    }
    let n = Ps(e5).split(","), i = n.length, o;
    for (let s = 0; s < i; s++) try {
      let a = n[s].trim(), l = Hc(t, a);
      o = V.decrypt(l.ciphertext, l.key);
      break;
    } catch (a) {
      if (s + 1 >= i) throw a;
    }
    return V.parse(o);
  }
  function Jc(e5) {
    console.log(`[dotenv@${vs}][WARN] ${e5}`);
  }
  function rt(e5) {
    console.log(`[dotenv@${vs}][DEBUG] ${e5}`);
  }
  function Ps(e5) {
    return e5 && e5.DOTENV_KEY && e5.DOTENV_KEY.length > 0 ? e5.DOTENV_KEY : process.env.DOTENV_KEY && process.env.DOTENV_KEY.length > 0 ? process.env.DOTENV_KEY : "";
  }
  function Hc(e5, r) {
    let t;
    try {
      t = new URL(r);
    } catch (a) {
      if (a.code === "ERR_INVALID_URL") {
        let l = new Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");
        throw l.code = "INVALID_DOTENV_KEY", l;
      }
      throw a;
    }
    let n = t.password;
    if (!n) {
      let a = new Error("INVALID_DOTENV_KEY: Missing key part");
      throw a.code = "INVALID_DOTENV_KEY", a;
    }
    let i = t.searchParams.get("environment");
    if (!i) {
      let a = new Error("INVALID_DOTENV_KEY: Missing environment part");
      throw a.code = "INVALID_DOTENV_KEY", a;
    }
    let o = `DOTENV_VAULT_${i.toUpperCase()}`, s = e5.parsed[o];
    if (!s) {
      let a = new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${o} in your .env.vault file.`);
      throw a.code = "NOT_FOUND_DOTENV_ENVIRONMENT", a;
    }
    return { ciphertext: s, key: n };
  }
  function Ts(e5) {
    let r = null;
    if (e5 && e5.path && e5.path.length > 0) if (Array.isArray(e5.path)) for (let t of e5.path) Pi.existsSync(t) && (r = t.endsWith(".vault") ? t : `${t}.vault`);
    else r = e5.path.endsWith(".vault") ? e5.path : `${e5.path}.vault`;
    else r = Ti.resolve(process.cwd(), ".env.vault");
    return Pi.existsSync(r) ? r : null;
  }
  function xs(e5) {
    return e5[0] === "~" ? Ti.join(Vc.homedir(), e5.slice(1)) : e5;
  }
  function Kc(e5) {
    !!(e5 && e5.debug) && rt("Loading env from encrypted .env.vault");
    let t = V._parseVault(e5), n = process.env;
    return e5 && e5.processEnv != null && (n = e5.processEnv), V.populate(n, t, e5), { parsed: t };
  }
  function Yc(e5) {
    let r = Ti.resolve(process.cwd(), ".env"), t = "utf8", n = !!(e5 && e5.debug);
    e5 && e5.encoding ? t = e5.encoding : n && rt("No encoding is specified. UTF-8 is used by default");
    let i = [r];
    if (e5 && e5.path) if (!Array.isArray(e5.path)) i = [xs(e5.path)];
    else {
      i = [];
      for (let l of e5.path) i.push(xs(l));
    }
    let o, s = {};
    for (let l of i) try {
      let u = V.parse(Pi.readFileSync(l, { encoding: t }));
      V.populate(s, u, e5);
    } catch (u) {
      n && rt(`Failed to load ${l} ${u.message}`), o = u;
    }
    let a = process.env;
    return e5 && e5.processEnv != null && (a = e5.processEnv), V.populate(a, s, e5), o ? { parsed: s, error: o } : { parsed: s };
  }
  function zc(e5) {
    if (Ps(e5).length === 0) return V.configDotenv(e5);
    let r = Ts(e5);
    return r ? V._configVault(e5) : (Jc(`You set DOTENV_KEY but you are missing a .env.vault file at ${r}. Did you forget to build it?`), V.configDotenv(e5));
  }
  function Zc(e5, r) {
    let t = Buffer.from(r.slice(-64), "hex"), n = Buffer.from(e5, "base64"), i = n.subarray(0, 12), o = n.subarray(-16);
    n = n.subarray(12, -16);
    try {
      let s = Bc.createDecipheriv("aes-256-gcm", t, i);
      return s.setAuthTag(o), `${s.update(n)}${s.final()}`;
    } catch (s) {
      let a = s instanceof RangeError, l = s.message === "Invalid key length", u = s.message === "Unsupported state or unable to authenticate data";
      if (a || l) {
        let c = new Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");
        throw c.code = "INVALID_DOTENV_KEY", c;
      } else if (u) {
        let c = new Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");
        throw c.code = "DECRYPTION_FAILED", c;
      } else throw s;
    }
  }
  function Xc(e5, r, t = {}) {
    let n = !!(t && t.debug), i = !!(t && t.override);
    if (typeof r != "object") {
      let o = new Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");
      throw o.code = "OBJECT_REQUIRED", o;
    }
    for (let o of Object.keys(r)) Object.prototype.hasOwnProperty.call(e5, o) ? (i === true && (e5[o] = r[o]), n && rt(i === true ? `"${o}" is already defined and WAS overwritten` : `"${o}" is already defined and was NOT overwritten`)) : e5[o] = r[o];
  }
  var V = { configDotenv: Yc, _configVault: Kc, _parseVault: Wc, config: zc, decrypt: Zc, parse: Gc, populate: Xc };
  Ne.exports.configDotenv = V.configDotenv;
  Ne.exports._configVault = V._configVault;
  Ne.exports._parseVault = V._parseVault;
  Ne.exports.config = V.config;
  Ne.exports.decrypt = V.decrypt;
  Ne.exports.parse = V.parse;
  Ne.exports.populate = V.populate;
  Ne.exports = V;
});
var Is = te((Qh, en) => {
  "use strict";
  en.exports = (e5 = {}) => {
    let r;
    if (e5.repoUrl) r = e5.repoUrl;
    else if (e5.user && e5.repo) r = `https://github.com/${e5.user}/${e5.repo}`;
    else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");
    let t = new URL(`${r}/issues/new`), n = ["body", "title", "labels", "template", "milestone", "assignee", "projects"];
    for (let i of n) {
      let o = e5[i];
      if (o !== void 0) {
        if (i === "labels" || i === "projects") {
          if (!Array.isArray(o)) throw new TypeError(`The \`${i}\` option should be an array`);
          o = o.join(",");
        }
        t.searchParams.set(i, o);
      }
    }
    return t.toString();
  };
  en.exports.default = en.exports;
});
var Fi = te((vb, Ys) => {
  "use strict";
  Ys.exports = /* @__PURE__ */ function() {
    function e5(r, t, n, i, o) {
      return r < t || n < t ? r > n ? n + 1 : r + 1 : i === o ? t : t + 1;
    }
    return function(r, t) {
      if (r === t) return 0;
      if (r.length > t.length) {
        var n = r;
        r = t, t = n;
      }
      for (var i = r.length, o = t.length; i > 0 && r.charCodeAt(i - 1) === t.charCodeAt(o - 1); ) i--, o--;
      for (var s = 0; s < i && r.charCodeAt(s) === t.charCodeAt(s); ) s++;
      if (i -= s, o -= s, i === 0 || o < 3) return o;
      var a = 0, l, u, c, p, d, f, g, h, I, P, S, b, k = [];
      for (l = 0; l < i; l++) k.push(l + 1), k.push(r.charCodeAt(s + l));
      for (var me = k.length - 1; a < o - 3; ) for (I = t.charCodeAt(s + (u = a)), P = t.charCodeAt(s + (c = a + 1)), S = t.charCodeAt(s + (p = a + 2)), b = t.charCodeAt(s + (d = a + 3)), f = a += 4, l = 0; l < me; l += 2) g = k[l], h = k[l + 1], u = e5(g, u, c, I, h), c = e5(u, c, p, P, h), p = e5(c, p, d, S, h), f = e5(p, d, f, b, h), k[l] = f, d = p, p = c, c = u, u = g;
      for (; a < o; ) for (I = t.charCodeAt(s + (u = a)), f = ++a, l = 0; l < me; l += 2) g = k[l], k[l] = f = e5(g, u, f, I, k[l + 1]), u = g;
      return f;
    };
  }();
});
var ra = Ro(() => {
  "use strict";
});
var ta = Ro(() => {
  "use strict";
});
var Io = {};
gr(Io, { defineExtension: () => Co, getExtensionContext: () => Ao });
function Co(e5) {
  return typeof e5 == "function" ? e5 : (r) => r.$extends(e5);
}
function Ao(e5) {
  return e5;
}
var Oo = {};
gr(Oo, { validator: () => ko });
function ko(...e5) {
  return (r) => r;
}
var $t = {};
gr($t, { $: () => Fo, bgBlack: () => gu, bgBlue: () => Eu, bgCyan: () => xu, bgGreen: () => yu, bgMagenta: () => wu, bgRed: () => hu, bgWhite: () => vu, bgYellow: () => bu, black: () => pu, blue: () => tr, bold: () => G, cyan: () => Oe, dim: () => Ie, gray: () => Wr, green: () => qe, grey: () => fu, hidden: () => uu, inverse: () => lu, italic: () => au, magenta: () => du, red: () => ue, reset: () => su, strikethrough: () => cu, underline: () => K, white: () => mu, yellow: () => ke });
var Jn;
var Do;
var _o;
var No;
var Lo = true;
typeof process < "u" && ({ FORCE_COLOR: Jn, NODE_DISABLE_COLORS: Do, NO_COLOR: _o, TERM: No } = process.env || {}, Lo = process.stdout && process.stdout.isTTY);
var Fo = { enabled: !Do && _o == null && No !== "dumb" && (Jn != null && Jn !== "0" || Lo) };
function N(e5, r) {
  let t = new RegExp(`\\x1b\\[${r}m`, "g"), n = `\x1B[${e5}m`, i = `\x1B[${r}m`;
  return function(o) {
    return !Fo.enabled || o == null ? o : n + (~("" + o).indexOf(i) ? o.replace(t, i + n) : o) + i;
  };
}
var su = N(0, 0);
var G = N(1, 22);
var Ie = N(2, 22);
var au = N(3, 23);
var K = N(4, 24);
var lu = N(7, 27);
var uu = N(8, 28);
var cu = N(9, 29);
var pu = N(30, 39);
var ue = N(31, 39);
var qe = N(32, 39);
var ke = N(33, 39);
var tr = N(34, 39);
var du = N(35, 39);
var Oe = N(36, 39);
var mu = N(37, 39);
var Wr = N(90, 39);
var fu = N(90, 39);
var gu = N(40, 49);
var hu = N(41, 49);
var yu = N(42, 49);
var bu = N(43, 49);
var Eu = N(44, 49);
var wu = N(45, 49);
var xu = N(46, 49);
var vu = N(47, 49);
var Pu = 100;
var Mo = ["green", "yellow", "blue", "magenta", "cyan", "red"];
var Jr = [];
var $o = Date.now();
var Tu = 0;
var Hn = typeof process < "u" ? process.env : {};
globalThis.DEBUG ?? (globalThis.DEBUG = Hn.DEBUG ?? "");
globalThis.DEBUG_COLORS ?? (globalThis.DEBUG_COLORS = Hn.DEBUG_COLORS ? Hn.DEBUG_COLORS === "true" : true);
var Hr = { enable(e5) {
  typeof e5 == "string" && (globalThis.DEBUG = e5);
}, disable() {
  let e5 = globalThis.DEBUG;
  return globalThis.DEBUG = "", e5;
}, enabled(e5) {
  let r = globalThis.DEBUG.split(",").map((i) => i.replace(/[.+?^${}()|[\]\\]/g, "\\$&")), t = r.some((i) => i === "" || i[0] === "-" ? false : e5.match(RegExp(i.split("*").join(".*") + "$"))), n = r.some((i) => i === "" || i[0] !== "-" ? false : e5.match(RegExp(i.slice(1).split("*").join(".*") + "$")));
  return t && !n;
}, log: (...e5) => {
  let [r, t, ...n] = e5;
  (console.warn ?? console.log)(`${r} ${t}`, ...n);
}, formatters: {} };
function Su(e5) {
  let r = { color: Mo[Tu++ % Mo.length], enabled: Hr.enabled(e5), namespace: e5, log: Hr.log, extend: () => {
  } }, t = (...n) => {
    let { enabled: i, namespace: o, color: s, log: a } = r;
    if (n.length !== 0 && Jr.push([o, ...n]), Jr.length > Pu && Jr.shift(), Hr.enabled(o) || i) {
      let l = n.map((c) => typeof c == "string" ? c : Ru(c)), u = `+${Date.now() - $o}ms`;
      $o = Date.now(), globalThis.DEBUG_COLORS ? a($t[s](G(o)), ...l, $t[s](u)) : a(o, ...l, u);
    }
  };
  return new Proxy(t, { get: (n, i) => r[i], set: (n, i, o) => r[i] = o });
}
var L = new Proxy(Su, { get: (e5, r) => Hr[r], set: (e5, r, t) => Hr[r] = t });
function Ru(e5, r = 2) {
  let t = /* @__PURE__ */ new Set();
  return JSON.stringify(e5, (n, i) => {
    if (typeof i == "object" && i !== null) {
      if (t.has(i)) return "[Circular *]";
      t.add(i);
    } else if (typeof i == "bigint") return i.toString();
    return i;
  }, r);
}
var hr = L;
var Yn = ["darwin", "darwin-arm64", "debian-openssl-1.0.x", "debian-openssl-1.1.x", "debian-openssl-3.0.x", "rhel-openssl-1.0.x", "rhel-openssl-1.1.x", "rhel-openssl-3.0.x", "linux-arm64-openssl-1.1.x", "linux-arm64-openssl-1.0.x", "linux-arm64-openssl-3.0.x", "linux-arm-openssl-1.1.x", "linux-arm-openssl-1.0.x", "linux-arm-openssl-3.0.x", "linux-musl", "linux-musl-openssl-3.0.x", "linux-musl-arm64-openssl-1.1.x", "linux-musl-arm64-openssl-3.0.x", "linux-nixos", "linux-static-x64", "linux-static-arm64", "windows", "freebsd11", "freebsd12", "freebsd13", "freebsd14", "freebsd15", "openbsd", "netbsd", "arm"];
var De = Symbol.for("@ts-pattern/matcher");
var Au = Symbol.for("@ts-pattern/isVariadic");
var Bt = "@ts-pattern/anonymous-select-key";
var zn = (e5) => !!(e5 && typeof e5 == "object");
var Vt = (e5) => e5 && !!e5[De];
var Ee = (e5, r, t) => {
  if (Vt(e5)) {
    let n = e5[De](), { matched: i, selections: o } = n.match(r);
    return i && o && Object.keys(o).forEach((s) => t(s, o[s])), i;
  }
  if (zn(e5)) {
    if (!zn(r)) return false;
    if (Array.isArray(e5)) {
      if (!Array.isArray(r)) return false;
      let n = [], i = [], o = [];
      for (let s of e5.keys()) {
        let a = e5[s];
        Vt(a) && a[Au] ? o.push(a) : o.length ? i.push(a) : n.push(a);
      }
      if (o.length) {
        if (o.length > 1) throw new Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");
        if (r.length < n.length + i.length) return false;
        let s = r.slice(0, n.length), a = i.length === 0 ? [] : r.slice(-i.length), l = r.slice(n.length, i.length === 0 ? 1 / 0 : -i.length);
        return n.every((u, c) => Ee(u, s[c], t)) && i.every((u, c) => Ee(u, a[c], t)) && (o.length === 0 || Ee(o[0], l, t));
      }
      return e5.length === r.length && e5.every((s, a) => Ee(s, r[a], t));
    }
    return Reflect.ownKeys(e5).every((n) => {
      let i = e5[n];
      return (n in r || Vt(o = i) && o[De]().matcherType === "optional") && Ee(i, r[n], t);
      var o;
    });
  }
  return Object.is(r, e5);
};
var Qe = (e5) => {
  var r, t, n;
  return zn(e5) ? Vt(e5) ? (r = (t = (n = e5[De]()).getSelectionKeys) == null ? void 0 : t.call(n)) != null ? r : [] : Array.isArray(e5) ? Kr(e5, Qe) : Kr(Object.values(e5), Qe) : [];
};
var Kr = (e5, r) => e5.reduce((t, n) => t.concat(r(n)), []);
function ce(e5) {
  return Object.assign(e5, { optional: () => Iu(e5), and: (r) => $(e5, r), or: (r) => ku(e5, r), select: (r) => r === void 0 ? Vo(e5) : Vo(r, e5) });
}
function Iu(e5) {
  return ce({ [De]: () => ({ match: (r) => {
    let t = {}, n = (i, o) => {
      t[i] = o;
    };
    return r === void 0 ? (Qe(e5).forEach((i) => n(i, void 0)), { matched: true, selections: t }) : { matched: Ee(e5, r, n), selections: t };
  }, getSelectionKeys: () => Qe(e5), matcherType: "optional" }) });
}
function $(...e5) {
  return ce({ [De]: () => ({ match: (r) => {
    let t = {}, n = (i, o) => {
      t[i] = o;
    };
    return { matched: e5.every((i) => Ee(i, r, n)), selections: t };
  }, getSelectionKeys: () => Kr(e5, Qe), matcherType: "and" }) });
}
function ku(...e5) {
  return ce({ [De]: () => ({ match: (r) => {
    let t = {}, n = (i, o) => {
      t[i] = o;
    };
    return Kr(e5, Qe).forEach((i) => n(i, void 0)), { matched: e5.some((i) => Ee(i, r, n)), selections: t };
  }, getSelectionKeys: () => Kr(e5, Qe), matcherType: "or" }) });
}
function C(e5) {
  return { [De]: () => ({ match: (r) => ({ matched: !!e5(r) }) }) };
}
function Vo(...e5) {
  let r = typeof e5[0] == "string" ? e5[0] : void 0, t = e5.length === 2 ? e5[1] : typeof e5[0] == "string" ? void 0 : e5[0];
  return ce({ [De]: () => ({ match: (n) => {
    let i = { [r ?? Bt]: n };
    return { matched: t === void 0 || Ee(t, n, (o, s) => {
      i[o] = s;
    }), selections: i };
  }, getSelectionKeys: () => [r ?? Bt].concat(t === void 0 ? [] : Qe(t)) }) });
}
function ye(e5) {
  return typeof e5 == "number";
}
function je(e5) {
  return typeof e5 == "string";
}
function Ve(e5) {
  return typeof e5 == "bigint";
}
var eg = ce(C(function(e5) {
  return true;
}));
var Be = (e5) => Object.assign(ce(e5), { startsWith: (r) => {
  return Be($(e5, (t = r, C((n) => je(n) && n.startsWith(t)))));
  var t;
}, endsWith: (r) => {
  return Be($(e5, (t = r, C((n) => je(n) && n.endsWith(t)))));
  var t;
}, minLength: (r) => Be($(e5, ((t) => C((n) => je(n) && n.length >= t))(r))), length: (r) => Be($(e5, ((t) => C((n) => je(n) && n.length === t))(r))), maxLength: (r) => Be($(e5, ((t) => C((n) => je(n) && n.length <= t))(r))), includes: (r) => {
  return Be($(e5, (t = r, C((n) => je(n) && n.includes(t)))));
  var t;
}, regex: (r) => {
  return Be($(e5, (t = r, C((n) => je(n) && !!n.match(t)))));
  var t;
} });
var rg = Be(C(je));
var be = (e5) => Object.assign(ce(e5), { between: (r, t) => be($(e5, ((n, i) => C((o) => ye(o) && n <= o && i >= o))(r, t))), lt: (r) => be($(e5, ((t) => C((n) => ye(n) && n < t))(r))), gt: (r) => be($(e5, ((t) => C((n) => ye(n) && n > t))(r))), lte: (r) => be($(e5, ((t) => C((n) => ye(n) && n <= t))(r))), gte: (r) => be($(e5, ((t) => C((n) => ye(n) && n >= t))(r))), int: () => be($(e5, C((r) => ye(r) && Number.isInteger(r)))), finite: () => be($(e5, C((r) => ye(r) && Number.isFinite(r)))), positive: () => be($(e5, C((r) => ye(r) && r > 0))), negative: () => be($(e5, C((r) => ye(r) && r < 0))) });
var tg = be(C(ye));
var Ue = (e5) => Object.assign(ce(e5), { between: (r, t) => Ue($(e5, ((n, i) => C((o) => Ve(o) && n <= o && i >= o))(r, t))), lt: (r) => Ue($(e5, ((t) => C((n) => Ve(n) && n < t))(r))), gt: (r) => Ue($(e5, ((t) => C((n) => Ve(n) && n > t))(r))), lte: (r) => Ue($(e5, ((t) => C((n) => Ve(n) && n <= t))(r))), gte: (r) => Ue($(e5, ((t) => C((n) => Ve(n) && n >= t))(r))), positive: () => Ue($(e5, C((r) => Ve(r) && r > 0))), negative: () => Ue($(e5, C((r) => Ve(r) && r < 0))) });
var ng = Ue(C(Ve));
var ig = ce(C(function(e5) {
  return typeof e5 == "boolean";
}));
var og = ce(C(function(e5) {
  return typeof e5 == "symbol";
}));
var sg = ce(C(function(e5) {
  return e5 == null;
}));
var ag = ce(C(function(e5) {
  return e5 != null;
}));
var Ou = { warn: ke("prisma:warn") };
var Lu = (0, import_node_util.promisify)(import_node_child_process.default.exec);
var z = hr("prisma:get-platform");
var Jt = {};
gr(Jt, { beep: () => bc, clearScreen: () => fc, clearTerminal: () => gc, cursorBackward: () => Xu, cursorDown: () => zu, cursorForward: () => Zu, cursorGetPosition: () => tc, cursorHide: () => oc, cursorLeft: () => zo, cursorMove: () => Yu, cursorNextLine: () => nc, cursorPrevLine: () => ic, cursorRestorePosition: () => rc, cursorSavePosition: () => ec, cursorShow: () => sc, cursorTo: () => Ku, cursorUp: () => Yo, enterAlternativeScreen: () => hc, eraseDown: () => cc, eraseEndLine: () => lc, eraseLine: () => Zo, eraseLines: () => ac, eraseScreen: () => ni, eraseStartLine: () => uc, eraseUp: () => pc, exitAlternativeScreen: () => yc, iTerm: () => xc, image: () => wc, link: () => Ec, scrollDown: () => mc, scrollUp: () => dc });
var _a;
var Wt = ((_a = globalThis.window) == null ? void 0 : _a.document) !== void 0;
var _a2, _b;
var Eg = ((_b = (_a2 = globalThis.process) == null ? void 0 : _a2.versions) == null ? void 0 : _b.node) !== void 0;
var _a3, _b2;
var wg = ((_b2 = (_a3 = globalThis.process) == null ? void 0 : _a3.versions) == null ? void 0 : _b2.bun) !== void 0;
var _a4, _b3;
var xg = ((_b3 = (_a4 = globalThis.Deno) == null ? void 0 : _a4.version) == null ? void 0 : _b3.deno) !== void 0;
var _a5, _b4;
var vg = ((_b4 = (_a5 = globalThis.process) == null ? void 0 : _a5.versions) == null ? void 0 : _b4.electron) !== void 0;
var _a6, _b5;
var Pg = ((_b5 = (_a6 = globalThis.navigator) == null ? void 0 : _a6.userAgent) == null ? void 0 : _b5.includes("jsdom")) === true;
var Tg = typeof WorkerGlobalScope < "u" && globalThis instanceof WorkerGlobalScope;
var Sg = typeof DedicatedWorkerGlobalScope < "u" && globalThis instanceof DedicatedWorkerGlobalScope;
var Rg = typeof SharedWorkerGlobalScope < "u" && globalThis instanceof SharedWorkerGlobalScope;
var Cg = typeof ServiceWorkerGlobalScope < "u" && globalThis instanceof ServiceWorkerGlobalScope;
var _a7, _b6;
var Yr = (_b6 = (_a7 = globalThis.navigator) == null ? void 0 : _a7.userAgentData) == null ? void 0 : _b6.platform;
var _a8, _b7, _c, _d;
var Ag = Yr === "macOS" || ((_a8 = globalThis.navigator) == null ? void 0 : _a8.platform) === "MacIntel" || ((_c = (_b7 = globalThis.navigator) == null ? void 0 : _b7.userAgent) == null ? void 0 : _c.includes(" Mac ")) === true || ((_d = globalThis.process) == null ? void 0 : _d.platform) === "darwin";
var _a9, _b8;
var Ig = Yr === "Windows" || ((_a9 = globalThis.navigator) == null ? void 0 : _a9.platform) === "Win32" || ((_b8 = globalThis.process) == null ? void 0 : _b8.platform) === "win32";
var _a10, _b9, _c2, _d2, _e;
var kg = Yr === "Linux" || ((_b9 = (_a10 = globalThis.navigator) == null ? void 0 : _a10.platform) == null ? void 0 : _b9.startsWith("Linux")) === true || ((_d2 = (_c2 = globalThis.navigator) == null ? void 0 : _c2.userAgent) == null ? void 0 : _d2.includes(" Linux ")) === true || ((_e = globalThis.process) == null ? void 0 : _e.platform) === "linux";
var _a11, _b10, _c3;
var Og = Yr === "iOS" || ((_a11 = globalThis.navigator) == null ? void 0 : _a11.platform) === "MacIntel" && ((_b10 = globalThis.navigator) == null ? void 0 : _b10.maxTouchPoints) > 1 || /iPad|iPhone|iPod/.test((_c3 = globalThis.navigator) == null ? void 0 : _c3.platform);
var _a12, _b11, _c4, _d3;
var Dg = Yr === "Android" || ((_a12 = globalThis.navigator) == null ? void 0 : _a12.platform) === "Android" || ((_c4 = (_b11 = globalThis.navigator) == null ? void 0 : _b11.userAgent) == null ? void 0 : _c4.includes(" Android ")) === true || ((_d3 = globalThis.process) == null ? void 0 : _d3.platform) === "android";
var A = "\x1B[";
var Zr = "\x1B]";
var br = "\x07";
var zr = ";";
var Ko = !Wt && import_node_process.default.env.TERM_PROGRAM === "Apple_Terminal";
var Ju = !Wt && import_node_process.default.platform === "win32";
var Hu = Wt ? () => {
  throw new Error("`process.cwd()` only works in Node.js, not the browser.");
} : import_node_process.default.cwd;
var Ku = (e5, r) => {
  if (typeof e5 != "number") throw new TypeError("The `x` argument is required");
  return typeof r != "number" ? A + (e5 + 1) + "G" : A + (r + 1) + zr + (e5 + 1) + "H";
};
var Yu = (e5, r) => {
  if (typeof e5 != "number") throw new TypeError("The `x` argument is required");
  let t = "";
  return e5 < 0 ? t += A + -e5 + "D" : e5 > 0 && (t += A + e5 + "C"), r < 0 ? t += A + -r + "A" : r > 0 && (t += A + r + "B"), t;
};
var Yo = (e5 = 1) => A + e5 + "A";
var zu = (e5 = 1) => A + e5 + "B";
var Zu = (e5 = 1) => A + e5 + "C";
var Xu = (e5 = 1) => A + e5 + "D";
var zo = A + "G";
var ec = Ko ? "\x1B7" : A + "s";
var rc = Ko ? "\x1B8" : A + "u";
var tc = A + "6n";
var nc = A + "E";
var ic = A + "F";
var oc = A + "?25l";
var sc = A + "?25h";
var ac = (e5) => {
  let r = "";
  for (let t = 0; t < e5; t++) r += Zo + (t < e5 - 1 ? Yo() : "");
  return e5 && (r += zo), r;
};
var lc = A + "K";
var uc = A + "1K";
var Zo = A + "2K";
var cc = A + "J";
var pc = A + "1J";
var ni = A + "2J";
var dc = A + "S";
var mc = A + "T";
var fc = "\x1Bc";
var gc = Ju ? `${ni}${A}0f` : `${ni}${A}3J${A}H`;
var hc = A + "?1049h";
var yc = A + "?1049l";
var bc = br;
var Ec = (e5, r) => [Zr, "8", zr, zr, r, br, e5, Zr, "8", zr, zr, br].join("");
var wc = (e5, r = {}) => {
  let t = `${Zr}1337;File=inline=1`;
  return r.width && (t += `;width=${r.width}`), r.height && (t += `;height=${r.height}`), r.preserveAspectRatio === false && (t += ";preserveAspectRatio=0"), t + ":" + Buffer.from(e5).toString("base64") + br;
};
var xc = { setCwd: (e5 = Hu()) => `${Zr}50;CurrentDir=${e5}${br}`, annotation(e5, r = {}) {
  let t = `${Zr}1337;`, n = r.x !== void 0, i = r.y !== void 0;
  if ((n || i) && !(n && i && r.length !== void 0)) throw new Error("`x`, `y` and `length` must be defined when `x` or `y` is defined");
  return e5 = e5.replaceAll("|", ""), t += r.isHidden ? "AddHiddenAnnotation=" : "AddAnnotation=", r.length > 0 ? t += (n ? [e5, r.length, r.x, r.y] : [r.length, e5]).join("|") : t += e5, t + br;
} };
var Ht = se(os(), 1);
function ir(e5, r, { target: t = "stdout", ...n } = {}) {
  return Ht.default[t] ? Jt.link(e5, r) : n.fallback === false ? e5 : typeof n.fallback == "function" ? n.fallback(e5, r) : `${e5} (​${r}​)`;
}
ir.isSupported = Ht.default.stdout;
ir.stderr = (e5, r, t = {}) => ir(e5, r, { target: "stderr", ...t });
ir.stderr.isSupported = Ht.default.stderr;
var Rc = ss();
var ci = Rc.version;
var kc = se(di());
var Oc = se(di());
var mh = L("prisma:engines");
import_node_path.default.join(__dirname, "../query-engine-darwin");
import_node_path.default.join(__dirname, "../query-engine-darwin-arm64");
import_node_path.default.join(__dirname, "../query-engine-debian-openssl-1.0.x");
import_node_path.default.join(__dirname, "../query-engine-debian-openssl-1.1.x");
import_node_path.default.join(__dirname, "../query-engine-debian-openssl-3.0.x");
import_node_path.default.join(__dirname, "../query-engine-linux-static-x64");
import_node_path.default.join(__dirname, "../query-engine-linux-static-arm64");
import_node_path.default.join(__dirname, "../query-engine-rhel-openssl-1.0.x");
import_node_path.default.join(__dirname, "../query-engine-rhel-openssl-1.1.x");
import_node_path.default.join(__dirname, "../query-engine-rhel-openssl-3.0.x");
import_node_path.default.join(__dirname, "../libquery_engine-darwin.dylib.node");
import_node_path.default.join(__dirname, "../libquery_engine-darwin-arm64.dylib.node");
import_node_path.default.join(__dirname, "../libquery_engine-debian-openssl-1.0.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-debian-openssl-1.1.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-debian-openssl-3.0.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-linux-arm64-openssl-1.0.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-linux-arm64-openssl-1.1.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-linux-arm64-openssl-3.0.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-linux-musl.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-linux-musl-openssl-3.0.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-rhel-openssl-1.0.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-rhel-openssl-1.1.x.so.node");
import_node_path.default.join(__dirname, "../libquery_engine-rhel-openssl-3.0.x.so.node");
import_node_path.default.join(__dirname, "../query_engine-windows.dll.node");
var us = hr("chmodPlusX");
var ds = se(ps(), 1);
var ms = "prisma+postgres";
var Yt = `${ms}:`;
var gs = se(yi());
var et = {};
gr(et, { error: () => Mc, info: () => Fc, log: () => Lc, query: () => $c, should: () => hs, tags: () => Xr, warn: () => wi });
var Xr = { error: ue("prisma:error"), warn: ke("prisma:warn"), info: Oe("prisma:info"), query: tr("prisma:query") };
var hs = { warn: () => !process.env.PRISMA_DISABLE_WARNINGS };
function Lc(...e5) {
  console.log(...e5);
}
function wi(e5, ...r) {
  hs.warn() && console.warn(`${Xr.warn} ${e5}`, ...r);
}
function Fc(e5, ...r) {
  console.info(`${Xr.info} ${e5}`, ...r);
}
function Mc(e5, ...r) {
  console.error(`${Xr.error} ${e5}`, ...r);
}
function $c(e5, ...r) {
  console.log(`${Xr.query} ${e5}`, ...r);
}
var Ci = se(Ss());
var Ri = hr("prisma:tryLoadEnv");
function x(e5, r) {
  Object.defineProperty(e5, "name", { value: r, configurable: true });
}
var T = class e extends Error {
  constructor(r, t, n) {
    super(r);
    __publicField(this, "clientVersion");
    __publicField(this, "errorCode");
    __publicField(this, "retryable");
    this.name = "PrismaClientInitializationError", this.clientVersion = t, this.errorCode = n, Error.captureStackTrace(e);
  }
  get [Symbol.toStringTag]() {
    return "PrismaClientInitializationError";
  }
};
x(T, "PrismaClientInitializationError");
var Z = class extends Error {
  constructor(r, { code: t, clientVersion: n, meta: i, batchRequestIdx: o }) {
    super(r);
    __publicField(this, "code");
    __publicField(this, "meta");
    __publicField(this, "clientVersion");
    __publicField(this, "batchRequestIdx");
    this.name = "PrismaClientKnownRequestError", this.code = t, this.clientVersion = n, this.meta = i, Object.defineProperty(this, "batchRequestIdx", { value: o, enumerable: false, writable: true });
  }
  get [Symbol.toStringTag]() {
    return "PrismaClientKnownRequestError";
  }
};
x(Z, "PrismaClientKnownRequestError");
var de = class extends Error {
  constructor(r, t) {
    super(r);
    __publicField(this, "clientVersion");
    this.name = "PrismaClientRustPanicError", this.clientVersion = t;
  }
  get [Symbol.toStringTag]() {
    return "PrismaClientRustPanicError";
  }
};
x(de, "PrismaClientRustPanicError");
var q = class extends Error {
  constructor(r, { clientVersion: t, batchRequestIdx: n }) {
    super(r);
    __publicField(this, "clientVersion");
    __publicField(this, "batchRequestIdx");
    this.name = "PrismaClientUnknownRequestError", this.clientVersion = t, Object.defineProperty(this, "batchRequestIdx", { value: n, writable: true, enumerable: false });
  }
  get [Symbol.toStringTag]() {
    return "PrismaClientUnknownRequestError";
  }
};
x(q, "PrismaClientUnknownRequestError");
var X = class extends Error {
  constructor(r, { clientVersion: t }) {
    super(r);
    __publicField(this, "name", "PrismaClientValidationError");
    __publicField(this, "clientVersion");
    this.clientVersion = t;
  }
  get [Symbol.toStringTag]() {
    return "PrismaClientValidationError";
  }
};
x(X, "PrismaClientValidationError");
var vr = 9e15;
var Ke = 1e9;
var ki = "0123456789abcdef";
var sn = "2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058";
var an = "3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789";
var Oi = { precision: 20, rounding: 4, modulo: 1, toExpNeg: -7, toExpPos: 21, minE: -vr, maxE: vr, crypto: false };
var Ns;
var Le;
var w = true;
var un = "[DecimalError] ";
var He = un + "Invalid argument: ";
var Ls = un + "Precision limit exceeded";
var Fs = un + "crypto unavailable";
var Ms = "[object Decimal]";
var Y = Math.floor;
var B = Math.pow;
var tp = /^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i;
var np = /^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i;
var ip = /^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i;
var $s = /^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;
var fe = 1e7;
var E = 7;
var op = 9007199254740991;
var sp = sn.length - 1;
var Di = an.length - 1;
var m = { toStringTag: Ms };
m.absoluteValue = m.abs = function() {
  var e5 = new this.constructor(this);
  return e5.s < 0 && (e5.s = 1), y(e5);
};
m.ceil = function() {
  return y(new this.constructor(this), this.e + 1, 2);
};
m.clampedTo = m.clamp = function(e5, r) {
  var t, n = this, i = n.constructor;
  if (e5 = new i(e5), r = new i(r), !e5.s || !r.s) return new i(NaN);
  if (e5.gt(r)) throw Error(He + r);
  return t = n.cmp(e5), t < 0 ? e5 : n.cmp(r) > 0 ? r : new i(n);
};
m.comparedTo = m.cmp = function(e5) {
  var r, t, n, i, o = this, s = o.d, a = (e5 = new o.constructor(e5)).d, l = o.s, u = e5.s;
  if (!s || !a) return !l || !u ? NaN : l !== u ? l : s === a ? 0 : !s ^ l < 0 ? 1 : -1;
  if (!s[0] || !a[0]) return s[0] ? l : a[0] ? -u : 0;
  if (l !== u) return l;
  if (o.e !== e5.e) return o.e > e5.e ^ l < 0 ? 1 : -1;
  for (n = s.length, i = a.length, r = 0, t = n < i ? n : i; r < t; ++r) if (s[r] !== a[r]) return s[r] > a[r] ^ l < 0 ? 1 : -1;
  return n === i ? 0 : n > i ^ l < 0 ? 1 : -1;
};
m.cosine = m.cos = function() {
  var e5, r, t = this, n = t.constructor;
  return t.d ? t.d[0] ? (e5 = n.precision, r = n.rounding, n.precision = e5 + Math.max(t.e, t.sd()) + E, n.rounding = 1, t = ap(n, Us(n, t)), n.precision = e5, n.rounding = r, y(Le == 2 || Le == 3 ? t.neg() : t, e5, r, true)) : new n(1) : new n(NaN);
};
m.cubeRoot = m.cbrt = function() {
  var e5, r, t, n, i, o, s, a, l, u, c = this, p = c.constructor;
  if (!c.isFinite() || c.isZero()) return new p(c);
  for (w = false, o = c.s * B(c.s * c, 1 / 3), !o || Math.abs(o) == 1 / 0 ? (t = W(c.d), e5 = c.e, (o = (e5 - t.length + 1) % 3) && (t += o == 1 || o == -2 ? "0" : "00"), o = B(t, 1 / 3), e5 = Y((e5 + 1) / 3) - (e5 % 3 == (e5 < 0 ? -1 : 2)), o == 1 / 0 ? t = "5e" + e5 : (t = o.toExponential(), t = t.slice(0, t.indexOf("e") + 1) + e5), n = new p(t), n.s = c.s) : n = new p(o.toString()), s = (e5 = p.precision) + 3; ; ) if (a = n, l = a.times(a).times(a), u = l.plus(c), n = _(u.plus(c).times(a), u.plus(l), s + 2, 1), W(a.d).slice(0, s) === (t = W(n.d)).slice(0, s)) if (t = t.slice(s - 3, s + 1), t == "9999" || !i && t == "4999") {
    if (!i && (y(a, e5 + 1, 0), a.times(a).times(a).eq(c))) {
      n = a;
      break;
    }
    s += 4, i = 1;
  } else {
    (!+t || !+t.slice(1) && t.charAt(0) == "5") && (y(n, e5 + 1, 1), r = !n.times(n).times(n).eq(c));
    break;
  }
  return w = true, y(n, e5, p.rounding, r);
};
m.decimalPlaces = m.dp = function() {
  var e5, r = this.d, t = NaN;
  if (r) {
    if (e5 = r.length - 1, t = (e5 - Y(this.e / E)) * E, e5 = r[e5], e5) for (; e5 % 10 == 0; e5 /= 10) t--;
    t < 0 && (t = 0);
  }
  return t;
};
m.dividedBy = m.div = function(e5) {
  return _(this, new this.constructor(e5));
};
m.dividedToIntegerBy = m.divToInt = function(e5) {
  var r = this, t = r.constructor;
  return y(_(r, new t(e5), 0, 1, 1), t.precision, t.rounding);
};
m.equals = m.eq = function(e5) {
  return this.cmp(e5) === 0;
};
m.floor = function() {
  return y(new this.constructor(this), this.e + 1, 3);
};
m.greaterThan = m.gt = function(e5) {
  return this.cmp(e5) > 0;
};
m.greaterThanOrEqualTo = m.gte = function(e5) {
  var r = this.cmp(e5);
  return r == 1 || r === 0;
};
m.hyperbolicCosine = m.cosh = function() {
  var e5, r, t, n, i, o = this, s = o.constructor, a = new s(1);
  if (!o.isFinite()) return new s(o.s ? 1 / 0 : NaN);
  if (o.isZero()) return a;
  t = s.precision, n = s.rounding, s.precision = t + Math.max(o.e, o.sd()) + 4, s.rounding = 1, i = o.d.length, i < 32 ? (e5 = Math.ceil(i / 3), r = (1 / pn(4, e5)).toString()) : (e5 = 16, r = "2.3283064365386962890625e-10"), o = Pr(s, 1, o.times(r), new s(1), true);
  for (var l, u = e5, c = new s(8); u--; ) l = o.times(o), o = a.minus(l.times(c.minus(l.times(c))));
  return y(o, s.precision = t, s.rounding = n, true);
};
m.hyperbolicSine = m.sinh = function() {
  var e5, r, t, n, i = this, o = i.constructor;
  if (!i.isFinite() || i.isZero()) return new o(i);
  if (r = o.precision, t = o.rounding, o.precision = r + Math.max(i.e, i.sd()) + 4, o.rounding = 1, n = i.d.length, n < 3) i = Pr(o, 2, i, i, true);
  else {
    e5 = 1.4 * Math.sqrt(n), e5 = e5 > 16 ? 16 : e5 | 0, i = i.times(1 / pn(5, e5)), i = Pr(o, 2, i, i, true);
    for (var s, a = new o(5), l = new o(16), u = new o(20); e5--; ) s = i.times(i), i = i.times(a.plus(s.times(l.times(s).plus(u))));
  }
  return o.precision = r, o.rounding = t, y(i, r, t, true);
};
m.hyperbolicTangent = m.tanh = function() {
  var e5, r, t = this, n = t.constructor;
  return t.isFinite() ? t.isZero() ? new n(t) : (e5 = n.precision, r = n.rounding, n.precision = e5 + 7, n.rounding = 1, _(t.sinh(), t.cosh(), n.precision = e5, n.rounding = r)) : new n(t.s);
};
m.inverseCosine = m.acos = function() {
  var e5 = this, r = e5.constructor, t = e5.abs().cmp(1), n = r.precision, i = r.rounding;
  return t !== -1 ? t === 0 ? e5.isNeg() ? we(r, n, i) : new r(0) : new r(NaN) : e5.isZero() ? we(r, n + 4, i).times(0.5) : (r.precision = n + 6, r.rounding = 1, e5 = new r(1).minus(e5).div(e5.plus(1)).sqrt().atan(), r.precision = n, r.rounding = i, e5.times(2));
};
m.inverseHyperbolicCosine = m.acosh = function() {
  var e5, r, t = this, n = t.constructor;
  return t.lte(1) ? new n(t.eq(1) ? 0 : NaN) : t.isFinite() ? (e5 = n.precision, r = n.rounding, n.precision = e5 + Math.max(Math.abs(t.e), t.sd()) + 4, n.rounding = 1, w = false, t = t.times(t).minus(1).sqrt().plus(t), w = true, n.precision = e5, n.rounding = r, t.ln()) : new n(t);
};
m.inverseHyperbolicSine = m.asinh = function() {
  var e5, r, t = this, n = t.constructor;
  return !t.isFinite() || t.isZero() ? new n(t) : (e5 = n.precision, r = n.rounding, n.precision = e5 + 2 * Math.max(Math.abs(t.e), t.sd()) + 6, n.rounding = 1, w = false, t = t.times(t).plus(1).sqrt().plus(t), w = true, n.precision = e5, n.rounding = r, t.ln());
};
m.inverseHyperbolicTangent = m.atanh = function() {
  var e5, r, t, n, i = this, o = i.constructor;
  return i.isFinite() ? i.e >= 0 ? new o(i.abs().eq(1) ? i.s / 0 : i.isZero() ? i : NaN) : (e5 = o.precision, r = o.rounding, n = i.sd(), Math.max(n, e5) < 2 * -i.e - 1 ? y(new o(i), e5, r, true) : (o.precision = t = n - i.e, i = _(i.plus(1), new o(1).minus(i), t + e5, 1), o.precision = e5 + 4, o.rounding = 1, i = i.ln(), o.precision = e5, o.rounding = r, i.times(0.5))) : new o(NaN);
};
m.inverseSine = m.asin = function() {
  var e5, r, t, n, i = this, o = i.constructor;
  return i.isZero() ? new o(i) : (r = i.abs().cmp(1), t = o.precision, n = o.rounding, r !== -1 ? r === 0 ? (e5 = we(o, t + 4, n).times(0.5), e5.s = i.s, e5) : new o(NaN) : (o.precision = t + 6, o.rounding = 1, i = i.div(new o(1).minus(i.times(i)).sqrt().plus(1)).atan(), o.precision = t, o.rounding = n, i.times(2)));
};
m.inverseTangent = m.atan = function() {
  var e5, r, t, n, i, o, s, a, l, u = this, c = u.constructor, p = c.precision, d = c.rounding;
  if (u.isFinite()) {
    if (u.isZero()) return new c(u);
    if (u.abs().eq(1) && p + 4 <= Di) return s = we(c, p + 4, d).times(0.25), s.s = u.s, s;
  } else {
    if (!u.s) return new c(NaN);
    if (p + 4 <= Di) return s = we(c, p + 4, d).times(0.5), s.s = u.s, s;
  }
  for (c.precision = a = p + 10, c.rounding = 1, t = Math.min(28, a / E + 2 | 0), e5 = t; e5; --e5) u = u.div(u.times(u).plus(1).sqrt().plus(1));
  for (w = false, r = Math.ceil(a / E), n = 1, l = u.times(u), s = new c(u), i = u; e5 !== -1; ) if (i = i.times(l), o = s.minus(i.div(n += 2)), i = i.times(l), s = o.plus(i.div(n += 2)), s.d[r] !== void 0) for (e5 = r; s.d[e5] === o.d[e5] && e5--; ) ;
  return t && (s = s.times(2 << t - 1)), w = true, y(s, c.precision = p, c.rounding = d, true);
};
m.isFinite = function() {
  return !!this.d;
};
m.isInteger = m.isInt = function() {
  return !!this.d && Y(this.e / E) > this.d.length - 2;
};
m.isNaN = function() {
  return !this.s;
};
m.isNegative = m.isNeg = function() {
  return this.s < 0;
};
m.isPositive = m.isPos = function() {
  return this.s > 0;
};
m.isZero = function() {
  return !!this.d && this.d[0] === 0;
};
m.lessThan = m.lt = function(e5) {
  return this.cmp(e5) < 0;
};
m.lessThanOrEqualTo = m.lte = function(e5) {
  return this.cmp(e5) < 1;
};
m.logarithm = m.log = function(e5) {
  var r, t, n, i, o, s, a, l, u = this, c = u.constructor, p = c.precision, d = c.rounding, f = 5;
  if (e5 == null) e5 = new c(10), r = true;
  else {
    if (e5 = new c(e5), t = e5.d, e5.s < 0 || !t || !t[0] || e5.eq(1)) return new c(NaN);
    r = e5.eq(10);
  }
  if (t = u.d, u.s < 0 || !t || !t[0] || u.eq(1)) return new c(t && !t[0] ? -1 / 0 : u.s != 1 ? NaN : t ? 0 : 1 / 0);
  if (r) if (t.length > 1) o = true;
  else {
    for (i = t[0]; i % 10 === 0; ) i /= 10;
    o = i !== 1;
  }
  if (w = false, a = p + f, s = Je(u, a), n = r ? ln(c, a + 10) : Je(e5, a), l = _(s, n, a, 1), it(l.d, i = p, d)) do
    if (a += 10, s = Je(u, a), n = r ? ln(c, a + 10) : Je(e5, a), l = _(s, n, a, 1), !o) {
      +W(l.d).slice(i + 1, i + 15) + 1 == 1e14 && (l = y(l, p + 1, 0));
      break;
    }
  while (it(l.d, i += 10, d));
  return w = true, y(l, p, d);
};
m.minus = m.sub = function(e5) {
  var r, t, n, i, o, s, a, l, u, c, p, d, f = this, g = f.constructor;
  if (e5 = new g(e5), !f.d || !e5.d) return !f.s || !e5.s ? e5 = new g(NaN) : f.d ? e5.s = -e5.s : e5 = new g(e5.d || f.s !== e5.s ? f : NaN), e5;
  if (f.s != e5.s) return e5.s = -e5.s, f.plus(e5);
  if (u = f.d, d = e5.d, a = g.precision, l = g.rounding, !u[0] || !d[0]) {
    if (d[0]) e5.s = -e5.s;
    else if (u[0]) e5 = new g(f);
    else return new g(l === 3 ? -0 : 0);
    return w ? y(e5, a, l) : e5;
  }
  if (t = Y(e5.e / E), c = Y(f.e / E), u = u.slice(), o = c - t, o) {
    for (p = o < 0, p ? (r = u, o = -o, s = d.length) : (r = d, t = c, s = u.length), n = Math.max(Math.ceil(a / E), s) + 2, o > n && (o = n, r.length = 1), r.reverse(), n = o; n--; ) r.push(0);
    r.reverse();
  } else {
    for (n = u.length, s = d.length, p = n < s, p && (s = n), n = 0; n < s; n++) if (u[n] != d[n]) {
      p = u[n] < d[n];
      break;
    }
    o = 0;
  }
  for (p && (r = u, u = d, d = r, e5.s = -e5.s), s = u.length, n = d.length - s; n > 0; --n) u[s++] = 0;
  for (n = d.length; n > o; ) {
    if (u[--n] < d[n]) {
      for (i = n; i && u[--i] === 0; ) u[i] = fe - 1;
      --u[i], u[n] += fe;
    }
    u[n] -= d[n];
  }
  for (; u[--s] === 0; ) u.pop();
  for (; u[0] === 0; u.shift()) --t;
  return u[0] ? (e5.d = u, e5.e = cn(u, t), w ? y(e5, a, l) : e5) : new g(l === 3 ? -0 : 0);
};
m.modulo = m.mod = function(e5) {
  var r, t = this, n = t.constructor;
  return e5 = new n(e5), !t.d || !e5.s || e5.d && !e5.d[0] ? new n(NaN) : !e5.d || t.d && !t.d[0] ? y(new n(t), n.precision, n.rounding) : (w = false, n.modulo == 9 ? (r = _(t, e5.abs(), 0, 3, 1), r.s *= e5.s) : r = _(t, e5, 0, n.modulo, 1), r = r.times(e5), w = true, t.minus(r));
};
m.naturalExponential = m.exp = function() {
  return _i(this);
};
m.naturalLogarithm = m.ln = function() {
  return Je(this);
};
m.negated = m.neg = function() {
  var e5 = new this.constructor(this);
  return e5.s = -e5.s, y(e5);
};
m.plus = m.add = function(e5) {
  var r, t, n, i, o, s, a, l, u, c, p = this, d = p.constructor;
  if (e5 = new d(e5), !p.d || !e5.d) return !p.s || !e5.s ? e5 = new d(NaN) : p.d || (e5 = new d(e5.d || p.s === e5.s ? p : NaN)), e5;
  if (p.s != e5.s) return e5.s = -e5.s, p.minus(e5);
  if (u = p.d, c = e5.d, a = d.precision, l = d.rounding, !u[0] || !c[0]) return c[0] || (e5 = new d(p)), w ? y(e5, a, l) : e5;
  if (o = Y(p.e / E), n = Y(e5.e / E), u = u.slice(), i = o - n, i) {
    for (i < 0 ? (t = u, i = -i, s = c.length) : (t = c, n = o, s = u.length), o = Math.ceil(a / E), s = o > s ? o + 1 : s + 1, i > s && (i = s, t.length = 1), t.reverse(); i--; ) t.push(0);
    t.reverse();
  }
  for (s = u.length, i = c.length, s - i < 0 && (i = s, t = c, c = u, u = t), r = 0; i; ) r = (u[--i] = u[i] + c[i] + r) / fe | 0, u[i] %= fe;
  for (r && (u.unshift(r), ++n), s = u.length; u[--s] == 0; ) u.pop();
  return e5.d = u, e5.e = cn(u, n), w ? y(e5, a, l) : e5;
};
m.precision = m.sd = function(e5) {
  var r, t = this;
  if (e5 !== void 0 && e5 !== !!e5 && e5 !== 1 && e5 !== 0) throw Error(He + e5);
  return t.d ? (r = qs(t.d), e5 && t.e + 1 > r && (r = t.e + 1)) : r = NaN, r;
};
m.round = function() {
  var e5 = this, r = e5.constructor;
  return y(new r(e5), e5.e + 1, r.rounding);
};
m.sine = m.sin = function() {
  var e5, r, t = this, n = t.constructor;
  return t.isFinite() ? t.isZero() ? new n(t) : (e5 = n.precision, r = n.rounding, n.precision = e5 + Math.max(t.e, t.sd()) + E, n.rounding = 1, t = up(n, Us(n, t)), n.precision = e5, n.rounding = r, y(Le > 2 ? t.neg() : t, e5, r, true)) : new n(NaN);
};
m.squareRoot = m.sqrt = function() {
  var e5, r, t, n, i, o, s = this, a = s.d, l = s.e, u = s.s, c = s.constructor;
  if (u !== 1 || !a || !a[0]) return new c(!u || u < 0 && (!a || a[0]) ? NaN : a ? s : 1 / 0);
  for (w = false, u = Math.sqrt(+s), u == 0 || u == 1 / 0 ? (r = W(a), (r.length + l) % 2 == 0 && (r += "0"), u = Math.sqrt(r), l = Y((l + 1) / 2) - (l < 0 || l % 2), u == 1 / 0 ? r = "5e" + l : (r = u.toExponential(), r = r.slice(0, r.indexOf("e") + 1) + l), n = new c(r)) : n = new c(u.toString()), t = (l = c.precision) + 3; ; ) if (o = n, n = o.plus(_(s, o, t + 2, 1)).times(0.5), W(o.d).slice(0, t) === (r = W(n.d)).slice(0, t)) if (r = r.slice(t - 3, t + 1), r == "9999" || !i && r == "4999") {
    if (!i && (y(o, l + 1, 0), o.times(o).eq(s))) {
      n = o;
      break;
    }
    t += 4, i = 1;
  } else {
    (!+r || !+r.slice(1) && r.charAt(0) == "5") && (y(n, l + 1, 1), e5 = !n.times(n).eq(s));
    break;
  }
  return w = true, y(n, l, c.rounding, e5);
};
m.tangent = m.tan = function() {
  var e5, r, t = this, n = t.constructor;
  return t.isFinite() ? t.isZero() ? new n(t) : (e5 = n.precision, r = n.rounding, n.precision = e5 + 10, n.rounding = 1, t = t.sin(), t.s = 1, t = _(t, new n(1).minus(t.times(t)).sqrt(), e5 + 10, 0), n.precision = e5, n.rounding = r, y(Le == 2 || Le == 4 ? t.neg() : t, e5, r, true)) : new n(NaN);
};
m.times = m.mul = function(e5) {
  var r, t, n, i, o, s, a, l, u, c = this, p = c.constructor, d = c.d, f = (e5 = new p(e5)).d;
  if (e5.s *= c.s, !d || !d[0] || !f || !f[0]) return new p(!e5.s || d && !d[0] && !f || f && !f[0] && !d ? NaN : !d || !f ? e5.s / 0 : e5.s * 0);
  for (t = Y(c.e / E) + Y(e5.e / E), l = d.length, u = f.length, l < u && (o = d, d = f, f = o, s = l, l = u, u = s), o = [], s = l + u, n = s; n--; ) o.push(0);
  for (n = u; --n >= 0; ) {
    for (r = 0, i = l + n; i > n; ) a = o[i] + f[n] * d[i - n - 1] + r, o[i--] = a % fe | 0, r = a / fe | 0;
    o[i] = (o[i] + r) % fe | 0;
  }
  for (; !o[--s]; ) o.pop();
  return r ? ++t : o.shift(), e5.d = o, e5.e = cn(o, t), w ? y(e5, p.precision, p.rounding) : e5;
};
m.toBinary = function(e5, r) {
  return Ni(this, 2, e5, r);
};
m.toDecimalPlaces = m.toDP = function(e5, r) {
  var t = this, n = t.constructor;
  return t = new n(t), e5 === void 0 ? t : (ne(e5, 0, Ke), r === void 0 ? r = n.rounding : ne(r, 0, 8), y(t, e5 + t.e + 1, r));
};
m.toExponential = function(e5, r) {
  var t, n = this, i = n.constructor;
  return e5 === void 0 ? t = xe(n, true) : (ne(e5, 0, Ke), r === void 0 ? r = i.rounding : ne(r, 0, 8), n = y(new i(n), e5 + 1, r), t = xe(n, true, e5 + 1)), n.isNeg() && !n.isZero() ? "-" + t : t;
};
m.toFixed = function(e5, r) {
  var t, n, i = this, o = i.constructor;
  return e5 === void 0 ? t = xe(i) : (ne(e5, 0, Ke), r === void 0 ? r = o.rounding : ne(r, 0, 8), n = y(new o(i), e5 + i.e + 1, r), t = xe(n, false, e5 + n.e + 1)), i.isNeg() && !i.isZero() ? "-" + t : t;
};
m.toFraction = function(e5) {
  var r, t, n, i, o, s, a, l, u, c, p, d, f = this, g = f.d, h = f.constructor;
  if (!g) return new h(f);
  if (u = t = new h(1), n = l = new h(0), r = new h(n), o = r.e = qs(g) - f.e - 1, s = o % E, r.d[0] = B(10, s < 0 ? E + s : s), e5 == null) e5 = o > 0 ? r : u;
  else {
    if (a = new h(e5), !a.isInt() || a.lt(u)) throw Error(He + a);
    e5 = a.gt(r) ? o > 0 ? r : u : a;
  }
  for (w = false, a = new h(W(g)), c = h.precision, h.precision = o = g.length * E * 2; p = _(a, r, 0, 1, 1), i = t.plus(p.times(n)), i.cmp(e5) != 1; ) t = n, n = i, i = u, u = l.plus(p.times(i)), l = i, i = r, r = a.minus(p.times(i)), a = i;
  return i = _(e5.minus(t), n, 0, 1, 1), l = l.plus(i.times(u)), t = t.plus(i.times(n)), l.s = u.s = f.s, d = _(u, n, o, 1).minus(f).abs().cmp(_(l, t, o, 1).minus(f).abs()) < 1 ? [u, n] : [l, t], h.precision = c, w = true, d;
};
m.toHexadecimal = m.toHex = function(e5, r) {
  return Ni(this, 16, e5, r);
};
m.toNearest = function(e5, r) {
  var t = this, n = t.constructor;
  if (t = new n(t), e5 == null) {
    if (!t.d) return t;
    e5 = new n(1), r = n.rounding;
  } else {
    if (e5 = new n(e5), r === void 0 ? r = n.rounding : ne(r, 0, 8), !t.d) return e5.s ? t : e5;
    if (!e5.d) return e5.s && (e5.s = t.s), e5;
  }
  return e5.d[0] ? (w = false, t = _(t, e5, 0, r, 1).times(e5), w = true, y(t)) : (e5.s = t.s, t = e5), t;
};
m.toNumber = function() {
  return +this;
};
m.toOctal = function(e5, r) {
  return Ni(this, 8, e5, r);
};
m.toPower = m.pow = function(e5) {
  var r, t, n, i, o, s, a = this, l = a.constructor, u = +(e5 = new l(e5));
  if (!a.d || !e5.d || !a.d[0] || !e5.d[0]) return new l(B(+a, u));
  if (a = new l(a), a.eq(1)) return a;
  if (n = l.precision, o = l.rounding, e5.eq(1)) return y(a, n, o);
  if (r = Y(e5.e / E), r >= e5.d.length - 1 && (t = u < 0 ? -u : u) <= op) return i = js(l, a, t, n), e5.s < 0 ? new l(1).div(i) : y(i, n, o);
  if (s = a.s, s < 0) {
    if (r < e5.d.length - 1) return new l(NaN);
    if ((e5.d[r] & 1) == 0 && (s = 1), a.e == 0 && a.d[0] == 1 && a.d.length == 1) return a.s = s, a;
  }
  return t = B(+a, u), r = t == 0 || !isFinite(t) ? Y(u * (Math.log("0." + W(a.d)) / Math.LN10 + a.e + 1)) : new l(t + "").e, r > l.maxE + 1 || r < l.minE - 1 ? new l(r > 0 ? s / 0 : 0) : (w = false, l.rounding = a.s = 1, t = Math.min(12, (r + "").length), i = _i(e5.times(Je(a, n + t)), n), i.d && (i = y(i, n + 5, 1), it(i.d, n, o) && (r = n + 10, i = y(_i(e5.times(Je(a, r + t)), r), r + 5, 1), +W(i.d).slice(n + 1, n + 15) + 1 == 1e14 && (i = y(i, n + 1, 0)))), i.s = s, w = true, l.rounding = o, y(i, n, o));
};
m.toPrecision = function(e5, r) {
  var t, n = this, i = n.constructor;
  return e5 === void 0 ? t = xe(n, n.e <= i.toExpNeg || n.e >= i.toExpPos) : (ne(e5, 1, Ke), r === void 0 ? r = i.rounding : ne(r, 0, 8), n = y(new i(n), e5, r), t = xe(n, e5 <= n.e || n.e <= i.toExpNeg, e5)), n.isNeg() && !n.isZero() ? "-" + t : t;
};
m.toSignificantDigits = m.toSD = function(e5, r) {
  var t = this, n = t.constructor;
  return e5 === void 0 ? (e5 = n.precision, r = n.rounding) : (ne(e5, 1, Ke), r === void 0 ? r = n.rounding : ne(r, 0, 8)), y(new n(t), e5, r);
};
m.toString = function() {
  var e5 = this, r = e5.constructor, t = xe(e5, e5.e <= r.toExpNeg || e5.e >= r.toExpPos);
  return e5.isNeg() && !e5.isZero() ? "-" + t : t;
};
m.truncated = m.trunc = function() {
  return y(new this.constructor(this), this.e + 1, 1);
};
m.valueOf = m.toJSON = function() {
  var e5 = this, r = e5.constructor, t = xe(e5, e5.e <= r.toExpNeg || e5.e >= r.toExpPos);
  return e5.isNeg() ? "-" + t : t;
};
function W(e5) {
  var r, t, n, i = e5.length - 1, o = "", s = e5[0];
  if (i > 0) {
    for (o += s, r = 1; r < i; r++) n = e5[r] + "", t = E - n.length, t && (o += We(t)), o += n;
    s = e5[r], n = s + "", t = E - n.length, t && (o += We(t));
  } else if (s === 0) return "0";
  for (; s % 10 === 0; ) s /= 10;
  return o + s;
}
function ne(e5, r, t) {
  if (e5 !== ~~e5 || e5 < r || e5 > t) throw Error(He + e5);
}
function it(e5, r, t, n) {
  var i, o, s, a;
  for (o = e5[0]; o >= 10; o /= 10) --r;
  return --r < 0 ? (r += E, i = 0) : (i = Math.ceil((r + 1) / E), r %= E), o = B(10, E - r), a = e5[i] % o | 0, n == null ? r < 3 ? (r == 0 ? a = a / 100 | 0 : r == 1 && (a = a / 10 | 0), s = t < 4 && a == 99999 || t > 3 && a == 49999 || a == 5e4 || a == 0) : s = (t < 4 && a + 1 == o || t > 3 && a + 1 == o / 2) && (e5[i + 1] / o / 100 | 0) == B(10, r - 2) - 1 || (a == o / 2 || a == 0) && (e5[i + 1] / o / 100 | 0) == 0 : r < 4 ? (r == 0 ? a = a / 1e3 | 0 : r == 1 ? a = a / 100 | 0 : r == 2 && (a = a / 10 | 0), s = (n || t < 4) && a == 9999 || !n && t > 3 && a == 4999) : s = ((n || t < 4) && a + 1 == o || !n && t > 3 && a + 1 == o / 2) && (e5[i + 1] / o / 1e3 | 0) == B(10, r - 3) - 1, s;
}
function nn(e5, r, t) {
  for (var n, i = [0], o, s = 0, a = e5.length; s < a; ) {
    for (o = i.length; o--; ) i[o] *= r;
    for (i[0] += ki.indexOf(e5.charAt(s++)), n = 0; n < i.length; n++) i[n] > t - 1 && (i[n + 1] === void 0 && (i[n + 1] = 0), i[n + 1] += i[n] / t | 0, i[n] %= t);
  }
  return i.reverse();
}
function ap(e5, r) {
  var t, n, i;
  if (r.isZero()) return r;
  n = r.d.length, n < 32 ? (t = Math.ceil(n / 3), i = (1 / pn(4, t)).toString()) : (t = 16, i = "2.3283064365386962890625e-10"), e5.precision += t, r = Pr(e5, 1, r.times(i), new e5(1));
  for (var o = t; o--; ) {
    var s = r.times(r);
    r = s.times(s).minus(s).times(8).plus(1);
  }
  return e5.precision -= t, r;
}
var _ = /* @__PURE__ */ function() {
  function e5(n, i, o) {
    var s, a = 0, l = n.length;
    for (n = n.slice(); l--; ) s = n[l] * i + a, n[l] = s % o | 0, a = s / o | 0;
    return a && n.unshift(a), n;
  }
  function r(n, i, o, s) {
    var a, l;
    if (o != s) l = o > s ? 1 : -1;
    else for (a = l = 0; a < o; a++) if (n[a] != i[a]) {
      l = n[a] > i[a] ? 1 : -1;
      break;
    }
    return l;
  }
  function t(n, i, o, s) {
    for (var a = 0; o--; ) n[o] -= a, a = n[o] < i[o] ? 1 : 0, n[o] = a * s + n[o] - i[o];
    for (; !n[0] && n.length > 1; ) n.shift();
  }
  return function(n, i, o, s, a, l) {
    var u, c, p, d, f, g, h, I, P, S, b, k, me, oe, Gr, j, re, Ae, J, mr, Mt = n.constructor, Gn = n.s == i.s ? 1 : -1, H = n.d, D = i.d;
    if (!H || !H[0] || !D || !D[0]) return new Mt(!n.s || !i.s || (H ? D && H[0] == D[0] : !D) ? NaN : H && H[0] == 0 || !D ? Gn * 0 : Gn / 0);
    for (l ? (f = 1, c = n.e - i.e) : (l = fe, f = E, c = Y(n.e / f) - Y(i.e / f)), J = D.length, re = H.length, P = new Mt(Gn), S = P.d = [], p = 0; D[p] == (H[p] || 0); p++) ;
    if (D[p] > (H[p] || 0) && c--, o == null ? (oe = o = Mt.precision, s = Mt.rounding) : a ? oe = o + (n.e - i.e) + 1 : oe = o, oe < 0) S.push(1), g = true;
    else {
      if (oe = oe / f + 2 | 0, p = 0, J == 1) {
        for (d = 0, D = D[0], oe++; (p < re || d) && oe--; p++) Gr = d * l + (H[p] || 0), S[p] = Gr / D | 0, d = Gr % D | 0;
        g = d || p < re;
      } else {
        for (d = l / (D[0] + 1) | 0, d > 1 && (D = e5(D, d, l), H = e5(H, d, l), J = D.length, re = H.length), j = J, b = H.slice(0, J), k = b.length; k < J; ) b[k++] = 0;
        mr = D.slice(), mr.unshift(0), Ae = D[0], D[1] >= l / 2 && ++Ae;
        do
          d = 0, u = r(D, b, J, k), u < 0 ? (me = b[0], J != k && (me = me * l + (b[1] || 0)), d = me / Ae | 0, d > 1 ? (d >= l && (d = l - 1), h = e5(D, d, l), I = h.length, k = b.length, u = r(h, b, I, k), u == 1 && (d--, t(h, J < I ? mr : D, I, l))) : (d == 0 && (u = d = 1), h = D.slice()), I = h.length, I < k && h.unshift(0), t(b, h, k, l), u == -1 && (k = b.length, u = r(D, b, J, k), u < 1 && (d++, t(b, J < k ? mr : D, k, l))), k = b.length) : u === 0 && (d++, b = [0]), S[p++] = d, u && b[0] ? b[k++] = H[j] || 0 : (b = [H[j]], k = 1);
        while ((j++ < re || b[0] !== void 0) && oe--);
        g = b[0] !== void 0;
      }
      S[0] || S.shift();
    }
    if (f == 1) P.e = c, Ns = g;
    else {
      for (p = 1, d = S[0]; d >= 10; d /= 10) p++;
      P.e = p + c * f - 1, y(P, a ? o + P.e + 1 : o, s, g);
    }
    return P;
  };
}();
function y(e5, r, t, n) {
  var i, o, s, a, l, u, c, p, d, f = e5.constructor;
  e: if (r != null) {
    if (p = e5.d, !p) return e5;
    for (i = 1, a = p[0]; a >= 10; a /= 10) i++;
    if (o = r - i, o < 0) o += E, s = r, c = p[d = 0], l = c / B(10, i - s - 1) % 10 | 0;
    else if (d = Math.ceil((o + 1) / E), a = p.length, d >= a) if (n) {
      for (; a++ <= d; ) p.push(0);
      c = l = 0, i = 1, o %= E, s = o - E + 1;
    } else break e;
    else {
      for (c = a = p[d], i = 1; a >= 10; a /= 10) i++;
      o %= E, s = o - E + i, l = s < 0 ? 0 : c / B(10, i - s - 1) % 10 | 0;
    }
    if (n = n || r < 0 || p[d + 1] !== void 0 || (s < 0 ? c : c % B(10, i - s - 1)), u = t < 4 ? (l || n) && (t == 0 || t == (e5.s < 0 ? 3 : 2)) : l > 5 || l == 5 && (t == 4 || n || t == 6 && (o > 0 ? s > 0 ? c / B(10, i - s) : 0 : p[d - 1]) % 10 & 1 || t == (e5.s < 0 ? 8 : 7)), r < 1 || !p[0]) return p.length = 0, u ? (r -= e5.e + 1, p[0] = B(10, (E - r % E) % E), e5.e = -r || 0) : p[0] = e5.e = 0, e5;
    if (o == 0 ? (p.length = d, a = 1, d--) : (p.length = d + 1, a = B(10, E - o), p[d] = s > 0 ? (c / B(10, i - s) % B(10, s) | 0) * a : 0), u) for (; ; ) if (d == 0) {
      for (o = 1, s = p[0]; s >= 10; s /= 10) o++;
      for (s = p[0] += a, a = 1; s >= 10; s /= 10) a++;
      o != a && (e5.e++, p[0] == fe && (p[0] = 1));
      break;
    } else {
      if (p[d] += a, p[d] != fe) break;
      p[d--] = 0, a = 1;
    }
    for (o = p.length; p[--o] === 0; ) p.pop();
  }
  return w && (e5.e > f.maxE ? (e5.d = null, e5.e = NaN) : e5.e < f.minE && (e5.e = 0, e5.d = [0])), e5;
}
function xe(e5, r, t) {
  if (!e5.isFinite()) return Bs(e5);
  var n, i = e5.e, o = W(e5.d), s = o.length;
  return r ? (t && (n = t - s) > 0 ? o = o.charAt(0) + "." + o.slice(1) + We(n) : s > 1 && (o = o.charAt(0) + "." + o.slice(1)), o = o + (e5.e < 0 ? "e" : "e+") + e5.e) : i < 0 ? (o = "0." + We(-i - 1) + o, t && (n = t - s) > 0 && (o += We(n))) : i >= s ? (o += We(i + 1 - s), t && (n = t - i - 1) > 0 && (o = o + "." + We(n))) : ((n = i + 1) < s && (o = o.slice(0, n) + "." + o.slice(n)), t && (n = t - s) > 0 && (i + 1 === s && (o += "."), o += We(n))), o;
}
function cn(e5, r) {
  var t = e5[0];
  for (r *= E; t >= 10; t /= 10) r++;
  return r;
}
function ln(e5, r, t) {
  if (r > sp) throw w = true, t && (e5.precision = t), Error(Ls);
  return y(new e5(sn), r, 1, true);
}
function we(e5, r, t) {
  if (r > Di) throw Error(Ls);
  return y(new e5(an), r, t, true);
}
function qs(e5) {
  var r = e5.length - 1, t = r * E + 1;
  if (r = e5[r], r) {
    for (; r % 10 == 0; r /= 10) t--;
    for (r = e5[0]; r >= 10; r /= 10) t++;
  }
  return t;
}
function We(e5) {
  for (var r = ""; e5--; ) r += "0";
  return r;
}
function js(e5, r, t, n) {
  var i, o = new e5(1), s = Math.ceil(n / E + 4);
  for (w = false; ; ) {
    if (t % 2 && (o = o.times(r), Ds(o.d, s) && (i = true)), t = Y(t / 2), t === 0) {
      t = o.d.length - 1, i && o.d[t] === 0 && ++o.d[t];
      break;
    }
    r = r.times(r), Ds(r.d, s);
  }
  return w = true, o;
}
function Os(e5) {
  return e5.d[e5.d.length - 1] & 1;
}
function Vs(e5, r, t) {
  for (var n, i, o = new e5(r[0]), s = 0; ++s < r.length; ) {
    if (i = new e5(r[s]), !i.s) {
      o = i;
      break;
    }
    n = o.cmp(i), (n === t || n === 0 && o.s === t) && (o = i);
  }
  return o;
}
function _i(e5, r) {
  var t, n, i, o, s, a, l, u = 0, c = 0, p = 0, d = e5.constructor, f = d.rounding, g = d.precision;
  if (!e5.d || !e5.d[0] || e5.e > 17) return new d(e5.d ? e5.d[0] ? e5.s < 0 ? 0 : 1 / 0 : 1 : e5.s ? e5.s < 0 ? 0 : e5 : NaN);
  for (r == null ? (w = false, l = g) : l = r, a = new d(0.03125); e5.e > -2; ) e5 = e5.times(a), p += 5;
  for (n = Math.log(B(2, p)) / Math.LN10 * 2 + 5 | 0, l += n, t = o = s = new d(1), d.precision = l; ; ) {
    if (o = y(o.times(e5), l, 1), t = t.times(++c), a = s.plus(_(o, t, l, 1)), W(a.d).slice(0, l) === W(s.d).slice(0, l)) {
      for (i = p; i--; ) s = y(s.times(s), l, 1);
      if (r == null) if (u < 3 && it(s.d, l - n, f, u)) d.precision = l += 10, t = o = a = new d(1), c = 0, u++;
      else return y(s, d.precision = g, f, w = true);
      else return d.precision = g, s;
    }
    s = a;
  }
}
function Je(e5, r) {
  var t, n, i, o, s, a, l, u, c, p, d, f = 1, g = 10, h = e5, I = h.d, P = h.constructor, S = P.rounding, b = P.precision;
  if (h.s < 0 || !I || !I[0] || !h.e && I[0] == 1 && I.length == 1) return new P(I && !I[0] ? -1 / 0 : h.s != 1 ? NaN : I ? 0 : h);
  if (r == null ? (w = false, c = b) : c = r, P.precision = c += g, t = W(I), n = t.charAt(0), Math.abs(o = h.e) < 15e14) {
    for (; n < 7 && n != 1 || n == 1 && t.charAt(1) > 3; ) h = h.times(e5), t = W(h.d), n = t.charAt(0), f++;
    o = h.e, n > 1 ? (h = new P("0." + t), o++) : h = new P(n + "." + t.slice(1));
  } else return u = ln(P, c + 2, b).times(o + ""), h = Je(new P(n + "." + t.slice(1)), c - g).plus(u), P.precision = b, r == null ? y(h, b, S, w = true) : h;
  for (p = h, l = s = h = _(h.minus(1), h.plus(1), c, 1), d = y(h.times(h), c, 1), i = 3; ; ) {
    if (s = y(s.times(d), c, 1), u = l.plus(_(s, new P(i), c, 1)), W(u.d).slice(0, c) === W(l.d).slice(0, c)) if (l = l.times(2), o !== 0 && (l = l.plus(ln(P, c + 2, b).times(o + ""))), l = _(l, new P(f), c, 1), r == null) if (it(l.d, c - g, S, a)) P.precision = c += g, u = s = h = _(p.minus(1), p.plus(1), c, 1), d = y(h.times(h), c, 1), i = a = 1;
    else return y(l, P.precision = b, S, w = true);
    else return P.precision = b, l;
    l = u, i += 2;
  }
}
function Bs(e5) {
  return String(e5.s * e5.s / 0);
}
function on(e5, r) {
  var t, n, i;
  for ((t = r.indexOf(".")) > -1 && (r = r.replace(".", "")), (n = r.search(/e/i)) > 0 ? (t < 0 && (t = n), t += +r.slice(n + 1), r = r.substring(0, n)) : t < 0 && (t = r.length), n = 0; r.charCodeAt(n) === 48; n++) ;
  for (i = r.length; r.charCodeAt(i - 1) === 48; --i) ;
  if (r = r.slice(n, i), r) {
    if (i -= n, e5.e = t = t - n - 1, e5.d = [], n = (t + 1) % E, t < 0 && (n += E), n < i) {
      for (n && e5.d.push(+r.slice(0, n)), i -= E; n < i; ) e5.d.push(+r.slice(n, n += E));
      r = r.slice(n), n = E - r.length;
    } else n -= i;
    for (; n--; ) r += "0";
    e5.d.push(+r), w && (e5.e > e5.constructor.maxE ? (e5.d = null, e5.e = NaN) : e5.e < e5.constructor.minE && (e5.e = 0, e5.d = [0]));
  } else e5.e = 0, e5.d = [0];
  return e5;
}
function lp(e5, r) {
  var t, n, i, o, s, a, l, u, c;
  if (r.indexOf("_") > -1) {
    if (r = r.replace(/(\d)_(?=\d)/g, "$1"), $s.test(r)) return on(e5, r);
  } else if (r === "Infinity" || r === "NaN") return +r || (e5.s = NaN), e5.e = NaN, e5.d = null, e5;
  if (np.test(r)) t = 16, r = r.toLowerCase();
  else if (tp.test(r)) t = 2;
  else if (ip.test(r)) t = 8;
  else throw Error(He + r);
  for (o = r.search(/p/i), o > 0 ? (l = +r.slice(o + 1), r = r.substring(2, o)) : r = r.slice(2), o = r.indexOf("."), s = o >= 0, n = e5.constructor, s && (r = r.replace(".", ""), a = r.length, o = a - o, i = js(n, new n(t), o, o * 2)), u = nn(r, t, fe), c = u.length - 1, o = c; u[o] === 0; --o) u.pop();
  return o < 0 ? new n(e5.s * 0) : (e5.e = cn(u, c), e5.d = u, w = false, s && (e5 = _(e5, i, a * 4)), l && (e5 = e5.times(Math.abs(l) < 54 ? B(2, l) : or.pow(2, l))), w = true, e5);
}
function up(e5, r) {
  var t, n = r.d.length;
  if (n < 3) return r.isZero() ? r : Pr(e5, 2, r, r);
  t = 1.4 * Math.sqrt(n), t = t > 16 ? 16 : t | 0, r = r.times(1 / pn(5, t)), r = Pr(e5, 2, r, r);
  for (var i, o = new e5(5), s = new e5(16), a = new e5(20); t--; ) i = r.times(r), r = r.times(o.plus(i.times(s.times(i).minus(a))));
  return r;
}
function Pr(e5, r, t, n, i) {
  var o, s, a, l, u = 1, c = e5.precision, p = Math.ceil(c / E);
  for (w = false, l = t.times(t), a = new e5(n); ; ) {
    if (s = _(a.times(l), new e5(r++ * r++), c, 1), a = i ? n.plus(s) : n.minus(s), n = _(s.times(l), new e5(r++ * r++), c, 1), s = a.plus(n), s.d[p] !== void 0) {
      for (o = p; s.d[o] === a.d[o] && o--; ) ;
      if (o == -1) break;
    }
    o = a, a = n, n = s, s = o, u++;
  }
  return w = true, s.d.length = p + 1, s;
}
function pn(e5, r) {
  for (var t = e5; --r; ) t *= e5;
  return t;
}
function Us(e5, r) {
  var t, n = r.s < 0, i = we(e5, e5.precision, 1), o = i.times(0.5);
  if (r = r.abs(), r.lte(o)) return Le = n ? 4 : 1, r;
  if (t = r.divToInt(i), t.isZero()) Le = n ? 3 : 2;
  else {
    if (r = r.minus(t.times(i)), r.lte(o)) return Le = Os(t) ? n ? 2 : 3 : n ? 4 : 1, r;
    Le = Os(t) ? n ? 1 : 4 : n ? 3 : 2;
  }
  return r.minus(i).abs();
}
function Ni(e5, r, t, n) {
  var i, o, s, a, l, u, c, p, d, f = e5.constructor, g = t !== void 0;
  if (g ? (ne(t, 1, Ke), n === void 0 ? n = f.rounding : ne(n, 0, 8)) : (t = f.precision, n = f.rounding), !e5.isFinite()) c = Bs(e5);
  else {
    for (c = xe(e5), s = c.indexOf("."), g ? (i = 2, r == 16 ? t = t * 4 - 3 : r == 8 && (t = t * 3 - 2)) : i = r, s >= 0 && (c = c.replace(".", ""), d = new f(1), d.e = c.length - s, d.d = nn(xe(d), 10, i), d.e = d.d.length), p = nn(c, 10, i), o = l = p.length; p[--l] == 0; ) p.pop();
    if (!p[0]) c = g ? "0p+0" : "0";
    else {
      if (s < 0 ? o-- : (e5 = new f(e5), e5.d = p, e5.e = o, e5 = _(e5, d, t, n, 0, i), p = e5.d, o = e5.e, u = Ns), s = p[t], a = i / 2, u = u || p[t + 1] !== void 0, u = n < 4 ? (s !== void 0 || u) && (n === 0 || n === (e5.s < 0 ? 3 : 2)) : s > a || s === a && (n === 4 || u || n === 6 && p[t - 1] & 1 || n === (e5.s < 0 ? 8 : 7)), p.length = t, u) for (; ++p[--t] > i - 1; ) p[t] = 0, t || (++o, p.unshift(1));
      for (l = p.length; !p[l - 1]; --l) ;
      for (s = 0, c = ""; s < l; s++) c += ki.charAt(p[s]);
      if (g) {
        if (l > 1) if (r == 16 || r == 8) {
          for (s = r == 16 ? 4 : 3, --l; l % s; l++) c += "0";
          for (p = nn(c, i, r), l = p.length; !p[l - 1]; --l) ;
          for (s = 1, c = "1."; s < l; s++) c += ki.charAt(p[s]);
        } else c = c.charAt(0) + "." + c.slice(1);
        c = c + (o < 0 ? "p" : "p+") + o;
      } else if (o < 0) {
        for (; ++o; ) c = "0" + c;
        c = "0." + c;
      } else if (++o > l) for (o -= l; o--; ) c += "0";
      else o < l && (c = c.slice(0, o) + "." + c.slice(o));
    }
    c = (r == 16 ? "0x" : r == 2 ? "0b" : r == 8 ? "0o" : "") + c;
  }
  return e5.s < 0 ? "-" + c : c;
}
function Ds(e5, r) {
  if (e5.length > r) return e5.length = r, true;
}
function cp(e5) {
  return new this(e5).abs();
}
function pp(e5) {
  return new this(e5).acos();
}
function dp(e5) {
  return new this(e5).acosh();
}
function mp(e5, r) {
  return new this(e5).plus(r);
}
function fp(e5) {
  return new this(e5).asin();
}
function gp(e5) {
  return new this(e5).asinh();
}
function hp(e5) {
  return new this(e5).atan();
}
function yp(e5) {
  return new this(e5).atanh();
}
function bp(e5, r) {
  e5 = new this(e5), r = new this(r);
  var t, n = this.precision, i = this.rounding, o = n + 4;
  return !e5.s || !r.s ? t = new this(NaN) : !e5.d && !r.d ? (t = we(this, o, 1).times(r.s > 0 ? 0.25 : 0.75), t.s = e5.s) : !r.d || e5.isZero() ? (t = r.s < 0 ? we(this, n, i) : new this(0), t.s = e5.s) : !e5.d || r.isZero() ? (t = we(this, o, 1).times(0.5), t.s = e5.s) : r.s < 0 ? (this.precision = o, this.rounding = 1, t = this.atan(_(e5, r, o, 1)), r = we(this, o, 1), this.precision = n, this.rounding = i, t = e5.s < 0 ? t.minus(r) : t.plus(r)) : t = this.atan(_(e5, r, o, 1)), t;
}
function Ep(e5) {
  return new this(e5).cbrt();
}
function wp(e5) {
  return y(e5 = new this(e5), e5.e + 1, 2);
}
function xp(e5, r, t) {
  return new this(e5).clamp(r, t);
}
function vp(e5) {
  if (!e5 || typeof e5 != "object") throw Error(un + "Object expected");
  var r, t, n, i = e5.defaults === true, o = ["precision", 1, Ke, "rounding", 0, 8, "toExpNeg", -vr, 0, "toExpPos", 0, vr, "maxE", 0, vr, "minE", -vr, 0, "modulo", 0, 9];
  for (r = 0; r < o.length; r += 3) if (t = o[r], i && (this[t] = Oi[t]), (n = e5[t]) !== void 0) if (Y(n) === n && n >= o[r + 1] && n <= o[r + 2]) this[t] = n;
  else throw Error(He + t + ": " + n);
  if (t = "crypto", i && (this[t] = Oi[t]), (n = e5[t]) !== void 0) if (n === true || n === false || n === 0 || n === 1) if (n) if (typeof crypto < "u" && crypto && (crypto.getRandomValues || crypto.randomBytes)) this[t] = true;
  else throw Error(Fs);
  else this[t] = false;
  else throw Error(He + t + ": " + n);
  return this;
}
function Pp(e5) {
  return new this(e5).cos();
}
function Tp(e5) {
  return new this(e5).cosh();
}
function Qs(e5) {
  var r, t, n;
  function i(o) {
    var s, a, l, u = this;
    if (!(u instanceof i)) return new i(o);
    if (u.constructor = i, _s(o)) {
      u.s = o.s, w ? !o.d || o.e > i.maxE ? (u.e = NaN, u.d = null) : o.e < i.minE ? (u.e = 0, u.d = [0]) : (u.e = o.e, u.d = o.d.slice()) : (u.e = o.e, u.d = o.d ? o.d.slice() : o.d);
      return;
    }
    if (l = typeof o, l === "number") {
      if (o === 0) {
        u.s = 1 / o < 0 ? -1 : 1, u.e = 0, u.d = [0];
        return;
      }
      if (o < 0 ? (o = -o, u.s = -1) : u.s = 1, o === ~~o && o < 1e7) {
        for (s = 0, a = o; a >= 10; a /= 10) s++;
        w ? s > i.maxE ? (u.e = NaN, u.d = null) : s < i.minE ? (u.e = 0, u.d = [0]) : (u.e = s, u.d = [o]) : (u.e = s, u.d = [o]);
        return;
      }
      if (o * 0 !== 0) {
        o || (u.s = NaN), u.e = NaN, u.d = null;
        return;
      }
      return on(u, o.toString());
    }
    if (l === "string") return (a = o.charCodeAt(0)) === 45 ? (o = o.slice(1), u.s = -1) : (a === 43 && (o = o.slice(1)), u.s = 1), $s.test(o) ? on(u, o) : lp(u, o);
    if (l === "bigint") return o < 0 ? (o = -o, u.s = -1) : u.s = 1, on(u, o.toString());
    throw Error(He + o);
  }
  if (i.prototype = m, i.ROUND_UP = 0, i.ROUND_DOWN = 1, i.ROUND_CEIL = 2, i.ROUND_FLOOR = 3, i.ROUND_HALF_UP = 4, i.ROUND_HALF_DOWN = 5, i.ROUND_HALF_EVEN = 6, i.ROUND_HALF_CEIL = 7, i.ROUND_HALF_FLOOR = 8, i.EUCLID = 9, i.config = i.set = vp, i.clone = Qs, i.isDecimal = _s, i.abs = cp, i.acos = pp, i.acosh = dp, i.add = mp, i.asin = fp, i.asinh = gp, i.atan = hp, i.atanh = yp, i.atan2 = bp, i.cbrt = Ep, i.ceil = wp, i.clamp = xp, i.cos = Pp, i.cosh = Tp, i.div = Sp, i.exp = Rp, i.floor = Cp, i.hypot = Ap, i.ln = Ip, i.log = kp, i.log10 = Dp, i.log2 = Op, i.max = _p, i.min = Np, i.mod = Lp, i.mul = Fp, i.pow = Mp, i.random = $p, i.round = qp, i.sign = jp, i.sin = Vp, i.sinh = Bp, i.sqrt = Up, i.sub = Qp, i.sum = Gp, i.tan = Wp, i.tanh = Jp, i.trunc = Hp, e5 === void 0 && (e5 = {}), e5 && e5.defaults !== true) for (n = ["precision", "rounding", "toExpNeg", "toExpPos", "maxE", "minE", "modulo", "crypto"], r = 0; r < n.length; ) e5.hasOwnProperty(t = n[r++]) || (e5[t] = this[t]);
  return i.config(e5), i;
}
function Sp(e5, r) {
  return new this(e5).div(r);
}
function Rp(e5) {
  return new this(e5).exp();
}
function Cp(e5) {
  return y(e5 = new this(e5), e5.e + 1, 3);
}
function Ap() {
  var e5, r, t = new this(0);
  for (w = false, e5 = 0; e5 < arguments.length; ) if (r = new this(arguments[e5++]), r.d) t.d && (t = t.plus(r.times(r)));
  else {
    if (r.s) return w = true, new this(1 / 0);
    t = r;
  }
  return w = true, t.sqrt();
}
function _s(e5) {
  return e5 instanceof or || e5 && e5.toStringTag === Ms || false;
}
function Ip(e5) {
  return new this(e5).ln();
}
function kp(e5, r) {
  return new this(e5).log(r);
}
function Op(e5) {
  return new this(e5).log(2);
}
function Dp(e5) {
  return new this(e5).log(10);
}
function _p() {
  return Vs(this, arguments, -1);
}
function Np() {
  return Vs(this, arguments, 1);
}
function Lp(e5, r) {
  return new this(e5).mod(r);
}
function Fp(e5, r) {
  return new this(e5).mul(r);
}
function Mp(e5, r) {
  return new this(e5).pow(r);
}
function $p(e5) {
  var r, t, n, i, o = 0, s = new this(1), a = [];
  if (e5 === void 0 ? e5 = this.precision : ne(e5, 1, Ke), n = Math.ceil(e5 / E), this.crypto) if (crypto.getRandomValues) for (r = crypto.getRandomValues(new Uint32Array(n)); o < n; ) i = r[o], i >= 429e7 ? r[o] = crypto.getRandomValues(new Uint32Array(1))[0] : a[o++] = i % 1e7;
  else if (crypto.randomBytes) {
    for (r = crypto.randomBytes(n *= 4); o < n; ) i = r[o] + (r[o + 1] << 8) + (r[o + 2] << 16) + ((r[o + 3] & 127) << 24), i >= 214e7 ? crypto.randomBytes(4).copy(r, o) : (a.push(i % 1e7), o += 4);
    o = n / 4;
  } else throw Error(Fs);
  else for (; o < n; ) a[o++] = Math.random() * 1e7 | 0;
  for (n = a[--o], e5 %= E, n && e5 && (i = B(10, E - e5), a[o] = (n / i | 0) * i); a[o] === 0; o--) a.pop();
  if (o < 0) t = 0, a = [0];
  else {
    for (t = -1; a[0] === 0; t -= E) a.shift();
    for (n = 1, i = a[0]; i >= 10; i /= 10) n++;
    n < E && (t -= E - n);
  }
  return s.e = t, s.d = a, s;
}
function qp(e5) {
  return y(e5 = new this(e5), e5.e + 1, this.rounding);
}
function jp(e5) {
  return e5 = new this(e5), e5.d ? e5.d[0] ? e5.s : 0 * e5.s : e5.s || NaN;
}
function Vp(e5) {
  return new this(e5).sin();
}
function Bp(e5) {
  return new this(e5).sinh();
}
function Up(e5) {
  return new this(e5).sqrt();
}
function Qp(e5, r) {
  return new this(e5).sub(r);
}
function Gp() {
  var e5 = 0, r = arguments, t = new this(r[e5]);
  for (w = false; t.s && ++e5 < r.length; ) t = t.plus(r[e5]);
  return w = true, y(t, this.precision, this.rounding);
}
function Wp(e5) {
  return new this(e5).tan();
}
function Jp(e5) {
  return new this(e5).tanh();
}
function Hp(e5) {
  return y(e5 = new this(e5), e5.e + 1, 1);
}
m[Symbol.for("nodejs.util.inspect.custom")] = m.toString;
m[Symbol.toStringTag] = "Decimal";
var or = m.constructor = Qs(Oi);
sn = new or(sn);
an = new or(an);
var mn = {};
gr(mn, { ModelAction: () => Rr, datamodelEnumToSchemaEnum: () => Zp });
function Zp(e5) {
  return { name: e5.name, values: e5.values.map((r) => r.name) };
}
var Rr = ((b) => (b.findUnique = "findUnique", b.findUniqueOrThrow = "findUniqueOrThrow", b.findFirst = "findFirst", b.findFirstOrThrow = "findFirstOrThrow", b.findMany = "findMany", b.create = "create", b.createMany = "createMany", b.createManyAndReturn = "createManyAndReturn", b.update = "update", b.updateMany = "updateMany", b.updateManyAndReturn = "updateManyAndReturn", b.upsert = "upsert", b.delete = "delete", b.deleteMany = "deleteMany", b.groupBy = "groupBy", b.count = "count", b.aggregate = "aggregate", b.findRaw = "findRaw", b.aggregateRaw = "aggregateRaw", b))(Rr || {});
var Ks = se(yi());
var Ws = { keyword: Oe, entity: Oe, value: (e5) => G(tr(e5)), punctuation: tr, directive: Oe, function: Oe, variable: (e5) => G(tr(e5)), string: (e5) => G(qe(e5)), boolean: ke, number: Oe, comment: Wr };
var Xp = (e5) => e5;
var fn = {};
var ed = 0;
var v = { manual: fn.Prism && fn.Prism.manual, disableWorkerMessageHandler: fn.Prism && fn.Prism.disableWorkerMessageHandler, util: { encode: function(e5) {
  if (e5 instanceof ge) {
    let r = e5;
    return new ge(r.type, v.util.encode(r.content), r.alias);
  } else return Array.isArray(e5) ? e5.map(v.util.encode) : e5.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/\u00a0/g, " ");
}, type: function(e5) {
  return Object.prototype.toString.call(e5).slice(8, -1);
}, objId: function(e5) {
  return e5.__id || Object.defineProperty(e5, "__id", { value: ++ed }), e5.__id;
}, clone: function e2(r, t) {
  let n, i, o = v.util.type(r);
  switch (t = t || {}, o) {
    case "Object":
      if (i = v.util.objId(r), t[i]) return t[i];
      n = {}, t[i] = n;
      for (let s in r) r.hasOwnProperty(s) && (n[s] = e2(r[s], t));
      return n;
    case "Array":
      return i = v.util.objId(r), t[i] ? t[i] : (n = [], t[i] = n, r.forEach(function(s, a) {
        n[a] = e2(s, t);
      }), n);
    default:
      return r;
  }
} }, languages: { extend: function(e5, r) {
  let t = v.util.clone(v.languages[e5]);
  for (let n in r) t[n] = r[n];
  return t;
}, insertBefore: function(e5, r, t, n) {
  n = n || v.languages;
  let i = n[e5], o = {};
  for (let a in i) if (i.hasOwnProperty(a)) {
    if (a == r) for (let l in t) t.hasOwnProperty(l) && (o[l] = t[l]);
    t.hasOwnProperty(a) || (o[a] = i[a]);
  }
  let s = n[e5];
  return n[e5] = o, v.languages.DFS(v.languages, function(a, l) {
    l === s && a != e5 && (this[a] = o);
  }), o;
}, DFS: function e3(r, t, n, i) {
  i = i || {};
  let o = v.util.objId;
  for (let s in r) if (r.hasOwnProperty(s)) {
    t.call(r, s, r[s], n || s);
    let a = r[s], l = v.util.type(a);
    l === "Object" && !i[o(a)] ? (i[o(a)] = true, e3(a, t, null, i)) : l === "Array" && !i[o(a)] && (i[o(a)] = true, e3(a, t, s, i));
  }
} }, plugins: {}, highlight: function(e5, r, t) {
  let n = { code: e5, grammar: r, language: t };
  return v.hooks.run("before-tokenize", n), n.tokens = v.tokenize(n.code, n.grammar), v.hooks.run("after-tokenize", n), ge.stringify(v.util.encode(n.tokens), n.language);
}, matchGrammar: function(e5, r, t, n, i, o, s) {
  for (let h in t) {
    if (!t.hasOwnProperty(h) || !t[h]) continue;
    if (h == s) return;
    let I = t[h];
    I = v.util.type(I) === "Array" ? I : [I];
    for (let P = 0; P < I.length; ++P) {
      let S = I[P], b = S.inside, k = !!S.lookbehind, me = !!S.greedy, oe = 0, Gr = S.alias;
      if (me && !S.pattern.global) {
        let j = S.pattern.toString().match(/[imuy]*$/)[0];
        S.pattern = RegExp(S.pattern.source, j + "g");
      }
      S = S.pattern || S;
      for (let j = n, re = i; j < r.length; re += r[j].length, ++j) {
        let Ae = r[j];
        if (r.length > e5.length) return;
        if (Ae instanceof ge) continue;
        if (me && j != r.length - 1) {
          S.lastIndex = re;
          var p = S.exec(e5);
          if (!p) break;
          var c = p.index + (k ? p[1].length : 0), d = p.index + p[0].length, a = j, l = re;
          for (let D = r.length; a < D && (l < d || !r[a].type && !r[a - 1].greedy); ++a) l += r[a].length, c >= l && (++j, re = l);
          if (r[j] instanceof ge) continue;
          u = a - j, Ae = e5.slice(re, l), p.index -= re;
        } else {
          S.lastIndex = 0;
          var p = S.exec(Ae), u = 1;
        }
        if (!p) {
          if (o) break;
          continue;
        }
        k && (oe = p[1] ? p[1].length : 0);
        var c = p.index + oe, p = p[0].slice(oe), d = c + p.length, f = Ae.slice(0, c), g = Ae.slice(d);
        let J = [j, u];
        f && (++j, re += f.length, J.push(f));
        let mr = new ge(h, b ? v.tokenize(p, b) : p, Gr, p, me);
        if (J.push(mr), g && J.push(g), Array.prototype.splice.apply(r, J), u != 1 && v.matchGrammar(e5, r, t, j, re, true, h), o) break;
      }
    }
  }
}, tokenize: function(e5, r) {
  let t = [e5], n = r.rest;
  if (n) {
    for (let i in n) r[i] = n[i];
    delete r.rest;
  }
  return v.matchGrammar(e5, t, r, 0, 0, false), t;
}, hooks: { all: {}, add: function(e5, r) {
  let t = v.hooks.all;
  t[e5] = t[e5] || [], t[e5].push(r);
}, run: function(e5, r) {
  let t = v.hooks.all[e5];
  if (!(!t || !t.length)) for (var n = 0, i; i = t[n++]; ) i(r);
} }, Token: ge };
v.languages.clike = { comment: [{ pattern: /(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/, lookbehind: true }, { pattern: /(^|[^\\:])\/\/.*/, lookbehind: true, greedy: true }], string: { pattern: /(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/, greedy: true }, "class-name": { pattern: /((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i, lookbehind: true, inside: { punctuation: /[.\\]/ } }, keyword: /\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/, boolean: /\b(?:true|false)\b/, function: /\w+(?=\()/, number: /\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i, operator: /--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/, punctuation: /[{}[\];(),.:]/ };
v.languages.javascript = v.languages.extend("clike", { "class-name": [v.languages.clike["class-name"], { pattern: /(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/, lookbehind: true }], keyword: [{ pattern: /((?:^|})\s*)(?:catch|finally)\b/, lookbehind: true }, { pattern: /(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/, lookbehind: true }], number: /\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/, function: /[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/, operator: /-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/ });
v.languages.javascript["class-name"][0].pattern = /(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/;
v.languages.insertBefore("javascript", "keyword", { regex: { pattern: /((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/, lookbehind: true, greedy: true }, "function-variable": { pattern: /[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/, alias: "function" }, parameter: [{ pattern: /(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/, lookbehind: true, inside: v.languages.javascript }, { pattern: /[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i, inside: v.languages.javascript }, { pattern: /(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/, lookbehind: true, inside: v.languages.javascript }, { pattern: /((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/, lookbehind: true, inside: v.languages.javascript }], constant: /\b[A-Z](?:[A-Z_]|\dx?)*\b/ });
v.languages.markup && v.languages.markup.tag.addInlined("script", "javascript");
v.languages.js = v.languages.javascript;
v.languages.typescript = v.languages.extend("javascript", { keyword: /\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/, builtin: /\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/ });
v.languages.ts = v.languages.typescript;
function ge(e5, r, t, n, i) {
  this.type = e5, this.content = r, this.alias = t, this.length = (n || "").length | 0, this.greedy = !!i;
}
ge.stringify = function(e5, r) {
  return typeof e5 == "string" ? e5 : Array.isArray(e5) ? e5.map(function(t) {
    return ge.stringify(t, r);
  }).join("") : rd(e5.type)(e5.content);
};
function rd(e5) {
  return Ws[e5] || Xp;
}
var ia = se(Fi());
ta();
ra();
var vn = Symbol();
var $i = /* @__PURE__ */ new WeakMap();
var Fe = class {
  constructor(r) {
    r === vn ? $i.set(this, `Prisma.${this._getName()}`) : $i.set(this, `new Prisma.${this._getNamespace()}.${this._getName()}()`);
  }
  _getName() {
    return this.constructor.name;
  }
  toString() {
    return $i.get(this);
  }
};
var pt = class extends Fe {
  _getNamespace() {
    return "NullTypes";
  }
};
var _e2, _a13;
var dt = (_a13 = class extends pt {
  constructor() {
    super(...arguments);
    __privateAdd(this, _e2);
  }
}, _e2 = new WeakMap(), _a13);
ji(dt, "DbNull");
var _e3, _a14;
var mt = (_a14 = class extends pt {
  constructor() {
    super(...arguments);
    __privateAdd(this, _e3);
  }
}, _e3 = new WeakMap(), _a14);
ji(mt, "JsonNull");
var _e4, _a15;
var ft = (_a15 = class extends pt {
  constructor() {
    super(...arguments);
    __privateAdd(this, _e4);
  }
}, _e4 = new WeakMap(), _a15);
ji(ft, "AnyNull");
var qi = { classes: { DbNull: dt, JsonNull: mt, AnyNull: ft }, instances: { DbNull: new dt(vn), JsonNull: new mt(vn), AnyNull: new ft(vn) } };
function ji(e5, r) {
  Object.defineProperty(e5, "name", { value: r, configurable: true });
}
var ba = Symbol();
var gt = class {
  constructor(r) {
    if (r !== ba) throw new Error("Skip instance can not be constructed directly");
  }
  ifUndefined(r) {
    return r === void 0 ? Bi : r;
  }
};
var Bi = new gt(ba);
var Xl = se(pi());
var le = class e4 {
  constructor(r, t) {
    if (r.length - 1 !== t.length) throw r.length === 0 ? new TypeError("Expected at least 1 string") : new TypeError(`Expected ${r.length} strings to have ${r.length - 1} values`);
    let n = t.reduce((s, a) => s + (a instanceof e4 ? a.values.length : 1), 0);
    this.values = new Array(n), this.strings = new Array(n + 1), this.strings[0] = r[0];
    let i = 0, o = 0;
    for (; i < t.length; ) {
      let s = t[i++], a = r[i];
      if (s instanceof e4) {
        this.strings[o] += s.strings[0];
        let l = 0;
        for (; l < s.values.length; ) this.values[o++] = s.values[l++], this.strings[o] = s.strings[l];
        this.strings[o] += a;
      } else this.values[o++] = s, this.strings[o] = a;
    }
  }
  get sql() {
    let r = this.strings.length, t = 1, n = this.strings[0];
    for (; t < r; ) n += `?${this.strings[t++]}`;
    return n;
  }
  get statement() {
    let r = this.strings.length, t = 1, n = this.strings[0];
    for (; t < r; ) n += `:${t}${this.strings[t++]}`;
    return n;
  }
  get text() {
    let r = this.strings.length, t = 1, n = this.strings[0];
    for (; t < r; ) n += `$${t}${this.strings[t++]}`;
    return n;
  }
  inspect() {
    return { sql: this.sql, statement: this.statement, text: this.text, values: this.values };
  }
};
function Pa(e5) {
  return new le([e5], []);
}
var Xd = Pa("");
var Sa = Symbol.for("nodejs.util.inspect.custom");
var Xi = Symbol();
var za = L("prisma:client");
var Wm = L("prisma:client:engines:resolveEnginePath");
var ro = se(xi());
var ul = se(Is());
var dl = L("driver-adapter-utils");
var qn = class extends Error {
  constructor(r, t) {
    super(r);
    __publicField(this, "clientVersion");
    __publicField(this, "cause");
    this.clientVersion = t.clientVersion, this.cause = t.cause;
  }
  get [Symbol.toStringTag]() {
    return this.name;
  }
};
var ie = class extends qn {
  constructor(r, t) {
    super(r, t);
    __publicField(this, "isRetryable");
    this.isRetryable = t.isRetryable ?? true;
  }
};
function R(e5, r) {
  return { ...e5, isRetryable: r };
}
var qr = class extends ie {
  constructor(r) {
    super("This request must be retried", R(r, true));
    __publicField(this, "name", "ForcedRetryError");
    __publicField(this, "code", "P5001");
  }
};
x(qr, "ForcedRetryError");
var lr = class extends ie {
  constructor(r, t) {
    super(r, R(t, false));
    __publicField(this, "name", "InvalidDatasourceError");
    __publicField(this, "code", "P6001");
  }
};
x(lr, "InvalidDatasourceError");
var ur = class extends ie {
  constructor(r, t) {
    super(r, R(t, false));
    __publicField(this, "name", "NotImplementedYetError");
    __publicField(this, "code", "P5004");
  }
};
x(ur, "NotImplementedYetError");
var F = class extends ie {
  constructor(r, t) {
    super(r, t);
    __publicField(this, "response");
    this.response = t.response;
    let n = this.response.headers.get("prisma-request-id");
    if (n) {
      let i = `(The request id was: ${n})`;
      this.message = this.message + " " + i;
    }
  }
};
var cr = class extends F {
  constructor(r) {
    super("Schema needs to be uploaded", R(r, true));
    __publicField(this, "name", "SchemaMissingError");
    __publicField(this, "code", "P5005");
  }
};
x(cr, "SchemaMissingError");
var oo = "This request could not be understood by the server";
var Pt = class extends F {
  constructor(r, t, n) {
    super(t || oo, R(r, false));
    __publicField(this, "name", "BadRequestError");
    __publicField(this, "code", "P5000");
    n && (this.code = n);
  }
};
x(Pt, "BadRequestError");
var Tt = class extends F {
  constructor(r, t) {
    super("Engine not started: healthcheck timeout", R(r, true));
    __publicField(this, "name", "HealthcheckTimeoutError");
    __publicField(this, "code", "P5013");
    __publicField(this, "logs");
    this.logs = t;
  }
};
x(Tt, "HealthcheckTimeoutError");
var St = class extends F {
  constructor(r, t, n) {
    super(t, R(r, true));
    __publicField(this, "name", "EngineStartupError");
    __publicField(this, "code", "P5014");
    __publicField(this, "logs");
    this.logs = n;
  }
};
x(St, "EngineStartupError");
var Rt = class extends F {
  constructor(r) {
    super("Engine version is not supported", R(r, false));
    __publicField(this, "name", "EngineVersionNotSupportedError");
    __publicField(this, "code", "P5012");
  }
};
x(Rt, "EngineVersionNotSupportedError");
var so = "Request timed out";
var Ct = class extends F {
  constructor(r, t = so) {
    super(t, R(r, false));
    __publicField(this, "name", "GatewayTimeoutError");
    __publicField(this, "code", "P5009");
  }
};
x(Ct, "GatewayTimeoutError");
var Zm = "Interactive transaction error";
var At = class extends F {
  constructor(r, t = Zm) {
    super(t, R(r, false));
    __publicField(this, "name", "InteractiveTransactionError");
    __publicField(this, "code", "P5015");
  }
};
x(At, "InteractiveTransactionError");
var Xm = "Request parameters are invalid";
var It = class extends F {
  constructor(r, t = Xm) {
    super(t, R(r, false));
    __publicField(this, "name", "InvalidRequestError");
    __publicField(this, "code", "P5011");
  }
};
x(It, "InvalidRequestError");
var ao = "Requested resource does not exist";
var kt = class extends F {
  constructor(r, t = ao) {
    super(t, R(r, false));
    __publicField(this, "name", "NotFoundError");
    __publicField(this, "code", "P5003");
  }
};
x(kt, "NotFoundError");
var lo = "Unknown server error";
var jr = class extends F {
  constructor(r, t, n) {
    super(t || lo, R(r, true));
    __publicField(this, "name", "ServerError");
    __publicField(this, "code", "P5006");
    __publicField(this, "logs");
    this.logs = n;
  }
};
x(jr, "ServerError");
var uo = "Unauthorized, check your connection string";
var Ot = class extends F {
  constructor(r, t = uo) {
    super(t, R(r, false));
    __publicField(this, "name", "UnauthorizedError");
    __publicField(this, "code", "P5007");
  }
};
x(Ot, "UnauthorizedError");
var co = "Usage exceeded, retry again later";
var Dt = class extends F {
  constructor(r, t = co) {
    super(t, R(r, true));
    __publicField(this, "name", "UsageExceededError");
    __publicField(this, "code", "P5008");
  }
};
x(Dt, "UsageExceededError");
var Nt = class extends ie {
  constructor(r, t) {
    super(`Cannot fetch data from service:
${r}`, R(t, true));
    __publicField(this, "name", "RequestError");
    __publicField(this, "code", "P5010");
  }
};
x(Nt, "RequestError");
var bl = L("prisma:client:dataproxyEngine");
var Lt = L("prisma:client:dataproxyEngine");
var fo = Symbol("PrismaLibraryEngineCache");
var Ce = L("prisma:client:libraryEngine");
var Sl = [...Yn, "native"];
var _l = L("prisma:client");
var bf = ci.split(".")[0];
var Vl = se(xi());
var vf = L("prisma:client:request_handler");
var Kl = se(Fi());
var O = class extends Error {
  constructor(r) {
    super(r + `
Read more at https://pris.ly/d/client-constructor`), this.name = "PrismaClientConstructorValidationError";
  }
  get [Symbol.toStringTag]() {
    return "PrismaClientConstructorValidationError";
  }
};
x(O, "PrismaClientConstructorValidationError");
var rr = L("prisma:client");
typeof globalThis == "object" && (globalThis.NODE_CLIENT = true);
var Nf = Symbol.for("prisma.client.transaction.id");

// node_modules/@auth/prisma-adapter/index.js
function PrismaAdapter(prisma) {
  const p = prisma;
  return {
    // We need to let Prisma generate the ID because our default UUID is incompatible with MongoDB
    createUser: ({ id, ...data }) => p.user.create(stripUndefined(data)),
    getUser: (id) => p.user.findUnique({ where: { id } }),
    getUserByEmail: (email) => p.user.findUnique({ where: { email } }),
    async getUserByAccount(provider_providerAccountId) {
      const account = await p.account.findUnique({
        where: { provider_providerAccountId },
        include: { user: true }
      });
      return (account == null ? void 0 : account.user) ?? null;
    },
    updateUser: ({ id, ...data }) => p.user.update({
      where: { id },
      ...stripUndefined(data)
    }),
    deleteUser: (id) => p.user.delete({ where: { id } }),
    linkAccount: (data) => p.account.create({ data }),
    unlinkAccount: (provider_providerAccountId) => p.account.delete({
      where: { provider_providerAccountId }
    }),
    async getSessionAndUser(sessionToken) {
      const userAndSession = await p.session.findUnique({
        where: { sessionToken },
        include: { user: true }
      });
      if (!userAndSession)
        return null;
      const { user, ...session } = userAndSession;
      return { user, session };
    },
    createSession: (data) => p.session.create(stripUndefined(data)),
    updateSession: (data) => p.session.update({
      where: { sessionToken: data.sessionToken },
      ...stripUndefined(data)
    }),
    deleteSession: (sessionToken) => p.session.delete({ where: { sessionToken } }),
    async createVerificationToken(data) {
      const verificationToken = await p.verificationToken.create(stripUndefined(data));
      if ("id" in verificationToken && verificationToken.id)
        delete verificationToken.id;
      return verificationToken;
    },
    async useVerificationToken(identifier_token) {
      try {
        const verificationToken = await p.verificationToken.delete({
          where: { identifier_token }
        });
        if ("id" in verificationToken && verificationToken.id)
          delete verificationToken.id;
        return verificationToken;
      } catch (error) {
        if (error instanceof Z && error.code === "P2025")
          return null;
        throw error;
      }
    },
    async getAccount(providerAccountId, provider) {
      return p.account.findFirst({
        where: { providerAccountId, provider }
      });
    },
    async createAuthenticator(data) {
      return p.authenticator.create(stripUndefined(data));
    },
    async getAuthenticator(credentialID) {
      return p.authenticator.findUnique({
        where: { credentialID }
      });
    },
    async listAuthenticatorsByUserId(userId) {
      return p.authenticator.findMany({
        where: { userId }
      });
    },
    async updateAuthenticatorCounter(credentialID, counter) {
      return p.authenticator.update({
        where: { credentialID },
        data: { counter }
      });
    }
  };
}
function stripUndefined(obj) {
  const data = {};
  for (const key in obj)
    if (obj[key] !== void 0)
      data[key] = obj[key];
  return { data };
}
export {
  PrismaAdapter
};
/*! Bundled license information:

@prisma/client/runtime/library.mjs:
  (*! Bundled license information:
  
  decimal.js/decimal.mjs:
    (*!
     *  decimal.js v10.5.0
     *  An arbitrary-precision Decimal type for JavaScript.
     *  https://github.com/MikeMcl/decimal.js
     *  Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>
     *  MIT Licence
     *)
  *)
*/
//# sourceMappingURL=@auth_prisma-adapter.js.map
