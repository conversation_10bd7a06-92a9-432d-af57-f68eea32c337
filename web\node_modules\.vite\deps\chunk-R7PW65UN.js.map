{"version": 3, "sources": ["../../d3-tile/dist/d3-tile.js"], "sourcesContent": ["// https://d3js.org/d3-tile/ v1.0.0 Copyright 2019 <PERSON>\n(function (global, factory) {\ntypeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\ntypeof define === 'function' && define.amd ? define(['exports'], factory) :\n(global = global || self, factory(global.d3 = global.d3 || {}));\n}(this, function (exports) { 'use strict';\n\nfunction defaultScale(t) {\n  return t.k;\n}\n\nfunction defaultTranslate(t) {\n  return [t.x, t.y];\n}\n\nfunction constant(x) {\n  return function() {\n    return x;\n  };\n}\n\nfunction tile() {\n  let x0 = 0, y0 = 0, x1 = 960, y1 = 500;\n  let clampX = true, clampY = true;\n  let tileSize = 256;\n  let scale = defaultScale;\n  let translate = defaultTranslate;\n  let zoomDelta = 0;\n\n  function tile() {\n    const scale_ = +scale.apply(this, arguments);\n    const translate_ = translate.apply(this, arguments);\n    const z = Math.log2(scale_ / tileSize);\n    const z0 = Math.round(Math.max(z + zoomD<PERSON><PERSON>, 0));\n    const k = Math.pow(2, z - z0) * tileSize;\n    const x = +translate_[0] - scale_ / 2;\n    const y = +translate_[1] - scale_ / 2;\n    const xmin = Math.max(clampX ? 0 : -Infinity, Math.floor((x0 - x) / k));\n    const xmax = Math.min(clampX ? 1 << z0 : Infinity, Math.ceil((x1 - x) / k));\n    const ymin = Math.max(clampY ? 0 : -Infinity, Math.floor((y0 - y) / k));\n    const ymax = Math.min(clampY ? 1 << z0 : Infinity, Math.ceil((y1 - y) / k));\n    const tiles = [];\n    for (let y = ymin; y < ymax; ++y) {\n      for (let x = xmin; x < xmax; ++x) {\n        tiles.push([x, y, z0]);\n      }\n    }\n    tiles.translate = [x / k, y / k];\n    tiles.scale = k;\n    return tiles;\n  }\n\n  tile.size = function(_) {\n    return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], tile) : [x1 - x0, y1 - y0];\n  };\n\n  tile.extent = function(_) {\n    return arguments.length ? (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1], tile) : [[x0, y0], [x1, y1]];\n  };\n\n  tile.scale = function(_) {\n    return arguments.length ? (scale = typeof _ === \"function\" ? _ : constant(+_), tile) : scale;\n  };\n\n  tile.translate = function(_) {\n    return arguments.length ? (translate = typeof _ === \"function\" ? _ : constant([+_[0], +_[1]]), tile) : translate;\n  };\n\n  tile.zoomDelta = function(_) {\n    return arguments.length ? (zoomDelta = +_, tile) : zoomDelta;\n  };\n\n  tile.tileSize = function(_) {\n    return arguments.length ? (tileSize = +_, tile) : tileSize;\n  };\n\n  tile.clamp = function(_) {\n    return arguments.length ? (clampX = clampY = !!_, tile) : clampX && clampY;\n  };\n\n  tile.clampX = function(_) {\n    return arguments.length ? (clampX = !!_, tile) : clampX;\n  };\n\n  tile.clampY = function(_) {\n    return arguments.length ? (clampY = !!_, tile) : clampY;\n  };\n\n  return tile;\n}\n\nfunction tileWrap([x, y, z]) {\n  const j = 1 << z;\n  return [x - Math.floor(x / j) * j, y - Math.floor(y / j) * j, z];\n}\n\nexports.tile = tile;\nexports.tileWrap = tileWrap;\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\n}));\n"], "mappings": ";;;;;AAAA;AAAA;AACA,KAAC,SAAU,QAAQ,SAAS;AAC5B,aAAO,YAAY,YAAY,OAAO,WAAW,cAAc,QAAQ,OAAO,IAC9E,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,CAAC,SAAS,GAAG,OAAO,KACvE,SAAS,UAAU,MAAM,QAAQ,OAAO,KAAK,OAAO,MAAM,CAAC,CAAC;AAAA,IAC7D,GAAE,SAAM,SAAUA,UAAS;AAAE;AAE7B,eAAS,aAAa,GAAG;AACvB,eAAO,EAAE;AAAA,MACX;AAEA,eAAS,iBAAiB,GAAG;AAC3B,eAAO,CAAC,EAAE,GAAG,EAAE,CAAC;AAAA,MAClB;AAEA,eAAS,SAAS,GAAG;AACnB,eAAO,WAAW;AAChB,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,OAAO;AACd,YAAI,KAAK,GAAG,KAAK,GAAG,KAAK,KAAK,KAAK;AACnC,YAAI,SAAS,MAAM,SAAS;AAC5B,YAAI,WAAW;AACf,YAAI,QAAQ;AACZ,YAAI,YAAY;AAChB,YAAI,YAAY;AAEhB,iBAASC,QAAO;AACd,gBAAM,SAAS,CAAC,MAAM,MAAM,MAAM,SAAS;AAC3C,gBAAM,aAAa,UAAU,MAAM,MAAM,SAAS;AAClD,gBAAM,IAAI,KAAK,KAAK,SAAS,QAAQ;AACrC,gBAAM,KAAK,KAAK,MAAM,KAAK,IAAI,IAAI,WAAW,CAAC,CAAC;AAChD,gBAAM,IAAI,KAAK,IAAI,GAAG,IAAI,EAAE,IAAI;AAChC,gBAAM,IAAI,CAAC,WAAW,CAAC,IAAI,SAAS;AACpC,gBAAM,IAAI,CAAC,WAAW,CAAC,IAAI,SAAS;AACpC,gBAAM,OAAO,KAAK,IAAI,SAAS,IAAI,WAAW,KAAK,OAAO,KAAK,KAAK,CAAC,CAAC;AACtE,gBAAM,OAAO,KAAK,IAAI,SAAS,KAAK,KAAK,UAAU,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC;AAC1E,gBAAM,OAAO,KAAK,IAAI,SAAS,IAAI,WAAW,KAAK,OAAO,KAAK,KAAK,CAAC,CAAC;AACtE,gBAAM,OAAO,KAAK,IAAI,SAAS,KAAK,KAAK,UAAU,KAAK,MAAM,KAAK,KAAK,CAAC,CAAC;AAC1E,gBAAM,QAAQ,CAAC;AACf,mBAASC,KAAI,MAAMA,KAAI,MAAM,EAAEA,IAAG;AAChC,qBAASC,KAAI,MAAMA,KAAI,MAAM,EAAEA,IAAG;AAChC,oBAAM,KAAK,CAACA,IAAGD,IAAG,EAAE,CAAC;AAAA,YACvB;AAAA,UACF;AACA,gBAAM,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;AAC/B,gBAAM,QAAQ;AACd,iBAAO;AAAA,QACT;AAEA,QAAAD,MAAK,OAAO,SAAS,GAAG;AACtB,iBAAO,UAAU,UAAU,KAAK,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAGA,SAAQ,CAAC,KAAK,IAAI,KAAK,EAAE;AAAA,QAC3F;AAEA,QAAAA,MAAK,SAAS,SAAS,GAAG;AACxB,iBAAO,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGA,SAAQ,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AAAA,QACpH;AAEA,QAAAA,MAAK,QAAQ,SAAS,GAAG;AACvB,iBAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,GAAGA,SAAQ;AAAA,QACzF;AAEA,QAAAA,MAAK,YAAY,SAAS,GAAG;AAC3B,iBAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGA,SAAQ;AAAA,QACzG;AAEA,QAAAA,MAAK,YAAY,SAAS,GAAG;AAC3B,iBAAO,UAAU,UAAU,YAAY,CAAC,GAAGA,SAAQ;AAAA,QACrD;AAEA,QAAAA,MAAK,WAAW,SAAS,GAAG;AAC1B,iBAAO,UAAU,UAAU,WAAW,CAAC,GAAGA,SAAQ;AAAA,QACpD;AAEA,QAAAA,MAAK,QAAQ,SAAS,GAAG;AACvB,iBAAO,UAAU,UAAU,SAAS,SAAS,CAAC,CAAC,GAAGA,SAAQ,UAAU;AAAA,QACtE;AAEA,QAAAA,MAAK,SAAS,SAAS,GAAG;AACxB,iBAAO,UAAU,UAAU,SAAS,CAAC,CAAC,GAAGA,SAAQ;AAAA,QACnD;AAEA,QAAAA,MAAK,SAAS,SAAS,GAAG;AACxB,iBAAO,UAAU,UAAU,SAAS,CAAC,CAAC,GAAGA,SAAQ;AAAA,QACnD;AAEA,eAAOA;AAAA,MACT;AAEA,eAAS,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG;AAC3B,cAAM,IAAI,KAAK;AACf,eAAO,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC;AAAA,MACjE;AAEA,MAAAD,SAAQ,OAAO;AACf,MAAAA,SAAQ,WAAW;AAEnB,aAAO,eAAeA,UAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAAA,IAE5D,CAAC;AAAA;AAAA;", "names": ["exports", "tile", "y", "x"]}