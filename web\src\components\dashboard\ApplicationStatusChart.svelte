<script lang="ts">
  import * as Card from '$lib/components/ui/card';
  import * as Chart from '$lib/components/ui/chart';
  import { BarChart } from 'layerchart';

  let {
    applicationStats,
  }: {
    applicationStats: {
      total: number;
      byStatus: Record<string, number>;
    };
  } = $props();

  // Format status for display
  const formatStatus = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  // Transform data for chart
  const chartData = $derived(() => {
    if (!applicationStats?.byStatus || Object.keys(applicationStats.byStatus).length === 0) {
      return [];
    }

    return Object.entries(applicationStats.byStatus)
      .map(([status, count]) => ({
        status: formatStatus(status),
        count: count as number,
        statusKey: status.toLowerCase(),
      }))
      .sort((a, b) => b.count - a.count);
  });

  // Chart configuration with status colors
  const chartConfig = {
    applied: {
      label: 'Applied',
      color: 'var(--chart-1)',
    },
    interview: {
      label: 'Interview',
      color: 'var(--chart-2)',
    },
    assessment: {
      label: 'Assessment',
      color: 'var(--chart-3)',
    },
    offer: {
      label: 'Offer',
      color: 'var(--chart-4)',
    },
    rejected: {
      label: 'Rejected',
      color: 'var(--chart-5)',
    },
    default: {
      label: 'Other',
      color: 'hsl(var(--muted-foreground))',
    },
  } satisfies Chart.ChartConfig;
</script>

<Card.Root>
  <Card.Header>
    <Card.Title>Application Status</Card.Title>
    <Card.Description>Distribution of applications by current status</Card.Description>
  </Card.Header>
  <Card.Content>
    {#if applicationStats.total === 0}
      <div class="flex flex-col items-center justify-center py-6 text-center text-gray-500">
        <p>No applications yet</p>
        <p class="mt-1 text-sm">Start applying to see your application statistics</p>
      </div>
    {:else if chartData.length === 0}
      <div class="flex flex-col items-center justify-center py-6 text-center text-gray-500">
        <p>No data available</p>
      </div>
    {:else}
      <Chart.Container config={chartConfig} class="h-40 w-full">
        <BarChart
          data={chartData}
          x="status"
          axis="x"
          legend
          series={chartData.map((item) => ({
            key: 'count',
            label: item.status,
            color: chartConfig[item.statusKey]?.color || chartConfig.default.color,
          }))}>
          {#snippet tooltip()}
            <Chart.Tooltip />
          {/snippet}
        </BarChart>
      </Chart.Container>
    {/if}
  </Card.Content>
</Card.Root>
