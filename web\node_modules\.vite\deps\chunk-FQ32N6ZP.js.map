{"version": 3, "sources": ["../../ts-deepmerge/esm/index.js"], "sourcesContent": ["// istanbul ignore next\nconst isObject = (obj) => {\n    if (typeof obj === \"object\" && obj !== null) {\n        if (typeof Object.getPrototypeOf === \"function\") {\n            const prototype = Object.getPrototypeOf(obj);\n            return prototype === Object.prototype || prototype === null;\n        }\n        return Object.prototype.toString.call(obj) === \"[object Object]\";\n    }\n    return false;\n};\nexport const merge = (...objects) => objects.reduce((result, current) => {\n    if (current === undefined) {\n        return result;\n    }\n    if (Array.isArray(current)) {\n        throw new TypeError(\"Arguments provided to ts-deepmerge must be objects, not arrays.\");\n    }\n    Object.keys(current).forEach((key) => {\n        if ([\"__proto__\", \"constructor\", \"prototype\"].includes(key)) {\n            return;\n        }\n        if (Array.isArray(result[key]) && Array.isArray(current[key])) {\n            result[key] = merge.options.mergeArrays\n                ? merge.options.uniqueArrayItems\n                    ? Array.from(new Set(result[key].concat(current[key])))\n                    : [...result[key], ...current[key]]\n                : current[key];\n        }\n        else if (isObject(result[key]) && isObject(current[key])) {\n            result[key] = merge(result[key], current[key]);\n        }\n        else if (!isObject(result[key]) && isObject(current[key])) {\n            result[key] = merge(current[key], undefined);\n        }\n        else {\n            result[key] =\n                current[key] === undefined\n                    ? merge.options.allowUndefinedOverrides\n                        ? current[key]\n                        : result[key]\n                    : current[key];\n        }\n    });\n    return result;\n}, {});\nconst defaultOptions = {\n    allowUndefinedOverrides: true,\n    mergeArrays: true,\n    uniqueArrayItems: true,\n};\nmerge.options = defaultOptions;\nmerge.withOptions = (options, ...objects) => {\n    merge.options = Object.assign(Object.assign({}, defaultOptions), options);\n    const result = merge(...objects);\n    merge.options = defaultOptions;\n    return result;\n};\n"], "mappings": ";AACA,IAAM,WAAW,CAAC,QAAQ;AACtB,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACzC,QAAI,OAAO,OAAO,mBAAmB,YAAY;AAC7C,YAAM,YAAY,OAAO,eAAe,GAAG;AAC3C,aAAO,cAAc,OAAO,aAAa,cAAc;AAAA,IAC3D;AACA,WAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAAA,EACnD;AACA,SAAO;AACX;AACO,IAAM,QAAQ,IAAI,YAAY,QAAQ,OAAO,CAAC,QAAQ,YAAY;AACrE,MAAI,YAAY,QAAW;AACvB,WAAO;AAAA,EACX;AACA,MAAI,MAAM,QAAQ,OAAO,GAAG;AACxB,UAAM,IAAI,UAAU,iEAAiE;AAAA,EACzF;AACA,SAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,QAAQ;AAClC,QAAI,CAAC,aAAa,eAAe,WAAW,EAAE,SAAS,GAAG,GAAG;AACzD;AAAA,IACJ;AACA,QAAI,MAAM,QAAQ,OAAO,GAAG,CAAC,KAAK,MAAM,QAAQ,QAAQ,GAAG,CAAC,GAAG;AAC3D,aAAO,GAAG,IAAI,MAAM,QAAQ,cACtB,MAAM,QAAQ,mBACV,MAAM,KAAK,IAAI,IAAI,OAAO,GAAG,EAAE,OAAO,QAAQ,GAAG,CAAC,CAAC,CAAC,IACpD,CAAC,GAAG,OAAO,GAAG,GAAG,GAAG,QAAQ,GAAG,CAAC,IACpC,QAAQ,GAAG;AAAA,IACrB,WACS,SAAS,OAAO,GAAG,CAAC,KAAK,SAAS,QAAQ,GAAG,CAAC,GAAG;AACtD,aAAO,GAAG,IAAI,MAAM,OAAO,GAAG,GAAG,QAAQ,GAAG,CAAC;AAAA,IACjD,WACS,CAAC,SAAS,OAAO,GAAG,CAAC,KAAK,SAAS,QAAQ,GAAG,CAAC,GAAG;AACvD,aAAO,GAAG,IAAI,MAAM,QAAQ,GAAG,GAAG,MAAS;AAAA,IAC/C,OACK;AACD,aAAO,GAAG,IACN,QAAQ,GAAG,MAAM,SACX,MAAM,QAAQ,0BACV,QAAQ,GAAG,IACX,OAAO,GAAG,IACd,QAAQ,GAAG;AAAA,IACzB;AAAA,EACJ,CAAC;AACD,SAAO;AACX,GAAG,CAAC,CAAC;AACL,IAAM,iBAAiB;AAAA,EACnB,yBAAyB;AAAA,EACzB,aAAa;AAAA,EACb,kBAAkB;AACtB;AACA,MAAM,UAAU;AAChB,MAAM,cAAc,CAAC,YAAY,YAAY;AACzC,QAAM,UAAU,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,cAAc,GAAG,OAAO;AACxE,QAAM,SAAS,MAAM,GAAG,OAAO;AAC/B,QAAM,UAAU;AAChB,SAAO;AACX;", "names": []}