import {
  __commonJS
} from "./chunk-KWPVD4H7.js";

// node_modules/memoize-weak/lib/memoize.js
var require_memoize = __commonJS({
  "node_modules/memoize-weak/lib/memoize.js"(exports, module) {
    function isPrimitive(value) {
      return typeof value !== "object" && typeof value !== "function" || value === null;
    }
    function MapTree() {
      this.childBranches = /* @__PURE__ */ new WeakMap();
      this.primitiveKeys = /* @__PURE__ */ new Map();
      this.hasValue = false;
      this.value = void 0;
    }
    MapTree.prototype.has = function has(key) {
      var keyObject = isPrimitive(key) ? this.primitiveKeys.get(key) : key;
      return keyObject ? this.childBranches.has(keyObject) : false;
    };
    MapTree.prototype.get = function get(key) {
      var keyObject = isPrimitive(key) ? this.primitiveKeys.get(key) : key;
      return keyObject ? this.childBranches.get(keyObject) : void 0;
    };
    MapTree.prototype.resolveBranch = function resolveBranch(key) {
      if (this.has(key)) {
        return this.get(key);
      }
      var newBranch = new MapTree();
      var keyObject = this.createKey(key);
      this.childBranches.set(keyObject, newBranch);
      return newBranch;
    };
    MapTree.prototype.setValue = function setValue(value) {
      this.hasValue = true;
      return this.value = value;
    };
    MapTree.prototype.createKey = function createKey(key) {
      if (isPrimitive(key)) {
        var keyObject = {};
        this.primitiveKeys.set(key, keyObject);
        return keyObject;
      }
      return key;
    };
    MapTree.prototype.clear = function clear() {
      if (arguments.length === 0) {
        this.childBranches = /* @__PURE__ */ new WeakMap();
        this.primitiveKeys.clear();
        this.hasValue = false;
        this.value = void 0;
      } else if (arguments.length === 1) {
        var key = arguments[0];
        if (isPrimitive(key)) {
          var keyObject = this.primitiveKeys.get(key);
          if (keyObject) {
            this.childBranches.delete(keyObject);
            this.primitiveKeys.delete(key);
          }
        } else {
          this.childBranches.delete(key);
        }
      } else {
        var childKey = arguments[0];
        if (this.has(childKey)) {
          var childBranch = this.get(childKey);
          childBranch.clear.apply(childBranch, Array.prototype.slice.call(arguments, 1));
        }
      }
    };
    module.exports = function memoize(fn) {
      var argsTree = new MapTree();
      function memoized() {
        var args = Array.prototype.slice.call(arguments);
        var argNode = args.reduce(function getBranch(parentBranch, arg) {
          return parentBranch.resolveBranch(arg);
        }, argsTree);
        if (argNode.hasValue) {
          return argNode.value;
        }
        var value = fn.apply(null, args);
        return argNode.setValue(value);
      }
      memoized.clear = argsTree.clear.bind(argsTree);
      return memoized;
    };
  }
});

// node_modules/memoize-weak/index.js
var require_memoize_weak = __commonJS({
  "node_modules/memoize-weak/index.js"(exports, module) {
    module.exports = require_memoize();
  }
});

export {
  require_memoize_weak
};
//# sourceMappingURL=chunk-BZE7UB6G.js.map
