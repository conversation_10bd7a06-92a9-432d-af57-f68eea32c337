{"version": 3, "sources": ["../../culori/src/rgb/parseNumber.js", "../../culori/src/colors/named.js", "../../culori/src/rgb/parseNamed.js", "../../culori/src/rgb/parseHex.js", "../../culori/src/util/regex.js", "../../culori/src/rgb/parseRgbLegacy.js", "../../culori/src/_prepare.js", "../../culori/src/converter.js", "../../culori/src/modes.js", "../../culori/src/parse.js", "../../culori/src/rgb/parseRgb.js", "../../culori/src/rgb/parseTransparent.js", "../../culori/src/interpolate/lerp.js", "../../culori/src/interpolate/piecewise.js", "../../culori/src/interpolate/linear.js", "../../culori/src/fixup/alpha.js", "../../culori/src/rgb/definition.js", "../../culori/src/a98/convertA98ToXyz65.js", "../../culori/src/a98/convertXyz65ToA98.js", "../../culori/src/lrgb/convertRgbToLrgb.js", "../../culori/src/xyz65/convertRgbToXyz65.js", "../../culori/src/lrgb/convertLrgbToRgb.js", "../../culori/src/xyz65/convertXyz65ToRgb.js", "../../culori/src/a98/definition.js", "../../culori/src/util/normalizeHue.js", "../../culori/src/fixup/hue.js", "../../culori/src/cubehelix/constants.js", "../../culori/src/cubehelix/convertRgbToCubehelix.js", "../../culori/src/cubehelix/convertCubehelixToRgb.js", "../../culori/src/difference.js", "../../culori/src/average.js", "../../culori/src/cubehelix/definition.js", "../../culori/src/lch/convertLabToLch.js", "../../culori/src/lch/convertLchToLab.js", "../../culori/src/xyz65/constants.js", "../../culori/src/constants.js", "../../culori/src/lab65/convertLab65ToXyz65.js", "../../culori/src/lab65/convertLab65ToRgb.js", "../../culori/src/lab65/convertXyz65ToLab65.js", "../../culori/src/lab65/convertRgbToLab65.js", "../../culori/src/dlch/constants.js", "../../culori/src/dlch/convertDlchToLab65.js", "../../culori/src/dlch/convertLab65ToDlch.js", "../../culori/src/dlab/definition.js", "../../culori/src/dlch/definition.js", "../../culori/src/hsi/convertHsiToRgb.js", "../../culori/src/hsi/convertRgbToHsi.js", "../../culori/src/hsi/definition.js", "../../culori/src/hsl/convertHslToRgb.js", "../../culori/src/hsl/convertRgbToHsl.js", "../../culori/src/util/hue.js", "../../culori/src/hsl/parseHslLegacy.js", "../../culori/src/hsl/parseHsl.js", "../../culori/src/hsl/definition.js", "../../culori/src/hsv/convertHsvToRgb.js", "../../culori/src/hsv/convertRgbToHsv.js", "../../culori/src/hsv/definition.js", "../../culori/src/hwb/convertHwbToRgb.js", "../../culori/src/hwb/convertRgbToHwb.js", "../../culori/src/hwb/parseHwb.js", "../../culori/src/hwb/definition.js", "../../culori/src/hdr/constants.js", "../../culori/src/hdr/transfer.js", "../../culori/src/itp/convertItpToXyz65.js", "../../culori/src/itp/convertXyz65ToItp.js", "../../culori/src/itp/definition.js", "../../culori/src/jab/convertXyz65ToJab.js", "../../culori/src/jab/convertJabToXyz65.js", "../../culori/src/jab/convertRgbToJab.js", "../../culori/src/jab/convertJabToRgb.js", "../../culori/src/jab/definition.js", "../../culori/src/jch/convertJabToJch.js", "../../culori/src/jch/convertJchToJab.js", "../../culori/src/jch/definition.js", "../../culori/src/xyz50/constants.js", "../../culori/src/lab/convertLabToXyz50.js", "../../culori/src/xyz50/convertXyz50ToRgb.js", "../../culori/src/lab/convertLabToRgb.js", "../../culori/src/xyz50/convertRgbToXyz50.js", "../../culori/src/lab/convertXyz50ToLab.js", "../../culori/src/lab/convertRgbToLab.js", "../../culori/src/lab/parseLab.js", "../../culori/src/lab/definition.js", "../../culori/src/lab65/definition.js", "../../culori/src/lch/parseLch.js", "../../culori/src/lch/definition.js", "../../culori/src/lch65/definition.js", "../../culori/src/lchuv/convertLuvToLchuv.js", "../../culori/src/lchuv/convertLchuvToLuv.js", "../../culori/src/luv/convertXyz50ToLuv.js", "../../culori/src/luv/convertLuvToXyz50.js", "../../culori/src/lchuv/definition.js", "../../culori/src/lrgb/definition.js", "../../culori/src/luv/definition.js", "../../culori/src/oklab/convertLrgbToOklab.js", "../../culori/src/oklab/convertRgbToOklab.js", "../../culori/src/oklab/convertOklabToLrgb.js", "../../culori/src/oklab/convertOklabToRgb.js", "../../culori/src/okhsl/helpers.js", "../../culori/src/okhsl/convertOklabToOkhsl.js", "../../culori/src/okhsl/convertOkhslToOklab.js", "../../culori/src/okhsl/modeOkhsl.js", "../../culori/src/okhsv/convertOklabToOkhsv.js", "../../culori/src/okhsv/convertOkhsvToOklab.js", "../../culori/src/okhsv/modeOkhsv.js", "../../culori/src/oklab/parseOklab.js", "../../culori/src/oklab/definition.js", "../../culori/src/oklch/parseOklch.js", "../../culori/src/oklch/definition.js", "../../culori/src/p3/convertP3ToXyz65.js", "../../culori/src/p3/convertXyz65ToP3.js", "../../culori/src/p3/definition.js", "../../culori/src/prophoto/convertXyz50ToProphoto.js", "../../culori/src/prophoto/convertProphotoToXyz50.js", "../../culori/src/prophoto/definition.js", "../../culori/src/rec2020/convertXyz65ToRec2020.js", "../../culori/src/rec2020/convertRec2020ToXyz65.js", "../../culori/src/rec2020/definition.js", "../../culori/src/xyb/constants.js", "../../culori/src/xyb/convertRgbToXyb.js", "../../culori/src/xyb/convertXybToRgb.js", "../../culori/src/xyb/definition.js", "../../culori/src/xyz50/definition.js", "../../culori/src/xyz65/convertXyz65ToXyz50.js", "../../culori/src/xyz65/convertXyz50ToXyz65.js", "../../culori/src/xyz65/definition.js", "../../culori/src/yiq/convertRgbToYiq.js", "../../culori/src/yiq/convertYiqToRgb.js", "../../culori/src/yiq/definition.js", "../../culori/src/round.js", "../../culori/src/formatter.js", "../../culori/src/map.js", "../../culori/src/util/normalizePositions.js", "../../culori/src/easing/midpoint.js", "../../culori/src/interpolate/interpolate.js", "../../culori/src/clamp.js", "../../culori/src/deficiency.js", "../../culori/src/wcag.js", "../../culori/src/index.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/array.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/object.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/typeHelpers.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/date.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/typeGuards.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/date_types.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/dateInternal.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/locale.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/duration.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/number.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/json.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/env.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/dateRange.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/map.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/rollup.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/routing.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/serialize.js", "../../@layerstack/tailwind/node_modules/@layerstack/utils/dist/styles.js", "../../@layerstack/tailwind/dist/theme.js", "../../@layerstack/tailwind/dist/utils.js"], "sourcesContent": ["const parseNumber = (color, len) => {\n\tif (typeof color !== 'number') return;\n\n\t// hex3: #c93 -> #cc9933\n\tif (len === 3) {\n\t\treturn {\n\t\t\tmode: 'rgb',\n\t\t\tr: (((color >> 8) & 0xf) | ((color >> 4) & 0xf0)) / 255,\n\t\t\tg: (((color >> 4) & 0xf) | (color & 0xf0)) / 255,\n\t\t\tb: ((color & 0xf) | ((color << 4) & 0xf0)) / 255\n\t\t};\n\t}\n\n\t// hex4: #c931 -> #cc993311\n\tif (len === 4) {\n\t\treturn {\n\t\t\tmode: 'rgb',\n\t\t\tr: (((color >> 12) & 0xf) | ((color >> 8) & 0xf0)) / 255,\n\t\t\tg: (((color >> 8) & 0xf) | ((color >> 4) & 0xf0)) / 255,\n\t\t\tb: (((color >> 4) & 0xf) | (color & 0xf0)) / 255,\n\t\t\talpha: ((color & 0xf) | ((color << 4) & 0xf0)) / 255\n\t\t};\n\t}\n\n\t// hex6: #f0f1f2\n\tif (len === 6) {\n\t\treturn {\n\t\t\tmode: 'rgb',\n\t\t\tr: ((color >> 16) & 0xff) / 255,\n\t\t\tg: ((color >> 8) & 0xff) / 255,\n\t\t\tb: (color & 0xff) / 255\n\t\t};\n\t}\n\n\t// hex8: #f0f1f2ff\n\tif (len === 8) {\n\t\treturn {\n\t\t\tmode: 'rgb',\n\t\t\tr: ((color >> 24) & 0xff) / 255,\n\t\t\tg: ((color >> 16) & 0xff) / 255,\n\t\t\tb: ((color >> 8) & 0xff) / 255,\n\t\t\talpha: (color & 0xff) / 255\n\t\t};\n\t}\n};\n\nexport default parseNumber;\n", "const named = {\n\taliceblue: 0xf0f8ff,\n\tantiquewhite: 0xfaebd7,\n\taqua: 0x00ffff,\n\taquamarine: 0x7fffd4,\n\tazure: 0xf0ffff,\n\tbeige: 0xf5f5dc,\n\tbisque: 0xffe4c4,\n\tblack: 0x000000,\n\tblanche<PERSON>mond: 0xffebcd,\n\tblue: 0x0000ff,\n\tblueviolet: 0x8a2be2,\n\tbrown: 0xa52a2a,\n\tburlywood: 0xdeb887,\n\tcadetblue: 0x5f9ea0,\n\tchartreuse: 0x7fff00,\n\tchocolate: 0xd2691e,\n\tcoral: 0xff7f50,\n\tcornflowerblue: 0x6495ed,\n\tcornsilk: 0xfff8dc,\n\tcrimson: 0xdc143c,\n\tcyan: 0x00ffff,\n\tdarkblue: 0x00008b,\n\tdarkcyan: 0x008b8b,\n\tdarkgoldenrod: 0xb8860b,\n\tdarkgray: 0xa9a9a9,\n\tdarkgreen: 0x006400,\n\tdarkgrey: 0xa9a9a9,\n\tdarkkhaki: 0xbdb76b,\n\tdarkmagenta: 0x8b008b,\n\tdarkolivegreen: 0x556b2f,\n\tdarkorange: 0xff8c00,\n\tdarkorchid: 0x9932cc,\n\tdarkred: 0x8b0000,\n\tdarksalmon: 0xe9967a,\n\tdarkseagreen: 0x8fbc8f,\n\tdarkslateblue: 0x483d8b,\n\tdarkslategray: 0x2f4f4f,\n\tdarkslategrey: 0x2f4f4f,\n\tdarkturquoise: 0x00ced1,\n\tdarkviolet: 0x9400d3,\n\tdeeppink: 0xff1493,\n\tdeepskyblue: 0x00bfff,\n\tdimgray: 0x696969,\n\tdimgrey: 0x696969,\n\tdodgerblue: 0x1e90ff,\n\tfirebrick: 0xb22222,\n\tfloralwhite: 0xfffaf0,\n\tforestgreen: 0x228b22,\n\tfuchsia: 0xff00ff,\n\tgainsboro: 0xdcdcdc,\n\tghostwhite: 0xf8f8ff,\n\tgold: 0xffd700,\n\tgoldenrod: 0xdaa520,\n\tgray: 0x808080,\n\tgreen: 0x008000,\n\tgreenyellow: 0xadff2f,\n\tgrey: 0x808080,\n\thoneydew: 0xf0fff0,\n\thotpink: 0xff69b4,\n\tindianred: 0xcd5c5c,\n\tindigo: 0x4b0082,\n\tivory: 0xfffff0,\n\tkhaki: 0xf0e68c,\n\tlavender: 0xe6e6fa,\n\tlavenderblush: 0xfff0f5,\n\tlawngreen: 0x7cfc00,\n\tlemonchiffon: 0xfffacd,\n\tlightblue: 0xadd8e6,\n\tlightcoral: 0xf08080,\n\tlightcyan: 0xe0ffff,\n\tlightgoldenrodyellow: 0xfafad2,\n\tlightgray: 0xd3d3d3,\n\tlightgreen: 0x90ee90,\n\tlightgrey: 0xd3d3d3,\n\tlightpink: 0xffb6c1,\n\tlightsalmon: 0xffa07a,\n\tlightseagreen: 0x20b2aa,\n\tlightskyblue: 0x87cefa,\n\tlightslategray: 0x778899,\n\tlightslategrey: 0x778899,\n\tlightsteelblue: 0xb0c4de,\n\tlightyellow: 0xffffe0,\n\tlime: 0x00ff00,\n\tlimegreen: 0x32cd32,\n\tlinen: 0xfaf0e6,\n\tmagenta: 0xff00ff,\n\tmaroon: 0x800000,\n\tmediumaquamarine: 0x66cdaa,\n\tmediumblue: 0x0000cd,\n\tmediumorchid: 0xba55d3,\n\tmediumpurple: 0x9370db,\n\tmediumseagreen: 0x3cb371,\n\tmediumslateblue: 0x7b68ee,\n\tmediumspringgreen: 0x00fa9a,\n\tmediumturquoise: 0x48d1cc,\n\tmediumvioletred: 0xc71585,\n\tmidnightblue: 0x191970,\n\tmintcream: 0xf5fffa,\n\tmistyrose: 0xffe4e1,\n\tmoccasin: 0xffe4b5,\n\tnavajowhite: 0xffdead,\n\tnavy: 0x000080,\n\toldlace: 0xfdf5e6,\n\tolive: 0x808000,\n\tolivedrab: 0x6b8e23,\n\torange: 0xffa500,\n\torangered: 0xff4500,\n\torchid: 0xda70d6,\n\tpalegoldenrod: 0xeee8aa,\n\tpalegreen: 0x98fb98,\n\tpaleturquoise: 0xafeeee,\n\tpalevioletred: 0xdb7093,\n\tpapayawhip: 0xffefd5,\n\tpeachpuff: 0xffdab9,\n\tperu: 0xcd853f,\n\tpink: 0xffc0cb,\n\tplum: 0xdda0dd,\n\tpowderblue: 0xb0e0e6,\n\tpurple: 0x800080,\n\n\t// Added in CSS Colors Level 4:\n\t// https://drafts.csswg.org/css-color/#changes-from-3\n\trebeccapurple: 0x663399,\n\n\tred: 0xff0000,\n\trosybrown: 0xbc8f8f,\n\troyalblue: 0x4169e1,\n\tsaddlebrown: 0x8b4513,\n\tsalmon: 0xfa8072,\n\tsandybrown: 0xf4a460,\n\tseagreen: 0x2e8b57,\n\tseashell: 0xfff5ee,\n\tsienna: 0xa0522d,\n\tsilver: 0xc0c0c0,\n\tskyblue: 0x87ceeb,\n\tslateblue: 0x6a5acd,\n\tslategray: 0x708090,\n\tslategrey: 0x708090,\n\tsnow: 0xfffafa,\n\tspringgreen: 0x00ff7f,\n\tsteelblue: 0x4682b4,\n\ttan: 0xd2b48c,\n\tteal: 0x008080,\n\tthistle: 0xd8bfd8,\n\ttomato: 0xff6347,\n\tturquoise: 0x40e0d0,\n\tviolet: 0xee82ee,\n\twheat: 0xf5deb3,\n\twhite: 0xffffff,\n\twhitesmoke: 0xf5f5f5,\n\tyellow: 0xffff00,\n\tyellowgreen: 0x9acd32\n};\n\nexport default named;\n", "import parseNumber from './parseNumber.js';\nimport named from '../colors/named.js';\n\n// Also supports the `transparent` color as defined in:\n// https://drafts.csswg.org/css-color/#transparent-black\nconst parseNamed = color => {\n\treturn parseNumber(named[color.toLowerCase()], 6);\n};\n\nexport default parseNamed;\n", "import parseNumber from './parseNumber.js';\n\nconst hex = /^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i;\n\nconst parseHex = color => {\n\tlet match;\n\t// eslint-disable-next-line no-cond-assign\n\treturn (match = color.match(hex))\n\t\t? parseNumber(parseInt(match[1], 16), match[1].length)\n\t\t: undefined;\n};\n\nexport default parseHex;\n", "/*\n\tBasic building blocks for color regexes\n\t---------------------------------------\n\n\tThese regexes are expressed as strings\n\tto be interpolated in the color regexes.\n */\n\n// <number>\nexport const num = '([+-]?\\\\d*\\\\.?\\\\d+(?:[eE][+-]?\\\\d+)?)';\n\n// <number> or 'none'\nexport const num_none = `(?:${num}|none)`;\n\n// <percentage>\nexport const per = `${num}%`;\n\n// <percent> or 'none'\nexport const per_none = `(?:${num}%|none)`;\n\n// <number-percentage> (<alpha-value>)\nexport const num_per = `(?:${num}%|${num})`;\n\n// <number-percentage> (<alpha-value>) or 'none'\nexport const num_per_none = `(?:${num}%|${num}|none)`;\n\n// <hue>\nexport const hue = `(?:${num}(deg|grad|rad|turn)|${num})`;\n\n// <hue> or 'none'\nexport const hue_none = `(?:${num}(deg|grad|rad|turn)|${num}|none)`;\n\nexport const c = `\\\\s*,\\\\s*`; // comma\nexport const so = '\\\\s*'; // space, optional\nexport const s = `\\\\s+`; // space\n\nexport const rx_num_per_none = new RegExp('^' + num_per_none + '$');\n", "import { num, per, num_per, c } from '../util/regex.js';\n\n/*\n\trgb() regular expressions for legacy format\n\tReference: https://drafts.csswg.org/css-color/#rgb-functions\n */\nconst rgb_num_old = new RegExp(\n\t`^rgba?\\\\(\\\\s*${num}${c}${num}${c}${num}\\\\s*(?:,\\\\s*${num_per}\\\\s*)?\\\\)$`\n);\n\nconst rgb_per_old = new RegExp(\n\t`^rgba?\\\\(\\\\s*${per}${c}${per}${c}${per}\\\\s*(?:,\\\\s*${num_per}\\\\s*)?\\\\)$`\n);\n\nconst parseRgbLegacy = color => {\n\tlet res = { mode: 'rgb' };\n\tlet match;\n\tif ((match = color.match(rgb_num_old))) {\n\t\tif (match[1] !== undefined) {\n\t\t\tres.r = match[1] / 255;\n\t\t}\n\t\tif (match[2] !== undefined) {\n\t\t\tres.g = match[2] / 255;\n\t\t}\n\t\tif (match[3] !== undefined) {\n\t\t\tres.b = match[3] / 255;\n\t\t}\n\t} else if ((match = color.match(rgb_per_old))) {\n\t\tif (match[1] !== undefined) {\n\t\t\tres.r = match[1] / 100;\n\t\t}\n\t\tif (match[2] !== undefined) {\n\t\t\tres.g = match[2] / 100;\n\t\t}\n\t\tif (match[3] !== undefined) {\n\t\t\tres.b = match[3] / 100;\n\t\t}\n\t} else {\n\t\treturn undefined;\n\t}\n\n\tif (match[4] !== undefined) {\n\t\tres.alpha = Math.max(0, Math.min(1, match[4] / 100));\n\t} else if (match[5] !== undefined) {\n\t\tres.alpha = Math.max(0, Math.min(1, +match[5]));\n\t}\n\n\treturn res;\n};\n\nexport default parseRgbLegacy;\n", "import parse from './parse.js';\n\nconst prepare = (color, mode) =>\n\tcolor === undefined\n\t\t? undefined\n\t\t: typeof color !== 'object'\n\t\t? parse(color)\n\t\t: color.mode !== undefined\n\t\t? color\n\t\t: mode\n\t\t? { ...color, mode }\n\t\t: undefined;\n\nexport default prepare;\n", "import { converters } from './modes.js';\nimport prepare from './_prepare.js';\n\nconst converter =\n\t(target_mode = 'rgb') =>\n\tcolor =>\n\t\t(color = prepare(color, target_mode)) !== undefined\n\t\t\t? // if the color's mode corresponds to our target mode\n\t\t\t  color.mode === target_mode\n\t\t\t\t? // then just return the color\n\t\t\t\t  color\n\t\t\t\t: // otherwise check to see if we have a dedicated\n\t\t\t\t// converter for the target mode\n\t\t\t\tconverters[color.mode][target_mode]\n\t\t\t\t? // and return its result...\n\t\t\t\t  converters[color.mode][target_mode](color)\n\t\t\t\t: // ...otherwise pass through RGB as an intermediary step.\n\t\t\t\t// if the target mode is RGB...\n\t\t\t\ttarget_mode === 'rgb'\n\t\t\t\t? // just return the RGB\n\t\t\t\t  converters[color.mode].rgb(color)\n\t\t\t\t: // otherwise convert color.mode -> RGB -> target_mode\n\t\t\t\t  converters.rgb[target_mode](converters[color.mode].rgb(color))\n\t\t\t: undefined;\n\nexport default converter;\n", "import converter from './converter.js';\n\nconst converters = {};\nconst modes = {};\n\nconst parsers = [];\nconst colorProfiles = {};\n\nconst identity = v => v;\n\nconst useMode = definition => {\n\tconverters[definition.mode] = {\n\t\t...converters[definition.mode],\n\t\t...definition.toMode\n\t};\n\n\tObject.keys(definition.fromMode || {}).forEach(k => {\n\t\tif (!converters[k]) {\n\t\t\tconverters[k] = {};\n\t\t}\n\t\tconverters[k][definition.mode] = definition.fromMode[k];\n\t});\n\n\t// Color space channel ranges\n\tif (!definition.ranges) {\n\t\tdefinition.ranges = {};\n\t}\n\n\tif (!definition.difference) {\n\t\tdefinition.difference = {};\n\t}\n\n\tdefinition.channels.forEach(channel => {\n\t\t// undefined channel ranges default to the [0, 1] interval\n\t\tif (definition.ranges[channel] === undefined) {\n\t\t\tdefinition.ranges[channel] = [0, 1];\n\t\t}\n\n\t\tif (!definition.interpolate[channel]) {\n\t\t\tthrow new Error(`Missing interpolator for: ${channel}`);\n\t\t}\n\n\t\tif (typeof definition.interpolate[channel] === 'function') {\n\t\t\tdefinition.interpolate[channel] = {\n\t\t\t\tuse: definition.interpolate[channel]\n\t\t\t};\n\t\t}\n\n\t\tif (!definition.interpolate[channel].fixup) {\n\t\t\tdefinition.interpolate[channel].fixup = identity;\n\t\t}\n\t});\n\n\tmodes[definition.mode] = definition;\n\t(definition.parse || []).forEach(parser => {\n\t\tuseParser(parser, definition.mode);\n\t});\n\n\treturn converter(definition.mode);\n};\n\nconst getMode = mode => modes[mode];\n\nconst useParser = (parser, mode) => {\n\tif (typeof parser === 'string') {\n\t\tif (!mode) {\n\t\t\tthrow new Error(`'mode' required when 'parser' is a string`);\n\t\t}\n\t\tcolorProfiles[parser] = mode;\n\t} else if (typeof parser === 'function') {\n\t\tif (parsers.indexOf(parser) < 0) {\n\t\t\tparsers.push(parser);\n\t\t}\n\t}\n};\n\nconst removeParser = parser => {\n\tif (typeof parser === 'string') {\n\t\tdelete colorProfiles[parser];\n\t} else if (typeof parser === 'function') {\n\t\tconst idx = parsers.indexOf(parser);\n\t\tif (idx > 0) {\n\t\t\tparsers.splice(idx, 1);\n\t\t}\n\t}\n};\n\nexport {\n\tuseMode,\n\tgetMode,\n\tuseParser,\n\tremoveParser,\n\tconverters,\n\tparsers,\n\tcolorProfiles\n};\n", "import { parsers, colorProfiles, getMode } from './modes.js';\n\n/* eslint-disable-next-line no-control-regex */\nconst IdentStartCodePoint = /[^\\x00-\\x7F]|[a-zA-Z_]/;\n\n/* eslint-disable-next-line no-control-regex */\nconst IdentCodePoint = /[^\\x00-\\x7F]|[-\\w]/;\n\nexport const Tok = {\n\tFunction: 'function',\n\tIdent: 'ident',\n\tNumber: 'number',\n\tPercentage: 'percentage',\n\tParenClose: ')',\n\tNone: 'none',\n\tHue: 'hue',\n\tAlpha: 'alpha'\n};\n\nlet _i = 0;\n\n/*\n\t4.3.10. Check if three code points would start a number\n\thttps://drafts.csswg.org/css-syntax/#starts-with-a-number\n */\nfunction is_num(chars) {\n\tlet ch = chars[_i];\n\tlet ch1 = chars[_i + 1];\n\tif (ch === '-' || ch === '+') {\n\t\treturn /\\d/.test(ch1) || (ch1 === '.' && /\\d/.test(chars[_i + 2]));\n\t}\n\tif (ch === '.') {\n\t\treturn /\\d/.test(ch1);\n\t}\n\treturn /\\d/.test(ch);\n}\n\n/*\n\tCheck if the stream starts with an identifier.\n */\n\nfunction is_ident(chars) {\n\tif (_i >= chars.length) {\n\t\treturn false;\n\t}\n\tlet ch = chars[_i];\n\tif (IdentStartCodePoint.test(ch)) {\n\t\treturn true;\n\t}\n\tif (ch === '-') {\n\t\tif (chars.length - _i < 2) {\n\t\t\treturn false;\n\t\t}\n\t\tlet ch1 = chars[_i + 1];\n\t\tif (ch1 === '-' || IdentStartCodePoint.test(ch1)) {\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\treturn false;\n}\n\n/*\n\t4.3.3. Consume a numeric token\n\thttps://drafts.csswg.org/css-syntax/#consume-numeric-token\n */\n\nconst huenits = {\n\tdeg: 1,\n\trad: 180 / Math.PI,\n\tgrad: 9 / 10,\n\tturn: 360\n};\n\nfunction num(chars) {\n\tlet value = '';\n\tif (chars[_i] === '-' || chars[_i] === '+') {\n\t\tvalue += chars[_i++];\n\t}\n\tvalue += digits(chars);\n\tif (chars[_i] === '.' && /\\d/.test(chars[_i + 1])) {\n\t\tvalue += chars[_i++] + digits(chars);\n\t}\n\tif (chars[_i] === 'e' || chars[_i] === 'E') {\n\t\tif (\n\t\t\t(chars[_i + 1] === '-' || chars[_i + 1] === '+') &&\n\t\t\t/\\d/.test(chars[_i + 2])\n\t\t) {\n\t\t\tvalue += chars[_i++] + chars[_i++] + digits(chars);\n\t\t} else if (/\\d/.test(chars[_i + 1])) {\n\t\t\tvalue += chars[_i++] + digits(chars);\n\t\t}\n\t}\n\tif (is_ident(chars)) {\n\t\tlet id = ident(chars);\n\t\tif (id === 'deg' || id === 'rad' || id === 'turn' || id === 'grad') {\n\t\t\treturn { type: Tok.Hue, value: value * huenits[id] };\n\t\t}\n\t\treturn undefined;\n\t}\n\tif (chars[_i] === '%') {\n\t\t_i++;\n\t\treturn { type: Tok.Percentage, value: +value };\n\t}\n\treturn { type: Tok.Number, value: +value };\n}\n\n/*\n\tConsume digits.\n */\nfunction digits(chars) {\n\tlet v = '';\n\twhile (/\\d/.test(chars[_i])) {\n\t\tv += chars[_i++];\n\t}\n\treturn v;\n}\n\n/*\n\tConsume an identifier.\n */\nfunction ident(chars) {\n\tlet v = '';\n\twhile (_i < chars.length && IdentCodePoint.test(chars[_i])) {\n\t\tv += chars[_i++];\n\t}\n\treturn v;\n}\n\n/*\n\tConsume an ident-like token.\n */\nfunction identlike(chars) {\n\tlet v = ident(chars);\n\tif (chars[_i] === '(') {\n\t\t_i++;\n\t\treturn { type: Tok.Function, value: v };\n\t}\n\tif (v === 'none') {\n\t\treturn { type: Tok.None, value: undefined };\n\t}\n\treturn { type: Tok.Ident, value: v };\n}\n\nexport function tokenize(str = '') {\n\tlet chars = str.trim();\n\tlet tokens = [];\n\tlet ch;\n\n\t/* reset counter */\n\t_i = 0;\n\n\twhile (_i < chars.length) {\n\t\tch = chars[_i++];\n\n\t\t/*\n\t\t\tConsume whitespace without emitting it\n\t\t */\n\t\tif (ch === '\\n' || ch === '\\t' || ch === ' ') {\n\t\t\twhile (\n\t\t\t\t_i < chars.length &&\n\t\t\t\t(chars[_i] === '\\n' || chars[_i] === '\\t' || chars[_i] === ' ')\n\t\t\t) {\n\t\t\t\t_i++;\n\t\t\t}\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (ch === ',') {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (ch === ')') {\n\t\t\ttokens.push({ type: Tok.ParenClose });\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (ch === '+') {\n\t\t\t_i--;\n\t\t\tif (is_num(chars)) {\n\t\t\t\ttokens.push(num(chars));\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (ch === '-') {\n\t\t\t_i--;\n\t\t\tif (is_num(chars)) {\n\t\t\t\ttokens.push(num(chars));\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tif (is_ident(chars)) {\n\t\t\t\ttokens.push({ type: Tok.Ident, value: ident(chars) });\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (ch === '.') {\n\t\t\t_i--;\n\t\t\tif (is_num(chars)) {\n\t\t\t\ttokens.push(num(chars));\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (ch === '/') {\n\t\t\twhile (\n\t\t\t\t_i < chars.length &&\n\t\t\t\t(chars[_i] === '\\n' || chars[_i] === '\\t' || chars[_i] === ' ')\n\t\t\t) {\n\t\t\t\t_i++;\n\t\t\t}\n\t\t\tlet alpha;\n\t\t\tif (is_num(chars)) {\n\t\t\t\talpha = num(chars);\n\t\t\t\tif (alpha.type !== Tok.Hue) {\n\t\t\t\t\ttokens.push({ type: Tok.Alpha, value: alpha });\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (is_ident(chars)) {\n\t\t\t\tif (ident(chars) === 'none') {\n\t\t\t\t\ttokens.push({\n\t\t\t\t\t\ttype: Tok.Alpha,\n\t\t\t\t\t\tvalue: { type: Tok.None, value: undefined }\n\t\t\t\t\t});\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (/\\d/.test(ch)) {\n\t\t\t_i--;\n\t\t\ttokens.push(num(chars));\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (IdentStartCodePoint.test(ch)) {\n\t\t\t_i--;\n\t\t\ttokens.push(identlike(chars));\n\t\t\tcontinue;\n\t\t}\n\n\t\t/*\n\t\t\tTreat everything not already handled as an error.\n\t\t */\n\t\treturn undefined;\n\t}\n\n\treturn tokens;\n}\n\nexport function parseColorSyntax(tokens) {\n\ttokens._i = 0;\n\tlet token = tokens[tokens._i++];\n\tif (!token || token.type !== Tok.Function || token.value !== 'color') {\n\t\treturn undefined;\n\t}\n\ttoken = tokens[tokens._i++];\n\tif (token.type !== Tok.Ident) {\n\t\treturn undefined;\n\t}\n\tconst mode = colorProfiles[token.value];\n\tif (!mode) {\n\t\treturn undefined;\n\t}\n\tconst res = { mode };\n\tconst coords = consumeCoords(tokens, false);\n\tif (!coords) {\n\t\treturn undefined;\n\t}\n\tconst channels = getMode(mode).channels;\n\tfor (let ii = 0, c, ch; ii < channels.length; ii++) {\n\t\tc = coords[ii];\n\t\tch = channels[ii];\n\t\tif (c.type !== Tok.None) {\n\t\t\tres[ch] = c.type === Tok.Number ? c.value : c.value / 100;\n\t\t\tif (ch === 'alpha') {\n\t\t\t\tres[ch] = Math.max(0, Math.min(1, res[ch]));\n\t\t\t}\n\t\t}\n\t}\n\treturn res;\n}\n\nfunction consumeCoords(tokens, includeHue) {\n\tconst coords = [];\n\tlet token;\n\twhile (tokens._i < tokens.length) {\n\t\ttoken = tokens[tokens._i++];\n\t\tif (\n\t\t\ttoken.type === Tok.None ||\n\t\t\ttoken.type === Tok.Number ||\n\t\t\ttoken.type === Tok.Alpha ||\n\t\t\ttoken.type === Tok.Percentage ||\n\t\t\t(includeHue && token.type === Tok.Hue)\n\t\t) {\n\t\t\tcoords.push(token);\n\t\t\tcontinue;\n\t\t}\n\t\tif (token.type === Tok.ParenClose) {\n\t\t\tif (tokens._i < tokens.length) {\n\t\t\t\treturn undefined;\n\t\t\t}\n\t\t\tcontinue;\n\t\t}\n\t\treturn undefined;\n\t}\n\n\tif (coords.length < 3 || coords.length > 4) {\n\t\treturn undefined;\n\t}\n\n\tif (coords.length === 4) {\n\t\tif (coords[3].type !== Tok.Alpha) {\n\t\t\treturn undefined;\n\t\t}\n\t\tcoords[3] = coords[3].value;\n\t}\n\tif (coords.length === 3) {\n\t\tcoords.push({ type: Tok.None, value: undefined });\n\t}\n\n\treturn coords.every(c => c.type !== Tok.Alpha) ? coords : undefined;\n}\n\nexport function parseModernSyntax(tokens, includeHue) {\n\ttokens._i = 0;\n\tlet token = tokens[tokens._i++];\n\tif (!token || token.type !== Tok.Function) {\n\t\treturn undefined;\n\t}\n\tlet coords = consumeCoords(tokens, includeHue);\n\tif (!coords) {\n\t\treturn undefined;\n\t}\n\tcoords.unshift(token.value);\n\treturn coords;\n}\n\nconst parse = color => {\n\tif (typeof color !== 'string') {\n\t\treturn undefined;\n\t}\n\tconst tokens = tokenize(color);\n\tconst parsed = tokens ? parseModernSyntax(tokens, true) : undefined;\n\tlet result = undefined;\n\tlet i = 0;\n\tlet len = parsers.length;\n\twhile (i < len) {\n\t\tif ((result = parsers[i++](color, parsed)) !== undefined) {\n\t\t\treturn result;\n\t\t}\n\t}\n\treturn tokens ? parseColorSyntax(tokens) : undefined;\n};\n\nexport default parse;\n", "import { Tok } from '../parse.js';\n\nfunction parseRgb(color, parsed) {\n\tif (!parsed || (parsed[0] !== 'rgb' && parsed[0] !== 'rgba')) {\n\t\treturn undefined;\n\t}\n\tconst res = { mode: 'rgb' };\n\tconst [, r, g, b, alpha] = parsed;\n\tif (r.type === Tok.Hue || g.type === Tok.Hue || b.type === Tok.Hue) {\n\t\treturn undefined;\n\t}\n\tif (r.type !== Tok.None) {\n\t\tres.r = r.type === Tok.Number ? r.value / 255 : r.value / 100;\n\t}\n\tif (g.type !== Tok.None) {\n\t\tres.g = g.type === Tok.Number ? g.value / 255 : g.value / 100;\n\t}\n\tif (b.type !== Tok.None) {\n\t\tres.b = b.type === Tok.Number ? b.value / 255 : b.value / 100;\n\t}\n\tif (alpha.type !== Tok.None) {\n\t\tres.alpha = Math.min(\n\t\t\t1,\n\t\t\tMath.max(\n\t\t\t\t0,\n\t\t\t\talpha.type === Tok.Number ? alpha.value : alpha.value / 100\n\t\t\t)\n\t\t);\n\t}\n\n\treturn res;\n}\n\nexport default parseRgb;\n", "const parseTransparent = c =>\n\tc === 'transparent'\n\t\t? { mode: 'rgb', r: 0, g: 0, b: 0, alpha: 0 }\n\t\t: undefined;\n\nexport default parseTransparent;\n", "const lerp = (a, b, t) => a + t * (b - a);\nconst unlerp = (a, b, v) => (v - a) / (b - a);\n\nconst blerp = (a00, a01, a10, a11, tx, ty) => {\n\treturn lerp(lerp(a00, a01, tx), lerp(a10, a11, tx), ty);\n};\n\nconst trilerp = (\n\ta000,\n\ta010,\n\ta100,\n\ta110,\n\ta001,\n\ta011,\n\ta101,\n\ta111,\n\ttx,\n\tty,\n\ttz\n) => {\n\treturn lerp(\n\t\tblerp(a000, a010, a100, a110, tx, ty),\n\t\tblerp(a001, a011, a101, a111, tx, ty),\n\t\ttz\n\t);\n};\n\nexport { lerp, blerp, trilerp, unlerp };\n", "const get_classes = arr => {\n\tlet classes = [];\n\tfor (let i = 0; i < arr.length - 1; i++) {\n\t\tlet a = arr[i];\n\t\tlet b = arr[i + 1];\n\t\tif (a === undefined && b === undefined) {\n\t\t\tclasses.push(undefined);\n\t\t} else if (a !== undefined && b !== undefined) {\n\t\t\tclasses.push([a, b]);\n\t\t} else {\n\t\t\tclasses.push(a !== undefined ? [a, a] : [b, b]);\n\t\t}\n\t}\n\treturn classes;\n};\n\nconst interpolatorPiecewise = interpolator => arr => {\n\tlet classes = get_classes(arr);\n\treturn t => {\n\t\tlet cls = t * classes.length;\n\t\tlet idx = t >= 1 ? classes.length - 1 : Math.max(Math.floor(cls), 0);\n\t\tlet pair = classes[idx];\n\t\treturn pair === undefined\n\t\t\t? undefined\n\t\t\t: interpolator(pair[0], pair[1], cls - idx);\n\t};\n};\n\nexport { interpolatorPiecewise };\n", "import { lerp } from './lerp.js';\nimport { interpolatorPiecewise } from './piecewise.js';\n\nexport const interpolatorLinear = interpolatorPiecewise(lerp);\n", "const fixupAlpha = arr => {\n\tlet some_defined = false;\n\tlet res = arr.map(v => {\n\t\tif (v !== undefined) {\n\t\t\tsome_defined = true;\n\t\t\treturn v;\n\t\t}\n\t\treturn 1;\n\t});\n\treturn some_defined ? res : arr;\n};\n\nexport { fixupAlpha };\n", "import parseNamed from './parseNamed.js';\nimport parseHex from './parseHex.js';\nimport parseRgbLegacy from './parseRgbLegacy.js';\nimport parseRgb from './parseRgb.js';\nimport parseTransparent from './parseTransparent.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\n\n/*\n\tsRGB color space\n */\n\nconst definition = {\n\tmode: 'rgb',\n\tchannels: ['r', 'g', 'b', 'alpha'],\n\tparse: [\n\t\tparseRgb,\n\t\tparseHex,\n\t\tparseRgbLegacy,\n\t\tparseNamed,\n\t\tparseTransparent,\n\t\t'srgb'\n\t],\n\tserialize: 'srgb',\n\tinterpolate: {\n\t\tr: interpolatorLinear,\n\t\tg: interpolatorLinear,\n\t\tb: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t},\n\tgamut: true,\n\twhite: { r: 1, g: 1, b: 1 },\n\tblack: { r: 0, g: 0, b: 0 }\n};\n\nexport default definition;\n", "/*\n\tConvert A98 RGB values to CIE XYZ D65\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\t\t* https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf\n*/\n\nconst linearize = (v = 0) => Math.pow(Math.abs(v), 563 / 256) * Math.sign(v);\n\nconst convertA98ToXyz65 = a98 => {\n\tlet r = linearize(a98.r);\n\tlet g = linearize(a98.g);\n\tlet b = linearize(a98.b);\n\tlet res = {\n\t\tmode: 'xyz65',\n\t\tx:\n\t\t\t0.5766690429101305 * r +\n\t\t\t0.1855582379065463 * g +\n\t\t\t0.1882286462349947 * b,\n\t\ty:\n\t\t\t0.297344975250536 * r +\n\t\t\t0.6273635662554661 * g +\n\t\t\t0.0752914584939979 * b,\n\t\tz:\n\t\t\t0.0270313613864123 * r +\n\t\t\t0.0706888525358272 * g +\n\t\t\t0.9913375368376386 * b\n\t};\n\tif (a98.alpha !== undefined) {\n\t\tres.alpha = a98.alpha;\n\t}\n\treturn res;\n};\n\nexport default convertA98ToXyz65;\n", "/*\n\tConvert CIE XYZ D65 values to A98 RGB\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n*/\n\nconst gamma = v => Math.pow(Math.abs(v), 256 / 563) * Math.sign(v);\n\nconst convertXyz65ToA98 = ({ x, y, z, alpha }) => {\n\tif (x === undefined) x = 0;\n\tif (y === undefined) y = 0;\n\tif (z === undefined) z = 0;\n\tlet res = {\n\t\tmode: 'a98',\n\t\tr: gamma(\n\t\t\tx * 2.0415879038107465 -\n\t\t\t\ty * 0.5650069742788597 -\n\t\t\t\t0.3447313507783297 * z\n\t\t),\n\t\tg: gamma(\n\t\t\tx * -0.9692436362808798 +\n\t\t\t\ty * 1.8759675015077206 +\n\t\t\t\t0.0415550574071756 * z\n\t\t),\n\t\tb: gamma(\n\t\t\tx * 0.0134442806320312 -\n\t\t\t\ty * 0.1183623922310184 +\n\t\t\t\t1.0151749943912058 * z\n\t\t)\n\t};\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertXyz65ToA98;\n", "const fn = (c = 0) => {\n\tconst abs = Math.abs(c);\n\tif (abs <= 0.04045) {\n\t\treturn c / 12.92;\n\t}\n\treturn (Math.sign(c) || 1) * Math.pow((abs + 0.055) / 1.055, 2.4);\n};\n\nconst convertRgbToLrgb = ({ r, g, b, alpha }) => {\n\tlet res = {\n\t\tmode: 'lrgb',\n\t\tr: fn(r),\n\t\tg: fn(g),\n\t\tb: fn(b)\n\t};\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n};\n\nexport default convertRgbToLrgb;\n", "/*\n\tConvert sRGB values to CIE XYZ D65\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\t\t* https://observablehq.com/@danburzo/color-matrix-calculator\n*/\n\nimport convertRgbToLrgb from '../lrgb/convertRgbToLrgb.js';\n\nconst convertRgbToXyz65 = rgb => {\n\tlet { r, g, b, alpha } = convertRgbToLrgb(rgb);\n\tlet res = {\n\t\tmode: 'xyz65',\n\t\tx:\n\t\t\t0.4123907992659593 * r +\n\t\t\t0.357584339383878 * g +\n\t\t\t0.1804807884018343 * b,\n\t\ty:\n\t\t\t0.2126390058715102 * r +\n\t\t\t0.715168678767756 * g +\n\t\t\t0.0721923153607337 * b,\n\t\tz:\n\t\t\t0.0193308187155918 * r +\n\t\t\t0.119194779794626 * g +\n\t\t\t0.9505321522496607 * b\n\t};\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertRgbToXyz65;\n", "const fn = (c = 0) => {\n\tconst abs = Math.abs(c);\n\tif (abs > 0.0031308) {\n\t\treturn (Math.sign(c) || 1) * (1.055 * Math.pow(abs, 1 / 2.4) - 0.055);\n\t}\n\treturn c * 12.92;\n};\n\nconst convertLrgbToRgb = ({ r, g, b, alpha }, mode = 'rgb') => {\n\tlet res = {\n\t\tmode,\n\t\tr: fn(r),\n\t\tg: fn(g),\n\t\tb: fn(b)\n\t};\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n};\n\nexport default convertLrgbToRgb;\n", "/*\n\tCIE XYZ D65 values to sRGB.\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\t\t* https://observablehq.com/@danburzo/color-matrix-calculator\n*/\n\nimport convertLrgbToRgb from '../lrgb/convertLrgbToRgb.js';\n\nconst convertXyz65ToRgb = ({ x, y, z, alpha }) => {\n\tif (x === undefined) x = 0;\n\tif (y === undefined) y = 0;\n\tif (z === undefined) z = 0;\n\tlet res = convertLrgbToRgb({\n\t\tr:\n\t\t\tx * 3.2409699419045226 -\n\t\t\ty * 1.5373831775700939 -\n\t\t\t0.4986107602930034 * z,\n\t\tg:\n\t\t\tx * -0.9692436362808796 +\n\t\t\ty * 1.8759675015077204 +\n\t\t\t0.0415550574071756 * z,\n\t\tb:\n\t\t\tx * 0.0556300796969936 -\n\t\t\ty * 0.2039769588889765 +\n\t\t\t1.0569715142428784 * z\n\t});\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertXyz65ToRgb;\n", "import rgb from '../rgb/definition.js';\n\nimport convertA98ToXyz65 from './convertA98ToXyz65.js';\nimport convertXyz65ToA98 from './convertXyz65ToA98.js';\nimport convertRgbToXyz65 from '../xyz65/convertRgbToXyz65.js';\nimport convertXyz65ToRgb from '../xyz65/convertXyz65ToRgb.js';\n\nconst definition = {\n\t...rgb,\n\tmode: 'a98',\n\tparse: ['a98-rgb'],\n\tserialize: 'a98-rgb',\n\n\tfromMode: {\n\t\trgb: color => convertXyz65ToA98(convertRgbToXyz65(color)),\n\t\txyz65: convertXyz65ToA98\n\t},\n\n\ttoMode: {\n\t\trgb: color => convertXyz65ToRgb(convertA98ToXyz65(color)),\n\t\txyz65: convertA98ToXyz65\n\t}\n};\n\nexport default definition;\n", "const normalizeHue = hue => ((hue = hue % 360) < 0 ? hue + 360 : hue);\n\nexport default normalizeHue;\n", "import normalizeHue from '../util/normalizeHue.js';\n\nconst hue = (hues, fn) => {\n\treturn hues\n\t\t.map((hue, idx, arr) => {\n\t\t\tif (hue === undefined) {\n\t\t\t\treturn hue;\n\t\t\t}\n\t\t\tlet normalized = normalizeHue(hue);\n\t\t\tif (idx === 0 || hues[idx - 1] === undefined) {\n\t\t\t\treturn normalized;\n\t\t\t}\n\t\t\treturn fn(normalized - normalizeHue(arr[idx - 1]));\n\t\t})\n\t\t.reduce((acc, curr) => {\n\t\t\tif (\n\t\t\t\t!acc.length ||\n\t\t\t\tcurr === undefined ||\n\t\t\t\tacc[acc.length - 1] === undefined\n\t\t\t) {\n\t\t\t\tacc.push(curr);\n\t\t\t\treturn acc;\n\t\t\t}\n\t\t\tacc.push(curr + acc[acc.length - 1]);\n\t\t\treturn acc;\n\t\t}, []);\n};\n\nconst fixupHueShorter = arr =>\n\thue(arr, d => (Math.abs(d) <= 180 ? d : d - 360 * Math.sign(d)));\nconst fixupHueLonger = arr =>\n\thue(arr, d => (Math.abs(d) >= 180 || d === 0 ? d : d - 360 * Math.sign(d)));\nconst fixupHueIncreasing = arr => hue(arr, d => (d >= 0 ? d : d + 360));\nconst fixupHueDecreasing = arr => hue(arr, d => (d <= 0 ? d : d - 360));\n\nexport {\n\tfixupHueShorter,\n\tfixupHueLonger,\n\tfixupHueIncreasing,\n\tfixupHueDecreasing\n};\n", "export const M = [-0.14861, 1.78277, -0.29227, -0.90649, 1.97294, 0];\n\nexport const degToRad = Math.PI / 180;\nexport const radToDeg = 180 / Math.PI;\n", "/*\n\tConvert a RGB color to the Cubehelix HSL color space.\n\n\tThis computation is not present in <PERSON>'s paper:\n\thttps://arxiv.org/pdf/1108.5083.pdf\n\n\t...but can be derived from the inverse, HSL to RGB conversion.\n\n\tIt matches the math in <PERSON>'s D3 implementation:\n\n\thttps://github.com/d3/d3-color/blob/master/src/cubehelix.js\n */\n\nimport { radToDeg, M } from './constants.js';\n\nlet DE = M[3] * M[4];\nlet BE = M[1] * M[4];\nlet BCAD = M[1] * M[2] - M[0] * M[3];\n\nconst convertRgbToCubehelix = ({ r, g, b, alpha }) => {\n\tif (r === undefined) r = 0;\n\tif (g === undefined) g = 0;\n\tif (b === undefined) b = 0;\n\tlet l = (BCAD * b + r * DE - g * BE) / (BCAD + DE - BE);\n\tlet x = b - l;\n\tlet y = (M[4] * (g - l) - M[2] * x) / M[3];\n\n\tlet res = {\n\t\tmode: 'cubehelix',\n\t\tl: l,\n\t\ts:\n\t\t\tl === 0 || l === 1\n\t\t\t\t? undefined\n\t\t\t\t: Math.sqrt(x * x + y * y) / (M[4] * l * (1 - l))\n\t};\n\n\tif (res.s) res.h = Math.atan2(y, x) * radToDeg - 120;\n\tif (alpha !== undefined) res.alpha = alpha;\n\n\treturn res;\n};\n\nexport default convertRgbToCubehelix;\n", "import { degToRad, M } from './constants.js';\n\nconst convertCubehelixToRgb = ({ h, s, l, alpha }) => {\n\tlet res = { mode: 'rgb' };\n\n\th = (h === undefined ? 0 : h + 120) * degToRad;\n\tif (l === undefined) l = 0;\n\n\tlet amp = s === undefined ? 0 : s * l * (1 - l);\n\n\tlet cosh = Math.cos(h);\n\tlet sinh = Math.sin(h);\n\n\tres.r = l + amp * (M[0] * cosh + M[1] * sinh);\n\tres.g = l + amp * (M[2] * cosh + M[3] * sinh);\n\tres.b = l + amp * (M[4] * cosh + M[5] * sinh);\n\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n};\n\nexport default convertCubehelixToRgb;\n", "import { getMode } from './modes.js';\nimport converter from './converter.js';\nimport normalizeHue from './util/normalizeHue.js';\n\nconst differenceHueSaturation = (std, smp) => {\n\tif (std.h === undefined || smp.h === undefined || !std.s || !smp.s) {\n\t\treturn 0;\n\t}\n\tlet std_h = normalizeHue(std.h);\n\tlet smp_h = normalizeHue(smp.h);\n\tlet dH = Math.sin((((smp_h - std_h + 360) / 2) * Math.PI) / 180);\n\treturn 2 * Math.sqrt(std.s * smp.s) * dH;\n};\n\nconst differenceHueNaive = (std, smp) => {\n\tif (std.h === undefined || smp.h === undefined) {\n\t\treturn 0;\n\t}\n\tlet std_h = normalizeHue(std.h);\n\tlet smp_h = normalizeHue(smp.h);\n\tif (Math.abs(smp_h - std_h) > 180) {\n\t\t// todo should this be normalized once again?\n\t\treturn std_h - (smp_h - 360 * Math.sign(smp_h - std_h));\n\t}\n\treturn smp_h - std_h;\n};\n\nconst differenceHueChroma = (std, smp) => {\n\tif (std.h === undefined || smp.h === undefined || !std.c || !smp.c) {\n\t\treturn 0;\n\t}\n\tlet std_h = normalizeHue(std.h);\n\tlet smp_h = normalizeHue(smp.h);\n\tlet dH = Math.sin((((smp_h - std_h + 360) / 2) * Math.PI) / 180);\n\treturn 2 * Math.sqrt(std.c * smp.c) * dH;\n};\n\nconst differenceEuclidean = (mode = 'rgb', weights = [1, 1, 1, 0]) => {\n\tlet def = getMode(mode);\n\tlet channels = def.channels;\n\tlet diffs = def.difference;\n\tlet conv = converter(mode);\n\treturn (std, smp) => {\n\t\tlet ConvStd = conv(std);\n\t\tlet ConvSmp = conv(smp);\n\t\treturn Math.sqrt(\n\t\t\tchannels.reduce((sum, k, idx) => {\n\t\t\t\tlet delta = diffs[k]\n\t\t\t\t\t? diffs[k](ConvStd, ConvSmp)\n\t\t\t\t\t: ConvStd[k] - ConvSmp[k];\n\t\t\t\treturn (\n\t\t\t\t\tsum +\n\t\t\t\t\t(weights[idx] || 0) * Math.pow(isNaN(delta) ? 0 : delta, 2)\n\t\t\t\t);\n\t\t\t}, 0)\n\t\t);\n\t};\n};\n\nconst differenceCie76 = () => differenceEuclidean('lab65');\n\nconst differenceCie94 = (kL = 1, K1 = 0.045, K2 = 0.015) => {\n\tlet lab = converter('lab65');\n\n\treturn (std, smp) => {\n\t\tlet LabStd = lab(std);\n\t\tlet LabSmp = lab(smp);\n\n\t\t// Extract Lab values, and compute Chroma\n\t\tlet lStd = LabStd.l;\n\t\tlet aStd = LabStd.a;\n\t\tlet bStd = LabStd.b;\n\t\tlet cStd = Math.sqrt(aStd * aStd + bStd * bStd);\n\n\t\tlet lSmp = LabSmp.l;\n\t\tlet aSmp = LabSmp.a;\n\t\tlet bSmp = LabSmp.b;\n\t\tlet cSmp = Math.sqrt(aSmp * aSmp + bSmp * bSmp);\n\n\t\tlet dL2 = Math.pow(lStd - lSmp, 2);\n\t\tlet dC2 = Math.pow(cStd - cSmp, 2);\n\t\tlet dH2 = Math.pow(aStd - aSmp, 2) + Math.pow(bStd - bSmp, 2) - dC2;\n\n\t\treturn Math.sqrt(\n\t\t\tdL2 / Math.pow(kL, 2) +\n\t\t\t\tdC2 / Math.pow(1 + K1 * cStd, 2) +\n\t\t\t\tdH2 / Math.pow(1 + K2 * cStd, 2)\n\t\t);\n\t};\n};\n\n/*\n\tCIEDE2000 color difference, original Matlab implementation by Gaurav Sharma\n\tBased on \"The CIEDE2000 Color-Difference Formula: Implementation Notes, Supplementary Test Data, and Mathematical Observations\" \n\tby Gaurav Sharma, Wencheng Wu, Edul N. Dalal in Color Research and Application, vol. 30. No. 1, pp. 21-30, February 2005.\n\thttp://www2.ece.rochester.edu/~gsharma/ciede2000/\n */\n\nconst differenceCiede2000 = (Kl = 1, Kc = 1, Kh = 1) => {\n\tlet lab = converter('lab65');\n\treturn (std, smp) => {\n\t\tlet LabStd = lab(std);\n\t\tlet LabSmp = lab(smp);\n\n\t\tlet lStd = LabStd.l;\n\t\tlet aStd = LabStd.a;\n\t\tlet bStd = LabStd.b;\n\t\tlet cStd = Math.sqrt(aStd * aStd + bStd * bStd);\n\n\t\tlet lSmp = LabSmp.l;\n\t\tlet aSmp = LabSmp.a;\n\t\tlet bSmp = LabSmp.b;\n\t\tlet cSmp = Math.sqrt(aSmp * aSmp + bSmp * bSmp);\n\n\t\tlet cAvg = (cStd + cSmp) / 2;\n\n\t\tlet G =\n\t\t\t0.5 *\n\t\t\t(1 -\n\t\t\t\tMath.sqrt(\n\t\t\t\t\tMath.pow(cAvg, 7) / (Math.pow(cAvg, 7) + Math.pow(25, 7))\n\t\t\t\t));\n\n\t\tlet apStd = aStd * (1 + G);\n\t\tlet apSmp = aSmp * (1 + G);\n\n\t\tlet cpStd = Math.sqrt(apStd * apStd + bStd * bStd);\n\t\tlet cpSmp = Math.sqrt(apSmp * apSmp + bSmp * bSmp);\n\n\t\tlet hpStd =\n\t\t\tMath.abs(apStd) + Math.abs(bStd) === 0\n\t\t\t\t? 0\n\t\t\t\t: Math.atan2(bStd, apStd);\n\t\thpStd += (hpStd < 0) * 2 * Math.PI;\n\n\t\tlet hpSmp =\n\t\t\tMath.abs(apSmp) + Math.abs(bSmp) === 0\n\t\t\t\t? 0\n\t\t\t\t: Math.atan2(bSmp, apSmp);\n\t\thpSmp += (hpSmp < 0) * 2 * Math.PI;\n\n\t\tlet dL = lSmp - lStd;\n\t\tlet dC = cpSmp - cpStd;\n\n\t\tlet dhp = cpStd * cpSmp === 0 ? 0 : hpSmp - hpStd;\n\t\tdhp -= (dhp > Math.PI) * 2 * Math.PI;\n\t\tdhp += (dhp < -Math.PI) * 2 * Math.PI;\n\n\t\tlet dH = 2 * Math.sqrt(cpStd * cpSmp) * Math.sin(dhp / 2);\n\n\t\tlet Lp = (lStd + lSmp) / 2;\n\t\tlet Cp = (cpStd + cpSmp) / 2;\n\n\t\tlet hp;\n\t\tif (cpStd * cpSmp === 0) {\n\t\t\thp = hpStd + hpSmp;\n\t\t} else {\n\t\t\thp = (hpStd + hpSmp) / 2;\n\t\t\thp -= (Math.abs(hpStd - hpSmp) > Math.PI) * Math.PI;\n\t\t\thp += (hp < 0) * 2 * Math.PI;\n\t\t}\n\n\t\tlet Lpm50 = Math.pow(Lp - 50, 2);\n\t\tlet T =\n\t\t\t1 -\n\t\t\t0.17 * Math.cos(hp - Math.PI / 6) +\n\t\t\t0.24 * Math.cos(2 * hp) +\n\t\t\t0.32 * Math.cos(3 * hp + Math.PI / 30) -\n\t\t\t0.2 * Math.cos(4 * hp - (63 * Math.PI) / 180);\n\n\t\tlet Sl = 1 + (0.015 * Lpm50) / Math.sqrt(20 + Lpm50);\n\t\tlet Sc = 1 + 0.045 * Cp;\n\t\tlet Sh = 1 + 0.015 * Cp * T;\n\n\t\tlet deltaTheta =\n\t\t\t((30 * Math.PI) / 180) *\n\t\t\tMath.exp(-1 * Math.pow(((180 / Math.PI) * hp - 275) / 25, 2));\n\t\tlet Rc =\n\t\t\t2 *\n\t\t\tMath.sqrt(Math.pow(Cp, 7) / (Math.pow(Cp, 7) + Math.pow(25, 7)));\n\n\t\tlet Rt = -1 * Math.sin(2 * deltaTheta) * Rc;\n\n\t\treturn Math.sqrt(\n\t\t\tMath.pow(dL / (Kl * Sl), 2) +\n\t\t\t\tMath.pow(dC / (Kc * Sc), 2) +\n\t\t\t\tMath.pow(dH / (Kh * Sh), 2) +\n\t\t\t\t(((Rt * dC) / (Kc * Sc)) * dH) / (Kh * Sh)\n\t\t);\n\t};\n};\n\n/*\n\tCMC (l:c) difference formula\n\n\tReferences:\n\t\thttps://en.wikipedia.org/wiki/Color_difference#CMC_l:c_(1984)\n\t\thttp://www.brucelindbloom.com/index.html?Eqn_DeltaE_CMC.html\n */\nconst differenceCmc = (l = 1, c = 1) => {\n\tlet lab = converter('lab65');\n\n\t/*\n\t\tComparte two colors:\n\t\tstd - standard (first) color\n\t\tsmp - sample (second) color\n\t */\n\treturn (std, smp) => {\n\t\t// convert standard color to Lab\n\t\tlet LabStd = lab(std);\n\t\tlet lStd = LabStd.l;\n\t\tlet aStd = LabStd.a;\n\t\tlet bStd = LabStd.b;\n\n\t\t// Obtain hue/chroma\n\t\tlet cStd = Math.sqrt(aStd * aStd + bStd * bStd);\n\t\tlet hStd = Math.atan2(bStd, aStd);\n\t\thStd = hStd + 2 * Math.PI * (hStd < 0);\n\n\t\t// convert sample color to Lab, obtain LCh\n\t\tlet LabSmp = lab(smp);\n\t\tlet lSmp = LabSmp.l;\n\t\tlet aSmp = LabSmp.a;\n\t\tlet bSmp = LabSmp.b;\n\n\t\t// Obtain chroma\n\t\tlet cSmp = Math.sqrt(aSmp * aSmp + bSmp * bSmp);\n\n\t\t// lightness delta squared\n\t\tlet dL2 = Math.pow(lStd - lSmp, 2);\n\n\t\t// chroma delta squared\n\t\tlet dC2 = Math.pow(cStd - cSmp, 2);\n\n\t\t// hue delta squared\n\t\tlet dH2 = Math.pow(aStd - aSmp, 2) + Math.pow(bStd - bSmp, 2) - dC2;\n\n\t\tlet F = Math.sqrt(Math.pow(cStd, 4) / (Math.pow(cStd, 4) + 1900));\n\t\tlet T =\n\t\t\thStd >= (164 / 180) * Math.PI && hStd <= (345 / 180) * Math.PI\n\t\t\t\t? 0.56 + Math.abs(0.2 * Math.cos(hStd + (168 / 180) * Math.PI))\n\t\t\t\t: 0.36 + Math.abs(0.4 * Math.cos(hStd + (35 / 180) * Math.PI));\n\n\t\tlet Sl = lStd < 16 ? 0.511 : (0.040975 * lStd) / (1 + 0.01765 * lStd);\n\t\tlet Sc = (0.0638 * cStd) / (1 + 0.0131 * cStd) + 0.638;\n\t\tlet Sh = Sc * (F * T + 1 - F);\n\n\t\treturn Math.sqrt(\n\t\t\tdL2 / Math.pow(l * Sl, 2) +\n\t\t\t\tdC2 / Math.pow(c * Sc, 2) +\n\t\t\t\tdH2 / Math.pow(Sh, 2)\n\t\t);\n\t};\n};\n\n/*\n\n\tHyAB color difference formula, introduced in:\n\n\t\tAbasi S, Amani Tehran M, Fairchild MD. \n\t\t\"Distance metrics for very large color differences.\"\n\t\tColor Res Appl. 2019; 1–16. \n\t\thttps://doi.org/10.1002/col.22451\n\n\tPDF available at:\n\t\n\t\thttp://markfairchild.org/PDFs/PAP40.pdf\n */\nconst differenceHyab = () => {\n\tlet lab = converter('lab65');\n\treturn (std, smp) => {\n\t\tlet LabStd = lab(std);\n\t\tlet LabSmp = lab(smp);\n\t\tlet dL = LabStd.l - LabSmp.l;\n\t\tlet dA = LabStd.a - LabSmp.a;\n\t\tlet dB = LabStd.b - LabSmp.b;\n\t\treturn Math.abs(dL) + Math.sqrt(dA * dA + dB * dB);\n\t};\n};\n\n/*\n\t\"Measuring perceived color difference using YIQ NTSC\n\ttransmission color space in mobile applications\"\n\t\t\n\t\tby Yuriy Kotsarenko, Fernando Ramos in:\n\t\tProgramación Matemática y Software (2010) \n\n\tAvailable at:\n\t\t\n\t\thttp://www.progmat.uaem.mx:8080/artVol2Num2/Articulo3Vol2Num2.pdf\n */\nconst differenceKotsarenkoRamos = () =>\n\tdifferenceEuclidean('yiq', [0.5053, 0.299, 0.1957]);\n\n/*\n\tΔE_ITP, as defined in Rec. ITU-R BT.2124:\n\n\thttps://www.itu.int/rec/R-REC-BT.2124/en\n*/\nconst differenceItp = () =>\n\tdifferenceEuclidean('itp', [518400, 129600, 518400]);\n\nexport {\n\tdifferenceHueChroma,\n\tdifferenceHueSaturation,\n\tdifferenceHueNaive,\n\tdifferenceEuclidean,\n\tdifferenceCie76,\n\tdifferenceCie94,\n\tdifferenceCiede2000,\n\tdifferenceCmc,\n\tdifferenceHyab,\n\tdifferenceKotsarenkoRamos,\n\tdifferenceItp\n};\n", "import converter from './converter.js';\nimport { getMode } from './modes.js';\n\nconst averageAngle = val => {\n\t// See: https://en.wikipedia.org/wiki/Mean_of_circular_quantities\n\tlet sum = val.reduce(\n\t\t(sum, val) => {\n\t\t\tif (val !== undefined) {\n\t\t\t\tlet rad = (val * Math.PI) / 180;\n\t\t\t\tsum.sin += Math.sin(rad);\n\t\t\t\tsum.cos += Math.cos(rad);\n\t\t\t}\n\t\t\treturn sum;\n\t\t},\n\t\t{ sin: 0, cos: 0 }\n\t);\n\tlet angle = (Math.atan2(sum.sin, sum.cos) * 180) / Math.PI;\n\treturn angle < 0 ? 360 + angle : angle;\n};\n\nconst averageNumber = val => {\n\tlet a = val.filter(v => v !== undefined);\n\treturn a.length ? a.reduce((sum, v) => sum + v, 0) / a.length : undefined;\n};\n\nconst isfn = o => typeof o === 'function';\n\nfunction average(colors, mode = 'rgb', overrides) {\n\tlet def = getMode(mode);\n\tlet cc = colors.map(converter(mode));\n\treturn def.channels.reduce(\n\t\t(res, ch) => {\n\t\t\tlet arr = cc.map(c => c[ch]).filter(val => val !== undefined);\n\t\t\tif (arr.length) {\n\t\t\t\tlet fn;\n\t\t\t\tif (isfn(overrides)) {\n\t\t\t\t\tfn = overrides;\n\t\t\t\t} else if (overrides && isfn(overrides[ch])) {\n\t\t\t\t\tfn = overrides[ch];\n\t\t\t\t} else if (def.average && isfn(def.average[ch])) {\n\t\t\t\t\tfn = def.average[ch];\n\t\t\t\t} else {\n\t\t\t\t\tfn = averageNumber;\n\t\t\t\t}\n\t\t\t\tres[ch] = fn(arr, ch);\n\t\t\t}\n\t\t\treturn res;\n\t\t},\n\t\t{ mode }\n\t);\n}\n\nexport { average, averageAngle, averageNumber };\n", "/* \n\t<PERSON>'s Cubehelix\n\t----------------------\n\n\t<PERSON>, D. A., 2011, \"A colour scheme for the display of astronomical intensity images\", \n\tBulletin of the Astronomical Society of India, 39, 289. (2011BASI...39..289G at ADS.) \n\n\thttps://www.mrao.cam.ac.uk/%7Edag/CUBEHELIX/\n\thttps://arxiv.org/pdf/1108.5083.pdf\n\n\tAlthough Cubehelix was defined to be a method to obtain a colour scheme,\n\tit actually contains a definition of a colour space, as identified by \n\t<PERSON> and implemented in D3.js<PERSON>\n\n\t<PERSON>'s paper introduces the following terminology:\n\n\t* \ta `lightness` dimension in the interval [0, 1] \n\t\ton which we interpolate to obtain the colour scheme\n\t*\ta `start` colour that is analogous to a Hue in HSL space\n\t*\ta number of `rotations` around the Hue cylinder.\n\t*\ta `hue` parameter which should more appropriately be called `saturation`\n\t\n\tAs such, the original definition of the Cubehelix scheme is actually an\n\tinterpolation between two colors in the Cubehelix space:\n\n\tH: start \t\t\t\tH: start + 360 * rotations\n\tS: hue \t\t\t->\t\tS: hue\n\tL: 0\t\t\t\t\tL: 1\n\n\tWe can therefore extend the interpolation to any two colors in this space,\n\twith a variable Saturation and a Lightness interval other than the fixed 0 -> 1.\n*/\n\nimport { fixupHueShorter } from '../fixup/hue.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport convertRgbToCubehelix from './convertRgbToCubehelix.js';\nimport convertCubehelixToRgb from './convertCubehelixToRgb.js';\nimport { differenceHueSaturation } from '../difference.js';\nimport { averageAngle } from '../average.js';\n\nconst definition = {\n\tmode: 'cubehelix',\n\tchannels: ['h', 's', 'l', 'alpha'],\n\tparse: ['--cubehelix'],\n\tserialize: '--cubehelix',\n\n\tranges: {\n\t\th: [0, 360],\n\t\ts: [0, 4.614],\n\t\tl: [0, 1]\n\t},\n\n\tfromMode: {\n\t\trgb: convertRgbToCubehelix\n\t},\n\n\ttoMode: {\n\t\trgb: convertCubehelixToRgb\n\t},\n\n\tinterpolate: {\n\t\th: {\n\t\t\tuse: interpolatorLinear,\n\t\t\tfixup: fixupHueShorter\n\t\t},\n\t\ts: interpolatorLinear,\n\t\tl: interpolatorLinear,\n\t\talpha: {\n\t\t\tuse: interpolatorLinear,\n\t\t\tfixup: fixupAlpha\n\t\t}\n\t},\n\n\tdifference: {\n\t\th: differenceHueSaturation\n\t},\n\n\taverage: {\n\t\th: averageAngle\n\t}\n};\n\nexport default definition;\n", "import normalizeHue from '../util/normalizeHue.js';\n\n/* \n\tReferences: \n\t\t* https://drafts.csswg.org/css-color/#lab-to-lch\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n*/\nconst convertLabToLch = ({ l, a, b, alpha }, mode = 'lch') => {\n\tif (a === undefined) a = 0;\n\tif (b === undefined) b = 0;\n\tlet c = Math.sqrt(a * a + b * b);\n\tlet res = { mode, l, c };\n\tif (c) res.h = normalizeHue((Math.atan2(b, a) * 180) / Math.PI);\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n};\n\nexport default convertLabToLch;\n", "/* \n\tReferences: \n\t\t* https://drafts.csswg.org/css-color/#lch-to-lab\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n*/\nconst convertLchToLab = ({ l, c, h, alpha }, mode = 'lab') => {\n\tif (h === undefined) h = 0;\n\tlet res = {\n\t\tmode,\n\t\tl,\n\t\ta: c ? c * Math.cos((h / 180) * Math.PI) : 0,\n\t\tb: c ? c * Math.sin((h / 180) * Math.PI) : 0\n\t};\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n};\n\nexport default convertLchToLab;\n", "export const k = Math.pow(29, 3) / Math.pow(3, 3);\nexport const e = Math.pow(6, 3) / Math.pow(29, 3);\n", "/*\n\tThe XYZ tristimulus values (white point)\n\tof standard illuminants for the CIE 1931 2° \n\tstandard observer.\n\n\tSee: https://en.wikipedia.org/wiki/Standard_illuminant\n */\n\nexport const D50 = {\n\tX: 0.3457 / 0.3585,\n\tY: 1,\n\tZ: (1 - 0.3457 - 0.3585) / 0.3585\n};\n\nexport const D65 = {\n\tX: 0.3127 / 0.329,\n\tY: 1,\n\tZ: (1 - 0.3127 - 0.329) / 0.329\n};\n\nexport const k = Math.pow(29, 3) / Math.pow(3, 3);\nexport const e = Math.pow(6, 3) / Math.pow(29, 3);\n", "import { k, e } from '../xyz65/constants.js';\nimport { D65 } from '../constants.js';\n\nlet fn = v => (Math.pow(v, 3) > e ? Math.pow(v, 3) : (116 * v - 16) / k);\n\nconst convertLab65ToXyz65 = ({ l, a, b, alpha }) => {\n\tif (l === undefined) l = 0;\n\tif (a === undefined) a = 0;\n\tif (b === undefined) b = 0;\n\n\tlet fy = (l + 16) / 116;\n\tlet fx = a / 500 + fy;\n\tlet fz = fy - b / 200;\n\n\tlet res = {\n\t\tmode: 'xyz65',\n\t\tx: fn(fx) * D65.X,\n\t\ty: fn(fy) * D65.Y,\n\t\tz: fn(fz) * D65.Z\n\t};\n\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\n\treturn res;\n};\n\nexport default convertLab65ToXyz65;\n", "import convertLab65ToXyz65 from './convertLab65ToXyz65.js';\nimport convertXyz65ToRgb from '../xyz65/convertXyz65ToRgb.js';\n\nconst convertLab65ToRgb = lab => convertXyz65ToRgb(convertLab65ToXyz65(lab));\n\nexport default convertLab65ToRgb;\n", "import { k, e } from '../xyz65/constants.js';\nimport { D65 } from '../constants.js';\n\nconst f = value => (value > e ? Math.cbrt(value) : (k * value + 16) / 116);\n\nconst convertXyz65ToLab65 = ({ x, y, z, alpha }) => {\n\tif (x === undefined) x = 0;\n\tif (y === undefined) y = 0;\n\tif (z === undefined) z = 0;\n\tlet f0 = f(x / D65.X);\n\tlet f1 = f(y / D65.Y);\n\tlet f2 = f(z / D65.Z);\n\n\tlet res = {\n\t\tmode: 'lab65',\n\t\tl: 116 * f1 - 16,\n\t\ta: 500 * (f0 - f1),\n\t\tb: 200 * (f1 - f2)\n\t};\n\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\n\treturn res;\n};\n\nexport default convertXyz65ToLab65;\n", "import convertRgbToXyz65 from '../xyz65/convertRgbToXyz65.js';\nimport convertXyz65ToLab65 from './convertXyz65ToLab65.js';\n\nconst convertRgbToLab65 = rgb => {\n\tlet res = convertXyz65ToLab65(convertRgbToXyz65(rgb));\n\n\t// Fixes achromatic RGB colors having a _slight_ chroma due to floating-point errors\n\t// and approximated computations in sRGB <-> CIELab.\n\t// See: https://github.com/d3/d3-color/pull/46\n\tif (rgb.r === rgb.b && rgb.b === rgb.g) {\n\t\tres.a = res.b = 0;\n\t}\n\treturn res;\n};\n\nexport default convertRgbToLab65;\n", "export const kE = 1;\nexport const kCH = 1;\nexport const θ = (26 / 180) * Math.PI;\nexport const cosθ = Math.cos(θ);\nexport const sinθ = Math.sin(θ);\nexport const factor = 100 / Math.log(139 / 100); // ~ 303.67\n", "import { kCH, kE, sinθ, cosθ, θ, factor } from './constants.js';\n\n/*\n\tConvert DIN99o LCh to CIELab D65\n\t--------------------------------\n */\n\nconst convertDlchToLab65 = ({ l, c, h, alpha }) => {\n\tif (l === undefined) l = 0;\n\tif (c === undefined) c = 0;\n\tif (h === undefined) h = 0;\n\tlet res = {\n\t\tmode: 'lab65',\n\t\tl: (Math.exp((l * kE) / factor) - 1) / 0.0039\n\t};\n\n\tlet G = (Math.exp(0.0435 * c * kCH * kE) - 1) / 0.075;\n\tlet e = G * Math.cos((h / 180) * Math.PI - θ);\n\tlet f = G * Math.sin((h / 180) * Math.PI - θ);\n\tres.a = e * cosθ - (f / 0.83) * sinθ;\n\tres.b = e * sinθ + (f / 0.83) * cosθ;\n\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n};\n\nexport default convertDlchToLab65;\n", "import { kCH, kE, sinθ, cosθ, θ, factor } from './constants.js';\nimport normalizeHue from '../util/normalizeHue.js';\n\n/*\n\tConvert CIELab D65 to DIN99o LCh\n\t================================\n */\n\nconst convertLab65ToDlch = ({ l, a, b, alpha }) => {\n\tif (l === undefined) l = 0;\n\tif (a === undefined) a = 0;\n\tif (b === undefined) b = 0;\n\tlet e = a * cosθ + b * sinθ;\n\tlet f = 0.83 * (b * cosθ - a * sinθ);\n\tlet G = Math.sqrt(e * e + f * f);\n\tlet res = {\n\t\tmode: 'dlch',\n\t\tl: (factor / kE) * Math.log(1 + 0.0039 * l),\n\t\tc: Math.log(1 + 0.075 * G) / (0.0435 * kCH * kE)\n\t};\n\n\tif (res.c) {\n\t\tres.h = normalizeHue(((Math.atan2(f, e) + θ) / Math.PI) * 180);\n\t}\n\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n};\n\nexport default convertLab65ToDlch;\n", "import convertLabToLch from '../lch/convertLabToLch.js';\nimport convertLchToLab from '../lch/convertLchToLab.js';\nimport convertLab65ToRgb from '../lab65/convertLab65ToRgb.js';\nimport convertRgbToLab65 from '../lab65/convertRgbToLab65.js';\nimport convertDlchToLab65 from '../dlch/convertDlchToLab65.js';\nimport convertLab65ToDlch from '../dlch/convertLab65ToDlch.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\n\nconst convertDlabToLab65 = c => convertDlchToLab65(convertLabToLch(c, 'dlch'));\nconst convertLab65ToDlab = c => convertLchToLab(convertLab65ToDlch(c), 'dlab');\n\nconst definition = {\n\tmode: 'dlab',\n\n\tparse: ['--din99o-lab'],\n\tserialize: '--din99o-lab',\n\n\ttoMode: {\n\t\tlab65: convertDlabToLab65,\n\t\trgb: c => convertLab65ToRgb(convertDlabToLab65(c))\n\t},\n\n\tfromMode: {\n\t\tlab65: convertLab65ToDlab,\n\t\trgb: c => convertLab65ToDlab(convertRgbToLab65(c))\n\t},\n\n\tchannels: ['l', 'a', 'b', 'alpha'],\n\n\tranges: {\n\t\tl: [0, 100],\n\t\ta: [-40.09, 45.501],\n\t\tb: [-40.469, 44.344]\n\t},\n\n\tinterpolate: {\n\t\tl: interpolatorLinear,\n\t\ta: interpolatorLinear,\n\t\tb: interpolatorLinear,\n\t\talpha: {\n\t\t\tuse: interpolatorLinear,\n\t\t\tfixup: fixupAlpha\n\t\t}\n\t}\n};\n\nexport default definition;\n", "import convertLabToLch from '../lch/convertLabToLch.js';\nimport convertLchToLab from '../lch/convertLchToLab.js';\nimport convertDlchToLab65 from './convertDlchToLab65.js';\nimport convertLab65ToDlch from './convertLab65ToDlch.js';\nimport convertLab65ToRgb from '../lab65/convertLab65ToRgb.js';\nimport convertRgbToLab65 from '../lab65/convertRgbToLab65.js';\n\nimport { fixupHueShorter } from '../fixup/hue.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { differenceHueChroma } from '../difference.js';\nimport { averageAngle } from '../average.js';\n\nconst definition = {\n\tmode: 'dlch',\n\n\tparse: ['--din99o-lch'],\n\tserialize: '--din99o-lch',\n\n\ttoMode: {\n\t\tlab65: convertDlchToLab65,\n\t\tdlab: c => convertLchToLab(c, 'dlab'),\n\t\trgb: c => convertLab65ToRgb(convertDlchToLab65(c))\n\t},\n\n\tfromMode: {\n\t\tlab65: convertLab65ToDlch,\n\t\tdlab: c => convertLabToLch(c, 'dlch'),\n\t\trgb: c => convertLab65ToDlch(convertRgbToLab65(c))\n\t},\n\n\tchannels: ['l', 'c', 'h', 'alpha'],\n\n\tranges: {\n\t\tl: [0, 100],\n\t\tc: [0, 51.484],\n\t\th: [0, 360]\n\t},\n\n\tinterpolate: {\n\t\tl: interpolatorLinear,\n\t\tc: interpolatorLinear,\n\t\th: {\n\t\t\tuse: interpolatorLinear,\n\t\t\tfixup: fixupHueShorter\n\t\t},\n\t\talpha: {\n\t\t\tuse: interpolatorLinear,\n\t\t\tfixup: fixupAlpha\n\t\t}\n\t},\n\n\tdifference: {\n\t\th: differenceHueChroma\n\t},\n\n\taverage: {\n\t\th: averageAngle\n\t}\n};\n\nexport default definition;\n", "import normalizeHue from '../util/normalizeHue.js';\n\n// Based on: https://en.wikipedia.org/wiki/HSL_and_HSV#Converting_to_RGB\n\nexport default function convertHsiToRgb({ h, s, i, alpha }) {\n\th = normalizeHue(h !== undefined ? h : 0);\n\tif (s === undefined) s = 0;\n\tif (i === undefined) i = 0;\n\tlet f = Math.abs(((h / 60) % 2) - 1);\n\tlet res;\n\tswitch (Math.floor(h / 60)) {\n\t\tcase 0:\n\t\t\tres = {\n\t\t\t\tr: i * (1 + s * (3 / (2 - f) - 1)),\n\t\t\t\tg: i * (1 + s * ((3 * (1 - f)) / (2 - f) - 1)),\n\t\t\t\tb: i * (1 - s)\n\t\t\t};\n\t\t\tbreak;\n\t\tcase 1:\n\t\t\tres = {\n\t\t\t\tr: i * (1 + s * ((3 * (1 - f)) / (2 - f) - 1)),\n\t\t\t\tg: i * (1 + s * (3 / (2 - f) - 1)),\n\t\t\t\tb: i * (1 - s)\n\t\t\t};\n\t\t\tbreak;\n\t\tcase 2:\n\t\t\tres = {\n\t\t\t\tr: i * (1 - s),\n\t\t\t\tg: i * (1 + s * (3 / (2 - f) - 1)),\n\t\t\t\tb: i * (1 + s * ((3 * (1 - f)) / (2 - f) - 1))\n\t\t\t};\n\t\t\tbreak;\n\t\tcase 3:\n\t\t\tres = {\n\t\t\t\tr: i * (1 - s),\n\t\t\t\tg: i * (1 + s * ((3 * (1 - f)) / (2 - f) - 1)),\n\t\t\t\tb: i * (1 + s * (3 / (2 - f) - 1))\n\t\t\t};\n\t\t\tbreak;\n\t\tcase 4:\n\t\t\tres = {\n\t\t\t\tr: i * (1 + s * ((3 * (1 - f)) / (2 - f) - 1)),\n\t\t\t\tg: i * (1 - s),\n\t\t\t\tb: i * (1 + s * (3 / (2 - f) - 1))\n\t\t\t};\n\t\t\tbreak;\n\t\tcase 5:\n\t\t\tres = {\n\t\t\t\tr: i * (1 + s * (3 / (2 - f) - 1)),\n\t\t\t\tg: i * (1 - s),\n\t\t\t\tb: i * (1 + s * ((3 * (1 - f)) / (2 - f) - 1))\n\t\t\t};\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tres = { r: i * (1 - s), g: i * (1 - s), b: i * (1 - s) };\n\t}\n\n\tres.mode = 'rgb';\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n}\n", "// Based on: https://en.wikipedia.org/wiki/HSL_and_HSV#Formal_derivation\n\nexport default function convertRgbToHsi({ r, g, b, alpha }) {\n\tif (r === undefined) r = 0;\n\tif (g === undefined) g = 0;\n\tif (b === undefined) b = 0;\n\tlet M = Math.max(r, g, b),\n\t\tm = Math.min(r, g, b);\n\tlet res = {\n\t\tmode: 'hsi',\n\t\ts: r + g + b === 0 ? 0 : 1 - (3 * m) / (r + g + b),\n\t\ti: (r + g + b) / 3\n\t};\n\tif (M - m !== 0)\n\t\tres.h =\n\t\t\t(M === r\n\t\t\t\t? (g - b) / (M - m) + (g < b) * 6\n\t\t\t\t: M === g\n\t\t\t\t? (b - r) / (M - m) + 2\n\t\t\t\t: (r - g) / (M - m) + 4) * 60;\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n}\n", "import convertHsiToRgb from './convertHsiToRgb.js';\nimport convertRgbToHsi from './convertRgbToHsi.js';\nimport { fixupHueShorter } from '../fixup/hue.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { differenceHueSaturation } from '../difference.js';\nimport { averageAngle } from '../average.js';\n\nconst definition = {\n\tmode: 'hsi',\n\n\ttoMode: {\n\t\trgb: convertHsiToRgb\n\t},\n\n\tparse: ['--hsi'],\n\tserialize: '--hsi',\n\n\tfromMode: {\n\t\trgb: convertRgbToHsi\n\t},\n\n\tchannels: ['h', 's', 'i', 'alpha'],\n\n\tranges: {\n\t\th: [0, 360]\n\t},\n\n\tgamut: 'rgb',\n\n\tinterpolate: {\n\t\th: { use: interpolatorLinear, fixup: fixupHueShorter },\n\t\ts: interpolatorLinear,\n\t\ti: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t},\n\n\tdifference: {\n\t\th: differenceHueSaturation\n\t},\n\n\taverage: {\n\t\th: averageAngle\n\t}\n};\n\nexport default definition;\n", "import normalizeHue from '../util/normalizeHue.js';\n// Based on: https://en.wikipedia.org/wiki/HSL_and_HSV#Converting_to_RGB\n\nexport default function convertHslToRgb({ h, s, l, alpha }) {\n\th = normalizeHue(h !== undefined ? h : 0);\n\tif (s === undefined) s = 0;\n\tif (l === undefined) l = 0;\n\tlet m1 = l + s * (l < 0.5 ? l : 1 - l);\n\tlet m2 = m1 - (m1 - l) * 2 * Math.abs(((h / 60) % 2) - 1);\n\tlet res;\n\tswitch (Math.floor(h / 60)) {\n\t\tcase 0:\n\t\t\tres = { r: m1, g: m2, b: 2 * l - m1 };\n\t\t\tbreak;\n\t\tcase 1:\n\t\t\tres = { r: m2, g: m1, b: 2 * l - m1 };\n\t\t\tbreak;\n\t\tcase 2:\n\t\t\tres = { r: 2 * l - m1, g: m1, b: m2 };\n\t\t\tbreak;\n\t\tcase 3:\n\t\t\tres = { r: 2 * l - m1, g: m2, b: m1 };\n\t\t\tbreak;\n\t\tcase 4:\n\t\t\tres = { r: m2, g: 2 * l - m1, b: m1 };\n\t\t\tbreak;\n\t\tcase 5:\n\t\t\tres = { r: m1, g: 2 * l - m1, b: m2 };\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tres = { r: 2 * l - m1, g: 2 * l - m1, b: 2 * l - m1 };\n\t}\n\tres.mode = 'rgb';\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n}\n", "// Based on: https://en.wikipedia.org/wiki/HSL_and_HSV#Formal_derivation\n\nexport default function convertRgbToHsl({ r, g, b, alpha }) {\n\tif (r === undefined) r = 0;\n\tif (g === undefined) g = 0;\n\tif (b === undefined) b = 0;\n\tlet M = Math.max(r, g, b),\n\t\tm = Math.min(r, g, b);\n\tlet res = {\n\t\tmode: 'hsl',\n\t\ts: M === m ? 0 : (M - m) / (1 - Math.abs(M + m - 1)),\n\t\tl: 0.5 * (M + m)\n\t};\n\tif (M - m !== 0)\n\t\tres.h =\n\t\t\t(M === r\n\t\t\t\t? (g - b) / (M - m) + (g < b) * 6\n\t\t\t\t: M === g\n\t\t\t\t? (b - r) / (M - m) + 2\n\t\t\t\t: (r - g) / (M - m) + 4) * 60;\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n}\n", "const hueToDeg = (val, unit) => {\n\tswitch (unit) {\n\t\tcase 'deg':\n\t\t\treturn +val;\n\t\tcase 'rad':\n\t\t\treturn (val / Math.PI) * 180;\n\t\tcase 'grad':\n\t\t\treturn (val / 10) * 9;\n\t\tcase 'turn':\n\t\t\treturn val * 360;\n\t}\n};\n\nexport default hueToDeg;\n", "import hueToDeg from '../util/hue.js';\nimport { hue, per, num_per, c } from '../util/regex.js';\n\n/*\n\thsl() regular expressions for legacy format\n\tReference: https://drafts.csswg.org/css-color/#the-hsl-notation\n */\nconst hsl_old = new RegExp(\n\t`^hsla?\\\\(\\\\s*${hue}${c}${per}${c}${per}\\\\s*(?:,\\\\s*${num_per}\\\\s*)?\\\\)$`\n);\n\nconst parseHslLegacy = color => {\n\tlet match = color.match(hsl_old);\n\tif (!match) return;\n\tlet res = { mode: 'hsl' };\n\n\tif (match[3] !== undefined) {\n\t\tres.h = +match[3];\n\t} else if (match[1] !== undefined && match[2] !== undefined) {\n\t\tres.h = hueToDeg(match[1], match[2]);\n\t}\n\n\tif (match[4] !== undefined) {\n\t\tres.s = Math.min(Math.max(0, match[4] / 100), 1);\n\t}\n\n\tif (match[5] !== undefined) {\n\t\tres.l = Math.min(Math.max(0, match[5] / 100), 1);\n\t}\n\n\tif (match[6] !== undefined) {\n\t\tres.alpha = Math.max(0, Math.min(1, match[6] / 100));\n\t} else if (match[7] !== undefined) {\n\t\tres.alpha = Math.max(0, Math.min(1, +match[7]));\n\t}\n\treturn res;\n};\n\nexport default parseHslLegacy;\n", "import { Tok } from '../parse.js';\n\nfunction parseHsl(color, parsed) {\n\tif (!parsed || (parsed[0] !== 'hsl' && parsed[0] !== 'hsla')) {\n\t\treturn undefined;\n\t}\n\tconst res = { mode: 'hsl' };\n\tconst [, h, s, l, alpha] = parsed;\n\n\tif (h.type !== Tok.None) {\n\t\tif (h.type === Tok.Percentage) {\n\t\t\treturn undefined;\n\t\t}\n\t\tres.h = h.value;\n\t}\n\n\tif (s.type !== Tok.None) {\n\t\tif (s.type === Tok.Hue) {\n\t\t\treturn undefined;\n\t\t}\n\t\tres.s = s.value / 100;\n\t}\n\n\tif (l.type !== Tok.None) {\n\t\tif (l.type === Tok.Hue) {\n\t\t\treturn undefined;\n\t\t}\n\t\tres.l = l.value / 100;\n\t}\n\n\tif (alpha.type !== Tok.None) {\n\t\tres.alpha = Math.min(\n\t\t\t1,\n\t\t\tMath.max(\n\t\t\t\t0,\n\t\t\t\talpha.type === Tok.Number ? alpha.value : alpha.value / 100\n\t\t\t)\n\t\t);\n\t}\n\n\treturn res;\n}\n\nexport default parseHsl;\n", "import convertHslToRgb from './convertHslToRgb.js';\nimport convertRgbToHsl from './convertRgbToHsl.js';\nimport parseHslLegacy from './parseHslLegacy.js';\nimport parseHsl from './parseHsl.js';\nimport { fixupHueShorter } from '../fixup/hue.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { differenceHueSaturation } from '../difference.js';\nimport { averageAngle } from '../average.js';\n\nconst definition = {\n\tmode: 'hsl',\n\n\ttoMode: {\n\t\trgb: convertHslToRgb\n\t},\n\n\tfromMode: {\n\t\trgb: convertRgbToHsl\n\t},\n\n\tchannels: ['h', 's', 'l', 'alpha'],\n\n\tranges: {\n\t\th: [0, 360]\n\t},\n\n\tgamut: 'rgb',\n\n\tparse: [parseHsl, parseHslLegacy],\n\tserialize: c =>\n\t\t`hsl(${c.h !== undefined ? c.h : 'none'} ${\n\t\t\tc.s !== undefined ? c.s * 100 + '%' : 'none'\n\t\t} ${c.l !== undefined ? c.l * 100 + '%' : 'none'}${\n\t\t\tc.alpha < 1 ? ` / ${c.alpha}` : ''\n\t\t})`,\n\n\tinterpolate: {\n\t\th: { use: interpolatorLinear, fixup: fixupHueShorter },\n\t\ts: interpolatorLinear,\n\t\tl: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t},\n\n\tdifference: {\n\t\th: differenceHueSaturation\n\t},\n\n\taverage: {\n\t\th: averageAngle\n\t}\n};\n\nexport default definition;\n", "import normalizeHue from '../util/normalizeHue.js';\n\n// Based on: https://en.wikipedia.org/wiki/HSL_and_HSV#Converting_to_RGB\n\nexport default function convertHsvToRgb({ h, s, v, alpha }) {\n\th = normalizeHue(h !== undefined ? h : 0);\n\tif (s === undefined) s = 0;\n\tif (v === undefined) v = 0;\n\tlet f = Math.abs(((h / 60) % 2) - 1);\n\tlet res;\n\tswitch (Math.floor(h / 60)) {\n\t\tcase 0:\n\t\t\tres = { r: v, g: v * (1 - s * f), b: v * (1 - s) };\n\t\t\tbreak;\n\t\tcase 1:\n\t\t\tres = { r: v * (1 - s * f), g: v, b: v * (1 - s) };\n\t\t\tbreak;\n\t\tcase 2:\n\t\t\tres = { r: v * (1 - s), g: v, b: v * (1 - s * f) };\n\t\t\tbreak;\n\t\tcase 3:\n\t\t\tres = { r: v * (1 - s), g: v * (1 - s * f), b: v };\n\t\t\tbreak;\n\t\tcase 4:\n\t\t\tres = { r: v * (1 - s * f), g: v * (1 - s), b: v };\n\t\t\tbreak;\n\t\tcase 5:\n\t\t\tres = { r: v, g: v * (1 - s), b: v * (1 - s * f) };\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tres = { r: v * (1 - s), g: v * (1 - s), b: v * (1 - s) };\n\t}\n\tres.mode = 'rgb';\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n}\n", "// Based on: https://en.wikipedia.org/wiki/HSL_and_HSV#Formal_derivation\n\nexport default function convertRgbToHsv({ r, g, b, alpha }) {\n\tif (r === undefined) r = 0;\n\tif (g === undefined) g = 0;\n\tif (b === undefined) b = 0;\n\tlet M = Math.max(r, g, b),\n\t\tm = Math.min(r, g, b);\n\tlet res = {\n\t\tmode: 'hsv',\n\t\ts: M === 0 ? 0 : 1 - m / M,\n\t\tv: M\n\t};\n\tif (M - m !== 0)\n\t\tres.h =\n\t\t\t(M === r\n\t\t\t\t? (g - b) / (M - m) + (g < b) * 6\n\t\t\t\t: M === g\n\t\t\t\t? (b - r) / (M - m) + 2\n\t\t\t\t: (r - g) / (M - m) + 4) * 60;\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n}\n", "import convertHsvToRgb from './convertHsvToRgb.js';\nimport convertRgbToHsv from './convertRgbToHsv.js';\nimport { fixupHueShorter } from '../fixup/hue.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { differenceHueSaturation } from '../difference.js';\nimport { averageAngle } from '../average.js';\n\nconst definition = {\n\tmode: 'hsv',\n\n\ttoMode: {\n\t\trgb: convertHsvToRgb\n\t},\n\n\tparse: ['--hsv'],\n\tserialize: '--hsv',\n\n\tfromMode: {\n\t\trgb: convertRgbToHsv\n\t},\n\n\tchannels: ['h', 's', 'v', 'alpha'],\n\n\tranges: {\n\t\th: [0, 360]\n\t},\n\n\tgamut: 'rgb',\n\n\tinterpolate: {\n\t\th: { use: interpolatorLinear, fixup: fixupHueShorter },\n\t\ts: interpolatorLinear,\n\t\tv: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t},\n\n\tdifference: {\n\t\th: differenceHueSaturation\n\t},\n\n\taverage: {\n\t\th: averageAngle\n\t}\n};\n\nexport default definition;\n", "/*\n\tHWB to RGB converter\n\t--------------------\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#hwb-to-rgb\n\t\t* https://en.wikipedia.org/wiki/HWB_color_model\n\t\t* http://alvyray.com/Papers/CG/HWB_JGTv208.pdf\n */\n\nimport convertHsvToRgb from '../hsv/convertHsvToRgb.js';\n\nexport default function convertHwbToRgb({ h, w, b, alpha }) {\n\tif (w === undefined) w = 0;\n\tif (b === undefined) b = 0;\n\t// normalize w + b to 1\n\tif (w + b > 1) {\n\t\tlet s = w + b;\n\t\tw /= s;\n\t\tb /= s;\n\t}\n\treturn convertHsvToRgb({\n\t\th: h,\n\t\ts: b === 1 ? 1 : 1 - w / (1 - b),\n\t\tv: 1 - b,\n\t\talpha: alpha\n\t});\n}\n", "/*\n\tRGB to HWB converter\n\t--------------------\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#hwb-to-rgb\n\t\t* https://en.wikipedia.org/wiki/HWB_color_model\n\t\t* http://alvyray.com/Papers/CG/HWB_JGTv208.pdf\n */\n\nimport convertRgbToHsv from '../hsv/convertRgbToHsv.js';\n\nexport default function convertRgbToHwb(rgba) {\n\tlet hsv = convertRgbToHsv(rgba);\n\tif (hsv === undefined) return undefined;\n\tlet s = hsv.s !== undefined ? hsv.s : 0;\n\tlet v = hsv.v !== undefined ? hsv.v : 0;\n\tlet res = {\n\t\tmode: 'hwb',\n\t\tw: (1 - s) * v,\n\t\tb: 1 - v\n\t};\n\tif (hsv.h !== undefined) res.h = hsv.h;\n\tif (hsv.alpha !== undefined) res.alpha = hsv.alpha;\n\treturn res;\n}\n", "import { Tok } from '../parse.js';\n\nfunction ParseHwb(color, parsed) {\n\tif (!parsed || parsed[0] !== 'hwb') {\n\t\treturn undefined;\n\t}\n\tconst res = { mode: 'hwb' };\n\tconst [, h, w, b, alpha] = parsed;\n\n\tif (h.type !== Tok.None) {\n\t\tif (h.type === Tok.Percentage) {\n\t\t\treturn undefined;\n\t\t}\n\t\tres.h = h.value;\n\t}\n\n\tif (w.type !== Tok.None) {\n\t\tif (w.type === Tok.Hue) {\n\t\t\treturn undefined;\n\t\t}\n\t\tres.w = w.value / 100;\n\t}\n\n\tif (b.type !== Tok.None) {\n\t\tif (b.type === Tok.Hue) {\n\t\t\treturn undefined;\n\t\t}\n\t\tres.b = b.value / 100;\n\t}\n\n\tif (alpha.type !== Tok.None) {\n\t\tres.alpha = Math.min(\n\t\t\t1,\n\t\t\tMath.max(\n\t\t\t\t0,\n\t\t\t\talpha.type === Tok.Number ? alpha.value : alpha.value / 100\n\t\t\t)\n\t\t);\n\t}\n\n\treturn res;\n}\n\nexport default ParseHwb;\n", "import convertHwbToRgb from './convertHwbToRgb.js';\nimport convertRgbToHwb from './convertRgbToHwb.js';\nimport parseHwb from './parseHwb.js';\nimport { fixupHueShorter } from '../fixup/hue.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { differenceHueNaive } from '../difference.js';\nimport { averageAngle } from '../average.js';\n\nconst definition = {\n\tmode: 'hwb',\n\n\ttoMode: {\n\t\trgb: convertHwbToRgb\n\t},\n\n\tfromMode: {\n\t\trgb: convertRgbToHwb\n\t},\n\n\tchannels: ['h', 'w', 'b', 'alpha'],\n\n\tranges: {\n\t\th: [0, 360]\n\t},\n\n\tgamut: 'rgb',\n\n\tparse: [parseHwb],\n\tserialize: c =>\n\t\t`hwb(${c.h !== undefined ? c.h : 'none'} ${\n\t\t\tc.w !== undefined ? c.w * 100 + '%' : 'none'\n\t\t} ${c.b !== undefined ? c.b * 100 + '%' : 'none'}${\n\t\t\tc.alpha < 1 ? ` / ${c.alpha}` : ''\n\t\t})`,\n\n\tinterpolate: {\n\t\th: { use: interpolatorLinear, fixup: fixupHueShorter },\n\t\tw: interpolatorLinear,\n\t\tb: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t},\n\n\tdifference: {\n\t\th: differenceHueNaive\n\t},\n\n\taverage: {\n\t\th: averageAngle\n\t}\n};\n\nexport default definition;\n", "/*\n\tRelative XYZ has Y=1 for media white,\n\tBT.2048 says media white Y=203 (at PQ 58).\n\tSee: https://www.itu.int/dms_pub/itu-r/opb/rep/R-REP-BT.2408-3-2019-PDF-E.pdf\n*/\nexport const YW = 203;\n", "/*\n\thttps://en.wikipedia.org/wiki/Transfer_functions_in_imaging\n*/\n\nexport const M1 = 0.1593017578125;\nexport const M2 = 78.84375;\nexport const C1 = 0.8359375;\nexport const C2 = 18.8515625;\nexport const C3 = 18.6875;\n\n/*\n\tPerceptual Quantizer, as defined in Rec. BT 2100-2 (2018)\n\n\t* https://www.itu.int/rec/R-REC-BT.2100-2-201807-I/en\n\t* https://en.wikipedia.org/wiki/Perceptual_quantizer\n*/\n\n/* PQ EOTF, defined for `v` in [0,1]. */\nexport function transferPqDecode(v) {\n\tif (v < 0) return 0;\n\tconst c = Math.pow(v, 1 / M2);\n\treturn 1e4 * Math.pow(Math.max(0, c - C1) / (C2 - C3 * c), 1 / M1);\n}\n\n/* PQ EOTF^-1, defined for `v` in [0, 1e4]. */\nexport function transferPqEncode(v) {\n\tif (v < 0) return 0;\n\tconst c = Math.pow(v / 1e4, M1);\n\treturn Math.pow((C1 + C2 * c) / (1 + C3 * c), M2);\n}\n", "import { YW } from '../hdr/constants.js';\nimport { transferPqDecode } from '../hdr/transfer.js';\n\nconst toRel = c => Math.max(c / YW, 0);\n\nconst convertItpToXyz65 = ({ i, t, p, alpha }) => {\n\tif (i === undefined) i = 0;\n\tif (t === undefined) t = 0;\n\tif (p === undefined) p = 0;\n\n\tconst l = transferPqDecode(\n\t\ti + 0.008609037037932761 * t + 0.11102962500302593 * p\n\t);\n\tconst m = transferPqDecode(\n\t\ti - 0.00860903703793275 * t - 0.11102962500302599 * p\n\t);\n\tconst s = transferPqDecode(\n\t\ti + 0.5600313357106791 * t - 0.32062717498731885 * p\n\t);\n\n\tconst res = {\n\t\tmode: 'xyz65',\n\t\tx: toRel(\n\t\t\t2.0701522183894219 * l -\n\t\t\t\t1.3263473389671556 * m +\n\t\t\t\t0.2066510476294051 * s\n\t\t),\n\t\ty: toRel(\n\t\t\t0.3647385209748074 * l +\n\t\t\t\t0.680566024947227 * m -\n\t\t\t\t0.0453045459220346 * s\n\t\t),\n\t\tz: toRel(\n\t\t\t-0.049747207535812 * l -\n\t\t\t\t0.0492609666966138 * m +\n\t\t\t\t1.1880659249923042 * s\n\t\t)\n\t};\n\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\n\treturn res;\n};\n\nexport default convertItpToXyz65;\n", "import { YW } from '../hdr/constants.js';\nimport { transferPqEncode } from '../hdr/transfer.js';\n\nconst toAbs = (c = 0) => Math.max(c * YW, 0);\n\nconst convertXyz65ToItp = ({ x, y, z, alpha }) => {\n\tconst absX = toAbs(x);\n\tconst absY = toAbs(y);\n\tconst absZ = toAbs(z);\n\tconst l = transferPqEncode(\n\t\t0.3592832590121217 * absX +\n\t\t\t0.6976051147779502 * absY -\n\t\t\t0.0358915932320289 * absZ\n\t);\n\tconst m = transferPqEncode(\n\t\t-0.1920808463704995 * absX +\n\t\t\t1.1004767970374323 * absY +\n\t\t\t0.0753748658519118 * absZ\n\t);\n\tconst s = transferPqEncode(\n\t\t0.0070797844607477 * absX +\n\t\t\t0.0748396662186366 * absY +\n\t\t\t0.8433265453898765 * absZ\n\t);\n\n\tconst i = 0.5 * l + 0.5 * m;\n\tconst t = 1.61376953125 * l - 3.323486328125 * m + 1.709716796875 * s;\n\tconst p = 4.378173828125 * l - 4.24560546875 * m - 0.132568359375 * s;\n\n\tconst res = { mode: 'itp', i, t, p };\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\n\treturn res;\n};\n\nexport default convertXyz65ToItp;\n", "import { interpolatorLinear } from '../interpolate/linear.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\nimport convertItpToXyz65 from './convertItpToXyz65.js';\nimport convertXyz65ToItp from './convertXyz65ToItp.js';\nimport convertRgbToXyz65 from '../xyz65/convertRgbToXyz65.js';\nimport convertXyz65ToRgb from '../xyz65/convertXyz65ToRgb.js';\n\n/*\n  ICtCp (or ITP) color space, as defined in ITU-R Recommendation BT.2100.\n\n  ICtCp is drafted to be supported in CSS within\n  [CSS Color HDR Module Level 1](https://drafts.csswg.org/css-color-hdr/#ICtCp) spec.\n*/\n\nconst definition = {\n\tmode: 'itp',\n\tchannels: ['i', 't', 'p', 'alpha'],\n\tparse: ['--ictcp'],\n\tserialize: '--ictcp',\n\n\ttoMode: {\n\t\txyz65: convertItpToXyz65,\n\t\trgb: color => convertXyz65ToRgb(convertItpToXyz65(color))\n\t},\n\n\tfromMode: {\n\t\txyz65: convertXyz65ToItp,\n\t\trgb: color => convertXyz65ToItp(convertRgbToXyz65(color))\n\t},\n\n\tranges: {\n\t\ti: [0, 0.581],\n\t\tt: [-0.369, 0.272],\n\t\tp: [-0.164, 0.331]\n\t},\n\n\tinterpolate: {\n\t\ti: interpolatorLinear,\n\t\tt: interpolatorLinear,\n\t\tp: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t}\n};\n\nexport default definition;\n", "import { M1 as n, C1, C2, C3 } from '../hdr/transfer.js';\nconst p = 134.03437499999998; // = 1.7 * 2523 / Math.pow(2, 5);\nconst d0 = 1.6295499532821566e-11;\n\n/* \n\tThe encoding function is derived from Perceptual Quantizer.\n*/\nconst jabPqEncode = v => {\n\tif (v < 0) return 0;\n\tlet vn = Math.pow(v / 10000, n);\n\treturn Math.pow((C1 + C2 * vn) / (1 + C3 * vn), p);\n};\n\n// Convert to Absolute XYZ\nconst abs = (v = 0) => Math.max(v * 203, 0);\n\nconst convertXyz65ToJab = ({ x, y, z, alpha }) => {\n\tx = abs(x);\n\ty = abs(y);\n\tz = abs(z);\n\n\tlet xp = 1.15 * x - 0.15 * z;\n\tlet yp = 0.66 * y + 0.34 * x;\n\n\tlet l = jabPqEncode(0.41478972 * xp + 0.579999 * yp + 0.014648 * z);\n\tlet m = jabPqEncode(-0.20151 * xp + 1.120649 * yp + 0.0531008 * z);\n\tlet s = jabPqEncode(-0.0166008 * xp + 0.2648 * yp + 0.6684799 * z);\n\n\tlet i = (l + m) / 2;\n\n\tlet res = {\n\t\tmode: 'jab',\n\t\tj: (0.44 * i) / (1 - 0.56 * i) - d0,\n\t\ta: 3.524 * l - 4.066708 * m + 0.542708 * s,\n\t\tb: 0.199076 * l + 1.096799 * m - 1.295875 * s\n\t};\n\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\n\treturn res;\n};\n\nexport default convertXyz65ToJab;\n", "import { M1 as n, C1, C2, C3 } from '../hdr/transfer.js';\nconst p = 134.03437499999998; // = 1.7 * 2523 / Math.pow(2, 5);\nconst d0 = 1.6295499532821566e-11;\n\n/* \n\tThe encoding function is derived from Perceptual Quantizer.\n*/\nconst jabPqDecode = v => {\n\tif (v < 0) return 0;\n\tlet vp = Math.pow(v, 1 / p);\n\treturn 10000 * Math.pow((C1 - vp) / (C3 * vp - C2), 1 / n);\n};\n\nconst rel = v => v / 203;\n\nconst convertJabToXyz65 = ({ j, a, b, alpha }) => {\n\tif (j === undefined) j = 0;\n\tif (a === undefined) a = 0;\n\tif (b === undefined) b = 0;\n\tlet i = (j + d0) / (0.44 + 0.56 * (j + d0));\n\n\tlet l = jabPqDecode(i + 0.13860504 * a + 0.058047316 * b);\n\tlet m = jabPqDecode(i - 0.13860504 * a - 0.058047316 * b);\n\tlet s = jabPqDecode(i - 0.096019242 * a - 0.8118919 * b);\n\n\tlet res = {\n\t\tmode: 'xyz65',\n\t\tx: rel(\n\t\t\t1.661373024652174 * l -\n\t\t\t\t0.914523081304348 * m +\n\t\t\t\t0.23136208173913045 * s\n\t\t),\n\t\ty: rel(\n\t\t\t-0.3250758611844533 * l +\n\t\t\t\t1.571847026732543 * m -\n\t\t\t\t0.21825383453227928 * s\n\t\t),\n\t\tz: rel(-0.090982811 * l - 0.31272829 * m + 1.5227666 * s)\n\t};\n\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\n\treturn res;\n};\n\nexport default convertJabToXyz65;\n", "/*\n\tConvert sRGB to JzAzBz.\n\n\tFor achromatic sRGB colors, adjust the equivalent JzAzBz color\n\tto be achromatic as well, insteading of having a very slight chroma.\n */\n\nimport convertXyz65ToJab from './convertXyz65ToJab.js';\nimport convertRgbToXyz65 from '../xyz65/convertRgbToXyz65.js';\n\nconst convertRgbToJab = rgb => {\n\tlet res = convertXyz65ToJab(convertRgbToXyz65(rgb));\n\tif (rgb.r === rgb.b && rgb.b === rgb.g) {\n\t\tres.a = res.b = 0;\n\t}\n\treturn res;\n};\n\nexport default convertRgbToJab;\n", "import convertXyz65ToRgb from '../xyz65/convertXyz65ToRgb.js';\nimport convertJabToXyz65 from './convertJabToXyz65.js';\n\nconst convertJabToRgb = color => convertXyz65ToRgb(convertJabToXyz65(color));\n\nexport default convertJabToRgb;\n", "/*\n\tThe JzAzBz color space.\n\n\tBased on:\n\n\t<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, \n\t\"Perceptually uniform color space for image signals \n\tincluding high dynamic range and wide gamut,\" \n\tOpt. Express 25, 15131-15151 (2017) \n\n\thttps://doi.org/10.1364/OE.25.015131\n */\n\nimport convertXyz65ToJab from './convertXyz65ToJab.js';\nimport convertJabToXyz65 from './convertJabToXyz65.js';\nimport convertRgbToJab from './convertRgbToJab.js';\nimport convertJabToRgb from './convertJabToRgb.js';\n\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\n\nconst definition = {\n\tmode: 'jab',\n\tchannels: ['j', 'a', 'b', 'alpha'],\n\n\tparse: ['--jzazbz'],\n\tserialize: '--jzazbz',\n\n\tfromMode: {\n\t\trgb: convertRgbToJab,\n\t\txyz65: convertXyz65ToJab\n\t},\n\n\ttoMode: {\n\t\trgb: convertJabToRgb,\n\t\txyz65: convertJabToXyz65\n\t},\n\n\tranges: {\n\t\tj: [0, 0.222],\n\t\ta: [-0.109, 0.129],\n\t\tb: [-0.185, 0.134]\n\t},\n\n\tinterpolate: {\n\t\tj: interpolatorLinear,\n\t\ta: interpolatorLinear,\n\t\tb: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t}\n};\n\nexport default definition;\n", "import normalizeHue from '../util/normalizeHue.js';\n\nconst convertJabToJch = ({ j, a, b, alpha }) => {\n\tif (a === undefined) a = 0;\n\tif (b === undefined) b = 0;\n\tlet c = Math.sqrt(a * a + b * b);\n\tlet res = {\n\t\tmode: 'jch',\n\t\tj,\n\t\tc\n\t};\n\tif (c) {\n\t\tres.h = normalizeHue((Math.atan2(b, a) * 180) / Math.PI);\n\t}\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertJabToJch;\n", "const convertJchToJab = ({ j, c, h, alpha }) => {\n\tif (h === undefined) h = 0;\n\tlet res = {\n\t\tmode: 'jab',\n\t\tj,\n\t\ta: c ? c * Math.cos((h / 180) * Math.PI) : 0,\n\t\tb: c ? c * Math.sin((h / 180) * Math.PI) : 0\n\t};\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n};\n\nexport default convertJchToJab;\n", "import convertJabToJch from './convertJabToJch.js';\nimport convertJchToJab from './convertJchToJab.js';\nimport convertJabToRgb from '../jab/convertJabToRgb.js';\nimport convertRgbToJab from '../jab/convertRgbToJab.js';\n\nimport { fixupHueShorter } from '../fixup/hue.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { differenceHueChroma } from '../difference.js';\nimport { averageAngle } from '../average.js';\n\nconst definition = {\n\tmode: 'jch',\n\n\tparse: ['--jzczhz'],\n\tserialize: '--jzczhz',\n\n\ttoMode: {\n\t\tjab: convertJchToJab,\n\t\trgb: c => convertJabToRgb(convertJchToJab(c))\n\t},\n\n\tfromMode: {\n\t\trgb: c => convertJabToJch(convertRgbToJab(c)),\n\t\tjab: convertJabToJch\n\t},\n\n\tchannels: ['j', 'c', 'h', 'alpha'],\n\n\tranges: {\n\t\tj: [0, 0.221],\n\t\tc: [0, 0.19],\n\t\th: [0, 360]\n\t},\n\n\tinterpolate: {\n\t\th: { use: interpolatorLinear, fixup: fixupHueShorter },\n\t\tc: interpolatorLinear,\n\t\tj: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t},\n\n\tdifference: {\n\t\th: differenceHueChroma\n\t},\n\n\taverage: {\n\t\th: averageAngle\n\t}\n};\n\nexport default definition;\n", "export const k = Math.pow(29, 3) / Math.pow(3, 3);\nexport const e = Math.pow(6, 3) / Math.pow(29, 3);\n", "import { k, e } from '../xyz50/constants.js';\nimport { D50 } from '../constants.js';\n\nlet fn = v => (Math.pow(v, 3) > e ? Math.pow(v, 3) : (116 * v - 16) / k);\n\nconst convertLabToXyz50 = ({ l, a, b, alpha }) => {\n\tif (l === undefined) l = 0;\n\tif (a === undefined) a = 0;\n\tif (b === undefined) b = 0;\n\tlet fy = (l + 16) / 116;\n\tlet fx = a / 500 + fy;\n\tlet fz = fy - b / 200;\n\n\tlet res = {\n\t\tmode: 'xyz50',\n\t\tx: fn(fx) * D50.X,\n\t\ty: fn(fy) * D50.Y,\n\t\tz: fn(fz) * D50.Z\n\t};\n\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\n\treturn res;\n};\n\nexport default convertLabToXyz50;\n", "/*\n\tCIE XYZ D50 values to sRGB.\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n*/\n\nimport convertLrgbToRgb from '../lrgb/convertLrgbToRgb.js';\n\nconst convertXyz50ToRgb = ({ x, y, z, alpha }) => {\n\tif (x === undefined) x = 0;\n\tif (y === undefined) y = 0;\n\tif (z === undefined) z = 0;\n\tlet res = convertLrgbToRgb({\n\t\tr:\n\t\t\tx * 3.1341359569958707 -\n\t\t\ty * 1.6173863321612538 -\n\t\t\t0.4906619460083532 * z,\n\t\tg:\n\t\t\tx * -0.978795502912089 +\n\t\t\ty * 1.916254567259524 +\n\t\t\t0.03344273116131949 * z,\n\t\tb:\n\t\t\tx * 0.07195537988411677 -\n\t\t\ty * 0.2289768264158322 +\n\t\t\t1.405386058324125 * z\n\t});\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertXyz50ToRgb;\n", "import convertLabToXyz50 from './convertLabToXyz50.js';\nimport convertXyz50ToRgb from '../xyz50/convertXyz50ToRgb.js';\n\nconst convertLabToRgb = lab => convertXyz50ToRgb(convertLabToXyz50(lab));\n\nexport default convertLabToRgb;\n", "/*\n\tConvert sRGB values to CIE XYZ D50\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\t\n*/\n\nimport convertRgbToLrgb from '../lrgb/convertRgbToLrgb.js';\n\nconst convertRgbToXyz50 = rgb => {\n\tlet { r, g, b, alpha } = convertRgbToLrgb(rgb);\n\tlet res = {\n\t\tmode: 'xyz50',\n\t\tx:\n\t\t\t0.436065742824811 * r +\n\t\t\t0.3851514688337912 * g +\n\t\t\t0.14307845442264197 * b,\n\t\ty:\n\t\t\t0.22249319175623702 * r +\n\t\t\t0.7168870538238823 * g +\n\t\t\t0.06061979053616537 * b,\n\t\tz:\n\t\t\t0.013923904500943465 * r +\n\t\t\t0.09708128566574634 * g +\n\t\t\t0.7140993584005155 * b\n\t};\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertRgbToXyz50;\n", "import { k, e } from '../xyz50/constants.js';\nimport { D50 } from '../constants.js';\n\nconst f = value => (value > e ? Math.cbrt(value) : (k * value + 16) / 116);\n\nconst convertXyz50ToLab = ({ x, y, z, alpha }) => {\n\tif (x === undefined) x = 0;\n\tif (y === undefined) y = 0;\n\tif (z === undefined) z = 0;\n\tlet f0 = f(x / D50.X);\n\tlet f1 = f(y / D50.Y);\n\tlet f2 = f(z / D50.Z);\n\n\tlet res = {\n\t\tmode: 'lab',\n\t\tl: 116 * f1 - 16,\n\t\ta: 500 * (f0 - f1),\n\t\tb: 200 * (f1 - f2)\n\t};\n\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\n\treturn res;\n};\n\nexport default convertXyz50ToLab;\n", "import convertRgbToXyz50 from '../xyz50/convertRgbToXyz50.js';\nimport convertXyz50ToLab from './convertXyz50ToLab.js';\n\nconst convertRgbToLab = rgb => {\n\tlet res = convertXyz50ToLab(convertRgbToXyz50(rgb));\n\n\t// Fixes achromatic RGB colors having a _slight_ chroma due to floating-point errors\n\t// and approximated computations in sRGB <-> CIELab.\n\t// See: https://github.com/d3/d3-color/pull/46\n\tif (rgb.r === rgb.b && rgb.b === rgb.g) {\n\t\tres.a = res.b = 0;\n\t}\n\treturn res;\n};\n\nexport default convertRgbToLab;\n", "import { Tok } from '../parse.js';\n\nfunction parseLab(color, parsed) {\n\tif (!parsed || parsed[0] !== 'lab') {\n\t\treturn undefined;\n\t}\n\tconst res = { mode: 'lab' };\n\tconst [, l, a, b, alpha] = parsed;\n\tif (l.type === Tok.Hue || a.type === Tok.Hue || b.type === Tok.Hue) {\n\t\treturn undefined;\n\t}\n\tif (l.type !== Tok.None) {\n\t\tres.l = Math.min(Math.max(0, l.value), 100);\n\t}\n\tif (a.type !== Tok.None) {\n\t\tres.a = a.type === Tok.Number ? a.value : (a.value * 125) / 100;\n\t}\n\tif (b.type !== Tok.None) {\n\t\tres.b = b.type === Tok.Number ? b.value : (b.value * 125) / 100;\n\t}\n\tif (alpha.type !== Tok.None) {\n\t\tres.alpha = Math.min(\n\t\t\t1,\n\t\t\tMath.max(\n\t\t\t\t0,\n\t\t\t\talpha.type === Tok.Number ? alpha.value : alpha.value / 100\n\t\t\t)\n\t\t);\n\t}\n\n\treturn res;\n}\n\nexport default parseLab;\n", "import convertLabToRgb from './convertLabToRgb.js';\nimport convertLabToXyz50 from './convertLabToXyz50.js';\nimport convertRgbToLab from './convertRgbToLab.js';\nimport convertXyz50ToLab from './convertXyz50ToLab.js';\nimport parseLab from './parseLab.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\n\nconst definition = {\n\tmode: 'lab',\n\n\ttoMode: {\n\t\txyz50: convertLabToXyz50,\n\t\trgb: convertLabToRgb\n\t},\n\n\tfromMode: {\n\t\txyz50: convertXyz50ToLab,\n\t\trgb: convertRgbToLab\n\t},\n\n\tchannels: ['l', 'a', 'b', 'alpha'],\n\n\tranges: {\n\t\tl: [0, 100],\n\t\ta: [-100, 100],\n\t\tb: [-100, 100]\n\t},\n\n\tparse: [parseLab],\n\tserialize: c =>\n\t\t`lab(${c.l !== undefined ? c.l : 'none'} ${\n\t\t\tc.a !== undefined ? c.a : 'none'\n\t\t} ${c.b !== undefined ? c.b : 'none'}${\n\t\t\tc.alpha < 1 ? ` / ${c.alpha}` : ''\n\t\t})`,\n\n\tinterpolate: {\n\t\tl: interpolatorLinear,\n\t\ta: interpolatorLinear,\n\t\tb: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t}\n};\n\nexport default definition;\n", "import convertLab65ToRgb from './convertLab65ToRgb.js';\nimport convertLab65ToXyz65 from './convertLab65ToXyz65.js';\nimport convertRgbToLab65 from './convertRgbToLab65.js';\nimport convertXyz65ToLab65 from './convertXyz65ToLab65.js';\nimport lab from '../lab/definition.js';\n\nconst definition = {\n\t...lab,\n\tmode: 'lab65',\n\n\tparse: ['--lab-d65'],\n\tserialize: '--lab-d65',\n\n\ttoMode: {\n\t\txyz65: convertLab65ToXyz65,\n\t\trgb: convertLab65ToRgb\n\t},\n\n\tfromMode: {\n\t\txyz65: convertXyz65ToLab65,\n\t\trgb: convertRgbToLab65\n\t},\n\n\tranges: {\n\t\tl: [0, 100],\n\t\ta: [-86.182, 98.234],\n\t\tb: [-107.86, 94.477]\n\t}\n};\n\nexport default definition;\n", "import { Tok } from '../parse.js';\n\nfunction parseLch(color, parsed) {\n\tif (!parsed || parsed[0] !== 'lch') {\n\t\treturn undefined;\n\t}\n\tconst res = { mode: 'lch' };\n\tconst [, l, c, h, alpha] = parsed;\n\tif (l.type !== Tok.None) {\n\t\tif (l.type === Tok.Hue) {\n\t\t\treturn undefined;\n\t\t}\n\t\tres.l = Math.min(Math.max(0, l.value), 100);\n\t}\n\tif (c.type !== Tok.None) {\n\t\tres.c = Math.max(\n\t\t\t0,\n\t\t\tc.type === Tok.Number ? c.value : (c.value * 150) / 100\n\t\t);\n\t}\n\tif (h.type !== Tok.None) {\n\t\tif (h.type === Tok.Percentage) {\n\t\t\treturn undefined;\n\t\t}\n\t\tres.h = h.value;\n\t}\n\tif (alpha.type !== Tok.None) {\n\t\tres.alpha = Math.min(\n\t\t\t1,\n\t\t\tMath.max(\n\t\t\t\t0,\n\t\t\t\talpha.type === Tok.Number ? alpha.value : alpha.value / 100\n\t\t\t)\n\t\t);\n\t}\n\n\treturn res;\n}\n\nexport default parseLch;\n", "import convertLabToLch from './convertLabToLch.js';\nimport convertLchToLab from './convertLchToLab.js';\nimport convertLabToRgb from '../lab/convertLabToRgb.js';\nimport convertRgbToLab from '../lab/convertRgbToLab.js';\nimport parseLch from './parseLch.js';\nimport { fixupHueShorter } from '../fixup/hue.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { differenceHueChroma } from '../difference.js';\nimport { averageAngle } from '../average.js';\n\nconst definition = {\n\tmode: 'lch',\n\n\ttoMode: {\n\t\tlab: convertLchToLab,\n\t\trgb: c => convertLabToRgb(convertLchToLab(c))\n\t},\n\n\tfromMode: {\n\t\trgb: c => convertLabToLch(convertRgbToLab(c)),\n\t\tlab: convertLabToLch\n\t},\n\n\tchannels: ['l', 'c', 'h', 'alpha'],\n\n\tranges: {\n\t\tl: [0, 100],\n\t\tc: [0, 150],\n\t\th: [0, 360]\n\t},\n\n\tparse: [parseLch],\n\tserialize: c =>\n\t\t`lch(${c.l !== undefined ? c.l : 'none'} ${\n\t\t\tc.c !== undefined ? c.c : 'none'\n\t\t} ${c.h !== undefined ? c.h : 'none'}${\n\t\t\tc.alpha < 1 ? ` / ${c.alpha}` : ''\n\t\t})`,\n\n\tinterpolate: {\n\t\th: { use: interpolatorLinear, fixup: fixupHueShorter },\n\t\tc: interpolatorLinear,\n\t\tl: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t},\n\n\tdifference: {\n\t\th: differenceHueChroma\n\t},\n\n\taverage: {\n\t\th: averageAngle\n\t}\n};\n\nexport default definition;\n", "import convertLabToLch from '../lch/convertLabToLch.js';\nimport convertLchToLab from '../lch/convertLchToLab.js';\nimport convertLab65ToRgb from '../lab65/convertLab65ToRgb.js';\nimport convertRgbToLab65 from '../lab65/convertRgbToLab65.js';\nimport lch from '../lch/definition.js';\n\nconst definition = {\n\t...lch,\n\tmode: 'lch65',\n\n\tparse: ['--lch-d65'],\n\tserialize: '--lch-d65',\n\n\ttoMode: {\n\t\tlab65: c => convertLchToLab(c, 'lab65'),\n\t\trgb: c => convertLab65ToRgb(convertLchToLab(c, 'lab65'))\n\t},\n\n\tfromMode: {\n\t\trgb: c => convertLabToLch(convertRgbToLab65(c), 'lch65'),\n\t\tlab65: c => convertLabToLch(c, 'lch65')\n\t},\n\n\tranges: {\n\t\tl: [0, 100],\n\t\tc: [0, 133.807],\n\t\th: [0, 360]\n\t}\n};\n\nexport default definition;\n", "import normalizeHue from '../util/normalizeHue.js';\n\nconst convertLuvToLchuv = ({ l, u, v, alpha }) => {\n\tif (u === undefined) u = 0;\n\tif (v === undefined) v = 0;\n\tlet c = Math.sqrt(u * u + v * v);\n\tlet res = {\n\t\tmode: 'lchuv',\n\t\tl: l,\n\t\tc: c\n\t};\n\tif (c) {\n\t\tres.h = normalizeHue((Math.atan2(v, u) * 180) / Math.PI);\n\t}\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertLuvToLchuv;\n", "const convertLchuvToLuv = ({ l, c, h, alpha }) => {\n\tif (h === undefined) h = 0;\n\tlet res = {\n\t\tmode: 'luv',\n\t\tl: l,\n\t\tu: c ? c * Math.cos((h / 180) * Math.PI) : 0,\n\t\tv: c ? c * Math.sin((h / 180) * Math.PI) : 0\n\t};\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertLchuvToLuv;\n", "import { k, e } from '../xyz50/constants.js';\nimport { D50 } from '../constants.js';\n\nexport const u_fn = (x, y, z) => (4 * x) / (x + 15 * y + 3 * z);\nexport const v_fn = (x, y, z) => (9 * y) / (x + 15 * y + 3 * z);\n\nexport const un = u_fn(D50.X, D50.Y, D50.Z);\nexport const vn = v_fn(D50.X, D50.Y, D50.Z);\n\nconst l_fn = value => (value <= e ? k * value : 116 * Math.cbrt(value) - 16);\n\nconst convertXyz50ToLuv = ({ x, y, z, alpha }) => {\n\tif (x === undefined) x = 0;\n\tif (y === undefined) y = 0;\n\tif (z === undefined) z = 0;\n\tlet l = l_fn(y / D50.Y);\n\tlet u = u_fn(x, y, z);\n\tlet v = v_fn(x, y, z);\n\n\t// guard against NaNs produced by `xyz(0 0 0)` black\n\tif (!isFinite(u) || !isFinite(v)) {\n\t\tl = u = v = 0;\n\t} else {\n\t\tu = 13 * l * (u - un);\n\t\tv = 13 * l * (v - vn);\n\t}\n\n\tlet res = {\n\t\tmode: 'luv',\n\t\tl,\n\t\tu,\n\t\tv\n\t};\n\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\n\treturn res;\n};\n\nexport default convertXyz50ToLuv;\n", "import { k } from '../xyz50/constants.js';\nimport { D50 } from '../constants.js';\n\nexport const u_fn = (x, y, z) => (4 * x) / (x + 15 * y + 3 * z);\nexport const v_fn = (x, y, z) => (9 * y) / (x + 15 * y + 3 * z);\n\nexport const un = u_fn(D50.X, D50.Y, D50.Z);\nexport const vn = v_fn(D50.X, D50.Y, D50.Z);\n\nconst convertLuvToXyz50 = ({ l, u, v, alpha }) => {\n\tif (l === undefined) l = 0;\n\tif (l === 0) {\n\t\treturn { mode: 'xyz50', x: 0, y: 0, z: 0 };\n\t}\n\n\tif (u === undefined) u = 0;\n\tif (v === undefined) v = 0;\n\n\tlet up = u / (13 * l) + un;\n\tlet vp = v / (13 * l) + vn;\n\tlet y = D50.Y * (l <= 8 ? l / k : Math.pow((l + 16) / 116, 3));\n\tlet x = (y * (9 * up)) / (4 * vp);\n\tlet z = (y * (12 - 3 * up - 20 * vp)) / (4 * vp);\n\n\tlet res = { mode: 'xyz50', x, y, z };\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\n\treturn res;\n};\n\nexport default convertLuvToXyz50;\n", "/*\n\tCIELChuv color space\n\t--------------------\n\n\tReference: \n\n\t\thttps://en.wikipedia.org/wiki/CIELUV\n */\n\nimport convertLuvToLchuv from './convertLuvToLchuv.js';\nimport convertLchuvToLuv from './convertLchuvToLuv.js';\nimport convertXyz50ToLuv from '../luv/convertXyz50ToLuv.js';\nimport convertLuvToXyz50 from '../luv/convertLuvToXyz50.js';\nimport convertXyz50ToRgb from '../xyz50/convertXyz50ToRgb.js';\nimport convertRgbToXyz50 from '../xyz50/convertRgbToXyz50.js';\n\nimport { fixupHueShorter } from '../fixup/hue.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { differenceHueChroma } from '../difference.js';\nimport { averageAngle } from '../average.js';\n\nconst convertRgbToLchuv = rgb =>\n\tconvertLuvToLchuv(convertXyz50ToLuv(convertRgbToXyz50(rgb)));\nconst convertLchuvToRgb = lchuv =>\n\tconvertXyz50ToRgb(convertLuvToXyz50(convertLchuvToLuv(lchuv)));\n\nconst definition = {\n\tmode: 'lchuv',\n\n\ttoMode: {\n\t\tluv: convertLchuvToLuv,\n\t\trgb: convertLchuvToRgb\n\t},\n\n\tfromMode: {\n\t\trgb: convertRgbToLchuv,\n\t\tluv: convertLuvToLchuv\n\t},\n\n\tchannels: ['l', 'c', 'h', 'alpha'],\n\n\tparse: ['--lchuv'],\n\tserialize: '--lchuv',\n\n\tranges: {\n\t\tl: [0, 100],\n\t\tc: [0, 176.956],\n\t\th: [0, 360]\n\t},\n\n\tinterpolate: {\n\t\th: { use: interpolatorLinear, fixup: fixupHueShorter },\n\t\tc: interpolatorLinear,\n\t\tl: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t},\n\n\tdifference: {\n\t\th: differenceHueChroma\n\t},\n\n\taverage: {\n\t\th: averageAngle\n\t}\n};\n\nexport default definition;\n", "import rgb from '../rgb/definition.js';\nimport convertRgbToLrgb from './convertRgbToLrgb.js';\nimport convertLrgbToRgb from './convertLrgbToRgb.js';\n\nconst definition = {\n\t...rgb,\n\tmode: 'lrgb',\n\n\ttoMode: {\n\t\trgb: convertLrgbToRgb\n\t},\n\n\tfromMode: {\n\t\trgb: convertRgbToLrgb\n\t},\n\n\tparse: ['srgb-linear'],\n\tserialize: 'srgb-linear'\n};\n\nexport default definition;\n", "/*\n\tCIELUV color space\n\t------------------\n\n\tReference: \n\n\t\thttps://en.wikipedia.org/wiki/CIELUV\n */\n\nimport convertXyz50ToLuv from './convertXyz50ToLuv.js';\nimport convertLuvToXyz50 from './convertLuvToXyz50.js';\nimport convertXyz50ToRgb from '../xyz50/convertXyz50ToRgb.js';\nimport convertRgbToXyz50 from '../xyz50/convertRgbToXyz50.js';\n\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\n\nconst definition = {\n\tmode: 'luv',\n\n\ttoMode: {\n\t\txyz50: convertLuvToXyz50,\n\t\trgb: luv => convertXyz50ToRgb(convertLuvToXyz50(luv))\n\t},\n\n\tfromMode: {\n\t\txyz50: convertXyz50ToLuv,\n\t\trgb: rgb => convertXyz50ToLuv(convertRgbToXyz50(rgb))\n\t},\n\n\tchannels: ['l', 'u', 'v', 'alpha'],\n\n\tparse: ['--luv'],\n\tserialize: '--luv',\n\n\tranges: {\n\t\tl: [0, 100],\n\t\tu: [-84.936, 175.042],\n\t\tv: [-125.882, 87.243]\n\t},\n\n\tinterpolate: {\n\t\tl: interpolatorLinear,\n\t\tu: interpolatorLinear,\n\t\tv: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t}\n};\n\nexport default definition;\n", "const convertLrgbToOklab = ({ r, g, b, alpha }) => {\n\tif (r === undefined) r = 0;\n\tif (g === undefined) g = 0;\n\tif (b === undefined) b = 0;\n\tlet L = Math.cbrt(\n\t\t0.41222147079999993 * r + 0.5363325363 * g + 0.0514459929 * b\n\t);\n\tlet M = Math.cbrt(\n\t\t0.2119034981999999 * r + 0.6806995450999999 * g + 0.1073969566 * b\n\t);\n\tlet S = Math.cbrt(\n\t\t0.08830246189999998 * r + 0.2817188376 * g + 0.6299787005000002 * b\n\t);\n\n\tlet res = {\n\t\tmode: 'oklab',\n\t\tl: 0.2104542553 * L + 0.793617785 * M - 0.0040720468 * S,\n\t\ta: 1.9779984951 * L - 2.428592205 * M + 0.4505937099 * S,\n\t\tb: 0.0259040371 * L + 0.7827717662 * M - 0.808675766 * S\n\t};\n\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\n\treturn res;\n};\n\nexport default convertLrgbToOklab;\n", "import convertRgbToLrgb from '../lrgb/convertRgbToLrgb.js';\nimport convertLrgbToOklab from './convertLrgbToOklab.js';\n\nconst convertRgbToOklab = rgb => {\n\tlet res = convertLrgbToOklab(convertRgbToLrgb(rgb));\n\tif (rgb.r === rgb.b && rgb.b === rgb.g) {\n\t\tres.a = res.b = 0;\n\t}\n\treturn res;\n};\n\nexport default convertRgbToOklab;\n", "const convertOklabToLrgb = ({ l, a, b, alpha }) => {\n\tif (l === undefined) l = 0;\n\tif (a === undefined) a = 0;\n\tif (b === undefined) b = 0;\n\tlet L = Math.pow(\n\t\tl * 0.99999999845051981432 +\n\t\t\t0.39633779217376785678 * a +\n\t\t\t0.21580375806075880339 * b,\n\t\t3\n\t);\n\tlet M = Math.pow(\n\t\tl * 1.0000000088817607767 -\n\t\t\t0.1055613423236563494 * a -\n\t\t\t0.063854174771705903402 * b,\n\t\t3\n\t);\n\tlet S = Math.pow(\n\t\tl * 1.0000000546724109177 -\n\t\t\t0.089484182094965759684 * a -\n\t\t\t1.2914855378640917399 * b,\n\t\t3\n\t);\n\n\tlet res = {\n\t\tmode: 'lrgb',\n\t\tr:\n\t\t\t+4.076741661347994 * L -\n\t\t\t3.307711590408193 * M +\n\t\t\t0.230969928729428 * S,\n\t\tg:\n\t\t\t-1.2684380040921763 * L +\n\t\t\t2.6097574006633715 * M -\n\t\t\t0.3413193963102197 * S,\n\t\tb:\n\t\t\t-0.004196086541837188 * L -\n\t\t\t0.7034186144594493 * M +\n\t\t\t1.7076147009309444 * S\n\t};\n\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\n\treturn res;\n};\n\nexport default convertOklabToLrgb;\n", "import convertLrgbToRgb from '../lrgb/convertLrgbToRgb.js';\nimport convertOklabToLrgb from './convertOklabToLrgb.js';\n\nconst convertOklabToRgb = c => convertLrgbToRgb(convertOklabToLrgb(c));\n\nexport default convertOklabToRgb;\n", "/*\n\tAdapted from code by <PERSON><PERSON><PERSON><PERSON>,\n\treleased under the MIT license:\n\n\tCopyright (c) 2021 <PERSON><PERSON><PERSON><PERSON>\n\n\tPermission is hereby granted, free of charge, to any person obtaining a copy of\n\tthis software and associated documentation files (the \"Software\"), to deal in\n\tthe Software without restriction, including without limitation the rights to\n\tuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n\tof the Software, and to permit persons to whom the Software is furnished to do\n\tso, subject to the following conditions:\n\n\tThe above copyright notice and this permission notice shall be included in all\n\tcopies or substantial portions of the Software.\n\n\tTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n\tIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n\tFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n\tAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n\tLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n\tOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n\tSOFTWARE.\n */\n\nimport convertOklabToLrgb from '../oklab/convertOklabToLrgb.js';\n\nexport function toe(x) {\n\tconst k_1 = 0.206;\n\tconst k_2 = 0.03;\n\tconst k_3 = (1 + k_1) / (1 + k_2);\n\treturn (\n\t\t0.5 *\n\t\t(k_3 * x -\n\t\t\tk_1 +\n\t\t\tMath.sqrt((k_3 * x - k_1) * (k_3 * x - k_1) + 4 * k_2 * k_3 * x))\n\t);\n}\n\nexport function toe_inv(x) {\n\tconst k_1 = 0.206;\n\tconst k_2 = 0.03;\n\tconst k_3 = (1 + k_1) / (1 + k_2);\n\treturn (x * x + k_1 * x) / (k_3 * (x + k_2));\n}\n\n// Finds the maximum saturation possible for a given hue that fits in sRGB\n// Saturation here is defined as S = C/L\n// a and b must be normalized so a^2 + b^2 == 1\nfunction compute_max_saturation(a, b) {\n\t// Max saturation will be when one of r, g or b goes below zero.\n\n\t// Select different coefficients depending on which component goes below zero first\n\tlet k0, k1, k2, k3, k4, wl, wm, ws;\n\n\tif (-1.88170328 * a - 0.80936493 * b > 1) {\n\t\t// Red component\n\t\tk0 = +1.19086277;\n\t\tk1 = +1.76576728;\n\t\tk2 = +0.59662641;\n\t\tk3 = +0.75515197;\n\t\tk4 = +0.56771245;\n\t\twl = +4.0767416621;\n\t\twm = -3.3077115913;\n\t\tws = +0.2309699292;\n\t} else if (1.81444104 * a - 1.19445276 * b > 1) {\n\t\t// Green component\n\t\tk0 = +0.73956515;\n\t\tk1 = -0.45954404;\n\t\tk2 = +0.08285427;\n\t\tk3 = +0.1254107;\n\t\tk4 = +0.14503204;\n\t\twl = -1.2684380046;\n\t\twm = +2.6097574011;\n\t\tws = -0.3413193965;\n\t} else {\n\t\t// Blue component\n\t\tk0 = +1.35733652;\n\t\tk1 = -0.00915799;\n\t\tk2 = -1.1513021;\n\t\tk3 = -0.50559606;\n\t\tk4 = +0.00692167;\n\t\twl = -0.0041960863;\n\t\twm = -0.7034186147;\n\t\tws = +1.707614701;\n\t}\n\n\t// Approximate max saturation using a polynomial:\n\tlet S = k0 + k1 * a + k2 * b + k3 * a * a + k4 * a * b;\n\n\t// Do one step Halley's method to get closer\n\t// this gives an error less than 10e6, except for some blue hues where the dS/dh is close to infinite\n\t// this should be sufficient for most applications, otherwise do two/three steps\n\n\tlet k_l = +0.3963377774 * a + 0.2158037573 * b;\n\tlet k_m = -0.1055613458 * a - 0.0638541728 * b;\n\tlet k_s = -0.0894841775 * a - 1.291485548 * b;\n\n\t{\n\t\tlet l_ = 1 + S * k_l;\n\t\tlet m_ = 1 + S * k_m;\n\t\tlet s_ = 1 + S * k_s;\n\n\t\tlet l = l_ * l_ * l_;\n\t\tlet m = m_ * m_ * m_;\n\t\tlet s = s_ * s_ * s_;\n\n\t\tlet l_dS = 3 * k_l * l_ * l_;\n\t\tlet m_dS = 3 * k_m * m_ * m_;\n\t\tlet s_dS = 3 * k_s * s_ * s_;\n\n\t\tlet l_dS2 = 6 * k_l * k_l * l_;\n\t\tlet m_dS2 = 6 * k_m * k_m * m_;\n\t\tlet s_dS2 = 6 * k_s * k_s * s_;\n\n\t\tlet f = wl * l + wm * m + ws * s;\n\t\tlet f1 = wl * l_dS + wm * m_dS + ws * s_dS;\n\t\tlet f2 = wl * l_dS2 + wm * m_dS2 + ws * s_dS2;\n\n\t\tS = S - (f * f1) / (f1 * f1 - 0.5 * f * f2);\n\t}\n\n\treturn S;\n}\n\nexport function find_cusp(a, b) {\n\t// First, find the maximum saturation (saturation S = C/L)\n\tlet S_cusp = compute_max_saturation(a, b);\n\n\t// Convert to linear sRGB to find the first point where at least one of r,g or b >= 1:\n\tlet rgb = convertOklabToLrgb({ l: 1, a: S_cusp * a, b: S_cusp * b });\n\tlet L_cusp = Math.cbrt(1 / Math.max(rgb.r, rgb.g, rgb.b));\n\tlet C_cusp = L_cusp * S_cusp;\n\n\treturn [L_cusp, C_cusp];\n}\n\n// Finds intersection of the line defined by\n// L = L0 * (1 - t) + t * L1;\n// C = t * C1;\n// a and b must be normalized so a^2 + b^2 == 1\nfunction find_gamut_intersection(a, b, L1, C1, L0, cusp = null) {\n\tif (!cusp) {\n\t\t// Find the cusp of the gamut triangle\n\t\tcusp = find_cusp(a, b);\n\t}\n\n\t// Find the intersection for upper and lower half seprately\n\tlet t;\n\tif ((L1 - L0) * cusp[1] - (cusp[0] - L0) * C1 <= 0) {\n\t\t// Lower half\n\n\t\tt = (cusp[1] * L0) / (C1 * cusp[0] + cusp[1] * (L0 - L1));\n\t} else {\n\t\t// Upper half\n\n\t\t// First intersect with triangle\n\t\tt = (cusp[1] * (L0 - 1)) / (C1 * (cusp[0] - 1) + cusp[1] * (L0 - L1));\n\n\t\t// Then one step Halley's method\n\t\t{\n\t\t\tlet dL = L1 - L0;\n\t\t\tlet dC = C1;\n\n\t\t\tlet k_l = +0.3963377774 * a + 0.2158037573 * b;\n\t\t\tlet k_m = -0.1055613458 * a - 0.0638541728 * b;\n\t\t\tlet k_s = -0.0894841775 * a - 1.291485548 * b;\n\n\t\t\tlet l_dt = dL + dC * k_l;\n\t\t\tlet m_dt = dL + dC * k_m;\n\t\t\tlet s_dt = dL + dC * k_s;\n\n\t\t\t// If higher accuracy is required, 2 or 3 iterations of the following block can be used:\n\t\t\t{\n\t\t\t\tlet L = L0 * (1 - t) + t * L1;\n\t\t\t\tlet C = t * C1;\n\n\t\t\t\tlet l_ = L + C * k_l;\n\t\t\t\tlet m_ = L + C * k_m;\n\t\t\t\tlet s_ = L + C * k_s;\n\n\t\t\t\tlet l = l_ * l_ * l_;\n\t\t\t\tlet m = m_ * m_ * m_;\n\t\t\t\tlet s = s_ * s_ * s_;\n\n\t\t\t\tlet ldt = 3 * l_dt * l_ * l_;\n\t\t\t\tlet mdt = 3 * m_dt * m_ * m_;\n\t\t\t\tlet sdt = 3 * s_dt * s_ * s_;\n\n\t\t\t\tlet ldt2 = 6 * l_dt * l_dt * l_;\n\t\t\t\tlet mdt2 = 6 * m_dt * m_dt * m_;\n\t\t\t\tlet sdt2 = 6 * s_dt * s_dt * s_;\n\n\t\t\t\tlet r =\n\t\t\t\t\t4.0767416621 * l - 3.3077115913 * m + 0.2309699292 * s - 1;\n\t\t\t\tlet r1 =\n\t\t\t\t\t4.0767416621 * ldt -\n\t\t\t\t\t3.3077115913 * mdt +\n\t\t\t\t\t0.2309699292 * sdt;\n\t\t\t\tlet r2 =\n\t\t\t\t\t4.0767416621 * ldt2 -\n\t\t\t\t\t3.3077115913 * mdt2 +\n\t\t\t\t\t0.2309699292 * sdt2;\n\n\t\t\t\tlet u_r = r1 / (r1 * r1 - 0.5 * r * r2);\n\t\t\t\tlet t_r = -r * u_r;\n\n\t\t\t\tlet g =\n\t\t\t\t\t-1.2684380046 * l + 2.6097574011 * m - 0.3413193965 * s - 1;\n\t\t\t\tlet g1 =\n\t\t\t\t\t-1.2684380046 * ldt +\n\t\t\t\t\t2.6097574011 * mdt -\n\t\t\t\t\t0.3413193965 * sdt;\n\t\t\t\tlet g2 =\n\t\t\t\t\t-1.2684380046 * ldt2 +\n\t\t\t\t\t2.6097574011 * mdt2 -\n\t\t\t\t\t0.3413193965 * sdt2;\n\n\t\t\t\tlet u_g = g1 / (g1 * g1 - 0.5 * g * g2);\n\t\t\t\tlet t_g = -g * u_g;\n\n\t\t\t\tlet b =\n\t\t\t\t\t-0.0041960863 * l - 0.7034186147 * m + 1.707614701 * s - 1;\n\t\t\t\tlet b1 =\n\t\t\t\t\t-0.0041960863 * ldt -\n\t\t\t\t\t0.7034186147 * mdt +\n\t\t\t\t\t1.707614701 * sdt;\n\t\t\t\tlet b2 =\n\t\t\t\t\t-0.0041960863 * ldt2 -\n\t\t\t\t\t0.7034186147 * mdt2 +\n\t\t\t\t\t1.707614701 * sdt2;\n\n\t\t\t\tlet u_b = b1 / (b1 * b1 - 0.5 * b * b2);\n\t\t\t\tlet t_b = -b * u_b;\n\n\t\t\t\tt_r = u_r >= 0 ? t_r : 10e5;\n\t\t\t\tt_g = u_g >= 0 ? t_g : 10e5;\n\t\t\t\tt_b = u_b >= 0 ? t_b : 10e5;\n\n\t\t\t\tt += Math.min(t_r, Math.min(t_g, t_b));\n\t\t\t}\n\t\t}\n\t}\n\n\treturn t;\n}\n\nexport function get_ST_max(a_, b_, cusp = null) {\n\tif (!cusp) {\n\t\tcusp = find_cusp(a_, b_);\n\t}\n\tlet L = cusp[0];\n\tlet C = cusp[1];\n\treturn [C / L, C / (1 - L)];\n}\n\nexport function get_ST_mid(a_, b_) {\n\tlet S =\n\t\t0.11516993 +\n\t\t1 /\n\t\t\t(+7.4477897 +\n\t\t\t\t4.1590124 * b_ +\n\t\t\t\ta_ *\n\t\t\t\t\t(-2.19557347 +\n\t\t\t\t\t\t1.75198401 * b_ +\n\t\t\t\t\t\ta_ *\n\t\t\t\t\t\t\t(-2.13704948 -\n\t\t\t\t\t\t\t\t10.02301043 * b_ +\n\t\t\t\t\t\t\t\ta_ *\n\t\t\t\t\t\t\t\t\t(-4.24894561 +\n\t\t\t\t\t\t\t\t\t\t5.38770819 * b_ +\n\t\t\t\t\t\t\t\t\t\t4.69891013 * a_))));\n\n\tlet T =\n\t\t0.11239642 +\n\t\t1 /\n\t\t\t(+1.6132032 -\n\t\t\t\t0.68124379 * b_ +\n\t\t\t\ta_ *\n\t\t\t\t\t(+0.40370612 +\n\t\t\t\t\t\t0.90148123 * b_ +\n\t\t\t\t\t\ta_ *\n\t\t\t\t\t\t\t(-0.27087943 +\n\t\t\t\t\t\t\t\t0.6122399 * b_ +\n\t\t\t\t\t\t\t\ta_ *\n\t\t\t\t\t\t\t\t\t(+0.00299215 -\n\t\t\t\t\t\t\t\t\t\t0.45399568 * b_ -\n\t\t\t\t\t\t\t\t\t\t0.14661872 * a_))));\n\n\treturn [S, T];\n}\n\nexport function get_Cs(L, a_, b_) {\n\tlet cusp = find_cusp(a_, b_);\n\n\tlet C_max = find_gamut_intersection(a_, b_, L, 1, L, cusp);\n\tlet ST_max = get_ST_max(a_, b_, cusp);\n\n\tlet S_mid =\n\t\t0.11516993 +\n\t\t1 /\n\t\t\t(+7.4477897 +\n\t\t\t\t4.1590124 * b_ +\n\t\t\t\ta_ *\n\t\t\t\t\t(-2.19557347 +\n\t\t\t\t\t\t1.75198401 * b_ +\n\t\t\t\t\t\ta_ *\n\t\t\t\t\t\t\t(-2.13704948 -\n\t\t\t\t\t\t\t\t10.02301043 * b_ +\n\t\t\t\t\t\t\t\ta_ *\n\t\t\t\t\t\t\t\t\t(-4.24894561 +\n\t\t\t\t\t\t\t\t\t\t5.38770819 * b_ +\n\t\t\t\t\t\t\t\t\t\t4.69891013 * a_))));\n\n\tlet T_mid =\n\t\t0.11239642 +\n\t\t1 /\n\t\t\t(+1.6132032 -\n\t\t\t\t0.68124379 * b_ +\n\t\t\t\ta_ *\n\t\t\t\t\t(+0.40370612 +\n\t\t\t\t\t\t0.90148123 * b_ +\n\t\t\t\t\t\ta_ *\n\t\t\t\t\t\t\t(-0.27087943 +\n\t\t\t\t\t\t\t\t0.6122399 * b_ +\n\t\t\t\t\t\t\t\ta_ *\n\t\t\t\t\t\t\t\t\t(+0.00299215 -\n\t\t\t\t\t\t\t\t\t\t0.45399568 * b_ -\n\t\t\t\t\t\t\t\t\t\t0.14661872 * a_))));\n\n\tlet k = C_max / Math.min(L * ST_max[0], (1 - L) * ST_max[1]);\n\n\tlet C_a = L * S_mid;\n\tlet C_b = (1 - L) * T_mid;\n\tlet C_mid =\n\t\t0.9 *\n\t\tk *\n\t\tMath.sqrt(\n\t\t\tMath.sqrt(\n\t\t\t\t1 / (1 / (C_a * C_a * C_a * C_a) + 1 / (C_b * C_b * C_b * C_b))\n\t\t\t)\n\t\t);\n\n\tC_a = L * 0.4;\n\tC_b = (1 - L) * 0.8;\n\tlet C_0 = Math.sqrt(1 / (1 / (C_a * C_a) + 1 / (C_b * C_b)));\n\treturn [C_0, C_mid, C_max];\n}\n", "/*\n\tAdapted from code by <PERSON><PERSON><PERSON><PERSON>,\n\treleased under the MIT license:\n\n\tCopyright (c) 2021 B<PERSON><PERSON><PERSON>\n\n\tPermission is hereby granted, free of charge, to any person obtaining a copy of\n\tthis software and associated documentation files (the \"Software\"), to deal in\n\tthe Software without restriction, including without limitation the rights to\n\tuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n\tof the Software, and to permit persons to whom the Software is furnished to do\n\tso, subject to the following conditions:\n\n\tThe above copyright notice and this permission notice shall be included in all\n\tcopies or substantial portions of the Software.\n\n\tTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n\tIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n\tFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n\tAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n\tLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n\tOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n\tSOFTWARE.\n */\n\nimport normalizeHue from '../util/normalizeHue.js';\nimport { get_Cs, toe } from './helpers.js';\n\nexport default function convertOklabToOkhsl(lab) {\n\tconst l = lab.l !== undefined ? lab.l : 0;\n\tconst a = lab.a !== undefined ? lab.a : 0;\n\tconst b = lab.b !== undefined ? lab.b : 0;\n\n\tconst ret = { mode: 'okhsl', l: toe(l) };\n\n\tif (lab.alpha !== undefined) {\n\t\tret.alpha = lab.alpha;\n\t}\n\tlet c = Math.sqrt(a * a + b * b);\n\tif (!c) {\n\t\tret.s = 0;\n\t\treturn ret;\n\t}\n\tlet [C_0, C_mid, C_max] = get_Cs(l, a / c, b / c);\n\tlet s;\n\tif (c < C_mid) {\n\t\tlet k_0 = 0;\n\t\tlet k_1 = 0.8 * C_0;\n\t\tlet k_2 = 1 - k_1 / C_mid;\n\t\tlet t = (c - k_0) / (k_1 + k_2 * (c - k_0));\n\t\ts = t * 0.8;\n\t} else {\n\t\tlet k_0 = C_mid;\n\t\tlet k_1 = (0.2 * C_mid * C_mid * 1.25 * 1.25) / C_0;\n\t\tlet k_2 = 1 - k_1 / (C_max - C_mid);\n\t\tlet t = (c - k_0) / (k_1 + k_2 * (c - k_0));\n\t\ts = 0.8 + 0.2 * t;\n\t}\n\tif (s) {\n\t\tret.s = s;\n\t\tret.h = normalizeHue((Math.atan2(b, a) * 180) / Math.PI);\n\t}\n\treturn ret;\n}\n", "/*\n\tAdapted from code by <PERSON><PERSON><PERSON><PERSON>,\n\treleased under the MIT license:\n\n\tCopyright (c) 2021 <PERSON><PERSON><PERSON><PERSON>\n\n\tPermission is hereby granted, free of charge, to any person obtaining a copy of\n\tthis software and associated documentation files (the \"Software\"), to deal in\n\tthe Software without restriction, including without limitation the rights to\n\tuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n\tof the Software, and to permit persons to whom the Software is furnished to do\n\tso, subject to the following conditions:\n\n\tThe above copyright notice and this permission notice shall be included in all\n\tcopies or substantial portions of the Software.\n\n\tTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n\tIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n\tFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n\tAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n\tLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n\tOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n\tSOFTWARE.\n */\n\nimport { toe_inv, get_Cs } from './helpers.js';\n\nexport default function convertOkhslToOklab(hsl) {\n\tlet h = hsl.h !== undefined ? hsl.h : 0;\n\tlet s = hsl.s !== undefined ? hsl.s : 0;\n\tlet l = hsl.l !== undefined ? hsl.l : 0;\n\n\tconst ret = { mode: 'oklab', l: toe_inv(l) };\n\n\tif (hsl.alpha !== undefined) {\n\t\tret.alpha = hsl.alpha;\n\t}\n\n\tif (!s || l === 1) {\n\t\tret.a = ret.b = 0;\n\t\treturn ret;\n\t}\n\n\tlet a_ = Math.cos((h / 180) * Math.PI);\n\tlet b_ = Math.sin((h / 180) * Math.PI);\n\tlet [C_0, C_mid, C_max] = get_Cs(ret.l, a_, b_);\n\tlet t, k_0, k_1, k_2;\n\tif (s < 0.8) {\n\t\tt = 1.25 * s;\n\t\tk_0 = 0;\n\t\tk_1 = 0.8 * C_0;\n\t\tk_2 = 1 - k_1 / C_mid;\n\t} else {\n\t\tt = 5 * (s - 0.8);\n\t\tk_0 = C_mid;\n\t\tk_1 = (0.2 * C_mid * C_mid * 1.25 * 1.25) / C_0;\n\t\tk_2 = 1 - k_1 / (C_max - C_mid);\n\t}\n\tlet C = k_0 + (t * k_1) / (1 - k_2 * t);\n\tret.a = C * a_;\n\tret.b = C * b_;\n\n\treturn ret;\n}\n", "import convertRgbToOklab from '../oklab/convertRgbToOklab.js';\nimport convertOklabToRgb from '../oklab/convertOklabToRgb.js';\nimport convertOklabToOkhsl from './convertOklabToOkhsl.js';\nimport convertOkhslToOklab from './convertOkhslToOklab.js';\n\nimport modeHsl from '../hsl/definition.js';\n\nconst modeOkhsl = {\n\t...modeHsl,\n\tmode: 'okhsl',\n\tchannels: ['h', 's', 'l', 'alpha'],\n\tparse: ['--okhsl'],\n\tserialize: '--okhsl',\n\tfromMode: {\n\t\toklab: convertOklabToOkhsl,\n\t\trgb: c => convertOklabToOkhsl(convertRgbToOklab(c))\n\t},\n\ttoMode: {\n\t\toklab: convertOkhslToOklab,\n\t\trgb: c => convertOklabToRgb(convertOkhslToOklab(c))\n\t}\n};\n\nexport default modeOkhsl;\n", "/*\n\tAdapted from code by <PERSON><PERSON><PERSON><PERSON>,\n\treleased under the MIT license:\n\n\tCopyright (c) 2021 B<PERSON><PERSON><PERSON>\n\n\tPermission is hereby granted, free of charge, to any person obtaining a copy of\n\tthis software and associated documentation files (the \"Software\"), to deal in\n\tthe Software without restriction, including without limitation the rights to\n\tuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n\tof the Software, and to permit persons to whom the Software is furnished to do\n\tso, subject to the following conditions:\n\n\tThe above copyright notice and this permission notice shall be included in all\n\tcopies or substantial portions of the Software.\n\n\tTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n\tIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n\tFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n\tAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n\tLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n\tOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n\tSOFTWARE.\n */\n\nimport normalizeHue from '../util/normalizeHue.js';\nimport convertOklabToLrgb from '../oklab/convertOklabToLrgb.js';\nimport { get_ST_max, toe_inv, toe } from '../okhsl/helpers.js';\n\nexport default function convertOklabToOkhsv(lab) {\n\tlet l = lab.l !== undefined ? lab.l : 0;\n\tlet a = lab.a !== undefined ? lab.a : 0;\n\tlet b = lab.b !== undefined ? lab.b : 0;\n\n\tlet c = Math.sqrt(a * a + b * b);\n\n\t// TODO: c = 0\n\tlet a_ = c ? a / c : 1;\n\tlet b_ = c ? b / c : 1;\n\n\tlet [S_max, T] = get_ST_max(a_, b_);\n\tlet S_0 = 0.5;\n\tlet k = 1 - S_0 / S_max;\n\n\tlet t = T / (c + l * T);\n\tlet L_v = t * l;\n\tlet C_v = t * c;\n\n\tlet L_vt = toe_inv(L_v);\n\tlet C_vt = (C_v * L_vt) / L_v;\n\n\tlet rgb_scale = convertOklabToLrgb({ l: L_vt, a: a_ * C_vt, b: b_ * C_vt });\n\tlet scale_L = Math.cbrt(\n\t\t1 / Math.max(rgb_scale.r, rgb_scale.g, rgb_scale.b, 0)\n\t);\n\n\tl = l / scale_L;\n\tc = ((c / scale_L) * toe(l)) / l;\n\tl = toe(l);\n\n\tconst ret = {\n\t\tmode: 'okhsv',\n\t\ts: c ? ((S_0 + T) * C_v) / (T * S_0 + T * k * C_v) : 0,\n\t\tv: l ? l / L_v : 0\n\t};\n\tif (ret.s) {\n\t\tret.h = normalizeHue((Math.atan2(b, a) * 180) / Math.PI);\n\t}\n\tif (lab.alpha !== undefined) {\n\t\tret.alpha = lab.alpha;\n\t}\n\treturn ret;\n}\n", "/*\n\tCopyright (c) 2021 <PERSON><PERSON><PERSON><PERSON>\n\n\tPermission is hereby granted, free of charge, to any person obtaining a copy of\n\tthis software and associated documentation files (the \"Software\"), to deal in\n\tthe Software without restriction, including without limitation the rights to\n\tuse, copy, modify, merge, publish, distribute, sublicense, and/or sell copies\n\tof the Software, and to permit persons to whom the Software is furnished to do\n\tso, subject to the following conditions:\n\n\tThe above copyright notice and this permission notice shall be included in all\n\tcopies or substantial portions of the Software.\n\n\tTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n\tIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n\tFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n\tAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n\tLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n\tOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n\tSOFTWARE.\n */\n\nimport convertOklabToLrgb from '../oklab/convertOklabToLrgb.js';\nimport { get_ST_max, toe_inv } from '../okhsl/helpers.js';\n\nexport default function convertOkhsvToOklab(hsv) {\n\tconst ret = { mode: 'oklab' };\n\tif (hsv.alpha !== undefined) {\n\t\tret.alpha = hsv.alpha;\n\t}\n\n\tconst h = hsv.h !== undefined ? hsv.h : 0;\n\tconst s = hsv.s !== undefined ? hsv.s : 0;\n\tconst v = hsv.v !== undefined ? hsv.v : 0;\n\n\tconst a_ = Math.cos((h / 180) * Math.PI);\n\tconst b_ = Math.sin((h / 180) * Math.PI);\n\n\tconst [S_max, T] = get_ST_max(a_, b_);\n\tconst S_0 = 0.5;\n\tconst k = 1 - S_0 / S_max;\n\tconst L_v = 1 - (s * S_0) / (S_0 + T - T * k * s);\n\tconst C_v = (s * T * S_0) / (S_0 + T - T * k * s);\n\n\tconst L_vt = toe_inv(L_v);\n\tconst C_vt = (C_v * L_vt) / L_v;\n\tconst rgb_scale = convertOklabToLrgb({\n\t\tl: L_vt,\n\t\ta: a_ * C_vt,\n\t\tb: b_ * C_vt\n\t});\n\tconst scale_L = Math.cbrt(\n\t\t1 / Math.max(rgb_scale.r, rgb_scale.g, rgb_scale.b, 0)\n\t);\n\n\tconst L_new = toe_inv(v * L_v);\n\tconst C = (C_v * L_new) / L_v;\n\n\tret.l = L_new * scale_L;\n\tret.a = C * a_ * scale_L;\n\tret.b = C * b_ * scale_L;\n\n\treturn ret;\n}\n", "import convertRgbToOklab from '../oklab/convertRgbToOklab.js';\nimport convertOklabToRgb from '../oklab/convertOklabToRgb.js';\nimport convertOklabToOkhsv from './convertOklabToOkhsv.js';\nimport convertOkhsvToOklab from './convertOkhsvToOklab.js';\n\nimport modeHsv from '../hsv/definition.js';\n\nconst modeOkhsv = {\n\t...modeHsv,\n\tmode: 'okhsv',\n\tchannels: ['h', 's', 'v', 'alpha'],\n\tparse: ['--okhsv'],\n\tserialize: '--okhsv',\n\tfromMode: {\n\t\toklab: convertOklabToOkhsv,\n\t\trgb: c => convertOklabToOkhsv(convertRgbToOklab(c))\n\t},\n\ttoMode: {\n\t\toklab: convertOkhsvToOklab,\n\t\trgb: c => convertOklabToRgb(convertOkhsvToOklab(c))\n\t}\n};\n\nexport default modeOkhsv;\n", "import { Tok } from '../parse.js';\n\nfunction parseOklab(color, parsed) {\n\tif (!parsed || parsed[0] !== 'oklab') {\n\t\treturn undefined;\n\t}\n\tconst res = { mode: 'oklab' };\n\tconst [, l, a, b, alpha] = parsed;\n\tif (l.type === Tok.Hue || a.type === Tok.Hue || b.type === Tok.Hue) {\n\t\treturn undefined;\n\t}\n\tif (l.type !== Tok.None) {\n\t\tres.l = Math.min(\n\t\t\tMath.max(0, l.type === Tok.Number ? l.value : l.value / 100),\n\t\t\t1\n\t\t);\n\t}\n\tif (a.type !== Tok.None) {\n\t\tres.a = a.type === Tok.Number ? a.value : (a.value * 0.4) / 100;\n\t}\n\tif (b.type !== Tok.None) {\n\t\tres.b = b.type === Tok.Number ? b.value : (b.value * 0.4) / 100;\n\t}\n\tif (alpha.type !== Tok.None) {\n\t\tres.alpha = Math.min(\n\t\t\t1,\n\t\t\tMath.max(\n\t\t\t\t0,\n\t\t\t\talpha.type === Tok.Number ? alpha.value : alpha.value / 100\n\t\t\t)\n\t\t);\n\t}\n\n\treturn res;\n}\n\nexport default parseOklab;\n", "import convertOklabToLrgb from './convertOklabToLrgb.js';\nimport convertLrgbToOklab from './convertLrgbToOklab.js';\nimport convertRgbToOklab from './convertRgbToOklab.js';\nimport convertOklabToRgb from './convertOklabToRgb.js';\nimport parseOklab from './parseOklab.js';\n\nimport lab from '../lab/definition.js';\n\n/*\n\tOklab, a perceptual color space for image processing by <PERSON><PERSON><PERSON><PERSON>\n\tReference: https://bottosson.github.io/posts/oklab/\n */\n\nconst definition = {\n\t...lab,\n\tmode: 'oklab',\n\n\ttoMode: {\n\t\tlrgb: convertOklabToLrgb,\n\t\trgb: convertOklabToRgb\n\t},\n\n\tfromMode: {\n\t\tlrgb: convertLrgbToOklab,\n\t\trgb: convertRgbToOklab\n\t},\n\n\tranges: {\n\t\tl: [0, 1],\n\t\ta: [-0.4, 0.4],\n\t\tb: [-0.4, 0.4]\n\t},\n\n\tparse: [parseOklab],\n\tserialize: c =>\n\t\t`oklab(${c.l !== undefined ? c.l : 'none'} ${\n\t\t\tc.a !== undefined ? c.a : 'none'\n\t\t} ${c.b !== undefined ? c.b : 'none'}${\n\t\t\tc.alpha < 1 ? ` / ${c.alpha}` : ''\n\t\t})`\n};\n\nexport default definition;\n", "import { Tok } from '../parse.js';\n\nfunction parseOklch(color, parsed) {\n\tif (!parsed || parsed[0] !== 'oklch') {\n\t\treturn undefined;\n\t}\n\tconst res = { mode: 'oklch' };\n\tconst [, l, c, h, alpha] = parsed;\n\tif (l.type !== Tok.None) {\n\t\tif (l.type === Tok.Hue) {\n\t\t\treturn undefined;\n\t\t}\n\t\tres.l = Math.min(\n\t\t\tMath.max(0, l.type === Tok.Number ? l.value : l.value / 100),\n\t\t\t1\n\t\t);\n\t}\n\tif (c.type !== Tok.None) {\n\t\tres.c = Math.max(\n\t\t\t0,\n\t\t\tc.type === Tok.Number ? c.value : (c.value * 0.4) / 100\n\t\t);\n\t}\n\tif (h.type !== Tok.None) {\n\t\tif (h.type === Tok.Percentage) {\n\t\t\treturn undefined;\n\t\t}\n\t\tres.h = h.value;\n\t}\n\tif (alpha.type !== Tok.None) {\n\t\tres.alpha = Math.min(\n\t\t\t1,\n\t\t\tMath.max(\n\t\t\t\t0,\n\t\t\t\talpha.type === Tok.Number ? alpha.value : alpha.value / 100\n\t\t\t)\n\t\t);\n\t}\n\n\treturn res;\n}\n\nexport default parseOklch;\n", "import lch from '../lch/definition.js';\nimport convertLabToLch from '../lch/convertLabToLch.js';\nimport convertLchToLab from '../lch/convertLchToLab.js';\nimport convertOklabToRgb from '../oklab/convertOklabToRgb.js';\nimport convertRgbToOklab from '../oklab/convertRgbToOklab.js';\nimport parseOklch from './parseOklch.js';\n\nconst definition = {\n\t...lch,\n\tmode: 'oklch',\n\n\ttoMode: {\n\t\toklab: c => convertLchToLab(c, 'oklab'),\n\t\trgb: c => convertOklabToRgb(convertLchToLab(c, 'oklab'))\n\t},\n\n\tfromMode: {\n\t\trgb: c => convertLabToLch(convertRgbToOklab(c), 'oklch'),\n\t\toklab: c => convertLabToLch(c, 'oklch')\n\t},\n\n\tparse: [parseOklch],\n\tserialize: c =>\n\t\t`oklch(${c.l !== undefined ? c.l : 'none'} ${\n\t\t\tc.c !== undefined ? c.c : 'none'\n\t\t} ${c.h !== undefined ? c.h : 'none'}${\n\t\t\tc.alpha < 1 ? ` / ${c.alpha}` : ''\n\t\t})`,\n\n\tranges: {\n\t\tl: [0, 1],\n\t\tc: [0, 0.4],\n\t\th: [0, 360]\n\t}\n};\n\nexport default definition;\n", "/*\n\tConvert Display P3 values to CIE XYZ D65\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n*/\n\nimport convertRgbToLrgb from '../lrgb/convertRgbToLrgb.js';\n\nconst convertP3ToXyz65 = rgb => {\n\tlet { r, g, b, alpha } = convertRgbToLrgb(rgb);\n\tlet res = {\n\t\tmode: 'xyz65',\n\t\tx:\n\t\t\t0.486570948648216 * r +\n\t\t\t0.265667693169093 * g +\n\t\t\t0.1982172852343625 * b,\n\t\ty:\n\t\t\t0.2289745640697487 * r +\n\t\t\t0.6917385218365062 * g +\n\t\t\t0.079286914093745 * b,\n\t\tz: 0.0 * r + 0.0451133818589026 * g + 1.043944368900976 * b\n\t};\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertP3ToXyz65;\n", "/*\n\tCIE XYZ D65 values to Display P3.\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n*/\n\nimport convertLrgbToRgb from '../lrgb/convertLrgbToRgb.js';\n\nconst convertXyz65ToP3 = ({ x, y, z, alpha }) => {\n\tif (x === undefined) x = 0;\n\tif (y === undefined) y = 0;\n\tif (z === undefined) z = 0;\n\tlet res = convertLrgbToRgb(\n\t\t{\n\t\t\tr:\n\t\t\t\tx * 2.4934969119414263 -\n\t\t\t\ty * 0.9313836179191242 -\n\t\t\t\t0.402710784450717 * z,\n\t\t\tg:\n\t\t\t\tx * -0.8294889695615749 +\n\t\t\t\ty * 1.7626640603183465 +\n\t\t\t\t0.0236246858419436 * z,\n\t\t\tb:\n\t\t\t\tx * 0.0358458302437845 -\n\t\t\t\ty * 0.0761723892680418 +\n\t\t\t\t0.9568845240076871 * z\n\t\t},\n\t\t'p3'\n\t);\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertXyz65ToP3;\n", "import rgb from '../rgb/definition.js';\nimport convertP3ToXyz65 from './convertP3ToXyz65.js';\nimport convertXyz65ToP3 from './convertXyz65ToP3.js';\nimport convertRgbToXyz65 from '../xyz65/convertRgbToXyz65.js';\nimport convertXyz65ToRgb from '../xyz65/convertXyz65ToRgb.js';\n\nconst definition = {\n\t...rgb,\n\tmode: 'p3',\n\tparse: ['display-p3'],\n\tserialize: 'display-p3',\n\n\tfromMode: {\n\t\trgb: color => convertXyz65ToP3(convertRgbToXyz65(color)),\n\t\txyz65: convertXyz65ToP3\n\t},\n\n\ttoMode: {\n\t\trgb: color => convertXyz65ToRgb(convertP3ToXyz65(color)),\n\t\txyz65: convertP3ToXyz65\n\t}\n};\n\nexport default definition;\n", "/*\n\tConvert CIE XYZ D50 values to ProPhoto RGB\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n*/\n\nconst gamma = v => {\n\tlet abs = Math.abs(v);\n\tif (abs >= 1 / 512) {\n\t\treturn Math.sign(v) * Math.pow(abs, 1 / 1.8);\n\t}\n\treturn 16 * v;\n};\n\nconst convertXyz50ToProphoto = ({ x, y, z, alpha }) => {\n\tif (x === undefined) x = 0;\n\tif (y === undefined) y = 0;\n\tif (z === undefined) z = 0;\n\tlet res = {\n\t\tmode: 'prophoto',\n\t\tr: gamma(\n\t\t\tx * 1.3457868816471585 -\n\t\t\t\ty * 0.2555720873797946 -\n\t\t\t\t0.0511018649755453 * z\n\t\t),\n\t\tg: gamma(\n\t\t\tx * -0.5446307051249019 +\n\t\t\t\ty * 1.5082477428451466 +\n\t\t\t\t0.0205274474364214 * z\n\t\t),\n\t\tb: gamma(x * 0.0 + y * 0.0 + 1.2119675456389452 * z)\n\t};\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertXyz50ToProphoto;\n", "/*\n\tConvert ProPhoto RGB values to CIE XYZ D50\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n*/\n\nconst linearize = (v = 0) => {\n\tlet abs = Math.abs(v);\n\tif (abs >= 16 / 512) {\n\t\treturn Math.sign(v) * Math.pow(abs, 1.8);\n\t}\n\treturn v / 16;\n};\n\nconst convertProphotoToXyz50 = prophoto => {\n\tlet r = linearize(prophoto.r);\n\tlet g = linearize(prophoto.g);\n\tlet b = linearize(prophoto.b);\n\tlet res = {\n\t\tmode: 'xyz50',\n\t\tx:\n\t\t\t0.7977666449006423 * r +\n\t\t\t0.1351812974005331 * g +\n\t\t\t0.0313477341283922 * b,\n\t\ty:\n\t\t\t0.2880748288194013 * r +\n\t\t\t0.7118352342418731 * g +\n\t\t\t0.0000899369387256 * b,\n\t\tz: 0 * r + 0 * g + 0.8251046025104602 * b\n\t};\n\tif (prophoto.alpha !== undefined) {\n\t\tres.alpha = prophoto.alpha;\n\t}\n\treturn res;\n};\n\nexport default convertProphotoToXyz50;\n", "import rgb from '../rgb/definition.js';\n\nimport convertXyz50ToProphoto from './convertXyz50ToProphoto.js';\nimport convertProphotoToXyz50 from './convertProphotoToXyz50.js';\n\nimport convertXyz50ToRgb from '../xyz50/convertXyz50ToRgb.js';\nimport convertRgbToXyz50 from '../xyz50/convertRgbToXyz50.js';\n\n/*\n\tProPhoto RGB Color space\n\n\tReferences:\n\t\t* https://en.wikipedia.org/wiki/ProPhoto_RGB_color_space\n */\n\nconst definition = {\n\t...rgb,\n\tmode: 'prophoto',\n\tparse: ['prophoto-rgb'],\n\tserialize: 'prophoto-rgb',\n\n\tfromMode: {\n\t\txyz50: convertXyz50ToProphoto,\n\t\trgb: color => convertXyz50ToProphoto(convertRgbToXyz50(color))\n\t},\n\n\ttoMode: {\n\t\txyz50: convertProphotoToXyz50,\n\t\trgb: color => convertXyz50ToRgb(convertProphotoToXyz50(color))\n\t}\n};\n\nexport default definition;\n", "/*\n\tConvert CIE XYZ D65 values to Rec. 2020\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\t\t* https://www.itu.int/rec/R-REC-BT.2020/en\n*/\n\nconst α = 1.09929682680944;\nconst β = 0.018053968510807;\nconst gamma = v => {\n\tconst abs = Math.abs(v);\n\tif (abs > β) {\n\t\treturn (Math.sign(v) || 1) * (α * Math.pow(abs, 0.45) - (α - 1));\n\t}\n\treturn 4.5 * v;\n};\n\nconst convertXyz65ToRec2020 = ({ x, y, z, alpha }) => {\n\tif (x === undefined) x = 0;\n\tif (y === undefined) y = 0;\n\tif (z === undefined) z = 0;\n\tlet res = {\n\t\tmode: 'rec2020',\n\t\tr: gamma(\n\t\t\tx * 1.7166511879712683 -\n\t\t\t\ty * 0.3556707837763925 -\n\t\t\t\t0.2533662813736599 * z\n\t\t),\n\t\tg: gamma(\n\t\t\tx * -0.6666843518324893 +\n\t\t\t\ty * 1.6164812366349395 +\n\t\t\t\t0.0157685458139111 * z\n\t\t),\n\t\tb: gamma(\n\t\t\tx * 0.0176398574453108 -\n\t\t\t\ty * 0.0427706132578085 +\n\t\t\t\t0.9421031212354739 * z\n\t\t)\n\t};\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertXyz65ToRec2020;\n", "/*\n\tConvert Rec. 2020 values to CIE XYZ D65\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\t\t* https://www.itu.int/rec/R-REC-BT.2020/en\n*/\n\nconst α = 1.09929682680944;\nconst β = 0.018053968510807;\n\nconst linearize = (v = 0) => {\n\tlet abs = Math.abs(v);\n\tif (abs < β * 4.5) {\n\t\treturn v / 4.5;\n\t}\n\treturn (Math.sign(v) || 1) * Math.pow((abs + α - 1) / α, 1 / 0.45);\n};\n\nconst convertRec2020ToXyz65 = rec2020 => {\n\tlet r = linearize(rec2020.r);\n\tlet g = linearize(rec2020.g);\n\tlet b = linearize(rec2020.b);\n\tlet res = {\n\t\tmode: 'xyz65',\n\t\tx:\n\t\t\t0.6369580483012911 * r +\n\t\t\t0.1446169035862083 * g +\n\t\t\t0.1688809751641721 * b,\n\t\ty:\n\t\t\t0.262700212011267 * r +\n\t\t\t0.6779980715188708 * g +\n\t\t\t0.059301716469862 * b,\n\t\tz: 0 * r + 0.0280726930490874 * g + 1.0609850577107909 * b\n\t};\n\tif (rec2020.alpha !== undefined) {\n\t\tres.alpha = rec2020.alpha;\n\t}\n\treturn res;\n};\n\nexport default convertRec2020ToXyz65;\n", "import rgb from '../rgb/definition.js';\n\nimport convertXyz65ToRec2020 from './convertXyz65ToRec2020.js';\nimport convertRec2020ToXyz65 from './convertRec2020ToXyz65.js';\n\nimport convertRgbToXyz65 from '../xyz65/convertRgbToXyz65.js';\nimport convertXyz65ToRgb from '../xyz65/convertXyz65ToRgb.js';\n\nconst definition = {\n\t...rgb,\n\tmode: 'rec2020',\n\n\tfromMode: {\n\t\txyz65: convertXyz65ToRec2020,\n\t\trgb: color => convertXyz65ToRec2020(convertRgbToXyz65(color))\n\t},\n\n\ttoMode: {\n\t\txyz65: convertRec2020ToXyz65,\n\t\trgb: color => convertXyz65ToRgb(convertRec2020ToXyz65(color))\n\t},\n\n\tparse: ['rec2020'],\n\tserialize: 'rec2020'\n};\n\nexport default definition;\n", "export const bias = 0.00379307325527544933;\nexport const bias_cbrt = Math.cbrt(bias);\n", "import convertRgbToLrgb from '../lrgb/convertRgbToLrgb.js';\nimport { bias, bias_cbrt } from './constants.js';\n\nconst transfer = v => Math.cbrt(v) - bias_cbrt;\n\nconst convertRgbToXyb = color => {\n\tconst { r, g, b, alpha } = convertRgbToLrgb(color);\n\tconst l = transfer(0.3 * r + 0.622 * g + 0.078 * b + bias);\n\tconst m = transfer(0.23 * r + 0.692 * g + 0.078 * b + bias);\n\tconst s = transfer(\n\t\t0.24342268924547819 * r +\n\t\t\t0.20476744424496821 * g +\n\t\t\t0.5518098665095536 * b +\n\t\t\tbias\n\t);\n\tconst res = {\n\t\tmode: 'xyb',\n\t\tx: (l - m) / 2,\n\t\ty: (l + m) / 2,\n\t\t/* Apply default chroma from luma (subtract Y from B) */\n\t\tb: s - (l + m) / 2\n\t};\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n};\n\nexport default convertRgbToXyb;\n", "import convertLrgbToRgb from '../lrgb/convertLrgbToRgb.js';\nimport { bias, bias_cbrt } from './constants.js';\n\nconst transfer = v => Math.pow(v + bias_cbrt, 3);\n\nconst convertXybToRgb = ({ x, y, b, alpha }) => {\n\tif (x === undefined) x = 0;\n\tif (y === undefined) y = 0;\n\tif (b === undefined) b = 0;\n\tconst l = transfer(x + y) - bias;\n\tconst m = transfer(y - x) - bias;\n\t/* Account for chroma from luma: add Y back to B */\n\tconst s = transfer(b + y) - bias;\n\n\tconst res = convertLrgbToRgb({\n\t\tr:\n\t\t\t11.*************** * l -\n\t\t\t9.*************** * m -\n\t\t\t0.***************** * s,\n\t\tg:\n\t\t\t-3.**************** * l +\n\t\t\t4.*************** * m -\n\t\t\t0.***************** * s,\n\t\tb:\n\t\t\t-3.**************** * l +\n\t\t\t2.**************** * m +\n\t\t\t1.**************** * s\n\t});\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n};\n\nexport default convertXybToRgb;\n", "import { interpolatorLinear } from '../interpolate/linear.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\nimport convertRgbToXyb from './convertRgbToXyb.js';\nimport convertXybToRgb from './convertXybToRgb.js';\n\n/*\n\tThe XYB color space, used in JPEG XL.\n\tReference: https://ds.jpeg.org/whitepapers/jpeg-xl-whitepaper.pdf\n*/\n\nconst definition = {\n\tmode: 'xyb',\n\tchannels: ['x', 'y', 'b', 'alpha'],\n\tparse: ['--xyb'],\n\tserialize: '--xyb',\n\n\ttoMode: {\n\t\trgb: convertXybToRgb\n\t},\n\n\tfromMode: {\n\t\trgb: convertRgbToXyb\n\t},\n\n\tranges: {\n\t\tx: [-0.0154, 0.0281],\n\t\ty: [0, 0.8453],\n\t\tb: [-0.2778, 0.388]\n\t},\n\n\tinterpolate: {\n\t\tx: interpolatorLinear,\n\t\ty: interpolatorLinear,\n\t\tb: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t}\n};\n\nexport default definition;\n", "/*\n\tThe XYZ D50 color space\n\t-----------------------\n */\n\nimport convertXyz50ToRgb from './convertXyz50ToRgb.js';\nimport convertXyz50ToLab from '../lab/convertXyz50ToLab.js';\nimport convertRgbToXyz50 from './convertRgbToXyz50.js';\nimport convertLabToXyz50 from '../lab/convertLabToXyz50.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\n\nconst definition = {\n\tmode: 'xyz50',\n\tparse: ['xyz-d50'],\n\tserialize: 'xyz-d50',\n\n\ttoMode: {\n\t\trgb: convertXyz50ToRgb,\n\t\tlab: convertXyz50ToLab\n\t},\n\n\tfromMode: {\n\t\trgb: convertRgbToXyz50,\n\t\tlab: convertLabToXyz50\n\t},\n\n\tchannels: ['x', 'y', 'z', 'alpha'],\n\n\tranges: {\n\t\tx: [0, 0.964],\n\t\ty: [0, 0.999],\n\t\tz: [0, 0.825]\n\t},\n\n\tinterpolate: {\n\t\tx: interpolatorLinear,\n\t\ty: interpolatorLinear,\n\t\tz: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t}\n};\n\nexport default definition;\n", "/*\n\tChromatic adaptation of CIE XYZ from D65 to D50 white point\n\tusing the Bradford method.\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_ChromAdapt.html\t\n*/\n\nconst convertXyz65ToXyz50 = xyz65 => {\n\tlet { x, y, z, alpha } = xyz65;\n\tif (x === undefined) x = 0;\n\tif (y === undefined) y = 0;\n\tif (z === undefined) z = 0;\n\tlet res = {\n\t\tmode: 'xyz50',\n\t\tx:\n\t\t\t1.0479298208405488 * x +\n\t\t\t0.0229467933410191 * y -\n\t\t\t0.0501922295431356 * z,\n\t\ty:\n\t\t\t0.0296278156881593 * x +\n\t\t\t0.990434484573249 * y -\n\t\t\t0.0170738250293851 * z,\n\t\tz:\n\t\t\t-0.0092430581525912 * x +\n\t\t\t0.0150551448965779 * y +\n\t\t\t0.7518742899580008 * z\n\t};\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertXyz65ToXyz50;\n", "/*\n\tChromatic adaptation of CIE XYZ from D50 to D65 white point\n\tusing the Bradford method.\n\n\tReferences:\n\t\t* https://drafts.csswg.org/css-color/#color-conversion-code\n\t\t* http://www.brucelindbloom.com/index.html?Eqn_ChromAdapt.html\t\n*/\n\nconst convertXyz50ToXyz65 = xyz50 => {\n\tlet { x, y, z, alpha } = xyz50;\n\tif (x === undefined) x = 0;\n\tif (y === undefined) y = 0;\n\tif (z === undefined) z = 0;\n\tlet res = {\n\t\tmode: 'xyz65',\n\t\tx:\n\t\t\t0.9554734527042182 * x -\n\t\t\t0.0230985368742614 * y +\n\t\t\t0.0632593086610217 * z,\n\t\ty:\n\t\t\t-0.0283697069632081 * x +\n\t\t\t1.0099954580058226 * y +\n\t\t\t0.021041398966943 * z,\n\t\tz:\n\t\t\t0.0123140016883199 * x -\n\t\t\t0.0205076964334779 * y +\n\t\t\t1.3303659366080753 * z\n\t};\n\tif (alpha !== undefined) {\n\t\tres.alpha = alpha;\n\t}\n\treturn res;\n};\n\nexport default convertXyz50ToXyz65;\n", "/*\n\tThe XYZ D65 color space\n\t-----------------------\n */\n\nimport convertXyz65ToRgb from './convertXyz65ToRgb.js';\nimport convertRgbToXyz65 from './convertRgbToXyz65.js';\n\nimport convertXyz65ToXyz50 from './convertXyz65ToXyz50.js';\nimport convertXyz50ToXyz65 from './convertXyz50ToXyz65.js';\n\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\n\nconst definition = {\n\tmode: 'xyz65',\n\n\ttoMode: {\n\t\trgb: convertXyz65ToRgb,\n\t\txyz50: convertXyz65ToXyz50\n\t},\n\n\tfromMode: {\n\t\trgb: convertRgbToXyz65,\n\t\txyz50: convertXyz50ToXyz65\n\t},\n\n\tranges: {\n\t\tx: [0, 0.95],\n\t\ty: [0, 1],\n\t\tz: [0, 1.088]\n\t},\n\n\tchannels: ['x', 'y', 'z', 'alpha'],\n\n\tparse: ['xyz', 'xyz-d65'],\n\tserialize: 'xyz-d65',\n\n\tinterpolate: {\n\t\tx: interpolatorLinear,\n\t\ty: interpolatorLinear,\n\t\tz: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t}\n};\n\nexport default definition;\n", "const convertRgbToYiq = ({ r, g, b, alpha }) => {\n\tif (r === undefined) r = 0;\n\tif (g === undefined) g = 0;\n\tif (b === undefined) b = 0;\n\tconst res = {\n\t\tmode: 'yiq',\n\t\ty: 0.29889531 * r + 0.58662247 * g + 0.11448223 * b,\n\t\ti: 0.59597799 * r - 0.2741761 * g - 0.32180189 * b,\n\t\tq: 0.21147017 * r - 0.52261711 * g + 0.31114694 * b\n\t};\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n};\n\nexport default convertRgbToYiq;\n", "const convertYiqToRgb = ({ y, i, q, alpha }) => {\n\tif (y === undefined) y = 0;\n\tif (i === undefined) i = 0;\n\tif (q === undefined) q = 0;\n\tconst res = {\n\t\tmode: 'rgb',\n\t\tr: y + 0.95608445 * i + 0.6208885 * q,\n\t\tg: y - 0.27137664 * i - 0.6486059 * q,\n\t\tb: y - 1.10561724 * i + 1.70250126 * q\n\t};\n\tif (alpha !== undefined) res.alpha = alpha;\n\treturn res;\n};\n\nexport default convertYiqToRgb;\n", "import convertRgbToYiq from './convertRgbToYiq.js';\nimport convertYiqToRgb from './convertYiqToRgb.js';\nimport { interpolatorLinear } from '../interpolate/linear.js';\nimport { fixupAlpha } from '../fixup/alpha.js';\n\n/*\n\tYIQ Color Space\n\n\tReferences\n\t----------\n\n\tWikipedia:\n\t\thttps://en.wikipedia.org/wiki/YIQ\n\n\t\"Measuring perceived color difference using YIQ NTSC\n\ttransmission color space in mobile applications\"\n\t\t\n\t\tby <PERSON><PERSON>, <PERSON> in:\n\t\tProgramación Matemática y Software (2010) \n\n\tAvailable at:\n\t\t\n\t\thttp://www.progmat.uaem.mx:8080/artVol2Num2/Articulo3Vol2Num2.pdf\n */\n\nconst definition = {\n\tmode: 'yiq',\n\n\ttoMode: {\n\t\trgb: convertYiqToRgb\n\t},\n\n\tfromMode: {\n\t\trgb: convertRgbToYiq\n\t},\n\n\tchannels: ['y', 'i', 'q', 'alpha'],\n\n\tparse: ['--yiq'],\n\tserialize: '--yiq',\n\n\tranges: {\n\t\ti: [-0.595, 0.595],\n\t\tq: [-0.522, 0.522]\n\t},\n\n\tinterpolate: {\n\t\ty: interpolatorLinear,\n\t\ti: interpolatorLinear,\n\t\tq: interpolatorLinear,\n\t\talpha: { use: interpolatorLinear, fixup: fixupAlpha }\n\t}\n};\n\nexport default definition;\n", "// From: https://github.com/d3/d3-format/issues/32\n\nconst r = (value, precision) =>\n\tMath.round(value * (precision = Math.pow(10, precision))) / precision;\n\nconst round =\n\t(precision = 4) =>\n\tvalue =>\n\t\ttypeof value === 'number' ? r(value, precision) : value;\n\nexport default round;\n", "import converter from './converter.js';\nimport round from './round.js';\nimport prepare from './_prepare.js';\nimport { getMode } from './modes.js';\n\nlet twoDecimals = round(2);\n\nconst clamp = value => Math.max(0, Math.min(1, value || 0));\nconst fixup = value => Math.round(clamp(value) * 255);\n\nconst rgb = converter('rgb');\nconst hsl = converter('hsl');\n\nexport const serializeHex = color => {\n\tif (color === undefined) {\n\t\treturn undefined;\n\t}\n\n\tlet r = fixup(color.r);\n\tlet g = fixup(color.g);\n\tlet b = fixup(color.b);\n\n\treturn '#' + ((1 << 24) | (r << 16) | (g << 8) | b).toString(16).slice(1);\n};\n\nexport const serializeHex8 = color => {\n\tif (color === undefined) {\n\t\treturn undefined;\n\t}\n\n\tlet a = fixup(color.alpha !== undefined ? color.alpha : 1);\n\treturn serializeHex(color) + ((1 << 8) | a).toString(16).slice(1);\n};\n\nexport const serializeRgb = color => {\n\tif (color === undefined) {\n\t\treturn undefined;\n\t}\n\n\tlet r = fixup(color.r);\n\tlet g = fixup(color.g);\n\tlet b = fixup(color.b);\n\n\tif (color.alpha === undefined || color.alpha === 1) {\n\t\t// opaque color\n\t\treturn `rgb(${r}, ${g}, ${b})`;\n\t} else {\n\t\t// transparent color\n\t\treturn `rgba(${r}, ${g}, ${b}, ${twoDecimals(clamp(color.alpha))})`;\n\t}\n};\n\nexport const serializeHsl = color => {\n\tif (color === undefined) {\n\t\treturn undefined;\n\t}\n\n\tconst h = twoDecimals(color.h || 0);\n\tconst s = twoDecimals(clamp(color.s) * 100) + '%';\n\tconst l = twoDecimals(clamp(color.l) * 100) + '%';\n\n\tif (color.alpha === undefined || color.alpha === 1) {\n\t\t// opaque color\n\t\treturn `hsl(${h}, ${s}, ${l})`;\n\t} else {\n\t\t// transparent color\n\t\treturn `hsla(${h}, ${s}, ${l}, ${twoDecimals(clamp(color.alpha))})`;\n\t}\n};\n\nexport const formatCss = c => {\n\tconst color = prepare(c);\n\tif (!color) {\n\t\treturn undefined;\n\t}\n\tconst def = getMode(color.mode);\n\tif (!def.serialize || typeof def.serialize === 'string') {\n\t\tlet res = `color(${def.serialize || `--${color.mode}`} `;\n\t\tdef.channels.forEach((ch, i) => {\n\t\t\tif (ch !== 'alpha') {\n\t\t\t\tres +=\n\t\t\t\t\t(i ? ' ' : '') +\n\t\t\t\t\t(color[ch] !== undefined ? color[ch] : 'none');\n\t\t\t}\n\t\t});\n\t\tif (color.alpha !== undefined && color.alpha < 1) {\n\t\t\tres += ` / ${color.alpha}`;\n\t\t}\n\t\treturn res + ')';\n\t}\n\tif (typeof def.serialize === 'function') {\n\t\treturn def.serialize(color);\n\t}\n\treturn undefined;\n};\n\nexport const formatHex = c => serializeHex(rgb(c));\nexport const formatHex8 = c => serializeHex8(rgb(c));\nexport const formatRgb = c => serializeRgb(rgb(c));\nexport const formatHsl = c => serializeHsl(hsl(c));\n", "import converter from './converter.js';\nimport prepare from './_prepare.js';\nimport { getMode } from './modes.js';\n\nconst mapper = (fn, mode = 'rgb', preserve_mode = false) => {\n\tlet channels = mode ? getMode(mode).channels : null;\n\tlet conv = mode ? converter(mode) : prepare;\n\treturn color => {\n\t\tlet conv_color = conv(color);\n\t\tif (!conv_color) {\n\t\t\treturn undefined;\n\t\t}\n\t\tlet res = (channels || getMode(conv_color.mode).channels).reduce(\n\t\t\t(res, ch) => {\n\t\t\t\tlet v = fn(conv_color[ch], ch, conv_color, mode);\n\t\t\t\tif (v !== undefined && !isNaN(v)) {\n\t\t\t\t\tres[ch] = v;\n\t\t\t\t}\n\t\t\t\treturn res;\n\t\t\t},\n\t\t\t{ mode: conv_color.mode }\n\t\t);\n\t\tif (!preserve_mode) {\n\t\t\treturn res;\n\t\t}\n\t\tlet prep = prepare(color);\n\t\tif (prep && prep.mode !== res.mode) {\n\t\t\treturn converter(prep.mode)(res);\n\t\t}\n\t\treturn res;\n\t};\n};\n\nconst mapAlphaMultiply = (v, ch, c) => {\n\tif (ch !== 'alpha') {\n\t\treturn (v || 0) * (c.alpha !== undefined ? c.alpha : 1);\n\t}\n\treturn v;\n};\n\nconst mapAlphaDivide = (v, ch, c) => {\n\tif (ch !== 'alpha' && c.alpha !== 0) {\n\t\treturn (v || 0) / (c.alpha !== undefined ? c.alpha : 1);\n\t}\n\treturn v;\n};\n\nconst mapTransferLinear =\n\t(slope = 1, intercept = 0) =>\n\t(v, ch) => {\n\t\tif (ch !== 'alpha') {\n\t\t\treturn v * slope + intercept;\n\t\t}\n\t\treturn v;\n\t};\n\nconst mapTransferGamma =\n\t(amplitude = 1, exponent = 1, offset = 0) =>\n\t(v, ch) => {\n\t\tif (ch !== 'alpha') {\n\t\t\treturn amplitude * Math.pow(v, exponent) + offset;\n\t\t}\n\t\treturn v;\n\t};\n\nexport {\n\tmapper,\n\tmapAlphaMultiply,\n\tmapAlphaDivide,\n\tmapTransferLinear,\n\tmapTransferGamma\n};\n", "/*\n\tNormalize an array of color stop positions for a gradient\n\tbased on the rules defined in the CSS Images Module 4 spec:\n\n\t1. make the first position 0 and the last position 1 if missing\n\t2. sequences of unpositioned color stops should be spread out evenly\n\t3. no position can be smaller than any of the ones preceding it\n\t\n\tReference: https://drafts.csswg.org/css-images-4/#color-stop-fixup\n\n\tNote: this method does not make a defensive copy of the array\n\tit receives as argument. Instead, it adjusts the values in-place.\n */\nconst normalizePositions = arr => {\n\t// 1. fix up first/last position if missing\n\tif (arr[0] === undefined) {\n\t\tarr[0] = 0;\n\t}\n\tif (arr[arr.length - 1] === undefined) {\n\t\tarr[arr.length - 1] = 1;\n\t}\n\n\tlet i = 1;\n\tlet j;\n\tlet from_idx;\n\tlet from_pos;\n\tlet inc;\n\twhile (i < arr.length) {\n\t\t// 2. fill up undefined positions\n\t\tif (arr[i] === undefined) {\n\t\t\tfrom_idx = i;\n\t\t\tfrom_pos = arr[i - 1];\n\t\t\tj = i;\n\n\t\t\t// find end of `undefined` sequence...\n\t\t\twhile (arr[j] === undefined) j++;\n\n\t\t\t// ...and add evenly-spread positions\n\t\t\tinc = (arr[j] - from_pos) / (j - i + 1);\n\t\t\twhile (i < j) {\n\t\t\t\tarr[i] = from_pos + (i + 1 - from_idx) * inc;\n\t\t\t\ti++;\n\t\t\t}\n\t\t} else if (arr[i] < arr[i - 1]) {\n\t\t\t// 3. make positions increase\n\t\t\tarr[i] = arr[i - 1];\n\t\t}\n\t\ti++;\n\t}\n\treturn arr;\n};\n\nexport default normalizePositions;\n", "// Color interpolation hint exponential function\nconst midpoint = (H = 0.5) => t =>\n\tH <= 0 ? 1 : H >= 1 ? 0 : Math.pow(t, Math.log(0.5) / Math.log(H));\n\nexport default midpoint;\n", "import converter from '../converter.js';\nimport { getMode } from '../modes.js';\nimport normalizePositions from '../util/normalizePositions.js';\nimport easingMidpoint from '../easing/midpoint.js';\nimport { mapper, mapAlphaMultiply, mapAlphaDivide } from '../map.js';\n\nconst isfn = o => typeof o === 'function';\nconst isobj = o => o && typeof o === 'object';\nconst isnum = o => typeof o === 'number';\n\nconst interpolate_fn = (colors, mode = 'rgb', overrides, premap) => {\n\tlet def = getMode(mode);\n\tlet conv = converter(mode);\n\n\tlet conv_colors = [];\n\tlet positions = [];\n\tlet fns = {};\n\n\tcolors.forEach(val => {\n\t\tif (Array.isArray(val)) {\n\t\t\tconv_colors.push(conv(val[0]));\n\t\t\tpositions.push(val[1]);\n\t\t} else if (isnum(val) || isfn(val)) {\n\t\t\t// Color interpolation hint or easing function\n\t\t\tfns[positions.length] = val;\n\t\t} else {\n\t\t\tconv_colors.push(conv(val));\n\t\t\tpositions.push(undefined);\n\t\t}\n\t});\n\n\tnormalizePositions(positions);\n\n\t// override the default interpolators\n\t// from the color space definition with any custom ones\n\tlet fixed = def.channels.reduce((res, ch) => {\n\t\tlet ffn;\n\t\tif (isobj(overrides) && isobj(overrides[ch]) && overrides[ch].fixup) {\n\t\t\tffn = overrides[ch].fixup;\n\t\t} else if (isobj(def.interpolate[ch]) && def.interpolate[ch].fixup) {\n\t\t\tffn = def.interpolate[ch].fixup;\n\t\t} else {\n\t\t\tffn = v => v;\n\t\t}\n\t\tres[ch] = ffn(conv_colors.map(color => color[ch]));\n\t\treturn res;\n\t}, {});\n\n\tif (premap) {\n\t\tlet ccolors = conv_colors.map((color, idx) => {\n\t\t\treturn def.channels.reduce(\n\t\t\t\t(c, ch) => {\n\t\t\t\t\tc[ch] = fixed[ch][idx];\n\t\t\t\t\treturn c;\n\t\t\t\t},\n\t\t\t\t{ mode }\n\t\t\t);\n\t\t});\n\t\tfixed = def.channels.reduce((res, ch) => {\n\t\t\tres[ch] = ccolors.map(c => {\n\t\t\t\tlet v = premap(c[ch], ch, c, mode);\n\t\t\t\treturn isNaN(v) ? undefined : v;\n\t\t\t});\n\t\t\treturn res;\n\t\t}, {});\n\t}\n\n\tlet interpolators = def.channels.reduce((res, ch) => {\n\t\tlet ifn;\n\t\tif (isfn(overrides)) {\n\t\t\tifn = overrides;\n\t\t} else if (isobj(overrides) && isfn(overrides[ch])) {\n\t\t\tifn = overrides[ch];\n\t\t} else if (\n\t\t\tisobj(overrides) &&\n\t\t\tisobj(overrides[ch]) &&\n\t\t\toverrides[ch].use\n\t\t) {\n\t\t\tifn = overrides[ch].use;\n\t\t} else if (isfn(def.interpolate[ch])) {\n\t\t\tifn = def.interpolate[ch];\n\t\t} else if (isobj(def.interpolate[ch])) {\n\t\t\tifn = def.interpolate[ch].use;\n\t\t}\n\n\t\tres[ch] = ifn(fixed[ch]);\n\t\treturn res;\n\t}, {});\n\n\tlet n = conv_colors.length - 1;\n\n\treturn t => {\n\t\t// clamp t to the [0, 1] interval\n\t\tt = Math.min(Math.max(0, t), 1);\n\n\t\tif (t <= positions[0]) {\n\t\t\treturn conv_colors[0];\n\t\t}\n\n\t\tif (t > positions[n]) {\n\t\t\treturn conv_colors[n];\n\t\t}\n\n\t\t// Convert `t` from [0, 1] to `t0` between the appropriate two colors.\n\t\t// First, look for the two colors between which `t` is located.\n\t\t// Note: this can be optimized by searching for the index\n\t\t// through bisection instead of start-to-end.\n\t\tlet idx = 0;\n\t\twhile (positions[idx] < t) idx++;\n\t\tlet start = positions[idx - 1];\n\t\tlet delta = positions[idx] - start;\n\n\t\tlet P = (t - start) / delta;\n\n\t\t// use either the local easing, or the global easing, if any\n\t\tlet fn = fns[idx] || fns[0];\n\t\tif (fn !== undefined) {\n\t\t\tif (isnum(fn)) {\n\t\t\t\tfn = easingMidpoint((fn - start) / delta);\n\t\t\t}\n\t\t\tP = fn(P);\n\t\t}\n\n\t\tlet t0 = (idx - 1 + P) / n;\n\n\t\treturn def.channels.reduce(\n\t\t\t(res, channel) => {\n\t\t\t\tlet val = interpolators[channel](t0);\n\t\t\t\tif (val !== undefined) {\n\t\t\t\t\tres[channel] = val;\n\t\t\t\t}\n\t\t\t\treturn res;\n\t\t\t},\n\t\t\t{ mode }\n\t\t);\n\t};\n};\n\nconst interpolate = (colors, mode = 'rgb', overrides) =>\n\tinterpolate_fn(colors, mode, overrides);\n\nconst interpolateWith =\n\t(premap, postmap) =>\n\t(colors, mode = 'rgb', overrides) => {\n\t\tlet post = postmap ? mapper(postmap, mode) : undefined;\n\t\tlet it = interpolate_fn(colors, mode, overrides, premap);\n\t\treturn post ? t => post(it(t)) : it;\n\t};\n\nconst interpolateWithPremultipliedAlpha = interpolateWith(\n\tmapAlphaMultiply,\n\tmapAlphaDivide\n);\n\nexport { interpolate, interpolateWith, interpolateWithPremultipliedAlpha };\n", "import converter from './converter.js';\nimport prepare from './_prepare.js';\nimport { getMode } from './modes.js';\nimport { differenceEuclidean } from './difference.js';\n\nconst rgb = converter('rgb');\nconst fixup_rgb = c => {\n\tconst res = {\n\t\tmode: c.mode,\n\t\tr: Math.max(0, Math.min(c.r !== undefined ? c.r : 0, 1)),\n\t\tg: Math.max(0, Math.min(c.g !== undefined ? c.g : 0, 1)),\n\t\tb: Math.max(0, Math.min(c.b !== undefined ? c.b : 0, 1))\n\t};\n\tif (c.alpha !== undefined) {\n\t\tres.alpha = c.alpha;\n\t}\n\treturn res;\n};\n\nconst to_displayable_srgb = c => fixup_rgb(rgb(c));\n\nconst inrange_rgb = c => {\n\treturn (\n\t\tc !== undefined &&\n\t\t(c.r === undefined || (c.r >= 0 && c.r <= 1)) &&\n\t\t(c.g === undefined || (c.g >= 0 && c.g <= 1)) &&\n\t\t(c.b === undefined || (c.b >= 0 && c.b <= 1))\n\t);\n};\n\n/*\n\tReturns whether the color is in the sRGB gamut.\n */\nexport function displayable(color) {\n\treturn inrange_rgb(rgb(color));\n}\n\n/*\n\tGiven a color space `mode`, returns a function\n\twith which to check whether a color is \n\tin that color space's gamut.\n */\nexport function inGamut(mode = 'rgb') {\n\tconst { gamut } = getMode(mode);\n\tif (!gamut) {\n\t\treturn color => true;\n\t}\n\tconst conv = converter(typeof gamut === 'string' ? gamut : mode);\n\treturn color => inrange_rgb(conv(color));\n}\n\n/*\n\tObtain a color that's in the sRGB gamut\n\tby converting it to sRGB and clipping the channel values\n\tso that they're within the [0, 1] range.\n\n\tThe result is returned in the color's original color space.\n */\nexport function clampRgb(color) {\n\tcolor = prepare(color);\n\n\t// if the color is undefined or displayable, return it directly\n\tif (color === undefined || displayable(color)) return color;\n\n\t// keep track of color's original mode\n\tlet conv = converter(color.mode);\n\n\treturn conv(to_displayable_srgb(color));\n}\n\n/*\n\tGiven the `mode` color space, returns a function\n\twith which to obtain a color that's in gamut for\n\tthe `mode` color space by clipping the channel values\n\tso that they fit in their respective ranges.\n\n\tIt's similar to `clampRgb`, but works for any \n\tbounded color space (RGB or not) for which \n\tany combination of in-range channel values\n\tproduces an in-gamut color.\n */\nexport function clampGamut(mode = 'rgb') {\n\tconst { gamut } = getMode(mode);\n\tif (!gamut) {\n\t\treturn color => prepare(color);\n\t}\n\tconst destMode = typeof gamut === 'string' ? gamut : mode;\n\tconst destConv = converter(destMode);\n\tconst inDestGamut = inGamut(destMode);\n\treturn color => {\n\t\tconst original = prepare(color);\n\t\tif (!original) {\n\t\t\treturn undefined;\n\t\t}\n\t\tconst converted = destConv(original);\n\t\tif (inDestGamut(converted)) {\n\t\t\treturn original;\n\t\t}\n\t\tconst clamped = fixup_rgb(converted);\n\t\tif (original.mode === clamped.mode) {\n\t\t\treturn clamped;\n\t\t}\n\t\treturn converter(original.mode)(clamped);\n\t};\n}\n\n/*\n\tObtain a color that’s in a RGB gamut (by default sRGB)\n\tby first converting it to `mode` and then finding \n\tthe greatest chroma value that fits the gamut.\n\n\tBy default, the CIELCh color space is used,\n\tbut any color that has a chroma component will do.\n\n\tThe result is returned in the color's original color space.\n */\nexport function clampChroma(color, mode = 'lch', rgbGamut = 'rgb') {\n\tcolor = prepare(color);\n\n\tlet inDestinationGamut =\n\t\trgbGamut === 'rgb' ? displayable : inGamut(rgbGamut);\n\tlet clipToGamut =\n\t\trgbGamut === 'rgb' ? to_displayable_srgb : clampGamut(rgbGamut);\n\n\t// if the color is undefined or displayable, return it directly\n\tif (color === undefined || inDestinationGamut(color)) return color;\n\n\t// keep track of color's original mode\n\tlet conv = converter(color.mode);\n\n\t// convert to the provided `mode` for clamping\n\tcolor = converter(mode)(color);\n\n\t// try with chroma = 0\n\tlet clamped = { ...color, c: 0 };\n\n\t// if not even chroma = 0 is displayable\n\t// fall back to RGB clamping\n\tif (!inDestinationGamut(clamped)) {\n\t\treturn conv(clipToGamut(clamped));\n\t}\n\n\t// By this time we know chroma = 0 is displayable and our current chroma is not.\n\t// Find the displayable chroma through the bisection method.\n\tlet start = 0;\n\tlet end = color.c !== undefined ? color.c : 0;\n\tlet range = getMode(mode).ranges.c;\n\tlet resolution = (range[1] - range[0]) / Math.pow(2, 13);\n\tlet _last_good_c = clamped.c;\n\n\twhile (end - start > resolution) {\n\t\tclamped.c = start + (end - start) * 0.5;\n\t\tif (inDestinationGamut(clamped)) {\n\t\t\t_last_good_c = clamped.c;\n\t\t\tstart = clamped.c;\n\t\t} else {\n\t\t\tend = clamped.c;\n\t\t}\n\t}\n\n\treturn conv(\n\t\tinDestinationGamut(clamped) ? clamped : { ...clamped, c: _last_good_c }\n\t);\n}\n\n/*\n\tObtain a color that's in the `dest` gamut,\n\tby first converting it to the `mode` color space\n\tand then finding the largest chroma that's in gamut,\n\tsimilar to `clampChroma`. \n\n\tThe color returned is in the `dest` color space.\n\n\tTo address the shortcomings of `clampChroma`, which can\n\tsometimes produce colors more desaturated than necessary,\n\tthe test used in the binary search is replaced with\n\t\"is color is roughly in gamut\", by comparing the candidate \n\tto the clipped version (obtained with `clampGamut`).\n\tThe test passes if the colors are not too dissimilar, \n\tjudged by the `delta` color difference function \n\tand an associated `jnd` just-noticeable difference value.\n\n\tThe default arguments for this function correspond to the\n\tgamut mapping algorithm defined in CSS Color Level 4:\n\thttps://drafts.csswg.org/css-color/#css-gamut-mapping\n\n\tTo disable the “roughly in gamut” part, pass either\n\t`null` for the `delta` parameter, or zero for `jnd`.\n */\nexport function toGamut(\n\tdest = 'rgb',\n\tmode = 'oklch',\n\tdelta = differenceEuclidean('oklch'),\n\tjnd = 0.02\n) {\n\tconst destConv = converter(dest);\n\tconst destMode = getMode(dest);\n\n\tif (!destMode.gamut) {\n\t\treturn color => destConv(color);\n\t}\n\n\tconst inDestinationGamut = inGamut(dest);\n\tconst clipToGamut = clampGamut(dest);\n\n\tconst ucs = converter(mode);\n\tconst { ranges } = getMode(mode);\n\n\treturn color => {\n\t\tcolor = prepare(color);\n\t\tif (color === undefined) {\n\t\t\treturn undefined;\n\t\t}\n\t\tconst candidate = { ...ucs(color) };\n\n\t\t// account for missing components\n\t\tif (candidate.l === undefined) candidate.l = 0;\n\t\tif (candidate.c === undefined) candidate.c = 0;\n\n\t\tif (candidate.l >= ranges.l[1]) {\n\t\t\tconst res = { ...destMode.white, mode: dest };\n\t\t\tif (color.alpha !== undefined) {\n\t\t\t\tres.alpha = color.alpha;\n\t\t\t}\n\t\t\treturn res;\n\t\t}\n\t\tif (candidate.l <= ranges.l[0]) {\n\t\t\tconst res = { ...destMode.black, mode: dest };\n\t\t\tif (color.alpha !== undefined) {\n\t\t\t\tres.alpha = color.alpha;\n\t\t\t}\n\t\t\treturn res;\n\t\t}\n\t\tif (inDestinationGamut(candidate)) {\n\t\t\treturn destConv(candidate);\n\t\t}\n\t\tlet start = 0;\n\t\tlet end = candidate.c;\n\t\tlet epsilon = (ranges.c[1] - ranges.c[0]) / 4000; // 0.0001 for oklch()\n\t\tlet clipped = clipToGamut(candidate);\n\t\twhile (end - start > epsilon) {\n\t\t\tcandidate.c = (start + end) * 0.5;\n\t\t\tclipped = clipToGamut(candidate);\n\t\t\tif (\n\t\t\t\tinDestinationGamut(candidate) ||\n\t\t\t\t(delta && jnd > 0 && delta(candidate, clipped) <= jnd)\n\t\t\t) {\n\t\t\t\tstart = candidate.c;\n\t\t\t} else {\n\t\t\t\tend = candidate.c;\n\t\t\t}\n\t\t}\n\t\treturn destConv(inDestinationGamut(candidate) ? candidate : clipped);\n\t};\n}\n", "import converter from './converter.js';\nimport prepare from './_prepare.js';\nimport { lerp } from './interpolate/lerp.js';\n\nlet rgb = converter('rgb');\n\n/*\n\tColor vision deficiency (CVD) simulation based on:\n\n\t\t<PERSON><PERSON> <PERSON>, <PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON>, \n\t\t\"A Physiologically-based Model for Simulation of Color Vision Deficiency\" \n\t\tin IEEE Transactions on Visualization and Computer Graphics, \n\t\tvol. 15, no. 6, pp. 1291-1298, Nov.-Dec. 2009, \n\t\tdoi: 10.1109/TVCG.2009.113.\n\n\tFunctions use precomputed matrices from:\n\n\t\thttps://www.inf.ufrgs.br/~oliveira/pubs_files/CVD_Simulation/CVD_Simulation.html\n\n\tVia the `colorspace` R package documentation:\n\n\t\thttp://colorspace.r-forge.r-project.org/reference/simulate_cvd.html\n */\n\nconst PROT = [\n\t[1.0, 0.0, -0.0, 0.0, 1.0, 0.0, -0.0, -0.0, 1.0],\n\t[\n\t\t0.856167, 0.182038, -0.038205, 0.029342, 0.955115, 0.015544, -0.00288,\n\t\t-0.001563, 1.004443\n\t],\n\t[\n\t\t0.734766, 0.334872, -0.069637, 0.05184, 0.919198, 0.028963, -0.004928,\n\t\t-0.004209, 1.009137\n\t],\n\t[\n\t\t0.630323, 0.465641, -0.095964, 0.069181, 0.890046, 0.040773, -0.006308,\n\t\t-0.007724, 1.014032\n\t],\n\t[\n\t\t0.539009, 0.579343, -0.118352, 0.082546, 0.866121, 0.051332, -0.007136,\n\t\t-0.011959, 1.019095\n\t],\n\t[\n\t\t0.458064, 0.679578, -0.137642, 0.092785, 0.846313, 0.060902, -0.007494,\n\t\t-0.016807, 1.024301\n\t],\n\t[\n\t\t0.38545, 0.769005, -0.154455, 0.100526, 0.829802, 0.069673, -0.007442,\n\t\t-0.02219, 1.029632\n\t],\n\t[\n\t\t0.319627, 0.849633, -0.169261, 0.106241, 0.815969, 0.07779, -0.007025,\n\t\t-0.028051, 1.035076\n\t],\n\t[\n\t\t0.259411, 0.923008, -0.18242, 0.110296, 0.80434, 0.085364, -0.006276,\n\t\t-0.034346, 1.040622\n\t],\n\t[\n\t\t0.203876, 0.990338, -0.194214, 0.112975, 0.794542, 0.092483, -0.005222,\n\t\t-0.041043, 1.046265\n\t],\n\t[\n\t\t0.152286, 1.052583, -0.204868, 0.114503, 0.786281, 0.099216, -0.003882,\n\t\t-0.048116, 1.051998\n\t]\n];\n\nconst DEUTER = [\n\t[1.0, 0.0, -0.0, 0.0, 1.0, 0.0, -0.0, -0.0, 1.0],\n\t[\n\t\t0.866435, 0.177704, -0.044139, 0.049567, 0.939063, 0.01137, -0.003453,\n\t\t0.007233, 0.99622\n\t],\n\t[\n\t\t0.760729, 0.319078, -0.079807, 0.090568, 0.889315, 0.020117, -0.006027,\n\t\t0.013325, 0.992702\n\t],\n\t[\n\t\t0.675425, 0.43385, -0.109275, 0.125303, 0.847755, 0.026942, -0.00795,\n\t\t0.018572, 0.989378\n\t],\n\t[\n\t\t0.605511, 0.52856, -0.134071, 0.155318, 0.812366, 0.032316, -0.009376,\n\t\t0.023176, 0.9862\n\t],\n\t[\n\t\t0.547494, 0.607765, -0.155259, 0.181692, 0.781742, 0.036566, -0.01041,\n\t\t0.027275, 0.983136\n\t],\n\t[\n\t\t0.498864, 0.674741, -0.173604, 0.205199, 0.754872, 0.039929, -0.011131,\n\t\t0.030969, 0.980162\n\t],\n\t[\n\t\t0.457771, 0.731899, -0.18967, 0.226409, 0.731012, 0.042579, -0.011595,\n\t\t0.034333, 0.977261\n\t],\n\t[\n\t\t0.422823, 0.781057, -0.203881, 0.245752, 0.709602, 0.044646, -0.011843,\n\t\t0.037423, 0.974421\n\t],\n\t[\n\t\t0.392952, 0.82361, -0.216562, 0.263559, 0.69021, 0.046232, -0.01191,\n\t\t0.040281, 0.97163\n\t],\n\t[\n\t\t0.367322, 0.860646, -0.227968, 0.280085, 0.672501, 0.047413, -0.01182,\n\t\t0.04294, 0.968881\n\t]\n];\n\nconst TRIT = [\n\t[1.0, 0.0, -0.0, 0.0, 1.0, 0.0, -0.0, -0.0, 1.0],\n\t[\n\t\t0.92667, 0.092514, -0.019184, 0.021191, 0.964503, 0.014306, 0.008437,\n\t\t0.054813, 0.93675\n\t],\n\t[\n\t\t0.89572, 0.13333, -0.02905, 0.029997, 0.9454, 0.024603, 0.013027,\n\t\t0.104707, 0.882266\n\t],\n\t[\n\t\t0.905871, 0.127791, -0.033662, 0.026856, 0.941251, 0.031893, 0.01341,\n\t\t0.148296, 0.838294\n\t],\n\t[\n\t\t0.948035, 0.08949, -0.037526, 0.014364, 0.946792, 0.038844, 0.010853,\n\t\t0.193991, 0.795156\n\t],\n\t[\n\t\t1.017277, 0.027029, -0.044306, -0.006113, 0.958479, 0.047634, 0.006379,\n\t\t0.248708, 0.744913\n\t],\n\t[\n\t\t1.104996, -0.046633, -0.058363, -0.032137, 0.971635, 0.060503, 0.001336,\n\t\t0.317922, 0.680742\n\t],\n\t[\n\t\t1.193214, -0.109812, -0.083402, -0.058496, 0.97941, 0.079086, -0.002346,\n\t\t0.403492, 0.598854\n\t],\n\t[\n\t\t1.257728, -0.139648, -0.118081, -0.078003, 0.975409, 0.102594,\n\t\t-0.003316, 0.501214, 0.502102\n\t],\n\t[\n\t\t1.278864, -0.125333, -0.153531, -0.084748, 0.957674, 0.127074,\n\t\t-0.000989, 0.601151, 0.399838\n\t],\n\t[\n\t\t1.255528, -0.076749, -0.178779, -0.078411, 0.930809, 0.147602, 0.004733,\n\t\t0.691367, 0.3039\n\t]\n];\n\nconst deficiency = (lut, t) => {\n\tlet tt = Math.max(0, Math.min(1, t));\n\tlet i = Math.round(tt / 0.1);\n\tlet w = Math.round(tt % 0.1);\n\tlet arr = lut[i];\n\tif (w > 0 && i < lut.length - 1) {\n\t\tlet arr_2 = lut[i + 1];\n\t\tarr = arr.map((v, idx) => lerp(arr[idx], arr_2[idx], w));\n\t}\n\treturn color => {\n\t\tlet c = prepare(color);\n\t\tif (c === undefined) {\n\t\t\treturn undefined;\n\t\t}\n\t\tlet { r, g, b } = rgb(c);\n\t\tlet ret = {\n\t\t\tmode: 'rgb',\n\t\t\tr: arr[0] * r + arr[1] * g + arr[2] * b,\n\t\t\tg: arr[3] * r + arr[4] * g + arr[5] * b,\n\t\t\tb: arr[6] * r + arr[7] * g + arr[8] * b\n\t\t};\n\t\tif (c.alpha !== undefined) {\n\t\t\tret.alpha = c.alpha;\n\t\t}\n\t\treturn converter(c.mode)(ret);\n\t};\n};\n\nexport const filterDeficiencyProt = (severity = 1) =>\n\tdeficiency(PROT, severity);\nexport const filterDeficiencyDeuter = (severity = 1) =>\n\tdeficiency(DEUTER, severity);\nexport const filterDeficiencyTrit = (severity = 1) =>\n\tdeficiency(TRIT, severity);\n", "import converter from './converter.js';\n\n/*\n\tWCAG luminance\n\tReferences: \n\n\thttps://en.wikipedia.org/wiki/Relative_luminance\n\thttps://github.com/w3c/wcag/issues/236#issuecomment-379526596\n */\nexport function luminance(color) {\n\tlet c = converter('lrgb')(color);\n\treturn 0.2126 * c.r + 0.7152 * c.g + 0.0722 * c.b;\n}\n\n/*\n\tWCAG contrast\n */\nexport function contrast(a, b) {\n\tlet L1 = luminance(a);\n\tlet L2 = luminance(b);\n\treturn (Math.max(L1, L2) + 0.05) / (Math.min(L1, L2) + 0.05);\n}\n", "// Color space definitions\nimport modeA98 from './a98/definition.js';\nimport modeCubehelix from './cubehelix/definition.js';\nimport modeDlab from './dlab/definition.js';\nimport modeDlch from './dlch/definition.js';\nimport modeHsi from './hsi/definition.js';\nimport modeHsl from './hsl/definition.js';\nimport modeHsv from './hsv/definition.js';\nimport modeHwb from './hwb/definition.js';\nimport modeItp from './itp/definition.js';\nimport modeJab from './jab/definition.js';\nimport modeJch from './jch/definition.js';\nimport modeLab from './lab/definition.js';\nimport modeLab65 from './lab65/definition.js';\nimport modeLch from './lch/definition.js';\nimport modeLch65 from './lch65/definition.js';\nimport modeLchuv from './lchuv/definition.js';\nimport modeLrgb from './lrgb/definition.js';\nimport modeLuv from './luv/definition.js';\nimport modeOkhsl from './okhsl/modeOkhsl.js';\nimport modeOkhsv from './okhsv/modeOkhsv.js';\nimport modeOklab from './oklab/definition.js';\nimport modeOklch from './oklch/definition.js';\nimport modeP3 from './p3/definition.js';\nimport modeProphoto from './prophoto/definition.js';\nimport modeRec2020 from './rec2020/definition.js';\nimport modeRgb from './rgb/definition.js';\nimport modeXyb from './xyb/definition.js';\nimport modeXyz50 from './xyz50/definition.js';\nimport modeXyz65 from './xyz65/definition.js';\nimport modeYiq from './yiq/definition.js';\nimport { useMode } from './modes.js';\n\nexport { default as converter } from './converter.js';\n\nexport {\n\tserializeHex,\n\tserializeHex8,\n\tserializeRgb,\n\tserializeHsl,\n\tformatHex,\n\tformatHex8,\n\tformatRgb,\n\tformatHsl,\n\tformatCss\n} from './formatter.js';\n\nexport { default as colorsNamed } from './colors/named.js';\nexport { default as blend } from './blend.js';\nexport { default as random } from './random.js';\n\nexport {\n\tfixupHueShorter,\n\tfixupHueLonger,\n\tfixupHueIncreasing,\n\tfixupHueDecreasing\n} from './fixup/hue.js';\n\nexport { fixupAlpha } from './fixup/alpha.js';\n\nexport {\n\tmapper,\n\tmapAlphaMultiply,\n\tmapAlphaDivide,\n\tmapTransferLinear,\n\tmapTransferGamma\n} from './map.js';\n\nexport { average, averageAngle, averageNumber } from './average.js';\n\nexport { default as round } from './round.js';\nexport {\n\tinterpolate,\n\tinterpolateWith,\n\tinterpolateWithPremultipliedAlpha\n} from './interpolate/interpolate.js';\n\nexport { interpolatorLinear } from './interpolate/linear.js';\n\nexport { interpolatorPiecewise } from './interpolate/piecewise.js';\n\nexport {\n\tinterpolatorSplineBasis,\n\tinterpolatorSplineBasisClosed\n} from './interpolate/splineBasis.js';\n\nexport {\n\tinterpolatorSplineNatural,\n\tinterpolatorSplineNaturalClosed\n} from './interpolate/splineNatural.js';\n\nexport {\n\tinterpolatorSplineMonotone,\n\tinterpolatorSplineMonotone2,\n\tinterpolatorSplineMonotoneClosed\n} from './interpolate/splineMonotone.js';\n\nexport { lerp, unlerp, blerp, trilerp } from './interpolate/lerp.js';\nexport { default as samples } from './samples.js';\nexport {\n\tdisplayable,\n\tinGamut,\n\tclampRgb,\n\tclampChroma,\n\tclampGamut,\n\ttoGamut\n} from './clamp.js';\nexport { default as nearest } from './nearest.js';\nexport { useMode, getMode, useParser, removeParser } from './modes.js';\nexport { default as parse } from './parse.js';\n\nexport {\n\tdifferenceEuclidean,\n\tdifferenceCie76,\n\tdifferenceCie94,\n\tdifferenceCiede2000,\n\tdifferenceCmc,\n\tdifferenceHyab,\n\tdifferenceHueSaturation,\n\tdifferenceHueChroma,\n\tdifferenceHueNaive,\n\tdifferenceKotsarenkoRamos,\n\tdifferenceItp\n} from './difference.js';\n\nexport {\n\tfilterBrightness,\n\tfilterContrast,\n\tfilterSepia,\n\tfilterInvert,\n\tfilterSaturate,\n\tfilterGrayscale,\n\tfilterHueRotate\n} from './filter.js';\n\nexport {\n\tfilterDeficiencyProt,\n\tfilterDeficiencyDeuter,\n\tfilterDeficiencyTrit\n} from './deficiency.js';\n\n// Easings\nexport { default as easingMidpoint } from './easing/midpoint.js';\nexport {\n\teasingSmoothstep,\n\teasingSmoothstepInverse\n} from './easing/smoothstep.js';\nexport { default as easingSmootherstep } from './easing/smootherstep.js';\nexport { default as easingInOutSine } from './easing/inOutSine.js';\nexport { default as easingGamma } from './easing/gamma.js';\n\nexport {\n\tluminance as wcagLuminance,\n\tcontrast as wcagContrast\n} from './wcag.js';\n\nexport { default as parseHsl } from './hsl/parseHsl.js';\nexport { default as parseHwb } from './hwb/parseHwb.js';\nexport { default as parseLab } from './lab/parseLab.js';\nexport { default as parseLch } from './lch/parseLch.js';\nexport { default as parseNamed } from './rgb/parseNamed.js';\nexport { default as parseTransparent } from './rgb/parseTransparent.js';\nexport { default as parseHex } from './rgb/parseHex.js';\nexport { default as parseRgb } from './rgb/parseRgb.js';\nexport { default as parseHslLegacy } from './hsl/parseHslLegacy.js';\nexport { default as parseRgbLegacy } from './rgb/parseRgbLegacy.js';\nexport { default as parseOklab } from './oklab/parseOklab.js';\nexport { default as parseOklch } from './oklch/parseOklch.js';\n\nexport { default as convertA98ToXyz65 } from './a98/convertA98ToXyz65.js';\nexport { default as convertCubehelixToRgb } from './cubehelix/convertCubehelixToRgb.js';\nexport { default as convertDlchToLab65 } from './dlch/convertDlchToLab65.js';\nexport { default as convertHsiToRgb } from './hsi/convertHsiToRgb.js';\nexport { default as convertHslToRgb } from './hsl/convertHslToRgb.js';\nexport { default as convertHsvToRgb } from './hsv/convertHsvToRgb.js';\nexport { default as convertHwbToRgb } from './hwb/convertHwbToRgb.js';\nexport { default as convertItpToXyz65 } from './itp/convertItpToXyz65.js';\nexport { default as convertJabToJch } from './jch/convertJabToJch.js';\nexport { default as convertJabToRgb } from './jab/convertJabToRgb.js';\nexport { default as convertJabToXyz65 } from './jab/convertJabToXyz65.js';\nexport { default as convertJchToJab } from './jch/convertJchToJab.js';\nexport { default as convertLab65ToDlch } from './dlch/convertLab65ToDlch.js';\nexport { default as convertLab65ToRgb } from './lab65/convertLab65ToRgb.js';\nexport { default as convertLab65ToXyz65 } from './lab65/convertLab65ToXyz65.js';\nexport { default as convertLabToLch } from './lch/convertLabToLch.js';\nexport { default as convertLabToRgb } from './lab/convertLabToRgb.js';\nexport { default as convertLabToXyz50 } from './lab/convertLabToXyz50.js';\nexport { default as convertLchToLab } from './lch/convertLchToLab.js';\nexport { default as convertLchuvToLuv } from './lchuv/convertLchuvToLuv.js';\nexport { default as convertLrgbToOklab } from './oklab/convertLrgbToOklab.js';\nexport { default as convertLrgbToRgb } from './lrgb/convertLrgbToRgb.js';\nexport { default as convertLuvToLchuv } from './lchuv/convertLuvToLchuv.js';\nexport { default as convertLuvToXyz50 } from './luv/convertLuvToXyz50.js';\nexport { default as convertOkhslToOklab } from './okhsl/convertOkhslToOklab.js';\nexport { default as convertOkhsvToOklab } from './okhsv/convertOkhsvToOklab.js';\nexport { default as convertOklabToLrgb } from './oklab/convertOklabToLrgb.js';\nexport { default as convertOklabToOkhsl } from './okhsl/convertOklabToOkhsl.js';\nexport { default as convertOklabToOkhsv } from './okhsv/convertOklabToOkhsv.js';\nexport { default as convertOklabToRgb } from './oklab/convertOklabToRgb.js';\nexport { default as convertP3ToXyz65 } from './p3/convertP3ToXyz65.js';\nexport { default as convertProphotoToXyz50 } from './prophoto/convertProphotoToXyz50.js';\nexport { default as convertRec2020ToXyz65 } from './rec2020/convertRec2020ToXyz65.js';\nexport { default as convertRgbToCubehelix } from './cubehelix/convertRgbToCubehelix.js';\nexport { default as convertRgbToHsi } from './hsi/convertRgbToHsi.js';\nexport { default as convertRgbToHsl } from './hsl/convertRgbToHsl.js';\nexport { default as convertRgbToHsv } from './hsv/convertRgbToHsv.js';\nexport { default as convertRgbToHwb } from './hwb/convertRgbToHwb.js';\nexport { default as convertRgbToJab } from './jab/convertRgbToJab.js';\nexport { default as convertRgbToLab } from './lab/convertRgbToLab.js';\nexport { default as convertRgbToLab65 } from './lab65/convertRgbToLab65.js';\nexport { default as convertRgbToLrgb } from './lrgb/convertRgbToLrgb.js';\nexport { default as convertRgbToOklab } from './oklab/convertRgbToOklab.js';\nexport { default as convertRgbToXyb } from './xyb/convertRgbToXyb.js';\nexport { default as convertRgbToXyz50 } from './xyz50/convertRgbToXyz50.js';\nexport { default as convertRgbToXyz65 } from './xyz65/convertRgbToXyz65.js';\nexport { default as convertRgbToYiq } from './yiq/convertRgbToYiq.js';\nexport { default as convertXybToRgb } from './xyb/convertXybToRgb.js';\nexport { default as convertXyz50ToLab } from './lab/convertXyz50ToLab.js';\nexport { default as convertXyz50ToLuv } from './luv/convertXyz50ToLuv.js';\nexport { default as convertXyz50ToProphoto } from './prophoto/convertXyz50ToProphoto.js';\nexport { default as convertXyz50ToRgb } from './xyz50/convertXyz50ToRgb.js';\nexport { default as convertXyz50ToXyz65 } from './xyz65/convertXyz50ToXyz65.js';\nexport { default as convertXyz65ToA98 } from './a98/convertXyz65ToA98.js';\nexport { default as convertXyz65ToItp } from './itp/convertXyz65ToItp.js';\nexport { default as convertXyz65ToJab } from './jab/convertXyz65ToJab.js';\nexport { default as convertXyz65ToLab65 } from './lab65/convertXyz65ToLab65.js';\nexport { default as convertXyz65ToP3 } from './p3/convertXyz65ToP3.js';\nexport { default as convertXyz65ToRec2020 } from './rec2020/convertXyz65ToRec2020.js';\nexport { default as convertXyz65ToRgb } from './xyz65/convertXyz65ToRgb.js';\nexport { default as convertXyz65ToXyz50 } from './xyz65/convertXyz65ToXyz50.js';\nexport { default as convertYiqToRgb } from './yiq/convertYiqToRgb.js';\n\nexport {\n\tmodeA98,\n\tmodeCubehelix,\n\tmodeDlab,\n\tmodeDlch,\n\tmodeHsi,\n\tmodeHsl,\n\tmodeHsv,\n\tmodeHwb,\n\tmodeItp,\n\tmodeJab,\n\tmodeJch,\n\tmodeLab,\n\tmodeLab65,\n\tmodeLch,\n\tmodeLch65,\n\tmodeLchuv,\n\tmodeLrgb,\n\tmodeLuv,\n\tmodeOkhsl,\n\tmodeOkhsv,\n\tmodeOklab,\n\tmodeOklch,\n\tmodeP3,\n\tmodeProphoto,\n\tmodeRec2020,\n\tmodeRgb,\n\tmodeXyb,\n\tmodeXyz50,\n\tmodeXyz65,\n\tmodeYiq\n};\n\nexport const a98 = useMode(modeA98);\nexport const cubehelix = useMode(modeCubehelix);\nexport const dlab = useMode(modeDlab);\nexport const dlch = useMode(modeDlch);\nexport const hsi = useMode(modeHsi);\nexport const hsl = useMode(modeHsl);\nexport const hsv = useMode(modeHsv);\nexport const hwb = useMode(modeHwb);\nexport const itp = useMode(modeItp);\nexport const jab = useMode(modeJab);\nexport const jch = useMode(modeJch);\nexport const lab = useMode(modeLab);\nexport const lab65 = useMode(modeLab65);\nexport const lch = useMode(modeLch);\nexport const lch65 = useMode(modeLch65);\nexport const lchuv = useMode(modeLchuv);\nexport const lrgb = useMode(modeLrgb);\nexport const luv = useMode(modeLuv);\nexport const okhsl = useMode(modeOkhsl);\nexport const okhsv = useMode(modeOkhsv);\nexport const oklab = useMode(modeOklab);\nexport const oklch = useMode(modeOklch);\nexport const p3 = useMode(modeP3);\nexport const prophoto = useMode(modeProphoto);\nexport const rec2020 = useMode(modeRec2020);\nexport const rgb = useMode(modeRgb);\nexport const xyb = useMode(modeXyb);\nexport const xyz50 = useMode(modeXyz50);\nexport const xyz65 = useMode(modeXyz65);\nexport const yiq = useMode(modeYiq);\n", "import { greatest, rollup } from 'd3-array';\nimport { propAccessor } from './object.js';\nimport { entries, fromEntries } from './typeHelpers.js';\n/**\n * Useful until Array.flat is more mainstream - https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/flat\n * see also: https://lodash.com/docs/4.17.11#flatten and https://stackoverflow.com/a/55345130/191902\n */\nexport function flatten(items) {\n    return items.reduce((prev, next) => prev.concat(next), []);\n}\n/**\n * Combine values using reducer.  Returns null if all values null (unlike d3.sum)\n */\nexport function combine(values, func) {\n    if (values.every((x) => x == null)) {\n        return null;\n    }\n    return values.reduce(func);\n}\n/**\n * Sum values but maintain null if all values null (unlike d3.sum)\n */\nexport function sum(items, prop) {\n    const getProp = propAccessor(prop);\n    const values = items.map((x) => getProp(x));\n    return combine(values, (total, operand) => (total || 0) + (operand || 0));\n}\n/**\n * Sum array of objects by property\n */\nexport function sumObjects(items, prop) {\n    const getProp = propAccessor(prop);\n    const result = rollup(items.flatMap((x) => entries(x ?? {})), (values) => sum(values, (d) => {\n        const value = Number(getProp(d[1]));\n        return Number.isFinite(value) ? value : 0;\n    }), (d) => d[0]);\n    return items.every(Array.isArray) ? Array.from(result.values()) : fromEntries(result);\n}\n/**\n * Subtract each value from previous but maintain null if all values null (unlike d3.sum)\n */\nexport function subtract(items, prop) {\n    const getProp = propAccessor(prop);\n    const values = items.map((x) => getProp(x));\n    return combine(values, (total, operand) => (total || 0) - (operand || 0));\n}\n/**\n * Average values but maintain null if all values null (unlike d3.mean)\n */\nexport function average(items, prop) {\n    const total = sum(items, prop);\n    return total !== null ? total / items.length : null;\n}\n/**\n * Moving average.\n *   @see https://observablehq.com/@d3/moving-average\n *   @see https://mathworld.wolfram.com/MovingAverage.html\n */\nexport function movingAverage(items, windowSize, prop) {\n    const getProp = propAccessor(prop);\n    let sum = 0;\n    const means = items.map((item, i) => {\n        const value = getProp(item);\n        sum += value ?? 0;\n        if (i >= windowSize - 1) {\n            const mean = sum / windowSize;\n            // Remove oldest item in window for next iteration\n            const oldestValue = getProp(items[i - windowSize + 1]);\n            sum -= oldestValue ?? 0;\n            return mean;\n        }\n        else {\n            // Not enough values available in window yet\n            return null;\n        }\n    });\n    return means;\n}\n/**\n * Return the unique set of values (remove duplicates)\n */\nexport function unique(values) {\n    return Array.from(new Set(values));\n}\n/**\n * Join values up to a maximum with `separator`, then truncate with total\n */\nexport function joinValues(values = [], max = 3, separator = ', ') {\n    const total = values.length;\n    if (total <= max) {\n        return values.join(separator);\n    }\n    else {\n        if (max === 0) {\n            if (values.length === 1) {\n                return values[0];\n            }\n            else {\n                return `(${total} total)`;\n            }\n        }\n        else {\n            return `${values.slice(0, max).join(separator)}, ... (${total} total)`;\n        }\n    }\n}\n/**\n * Recursively transverse nested arrays by path\n */\nexport function nestedFindByPath(arr, path, props, depth = 0) {\n    const getKeyProp = propAccessor(props?.key ?? 'key');\n    const getValuesProp = propAccessor(props?.values ?? 'values');\n    const item = arr.find((x) => getKeyProp(x) === path[depth]);\n    if (depth === path.length - 1) {\n        return item;\n    }\n    else {\n        const children = getValuesProp(item);\n        if (children) {\n            return nestedFindByPath(getValuesProp(item), path, props, depth + 1);\n        }\n    }\n}\n/**\n * Recursively transverse nested arrays looking for item\n */\nexport function nestedFindByPredicate(arr, predicate, childrenProp) {\n    const getChildrenProp = propAccessor(childrenProp ?? 'children');\n    let match = arr.find(predicate);\n    if (match) {\n        return match;\n    }\n    else {\n        for (var item of arr) {\n            const children = getChildrenProp(item);\n            if (children) {\n                match = nestedFindByPredicate(getChildrenProp(item), predicate, childrenProp);\n                if (match) {\n                    return match;\n                }\n            }\n        }\n    }\n    return undefined;\n}\n/**\n * Given a flat array of objects with a `level` property, build a nested object with `children`\n */\nexport function buildTree(arr) {\n    var levels = [{}];\n    arr.forEach((o) => {\n        levels.length = o.level;\n        levels[o.level - 1].children = levels[o.level - 1].children || [];\n        levels[o.level - 1].children?.push(o);\n        levels[o.level] = o;\n    });\n    return levels[0].children ?? [];\n}\n/**\n * Transverse array tree in depth-first order and execute callback for each item\n */\nexport function walk(arr, children, callback) {\n    arr.forEach((item) => {\n        callback(item);\n        if (children(item)) {\n            walk(children(item), children, callback);\n        }\n    });\n}\n/**\n * Build flatten array in depth-first order (using `walk`)\n */\nexport function flattenTree(arr, children) {\n    const flatArray = [];\n    walk(arr, children, (item) => flatArray.push(item));\n    return flatArray;\n}\nexport function chunk(array, size) {\n    return array.reduce((acc, item, index) => {\n        const bucket = Math.floor(index / size);\n        if (!acc[bucket]) {\n            acc[bucket] = [];\n        }\n        acc[bucket].push(item);\n        return acc;\n    }, []);\n}\n/**\n * Get evenly spaced samples from array\n * see: https://observablehq.com/@mbostock/evenly-spaced-sampling\n * see also: https://observablehq.com/@jonhelfman/uniform-sampling-variants\n */\nexport function samples(array, size) {\n    if (!((size = Math.floor(size)) > 0))\n        return []; // return nothing\n    const n = array.length;\n    if (!(n > size))\n        return [...array]; // return everything\n    if (size === 1)\n        return [array[n >> 1]]; // return the midpoint\n    return Array.from({ length: size }, (_, i) => array[Math.round((i / (size - 1)) * (n - 1))]);\n}\n/**\n * Adds item at `index` and returns array\n * Note: mutates, wrap with immer `produce(array, draft => addItem(draft))` for immutable\n */\nexport function addItem(array, item, index) {\n    array.splice(index, 0, item);\n    return array;\n}\n/**\n * Move item `from` index `to` index and returns array\n * Note: mutates, wrap with immer `produce(array, draft => moveItem(draft))` for immutable\n */\nexport function moveItem(array, from, to) {\n    var item = array[from];\n    array.splice(from, 1);\n    array.splice(to, 0, item);\n    return array;\n}\n/**\n * Remove item at `index` returns array (not removed item)\n * Note: mutates, wrap with immer `produce(array, draft => removeItem(draft))` for immutable\n */\nexport function removeItem(array, index) {\n    array.splice(index, 1);\n    return array;\n}\n/**\n * Get the greatest absolute value in an array of numbers, and maintain sign of value\n */\nexport function greatestAbs(array) {\n    return greatest(array, (a, b) => Math.abs(a) - Math.abs(b));\n}\n", "import { get, camelCase, mergeWith } from 'lodash-es';\nimport { entries, fromEntries, keys } from './typeHelpers.js';\nexport function isLiteralObject(obj) {\n    return obj && typeof obj === 'object' && obj.constructor === Object;\n}\nexport function isEmptyObject(obj) {\n    return isLiteralObject(obj) && keys(obj).length === 0;\n}\nexport function camelCaseKeys(obj) {\n    return keys(obj).reduce((acc, key) => ((acc[camelCase(key ? String(key) : undefined)] = obj[key]), acc), {});\n}\n// https://codereview.stackexchange.com/questions/73714/find-a-nested-property-in-an-object\n// https://github.com/dominik791/obj-traverse\nexport function nestedFindByPredicate(obj, predicate, childrenProp) {\n    const getChildrenProp = propAccessor(childrenProp ?? 'children');\n    if (predicate(obj)) {\n        return obj;\n    }\n    else {\n        const children = getChildrenProp(obj);\n        if (children) {\n            for (let o of children) {\n                const match = nestedFindByPredicate(o, predicate, childrenProp);\n                if (match) {\n                    return match;\n                }\n            }\n        }\n    }\n}\nexport function propAccessor(prop) {\n    return typeof prop === 'function'\n        ? prop\n        : typeof prop === 'string'\n            ? (d) => get(d, prop)\n            : (x) => x;\n}\n/**\n * Produce a unique Id for an object (helpful for debugging)\n * See: https://stackoverflow.com/a/35306050/191902\n */\nvar objIdMap = new WeakMap(), objectCount = 0;\nexport function objectId(object) {\n    if (!objIdMap.has(object))\n        objIdMap.set(object, ++objectCount);\n    return objIdMap.get(object);\n}\nexport function distinctKeys(...objs) {\n    return [...new Set(flatten(objs.map((x) => keys(x))))];\n}\n// Copied from `array.ts` to remove circular dependency\nfunction flatten(items) {\n    return items.reduce((prev, next) => prev.concat(next), []);\n}\n/**\n * Recursive merge objects\n * @param object The destination object\n * @param source  The source object\n * @returns\n */\nexport function merge(object, source) {\n    return mergeWith(object, source, (objValue, srcValue) => {\n        if (Array.isArray(srcValue)) {\n            // Overwrite instead of merging by index with objValue (like standard lodash `merge` does)\n            return srcValue;\n        }\n    });\n}\n/**\n * Remove properties from object based on expiration\n */\nexport function expireObject(object, expiry) {\n    const now = new Date();\n    if (expiry instanceof Date || typeof object !== 'object' || object == null) {\n        // Expired\n        if (expiry < now) {\n            return null;\n        }\n        // Not expired\n        return object;\n    }\n    // LoopIterate over the properties in `object`\n    for (let [prop, propExpiry] of entries(expiry)) {\n        if (propExpiry instanceof Date) {\n            // Check if expired\n            if (propExpiry < now) {\n                if (prop === '$default') {\n                    // Delete all properties which do not have explicit expiry to check\n                    for (let objProp of keys(object)) {\n                        if (!(objProp in expiry)) {\n                            delete object[objProp];\n                        }\n                    }\n                    // Remove expired `$default` property\n                    // @ts-expect-error it's fine if the property doesn't exist in object\n                    delete object[prop];\n                }\n                else {\n                    // Remove expired property\n                    // @ts-expect-error it's fine if the property doesn't exist in object\n                    delete object[prop];\n                }\n            }\n            else {\n                // Keep value\n            }\n        }\n        else {\n            // Check expiry for each property in object.  Skip if prop not in object (expiry only)\n            const value = object[prop];\n            if (value && typeof value === 'object') {\n                expireObject(value, propExpiry);\n                // Remove property if empty object (all properties removed)\n                if (isEmptyObject(value)) {\n                    delete object[prop];\n                }\n            }\n        }\n    }\n    return isEmptyObject(object) ? null : object;\n}\n/**\n * Remove properties from an object.  See also lodash `_.omit()`\n */\nexport function omit(obj, keys) {\n    if (keys.length === 0) {\n        return obj;\n    }\n    else {\n        return fromEntries(entries(obj).filter(([key]) => !keys.includes(key)));\n    }\n}\n/**\n * Remove `null` or `undefined` properties from an object\n */\nexport function omitNil(obj) {\n    if (keys.length === 0) {\n        return obj;\n    }\n    else {\n        return fromEntries(entries(obj).filter(([key, value]) => value != null));\n    }\n}\n/**\n * Pick properties from an object.  See also lodash `_.pick()`\n */\nexport function pick(obj, keys) {\n    if (keys.length === 0) {\n        return obj;\n    }\n    else {\n        return fromEntries(keys.filter((key) => key in obj).map((key) => [key, obj[key]]));\n    }\n}\n/**\n * Create new object with keys and values swapped.  Last value's key is used if duplicated\n */\nexport function keysByValues(obj) {\n    return fromEntries(entries(obj).map(([key, value]) => [String(value), key]));\n}\n/**\n * Map keys of an object\n */\nexport function mapKeys(obj, fn) {\n    return fromEntries(entries(obj).map(([key, value]) => [fn(key), value]));\n}\n/**\n * Map values of an object\n */\nexport function mapValues(obj, fn) {\n    return fromEntries(entries(obj).map(([key, value]) => [key, fn(value)]));\n}\n", "// https://basarat.gitbooks.io/typescript/docs/types/never.html#use-case-exhaustive-checks\n// https://www.typescriptlang.org/docs/handbook/basic-types.html#never\nexport function fail(message) {\n    throw new Error(message);\n}\n// Get keys of object (strongly-typed)\n// Reason Object.keys() isn't like this by default due to runtime properties: https://github.com/Microsoft/TypeScript/pull/12253#issuecomment-263132208\nexport function keys(o) {\n    return Object.keys(o);\n}\n// @ts-expect-error\nexport function entries(o) {\n    if (o instanceof Map)\n        return Array.from(o.entries());\n    return Object.entries(o); // TODO: Improve based on key/value pair - https://stackoverflow.com/questions/60141960/typescript-key-value-relation-preserving-object-entries-type\n}\n// Get object from entries (array of [key, value] arrays) (strongly-typed)\nexport function fromEntries(entries) {\n    return Object.fromEntries(entries);\n}\n// https://github.com/Microsoft/TypeScript/issues/17198#issuecomment-315400819\nexport function enumKeys(E) {\n    return keys(E).filter((k) => typeof E[k] === 'number'); // [\"A\", \"B\"]\n}\nexport function enumValues(E) {\n    const keys = enumKeys(E);\n    return keys.map((k) => E[k]); // [0, 1]\n}\n/**\n * util to make sure we have handled all enum cases in a switch statement\n * Just add at the end of the switch statement a `default` like this:\n *\n * ```ts\n * switch (periodType) {\n *   case xxx:\n *     ...\n *\n *   default:\n *     assertNever(periodType); // This will now report unhandled cases\n * }\n * ```\n */\nexport function assertNever(x) {\n    throw new Error(`Unhandled enum case: ${x}`);\n}\n", "import { startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfQuarter, endOfQuarter, startOfYear, endOfYear, min, max, addMonths, addDays, differenceInDays, differenceInWeeks, differenceInMonths, differenceInQuarters, differenceInYears, addWeeks, addQuarters, addYears, isSameDay, isSameWeek, isSameMonth, isSameQuarter, isSameYear, parseISO, formatISO, } from 'date-fns';\nimport { hasKeyOf } from './typeGuards.js';\nimport { assertNever, entries } from './typeHelpers.js';\nimport { chunk } from './array.js';\nimport { PeriodType, DayOfWeek, DateToken, } from './date_types.js';\nimport { defaultLocale } from './locale.js';\nexport * from './date_types.js';\nexport function getDayOfWeekName(weekStartsOn, locales) {\n    // Create a date object for a specific day (0 = Sunday, 1 = Monday, etc.)\n    // And \"7 of Jan 2024\" is a Sunday\n    const date = new Date(2024, 0, 7 + weekStartsOn);\n    const formatter = new Intl.DateTimeFormat(locales, { weekday: 'short' });\n    return formatter.format(date);\n}\nexport function getPeriodTypeName(periodType) {\n    return getPeriodTypeNameWithLocale(defaultLocale, periodType);\n}\nexport function getPeriodTypeNameWithLocale(settings, periodType) {\n    const { locale: locale, dictionary: { Date: dico }, } = settings;\n    switch (periodType) {\n        case PeriodType.Custom:\n            return 'Custom';\n        case PeriodType.Day:\n            return dico.Day;\n        case PeriodType.DayTime:\n            return dico.DayTime;\n        case PeriodType.TimeOnly:\n            return dico.Time;\n        case PeriodType.WeekSun:\n            return `${dico.Week} (${getDayOfWeekName(DayOfWeek.Sunday, locale)})`;\n        case PeriodType.WeekMon:\n            return `${dico.Week} (${getDayOfWeekName(1, locale)})`;\n        case PeriodType.WeekTue:\n            return `${dico.Week} (${getDayOfWeekName(2, locale)})`;\n        case PeriodType.WeekWed:\n            return `${dico.Week} (${getDayOfWeekName(3, locale)})`;\n        case PeriodType.WeekThu:\n            return `${dico.Week} (${getDayOfWeekName(4, locale)})`;\n        case PeriodType.WeekFri:\n            return `${dico.Week} (${getDayOfWeekName(5, locale)})`;\n        case PeriodType.WeekSat:\n            return `${dico.Week} (${getDayOfWeekName(6, locale)})`;\n        case PeriodType.Week:\n            return dico.Week;\n        case PeriodType.Month:\n            return dico.Month;\n        case PeriodType.MonthYear:\n            return dico.Month;\n        case PeriodType.Quarter:\n            return dico.Quarter;\n        case PeriodType.CalendarYear:\n            return dico.CalendarYear;\n        case PeriodType.FiscalYearOctober:\n            return dico.FiscalYearOct;\n        case PeriodType.BiWeek1Sun:\n            return `${dico.BiWeek} (${getDayOfWeekName(0, locale)})`;\n        case PeriodType.BiWeek1Mon:\n            return `${dico.BiWeek} (${getDayOfWeekName(1, locale)})`;\n        case PeriodType.BiWeek1Tue:\n            return `${dico.BiWeek} (${getDayOfWeekName(2, locale)})`;\n        case PeriodType.BiWeek1Wed:\n            return `${dico.BiWeek} (${getDayOfWeekName(3, locale)})`;\n        case PeriodType.BiWeek1Thu:\n            return `${dico.BiWeek} (${getDayOfWeekName(4, locale)})`;\n        case PeriodType.BiWeek1Fri:\n            return `${dico.BiWeek} (${getDayOfWeekName(5, locale)})`;\n        case PeriodType.BiWeek1Sat:\n            return `${dico.BiWeek} (${getDayOfWeekName(6, locale)})`;\n        case PeriodType.BiWeek1:\n            return dico.BiWeek;\n        case PeriodType.BiWeek2Sun:\n            return `${dico.BiWeek} 2 (${getDayOfWeekName(0, locale)})`;\n        case PeriodType.BiWeek2Mon:\n            return `${dico.BiWeek} 2 (${getDayOfWeekName(1, locale)})`;\n        case PeriodType.BiWeek2Tue:\n            return `${dico.BiWeek} 2 (${getDayOfWeekName(2, locale)})`;\n        case PeriodType.BiWeek2Wed:\n            return `${dico.BiWeek} 2 (${getDayOfWeekName(3, locale)})`;\n        case PeriodType.BiWeek2Thu:\n            return `${dico.BiWeek} 2 (${getDayOfWeekName(4, locale)})`;\n        case PeriodType.BiWeek2Fri:\n            return `${dico.BiWeek} 2 (${getDayOfWeekName(5, locale)})`;\n        case PeriodType.BiWeek2Sat:\n            return `${dico.BiWeek} 2 (${getDayOfWeekName(6, locale)})`;\n        case PeriodType.BiWeek2:\n            return `${dico.BiWeek} 2`;\n        default:\n            assertNever(periodType); // This will now report unhandled cases\n    }\n}\nconst periodTypeMappings = {\n    [PeriodType.Custom]: 'CUSTOM',\n    [PeriodType.Day]: 'DAY',\n    [PeriodType.DayTime]: 'DAY-TIME',\n    [PeriodType.TimeOnly]: 'TIME',\n    [PeriodType.WeekSun]: 'WEEK-SUN',\n    [PeriodType.WeekMon]: 'WEEK-MON',\n    [PeriodType.WeekTue]: 'WEEK-TUE',\n    [PeriodType.WeekWed]: 'WEEK-WED',\n    [PeriodType.WeekThu]: 'WEEK-THU',\n    [PeriodType.WeekFri]: 'WEEK-FRI',\n    [PeriodType.WeekSat]: 'WEEK-SAT',\n    [PeriodType.Week]: 'WEEK',\n    [PeriodType.Month]: 'MTH',\n    [PeriodType.MonthYear]: 'MTH-CY',\n    [PeriodType.Quarter]: 'QTR',\n    [PeriodType.CalendarYear]: 'CY',\n    [PeriodType.FiscalYearOctober]: 'FY-OCT',\n    [PeriodType.BiWeek1Sun]: 'BIWEEK1-SUN',\n    [PeriodType.BiWeek1Mon]: 'BIWEEK1-MON',\n    [PeriodType.BiWeek1Tue]: 'BIWEEK1-TUE',\n    [PeriodType.BiWeek1Wed]: 'BIWEEK1-WED',\n    [PeriodType.BiWeek1Thu]: 'BIWEEK1-THU',\n    [PeriodType.BiWeek1Fri]: 'BIWEEK1-FRI',\n    [PeriodType.BiWeek1Sat]: 'BIWEEK1-SAT',\n    [PeriodType.BiWeek1]: 'BIWEEK1',\n    [PeriodType.BiWeek2Sun]: 'BIWEEK2-SUN',\n    [PeriodType.BiWeek2Mon]: 'BIWEEK2-MON',\n    [PeriodType.BiWeek2Tue]: 'BIWEEK2-TUE',\n    [PeriodType.BiWeek2Wed]: 'BIWEEK2-WED',\n    [PeriodType.BiWeek2Thu]: 'BIWEEK2-THU',\n    [PeriodType.BiWeek2Fri]: 'BIWEEK2-FRI',\n    [PeriodType.BiWeek2Sat]: 'BIWEEK2-SAT',\n    [PeriodType.BiWeek2]: 'BIWEEK2',\n};\nexport function getPeriodTypeCode(periodType) {\n    return periodTypeMappings[periodType];\n}\nexport function getPeriodTypeByCode(code) {\n    const element = entries(periodTypeMappings).find((c) => c[1] === code);\n    return parseInt(String(element?.[0] ?? '1'));\n}\nexport function getDayOfWeek(periodType) {\n    if ((periodType >= PeriodType.WeekSun && periodType <= PeriodType.WeekSat) ||\n        (periodType >= PeriodType.BiWeek1Sun && periodType <= PeriodType.BiWeek1Sat) ||\n        (periodType >= PeriodType.BiWeek2Sun && periodType <= PeriodType.BiWeek2Sat)) {\n        return (periodType % 10) - 1;\n    }\n    else {\n        return null;\n    }\n}\n/** Replace day of week for `periodType`, if applicable */\nexport function replaceDayOfWeek(periodType, dayOfWeek) {\n    if (hasDayOfWeek(periodType)) {\n        return periodType - (getDayOfWeek(periodType) ?? 0) + dayOfWeek;\n    }\n    else if (missingDayOfWeek(periodType)) {\n        return periodType + dayOfWeek + 1;\n    }\n    else {\n        return periodType;\n    }\n}\n/** Check if `periodType` has day of week (Sun-Sat) */\nexport function hasDayOfWeek(periodType) {\n    if (periodType >= PeriodType.WeekSun && periodType <= PeriodType.WeekSat) {\n        return true;\n    }\n    if (periodType >= PeriodType.BiWeek1Sun && periodType <= PeriodType.BiWeek1Sat) {\n        return true;\n    }\n    if (periodType >= PeriodType.BiWeek2Sun && periodType <= PeriodType.BiWeek2Sat) {\n        return true;\n    }\n    return false;\n}\n/** Is `periodType` missing day of week (Sun-Sat) */\nexport function missingDayOfWeek(periodType) {\n    return [PeriodType.Week, PeriodType.BiWeek1, PeriodType.BiWeek2].includes(periodType);\n}\nexport function getMonths(year = new Date().getFullYear()) {\n    return Array.from({ length: 12 }, (_, i) => new Date(year, i, 1));\n}\nexport function getMonthDaysByWeek(dateInTheMonth, weekStartsOn = DayOfWeek.Sunday) {\n    const startOfFirstWeek = startOfWeek(startOfMonth(dateInTheMonth), { weekStartsOn });\n    const endOfLastWeek = endOfWeek(endOfMonth(dateInTheMonth), { weekStartsOn });\n    const list = [];\n    let valueToAdd = startOfFirstWeek;\n    while (valueToAdd <= endOfLastWeek) {\n        list.push(valueToAdd);\n        valueToAdd = addDays(valueToAdd, 1);\n    }\n    return chunk(list, 7);\n}\nexport function getMinSelectedDate(date) {\n    if (date instanceof Date) {\n        return date;\n    }\n    else if (date instanceof Array) {\n        return min(date);\n    }\n    else if (hasKeyOf(date, 'from')) {\n        return date.from;\n    }\n    else {\n        return null;\n    }\n}\nexport function getMaxSelectedDate(date) {\n    if (date instanceof Date) {\n        return date;\n    }\n    else if (date instanceof Array) {\n        return max(date);\n    }\n    else if (hasKeyOf(date, 'to')) {\n        return date.to;\n    }\n    else {\n        return null;\n    }\n}\n/*\n * Fiscal Year\n */\nexport function getFiscalYear(date = new Date(), options) {\n    if (date === null) {\n        // null explicitly passed in (default value overridden)\n        return NaN;\n    }\n    const startMonth = (options && options.startMonth) || 10;\n    return date.getMonth() >= startMonth - 1 ? date.getFullYear() + 1 : date.getFullYear();\n}\nexport function getFiscalYearRange(date = new Date(), options) {\n    const fiscalYear = getFiscalYear(date, options);\n    const startMonth = (options && options.startMonth) || 10;\n    const numberOfMonths = (options && options.numberOfMonths) || 12;\n    const startDate = new Date((fiscalYear || 0) - 1, startMonth - 1, 1);\n    const endDate = endOfMonth(addMonths(startDate, numberOfMonths - 1));\n    return { startDate, endDate };\n}\nexport function startOfFiscalYear(date, options) {\n    return getFiscalYearRange(date, options).startDate;\n}\nexport function endOfFiscalYear(date, options) {\n    return getFiscalYearRange(date, options).endDate;\n}\nexport function isSameFiscalYear(dateLeft, dateRight) {\n    return getFiscalYear(dateLeft) === getFiscalYear(dateRight);\n}\n/*\n * Bi-Weekly\n */\nconst biweekBaseDates = [new Date('1799-12-22T00:00'), new Date('1799-12-15T00:00')];\nexport function startOfBiWeek(date, week, startOfWeek) {\n    var weekBaseDate = biweekBaseDates[week - 1];\n    var baseDate = addDays(weekBaseDate, startOfWeek);\n    var periodsSince = Math.floor(differenceInDays(date, baseDate) / 14);\n    return addDays(baseDate, periodsSince * 14);\n}\nexport function endOfBiWeek(date, week, startOfWeek) {\n    return addDays(startOfBiWeek(date, week, startOfWeek), 13);\n}\nexport function getDateFuncsByPeriodType(settings, periodType) {\n    if (settings) {\n        periodType = updatePeriodTypeWithWeekStartsOn(settings.formats.dates.weekStartsOn, periodType);\n    }\n    switch (periodType) {\n        case PeriodType.Day:\n            return {\n                start: startOfDay,\n                end: endOfDay,\n                add: addDays,\n                difference: differenceInDays,\n                isSame: isSameDay,\n            };\n        case PeriodType.Week:\n        case PeriodType.WeekSun:\n            return {\n                start: startOfWeek,\n                end: endOfWeek,\n                add: addWeeks,\n                difference: differenceInWeeks,\n                isSame: isSameWeek,\n            };\n        case PeriodType.WeekMon:\n            return {\n                start: (date) => startOfWeek(date, { weekStartsOn: 1 }),\n                end: (date) => endOfWeek(date, { weekStartsOn: 1 }),\n                add: addWeeks,\n                difference: differenceInWeeks,\n                isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 1 }),\n            };\n        case PeriodType.WeekTue:\n            return {\n                start: (date) => startOfWeek(date, { weekStartsOn: 2 }),\n                end: (date) => endOfWeek(date, { weekStartsOn: 2 }),\n                add: addWeeks,\n                difference: differenceInWeeks,\n                isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 2 }),\n            };\n        case PeriodType.WeekWed:\n            return {\n                start: (date) => startOfWeek(date, { weekStartsOn: 3 }),\n                end: (date) => endOfWeek(date, { weekStartsOn: 3 }),\n                add: addWeeks,\n                difference: differenceInWeeks,\n                isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 3 }),\n            };\n        case PeriodType.WeekThu:\n            return {\n                start: (date) => startOfWeek(date, { weekStartsOn: 4 }),\n                end: (date) => endOfWeek(date, { weekStartsOn: 4 }),\n                add: addWeeks,\n                difference: differenceInWeeks,\n                isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 4 }),\n            };\n        case PeriodType.WeekFri:\n            return {\n                start: (date) => startOfWeek(date, { weekStartsOn: 5 }),\n                end: (date) => endOfWeek(date, { weekStartsOn: 5 }),\n                add: addWeeks,\n                difference: differenceInWeeks,\n                isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 5 }),\n            };\n        case PeriodType.WeekSat:\n            return {\n                start: (date) => startOfWeek(date, { weekStartsOn: 6 }),\n                end: (date) => endOfWeek(date, { weekStartsOn: 6 }),\n                add: addWeeks,\n                difference: differenceInWeeks,\n                isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 6 }),\n            };\n        case PeriodType.Month:\n            return {\n                start: startOfMonth,\n                end: endOfMonth,\n                add: addMonths,\n                difference: differenceInMonths,\n                isSame: isSameMonth,\n            };\n        case PeriodType.Quarter:\n            return {\n                start: startOfQuarter,\n                end: endOfQuarter,\n                add: addQuarters,\n                difference: differenceInQuarters,\n                isSame: isSameQuarter,\n            };\n        case PeriodType.CalendarYear:\n            return {\n                start: startOfYear,\n                end: endOfYear,\n                add: addYears,\n                difference: differenceInYears,\n                isSame: isSameYear,\n            };\n        case PeriodType.FiscalYearOctober:\n            return {\n                start: startOfFiscalYear,\n                end: endOfFiscalYear,\n                add: addYears,\n                difference: differenceInYears,\n                isSame: isSameFiscalYear,\n            };\n        // BiWeek 1\n        case PeriodType.BiWeek1:\n        case PeriodType.BiWeek1Sun:\n        case PeriodType.BiWeek1Mon:\n        case PeriodType.BiWeek1Tue:\n        case PeriodType.BiWeek1Wed:\n        case PeriodType.BiWeek1Thu:\n        case PeriodType.BiWeek1Fri:\n        case PeriodType.BiWeek1Sat:\n        // BiWeek 2\n        case PeriodType.BiWeek2:\n        case PeriodType.BiWeek2Sun:\n        case PeriodType.BiWeek2Mon:\n        case PeriodType.BiWeek2Tue:\n        case PeriodType.BiWeek2Wed:\n        case PeriodType.BiWeek2Thu:\n        case PeriodType.BiWeek2Fri:\n        case PeriodType.BiWeek2Sat: {\n            const week = getPeriodTypeCode(periodType).startsWith('BIWEEK1') ? 1 : 2;\n            const dayOfWeek = getDayOfWeek(periodType);\n            return {\n                start: (date) => startOfBiWeek(date, week, dayOfWeek),\n                end: (date) => endOfBiWeek(date, week, dayOfWeek),\n                add: (date, amount) => addWeeks(date, amount * 2),\n                difference: (dateLeft, dateRight) => {\n                    return differenceInWeeks(dateLeft, dateRight) / 2;\n                },\n                isSame: (dateLeft, dateRight) => {\n                    return isSameDay(startOfBiWeek(dateLeft, week, dayOfWeek), startOfBiWeek(dateRight, week, dayOfWeek));\n                },\n            };\n        }\n        // All cases not handled above\n        case PeriodType.Custom:\n        case PeriodType.DayTime:\n        case PeriodType.TimeOnly:\n        case PeriodType.MonthYear:\n        case null:\n        case undefined:\n            // Default to end of day if periodType == null, etc\n            return {\n                start: startOfDay,\n                end: endOfDay,\n                add: addDays,\n                difference: differenceInDays,\n                isSame: isSameDay,\n            };\n        default:\n            assertNever(periodType); // This will now report unhandled cases\n    }\n}\nexport function formatISODate(date, representation = 'complete') {\n    if (date == null) {\n        return '';\n    }\n    if (typeof date === 'string') {\n        date = parseISO(date);\n    }\n    return formatISO(date, { representation });\n}\nexport function formatIntl(settings, dt, tokens_or_intlOptions) {\n    const { locale, formats: { dates: { ordinalSuffixes: suffixes }, }, } = settings;\n    function formatIntlOrdinal(formatter, with_ordinal = false) {\n        if (with_ordinal) {\n            const rules = new Intl.PluralRules(locale, { type: 'ordinal' });\n            const splited = formatter.formatToParts(dt);\n            return splited\n                .map((c) => {\n                if (c.type === 'day') {\n                    const ordinal = rules.select(parseInt(c.value, 10));\n                    const suffix = suffixes[ordinal];\n                    return `${c.value}${suffix}`;\n                }\n                return c.value;\n            })\n                .join('');\n        }\n        return formatter.format(dt);\n    }\n    if (typeof tokens_or_intlOptions !== 'string' && !Array.isArray(tokens_or_intlOptions)) {\n        return formatIntlOrdinal(new Intl.DateTimeFormat(locale, tokens_or_intlOptions), tokens_or_intlOptions.withOrdinal);\n    }\n    const tokens = Array.isArray(tokens_or_intlOptions)\n        ? tokens_or_intlOptions.join('')\n        : tokens_or_intlOptions;\n    // Order of includes check is important! (longest first)\n    const formatter = new Intl.DateTimeFormat(locale, {\n        year: tokens.includes(DateToken.Year_numeric)\n            ? 'numeric'\n            : tokens.includes(DateToken.Year_2Digit)\n                ? '2-digit'\n                : undefined,\n        month: tokens.includes(DateToken.Month_long)\n            ? 'long'\n            : tokens.includes(DateToken.Month_short)\n                ? 'short'\n                : tokens.includes(DateToken.Month_2Digit)\n                    ? '2-digit'\n                    : tokens.includes(DateToken.Month_numeric)\n                        ? 'numeric'\n                        : undefined,\n        day: tokens.includes(DateToken.DayOfMonth_2Digit)\n            ? '2-digit'\n            : tokens.includes(DateToken.DayOfMonth_numeric)\n                ? 'numeric'\n                : undefined,\n        hour: tokens.includes(DateToken.Hour_2Digit)\n            ? '2-digit'\n            : tokens.includes(DateToken.Hour_numeric)\n                ? 'numeric'\n                : undefined,\n        hour12: tokens.includes(DateToken.Hour_woAMPM)\n            ? false\n            : tokens.includes(DateToken.Hour_wAMPM)\n                ? true\n                : undefined,\n        minute: tokens.includes(DateToken.Minute_2Digit)\n            ? '2-digit'\n            : tokens.includes(DateToken.Minute_numeric)\n                ? 'numeric'\n                : undefined,\n        second: tokens.includes(DateToken.Second_2Digit)\n            ? '2-digit'\n            : tokens.includes(DateToken.Second_numeric)\n                ? 'numeric'\n                : undefined,\n        fractionalSecondDigits: tokens.includes(DateToken.MiliSecond_3) ? 3 : undefined,\n        weekday: tokens.includes(DateToken.DayOfWeek_narrow)\n            ? 'narrow'\n            : tokens.includes(DateToken.DayOfWeek_long)\n                ? 'long'\n                : tokens.includes(DateToken.DayOfWeek_short)\n                    ? 'short'\n                    : undefined,\n    });\n    return formatIntlOrdinal(formatter, tokens.includes(DateToken.DayOfMonth_withOrdinal));\n}\nfunction range(settings, date, weekStartsOn, formatToUse, biWeek = undefined // undefined means that it's not a bi-week\n) {\n    const start = biWeek === undefined\n        ? startOfWeek(date, { weekStartsOn })\n        : startOfBiWeek(date, biWeek, weekStartsOn);\n    const end = biWeek === undefined\n        ? endOfWeek(date, { weekStartsOn })\n        : endOfBiWeek(date, biWeek, weekStartsOn);\n    return formatIntl(settings, start, formatToUse) + ' - ' + formatIntl(settings, end, formatToUse);\n}\nexport function formatDate(date, periodType, options = {}) {\n    return formatDateWithLocale(defaultLocale, date, periodType, options);\n}\nexport function updatePeriodTypeWithWeekStartsOn(weekStartsOn, periodType) {\n    if (periodType === PeriodType.Week) {\n        periodType = [\n            PeriodType.WeekSun,\n            PeriodType.WeekMon,\n            PeriodType.WeekTue,\n            PeriodType.WeekWed,\n            PeriodType.WeekThu,\n            PeriodType.WeekFri,\n            PeriodType.WeekSat,\n        ][weekStartsOn];\n    }\n    else if (periodType === PeriodType.BiWeek1) {\n        periodType = [\n            PeriodType.BiWeek1Sun,\n            PeriodType.BiWeek1Mon,\n            PeriodType.BiWeek1Tue,\n            PeriodType.BiWeek1Wed,\n            PeriodType.BiWeek1Thu,\n            PeriodType.BiWeek1Fri,\n            PeriodType.BiWeek1Sat,\n        ][weekStartsOn];\n    }\n    else if (periodType === PeriodType.BiWeek2) {\n        periodType = [\n            PeriodType.BiWeek2Sun,\n            PeriodType.BiWeek2Mon,\n            PeriodType.BiWeek2Tue,\n            PeriodType.BiWeek2Wed,\n            PeriodType.BiWeek2Thu,\n            PeriodType.BiWeek2Fri,\n            PeriodType.BiWeek2Sat,\n        ][weekStartsOn];\n    }\n    return periodType;\n}\nexport function formatDateWithLocale(settings, date, periodType, options = {}) {\n    if (typeof date === 'string') {\n        date = parseISO(date);\n    }\n    // Handle 'Invalid Date'\n    // @ts-expect-error - Date is a number (see: https://stackoverflow.com/questions/1353684/detecting-an-invalid-date-date-instance-in-javascript)\n    if (date == null || isNaN(date)) {\n        return '';\n    }\n    const weekStartsOn = options.weekStartsOn ?? settings.formats.dates.weekStartsOn;\n    const { day, dayTime, timeOnly, week, month, monthsYear, year } = settings.formats.dates.presets;\n    periodType = updatePeriodTypeWithWeekStartsOn(weekStartsOn, periodType) ?? periodType;\n    /** Resolve a preset given the chosen variant */\n    function rv(preset) {\n        if (options.variant === 'custom') {\n            return options.custom ?? preset.default;\n        }\n        else if (options.custom && !options.variant) {\n            return options.custom;\n        }\n        return preset[options.variant ?? 'default'];\n    }\n    switch (periodType) {\n        case PeriodType.Custom:\n            return formatIntl(settings, date, options.custom);\n        case PeriodType.Day:\n            return formatIntl(settings, date, rv(day));\n        case PeriodType.DayTime:\n            return formatIntl(settings, date, rv(dayTime));\n        case PeriodType.TimeOnly:\n            return formatIntl(settings, date, rv(timeOnly));\n        case PeriodType.Week: //Should never happen, but to make types happy\n        case PeriodType.WeekSun:\n            return range(settings, date, 0, rv(week));\n        case PeriodType.WeekMon:\n            return range(settings, date, 1, rv(week));\n        case PeriodType.WeekTue:\n            return range(settings, date, 2, rv(week));\n        case PeriodType.WeekWed:\n            return range(settings, date, 3, rv(week));\n        case PeriodType.WeekThu:\n            return range(settings, date, 4, rv(week));\n        case PeriodType.WeekFri:\n            return range(settings, date, 5, rv(week));\n        case PeriodType.WeekSat:\n            return range(settings, date, 6, rv(week));\n        case PeriodType.Month:\n            return formatIntl(settings, date, rv(month));\n        case PeriodType.MonthYear:\n            return formatIntl(settings, date, rv(monthsYear));\n        case PeriodType.Quarter:\n            return [\n                formatIntl(settings, startOfQuarter(date), rv(month)),\n                formatIntl(settings, endOfQuarter(date), rv(monthsYear)),\n            ].join(' - ');\n        case PeriodType.CalendarYear:\n            return formatIntl(settings, date, rv(year));\n        case PeriodType.FiscalYearOctober:\n            const fDate = new Date(getFiscalYear(date), 0, 1);\n            return formatIntl(settings, fDate, rv(year));\n        case PeriodType.BiWeek1: //Should never happen, but to make types happy\n        case PeriodType.BiWeek1Sun:\n            return range(settings, date, 0, rv(week), 1);\n        case PeriodType.BiWeek1Mon:\n            return range(settings, date, 1, rv(week), 1);\n        case PeriodType.BiWeek1Tue:\n            return range(settings, date, 2, rv(week), 1);\n        case PeriodType.BiWeek1Wed:\n            return range(settings, date, 3, rv(week), 1);\n        case PeriodType.BiWeek1Thu:\n            return range(settings, date, 4, rv(week), 1);\n        case PeriodType.BiWeek1Fri:\n            return range(settings, date, 5, rv(week), 1);\n        case PeriodType.BiWeek1Sat:\n            return range(settings, date, 6, rv(week), 1);\n        case PeriodType.BiWeek2: //Should never happen, but to make types happy\n        case PeriodType.BiWeek2Sun:\n            return range(settings, date, 0, rv(week), 2);\n        case PeriodType.BiWeek2Mon:\n            return range(settings, date, 1, rv(week), 2);\n        case PeriodType.BiWeek2Tue:\n            return range(settings, date, 2, rv(week), 2);\n        case PeriodType.BiWeek2Wed:\n            return range(settings, date, 3, rv(week), 2);\n        case PeriodType.BiWeek2Thu:\n            return range(settings, date, 4, rv(week), 2);\n        case PeriodType.BiWeek2Fri:\n            return range(settings, date, 5, rv(week), 2);\n        case PeriodType.BiWeek2Sat:\n            return range(settings, date, 6, rv(week), 2);\n        default:\n            return formatISO(date);\n        // default:\n        //   assertNever(periodType); // This will now report unhandled cases\n    }\n}\n/**\n * Return new Date using UTC date/time as local date/time\n */\nexport function utcToLocalDate(date) {\n    date = date instanceof Date ? date : typeof date === 'string' ? new Date(date) : new Date();\n    // https://github.com/date-fns/date-fns/issues/376#issuecomment-454163253\n    // return new Date(date.getTime() + date.getTimezoneOffset() * 60 * 1000);\n    const d = new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds());\n    d.setUTCFullYear(date.getUTCFullYear());\n    return d;\n}\n/**\n * Return new Date using local date/time as UTC date/time\n */\nexport function localToUtcDate(date) {\n    date = date instanceof Date ? date : typeof date === 'string' ? new Date(date) : new Date();\n    // return new Date(date.getTime() - date.getTimezoneOffset() * 60 * 1000);\n    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds()));\n    return d;\n}\n/**\n * Generate a random Date between `from` and `to` (exclusive)\n */\nexport function randomDate(from, to) {\n    const fromTime = from.getTime();\n    const toTime = to.getTime();\n    return new Date(fromTime + Math.random() * (toTime - fromTime));\n}\n// '1982-03-30'\n// '1982-03-30T11:25:59Z'\n// '1982-03-30T11:25:59-04:00'\n// '1982-03-30T11:25:59.123Z'\n// '1982-03-30T11:25:59.1234567Z'\nconst DATE_FORMAT = /^\\d{4}-\\d{2}-\\d{2}(T\\d{2}:\\d{2}:\\d{2}(.\\d+|)(Z|(-|\\+)\\d{2}:\\d{2}))?$/;\n/**\n * Determine if string is UTC (yyyy-mm-ddThh:mm:ssZ) or Offset (yyyy-mm-ddThh:mm:ss-ZZ:ZZ) or Date-only (yyyy-mm-dd) date string\n */\nexport function isStringDate(value) {\n    return DATE_FORMAT.test(value);\n}\n", "// Generic type guard -  https://stackoverflow.com/a/43423642/191902\nexport function hasKeyOf(object, key) {\n    if (object) {\n        return key in object;\n    }\n    else {\n        return false;\n    }\n}\n// Similar to Object.hasOwnProperty\n// http://www.typescriptlang.org/docs/handbook/advanced-types.html#user-defined-type-guards\nexport function hasProperty(o, name) {\n    return name in o;\n}\n// Typesafe way to get property names\n// https://www.meziantou.net/typescript-nameof-operator-equivalent.htm\n// https://schneidenbach.gitbooks.io/typescript-cookbook/nameof-operator.html\nexport function nameof(key, instance) {\n    return key;\n}\nexport function isNumber(val) {\n    return typeof val === 'number';\n}\n/**\n * Check if value is present (not `null`/`undefined`).  Useful with `arr.filter(notNull)`\n */\nexport function notNull(value) {\n    return value != null;\n}\nexport function isElement(elem) {\n    return !!elem && elem instanceof Element;\n}\n// functional definition of isSVGElement. Note that SVGSVGElements are HTMLElements\nexport function isSVGElement(elem) {\n    return !!elem && (elem instanceof SVGElement || 'ownerSVGElement' in elem);\n}\n// functional definition of SVGGElement\nexport function isSVGSVGElement(elem) {\n    return !!elem && 'createSVGPoint' in elem;\n}\nexport function isSVGGraphicsElement(elem) {\n    return !!elem && 'getScreenCTM' in elem;\n}\n// functional definition of TouchEvent\nexport function isTouchEvent(event) {\n    return !!event && 'changedTouches' in event;\n}\n// functional definition of event\nexport function isEvent(event) {\n    return (!!event &&\n        (event instanceof Event || ('nativeEvent' in event && event.nativeEvent instanceof Event)));\n}\n", "export var PeriodType;\n(function (PeriodType) {\n    PeriodType[PeriodType[\"Custom\"] = 1] = \"Custom\";\n    PeriodType[PeriodType[\"Day\"] = 10] = \"Day\";\n    PeriodType[PeriodType[\"DayTime\"] = 11] = \"DayTime\";\n    PeriodType[PeriodType[\"TimeOnly\"] = 15] = \"TimeOnly\";\n    PeriodType[PeriodType[\"Week\"] = 20] = \"Week\";\n    PeriodType[PeriodType[\"WeekSun\"] = 21] = \"WeekSun\";\n    PeriodType[PeriodType[\"WeekMon\"] = 22] = \"WeekMon\";\n    PeriodType[PeriodType[\"WeekTue\"] = 23] = \"WeekTue\";\n    PeriodType[PeriodType[\"WeekWed\"] = 24] = \"WeekWed\";\n    PeriodType[PeriodType[\"WeekThu\"] = 25] = \"WeekThu\";\n    PeriodType[PeriodType[\"WeekFri\"] = 26] = \"WeekFri\";\n    PeriodType[PeriodType[\"WeekSat\"] = 27] = \"WeekSat\";\n    PeriodType[PeriodType[\"Month\"] = 30] = \"Month\";\n    PeriodType[PeriodType[\"MonthYear\"] = 31] = \"MonthYear\";\n    PeriodType[PeriodType[\"Quarter\"] = 40] = \"Quarter\";\n    PeriodType[PeriodType[\"CalendarYear\"] = 50] = \"CalendarYear\";\n    PeriodType[PeriodType[\"FiscalYearOctober\"] = 60] = \"FiscalYearOctober\";\n    PeriodType[PeriodType[\"BiWeek1\"] = 70] = \"BiWeek1\";\n    PeriodType[PeriodType[\"BiWeek1Sun\"] = 71] = \"BiWeek1Sun\";\n    PeriodType[PeriodType[\"BiWeek1Mon\"] = 72] = \"BiWeek1Mon\";\n    PeriodType[PeriodType[\"BiWeek1Tue\"] = 73] = \"BiWeek1Tue\";\n    PeriodType[PeriodType[\"BiWeek1Wed\"] = 74] = \"BiWeek1Wed\";\n    PeriodType[PeriodType[\"BiWeek1Thu\"] = 75] = \"BiWeek1Thu\";\n    PeriodType[PeriodType[\"BiWeek1Fri\"] = 76] = \"BiWeek1Fri\";\n    PeriodType[PeriodType[\"BiWeek1Sat\"] = 77] = \"BiWeek1Sat\";\n    PeriodType[PeriodType[\"BiWeek2\"] = 80] = \"BiWeek2\";\n    PeriodType[PeriodType[\"BiWeek2Sun\"] = 81] = \"BiWeek2Sun\";\n    PeriodType[PeriodType[\"BiWeek2Mon\"] = 82] = \"BiWeek2Mon\";\n    PeriodType[PeriodType[\"BiWeek2Tue\"] = 83] = \"BiWeek2Tue\";\n    PeriodType[PeriodType[\"BiWeek2Wed\"] = 84] = \"BiWeek2Wed\";\n    PeriodType[PeriodType[\"BiWeek2Thu\"] = 85] = \"BiWeek2Thu\";\n    PeriodType[PeriodType[\"BiWeek2Fri\"] = 86] = \"BiWeek2Fri\";\n    PeriodType[PeriodType[\"BiWeek2Sat\"] = 87] = \"BiWeek2Sat\";\n})(PeriodType || (PeriodType = {}));\nexport var DayOfWeek;\n(function (DayOfWeek) {\n    DayOfWeek[DayOfWeek[\"Sunday\"] = 0] = \"Sunday\";\n    DayOfWeek[DayOfWeek[\"Monday\"] = 1] = \"Monday\";\n    DayOfWeek[DayOfWeek[\"Tuesday\"] = 2] = \"Tuesday\";\n    DayOfWeek[DayOfWeek[\"Wednesday\"] = 3] = \"Wednesday\";\n    DayOfWeek[DayOfWeek[\"Thursday\"] = 4] = \"Thursday\";\n    DayOfWeek[DayOfWeek[\"Friday\"] = 5] = \"Friday\";\n    DayOfWeek[DayOfWeek[\"Saturday\"] = 6] = \"Saturday\";\n})(DayOfWeek || (DayOfWeek = {}));\nexport var DateToken;\n(function (DateToken) {\n    /** `1982, 1986, 2024` */\n    DateToken[\"Year_numeric\"] = \"yyy\";\n    /** `82, 86, 24` */\n    DateToken[\"Year_2Digit\"] = \"yy\";\n    /** `January, February, ..., December` */\n    DateToken[\"Month_long\"] = \"MMMM\";\n    /** `Jan, Feb, ..., Dec` */\n    DateToken[\"Month_short\"] = \"MMM\";\n    /** `01, 02, ..., 12` */\n    DateToken[\"Month_2Digit\"] = \"MM\";\n    /** `1, 2, ..., 12` */\n    DateToken[\"Month_numeric\"] = \"M\";\n    /** `1, 2, ..., 11, 12` */\n    DateToken[\"Hour_numeric\"] = \"h\";\n    /** `01, 02, ..., 11, 12` */\n    DateToken[\"Hour_2Digit\"] = \"hh\";\n    /** You should probably not use this. Force with AM/PM (and the good locale), not specifying this will automatically take the good local */\n    DateToken[\"Hour_wAMPM\"] = \"a\";\n    /** You should probably not use this. Force without AM/PM (and the good locale), not specifying this will automatically take the good local */\n    DateToken[\"Hour_woAMPM\"] = \"aaaaaa\";\n    /** `0, 1, ..., 59` */\n    DateToken[\"Minute_numeric\"] = \"m\";\n    /** `00, 01, ..., 59` */\n    DateToken[\"Minute_2Digit\"] = \"mm\";\n    /** `0, 1, ..., 59` */\n    DateToken[\"Second_numeric\"] = \"s\";\n    /** `00, 01, ..., 59` */\n    DateToken[\"Second_2Digit\"] = \"ss\";\n    /** `000, 001, ..., 999` */\n    DateToken[\"MiliSecond_3\"] = \"SSS\";\n    /** Minimize digit: `1, 2, 11, ...` */\n    DateToken[\"DayOfMonth_numeric\"] = \"d\";\n    /** `01, 02, 11, ...` */\n    DateToken[\"DayOfMonth_2Digit\"] = \"dd\";\n    /** `1st, 2nd, 11th, ...` You can have your local ordinal by passing `ordinalSuffixes` in options / settings */\n    DateToken[\"DayOfMonth_withOrdinal\"] = \"do\";\n    /** `M, T, W, T, F, S, S` */\n    DateToken[\"DayOfWeek_narrow\"] = \"eeeee\";\n    /** `Monday, Tuesday, ..., Sunday` */\n    DateToken[\"DayOfWeek_long\"] = \"eeee\";\n    /** `Mon, Tue, Wed, ..., Sun` */\n    DateToken[\"DayOfWeek_short\"] = \"eee\";\n})(DateToken || (DateToken = {}));\n", "import { DayOfWeek } from './date_types.js';\nexport function getWeekStartsOnFromIntl(locales) {\n    if (!locales) {\n        return DayOfWeek.Sunday;\n    }\n    const locale = new Intl.Locale(locales);\n    // @ts-expect-error\n    const weekInfo = locale.weekInfo ?? locale.getWeekInfo?.();\n    return (weekInfo?.firstDay ?? 0) % 7; // (in Intl, sunday is 7 not 0, so we need to mod 7)\n}\n", "import { entries, fromEntries } from './typeHelpers.js';\nimport { defaultsDeep } from 'lodash-es';\nimport { derived, writable } from 'svelte/store';\nimport { DateToken, DayOfWeek, } from './date_types.js';\nimport { getWeekStartsOnFromIntl } from './dateInternal.js';\nfunction resolvedLocaleStore(forceLocales, fallbackLocale) {\n    return derived(forceLocales, ($forceLocales) => {\n        let result;\n        if ($forceLocales?.length) {\n            if (Array.isArray($forceLocales)) {\n                result = $forceLocales[0];\n            }\n            else {\n                result = $forceLocales;\n            }\n        }\n        return result ?? fallbackLocale ?? 'en';\n    });\n}\nexport function localeStore(forceLocale, fallbackLocale) {\n    let currentLocale = writable(forceLocale ?? null);\n    let resolvedLocale = resolvedLocaleStore(currentLocale, fallbackLocale);\n    return {\n        ...resolvedLocale,\n        set(value) {\n            currentLocale.set(value);\n        },\n    };\n}\nconst defaultLocaleSettings = {\n    locale: 'en',\n    dictionary: {\n        Ok: 'Ok',\n        Cancel: 'Cancel',\n        Date: {\n            Start: 'Start',\n            End: 'End',\n            Empty: 'Empty',\n            Day: 'Day',\n            DayTime: 'Day Time',\n            Time: 'Time',\n            Week: 'Week',\n            BiWeek: 'Bi-Week',\n            Month: 'Month',\n            Quarter: 'Quarter',\n            CalendarYear: 'Calendar Year',\n            FiscalYearOct: 'Fiscal Year (Oct)',\n            PeriodDay: {\n                Current: 'Today',\n                Last: 'Yesterday',\n                LastX: 'Last {0} days',\n            },\n            PeriodWeek: {\n                Current: 'This week',\n                Last: 'Last week',\n                LastX: 'Last {0} weeks',\n            },\n            PeriodBiWeek: {\n                Current: 'This bi-week',\n                Last: 'Last bi-week',\n                LastX: 'Last {0} bi-weeks',\n            },\n            PeriodMonth: {\n                Current: 'This month',\n                Last: 'Last month',\n                LastX: 'Last {0} months',\n            },\n            PeriodQuarter: {\n                Current: 'This quarter',\n                Last: 'Last quarter',\n                LastX: 'Last {0} quarters',\n            },\n            PeriodQuarterSameLastyear: 'Same quarter last year',\n            PeriodYear: {\n                Current: 'This year',\n                Last: 'Last year',\n                LastX: 'Last {0} years',\n            },\n            PeriodFiscalYear: {\n                Current: 'This fiscal year',\n                Last: 'Last fiscal year',\n                LastX: 'Last {0} fiscal years',\n            },\n        },\n    },\n    formats: {\n        numbers: {\n            defaults: {\n                currency: 'USD',\n                fractionDigits: 2,\n                currencyDisplay: 'symbol',\n            },\n        },\n        dates: {\n            baseParsing: 'MM/dd/yyyy',\n            weekStartsOn: DayOfWeek.Sunday,\n            ordinalSuffixes: {\n                one: 'st',\n                two: 'nd',\n                few: 'rd',\n                other: 'th',\n            },\n            presets: {\n                day: {\n                    short: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric],\n                    default: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric],\n                    long: [DateToken.DayOfMonth_numeric, DateToken.Month_short, DateToken.Year_numeric],\n                },\n                dayTime: {\n                    short: [\n                        DateToken.DayOfMonth_numeric,\n                        DateToken.Month_numeric,\n                        DateToken.Year_numeric,\n                        DateToken.Hour_numeric,\n                        DateToken.Minute_numeric,\n                    ],\n                    default: [\n                        DateToken.DayOfMonth_numeric,\n                        DateToken.Month_numeric,\n                        DateToken.Year_numeric,\n                        DateToken.Hour_2Digit,\n                        DateToken.Minute_2Digit,\n                    ],\n                    long: [\n                        DateToken.DayOfMonth_numeric,\n                        DateToken.Month_numeric,\n                        DateToken.Year_numeric,\n                        DateToken.Hour_2Digit,\n                        DateToken.Minute_2Digit,\n                        DateToken.Second_2Digit,\n                    ],\n                },\n                timeOnly: {\n                    short: [DateToken.Hour_numeric, DateToken.Minute_numeric],\n                    default: [DateToken.Hour_2Digit, DateToken.Minute_2Digit, DateToken.Second_2Digit],\n                    long: [\n                        DateToken.Hour_2Digit,\n                        DateToken.Minute_2Digit,\n                        DateToken.Second_2Digit,\n                        DateToken.MiliSecond_3,\n                    ],\n                },\n                week: {\n                    short: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric],\n                    default: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric],\n                    long: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric],\n                },\n                month: {\n                    short: DateToken.Month_short,\n                    default: DateToken.Month_short,\n                    long: DateToken.Month_long,\n                },\n                monthsYear: {\n                    short: [DateToken.Month_short, DateToken.Year_2Digit],\n                    default: [DateToken.Month_long, DateToken.Year_numeric],\n                    long: [DateToken.Month_long, DateToken.Year_numeric],\n                },\n                year: {\n                    short: DateToken.Year_2Digit,\n                    default: DateToken.Year_numeric,\n                    long: DateToken.Year_numeric,\n                },\n            },\n        },\n    },\n};\n/** Creates a locale settings object, using the `base` locale settings as defaults.\n * If omitted, the `en` locale is used as the base. */\nexport function createLocaleSettings(localeSettings, base = defaultLocaleSettings) {\n    // if ordinalSuffixes is specified, we want to make sure that all are empty first\n    if (localeSettings.formats?.dates?.ordinalSuffixes) {\n        localeSettings.formats.dates.ordinalSuffixes = {\n            one: '',\n            two: '',\n            few: '',\n            other: '',\n            zero: '',\n            many: '',\n            ...localeSettings.formats.dates.ordinalSuffixes,\n        };\n    }\n    // if weekStartsOn is not specified, let's default to the local one\n    if (localeSettings.formats?.dates?.weekStartsOn === undefined) {\n        localeSettings = defaultsDeep(localeSettings, {\n            formats: { dates: { weekStartsOn: getWeekStartsOnFromIntl(localeSettings.locale) } },\n        });\n    }\n    return defaultsDeep(localeSettings, base);\n}\nexport const defaultLocale = createLocaleSettings({ locale: 'en' });\nexport function getAllKnownLocales(additionalLocales) {\n    const additional = additionalLocales\n        ? entries(additionalLocales).map(([key, value]) => [key, createLocaleSettings(value)])\n        : [];\n    return { en: defaultLocale, ...fromEntries(additional) };\n}\n", "import { parseISO } from 'date-fns';\nexport var DurationUnits;\n(function (DurationUnits) {\n    DurationUnits[DurationUnits[\"Year\"] = 0] = \"Year\";\n    DurationUnits[DurationUnits[\"Day\"] = 1] = \"Day\";\n    DurationUnits[DurationUnits[\"Hour\"] = 2] = \"Hour\";\n    DurationUnits[DurationUnits[\"Minute\"] = 3] = \"Minute\";\n    DurationUnits[DurationUnits[\"Second\"] = 4] = \"Second\";\n    DurationUnits[DurationUnits[\"Millisecond\"] = 5] = \"Millisecond\";\n})(DurationUnits || (DurationUnits = {}));\n// export enum DurationUnits {\n//   Millisecond = 1,\n//   Second = 1000 * Millisecond,\n//   Minute = 60 * Second,\n//   Hour = 60 * Minute,\n//   Day = 24 * Hour,\n//   Year = 365 * Day,\n// }\nexport function getDuration(start, end, duration) {\n    const startDate = typeof start === 'string' ? parseISO(start) : start;\n    const endDate = typeof end === 'string' ? parseISO(end) : end;\n    const differenceInMs = startDate\n        ? Math.abs(Number(endDate || new Date()) - Number(startDate))\n        : undefined;\n    if (!Number.isFinite(differenceInMs) && duration == null) {\n        return null;\n    }\n    var milliseconds = duration?.milliseconds ?? differenceInMs ?? 0;\n    var seconds = duration?.seconds ?? 0;\n    var minutes = duration?.minutes ?? 0;\n    var hours = duration?.hours ?? 0;\n    var days = duration?.days ?? 0;\n    var years = duration?.years ?? 0;\n    if (milliseconds >= 1000) {\n        const carrySeconds = (milliseconds - (milliseconds % 1000)) / 1000;\n        seconds += carrySeconds;\n        milliseconds = milliseconds - carrySeconds * 1000;\n    }\n    if (seconds >= 60) {\n        const carryMinutes = (seconds - (seconds % 60)) / 60;\n        minutes += carryMinutes;\n        seconds = seconds - carryMinutes * 60;\n    }\n    if (minutes >= 60) {\n        const carryHours = (minutes - (minutes % 60)) / 60;\n        hours += carryHours;\n        minutes = minutes - carryHours * 60;\n    }\n    if (hours >= 24) {\n        const carryDays = (hours - (hours % 24)) / 24;\n        days += carryDays;\n        hours = hours - carryDays * 24;\n    }\n    if (days >= 365) {\n        const carryYears = (days - (days % 365)) / 365;\n        years += carryYears;\n        days = days - carryYears * 365;\n    }\n    return {\n        milliseconds,\n        seconds,\n        minutes,\n        hours,\n        days,\n        years,\n    };\n}\n// See also: https://stackoverflow.com/questions/19700283/how-to-convert-time-milliseconds-to-hours-min-sec-format-in-javascript/33909506\nexport function humanizeDuration(config) {\n    const { start, end, minUnits, totalUnits = 99, variant = 'short' } = config;\n    const duration = getDuration(start, end, config.duration);\n    if (duration === null) {\n        return 'unknown';\n    }\n    var sentenceArr = [];\n    var unitNames = variant === 'short'\n        ? ['y', 'd', 'h', 'm', 's', 'ms']\n        : ['years', 'days', 'hours', 'minutes', 'seconds', 'milliseconds'];\n    var unitNums = [\n        duration.years,\n        duration.days,\n        duration.hours,\n        duration.minutes,\n        duration.seconds,\n        duration.milliseconds,\n    ].filter((x, i) => i <= (minUnits ?? 99));\n    // Combine unit numbers and names\n    for (var i in unitNums) {\n        if (sentenceArr.length >= totalUnits) {\n            break;\n        }\n        const unitNum = unitNums[i];\n        let unitName = unitNames[i];\n        // Hide `0` values unless last unit (and none shown before)\n        if (unitNum !== 0 || (sentenceArr.length === 0 && Number(i) === unitNums.length - 1)) {\n            switch (variant) {\n                case 'short':\n                    sentenceArr.push(unitNum + unitName);\n                    break;\n                case 'long':\n                    if (unitNum === 1) {\n                        // Trim off plural `s`\n                        unitName = unitName.slice(0, -1);\n                    }\n                    sentenceArr.push(unitNum + ' ' + unitName);\n                    break;\n            }\n        }\n    }\n    const sentence = sentenceArr.join(variant === 'long' ? ' and ' : ' ');\n    return sentence;\n}\n", "import { defaultLocale } from './locale.js';\nimport { omitNil } from './object.js';\nfunction getFormatNumber(settings, style) {\n    const { numbers } = settings.formats;\n    const styleSettings = style && style != 'none' ? numbers[style] : {};\n    return {\n        ...numbers.defaults,\n        ...styleSettings,\n    };\n}\nexport function formatNumber(number, style, options) {\n    return formatNumberWithLocale(defaultLocale, number, style, options);\n}\n// See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/NumberFormat\nexport function formatNumberWithLocale(settings, number, style, options = {}) {\n    if (number == null) {\n        return '';\n    }\n    if (style === 'none') {\n        return `${number}`;\n    }\n    // Determine default style if not provided (undefined or null)\n    if (style == null) {\n        style = Number.isInteger(number) ? 'integer' : 'decimal';\n    }\n    const defaults = getFormatNumber(settings, style);\n    // @ts-expect-error: Determine how to access `NumberFormatOptionsStyleRegistry` and check instead of just `style !=== 'default' below)\n    const formatter = Intl.NumberFormat(settings.locale, {\n        // Let's always starts with all defaults\n        ...defaults,\n        ...(style !== 'default' && {\n            style,\n        }),\n        // Let's shorten min / max with fractionDigits\n        ...{\n            minimumFractionDigits: options.fractionDigits ?? defaults.fractionDigits,\n            maximumFractionDigits: options.fractionDigits ?? defaults.fractionDigits,\n        },\n        // now we bring in user specified options\n        ...omitNil(options),\n        ...(style === 'currencyRound' && {\n            style: 'currency',\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0,\n        }),\n        // Let's overwrite for style=percentRound\n        ...(style === 'percentRound' && {\n            style: 'percent',\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0,\n        }),\n        // Let's overwrite for style=metric\n        ...(style === 'metric' && {\n            style: 'decimal',\n            notation: 'compact',\n            minimumFractionDigits: 0,\n        }),\n        // Let's overwrite for style=integer\n        ...(style === 'integer' && {\n            style: 'decimal',\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0,\n        }),\n    });\n    const value = formatter.format(number);\n    let suffix = options.suffix ?? '';\n    if (suffix && Math.abs(number) >= 2 && options.suffixExtraIfMany !== '') {\n        suffix += options.suffixExtraIfMany ?? 's';\n    }\n    return `${value}${suffix}`;\n}\n/**\n * Clamps value within min and max\n */\nexport function clamp(value, min, max) {\n    return value < min ? min : value > max ? max : value;\n}\n/**\n * Return the number of decimal positions (ex. 123.45 => 2, 123 => 0)\n */\nexport function decimalCount(value) {\n    return value?.toString().split('.')[1]?.length ?? 0;\n}\n/**\n * Round to the number of decimals (ex. round(123.45, 1) => 123.5)\n */\nexport function round(value, decimals) {\n    return Number(value.toFixed(decimals));\n}\n/**\n * Step value while rounding to the nearest step precision (work around float issues such as `0.2` + `0.1`)\n */\nexport function step(value, step) {\n    return round(value + step, decimalCount(step));\n}\n/**\n * Get random number between min and max (inclusive).  See also d3.randomInt()\n */\nexport function randomInteger(min, max) {\n    return Math.floor(Math.random() * (max - min + 1)) + min;\n}\n/**\n * Remainder (n % m) with support for negative numbers\n * See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Remainder#description\n */\nexport function modulo(n, m) {\n    return ((n % m) + m) % m;\n}\n", "import { parseISO } from 'date-fns';\nimport { isStringDate } from './date.js';\n/**\n *  JSON.stringify() with custom handling for `Map` and `Set`.  To be used with `parse()`\n */\nexport function stringify(value) {\n    return JSON.stringify(value, replacer);\n}\nexport function replacer(key, value) {\n    if (value instanceof Map) {\n        return {\n            _type: 'Map',\n            value: Array.from(value.entries()),\n        };\n    }\n    else if (value instanceof Set) {\n        return {\n            _type: 'Set',\n            value: Array.from(value.values()),\n        };\n    }\n    else {\n        return value;\n    }\n}\n/**\n * JSON.parse() with support for restoring `Date`, `Map`, and `Set` instances. `Map` and `Set` require using accompanying `stringify()`\n */\nexport function parse(value) {\n    let result;\n    try {\n        result = JSON.parse(value, reviver);\n    }\n    catch (e) {\n        result = value;\n    }\n    return result;\n}\n/**\n * Convert date strings to Date instances\n */\nexport function reviver(key, value) {\n    if (typeof value === 'string' && isStringDate(value)) {\n        return parseISO(value);\n    }\n    else if (typeof value === 'object' && value !== null) {\n        if (value._type === 'Map') {\n            return new Map(value.value);\n        }\n        else if (value._type === 'Set') {\n            return new Set(value.value);\n        }\n    }\n    return value;\n}\n", "// Since it's not recommended to use `$app/environment` or `import.meta.‍env.SSR`, expose these instead\n// See: https://kit.svelte.dev/docs/packaging\nexport const browser = typeof window !== 'undefined';\nexport const ssr = typeof window === 'undefined';\n", "import { startOfDay, isLeap<PERSON>ear, isAfter, isBefore, subYears } from 'date-fns';\nimport { getDateFuncsByPeriodType, updatePeriodTypeWithWeekStartsOn } from './date.js';\nimport { PeriodType } from './date_types.js';\nfunction formatMsg(settings, type, lastX) {\n    return lastX === 0\n        ? settings.dictionary.Date[type].Current\n        : lastX === 1\n            ? settings.dictionary.Date[type].Last\n            : settings.dictionary.Date[type].LastX.replace('{0}', lastX.toString());\n}\nexport function getDateRangePresets(settings, periodType) {\n    let now = new Date();\n    const today = startOfDay(now);\n    if (settings) {\n        periodType =\n            updatePeriodTypeWithWeekStartsOn(settings.formats.dates.weekStartsOn, periodType) ??\n                periodType;\n    }\n    const { start, end, add } = getDateFuncsByPeriodType(settings, periodType);\n    switch (periodType) {\n        case PeriodType.Day: {\n            const last = start(add(today, -1));\n            return [0, 1, 3, 7, 14, 30].map((lastX) => {\n                return {\n                    label: formatMsg(settings, 'PeriodDay', lastX),\n                    value: {\n                        from: add(last, -lastX + 1),\n                        to: lastX === 0 ? end(today) : end(last),\n                        periodType,\n                    },\n                };\n            });\n        }\n        case PeriodType.WeekSun:\n        case PeriodType.WeekMon:\n        case PeriodType.WeekTue:\n        case PeriodType.WeekWed:\n        case PeriodType.WeekThu:\n        case PeriodType.WeekFri:\n        case PeriodType.WeekSat: {\n            const last = start(add(today, -1));\n            return [0, 1, 2, 4, 6].map((lastX) => {\n                return {\n                    label: formatMsg(settings, 'PeriodWeek', lastX),\n                    value: {\n                        from: add(last, -lastX + 1),\n                        to: lastX === 0 ? end(today) : end(last),\n                        periodType,\n                    },\n                };\n            });\n        }\n        case PeriodType.BiWeek1Sun:\n        case PeriodType.BiWeek1Mon:\n        case PeriodType.BiWeek1Tue:\n        case PeriodType.BiWeek1Wed:\n        case PeriodType.BiWeek1Thu:\n        case PeriodType.BiWeek1Fri:\n        case PeriodType.BiWeek1Sat:\n        case PeriodType.BiWeek2Sun:\n        case PeriodType.BiWeek2Mon:\n        case PeriodType.BiWeek2Tue:\n        case PeriodType.BiWeek2Wed:\n        case PeriodType.BiWeek2Thu:\n        case PeriodType.BiWeek2Fri:\n        case PeriodType.BiWeek2Sat: {\n            const last = start(add(today, -1));\n            return [0, 1, 2, 4, 6].map((lastX) => {\n                return {\n                    label: formatMsg(settings, 'PeriodBiWeek', lastX),\n                    value: {\n                        from: add(last, -lastX + 1),\n                        to: lastX === 0 ? end(today) : end(last),\n                        periodType,\n                    },\n                };\n            });\n        }\n        case PeriodType.Month: {\n            const last = start(add(today, -1));\n            return [0, 1, 2, 3, 6, 12].map((lastX) => {\n                return {\n                    label: formatMsg(settings, 'PeriodMonth', lastX),\n                    value: {\n                        from: add(last, -lastX + 1),\n                        to: lastX === 0 ? end(today) : end(last),\n                        periodType,\n                    },\n                };\n            });\n        }\n        case PeriodType.Quarter: {\n            const last = start(add(today, -1));\n            return [0, 1, -1, 4, 12].map((lastX) => {\n                // Special thing\n                if (lastX === -1) {\n                    return {\n                        label: settings.dictionary.Date.PeriodQuarterSameLastyear,\n                        value: {\n                            from: start(add(today, -4)),\n                            to: end(add(today, -4)),\n                            periodType,\n                        },\n                    };\n                }\n                return {\n                    label: formatMsg(settings, 'PeriodQuarter', lastX),\n                    value: {\n                        from: add(last, -lastX + 1),\n                        to: lastX === 0 ? end(today) : end(last),\n                        periodType,\n                    },\n                };\n            });\n        }\n        case PeriodType.CalendarYear: {\n            const last = start(add(today, -1));\n            return [0, 1, 3, 5].map((lastX) => {\n                return {\n                    label: formatMsg(settings, 'PeriodYear', lastX),\n                    value: {\n                        from: add(last, -lastX + 1),\n                        to: lastX === 0 ? end(today) : end(last),\n                        periodType,\n                    },\n                };\n            });\n        }\n        case PeriodType.FiscalYearOctober: {\n            const last = start(add(today, -1));\n            return [0, 1, 3, 5].map((lastX) => {\n                return {\n                    label: formatMsg(settings, 'PeriodFiscalYear', lastX),\n                    value: {\n                        from: add(last, -lastX + 1),\n                        to: lastX === 0 ? end(today) : end(last),\n                        periodType,\n                    },\n                };\n            });\n        }\n        default: {\n            return [];\n        }\n    }\n}\nexport function getPreviousYearPeriodOffset(periodType, options) {\n    switch (periodType) {\n        case PeriodType.Day:\n            // If year of reference date is a leap year and is on/after 2/29\n            // or\n            // if year before reference date is a leap year and is before 2/29\n            const adjustForLeapYear = options?.referenceDate\n                ? (isLeapYear(options?.referenceDate) &&\n                    isAfter(options?.referenceDate, new Date(options?.referenceDate.getFullYear(), /*Feb*/ 1, 28))) ||\n                    (isLeapYear(subYears(options?.referenceDate, 1)) &&\n                        isBefore(options?.referenceDate, new Date(options?.referenceDate.getFullYear(), /*Feb*/ 1, 29)))\n                : false;\n            return options?.alignDayOfWeek\n                ? -364 // Align day of week is always 364 days (52 *7).\n                : adjustForLeapYear\n                    ? -366\n                    : -365;\n        case PeriodType.WeekSun:\n        case PeriodType.WeekMon:\n        case PeriodType.WeekTue:\n        case PeriodType.WeekWed:\n        case PeriodType.WeekThu:\n        case PeriodType.WeekFri:\n        case PeriodType.WeekSat:\n            return -52;\n        case PeriodType.BiWeek1Sun:\n        case PeriodType.BiWeek1Mon:\n        case PeriodType.BiWeek1Tue:\n        case PeriodType.BiWeek1Wed:\n        case PeriodType.BiWeek1Thu:\n        case PeriodType.BiWeek1Fri:\n        case PeriodType.BiWeek1Sat:\n        case PeriodType.BiWeek2Sun:\n        case PeriodType.BiWeek2Mon:\n        case PeriodType.BiWeek2Tue:\n        case PeriodType.BiWeek2Wed:\n        case PeriodType.BiWeek2Thu:\n        case PeriodType.BiWeek2Fri:\n        case PeriodType.BiWeek2Sat:\n            return -26;\n        case PeriodType.Month:\n            return -12;\n        case PeriodType.Quarter:\n            return -4;\n        case PeriodType.CalendarYear:\n        case PeriodType.FiscalYearOctober:\n            return -1;\n    }\n}\nexport function getPeriodComparisonOffset(settings, view, period) {\n    if (period == null || period.from == null || period.to == null || period.periodType == null) {\n        throw new Error('Period must be defined to calculate offset');\n    }\n    switch (view) {\n        case 'prevPeriod':\n            const dateFuncs = getDateFuncsByPeriodType(settings, period.periodType);\n            return dateFuncs.difference(period.from, period.to) - 1; // Difference counts full days, need additoinal offset\n        case 'prevYear':\n            return getPreviousYearPeriodOffset(period.periodType, {\n                referenceDate: period.from,\n            });\n        case 'fiftyTwoWeeksAgo':\n            return getPreviousYearPeriodOffset(period.periodType, {\n                alignDayOfWeek: true,\n            });\n        default:\n            throw new Error('Unhandled period offset: ' + view);\n    }\n}\n", "/**\n * Get the value at path of Map.  Useful for nested maps (d3-array group, etc).\n * Similar to lodash get() but for Map instead of Object\n */\nexport function get(map, path) {\n    let key = undefined;\n    let value = map;\n    const currentPath = [...path]; // Copy since .shift() mutates original array\n    while ((key = currentPath.shift())) {\n        if (value instanceof Map && value.has(key)) {\n            value = value.get(key);\n        }\n        else {\n            return undefined;\n        }\n    }\n    return value;\n}\n", "import { rollup } from 'd3-array';\nimport { get, isFunction } from 'lodash-es';\nexport default function (data, reduce, keys = [], emptyKey = 'Unknown') {\n    // TODO: Fix object[] type if needed\n    // if (keys.length === 0) {\n    //   return data;\n    // }\n    const keyFuncs = keys.map((key) => {\n        if (isFunction(key)) {\n            return key;\n        }\n        else if (typeof key === 'string') {\n            return (d) => get(d, key) || emptyKey;\n        }\n        else {\n            return () => 'Overall';\n        }\n    });\n    return rollup(data, reduce, ...keyFuncs);\n}\n", "// See: routify's helpers: https://github.com/roxiness/routify/blob/9a1b7f5f8fc950a344cf20f7cbaa760593ded8fb/runtime/helpers.js#L244-L268\nexport function url(currentUrl, path) {\n    // console.log({ $page, path });\n    if (path == null) {\n        return path;\n    }\n    else if (path.match(/^\\.\\.?\\//)) {\n        // Relative path (starts wtih `./` or `../`)\n        // console.log('relative path');\n        let [, breadcrumbs, relativePath] = path.match(/^([\\.\\/]+)(.*)/);\n        let dir = currentUrl.pathname.replace(/\\/$/, '');\n        // console.log({ dir, breadcrumbs, relativePath });\n        const traverse = breadcrumbs.match(/\\.\\.\\//g) || [];\n        // if this is a page, we want to traverse one step back to its folder\n        // if (component.isPage) traverse.push(null)\n        traverse.forEach(() => (dir = dir.replace(/\\/[^\\/]+\\/?$/, '')));\n        path = `${dir}/${relativePath}`.replace(/\\/$/, '');\n        path = path || '/'; // empty means root\n        // console.groupEnd();\n    }\n    else if (path.match(/^\\//)) {\n        // Absolute path (starts with `/`)\n        // console.log('absoute path');\n        return path;\n    }\n    else {\n        // Unknown (no named path)\n        return path;\n    }\n    // console.log({ path });\n    return path;\n}\nexport function isActive(currentUrl, path) {\n    if (path === '/') {\n        // home must be direct match (otherwise matches all)\n        return currentUrl.pathname === path;\n    }\n    else {\n        // Matches full path next character is `/`\n        return currentUrl.pathname.match(path + '($|\\\\/)') != null;\n    }\n}\n", "import { keys } from './typeHelpers.js';\nimport { parse, stringify } from './json.js';\nimport { isEmptyObject } from './object.js';\n// See: https://github.com/pbeshai/serialize-query-params/blob/master/src/serialize.ts\n/**\n * Interprets an encoded string and returns either the string or null/undefined if not available.\n * Ignores array inputs (takes just first element in array)\n * @param input encoded string\n */\nfunction getEncodedValue(input, allowEmptyString) {\n    if (input == null) {\n        return input;\n    }\n    // '' or []\n    if (input.length === 0 && (!allowEmptyString || (allowEmptyString && input !== ''))) {\n        return null;\n    }\n    const str = input instanceof Array ? input[0] : input;\n    if (str == null) {\n        return str;\n    }\n    if (!allowEmptyString && str === '') {\n        return null;\n    }\n    return str;\n}\n/**\n * Interprets an encoded string and return null/undefined or an array with\n * the encoded string contents\n * @param input encoded string\n */\nfunction getEncodedValueArray(input) {\n    if (input == null) {\n        return input;\n    }\n    return input instanceof Array ? input : input === '' ? [] : [input];\n}\n/**\n * Encodes a date as a string in YYYY-MM-DD format.\n *\n * @param {Date} date\n * @return {String} the encoded date\n */\nexport function encodeDate(date) {\n    if (date == null) {\n        return date;\n    }\n    const year = date.getFullYear();\n    const month = date.getMonth() + 1;\n    const day = date.getDate();\n    return `${year}-${month < 10 ? `0${month}` : month}-${day < 10 ? `0${day}` : day}`;\n}\n/**\n * Converts a date in the format 'YYYY-mm-dd...' into a proper date, because\n * new Date() does not do that correctly. The date can be as complete or incomplete\n * as necessary (aka, '2015', '2015-10', '2015-10-01').\n * It will not work for dates that have times included in them.\n *\n * If an array is provided, only the first entry is used.\n *\n * @param  {String} input String date form like '2015-10-01'\n * @return {Date} parsed date\n */\nexport function decodeDate(input) {\n    const dateString = getEncodedValue(input);\n    if (dateString == null)\n        return dateString;\n    const parts = dateString.split('-');\n    // may only be a year so won't even have a month\n    if (parts[1] != null) {\n        parts[1] -= 1; // Note: months are 0-based\n    }\n    else {\n        // just a year, set the month and day to the first\n        parts[1] = 0;\n        parts[2] = 1;\n    }\n    const decoded = new Date(...parts);\n    if (isNaN(decoded.getTime())) {\n        return null;\n    }\n    return decoded;\n}\n/**\n * Encodes a date as a string in ISO 8601 (\"2019-05-28T10:58:40Z\") format.\n *\n * @param {Date} date\n * @return {String} the encoded date\n */\nexport function encodeDateTime(date) {\n    if (date == null) {\n        return date;\n    }\n    return date.toISOString();\n}\n/**\n * Converts a date in the https://en.wikipedia.org/wiki/ISO_8601 format.\n * For allowed inputs see specs:\n *  - https://tools.ietf.org/html/rfc2822#page-14\n *  - http://www.ecma-international.org/ecma-262/5.1/#sec-*********\n *\n * If an array is provided, only the first entry is used.\n *\n * @param  {String} input String date form like '1995-12-17T03:24:00'\n * @return {Date} parsed date\n */\nexport function decodeDateTime(input) {\n    const dateString = getEncodedValue(input);\n    if (dateString == null)\n        return dateString;\n    const decoded = new Date(dateString);\n    if (isNaN(decoded.getTime())) {\n        return null;\n    }\n    return decoded;\n}\n/**\n * Encodes a boolean as a string. true -> \"1\", false -> \"0\".\n *\n * @param {Boolean} bool\n * @return {String} the encoded boolean\n */\nexport function encodeBoolean(bool) {\n    if (bool == null) {\n        return bool;\n    }\n    return bool ? '1' : '0';\n}\n/**\n * Decodes a boolean from a string. \"1\" -> true, \"0\" -> false.\n * Everything else maps to undefined.\n *\n * If an array is provided, only the first entry is used.\n *\n * @param {String} input the encoded boolean string\n * @return {Boolean} the boolean value\n */\nexport function decodeBoolean(input) {\n    const boolStr = getEncodedValue(input);\n    if (boolStr == null)\n        return boolStr;\n    if (boolStr === '1') {\n        return true;\n    }\n    else if (boolStr === '0') {\n        return false;\n    }\n    return null;\n}\n/**\n * Encodes a number as a string.\n *\n * @param {Number} num\n * @return {String} the encoded number\n */\nexport function encodeNumber(num) {\n    if (num == null) {\n        return num;\n    }\n    return String(num);\n}\n/**\n * Decodes a number from a string. If the number is invalid,\n * it returns undefined.\n *\n * If an array is provided, only the first entry is used.\n *\n * @param {String} input the encoded number string\n * @return {Number} the number value\n */\nexport function decodeNumber(input) {\n    const numStr = getEncodedValue(input);\n    if (numStr == null)\n        return numStr;\n    if (numStr === '')\n        return null;\n    const result = +numStr;\n    return result;\n}\n/**\n * Encodes a string while safely handling null and undefined values.\n *\n * @param {String} str a string to encode\n * @return {String} the encoded string\n */\nexport function encodeString(str) {\n    if (str == null) {\n        return str;\n    }\n    return String(str);\n}\n/**\n * Decodes a string while safely handling null and undefined values.\n *\n * If an array is provided, only the first entry is used.\n *\n * @param {String} input the encoded string\n * @return {String} the string value\n */\nexport function decodeString(input) {\n    const str = getEncodedValue(input, true);\n    if (str == null)\n        return str;\n    return String(str);\n}\n/**\n * Decodes an enum value while safely handling null and undefined values.\n *\n * If an array is provided, only the first entry is used.\n *\n * @param {String} input the encoded string\n * @param {String[]} enumValues allowed enum values\n * @return {String} the string value from enumValues\n */\nexport function decodeEnum(input, enumValues) {\n    const str = decodeString(input);\n    if (str == null)\n        return str;\n    return enumValues.includes(str) ? str : undefined;\n}\n/**\n * Encodes anything as a JSON string.\n *\n * @param {Any} any The thing to be encoded\n * @return {String} The JSON string representation of any\n */\nexport function encodeJson(any) {\n    if (any == null) {\n        return any;\n    }\n    return stringify(any);\n}\n/**\n * Decodes a JSON string into javascript.\n *\n * If an array is provided, only the first entry is used.\n *\n * Restores Date strings to date objects\n *\n * @param {String} input The JSON string representation\n * @return {Any} The javascript representation\n */\nexport function decodeJson(input) {\n    const jsonStr = getEncodedValue(input);\n    if (jsonStr == null)\n        return jsonStr;\n    let result = null;\n    try {\n        result = parse(jsonStr);\n    }\n    catch (e) {\n        /* ignore errors, returning undefined */\n    }\n    return result;\n}\n/**\n * Encodes an array as a JSON string.\n *\n * @param {Array} array The array to be encoded\n * @return {String[]} The array of strings to be put in the URL\n * as repeated query parameters\n */\nexport function encodeArray(array) {\n    if (array == null) {\n        return array;\n    }\n    return array;\n}\n/**\n * Decodes an array or singular value and returns it as an array\n * or undefined if falsy. Filters out undefined values.\n *\n * @param {String | Array} input The input value\n * @return {Array} The javascript representation\n */\nexport function decodeArray(input) {\n    const arr = getEncodedValueArray(input);\n    if (arr == null)\n        return arr;\n    return arr;\n}\n/**\n * Encodes a numeric array as a JSON string.\n *\n * @param {Array} array The array to be encoded\n * @return {String[]} The array of strings to be put in the URL\n * as repeated query parameters\n */\nexport function encodeNumericArray(array) {\n    if (array == null) {\n        return array;\n    }\n    return array.map(String);\n}\n/**\n * Decodes an array or singular value and returns it as an array\n * or undefined if falsy. Filters out undefined and NaN values.\n *\n * @param {String | Array} input The input value\n * @return {Array} The javascript representation\n */\nexport function decodeNumericArray(input) {\n    const arr = decodeArray(input);\n    if (arr == null)\n        return arr;\n    return arr.map((d) => (d === '' || d == null ? null : +d));\n}\n/**\n * Encodes an array as a delimited string. For example,\n * ['a', 'b'] -> 'a_b' with entrySeparator='_'\n *\n * @param array The array to be encoded\n * @param entrySeparator The string used to delimit entries\n * @return The array as a string with elements joined by the\n * entry separator\n */\nexport function encodeDelimitedArray(array, entrySeparator = '_') {\n    if (array == null) {\n        return array;\n    }\n    return array.join(entrySeparator);\n}\n/**\n * Decodes a delimited string into javascript array. For example,\n * 'a_b' -> ['a', 'b'] with entrySeparator='_'\n *\n * If an array is provided as input, only the first entry is used.\n *\n * @param {String} input The JSON string representation\n * @param entrySeparator The array as a string with elements joined by the\n * entry separator\n * @return {Array} The javascript representation\n */\nexport function decodeDelimitedArray(input, entrySeparator = '_') {\n    const arrayStr = getEncodedValue(input, true);\n    if (arrayStr == null)\n        return arrayStr;\n    if (arrayStr === '')\n        return [];\n    return arrayStr.split(entrySeparator);\n}\n/**\n * Encodes a numeric array as a delimited string. (alias of encodeDelimitedArray)\n * For example, [1, 2] -> '1_2' with entrySeparator='_'\n *\n * @param {Array} array The array to be encoded\n * @return {String} The JSON string representation of array\n */\nexport const encodeDelimitedNumericArray = encodeDelimitedArray;\n/**\n * Decodes a delimited string into javascript array where all entries are numbers\n * For example, '1_2' -> [1, 2] with entrySeparator='_'\n *\n * If an array is provided as input, only the first entry is used.\n *\n * @param {String} jsonStr The JSON string representation\n * @return {Array} The javascript representation\n */\nexport function decodeDelimitedNumericArray(arrayStr, entrySeparator = '_') {\n    const decoded = decodeDelimitedArray(arrayStr, entrySeparator);\n    if (decoded == null)\n        return decoded;\n    return decoded.map((d) => (d === '' || d == null ? null : +d));\n}\n/**\n * Encode simple objects as readable strings.\n *\n * For example { foo: bar, boo: baz } -> \"foo-bar_boo-baz\"\n *\n * @param {Object} object The object to encode\n * @param {String} keyValSeparator=\"-\" The separator between keys and values\n * @param {String} entrySeparator=\"_\" The separator between entries\n * @return {String} The encoded object\n */\nexport function encodeObject(obj, keyValSeparator = '-', entrySeparator = '_') {\n    if (obj == null)\n        return obj; // null or undefined\n    if (isEmptyObject(obj))\n        return ''; // {} case\n    return keys(obj)\n        .map((key) => {\n        const value = encodeJson(obj[key]);\n        return `${key}${keyValSeparator}${value}`;\n    })\n        .join(entrySeparator);\n}\n/**\n * Decodes a simple object to javascript. Currently works only for simple,\n * flat objects where values are strings.\n *\n * For example \"foo-bar_boo-baz\" -> { foo: bar, boo: baz }\n *\n * If an array is provided as input, only the first entry is used.\n *\n * @param {String} input The object string to decode\n * @param {String} keyValSeparator=\"-\" The separator between keys and values\n * @param {String} entrySeparator=\"_\" The separator between entries\n * @return {Object} The javascript object\n */\nexport function decodeObject(input, keyValSeparator = '-', entrySeparator = '_') {\n    const objStr = getEncodedValue(input, true);\n    if (objStr == null)\n        return objStr;\n    if (objStr === '')\n        return {};\n    const obj = {};\n    const keyValSeparatorRegExp = new RegExp(`${keyValSeparator}(.*)`);\n    objStr.split(entrySeparator).forEach((entryStr) => {\n        const [key, value] = entryStr.split(keyValSeparatorRegExp);\n        obj[key] = decodeJson(value);\n    });\n    return obj;\n}\n/**\n * Encode simple objects as readable strings. Alias of encodeObject.\n *\n * For example { foo: 123, boo: 521 } -> \"foo-123_boo-521\"\n *\n * @param {Object} object The object to encode\n * @param {String} keyValSeparator=\"-\" The separator between keys and values\n * @param {String} entrySeparator=\"_\" The separator between entries\n * @return {String} The encoded object\n */\nexport const encodeNumericObject = encodeObject;\n/**\n * Decodes a simple object to javascript where all values are numbers.\n * Currently works only for simple, flat objects.\n *\n * For example \"foo-123_boo-521\" -> { foo: 123, boo: 521 }\n *\n * If an array is provided as input, only the first entry is used.\n *\n * @param {String} input The object string to decode\n * @param {String} keyValSeparator=\"-\" The separator between keys and values\n * @param {String} entrySeparator=\"_\" The separator between entries\n * @return {Object} The javascript object\n */\nexport function decodeNumericObject(input, keyValSeparator = '-', entrySeparator = '_') {\n    const decoded = decodeObject(input, keyValSeparator, entrySeparator);\n    if (decoded == null)\n        return decoded;\n    // convert to numbers\n    const decodedNumberObj = {};\n    for (const key of keys(decoded)) {\n        decodedNumberObj[key] = decodeNumber(decoded[key]);\n    }\n    return decodedNumberObj;\n}\n", "import { entries } from './typeHelpers.js';\n/**\n * Convert object to style string\n */\nexport function objectToString(styleObj) {\n    return entries(styleObj)\n        .map(([key, value]) => {\n        if (value) {\n            // Convert camelCase into kaboob-case (ex.  (transformOrigin => transform-origin))\n            const propertyName = key.replace(/([A-Z])/g, '-$1').toLowerCase();\n            return `${propertyName}: ${value};`;\n        }\n        else {\n            return null;\n        }\n    })\n        .filter((x) => x)\n        .join(' ');\n}\n", "import { range } from 'd3-array';\nimport { rgb, hsl, oklch, clampRgb, interpolate, wcagContrast, formatCss, } from 'culori';\nimport { entries, fromEntries, keys } from '@layerstack/utils';\nexport const semanticColors = ['primary', 'secondary', 'accent', 'neutral'];\nexport const stateColors = ['info', 'success', 'warning', 'danger'];\nexport const colors = [...semanticColors, ...stateColors];\nexport const shades = [50, ...range(100, 1000, 100)];\nexport const colorNames = [\n    // Semantic & State colors (ex. `priamry`, 'primary-content`, 'primary-100`, ...)\n    ...colors.flatMap((color) => [\n        color, // default\n        `${color}-content`, // text/content\n        ...shades.map((shade) => `${color}-${shade}`),\n    ]),\n    // Surfaces\n    'surface-100',\n    'surface-200',\n    'surface-300',\n    'surface-content',\n];\n/**\n * Get themes names (`[data-theme=\"...\"]`) split into `light` and `dark` collections determined by `color-scheme` property\n */\nexport function getThemeNames(cssContent) {\n    const themeBlocks = cssContent.split(/\\[data-theme=/);\n    const light = [];\n    const dark = [];\n    // Skip first element as it's content before first theme\n    for (let i = 1; i < themeBlocks.length; i++) {\n        const block = themeBlocks[i];\n        // Extract theme name\n        const nameMatch = block.match(/^\"([^\"]+)\"/);\n        if (!nameMatch)\n            continue;\n        const themeName = nameMatch[1];\n        if (block.includes('color-scheme: dark')) {\n            dark.push(themeName);\n        }\n        else {\n            light.push(themeName);\n        }\n    }\n    return { light, dark };\n}\n/**\n * Generate missing theme colors (if needed), convert names to CSS variables and to a common color space (hsl, oklch, etc)\n */\nexport function processThemeColors(colors, colorSpace) {\n    // TODO: make all semanatic colors optional as well\n    // Generate optional semanatic colors\n    colors['neutral'] ??= colors['neutral-500'] ?? 'oklch(.355192 .032071 262.988584)';\n    // Generate optional state colors\n    colors['info'] ??= colors['info-500'] ?? 'oklch(0.7206 0.191 231.6)';\n    colors['success'] ??= colors['success-500'] ?? 'oklch(64.8% 0.150 160)';\n    colors['warning'] ??= colors['warning-500'] ?? 'oklch(0.8471 0.199 83.87)';\n    colors['danger'] ??= colors['danger-500'] ?? 'oklch(0.7176 0.221 22.18)';\n    // Generate optional content colors\n    for (const color of [...semanticColors, ...stateColors]) {\n        // Add `primary` from `primary-500` if not defined in theme (ex. Skeleton)\n        colors[color] ??= colors[`${color}-500`];\n        colors[`${color}-content`] ??= foregroundColor(colors[color]);\n        // Generate color shades (ex. `primary-500`) if not defined.  Useful for Daisy but not Skeleton themes, for example\n        for (const shade of shades) {\n            const shadeColorName = `${color}-${shade}`;\n            if (!(shadeColorName in colors)) {\n                // Find the next shade above (shade < 500) or below (shade > 500) and use as reference, if available\n                const referenceShade = keys(colors)\n                    .map((key) => {\n                    const [c, s] = String(key).split('-');\n                    return [c, Number(s)];\n                })\n                    .find(([c, s]) => c === color && (s < 500 ? s > shade : s < shade))?.[1] ?? 500;\n                const referenceColor = colors[`${color}-${referenceShade}`] ?? colors[color];\n                if (shade < 500) {\n                    colors[shadeColorName] ??= lightenColor(referenceColor, (referenceShade - shade) / 1000); // 100 == 0.1\n                }\n                else if (shade > 500) {\n                    colors[shadeColorName] ??= darkenColor(colors[color], (shade - referenceShade) / 1000); // 100 == 0.1\n                }\n                else {\n                    colors[shadeColorName] ??= colors[color];\n                }\n            }\n        }\n    }\n    // Generate optional surface colors\n    colors['surface-100'] ??= 'oklch(100 0 0)';\n    colors['surface-200'] ??= darkenColor(colors['surface-100'], 0.07);\n    colors['surface-300'] ??= darkenColor(colors['surface-200'], 0.07);\n    colors['surface-content'] ??= foregroundColor(colors['surface-100']);\n    // Add `color-scheme: \"dark\"` for `dark` theme (if not set)\n    colors['color-scheme'] ??= isDark(colors['surface-content']) ? 'light' : 'dark';\n    const result = fromEntries(entries(colors).map(([name, value]) => {\n        if (colorNames.includes(String(name))) {\n            // Convert each color to common colorspace and add variable\n            return [`--color-${name}`, convertColor(value, colorSpace)];\n        }\n        else {\n            // Additional properties such as `color-scheme` or CSS variable\n            return [name, value];\n        }\n    }));\n    return result;\n}\nfunction round(value, decimals) {\n    if (value) {\n        return Number(value.toFixed(decimals));\n    }\n    else {\n        return 0;\n    }\n}\nfunction isDark(color) {\n    try {\n        if (wcagContrast(color, 'black') < wcagContrast(color, 'white')) {\n            return true;\n        }\n        return false;\n    }\n    catch (e) {\n        return false;\n    }\n}\n/** Lighten or darken color based on contrast of input */\nfunction foregroundColor(color, percentage = 0.8) {\n    try {\n        return isDark(color) ? lightenColor(color, percentage) : darkenColor(color, percentage);\n    }\n    catch (e) {\n        // console.error('Unable to generate foreground color', color);\n    }\n}\nfunction lightenColor(color, percentage) {\n    try {\n        return formatCss(interpolate([color, 'white'], 'oklch')(percentage));\n    }\n    catch (e) {\n        // console.error('Unable to generate lighten color', color);\n    }\n}\nfunction darkenColor(color, percentage) {\n    try {\n        return formatCss(interpolate([color, 'black'], 'oklch')(percentage));\n    }\n    catch (e) {\n        // console.error('Unable to generate darken color', color);\n    }\n}\n/**\n * Convert color to space separated components string\n */\nexport function convertColor(color, colorSpace, decimals = 4) {\n    try {\n        if (colorSpace === 'rgb') {\n            const computedColor = typeof color === 'string' ? rgb(color) : color;\n            if (computedColor) {\n                const { r, g, b } = computedColor;\n                return `rgb(${round(r * 255, decimals)} ${round(g * 255, decimals)} ${round(b * 255, decimals)})`;\n            }\n        }\n        else if (colorSpace === 'hsl') {\n            const computedColor = typeof color === 'string' ? hsl(clampRgb(color)) : color;\n            if (computedColor) {\n                const { h, s, l } = computedColor;\n                return `hsl(${round(h ?? 0, decimals)} ${round(s * 100, decimals)}% ${round(l * 100, decimals)}%)`;\n            }\n        }\n        else if (colorSpace === 'oklch') {\n            const computedColor = typeof color === 'string' ? oklch(clampRgb(color)) : color;\n            if (computedColor) {\n                const { l, c, h } = computedColor;\n                return `oklch(${round(l, decimals)} ${round(c, decimals)} ${round(h ?? 0, decimals)})`;\n            }\n        }\n    }\n    catch (e) {\n        // console.error('Unable to convert color object to string', color);\n    }\n}\n/**\n * Process theme to style variables\n */\nexport function themeStylesString(colors, colorSpace) {\n    const styleProperties = processThemeColors(colors, colorSpace);\n    return entries(styleProperties)\n        .map(([key, value]) => {\n        return `${key}: ${value};`;\n    })\n        .join('\\n');\n}\n/** Return a script tag that will set the initial theme from localStorage. This allows setting\n * the theme before anything starts rendering, even when SSR is in use.\n *\n * This feels a bit weird compared to just placing the function directly in svelte:head,\n * but it's the only way to inject the `darkThemes` array into the function.\n **/\nexport function createHeadSnippet(darkThemes) {\n    const applyInitialStyle = `\n  function applyInitialStyle(darkThemes) {\n    let theme = localStorage.getItem('theme');\n    // Ignore if no dark things registered (default 'dark' removed)\n    if (darkThemes.length > 0) {\n      if (theme) {\n        document.documentElement.dataset.theme = theme;\n        if (darkThemes.includes(theme)) {\n          document.documentElement.classList.add('dark');\n        }\n      } else if (window.matchMedia('(prefers-color-scheme: dark)').matches) {\n        document.documentElement.classList.add('dark');\n      }\n    }\n  }\n  `;\n    let darkThemeList = darkThemes.map((theme) => `'${theme}'`).join(', ');\n    return `<script>${applyInitialStyle}([${darkThemeList}])</script>`;\n}\n", "import clsx from 'clsx';\nimport { extendTailwindMerge } from 'tailwind-merge';\nimport { range } from 'd3-array';\nimport { mergeWith } from 'lodash-es';\n/**\n * Wrapper around `tailwind-merge` and `clsx`\n */\nconst twMerge = extendTailwindMerge({\n    extend: {\n        classGroups: {\n            shadow: [\n                'shadow-border-l',\n                'shadow-border-r',\n                'shadow-border-t',\n                'shadow-border-b',\n                'elevation-none',\n                ...range(1, 25).map((x) => `elevation-${x}`),\n            ],\n        },\n    },\n});\nexport const cls = (...inputs) => twMerge(clsx(...inputs));\nexport const clsMerge = (...inputs) => mergeWith({}, ...inputs.filter(Boolean), (a, b) => twMerge(a, b));\nexport const normalizeClasses = (classes) => {\n    return classes && typeof classes === 'object' ? classes : { root: classes };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAM,cAAc,CAAC,OAAO,QAAQ;AACnC,MAAI,OAAO,UAAU,SAAU;AAG/B,MAAI,QAAQ,GAAG;AACd,WAAO;AAAA,MACN,MAAM;AAAA,MACN,IAAM,SAAS,IAAK,KAAS,SAAS,IAAK,OAAS;AAAA,MACpD,IAAM,SAAS,IAAK,KAAQ,QAAQ,OAAS;AAAA,MAC7C,IAAK,QAAQ,KAAS,SAAS,IAAK,OAAS;AAAA,IAC9C;AAAA,EACD;AAGA,MAAI,QAAQ,GAAG;AACd,WAAO;AAAA,MACN,MAAM;AAAA,MACN,IAAM,SAAS,KAAM,KAAS,SAAS,IAAK,OAAS;AAAA,MACrD,IAAM,SAAS,IAAK,KAAS,SAAS,IAAK,OAAS;AAAA,MACpD,IAAM,SAAS,IAAK,KAAQ,QAAQ,OAAS;AAAA,MAC7C,QAAS,QAAQ,KAAS,SAAS,IAAK,OAAS;AAAA,IAClD;AAAA,EACD;AAGA,MAAI,QAAQ,GAAG;AACd,WAAO;AAAA,MACN,MAAM;AAAA,MACN,IAAK,SAAS,KAAM,OAAQ;AAAA,MAC5B,IAAK,SAAS,IAAK,OAAQ;AAAA,MAC3B,IAAI,QAAQ,OAAQ;AAAA,IACrB;AAAA,EACD;AAGA,MAAI,QAAQ,GAAG;AACd,WAAO;AAAA,MACN,MAAM;AAAA,MACN,IAAK,SAAS,KAAM,OAAQ;AAAA,MAC5B,IAAK,SAAS,KAAM,OAAQ;AAAA,MAC5B,IAAK,SAAS,IAAK,OAAQ;AAAA,MAC3B,QAAQ,QAAQ,OAAQ;AAAA,IACzB;AAAA,EACD;AACD;AAEA,IAAO,sBAAQ;;;AC9Cf,IAAM,QAAQ;AAAA,EACb,WAAW;AAAA,EACX,cAAc;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,eAAe;AAAA,EACf,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA;AAAA;AAAA,EAIR,eAAe;AAAA,EAEf,KAAK;AAAA,EACL,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,aAAa;AACd;AAEA,IAAO,gBAAQ;;;ACtJf,IAAM,aAAa,WAAS;AAC3B,SAAO,oBAAY,cAAM,MAAM,YAAY,CAAC,GAAG,CAAC;AACjD;AAEA,IAAO,qBAAQ;;;ACPf,IAAM,MAAM;AAEZ,IAAM,WAAW,WAAS;AACzB,MAAI;AAEJ,UAAQ,QAAQ,MAAM,MAAM,GAAG,KAC5B,oBAAY,SAAS,MAAM,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,EAAE,MAAM,IACnD;AACJ;AAEA,IAAO,mBAAQ;;;ACHR,IAAM,MAAM;AAGZ,IAAM,WAAW,MAAM,GAAG;AAG1B,IAAM,MAAM,GAAG,GAAG;AAGlB,IAAM,WAAW,MAAM,GAAG;AAG1B,IAAM,UAAU,MAAM,GAAG,KAAK,GAAG;AAGjC,IAAM,eAAe,MAAM,GAAG,KAAK,GAAG;AAGtC,IAAM,MAAM,MAAM,GAAG,uBAAuB,GAAG;AAG/C,IAAM,WAAW,MAAM,GAAG,uBAAuB,GAAG;AAEpD,IAAM,IAAI;AAIV,IAAM,kBAAkB,IAAI,OAAO,MAAM,eAAe,GAAG;;;AC9BlE,IAAM,cAAc,IAAI;AAAA,EACvB,gBAAgB,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,eAAe,OAAO;AAC9D;AAEA,IAAM,cAAc,IAAI;AAAA,EACvB,gBAAgB,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,eAAe,OAAO;AAC9D;AAEA,IAAM,iBAAiB,WAAS;AAC/B,MAAI,MAAM,EAAE,MAAM,MAAM;AACxB,MAAI;AACJ,MAAK,QAAQ,MAAM,MAAM,WAAW,GAAI;AACvC,QAAI,MAAM,CAAC,MAAM,QAAW;AAC3B,UAAI,IAAI,MAAM,CAAC,IAAI;AAAA,IACpB;AACA,QAAI,MAAM,CAAC,MAAM,QAAW;AAC3B,UAAI,IAAI,MAAM,CAAC,IAAI;AAAA,IACpB;AACA,QAAI,MAAM,CAAC,MAAM,QAAW;AAC3B,UAAI,IAAI,MAAM,CAAC,IAAI;AAAA,IACpB;AAAA,EACD,WAAY,QAAQ,MAAM,MAAM,WAAW,GAAI;AAC9C,QAAI,MAAM,CAAC,MAAM,QAAW;AAC3B,UAAI,IAAI,MAAM,CAAC,IAAI;AAAA,IACpB;AACA,QAAI,MAAM,CAAC,MAAM,QAAW;AAC3B,UAAI,IAAI,MAAM,CAAC,IAAI;AAAA,IACpB;AACA,QAAI,MAAM,CAAC,MAAM,QAAW;AAC3B,UAAI,IAAI,MAAM,CAAC,IAAI;AAAA,IACpB;AAAA,EACD,OAAO;AACN,WAAO;AAAA,EACR;AAEA,MAAI,MAAM,CAAC,MAAM,QAAW;AAC3B,QAAI,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,CAAC;AAAA,EACpD,WAAW,MAAM,CAAC,MAAM,QAAW;AAClC,QAAI,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;AAAA,EAC/C;AAEA,SAAO;AACR;AAEA,IAAO,yBAAQ;;;AChDf,IAAM,UAAU,CAAC,OAAO,SACvB,UAAU,SACP,SACA,OAAO,UAAU,WACjB,cAAM,KAAK,IACX,MAAM,SAAS,SACf,QACA,OACA,EAAE,GAAG,OAAO,KAAK,IACjB;AAEJ,IAAO,kBAAQ;;;ACVf,IAAM,YACL,CAAC,cAAc,UACf,YACE,QAAQ,gBAAQ,OAAO,WAAW,OAAO;AAAA;AAAA,EAEvC,MAAM,SAAS;AAAA;AAAA,IAEd;AAAA;AAAA;AAAA;AAAA,IAGF,WAAW,MAAM,IAAI,EAAE,WAAW;AAAA;AAAA,MAEhC,WAAW,MAAM,IAAI,EAAE,WAAW,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA,MAG3C,gBAAgB;AAAA;AAAA,QAEd,WAAW,MAAM,IAAI,EAAE,IAAI,KAAK;AAAA;AAAA;AAAA,QAEhC,WAAW,IAAI,WAAW,EAAE,WAAW,MAAM,IAAI,EAAE,IAAI,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA,IAC9D;AAEL,IAAO,oBAAQ;;;ACvBf,IAAM,aAAa,CAAC;AACpB,IAAM,QAAQ,CAAC;AAEf,IAAM,UAAU,CAAC;AACjB,IAAM,gBAAgB,CAAC;AAEvB,IAAM,WAAW,OAAK;AAEtB,IAAM,UAAU,CAAAA,iBAAc;AAC7B,aAAWA,aAAW,IAAI,IAAI;AAAA,IAC7B,GAAG,WAAWA,aAAW,IAAI;AAAA,IAC7B,GAAGA,aAAW;AAAA,EACf;AAEA,SAAO,KAAKA,aAAW,YAAY,CAAC,CAAC,EAAE,QAAQ,CAAAC,OAAK;AACnD,QAAI,CAAC,WAAWA,EAAC,GAAG;AACnB,iBAAWA,EAAC,IAAI,CAAC;AAAA,IAClB;AACA,eAAWA,EAAC,EAAED,aAAW,IAAI,IAAIA,aAAW,SAASC,EAAC;AAAA,EACvD,CAAC;AAGD,MAAI,CAACD,aAAW,QAAQ;AACvB,IAAAA,aAAW,SAAS,CAAC;AAAA,EACtB;AAEA,MAAI,CAACA,aAAW,YAAY;AAC3B,IAAAA,aAAW,aAAa,CAAC;AAAA,EAC1B;AAEA,EAAAA,aAAW,SAAS,QAAQ,aAAW;AAEtC,QAAIA,aAAW,OAAO,OAAO,MAAM,QAAW;AAC7C,MAAAA,aAAW,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC;AAAA,IACnC;AAEA,QAAI,CAACA,aAAW,YAAY,OAAO,GAAG;AACrC,YAAM,IAAI,MAAM,6BAA6B,OAAO,EAAE;AAAA,IACvD;AAEA,QAAI,OAAOA,aAAW,YAAY,OAAO,MAAM,YAAY;AAC1D,MAAAA,aAAW,YAAY,OAAO,IAAI;AAAA,QACjC,KAAKA,aAAW,YAAY,OAAO;AAAA,MACpC;AAAA,IACD;AAEA,QAAI,CAACA,aAAW,YAAY,OAAO,EAAE,OAAO;AAC3C,MAAAA,aAAW,YAAY,OAAO,EAAE,QAAQ;AAAA,IACzC;AAAA,EACD,CAAC;AAED,QAAMA,aAAW,IAAI,IAAIA;AACzB,GAACA,aAAW,SAAS,CAAC,GAAG,QAAQ,YAAU;AAC1C,cAAU,QAAQA,aAAW,IAAI;AAAA,EAClC,CAAC;AAED,SAAO,kBAAUA,aAAW,IAAI;AACjC;AAEA,IAAM,UAAU,UAAQ,MAAM,IAAI;AAElC,IAAM,YAAY,CAAC,QAAQ,SAAS;AACnC,MAAI,OAAO,WAAW,UAAU;AAC/B,QAAI,CAAC,MAAM;AACV,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC5D;AACA,kBAAc,MAAM,IAAI;AAAA,EACzB,WAAW,OAAO,WAAW,YAAY;AACxC,QAAI,QAAQ,QAAQ,MAAM,IAAI,GAAG;AAChC,cAAQ,KAAK,MAAM;AAAA,IACpB;AAAA,EACD;AACD;;;ACvEA,IAAM,sBAAsB;AAG5B,IAAM,iBAAiB;AAEhB,IAAM,MAAM;AAAA,EAClB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AACR;AAEA,IAAI,KAAK;AAMT,SAAS,OAAO,OAAO;AACtB,MAAI,KAAK,MAAM,EAAE;AACjB,MAAI,MAAM,MAAM,KAAK,CAAC;AACtB,MAAI,OAAO,OAAO,OAAO,KAAK;AAC7B,WAAO,KAAK,KAAK,GAAG,KAAM,QAAQ,OAAO,KAAK,KAAK,MAAM,KAAK,CAAC,CAAC;AAAA,EACjE;AACA,MAAI,OAAO,KAAK;AACf,WAAO,KAAK,KAAK,GAAG;AAAA,EACrB;AACA,SAAO,KAAK,KAAK,EAAE;AACpB;AAMA,SAAS,SAAS,OAAO;AACxB,MAAI,MAAM,MAAM,QAAQ;AACvB,WAAO;AAAA,EACR;AACA,MAAI,KAAK,MAAM,EAAE;AACjB,MAAI,oBAAoB,KAAK,EAAE,GAAG;AACjC,WAAO;AAAA,EACR;AACA,MAAI,OAAO,KAAK;AACf,QAAI,MAAM,SAAS,KAAK,GAAG;AAC1B,aAAO;AAAA,IACR;AACA,QAAI,MAAM,MAAM,KAAK,CAAC;AACtB,QAAI,QAAQ,OAAO,oBAAoB,KAAK,GAAG,GAAG;AACjD,aAAO;AAAA,IACR;AACA,WAAO;AAAA,EACR;AACA,SAAO;AACR;AAOA,IAAM,UAAU;AAAA,EACf,KAAK;AAAA,EACL,KAAK,MAAM,KAAK;AAAA,EAChB,MAAM,IAAI;AAAA,EACV,MAAM;AACP;AAEA,SAASE,KAAI,OAAO;AACnB,MAAI,QAAQ;AACZ,MAAI,MAAM,EAAE,MAAM,OAAO,MAAM,EAAE,MAAM,KAAK;AAC3C,aAAS,MAAM,IAAI;AAAA,EACpB;AACA,WAAS,OAAO,KAAK;AACrB,MAAI,MAAM,EAAE,MAAM,OAAO,KAAK,KAAK,MAAM,KAAK,CAAC,CAAC,GAAG;AAClD,aAAS,MAAM,IAAI,IAAI,OAAO,KAAK;AAAA,EACpC;AACA,MAAI,MAAM,EAAE,MAAM,OAAO,MAAM,EAAE,MAAM,KAAK;AAC3C,SACE,MAAM,KAAK,CAAC,MAAM,OAAO,MAAM,KAAK,CAAC,MAAM,QAC5C,KAAK,KAAK,MAAM,KAAK,CAAC,CAAC,GACtB;AACD,eAAS,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,OAAO,KAAK;AAAA,IAClD,WAAW,KAAK,KAAK,MAAM,KAAK,CAAC,CAAC,GAAG;AACpC,eAAS,MAAM,IAAI,IAAI,OAAO,KAAK;AAAA,IACpC;AAAA,EACD;AACA,MAAI,SAAS,KAAK,GAAG;AACpB,QAAI,KAAK,MAAM,KAAK;AACpB,QAAI,OAAO,SAAS,OAAO,SAAS,OAAO,UAAU,OAAO,QAAQ;AACnE,aAAO,EAAE,MAAM,IAAI,KAAK,OAAO,QAAQ,QAAQ,EAAE,EAAE;AAAA,IACpD;AACA,WAAO;AAAA,EACR;AACA,MAAI,MAAM,EAAE,MAAM,KAAK;AACtB;AACA,WAAO,EAAE,MAAM,IAAI,YAAY,OAAO,CAAC,MAAM;AAAA,EAC9C;AACA,SAAO,EAAE,MAAM,IAAI,QAAQ,OAAO,CAAC,MAAM;AAC1C;AAKA,SAAS,OAAO,OAAO;AACtB,MAAI,IAAI;AACR,SAAO,KAAK,KAAK,MAAM,EAAE,CAAC,GAAG;AAC5B,SAAK,MAAM,IAAI;AAAA,EAChB;AACA,SAAO;AACR;AAKA,SAAS,MAAM,OAAO;AACrB,MAAI,IAAI;AACR,SAAO,KAAK,MAAM,UAAU,eAAe,KAAK,MAAM,EAAE,CAAC,GAAG;AAC3D,SAAK,MAAM,IAAI;AAAA,EAChB;AACA,SAAO;AACR;AAKA,SAAS,UAAU,OAAO;AACzB,MAAI,IAAI,MAAM,KAAK;AACnB,MAAI,MAAM,EAAE,MAAM,KAAK;AACtB;AACA,WAAO,EAAE,MAAM,IAAI,UAAU,OAAO,EAAE;AAAA,EACvC;AACA,MAAI,MAAM,QAAQ;AACjB,WAAO,EAAE,MAAM,IAAI,MAAM,OAAO,OAAU;AAAA,EAC3C;AACA,SAAO,EAAE,MAAM,IAAI,OAAO,OAAO,EAAE;AACpC;AAEO,SAAS,SAAS,MAAM,IAAI;AAClC,MAAI,QAAQ,IAAI,KAAK;AACrB,MAAI,SAAS,CAAC;AACd,MAAI;AAGJ,OAAK;AAEL,SAAO,KAAK,MAAM,QAAQ;AACzB,SAAK,MAAM,IAAI;AAKf,QAAI,OAAO,QAAQ,OAAO,OAAQ,OAAO,KAAK;AAC7C,aACC,KAAK,MAAM,WACV,MAAM,EAAE,MAAM,QAAQ,MAAM,EAAE,MAAM,OAAQ,MAAM,EAAE,MAAM,MAC1D;AACD;AAAA,MACD;AACA;AAAA,IACD;AAEA,QAAI,OAAO,KAAK;AACf,aAAO;AAAA,IACR;AAEA,QAAI,OAAO,KAAK;AACf,aAAO,KAAK,EAAE,MAAM,IAAI,WAAW,CAAC;AACpC;AAAA,IACD;AAEA,QAAI,OAAO,KAAK;AACf;AACA,UAAI,OAAO,KAAK,GAAG;AAClB,eAAO,KAAKA,KAAI,KAAK,CAAC;AACtB;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAEA,QAAI,OAAO,KAAK;AACf;AACA,UAAI,OAAO,KAAK,GAAG;AAClB,eAAO,KAAKA,KAAI,KAAK,CAAC;AACtB;AAAA,MACD;AACA,UAAI,SAAS,KAAK,GAAG;AACpB,eAAO,KAAK,EAAE,MAAM,IAAI,OAAO,OAAO,MAAM,KAAK,EAAE,CAAC;AACpD;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAEA,QAAI,OAAO,KAAK;AACf;AACA,UAAI,OAAO,KAAK,GAAG;AAClB,eAAO,KAAKA,KAAI,KAAK,CAAC;AACtB;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAEA,QAAI,OAAO,KAAK;AACf,aACC,KAAK,MAAM,WACV,MAAM,EAAE,MAAM,QAAQ,MAAM,EAAE,MAAM,OAAQ,MAAM,EAAE,MAAM,MAC1D;AACD;AAAA,MACD;AACA,UAAI;AACJ,UAAI,OAAO,KAAK,GAAG;AAClB,gBAAQA,KAAI,KAAK;AACjB,YAAI,MAAM,SAAS,IAAI,KAAK;AAC3B,iBAAO,KAAK,EAAE,MAAM,IAAI,OAAO,OAAO,MAAM,CAAC;AAC7C;AAAA,QACD;AAAA,MACD;AACA,UAAI,SAAS,KAAK,GAAG;AACpB,YAAI,MAAM,KAAK,MAAM,QAAQ;AAC5B,iBAAO,KAAK;AAAA,YACX,MAAM,IAAI;AAAA,YACV,OAAO,EAAE,MAAM,IAAI,MAAM,OAAO,OAAU;AAAA,UAC3C,CAAC;AACD;AAAA,QACD;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAEA,QAAI,KAAK,KAAK,EAAE,GAAG;AAClB;AACA,aAAO,KAAKA,KAAI,KAAK,CAAC;AACtB;AAAA,IACD;AAEA,QAAI,oBAAoB,KAAK,EAAE,GAAG;AACjC;AACA,aAAO,KAAK,UAAU,KAAK,CAAC;AAC5B;AAAA,IACD;AAKA,WAAO;AAAA,EACR;AAEA,SAAO;AACR;AAEO,SAAS,iBAAiB,QAAQ;AACxC,SAAO,KAAK;AACZ,MAAI,QAAQ,OAAO,OAAO,IAAI;AAC9B,MAAI,CAAC,SAAS,MAAM,SAAS,IAAI,YAAY,MAAM,UAAU,SAAS;AACrE,WAAO;AAAA,EACR;AACA,UAAQ,OAAO,OAAO,IAAI;AAC1B,MAAI,MAAM,SAAS,IAAI,OAAO;AAC7B,WAAO;AAAA,EACR;AACA,QAAM,OAAO,cAAc,MAAM,KAAK;AACtC,MAAI,CAAC,MAAM;AACV,WAAO;AAAA,EACR;AACA,QAAM,MAAM,EAAE,KAAK;AACnB,QAAM,SAAS,cAAc,QAAQ,KAAK;AAC1C,MAAI,CAAC,QAAQ;AACZ,WAAO;AAAA,EACR;AACA,QAAM,WAAW,QAAQ,IAAI,EAAE;AAC/B,WAAS,KAAK,GAAGC,IAAG,IAAI,KAAK,SAAS,QAAQ,MAAM;AACnD,IAAAA,KAAI,OAAO,EAAE;AACb,SAAK,SAAS,EAAE;AAChB,QAAIA,GAAE,SAAS,IAAI,MAAM;AACxB,UAAI,EAAE,IAAIA,GAAE,SAAS,IAAI,SAASA,GAAE,QAAQA,GAAE,QAAQ;AACtD,UAAI,OAAO,SAAS;AACnB,YAAI,EAAE,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC;AAAA,MAC3C;AAAA,IACD;AAAA,EACD;AACA,SAAO;AACR;AAEA,SAAS,cAAc,QAAQ,YAAY;AAC1C,QAAM,SAAS,CAAC;AAChB,MAAI;AACJ,SAAO,OAAO,KAAK,OAAO,QAAQ;AACjC,YAAQ,OAAO,OAAO,IAAI;AAC1B,QACC,MAAM,SAAS,IAAI,QACnB,MAAM,SAAS,IAAI,UACnB,MAAM,SAAS,IAAI,SACnB,MAAM,SAAS,IAAI,cAClB,cAAc,MAAM,SAAS,IAAI,KACjC;AACD,aAAO,KAAK,KAAK;AACjB;AAAA,IACD;AACA,QAAI,MAAM,SAAS,IAAI,YAAY;AAClC,UAAI,OAAO,KAAK,OAAO,QAAQ;AAC9B,eAAO;AAAA,MACR;AACA;AAAA,IACD;AACA,WAAO;AAAA,EACR;AAEA,MAAI,OAAO,SAAS,KAAK,OAAO,SAAS,GAAG;AAC3C,WAAO;AAAA,EACR;AAEA,MAAI,OAAO,WAAW,GAAG;AACxB,QAAI,OAAO,CAAC,EAAE,SAAS,IAAI,OAAO;AACjC,aAAO;AAAA,IACR;AACA,WAAO,CAAC,IAAI,OAAO,CAAC,EAAE;AAAA,EACvB;AACA,MAAI,OAAO,WAAW,GAAG;AACxB,WAAO,KAAK,EAAE,MAAM,IAAI,MAAM,OAAO,OAAU,CAAC;AAAA,EACjD;AAEA,SAAO,OAAO,MAAM,CAAAA,OAAKA,GAAE,SAAS,IAAI,KAAK,IAAI,SAAS;AAC3D;AAEO,SAAS,kBAAkB,QAAQ,YAAY;AACrD,SAAO,KAAK;AACZ,MAAI,QAAQ,OAAO,OAAO,IAAI;AAC9B,MAAI,CAAC,SAAS,MAAM,SAAS,IAAI,UAAU;AAC1C,WAAO;AAAA,EACR;AACA,MAAI,SAAS,cAAc,QAAQ,UAAU;AAC7C,MAAI,CAAC,QAAQ;AACZ,WAAO;AAAA,EACR;AACA,SAAO,QAAQ,MAAM,KAAK;AAC1B,SAAO;AACR;AAEA,IAAM,QAAQ,WAAS;AACtB,MAAI,OAAO,UAAU,UAAU;AAC9B,WAAO;AAAA,EACR;AACA,QAAM,SAAS,SAAS,KAAK;AAC7B,QAAM,SAAS,SAAS,kBAAkB,QAAQ,IAAI,IAAI;AAC1D,MAAI,SAAS;AACb,MAAI,IAAI;AACR,MAAI,MAAM,QAAQ;AAClB,SAAO,IAAI,KAAK;AACf,SAAK,SAAS,QAAQ,GAAG,EAAE,OAAO,MAAM,OAAO,QAAW;AACzD,aAAO;AAAA,IACR;AAAA,EACD;AACA,SAAO,SAAS,iBAAiB,MAAM,IAAI;AAC5C;AAEA,IAAO,gBAAQ;;;ACvWf,SAAS,SAAS,OAAO,QAAQ;AAChC,MAAI,CAAC,UAAW,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,MAAM,QAAS;AAC7D,WAAO;AAAA,EACR;AACA,QAAM,MAAM,EAAE,MAAM,MAAM;AAC1B,QAAM,CAAC,EAAEC,IAAG,GAAG,GAAG,KAAK,IAAI;AAC3B,MAAIA,GAAE,SAAS,IAAI,OAAO,EAAE,SAAS,IAAI,OAAO,EAAE,SAAS,IAAI,KAAK;AACnE,WAAO;AAAA,EACR;AACA,MAAIA,GAAE,SAAS,IAAI,MAAM;AACxB,QAAI,IAAIA,GAAE,SAAS,IAAI,SAASA,GAAE,QAAQ,MAAMA,GAAE,QAAQ;AAAA,EAC3D;AACA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,IAAI,EAAE,SAAS,IAAI,SAAS,EAAE,QAAQ,MAAM,EAAE,QAAQ;AAAA,EAC3D;AACA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,IAAI,EAAE,SAAS,IAAI,SAAS,EAAE,QAAQ,MAAM,EAAE,QAAQ;AAAA,EAC3D;AACA,MAAI,MAAM,SAAS,IAAI,MAAM;AAC5B,QAAI,QAAQ,KAAK;AAAA,MAChB;AAAA,MACA,KAAK;AAAA,QACJ;AAAA,QACA,MAAM,SAAS,IAAI,SAAS,MAAM,QAAQ,MAAM,QAAQ;AAAA,MACzD;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEA,IAAO,mBAAQ;;;ACjCf,IAAM,mBAAmB,CAAAC,OACxBA,OAAM,gBACH,EAAE,MAAM,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,EAAE,IAC1C;AAEJ,IAAO,2BAAQ;;;ACLf,IAAM,OAAO,CAAC,GAAG,GAAG,MAAM,IAAI,KAAK,IAAI;;;ACAvC,IAAM,cAAc,SAAO;AAC1B,MAAI,UAAU,CAAC;AACf,WAAS,IAAI,GAAG,IAAI,IAAI,SAAS,GAAG,KAAK;AACxC,QAAI,IAAI,IAAI,CAAC;AACb,QAAI,IAAI,IAAI,IAAI,CAAC;AACjB,QAAI,MAAM,UAAa,MAAM,QAAW;AACvC,cAAQ,KAAK,MAAS;AAAA,IACvB,WAAW,MAAM,UAAa,MAAM,QAAW;AAC9C,cAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IACpB,OAAO;AACN,cAAQ,KAAK,MAAM,SAAY,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAAA,IAC/C;AAAA,EACD;AACA,SAAO;AACR;AAEA,IAAM,wBAAwB,kBAAgB,SAAO;AACpD,MAAI,UAAU,YAAY,GAAG;AAC7B,SAAO,OAAK;AACX,QAAIC,OAAM,IAAI,QAAQ;AACtB,QAAI,MAAM,KAAK,IAAI,QAAQ,SAAS,IAAI,KAAK,IAAI,KAAK,MAAMA,IAAG,GAAG,CAAC;AACnE,QAAI,OAAO,QAAQ,GAAG;AACtB,WAAO,SAAS,SACb,SACA,aAAa,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAM,GAAG;AAAA,EAC5C;AACD;;;ACvBO,IAAM,qBAAqB,sBAAsB,IAAI;;;ACH5D,IAAM,aAAa,SAAO;AACzB,MAAI,eAAe;AACnB,MAAI,MAAM,IAAI,IAAI,OAAK;AACtB,QAAI,MAAM,QAAW;AACpB,qBAAe;AACf,aAAO;AAAA,IACR;AACA,WAAO;AAAA,EACR,CAAC;AACD,SAAO,eAAe,MAAM;AAC7B;;;ACEA,IAAM,aAAa;AAAA,EAClB,MAAM;AAAA,EACN,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EACjC,OAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAAA,EACA,WAAW;AAAA,EACX,aAAa;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AAAA,EACA,OAAO;AAAA,EACP,OAAO,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AAAA,EAC1B,OAAO,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AAC3B;AAEA,IAAO,qBAAQ;;;AC1Bf,IAAM,YAAY,CAAC,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC;AAE3E,IAAM,oBAAoB,CAAAC,SAAO;AAChC,MAAIC,KAAI,UAAUD,KAAI,CAAC;AACvB,MAAI,IAAI,UAAUA,KAAI,CAAC;AACvB,MAAI,IAAI,UAAUA,KAAI,CAAC;AACvB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GACC,qBAAqBC,KACrB,qBAAqB,IACrB,qBAAqB;AAAA,IACtB,GACC,oBAAoBA,KACpB,qBAAqB,IACrB,qBAAqB;AAAA,IACtB,GACC,qBAAqBA,KACrB,qBAAqB,IACrB,qBAAqB;AAAA,EACvB;AACA,MAAID,KAAI,UAAU,QAAW;AAC5B,QAAI,QAAQA,KAAI;AAAA,EACjB;AACA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;AC5Bf,IAAM,QAAQ,OAAK,KAAK,IAAI,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC;AAEjE,IAAM,oBAAoB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACjD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAG;AAAA,MACF,IAAI,qBACH,IAAI,qBACJ,qBAAqB;AAAA,IACvB;AAAA,IACA,GAAG;AAAA,MACF,IAAI,sBACH,IAAI,qBACJ,qBAAqB;AAAA,IACvB;AAAA,IACA,GAAG;AAAA,MACF,IAAI,qBACH,IAAI,qBACJ,qBAAqB;AAAA,IACvB;AAAA,EACD;AACA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;ACtCf,IAAM,KAAK,CAACE,KAAI,MAAM;AACrB,QAAMC,OAAM,KAAK,IAAID,EAAC;AACtB,MAAIC,QAAO,SAAS;AACnB,WAAOD,KAAI;AAAA,EACZ;AACA,UAAQ,KAAK,KAAKA,EAAC,KAAK,KAAK,KAAK,KAAKC,OAAM,SAAS,OAAO,GAAG;AACjE;AAEA,IAAM,mBAAmB,CAAC,EAAE,GAAAC,IAAG,GAAG,GAAG,MAAM,MAAM;AAChD,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAG,GAAGA,EAAC;AAAA,IACP,GAAG,GAAG,CAAC;AAAA,IACP,GAAG,GAAG,CAAC;AAAA,EACR;AACA,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;AAEA,IAAO,2BAAQ;;;ACRf,IAAM,oBAAoB,CAAAC,SAAO;AAChC,MAAI,EAAE,GAAAC,IAAG,GAAG,GAAG,MAAM,IAAI,yBAAiBD,IAAG;AAC7C,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GACC,qBAAqBC,KACrB,oBAAoB,IACpB,qBAAqB;AAAA,IACtB,GACC,qBAAqBA,KACrB,oBAAoB,IACpB,qBAAqB;AAAA,IACtB,GACC,qBAAqBA,KACrB,oBAAoB,IACpB,qBAAqB;AAAA,EACvB;AACA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;AClCf,IAAMC,MAAK,CAACC,KAAI,MAAM;AACrB,QAAMC,OAAM,KAAK,IAAID,EAAC;AACtB,MAAIC,OAAM,UAAW;AACpB,YAAQ,KAAK,KAAKD,EAAC,KAAK,MAAM,QAAQ,KAAK,IAAIC,MAAK,IAAI,GAAG,IAAI;AAAA,EAChE;AACA,SAAOD,KAAI;AACZ;AAEA,IAAM,mBAAmB,CAAC,EAAE,GAAAE,IAAG,GAAG,GAAG,MAAM,GAAG,OAAO,UAAU;AAC9D,MAAI,MAAM;AAAA,IACT;AAAA,IACA,GAAGH,IAAGG,EAAC;AAAA,IACP,GAAGH,IAAG,CAAC;AAAA,IACP,GAAGA,IAAG,CAAC;AAAA,EACR;AACA,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;AAEA,IAAO,2BAAQ;;;ACRf,IAAM,oBAAoB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACjD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,yBAAiB;AAAA,IAC1B,GACC,IAAI,qBACJ,IAAI,oBACJ,qBAAqB;AAAA,IACtB,GACC,IAAI,sBACJ,IAAI,qBACJ,qBAAqB;AAAA,IACtB,GACC,IAAI,qBACJ,IAAI,qBACJ,qBAAqB;AAAA,EACvB,CAAC;AACD,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;AC5Bf,IAAMI,cAAa;AAAA,EAClB,GAAG;AAAA,EACH,MAAM;AAAA,EACN,OAAO,CAAC,SAAS;AAAA,EACjB,WAAW;AAAA,EAEX,UAAU;AAAA,IACT,KAAK,WAAS,0BAAkB,0BAAkB,KAAK,CAAC;AAAA,IACxD,OAAO;AAAA,EACR;AAAA,EAEA,QAAQ;AAAA,IACP,KAAK,WAAS,0BAAkB,0BAAkB,KAAK,CAAC;AAAA,IACxD,OAAO;AAAA,EACR;AACD;AAEA,IAAOC,sBAAQD;;;ACxBf,IAAM,eAAe,CAAAE,UAASA,OAAMA,OAAM,OAAO,IAAIA,OAAM,MAAMA;AAEjE,IAAO,uBAAQ;;;ACAf,IAAMC,OAAM,CAAC,MAAMC,QAAO;AACzB,SAAO,KACL,IAAI,CAACD,MAAK,KAAK,QAAQ;AACvB,QAAIA,SAAQ,QAAW;AACtB,aAAOA;AAAA,IACR;AACA,QAAI,aAAa,qBAAaA,IAAG;AACjC,QAAI,QAAQ,KAAK,KAAK,MAAM,CAAC,MAAM,QAAW;AAC7C,aAAO;AAAA,IACR;AACA,WAAOC,IAAG,aAAa,qBAAa,IAAI,MAAM,CAAC,CAAC,CAAC;AAAA,EAClD,CAAC,EACA,OAAO,CAAC,KAAK,SAAS;AACtB,QACC,CAAC,IAAI,UACL,SAAS,UACT,IAAI,IAAI,SAAS,CAAC,MAAM,QACvB;AACD,UAAI,KAAK,IAAI;AACb,aAAO;AAAA,IACR;AACA,QAAI,KAAK,OAAO,IAAI,IAAI,SAAS,CAAC,CAAC;AACnC,WAAO;AAAA,EACR,GAAG,CAAC,CAAC;AACP;AAEA,IAAM,kBAAkB,SACvBD,KAAI,KAAK,OAAM,KAAK,IAAI,CAAC,KAAK,MAAM,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC,CAAE;;;AC7BzD,IAAM,IAAI,CAAC,UAAU,SAAS,UAAU,UAAU,SAAS,CAAC;AAE5D,IAAM,WAAW,KAAK,KAAK;AAC3B,IAAM,WAAW,MAAM,KAAK;;;ACYnC,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB,IAAI,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAEnC,IAAM,wBAAwB,CAAC,EAAE,GAAAE,IAAG,GAAG,GAAG,MAAM,MAAM;AACrD,MAAIA,OAAM,OAAW,CAAAA,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,KAAK,OAAO,IAAIA,KAAI,KAAK,IAAI,OAAO,OAAO,KAAK;AACpD,MAAI,IAAI,IAAI;AACZ,MAAI,KAAK,EAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC;AAEzC,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN;AAAA,IACA,GACC,MAAM,KAAK,MAAM,IACd,SACA,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,KAAK,IAAI;AAAA,EACjD;AAEA,MAAI,IAAI,EAAG,KAAI,IAAI,KAAK,MAAM,GAAG,CAAC,IAAI,WAAW;AACjD,MAAI,UAAU,OAAW,KAAI,QAAQ;AAErC,SAAO;AACR;AAEA,IAAO,gCAAQ;;;ACxCf,IAAM,wBAAwB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACrD,MAAI,MAAM,EAAE,MAAM,MAAM;AAExB,OAAK,MAAM,SAAY,IAAI,IAAI,OAAO;AACtC,MAAI,MAAM,OAAW,KAAI;AAEzB,MAAI,MAAM,MAAM,SAAY,IAAI,IAAI,KAAK,IAAI;AAE7C,MAAI,OAAO,KAAK,IAAI,CAAC;AACrB,MAAI,OAAO,KAAK,IAAI,CAAC;AAErB,MAAI,IAAI,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI;AACxC,MAAI,IAAI,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI;AACxC,MAAI,IAAI,IAAI,OAAO,EAAE,CAAC,IAAI,OAAO,EAAE,CAAC,IAAI;AAExC,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;AAEA,IAAO,gCAAQ;;;ACjBf,IAAM,0BAA0B,CAAC,KAAK,QAAQ;AAC7C,MAAI,IAAI,MAAM,UAAa,IAAI,MAAM,UAAa,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG;AACnE,WAAO;AAAA,EACR;AACA,MAAI,QAAQ,qBAAa,IAAI,CAAC;AAC9B,MAAI,QAAQ,qBAAa,IAAI,CAAC;AAC9B,MAAI,KAAK,KAAK,KAAO,QAAQ,QAAQ,OAAO,IAAK,KAAK,KAAM,GAAG;AAC/D,SAAO,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI;AACvC;AAEA,IAAM,qBAAqB,CAAC,KAAK,QAAQ;AACxC,MAAI,IAAI,MAAM,UAAa,IAAI,MAAM,QAAW;AAC/C,WAAO;AAAA,EACR;AACA,MAAI,QAAQ,qBAAa,IAAI,CAAC;AAC9B,MAAI,QAAQ,qBAAa,IAAI,CAAC;AAC9B,MAAI,KAAK,IAAI,QAAQ,KAAK,IAAI,KAAK;AAElC,WAAO,SAAS,QAAQ,MAAM,KAAK,KAAK,QAAQ,KAAK;AAAA,EACtD;AACA,SAAO,QAAQ;AAChB;AAEA,IAAM,sBAAsB,CAAC,KAAK,QAAQ;AACzC,MAAI,IAAI,MAAM,UAAa,IAAI,MAAM,UAAa,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG;AACnE,WAAO;AAAA,EACR;AACA,MAAI,QAAQ,qBAAa,IAAI,CAAC;AAC9B,MAAI,QAAQ,qBAAa,IAAI,CAAC;AAC9B,MAAI,KAAK,KAAK,KAAO,QAAQ,QAAQ,OAAO,IAAK,KAAK,KAAM,GAAG;AAC/D,SAAO,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI;AACvC;;;AChCA,IAAM,eAAe,SAAO;AAE3B,MAAIC,OAAM,IAAI;AAAA,IACb,CAACA,MAAKC,SAAQ;AACb,UAAIA,SAAQ,QAAW;AACtB,YAAI,MAAOA,OAAM,KAAK,KAAM;AAC5B,QAAAD,KAAI,OAAO,KAAK,IAAI,GAAG;AACvB,QAAAA,KAAI,OAAO,KAAK,IAAI,GAAG;AAAA,MACxB;AACA,aAAOA;AAAA,IACR;AAAA,IACA,EAAE,KAAK,GAAG,KAAK,EAAE;AAAA,EAClB;AACA,MAAI,QAAS,KAAK,MAAMA,KAAI,KAAKA,KAAI,GAAG,IAAI,MAAO,KAAK;AACxD,SAAO,QAAQ,IAAI,MAAM,QAAQ;AAClC;;;ACuBA,IAAME,cAAa;AAAA,EAClB,MAAM;AAAA,EACN,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EACjC,OAAO,CAAC,aAAa;AAAA,EACrB,WAAW;AAAA,EAEX,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,GAAG;AAAA,IACV,GAAG,CAAC,GAAG,KAAK;AAAA,IACZ,GAAG,CAAC,GAAG,CAAC;AAAA,EACT;AAAA,EAEA,UAAU;AAAA,IACT,KAAK;AAAA,EACN;AAAA,EAEA,QAAQ;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EAEA,aAAa;AAAA,IACZ,GAAG;AAAA,MACF,KAAK;AAAA,MACL,OAAO;AAAA,IACR;AAAA,IACA,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA,IACR;AAAA,EACD;AAAA,EAEA,YAAY;AAAA,IACX,GAAG;AAAA,EACJ;AAAA,EAEA,SAAS;AAAA,IACR,GAAG;AAAA,EACJ;AACD;AAEA,IAAOC,sBAAQD;;;AC5Ef,IAAM,kBAAkB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,GAAG,OAAO,UAAU;AAC7D,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAIE,KAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC/B,MAAI,MAAM,EAAE,MAAM,GAAG,GAAAA,GAAE;AACvB,MAAIA,GAAG,KAAI,IAAI,qBAAc,KAAK,MAAM,GAAG,CAAC,IAAI,MAAO,KAAK,EAAE;AAC9D,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;AAEA,IAAO,0BAAQ;;;ACZf,IAAM,kBAAkB,CAAC,EAAE,GAAG,GAAAC,IAAG,GAAG,MAAM,GAAG,OAAO,UAAU;AAC7D,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM;AAAA,IACT;AAAA,IACA;AAAA,IACA,GAAGA,KAAIA,KAAI,KAAK,IAAK,IAAI,MAAO,KAAK,EAAE,IAAI;AAAA,IAC3C,GAAGA,KAAIA,KAAI,KAAK,IAAK,IAAI,MAAO,KAAK,EAAE,IAAI;AAAA,EAC5C;AACA,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;AAEA,IAAO,0BAAQ;;;ACjBR,IAAM,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC;AACzC,IAAM,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC;;;ACOzC,IAAM,MAAM;AAAA,EAClB,GAAG,SAAS;AAAA,EACZ,GAAG;AAAA,EACH,IAAI,IAAI,SAAS,UAAU;AAC5B;AAEO,IAAM,MAAM;AAAA,EAClB,GAAG,SAAS;AAAA,EACZ,GAAG;AAAA,EACH,IAAI,IAAI,SAAS,SAAS;AAC3B;AAEO,IAAMC,KAAI,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC;AACzC,IAAMC,KAAI,KAAK,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC;;;AClBhD,IAAIC,MAAK,OAAM,KAAK,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,IAAI,GAAG,CAAC,KAAK,MAAM,IAAI,MAAM;AAEtE,IAAM,sBAAsB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACnD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AAEzB,MAAI,MAAM,IAAI,MAAM;AACpB,MAAI,KAAK,IAAI,MAAM;AACnB,MAAI,KAAK,KAAK,IAAI;AAElB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAGA,IAAG,EAAE,IAAI,IAAI;AAAA,IAChB,GAAGA,IAAG,EAAE,IAAI,IAAI;AAAA,IAChB,GAAGA,IAAG,EAAE,IAAI,IAAI;AAAA,EACjB;AAEA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AAEA,SAAO;AACR;AAEA,IAAO,8BAAQ;;;ACzBf,IAAM,oBAAoB,CAAAC,SAAO,0BAAkB,4BAAoBA,IAAG,CAAC;AAE3E,IAAO,4BAAQ;;;ACFf,IAAM,IAAI,WAAU,QAAQ,IAAI,KAAK,KAAK,KAAK,KAAK,IAAI,QAAQ,MAAM;AAEtE,IAAM,sBAAsB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACnD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,KAAK,EAAE,IAAI,IAAI,CAAC;AACpB,MAAI,KAAK,EAAE,IAAI,IAAI,CAAC;AACpB,MAAIC,MAAK,EAAE,IAAI,IAAI,CAAC;AAEpB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAG,MAAM,KAAK;AAAA,IACd,GAAG,OAAO,KAAK;AAAA,IACf,GAAG,OAAO,KAAKA;AAAA,EAChB;AAEA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AAEA,SAAO;AACR;AAEA,IAAO,8BAAQ;;;ACxBf,IAAM,oBAAoB,CAAAC,SAAO;AAChC,MAAI,MAAM,4BAAoB,0BAAkBA,IAAG,CAAC;AAKpD,MAAIA,KAAI,MAAMA,KAAI,KAAKA,KAAI,MAAMA,KAAI,GAAG;AACvC,QAAI,IAAI,IAAI,IAAI;AAAA,EACjB;AACA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;ACfR,IAAM,KAAK;AACX,IAAM,MAAM;AACZ,IAAM,IAAK,KAAK,MAAO,KAAK;AAC5B,IAAM,OAAO,KAAK,IAAI,CAAC;AACvB,IAAM,OAAO,KAAK,IAAI,CAAC;AACvB,IAAM,SAAS,MAAM,KAAK,IAAI,MAAM,GAAG;;;ACE9C,IAAM,qBAAqB,CAAC,EAAE,GAAG,GAAAC,IAAG,GAAG,MAAM,MAAM;AAClD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAIA,OAAM,OAAW,CAAAA,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,IAAI,KAAK,IAAK,IAAI,KAAM,MAAM,IAAI,KAAK;AAAA,EACxC;AAEA,MAAI,KAAK,KAAK,IAAI,SAASA,KAAI,MAAM,EAAE,IAAI,KAAK;AAChD,MAAIC,KAAI,IAAI,KAAK,IAAK,IAAI,MAAO,KAAK,KAAK,CAAC;AAC5C,MAAIC,KAAI,IAAI,KAAK,IAAK,IAAI,MAAO,KAAK,KAAK,CAAC;AAC5C,MAAI,IAAID,KAAI,OAAQC,KAAI,OAAQ;AAChC,MAAI,IAAID,KAAI,OAAQC,KAAI,OAAQ;AAEhC,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;AAEA,IAAO,6BAAQ;;;AClBf,IAAM,qBAAqB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AAClD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAIC,KAAI,IAAI,OAAO,IAAI;AACvB,MAAIC,KAAI,QAAQ,IAAI,OAAO,IAAI;AAC/B,MAAI,IAAI,KAAK,KAAKD,KAAIA,KAAIC,KAAIA,EAAC;AAC/B,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAI,SAAS,KAAM,KAAK,IAAI,IAAI,QAAS,CAAC;AAAA,IAC1C,GAAG,KAAK,IAAI,IAAI,QAAQ,CAAC,KAAK,SAAS,MAAM;AAAA,EAC9C;AAEA,MAAI,IAAI,GAAG;AACV,QAAI,IAAI,sBAAe,KAAK,MAAMA,IAAGD,EAAC,IAAI,KAAK,KAAK,KAAM,GAAG;AAAA,EAC9D;AAEA,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;AAEA,IAAO,6BAAQ;;;ACpBf,IAAM,qBAAqB,CAAAE,OAAK,2BAAmB,wBAAgBA,IAAG,MAAM,CAAC;AAC7E,IAAM,qBAAqB,CAAAA,OAAK,wBAAgB,2BAAmBA,EAAC,GAAG,MAAM;AAE7E,IAAMC,cAAa;AAAA,EAClB,MAAM;AAAA,EAEN,OAAO,CAAC,cAAc;AAAA,EACtB,WAAW;AAAA,EAEX,QAAQ;AAAA,IACP,OAAO;AAAA,IACP,KAAK,CAAAD,OAAK,0BAAkB,mBAAmBA,EAAC,CAAC;AAAA,EAClD;AAAA,EAEA,UAAU;AAAA,IACT,OAAO;AAAA,IACP,KAAK,CAAAA,OAAK,mBAAmB,0BAAkBA,EAAC,CAAC;AAAA,EAClD;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,GAAG;AAAA,IACV,GAAG,CAAC,QAAQ,MAAM;AAAA,IAClB,GAAG,CAAC,SAAS,MAAM;AAAA,EACpB;AAAA,EAEA,aAAa;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA,IACR;AAAA,EACD;AACD;AAEA,IAAOE,sBAAQD;;;AClCf,IAAME,cAAa;AAAA,EAClB,MAAM;AAAA,EAEN,OAAO,CAAC,cAAc;AAAA,EACtB,WAAW;AAAA,EAEX,QAAQ;AAAA,IACP,OAAO;AAAA,IACP,MAAM,CAAAC,OAAK,wBAAgBA,IAAG,MAAM;AAAA,IACpC,KAAK,CAAAA,OAAK,0BAAkB,2BAAmBA,EAAC,CAAC;AAAA,EAClD;AAAA,EAEA,UAAU;AAAA,IACT,OAAO;AAAA,IACP,MAAM,CAAAA,OAAK,wBAAgBA,IAAG,MAAM;AAAA,IACpC,KAAK,CAAAA,OAAK,2BAAmB,0BAAkBA,EAAC,CAAC;AAAA,EAClD;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,GAAG;AAAA,IACV,GAAG,CAAC,GAAG,MAAM;AAAA,IACb,GAAG,CAAC,GAAG,GAAG;AAAA,EACX;AAAA,EAEA,aAAa;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,MACF,KAAK;AAAA,MACL,OAAO;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACN,KAAK;AAAA,MACL,OAAO;AAAA,IACR;AAAA,EACD;AAAA,EAEA,YAAY;AAAA,IACX,GAAG;AAAA,EACJ;AAAA,EAEA,SAAS;AAAA,IACR,GAAG;AAAA,EACJ;AACD;AAEA,IAAOC,sBAAQF;;;ACzDA,SAAR,gBAAiC,EAAE,GAAG,GAAG,GAAG,MAAM,GAAG;AAC3D,MAAI,qBAAa,MAAM,SAAY,IAAI,CAAC;AACxC,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAIG,KAAI,KAAK,IAAM,IAAI,KAAM,IAAK,CAAC;AACnC,MAAI;AACJ,UAAQ,KAAK,MAAM,IAAI,EAAE,GAAG;AAAA,IAC3B,KAAK;AACJ,YAAM;AAAA,QACL,GAAG,KAAK,IAAI,KAAK,KAAK,IAAIA,MAAK;AAAA,QAC/B,GAAG,KAAK,IAAI,KAAM,KAAK,IAAIA,OAAO,IAAIA,MAAK;AAAA,QAC3C,GAAG,KAAK,IAAI;AAAA,MACb;AACA;AAAA,IACD,KAAK;AACJ,YAAM;AAAA,QACL,GAAG,KAAK,IAAI,KAAM,KAAK,IAAIA,OAAO,IAAIA,MAAK;AAAA,QAC3C,GAAG,KAAK,IAAI,KAAK,KAAK,IAAIA,MAAK;AAAA,QAC/B,GAAG,KAAK,IAAI;AAAA,MACb;AACA;AAAA,IACD,KAAK;AACJ,YAAM;AAAA,QACL,GAAG,KAAK,IAAI;AAAA,QACZ,GAAG,KAAK,IAAI,KAAK,KAAK,IAAIA,MAAK;AAAA,QAC/B,GAAG,KAAK,IAAI,KAAM,KAAK,IAAIA,OAAO,IAAIA,MAAK;AAAA,MAC5C;AACA;AAAA,IACD,KAAK;AACJ,YAAM;AAAA,QACL,GAAG,KAAK,IAAI;AAAA,QACZ,GAAG,KAAK,IAAI,KAAM,KAAK,IAAIA,OAAO,IAAIA,MAAK;AAAA,QAC3C,GAAG,KAAK,IAAI,KAAK,KAAK,IAAIA,MAAK;AAAA,MAChC;AACA;AAAA,IACD,KAAK;AACJ,YAAM;AAAA,QACL,GAAG,KAAK,IAAI,KAAM,KAAK,IAAIA,OAAO,IAAIA,MAAK;AAAA,QAC3C,GAAG,KAAK,IAAI;AAAA,QACZ,GAAG,KAAK,IAAI,KAAK,KAAK,IAAIA,MAAK;AAAA,MAChC;AACA;AAAA,IACD,KAAK;AACJ,YAAM;AAAA,QACL,GAAG,KAAK,IAAI,KAAK,KAAK,IAAIA,MAAK;AAAA,QAC/B,GAAG,KAAK,IAAI;AAAA,QACZ,GAAG,KAAK,IAAI,KAAM,KAAK,IAAIA,OAAO,IAAIA,MAAK;AAAA,MAC5C;AACA;AAAA,IACD;AACC,YAAM,EAAE,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,GAAG;AAAA,EACzD;AAEA,MAAI,OAAO;AACX,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;;;AC1De,SAAR,gBAAiC,EAAE,GAAAC,IAAG,GAAG,GAAG,MAAM,GAAG;AAC3D,MAAIA,OAAM,OAAW,CAAAA,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAIC,KAAI,KAAK,IAAID,IAAG,GAAG,CAAC,GACvB,IAAI,KAAK,IAAIA,IAAG,GAAG,CAAC;AACrB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAGA,KAAI,IAAI,MAAM,IAAI,IAAI,IAAK,IAAI,KAAMA,KAAI,IAAI;AAAA,IAChD,IAAIA,KAAI,IAAI,KAAK;AAAA,EAClB;AACA,MAAIC,KAAI,MAAM;AACb,QAAI,KACFA,OAAMD,MACH,IAAI,MAAMC,KAAI,MAAM,IAAI,KAAK,IAC9BA,OAAM,KACL,IAAID,OAAMC,KAAI,KAAK,KACnBD,KAAI,MAAMC,KAAI,KAAK,KAAK;AAC9B,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;;;ACdA,IAAMC,cAAa;AAAA,EAClB,MAAM;AAAA,EAEN,QAAQ;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EAEA,OAAO,CAAC,OAAO;AAAA,EACf,WAAW;AAAA,EAEX,UAAU;AAAA,IACT,KAAK;AAAA,EACN;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,GAAG;AAAA,EACX;AAAA,EAEA,OAAO;AAAA,EAEP,aAAa;AAAA,IACZ,GAAG,EAAE,KAAK,oBAAoB,OAAO,gBAAgB;AAAA,IACrD,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AAAA,EAEA,YAAY;AAAA,IACX,GAAG;AAAA,EACJ;AAAA,EAEA,SAAS;AAAA,IACR,GAAG;AAAA,EACJ;AACD;AAEA,IAAOC,sBAAQD;;;AC3CA,SAAR,gBAAiC,EAAE,GAAG,GAAG,GAAG,MAAM,GAAG;AAC3D,MAAI,qBAAa,MAAM,SAAY,IAAI,CAAC;AACxC,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,KAAK,IAAI,KAAK,IAAI,MAAM,IAAI,IAAI;AACpC,MAAI,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,IAAM,IAAI,KAAM,IAAK,CAAC;AACxD,MAAI;AACJ,UAAQ,KAAK,MAAM,IAAI,EAAE,GAAG;AAAA,IAC3B,KAAK;AACJ,YAAM,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG;AACpC;AAAA,IACD,KAAK;AACJ,YAAM,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG;AACpC;AAAA,IACD,KAAK;AACJ,YAAM,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG;AACpC;AAAA,IACD,KAAK;AACJ,YAAM,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG;AACpC;AAAA,IACD,KAAK;AACJ,YAAM,EAAE,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG;AACpC;AAAA,IACD,KAAK;AACJ,YAAM,EAAE,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG;AACpC;AAAA,IACD;AACC,YAAM,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG;AAAA,EACtD;AACA,MAAI,OAAO;AACX,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;;;ACjCe,SAAR,gBAAiC,EAAE,GAAAE,IAAG,GAAG,GAAG,MAAM,GAAG;AAC3D,MAAIA,OAAM,OAAW,CAAAA,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAIC,KAAI,KAAK,IAAID,IAAG,GAAG,CAAC,GACvB,IAAI,KAAK,IAAIA,IAAG,GAAG,CAAC;AACrB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAGC,OAAM,IAAI,KAAKA,KAAI,MAAM,IAAI,KAAK,IAAIA,KAAI,IAAI,CAAC;AAAA,IAClD,GAAG,OAAOA,KAAI;AAAA,EACf;AACA,MAAIA,KAAI,MAAM;AACb,QAAI,KACFA,OAAMD,MACH,IAAI,MAAMC,KAAI,MAAM,IAAI,KAAK,IAC9BA,OAAM,KACL,IAAID,OAAMC,KAAI,KAAK,KACnBD,KAAI,MAAMC,KAAI,KAAK,KAAK;AAC9B,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;;;ACtBA,IAAM,WAAW,CAAC,KAAK,SAAS;AAC/B,UAAQ,MAAM;AAAA,IACb,KAAK;AACJ,aAAO,CAAC;AAAA,IACT,KAAK;AACJ,aAAQ,MAAM,KAAK,KAAM;AAAA,IAC1B,KAAK;AACJ,aAAQ,MAAM,KAAM;AAAA,IACrB,KAAK;AACJ,aAAO,MAAM;AAAA,EACf;AACD;AAEA,IAAO,cAAQ;;;ACNf,IAAM,UAAU,IAAI;AAAA,EACnB,gBAAgB,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,eAAe,OAAO;AAC9D;AAEA,IAAM,iBAAiB,WAAS;AAC/B,MAAI,QAAQ,MAAM,MAAM,OAAO;AAC/B,MAAI,CAAC,MAAO;AACZ,MAAI,MAAM,EAAE,MAAM,MAAM;AAExB,MAAI,MAAM,CAAC,MAAM,QAAW;AAC3B,QAAI,IAAI,CAAC,MAAM,CAAC;AAAA,EACjB,WAAW,MAAM,CAAC,MAAM,UAAa,MAAM,CAAC,MAAM,QAAW;AAC5D,QAAI,IAAI,YAAS,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,EACpC;AAEA,MAAI,MAAM,CAAC,MAAM,QAAW;AAC3B,QAAI,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC;AAAA,EAChD;AAEA,MAAI,MAAM,CAAC,MAAM,QAAW;AAC3B,QAAI,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC;AAAA,EAChD;AAEA,MAAI,MAAM,CAAC,MAAM,QAAW;AAC3B,QAAI,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,CAAC;AAAA,EACpD,WAAW,MAAM,CAAC,MAAM,QAAW;AAClC,QAAI,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;AAAA,EAC/C;AACA,SAAO;AACR;AAEA,IAAO,yBAAQ;;;ACpCf,SAAS,SAAS,OAAO,QAAQ;AAChC,MAAI,CAAC,UAAW,OAAO,CAAC,MAAM,SAAS,OAAO,CAAC,MAAM,QAAS;AAC7D,WAAO;AAAA,EACR;AACA,QAAM,MAAM,EAAE,MAAM,MAAM;AAC1B,QAAM,CAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAE3B,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,EAAE,SAAS,IAAI,YAAY;AAC9B,aAAO;AAAA,IACR;AACA,QAAI,IAAI,EAAE;AAAA,EACX;AAEA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,EAAE,SAAS,IAAI,KAAK;AACvB,aAAO;AAAA,IACR;AACA,QAAI,IAAI,EAAE,QAAQ;AAAA,EACnB;AAEA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,EAAE,SAAS,IAAI,KAAK;AACvB,aAAO;AAAA,IACR;AACA,QAAI,IAAI,EAAE,QAAQ;AAAA,EACnB;AAEA,MAAI,MAAM,SAAS,IAAI,MAAM;AAC5B,QAAI,QAAQ,KAAK;AAAA,MAChB;AAAA,MACA,KAAK;AAAA,QACJ;AAAA,QACA,MAAM,SAAS,IAAI,SAAS,MAAM,QAAQ,MAAM,QAAQ;AAAA,MACzD;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEA,IAAO,mBAAQ;;;ACjCf,IAAMC,cAAa;AAAA,EAClB,MAAM;AAAA,EAEN,QAAQ;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EAEA,UAAU;AAAA,IACT,KAAK;AAAA,EACN;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,GAAG;AAAA,EACX;AAAA,EAEA,OAAO;AAAA,EAEP,OAAO,CAAC,kBAAU,sBAAc;AAAA,EAChC,WAAW,CAAAC,OACV,OAAOA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,IACtCA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,MAAM,MACvC,IAAIA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,MAAM,MAAM,GAC/CA,GAAE,QAAQ,IAAI,MAAMA,GAAE,KAAK,KAAK,EACjC;AAAA,EAED,aAAa;AAAA,IACZ,GAAG,EAAE,KAAK,oBAAoB,OAAO,gBAAgB;AAAA,IACrD,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AAAA,EAEA,YAAY;AAAA,IACX,GAAG;AAAA,EACJ;AAAA,EAEA,SAAS;AAAA,IACR,GAAG;AAAA,EACJ;AACD;AAEA,IAAOC,sBAAQF;;;ACjDA,SAAR,gBAAiC,EAAE,GAAG,GAAG,GAAG,MAAM,GAAG;AAC3D,MAAI,qBAAa,MAAM,SAAY,IAAI,CAAC;AACxC,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAIG,KAAI,KAAK,IAAM,IAAI,KAAM,IAAK,CAAC;AACnC,MAAI;AACJ,UAAQ,KAAK,MAAM,IAAI,EAAE,GAAG;AAAA,IAC3B,KAAK;AACJ,YAAM,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,IAAIA,KAAI,GAAG,KAAK,IAAI,GAAG;AACjD;AAAA,IACD,KAAK;AACJ,YAAM,EAAE,GAAG,KAAK,IAAI,IAAIA,KAAI,GAAG,GAAG,GAAG,KAAK,IAAI,GAAG;AACjD;AAAA,IACD,KAAK;AACJ,YAAM,EAAE,GAAG,KAAK,IAAI,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,IAAIA,IAAG;AACjD;AAAA,IACD,KAAK;AACJ,YAAM,EAAE,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAIA,KAAI,GAAG,EAAE;AACjD;AAAA,IACD,KAAK;AACJ,YAAM,EAAE,GAAG,KAAK,IAAI,IAAIA,KAAI,GAAG,KAAK,IAAI,IAAI,GAAG,EAAE;AACjD;AAAA,IACD,KAAK;AACJ,YAAM,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAIA,IAAG;AACjD;AAAA,IACD;AACC,YAAM,EAAE,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,GAAG;AAAA,EACzD;AACA,MAAI,OAAO;AACX,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;;;ACjCe,SAAR,gBAAiC,EAAE,GAAAC,IAAG,GAAG,GAAG,MAAM,GAAG;AAC3D,MAAIA,OAAM,OAAW,CAAAA,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAIC,KAAI,KAAK,IAAID,IAAG,GAAG,CAAC,GACvB,IAAI,KAAK,IAAIA,IAAG,GAAG,CAAC;AACrB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAGC,OAAM,IAAI,IAAI,IAAI,IAAIA;AAAA,IACzB,GAAGA;AAAA,EACJ;AACA,MAAIA,KAAI,MAAM;AACb,QAAI,KACFA,OAAMD,MACH,IAAI,MAAMC,KAAI,MAAM,IAAI,KAAK,IAC9BA,OAAM,KACL,IAAID,OAAMC,KAAI,KAAK,KACnBD,KAAI,MAAMC,KAAI,KAAK,KAAK;AAC9B,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;;;ACdA,IAAMC,cAAa;AAAA,EAClB,MAAM;AAAA,EAEN,QAAQ;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EAEA,OAAO,CAAC,OAAO;AAAA,EACf,WAAW;AAAA,EAEX,UAAU;AAAA,IACT,KAAK;AAAA,EACN;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,GAAG;AAAA,EACX;AAAA,EAEA,OAAO;AAAA,EAEP,aAAa;AAAA,IACZ,GAAG,EAAE,KAAK,oBAAoB,OAAO,gBAAgB;AAAA,IACrD,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AAAA,EAEA,YAAY;AAAA,IACX,GAAG;AAAA,EACJ;AAAA,EAEA,SAAS;AAAA,IACR,GAAG;AAAA,EACJ;AACD;AAEA,IAAOC,sBAAQD;;;AClCA,SAAR,gBAAiC,EAAE,GAAG,GAAG,GAAG,MAAM,GAAG;AAC3D,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AAEzB,MAAI,IAAI,IAAI,GAAG;AACd,QAAI,IAAI,IAAI;AACZ,SAAK;AACL,SAAK;AAAA,EACN;AACA,SAAO,gBAAgB;AAAA,IACtB;AAAA,IACA,GAAG,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI;AAAA,IAC9B,GAAG,IAAI;AAAA,IACP;AAAA,EACD,CAAC;AACF;;;ACfe,SAAR,gBAAiC,MAAM;AAC7C,MAAIE,OAAM,gBAAgB,IAAI;AAC9B,MAAIA,SAAQ,OAAW,QAAO;AAC9B,MAAI,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AACtC,MAAI,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AACtC,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,IAAI,IAAI,KAAK;AAAA,IACb,GAAG,IAAI;AAAA,EACR;AACA,MAAIA,KAAI,MAAM,OAAW,KAAI,IAAIA,KAAI;AACrC,MAAIA,KAAI,UAAU,OAAW,KAAI,QAAQA,KAAI;AAC7C,SAAO;AACR;;;ACvBA,SAAS,SAAS,OAAO,QAAQ;AAChC,MAAI,CAAC,UAAU,OAAO,CAAC,MAAM,OAAO;AACnC,WAAO;AAAA,EACR;AACA,QAAM,MAAM,EAAE,MAAM,MAAM;AAC1B,QAAM,CAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAE3B,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,EAAE,SAAS,IAAI,YAAY;AAC9B,aAAO;AAAA,IACR;AACA,QAAI,IAAI,EAAE;AAAA,EACX;AAEA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,EAAE,SAAS,IAAI,KAAK;AACvB,aAAO;AAAA,IACR;AACA,QAAI,IAAI,EAAE,QAAQ;AAAA,EACnB;AAEA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,EAAE,SAAS,IAAI,KAAK;AACvB,aAAO;AAAA,IACR;AACA,QAAI,IAAI,EAAE,QAAQ;AAAA,EACnB;AAEA,MAAI,MAAM,SAAS,IAAI,MAAM;AAC5B,QAAI,QAAQ,KAAK;AAAA,MAChB;AAAA,MACA,KAAK;AAAA,QACJ;AAAA,QACA,MAAM,SAAS,IAAI,SAAS,MAAM,QAAQ,MAAM,QAAQ;AAAA,MACzD;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEA,IAAO,mBAAQ;;;AClCf,IAAMC,cAAa;AAAA,EAClB,MAAM;AAAA,EAEN,QAAQ;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EAEA,UAAU;AAAA,IACT,KAAK;AAAA,EACN;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,GAAG;AAAA,EACX;AAAA,EAEA,OAAO;AAAA,EAEP,OAAO,CAAC,gBAAQ;AAAA,EAChB,WAAW,CAAAC,OACV,OAAOA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,IACtCA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,MAAM,MACvC,IAAIA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,MAAM,MAAM,GAC/CA,GAAE,QAAQ,IAAI,MAAMA,GAAE,KAAK,KAAK,EACjC;AAAA,EAED,aAAa;AAAA,IACZ,GAAG,EAAE,KAAK,oBAAoB,OAAO,gBAAgB;AAAA,IACrD,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AAAA,EAEA,YAAY;AAAA,IACX,GAAG;AAAA,EACJ;AAAA,EAEA,SAAS;AAAA,IACR,GAAG;AAAA,EACJ;AACD;AAEA,IAAOC,sBAAQF;;;AC/CR,IAAM,KAAK;;;ACDX,IAAM,KAAK;AACX,IAAM,KAAK;AACX,IAAM,KAAK;AACX,IAAM,KAAK;AACX,IAAM,KAAK;AAUX,SAAS,iBAAiB,GAAG;AACnC,MAAI,IAAI,EAAG,QAAO;AAClB,QAAMG,KAAI,KAAK,IAAI,GAAG,IAAI,EAAE;AAC5B,SAAO,MAAM,KAAK,IAAI,KAAK,IAAI,GAAGA,KAAI,EAAE,KAAK,KAAK,KAAKA,KAAI,IAAI,EAAE;AAClE;AAGO,SAAS,iBAAiB,GAAG;AACnC,MAAI,IAAI,EAAG,QAAO;AAClB,QAAMA,KAAI,KAAK,IAAI,IAAI,KAAK,EAAE;AAC9B,SAAO,KAAK,KAAK,KAAK,KAAKA,OAAM,IAAI,KAAKA,KAAI,EAAE;AACjD;;;AC1BA,IAAM,QAAQ,CAAAC,OAAK,KAAK,IAAIA,KAAI,IAAI,CAAC;AAErC,IAAM,oBAAoB,CAAC,EAAE,GAAG,GAAG,GAAAC,IAAG,MAAM,MAAM;AACjD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAIA,OAAM,OAAW,CAAAA,KAAI;AAEzB,QAAM,IAAI;AAAA,IACT,IAAI,uBAAuB,IAAI,sBAAsBA;AAAA,EACtD;AACA,QAAM,IAAI;AAAA,IACT,IAAI,sBAAsB,IAAI,sBAAsBA;AAAA,EACrD;AACA,QAAM,IAAI;AAAA,IACT,IAAI,qBAAqB,IAAI,sBAAsBA;AAAA,EACpD;AAEA,QAAM,MAAM;AAAA,IACX,MAAM;AAAA,IACN,GAAG;AAAA,MACF,oBAAqB,IACpB,qBAAqB,IACrB,qBAAqB;AAAA,IACvB;AAAA,IACA,GAAG;AAAA,MACF,qBAAqB,IACpB,oBAAoB,IACpB,qBAAqB;AAAA,IACvB;AAAA,IACA,GAAG;AAAA,MACF,qBAAqB,IACpB,qBAAqB,IACrB,qBAAqB;AAAA,IACvB;AAAA,EACD;AAEA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AAEA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;AC3Cf,IAAM,QAAQ,CAACC,KAAI,MAAM,KAAK,IAAIA,KAAI,IAAI,CAAC;AAE3C,IAAM,oBAAoB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACjD,QAAM,OAAO,MAAM,CAAC;AACpB,QAAM,OAAO,MAAM,CAAC;AACpB,QAAM,OAAO,MAAM,CAAC;AACpB,QAAM,IAAI;AAAA,IACT,qBAAqB,OACpB,qBAAqB,OACrB,qBAAqB;AAAA,EACvB;AACA,QAAM,IAAI;AAAA,IACT,sBAAsB,OACrB,qBAAqB,OACrB,qBAAqB;AAAA,EACvB;AACA,QAAM,IAAI;AAAA,IACT,qBAAqB,OACpB,qBAAqB,OACrB,qBAAqB;AAAA,EACvB;AAEA,QAAM,IAAI,MAAM,IAAI,MAAM;AAC1B,QAAM,IAAI,gBAAgB,IAAI,iBAAiB,IAAI,iBAAiB;AACpE,QAAMC,KAAI,iBAAiB,IAAI,gBAAgB,IAAI,iBAAiB;AAEpE,QAAM,MAAM,EAAE,MAAM,OAAO,GAAG,GAAG,GAAAA,GAAE;AACnC,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AAEA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;ACvBf,IAAMC,eAAa;AAAA,EAClB,MAAM;AAAA,EACN,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EACjC,OAAO,CAAC,SAAS;AAAA,EACjB,WAAW;AAAA,EAEX,QAAQ;AAAA,IACP,OAAO;AAAA,IACP,KAAK,WAAS,0BAAkB,0BAAkB,KAAK,CAAC;AAAA,EACzD;AAAA,EAEA,UAAU;AAAA,IACT,OAAO;AAAA,IACP,KAAK,WAAS,0BAAkB,0BAAkB,KAAK,CAAC;AAAA,EACzD;AAAA,EAEA,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,KAAK;AAAA,IACZ,GAAG,CAAC,QAAQ,KAAK;AAAA,IACjB,GAAG,CAAC,QAAQ,KAAK;AAAA,EAClB;AAAA,EAEA,aAAa;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AACD;AAEA,IAAOC,uBAAQD;;;AC3Cf,IAAM,IAAI;AACV,IAAM,KAAK;AAKX,IAAM,cAAc,OAAK;AACxB,MAAI,IAAI,EAAG,QAAO;AAClB,MAAIE,MAAK,KAAK,IAAI,IAAI,KAAO,EAAC;AAC9B,SAAO,KAAK,KAAK,KAAK,KAAKA,QAAO,IAAI,KAAKA,MAAK,CAAC;AAClD;AAGA,IAAM,MAAM,CAAC,IAAI,MAAM,KAAK,IAAI,IAAI,KAAK,CAAC;AAE1C,IAAM,oBAAoB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACjD,MAAI,IAAI,CAAC;AACT,MAAI,IAAI,CAAC;AACT,MAAI,IAAI,CAAC;AAET,MAAI,KAAK,OAAO,IAAI,OAAO;AAC3B,MAAI,KAAK,OAAO,IAAI,OAAO;AAE3B,MAAI,IAAI,YAAY,aAAa,KAAK,WAAW,KAAK,WAAW,CAAC;AAClE,MAAI,IAAI,YAAY,WAAW,KAAK,WAAW,KAAK,YAAY,CAAC;AACjE,MAAI,IAAI,YAAY,aAAa,KAAK,SAAS,KAAK,YAAY,CAAC;AAEjE,MAAI,KAAK,IAAI,KAAK;AAElB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAI,OAAO,KAAM,IAAI,OAAO,KAAK;AAAA,IACjC,GAAG,QAAQ,IAAI,WAAW,IAAI,WAAW;AAAA,IACzC,GAAG,WAAW,IAAI,WAAW,IAAI,WAAW;AAAA,EAC7C;AAEA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AAEA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;AC3Cf,IAAMC,KAAI;AACV,IAAMC,MAAK;AAKX,IAAM,cAAc,OAAK;AACxB,MAAI,IAAI,EAAG,QAAO;AAClB,MAAI,KAAK,KAAK,IAAI,GAAG,IAAID,EAAC;AAC1B,SAAO,MAAQ,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK,IAAI,EAAC;AAC1D;AAEA,IAAM,MAAM,OAAK,IAAI;AAErB,IAAM,oBAAoB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACjD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,KAAK,IAAIC,QAAO,OAAO,QAAQ,IAAIA;AAEvC,MAAI,IAAI,YAAY,IAAI,aAAa,IAAI,cAAc,CAAC;AACxD,MAAI,IAAI,YAAY,IAAI,aAAa,IAAI,cAAc,CAAC;AACxD,MAAI,IAAI,YAAY,IAAI,cAAc,IAAI,YAAY,CAAC;AAEvD,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAG;AAAA,MACF,oBAAoB,IACnB,oBAAoB,IACpB,sBAAsB;AAAA,IACxB;AAAA,IACA,GAAG;AAAA,MACF,sBAAsB,IACrB,oBAAoB,IACpB,sBAAsB;AAAA,IACxB;AAAA,IACA,GAAG,IAAI,eAAe,IAAI,aAAa,IAAI,YAAY,CAAC;AAAA,EACzD;AAEA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AAEA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;ACrCf,IAAM,kBAAkB,CAAAC,SAAO;AAC9B,MAAI,MAAM,0BAAkB,0BAAkBA,IAAG,CAAC;AAClD,MAAIA,KAAI,MAAMA,KAAI,KAAKA,KAAI,MAAMA,KAAI,GAAG;AACvC,QAAI,IAAI,IAAI,IAAI;AAAA,EACjB;AACA,SAAO;AACR;AAEA,IAAO,0BAAQ;;;ACff,IAAM,kBAAkB,WAAS,0BAAkB,0BAAkB,KAAK,CAAC;AAE3E,IAAO,0BAAQ;;;ACgBf,IAAMC,eAAa;AAAA,EAClB,MAAM;AAAA,EACN,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,OAAO,CAAC,UAAU;AAAA,EAClB,WAAW;AAAA,EAEX,UAAU;AAAA,IACT,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAEA,QAAQ;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAEA,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,KAAK;AAAA,IACZ,GAAG,CAAC,QAAQ,KAAK;AAAA,IACjB,GAAG,CAAC,QAAQ,KAAK;AAAA,EAClB;AAAA,EAEA,aAAa;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AACD;AAEA,IAAOC,uBAAQD;;;AClDf,IAAM,kBAAkB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AAC/C,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAIE,KAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC/B,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN;AAAA,IACA,GAAAA;AAAA,EACD;AACA,MAAIA,IAAG;AACN,QAAI,IAAI,qBAAc,KAAK,MAAM,GAAG,CAAC,IAAI,MAAO,KAAK,EAAE;AAAA,EACxD;AACA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,0BAAQ;;;ACpBf,IAAM,kBAAkB,CAAC,EAAE,GAAG,GAAAC,IAAG,GAAG,MAAM,MAAM;AAC/C,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN;AAAA,IACA,GAAGA,KAAIA,KAAI,KAAK,IAAK,IAAI,MAAO,KAAK,EAAE,IAAI;AAAA,IAC3C,GAAGA,KAAIA,KAAI,KAAK,IAAK,IAAI,MAAO,KAAK,EAAE,IAAI;AAAA,EAC5C;AACA,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;AAEA,IAAO,0BAAQ;;;ACDf,IAAMC,eAAa;AAAA,EAClB,MAAM;AAAA,EAEN,OAAO,CAAC,UAAU;AAAA,EAClB,WAAW;AAAA,EAEX,QAAQ;AAAA,IACP,KAAK;AAAA,IACL,KAAK,CAAAC,OAAK,wBAAgB,wBAAgBA,EAAC,CAAC;AAAA,EAC7C;AAAA,EAEA,UAAU;AAAA,IACT,KAAK,CAAAA,OAAK,wBAAgB,wBAAgBA,EAAC,CAAC;AAAA,IAC5C,KAAK;AAAA,EACN;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,KAAK;AAAA,IACZ,GAAG,CAAC,GAAG,IAAI;AAAA,IACX,GAAG,CAAC,GAAG,GAAG;AAAA,EACX;AAAA,EAEA,aAAa;AAAA,IACZ,GAAG,EAAE,KAAK,oBAAoB,OAAO,gBAAgB;AAAA,IACrD,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AAAA,EAEA,YAAY;AAAA,IACX,GAAG;AAAA,EACJ;AAAA,EAEA,SAAS;AAAA,IACR,GAAG;AAAA,EACJ;AACD;AAEA,IAAOC,uBAAQF;;;ACnDR,IAAMG,KAAI,KAAK,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,GAAG,CAAC;AACzC,IAAMC,KAAI,KAAK,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC;;;ACEhD,IAAIC,MAAK,OAAM,KAAK,IAAI,GAAG,CAAC,IAAIC,KAAI,KAAK,IAAI,GAAG,CAAC,KAAK,MAAM,IAAI,MAAMC;AAEtE,IAAM,oBAAoB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACjD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,IAAI,MAAM;AACpB,MAAI,KAAK,IAAI,MAAM;AACnB,MAAI,KAAK,KAAK,IAAI;AAElB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAGF,IAAG,EAAE,IAAI,IAAI;AAAA,IAChB,GAAGA,IAAG,EAAE,IAAI,IAAI;AAAA,IAChB,GAAGA,IAAG,EAAE,IAAI,IAAI;AAAA,EACjB;AAEA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AAEA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;ACjBf,IAAM,oBAAoB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACjD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,yBAAiB;AAAA,IAC1B,GACC,IAAI,qBACJ,IAAI,qBACJ,qBAAqB;AAAA,IACtB,GACC,IAAI,qBACJ,IAAI,oBACJ,sBAAsB;AAAA,IACvB,GACC,IAAI,sBACJ,IAAI,qBACJ,oBAAoB;AAAA,EACtB,CAAC;AACD,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;AC/Bf,IAAM,kBAAkB,CAAAG,SAAO,0BAAkB,0BAAkBA,IAAG,CAAC;AAEvE,IAAO,0BAAQ;;;ACMf,IAAM,oBAAoB,CAAAC,SAAO;AAChC,MAAI,EAAE,GAAAC,IAAG,GAAG,GAAG,MAAM,IAAI,yBAAiBD,IAAG;AAC7C,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GACC,oBAAoBC,KACpB,qBAAqB,IACrB,sBAAsB;AAAA,IACvB,GACC,sBAAsBA,KACtB,qBAAqB,IACrB,sBAAsB;AAAA,IACvB,GACC,uBAAuBA,KACvB,sBAAsB,IACtB,qBAAqB;AAAA,EACvB;AACA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;AC/Bf,IAAMC,KAAI,WAAU,QAAQC,KAAI,KAAK,KAAK,KAAK,KAAKC,KAAI,QAAQ,MAAM;AAEtE,IAAM,oBAAoB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACjD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,KAAKF,GAAE,IAAI,IAAI,CAAC;AACpB,MAAI,KAAKA,GAAE,IAAI,IAAI,CAAC;AACpB,MAAIG,MAAKH,GAAE,IAAI,IAAI,CAAC;AAEpB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAG,MAAM,KAAK;AAAA,IACd,GAAG,OAAO,KAAK;AAAA,IACf,GAAG,OAAO,KAAKG;AAAA,EAChB;AAEA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AAEA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;ACxBf,IAAM,kBAAkB,CAAAC,SAAO;AAC9B,MAAI,MAAM,0BAAkB,0BAAkBA,IAAG,CAAC;AAKlD,MAAIA,KAAI,MAAMA,KAAI,KAAKA,KAAI,MAAMA,KAAI,GAAG;AACvC,QAAI,IAAI,IAAI,IAAI;AAAA,EACjB;AACA,SAAO;AACR;AAEA,IAAO,0BAAQ;;;ACbf,SAAS,SAAS,OAAO,QAAQ;AAChC,MAAI,CAAC,UAAU,OAAO,CAAC,MAAM,OAAO;AACnC,WAAO;AAAA,EACR;AACA,QAAM,MAAM,EAAE,MAAM,MAAM;AAC1B,QAAM,CAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAC3B,MAAI,EAAE,SAAS,IAAI,OAAO,EAAE,SAAS,IAAI,OAAO,EAAE,SAAS,IAAI,KAAK;AACnE,WAAO;AAAA,EACR;AACA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,KAAK,GAAG,GAAG;AAAA,EAC3C;AACA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,IAAI,EAAE,SAAS,IAAI,SAAS,EAAE,QAAS,EAAE,QAAQ,MAAO;AAAA,EAC7D;AACA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,IAAI,EAAE,SAAS,IAAI,SAAS,EAAE,QAAS,EAAE,QAAQ,MAAO;AAAA,EAC7D;AACA,MAAI,MAAM,SAAS,IAAI,MAAM;AAC5B,QAAI,QAAQ,KAAK;AAAA,MAChB;AAAA,MACA,KAAK;AAAA,QACJ;AAAA,QACA,MAAM,SAAS,IAAI,SAAS,MAAM,QAAQ,MAAM,QAAQ;AAAA,MACzD;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEA,IAAO,mBAAQ;;;ACzBf,IAAMC,eAAa;AAAA,EAClB,MAAM;AAAA,EAEN,QAAQ;AAAA,IACP,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EAEA,UAAU;AAAA,IACT,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,GAAG;AAAA,IACV,GAAG,CAAC,MAAM,GAAG;AAAA,IACb,GAAG,CAAC,MAAM,GAAG;AAAA,EACd;AAAA,EAEA,OAAO,CAAC,gBAAQ;AAAA,EAChB,WAAW,CAAAC,OACV,OAAOA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,IACtCA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAC3B,IAAIA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,GACnCA,GAAE,QAAQ,IAAI,MAAMA,GAAE,KAAK,KAAK,EACjC;AAAA,EAED,aAAa;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AACD;AAEA,IAAOC,uBAAQF;;;ACvCf,IAAMG,eAAa;AAAA,EAClB,GAAGC;AAAA,EACH,MAAM;AAAA,EAEN,OAAO,CAAC,WAAW;AAAA,EACnB,WAAW;AAAA,EAEX,QAAQ;AAAA,IACP,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EAEA,UAAU;AAAA,IACT,OAAO;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EAEA,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,GAAG;AAAA,IACV,GAAG,CAAC,SAAS,MAAM;AAAA,IACnB,GAAG,CAAC,SAAS,MAAM;AAAA,EACpB;AACD;AAEA,IAAOA,uBAAQD;;;AC5Bf,SAAS,SAAS,OAAO,QAAQ;AAChC,MAAI,CAAC,UAAU,OAAO,CAAC,MAAM,OAAO;AACnC,WAAO;AAAA,EACR;AACA,QAAM,MAAM,EAAE,MAAM,MAAM;AAC1B,QAAM,CAAC,EAAE,GAAGE,IAAG,GAAG,KAAK,IAAI;AAC3B,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,EAAE,SAAS,IAAI,KAAK;AACvB,aAAO;AAAA,IACR;AACA,QAAI,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,KAAK,GAAG,GAAG;AAAA,EAC3C;AACA,MAAIA,GAAE,SAAS,IAAI,MAAM;AACxB,QAAI,IAAI,KAAK;AAAA,MACZ;AAAA,MACAA,GAAE,SAAS,IAAI,SAASA,GAAE,QAASA,GAAE,QAAQ,MAAO;AAAA,IACrD;AAAA,EACD;AACA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,EAAE,SAAS,IAAI,YAAY;AAC9B,aAAO;AAAA,IACR;AACA,QAAI,IAAI,EAAE;AAAA,EACX;AACA,MAAI,MAAM,SAAS,IAAI,MAAM;AAC5B,QAAI,QAAQ,KAAK;AAAA,MAChB;AAAA,MACA,KAAK;AAAA,QACJ;AAAA,QACA,MAAM,SAAS,IAAI,SAAS,MAAM,QAAQ,MAAM,QAAQ;AAAA,MACzD;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEA,IAAO,mBAAQ;;;AC5Bf,IAAMC,eAAa;AAAA,EAClB,MAAM;AAAA,EAEN,QAAQ;AAAA,IACP,KAAK;AAAA,IACL,KAAK,CAAAC,OAAK,wBAAgB,wBAAgBA,EAAC,CAAC;AAAA,EAC7C;AAAA,EAEA,UAAU;AAAA,IACT,KAAK,CAAAA,OAAK,wBAAgB,wBAAgBA,EAAC,CAAC;AAAA,IAC5C,KAAK;AAAA,EACN;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,GAAG;AAAA,IACV,GAAG,CAAC,GAAG,GAAG;AAAA,IACV,GAAG,CAAC,GAAG,GAAG;AAAA,EACX;AAAA,EAEA,OAAO,CAAC,gBAAQ;AAAA,EAChB,WAAW,CAAAA,OACV,OAAOA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,IACtCA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAC3B,IAAIA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,GACnCA,GAAE,QAAQ,IAAI,MAAMA,GAAE,KAAK,KAAK,EACjC;AAAA,EAED,aAAa;AAAA,IACZ,GAAG,EAAE,KAAK,oBAAoB,OAAO,gBAAgB;AAAA,IACrD,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AAAA,EAEA,YAAY;AAAA,IACX,GAAG;AAAA,EACJ;AAAA,EAEA,SAAS;AAAA,IACR,GAAG;AAAA,EACJ;AACD;AAEA,IAAOC,uBAAQF;;;AClDf,IAAMG,eAAa;AAAA,EAClB,GAAGC;AAAA,EACH,MAAM;AAAA,EAEN,OAAO,CAAC,WAAW;AAAA,EACnB,WAAW;AAAA,EAEX,QAAQ;AAAA,IACP,OAAO,CAAAC,OAAK,wBAAgBA,IAAG,OAAO;AAAA,IACtC,KAAK,CAAAA,OAAK,0BAAkB,wBAAgBA,IAAG,OAAO,CAAC;AAAA,EACxD;AAAA,EAEA,UAAU;AAAA,IACT,KAAK,CAAAA,OAAK,wBAAgB,0BAAkBA,EAAC,GAAG,OAAO;AAAA,IACvD,OAAO,CAAAA,OAAK,wBAAgBA,IAAG,OAAO;AAAA,EACvC;AAAA,EAEA,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,GAAG;AAAA,IACV,GAAG,CAAC,GAAG,OAAO;AAAA,IACd,GAAG,CAAC,GAAG,GAAG;AAAA,EACX;AACD;AAEA,IAAOD,uBAAQD;;;AC5Bf,IAAM,oBAAoB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACjD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAIG,KAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC/B,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN;AAAA,IACA,GAAGA;AAAA,EACJ;AACA,MAAIA,IAAG;AACN,QAAI,IAAI,qBAAc,KAAK,MAAM,GAAG,CAAC,IAAI,MAAO,KAAK,EAAE;AAAA,EACxD;AACA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;ACpBf,IAAM,oBAAoB,CAAC,EAAE,GAAG,GAAAC,IAAG,GAAG,MAAM,MAAM;AACjD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN;AAAA,IACA,GAAGA,KAAIA,KAAI,KAAK,IAAK,IAAI,MAAO,KAAK,EAAE,IAAI;AAAA,IAC3C,GAAGA,KAAIA,KAAI,KAAK,IAAK,IAAI,MAAO,KAAK,EAAE,IAAI;AAAA,EAC5C;AACA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;ACXR,IAAM,OAAO,CAAC,GAAG,GAAG,MAAO,IAAI,KAAM,IAAI,KAAK,IAAI,IAAI;AACtD,IAAM,OAAO,CAAC,GAAG,GAAG,MAAO,IAAI,KAAM,IAAI,KAAK,IAAI,IAAI;AAEtD,IAAM,KAAK,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AACnC,IAAM,KAAK,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAE1C,IAAM,OAAO,WAAU,SAASC,KAAIC,KAAI,QAAQ,MAAM,KAAK,KAAK,KAAK,IAAI;AAEzE,IAAM,oBAAoB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACjD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,IAAI,KAAK,IAAI,IAAI,CAAC;AACtB,MAAI,IAAI,KAAK,GAAG,GAAG,CAAC;AACpB,MAAI,IAAI,KAAK,GAAG,GAAG,CAAC;AAGpB,MAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG;AACjC,QAAI,IAAI,IAAI;AAAA,EACb,OAAO;AACN,QAAI,KAAK,KAAK,IAAI;AAClB,QAAI,KAAK,KAAK,IAAI;AAAA,EACnB;AAEA,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EACD;AAEA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AAEA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;ACtCR,IAAMC,QAAO,CAAC,GAAG,GAAG,MAAO,IAAI,KAAM,IAAI,KAAK,IAAI,IAAI;AACtD,IAAMC,QAAO,CAAC,GAAG,GAAG,MAAO,IAAI,KAAM,IAAI,KAAK,IAAI,IAAI;AAEtD,IAAMC,MAAKF,MAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AACnC,IAAMG,MAAKF,MAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAE1C,IAAM,oBAAoB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACjD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,GAAG;AACZ,WAAO,EAAE,MAAM,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE;AAAA,EAC1C;AAEA,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AAEzB,MAAI,KAAK,KAAK,KAAK,KAAKC;AACxB,MAAI,KAAK,KAAK,KAAK,KAAKC;AACxB,MAAI,IAAI,IAAI,KAAK,KAAK,IAAI,IAAIC,KAAI,KAAK,KAAK,IAAI,MAAM,KAAK,CAAC;AAC5D,MAAI,IAAK,KAAK,IAAI,OAAQ,IAAI;AAC9B,MAAI,IAAK,KAAK,KAAK,IAAI,KAAK,KAAK,OAAQ,IAAI;AAE7C,MAAI,MAAM,EAAE,MAAM,SAAS,GAAG,GAAG,EAAE;AACnC,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AAEA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;ACVf,IAAM,oBAAoB,CAAAC,SACzB,0BAAkB,0BAAkB,0BAAkBA,IAAG,CAAC,CAAC;AAC5D,IAAM,oBAAoB,CAAAC,WACzB,0BAAkB,0BAAkB,0BAAkBA,MAAK,CAAC,CAAC;AAE9D,IAAMC,eAAa;AAAA,EAClB,MAAM;AAAA,EAEN,QAAQ;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AAAA,EACN;AAAA,EAEA,UAAU;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,EACN;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,OAAO,CAAC,SAAS;AAAA,EACjB,WAAW;AAAA,EAEX,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,GAAG;AAAA,IACV,GAAG,CAAC,GAAG,OAAO;AAAA,IACd,GAAG,CAAC,GAAG,GAAG;AAAA,EACX;AAAA,EAEA,aAAa;AAAA,IACZ,GAAG,EAAE,KAAK,oBAAoB,OAAO,gBAAgB;AAAA,IACrD,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AAAA,EAEA,YAAY;AAAA,IACX,GAAG;AAAA,EACJ;AAAA,EAEA,SAAS;AAAA,IACR,GAAG;AAAA,EACJ;AACD;AAEA,IAAOC,uBAAQD;;;AC/Df,IAAME,eAAa;AAAA,EAClB,GAAG;AAAA,EACH,MAAM;AAAA,EAEN,QAAQ;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EAEA,UAAU;AAAA,IACT,KAAK;AAAA,EACN;AAAA,EAEA,OAAO,CAAC,aAAa;AAAA,EACrB,WAAW;AACZ;AAEA,IAAOC,uBAAQD;;;ACHf,IAAME,eAAa;AAAA,EAClB,MAAM;AAAA,EAEN,QAAQ;AAAA,IACP,OAAO;AAAA,IACP,KAAK,CAAAC,SAAO,0BAAkB,0BAAkBA,IAAG,CAAC;AAAA,EACrD;AAAA,EAEA,UAAU;AAAA,IACT,OAAO;AAAA,IACP,KAAK,CAAAC,SAAO,0BAAkB,0BAAkBA,IAAG,CAAC;AAAA,EACrD;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,OAAO,CAAC,OAAO;AAAA,EACf,WAAW;AAAA,EAEX,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,GAAG;AAAA,IACV,GAAG,CAAC,SAAS,OAAO;AAAA,IACpB,GAAG,CAAC,UAAU,MAAM;AAAA,EACrB;AAAA,EAEA,aAAa;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AACD;AAEA,IAAOC,uBAAQH;;;ACjDf,IAAM,qBAAqB,CAAC,EAAE,GAAAI,IAAG,GAAG,GAAG,MAAM,MAAM;AAClD,MAAIA,OAAM,OAAW,CAAAA,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,IAAI,KAAK;AAAA,IACZ,sBAAsBA,KAAI,eAAe,IAAI,eAAe;AAAA,EAC7D;AACA,MAAIC,KAAI,KAAK;AAAA,IACZ,qBAAqBD,KAAI,qBAAqB,IAAI,eAAe;AAAA,EAClE;AACA,MAAI,IAAI,KAAK;AAAA,IACZ,sBAAsBA,KAAI,eAAe,IAAI,qBAAqB;AAAA,EACnE;AAEA,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAG,eAAe,IAAI,cAAcC,KAAI,eAAe;AAAA,IACvD,GAAG,eAAe,IAAI,cAAcA,KAAI,eAAe;AAAA,IACvD,GAAG,eAAe,IAAI,eAAeA,KAAI,cAAc;AAAA,EACxD;AAEA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AAEA,SAAO;AACR;AAEA,IAAO,6BAAQ;;;ACzBf,IAAM,oBAAoB,CAAAC,SAAO;AAChC,MAAI,MAAM,2BAAmB,yBAAiBA,IAAG,CAAC;AAClD,MAAIA,KAAI,MAAMA,KAAI,KAAKA,KAAI,MAAMA,KAAI,GAAG;AACvC,QAAI,IAAI,IAAI,IAAI;AAAA,EACjB;AACA,SAAO;AACR;AAEA,IAAO,4BAAQ;;;ACXf,IAAM,qBAAqB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AAClD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,IAAI,KAAK;AAAA,IACZ,IAAI,qBACH,sBAAyB,IACzB,qBAAyB;AAAA,IAC1B;AAAA,EACD;AACA,MAAIC,KAAI,KAAK;AAAA,IACZ,IAAI,qBACH,sBAAwB,IACxB,sBAA0B;AAAA,IAC3B;AAAA,EACD;AACA,MAAI,IAAI,KAAK;AAAA,IACZ,IAAI,qBACH,sBAA0B,IAC1B,qBAAwB;AAAA,IACzB;AAAA,EACD;AAEA,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GACC,oBAAqB,IACrB,oBAAoBA,KACpB,oBAAoB;AAAA,IACrB,GACC,sBAAsB,IACtB,qBAAqBA,KACrB,qBAAqB;AAAA,IACtB,GACC,wBAAwB,IACxB,qBAAqBA,KACrB,qBAAqB;AAAA,EACvB;AAEA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AAEA,SAAO;AACR;AAEA,IAAO,6BAAQ;;;AC3Cf,IAAM,oBAAoB,CAAAC,OAAK,yBAAiB,2BAAmBA,EAAC,CAAC;AAErE,IAAO,4BAAQ;;;ACsBR,SAAS,IAAI,GAAG;AACtB,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,QAAM,OAAO,IAAI,QAAQ,IAAI;AAC7B,SACC,OACC,MAAM,IACN,MACA,KAAK,MAAM,MAAM,IAAI,QAAQ,MAAM,IAAI,OAAO,IAAI,MAAM,MAAM,CAAC;AAElE;AAEO,SAAS,QAAQ,GAAG;AAC1B,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,QAAM,OAAO,IAAI,QAAQ,IAAI;AAC7B,UAAQ,IAAI,IAAI,MAAM,MAAM,OAAO,IAAI;AACxC;AAKA,SAAS,uBAAuB,GAAG,GAAG;AAIrC,MAAI,IAAI,IAAIC,KAAIC,KAAI,IAAI,IAAI,IAAI;AAEhC,MAAI,cAAc,IAAI,aAAa,IAAI,GAAG;AAEzC,SAAK;AACL,SAAK;AACL,IAAAD,MAAK;AACL,IAAAC,MAAK;AACL,SAAK;AACL,SAAK;AACL,SAAK;AACL,SAAK;AAAA,EACN,WAAW,aAAa,IAAI,aAAa,IAAI,GAAG;AAE/C,SAAK;AACL,SAAK;AACL,IAAAD,MAAK;AACL,IAAAC,MAAK;AACL,SAAK;AACL,SAAK;AACL,SAAK;AACL,SAAK;AAAA,EACN,OAAO;AAEN,SAAK;AACL,SAAK;AACL,IAAAD,MAAK;AACL,IAAAC,MAAK;AACL,SAAK;AACL,SAAK;AACL,SAAK;AACL,SAAK;AAAA,EACN;AAGA,MAAI,IAAI,KAAK,KAAK,IAAID,MAAK,IAAIC,MAAK,IAAI,IAAI,KAAK,IAAI;AAMrD,MAAI,MAAM,eAAgB,IAAI,eAAe;AAC7C,MAAI,MAAM,gBAAgB,IAAI,eAAe;AAC7C,MAAI,MAAM,gBAAgB,IAAI,cAAc;AAE5C;AACC,QAAI,KAAK,IAAI,IAAI;AACjB,QAAI,KAAK,IAAI,IAAI;AACjB,QAAI,KAAK,IAAI,IAAI;AAEjB,QAAI,IAAI,KAAK,KAAK;AAClB,QAAI,IAAI,KAAK,KAAK;AAClB,QAAI,IAAI,KAAK,KAAK;AAElB,QAAI,OAAO,IAAI,MAAM,KAAK;AAC1B,QAAI,OAAO,IAAI,MAAM,KAAK;AAC1B,QAAI,OAAO,IAAI,MAAM,KAAK;AAE1B,QAAI,QAAQ,IAAI,MAAM,MAAM;AAC5B,QAAI,QAAQ,IAAI,MAAM,MAAM;AAC5B,QAAI,QAAQ,IAAI,MAAM,MAAM;AAE5B,QAAIC,KAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AAC/B,QAAI,KAAK,KAAK,OAAO,KAAK,OAAO,KAAK;AACtC,QAAIC,MAAK,KAAK,QAAQ,KAAK,QAAQ,KAAK;AAExC,QAAI,IAAKD,KAAI,MAAO,KAAK,KAAK,MAAMA,KAAIC;AAAA,EACzC;AAEA,SAAO;AACR;AAEO,SAAS,UAAU,GAAG,GAAG;AAE/B,MAAI,SAAS,uBAAuB,GAAG,CAAC;AAGxC,MAAIC,OAAM,2BAAmB,EAAE,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,SAAS,EAAE,CAAC;AACnE,MAAI,SAAS,KAAK,KAAK,IAAI,KAAK,IAAIA,KAAI,GAAGA,KAAI,GAAGA,KAAI,CAAC,CAAC;AACxD,MAAI,SAAS,SAAS;AAEtB,SAAO,CAAC,QAAQ,MAAM;AACvB;AAMA,SAAS,wBAAwB,GAAG,GAAG,IAAIC,KAAI,IAAI,OAAO,MAAM;AAC/D,MAAI,CAAC,MAAM;AAEV,WAAO,UAAU,GAAG,CAAC;AAAA,EACtB;AAGA,MAAI;AACJ,OAAK,KAAK,MAAM,KAAK,CAAC,KAAK,KAAK,CAAC,IAAI,MAAMA,OAAM,GAAG;AAGnD,QAAK,KAAK,CAAC,IAAI,MAAOA,MAAK,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK;AAAA,EACtD,OAAO;AAIN,QAAK,KAAK,CAAC,KAAK,KAAK,MAAOA,OAAM,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,KAAK,KAAK;AAGjE;AACC,UAAI,KAAK,KAAK;AACd,UAAI,KAAKA;AAET,UAAI,MAAM,eAAgB,IAAI,eAAe;AAC7C,UAAI,MAAM,gBAAgB,IAAI,eAAe;AAC7C,UAAI,MAAM,gBAAgB,IAAI,cAAc;AAE5C,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,OAAO,KAAK,KAAK;AAGrB;AACC,YAAI,IAAI,MAAM,IAAI,KAAK,IAAI;AAC3B,YAAI,IAAI,IAAIA;AAEZ,YAAI,KAAK,IAAI,IAAI;AACjB,YAAI,KAAK,IAAI,IAAI;AACjB,YAAI,KAAK,IAAI,IAAI;AAEjB,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,IAAI,KAAK,KAAK;AAClB,YAAI,IAAI,KAAK,KAAK;AAElB,YAAI,MAAM,IAAI,OAAO,KAAK;AAC1B,YAAI,MAAM,IAAI,OAAO,KAAK;AAC1B,YAAI,MAAM,IAAI,OAAO,KAAK;AAE1B,YAAI,OAAO,IAAI,OAAO,OAAO;AAC7B,YAAI,OAAO,IAAI,OAAO,OAAO;AAC7B,YAAI,OAAO,IAAI,OAAO,OAAO;AAE7B,YAAIC,KACH,eAAe,IAAI,eAAe,IAAI,eAAe,IAAI;AAC1D,YAAI,KACH,eAAe,MACf,eAAe,MACf,eAAe;AAChB,YAAIC,MACH,eAAe,OACf,eAAe,OACf,eAAe;AAEhB,YAAI,MAAM,MAAM,KAAK,KAAK,MAAMD,KAAIC;AACpC,YAAI,MAAM,CAACD,KAAI;AAEf,YAAI,IACH,gBAAgB,IAAI,eAAe,IAAI,eAAe,IAAI;AAC3D,YAAI,KACH,gBAAgB,MAChB,eAAe,MACf,eAAe;AAChB,YAAI,KACH,gBAAgB,OAChB,eAAe,OACf,eAAe;AAEhB,YAAI,MAAM,MAAM,KAAK,KAAK,MAAM,IAAI;AACpC,YAAI,MAAM,CAAC,IAAI;AAEf,YAAIE,KACH,gBAAgB,IAAI,eAAe,IAAI,cAAc,IAAI;AAC1D,YAAI,KACH,gBAAgB,MAChB,eAAe,MACf,cAAc;AACf,YAAIC,MACH,gBAAgB,OAChB,eAAe,OACf,cAAc;AAEf,YAAI,MAAM,MAAM,KAAK,KAAK,MAAMD,KAAIC;AACpC,YAAI,MAAM,CAACD,KAAI;AAEf,cAAM,OAAO,IAAI,MAAM;AACvB,cAAM,OAAO,IAAI,MAAM;AACvB,cAAM,OAAO,IAAI,MAAM;AAEvB,aAAK,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,CAAC;AAAA,MACtC;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEO,SAAS,WAAW,IAAI,IAAI,OAAO,MAAM;AAC/C,MAAI,CAAC,MAAM;AACV,WAAO,UAAU,IAAI,EAAE;AAAA,EACxB;AACA,MAAI,IAAI,KAAK,CAAC;AACd,MAAI,IAAI,KAAK,CAAC;AACd,SAAO,CAAC,IAAI,GAAG,KAAK,IAAI,EAAE;AAC3B;AAsCO,SAAS,OAAO,GAAG,IAAI,IAAI;AACjC,MAAI,OAAO,UAAU,IAAI,EAAE;AAE3B,MAAI,QAAQ,wBAAwB,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI;AACzD,MAAI,SAAS,WAAW,IAAI,IAAI,IAAI;AAEpC,MAAI,QACH,aACA,KACE,YACA,YAAY,KACZ,MACE,cACA,aAAa,KACb,MACE,cACA,cAAc,KACd,MACE,cACA,aAAa,KACb,aAAa;AAEtB,MAAI,QACH,aACA,KACE,YACA,aAAa,KACb,MACE,aACA,aAAa,KACb,MACE,cACA,YAAY,KACZ,MACE,YACA,aAAa,KACb,aAAa;AAEtB,MAAIE,KAAI,QAAQ,KAAK,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,CAAC;AAE3D,MAAI,MAAM,IAAI;AACd,MAAI,OAAO,IAAI,KAAK;AACpB,MAAI,QACH,MACAA,KACA,KAAK;AAAA,IACJ,KAAK;AAAA,MACJ,KAAK,KAAK,MAAM,MAAM,MAAM,OAAO,KAAK,MAAM,MAAM,MAAM;AAAA,IAC3D;AAAA,EACD;AAED,QAAM,IAAI;AACV,SAAO,IAAI,KAAK;AAChB,MAAI,MAAM,KAAK,KAAK,KAAK,KAAK,MAAM,OAAO,KAAK,MAAM,KAAK;AAC3D,SAAO,CAAC,KAAK,OAAO,KAAK;AAC1B;;;AC/Te,SAAR,oBAAqCC,MAAK;AAChD,QAAM,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AACxC,QAAM,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AACxC,QAAM,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AAExC,QAAM,MAAM,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE;AAEvC,MAAIA,KAAI,UAAU,QAAW;AAC5B,QAAI,QAAQA,KAAI;AAAA,EACjB;AACA,MAAIC,KAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAC/B,MAAI,CAACA,IAAG;AACP,QAAI,IAAI;AACR,WAAO;AAAA,EACR;AACA,MAAI,CAAC,KAAK,OAAO,KAAK,IAAI,OAAO,GAAG,IAAIA,IAAG,IAAIA,EAAC;AAChD,MAAI;AACJ,MAAIA,KAAI,OAAO;AACd,QAAI,MAAM;AACV,QAAI,MAAM,MAAM;AAChB,QAAI,MAAM,IAAI,MAAM;AACpB,QAAI,KAAKA,KAAI,QAAQ,MAAM,OAAOA,KAAI;AACtC,QAAI,IAAI;AAAA,EACT,OAAO;AACN,QAAI,MAAM;AACV,QAAI,MAAO,MAAM,QAAQ,QAAQ,OAAO,OAAQ;AAChD,QAAI,MAAM,IAAI,OAAO,QAAQ;AAC7B,QAAI,KAAKA,KAAI,QAAQ,MAAM,OAAOA,KAAI;AACtC,QAAI,MAAM,MAAM;AAAA,EACjB;AACA,MAAI,GAAG;AACN,QAAI,IAAI;AACR,QAAI,IAAI,qBAAc,KAAK,MAAM,GAAG,CAAC,IAAI,MAAO,KAAK,EAAE;AAAA,EACxD;AACA,SAAO;AACR;;;ACpCe,SAAR,oBAAqCC,MAAK;AAChD,MAAI,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AACtC,MAAI,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AACtC,MAAI,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AAEtC,QAAM,MAAM,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,EAAE;AAE3C,MAAIA,KAAI,UAAU,QAAW;AAC5B,QAAI,QAAQA,KAAI;AAAA,EACjB;AAEA,MAAI,CAAC,KAAK,MAAM,GAAG;AAClB,QAAI,IAAI,IAAI,IAAI;AAChB,WAAO;AAAA,EACR;AAEA,MAAI,KAAK,KAAK,IAAK,IAAI,MAAO,KAAK,EAAE;AACrC,MAAI,KAAK,KAAK,IAAK,IAAI,MAAO,KAAK,EAAE;AACrC,MAAI,CAAC,KAAK,OAAO,KAAK,IAAI,OAAO,IAAI,GAAG,IAAI,EAAE;AAC9C,MAAI,GAAG,KAAK,KAAK;AACjB,MAAI,IAAI,KAAK;AACZ,QAAI,OAAO;AACX,UAAM;AACN,UAAM,MAAM;AACZ,UAAM,IAAI,MAAM;AAAA,EACjB,OAAO;AACN,QAAI,KAAK,IAAI;AACb,UAAM;AACN,UAAO,MAAM,QAAQ,QAAQ,OAAO,OAAQ;AAC5C,UAAM,IAAI,OAAO,QAAQ;AAAA,EAC1B;AACA,MAAI,IAAI,MAAO,IAAI,OAAQ,IAAI,MAAM;AACrC,MAAI,IAAI,IAAI;AACZ,MAAI,IAAI,IAAI;AAEZ,SAAO;AACR;;;ACxDA,IAAM,YAAY;AAAA,EACjB,GAAGC;AAAA,EACH,MAAM;AAAA,EACN,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EACjC,OAAO,CAAC,SAAS;AAAA,EACjB,WAAW;AAAA,EACX,UAAU;AAAA,IACT,OAAO;AAAA,IACP,KAAK,CAAAC,OAAK,oBAAoB,0BAAkBA,EAAC,CAAC;AAAA,EACnD;AAAA,EACA,QAAQ;AAAA,IACP,OAAO;AAAA,IACP,KAAK,CAAAA,OAAK,0BAAkB,oBAAoBA,EAAC,CAAC;AAAA,EACnD;AACD;AAEA,IAAO,oBAAQ;;;ACMA,SAAR,oBAAqCC,MAAK;AAChD,MAAI,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AACtC,MAAI,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AACtC,MAAI,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AAEtC,MAAIC,KAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AAG/B,MAAI,KAAKA,KAAI,IAAIA,KAAI;AACrB,MAAI,KAAKA,KAAI,IAAIA,KAAI;AAErB,MAAI,CAAC,OAAO,CAAC,IAAI,WAAW,IAAI,EAAE;AAClC,MAAI,MAAM;AACV,MAAIC,KAAI,IAAI,MAAM;AAElB,MAAI,IAAI,KAAKD,KAAI,IAAI;AACrB,MAAI,MAAM,IAAI;AACd,MAAI,MAAM,IAAIA;AAEd,MAAI,OAAO,QAAQ,GAAG;AACtB,MAAI,OAAQ,MAAM,OAAQ;AAE1B,MAAI,YAAY,2BAAmB,EAAE,GAAG,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,KAAK,CAAC;AAC1E,MAAI,UAAU,KAAK;AAAA,IAClB,IAAI,KAAK,IAAI,UAAU,GAAG,UAAU,GAAG,UAAU,GAAG,CAAC;AAAA,EACtD;AAEA,MAAI,IAAI;AACR,EAAAA,KAAMA,KAAI,UAAW,IAAI,CAAC,IAAK;AAC/B,MAAI,IAAI,CAAC;AAET,QAAM,MAAM;AAAA,IACX,MAAM;AAAA,IACN,GAAGA,MAAM,MAAM,KAAK,OAAQ,IAAI,MAAM,IAAIC,KAAI,OAAO;AAAA,IACrD,GAAG,IAAI,IAAI,MAAM;AAAA,EAClB;AACA,MAAI,IAAI,GAAG;AACV,QAAI,IAAI,qBAAc,KAAK,MAAM,GAAG,CAAC,IAAI,MAAO,KAAK,EAAE;AAAA,EACxD;AACA,MAAIF,KAAI,UAAU,QAAW;AAC5B,QAAI,QAAQA,KAAI;AAAA,EACjB;AACA,SAAO;AACR;;;AC/Ce,SAAR,oBAAqCG,MAAK;AAChD,QAAM,MAAM,EAAE,MAAM,QAAQ;AAC5B,MAAIA,KAAI,UAAU,QAAW;AAC5B,QAAI,QAAQA,KAAI;AAAA,EACjB;AAEA,QAAM,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AACxC,QAAM,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AACxC,QAAM,IAAIA,KAAI,MAAM,SAAYA,KAAI,IAAI;AAExC,QAAM,KAAK,KAAK,IAAK,IAAI,MAAO,KAAK,EAAE;AACvC,QAAM,KAAK,KAAK,IAAK,IAAI,MAAO,KAAK,EAAE;AAEvC,QAAM,CAAC,OAAO,CAAC,IAAI,WAAW,IAAI,EAAE;AACpC,QAAM,MAAM;AACZ,QAAMC,KAAI,IAAI,MAAM;AACpB,QAAM,MAAM,IAAK,IAAI,OAAQ,MAAM,IAAI,IAAIA,KAAI;AAC/C,QAAM,MAAO,IAAI,IAAI,OAAQ,MAAM,IAAI,IAAIA,KAAI;AAE/C,QAAM,OAAO,QAAQ,GAAG;AACxB,QAAM,OAAQ,MAAM,OAAQ;AAC5B,QAAM,YAAY,2BAAmB;AAAA,IACpC,GAAG;AAAA,IACH,GAAG,KAAK;AAAA,IACR,GAAG,KAAK;AAAA,EACT,CAAC;AACD,QAAM,UAAU,KAAK;AAAA,IACpB,IAAI,KAAK,IAAI,UAAU,GAAG,UAAU,GAAG,UAAU,GAAG,CAAC;AAAA,EACtD;AAEA,QAAM,QAAQ,QAAQ,IAAI,GAAG;AAC7B,QAAM,IAAK,MAAM,QAAS;AAE1B,MAAI,IAAI,QAAQ;AAChB,MAAI,IAAI,IAAI,KAAK;AACjB,MAAI,IAAI,IAAI,KAAK;AAEjB,SAAO;AACR;;;ACxDA,IAAM,YAAY;AAAA,EACjB,GAAGC;AAAA,EACH,MAAM;AAAA,EACN,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EACjC,OAAO,CAAC,SAAS;AAAA,EACjB,WAAW;AAAA,EACX,UAAU;AAAA,IACT,OAAO;AAAA,IACP,KAAK,CAAAC,OAAK,oBAAoB,0BAAkBA,EAAC,CAAC;AAAA,EACnD;AAAA,EACA,QAAQ;AAAA,IACP,OAAO;AAAA,IACP,KAAK,CAAAA,OAAK,0BAAkB,oBAAoBA,EAAC,CAAC;AAAA,EACnD;AACD;AAEA,IAAO,oBAAQ;;;ACrBf,SAAS,WAAW,OAAO,QAAQ;AAClC,MAAI,CAAC,UAAU,OAAO,CAAC,MAAM,SAAS;AACrC,WAAO;AAAA,EACR;AACA,QAAM,MAAM,EAAE,MAAM,QAAQ;AAC5B,QAAM,CAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAC3B,MAAI,EAAE,SAAS,IAAI,OAAO,EAAE,SAAS,IAAI,OAAO,EAAE,SAAS,IAAI,KAAK;AACnE,WAAO;AAAA,EACR;AACA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,IAAI,KAAK;AAAA,MACZ,KAAK,IAAI,GAAG,EAAE,SAAS,IAAI,SAAS,EAAE,QAAQ,EAAE,QAAQ,GAAG;AAAA,MAC3D;AAAA,IACD;AAAA,EACD;AACA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,IAAI,EAAE,SAAS,IAAI,SAAS,EAAE,QAAS,EAAE,QAAQ,MAAO;AAAA,EAC7D;AACA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,IAAI,EAAE,SAAS,IAAI,SAAS,EAAE,QAAS,EAAE,QAAQ,MAAO;AAAA,EAC7D;AACA,MAAI,MAAM,SAAS,IAAI,MAAM;AAC5B,QAAI,QAAQ,KAAK;AAAA,MAChB;AAAA,MACA,KAAK;AAAA,QACJ;AAAA,QACA,MAAM,SAAS,IAAI,SAAS,MAAM,QAAQ,MAAM,QAAQ;AAAA,MACzD;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEA,IAAO,qBAAQ;;;ACvBf,IAAMC,eAAa;AAAA,EAClB,GAAGC;AAAA,EACH,MAAM;AAAA,EAEN,QAAQ;AAAA,IACP,MAAM;AAAA,IACN,KAAK;AAAA,EACN;AAAA,EAEA,UAAU;AAAA,IACT,MAAM;AAAA,IACN,KAAK;AAAA,EACN;AAAA,EAEA,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,CAAC;AAAA,IACR,GAAG,CAAC,MAAM,GAAG;AAAA,IACb,GAAG,CAAC,MAAM,GAAG;AAAA,EACd;AAAA,EAEA,OAAO,CAAC,kBAAU;AAAA,EAClB,WAAW,CAAAC,OACV,SAASA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,IACxCA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAC3B,IAAIA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,GACnCA,GAAE,QAAQ,IAAI,MAAMA,GAAE,KAAK,KAAK,EACjC;AACF;AAEA,IAAOD,uBAAQD;;;ACxCf,SAAS,WAAW,OAAO,QAAQ;AAClC,MAAI,CAAC,UAAU,OAAO,CAAC,MAAM,SAAS;AACrC,WAAO;AAAA,EACR;AACA,QAAM,MAAM,EAAE,MAAM,QAAQ;AAC5B,QAAM,CAAC,EAAE,GAAGG,IAAG,GAAG,KAAK,IAAI;AAC3B,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,EAAE,SAAS,IAAI,KAAK;AACvB,aAAO;AAAA,IACR;AACA,QAAI,IAAI,KAAK;AAAA,MACZ,KAAK,IAAI,GAAG,EAAE,SAAS,IAAI,SAAS,EAAE,QAAQ,EAAE,QAAQ,GAAG;AAAA,MAC3D;AAAA,IACD;AAAA,EACD;AACA,MAAIA,GAAE,SAAS,IAAI,MAAM;AACxB,QAAI,IAAI,KAAK;AAAA,MACZ;AAAA,MACAA,GAAE,SAAS,IAAI,SAASA,GAAE,QAASA,GAAE,QAAQ,MAAO;AAAA,IACrD;AAAA,EACD;AACA,MAAI,EAAE,SAAS,IAAI,MAAM;AACxB,QAAI,EAAE,SAAS,IAAI,YAAY;AAC9B,aAAO;AAAA,IACR;AACA,QAAI,IAAI,EAAE;AAAA,EACX;AACA,MAAI,MAAM,SAAS,IAAI,MAAM;AAC5B,QAAI,QAAQ,KAAK;AAAA,MAChB;AAAA,MACA,KAAK;AAAA,QACJ;AAAA,QACA,MAAM,SAAS,IAAI,SAAS,MAAM,QAAQ,MAAM,QAAQ;AAAA,MACzD;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEA,IAAO,qBAAQ;;;ACnCf,IAAMC,eAAa;AAAA,EAClB,GAAGC;AAAA,EACH,MAAM;AAAA,EAEN,QAAQ;AAAA,IACP,OAAO,CAAAC,OAAK,wBAAgBA,IAAG,OAAO;AAAA,IACtC,KAAK,CAAAA,OAAK,0BAAkB,wBAAgBA,IAAG,OAAO,CAAC;AAAA,EACxD;AAAA,EAEA,UAAU;AAAA,IACT,KAAK,CAAAA,OAAK,wBAAgB,0BAAkBA,EAAC,GAAG,OAAO;AAAA,IACvD,OAAO,CAAAA,OAAK,wBAAgBA,IAAG,OAAO;AAAA,EACvC;AAAA,EAEA,OAAO,CAAC,kBAAU;AAAA,EAClB,WAAW,CAAAA,OACV,SAASA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,IACxCA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAC3B,IAAIA,GAAE,MAAM,SAAYA,GAAE,IAAI,MAAM,GACnCA,GAAE,QAAQ,IAAI,MAAMA,GAAE,KAAK,KAAK,EACjC;AAAA,EAED,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,CAAC;AAAA,IACR,GAAG,CAAC,GAAG,GAAG;AAAA,IACV,GAAG,CAAC,GAAG,GAAG;AAAA,EACX;AACD;AAEA,IAAOD,uBAAQD;;;AC1Bf,IAAM,mBAAmB,CAAAG,SAAO;AAC/B,MAAI,EAAE,GAAAC,IAAG,GAAG,GAAG,MAAM,IAAI,yBAAiBD,IAAG;AAC7C,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GACC,oBAAoBC,KACpB,oBAAoB,IACpB,qBAAqB;AAAA,IACtB,GACC,qBAAqBA,KACrB,qBAAqB,IACrB,oBAAoB;AAAA,IACrB,GAAG,IAAMA,KAAI,qBAAqB,IAAI,oBAAoB;AAAA,EAC3D;AACA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,2BAAQ;;;ACpBf,IAAM,mBAAmB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AAChD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM;AAAA,IACT;AAAA,MACC,GACC,IAAI,qBACJ,IAAI,qBACJ,oBAAoB;AAAA,MACrB,GACC,IAAI,sBACJ,IAAI,qBACJ,qBAAqB;AAAA,MACtB,GACC,IAAI,qBACJ,IAAI,qBACJ,qBAAqB;AAAA,IACvB;AAAA,IACA;AAAA,EACD;AACA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,2BAAQ;;;AC/Bf,IAAMC,eAAa;AAAA,EAClB,GAAG;AAAA,EACH,MAAM;AAAA,EACN,OAAO,CAAC,YAAY;AAAA,EACpB,WAAW;AAAA,EAEX,UAAU;AAAA,IACT,KAAK,WAAS,yBAAiB,0BAAkB,KAAK,CAAC;AAAA,IACvD,OAAO;AAAA,EACR;AAAA,EAEA,QAAQ;AAAA,IACP,KAAK,WAAS,0BAAkB,yBAAiB,KAAK,CAAC;AAAA,IACvD,OAAO;AAAA,EACR;AACD;AAEA,IAAOC,uBAAQD;;;ACff,IAAME,SAAQ,OAAK;AAClB,MAAIC,OAAM,KAAK,IAAI,CAAC;AACpB,MAAIA,QAAO,IAAI,KAAK;AACnB,WAAO,KAAK,KAAK,CAAC,IAAI,KAAK,IAAIA,MAAK,IAAI,GAAG;AAAA,EAC5C;AACA,SAAO,KAAK;AACb;AAEA,IAAM,yBAAyB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACtD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAGD;AAAA,MACF,IAAI,qBACH,IAAI,qBACJ,qBAAqB;AAAA,IACvB;AAAA,IACA,GAAGA;AAAA,MACF,IAAI,sBACH,IAAI,qBACJ,qBAAqB;AAAA,IACvB;AAAA,IACA,GAAGA,OAAM,IAAI,IAAM,IAAI,IAAM,qBAAqB,CAAC;AAAA,EACpD;AACA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,iCAAQ;;;AChCf,IAAME,aAAY,CAAC,IAAI,MAAM;AAC5B,MAAIC,OAAM,KAAK,IAAI,CAAC;AACpB,MAAIA,QAAO,KAAK,KAAK;AACpB,WAAO,KAAK,KAAK,CAAC,IAAI,KAAK,IAAIA,MAAK,GAAG;AAAA,EACxC;AACA,SAAO,IAAI;AACZ;AAEA,IAAM,yBAAyB,CAAAC,cAAY;AAC1C,MAAIC,KAAIH,WAAUE,UAAS,CAAC;AAC5B,MAAI,IAAIF,WAAUE,UAAS,CAAC;AAC5B,MAAI,IAAIF,WAAUE,UAAS,CAAC;AAC5B,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GACC,qBAAqBC,KACrB,qBAAqB,IACrB,qBAAqB;AAAA,IACtB,GACC,qBAAqBA,KACrB,qBAAqB,IACrB,mBAAqB;AAAA,IACtB,GAAG,IAAIA,KAAI,IAAI,IAAI,qBAAqB;AAAA,EACzC;AACA,MAAID,UAAS,UAAU,QAAW;AACjC,QAAI,QAAQA,UAAS;AAAA,EACtB;AACA,SAAO;AACR;AAEA,IAAO,iCAAQ;;;ACvBf,IAAME,eAAa;AAAA,EAClB,GAAG;AAAA,EACH,MAAM;AAAA,EACN,OAAO,CAAC,cAAc;AAAA,EACtB,WAAW;AAAA,EAEX,UAAU;AAAA,IACT,OAAO;AAAA,IACP,KAAK,WAAS,+BAAuB,0BAAkB,KAAK,CAAC;AAAA,EAC9D;AAAA,EAEA,QAAQ;AAAA,IACP,OAAO;AAAA,IACP,KAAK,WAAS,0BAAkB,+BAAuB,KAAK,CAAC;AAAA,EAC9D;AACD;AAEA,IAAOC,uBAAQD;;;ACvBf,IAAM,IAAI;AACV,IAAM,IAAI;AACV,IAAME,SAAQ,OAAK;AAClB,QAAMC,OAAM,KAAK,IAAI,CAAC;AACtB,MAAIA,OAAM,GAAG;AACZ,YAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,IAAI,KAAK,IAAIA,MAAK,IAAI,KAAK,IAAI;AAAA,EAC9D;AACA,SAAO,MAAM;AACd;AAEA,IAAM,wBAAwB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AACrD,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GAAGD;AAAA,MACF,IAAI,qBACH,IAAI,qBACJ,qBAAqB;AAAA,IACvB;AAAA,IACA,GAAGA;AAAA,MACF,IAAI,sBACH,IAAI,qBACJ,qBAAqB;AAAA,IACvB;AAAA,IACA,GAAGA;AAAA,MACF,IAAI,qBACH,IAAI,qBACJ,qBAAqB;AAAA,IACvB;AAAA,EACD;AACA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,gCAAQ;;;ACtCf,IAAME,KAAI;AACV,IAAMC,KAAI;AAEV,IAAMC,aAAY,CAAC,IAAI,MAAM;AAC5B,MAAIC,OAAM,KAAK,IAAI,CAAC;AACpB,MAAIA,OAAMF,KAAI,KAAK;AAClB,WAAO,IAAI;AAAA,EACZ;AACA,UAAQ,KAAK,KAAK,CAAC,KAAK,KAAK,KAAK,KAAKE,OAAMH,KAAI,KAAKA,IAAG,IAAI,IAAI;AAClE;AAEA,IAAM,wBAAwB,CAAAI,aAAW;AACxC,MAAIC,KAAIH,WAAUE,SAAQ,CAAC;AAC3B,MAAI,IAAIF,WAAUE,SAAQ,CAAC;AAC3B,MAAI,IAAIF,WAAUE,SAAQ,CAAC;AAC3B,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GACC,qBAAqBC,KACrB,qBAAqB,IACrB,qBAAqB;AAAA,IACtB,GACC,oBAAoBA,KACpB,qBAAqB,IACrB,oBAAoB;AAAA,IACrB,GAAG,IAAIA,KAAI,qBAAqB,IAAI,qBAAqB;AAAA,EAC1D;AACA,MAAID,SAAQ,UAAU,QAAW;AAChC,QAAI,QAAQA,SAAQ;AAAA,EACrB;AACA,SAAO;AACR;AAEA,IAAO,gCAAQ;;;AClCf,IAAME,eAAa;AAAA,EAClB,GAAG;AAAA,EACH,MAAM;AAAA,EAEN,UAAU;AAAA,IACT,OAAO;AAAA,IACP,KAAK,WAAS,8BAAsB,0BAAkB,KAAK,CAAC;AAAA,EAC7D;AAAA,EAEA,QAAQ;AAAA,IACP,OAAO;AAAA,IACP,KAAK,WAAS,0BAAkB,8BAAsB,KAAK,CAAC;AAAA,EAC7D;AAAA,EAEA,OAAO,CAAC,SAAS;AAAA,EACjB,WAAW;AACZ;AAEA,IAAOC,uBAAQD;;;AC1BR,IAAM,OAAO;AACb,IAAM,YAAY,KAAK,KAAK,IAAI;;;ACEvC,IAAM,WAAW,OAAK,KAAK,KAAK,CAAC,IAAI;AAErC,IAAM,kBAAkB,WAAS;AAChC,QAAM,EAAE,GAAAE,IAAG,GAAG,GAAG,MAAM,IAAI,yBAAiB,KAAK;AACjD,QAAM,IAAI,SAAS,MAAMA,KAAI,QAAQ,IAAI,QAAQ,IAAI,IAAI;AACzD,QAAM,IAAI,SAAS,OAAOA,KAAI,QAAQ,IAAI,QAAQ,IAAI,IAAI;AAC1D,QAAM,IAAI;AAAA,IACT,qBAAsBA,KACrB,qBAAsB,IACtB,qBAAqB,IACrB;AAAA,EACF;AACA,QAAM,MAAM;AAAA,IACX,MAAM;AAAA,IACN,IAAI,IAAI,KAAK;AAAA,IACb,IAAI,IAAI,KAAK;AAAA;AAAA,IAEb,GAAG,KAAK,IAAI,KAAK;AAAA,EAClB;AACA,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;AAEA,IAAO,0BAAQ;;;ACvBf,IAAMC,YAAW,OAAK,KAAK,IAAI,IAAI,WAAW,CAAC;AAE/C,IAAM,kBAAkB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AAC/C,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,QAAM,IAAIA,UAAS,IAAI,CAAC,IAAI;AAC5B,QAAM,IAAIA,UAAS,IAAI,CAAC,IAAI;AAE5B,QAAM,IAAIA,UAAS,IAAI,CAAC,IAAI;AAE5B,QAAM,MAAM,yBAAiB;AAAA,IAC5B,GACC,qBAAqB,IACrB,oBAAoB,IACpB,sBAAsB;AAAA,IACvB,GACC,sBAAsB,IACtB,oBAAoB,IACpB,sBAAsB;AAAA,IACvB,GACC,sBAAsB,IACtB,qBAAqB,IACrB,qBAAqB;AAAA,EACvB,CAAC;AACD,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;AAEA,IAAO,0BAAQ;;;ACtBf,IAAMC,eAAa;AAAA,EAClB,MAAM;AAAA,EACN,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EACjC,OAAO,CAAC,OAAO;AAAA,EACf,WAAW;AAAA,EAEX,QAAQ;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EAEA,UAAU;AAAA,IACT,KAAK;AAAA,EACN;AAAA,EAEA,QAAQ;AAAA,IACP,GAAG,CAAC,SAAS,MAAM;AAAA,IACnB,GAAG,CAAC,GAAG,MAAM;AAAA,IACb,GAAG,CAAC,SAAS,KAAK;AAAA,EACnB;AAAA,EAEA,aAAa;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AACD;AAEA,IAAOC,uBAAQD;;;AC1Bf,IAAME,eAAa;AAAA,EAClB,MAAM;AAAA,EACN,OAAO,CAAC,SAAS;AAAA,EACjB,WAAW;AAAA,EAEX,QAAQ;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AAAA,EACN;AAAA,EAEA,UAAU;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,EACN;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,KAAK;AAAA,IACZ,GAAG,CAAC,GAAG,KAAK;AAAA,IACZ,GAAG,CAAC,GAAG,KAAK;AAAA,EACb;AAAA,EAEA,aAAa;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AACD;AAEA,IAAOC,uBAAQD;;;AClCf,IAAM,sBAAsB,CAAAE,WAAS;AACpC,MAAI,EAAE,GAAG,GAAG,GAAG,MAAM,IAAIA;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GACC,qBAAqB,IACrB,qBAAqB,IACrB,qBAAqB;AAAA,IACtB,GACC,qBAAqB,IACrB,oBAAoB,IACpB,qBAAqB;AAAA,IACtB,GACC,sBAAsB,IACtB,qBAAqB,IACrB,qBAAqB;AAAA,EACvB;AACA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,8BAAQ;;;AC1Bf,IAAM,sBAAsB,CAAAC,WAAS;AACpC,MAAI,EAAE,GAAG,GAAG,GAAG,MAAM,IAAIA;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM;AAAA,IACT,MAAM;AAAA,IACN,GACC,qBAAqB,IACrB,qBAAqB,IACrB,qBAAqB;AAAA,IACtB,GACC,sBAAsB,IACtB,qBAAqB,IACrB,oBAAoB;AAAA,IACrB,GACC,qBAAqB,IACrB,qBAAqB,IACrB,qBAAqB;AAAA,EACvB;AACA,MAAI,UAAU,QAAW;AACxB,QAAI,QAAQ;AAAA,EACb;AACA,SAAO;AACR;AAEA,IAAO,8BAAQ;;;ACrBf,IAAMC,eAAa;AAAA,EAClB,MAAM;AAAA,EAEN,QAAQ;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAEA,UAAU;AAAA,IACT,KAAK;AAAA,IACL,OAAO;AAAA,EACR;AAAA,EAEA,QAAQ;AAAA,IACP,GAAG,CAAC,GAAG,IAAI;AAAA,IACX,GAAG,CAAC,GAAG,CAAC;AAAA,IACR,GAAG,CAAC,GAAG,KAAK;AAAA,EACb;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,OAAO,CAAC,OAAO,SAAS;AAAA,EACxB,WAAW;AAAA,EAEX,aAAa;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AACD;AAEA,IAAOC,uBAAQD;;;AC9Cf,IAAM,kBAAkB,CAAC,EAAE,GAAAE,IAAG,GAAG,GAAG,MAAM,MAAM;AAC/C,MAAIA,OAAM,OAAW,CAAAA,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,QAAM,MAAM;AAAA,IACX,MAAM;AAAA,IACN,GAAG,aAAaA,KAAI,aAAa,IAAI,aAAa;AAAA,IAClD,GAAG,aAAaA,KAAI,YAAY,IAAI,aAAa;AAAA,IACjD,GAAG,aAAaA,KAAI,aAAa,IAAI,aAAa;AAAA,EACnD;AACA,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;AAEA,IAAO,0BAAQ;;;ACdf,IAAM,kBAAkB,CAAC,EAAE,GAAG,GAAG,GAAG,MAAM,MAAM;AAC/C,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,MAAI,MAAM,OAAW,KAAI;AACzB,QAAM,MAAM;AAAA,IACX,MAAM;AAAA,IACN,GAAG,IAAI,aAAa,IAAI,YAAY;AAAA,IACpC,GAAG,IAAI,aAAa,IAAI,YAAY;AAAA,IACpC,GAAG,IAAI,aAAa,IAAI,aAAa;AAAA,EACtC;AACA,MAAI,UAAU,OAAW,KAAI,QAAQ;AACrC,SAAO;AACR;AAEA,IAAO,0BAAQ;;;ACWf,IAAMC,eAAa;AAAA,EAClB,MAAM;AAAA,EAEN,QAAQ;AAAA,IACP,KAAK;AAAA,EACN;AAAA,EAEA,UAAU;AAAA,IACT,KAAK;AAAA,EACN;AAAA,EAEA,UAAU,CAAC,KAAK,KAAK,KAAK,OAAO;AAAA,EAEjC,OAAO,CAAC,OAAO;AAAA,EACf,WAAW;AAAA,EAEX,QAAQ;AAAA,IACP,GAAG,CAAC,QAAQ,KAAK;AAAA,IACjB,GAAG,CAAC,QAAQ,KAAK;AAAA,EAClB;AAAA,EAEA,aAAa;AAAA,IACZ,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,IACH,OAAO,EAAE,KAAK,oBAAoB,OAAO,WAAW;AAAA,EACrD;AACD;AAEA,IAAOC,uBAAQD;;;ACpDf,IAAM,IAAI,CAAC,OAAO,cACjB,KAAK,MAAM,SAAS,YAAY,KAAK,IAAI,IAAI,SAAS,EAAE,IAAI;AAE7D,IAAM,QACL,CAAC,YAAY,MACb,WACC,OAAO,UAAU,WAAW,EAAE,OAAO,SAAS,IAAI;AAEpD,IAAO,gBAAQ;;;ACLf,IAAI,cAAc,cAAM,CAAC;AAKzB,IAAM,MAAM,kBAAU,KAAK;AAC3B,IAAM,MAAM,kBAAU,KAAK;AA2DpB,IAAM,YAAY,CAAAE,OAAK;AAC7B,QAAM,QAAQ,gBAAQA,EAAC;AACvB,MAAI,CAAC,OAAO;AACX,WAAO;AAAA,EACR;AACA,QAAM,MAAM,QAAQ,MAAM,IAAI;AAC9B,MAAI,CAAC,IAAI,aAAa,OAAO,IAAI,cAAc,UAAU;AACxD,QAAI,MAAM,SAAS,IAAI,aAAa,KAAK,MAAM,IAAI,EAAE;AACrD,QAAI,SAAS,QAAQ,CAAC,IAAI,MAAM;AAC/B,UAAI,OAAO,SAAS;AACnB,gBACE,IAAI,MAAM,OACV,MAAM,EAAE,MAAM,SAAY,MAAM,EAAE,IAAI;AAAA,MACzC;AAAA,IACD,CAAC;AACD,QAAI,MAAM,UAAU,UAAa,MAAM,QAAQ,GAAG;AACjD,aAAO,MAAM,MAAM,KAAK;AAAA,IACzB;AACA,WAAO,MAAM;AAAA,EACd;AACA,MAAI,OAAO,IAAI,cAAc,YAAY;AACxC,WAAO,IAAI,UAAU,KAAK;AAAA,EAC3B;AACA,SAAO;AACR;;;AC1FA,IAAM,SAAS,CAACC,KAAI,OAAO,OAAO,gBAAgB,UAAU;AAC3D,MAAI,WAAW,OAAO,QAAQ,IAAI,EAAE,WAAW;AAC/C,MAAI,OAAO,OAAO,kBAAU,IAAI,IAAI;AACpC,SAAO,WAAS;AACf,QAAI,aAAa,KAAK,KAAK;AAC3B,QAAI,CAAC,YAAY;AAChB,aAAO;AAAA,IACR;AACA,QAAI,OAAO,YAAY,QAAQ,WAAW,IAAI,EAAE,UAAU;AAAA,MACzD,CAACC,MAAK,OAAO;AACZ,YAAI,IAAID,IAAG,WAAW,EAAE,GAAG,IAAI,YAAY,IAAI;AAC/C,YAAI,MAAM,UAAa,CAAC,MAAM,CAAC,GAAG;AACjC,UAAAC,KAAI,EAAE,IAAI;AAAA,QACX;AACA,eAAOA;AAAA,MACR;AAAA,MACA,EAAE,MAAM,WAAW,KAAK;AAAA,IACzB;AACA,QAAI,CAAC,eAAe;AACnB,aAAO;AAAA,IACR;AACA,QAAI,OAAO,gBAAQ,KAAK;AACxB,QAAI,QAAQ,KAAK,SAAS,IAAI,MAAM;AACnC,aAAO,kBAAU,KAAK,IAAI,EAAE,GAAG;AAAA,IAChC;AACA,WAAO;AAAA,EACR;AACD;AAEA,IAAM,mBAAmB,CAAC,GAAG,IAAIC,OAAM;AACtC,MAAI,OAAO,SAAS;AACnB,YAAQ,KAAK,MAAMA,GAAE,UAAU,SAAYA,GAAE,QAAQ;AAAA,EACtD;AACA,SAAO;AACR;AAEA,IAAM,iBAAiB,CAAC,GAAG,IAAIA,OAAM;AACpC,MAAI,OAAO,WAAWA,GAAE,UAAU,GAAG;AACpC,YAAQ,KAAK,MAAMA,GAAE,UAAU,SAAYA,GAAE,QAAQ;AAAA,EACtD;AACA,SAAO;AACR;;;AChCA,IAAM,qBAAqB,SAAO;AAEjC,MAAI,IAAI,CAAC,MAAM,QAAW;AACzB,QAAI,CAAC,IAAI;AAAA,EACV;AACA,MAAI,IAAI,IAAI,SAAS,CAAC,MAAM,QAAW;AACtC,QAAI,IAAI,SAAS,CAAC,IAAI;AAAA,EACvB;AAEA,MAAI,IAAI;AACR,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAO,IAAI,IAAI,QAAQ;AAEtB,QAAI,IAAI,CAAC,MAAM,QAAW;AACzB,iBAAW;AACX,iBAAW,IAAI,IAAI,CAAC;AACpB,UAAI;AAGJ,aAAO,IAAI,CAAC,MAAM,OAAW;AAG7B,aAAO,IAAI,CAAC,IAAI,aAAa,IAAI,IAAI;AACrC,aAAO,IAAI,GAAG;AACb,YAAI,CAAC,IAAI,YAAY,IAAI,IAAI,YAAY;AACzC;AAAA,MACD;AAAA,IACD,WAAW,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG;AAE/B,UAAI,CAAC,IAAI,IAAI,IAAI,CAAC;AAAA,IACnB;AACA;AAAA,EACD;AACA,SAAO;AACR;AAEA,IAAO,6BAAQ;;;ACnDf,IAAM,WAAW,CAAC,IAAI,QAAQ,OAC7B,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC;AAElE,IAAO,mBAAQ;;;ACEf,IAAM,OAAO,OAAK,OAAO,MAAM;AAC/B,IAAM,QAAQ,OAAK,KAAK,OAAO,MAAM;AACrC,IAAM,QAAQ,OAAK,OAAO,MAAM;AAEhC,IAAM,iBAAiB,CAACC,SAAQ,OAAO,OAAO,WAAW,WAAW;AACnE,MAAI,MAAM,QAAQ,IAAI;AACtB,MAAI,OAAO,kBAAU,IAAI;AAEzB,MAAI,cAAc,CAAC;AACnB,MAAI,YAAY,CAAC;AACjB,MAAI,MAAM,CAAC;AAEX,EAAAA,QAAO,QAAQ,SAAO;AACrB,QAAI,MAAM,QAAQ,GAAG,GAAG;AACvB,kBAAY,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC;AAC7B,gBAAU,KAAK,IAAI,CAAC,CAAC;AAAA,IACtB,WAAW,MAAM,GAAG,KAAK,KAAK,GAAG,GAAG;AAEnC,UAAI,UAAU,MAAM,IAAI;AAAA,IACzB,OAAO;AACN,kBAAY,KAAK,KAAK,GAAG,CAAC;AAC1B,gBAAU,KAAK,MAAS;AAAA,IACzB;AAAA,EACD,CAAC;AAED,6BAAmB,SAAS;AAI5B,MAAI,QAAQ,IAAI,SAAS,OAAO,CAAC,KAAK,OAAO;AAC5C,QAAI;AACJ,QAAI,MAAM,SAAS,KAAK,MAAM,UAAU,EAAE,CAAC,KAAK,UAAU,EAAE,EAAE,OAAO;AACpE,YAAM,UAAU,EAAE,EAAE;AAAA,IACrB,WAAW,MAAM,IAAI,YAAY,EAAE,CAAC,KAAK,IAAI,YAAY,EAAE,EAAE,OAAO;AACnE,YAAM,IAAI,YAAY,EAAE,EAAE;AAAA,IAC3B,OAAO;AACN,YAAM,OAAK;AAAA,IACZ;AACA,QAAI,EAAE,IAAI,IAAI,YAAY,IAAI,WAAS,MAAM,EAAE,CAAC,CAAC;AACjD,WAAO;AAAA,EACR,GAAG,CAAC,CAAC;AAEL,MAAI,QAAQ;AACX,QAAI,UAAU,YAAY,IAAI,CAAC,OAAO,QAAQ;AAC7C,aAAO,IAAI,SAAS;AAAA,QACnB,CAACC,IAAG,OAAO;AACV,UAAAA,GAAE,EAAE,IAAI,MAAM,EAAE,EAAE,GAAG;AACrB,iBAAOA;AAAA,QACR;AAAA,QACA,EAAE,KAAK;AAAA,MACR;AAAA,IACD,CAAC;AACD,YAAQ,IAAI,SAAS,OAAO,CAAC,KAAK,OAAO;AACxC,UAAI,EAAE,IAAI,QAAQ,IAAI,CAAAA,OAAK;AAC1B,YAAI,IAAI,OAAOA,GAAE,EAAE,GAAG,IAAIA,IAAG,IAAI;AACjC,eAAO,MAAM,CAAC,IAAI,SAAY;AAAA,MAC/B,CAAC;AACD,aAAO;AAAA,IACR,GAAG,CAAC,CAAC;AAAA,EACN;AAEA,MAAI,gBAAgB,IAAI,SAAS,OAAO,CAAC,KAAK,OAAO;AACpD,QAAI;AACJ,QAAI,KAAK,SAAS,GAAG;AACpB,YAAM;AAAA,IACP,WAAW,MAAM,SAAS,KAAK,KAAK,UAAU,EAAE,CAAC,GAAG;AACnD,YAAM,UAAU,EAAE;AAAA,IACnB,WACC,MAAM,SAAS,KACf,MAAM,UAAU,EAAE,CAAC,KACnB,UAAU,EAAE,EAAE,KACb;AACD,YAAM,UAAU,EAAE,EAAE;AAAA,IACrB,WAAW,KAAK,IAAI,YAAY,EAAE,CAAC,GAAG;AACrC,YAAM,IAAI,YAAY,EAAE;AAAA,IACzB,WAAW,MAAM,IAAI,YAAY,EAAE,CAAC,GAAG;AACtC,YAAM,IAAI,YAAY,EAAE,EAAE;AAAA,IAC3B;AAEA,QAAI,EAAE,IAAI,IAAI,MAAM,EAAE,CAAC;AACvB,WAAO;AAAA,EACR,GAAG,CAAC,CAAC;AAEL,MAAI,IAAI,YAAY,SAAS;AAE7B,SAAO,OAAK;AAEX,QAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAE9B,QAAI,KAAK,UAAU,CAAC,GAAG;AACtB,aAAO,YAAY,CAAC;AAAA,IACrB;AAEA,QAAI,IAAI,UAAU,CAAC,GAAG;AACrB,aAAO,YAAY,CAAC;AAAA,IACrB;AAMA,QAAI,MAAM;AACV,WAAO,UAAU,GAAG,IAAI,EAAG;AAC3B,QAAI,QAAQ,UAAU,MAAM,CAAC;AAC7B,QAAI,QAAQ,UAAU,GAAG,IAAI;AAE7B,QAAI,KAAK,IAAI,SAAS;AAGtB,QAAIC,MAAK,IAAI,GAAG,KAAK,IAAI,CAAC;AAC1B,QAAIA,QAAO,QAAW;AACrB,UAAI,MAAMA,GAAE,GAAG;AACd,QAAAA,MAAK,kBAAgBA,MAAK,SAAS,KAAK;AAAA,MACzC;AACA,UAAIA,IAAG,CAAC;AAAA,IACT;AAEA,QAAI,MAAM,MAAM,IAAI,KAAK;AAEzB,WAAO,IAAI,SAAS;AAAA,MACnB,CAAC,KAAK,YAAY;AACjB,YAAI,MAAM,cAAc,OAAO,EAAE,EAAE;AACnC,YAAI,QAAQ,QAAW;AACtB,cAAI,OAAO,IAAI;AAAA,QAChB;AACA,eAAO;AAAA,MACR;AAAA,MACA,EAAE,KAAK;AAAA,IACR;AAAA,EACD;AACD;AAEA,IAAM,cAAc,CAACF,SAAQ,OAAO,OAAO,cAC1C,eAAeA,SAAQ,MAAM,SAAS;AAEvC,IAAM,kBACL,CAAC,QAAQ,YACT,CAACA,SAAQ,OAAO,OAAO,cAAc;AACpC,MAAI,OAAO,UAAU,OAAO,SAAS,IAAI,IAAI;AAC7C,MAAI,KAAK,eAAeA,SAAQ,MAAM,WAAW,MAAM;AACvD,SAAO,OAAO,OAAK,KAAK,GAAG,CAAC,CAAC,IAAI;AAClC;AAED,IAAM,oCAAoC;AAAA,EACzC;AAAA,EACA;AACD;;;ACnJA,IAAMG,OAAM,kBAAU,KAAK;AAC3B,IAAM,YAAY,CAAAC,OAAK;AACtB,QAAM,MAAM;AAAA,IACX,MAAMA,GAAE;AAAA,IACR,GAAG,KAAK,IAAI,GAAG,KAAK,IAAIA,GAAE,MAAM,SAAYA,GAAE,IAAI,GAAG,CAAC,CAAC;AAAA,IACvD,GAAG,KAAK,IAAI,GAAG,KAAK,IAAIA,GAAE,MAAM,SAAYA,GAAE,IAAI,GAAG,CAAC,CAAC;AAAA,IACvD,GAAG,KAAK,IAAI,GAAG,KAAK,IAAIA,GAAE,MAAM,SAAYA,GAAE,IAAI,GAAG,CAAC,CAAC;AAAA,EACxD;AACA,MAAIA,GAAE,UAAU,QAAW;AAC1B,QAAI,QAAQA,GAAE;AAAA,EACf;AACA,SAAO;AACR;AAEA,IAAM,sBAAsB,CAAAA,OAAK,UAAUD,KAAIC,EAAC,CAAC;AAEjD,IAAM,cAAc,CAAAA,OAAK;AACxB,SACCA,OAAM,WACLA,GAAE,MAAM,UAAcA,GAAE,KAAK,KAAKA,GAAE,KAAK,OACzCA,GAAE,MAAM,UAAcA,GAAE,KAAK,KAAKA,GAAE,KAAK,OACzCA,GAAE,MAAM,UAAcA,GAAE,KAAK,KAAKA,GAAE,KAAK;AAE5C;AAKO,SAAS,YAAY,OAAO;AAClC,SAAO,YAAYD,KAAI,KAAK,CAAC;AAC9B;AAuBO,SAAS,SAAS,OAAO;AAC/B,UAAQ,gBAAQ,KAAK;AAGrB,MAAI,UAAU,UAAa,YAAY,KAAK,EAAG,QAAO;AAGtD,MAAI,OAAO,kBAAU,MAAM,IAAI;AAE/B,SAAO,KAAK,oBAAoB,KAAK,CAAC;AACvC;;;AChEA,IAAIE,OAAM,kBAAU,KAAK;;;ACKlB,SAAS,UAAU,OAAO;AAChC,MAAIC,KAAI,kBAAU,MAAM,EAAE,KAAK;AAC/B,SAAO,SAASA,GAAE,IAAI,SAASA,GAAE,IAAI,SAASA,GAAE;AACjD;AAKO,SAAS,SAAS,GAAG,GAAG;AAC9B,MAAI,KAAK,UAAU,CAAC;AACpB,MAAI,KAAK,UAAU,CAAC;AACpB,UAAQ,KAAK,IAAI,IAAI,EAAE,IAAI,SAAS,KAAK,IAAI,IAAI,EAAE,IAAI;AACxD;;;ACoPO,IAAM,MAAM,QAAQC,mBAAO;AAC3B,IAAM,YAAY,QAAQA,mBAAa;AACvC,IAAM,OAAO,QAAQA,mBAAQ;AAC7B,IAAM,OAAO,QAAQA,mBAAQ;AAC7B,IAAM,MAAM,QAAQA,mBAAO;AAC3B,IAAMC,OAAM,QAAQD,mBAAO;AAC3B,IAAM,MAAM,QAAQA,mBAAO;AAC3B,IAAM,MAAM,QAAQA,mBAAO;AAC3B,IAAM,MAAM,QAAQA,oBAAO;AAC3B,IAAM,MAAM,QAAQA,oBAAO;AAC3B,IAAM,MAAM,QAAQA,oBAAO;AAC3B,IAAM,MAAM,QAAQA,oBAAO;AAC3B,IAAM,QAAQ,QAAQA,oBAAS;AAC/B,IAAM,MAAM,QAAQA,oBAAO;AAC3B,IAAM,QAAQ,QAAQA,oBAAS;AAC/B,IAAM,QAAQ,QAAQA,oBAAS;AAC/B,IAAM,OAAO,QAAQA,oBAAQ;AAC7B,IAAM,MAAM,QAAQA,oBAAO;AAC3B,IAAM,QAAQ,QAAQ,iBAAS;AAC/B,IAAM,QAAQ,QAAQ,iBAAS;AAC/B,IAAM,QAAQ,QAAQA,oBAAS;AAC/B,IAAM,QAAQ,QAAQA,oBAAS;AAC/B,IAAM,KAAK,QAAQA,oBAAM;AACzB,IAAM,WAAW,QAAQA,oBAAY;AACrC,IAAM,UAAU,QAAQA,oBAAW;AACnC,IAAME,OAAM,QAAQ,kBAAO;AAC3B,IAAM,MAAM,QAAQF,oBAAO;AAC3B,IAAM,QAAQ,QAAQA,oBAAS;AAC/B,IAAM,QAAQ,QAAQA,oBAAS;AAC/B,IAAM,MAAM,QAAQA,oBAAO;;;ACtSlC;AAAA;AAAA;AAAA,iBAAAG;AAAA,EAAA;AAAA;AAAA;AAAA,iBAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+BAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACOO,SAAS,KAAK,GAAG;AACpB,SAAO,OAAO,KAAK,CAAC;AACxB;AAEO,SAAS,QAAQ,GAAG;AACvB,MAAI,aAAa;AACb,WAAO,MAAM,KAAK,EAAE,QAAQ,CAAC;AACjC,SAAO,OAAO,QAAQ,CAAC;AAC3B;AAEO,SAAS,YAAYC,UAAS;AACjC,SAAO,OAAO,YAAYA,QAAO;AACrC;AAuBO,SAAS,YAAY,GAAG;AAC3B,QAAM,IAAI,MAAM,wBAAwB,CAAC,EAAE;AAC/C;;;AD1CO,SAAS,gBAAgB,KAAK;AACjC,SAAO,OAAO,OAAO,QAAQ,YAAY,IAAI,gBAAgB;AACjE;AACO,SAAS,cAAc,KAAK;AAC/B,SAAO,gBAAgB,GAAG,KAAK,KAAK,GAAG,EAAE,WAAW;AACxD;AACO,SAAS,cAAc,KAAK;AAC/B,SAAO,KAAK,GAAG,EAAE,OAAO,CAAC,KAAK,SAAU,IAAI,kBAAU,MAAM,OAAO,GAAG,IAAI,MAAS,CAAC,IAAI,IAAI,GAAG,GAAI,MAAM,CAAC,CAAC;AAC/G;AAGO,SAAS,sBAAsB,KAAK,WAAW,cAAc;AAChE,QAAM,kBAAkB,aAAa,gBAAgB,UAAU;AAC/D,MAAI,UAAU,GAAG,GAAG;AAChB,WAAO;AAAA,EACX,OACK;AACD,UAAM,WAAW,gBAAgB,GAAG;AACpC,QAAI,UAAU;AACV,eAAS,KAAK,UAAU;AACpB,cAAM,QAAQ,sBAAsB,GAAG,WAAW,YAAY;AAC9D,YAAI,OAAO;AACP,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACO,SAAS,aAAa,MAAM;AAC/B,SAAO,OAAO,SAAS,aACjB,OACA,OAAO,SAAS,WACZ,CAAC,MAAM,YAAI,GAAG,IAAI,IAClB,CAAC,MAAM;AACrB;AAKA,IAAI,WAAW,oBAAI,QAAQ;AAA3B,IAA8B,cAAc;AACrC,SAAS,SAAS,QAAQ;AAC7B,MAAI,CAAC,SAAS,IAAI,MAAM;AACpB,aAAS,IAAI,QAAQ,EAAE,WAAW;AACtC,SAAO,SAAS,IAAI,MAAM;AAC9B;AACO,SAAS,gBAAgB,MAAM;AAClC,SAAO,CAAC,GAAG,IAAI,IAAI,QAAQ,KAAK,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD;AAEA,SAAS,QAAQ,OAAO;AACpB,SAAO,MAAM,OAAO,CAAC,MAAM,SAAS,KAAK,OAAO,IAAI,GAAG,CAAC,CAAC;AAC7D;AAOO,SAAS,MAAM,QAAQ,QAAQ;AAClC,SAAO,kBAAU,QAAQ,QAAQ,CAAC,UAAU,aAAa;AACrD,QAAI,MAAM,QAAQ,QAAQ,GAAG;AAEzB,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AAIO,SAAS,aAAa,QAAQ,QAAQ;AACzC,QAAM,MAAM,oBAAI,KAAK;AACrB,MAAI,kBAAkB,QAAQ,OAAO,WAAW,YAAY,UAAU,MAAM;AAExE,QAAI,SAAS,KAAK;AACd,aAAO;AAAA,IACX;AAEA,WAAO;AAAA,EACX;AAEA,WAAS,CAAC,MAAM,UAAU,KAAK,QAAQ,MAAM,GAAG;AAC5C,QAAI,sBAAsB,MAAM;AAE5B,UAAI,aAAa,KAAK;AAClB,YAAI,SAAS,YAAY;AAErB,mBAAS,WAAW,KAAK,MAAM,GAAG;AAC9B,gBAAI,EAAE,WAAW,SAAS;AACtB,qBAAO,OAAO,OAAO;AAAA,YACzB;AAAA,UACJ;AAGA,iBAAO,OAAO,IAAI;AAAA,QACtB,OACK;AAGD,iBAAO,OAAO,IAAI;AAAA,QACtB;AAAA,MACJ,OACK;AAAA,MAEL;AAAA,IACJ,OACK;AAED,YAAM,QAAQ,OAAO,IAAI;AACzB,UAAI,SAAS,OAAO,UAAU,UAAU;AACpC,qBAAa,OAAO,UAAU;AAE9B,YAAI,cAAc,KAAK,GAAG;AACtB,iBAAO,OAAO,IAAI;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,cAAc,MAAM,IAAI,OAAO;AAC1C;AAIO,SAAS,KAAK,KAAKC,OAAM;AAC5B,MAAIA,MAAK,WAAW,GAAG;AACnB,WAAO;AAAA,EACX,OACK;AACD,WAAO,YAAY,QAAQ,GAAG,EAAE,OAAO,CAAC,CAAC,GAAG,MAAM,CAACA,MAAK,SAAS,GAAG,CAAC,CAAC;AAAA,EAC1E;AACJ;AAIO,SAAS,QAAQ,KAAK;AACzB,MAAI,KAAK,WAAW,GAAG;AACnB,WAAO;AAAA,EACX,OACK;AACD,WAAO,YAAY,QAAQ,GAAG,EAAE,OAAO,CAAC,CAAC,KAAK,KAAK,MAAM,SAAS,IAAI,CAAC;AAAA,EAC3E;AACJ;AAIO,SAAS,KAAK,KAAKA,OAAM;AAC5B,MAAIA,MAAK,WAAW,GAAG;AACnB,WAAO;AAAA,EACX,OACK;AACD,WAAO,YAAYA,MAAK,OAAO,CAAC,QAAQ,OAAO,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC;AAAA,EACrF;AACJ;AAIO,SAAS,aAAa,KAAK;AAC9B,SAAO,YAAY,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,OAAO,KAAK,GAAG,GAAG,CAAC,CAAC;AAC/E;AAIO,SAAS,QAAQ,KAAKC,KAAI;AAC7B,SAAO,YAAY,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAACA,IAAG,GAAG,GAAG,KAAK,CAAC,CAAC;AAC3E;AAIO,SAAS,UAAU,KAAKA,KAAI;AAC/B,SAAO,YAAY,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,KAAKA,IAAG,KAAK,CAAC,CAAC,CAAC;AAC3E;;;ADpKO,SAASC,SAAQ,OAAO;AAC3B,SAAO,MAAM,OAAO,CAAC,MAAM,SAAS,KAAK,OAAO,IAAI,GAAG,CAAC,CAAC;AAC7D;AAIO,SAAS,QAAQ,QAAQ,MAAM;AAClC,MAAI,OAAO,MAAM,CAAC,MAAM,KAAK,IAAI,GAAG;AAChC,WAAO;AAAA,EACX;AACA,SAAO,OAAO,OAAO,IAAI;AAC7B;AAIO,SAAS,IAAI,OAAO,MAAM;AAC7B,QAAM,UAAU,aAAa,IAAI;AACjC,QAAM,SAAS,MAAM,IAAI,CAAC,MAAM,QAAQ,CAAC,CAAC;AAC1C,SAAO,QAAQ,QAAQ,CAAC,OAAO,aAAa,SAAS,MAAM,WAAW,EAAE;AAC5E;AAIO,SAAS,WAAW,OAAO,MAAM;AACpC,QAAM,UAAU,aAAa,IAAI;AACjC,QAAM,SAAS,OAAO,MAAM,QAAQ,CAAC,MAAM,QAAQ,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,IAAI,QAAQ,CAAC,MAAM;AACzF,UAAM,QAAQ,OAAO,QAAQ,EAAE,CAAC,CAAC,CAAC;AAClC,WAAO,OAAO,SAAS,KAAK,IAAI,QAAQ;AAAA,EAC5C,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;AACf,SAAO,MAAM,MAAM,MAAM,OAAO,IAAI,MAAM,KAAK,OAAO,OAAO,CAAC,IAAI,YAAY,MAAM;AACxF;AAIO,SAAS,SAAS,OAAO,MAAM;AAClC,QAAM,UAAU,aAAa,IAAI;AACjC,QAAM,SAAS,MAAM,IAAI,CAAC,MAAM,QAAQ,CAAC,CAAC;AAC1C,SAAO,QAAQ,QAAQ,CAAC,OAAO,aAAa,SAAS,MAAM,WAAW,EAAE;AAC5E;AAIO,SAASC,SAAQ,OAAO,MAAM;AACjC,QAAM,QAAQ,IAAI,OAAO,IAAI;AAC7B,SAAO,UAAU,OAAO,QAAQ,MAAM,SAAS;AACnD;AAMO,SAAS,cAAc,OAAO,YAAY,MAAM;AACnD,QAAM,UAAU,aAAa,IAAI;AACjC,MAAIC,OAAM;AACV,QAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,MAAM;AACjC,UAAM,QAAQ,QAAQ,IAAI;AAC1B,IAAAA,QAAO,SAAS;AAChB,QAAI,KAAK,aAAa,GAAG;AACrB,YAAM,OAAOA,OAAM;AAEnB,YAAM,cAAc,QAAQ,MAAM,IAAI,aAAa,CAAC,CAAC;AACrD,MAAAA,QAAO,eAAe;AACtB,aAAO;AAAA,IACX,OACK;AAED,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAIO,SAAS,OAAO,QAAQ;AAC3B,SAAO,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC;AACrC;AAIO,SAAS,WAAW,SAAS,CAAC,GAAGC,OAAM,GAAG,YAAY,MAAM;AAC/D,QAAM,QAAQ,OAAO;AACrB,MAAI,SAASA,MAAK;AACd,WAAO,OAAO,KAAK,SAAS;AAAA,EAChC,OACK;AACD,QAAIA,SAAQ,GAAG;AACX,UAAI,OAAO,WAAW,GAAG;AACrB,eAAO,OAAO,CAAC;AAAA,MACnB,OACK;AACD,eAAO,IAAI,KAAK;AAAA,MACpB;AAAA,IACJ,OACK;AACD,aAAO,GAAG,OAAO,MAAM,GAAGA,IAAG,EAAE,KAAK,SAAS,CAAC,UAAU,KAAK;AAAA,IACjE;AAAA,EACJ;AACJ;AAIO,SAAS,iBAAiB,KAAK,MAAM,OAAO,QAAQ,GAAG;AAC1D,QAAM,aAAa,cAAa,+BAAO,QAAO,KAAK;AACnD,QAAM,gBAAgB,cAAa,+BAAO,WAAU,QAAQ;AAC5D,QAAM,OAAO,IAAI,KAAK,CAAC,MAAM,WAAW,CAAC,MAAM,KAAK,KAAK,CAAC;AAC1D,MAAI,UAAU,KAAK,SAAS,GAAG;AAC3B,WAAO;AAAA,EACX,OACK;AACD,UAAM,WAAW,cAAc,IAAI;AACnC,QAAI,UAAU;AACV,aAAO,iBAAiB,cAAc,IAAI,GAAG,MAAM,OAAO,QAAQ,CAAC;AAAA,IACvE;AAAA,EACJ;AACJ;AAIO,SAASC,uBAAsB,KAAK,WAAW,cAAc;AAChE,QAAM,kBAAkB,aAAa,gBAAgB,UAAU;AAC/D,MAAI,QAAQ,IAAI,KAAK,SAAS;AAC9B,MAAI,OAAO;AACP,WAAO;AAAA,EACX,OACK;AACD,aAAS,QAAQ,KAAK;AAClB,YAAM,WAAW,gBAAgB,IAAI;AACrC,UAAI,UAAU;AACV,gBAAQA,uBAAsB,gBAAgB,IAAI,GAAG,WAAW,YAAY;AAC5E,YAAI,OAAO;AACP,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAIO,SAAS,UAAU,KAAK;AAC3B,MAAI,SAAS,CAAC,CAAC,CAAC;AAChB,MAAI,QAAQ,CAAC,MAAM;AAtJvB;AAuJQ,WAAO,SAAS,EAAE;AAClB,WAAO,EAAE,QAAQ,CAAC,EAAE,WAAW,OAAO,EAAE,QAAQ,CAAC,EAAE,YAAY,CAAC;AAChE,iBAAO,EAAE,QAAQ,CAAC,EAAE,aAApB,mBAA8B,KAAK;AACnC,WAAO,EAAE,KAAK,IAAI;AAAA,EACtB,CAAC;AACD,SAAO,OAAO,CAAC,EAAE,YAAY,CAAC;AAClC;AAIO,SAAS,KAAK,KAAK,UAAU,UAAU;AAC1C,MAAI,QAAQ,CAAC,SAAS;AAClB,aAAS,IAAI;AACb,QAAI,SAAS,IAAI,GAAG;AAChB,WAAK,SAAS,IAAI,GAAG,UAAU,QAAQ;AAAA,IAC3C;AAAA,EACJ,CAAC;AACL;AAIO,SAAS,YAAY,KAAK,UAAU;AACvC,QAAM,YAAY,CAAC;AACnB,OAAK,KAAK,UAAU,CAAC,SAAS,UAAU,KAAK,IAAI,CAAC;AAClD,SAAO;AACX;AACO,SAAS,MAAM,OAAO,MAAM;AAC/B,SAAO,MAAM,OAAO,CAAC,KAAK,MAAM,UAAU;AACtC,UAAM,SAAS,KAAK,MAAM,QAAQ,IAAI;AACtC,QAAI,CAAC,IAAI,MAAM,GAAG;AACd,UAAI,MAAM,IAAI,CAAC;AAAA,IACnB;AACA,QAAI,MAAM,EAAE,KAAK,IAAI;AACrB,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AAMO,SAAS,QAAQ,OAAO,MAAM;AACjC,MAAI,GAAG,OAAO,KAAK,MAAM,IAAI,KAAK;AAC9B,WAAO,CAAC;AACZ,QAAM,IAAI,MAAM;AAChB,MAAI,EAAE,IAAI;AACN,WAAO,CAAC,GAAG,KAAK;AACpB,MAAI,SAAS;AACT,WAAO,CAAC,MAAM,KAAK,CAAC,CAAC;AACzB,SAAO,MAAM,KAAK,EAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,KAAK,MAAO,KAAK,OAAO,MAAO,IAAI,EAAE,CAAC,CAAC;AAC/F;AAKO,SAAS,QAAQ,OAAO,MAAM,OAAO;AACxC,QAAM,OAAO,OAAO,GAAG,IAAI;AAC3B,SAAO;AACX;AAKO,SAAS,SAAS,OAAO,MAAM,IAAI;AACtC,MAAI,OAAO,MAAM,IAAI;AACrB,QAAM,OAAO,MAAM,CAAC;AACpB,QAAM,OAAO,IAAI,GAAG,IAAI;AACxB,SAAO;AACX;AAKO,SAAS,WAAW,OAAO,OAAO;AACrC,QAAM,OAAO,OAAO,CAAC;AACrB,SAAO;AACX;AAIO,SAAS,YAAY,OAAO;AAC/B,SAAO,SAAS,OAAO,CAAC,GAAG,MAAM,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AAC9D;;;AGzOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACCO,SAAS,SAAS,QAAQ,KAAK;AAClC,MAAI,QAAQ;AACR,WAAO,OAAO;AAAA,EAClB,OACK;AACD,WAAO;AAAA,EACX;AACJ;;;ACRO,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,EAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACvC,EAAAA,YAAWA,YAAW,KAAK,IAAI,EAAE,IAAI;AACrC,EAAAA,YAAWA,YAAW,SAAS,IAAI,EAAE,IAAI;AACzC,EAAAA,YAAWA,YAAW,UAAU,IAAI,EAAE,IAAI;AAC1C,EAAAA,YAAWA,YAAW,MAAM,IAAI,EAAE,IAAI;AACtC,EAAAA,YAAWA,YAAW,SAAS,IAAI,EAAE,IAAI;AACzC,EAAAA,YAAWA,YAAW,SAAS,IAAI,EAAE,IAAI;AACzC,EAAAA,YAAWA,YAAW,SAAS,IAAI,EAAE,IAAI;AACzC,EAAAA,YAAWA,YAAW,SAAS,IAAI,EAAE,IAAI;AACzC,EAAAA,YAAWA,YAAW,SAAS,IAAI,EAAE,IAAI;AACzC,EAAAA,YAAWA,YAAW,SAAS,IAAI,EAAE,IAAI;AACzC,EAAAA,YAAWA,YAAW,SAAS,IAAI,EAAE,IAAI;AACzC,EAAAA,YAAWA,YAAW,OAAO,IAAI,EAAE,IAAI;AACvC,EAAAA,YAAWA,YAAW,WAAW,IAAI,EAAE,IAAI;AAC3C,EAAAA,YAAWA,YAAW,SAAS,IAAI,EAAE,IAAI;AACzC,EAAAA,YAAWA,YAAW,cAAc,IAAI,EAAE,IAAI;AAC9C,EAAAA,YAAWA,YAAW,mBAAmB,IAAI,EAAE,IAAI;AACnD,EAAAA,YAAWA,YAAW,SAAS,IAAI,EAAE,IAAI;AACzC,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,SAAS,IAAI,EAAE,IAAI;AACzC,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAC5C,EAAAA,YAAWA,YAAW,YAAY,IAAI,EAAE,IAAI;AAChD,GAAG,eAAe,aAAa,CAAC,EAAE;AAC3B,IAAI;AAAA,CACV,SAAUC,YAAW;AAClB,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACrC,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACrC,EAAAA,WAAUA,WAAU,SAAS,IAAI,CAAC,IAAI;AACtC,EAAAA,WAAUA,WAAU,WAAW,IAAI,CAAC,IAAI;AACxC,EAAAA,WAAUA,WAAU,UAAU,IAAI,CAAC,IAAI;AACvC,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACrC,EAAAA,WAAUA,WAAU,UAAU,IAAI,CAAC,IAAI;AAC3C,GAAG,cAAc,YAAY,CAAC,EAAE;AACzB,IAAI;AAAA,CACV,SAAUC,YAAW;AAElB,EAAAA,WAAU,cAAc,IAAI;AAE5B,EAAAA,WAAU,aAAa,IAAI;AAE3B,EAAAA,WAAU,YAAY,IAAI;AAE1B,EAAAA,WAAU,aAAa,IAAI;AAE3B,EAAAA,WAAU,cAAc,IAAI;AAE5B,EAAAA,WAAU,eAAe,IAAI;AAE7B,EAAAA,WAAU,cAAc,IAAI;AAE5B,EAAAA,WAAU,aAAa,IAAI;AAE3B,EAAAA,WAAU,YAAY,IAAI;AAE1B,EAAAA,WAAU,aAAa,IAAI;AAE3B,EAAAA,WAAU,gBAAgB,IAAI;AAE9B,EAAAA,WAAU,eAAe,IAAI;AAE7B,EAAAA,WAAU,gBAAgB,IAAI;AAE9B,EAAAA,WAAU,eAAe,IAAI;AAE7B,EAAAA,WAAU,cAAc,IAAI;AAE5B,EAAAA,WAAU,oBAAoB,IAAI;AAElC,EAAAA,WAAU,mBAAmB,IAAI;AAEjC,EAAAA,WAAU,wBAAwB,IAAI;AAEtC,EAAAA,WAAU,kBAAkB,IAAI;AAEhC,EAAAA,WAAU,gBAAgB,IAAI;AAE9B,EAAAA,WAAU,iBAAiB,IAAI;AACnC,GAAG,cAAc,YAAY,CAAC,EAAE;;;ACzFzB,SAAS,wBAAwB,SAAS;AADjD;AAEI,MAAI,CAAC,SAAS;AACV,WAAO,UAAU;AAAA,EACrB;AACA,QAAM,SAAS,IAAI,KAAK,OAAO,OAAO;AAEtC,QAAM,WAAW,OAAO,cAAY,YAAO,gBAAP;AACpC,WAAQ,qCAAU,aAAY,KAAK;AACvC;;;ACoBA,IAAM,wBAAwB;AAAA,EAC1B,QAAQ;AAAA,EACR,YAAY;AAAA,IACR,IAAI;AAAA,IACJ,QAAQ;AAAA,IACR,MAAM;AAAA,MACF,OAAO;AAAA,MACP,KAAK;AAAA,MACL,OAAO;AAAA,MACP,KAAK;AAAA,MACL,SAAS;AAAA,MACT,MAAM;AAAA,MACN,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS;AAAA,MACT,cAAc;AAAA,MACd,eAAe;AAAA,MACf,WAAW;AAAA,QACP,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAAA,MACA,YAAY;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAAA,MACA,cAAc;AAAA,QACV,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAAA,MACA,aAAa;AAAA,QACT,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAAA,MACA,eAAe;AAAA,QACX,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAAA,MACA,2BAA2B;AAAA,MAC3B,YAAY;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA,QACd,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL,SAAS;AAAA,MACL,UAAU;AAAA,QACN,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,MACrB;AAAA,IACJ;AAAA,IACA,OAAO;AAAA,MACH,aAAa;AAAA,MACb,cAAc,UAAU;AAAA,MACxB,iBAAiB;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,OAAO;AAAA,MACX;AAAA,MACA,SAAS;AAAA,QACL,KAAK;AAAA,UACD,OAAO,CAAC,UAAU,oBAAoB,UAAU,aAAa;AAAA,UAC7D,SAAS,CAAC,UAAU,oBAAoB,UAAU,eAAe,UAAU,YAAY;AAAA,UACvF,MAAM,CAAC,UAAU,oBAAoB,UAAU,aAAa,UAAU,YAAY;AAAA,QACtF;AAAA,QACA,SAAS;AAAA,UACL,OAAO;AAAA,YACH,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,UACd;AAAA,UACA,SAAS;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,UACd;AAAA,UACA,MAAM;AAAA,YACF,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,UACd;AAAA,QACJ;AAAA,QACA,UAAU;AAAA,UACN,OAAO,CAAC,UAAU,cAAc,UAAU,cAAc;AAAA,UACxD,SAAS,CAAC,UAAU,aAAa,UAAU,eAAe,UAAU,aAAa;AAAA,UACjF,MAAM;AAAA,YACF,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,UACd;AAAA,QACJ;AAAA,QACA,MAAM;AAAA,UACF,OAAO,CAAC,UAAU,oBAAoB,UAAU,aAAa;AAAA,UAC7D,SAAS,CAAC,UAAU,oBAAoB,UAAU,eAAe,UAAU,YAAY;AAAA,UACvF,MAAM,CAAC,UAAU,oBAAoB,UAAU,eAAe,UAAU,YAAY;AAAA,QACxF;AAAA,QACA,OAAO;AAAA,UACH,OAAO,UAAU;AAAA,UACjB,SAAS,UAAU;AAAA,UACnB,MAAM,UAAU;AAAA,QACpB;AAAA,QACA,YAAY;AAAA,UACR,OAAO,CAAC,UAAU,aAAa,UAAU,WAAW;AAAA,UACpD,SAAS,CAAC,UAAU,YAAY,UAAU,YAAY;AAAA,UACtD,MAAM,CAAC,UAAU,YAAY,UAAU,YAAY;AAAA,QACvD;AAAA,QACA,MAAM;AAAA,UACF,OAAO,UAAU;AAAA,UACjB,SAAS,UAAU;AAAA,UACnB,MAAM,UAAU;AAAA,QACpB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAGO,SAAS,qBAAqB,gBAAgB,OAAO,uBAAuB;AAxKnF;AA0KI,OAAI,0BAAe,YAAf,mBAAwB,UAAxB,mBAA+B,iBAAiB;AAChD,mBAAe,QAAQ,MAAM,kBAAkB;AAAA,MAC3C,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,GAAG,eAAe,QAAQ,MAAM;AAAA,IACpC;AAAA,EACJ;AAEA,QAAI,0BAAe,YAAf,mBAAwB,UAAxB,mBAA+B,kBAAiB,QAAW;AAC3D,qBAAiB,qBAAa,gBAAgB;AAAA,MAC1C,SAAS,EAAE,OAAO,EAAE,cAAc,wBAAwB,eAAe,MAAM,EAAE,EAAE;AAAA,IACvF,CAAC;AAAA,EACL;AACA,SAAO,qBAAa,gBAAgB,IAAI;AAC5C;AACO,IAAM,gBAAgB,qBAAqB,EAAE,QAAQ,KAAK,CAAC;;;AJtL3D,SAAS,iBAAiB,cAAc,SAAS;AAGpD,QAAM,OAAO,IAAI,KAAK,MAAM,GAAG,IAAI,YAAY;AAC/C,QAAM,YAAY,IAAI,KAAK,eAAe,SAAS,EAAE,SAAS,QAAQ,CAAC;AACvE,SAAO,UAAU,OAAO,IAAI;AAChC;AACO,SAAS,kBAAkB,YAAY;AAC1C,SAAO,4BAA4B,eAAe,UAAU;AAChE;AACO,SAAS,4BAA4B,UAAU,YAAY;AAC9D,QAAM,EAAE,QAAgB,YAAY,EAAE,MAAM,KAAK,EAAG,IAAI;AACxD,UAAQ,YAAY;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO;AAAA,IACX,KAAK,WAAW;AACZ,aAAO,KAAK;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO,KAAK;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO,KAAK;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,IAAI,KAAK,iBAAiB,UAAU,QAAQ,MAAM,CAAC;AAAA,IACtE,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,IAAI,KAAK,iBAAiB,GAAG,MAAM,CAAC;AAAA,IACvD,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,IAAI,KAAK,iBAAiB,GAAG,MAAM,CAAC;AAAA,IACvD,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,IAAI,KAAK,iBAAiB,GAAG,MAAM,CAAC;AAAA,IACvD,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,IAAI,KAAK,iBAAiB,GAAG,MAAM,CAAC;AAAA,IACvD,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,IAAI,KAAK,iBAAiB,GAAG,MAAM,CAAC;AAAA,IACvD,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,IAAI,KAAK,iBAAiB,GAAG,MAAM,CAAC;AAAA,IACvD,KAAK,WAAW;AACZ,aAAO,KAAK;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO,KAAK;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO,KAAK;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO,KAAK;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO,KAAK;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO,KAAK;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,KAAK,iBAAiB,GAAG,MAAM,CAAC;AAAA,IACzD,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,KAAK,iBAAiB,GAAG,MAAM,CAAC;AAAA,IACzD,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,KAAK,iBAAiB,GAAG,MAAM,CAAC;AAAA,IACzD,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,KAAK,iBAAiB,GAAG,MAAM,CAAC;AAAA,IACzD,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,KAAK,iBAAiB,GAAG,MAAM,CAAC;AAAA,IACzD,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,KAAK,iBAAiB,GAAG,MAAM,CAAC;AAAA,IACzD,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,KAAK,iBAAiB,GAAG,MAAM,CAAC;AAAA,IACzD,KAAK,WAAW;AACZ,aAAO,KAAK;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,OAAO,iBAAiB,GAAG,MAAM,CAAC;AAAA,IAC3D,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,OAAO,iBAAiB,GAAG,MAAM,CAAC;AAAA,IAC3D,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,OAAO,iBAAiB,GAAG,MAAM,CAAC;AAAA,IAC3D,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,OAAO,iBAAiB,GAAG,MAAM,CAAC;AAAA,IAC3D,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,OAAO,iBAAiB,GAAG,MAAM,CAAC;AAAA,IAC3D,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,OAAO,iBAAiB,GAAG,MAAM,CAAC;AAAA,IAC3D,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM,OAAO,iBAAiB,GAAG,MAAM,CAAC;AAAA,IAC3D,KAAK,WAAW;AACZ,aAAO,GAAG,KAAK,MAAM;AAAA,IACzB;AACI,kBAAY,UAAU;AAAA,EAC9B;AACJ;AACA,IAAM,qBAAqB;AAAA,EACvB,CAAC,WAAW,MAAM,GAAG;AAAA,EACrB,CAAC,WAAW,GAAG,GAAG;AAAA,EAClB,CAAC,WAAW,OAAO,GAAG;AAAA,EACtB,CAAC,WAAW,QAAQ,GAAG;AAAA,EACvB,CAAC,WAAW,OAAO,GAAG;AAAA,EACtB,CAAC,WAAW,OAAO,GAAG;AAAA,EACtB,CAAC,WAAW,OAAO,GAAG;AAAA,EACtB,CAAC,WAAW,OAAO,GAAG;AAAA,EACtB,CAAC,WAAW,OAAO,GAAG;AAAA,EACtB,CAAC,WAAW,OAAO,GAAG;AAAA,EACtB,CAAC,WAAW,OAAO,GAAG;AAAA,EACtB,CAAC,WAAW,IAAI,GAAG;AAAA,EACnB,CAAC,WAAW,KAAK,GAAG;AAAA,EACpB,CAAC,WAAW,SAAS,GAAG;AAAA,EACxB,CAAC,WAAW,OAAO,GAAG;AAAA,EACtB,CAAC,WAAW,YAAY,GAAG;AAAA,EAC3B,CAAC,WAAW,iBAAiB,GAAG;AAAA,EAChC,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,OAAO,GAAG;AAAA,EACtB,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,UAAU,GAAG;AAAA,EACzB,CAAC,WAAW,OAAO,GAAG;AAC1B;AACO,SAAS,kBAAkB,YAAY;AAC1C,SAAO,mBAAmB,UAAU;AACxC;AACO,SAAS,oBAAoB,MAAM;AACtC,QAAM,UAAU,QAAQ,kBAAkB,EAAE,KAAK,CAACC,OAAMA,GAAE,CAAC,MAAM,IAAI;AACrE,SAAO,SAAS,QAAO,mCAAU,OAAM,GAAG,CAAC;AAC/C;AACO,SAAS,aAAa,YAAY;AACrC,MAAK,cAAc,WAAW,WAAW,cAAc,WAAW,WAC7D,cAAc,WAAW,cAAc,cAAc,WAAW,cAChE,cAAc,WAAW,cAAc,cAAc,WAAW,YAAa;AAC9E,WAAQ,aAAa,KAAM;AAAA,EAC/B,OACK;AACD,WAAO;AAAA,EACX;AACJ;AAEO,SAAS,iBAAiB,YAAY,WAAW;AACpD,MAAI,aAAa,UAAU,GAAG;AAC1B,WAAO,cAAc,aAAa,UAAU,KAAK,KAAK;AAAA,EAC1D,WACS,iBAAiB,UAAU,GAAG;AACnC,WAAO,aAAa,YAAY;AAAA,EACpC,OACK;AACD,WAAO;AAAA,EACX;AACJ;AAEO,SAAS,aAAa,YAAY;AACrC,MAAI,cAAc,WAAW,WAAW,cAAc,WAAW,SAAS;AACtE,WAAO;AAAA,EACX;AACA,MAAI,cAAc,WAAW,cAAc,cAAc,WAAW,YAAY;AAC5E,WAAO;AAAA,EACX;AACA,MAAI,cAAc,WAAW,cAAc,cAAc,WAAW,YAAY;AAC5E,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAEO,SAAS,iBAAiB,YAAY;AACzC,SAAO,CAAC,WAAW,MAAM,WAAW,SAAS,WAAW,OAAO,EAAE,SAAS,UAAU;AACxF;AACO,SAAS,UAAU,QAAO,oBAAI,KAAK,GAAE,YAAY,GAAG;AACvD,SAAO,MAAM,KAAK,EAAE,QAAQ,GAAG,GAAG,CAAC,GAAG,MAAM,IAAI,KAAK,MAAM,GAAG,CAAC,CAAC;AACpE;AACO,SAAS,mBAAmB,gBAAgB,eAAe,UAAU,QAAQ;AAChF,QAAM,mBAAmB,YAAY,aAAa,cAAc,GAAG,EAAE,aAAa,CAAC;AACnF,QAAM,gBAAgB,UAAU,WAAW,cAAc,GAAG,EAAE,aAAa,CAAC;AAC5E,QAAM,OAAO,CAAC;AACd,MAAI,aAAa;AACjB,SAAO,cAAc,eAAe;AAChC,SAAK,KAAK,UAAU;AACpB,iBAAa,QAAQ,YAAY,CAAC;AAAA,EACtC;AACA,SAAO,MAAM,MAAM,CAAC;AACxB;AACO,SAAS,mBAAmB,MAAM;AACrC,MAAI,gBAAgB,MAAM;AACtB,WAAO;AAAA,EACX,WACS,gBAAgB,OAAO;AAC5B,WAAO,IAAI,IAAI;AAAA,EACnB,WACS,SAAS,MAAM,MAAM,GAAG;AAC7B,WAAO,KAAK;AAAA,EAChB,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACO,SAAS,mBAAmB,MAAM;AACrC,MAAI,gBAAgB,MAAM;AACtB,WAAO;AAAA,EACX,WACS,gBAAgB,OAAO;AAC5B,WAAO,IAAI,IAAI;AAAA,EACnB,WACS,SAAS,MAAM,IAAI,GAAG;AAC3B,WAAO,KAAK;AAAA,EAChB,OACK;AACD,WAAO;AAAA,EACX;AACJ;AAIO,SAAS,cAAc,OAAO,oBAAI,KAAK,GAAG,SAAS;AACtD,MAAI,SAAS,MAAM;AAEf,WAAO;AAAA,EACX;AACA,QAAM,aAAc,WAAW,QAAQ,cAAe;AACtD,SAAO,KAAK,SAAS,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,YAAY;AACzF;AACO,SAAS,mBAAmB,OAAO,oBAAI,KAAK,GAAG,SAAS;AAC3D,QAAM,aAAa,cAAc,MAAM,OAAO;AAC9C,QAAM,aAAc,WAAW,QAAQ,cAAe;AACtD,QAAM,iBAAkB,WAAW,QAAQ,kBAAmB;AAC9D,QAAM,YAAY,IAAI,MAAM,cAAc,KAAK,GAAG,aAAa,GAAG,CAAC;AACnE,QAAM,UAAU,WAAW,UAAU,WAAW,iBAAiB,CAAC,CAAC;AACnE,SAAO,EAAE,WAAW,QAAQ;AAChC;AACO,SAAS,kBAAkB,MAAM,SAAS;AAC7C,SAAO,mBAAmB,MAAM,OAAO,EAAE;AAC7C;AACO,SAAS,gBAAgB,MAAM,SAAS;AAC3C,SAAO,mBAAmB,MAAM,OAAO,EAAE;AAC7C;AACO,SAAS,iBAAiB,UAAU,WAAW;AAClD,SAAO,cAAc,QAAQ,MAAM,cAAc,SAAS;AAC9D;AAIA,IAAM,kBAAkB,CAAC,oBAAI,KAAK,kBAAkB,GAAG,oBAAI,KAAK,kBAAkB,CAAC;AAC5E,SAAS,cAAc,MAAM,MAAMC,cAAa;AACnD,MAAI,eAAe,gBAAgB,OAAO,CAAC;AAC3C,MAAI,WAAW,QAAQ,cAAcA,YAAW;AAChD,MAAI,eAAe,KAAK,MAAM,iBAAiB,MAAM,QAAQ,IAAI,EAAE;AACnE,SAAO,QAAQ,UAAU,eAAe,EAAE;AAC9C;AACO,SAAS,YAAY,MAAM,MAAMA,cAAa;AACjD,SAAO,QAAQ,cAAc,MAAM,MAAMA,YAAW,GAAG,EAAE;AAC7D;AACO,SAAS,yBAAyB,UAAU,YAAY;AAC3D,MAAI,UAAU;AACV,iBAAa,iCAAiC,SAAS,QAAQ,MAAM,cAAc,UAAU;AAAA,EACjG;AACA,UAAQ,YAAY;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO;AAAA,QACH,OAAO;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ;AAAA,MACZ;AAAA,IACJ,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO;AAAA,QACH,OAAO;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ;AAAA,MACZ;AAAA,IACJ,KAAK,WAAW;AACZ,aAAO;AAAA,QACH,OAAO,CAAC,SAAS,YAAY,MAAM,EAAE,cAAc,EAAE,CAAC;AAAA,QACtD,KAAK,CAAC,SAAS,UAAU,MAAM,EAAE,cAAc,EAAE,CAAC;AAAA,QAClD,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ,CAAC,UAAU,cAAc,WAAW,UAAU,WAAW,EAAE,cAAc,EAAE,CAAC;AAAA,MACxF;AAAA,IACJ,KAAK,WAAW;AACZ,aAAO;AAAA,QACH,OAAO,CAAC,SAAS,YAAY,MAAM,EAAE,cAAc,EAAE,CAAC;AAAA,QACtD,KAAK,CAAC,SAAS,UAAU,MAAM,EAAE,cAAc,EAAE,CAAC;AAAA,QAClD,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ,CAAC,UAAU,cAAc,WAAW,UAAU,WAAW,EAAE,cAAc,EAAE,CAAC;AAAA,MACxF;AAAA,IACJ,KAAK,WAAW;AACZ,aAAO;AAAA,QACH,OAAO,CAAC,SAAS,YAAY,MAAM,EAAE,cAAc,EAAE,CAAC;AAAA,QACtD,KAAK,CAAC,SAAS,UAAU,MAAM,EAAE,cAAc,EAAE,CAAC;AAAA,QAClD,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ,CAAC,UAAU,cAAc,WAAW,UAAU,WAAW,EAAE,cAAc,EAAE,CAAC;AAAA,MACxF;AAAA,IACJ,KAAK,WAAW;AACZ,aAAO;AAAA,QACH,OAAO,CAAC,SAAS,YAAY,MAAM,EAAE,cAAc,EAAE,CAAC;AAAA,QACtD,KAAK,CAAC,SAAS,UAAU,MAAM,EAAE,cAAc,EAAE,CAAC;AAAA,QAClD,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ,CAAC,UAAU,cAAc,WAAW,UAAU,WAAW,EAAE,cAAc,EAAE,CAAC;AAAA,MACxF;AAAA,IACJ,KAAK,WAAW;AACZ,aAAO;AAAA,QACH,OAAO,CAAC,SAAS,YAAY,MAAM,EAAE,cAAc,EAAE,CAAC;AAAA,QACtD,KAAK,CAAC,SAAS,UAAU,MAAM,EAAE,cAAc,EAAE,CAAC;AAAA,QAClD,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ,CAAC,UAAU,cAAc,WAAW,UAAU,WAAW,EAAE,cAAc,EAAE,CAAC;AAAA,MACxF;AAAA,IACJ,KAAK,WAAW;AACZ,aAAO;AAAA,QACH,OAAO,CAAC,SAAS,YAAY,MAAM,EAAE,cAAc,EAAE,CAAC;AAAA,QACtD,KAAK,CAAC,SAAS,UAAU,MAAM,EAAE,cAAc,EAAE,CAAC;AAAA,QAClD,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ,CAAC,UAAU,cAAc,WAAW,UAAU,WAAW,EAAE,cAAc,EAAE,CAAC;AAAA,MACxF;AAAA,IACJ,KAAK,WAAW;AACZ,aAAO;AAAA,QACH,OAAO;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ;AAAA,MACZ;AAAA,IACJ,KAAK,WAAW;AACZ,aAAO;AAAA,QACH,OAAO;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ;AAAA,MACZ;AAAA,IACJ,KAAK,WAAW;AACZ,aAAO;AAAA,QACH,OAAO;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ;AAAA,MACZ;AAAA,IACJ,KAAK,WAAW;AACZ,aAAO;AAAA,QACH,OAAO;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ;AAAA,MACZ;AAAA,IAEJ,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAEhB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW,YAAY;AACxB,YAAM,OAAO,kBAAkB,UAAU,EAAE,WAAW,SAAS,IAAI,IAAI;AACvE,YAAM,YAAY,aAAa,UAAU;AACzC,aAAO;AAAA,QACH,OAAO,CAAC,SAAS,cAAc,MAAM,MAAM,SAAS;AAAA,QACpD,KAAK,CAAC,SAAS,YAAY,MAAM,MAAM,SAAS;AAAA,QAChD,KAAK,CAAC,MAAM,WAAW,SAAS,MAAM,SAAS,CAAC;AAAA,QAChD,YAAY,CAAC,UAAU,cAAc;AACjC,iBAAO,kBAAkB,UAAU,SAAS,IAAI;AAAA,QACpD;AAAA,QACA,QAAQ,CAAC,UAAU,cAAc;AAC7B,iBAAO,UAAU,cAAc,UAAU,MAAM,SAAS,GAAG,cAAc,WAAW,MAAM,SAAS,CAAC;AAAA,QACxG;AAAA,MACJ;AAAA,IACJ;AAAA,IAEA,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK;AAAA,IACL,KAAK;AAED,aAAO;AAAA,QACH,OAAO;AAAA,QACP,KAAK;AAAA,QACL,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,QAAQ;AAAA,MACZ;AAAA,IACJ;AACI,kBAAY,UAAU;AAAA,EAC9B;AACJ;AACO,SAAS,cAAc,MAAM,iBAAiB,YAAY;AAC7D,MAAI,QAAQ,MAAM;AACd,WAAO;AAAA,EACX;AACA,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO,SAAS,IAAI;AAAA,EACxB;AACA,SAAO,UAAU,MAAM,EAAE,eAAe,CAAC;AAC7C;AACO,SAAS,WAAW,UAAU,IAAI,uBAAuB;AAC5D,QAAM,EAAE,QAAQ,SAAS,EAAE,OAAO,EAAE,iBAAiB,SAAS,EAAG,EAAG,IAAI;AACxE,WAAS,kBAAkBC,YAAW,eAAe,OAAO;AACxD,QAAI,cAAc;AACd,YAAM,QAAQ,IAAI,KAAK,YAAY,QAAQ,EAAE,MAAM,UAAU,CAAC;AAC9D,YAAM,UAAUA,WAAU,cAAc,EAAE;AAC1C,aAAO,QACF,IAAI,CAACF,OAAM;AACZ,YAAIA,GAAE,SAAS,OAAO;AAClB,gBAAM,UAAU,MAAM,OAAO,SAASA,GAAE,OAAO,EAAE,CAAC;AAClD,gBAAM,SAAS,SAAS,OAAO;AAC/B,iBAAO,GAAGA,GAAE,KAAK,GAAG,MAAM;AAAA,QAC9B;AACA,eAAOA,GAAE;AAAA,MACb,CAAC,EACI,KAAK,EAAE;AAAA,IAChB;AACA,WAAOE,WAAU,OAAO,EAAE;AAAA,EAC9B;AACA,MAAI,OAAO,0BAA0B,YAAY,CAAC,MAAM,QAAQ,qBAAqB,GAAG;AACpF,WAAO,kBAAkB,IAAI,KAAK,eAAe,QAAQ,qBAAqB,GAAG,sBAAsB,WAAW;AAAA,EACtH;AACA,QAAM,SAAS,MAAM,QAAQ,qBAAqB,IAC5C,sBAAsB,KAAK,EAAE,IAC7B;AAEN,QAAM,YAAY,IAAI,KAAK,eAAe,QAAQ;AAAA,IAC9C,MAAM,OAAO,SAAS,UAAU,YAAY,IACtC,YACA,OAAO,SAAS,UAAU,WAAW,IACjC,YACA;AAAA,IACV,OAAO,OAAO,SAAS,UAAU,UAAU,IACrC,SACA,OAAO,SAAS,UAAU,WAAW,IACjC,UACA,OAAO,SAAS,UAAU,YAAY,IAClC,YACA,OAAO,SAAS,UAAU,aAAa,IACnC,YACA;AAAA,IAClB,KAAK,OAAO,SAAS,UAAU,iBAAiB,IAC1C,YACA,OAAO,SAAS,UAAU,kBAAkB,IACxC,YACA;AAAA,IACV,MAAM,OAAO,SAAS,UAAU,WAAW,IACrC,YACA,OAAO,SAAS,UAAU,YAAY,IAClC,YACA;AAAA,IACV,QAAQ,OAAO,SAAS,UAAU,WAAW,IACvC,QACA,OAAO,SAAS,UAAU,UAAU,IAChC,OACA;AAAA,IACV,QAAQ,OAAO,SAAS,UAAU,aAAa,IACzC,YACA,OAAO,SAAS,UAAU,cAAc,IACpC,YACA;AAAA,IACV,QAAQ,OAAO,SAAS,UAAU,aAAa,IACzC,YACA,OAAO,SAAS,UAAU,cAAc,IACpC,YACA;AAAA,IACV,wBAAwB,OAAO,SAAS,UAAU,YAAY,IAAI,IAAI;AAAA,IACtE,SAAS,OAAO,SAAS,UAAU,gBAAgB,IAC7C,WACA,OAAO,SAAS,UAAU,cAAc,IACpC,SACA,OAAO,SAAS,UAAU,eAAe,IACrC,UACA;AAAA,EAClB,CAAC;AACD,SAAO,kBAAkB,WAAW,OAAO,SAAS,UAAU,sBAAsB,CAAC;AACzF;AACA,SAASC,OAAM,UAAU,MAAM,cAAc,aAAa,SAAS,QACjE;AACE,QAAM,QAAQ,WAAW,SACnB,YAAY,MAAM,EAAE,aAAa,CAAC,IAClC,cAAc,MAAM,QAAQ,YAAY;AAC9C,QAAM,MAAM,WAAW,SACjB,UAAU,MAAM,EAAE,aAAa,CAAC,IAChC,YAAY,MAAM,QAAQ,YAAY;AAC5C,SAAO,WAAW,UAAU,OAAO,WAAW,IAAI,QAAQ,WAAW,UAAU,KAAK,WAAW;AACnG;AACO,SAAS,WAAW,MAAM,YAAY,UAAU,CAAC,GAAG;AACvD,SAAO,qBAAqB,eAAe,MAAM,YAAY,OAAO;AACxE;AACO,SAAS,iCAAiC,cAAc,YAAY;AACvE,MAAI,eAAe,WAAW,MAAM;AAChC,iBAAa;AAAA,MACT,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,IACf,EAAE,YAAY;AAAA,EAClB,WACS,eAAe,WAAW,SAAS;AACxC,iBAAa;AAAA,MACT,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,IACf,EAAE,YAAY;AAAA,EAClB,WACS,eAAe,WAAW,SAAS;AACxC,iBAAa;AAAA,MACT,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,MACX,WAAW;AAAA,IACf,EAAE,YAAY;AAAA,EAClB;AACA,SAAO;AACX;AACO,SAAS,qBAAqB,UAAU,MAAM,YAAY,UAAU,CAAC,GAAG;AAC3E,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO,SAAS,IAAI;AAAA,EACxB;AAGA,MAAI,QAAQ,QAAQ,MAAM,IAAI,GAAG;AAC7B,WAAO;AAAA,EACX;AACA,QAAM,eAAe,QAAQ,gBAAgB,SAAS,QAAQ,MAAM;AACpE,QAAM,EAAE,KAAK,SAAS,UAAU,MAAM,OAAO,YAAY,KAAK,IAAI,SAAS,QAAQ,MAAM;AACzF,eAAa,iCAAiC,cAAc,UAAU,KAAK;AAE3E,WAAS,GAAG,QAAQ;AAChB,QAAI,QAAQ,YAAY,UAAU;AAC9B,aAAO,QAAQ,UAAU,OAAO;AAAA,IACpC,WACS,QAAQ,UAAU,CAAC,QAAQ,SAAS;AACzC,aAAO,QAAQ;AAAA,IACnB;AACA,WAAO,OAAO,QAAQ,WAAW,SAAS;AAAA,EAC9C;AACA,UAAQ,YAAY;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO,WAAW,UAAU,MAAM,QAAQ,MAAM;AAAA,IACpD,KAAK,WAAW;AACZ,aAAO,WAAW,UAAU,MAAM,GAAG,GAAG,CAAC;AAAA,IAC7C,KAAK,WAAW;AACZ,aAAO,WAAW,UAAU,MAAM,GAAG,OAAO,CAAC;AAAA,IACjD,KAAK,WAAW;AACZ,aAAO,WAAW,UAAU,MAAM,GAAG,QAAQ,CAAC;AAAA,IAClD,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,CAAC;AAAA,IAC5C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,CAAC;AAAA,IAC5C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,CAAC;AAAA,IAC5C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,CAAC;AAAA,IAC5C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,CAAC;AAAA,IAC5C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,CAAC;AAAA,IAC5C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,CAAC;AAAA,IAC5C,KAAK,WAAW;AACZ,aAAO,WAAW,UAAU,MAAM,GAAG,KAAK,CAAC;AAAA,IAC/C,KAAK,WAAW;AACZ,aAAO,WAAW,UAAU,MAAM,GAAG,UAAU,CAAC;AAAA,IACpD,KAAK,WAAW;AACZ,aAAO;AAAA,QACH,WAAW,UAAU,eAAe,IAAI,GAAG,GAAG,KAAK,CAAC;AAAA,QACpD,WAAW,UAAU,aAAa,IAAI,GAAG,GAAG,UAAU,CAAC;AAAA,MAC3D,EAAE,KAAK,KAAK;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO,WAAW,UAAU,MAAM,GAAG,IAAI,CAAC;AAAA,IAC9C,KAAK,WAAW;AACZ,YAAM,QAAQ,IAAI,KAAK,cAAc,IAAI,GAAG,GAAG,CAAC;AAChD,aAAO,WAAW,UAAU,OAAO,GAAG,IAAI,CAAC;AAAA,IAC/C,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C,KAAK,WAAW;AACZ,aAAOA,OAAM,UAAU,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,IAC/C;AACI,aAAO,UAAU,IAAI;AAAA,EAG7B;AACJ;AAIO,SAAS,eAAe,MAAM;AACjC,SAAO,gBAAgB,OAAO,OAAO,OAAO,SAAS,WAAW,IAAI,KAAK,IAAI,IAAI,oBAAI,KAAK;AAG1F,QAAM,IAAI,IAAI,KAAK,KAAK,eAAe,GAAG,KAAK,YAAY,GAAG,KAAK,WAAW,GAAG,KAAK,YAAY,GAAG,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC;AAC/I,IAAE,eAAe,KAAK,eAAe,CAAC;AACtC,SAAO;AACX;AAIO,SAAS,eAAe,MAAM;AACjC,SAAO,gBAAgB,OAAO,OAAO,OAAO,SAAS,WAAW,IAAI,KAAK,IAAI,IAAI,oBAAI,KAAK;AAE1F,QAAM,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,YAAY,GAAG,KAAK,SAAS,GAAG,KAAK,QAAQ,GAAG,KAAK,SAAS,GAAG,KAAK,WAAW,GAAG,KAAK,WAAW,CAAC,CAAC;AACvI,SAAO;AACX;AAIO,SAAS,WAAW,MAAM,IAAI;AACjC,QAAM,WAAW,KAAK,QAAQ;AAC9B,QAAM,SAAS,GAAG,QAAQ;AAC1B,SAAO,IAAI,KAAK,WAAW,KAAK,OAAO,KAAK,SAAS,SAAS;AAClE;AAMA,IAAM,cAAc;AAIb,SAAS,aAAa,OAAO;AAChC,SAAO,YAAY,KAAK,KAAK;AACjC;;;AKrqBA;AAAA;AAAA;AAAA;AAAA;AAAA;AACO,IAAI;AAAA,CACV,SAAUC,gBAAe;AACtB,EAAAA,eAAcA,eAAc,MAAM,IAAI,CAAC,IAAI;AAC3C,EAAAA,eAAcA,eAAc,KAAK,IAAI,CAAC,IAAI;AAC1C,EAAAA,eAAcA,eAAc,MAAM,IAAI,CAAC,IAAI;AAC3C,EAAAA,eAAcA,eAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,EAAAA,eAAcA,eAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,EAAAA,eAAcA,eAAc,aAAa,IAAI,CAAC,IAAI;AACtD,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AASjC,SAAS,YAAY,OAAO,KAAK,UAAU;AAC9C,QAAM,YAAY,OAAO,UAAU,WAAW,SAAS,KAAK,IAAI;AAChE,QAAM,UAAU,OAAO,QAAQ,WAAW,SAAS,GAAG,IAAI;AAC1D,QAAM,iBAAiB,YACjB,KAAK,IAAI,OAAO,WAAW,oBAAI,KAAK,CAAC,IAAI,OAAO,SAAS,CAAC,IAC1D;AACN,MAAI,CAAC,OAAO,SAAS,cAAc,KAAK,YAAY,MAAM;AACtD,WAAO;AAAA,EACX;AACA,MAAI,gBAAe,qCAAU,iBAAgB,kBAAkB;AAC/D,MAAI,WAAU,qCAAU,YAAW;AACnC,MAAI,WAAU,qCAAU,YAAW;AACnC,MAAI,SAAQ,qCAAU,UAAS;AAC/B,MAAI,QAAO,qCAAU,SAAQ;AAC7B,MAAI,SAAQ,qCAAU,UAAS;AAC/B,MAAI,gBAAgB,KAAM;AACtB,UAAM,gBAAgB,eAAgB,eAAe,OAAS;AAC9D,eAAW;AACX,mBAAe,eAAe,eAAe;AAAA,EACjD;AACA,MAAI,WAAW,IAAI;AACf,UAAM,gBAAgB,UAAW,UAAU,MAAO;AAClD,eAAW;AACX,cAAU,UAAU,eAAe;AAAA,EACvC;AACA,MAAI,WAAW,IAAI;AACf,UAAM,cAAc,UAAW,UAAU,MAAO;AAChD,aAAS;AACT,cAAU,UAAU,aAAa;AAAA,EACrC;AACA,MAAI,SAAS,IAAI;AACb,UAAM,aAAa,QAAS,QAAQ,MAAO;AAC3C,YAAQ;AACR,YAAQ,QAAQ,YAAY;AAAA,EAChC;AACA,MAAI,QAAQ,KAAK;AACb,UAAM,cAAc,OAAQ,OAAO,OAAQ;AAC3C,aAAS;AACT,WAAO,OAAO,aAAa;AAAA,EAC/B;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEO,SAAS,iBAAiB,QAAQ;AACrC,QAAM,EAAE,OAAO,KAAK,UAAU,aAAa,IAAI,UAAU,QAAQ,IAAI;AACrE,QAAM,WAAW,YAAY,OAAO,KAAK,OAAO,QAAQ;AACxD,MAAI,aAAa,MAAM;AACnB,WAAO;AAAA,EACX;AACA,MAAI,cAAc,CAAC;AACnB,MAAI,YAAY,YAAY,UACtB,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,IAC9B,CAAC,SAAS,QAAQ,SAAS,WAAW,WAAW,cAAc;AACrE,MAAI,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,IACT,SAAS;AAAA,EACb,EAAE,OAAO,CAAC,GAAGC,OAAMA,OAAM,YAAY,GAAG;AAExC,WAAS,KAAK,UAAU;AACpB,QAAI,YAAY,UAAU,YAAY;AAClC;AAAA,IACJ;AACA,UAAM,UAAU,SAAS,CAAC;AAC1B,QAAI,WAAW,UAAU,CAAC;AAE1B,QAAI,YAAY,KAAM,YAAY,WAAW,KAAK,OAAO,CAAC,MAAM,SAAS,SAAS,GAAI;AAClF,cAAQ,SAAS;AAAA,QACb,KAAK;AACD,sBAAY,KAAK,UAAU,QAAQ;AACnC;AAAA,QACJ,KAAK;AACD,cAAI,YAAY,GAAG;AAEf,uBAAW,SAAS,MAAM,GAAG,EAAE;AAAA,UACnC;AACA,sBAAY,KAAK,UAAU,MAAM,QAAQ;AACzC;AAAA,MACR;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,WAAW,YAAY,KAAK,YAAY,SAAS,UAAU,GAAG;AACpE,SAAO;AACX;;;AC/GA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAAAC;AAAA,EAAA;AAAA;AAEA,SAAS,gBAAgB,UAAU,OAAO;AACtC,QAAM,EAAE,QAAQ,IAAI,SAAS;AAC7B,QAAM,gBAAgB,SAAS,SAAS,SAAS,QAAQ,KAAK,IAAI,CAAC;AACnE,SAAO;AAAA,IACH,GAAG,QAAQ;AAAA,IACX,GAAG;AAAA,EACP;AACJ;AACO,SAAS,aAAa,QAAQ,OAAO,SAAS;AACjD,SAAO,uBAAuB,eAAe,QAAQ,OAAO,OAAO;AACvE;AAEO,SAAS,uBAAuB,UAAU,QAAQ,OAAO,UAAU,CAAC,GAAG;AAC1E,MAAI,UAAU,MAAM;AAChB,WAAO;AAAA,EACX;AACA,MAAI,UAAU,QAAQ;AAClB,WAAO,GAAG,MAAM;AAAA,EACpB;AAEA,MAAI,SAAS,MAAM;AACf,YAAQ,OAAO,UAAU,MAAM,IAAI,YAAY;AAAA,EACnD;AACA,QAAM,WAAW,gBAAgB,UAAU,KAAK;AAEhD,QAAM,YAAY,KAAK,aAAa,SAAS,QAAQ;AAAA;AAAA,IAEjD,GAAG;AAAA,IACH,GAAI,UAAU,aAAa;AAAA,MACvB;AAAA,IACJ;AAAA;AAAA,IAEA,GAAG;AAAA,MACC,uBAAuB,QAAQ,kBAAkB,SAAS;AAAA,MAC1D,uBAAuB,QAAQ,kBAAkB,SAAS;AAAA,IAC9D;AAAA;AAAA,IAEA,GAAG,QAAQ,OAAO;AAAA,IAClB,GAAI,UAAU,mBAAmB;AAAA,MAC7B,OAAO;AAAA,MACP,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,IAC3B;AAAA;AAAA,IAEA,GAAI,UAAU,kBAAkB;AAAA,MAC5B,OAAO;AAAA,MACP,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,IAC3B;AAAA;AAAA,IAEA,GAAI,UAAU,YAAY;AAAA,MACtB,OAAO;AAAA,MACP,UAAU;AAAA,MACV,uBAAuB;AAAA,IAC3B;AAAA;AAAA,IAEA,GAAI,UAAU,aAAa;AAAA,MACvB,OAAO;AAAA,MACP,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,IAC3B;AAAA,EACJ,CAAC;AACD,QAAM,QAAQ,UAAU,OAAO,MAAM;AACrC,MAAI,SAAS,QAAQ,UAAU;AAC/B,MAAI,UAAU,KAAK,IAAI,MAAM,KAAK,KAAK,QAAQ,sBAAsB,IAAI;AACrE,cAAU,QAAQ,qBAAqB;AAAA,EAC3C;AACA,SAAO,GAAG,KAAK,GAAG,MAAM;AAC5B;AAIO,SAAS,MAAM,OAAOC,MAAKC,MAAK;AACnC,SAAO,QAAQD,OAAMA,OAAM,QAAQC,OAAMA,OAAM;AACnD;AAIO,SAAS,aAAa,OAAO;AAhFpC;AAiFI,WAAO,oCAAO,WAAW,MAAM,KAAK,OAA7B,mBAAiC,WAAU;AACtD;AAIO,SAASC,OAAM,OAAO,UAAU;AACnC,SAAO,OAAO,MAAM,QAAQ,QAAQ,CAAC;AACzC;AAIO,SAAS,KAAK,OAAOC,OAAM;AAC9B,SAAOD,OAAM,QAAQC,OAAM,aAAaA,KAAI,CAAC;AACjD;AAIO,SAAS,cAAcH,MAAKC,MAAK;AACpC,SAAO,KAAK,MAAM,KAAK,OAAO,KAAKA,OAAMD,OAAM,EAAE,IAAIA;AACzD;AAKO,SAAS,OAAO,GAAG,GAAG;AACzB,UAAS,IAAI,IAAK,KAAK;AAC3B;;;ACtGO,SAAS,UAAU,OAAO;AAC7B,SAAO,KAAK,UAAU,OAAO,QAAQ;AACzC;AACO,SAAS,SAAS,KAAK,OAAO;AACjC,MAAI,iBAAiB,KAAK;AACtB,WAAO;AAAA,MACH,OAAO;AAAA,MACP,OAAO,MAAM,KAAK,MAAM,QAAQ,CAAC;AAAA,IACrC;AAAA,EACJ,WACS,iBAAiB,KAAK;AAC3B,WAAO;AAAA,MACH,OAAO;AAAA,MACP,OAAO,MAAM,KAAK,MAAM,OAAO,CAAC;AAAA,IACpC;AAAA,EACJ,OACK;AACD,WAAO;AAAA,EACX;AACJ;AAIO,SAASI,OAAM,OAAO;AACzB,MAAI;AACJ,MAAI;AACA,aAAS,KAAK,MAAM,OAAO,OAAO;AAAA,EACtC,SACOC,IAAG;AACN,aAAS;AAAA,EACb;AACA,SAAO;AACX;AAIO,SAAS,QAAQ,KAAK,OAAO;AAChC,MAAI,OAAO,UAAU,YAAY,aAAa,KAAK,GAAG;AAClD,WAAO,SAAS,KAAK;AAAA,EACzB,WACS,OAAO,UAAU,YAAY,UAAU,MAAM;AAClD,QAAI,MAAM,UAAU,OAAO;AACvB,aAAO,IAAI,IAAI,MAAM,KAAK;AAAA,IAC9B,WACS,MAAM,UAAU,OAAO;AAC5B,aAAO,IAAI,IAAI,MAAM,KAAK;AAAA,IAC9B;AAAA,EACJ;AACA,SAAO;AACX;;;ACtDA;AAAA;AAAA;AAAA;AAAA;AAEO,IAAM,UAAU,OAAO,WAAW;AAClC,IAAM,MAAM,OAAO,WAAW;;;ACHrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,SAAS,UAAU,UAAU,MAAM,OAAO;AACtC,SAAO,UAAU,IACX,SAAS,WAAW,KAAK,IAAI,EAAE,UAC/B,UAAU,IACN,SAAS,WAAW,KAAK,IAAI,EAAE,OAC/B,SAAS,WAAW,KAAK,IAAI,EAAE,MAAM,QAAQ,OAAO,MAAM,SAAS,CAAC;AAClF;AACO,SAAS,oBAAoB,UAAU,YAAY;AACtD,MAAI,MAAM,oBAAI,KAAK;AACnB,QAAM,QAAQ,WAAW,GAAG;AAC5B,MAAI,UAAU;AACV,iBACI,iCAAiC,SAAS,QAAQ,MAAM,cAAc,UAAU,KAC5E;AAAA,EACZ;AACA,QAAM,EAAE,OAAO,KAAK,IAAI,IAAI,yBAAyB,UAAU,UAAU;AACzE,UAAQ,YAAY;AAAA,IAChB,KAAK,WAAW,KAAK;AACjB,YAAM,OAAO,MAAM,IAAI,OAAO,EAAE,CAAC;AACjC,aAAO,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,EAAE,EAAE,IAAI,CAAC,UAAU;AACvC,eAAO;AAAA,UACH,OAAO,UAAU,UAAU,aAAa,KAAK;AAAA,UAC7C,OAAO;AAAA,YACH,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC;AAAA,YAC1B,IAAI,UAAU,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,YACvC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW,SAAS;AACrB,YAAM,OAAO,MAAM,IAAI,OAAO,EAAE,CAAC;AACjC,aAAO,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU;AAClC,eAAO;AAAA,UACH,OAAO,UAAU,UAAU,cAAc,KAAK;AAAA,UAC9C,OAAO;AAAA,YACH,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC;AAAA,YAC1B,IAAI,UAAU,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,YACvC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW,YAAY;AACxB,YAAM,OAAO,MAAM,IAAI,OAAO,EAAE,CAAC;AACjC,aAAO,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU;AAClC,eAAO;AAAA,UACH,OAAO,UAAU,UAAU,gBAAgB,KAAK;AAAA,UAChD,OAAO;AAAA,YACH,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC;AAAA,YAC1B,IAAI,UAAU,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,YACvC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,KAAK,WAAW,OAAO;AACnB,YAAM,OAAO,MAAM,IAAI,OAAO,EAAE,CAAC;AACjC,aAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,EAAE,IAAI,CAAC,UAAU;AACtC,eAAO;AAAA,UACH,OAAO,UAAU,UAAU,eAAe,KAAK;AAAA,UAC/C,OAAO;AAAA,YACH,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC;AAAA,YAC1B,IAAI,UAAU,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,YACvC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,KAAK,WAAW,SAAS;AACrB,YAAM,OAAO,MAAM,IAAI,OAAO,EAAE,CAAC;AACjC,aAAO,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,EAAE,IAAI,CAAC,UAAU;AAEpC,YAAI,UAAU,IAAI;AACd,iBAAO;AAAA,YACH,OAAO,SAAS,WAAW,KAAK;AAAA,YAChC,OAAO;AAAA,cACH,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;AAAA,cAC1B,IAAI,IAAI,IAAI,OAAO,EAAE,CAAC;AAAA,cACtB;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AACA,eAAO;AAAA,UACH,OAAO,UAAU,UAAU,iBAAiB,KAAK;AAAA,UACjD,OAAO;AAAA,YACH,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC;AAAA,YAC1B,IAAI,UAAU,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,YACvC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,KAAK,WAAW,cAAc;AAC1B,YAAM,OAAO,MAAM,IAAI,OAAO,EAAE,CAAC;AACjC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU;AAC/B,eAAO;AAAA,UACH,OAAO,UAAU,UAAU,cAAc,KAAK;AAAA,UAC9C,OAAO;AAAA,YACH,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC;AAAA,YAC1B,IAAI,UAAU,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,YACvC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,KAAK,WAAW,mBAAmB;AAC/B,YAAM,OAAO,MAAM,IAAI,OAAO,EAAE,CAAC;AACjC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU;AAC/B,eAAO;AAAA,UACH,OAAO,UAAU,UAAU,oBAAoB,KAAK;AAAA,UACpD,OAAO;AAAA,YACH,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC;AAAA,YAC1B,IAAI,UAAU,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,YACvC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,SAAS;AACL,aAAO,CAAC;AAAA,IACZ;AAAA,EACJ;AACJ;AACO,SAAS,4BAA4B,YAAY,SAAS;AAC7D,UAAQ,YAAY;AAAA,IAChB,KAAK,WAAW;AAIZ,YAAM,qBAAoB,mCAAS,iBAC5B,WAAW,mCAAS,aAAa,KAChC,QAAQ,mCAAS,eAAe,IAAI;AAAA,QAAK,mCAAS,cAAc;AAAA;AAAA,QAAuB;AAAA,QAAG;AAAA,MAAE,CAAC,KAC5F,WAAW,SAAS,mCAAS,eAAe,CAAC,CAAC,KAC3C,SAAS,mCAAS,eAAe,IAAI;AAAA,QAAK,mCAAS,cAAc;AAAA;AAAA,QAAuB;AAAA,QAAG;AAAA,MAAE,CAAC,IACpG;AACN,cAAO,mCAAS,kBACV,OACA,oBACI,OACA;AAAA,IACd,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO;AAAA,IACX,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO;AAAA,IACX,KAAK,WAAW;AACZ,aAAO;AAAA,IACX,KAAK,WAAW;AACZ,aAAO;AAAA,IACX,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AACZ,aAAO;AAAA,EACf;AACJ;AACO,SAAS,0BAA0B,UAAU,MAAM,QAAQ;AAC9D,MAAI,UAAU,QAAQ,OAAO,QAAQ,QAAQ,OAAO,MAAM,QAAQ,OAAO,cAAc,MAAM;AACzF,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAChE;AACA,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,YAAM,YAAY,yBAAyB,UAAU,OAAO,UAAU;AACtE,aAAO,UAAU,WAAW,OAAO,MAAM,OAAO,EAAE,IAAI;AAAA,IAC1D,KAAK;AACD,aAAO,4BAA4B,OAAO,YAAY;AAAA,QAClD,eAAe,OAAO;AAAA,MAC1B,CAAC;AAAA,IACL,KAAK;AACD,aAAO,4BAA4B,OAAO,YAAY;AAAA,QAClD,gBAAgB;AAAA,MACpB,CAAC;AAAA,IACL;AACI,YAAM,IAAI,MAAM,8BAA8B,IAAI;AAAA,EAC1D;AACJ;;;ACtNA;AAAA;AAAA;AAAA;AAIO,SAAS,IAAI,KAAK,MAAM;AAC3B,MAAI,MAAM;AACV,MAAI,QAAQ;AACZ,QAAM,cAAc,CAAC,GAAG,IAAI;AAC5B,SAAQ,MAAM,YAAY,MAAM,GAAI;AAChC,QAAI,iBAAiB,OAAO,MAAM,IAAI,GAAG,GAAG;AACxC,cAAQ,MAAM,IAAI,GAAG;AAAA,IACzB,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;;;ACjBA;AAAA;AAAA;AAAA;AAEe,SAAR,eAAkB,MAAM,QAAQC,QAAO,CAAC,GAAG,WAAW,WAAW;AAKpE,QAAM,WAAWA,MAAK,IAAI,CAAC,QAAQ;AAC/B,QAAI,mBAAW,GAAG,GAAG;AACjB,aAAO;AAAA,IACX,WACS,OAAO,QAAQ,UAAU;AAC9B,aAAO,CAAC,MAAM,YAAI,GAAG,GAAG,KAAK;AAAA,IACjC,OACK;AACD,aAAO,MAAM;AAAA,IACjB;AAAA,EACJ,CAAC;AACD,SAAO,OAAO,MAAM,QAAQ,GAAG,QAAQ;AAC3C;;;ACnBA;AAAA;AAAA;AAAA;AAAA;AACO,SAAS,IAAI,YAAY,MAAM;AAElC,MAAI,QAAQ,MAAM;AACd,WAAO;AAAA,EACX,WACS,KAAK,MAAM,UAAU,GAAG;AAG7B,QAAI,CAAC,EAAE,aAAa,YAAY,IAAI,KAAK,MAAM,gBAAgB;AAC/D,QAAI,MAAM,WAAW,SAAS,QAAQ,OAAO,EAAE;AAE/C,UAAM,WAAW,YAAY,MAAM,SAAS,KAAK,CAAC;AAGlD,aAAS,QAAQ,MAAO,MAAM,IAAI,QAAQ,gBAAgB,EAAE,CAAE;AAC9D,WAAO,GAAG,GAAG,IAAI,YAAY,GAAG,QAAQ,OAAO,EAAE;AACjD,WAAO,QAAQ;AAAA,EAEnB,WACS,KAAK,MAAM,KAAK,GAAG;AAGxB,WAAO;AAAA,EACX,OACK;AAED,WAAO;AAAA,EACX;AAEA,SAAO;AACX;AACO,SAAS,SAAS,YAAY,MAAM;AACvC,MAAI,SAAS,KAAK;AAEd,WAAO,WAAW,aAAa;AAAA,EACnC,OACK;AAED,WAAO,WAAW,SAAS,MAAM,OAAO,SAAS,KAAK;AAAA,EAC1D;AACJ;;;ACzCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,SAAS,gBAAgB,OAAO,kBAAkB;AAC9C,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AAEA,MAAI,MAAM,WAAW,MAAM,CAAC,oBAAqB,oBAAoB,UAAU,KAAM;AACjF,WAAO;AAAA,EACX;AACA,QAAM,MAAM,iBAAiB,QAAQ,MAAM,CAAC,IAAI;AAChD,MAAI,OAAO,MAAM;AACb,WAAO;AAAA,EACX;AACA,MAAI,CAAC,oBAAoB,QAAQ,IAAI;AACjC,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAMA,SAAS,qBAAqB,OAAO;AACjC,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,SAAO,iBAAiB,QAAQ,QAAQ,UAAU,KAAK,CAAC,IAAI,CAAC,KAAK;AACtE;AAOO,SAAS,WAAW,MAAM;AAC7B,MAAI,QAAQ,MAAM;AACd,WAAO;AAAA,EACX;AACA,QAAM,OAAO,KAAK,YAAY;AAC9B,QAAM,QAAQ,KAAK,SAAS,IAAI;AAChC,QAAM,MAAM,KAAK,QAAQ;AACzB,SAAO,GAAG,IAAI,IAAI,QAAQ,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,MAAM,KAAK,IAAI,GAAG,KAAK,GAAG;AACpF;AAYO,SAAS,WAAW,OAAO;AAC9B,QAAM,aAAa,gBAAgB,KAAK;AACxC,MAAI,cAAc;AACd,WAAO;AACX,QAAM,QAAQ,WAAW,MAAM,GAAG;AAElC,MAAI,MAAM,CAAC,KAAK,MAAM;AAClB,UAAM,CAAC,KAAK;AAAA,EAChB,OACK;AAED,UAAM,CAAC,IAAI;AACX,UAAM,CAAC,IAAI;AAAA,EACf;AACA,QAAM,UAAU,IAAI,KAAK,GAAG,KAAK;AACjC,MAAI,MAAM,QAAQ,QAAQ,CAAC,GAAG;AAC1B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAOO,SAAS,eAAe,MAAM;AACjC,MAAI,QAAQ,MAAM;AACd,WAAO;AAAA,EACX;AACA,SAAO,KAAK,YAAY;AAC5B;AAYO,SAAS,eAAe,OAAO;AAClC,QAAM,aAAa,gBAAgB,KAAK;AACxC,MAAI,cAAc;AACd,WAAO;AACX,QAAM,UAAU,IAAI,KAAK,UAAU;AACnC,MAAI,MAAM,QAAQ,QAAQ,CAAC,GAAG;AAC1B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAOO,SAAS,cAAc,MAAM;AAChC,MAAI,QAAQ,MAAM;AACd,WAAO;AAAA,EACX;AACA,SAAO,OAAO,MAAM;AACxB;AAUO,SAAS,cAAc,OAAO;AACjC,QAAM,UAAU,gBAAgB,KAAK;AACrC,MAAI,WAAW;AACX,WAAO;AACX,MAAI,YAAY,KAAK;AACjB,WAAO;AAAA,EACX,WACS,YAAY,KAAK;AACtB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAOO,SAAS,aAAaC,MAAK;AAC9B,MAAIA,QAAO,MAAM;AACb,WAAOA;AAAA,EACX;AACA,SAAO,OAAOA,IAAG;AACrB;AAUO,SAAS,aAAa,OAAO;AAChC,QAAM,SAAS,gBAAgB,KAAK;AACpC,MAAI,UAAU;AACV,WAAO;AACX,MAAI,WAAW;AACX,WAAO;AACX,QAAM,SAAS,CAAC;AAChB,SAAO;AACX;AAOO,SAAS,aAAa,KAAK;AAC9B,MAAI,OAAO,MAAM;AACb,WAAO;AAAA,EACX;AACA,SAAO,OAAO,GAAG;AACrB;AASO,SAAS,aAAa,OAAO;AAChC,QAAM,MAAM,gBAAgB,OAAO,IAAI;AACvC,MAAI,OAAO;AACP,WAAO;AACX,SAAO,OAAO,GAAG;AACrB;AAUO,SAAS,WAAW,OAAO,YAAY;AAC1C,QAAM,MAAM,aAAa,KAAK;AAC9B,MAAI,OAAO;AACP,WAAO;AACX,SAAO,WAAW,SAAS,GAAG,IAAI,MAAM;AAC5C;AAOO,SAAS,WAAW,KAAK;AAC5B,MAAI,OAAO,MAAM;AACb,WAAO;AAAA,EACX;AACA,SAAO,UAAU,GAAG;AACxB;AAWO,SAAS,WAAW,OAAO;AAC9B,QAAM,UAAU,gBAAgB,KAAK;AACrC,MAAI,WAAW;AACX,WAAO;AACX,MAAI,SAAS;AACb,MAAI;AACA,aAASC,OAAM,OAAO;AAAA,EAC1B,SACOC,IAAG;AAAA,EAEV;AACA,SAAO;AACX;AAQO,SAAS,YAAY,OAAO;AAC/B,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAQO,SAAS,YAAY,OAAO;AAC/B,QAAM,MAAM,qBAAqB,KAAK;AACtC,MAAI,OAAO;AACP,WAAO;AACX,SAAO;AACX;AAQO,SAAS,mBAAmB,OAAO;AACtC,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,SAAO,MAAM,IAAI,MAAM;AAC3B;AAQO,SAAS,mBAAmB,OAAO;AACtC,QAAM,MAAM,YAAY,KAAK;AAC7B,MAAI,OAAO;AACP,WAAO;AACX,SAAO,IAAI,IAAI,CAAC,MAAO,MAAM,MAAM,KAAK,OAAO,OAAO,CAAC,CAAE;AAC7D;AAUO,SAAS,qBAAqB,OAAO,iBAAiB,KAAK;AAC9D,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,SAAO,MAAM,KAAK,cAAc;AACpC;AAYO,SAAS,qBAAqB,OAAO,iBAAiB,KAAK;AAC9D,QAAM,WAAW,gBAAgB,OAAO,IAAI;AAC5C,MAAI,YAAY;AACZ,WAAO;AACX,MAAI,aAAa;AACb,WAAO,CAAC;AACZ,SAAO,SAAS,MAAM,cAAc;AACxC;AAQO,IAAM,8BAA8B;AAUpC,SAAS,4BAA4B,UAAU,iBAAiB,KAAK;AACxE,QAAM,UAAU,qBAAqB,UAAU,cAAc;AAC7D,MAAI,WAAW;AACX,WAAO;AACX,SAAO,QAAQ,IAAI,CAAC,MAAO,MAAM,MAAM,KAAK,OAAO,OAAO,CAAC,CAAE;AACjE;AAWO,SAAS,aAAa,KAAK,kBAAkB,KAAK,iBAAiB,KAAK;AAC3E,MAAI,OAAO;AACP,WAAO;AACX,MAAI,cAAc,GAAG;AACjB,WAAO;AACX,SAAO,KAAK,GAAG,EACV,IAAI,CAAC,QAAQ;AACd,UAAM,QAAQ,WAAW,IAAI,GAAG,CAAC;AACjC,WAAO,GAAG,GAAG,GAAG,eAAe,GAAG,KAAK;AAAA,EAC3C,CAAC,EACI,KAAK,cAAc;AAC5B;AAcO,SAAS,aAAa,OAAO,kBAAkB,KAAK,iBAAiB,KAAK;AAC7E,QAAM,SAAS,gBAAgB,OAAO,IAAI;AAC1C,MAAI,UAAU;AACV,WAAO;AACX,MAAI,WAAW;AACX,WAAO,CAAC;AACZ,QAAM,MAAM,CAAC;AACb,QAAM,wBAAwB,IAAI,OAAO,GAAG,eAAe,MAAM;AACjE,SAAO,MAAM,cAAc,EAAE,QAAQ,CAAC,aAAa;AAC/C,UAAM,CAAC,KAAK,KAAK,IAAI,SAAS,MAAM,qBAAqB;AACzD,QAAI,GAAG,IAAI,WAAW,KAAK;AAAA,EAC/B,CAAC;AACD,SAAO;AACX;AAWO,IAAM,sBAAsB;AAc5B,SAAS,oBAAoB,OAAO,kBAAkB,KAAK,iBAAiB,KAAK;AACpF,QAAM,UAAU,aAAa,OAAO,iBAAiB,cAAc;AACnE,MAAI,WAAW;AACX,WAAO;AAEX,QAAM,mBAAmB,CAAC;AAC1B,aAAW,OAAO,KAAK,OAAO,GAAG;AAC7B,qBAAiB,GAAG,IAAI,aAAa,QAAQ,GAAG,CAAC;AAAA,EACrD;AACA,SAAO;AACX;;;AC/bA;AAAA;AAAA;AAAA;AAIO,SAAS,eAAe,UAAU;AACrC,SAAO,QAAQ,QAAQ,EAClB,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AACvB,QAAI,OAAO;AAEP,YAAM,eAAe,IAAI,QAAQ,YAAY,KAAK,EAAE,YAAY;AAChE,aAAO,GAAG,YAAY,KAAK,KAAK;AAAA,IACpC,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ,CAAC,EACI,OAAO,CAAC,MAAM,CAAC,EACf,KAAK,GAAG;AACjB;;;ACfO,IAAM,iBAAiB,CAAC,WAAW,aAAa,UAAU,SAAS;AACnE,IAAM,cAAc,CAAC,QAAQ,WAAW,WAAW,QAAQ;AAC3D,IAAM,SAAS,CAAC,GAAG,gBAAgB,GAAG,WAAW;AACjD,IAAM,SAAS,CAAC,IAAI,GAAG,MAAM,KAAK,KAAM,GAAG,CAAC;AAC5C,IAAM,aAAa;AAAA;AAAA,EAEtB,GAAG,OAAO,QAAQ,CAAC,UAAU;AAAA,IACzB;AAAA;AAAA,IACA,GAAG,KAAK;AAAA;AAAA,IACR,GAAG,OAAO,IAAI,CAAC,UAAU,GAAG,KAAK,IAAI,KAAK,EAAE;AAAA,EAChD,CAAC;AAAA;AAAA,EAED;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAIO,SAAS,cAAc,YAAY;AACtC,QAAM,cAAc,WAAW,MAAM,eAAe;AACpD,QAAM,QAAQ,CAAC;AACf,QAAM,OAAO,CAAC;AAEd,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AACzC,UAAM,QAAQ,YAAY,CAAC;AAE3B,UAAM,YAAY,MAAM,MAAM,YAAY;AAC1C,QAAI,CAAC;AACD;AACJ,UAAM,YAAY,UAAU,CAAC;AAC7B,QAAI,MAAM,SAAS,oBAAoB,GAAG;AACtC,WAAK,KAAK,SAAS;AAAA,IACvB,OACK;AACD,YAAM,KAAK,SAAS;AAAA,IACxB;AAAA,EACJ;AACA,SAAO,EAAE,OAAO,KAAK;AACzB;AAIO,SAAS,mBAAmBC,SAAQ,YAAY;AA/CvD;AAkDI,EAAAA,QAAA,eAAAA,QAAA,aAAsBA,QAAO,aAAa,KAAK;AAE/C,EAAAA,QAAA,YAAAA,QAAA,UAAmBA,QAAO,UAAU,KAAK;AACzC,EAAAA,QAAA,eAAAA,QAAA,aAAsBA,QAAO,aAAa,KAAK;AAC/C,EAAAA,QAAA,eAAAA,QAAA,aAAsBA,QAAO,aAAa,KAAK;AAC/C,EAAAA,QAAA,cAAAA,QAAA,YAAqBA,QAAO,YAAY,KAAK;AAE7C,aAAW,SAAS,CAAC,GAAG,gBAAgB,GAAG,WAAW,GAAG;AAErD,IAAAA,QAAA,WAAAA,QAAA,SAAkBA,QAAO,GAAG,KAAK,MAAM;AACvC,IAAAA,QAAA,KAAO,GAAG,KAAK,gBAAfA,QAAA,MAA+B,gBAAgBA,QAAO,KAAK,CAAC;AAE5D,eAAW,SAAS,QAAQ;AACxB,YAAM,iBAAiB,GAAG,KAAK,IAAI,KAAK;AACxC,UAAI,EAAE,kBAAkBA,UAAS;AAE7B,cAAM,mBAAiB,UAAKA,OAAM,EAC7B,IAAI,CAAC,QAAQ;AACd,gBAAM,CAACC,IAAG,CAAC,IAAI,OAAO,GAAG,EAAE,MAAM,GAAG;AACpC,iBAAO,CAACA,IAAG,OAAO,CAAC,CAAC;AAAA,QACxB,CAAC,EACI,KAAK,CAAC,CAACA,IAAG,CAAC,MAAMA,OAAM,UAAU,IAAI,MAAM,IAAI,QAAQ,IAAI,MAAM,MAL/C,mBAKmD,OAAM;AAChF,cAAM,iBAAiBD,QAAO,GAAG,KAAK,IAAI,cAAc,EAAE,KAAKA,QAAO,KAAK;AAC3E,YAAI,QAAQ,KAAK;AACb,UAAAA,QAAA,oBAAAA,QAAA,kBAA2B,aAAa,iBAAiB,iBAAiB,SAAS,GAAI;AAAA,QAC3F,WACS,QAAQ,KAAK;AAClB,UAAAA,QAAA,oBAAAA,QAAA,kBAA2B,YAAYA,QAAO,KAAK,IAAI,QAAQ,kBAAkB,GAAI;AAAA,QACzF,OACK;AACD,UAAAA,QAAA,oBAAAA,QAAA,kBAA2BA,QAAO,KAAK;AAAA,QAC3C;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,EAAAA,QAAA,mBAAAA,QAAA,iBAA0B;AAC1B,EAAAA,QAAA,mBAAAA,QAAA,iBAA0B,YAAYA,QAAO,aAAa,GAAG,IAAI;AACjE,EAAAA,QAAA,mBAAAA,QAAA,iBAA0B,YAAYA,QAAO,aAAa,GAAG,IAAI;AACjE,EAAAA,QAAA,uBAAAA,QAAA,qBAA8B,gBAAgBA,QAAO,aAAa,CAAC;AAEnE,EAAAA,QAAA,oBAAAA,QAAA,kBAA2B,OAAOA,QAAO,iBAAiB,CAAC,IAAI,UAAU;AACzE,QAAM,SAAS,YAAY,QAAQA,OAAM,EAAE,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM;AAC9D,QAAI,WAAW,SAAS,OAAO,IAAI,CAAC,GAAG;AAEnC,aAAO,CAAC,WAAW,IAAI,IAAI,aAAa,OAAO,UAAU,CAAC;AAAA,IAC9D,OACK;AAED,aAAO,CAAC,MAAM,KAAK;AAAA,IACvB;AAAA,EACJ,CAAC,CAAC;AACF,SAAO;AACX;AACA,SAASE,OAAM,OAAO,UAAU;AAC5B,MAAI,OAAO;AACP,WAAO,OAAO,MAAM,QAAQ,QAAQ,CAAC;AAAA,EACzC,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,OAAO,OAAO;AACnB,MAAI;AACA,QAAI,SAAa,OAAO,OAAO,IAAI,SAAa,OAAO,OAAO,GAAG;AAC7D,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX,SACOC,IAAG;AACN,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,gBAAgB,OAAO,aAAa,KAAK;AAC9C,MAAI;AACA,WAAO,OAAO,KAAK,IAAI,aAAa,OAAO,UAAU,IAAI,YAAY,OAAO,UAAU;AAAA,EAC1F,SACOA,IAAG;AAAA,EAEV;AACJ;AACA,SAAS,aAAa,OAAO,YAAY;AACrC,MAAI;AACA,WAAO,UAAU,YAAY,CAAC,OAAO,OAAO,GAAG,OAAO,EAAE,UAAU,CAAC;AAAA,EACvE,SACOA,IAAG;AAAA,EAEV;AACJ;AACA,SAAS,YAAY,OAAO,YAAY;AACpC,MAAI;AACA,WAAO,UAAU,YAAY,CAAC,OAAO,OAAO,GAAG,OAAO,EAAE,UAAU,CAAC;AAAA,EACvE,SACOA,IAAG;AAAA,EAEV;AACJ;AAIO,SAAS,aAAa,OAAO,YAAY,WAAW,GAAG;AAC1D,MAAI;AACA,QAAI,eAAe,OAAO;AACtB,YAAM,gBAAgB,OAAO,UAAU,WAAWC,KAAI,KAAK,IAAI;AAC/D,UAAI,eAAe;AACf,cAAM,EAAE,GAAAC,IAAG,GAAG,EAAE,IAAI;AACpB,eAAO,OAAOH,OAAMG,KAAI,KAAK,QAAQ,CAAC,IAAIH,OAAM,IAAI,KAAK,QAAQ,CAAC,IAAIA,OAAM,IAAI,KAAK,QAAQ,CAAC;AAAA,MAClG;AAAA,IACJ,WACS,eAAe,OAAO;AAC3B,YAAM,gBAAgB,OAAO,UAAU,WAAWI,KAAI,SAAS,KAAK,CAAC,IAAI;AACzE,UAAI,eAAe;AACf,cAAM,EAAE,GAAG,GAAG,EAAE,IAAI;AACpB,eAAO,OAAOJ,OAAM,KAAK,GAAG,QAAQ,CAAC,IAAIA,OAAM,IAAI,KAAK,QAAQ,CAAC,KAAKA,OAAM,IAAI,KAAK,QAAQ,CAAC;AAAA,MAClG;AAAA,IACJ,WACS,eAAe,SAAS;AAC7B,YAAM,gBAAgB,OAAO,UAAU,WAAW,MAAM,SAAS,KAAK,CAAC,IAAI;AAC3E,UAAI,eAAe;AACf,cAAM,EAAE,GAAG,GAAAD,IAAG,EAAE,IAAI;AACpB,eAAO,SAASC,OAAM,GAAG,QAAQ,CAAC,IAAIA,OAAMD,IAAG,QAAQ,CAAC,IAAIC,OAAM,KAAK,GAAG,QAAQ,CAAC;AAAA,MACvF;AAAA,IACJ;AAAA,EACJ,SACOC,IAAG;AAAA,EAEV;AACJ;AAIO,SAAS,kBAAkBH,SAAQ,YAAY;AAClD,QAAM,kBAAkB,mBAAmBA,SAAQ,UAAU;AAC7D,SAAO,QAAQ,eAAe,EACzB,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AACvB,WAAO,GAAG,GAAG,KAAK,KAAK;AAAA,EAC3B,CAAC,EACI,KAAK,IAAI;AAClB;AAOO,SAAS,kBAAkB,YAAY;AAC1C,QAAM,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgB1B,MAAI,gBAAgB,WAAW,IAAI,CAAC,UAAU,IAAI,KAAK,GAAG,EAAE,KAAK,IAAI;AACrE,SAAO,WAAW,iBAAiB,KAAK,aAAa;AACzD;;;AChNA,IAAM,UAAU,oBAAoB;AAAA,EAChC,QAAQ;AAAA,IACJ,aAAa;AAAA,MACT,QAAQ;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,GAAG,MAAM,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,aAAa,CAAC,EAAE;AAAA,MAC/C;AAAA,IACJ;AAAA,EACJ;AACJ,CAAC;AACM,IAAM,MAAM,IAAI,WAAW,QAAQ,aAAK,GAAG,MAAM,CAAC;AAClD,IAAM,WAAW,IAAI,WAAW,kBAAU,CAAC,GAAG,GAAG,OAAO,OAAO,OAAO,GAAG,CAAC,GAAG,MAAM,QAAQ,GAAG,CAAC,CAAC;AAChG,IAAM,mBAAmB,CAAC,YAAY;AACzC,SAAO,WAAW,OAAO,YAAY,WAAW,UAAU,EAAE,MAAM,QAAQ;AAC9E;", "names": ["definition", "k", "num", "c", "r", "c", "cls", "a98", "r", "c", "abs", "r", "rgb", "r", "fn", "c", "abs", "r", "definition", "definition_default", "hue", "hue", "fn", "r", "sum", "val", "definition", "definition_default", "c", "c", "k", "e", "fn", "lab", "f2", "rgb", "c", "e", "f", "e", "f", "c", "definition", "definition_default", "definition", "c", "definition_default", "f", "r", "M", "definition", "definition_default", "r", "M", "definition", "c", "definition_default", "f", "r", "M", "definition", "definition_default", "hsv", "definition", "c", "definition_default", "c", "c", "p", "c", "p", "definition", "definition_default", "vn", "p", "d0", "rgb", "definition", "definition_default", "c", "c", "definition", "c", "definition_default", "k", "e", "fn", "e", "k", "lab", "rgb", "r", "f", "e", "k", "f2", "rgb", "definition", "c", "definition_default", "definition", "definition_default", "c", "definition", "c", "definition_default", "definition", "definition_default", "c", "c", "c", "e", "k", "u_fn", "v_fn", "un", "vn", "k", "rgb", "lchuv", "definition", "definition_default", "definition", "definition_default", "definition", "luv", "rgb", "definition_default", "r", "M", "rgb", "M", "c", "k2", "k3", "f", "f2", "rgb", "C1", "r", "r2", "b", "b2", "k", "lab", "c", "hsl", "definition_default", "c", "lab", "c", "k", "hsv", "k", "definition_default", "c", "definition", "definition_default", "c", "c", "definition", "definition_default", "c", "rgb", "r", "definition", "definition_default", "gamma", "abs", "linearize", "abs", "prophoto", "r", "definition", "definition_default", "gamma", "abs", "α", "β", "linearize", "abs", "rec2020", "r", "definition", "definition_default", "r", "transfer", "definition", "definition_default", "definition", "definition_default", "xyz65", "xyz50", "definition", "definition_default", "r", "definition", "definition_default", "c", "fn", "res", "c", "colors", "c", "fn", "rgb", "c", "rgb", "c", "definition_default", "hsl", "rgb", "average", "flatten", "nestedFindByPredicate", "entries", "keys", "fn", "flatten", "average", "sum", "max", "nestedFindByPredicate", "PeriodType", "DayOfWeek", "DateToken", "c", "startOfWeek", "formatter", "range", "DurationUnits", "i", "round", "min", "max", "round", "step", "parse", "e", "keys", "num", "parse", "e", "colors", "c", "round", "e", "rgb", "r", "hsl"]}