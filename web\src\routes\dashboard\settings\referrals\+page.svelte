<script lang="ts">
  import { toast } from 'svelte-sonner';
  import { <PERSON><PERSON> } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import * as Card from '$lib/components/ui/card/index.js';
  import { Badge } from '$lib/components/ui/badge/index.js';
  import { Separator } from '$lib/components/ui/separator/index.js';
  import * as Tabs from '$lib/components/ui/tabs/index.js';
  import { Copy, RefreshCw, Users, Gift, Share2, Mail, TrendingUp } from 'lucide-svelte';
  import ReferralChart from './ReferralChart.svelte';
  import Seo from '$components/shared/SEO.svelte';

  let { data } = $props();
  let referralData = $state(data.referralData);
  let customCode = $state('');
  let updating = $state(false);
  let copying = $state(false);
  let analyticsData = $state<any>(null);
  let loadingAnalytics = $state(true);

  // Load analytics data
  const loadAnalytics = async () => {
    try {
      const response = await fetch('/api/referrals/analytics');
      if (response.ok) {
        analyticsData = await response.json();
      }
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      loadingAnalytics = false;
    }
  };

  // Load analytics on component initialization
  loadAnalytics();

  // Copy referral link to clipboard
  const copyReferralLink = async () => {
    if (!referralData?.referralLink) return;

    copying = true;
    try {
      await navigator.clipboard.writeText(referralData.referralLink);
      toast.success('Referral link copied to clipboard!');
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast.error('Failed to copy referral link');
    } finally {
      copying = false;
    }
  };

  // Generate new referral code
  const generateNewCode = async () => {
    updating = true;
    try {
      const response = await fetch('/api/referrals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'regenerate' }),
      });

      if (response.ok) {
        const data = await response.json();
        referralData.referralCode = data.referralCode;
        referralData.referralLink = data.referralLink;
        toast.success('New referral code generated!');
        // Reload analytics to show updated history
        loadAnalytics();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to generate new code');
      }
    } catch (error) {
      console.error('Error generating new code:', error);
      toast.error('Failed to generate new code');
    } finally {
      updating = false;
    }
  };

  // Set custom referral code
  const setCustomCode = async () => {
    if (!customCode.trim()) {
      toast.error('Please enter a custom code');
      return;
    }

    updating = true;
    try {
      const response = await fetch('/api/referrals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'create', customCode: customCode.trim() }),
      });

      if (response.ok) {
        const data = await response.json();
        referralData.referralCode = data.referralCode;
        referralData.referralLink = data.referralLink;
        customCode = '';
        toast.success('Custom referral code set!');
        // Reload analytics to show updated history
        loadAnalytics();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to set custom code');
      }
    } catch (error) {
      console.error('Error setting custom code:', error);
      toast.error('Failed to set custom code');
    } finally {
      updating = false;
    }
  };

  // Share referral link
  const shareReferralLink = async () => {
    if (!referralData?.referralLink) return;

    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Join Hirli with my referral link',
          text: 'Sign up for Hirli using my referral link and get started with job automation!',
          url: referralData.referralLink,
        });
      } catch (error) {
        console.error('Error sharing:', error);
        copyReferralLink();
      }
    } else {
      copyReferralLink();
    }
  };

  // Generate share message
  const generateShareMessage = () => {
    if (!referralData?.referralLink) return '';
    return `🚀 Join me on Hirli and automate your job search! Use my referral link to get started: ${referralData.referralLink}`;
  };

  // Share via email
  const shareViaEmail = () => {
    const subject = encodeURIComponent('Join Hirli - Automate Your Job Search');
    const body = encodeURIComponent(generateShareMessage());
    window.open(`mailto:?subject=${subject}&body=${body}`);
  };

  // Share via social media
  const shareViaSocial = (platform: string) => {
    const message = encodeURIComponent(generateShareMessage());
    const url = encodeURIComponent(referralData.referralLink);

    let shareUrl = '';
    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${message}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
        break;
    }

    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  };
</script>

<Seo
  title="Referral Program | Hirli"
  description="Share Hirli with friends and earn rewards for successful referrals."
  keywords="referral program, share, earn, rewards, referrals, Hirli"
  url="https://hirli.com/dashboard/settings/referrals" />

<div class="flex h-full flex-col">
  <div class="flex flex-col justify-between p-6">
    <h2 class="text-lg font-semibold">Referral Program</h2>
    <p class="text-muted-foreground">
      Share Hirli with friends and earn rewards for successful referrals.
    </p>
  </div>

  <div class="flex-1">
    {#if referralData}
      <Tabs.Root value="overview" class="w-full">
        <Tabs.List>
          <Tabs.Trigger value="overview">Overview</Tabs.Trigger>
          <Tabs.Trigger value="analytics">Analytics</Tabs.Trigger>
          <Tabs.Trigger value="share">Share & Earn</Tabs.Trigger>
          <Tabs.Trigger value="settings">Settings</Tabs.Trigger>
        </Tabs.List>

        <!-- Overview Tab -->
        <Tabs.Content value="overview" class="space-y-4">
          <!-- Referral Stats -->
          <div class="border-border grid gap-4 divide-x border-b md:grid-cols-3">
            <div class="p-4">
              <div class="flex flex-row items-center justify-between space-y-0 pb-2">
                <Card.Title class="text-sm font-medium">Total Referrals</Card.Title>
                <Users class="text-muted-foreground h-4 w-4" />
              </div>
              <div>
                <div class="text-2xl font-bold">{referralData.referralCount || 0}</div>
                <p class="text-muted-foreground text-xs">People you've referred</p>
              </div>
            </div>

            <div class="p-4">
              <div class="flex flex-row items-center justify-between space-y-0 pb-2">
                <Card.Title class="text-sm font-medium">Rewards Earned</Card.Title>
                <Gift class="text-muted-foreground h-4 w-4" />
              </div>
              <div>
                <div class="text-2xl font-bold">$0</div>
                <p class="text-muted-foreground text-xs">Coming soon</p>
              </div>
            </div>

            <div class="p-4">
              <div class="flex flex-row items-center justify-between space-y-0 pb-2">
                <Card.Title class="text-sm font-medium">Your Code</Card.Title>
                <Share2 class="text-muted-foreground h-4 w-4" />
              </div>
              <div>
                <div class="font-mono text-2xl font-bold">{referralData.referralCode}</div>
                <p class="text-muted-foreground text-xs">Your unique referral code</p>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="flex flex-col gap-8 px-4">
            <div class="grid grid-cols-2 gap-4">
              <div class="flex flex-col gap-4">
                <div class="flex flex-col gap-1">
                  <h2 class="text-lg font-medium">Invite friends & earn rewards</h2>
                  <p class="text-muted-foreground text-sm">
                    Refer a friend and earn $10 credit for every paying user. They’ll get 50% off
                    their first month on Starter or Pro plans. <strong
                      >Start sharing today!
                    </strong>
                  </p>
                </div>

                <div class="w-2/3">
                  <h3 class="mb-3 font-medium">Share your link</h3>
                  <div class="space-y-4">
                    <div class="flex gap-2">
                      <input
                        value={referralData.referralLink}
                        readonly
                        class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 font-mono text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
                      <Button
                        variant="outline"
                        size="sm"
                        onclick={copyReferralLink}
                        disabled={copying}>
                        <Copy class="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" onclick={shareReferralLink}>
                        <Share2 class="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Referrals -->
          {#if referralData.referrals && referralData.referrals.length > 0}
            <Card.Root>
              <Card.Header>
                <Card.Title>Recent Referrals</Card.Title>
                <Card.Description>People who signed up using your referral code.</Card.Description>
              </Card.Header>
              <Card.Content>
                <div class="space-y-3">
                  {#each referralData.referrals.slice(0, 5) as referral}
                    <div class="flex items-center justify-between">
                      <div>
                        <p class="font-medium">
                          {referral.referred?.name || referral.referred?.email || 'Unknown User'}
                        </p>
                        <p class="text-muted-foreground text-sm">
                          Joined {new Date(referral.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge variant={referral.status === 'completed' ? 'default' : 'secondary'}>
                        {referral.status || 'pending'}
                      </Badge>
                    </div>
                    {#if referral !== referralData.referrals[referralData.referrals.length - 1]}
                      <Separator />
                    {/if}
                  {/each}
                </div>
              </Card.Content>
            </Card.Root>
          {/if}
        </Tabs.Content>

        <!-- Analytics Tab -->
        <Tabs.Content value="analytics">
          <ReferralChart {referralData} />
        </Tabs.Content>

        <!-- Share & Earn Tab -->
        <Tabs.Content value="share" class="space-y-6">
          <Card.Root>
            <Card.Header>
              <Card.Title>Share Your Referral Link</Card.Title>
              <Card.Description>
                Choose how you want to share your referral link and start earning rewards.
              </Card.Description>
            </Card.Header>
            <Card.Content class="space-y-6">
              <!-- Share Message Preview -->
              <div class="bg-muted/50 rounded-lg border p-4">
                <h4 class="mb-2 font-medium">Share Message Preview:</h4>
                <p class="text-muted-foreground text-sm">
                  {generateShareMessage()}
                </p>
              </div>

              <!-- Direct Link Sharing -->
              <div class="space-y-4">
                <h4 class="font-medium">Your Referral Link</h4>
                <div class="flex gap-2">
                  <input
                    value={referralData.referralLink}
                    readonly
                    class="border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 font-mono text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
                  <Button onclick={copyReferralLink} disabled={copying}>
                    <Copy class="mr-2 h-4 w-4" />
                    {copying ? 'Copied!' : 'Copy'}
                  </Button>
                </div>
              </div>

              <!-- Share Options -->
              <div class="space-y-4">
                <h4 class="font-medium">Share Options</h4>
                <div class="grid grid-cols-2 gap-3 sm:grid-cols-3">
                  <Button
                    variant="outline"
                    class="flex h-auto flex-col gap-2 py-4"
                    onclick={() => shareViaSocial('twitter')}>
                    <Share2 class="h-5 w-5" />
                    <span class="text-xs">Twitter</span>
                  </Button>
                  <Button
                    variant="outline"
                    class="flex h-auto flex-col gap-2 py-4"
                    onclick={() => shareViaSocial('facebook')}>
                    <Share2 class="h-5 w-5" />
                    <span class="text-xs">Facebook</span>
                  </Button>
                  <Button
                    variant="outline"
                    class="flex h-auto flex-col gap-2 py-4"
                    onclick={() => shareViaSocial('linkedin')}>
                    <Share2 class="h-5 w-5" />
                    <span class="text-xs">LinkedIn</span>
                  </Button>
                  <Button
                    variant="outline"
                    class="flex h-auto flex-col gap-2 py-4"
                    onclick={shareViaEmail}>
                    <Mail class="h-5 w-5" />
                    <span class="text-xs">Email</span>
                  </Button>
                  <Button
                    variant="outline"
                    class="flex h-auto flex-col gap-2 py-4"
                    onclick={shareReferralLink}>
                    <Share2 class="h-5 w-5" />
                    <span class="text-xs">Native Share</span>
                  </Button>
                </div>
              </div>

              <!-- Referral Tips -->
              <div class="rounded-lg border bg-blue-50 p-4 dark:bg-blue-950/20">
                <h4 class="mb-2 flex items-center gap-2 font-medium">
                  <TrendingUp class="h-4 w-4" />
                  Tips for Better Results
                </h4>
                <ul class="text-muted-foreground space-y-1 text-sm">
                  <li>• Share with people actively looking for jobs</li>
                  <li>• Explain how Hirli can help automate their job search</li>
                  <li>• Follow up to help them get started</li>
                  <li>• Share in relevant professional groups</li>
                </ul>
              </div>
            </Card.Content>
          </Card.Root>
        </Tabs.Content>

        <!-- Settings Tab -->
        <Tabs.Content value="settings" class="space-y-6">
          <!-- Customize Referral Code -->
          <Card.Root>
            <Card.Header>
              <Card.Title>Customize Your Referral Code</Card.Title>
              <Card.Description>
                Create a custom referral code that's easy to remember and share.
              </Card.Description>
            </Card.Header>
            <Card.Content class="space-y-4">
              <div class="flex gap-2">
                <Input
                  bind:value={customCode}
                  placeholder="Enter custom code (4-12 characters)"
                  class="font-mono" />
                <Button onclick={setCustomCode} disabled={updating || !customCode.trim()}>
                  Set Code
                </Button>
              </div>
              <div class="flex gap-2">
                <Button variant="outline" onclick={generateNewCode} disabled={updating}>
                  <RefreshCw class="mr-2 h-4 w-4" />
                  Generate New Code
                </Button>
              </div>
              <p class="text-muted-foreground text-xs">
                Custom codes must be 4-12 characters long and contain only letters and numbers.
              </p>
            </Card.Content>
          </Card.Root>

          <!-- Current Code Info -->
          <Card.Root>
            <Card.Header>
              <Card.Title>Current Referral Information</Card.Title>
            </Card.Header>
            <Card.Content class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <div class="text-sm font-medium">Referral Code</div>
                  <p class="font-mono text-lg">{referralData.referralCode}</p>
                </div>
                <div>
                  <div class="text-sm font-medium">Total Referrals</div>
                  <p class="text-lg font-semibold">{referralData.referralCount || 0}</p>
                </div>
              </div>
              <div>
                <div class="text-sm font-medium">Full Referral URL</div>
                <input
                  value={referralData.referralLink}
                  readonly
                  class="border-input placeholder:text-muted-foreground focus-visible:ring-ring mt-1 flex h-9 w-full rounded-md border bg-transparent px-3 py-1 font-mono text-xs shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50" />
              </div>
            </Card.Content>
          </Card.Root>

          <!-- Referred By -->
          {#if referralData.referredBy}
            <Card.Root>
              <Card.Header>
                <Card.Title>You Were Referred By</Card.Title>
              </Card.Header>
              <Card.Content>
                <div class="flex items-center gap-2">
                  <p class="font-medium">
                    {referralData.referredBy.name || referralData.referredBy.email}
                  </p>
                  <Badge variant="outline">Referrer</Badge>
                </div>
              </Card.Content>
            </Card.Root>
          {/if}
        </Tabs.Content>
      </Tabs.Root>
    {:else}
      <div class="flex items-center justify-center py-8">
        <div class="text-muted-foreground">Failed to load referral data</div>
      </div>
    {/if}
  </div>
</div>
