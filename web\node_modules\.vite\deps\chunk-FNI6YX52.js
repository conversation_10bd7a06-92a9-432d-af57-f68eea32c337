import {
  __commonJS
} from "./chunk-KWPVD4H7.js";

// node_modules/d3-interpolate-path/build/d3-interpolate-path.js
var require_d3_interpolate_path = __commonJS({
  "node_modules/d3-interpolate-path/build/d3-interpolate-path.js"(exports, module) {
    (function(global, factory) {
      typeof exports === "object" && typeof module !== "undefined" ? factory(exports) : typeof define === "function" && define.amd ? define(["exports"], factory) : (global = typeof globalThis !== "undefined" ? globalThis : global || self, factory(global.d3 = global.d3 || {}));
    })(exports, function(exports2) {
      "use strict";
      function ownKeys(object, enumerableOnly) {
        var keys = Object.keys(object);
        if (Object.getOwnPropertySymbols) {
          var symbols = Object.getOwnPropertySymbols(object);
          if (enumerableOnly) {
            symbols = symbols.filter(function(sym) {
              return Object.getOwnPropertyDescriptor(object, sym).enumerable;
            });
          }
          keys.push.apply(keys, symbols);
        }
        return keys;
      }
      function _objectSpread2(target) {
        for (var i = 1; i < arguments.length; i++) {
          var source = arguments[i] != null ? arguments[i] : {};
          if (i % 2) {
            ownKeys(Object(source), true).forEach(function(key) {
              _defineProperty(target, key, source[key]);
            });
          } else if (Object.getOwnPropertyDescriptors) {
            Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
          } else {
            ownKeys(Object(source)).forEach(function(key) {
              Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
            });
          }
        }
        return target;
      }
      function _typeof(obj) {
        "@babel/helpers - typeof";
        if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {
          _typeof = function(obj2) {
            return typeof obj2;
          };
        } else {
          _typeof = function(obj2) {
            return obj2 && typeof Symbol === "function" && obj2.constructor === Symbol && obj2 !== Symbol.prototype ? "symbol" : typeof obj2;
          };
        }
        return _typeof(obj);
      }
      function _defineProperty(obj, key, value) {
        if (key in obj) {
          Object.defineProperty(obj, key, {
            value,
            enumerable: true,
            configurable: true,
            writable: true
          });
        } else {
          obj[key] = value;
        }
        return obj;
      }
      function _extends() {
        _extends = Object.assign || function(target) {
          for (var i = 1; i < arguments.length; i++) {
            var source = arguments[i];
            for (var key in source) {
              if (Object.prototype.hasOwnProperty.call(source, key)) {
                target[key] = source[key];
              }
            }
          }
          return target;
        };
        return _extends.apply(this, arguments);
      }
      function _unsupportedIterableToArray(o, minLen) {
        if (!o) return;
        if (typeof o === "string") return _arrayLikeToArray(o, minLen);
        var n = Object.prototype.toString.call(o).slice(8, -1);
        if (n === "Object" && o.constructor) n = o.constructor.name;
        if (n === "Map" || n === "Set") return Array.from(o);
        if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
      }
      function _arrayLikeToArray(arr, len) {
        if (len == null || len > arr.length) len = arr.length;
        for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];
        return arr2;
      }
      function _createForOfIteratorHelper(o, allowArrayLike) {
        var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
        if (!it) {
          if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {
            if (it) o = it;
            var i = 0;
            var F = function() {
            };
            return {
              s: F,
              n: function() {
                if (i >= o.length) return {
                  done: true
                };
                return {
                  done: false,
                  value: o[i++]
                };
              },
              e: function(e) {
                throw e;
              },
              f: F
            };
          }
          throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
        }
        var normalCompletion = true, didErr = false, err;
        return {
          s: function() {
            it = it.call(o);
          },
          n: function() {
            var step = it.next();
            normalCompletion = step.done;
            return step;
          },
          e: function(e) {
            didErr = true;
            err = e;
          },
          f: function() {
            try {
              if (!normalCompletion && it.return != null) it.return();
            } finally {
              if (didErr) throw err;
            }
          }
        };
      }
      function decasteljau(points, t) {
        var left = [];
        var right = [];
        function decasteljauRecurse(points2, t2) {
          if (points2.length === 1) {
            left.push(points2[0]);
            right.push(points2[0]);
          } else {
            var newPoints = Array(points2.length - 1);
            for (var i = 0; i < newPoints.length; i++) {
              if (i === 0) {
                left.push(points2[0]);
              }
              if (i === newPoints.length - 1) {
                right.push(points2[i + 1]);
              }
              newPoints[i] = [(1 - t2) * points2[i][0] + t2 * points2[i + 1][0], (1 - t2) * points2[i][1] + t2 * points2[i + 1][1]];
            }
            decasteljauRecurse(newPoints, t2);
          }
        }
        if (points.length) {
          decasteljauRecurse(points, t);
        }
        return {
          left,
          right: right.reverse()
        };
      }
      function pointsToCommand(points) {
        var command = {};
        if (points.length === 4) {
          command.x2 = points[2][0];
          command.y2 = points[2][1];
        }
        if (points.length >= 3) {
          command.x1 = points[1][0];
          command.y1 = points[1][1];
        }
        command.x = points[points.length - 1][0];
        command.y = points[points.length - 1][1];
        if (points.length === 4) {
          command.type = "C";
        } else if (points.length === 3) {
          command.type = "Q";
        } else {
          command.type = "L";
        }
        return command;
      }
      function splitCurveAsPoints(points, segmentCount) {
        segmentCount = segmentCount || 2;
        var segments = [];
        var remainingCurve = points;
        var tIncrement = 1 / segmentCount;
        for (var i = 0; i < segmentCount - 1; i++) {
          var tRelative = tIncrement / (1 - tIncrement * i);
          var split = decasteljau(remainingCurve, tRelative);
          segments.push(split.left);
          remainingCurve = split.right;
        }
        segments.push(remainingCurve);
        return segments;
      }
      function splitCurve(commandStart, commandEnd, segmentCount) {
        var points = [[commandStart.x, commandStart.y]];
        if (commandEnd.x1 != null) {
          points.push([commandEnd.x1, commandEnd.y1]);
        }
        if (commandEnd.x2 != null) {
          points.push([commandEnd.x2, commandEnd.y2]);
        }
        points.push([commandEnd.x, commandEnd.y]);
        return splitCurveAsPoints(points, segmentCount).map(pointsToCommand);
      }
      var commandTokenRegex = /[MLCSTQAHVZmlcstqahv]|-?[\d.e+-]+/g;
      var typeMap = {
        M: ["x", "y"],
        L: ["x", "y"],
        H: ["x"],
        V: ["y"],
        C: ["x1", "y1", "x2", "y2", "x", "y"],
        S: ["x2", "y2", "x", "y"],
        Q: ["x1", "y1", "x", "y"],
        T: ["x", "y"],
        A: ["rx", "ry", "xAxisRotation", "largeArcFlag", "sweepFlag", "x", "y"],
        Z: []
      };
      Object.keys(typeMap).forEach(function(key) {
        typeMap[key.toLowerCase()] = typeMap[key];
      });
      function arrayOfLength(length, value) {
        var array = Array(length);
        for (var i = 0; i < length; i++) {
          array[i] = value;
        }
        return array;
      }
      function commandToString(command) {
        return "".concat(command.type).concat(typeMap[command.type].map(function(p) {
          return command[p];
        }).join(","));
      }
      function convertToSameType(aCommand, bCommand) {
        var conversionMap = {
          x1: "x",
          y1: "y",
          x2: "x",
          y2: "y"
        };
        var readFromBKeys = ["xAxisRotation", "largeArcFlag", "sweepFlag"];
        if (aCommand.type !== bCommand.type && bCommand.type.toUpperCase() !== "M") {
          var aConverted = {};
          Object.keys(bCommand).forEach(function(bKey) {
            var bValue = bCommand[bKey];
            var aValue = aCommand[bKey];
            if (aValue === void 0) {
              if (readFromBKeys.includes(bKey)) {
                aValue = bValue;
              } else {
                if (aValue === void 0 && conversionMap[bKey]) {
                  aValue = aCommand[conversionMap[bKey]];
                }
                if (aValue === void 0) {
                  aValue = 0;
                }
              }
            }
            aConverted[bKey] = aValue;
          });
          aConverted.type = bCommand.type;
          aCommand = aConverted;
        }
        return aCommand;
      }
      function splitSegment(commandStart, commandEnd, segmentCount) {
        var segments = [];
        if (commandEnd.type === "L" || commandEnd.type === "Q" || commandEnd.type === "C") {
          segments = segments.concat(splitCurve(commandStart, commandEnd, segmentCount));
        } else {
          var copyCommand = _extends({}, commandStart);
          if (copyCommand.type === "M") {
            copyCommand.type = "L";
          }
          segments = segments.concat(arrayOfLength(segmentCount - 1).map(function() {
            return copyCommand;
          }));
          segments.push(commandEnd);
        }
        return segments;
      }
      function extend(commandsToExtend, referenceCommands, excludeSegment) {
        var numSegmentsToExtend = commandsToExtend.length - 1;
        var numReferenceSegments = referenceCommands.length - 1;
        var segmentRatio = numSegmentsToExtend / numReferenceSegments;
        var countPointsPerSegment = arrayOfLength(numReferenceSegments).reduce(function(accum, d, i) {
          var insertIndex = Math.floor(segmentRatio * i);
          if (excludeSegment && insertIndex < commandsToExtend.length - 1 && excludeSegment(commandsToExtend[insertIndex], commandsToExtend[insertIndex + 1])) {
            var addToPriorSegment = segmentRatio * i % 1 < 0.5;
            if (accum[insertIndex]) {
              if (addToPriorSegment) {
                if (insertIndex > 0) {
                  insertIndex -= 1;
                } else if (insertIndex < commandsToExtend.length - 1) {
                  insertIndex += 1;
                }
              } else if (insertIndex < commandsToExtend.length - 1) {
                insertIndex += 1;
              } else if (insertIndex > 0) {
                insertIndex -= 1;
              }
            }
          }
          accum[insertIndex] = (accum[insertIndex] || 0) + 1;
          return accum;
        }, []);
        var extended = countPointsPerSegment.reduce(function(extended2, segmentCount, i) {
          if (i === commandsToExtend.length - 1) {
            var lastCommandCopies = arrayOfLength(segmentCount, _extends({}, commandsToExtend[commandsToExtend.length - 1]));
            if (lastCommandCopies[0].type === "M") {
              lastCommandCopies.forEach(function(d) {
                d.type = "L";
              });
            }
            return extended2.concat(lastCommandCopies);
          }
          return extended2.concat(splitSegment(commandsToExtend[i], commandsToExtend[i + 1], segmentCount));
        }, []);
        extended.unshift(commandsToExtend[0]);
        return extended;
      }
      function pathCommandsFromString(d) {
        var tokens = (d || "").match(commandTokenRegex) || [];
        var commands = [];
        var commandArgs;
        var command;
        for (var i = 0; i < tokens.length; ++i) {
          commandArgs = typeMap[tokens[i]];
          if (commandArgs) {
            command = {
              type: tokens[i]
            };
            for (var a = 0; a < commandArgs.length; ++a) {
              command[commandArgs[a]] = +tokens[i + a + 1];
            }
            i += commandArgs.length;
            commands.push(command);
          }
        }
        return commands;
      }
      function interpolatePathCommands(aCommandsInput, bCommandsInput, interpolateOptions) {
        var aCommands = aCommandsInput == null ? [] : aCommandsInput.slice();
        var bCommands = bCommandsInput == null ? [] : bCommandsInput.slice();
        var _ref = _typeof(interpolateOptions) === "object" ? interpolateOptions : {
          excludeSegment: interpolateOptions,
          snapEndsToInput: true
        }, excludeSegment = _ref.excludeSegment, snapEndsToInput = _ref.snapEndsToInput;
        if (!aCommands.length && !bCommands.length) {
          return function nullInterpolator() {
            return [];
          };
        }
        var addZ = (aCommands.length === 0 || aCommands[aCommands.length - 1].type === "Z") && (bCommands.length === 0 || bCommands[bCommands.length - 1].type === "Z");
        if (aCommands.length > 0 && aCommands[aCommands.length - 1].type === "Z") {
          aCommands.pop();
        }
        if (bCommands.length > 0 && bCommands[bCommands.length - 1].type === "Z") {
          bCommands.pop();
        }
        if (!aCommands.length) {
          aCommands.push(bCommands[0]);
        } else if (!bCommands.length) {
          bCommands.push(aCommands[0]);
        }
        var numPointsToExtend = Math.abs(bCommands.length - aCommands.length);
        if (numPointsToExtend !== 0) {
          if (bCommands.length > aCommands.length) {
            aCommands = extend(aCommands, bCommands, excludeSegment);
          } else if (bCommands.length < aCommands.length) {
            bCommands = extend(bCommands, aCommands, excludeSegment);
          }
        }
        aCommands = aCommands.map(function(aCommand, i) {
          return convertToSameType(aCommand, bCommands[i]);
        });
        var interpolatedCommands = aCommands.map(function(aCommand) {
          return _objectSpread2({}, aCommand);
        });
        if (addZ) {
          interpolatedCommands.push({
            type: "Z"
          });
          aCommands.push({
            type: "Z"
          });
        }
        return function pathCommandInterpolator(t) {
          if (t === 1 && snapEndsToInput) {
            return bCommandsInput == null ? [] : bCommandsInput;
          }
          if (t === 0) {
            return aCommands;
          }
          for (var i = 0; i < interpolatedCommands.length; ++i) {
            var aCommand = aCommands[i];
            var bCommand = bCommands[i];
            var interpolatedCommand = interpolatedCommands[i];
            var _iterator = _createForOfIteratorHelper(typeMap[interpolatedCommand.type]), _step;
            try {
              for (_iterator.s(); !(_step = _iterator.n()).done; ) {
                var arg = _step.value;
                interpolatedCommand[arg] = (1 - t) * aCommand[arg] + t * bCommand[arg];
                if (arg === "largeArcFlag" || arg === "sweepFlag") {
                  interpolatedCommand[arg] = Math.round(interpolatedCommand[arg]);
                }
              }
            } catch (err) {
              _iterator.e(err);
            } finally {
              _iterator.f();
            }
          }
          return interpolatedCommands;
        };
      }
      function interpolatePath(a, b, interpolateOptions) {
        var aCommands = pathCommandsFromString(a);
        var bCommands = pathCommandsFromString(b);
        var _ref2 = _typeof(interpolateOptions) === "object" ? interpolateOptions : {
          excludeSegment: interpolateOptions,
          snapEndsToInput: true
        }, excludeSegment = _ref2.excludeSegment, snapEndsToInput = _ref2.snapEndsToInput;
        if (!aCommands.length && !bCommands.length) {
          return function nullInterpolator() {
            return "";
          };
        }
        var commandInterpolator = interpolatePathCommands(aCommands, bCommands, {
          excludeSegment,
          snapEndsToInput
        });
        return function pathStringInterpolator(t) {
          if (t === 1 && snapEndsToInput) {
            return b == null ? "" : b;
          }
          var interpolatedCommands = commandInterpolator(t);
          var interpolatedString = "";
          var _iterator2 = _createForOfIteratorHelper(interpolatedCommands), _step2;
          try {
            for (_iterator2.s(); !(_step2 = _iterator2.n()).done; ) {
              var interpolatedCommand = _step2.value;
              interpolatedString += commandToString(interpolatedCommand);
            }
          } catch (err) {
            _iterator2.e(err);
          } finally {
            _iterator2.f();
          }
          return interpolatedString;
        };
      }
      exports2.interpolatePath = interpolatePath;
      exports2.interpolatePathCommands = interpolatePathCommands;
      exports2.pathCommandsFromString = pathCommandsFromString;
      Object.defineProperty(exports2, "__esModule", { value: true });
    });
  }
});

export {
  require_d3_interpolate_path
};
//# sourceMappingURL=chunk-FNI6YX52.js.map
