import {
  linear
} from "./chunk-J4V4NQFR.js";
import {
  Delaunay
} from "./chunk-WTISF3M3.js";
import {
  centroid_default,
  distance_default,
  interpolate_default,
  rotation_default,
  stereographic_default
} from "./chunk-QLI5KYFO.js";
import {
  extent
} from "./chunk-3VW5CGFU.js";

// node_modules/d3-geo-voronoi/src/math.js
var pi = Math.PI;
var halfPi = pi / 2;
var quarterPi = pi / 4;
var tau = pi * 2;
var degrees = 180 / pi;
var radians = pi / 180;
var atan2 = Math.atan2;
var cos = Math.cos;
var max = Math.max;
var min = Math.min;
var sin = Math.sin;
var sign = Math.sign || function(x) {
  return x > 0 ? 1 : x < 0 ? -1 : 0;
};
var sqrt = Math.sqrt;
function asin(x) {
  return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);
}

// node_modules/d3-geo-voronoi/src/cartesian.js
function cartesianDot(a, b) {
  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];
}
function cartesianCross(a, b) {
  return [
    a[1] * b[2] - a[2] * b[1],
    a[2] * b[0] - a[0] * b[2],
    a[0] * b[1] - a[1] * b[0]
  ];
}
function cartesianAdd(a, b) {
  return [a[0] + b[0], a[1] + b[1], a[2] + b[2]];
}
function cartesianNormalize(d) {
  var l = sqrt(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);
  return [d[0] / l, d[1] / l, d[2] / l];
}

// node_modules/d3-geo-voronoi/src/delaunay.js
function spherical(cartesian2) {
  return [
    atan2(cartesian2[1], cartesian2[0]) * degrees,
    asin(max(-1, min(1, cartesian2[2]))) * degrees
  ];
}
function cartesian(coordinates) {
  const lambda = coordinates[0] * radians, phi = coordinates[1] * radians, cosphi = cos(phi);
  return [cosphi * cos(lambda), cosphi * sin(lambda), sin(phi)];
}
function excess(triangle) {
  triangle = triangle.map((p) => cartesian(p));
  return cartesianDot(triangle[0], cartesianCross(triangle[2], triangle[1]));
}
function geoDelaunay(points) {
  const delaunay = geo_delaunay_from(points), triangles = geo_triangles(delaunay), edges = geo_edges(triangles, points), neighbors = geo_neighbors(triangles, points.length), find = geo_find(neighbors, points), circumcenters = geo_circumcenters(triangles, points), { polygons, centers } = geo_polygons(circumcenters, triangles, points), mesh = geo_mesh(polygons), hull = geo_hull(triangles, points), urquhart = geo_urquhart(edges, triangles);
  return {
    delaunay,
    edges,
    triangles,
    centers,
    neighbors,
    polygons,
    mesh,
    hull,
    urquhart,
    find
  };
}
function geo_find(neighbors, points) {
  function distance2(a, b) {
    let x = a[0] - b[0], y = a[1] - b[1], z = a[2] - b[2];
    return x * x + y * y + z * z;
  }
  return function find(x, y, next) {
    if (next === void 0) next = 0;
    let cell, dist, found = next;
    const xyz = cartesian([x, y]);
    do {
      cell = next;
      next = null;
      dist = distance2(xyz, cartesian(points[cell]));
      neighbors[cell].forEach((i) => {
        let ndist = distance2(xyz, cartesian(points[i]));
        if (ndist < dist) {
          dist = ndist;
          next = i;
          found = i;
          return;
        }
      });
    } while (next !== null);
    return found;
  };
}
function geo_delaunay_from(points) {
  if (points.length < 2) return {};
  let pivot = 0;
  while (isNaN(points[pivot][0] + points[pivot][1]) && pivot++ < points.length) ;
  const r = rotation_default(points[pivot]), projection = stereographic_default().translate([0, 0]).scale(1).rotate(r.invert([180, 0]));
  points = points.map(projection);
  const zeros = [];
  let max2 = 1;
  for (let i = 0, n = points.length; i < n; i++) {
    let m = points[i][0] ** 2 + points[i][1] ** 2;
    if (!isFinite(m) || m > 1e32) zeros.push(i);
    else if (m > max2) max2 = m;
  }
  const FAR = 1e6 * sqrt(max2);
  zeros.forEach((i) => points[i] = [FAR, 0]);
  points.push([0, FAR]);
  points.push([-FAR, 0]);
  points.push([0, -FAR]);
  const delaunay = Delaunay.from(points);
  delaunay.projection = projection;
  const { triangles, halfedges, inedges } = delaunay;
  const degenerate = [];
  for (let i = 0, l = halfedges.length; i < l; i++) {
    if (halfedges[i] < 0) {
      const j = i % 3 == 2 ? i - 2 : i + 1;
      const k = i % 3 == 0 ? i + 2 : i - 1;
      const a = halfedges[j];
      const b = halfedges[k];
      halfedges[a] = b;
      halfedges[b] = a;
      halfedges[j] = halfedges[k] = -1;
      triangles[i] = triangles[j] = triangles[k] = pivot;
      inedges[triangles[a]] = a % 3 == 0 ? a + 2 : a - 1;
      inedges[triangles[b]] = b % 3 == 0 ? b + 2 : b - 1;
      degenerate.push(Math.min(i, j, k));
      i += 2 - i % 3;
    } else if (triangles[i] > points.length - 3 - 1) {
      triangles[i] = pivot;
    }
  }
  return delaunay;
}
function geo_edges(triangles, points) {
  const _index = /* @__PURE__ */ new Set();
  if (points.length === 2) return [[0, 1]];
  triangles.forEach((tri) => {
    if (tri[0] === tri[1]) return;
    if (excess(tri.map((i) => points[i])) < 0) return;
    for (let i = 0, j; i < 3; i++) {
      j = (i + 1) % 3;
      _index.add(extent([tri[i], tri[j]]).join("-"));
    }
  });
  return Array.from(_index, (d) => d.split("-").map(Number));
}
function geo_triangles(delaunay) {
  const { triangles } = delaunay;
  if (!triangles) return [];
  const geo_triangles2 = [];
  for (let i = 0, n = triangles.length / 3; i < n; i++) {
    const a = triangles[3 * i], b = triangles[3 * i + 1], c = triangles[3 * i + 2];
    if (a !== b && b !== c) {
      geo_triangles2.push([a, c, b]);
    }
  }
  return geo_triangles2;
}
function geo_circumcenters(triangles, points) {
  return triangles.map((tri) => {
    const c = tri.map((i) => points[i]).map(cartesian), V = cartesianAdd(
      cartesianAdd(cartesianCross(c[1], c[0]), cartesianCross(c[2], c[1])),
      cartesianCross(c[0], c[2])
    );
    return spherical(cartesianNormalize(V));
  });
}
function geo_neighbors(triangles, npoints) {
  const neighbors = [];
  triangles.forEach((tri) => {
    for (let j = 0; j < 3; j++) {
      const a = tri[j], b = tri[(j + 1) % 3];
      neighbors[a] = neighbors[a] || [];
      neighbors[a].push(b);
    }
  });
  if (triangles.length === 0) {
    if (npoints === 2) neighbors[0] = [1], neighbors[1] = [0];
    else if (npoints === 1) neighbors[0] = [];
  }
  return neighbors;
}
function geo_polygons(circumcenters, triangles, points) {
  const polygons = [];
  const centers = circumcenters.slice();
  if (triangles.length === 0) {
    if (points.length < 2) return { polygons, centers };
    if (points.length === 2) {
      const a = cartesian(points[0]), b = cartesian(points[1]), m = cartesianNormalize(cartesianAdd(a, b)), d = cartesianNormalize(cartesianCross(a, b)), c = cartesianCross(m, d);
      const poly = [
        m,
        cartesianCross(m, c),
        cartesianCross(cartesianCross(m, c), c),
        cartesianCross(cartesianCross(cartesianCross(m, c), c), c)
      ].map(spherical).map(supplement);
      return polygons.push(poly), polygons.push(poly.slice().reverse()), { polygons, centers };
    }
  }
  triangles.forEach((tri, t) => {
    for (let j = 0; j < 3; j++) {
      const a = tri[j], b = tri[(j + 1) % 3], c = tri[(j + 2) % 3];
      polygons[a] = polygons[a] || [];
      polygons[a].push([b, c, t, [a, b, c]]);
    }
  });
  const reordered = polygons.map((poly) => {
    const p = [poly[0][2]];
    let k = poly[0][1];
    for (let i = 1; i < poly.length; i++) {
      for (let j = 0; j < poly.length; j++) {
        if (poly[j][0] == k) {
          k = poly[j][1];
          p.push(poly[j][2]);
          break;
        }
      }
    }
    if (p.length > 2) {
      return p;
    } else if (p.length == 2) {
      const R0 = o_midpoint(
        points[poly[0][3][0]],
        points[poly[0][3][1]],
        centers[p[0]]
      ), R1 = o_midpoint(
        points[poly[0][3][2]],
        points[poly[0][3][0]],
        centers[p[0]]
      );
      const i0 = supplement(R0), i1 = supplement(R1);
      return [p[0], i1, p[1], i0];
    }
  });
  function supplement(point) {
    let f = -1;
    centers.slice(triangles.length, Infinity).forEach((p, i) => {
      if (p[0] === point[0] && p[1] === point[1]) f = i + triangles.length;
    });
    if (f < 0) f = centers.length, centers.push(point);
    return f;
  }
  return { polygons: reordered, centers };
}
function o_midpoint(a, b, c) {
  a = cartesian(a);
  b = cartesian(b);
  c = cartesian(c);
  const s = sign(cartesianDot(cartesianCross(b, a), c));
  return spherical(cartesianNormalize(cartesianAdd(a, b)).map((d) => s * d));
}
function geo_mesh(polygons) {
  const mesh = [];
  polygons.forEach((poly) => {
    if (!poly) return;
    let p = poly[poly.length - 1];
    for (let q of poly) {
      if (q > p) mesh.push([p, q]);
      p = q;
    }
  });
  return mesh;
}
function geo_urquhart(edges, triangles) {
  return function(distances) {
    const _lengths = /* @__PURE__ */ new Map(), _urquhart = /* @__PURE__ */ new Map();
    edges.forEach((edge, i) => {
      const u = edge.join("-");
      _lengths.set(u, distances[i]);
      _urquhart.set(u, true);
    });
    triangles.forEach((tri) => {
      let l = 0, remove = -1;
      for (let j = 0; j < 3; j++) {
        let u = extent([tri[j], tri[(j + 1) % 3]]).join("-");
        if (_lengths.get(u) > l) {
          l = _lengths.get(u);
          remove = u;
        }
      }
      _urquhart.set(remove, false);
    });
    return edges.map((edge) => _urquhart.get(edge.join("-")));
  };
}
function geo_hull(triangles, points) {
  const _hull = /* @__PURE__ */ new Set(), hull = [];
  triangles.map((tri) => {
    if (excess(tri.map((i) => points[i > points.length ? 0 : i])) > 1e-12)
      return;
    for (let i = 0; i < 3; i++) {
      let e = [tri[i], tri[(i + 1) % 3]], code = `${e[0]}-${e[1]}`;
      if (_hull.has(code)) _hull.delete(code);
      else _hull.add(`${e[1]}-${e[0]}`);
    }
  });
  const _index = /* @__PURE__ */ new Map();
  let start;
  _hull.forEach((e) => {
    e = e.split("-").map(Number);
    _index.set(e[0], e[1]);
    start = e[0];
  });
  if (start === void 0) return hull;
  let next = start;
  do {
    hull.push(next);
    let n = _index.get(next);
    _index.set(next, -1);
    next = n;
  } while (next > -1 && next !== start);
  return hull;
}

// node_modules/d3-geo-voronoi/src/voronoi.js
function geoVoronoi(data) {
  const v = function(data2) {
    v.delaunay = null;
    v._data = data2;
    if (typeof v._data === "object" && v._data.type === "FeatureCollection") {
      v._data = v._data.features;
    }
    if (typeof v._data === "object") {
      const temp = v._data.map((d) => [v._vx(d), v._vy(d), d]).filter((d) => isFinite(d[0] + d[1]));
      v.points = temp.map((d) => [d[0], d[1]]);
      v.valid = temp.map((d) => d[2]);
      v.delaunay = geoDelaunay(v.points);
    }
    return v;
  };
  v._vx = function(d) {
    if (typeof d == "object" && "type" in d) {
      return centroid_default(d)[0];
    }
    if (0 in d) return d[0];
  };
  v._vy = function(d) {
    if (typeof d == "object" && "type" in d) {
      return centroid_default(d)[1];
    }
    if (1 in d) return d[1];
  };
  v.x = function(f) {
    if (!f) return v._vx;
    v._vx = f;
    return v;
  };
  v.y = function(f) {
    if (!f) return v._vy;
    v._vy = f;
    return v;
  };
  v.polygons = function(data2) {
    if (data2 !== void 0) {
      v(data2);
    }
    if (!v.delaunay) return false;
    const coll = {
      type: "FeatureCollection",
      features: []
    };
    if (v.valid.length === 0) return coll;
    v.delaunay.polygons.forEach(
      (poly, i) => coll.features.push({
        type: "Feature",
        geometry: !poly ? null : {
          type: "Polygon",
          coordinates: [
            [...poly, poly[0]].map((i2) => v.delaunay.centers[i2])
          ]
        },
        properties: {
          site: v.valid[i],
          sitecoordinates: v.points[i],
          neighbours: v.delaunay.neighbors[i]
          // not part of the public API
        }
      })
    );
    if (v.valid.length === 1)
      coll.features.push({
        type: "Feature",
        geometry: { type: "Sphere" },
        properties: {
          site: v.valid[0],
          sitecoordinates: v.points[0],
          neighbours: []
        }
      });
    return coll;
  };
  v.triangles = function(data2) {
    if (data2 !== void 0) {
      v(data2);
    }
    if (!v.delaunay) return false;
    return {
      type: "FeatureCollection",
      features: v.delaunay.triangles.map((tri, index) => {
        tri = tri.map((i) => v.points[i]);
        tri.center = v.delaunay.centers[index];
        return tri;
      }).filter((tri) => excess(tri) > 0).map((tri) => ({
        type: "Feature",
        properties: {
          circumcenter: tri.center
        },
        geometry: {
          type: "Polygon",
          coordinates: [[...tri, tri[0]]]
        }
      }))
    };
  };
  v.links = function(data2) {
    if (data2 !== void 0) {
      v(data2);
    }
    if (!v.delaunay) return false;
    const _distances = v.delaunay.edges.map(
      (e) => distance_default(v.points[e[0]], v.points[e[1]])
    ), _urquart = v.delaunay.urquhart(_distances);
    return {
      type: "FeatureCollection",
      features: v.delaunay.edges.map((e, i) => ({
        type: "Feature",
        properties: {
          source: v.valid[e[0]],
          target: v.valid[e[1]],
          length: _distances[i],
          urquhart: !!_urquart[i]
        },
        geometry: {
          type: "LineString",
          coordinates: [v.points[e[0]], v.points[e[1]]]
        }
      }))
    };
  };
  v.mesh = function(data2) {
    if (data2 !== void 0) {
      v(data2);
    }
    if (!v.delaunay) return false;
    return {
      type: "MultiLineString",
      coordinates: v.delaunay.edges.map((e) => [
        v.points[e[0]],
        v.points[e[1]]
      ])
    };
  };
  v.cellMesh = function(data2) {
    if (data2 !== void 0) {
      v(data2);
    }
    if (!v.delaunay) return false;
    const { centers, polygons } = v.delaunay;
    const coordinates = [];
    for (const p of polygons) {
      if (!p) continue;
      for (let n = p.length, p0 = p[n - 1], p1 = p[0], i = 0; i < n; p0 = p1, p1 = p[++i]) {
        if (p1 > p0) {
          coordinates.push([centers[p0], centers[p1]]);
        }
      }
    }
    return {
      type: "MultiLineString",
      coordinates
    };
  };
  v._found = void 0;
  v.find = function(x, y, radius) {
    v._found = v.delaunay.find(x, y, v._found);
    if (!radius || distance_default([x, y], v.points[v._found]) < radius)
      return v._found;
  };
  v.hull = function(data2) {
    if (data2 !== void 0) {
      v(data2);
    }
    const hull = v.delaunay.hull, points = v.points;
    return hull.length === 0 ? null : {
      type: "Polygon",
      coordinates: [[...hull.map((i) => points[i]), points[hull[0]]]]
    };
  };
  return data ? v(data) : v;
}

// node_modules/d3-tricontour/src/extent.js
function extent_default(values) {
  let min2, max2;
  for (const value of values) {
    if (value != null) {
      if (min2 === void 0) {
        if (value >= value) min2 = max2 = value;
      } else {
        if (min2 > value) min2 = value;
        if (max2 < value) max2 = value;
      }
    }
  }
  return [min2, max2];
}

// node_modules/d3-tricontour/src/merge.js
function* flatten(arrays) {
  for (const array of arrays) {
    yield* array;
  }
}
function merge_default(arrays) {
  return Array.from(flatten(arrays));
}

// node_modules/d3-tricontour/src/contains.js
function contains_default(ring, hole) {
  const n = hole.length;
  let i = -1;
  while (++i < n) {
    const c = ringContains(ring, hole[i]);
    if (c) return c;
  }
  return 0;
}
function ringContains(ring, point) {
  let x = point[0], y = point[1], contains = -1;
  for (let i = 0, n = ring.length, j = n - 1; i < n; j = i++) {
    const pi2 = ring[i], xi = pi2[0], yi = pi2[1], pj = ring[j], xj = pj[0], yj = pj[1];
    if (segmentContains(pi2, pj, point)) return 0;
    if (yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi) contains = -contains;
  }
  return contains;
}
function segmentContains(a, b, c) {
  let i;
  return collinear(a, b, c) && within(a[i = +(a[0] === b[0])], c[i], b[i]);
}
function collinear(a, b, c) {
  return (b[0] - a[0]) * (c[1] - a[1]) === (c[0] - a[0]) * (b[1] - a[1]);
}
function within(p, q, r) {
  return p <= q && q <= r || r <= q && q <= p;
}

// node_modules/d3-tricontour/src/area.js
function area_default(ring) {
  let i = 0, n = ring.length, area = ring[n - 1][1] * ring[0][0] - ring[n - 1][0] * ring[0][1];
  while (++i < n) area += ring[i - 1][1] * ring[i][0] - ring[i - 1][0] * ring[i][1];
  return area;
}

// node_modules/d3-tricontour/src/ringsort.js
function ringsort_default(rings) {
  const polygons = [];
  const holes = [];
  for (const ring of rings) {
    if (area_default(ring) > 0) polygons.push([ring]);
    else holes.push(ring);
  }
  holes.forEach(function(hole) {
    for (let i = 0, n = polygons.length, polygon; i < n; ++i) {
      if (contains_default((polygon = polygons[i])[0], hole) !== -1) {
        polygon.push(hole);
        return;
      }
    }
  });
  return polygons;
}

// node_modules/d3-tricontour/src/tricontour.js
function tricontour_default() {
  let x = (d) => d[0], y = (d) => d[1], value = (d) => isFinite(+d[2]) ? +d[2] : 0, triangulate = Delaunay.from, pointInterpolate = (i, j, a) => {
    const { points } = triangulation;
    const A = [points[2 * i], points[2 * i + 1]], B = [points[2 * j], points[2 * j + 1]];
    return [a * B[0] + (1 - a) * A[0], a * B[1] + (1 - a) * A[1]];
  }, ringsort = ringsort_default;
  let thresholds, values, triangulation;
  function init(points) {
    triangulation = triangulate(points, x, y);
    values = Array.from(points, value);
    if (typeof thresholds !== "object") {
      thresholds = linear().domain(extent_default(values)).nice().ticks(thresholds);
    }
  }
  function* tricontours(points) {
    init(points);
    for (const threshold of thresholds) {
      const polygon = tricontour(triangulation, values, threshold);
      yield {
        type: "MultiPolygon",
        coordinates: polygon,
        value: threshold
      };
    }
  }
  function contour(points, threshold) {
    init(points);
    return {
      type: "MultiPolygon",
      coordinates: tricontour(triangulation, values, threshold),
      value: threshold
    };
  }
  function* isobands(points) {
    init(points);
    let p0, p1, th0;
    for (const th of thresholds) {
      if (p1) p0 = p1;
      p1 = merge_default(tricontour(triangulation, values, th));
      if (p0) {
        yield {
          type: "MultiPolygon",
          coordinates: ringsort(
            p0.concat(p1.map((ring) => ring.slice().reverse()))
          ),
          value: th0,
          valueMax: th
        };
      }
      th0 = th;
    }
  }
  const contours = function(data) {
    return [...tricontours(data)];
  };
  contours.x = (_) => _ ? (x = _, contours) : x;
  contours.y = (_) => _ ? (y = _, contours) : y;
  contours.value = (_) => _ ? (value = _, contours) : value;
  contours.thresholds = (_) => _ ? (thresholds = _, contours) : thresholds;
  contours.triangulate = (_) => _ ? (triangulate = _, contours) : triangulate;
  contours.pointInterpolate = (_) => _ ? (pointInterpolate = _, contours) : pointInterpolate;
  contours.ringsort = (_) => _ ? (ringsort = _, contours) : ringsort;
  contours.contours = tricontours;
  contours.contour = contour;
  contours.isobands = isobands;
  contours._values = () => values;
  contours._triangulation = () => triangulation;
  return contours;
  function next(i) {
    return i % 3 === 2 ? i - 2 : i + 1;
  }
  function prev(i) {
    return i % 3 === 0 ? i + 2 : i - 1;
  }
  function tricontour(triangulation2, values2, v0 = 0) {
    for (const d of values2) if (!isFinite(d)) throw ["Invalid value", d];
    const { halfedges, hull, inedges, triangles } = triangulation2, n = values2.length;
    function edgealpha(i2) {
      return alpha(triangles[i2], triangles[next(i2)]);
    }
    function alpha(i2, j2) {
      const u = values2[i2], v = values2[j2];
      if (u <= v0 && v >= v0 && u < v) {
        return (v0 - u) / (v - u);
      }
    }
    const rings = [], visited = new Uint8Array(halfedges.length).fill(0);
    let path, i, j, k, a;
    for (k = 0; k < halfedges.length; k++) {
      if (visited[k]) continue;
      i = k;
      path = [];
      while ((a = edgealpha(i)) > 0) {
        const [ti, tj] = [triangles[i], triangles[j = next(i)]];
        if (path.length && (ti === path[0].ti && tj === path[0].tj) || path.length > 2 * n)
          break;
        visited[i] = 1;
        path.push({ ti, tj, a });
        if ((j = halfedges[i]) > -1) {
          if (edgealpha(j = next(j)) > 0) {
            i = j;
            continue;
          }
          if (edgealpha(j = next(j)) > 0) {
            i = j;
            continue;
          }
        } else {
          let h = (hull.indexOf(triangles[i]) + 1) % hull.length;
          while (values2[hull[h]] < v0) {
            h = (h + 1) % hull.length;
          }
          while (values2[hull[h]] >= v0) {
            path.push({ ti: hull[h], tj: hull[h], a: 0 });
            h = (h + 1) % hull.length;
          }
          j = inedges[hull[h]];
          path.push({
            ti: hull[h],
            tj: triangles[j],
            a: alpha(hull[h], triangles[j])
          });
          if (edgealpha(i = next(j)) > 0) continue;
          if (edgealpha(i = prev(j)) > 0) continue;
        }
      }
      if (path.length) {
        path.push(path[0]);
        rings.push(path.map(({ ti, tj, a: a2 }) => pointInterpolate(ti, tj, a2)));
      }
    }
    if (hull.every((d) => values2[d] >= v0)) {
      rings.unshift(
        Array.from(hull).concat([hull[0]]).map((i2) => pointInterpolate(i2, i2, 0))
      );
    }
    return ringsort(rings);
  }
}

// node_modules/d3-geo-voronoi/src/contour.js
function geoContour() {
  let v;
  const contour = tricontour_default().triangulate((data, x, y) => {
    v = geoDelaunay(data.map((d, i) => [x(d, i), y(d, i)]));
    return v.delaunay;
  }).pointInterpolate((i, j, a) => {
    const { points, projection } = v.delaunay;
    const A = projection.invert([points[2 * i], points[2 * i + 1]]), B = projection.invert([points[2 * j], points[2 * j + 1]]);
    return interpolate_default(A, B)(a);
  }).ringsort((rings) => {
    if (rings.length && !rings[0].reversed) {
      rings.forEach((ring) => ring.reverse());
      rings[0].reversed = true;
    }
    return [rings];
  });
  return contour;
}

export {
  geoDelaunay,
  geoVoronoi,
  geoContour
};
//# sourceMappingURL=chunk-NPKFOH5Y.js.map
