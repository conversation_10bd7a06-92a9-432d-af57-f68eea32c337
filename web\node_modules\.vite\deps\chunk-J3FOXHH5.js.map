{"version": 3, "sources": ["../../d3-time/src/interval.js", "../../d3-time/src/millisecond.js", "../../d3-time/src/duration.js", "../../d3-time/src/second.js", "../../d3-time/src/minute.js", "../../d3-time/src/hour.js", "../../d3-time/src/day.js", "../../d3-time/src/week.js", "../../d3-time/src/month.js", "../../d3-time/src/year.js", "../../d3-time/src/ticks.js"], "sourcesContent": ["const t0 = new Date, t1 = new Date;\n\nexport function timeInterval(floori, offseti, count, field) {\n\n  function interval(date) {\n    return floori(date = arguments.length === 0 ? new Date : new Date(+date)), date;\n  }\n\n  interval.floor = (date) => {\n    return floori(date = new Date(+date)), date;\n  };\n\n  interval.ceil = (date) => {\n    return floori(date = new Date(date - 1)), offseti(date, 1), floori(date), date;\n  };\n\n  interval.round = (date) => {\n    const d0 = interval(date), d1 = interval.ceil(date);\n    return date - d0 < d1 - date ? d0 : d1;\n  };\n\n  interval.offset = (date, step) => {\n    return offseti(date = new Date(+date), step == null ? 1 : Math.floor(step)), date;\n  };\n\n  interval.range = (start, stop, step) => {\n    const range = [];\n    start = interval.ceil(start);\n    step = step == null ? 1 : Math.floor(step);\n    if (!(start < stop) || !(step > 0)) return range; // also handles Invalid Date\n    let previous;\n    do range.push(previous = new Date(+start)), offseti(start, step), floori(start);\n    while (previous < start && start < stop);\n    return range;\n  };\n\n  interval.filter = (test) => {\n    return timeInterval((date) => {\n      if (date >= date) while (floori(date), !test(date)) date.setTime(date - 1);\n    }, (date, step) => {\n      if (date >= date) {\n        if (step < 0) while (++step <= 0) {\n          while (offseti(date, -1), !test(date)) {} // eslint-disable-line no-empty\n        } else while (--step >= 0) {\n          while (offseti(date, +1), !test(date)) {} // eslint-disable-line no-empty\n        }\n      }\n    });\n  };\n\n  if (count) {\n    interval.count = (start, end) => {\n      t0.setTime(+start), t1.setTime(+end);\n      floori(t0), floori(t1);\n      return Math.floor(count(t0, t1));\n    };\n\n    interval.every = (step) => {\n      step = Math.floor(step);\n      return !isFinite(step) || !(step > 0) ? null\n          : !(step > 1) ? interval\n          : interval.filter(field\n              ? (d) => field(d) % step === 0\n              : (d) => interval.count(0, d) % step === 0);\n    };\n  }\n\n  return interval;\n}\n", "import {timeInterval} from \"./interval.js\";\n\nexport const millisecond = timeInterval(() => {\n  // noop\n}, (date, step) => {\n  date.setTime(+date + step);\n}, (start, end) => {\n  return end - start;\n});\n\n// An optimized implementation for this simple case.\nmillisecond.every = (k) => {\n  k = Math.floor(k);\n  if (!isFinite(k) || !(k > 0)) return null;\n  if (!(k > 1)) return millisecond;\n  return timeInterval((date) => {\n    date.setTime(Math.floor(date / k) * k);\n  }, (date, step) => {\n    date.setTime(+date + step * k);\n  }, (start, end) => {\n    return (end - start) / k;\n  });\n};\n\nexport const milliseconds = millisecond.range;\n", "export const durationSecond = 1000;\nexport const durationMinute = durationSecond * 60;\nexport const durationHour = durationMinute * 60;\nexport const durationDay = durationHour * 24;\nexport const durationWeek = durationDay * 7;\nexport const durationMonth = durationDay * 30;\nexport const durationYear = durationDay * 365;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationSecond} from \"./duration.js\";\n\nexport const second = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds());\n}, (date, step) => {\n  date.setTime(+date + step * durationSecond);\n}, (start, end) => {\n  return (end - start) / durationSecond;\n}, (date) => {\n  return date.getUTCSeconds();\n});\n\nexport const seconds = second.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationMinute, durationSecond} from \"./duration.js\";\n\nexport const timeMinute = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, (date) => {\n  return date.getMinutes();\n});\n\nexport const timeMinutes = timeMinute.range;\n\nexport const utcMinute = timeInterval((date) => {\n  date.setUTCSeconds(0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationMinute);\n}, (start, end) => {\n  return (end - start) / durationMinute;\n}, (date) => {\n  return date.getUTCMinutes();\n});\n\nexport const utcMinutes = utcMinute.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationHour, durationMinute, durationSecond} from \"./duration.js\";\n\nexport const timeHour = timeInterval((date) => {\n  date.setTime(date - date.getMilliseconds() - date.getSeconds() * durationSecond - date.getMinutes() * durationMinute);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, (date) => {\n  return date.getHours();\n});\n\nexport const timeHours = timeHour.range;\n\nexport const utcHour = timeInterval((date) => {\n  date.setUTCMinutes(0, 0, 0);\n}, (date, step) => {\n  date.setTime(+date + step * durationHour);\n}, (start, end) => {\n  return (end - start) / durationHour;\n}, (date) => {\n  return date.getUTCHours();\n});\n\nexport const utcHours = utcHour.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationDay, durationMinute} from \"./duration.js\";\n\nexport const timeDay = timeInterval(\n  date => date.setHours(0, 0, 0, 0),\n  (date, step) => date.setDate(date.getDate() + step),\n  (start, end) => (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationDay,\n  date => date.getDate() - 1\n);\n\nexport const timeDays = timeDay.range;\n\nexport const utcDay = timeInterval((date) => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, (date) => {\n  return date.getUTCDate() - 1;\n});\n\nexport const utcDays = utcDay.range;\n\nexport const unixDay = timeInterval((date) => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, (date) => {\n  return Math.floor(date / durationDay);\n});\n\nexport const unixDays = unixDay.range;\n", "import {timeInterval} from \"./interval.js\";\nimport {durationMinute, durationWeek} from \"./duration.js\";\n\nfunction timeWeekday(i) {\n  return timeInterval((date) => {\n    date.setDate(date.getDate() - (date.getDay() + 7 - i) % 7);\n    date.setHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setDate(date.getDate() + step * 7);\n  }, (start, end) => {\n    return (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationWeek;\n  });\n}\n\nexport const timeSunday = timeWeekday(0);\nexport const timeMonday = timeWeekday(1);\nexport const timeTuesday = timeWeekday(2);\nexport const timeWednesday = timeWeekday(3);\nexport const timeThursday = timeWeekday(4);\nexport const timeFriday = timeWeekday(5);\nexport const timeSaturday = timeWeekday(6);\n\nexport const timeSundays = timeSunday.range;\nexport const timeMondays = timeMonday.range;\nexport const timeTuesdays = timeTuesday.range;\nexport const timeWednesdays = timeWednesday.range;\nexport const timeThursdays = timeThursday.range;\nexport const timeFridays = timeFriday.range;\nexport const timeSaturdays = timeSaturday.range;\n\nfunction utcWeekday(i) {\n  return timeInterval((date) => {\n    date.setUTCDate(date.getUTCDate() - (date.getUTCDay() + 7 - i) % 7);\n    date.setUTCHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setUTCDate(date.getUTCDate() + step * 7);\n  }, (start, end) => {\n    return (end - start) / durationWeek;\n  });\n}\n\nexport const utcSunday = utcWeekday(0);\nexport const utcMonday = utcWeekday(1);\nexport const utcTuesday = utcWeekday(2);\nexport const utcWednesday = utcWeekday(3);\nexport const utcThursday = utcWeekday(4);\nexport const utcFriday = utcWeekday(5);\nexport const utcSaturday = utcWeekday(6);\n\nexport const utcSundays = utcSunday.range;\nexport const utcMondays = utcMonday.range;\nexport const utcTuesdays = utcTuesday.range;\nexport const utcWednesdays = utcWednesday.range;\nexport const utcThursdays = utcThursday.range;\nexport const utcFridays = utcFriday.range;\nexport const utcSaturdays = utcSaturday.range;\n", "import {timeInterval} from \"./interval.js\";\n\nexport const timeMonth = timeInterval((date) => {\n  date.setDate(1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setMonth(date.getMonth() + step);\n}, (start, end) => {\n  return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;\n}, (date) => {\n  return date.getMonth();\n});\n\nexport const timeMonths = timeMonth.range;\n\nexport const utcMonth = timeInterval((date) => {\n  date.setUTCDate(1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCMonth(date.getUTCMonth() + step);\n}, (start, end) => {\n  return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;\n}, (date) => {\n  return date.getUTCMonth();\n});\n\nexport const utcMonths = utcMonth.range;\n", "import {timeInterval} from \"./interval.js\";\n\nexport const timeYear = timeInterval((date) => {\n  date.setMonth(0, 1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setFullYear(date.getFullYear() + step);\n}, (start, end) => {\n  return end.getFullYear() - start.getFullYear();\n}, (date) => {\n  return date.getFullYear();\n});\n\n// An optimized implementation for this simple case.\ntimeYear.every = (k) => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval((date) => {\n    date.setFullYear(Math.floor(date.getFullYear() / k) * k);\n    date.setMonth(0, 1);\n    date.setHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setFullYear(date.getFullYear() + step * k);\n  });\n};\n\nexport const timeYears = timeYear.range;\n\nexport const utcYear = timeInterval((date) => {\n  date.setUTCMonth(0, 1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCFullYear(date.getUTCFullYear() + step);\n}, (start, end) => {\n  return end.getUTCFullYear() - start.getUTCFullYear();\n}, (date) => {\n  return date.getUTCFullYear();\n});\n\n// An optimized implementation for this simple case.\nutcYear.every = (k) => {\n  return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : timeInterval((date) => {\n    date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);\n    date.setUTCMonth(0, 1);\n    date.setUTCHours(0, 0, 0, 0);\n  }, (date, step) => {\n    date.setUTCFullYear(date.getUTCFullYear() + step * k);\n  });\n};\n\nexport const utcYears = utcYear.range;\n", "import {bisector, tickStep} from \"d3-array\";\nimport {durationDay, durationHour, durationMinute, durationMonth, durationSecond, durationWeek, durationYear} from \"./duration.js\";\nimport {millisecond} from \"./millisecond.js\";\nimport {second} from \"./second.js\";\nimport {timeMinute, utcMinute} from \"./minute.js\";\nimport {timeHour, utcHour} from \"./hour.js\";\nimport {timeDay, unixDay} from \"./day.js\";\nimport {timeSunday, utcSunday} from \"./week.js\";\nimport {timeMonth, utcMonth} from \"./month.js\";\nimport {timeYear, utcYear} from \"./year.js\";\n\nfunction ticker(year, month, week, day, hour, minute) {\n\n  const tickIntervals = [\n    [second,  1,      durationSecond],\n    [second,  5,  5 * durationSecond],\n    [second, 15, 15 * durationSecond],\n    [second, 30, 30 * durationSecond],\n    [minute,  1,      durationMinute],\n    [minute,  5,  5 * durationMinute],\n    [minute, 15, 15 * durationMinute],\n    [minute, 30, 30 * durationMinute],\n    [  hour,  1,      durationHour  ],\n    [  hour,  3,  3 * durationHour  ],\n    [  hour,  6,  6 * durationHour  ],\n    [  hour, 12, 12 * durationHour  ],\n    [   day,  1,      durationDay   ],\n    [   day,  2,  2 * durationDay   ],\n    [  week,  1,      durationWeek  ],\n    [ month,  1,      durationMonth ],\n    [ month,  3,  3 * durationMonth ],\n    [  year,  1,      durationYear  ]\n  ];\n\n  function ticks(start, stop, count) {\n    const reverse = stop < start;\n    if (reverse) [start, stop] = [stop, start];\n    const interval = count && typeof count.range === \"function\" ? count : tickInterval(start, stop, count);\n    const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop\n    return reverse ? ticks.reverse() : ticks;\n  }\n\n  function tickInterval(start, stop, count) {\n    const target = Math.abs(stop - start) / count;\n    const i = bisector(([,, step]) => step).right(tickIntervals, target);\n    if (i === tickIntervals.length) return year.every(tickStep(start / durationYear, stop / durationYear, count));\n    if (i === 0) return millisecond.every(Math.max(tickStep(start, stop, count), 1));\n    const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n    return t.every(step);\n  }\n\n  return [ticks, tickInterval];\n}\n\nconst [utcTicks, utcTickInterval] = ticker(utcYear, utcMonth, utcSunday, unixDay, utcHour, utcMinute);\nconst [timeTicks, timeTickInterval] = ticker(timeYear, timeMonth, timeSunday, timeDay, timeHour, timeMinute);\n\nexport {utcTicks, utcTickInterval, timeTicks, timeTickInterval};\n"], "mappings": ";;;;;;AAAA,IAAM,KAAK,oBAAI;AAAf,IAAqB,KAAK,oBAAI;AAEvB,SAAS,aAAa,QAAQ,SAAS,OAAO,OAAO;AAE1D,WAAS,SAAS,MAAM;AACtB,WAAO,OAAO,OAAO,UAAU,WAAW,IAAI,oBAAI,SAAO,oBAAI,KAAK,CAAC,IAAI,CAAC,GAAG;AAAA,EAC7E;AAEA,WAAS,QAAQ,CAAC,SAAS;AACzB,WAAO,OAAO,OAAO,oBAAI,KAAK,CAAC,IAAI,CAAC,GAAG;AAAA,EACzC;AAEA,WAAS,OAAO,CAAC,SAAS;AACxB,WAAO,OAAO,OAAO,IAAI,KAAK,OAAO,CAAC,CAAC,GAAG,QAAQ,MAAM,CAAC,GAAG,OAAO,IAAI,GAAG;AAAA,EAC5E;AAEA,WAAS,QAAQ,CAAC,SAAS;AACzB,UAAM,KAAK,SAAS,IAAI,GAAG,KAAK,SAAS,KAAK,IAAI;AAClD,WAAO,OAAO,KAAK,KAAK,OAAO,KAAK;AAAA,EACtC;AAEA,WAAS,SAAS,CAAC,MAAM,SAAS;AAChC,WAAO,QAAQ,OAAO,oBAAI,KAAK,CAAC,IAAI,GAAG,QAAQ,OAAO,IAAI,KAAK,MAAM,IAAI,CAAC,GAAG;AAAA,EAC/E;AAEA,WAAS,QAAQ,CAAC,OAAO,MAAM,SAAS;AACtC,UAAM,QAAQ,CAAC;AACf,YAAQ,SAAS,KAAK,KAAK;AAC3B,WAAO,QAAQ,OAAO,IAAI,KAAK,MAAM,IAAI;AACzC,QAAI,EAAE,QAAQ,SAAS,EAAE,OAAO,GAAI,QAAO;AAC3C,QAAI;AACJ;AAAG,YAAM,KAAK,WAAW,oBAAI,KAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,OAAO,IAAI,GAAG,OAAO,KAAK;AAAA,WACvE,WAAW,SAAS,QAAQ;AACnC,WAAO;AAAA,EACT;AAEA,WAAS,SAAS,CAAC,SAAS;AAC1B,WAAO,aAAa,CAAC,SAAS;AAC5B,UAAI,QAAQ,KAAM,QAAO,OAAO,IAAI,GAAG,CAAC,KAAK,IAAI,EAAG,MAAK,QAAQ,OAAO,CAAC;AAAA,IAC3E,GAAG,CAAC,MAAM,SAAS;AACjB,UAAI,QAAQ,MAAM;AAChB,YAAI,OAAO,EAAG,QAAO,EAAE,QAAQ,GAAG;AAChC,iBAAO,QAAQ,MAAM,EAAE,GAAG,CAAC,KAAK,IAAI,GAAG;AAAA,UAAC;AAAA,QAC1C;AAAA,YAAO,QAAO,EAAE,QAAQ,GAAG;AACzB,iBAAO,QAAQ,MAAM,CAAE,GAAG,CAAC,KAAK,IAAI,GAAG;AAAA,UAAC;AAAA,QAC1C;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,OAAO;AACT,aAAS,QAAQ,CAAC,OAAO,QAAQ;AAC/B,SAAG,QAAQ,CAAC,KAAK,GAAG,GAAG,QAAQ,CAAC,GAAG;AACnC,aAAO,EAAE,GAAG,OAAO,EAAE;AACrB,aAAO,KAAK,MAAM,MAAM,IAAI,EAAE,CAAC;AAAA,IACjC;AAEA,aAAS,QAAQ,CAAC,SAAS;AACzB,aAAO,KAAK,MAAM,IAAI;AACtB,aAAO,CAAC,SAAS,IAAI,KAAK,EAAE,OAAO,KAAK,OAClC,EAAE,OAAO,KAAK,WACd,SAAS,OAAO,QACZ,CAAC,MAAM,MAAM,CAAC,IAAI,SAAS,IAC3B,CAAC,MAAM,SAAS,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC;AAAA,IACpD;AAAA,EACF;AAEA,SAAO;AACT;;;AClEO,IAAM,cAAc,aAAa,MAAM;AAE9C,GAAG,CAAC,MAAM,SAAS;AACjB,OAAK,QAAQ,CAAC,OAAO,IAAI;AAC3B,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,MAAM;AACf,CAAC;AAGD,YAAY,QAAQ,CAAC,MAAM;AACzB,MAAI,KAAK,MAAM,CAAC;AAChB,MAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,GAAI,QAAO;AACrC,MAAI,EAAE,IAAI,GAAI,QAAO;AACrB,SAAO,aAAa,CAAC,SAAS;AAC5B,SAAK,QAAQ,KAAK,MAAM,OAAO,CAAC,IAAI,CAAC;AAAA,EACvC,GAAG,CAAC,MAAM,SAAS;AACjB,SAAK,QAAQ,CAAC,OAAO,OAAO,CAAC;AAAA,EAC/B,GAAG,CAAC,OAAO,QAAQ;AACjB,YAAQ,MAAM,SAAS;AAAA,EACzB,CAAC;AACH;AAEO,IAAM,eAAe,YAAY;;;ACxBjC,IAAM,iBAAiB;AACvB,IAAM,iBAAiB,iBAAiB;AACxC,IAAM,eAAe,iBAAiB;AACtC,IAAM,cAAc,eAAe;AACnC,IAAM,eAAe,cAAc;AACnC,IAAM,gBAAgB,cAAc;AACpC,IAAM,eAAe,cAAc;;;ACHnC,IAAM,SAAS,aAAa,CAAC,SAAS;AAC3C,OAAK,QAAQ,OAAO,KAAK,gBAAgB,CAAC;AAC5C,GAAG,CAAC,MAAM,SAAS;AACjB,OAAK,QAAQ,CAAC,OAAO,OAAO,cAAc;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAAC,SAAS;AACX,SAAO,KAAK,cAAc;AAC5B,CAAC;AAEM,IAAM,UAAU,OAAO;;;ACVvB,IAAM,aAAa,aAAa,CAAC,SAAS;AAC/C,OAAK,QAAQ,OAAO,KAAK,gBAAgB,IAAI,KAAK,WAAW,IAAI,cAAc;AACjF,GAAG,CAAC,MAAM,SAAS;AACjB,OAAK,QAAQ,CAAC,OAAO,OAAO,cAAc;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAAC,SAAS;AACX,SAAO,KAAK,WAAW;AACzB,CAAC;AAEM,IAAM,cAAc,WAAW;AAE/B,IAAM,YAAY,aAAa,CAAC,SAAS;AAC9C,OAAK,cAAc,GAAG,CAAC;AACzB,GAAG,CAAC,MAAM,SAAS;AACjB,OAAK,QAAQ,CAAC,OAAO,OAAO,cAAc;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAAC,SAAS;AACX,SAAO,KAAK,cAAc;AAC5B,CAAC;AAEM,IAAM,aAAa,UAAU;;;ACtB7B,IAAM,WAAW,aAAa,CAAC,SAAS;AAC7C,OAAK,QAAQ,OAAO,KAAK,gBAAgB,IAAI,KAAK,WAAW,IAAI,iBAAiB,KAAK,WAAW,IAAI,cAAc;AACtH,GAAG,CAAC,MAAM,SAAS;AACjB,OAAK,QAAQ,CAAC,OAAO,OAAO,YAAY;AAC1C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAAC,SAAS;AACX,SAAO,KAAK,SAAS;AACvB,CAAC;AAEM,IAAM,YAAY,SAAS;AAE3B,IAAM,UAAU,aAAa,CAAC,SAAS;AAC5C,OAAK,cAAc,GAAG,GAAG,CAAC;AAC5B,GAAG,CAAC,MAAM,SAAS;AACjB,OAAK,QAAQ,CAAC,OAAO,OAAO,YAAY;AAC1C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAAC,SAAS;AACX,SAAO,KAAK,YAAY;AAC1B,CAAC;AAEM,IAAM,WAAW,QAAQ;;;ACtBzB,IAAM,UAAU;AAAA,EACrB,UAAQ,KAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EAChC,CAAC,MAAM,SAAS,KAAK,QAAQ,KAAK,QAAQ,IAAI,IAAI;AAAA,EAClD,CAAC,OAAO,SAAS,MAAM,SAAS,IAAI,kBAAkB,IAAI,MAAM,kBAAkB,KAAK,kBAAkB;AAAA,EACzG,UAAQ,KAAK,QAAQ,IAAI;AAC3B;AAEO,IAAM,WAAW,QAAQ;AAEzB,IAAM,SAAS,aAAa,CAAC,SAAS;AAC3C,OAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC7B,GAAG,CAAC,MAAM,SAAS;AACjB,OAAK,WAAW,KAAK,WAAW,IAAI,IAAI;AAC1C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAAC,SAAS;AACX,SAAO,KAAK,WAAW,IAAI;AAC7B,CAAC;AAEM,IAAM,UAAU,OAAO;AAEvB,IAAM,UAAU,aAAa,CAAC,SAAS;AAC5C,OAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC7B,GAAG,CAAC,MAAM,SAAS;AACjB,OAAK,WAAW,KAAK,WAAW,IAAI,IAAI;AAC1C,GAAG,CAAC,OAAO,QAAQ;AACjB,UAAQ,MAAM,SAAS;AACzB,GAAG,CAAC,SAAS;AACX,SAAO,KAAK,MAAM,OAAO,WAAW;AACtC,CAAC;AAEM,IAAM,WAAW,QAAQ;;;AC/BhC,SAAS,YAAY,GAAG;AACtB,SAAO,aAAa,CAAC,SAAS;AAC5B,SAAK,QAAQ,KAAK,QAAQ,KAAK,KAAK,OAAO,IAAI,IAAI,KAAK,CAAC;AACzD,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1B,GAAG,CAAC,MAAM,SAAS;AACjB,SAAK,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC;AAAA,EACxC,GAAG,CAAC,OAAO,QAAQ;AACjB,YAAQ,MAAM,SAAS,IAAI,kBAAkB,IAAI,MAAM,kBAAkB,KAAK,kBAAkB;AAAA,EAClG,CAAC;AACH;AAEO,IAAM,aAAa,YAAY,CAAC;AAChC,IAAM,aAAa,YAAY,CAAC;AAChC,IAAM,cAAc,YAAY,CAAC;AACjC,IAAM,gBAAgB,YAAY,CAAC;AACnC,IAAM,eAAe,YAAY,CAAC;AAClC,IAAM,aAAa,YAAY,CAAC;AAChC,IAAM,eAAe,YAAY,CAAC;AAElC,IAAM,cAAc,WAAW;AAC/B,IAAM,cAAc,WAAW;AAC/B,IAAM,eAAe,YAAY;AACjC,IAAM,iBAAiB,cAAc;AACrC,IAAM,gBAAgB,aAAa;AACnC,IAAM,cAAc,WAAW;AAC/B,IAAM,gBAAgB,aAAa;AAE1C,SAAS,WAAW,GAAG;AACrB,SAAO,aAAa,CAAC,SAAS;AAC5B,SAAK,WAAW,KAAK,WAAW,KAAK,KAAK,UAAU,IAAI,IAAI,KAAK,CAAC;AAClE,SAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAAA,EAC7B,GAAG,CAAC,MAAM,SAAS;AACjB,SAAK,WAAW,KAAK,WAAW,IAAI,OAAO,CAAC;AAAA,EAC9C,GAAG,CAAC,OAAO,QAAQ;AACjB,YAAQ,MAAM,SAAS;AAAA,EACzB,CAAC;AACH;AAEO,IAAM,YAAY,WAAW,CAAC;AAC9B,IAAM,YAAY,WAAW,CAAC;AAC9B,IAAM,aAAa,WAAW,CAAC;AAC/B,IAAM,eAAe,WAAW,CAAC;AACjC,IAAM,cAAc,WAAW,CAAC;AAChC,IAAM,YAAY,WAAW,CAAC;AAC9B,IAAM,cAAc,WAAW,CAAC;AAEhC,IAAM,aAAa,UAAU;AAC7B,IAAM,aAAa,UAAU;AAC7B,IAAM,cAAc,WAAW;AAC/B,IAAM,gBAAgB,aAAa;AACnC,IAAM,eAAe,YAAY;AACjC,IAAM,aAAa,UAAU;AAC7B,IAAM,eAAe,YAAY;;;ACrDjC,IAAM,YAAY,aAAa,CAAC,SAAS;AAC9C,OAAK,QAAQ,CAAC;AACd,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAC1B,GAAG,CAAC,MAAM,SAAS;AACjB,OAAK,SAAS,KAAK,SAAS,IAAI,IAAI;AACtC,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,IAAI,SAAS,IAAI,MAAM,SAAS,KAAK,IAAI,YAAY,IAAI,MAAM,YAAY,KAAK;AACzF,GAAG,CAAC,SAAS;AACX,SAAO,KAAK,SAAS;AACvB,CAAC;AAEM,IAAM,aAAa,UAAU;AAE7B,IAAM,WAAW,aAAa,CAAC,SAAS;AAC7C,OAAK,WAAW,CAAC;AACjB,OAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC7B,GAAG,CAAC,MAAM,SAAS;AACjB,OAAK,YAAY,KAAK,YAAY,IAAI,IAAI;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,IAAI,YAAY,IAAI,MAAM,YAAY,KAAK,IAAI,eAAe,IAAI,MAAM,eAAe,KAAK;AACrG,GAAG,CAAC,SAAS;AACX,SAAO,KAAK,YAAY;AAC1B,CAAC;AAEM,IAAM,YAAY,SAAS;;;ACxB3B,IAAM,WAAW,aAAa,CAAC,SAAS;AAC7C,OAAK,SAAS,GAAG,CAAC;AAClB,OAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAC1B,GAAG,CAAC,MAAM,SAAS;AACjB,OAAK,YAAY,KAAK,YAAY,IAAI,IAAI;AAC5C,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,IAAI,YAAY,IAAI,MAAM,YAAY;AAC/C,GAAG,CAAC,SAAS;AACX,SAAO,KAAK,YAAY;AAC1B,CAAC;AAGD,SAAS,QAAQ,CAAC,MAAM;AACtB,SAAO,CAAC,SAAS,IAAI,KAAK,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK,OAAO,aAAa,CAAC,SAAS;AAC9E,SAAK,YAAY,KAAK,MAAM,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC;AACvD,SAAK,SAAS,GAAG,CAAC;AAClB,SAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EAC1B,GAAG,CAAC,MAAM,SAAS;AACjB,SAAK,YAAY,KAAK,YAAY,IAAI,OAAO,CAAC;AAAA,EAChD,CAAC;AACH;AAEO,IAAM,YAAY,SAAS;AAE3B,IAAM,UAAU,aAAa,CAAC,SAAS;AAC5C,OAAK,YAAY,GAAG,CAAC;AACrB,OAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAC7B,GAAG,CAAC,MAAM,SAAS;AACjB,OAAK,eAAe,KAAK,eAAe,IAAI,IAAI;AAClD,GAAG,CAAC,OAAO,QAAQ;AACjB,SAAO,IAAI,eAAe,IAAI,MAAM,eAAe;AACrD,GAAG,CAAC,SAAS;AACX,SAAO,KAAK,eAAe;AAC7B,CAAC;AAGD,QAAQ,QAAQ,CAAC,MAAM;AACrB,SAAO,CAAC,SAAS,IAAI,KAAK,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK,OAAO,aAAa,CAAC,SAAS;AAC9E,SAAK,eAAe,KAAK,MAAM,KAAK,eAAe,IAAI,CAAC,IAAI,CAAC;AAC7D,SAAK,YAAY,GAAG,CAAC;AACrB,SAAK,YAAY,GAAG,GAAG,GAAG,CAAC;AAAA,EAC7B,GAAG,CAAC,MAAM,SAAS;AACjB,SAAK,eAAe,KAAK,eAAe,IAAI,OAAO,CAAC;AAAA,EACtD,CAAC;AACH;AAEO,IAAM,WAAW,QAAQ;;;ACrChC,SAAS,OAAO,MAAM,OAAO,MAAM,KAAK,MAAM,QAAQ;AAEpD,QAAM,gBAAgB;AAAA,IACpB,CAAC,QAAS,GAAQ,cAAc;AAAA,IAChC,CAAC,QAAS,GAAI,IAAI,cAAc;AAAA,IAChC,CAAC,QAAQ,IAAI,KAAK,cAAc;AAAA,IAChC,CAAC,QAAQ,IAAI,KAAK,cAAc;AAAA,IAChC,CAAC,QAAS,GAAQ,cAAc;AAAA,IAChC,CAAC,QAAS,GAAI,IAAI,cAAc;AAAA,IAChC,CAAC,QAAQ,IAAI,KAAK,cAAc;AAAA,IAChC,CAAC,QAAQ,IAAI,KAAK,cAAc;AAAA,IAChC,CAAG,MAAO,GAAQ,YAAc;AAAA,IAChC,CAAG,MAAO,GAAI,IAAI,YAAc;AAAA,IAChC,CAAG,MAAO,GAAI,IAAI,YAAc;AAAA,IAChC,CAAG,MAAM,IAAI,KAAK,YAAc;AAAA,IAChC,CAAI,KAAM,GAAQ,WAAc;AAAA,IAChC,CAAI,KAAM,GAAI,IAAI,WAAc;AAAA,IAChC,CAAG,MAAO,GAAQ,YAAc;AAAA,IAChC,CAAE,OAAQ,GAAQ,aAAc;AAAA,IAChC,CAAE,OAAQ,GAAI,IAAI,aAAc;AAAA,IAChC,CAAG,MAAO,GAAQ,YAAc;AAAA,EAClC;AAEA,WAAS,MAAM,OAAO,MAAM,OAAO;AACjC,UAAM,UAAU,OAAO;AACvB,QAAI,QAAS,EAAC,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK;AACzC,UAAM,WAAW,SAAS,OAAO,MAAM,UAAU,aAAa,QAAQ,aAAa,OAAO,MAAM,KAAK;AACrG,UAAMA,SAAQ,WAAW,SAAS,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC;AAC7D,WAAO,UAAUA,OAAM,QAAQ,IAAIA;AAAA,EACrC;AAEA,WAAS,aAAa,OAAO,MAAM,OAAO;AACxC,UAAM,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI;AACxC,UAAM,IAAI,SAAS,CAAC,CAAC,EAAC,EAAEC,KAAI,MAAMA,KAAI,EAAE,MAAM,eAAe,MAAM;AACnE,QAAI,MAAM,cAAc,OAAQ,QAAO,KAAK,MAAM,SAAS,QAAQ,cAAc,OAAO,cAAc,KAAK,CAAC;AAC5G,QAAI,MAAM,EAAG,QAAO,YAAY,MAAM,KAAK,IAAI,SAAS,OAAO,MAAM,KAAK,GAAG,CAAC,CAAC;AAC/E,UAAM,CAAC,GAAG,IAAI,IAAI,cAAc,SAAS,cAAc,IAAI,CAAC,EAAE,CAAC,IAAI,cAAc,CAAC,EAAE,CAAC,IAAI,SAAS,IAAI,IAAI,CAAC;AAC3G,WAAO,EAAE,MAAM,IAAI;AAAA,EACrB;AAEA,SAAO,CAAC,OAAO,YAAY;AAC7B;AAEA,IAAM,CAAC,UAAU,eAAe,IAAI,OAAO,SAAS,UAAU,WAAW,SAAS,SAAS,SAAS;AACpG,IAAM,CAAC,WAAW,gBAAgB,IAAI,OAAO,UAAU,WAAW,YAAY,SAAS,UAAU,UAAU;", "names": ["ticks", "step"]}