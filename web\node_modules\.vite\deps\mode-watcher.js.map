{"version": 3, "sources": ["../../mode-watcher/dist/without-transition.js", "../../mode-watcher/dist/utils.js", "../../mode-watcher/dist/stores.js", "../../mode-watcher/dist/mode.js", "../../mode-watcher/dist/mode-watcher-lite.svelte", "../../mode-watcher/dist/mode-watcher-full.svelte", "../../mode-watcher/dist/mode-watcher.svelte"], "sourcesContent": ["// Original Source: https://reemus.dev/article/disable-css-transition-color-scheme-change#heading-ultimate-solution-for-changing-color-scheme-without-transitions\nlet timeoutAction;\nlet timeoutEnable;\n// Perform a task without any css transitions\n// eslint-disable-next-line ts/no-explicit-any\nexport function withoutTransition(action) {\n    if (typeof document === \"undefined\")\n        return;\n    // Clear fallback timeouts\n    clearTimeout(timeoutAction);\n    clearTimeout(timeoutEnable);\n    // Create style element to disable transitions\n    const style = document.createElement(\"style\");\n    const css = document.createTextNode(`* {\n     -webkit-transition: none !important;\n     -moz-transition: none !important;\n     -o-transition: none !important;\n     -ms-transition: none !important;\n     transition: none !important;\n  }`);\n    style.appendChild(css);\n    // Functions to insert and remove style element\n    const disable = () => document.head.appendChild(style);\n    const enable = () => document.head.removeChild(style);\n    // Best method, getComputedStyle forces browser to repaint\n    if (typeof window.getComputedStyle !== \"undefined\") {\n        disable();\n        action();\n        // eslint-disable-next-line ts/no-unused-expressions -- this is a side effect\n        window.getComputedStyle(style).opacity;\n        enable();\n        return;\n    }\n    // Better method, requestAnimationFrame processes function before next repaint\n    if (typeof window.requestAnimationFrame !== \"undefined\") {\n        disable();\n        action();\n        window.requestAnimationFrame(enable);\n        return;\n    }\n    // Fallback\n    disable();\n    timeoutAction = window.setTimeout(() => {\n        action();\n        timeoutEnable = window.setTimeout(enable, 120);\n    }, 120);\n}\n", "/**\n * Santizes an array of classnames by removing any empty strings.\n */\nexport function sanitizeClassNames(classNames) {\n    return classNames.filter((className) => className.length > 0);\n}\n", "import { derived, get, writable } from \"svelte/store\";\nimport { withoutTransition } from \"./without-transition.js\";\nimport { sanitizeClassNames } from \"./utils.js\";\n// saves having to branch for server vs client\nconst noopStorage = {\n    getItem: (_key) => null,\n    setItem: (_key, _value) => { },\n};\n// whether we are running on server vs client\nconst isBrowser = typeof document !== \"undefined\";\n/**  the modes that are supported, used for validation & type derivation */\nexport const modes = [\"dark\", \"light\", \"system\"];\n/**\n * The key used to store the `mode` in localStorage.\n */\nexport const modeStorageKey = writable(\"mode-watcher-mode\");\n/**\n * The key used to store the `theme` in localStorage.\n */\nexport const themeStorageKey = writable(\"mode-watcher-theme\");\n/**\n * Writable store that represents the user's preferred mode (`\"dark\"`, `\"light\"` or `\"system\"`)\n */\nexport const userPrefersMode = createUserPrefersMode();\n/**\n * Readable store that represents the system's preferred mode (`\"dark\"`, `\"light\"` or `undefined`)\n */\nexport const systemPrefersMode = createSystemMode();\n/**\n * Theme colors for light and dark modes.\n */\nexport const themeColors = writable(undefined);\n/**\n * A custom theme to apply and persist to the root `html` element.\n */\nexport const theme = createCustomTheme();\n/**\n * Whether to disable transitions when changing the mode.\n */\nexport const disableTransitions = writable(true);\n/**\n * The classnames to add to the root `html` element when the mode is dark.\n */\nexport const darkClassNames = writable([]);\n/**\n * The classnames to add to the root `html` element when the mode is light.\n */\nexport const lightClassNames = writable([]);\n/**\n * Derived store that represents the current mode (`\"dark\"`, `\"light\"` or `undefined`)\n */\nexport const derivedMode = createDerivedMode();\n/**\n * Derived store that represents the current custom theme\n */\nexport const derivedTheme = createDerivedTheme();\n// derived from: https://github.com/CaptainCodeman/svelte-web-storage\nfunction createUserPrefersMode() {\n    const defaultValue = \"system\";\n    const storage = isBrowser ? localStorage : noopStorage;\n    const initialValue = storage.getItem(getModeStorageKey());\n    let value = isValidMode(initialValue) ? initialValue : defaultValue;\n    function getModeStorageKey() {\n        return get(modeStorageKey);\n    }\n    const { subscribe, set: _set } = writable(value, () => {\n        if (!isBrowser)\n            return;\n        const handler = (e) => {\n            if (e.key !== getModeStorageKey())\n                return;\n            const newValue = e.newValue;\n            if (isValidMode(newValue)) {\n                _set((value = newValue));\n            }\n            else {\n                _set((value = defaultValue));\n            }\n        };\n        addEventListener(\"storage\", handler);\n        return () => removeEventListener(\"storage\", handler);\n    });\n    function set(v) {\n        _set((value = v));\n        storage.setItem(getModeStorageKey(), value);\n    }\n    return {\n        subscribe,\n        set,\n    };\n}\nfunction createCustomTheme() {\n    const storage = isBrowser ? localStorage : noopStorage;\n    const initialValue = storage.getItem(getThemeStorageKey());\n    let value = initialValue === null || initialValue === undefined ? \"\" : initialValue;\n    function getThemeStorageKey() {\n        return get(themeStorageKey);\n    }\n    const { subscribe, set: _set } = writable(value, () => {\n        if (!isBrowser)\n            return;\n        const handler = (e) => {\n            if (e.key !== getThemeStorageKey())\n                return;\n            const newValue = e.newValue;\n            if (newValue === null) {\n                _set((value = \"\"));\n            }\n            else {\n                _set((value = newValue));\n            }\n        };\n        addEventListener(\"storage\", handler);\n        return () => removeEventListener(\"storage\", handler);\n    });\n    function set(v) {\n        _set((value = v));\n        storage.setItem(getThemeStorageKey(), value);\n    }\n    return {\n        subscribe,\n        set,\n    };\n}\nfunction createSystemMode() {\n    const defaultValue = undefined;\n    let track = true;\n    const { subscribe, set } = writable(defaultValue, () => {\n        if (!isBrowser)\n            return;\n        const handler = (e) => {\n            if (!track)\n                return;\n            set(e.matches ? \"light\" : \"dark\");\n        };\n        const mediaQueryState = window.matchMedia(\"(prefers-color-scheme: light)\");\n        mediaQueryState.addEventListener(\"change\", handler);\n        return () => mediaQueryState.removeEventListener(\"change\", handler);\n    });\n    /**\n     * Query system preferences and update the store.\n     */\n    function query() {\n        if (!isBrowser)\n            return;\n        const mediaQueryState = window.matchMedia(\"(prefers-color-scheme: light)\");\n        set(mediaQueryState.matches ? \"light\" : \"dark\");\n    }\n    /**\n     * Enable or disable tracking of system preference changes.\n     */\n    function tracking(active) {\n        track = active;\n    }\n    return {\n        subscribe,\n        query,\n        tracking,\n    };\n}\nfunction createDerivedMode() {\n    const { subscribe } = derived([\n        userPrefersMode,\n        systemPrefersMode,\n        themeColors,\n        disableTransitions,\n        darkClassNames,\n        lightClassNames,\n    ], ([$userPrefersMode, $systemPrefersMode, $themeColors, $disableTransitions, $darkClassNames, $lightClassNames,]) => {\n        if (!isBrowser)\n            return undefined;\n        const derivedMode = $userPrefersMode === \"system\" ? $systemPrefersMode : $userPrefersMode;\n        const sanitizedDarkClassNames = sanitizeClassNames($darkClassNames);\n        const sanitizedLightClassNames = sanitizeClassNames($lightClassNames);\n        function update() {\n            const htmlEl = document.documentElement;\n            const themeColorEl = document.querySelector('meta[name=\"theme-color\"]');\n            if (derivedMode === \"light\") {\n                if (sanitizedDarkClassNames.length)\n                    htmlEl.classList.remove(...sanitizedDarkClassNames);\n                if (sanitizedLightClassNames.length)\n                    htmlEl.classList.add(...sanitizedLightClassNames);\n                htmlEl.style.colorScheme = \"light\";\n                if (themeColorEl && $themeColors) {\n                    themeColorEl.setAttribute(\"content\", $themeColors.light);\n                }\n            }\n            else {\n                if (sanitizedLightClassNames.length)\n                    htmlEl.classList.remove(...sanitizedLightClassNames);\n                if (sanitizedDarkClassNames.length)\n                    htmlEl.classList.add(...sanitizedDarkClassNames);\n                htmlEl.style.colorScheme = \"dark\";\n                if (themeColorEl && $themeColors) {\n                    themeColorEl.setAttribute(\"content\", $themeColors.dark);\n                }\n            }\n        }\n        if ($disableTransitions) {\n            withoutTransition(update);\n        }\n        else {\n            update();\n        }\n        return derivedMode;\n    });\n    return {\n        subscribe,\n    };\n}\nfunction createDerivedTheme() {\n    const { subscribe } = derived([theme, disableTransitions], ([$theme, $disableTransitions]) => {\n        if (!isBrowser)\n            return undefined;\n        function update() {\n            const htmlEl = document.documentElement;\n            htmlEl.setAttribute(\"data-theme\", $theme);\n        }\n        if ($disableTransitions) {\n            withoutTransition(update);\n        }\n        else {\n            update();\n        }\n        return $theme;\n    });\n    return {\n        subscribe,\n    };\n}\nexport function isValidMode(value) {\n    if (typeof value !== \"string\")\n        return false;\n    return modes.includes(value);\n}\n", "import { get } from \"svelte/store\";\nimport { derivedMode, derivedTheme, disableTransitions, modeStorageKey, systemPrefersMode, themeColors, themeStorageKey, theme as themeStore, userPrefersMode, } from \"./stores.js\";\n/** Toggle between light and dark mode */\nexport function toggleMode() {\n    userPrefersMode.set(get(derivedMode) === \"dark\" ? \"light\" : \"dark\");\n}\n/** Set the mode to light or dark */\nexport function setMode(mode) {\n    userPrefersMode.set(mode);\n}\n/** Reset the mode to operating system preference */\nexport function resetMode() {\n    userPrefersMode.set(\"system\");\n}\n/** Set the theme to a custom value */\nexport function setTheme(theme) {\n    themeStore.set(theme);\n}\nexport function defineConfig(config) {\n    return config;\n}\n/** Used to set the mode on initial page load to prevent FOUC */\nexport function setInitialMode({ defaultMode = \"system\", themeColors, darkClassNames = [\"dark\"], lightClassNames = [], defaultTheme = \"\", modeStorageKey = \"mode-watcher-mode\", themeStorageKey = \"mode-watcher-theme\", }) {\n    const rootEl = document.documentElement;\n    const mode = localStorage.getItem(modeStorageKey) || defaultMode;\n    const theme = localStorage.getItem(themeStorageKey) || defaultTheme;\n    const light = mode === \"light\" ||\n        (mode === \"system\" && window.matchMedia(\"(prefers-color-scheme: light)\").matches);\n    if (light) {\n        if (darkClassNames.length)\n            rootEl.classList.remove(...darkClassNames);\n        if (lightClassNames.length)\n            rootEl.classList.add(...lightClassNames);\n    }\n    else {\n        if (lightClassNames.length)\n            rootEl.classList.remove(...lightClassNames);\n        if (darkClassNames.length)\n            rootEl.classList.add(...darkClassNames);\n    }\n    rootEl.style.colorScheme = light ? \"light\" : \"dark\";\n    if (themeColors) {\n        const themeMetaEl = document.querySelector('meta[name=\"theme-color\"]');\n        if (themeMetaEl) {\n            themeMetaEl.setAttribute(\"content\", mode === \"light\" ? themeColors.light : themeColors.dark);\n        }\n    }\n    if (theme) {\n        rootEl.setAttribute(\"data-theme\", theme);\n        localStorage.setItem(themeStorageKey, theme);\n    }\n    localStorage.setItem(modeStorageKey, mode);\n}\n/**\n * A type-safe way to generate the source expression used to set the initial mode and avoid FOUC.\n */\nexport function generateSetInitialModeExpression(config = {}) {\n    return `(${setInitialMode.toString()})(${JSON.stringify(config)});`;\n}\nexport { modeStorageKey, themeStorageKey, derivedTheme as theme, userPrefersMode, systemPrefersMode, derivedMode as mode, themeColors, disableTransitions, };\n", null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAI;AACJ,IAAI;AAGG,SAAS,kBAAkB,QAAQ;AACtC,MAAI,OAAO,aAAa;AACpB;AAEJ,eAAa,aAAa;AAC1B,eAAa,aAAa;AAE1B,QAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,QAAM,MAAM,SAAS,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMpC;AACA,QAAM,YAAY,GAAG;AAErB,QAAM,UAAU,MAAM,SAAS,KAAK,YAAY,KAAK;AACrD,QAAM,SAAS,MAAM,SAAS,KAAK,YAAY,KAAK;AAEpD,MAAI,OAAO,OAAO,qBAAqB,aAAa;AAChD,YAAQ;AACR,WAAO;AAEP,WAAO,iBAAiB,KAAK,EAAE;AAC/B,WAAO;AACP;AAAA,EACJ;AAEA,MAAI,OAAO,OAAO,0BAA0B,aAAa;AACrD,YAAQ;AACR,WAAO;AACP,WAAO,sBAAsB,MAAM;AACnC;AAAA,EACJ;AAEA,UAAQ;AACR,kBAAgB,OAAO,WAAW,MAAM;AACpC,WAAO;AACP,oBAAgB,OAAO,WAAW,QAAQ,GAAG;AAAA,EACjD,GAAG,GAAG;AACV;;;AC3CO,SAAS,mBAAmB,YAAY;AAC3C,SAAO,WAAW,OAAO,CAAC,cAAc,UAAU,SAAS,CAAC;AAChE;;;ACDA,IAAM,cAAc;AAAA,EAChB,SAAS,CAAC,SAAS;AAAA,EACnB,SAAS,CAAC,MAAM,WAAW;AAAA,EAAE;AACjC;AAEA,IAAM,YAAY,OAAO,aAAa;AAE/B,IAAM,QAAQ,CAAC,QAAQ,SAAS,QAAQ;AAIxC,IAAM,iBAAiB,SAAS,mBAAmB;AAInD,IAAM,kBAAkB,SAAS,oBAAoB;AAIrD,IAAM,kBAAkB,sBAAsB;AAI9C,IAAM,oBAAoB,iBAAiB;AAI3C,IAAM,cAAc,SAAS,MAAS;AAItC,IAAM,QAAQ,kBAAkB;AAIhC,IAAM,qBAAqB,SAAS,IAAI;AAIxC,IAAM,iBAAiB,SAAS,CAAC,CAAC;AAIlC,IAAM,kBAAkB,SAAS,CAAC,CAAC;AAInC,IAAM,cAAc,kBAAkB;AAItC,IAAM,eAAe,mBAAmB;AAE/C,SAAS,wBAAwB;AAC7B,QAAM,eAAe;AACrB,QAAM,UAAU,YAAY,eAAe;AAC3C,QAAM,eAAe,QAAQ,QAAQ,kBAAkB,CAAC;AACxD,MAAI,QAAQ,YAAY,YAAY,IAAI,eAAe;AACvD,WAAS,oBAAoB;AACzB,WAAOA,KAAI,cAAc;AAAA,EAC7B;AACA,QAAM,EAAE,WAAW,KAAK,KAAK,IAAI,SAAS,OAAO,MAAM;AACnD,QAAI,CAAC;AACD;AACJ,UAAM,UAAU,CAAC,MAAM;AACnB,UAAI,EAAE,QAAQ,kBAAkB;AAC5B;AACJ,YAAM,WAAW,EAAE;AACnB,UAAI,YAAY,QAAQ,GAAG;AACvB,aAAM,QAAQ,QAAS;AAAA,MAC3B,OACK;AACD,aAAM,QAAQ,YAAa;AAAA,MAC/B;AAAA,IACJ;AACA,qBAAiB,WAAW,OAAO;AACnC,WAAO,MAAM,oBAAoB,WAAW,OAAO;AAAA,EACvD,CAAC;AACD,WAASC,KAAI,GAAG;AACZ,SAAM,QAAQ,CAAE;AAChB,YAAQ,QAAQ,kBAAkB,GAAG,KAAK;AAAA,EAC9C;AACA,SAAO;AAAA,IACH;AAAA,IACA,KAAAA;AAAA,EACJ;AACJ;AACA,SAAS,oBAAoB;AACzB,QAAM,UAAU,YAAY,eAAe;AAC3C,QAAM,eAAe,QAAQ,QAAQ,mBAAmB,CAAC;AACzD,MAAI,QAAQ,iBAAiB,QAAQ,iBAAiB,SAAY,KAAK;AACvE,WAAS,qBAAqB;AAC1B,WAAOD,KAAI,eAAe;AAAA,EAC9B;AACA,QAAM,EAAE,WAAW,KAAK,KAAK,IAAI,SAAS,OAAO,MAAM;AACnD,QAAI,CAAC;AACD;AACJ,UAAM,UAAU,CAAC,MAAM;AACnB,UAAI,EAAE,QAAQ,mBAAmB;AAC7B;AACJ,YAAM,WAAW,EAAE;AACnB,UAAI,aAAa,MAAM;AACnB,aAAM,QAAQ,EAAG;AAAA,MACrB,OACK;AACD,aAAM,QAAQ,QAAS;AAAA,MAC3B;AAAA,IACJ;AACA,qBAAiB,WAAW,OAAO;AACnC,WAAO,MAAM,oBAAoB,WAAW,OAAO;AAAA,EACvD,CAAC;AACD,WAASC,KAAI,GAAG;AACZ,SAAM,QAAQ,CAAE;AAChB,YAAQ,QAAQ,mBAAmB,GAAG,KAAK;AAAA,EAC/C;AACA,SAAO;AAAA,IACH;AAAA,IACA,KAAAA;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB;AACxB,QAAM,eAAe;AACrB,MAAI,QAAQ;AACZ,QAAM,EAAE,WAAW,KAAAA,KAAI,IAAI,SAAS,cAAc,MAAM;AACpD,QAAI,CAAC;AACD;AACJ,UAAM,UAAU,CAAC,MAAM;AACnB,UAAI,CAAC;AACD;AACJ,MAAAA,KAAI,EAAE,UAAU,UAAU,MAAM;AAAA,IACpC;AACA,UAAM,kBAAkB,OAAO,WAAW,+BAA+B;AACzE,oBAAgB,iBAAiB,UAAU,OAAO;AAClD,WAAO,MAAM,gBAAgB,oBAAoB,UAAU,OAAO;AAAA,EACtE,CAAC;AAID,WAAS,QAAQ;AACb,QAAI,CAAC;AACD;AACJ,UAAM,kBAAkB,OAAO,WAAW,+BAA+B;AACzE,IAAAA,KAAI,gBAAgB,UAAU,UAAU,MAAM;AAAA,EAClD;AAIA,WAAS,SAAS,QAAQ;AACtB,YAAQ;AAAA,EACZ;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,oBAAoB;AACzB,QAAM,EAAE,UAAU,IAAI,QAAQ;AAAA,IAC1B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,GAAG,CAAC,CAAC,kBAAkB,oBAAoB,cAAc,qBAAqB,iBAAiB,gBAAiB,MAAM;AAClH,QAAI,CAAC;AACD,aAAO;AACX,UAAMC,eAAc,qBAAqB,WAAW,qBAAqB;AACzE,UAAM,0BAA0B,mBAAmB,eAAe;AAClE,UAAM,2BAA2B,mBAAmB,gBAAgB;AACpE,aAAS,SAAS;AACd,YAAM,SAAS,SAAS;AACxB,YAAM,eAAe,SAAS,cAAc,0BAA0B;AACtE,UAAIA,iBAAgB,SAAS;AACzB,YAAI,wBAAwB;AACxB,iBAAO,UAAU,OAAO,GAAG,uBAAuB;AACtD,YAAI,yBAAyB;AACzB,iBAAO,UAAU,IAAI,GAAG,wBAAwB;AACpD,eAAO,MAAM,cAAc;AAC3B,YAAI,gBAAgB,cAAc;AAC9B,uBAAa,aAAa,WAAW,aAAa,KAAK;AAAA,QAC3D;AAAA,MACJ,OACK;AACD,YAAI,yBAAyB;AACzB,iBAAO,UAAU,OAAO,GAAG,wBAAwB;AACvD,YAAI,wBAAwB;AACxB,iBAAO,UAAU,IAAI,GAAG,uBAAuB;AACnD,eAAO,MAAM,cAAc;AAC3B,YAAI,gBAAgB,cAAc;AAC9B,uBAAa,aAAa,WAAW,aAAa,IAAI;AAAA,QAC1D;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,qBAAqB;AACrB,wBAAkB,MAAM;AAAA,IAC5B,OACK;AACD,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,CAAC;AACD,SAAO;AAAA,IACH;AAAA,EACJ;AACJ;AACA,SAAS,qBAAqB;AAC1B,QAAM,EAAE,UAAU,IAAI,QAAQ,CAAC,OAAO,kBAAkB,GAAG,CAAC,CAAC,QAAQ,mBAAmB,MAAM;AAC1F,QAAI,CAAC;AACD,aAAO;AACX,aAAS,SAAS;AACd,YAAM,SAAS,SAAS;AACxB,aAAO,aAAa,cAAc,MAAM;AAAA,IAC5C;AACA,QAAI,qBAAqB;AACrB,wBAAkB,MAAM;AAAA,IAC5B,OACK;AACD,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX,CAAC;AACD,SAAO;AAAA,IACH;AAAA,EACJ;AACJ;AACO,SAAS,YAAY,OAAO;AAC/B,MAAI,OAAO,UAAU;AACjB,WAAO;AACX,SAAO,MAAM,SAAS,KAAK;AAC/B;;;ACvOO,SAAS,aAAa;AACzB,kBAAgB,IAAIC,KAAI,WAAW,MAAM,SAAS,UAAU,MAAM;AACtE;AAEO,SAAS,QAAQ,MAAM;AAC1B,kBAAgB,IAAI,IAAI;AAC5B;AAEO,SAAS,YAAY;AACxB,kBAAgB,IAAI,QAAQ;AAChC;AAEO,SAAS,SAASC,QAAO;AAC5B,QAAW,IAAIA,MAAK;AACxB;AACO,SAAS,aAAa,QAAQ;AACjC,SAAO;AACX;AAEO,SAAS,eAAe,EAAE,cAAc,UAAU,aAAAC,cAAa,gBAAAC,kBAAiB,CAAC,MAAM,GAAG,iBAAAC,mBAAkB,CAAC,GAAG,eAAe,IAAI,gBAAAC,kBAAiB,qBAAqB,iBAAAC,mBAAkB,qBAAsB,GAAG;AACvN,QAAM,SAAS,SAAS;AACxB,QAAM,OAAO,aAAa,QAAQD,eAAc,KAAK;AACrD,QAAMJ,SAAQ,aAAa,QAAQK,gBAAe,KAAK;AACvD,QAAM,QAAQ,SAAS,WAClB,SAAS,YAAY,OAAO,WAAW,+BAA+B,EAAE;AAC7E,MAAI,OAAO;AACP,QAAIH,gBAAe;AACf,aAAO,UAAU,OAAO,GAAGA,eAAc;AAC7C,QAAIC,iBAAgB;AAChB,aAAO,UAAU,IAAI,GAAGA,gBAAe;AAAA,EAC/C,OACK;AACD,QAAIA,iBAAgB;AAChB,aAAO,UAAU,OAAO,GAAGA,gBAAe;AAC9C,QAAID,gBAAe;AACf,aAAO,UAAU,IAAI,GAAGA,eAAc;AAAA,EAC9C;AACA,SAAO,MAAM,cAAc,QAAQ,UAAU;AAC7C,MAAID,cAAa;AACb,UAAM,cAAc,SAAS,cAAc,0BAA0B;AACrE,QAAI,aAAa;AACb,kBAAY,aAAa,WAAW,SAAS,UAAUA,aAAY,QAAQA,aAAY,IAAI;AAAA,IAC/F;AAAA,EACJ;AACA,MAAID,QAAO;AACP,WAAO,aAAa,cAAcA,MAAK;AACvC,iBAAa,QAAQK,kBAAiBL,MAAK;AAAA,EAC/C;AACA,eAAa,QAAQI,iBAAgB,IAAI;AAC7C;AAIO,SAAS,iCAAiC,SAAS,CAAC,GAAG;AAC1D,SAAO,IAAI,eAAe,SAAS,CAAC,KAAK,KAAK,UAAU,MAAM,CAAC;AACnE;;;;;;;;MC1DmBE,eAAW,KAAA,SAAA,eAAA,IAAA,MAAA,MAAS;;;;;;;2DAOJA,aAAW,EAAC,IAAI,CAAA;;;;UAJ9CA,aAAW,EAAA,UAAA,UAAA;;;;;;;;;;;;;;;;;;;;;;MCFL,YAAS,KAAA,SAAA,aAAA,GAAG,EAAE;MACd,aAAU,KAAA,SAAA,cAAA,CAAA;MACVC,eAAW,KAAA,SAAA,eAAA,IAAA,MAAA,MAAS;;;;;;;;6DAQKA,aAAW,EAAC,IAAI,CAAA;;;;YAJ9CA,aAAW,EAAA,UAAA,UAAA;;;;iCAOC,UAAS,IAAA,UAAa,UAAS,CAAA,KAAK,EAAE,OACtD,eAAe,SAAQ,IAAA,OAEvB,KAAK,UAAU,WAAU,CAAA,IAAA,gBAAA,OAAA,OAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;MCGhB,QAAK,KAAA,SAAA,SAAA,GAAG,IAAI;MACZ,cAAW,KAAA,SAAA,eAAA,GAAG,QAAQ;MACtBC,eAAW,KAAA,SAAA,eAAA,IAAA,MAAA,MAAS;MACpBC,sBAAkB,KAAA,SAAA,sBAAA,GAAG,IAAI;MACzBC,kBAAc,KAAA,SAAA,kBAAA,IAAA,MAAA,CAAI,MAAM,CAAA;MACxBC,mBAAe,KAAA,SAAA,mBAAA,IAAA,MAAA,CAAA,CAAA;MACf,eAAY,KAAA,SAAA,gBAAA,GAAG,EAAE;MACjB,QAAK,KAAA,SAAA,SAAA,GAAG,EAAE;MACVC,mBAAe,KAAA,SAAA,mBAAA,GAAG,oBAAoB;MACtCC,kBAAc,KAAA,SAAA,kBAAA,GAAG,mBAAmB;MACpC,6BAA0B,KAAA,SAAA,8BAAA,GAAG,KAAK;AAa7C,UAAO,MAAO;UACN,kBAAkB,YAAK,UAAS,MAAO;IAC7C,CAAC;UACK,mBAAmB,aAAM,UAAS,MAAO;IAC/C,CAAC;AACD,sBAAkB,SAAS,MAAK,CAAA;AAChC,sBAAkB,MAAK;UACjB,mBAAmB,aAAa,QAAQ,qBAAoB,CAAA;AAClE,YAAQ,YAAY,gBAAgB,IAAI,mBAAmB,YAAW,CAAA;UAChE,oBAAoB,aAAa,QAAQ,sBAAqB,CAAA;AACpE,aAAS,qBAAqB,aAAY,CAAA;iBAC7B;AACX,sBAAe;AACf,uBAAgB;IAClB;EACF,CAAC;QACK,aAAa,aAAY;IAC7B,aAAA,YAAW;IACX,aAAAL,aAAW;IACX,gBAAAE,gBAAc;IACd,iBAAAC,iBAAe;IACf,cAAA,aAAY;IACZ,gBAAAE,gBAAc;IACd,iBAAAD,iBAAA;;;;;AAlCA,yBAAwB,IAAIH,oBAAkB,CAAA;;;;;;AAE9C,kBAAiB,IAAID,aAAW,CAAA;;;;;;AAEhC,qBAAoB,IAAIE,gBAAc,CAAA;;;;;;AAEtC,sBAAqB,IAAIC,iBAAe,CAAA;;;;;;AAExC,qBAAoB,IAAIE,gBAAc,CAAA;;;;;;AAEtC,sBAAqB,IAAID,iBAAe,CAAA;;;;QA2BxC,WAAS,cAAA,OAAU,QAAW,WAAW,IAAG,MAAK,IAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAGnD,2BAA0B,EAAA,UAAA,UAAA;UAAA,UAAA,WAAA,KAAA;;;;;;;;;;;;;;;;", "names": ["get", "set", "derivedMode", "get", "theme", "themeColors", "darkClassNames", "lightClassNames", "modeStorageKey", "themeStorageKey", "themeColors", "themeColors", "themeColors", "disableTransitions", "darkClassNames", "lightClassNames", "themeStorageKey", "modeStorageKey"]}