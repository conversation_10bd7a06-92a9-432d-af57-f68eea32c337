{"version": 3, "sources": ["../../zod-to-json-schema/dist/esm/Options.js", "../../zod-to-json-schema/dist/esm/Refs.js", "../../zod-to-json-schema/dist/esm/errorMessages.js", "../../zod-to-json-schema/dist/esm/parsers/any.js", "../../zod-to-json-schema/dist/esm/parsers/array.js", "../../zod-to-json-schema/dist/esm/parsers/bigint.js", "../../zod-to-json-schema/dist/esm/parsers/boolean.js", "../../zod-to-json-schema/dist/esm/parsers/branded.js", "../../zod-to-json-schema/dist/esm/parsers/catch.js", "../../zod-to-json-schema/dist/esm/parsers/date.js", "../../zod-to-json-schema/dist/esm/parsers/default.js", "../../zod-to-json-schema/dist/esm/parsers/effects.js", "../../zod-to-json-schema/dist/esm/parsers/enum.js", "../../zod-to-json-schema/dist/esm/parsers/intersection.js", "../../zod-to-json-schema/dist/esm/parsers/literal.js", "../../zod-to-json-schema/dist/esm/parsers/string.js", "../../zod-to-json-schema/dist/esm/parsers/record.js", "../../zod-to-json-schema/dist/esm/parsers/map.js", "../../zod-to-json-schema/dist/esm/parsers/nativeEnum.js", "../../zod-to-json-schema/dist/esm/parsers/never.js", "../../zod-to-json-schema/dist/esm/parsers/null.js", "../../zod-to-json-schema/dist/esm/parsers/union.js", "../../zod-to-json-schema/dist/esm/parsers/nullable.js", "../../zod-to-json-schema/dist/esm/parsers/number.js", "../../zod-to-json-schema/dist/esm/parsers/object.js", "../../zod-to-json-schema/dist/esm/parsers/optional.js", "../../zod-to-json-schema/dist/esm/parsers/pipeline.js", "../../zod-to-json-schema/dist/esm/parsers/promise.js", "../../zod-to-json-schema/dist/esm/parsers/set.js", "../../zod-to-json-schema/dist/esm/parsers/tuple.js", "../../zod-to-json-schema/dist/esm/parsers/undefined.js", "../../zod-to-json-schema/dist/esm/parsers/unknown.js", "../../zod-to-json-schema/dist/esm/parsers/readonly.js", "../../zod-to-json-schema/dist/esm/selectParser.js", "../../zod-to-json-schema/dist/esm/parseDef.js", "../../zod-to-json-schema/dist/esm/zodToJsonSchema.js", "../../zod-to-json-schema/dist/esm/index.js"], "sourcesContent": ["export const ignoreOverride = Symbol(\"Let zodToJsonSchema decide on which parser to use\");\nexport const jsonDescription = (jsonSchema, def) => {\n    if (def.description) {\n        try {\n            return {\n                ...jsonSchema,\n                ...JSON.parse(def.description),\n            };\n        }\n        catch { }\n    }\n    return jsonSchema;\n};\nexport const defaultOptions = {\n    name: undefined,\n    $refStrategy: \"root\",\n    basePath: [\"#\"],\n    effectStrategy: \"input\",\n    pipeStrategy: \"all\",\n    dateStrategy: \"format:date-time\",\n    mapStrategy: \"entries\",\n    removeAdditionalStrategy: \"passthrough\",\n    allowedAdditionalProperties: true,\n    rejectedAdditionalProperties: false,\n    definitionPath: \"definitions\",\n    target: \"jsonSchema7\",\n    strictUnions: false,\n    definitions: {},\n    errorMessages: false,\n    markdownDescription: false,\n    patternStrategy: \"escape\",\n    applyRegexFlags: false,\n    emailStrategy: \"format:email\",\n    base64Strategy: \"contentEncoding:base64\",\n    nameStrategy: \"ref\",\n};\nexport const getDefaultOptions = (options) => (typeof options === \"string\"\n    ? {\n        ...defaultOptions,\n        name: options,\n    }\n    : {\n        ...defaultOptions,\n        ...options,\n    });\n", "import { getDefaultOptions } from \"./Options.js\";\nexport const getRefs = (options) => {\n    const _options = getDefaultOptions(options);\n    const currentPath = _options.name !== undefined\n        ? [..._options.basePath, _options.definitionPath, _options.name]\n        : _options.basePath;\n    return {\n        ..._options,\n        currentPath: currentPath,\n        propertyPath: undefined,\n        seen: new Map(Object.entries(_options.definitions).map(([name, def]) => [\n            def._def,\n            {\n                def: def._def,\n                path: [..._options.basePath, _options.definitionPath, name],\n                // Resolution of references will be forced even though seen, so it's ok that the schema is undefined here for now.\n                jsonSchema: undefined,\n            },\n        ])),\n    };\n};\n", "export function addErrorMessage(res, key, errorMessage, refs) {\n    if (!refs?.errorMessages)\n        return;\n    if (errorMessage) {\n        res.errorMessage = {\n            ...res.errorMessage,\n            [key]: errorMessage,\n        };\n    }\n}\nexport function setResponseValueAndErrors(res, key, value, errorMessage, refs) {\n    res[key] = value;\n    addErrorMessage(res, key, errorMessage, refs);\n}\n", "export function parseAnyDef() {\n    return {};\n}\n", "import { ZodFirstPartyTypeKind } from \"zod\";\nimport { setResponseValueAndErrors } from \"../errorMessages.js\";\nimport { parseDef } from \"../parseDef.js\";\nexport function parseArrayDef(def, refs) {\n    const res = {\n        type: \"array\",\n    };\n    if (def.type?._def &&\n        def.type?._def?.typeName !== ZodFirstPartyTypeKind.ZodAny) {\n        res.items = parseDef(def.type._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"items\"],\n        });\n    }\n    if (def.minLength) {\n        setResponseValueAndErrors(res, \"minItems\", def.minLength.value, def.minLength.message, refs);\n    }\n    if (def.maxLength) {\n        setResponseValueAndErrors(res, \"maxItems\", def.maxLength.value, def.maxLength.message, refs);\n    }\n    if (def.exactLength) {\n        setResponseValueAndErrors(res, \"minItems\", def.exactLength.value, def.exactLength.message, refs);\n        setResponseValueAndErrors(res, \"maxItems\", def.exactLength.value, def.exactLength.message, refs);\n    }\n    return res;\n}\n", "import { setResponseValueAndErrors } from \"../errorMessages.js\";\nexport function parseBigintDef(def, refs) {\n    const res = {\n        type: \"integer\",\n        format: \"int64\",\n    };\n    if (!def.checks)\n        return res;\n    for (const check of def.checks) {\n        switch (check.kind) {\n            case \"min\":\n                if (refs.target === \"jsonSchema7\") {\n                    if (check.inclusive) {\n                        setResponseValueAndErrors(res, \"minimum\", check.value, check.message, refs);\n                    }\n                    else {\n                        setResponseValueAndErrors(res, \"exclusiveMinimum\", check.value, check.message, refs);\n                    }\n                }\n                else {\n                    if (!check.inclusive) {\n                        res.exclusiveMinimum = true;\n                    }\n                    setResponseValueAndErrors(res, \"minimum\", check.value, check.message, refs);\n                }\n                break;\n            case \"max\":\n                if (refs.target === \"jsonSchema7\") {\n                    if (check.inclusive) {\n                        setResponseValueAndErrors(res, \"maximum\", check.value, check.message, refs);\n                    }\n                    else {\n                        setResponseValueAndErrors(res, \"exclusiveMaximum\", check.value, check.message, refs);\n                    }\n                }\n                else {\n                    if (!check.inclusive) {\n                        res.exclusiveMaximum = true;\n                    }\n                    setResponseValueAndErrors(res, \"maximum\", check.value, check.message, refs);\n                }\n                break;\n            case \"multipleOf\":\n                setResponseValueAndErrors(res, \"multipleOf\", check.value, check.message, refs);\n                break;\n        }\n    }\n    return res;\n}\n", "export function parseBooleanDef() {\n    return {\n        type: \"boolean\",\n    };\n}\n", "import { parseDef } from \"../parseDef.js\";\nexport function parseBrandedDef(_def, refs) {\n    return parseDef(_def.type._def, refs);\n}\n", "import { parseDef } from \"../parseDef.js\";\nexport const parseCatchDef = (def, refs) => {\n    return parseDef(def.innerType._def, refs);\n};\n", "import { setResponseValueAndErrors } from \"../errorMessages.js\";\nexport function parseDateDef(def, refs, overrideDateStrategy) {\n    const strategy = overrideDateStrategy ?? refs.dateStrategy;\n    if (Array.isArray(strategy)) {\n        return {\n            anyOf: strategy.map((item, i) => parseDateDef(def, refs, item)),\n        };\n    }\n    switch (strategy) {\n        case \"string\":\n        case \"format:date-time\":\n            return {\n                type: \"string\",\n                format: \"date-time\",\n            };\n        case \"format:date\":\n            return {\n                type: \"string\",\n                format: \"date\",\n            };\n        case \"integer\":\n            return integerDateParser(def, refs);\n    }\n}\nconst integerDateParser = (def, refs) => {\n    const res = {\n        type: \"integer\",\n        format: \"unix-time\",\n    };\n    if (refs.target === \"openApi3\") {\n        return res;\n    }\n    for (const check of def.checks) {\n        switch (check.kind) {\n            case \"min\":\n                setResponseValueAndErrors(res, \"minimum\", check.value, // This is in milliseconds\n                check.message, refs);\n                break;\n            case \"max\":\n                setResponseValueAndErrors(res, \"maximum\", check.value, // This is in milliseconds\n                check.message, refs);\n                break;\n        }\n    }\n    return res;\n};\n", "import { parseDef } from \"../parseDef.js\";\nexport function parseDefaultDef(_def, refs) {\n    return {\n        ...parseDef(_def.innerType._def, refs),\n        default: _def.defaultValue(),\n    };\n}\n", "import { parseDef } from \"../parseDef.js\";\nexport function parseEffectsDef(_def, refs) {\n    return refs.effectStrategy === \"input\"\n        ? parseDef(_def.schema._def, refs)\n        : {};\n}\n", "export function parseEnumDef(def) {\n    return {\n        type: \"string\",\n        enum: Array.from(def.values),\n    };\n}\n", "import { parseDef } from \"../parseDef.js\";\nconst isJsonSchema7AllOfType = (type) => {\n    if (\"type\" in type && type.type === \"string\")\n        return false;\n    return \"allOf\" in type;\n};\nexport function parseIntersectionDef(def, refs) {\n    const allOf = [\n        parseDef(def.left._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"allOf\", \"0\"],\n        }),\n        parseDef(def.right._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"allOf\", \"1\"],\n        }),\n    ].filter((x) => !!x);\n    let unevaluatedProperties = refs.target === \"jsonSchema2019-09\"\n        ? { unevaluatedProperties: false }\n        : undefined;\n    const mergedAllOf = [];\n    // If either of the schemas is an allOf, merge them into a single allOf\n    allOf.forEach((schema) => {\n        if (isJsonSchema7AllOfType(schema)) {\n            mergedAllOf.push(...schema.allOf);\n            if (schema.unevaluatedProperties === undefined) {\n                // If one of the schemas has no unevaluatedProperties set,\n                // the merged schema should also have no unevaluatedProperties set\n                unevaluatedProperties = undefined;\n            }\n        }\n        else {\n            let nestedSchema = schema;\n            if (\"additionalProperties\" in schema &&\n                schema.additionalProperties === false) {\n                const { additionalProperties, ...rest } = schema;\n                nestedSchema = rest;\n            }\n            else {\n                // As soon as one of the schemas has additionalProperties set not to false, we allow unevaluatedProperties\n                unevaluatedProperties = undefined;\n            }\n            mergedAllOf.push(nestedSchema);\n        }\n    });\n    return mergedAllOf.length\n        ? {\n            allOf: mergedAllOf,\n            ...unevaluatedProperties,\n        }\n        : undefined;\n}\n", "export function parseLiteralDef(def, refs) {\n    const parsedType = typeof def.value;\n    if (parsedType !== \"bigint\" &&\n        parsedType !== \"number\" &&\n        parsedType !== \"boolean\" &&\n        parsedType !== \"string\") {\n        return {\n            type: Array.isArray(def.value) ? \"array\" : \"object\",\n        };\n    }\n    if (refs.target === \"openApi3\") {\n        return {\n            type: parsedType === \"bigint\" ? \"integer\" : parsedType,\n            enum: [def.value],\n        };\n    }\n    return {\n        type: parsedType === \"bigint\" ? \"integer\" : parsedType,\n        const: def.value,\n    };\n}\n", "import { setResponseValueAndErrors } from \"../errorMessages.js\";\nlet emojiRegex = undefined;\n/**\n * Generated from the regular expressions found here as of 2024-05-22:\n * https://github.com/colinhacks/zod/blob/master/src/types.ts.\n *\n * Expressions with /i flag have been changed accordingly.\n */\nexport const zodPatterns = {\n    /**\n     * `c` was changed to `[cC]` to replicate /i flag\n     */\n    cuid: /^[cC][^\\s-]{8,}$/,\n    cuid2: /^[0-9a-z]+$/,\n    ulid: /^[0-9A-HJKMNP-TV-Z]{26}$/,\n    /**\n     * `a-z` was added to replicate /i flag\n     */\n    email: /^(?!\\.)(?!.*\\.\\.)([a-zA-Z0-9_'+\\-\\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\\-]*\\.)+[a-zA-Z]{2,}$/,\n    /**\n     * Constructed a valid Unicode RegExp\n     *\n     * Lazily instantiate since this type of regex isn't supported\n     * in all envs (e.g. React Native).\n     *\n     * See:\n     * https://github.com/colinhacks/zod/issues/2433\n     * Fix in Zod:\n     * https://github.com/colinhacks/zod/commit/9340fd51e48576a75adc919bff65dbc4a5d4c99b\n     */\n    emoji: () => {\n        if (emojiRegex === undefined) {\n            emojiRegex = RegExp(\"^(\\\\p{Extended_Pictographic}|\\\\p{Emoji_Component})+$\", \"u\");\n        }\n        return emojiRegex;\n    },\n    /**\n     * Unused\n     */\n    uuid: /^[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$/,\n    /**\n     * Unused\n     */\n    ipv4: /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,\n    ipv4Cidr: /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\/(3[0-2]|[12]?[0-9])$/,\n    /**\n     * Unused\n     */\n    ipv6: /^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,\n    ipv6Cidr: /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,\n    base64: /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,\n    base64url: /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,\n    nanoid: /^[a-zA-Z0-9_-]{21}$/,\n    jwt: /^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$/,\n};\nexport function parseStringDef(def, refs) {\n    const res = {\n        type: \"string\",\n    };\n    if (def.checks) {\n        for (const check of def.checks) {\n            switch (check.kind) {\n                case \"min\":\n                    setResponseValueAndErrors(res, \"minLength\", typeof res.minLength === \"number\"\n                        ? Math.max(res.minLength, check.value)\n                        : check.value, check.message, refs);\n                    break;\n                case \"max\":\n                    setResponseValueAndErrors(res, \"maxLength\", typeof res.maxLength === \"number\"\n                        ? Math.min(res.maxLength, check.value)\n                        : check.value, check.message, refs);\n                    break;\n                case \"email\":\n                    switch (refs.emailStrategy) {\n                        case \"format:email\":\n                            addFormat(res, \"email\", check.message, refs);\n                            break;\n                        case \"format:idn-email\":\n                            addFormat(res, \"idn-email\", check.message, refs);\n                            break;\n                        case \"pattern:zod\":\n                            addPattern(res, zodPatterns.email, check.message, refs);\n                            break;\n                    }\n                    break;\n                case \"url\":\n                    addFormat(res, \"uri\", check.message, refs);\n                    break;\n                case \"uuid\":\n                    addFormat(res, \"uuid\", check.message, refs);\n                    break;\n                case \"regex\":\n                    addPattern(res, check.regex, check.message, refs);\n                    break;\n                case \"cuid\":\n                    addPattern(res, zodPatterns.cuid, check.message, refs);\n                    break;\n                case \"cuid2\":\n                    addPattern(res, zodPatterns.cuid2, check.message, refs);\n                    break;\n                case \"startsWith\":\n                    addPattern(res, RegExp(`^${escapeLiteralCheckValue(check.value, refs)}`), check.message, refs);\n                    break;\n                case \"endsWith\":\n                    addPattern(res, RegExp(`${escapeLiteralCheckValue(check.value, refs)}$`), check.message, refs);\n                    break;\n                case \"datetime\":\n                    addFormat(res, \"date-time\", check.message, refs);\n                    break;\n                case \"date\":\n                    addFormat(res, \"date\", check.message, refs);\n                    break;\n                case \"time\":\n                    addFormat(res, \"time\", check.message, refs);\n                    break;\n                case \"duration\":\n                    addFormat(res, \"duration\", check.message, refs);\n                    break;\n                case \"length\":\n                    setResponseValueAndErrors(res, \"minLength\", typeof res.minLength === \"number\"\n                        ? Math.max(res.minLength, check.value)\n                        : check.value, check.message, refs);\n                    setResponseValueAndErrors(res, \"maxLength\", typeof res.maxLength === \"number\"\n                        ? Math.min(res.maxLength, check.value)\n                        : check.value, check.message, refs);\n                    break;\n                case \"includes\": {\n                    addPattern(res, RegExp(escapeLiteralCheckValue(check.value, refs)), check.message, refs);\n                    break;\n                }\n                case \"ip\": {\n                    if (check.version !== \"v6\") {\n                        addFormat(res, \"ipv4\", check.message, refs);\n                    }\n                    if (check.version !== \"v4\") {\n                        addFormat(res, \"ipv6\", check.message, refs);\n                    }\n                    break;\n                }\n                case \"base64url\":\n                    addPattern(res, zodPatterns.base64url, check.message, refs);\n                    break;\n                case \"jwt\":\n                    addPattern(res, zodPatterns.jwt, check.message, refs);\n                    break;\n                case \"cidr\": {\n                    if (check.version !== \"v6\") {\n                        addPattern(res, zodPatterns.ipv4Cidr, check.message, refs);\n                    }\n                    if (check.version !== \"v4\") {\n                        addPattern(res, zodPatterns.ipv6Cidr, check.message, refs);\n                    }\n                    break;\n                }\n                case \"emoji\":\n                    addPattern(res, zodPatterns.emoji(), check.message, refs);\n                    break;\n                case \"ulid\": {\n                    addPattern(res, zodPatterns.ulid, check.message, refs);\n                    break;\n                }\n                case \"base64\": {\n                    switch (refs.base64Strategy) {\n                        case \"format:binary\": {\n                            addFormat(res, \"binary\", check.message, refs);\n                            break;\n                        }\n                        case \"contentEncoding:base64\": {\n                            setResponseValueAndErrors(res, \"contentEncoding\", \"base64\", check.message, refs);\n                            break;\n                        }\n                        case \"pattern:zod\": {\n                            addPattern(res, zodPatterns.base64, check.message, refs);\n                            break;\n                        }\n                    }\n                    break;\n                }\n                case \"nanoid\": {\n                    addPattern(res, zodPatterns.nanoid, check.message, refs);\n                }\n                case \"toLowerCase\":\n                case \"toUpperCase\":\n                case \"trim\":\n                    break;\n                default:\n                    /* c8 ignore next */\n                    ((_) => { })(check);\n            }\n        }\n    }\n    return res;\n}\nfunction escapeLiteralCheckValue(literal, refs) {\n    return refs.patternStrategy === \"escape\"\n        ? escapeNonAlphaNumeric(literal)\n        : literal;\n}\nconst ALPHA_NUMERIC = new Set(\"ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789\");\nfunction escapeNonAlphaNumeric(source) {\n    let result = \"\";\n    for (let i = 0; i < source.length; i++) {\n        if (!ALPHA_NUMERIC.has(source[i])) {\n            result += \"\\\\\";\n        }\n        result += source[i];\n    }\n    return result;\n}\n// Adds a \"format\" keyword to the schema. If a format exists, both formats will be joined in an allOf-node, along with subsequent ones.\nfunction addFormat(schema, value, message, refs) {\n    if (schema.format || schema.anyOf?.some((x) => x.format)) {\n        if (!schema.anyOf) {\n            schema.anyOf = [];\n        }\n        if (schema.format) {\n            schema.anyOf.push({\n                format: schema.format,\n                ...(schema.errorMessage &&\n                    refs.errorMessages && {\n                    errorMessage: { format: schema.errorMessage.format },\n                }),\n            });\n            delete schema.format;\n            if (schema.errorMessage) {\n                delete schema.errorMessage.format;\n                if (Object.keys(schema.errorMessage).length === 0) {\n                    delete schema.errorMessage;\n                }\n            }\n        }\n        schema.anyOf.push({\n            format: value,\n            ...(message &&\n                refs.errorMessages && { errorMessage: { format: message } }),\n        });\n    }\n    else {\n        setResponseValueAndErrors(schema, \"format\", value, message, refs);\n    }\n}\n// Adds a \"pattern\" keyword to the schema. If a pattern exists, both patterns will be joined in an allOf-node, along with subsequent ones.\nfunction addPattern(schema, regex, message, refs) {\n    if (schema.pattern || schema.allOf?.some((x) => x.pattern)) {\n        if (!schema.allOf) {\n            schema.allOf = [];\n        }\n        if (schema.pattern) {\n            schema.allOf.push({\n                pattern: schema.pattern,\n                ...(schema.errorMessage &&\n                    refs.errorMessages && {\n                    errorMessage: { pattern: schema.errorMessage.pattern },\n                }),\n            });\n            delete schema.pattern;\n            if (schema.errorMessage) {\n                delete schema.errorMessage.pattern;\n                if (Object.keys(schema.errorMessage).length === 0) {\n                    delete schema.errorMessage;\n                }\n            }\n        }\n        schema.allOf.push({\n            pattern: stringifyRegExpWithFlags(regex, refs),\n            ...(message &&\n                refs.errorMessages && { errorMessage: { pattern: message } }),\n        });\n    }\n    else {\n        setResponseValueAndErrors(schema, \"pattern\", stringifyRegExpWithFlags(regex, refs), message, refs);\n    }\n}\n// Mutate z.string.regex() in a best attempt to accommodate for regex flags when applyRegexFlags is true\nfunction stringifyRegExpWithFlags(regex, refs) {\n    if (!refs.applyRegexFlags || !regex.flags) {\n        return regex.source;\n    }\n    // Currently handled flags\n    const flags = {\n        i: regex.flags.includes(\"i\"),\n        m: regex.flags.includes(\"m\"),\n        s: regex.flags.includes(\"s\"), // `.` matches newlines\n    };\n    // The general principle here is to step through each character, one at a time, applying mutations as flags require. We keep track when the current character is escaped, and when it's inside a group /like [this]/ or (also) a range like /[a-z]/. The following is fairly brittle imperative code; edit at your peril!\n    const source = flags.i ? regex.source.toLowerCase() : regex.source;\n    let pattern = \"\";\n    let isEscaped = false;\n    let inCharGroup = false;\n    let inCharRange = false;\n    for (let i = 0; i < source.length; i++) {\n        if (isEscaped) {\n            pattern += source[i];\n            isEscaped = false;\n            continue;\n        }\n        if (flags.i) {\n            if (inCharGroup) {\n                if (source[i].match(/[a-z]/)) {\n                    if (inCharRange) {\n                        pattern += source[i];\n                        pattern += `${source[i - 2]}-${source[i]}`.toUpperCase();\n                        inCharRange = false;\n                    }\n                    else if (source[i + 1] === \"-\" && source[i + 2]?.match(/[a-z]/)) {\n                        pattern += source[i];\n                        inCharRange = true;\n                    }\n                    else {\n                        pattern += `${source[i]}${source[i].toUpperCase()}`;\n                    }\n                    continue;\n                }\n            }\n            else if (source[i].match(/[a-z]/)) {\n                pattern += `[${source[i]}${source[i].toUpperCase()}]`;\n                continue;\n            }\n        }\n        if (flags.m) {\n            if (source[i] === \"^\") {\n                pattern += `(^|(?<=[\\r\\n]))`;\n                continue;\n            }\n            else if (source[i] === \"$\") {\n                pattern += `($|(?=[\\r\\n]))`;\n                continue;\n            }\n        }\n        if (flags.s && source[i] === \".\") {\n            pattern += inCharGroup ? `${source[i]}\\r\\n` : `[${source[i]}\\r\\n]`;\n            continue;\n        }\n        pattern += source[i];\n        if (source[i] === \"\\\\\") {\n            isEscaped = true;\n        }\n        else if (inCharGroup && source[i] === \"]\") {\n            inCharGroup = false;\n        }\n        else if (!inCharGroup && source[i] === \"[\") {\n            inCharGroup = true;\n        }\n    }\n    try {\n        new RegExp(pattern);\n    }\n    catch {\n        console.warn(`Could not convert regex pattern at ${refs.currentPath.join(\"/\")} to a flag-independent form! Falling back to the flag-ignorant source`);\n        return regex.source;\n    }\n    return pattern;\n}\n", "import { ZodFirstPartyTypeKind, } from \"zod\";\nimport { parseDef } from \"../parseDef.js\";\nimport { parseStringDef } from \"./string.js\";\nimport { parseBrandedDef } from \"./branded.js\";\nexport function parseRecordDef(def, refs) {\n    if (refs.target === \"openAi\") {\n        console.warn(\"Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead.\");\n    }\n    if (refs.target === \"openApi3\" &&\n        def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodEnum) {\n        return {\n            type: \"object\",\n            required: def.keyType._def.values,\n            properties: def.keyType._def.values.reduce((acc, key) => ({\n                ...acc,\n                [key]: parseDef(def.valueType._def, {\n                    ...refs,\n                    currentPath: [...refs.currentPath, \"properties\", key],\n                }) ?? {},\n            }), {}),\n            additionalProperties: refs.rejectedAdditionalProperties,\n        };\n    }\n    const schema = {\n        type: \"object\",\n        additionalProperties: parseDef(def.valueType._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"additionalProperties\"],\n        }) ?? refs.allowedAdditionalProperties,\n    };\n    if (refs.target === \"openApi3\") {\n        return schema;\n    }\n    if (def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodString &&\n        def.keyType._def.checks?.length) {\n        const { type, ...keyType } = parseStringDef(def.keyType._def, refs);\n        return {\n            ...schema,\n            propertyNames: keyType,\n        };\n    }\n    else if (def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodEnum) {\n        return {\n            ...schema,\n            propertyNames: {\n                enum: def.keyType._def.values,\n            },\n        };\n    }\n    else if (def.keyType?._def.typeName === ZodFirstPartyTypeKind.ZodBranded &&\n        def.keyType._def.type._def.typeName === ZodFirstPartyTypeKind.ZodString &&\n        def.keyType._def.type._def.checks?.length) {\n        const { type, ...keyType } = parseBrandedDef(def.keyType._def, refs);\n        return {\n            ...schema,\n            propertyNames: keyType,\n        };\n    }\n    return schema;\n}\n", "import { parseDef } from \"../parseDef.js\";\nimport { parseRecordDef } from \"./record.js\";\nexport function parseMapDef(def, refs) {\n    if (refs.mapStrategy === \"record\") {\n        return parseRecordDef(def, refs);\n    }\n    const keys = parseDef(def.keyType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"items\", \"items\", \"0\"],\n    }) || {};\n    const values = parseDef(def.valueType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"items\", \"items\", \"1\"],\n    }) || {};\n    return {\n        type: \"array\",\n        maxItems: 125,\n        items: {\n            type: \"array\",\n            items: [keys, values],\n            minItems: 2,\n            maxItems: 2,\n        },\n    };\n}\n", "export function parseNativeEnumDef(def) {\n    const object = def.values;\n    const actualKeys = Object.keys(def.values).filter((key) => {\n        return typeof object[object[key]] !== \"number\";\n    });\n    const actualValues = actualKeys.map((key) => object[key]);\n    const parsedTypes = Array.from(new Set(actualValues.map((values) => typeof values)));\n    return {\n        type: parsedTypes.length === 1\n            ? parsedTypes[0] === \"string\"\n                ? \"string\"\n                : \"number\"\n            : [\"string\", \"number\"],\n        enum: actualValues,\n    };\n}\n", "export function parseNeverDef() {\n    return {\n        not: {},\n    };\n}\n", "export function parseNullDef(refs) {\n    return refs.target === \"openApi3\"\n        ? {\n            enum: [\"null\"],\n            nullable: true,\n        }\n        : {\n            type: \"null\",\n        };\n}\n", "import { parseDef } from \"../parseDef.js\";\nexport const primitiveMappings = {\n    ZodString: \"string\",\n    ZodNumber: \"number\",\n    ZodBigInt: \"integer\",\n    ZodBoolean: \"boolean\",\n    ZodNull: \"null\",\n};\nexport function parseUnionDef(def, refs) {\n    if (refs.target === \"openApi3\")\n        return asAnyOf(def, refs);\n    const options = def.options instanceof Map ? Array.from(def.options.values()) : def.options;\n    // This blocks tries to look ahead a bit to produce nicer looking schemas with type array instead of anyOf.\n    if (options.every((x) => x._def.typeName in primitiveMappings &&\n        (!x._def.checks || !x._def.checks.length))) {\n        // all types in union are primitive and lack checks, so might as well squash into {type: [...]}\n        const types = options.reduce((types, x) => {\n            const type = primitiveMappings[x._def.typeName]; //Can be safely casted due to row 43\n            return type && !types.includes(type) ? [...types, type] : types;\n        }, []);\n        return {\n            type: types.length > 1 ? types : types[0],\n        };\n    }\n    else if (options.every((x) => x._def.typeName === \"ZodLiteral\" && !x.description)) {\n        // all options literals\n        const types = options.reduce((acc, x) => {\n            const type = typeof x._def.value;\n            switch (type) {\n                case \"string\":\n                case \"number\":\n                case \"boolean\":\n                    return [...acc, type];\n                case \"bigint\":\n                    return [...acc, \"integer\"];\n                case \"object\":\n                    if (x._def.value === null)\n                        return [...acc, \"null\"];\n                case \"symbol\":\n                case \"undefined\":\n                case \"function\":\n                default:\n                    return acc;\n            }\n        }, []);\n        if (types.length === options.length) {\n            // all the literals are primitive, as far as null can be considered primitive\n            const uniqueTypes = types.filter((x, i, a) => a.indexOf(x) === i);\n            return {\n                type: uniqueTypes.length > 1 ? uniqueTypes : uniqueTypes[0],\n                enum: options.reduce((acc, x) => {\n                    return acc.includes(x._def.value) ? acc : [...acc, x._def.value];\n                }, []),\n            };\n        }\n    }\n    else if (options.every((x) => x._def.typeName === \"ZodEnum\")) {\n        return {\n            type: \"string\",\n            enum: options.reduce((acc, x) => [\n                ...acc,\n                ...x._def.values.filter((x) => !acc.includes(x)),\n            ], []),\n        };\n    }\n    return asAnyOf(def, refs);\n}\nconst asAnyOf = (def, refs) => {\n    const anyOf = (def.options instanceof Map\n        ? Array.from(def.options.values())\n        : def.options)\n        .map((x, i) => parseDef(x._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"anyOf\", `${i}`],\n    }))\n        .filter((x) => !!x &&\n        (!refs.strictUnions ||\n            (typeof x === \"object\" && Object.keys(x).length > 0)));\n    return anyOf.length ? { anyOf } : undefined;\n};\n", "import { parseDef } from \"../parseDef.js\";\nimport { primitiveMappings } from \"./union.js\";\nexport function parseNullableDef(def, refs) {\n    if ([\"ZodString\", \"ZodNumber\", \"ZodBigInt\", \"ZodBoolean\", \"ZodNull\"].includes(def.innerType._def.typeName) &&\n        (!def.innerType._def.checks || !def.innerType._def.checks.length)) {\n        if (refs.target === \"openApi3\") {\n            return {\n                type: primitiveMappings[def.innerType._def.typeName],\n                nullable: true,\n            };\n        }\n        return {\n            type: [\n                primitiveMappings[def.innerType._def.typeName],\n                \"null\",\n            ],\n        };\n    }\n    if (refs.target === \"openApi3\") {\n        const base = parseDef(def.innerType._def, {\n            ...refs,\n            currentPath: [...refs.currentPath],\n        });\n        if (base && \"$ref\" in base)\n            return { allOf: [base], nullable: true };\n        return base && { ...base, nullable: true };\n    }\n    const base = parseDef(def.innerType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"anyOf\", \"0\"],\n    });\n    return base && { anyOf: [base, { type: \"null\" }] };\n}\n", "import { addErrorMessage, setResponseValueAndErrors, } from \"../errorMessages.js\";\nexport function parseNumberDef(def, refs) {\n    const res = {\n        type: \"number\",\n    };\n    if (!def.checks)\n        return res;\n    for (const check of def.checks) {\n        switch (check.kind) {\n            case \"int\":\n                res.type = \"integer\";\n                addErrorMessage(res, \"type\", check.message, refs);\n                break;\n            case \"min\":\n                if (refs.target === \"jsonSchema7\") {\n                    if (check.inclusive) {\n                        setResponseValueAndErrors(res, \"minimum\", check.value, check.message, refs);\n                    }\n                    else {\n                        setResponseValueAndErrors(res, \"exclusiveMinimum\", check.value, check.message, refs);\n                    }\n                }\n                else {\n                    if (!check.inclusive) {\n                        res.exclusiveMinimum = true;\n                    }\n                    setResponseValueAndErrors(res, \"minimum\", check.value, check.message, refs);\n                }\n                break;\n            case \"max\":\n                if (refs.target === \"jsonSchema7\") {\n                    if (check.inclusive) {\n                        setResponseValueAndErrors(res, \"maximum\", check.value, check.message, refs);\n                    }\n                    else {\n                        setResponseValueAndErrors(res, \"exclusiveMaximum\", check.value, check.message, refs);\n                    }\n                }\n                else {\n                    if (!check.inclusive) {\n                        res.exclusiveMaximum = true;\n                    }\n                    setResponseValueAndErrors(res, \"maximum\", check.value, check.message, refs);\n                }\n                break;\n            case \"multipleOf\":\n                setResponseValueAndErrors(res, \"multipleOf\", check.value, check.message, refs);\n                break;\n        }\n    }\n    return res;\n}\n", "import { ZodOptional } from \"zod\";\nimport { parseDef } from \"../parseDef.js\";\nexport function parseObjectDef(def, refs) {\n    const forceOptionalIntoNullable = refs.target === \"openAi\";\n    const result = {\n        type: \"object\",\n        properties: {},\n    };\n    const required = [];\n    const shape = def.shape();\n    for (const propName in shape) {\n        let propDef = shape[propName];\n        if (propDef === undefined || propDef._def === undefined) {\n            continue;\n        }\n        let propOptional = safeIsOptional(propDef);\n        if (propOptional && forceOptionalIntoNullable) {\n            if (propDef instanceof ZodOptional) {\n                propDef = propDef._def.innerType;\n            }\n            if (!propDef.isNullable()) {\n                propDef = propDef.nullable();\n            }\n            propOptional = false;\n        }\n        const parsedDef = parseDef(propDef._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"properties\", propName],\n            propertyPath: [...refs.currentPath, \"properties\", propName],\n        });\n        if (parsedDef === undefined) {\n            continue;\n        }\n        result.properties[propName] = parsedDef;\n        if (!propOptional) {\n            required.push(propName);\n        }\n    }\n    if (required.length) {\n        result.required = required;\n    }\n    const additionalProperties = decideAdditionalProperties(def, refs);\n    if (additionalProperties !== undefined) {\n        result.additionalProperties = additionalProperties;\n    }\n    return result;\n}\nfunction decideAdditionalProperties(def, refs) {\n    if (def.catchall._def.typeName !== \"ZodNever\") {\n        return parseDef(def.catchall._def, {\n            ...refs,\n            currentPath: [...refs.currentPath, \"additionalProperties\"],\n        });\n    }\n    switch (def.unknownKeys) {\n        case \"passthrough\":\n            return refs.allowedAdditionalProperties;\n        case \"strict\":\n            return refs.rejectedAdditionalProperties;\n        case \"strip\":\n            return refs.removeAdditionalStrategy === \"strict\"\n                ? refs.allowedAdditionalProperties\n                : refs.rejectedAdditionalProperties;\n    }\n}\nfunction safeIsOptional(schema) {\n    try {\n        return schema.isOptional();\n    }\n    catch {\n        return true;\n    }\n}\n", "import { parseDef } from \"../parseDef.js\";\nexport const parseOptionalDef = (def, refs) => {\n    if (refs.currentPath.toString() === refs.propertyPath?.toString()) {\n        return parseDef(def.innerType._def, refs);\n    }\n    const innerSchema = parseDef(def.innerType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"anyOf\", \"1\"],\n    });\n    return innerSchema\n        ? {\n            anyOf: [\n                {\n                    not: {},\n                },\n                innerSchema,\n            ],\n        }\n        : {};\n};\n", "import { parseDef } from \"../parseDef.js\";\nexport const parsePipelineDef = (def, refs) => {\n    if (refs.pipeStrategy === \"input\") {\n        return parseDef(def.in._def, refs);\n    }\n    else if (refs.pipeStrategy === \"output\") {\n        return parseDef(def.out._def, refs);\n    }\n    const a = parseDef(def.in._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"allOf\", \"0\"],\n    });\n    const b = parseDef(def.out._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"allOf\", a ? \"1\" : \"0\"],\n    });\n    return {\n        allOf: [a, b].filter((x) => x !== undefined),\n    };\n};\n", "import { parseDef } from \"../parseDef.js\";\nexport function parsePromiseDef(def, refs) {\n    return parseDef(def.type._def, refs);\n}\n", "import { setResponseValueAndErrors } from \"../errorMessages.js\";\nimport { parseDef } from \"../parseDef.js\";\nexport function parseSetDef(def, refs) {\n    const items = parseDef(def.valueType._def, {\n        ...refs,\n        currentPath: [...refs.currentPath, \"items\"],\n    });\n    const schema = {\n        type: \"array\",\n        uniqueItems: true,\n        items,\n    };\n    if (def.minSize) {\n        setResponseValueAndErrors(schema, \"minItems\", def.minSize.value, def.minSize.message, refs);\n    }\n    if (def.maxSize) {\n        setResponseValueAndErrors(schema, \"maxItems\", def.maxSize.value, def.maxSize.message, refs);\n    }\n    return schema;\n}\n", "import { parseDef } from \"../parseDef.js\";\nexport function parseTupleDef(def, refs) {\n    if (def.rest) {\n        return {\n            type: \"array\",\n            minItems: def.items.length,\n            items: def.items\n                .map((x, i) => parseDef(x._def, {\n                ...refs,\n                currentPath: [...refs.currentPath, \"items\", `${i}`],\n            }))\n                .reduce((acc, x) => (x === undefined ? acc : [...acc, x]), []),\n            additionalItems: parseDef(def.rest._def, {\n                ...refs,\n                currentPath: [...refs.currentPath, \"additionalItems\"],\n            }),\n        };\n    }\n    else {\n        return {\n            type: \"array\",\n            minItems: def.items.length,\n            maxItems: def.items.length,\n            items: def.items\n                .map((x, i) => parseDef(x._def, {\n                ...refs,\n                currentPath: [...refs.currentPath, \"items\", `${i}`],\n            }))\n                .reduce((acc, x) => (x === undefined ? acc : [...acc, x]), []),\n        };\n    }\n}\n", "export function parseUndefinedDef() {\n    return {\n        not: {},\n    };\n}\n", "export function parseUnknownDef() {\n    return {};\n}\n", "import { parseDef } from \"../parseDef.js\";\nexport const parseReadonlyDef = (def, refs) => {\n    return parseDef(def.innerType._def, refs);\n};\n", "import { Zod<PERSON>irstPartyTypeKind } from \"zod\";\nimport { parseAnyDef } from \"./parsers/any.js\";\nimport { parseArrayDef } from \"./parsers/array.js\";\nimport { parseBigintDef } from \"./parsers/bigint.js\";\nimport { parseBooleanDef } from \"./parsers/boolean.js\";\nimport { parseBrandedDef } from \"./parsers/branded.js\";\nimport { parseCatchDef } from \"./parsers/catch.js\";\nimport { parseDateDef } from \"./parsers/date.js\";\nimport { parseDefaultDef } from \"./parsers/default.js\";\nimport { parseEffectsDef } from \"./parsers/effects.js\";\nimport { parseEnumDef } from \"./parsers/enum.js\";\nimport { parseIntersectionDef } from \"./parsers/intersection.js\";\nimport { parseLiteralDef } from \"./parsers/literal.js\";\nimport { parseMapDef } from \"./parsers/map.js\";\nimport { parseNativeEnumDef } from \"./parsers/nativeEnum.js\";\nimport { parseNeverDef } from \"./parsers/never.js\";\nimport { parseNullDef } from \"./parsers/null.js\";\nimport { parseNullableDef } from \"./parsers/nullable.js\";\nimport { parseNumberDef } from \"./parsers/number.js\";\nimport { parseObjectDef } from \"./parsers/object.js\";\nimport { parseOptionalDef } from \"./parsers/optional.js\";\nimport { parsePipelineDef } from \"./parsers/pipeline.js\";\nimport { parsePromiseDef } from \"./parsers/promise.js\";\nimport { parseRecordDef } from \"./parsers/record.js\";\nimport { parseSetDef } from \"./parsers/set.js\";\nimport { parseStringDef } from \"./parsers/string.js\";\nimport { parseTupleDef } from \"./parsers/tuple.js\";\nimport { parseUndefinedDef } from \"./parsers/undefined.js\";\nimport { parseUnionDef } from \"./parsers/union.js\";\nimport { parseUnknownDef } from \"./parsers/unknown.js\";\nimport { parseReadonlyDef } from \"./parsers/readonly.js\";\nexport const selectParser = (def, typeName, refs) => {\n    switch (typeName) {\n        case ZodFirstPartyTypeKind.ZodString:\n            return parseStringDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodNumber:\n            return parseNumberDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodObject:\n            return parseObjectDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodBigInt:\n            return parseBigintDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodBoolean:\n            return parseBooleanDef();\n        case ZodFirstPartyTypeKind.ZodDate:\n            return parseDateDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodUndefined:\n            return parseUndefinedDef();\n        case ZodFirstPartyTypeKind.ZodNull:\n            return parseNullDef(refs);\n        case ZodFirstPartyTypeKind.ZodArray:\n            return parseArrayDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodUnion:\n        case ZodFirstPartyTypeKind.ZodDiscriminatedUnion:\n            return parseUnionDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodIntersection:\n            return parseIntersectionDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodTuple:\n            return parseTupleDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodRecord:\n            return parseRecordDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodLiteral:\n            return parseLiteralDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodEnum:\n            return parseEnumDef(def);\n        case ZodFirstPartyTypeKind.ZodNativeEnum:\n            return parseNativeEnumDef(def);\n        case ZodFirstPartyTypeKind.ZodNullable:\n            return parseNullableDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodOptional:\n            return parseOptionalDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodMap:\n            return parseMapDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodSet:\n            return parseSetDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodLazy:\n            return () => def.getter()._def;\n        case ZodFirstPartyTypeKind.ZodPromise:\n            return parsePromiseDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodNaN:\n        case ZodFirstPartyTypeKind.ZodNever:\n            return parseNeverDef();\n        case ZodFirstPartyTypeKind.ZodEffects:\n            return parseEffectsDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodAny:\n            return parseAnyDef();\n        case ZodFirstPartyTypeKind.ZodUnknown:\n            return parseUnknownDef();\n        case ZodFirstPartyTypeKind.ZodDefault:\n            return parseDefaultDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodBranded:\n            return parseBrandedDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodReadonly:\n            return parseReadonlyDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodCatch:\n            return parseCatchDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodPipeline:\n            return parsePipelineDef(def, refs);\n        case ZodFirstPartyTypeKind.ZodFunction:\n        case ZodFirstPartyTypeKind.ZodVoid:\n        case ZodFirstPartyTypeKind.ZodSymbol:\n            return undefined;\n        default:\n            /* c8 ignore next */\n            return ((_) => undefined)(typeName);\n    }\n};\n", "import { ignoreOverride } from \"./Options.js\";\nimport { selectParser } from \"./selectParser.js\";\nexport function parseDef(def, refs, forceResolution = false) {\n    const seenItem = refs.seen.get(def);\n    if (refs.override) {\n        const overrideResult = refs.override?.(def, refs, seenItem, forceResolution);\n        if (overrideResult !== ignoreOverride) {\n            return overrideResult;\n        }\n    }\n    if (seenItem && !forceResolution) {\n        const seenSchema = get$ref(seenItem, refs);\n        if (seenSchema !== undefined) {\n            return seenSchema;\n        }\n    }\n    const newItem = { def, path: refs.currentPath, jsonSchema: undefined };\n    refs.seen.set(def, newItem);\n    const jsonSchemaOrGetter = selectParser(def, def.typeName, refs);\n    // If the return was a function, then the inner definition needs to be extracted before a call to parseDef (recursive)\n    const jsonSchema = typeof jsonSchemaOrGetter === \"function\"\n        ? parseDef(jsonSchemaOrGetter(), refs)\n        : jsonSchemaOrGetter;\n    if (jsonSchema) {\n        addMeta(def, refs, jsonSchema);\n    }\n    if (refs.postProcess) {\n        const postProcessResult = refs.postProcess(jsonSchema, def, refs);\n        newItem.jsonSchema = jsonSchema;\n        return postProcessResult;\n    }\n    newItem.jsonSchema = jsonSchema;\n    return jsonSchema;\n}\nconst get$ref = (item, refs) => {\n    switch (refs.$refStrategy) {\n        case \"root\":\n            return { $ref: item.path.join(\"/\") };\n        case \"relative\":\n            return { $ref: getRelativePath(refs.currentPath, item.path) };\n        case \"none\":\n        case \"seen\": {\n            if (item.path.length < refs.currentPath.length &&\n                item.path.every((value, index) => refs.currentPath[index] === value)) {\n                console.warn(`Recursive reference detected at ${refs.currentPath.join(\"/\")}! Defaulting to any`);\n                return {};\n            }\n            return refs.$refStrategy === \"seen\" ? {} : undefined;\n        }\n    }\n};\nconst getRelativePath = (pathA, pathB) => {\n    let i = 0;\n    for (; i < pathA.length && i < pathB.length; i++) {\n        if (pathA[i] !== pathB[i])\n            break;\n    }\n    return [(pathA.length - i).toString(), ...pathB.slice(i)].join(\"/\");\n};\nconst addMeta = (def, refs, jsonSchema) => {\n    if (def.description) {\n        jsonSchema.description = def.description;\n        if (refs.markdownDescription) {\n            jsonSchema.markdownDescription = def.description;\n        }\n    }\n    return jsonSchema;\n};\n", "import { parseDef } from \"./parseDef.js\";\nimport { getRefs } from \"./Refs.js\";\nconst zodToJsonSchema = (schema, options) => {\n    const refs = getRefs(options);\n    const definitions = typeof options === \"object\" && options.definitions\n        ? Object.entries(options.definitions).reduce((acc, [name, schema]) => ({\n            ...acc,\n            [name]: parseDef(schema._def, {\n                ...refs,\n                currentPath: [...refs.basePath, refs.definitionPath, name],\n            }, true) ?? {},\n        }), {})\n        : undefined;\n    const name = typeof options === \"string\"\n        ? options\n        : options?.nameStrategy === \"title\"\n            ? undefined\n            : options?.name;\n    const main = parseDef(schema._def, name === undefined\n        ? refs\n        : {\n            ...refs,\n            currentPath: [...refs.basePath, refs.definitionPath, name],\n        }, false) ?? {};\n    const title = typeof options === \"object\" &&\n        options.name !== undefined &&\n        options.nameStrategy === \"title\"\n        ? options.name\n        : undefined;\n    if (title !== undefined) {\n        main.title = title;\n    }\n    const combined = name === undefined\n        ? definitions\n            ? {\n                ...main,\n                [refs.definitionPath]: definitions,\n            }\n            : main\n        : {\n            $ref: [\n                ...(refs.$refStrategy === \"relative\" ? [] : refs.basePath),\n                refs.definitionPath,\n                name,\n            ].join(\"/\"),\n            [refs.definitionPath]: {\n                ...definitions,\n                [name]: main,\n            },\n        };\n    if (refs.target === \"jsonSchema7\") {\n        combined.$schema = \"http://json-schema.org/draft-07/schema#\";\n    }\n    else if (refs.target === \"jsonSchema2019-09\" || refs.target === \"openAi\") {\n        combined.$schema = \"https://json-schema.org/draft/2019-09/schema#\";\n    }\n    if (refs.target === \"openAi\" &&\n        (\"anyOf\" in combined ||\n            \"oneOf\" in combined ||\n            \"allOf\" in combined ||\n            (\"type\" in combined && Array.isArray(combined.type)))) {\n        console.warn(\"Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property.\");\n    }\n    return combined;\n};\nexport { zodToJsonSchema };\n", "export * from \"./Options.js\";\nexport * from \"./Refs.js\";\nexport * from \"./errorMessages.js\";\nexport * from \"./parseDef.js\";\nexport * from \"./parseTypes.js\";\nexport * from \"./parsers/any.js\";\nexport * from \"./parsers/array.js\";\nexport * from \"./parsers/bigint.js\";\nexport * from \"./parsers/boolean.js\";\nexport * from \"./parsers/branded.js\";\nexport * from \"./parsers/catch.js\";\nexport * from \"./parsers/date.js\";\nexport * from \"./parsers/default.js\";\nexport * from \"./parsers/effects.js\";\nexport * from \"./parsers/enum.js\";\nexport * from \"./parsers/intersection.js\";\nexport * from \"./parsers/literal.js\";\nexport * from \"./parsers/map.js\";\nexport * from \"./parsers/nativeEnum.js\";\nexport * from \"./parsers/never.js\";\nexport * from \"./parsers/null.js\";\nexport * from \"./parsers/nullable.js\";\nexport * from \"./parsers/number.js\";\nexport * from \"./parsers/object.js\";\nexport * from \"./parsers/optional.js\";\nexport * from \"./parsers/pipeline.js\";\nexport * from \"./parsers/promise.js\";\nexport * from \"./parsers/readonly.js\";\nexport * from \"./parsers/record.js\";\nexport * from \"./parsers/set.js\";\nexport * from \"./parsers/string.js\";\nexport * from \"./parsers/tuple.js\";\nexport * from \"./parsers/undefined.js\";\nexport * from \"./parsers/union.js\";\nexport * from \"./parsers/unknown.js\";\nexport * from \"./selectParser.js\";\nexport * from \"./zodToJsonSchema.js\";\nimport { zodToJsonSchema } from \"./zodToJsonSchema.js\";\nexport default zodToJsonSchema;\n"], "mappings": ";;;;;;AAAO,IAAM,iBAAiB,OAAO,mDAAmD;AACjF,IAAM,kBAAkB,CAAC,YAAY,QAAQ;AAChD,MAAI,IAAI,aAAa;AACjB,QAAI;AACA,aAAO;AAAA,QACH,GAAG;AAAA,QACH,GAAG,KAAK,MAAM,IAAI,WAAW;AAAA,MACjC;AAAA,IACJ,QACM;AAAA,IAAE;AAAA,EACZ;AACA,SAAO;AACX;AACO,IAAM,iBAAiB;AAAA,EAC1B,MAAM;AAAA,EACN,cAAc;AAAA,EACd,UAAU,CAAC,GAAG;AAAA,EACd,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,aAAa;AAAA,EACb,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8BAA8B;AAAA,EAC9B,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,aAAa,CAAC;AAAA,EACd,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,cAAc;AAClB;AACO,IAAM,oBAAoB,CAAC,YAAa,OAAO,YAAY,WAC5D;AAAA,EACE,GAAG;AAAA,EACH,MAAM;AACV,IACE;AAAA,EACE,GAAG;AAAA,EACH,GAAG;AACP;;;AC3CG,IAAM,UAAU,CAAC,YAAY;AAChC,QAAM,WAAW,kBAAkB,OAAO;AAC1C,QAAM,cAAc,SAAS,SAAS,SAChC,CAAC,GAAG,SAAS,UAAU,SAAS,gBAAgB,SAAS,IAAI,IAC7D,SAAS;AACf,SAAO;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA,cAAc;AAAA,IACd,MAAM,IAAI,IAAI,OAAO,QAAQ,SAAS,WAAW,EAAE,IAAI,CAAC,CAAC,MAAM,GAAG,MAAM;AAAA,MACpE,IAAI;AAAA,MACJ;AAAA,QACI,KAAK,IAAI;AAAA,QACT,MAAM,CAAC,GAAG,SAAS,UAAU,SAAS,gBAAgB,IAAI;AAAA;AAAA,QAE1D,YAAY;AAAA,MAChB;AAAA,IACJ,CAAC,CAAC;AAAA,EACN;AACJ;;;ACpBO,SAAS,gBAAgB,KAAK,KAAK,cAAc,MAAM;AAC1D,MAAI,EAAC,6BAAM;AACP;AACJ,MAAI,cAAc;AACd,QAAI,eAAe;AAAA,MACf,GAAG,IAAI;AAAA,MACP,CAAC,GAAG,GAAG;AAAA,IACX;AAAA,EACJ;AACJ;AACO,SAAS,0BAA0B,KAAK,KAAK,OAAO,cAAc,MAAM;AAC3E,MAAI,GAAG,IAAI;AACX,kBAAgB,KAAK,KAAK,cAAc,IAAI;AAChD;;;ACbO,SAAS,cAAc;AAC1B,SAAO,CAAC;AACZ;;;ACCO,SAAS,cAAc,KAAK,MAAM;AAHzC;AAII,QAAM,MAAM;AAAA,IACR,MAAM;AAAA,EACV;AACA,QAAI,SAAI,SAAJ,mBAAU,WACV,eAAI,SAAJ,mBAAU,SAAV,mBAAgB,cAAa,sBAAsB,QAAQ;AAC3D,QAAI,QAAQ,SAAS,IAAI,KAAK,MAAM;AAAA,MAChC,GAAG;AAAA,MACH,aAAa,CAAC,GAAG,KAAK,aAAa,OAAO;AAAA,IAC9C,CAAC;AAAA,EACL;AACA,MAAI,IAAI,WAAW;AACf,8BAA0B,KAAK,YAAY,IAAI,UAAU,OAAO,IAAI,UAAU,SAAS,IAAI;AAAA,EAC/F;AACA,MAAI,IAAI,WAAW;AACf,8BAA0B,KAAK,YAAY,IAAI,UAAU,OAAO,IAAI,UAAU,SAAS,IAAI;AAAA,EAC/F;AACA,MAAI,IAAI,aAAa;AACjB,8BAA0B,KAAK,YAAY,IAAI,YAAY,OAAO,IAAI,YAAY,SAAS,IAAI;AAC/F,8BAA0B,KAAK,YAAY,IAAI,YAAY,OAAO,IAAI,YAAY,SAAS,IAAI;AAAA,EACnG;AACA,SAAO;AACX;;;ACxBO,SAAS,eAAe,KAAK,MAAM;AACtC,QAAM,MAAM;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,EACZ;AACA,MAAI,CAAC,IAAI;AACL,WAAO;AACX,aAAW,SAAS,IAAI,QAAQ;AAC5B,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AACD,YAAI,KAAK,WAAW,eAAe;AAC/B,cAAI,MAAM,WAAW;AACjB,sCAA0B,KAAK,WAAW,MAAM,OAAO,MAAM,SAAS,IAAI;AAAA,UAC9E,OACK;AACD,sCAA0B,KAAK,oBAAoB,MAAM,OAAO,MAAM,SAAS,IAAI;AAAA,UACvF;AAAA,QACJ,OACK;AACD,cAAI,CAAC,MAAM,WAAW;AAClB,gBAAI,mBAAmB;AAAA,UAC3B;AACA,oCAA0B,KAAK,WAAW,MAAM,OAAO,MAAM,SAAS,IAAI;AAAA,QAC9E;AACA;AAAA,MACJ,KAAK;AACD,YAAI,KAAK,WAAW,eAAe;AAC/B,cAAI,MAAM,WAAW;AACjB,sCAA0B,KAAK,WAAW,MAAM,OAAO,MAAM,SAAS,IAAI;AAAA,UAC9E,OACK;AACD,sCAA0B,KAAK,oBAAoB,MAAM,OAAO,MAAM,SAAS,IAAI;AAAA,UACvF;AAAA,QACJ,OACK;AACD,cAAI,CAAC,MAAM,WAAW;AAClB,gBAAI,mBAAmB;AAAA,UAC3B;AACA,oCAA0B,KAAK,WAAW,MAAM,OAAO,MAAM,SAAS,IAAI;AAAA,QAC9E;AACA;AAAA,MACJ,KAAK;AACD,kCAA0B,KAAK,cAAc,MAAM,OAAO,MAAM,SAAS,IAAI;AAC7E;AAAA,IACR;AAAA,EACJ;AACA,SAAO;AACX;;;AChDO,SAAS,kBAAkB;AAC9B,SAAO;AAAA,IACH,MAAM;AAAA,EACV;AACJ;;;ACHO,SAAS,gBAAgB,MAAM,MAAM;AACxC,SAAO,SAAS,KAAK,KAAK,MAAM,IAAI;AACxC;;;ACFO,IAAM,gBAAgB,CAAC,KAAK,SAAS;AACxC,SAAO,SAAS,IAAI,UAAU,MAAM,IAAI;AAC5C;;;ACFO,SAAS,aAAa,KAAK,MAAM,sBAAsB;AAC1D,QAAM,WAAW,wBAAwB,KAAK;AAC9C,MAAI,MAAM,QAAQ,QAAQ,GAAG;AACzB,WAAO;AAAA,MACH,OAAO,SAAS,IAAI,CAAC,MAAM,MAAM,aAAa,KAAK,MAAM,IAAI,CAAC;AAAA,IAClE;AAAA,EACJ;AACA,UAAQ,UAAU;AAAA,IACd,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,MAAM;AAAA,QACN,QAAQ;AAAA,MACZ;AAAA,IACJ,KAAK;AACD,aAAO;AAAA,QACH,MAAM;AAAA,QACN,QAAQ;AAAA,MACZ;AAAA,IACJ,KAAK;AACD,aAAO,kBAAkB,KAAK,IAAI;AAAA,EAC1C;AACJ;AACA,IAAM,oBAAoB,CAAC,KAAK,SAAS;AACrC,QAAM,MAAM;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,EACZ;AACA,MAAI,KAAK,WAAW,YAAY;AAC5B,WAAO;AAAA,EACX;AACA,aAAW,SAAS,IAAI,QAAQ;AAC5B,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AACD;AAAA,UAA0B;AAAA,UAAK;AAAA,UAAW,MAAM;AAAA;AAAA,UAChD,MAAM;AAAA,UAAS;AAAA,QAAI;AACnB;AAAA,MACJ,KAAK;AACD;AAAA,UAA0B;AAAA,UAAK;AAAA,UAAW,MAAM;AAAA;AAAA,UAChD,MAAM;AAAA,UAAS;AAAA,QAAI;AACnB;AAAA,IACR;AAAA,EACJ;AACA,SAAO;AACX;;;AC5CO,SAAS,gBAAgB,MAAM,MAAM;AACxC,SAAO;AAAA,IACH,GAAG,SAAS,KAAK,UAAU,MAAM,IAAI;AAAA,IACrC,SAAS,KAAK,aAAa;AAAA,EAC/B;AACJ;;;ACLO,SAAS,gBAAgB,MAAM,MAAM;AACxC,SAAO,KAAK,mBAAmB,UACzB,SAAS,KAAK,OAAO,MAAM,IAAI,IAC/B,CAAC;AACX;;;ACLO,SAAS,aAAa,KAAK;AAC9B,SAAO;AAAA,IACH,MAAM;AAAA,IACN,MAAM,MAAM,KAAK,IAAI,MAAM;AAAA,EAC/B;AACJ;;;ACJA,IAAM,yBAAyB,CAAC,SAAS;AACrC,MAAI,UAAU,QAAQ,KAAK,SAAS;AAChC,WAAO;AACX,SAAO,WAAW;AACtB;AACO,SAAS,qBAAqB,KAAK,MAAM;AAC5C,QAAM,QAAQ;AAAA,IACV,SAAS,IAAI,KAAK,MAAM;AAAA,MACpB,GAAG;AAAA,MACH,aAAa,CAAC,GAAG,KAAK,aAAa,SAAS,GAAG;AAAA,IACnD,CAAC;AAAA,IACD,SAAS,IAAI,MAAM,MAAM;AAAA,MACrB,GAAG;AAAA,MACH,aAAa,CAAC,GAAG,KAAK,aAAa,SAAS,GAAG;AAAA,IACnD,CAAC;AAAA,EACL,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACnB,MAAI,wBAAwB,KAAK,WAAW,sBACtC,EAAE,uBAAuB,MAAM,IAC/B;AACN,QAAM,cAAc,CAAC;AAErB,QAAM,QAAQ,CAAC,WAAW;AACtB,QAAI,uBAAuB,MAAM,GAAG;AAChC,kBAAY,KAAK,GAAG,OAAO,KAAK;AAChC,UAAI,OAAO,0BAA0B,QAAW;AAG5C,gCAAwB;AAAA,MAC5B;AAAA,IACJ,OACK;AACD,UAAI,eAAe;AACnB,UAAI,0BAA0B,UAC1B,OAAO,yBAAyB,OAAO;AACvC,cAAM,EAAE,sBAAsB,GAAG,KAAK,IAAI;AAC1C,uBAAe;AAAA,MACnB,OACK;AAED,gCAAwB;AAAA,MAC5B;AACA,kBAAY,KAAK,YAAY;AAAA,IACjC;AAAA,EACJ,CAAC;AACD,SAAO,YAAY,SACb;AAAA,IACE,OAAO;AAAA,IACP,GAAG;AAAA,EACP,IACE;AACV;;;ACnDO,SAAS,gBAAgB,KAAK,MAAM;AACvC,QAAM,aAAa,OAAO,IAAI;AAC9B,MAAI,eAAe,YACf,eAAe,YACf,eAAe,aACf,eAAe,UAAU;AACzB,WAAO;AAAA,MACH,MAAM,MAAM,QAAQ,IAAI,KAAK,IAAI,UAAU;AAAA,IAC/C;AAAA,EACJ;AACA,MAAI,KAAK,WAAW,YAAY;AAC5B,WAAO;AAAA,MACH,MAAM,eAAe,WAAW,YAAY;AAAA,MAC5C,MAAM,CAAC,IAAI,KAAK;AAAA,IACpB;AAAA,EACJ;AACA,SAAO;AAAA,IACH,MAAM,eAAe,WAAW,YAAY;AAAA,IAC5C,OAAO,IAAI;AAAA,EACf;AACJ;;;ACnBA,IAAI,aAAa;AAOV,IAAM,cAAc;AAAA;AAAA;AAAA;AAAA,EAIvB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA;AAAA;AAAA;AAAA,EAIN,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYP,OAAO,MAAM;AACT,QAAI,eAAe,QAAW;AAC1B,mBAAa,OAAO,wDAAwD,GAAG;AAAA,IACnF;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM;AAAA;AAAA;AAAA;AAAA,EAIN,MAAM;AAAA,EACN,UAAU;AAAA;AAAA;AAAA;AAAA,EAIV,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,KAAK;AACT;AACO,SAAS,eAAe,KAAK,MAAM;AACtC,QAAM,MAAM;AAAA,IACR,MAAM;AAAA,EACV;AACA,MAAI,IAAI,QAAQ;AACZ,eAAW,SAAS,IAAI,QAAQ;AAC5B,cAAQ,MAAM,MAAM;AAAA,QAChB,KAAK;AACD,oCAA0B,KAAK,aAAa,OAAO,IAAI,cAAc,WAC/D,KAAK,IAAI,IAAI,WAAW,MAAM,KAAK,IACnC,MAAM,OAAO,MAAM,SAAS,IAAI;AACtC;AAAA,QACJ,KAAK;AACD,oCAA0B,KAAK,aAAa,OAAO,IAAI,cAAc,WAC/D,KAAK,IAAI,IAAI,WAAW,MAAM,KAAK,IACnC,MAAM,OAAO,MAAM,SAAS,IAAI;AACtC;AAAA,QACJ,KAAK;AACD,kBAAQ,KAAK,eAAe;AAAA,YACxB,KAAK;AACD,wBAAU,KAAK,SAAS,MAAM,SAAS,IAAI;AAC3C;AAAA,YACJ,KAAK;AACD,wBAAU,KAAK,aAAa,MAAM,SAAS,IAAI;AAC/C;AAAA,YACJ,KAAK;AACD,yBAAW,KAAK,YAAY,OAAO,MAAM,SAAS,IAAI;AACtD;AAAA,UACR;AACA;AAAA,QACJ,KAAK;AACD,oBAAU,KAAK,OAAO,MAAM,SAAS,IAAI;AACzC;AAAA,QACJ,KAAK;AACD,oBAAU,KAAK,QAAQ,MAAM,SAAS,IAAI;AAC1C;AAAA,QACJ,KAAK;AACD,qBAAW,KAAK,MAAM,OAAO,MAAM,SAAS,IAAI;AAChD;AAAA,QACJ,KAAK;AACD,qBAAW,KAAK,YAAY,MAAM,MAAM,SAAS,IAAI;AACrD;AAAA,QACJ,KAAK;AACD,qBAAW,KAAK,YAAY,OAAO,MAAM,SAAS,IAAI;AACtD;AAAA,QACJ,KAAK;AACD,qBAAW,KAAK,OAAO,IAAI,wBAAwB,MAAM,OAAO,IAAI,CAAC,EAAE,GAAG,MAAM,SAAS,IAAI;AAC7F;AAAA,QACJ,KAAK;AACD,qBAAW,KAAK,OAAO,GAAG,wBAAwB,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,MAAM,SAAS,IAAI;AAC7F;AAAA,QACJ,KAAK;AACD,oBAAU,KAAK,aAAa,MAAM,SAAS,IAAI;AAC/C;AAAA,QACJ,KAAK;AACD,oBAAU,KAAK,QAAQ,MAAM,SAAS,IAAI;AAC1C;AAAA,QACJ,KAAK;AACD,oBAAU,KAAK,QAAQ,MAAM,SAAS,IAAI;AAC1C;AAAA,QACJ,KAAK;AACD,oBAAU,KAAK,YAAY,MAAM,SAAS,IAAI;AAC9C;AAAA,QACJ,KAAK;AACD,oCAA0B,KAAK,aAAa,OAAO,IAAI,cAAc,WAC/D,KAAK,IAAI,IAAI,WAAW,MAAM,KAAK,IACnC,MAAM,OAAO,MAAM,SAAS,IAAI;AACtC,oCAA0B,KAAK,aAAa,OAAO,IAAI,cAAc,WAC/D,KAAK,IAAI,IAAI,WAAW,MAAM,KAAK,IACnC,MAAM,OAAO,MAAM,SAAS,IAAI;AACtC;AAAA,QACJ,KAAK,YAAY;AACb,qBAAW,KAAK,OAAO,wBAAwB,MAAM,OAAO,IAAI,CAAC,GAAG,MAAM,SAAS,IAAI;AACvF;AAAA,QACJ;AAAA,QACA,KAAK,MAAM;AACP,cAAI,MAAM,YAAY,MAAM;AACxB,sBAAU,KAAK,QAAQ,MAAM,SAAS,IAAI;AAAA,UAC9C;AACA,cAAI,MAAM,YAAY,MAAM;AACxB,sBAAU,KAAK,QAAQ,MAAM,SAAS,IAAI;AAAA,UAC9C;AACA;AAAA,QACJ;AAAA,QACA,KAAK;AACD,qBAAW,KAAK,YAAY,WAAW,MAAM,SAAS,IAAI;AAC1D;AAAA,QACJ,KAAK;AACD,qBAAW,KAAK,YAAY,KAAK,MAAM,SAAS,IAAI;AACpD;AAAA,QACJ,KAAK,QAAQ;AACT,cAAI,MAAM,YAAY,MAAM;AACxB,uBAAW,KAAK,YAAY,UAAU,MAAM,SAAS,IAAI;AAAA,UAC7D;AACA,cAAI,MAAM,YAAY,MAAM;AACxB,uBAAW,KAAK,YAAY,UAAU,MAAM,SAAS,IAAI;AAAA,UAC7D;AACA;AAAA,QACJ;AAAA,QACA,KAAK;AACD,qBAAW,KAAK,YAAY,MAAM,GAAG,MAAM,SAAS,IAAI;AACxD;AAAA,QACJ,KAAK,QAAQ;AACT,qBAAW,KAAK,YAAY,MAAM,MAAM,SAAS,IAAI;AACrD;AAAA,QACJ;AAAA,QACA,KAAK,UAAU;AACX,kBAAQ,KAAK,gBAAgB;AAAA,YACzB,KAAK,iBAAiB;AAClB,wBAAU,KAAK,UAAU,MAAM,SAAS,IAAI;AAC5C;AAAA,YACJ;AAAA,YACA,KAAK,0BAA0B;AAC3B,wCAA0B,KAAK,mBAAmB,UAAU,MAAM,SAAS,IAAI;AAC/E;AAAA,YACJ;AAAA,YACA,KAAK,eAAe;AAChB,yBAAW,KAAK,YAAY,QAAQ,MAAM,SAAS,IAAI;AACvD;AAAA,YACJ;AAAA,UACJ;AACA;AAAA,QACJ;AAAA,QACA,KAAK,UAAU;AACX,qBAAW,KAAK,YAAY,QAAQ,MAAM,SAAS,IAAI;AAAA,QAC3D;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD;AAAA,QACJ;AAEI,UAAC,kBAAC,MAAM;AAAA,UAAE,GAAG,KAAK;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,wBAAwB,SAAS,MAAM;AAC5C,SAAO,KAAK,oBAAoB,WAC1B,sBAAsB,OAAO,IAC7B;AACV;AACA,IAAM,gBAAgB,IAAI,IAAI,8DAA8D;AAC5F,SAAS,sBAAsB,QAAQ;AACnC,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,QAAI,CAAC,cAAc,IAAI,OAAO,CAAC,CAAC,GAAG;AAC/B,gBAAU;AAAA,IACd;AACA,cAAU,OAAO,CAAC;AAAA,EACtB;AACA,SAAO;AACX;AAEA,SAAS,UAAU,QAAQ,OAAO,SAAS,MAAM;AAlNjD;AAmNI,MAAI,OAAO,YAAU,YAAO,UAAP,mBAAc,KAAK,CAAC,MAAM,EAAE,UAAS;AACtD,QAAI,CAAC,OAAO,OAAO;AACf,aAAO,QAAQ,CAAC;AAAA,IACpB;AACA,QAAI,OAAO,QAAQ;AACf,aAAO,MAAM,KAAK;AAAA,QACd,QAAQ,OAAO;AAAA,QACf,GAAI,OAAO,gBACP,KAAK,iBAAiB;AAAA,UACtB,cAAc,EAAE,QAAQ,OAAO,aAAa,OAAO;AAAA,QACvD;AAAA,MACJ,CAAC;AACD,aAAO,OAAO;AACd,UAAI,OAAO,cAAc;AACrB,eAAO,OAAO,aAAa;AAC3B,YAAI,OAAO,KAAK,OAAO,YAAY,EAAE,WAAW,GAAG;AAC/C,iBAAO,OAAO;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,MAAM,KAAK;AAAA,MACd,QAAQ;AAAA,MACR,GAAI,WACA,KAAK,iBAAiB,EAAE,cAAc,EAAE,QAAQ,QAAQ,EAAE;AAAA,IAClE,CAAC;AAAA,EACL,OACK;AACD,8BAA0B,QAAQ,UAAU,OAAO,SAAS,IAAI;AAAA,EACpE;AACJ;AAEA,SAAS,WAAW,QAAQ,OAAO,SAAS,MAAM;AAlPlD;AAmPI,MAAI,OAAO,aAAW,YAAO,UAAP,mBAAc,KAAK,CAAC,MAAM,EAAE,WAAU;AACxD,QAAI,CAAC,OAAO,OAAO;AACf,aAAO,QAAQ,CAAC;AAAA,IACpB;AACA,QAAI,OAAO,SAAS;AAChB,aAAO,MAAM,KAAK;AAAA,QACd,SAAS,OAAO;AAAA,QAChB,GAAI,OAAO,gBACP,KAAK,iBAAiB;AAAA,UACtB,cAAc,EAAE,SAAS,OAAO,aAAa,QAAQ;AAAA,QACzD;AAAA,MACJ,CAAC;AACD,aAAO,OAAO;AACd,UAAI,OAAO,cAAc;AACrB,eAAO,OAAO,aAAa;AAC3B,YAAI,OAAO,KAAK,OAAO,YAAY,EAAE,WAAW,GAAG;AAC/C,iBAAO,OAAO;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,MAAM,KAAK;AAAA,MACd,SAAS,yBAAyB,OAAO,IAAI;AAAA,MAC7C,GAAI,WACA,KAAK,iBAAiB,EAAE,cAAc,EAAE,SAAS,QAAQ,EAAE;AAAA,IACnE,CAAC;AAAA,EACL,OACK;AACD,8BAA0B,QAAQ,WAAW,yBAAyB,OAAO,IAAI,GAAG,SAAS,IAAI;AAAA,EACrG;AACJ;AAEA,SAAS,yBAAyB,OAAO,MAAM;AAlR/C;AAmRI,MAAI,CAAC,KAAK,mBAAmB,CAAC,MAAM,OAAO;AACvC,WAAO,MAAM;AAAA,EACjB;AAEA,QAAM,QAAQ;AAAA,IACV,GAAG,MAAM,MAAM,SAAS,GAAG;AAAA,IAC3B,GAAG,MAAM,MAAM,SAAS,GAAG;AAAA,IAC3B,GAAG,MAAM,MAAM,SAAS,GAAG;AAAA;AAAA,EAC/B;AAEA,QAAM,SAAS,MAAM,IAAI,MAAM,OAAO,YAAY,IAAI,MAAM;AAC5D,MAAI,UAAU;AACd,MAAI,YAAY;AAChB,MAAI,cAAc;AAClB,MAAI,cAAc;AAClB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,QAAI,WAAW;AACX,iBAAW,OAAO,CAAC;AACnB,kBAAY;AACZ;AAAA,IACJ;AACA,QAAI,MAAM,GAAG;AACT,UAAI,aAAa;AACb,YAAI,OAAO,CAAC,EAAE,MAAM,OAAO,GAAG;AAC1B,cAAI,aAAa;AACb,uBAAW,OAAO,CAAC;AACnB,uBAAW,GAAG,OAAO,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,GAAG,YAAY;AACvD,0BAAc;AAAA,UAClB,WACS,OAAO,IAAI,CAAC,MAAM,SAAO,YAAO,IAAI,CAAC,MAAZ,mBAAe,MAAM,WAAU;AAC7D,uBAAW,OAAO,CAAC;AACnB,0BAAc;AAAA,UAClB,OACK;AACD,uBAAW,GAAG,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC;AAAA,UACrD;AACA;AAAA,QACJ;AAAA,MACJ,WACS,OAAO,CAAC,EAAE,MAAM,OAAO,GAAG;AAC/B,mBAAW,IAAI,OAAO,CAAC,CAAC,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC;AAClD;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,MAAM,GAAG;AACT,UAAI,OAAO,CAAC,MAAM,KAAK;AACnB,mBAAW;AAAA;AACX;AAAA,MACJ,WACS,OAAO,CAAC,MAAM,KAAK;AACxB,mBAAW;AAAA;AACX;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,MAAM,KAAK,OAAO,CAAC,MAAM,KAAK;AAC9B,iBAAW,cAAc,GAAG,OAAO,CAAC,CAAC;AAAA,IAAS,IAAI,OAAO,CAAC,CAAC;AAAA;AAC3D;AAAA,IACJ;AACA,eAAW,OAAO,CAAC;AACnB,QAAI,OAAO,CAAC,MAAM,MAAM;AACpB,kBAAY;AAAA,IAChB,WACS,eAAe,OAAO,CAAC,MAAM,KAAK;AACvC,oBAAc;AAAA,IAClB,WACS,CAAC,eAAe,OAAO,CAAC,MAAM,KAAK;AACxC,oBAAc;AAAA,IAClB;AAAA,EACJ;AACA,MAAI;AACA,QAAI,OAAO,OAAO;AAAA,EACtB,QACM;AACF,YAAQ,KAAK,sCAAsC,KAAK,YAAY,KAAK,GAAG,CAAC,uEAAuE;AACpJ,WAAO,MAAM;AAAA,EACjB;AACA,SAAO;AACX;;;AC5VO,SAAS,eAAe,KAAK,MAAM;AAJ1C;AAKI,MAAI,KAAK,WAAW,UAAU;AAC1B,YAAQ,KAAK,8FAA8F;AAAA,EAC/G;AACA,MAAI,KAAK,WAAW,gBAChB,SAAI,YAAJ,mBAAa,KAAK,cAAa,sBAAsB,SAAS;AAC9D,WAAO;AAAA,MACH,MAAM;AAAA,MACN,UAAU,IAAI,QAAQ,KAAK;AAAA,MAC3B,YAAY,IAAI,QAAQ,KAAK,OAAO,OAAO,CAAC,KAAK,SAAS;AAAA,QACtD,GAAG;AAAA,QACH,CAAC,GAAG,GAAG,SAAS,IAAI,UAAU,MAAM;AAAA,UAChC,GAAG;AAAA,UACH,aAAa,CAAC,GAAG,KAAK,aAAa,cAAc,GAAG;AAAA,QACxD,CAAC,KAAK,CAAC;AAAA,MACX,IAAI,CAAC,CAAC;AAAA,MACN,sBAAsB,KAAK;AAAA,IAC/B;AAAA,EACJ;AACA,QAAM,SAAS;AAAA,IACX,MAAM;AAAA,IACN,sBAAsB,SAAS,IAAI,UAAU,MAAM;AAAA,MAC/C,GAAG;AAAA,MACH,aAAa,CAAC,GAAG,KAAK,aAAa,sBAAsB;AAAA,IAC7D,CAAC,KAAK,KAAK;AAAA,EACf;AACA,MAAI,KAAK,WAAW,YAAY;AAC5B,WAAO;AAAA,EACX;AACA,QAAI,SAAI,YAAJ,mBAAa,KAAK,cAAa,sBAAsB,eACrD,SAAI,QAAQ,KAAK,WAAjB,mBAAyB,SAAQ;AACjC,UAAM,EAAE,MAAM,GAAG,QAAQ,IAAI,eAAe,IAAI,QAAQ,MAAM,IAAI;AAClE,WAAO;AAAA,MACH,GAAG;AAAA,MACH,eAAe;AAAA,IACnB;AAAA,EACJ,aACS,SAAI,YAAJ,mBAAa,KAAK,cAAa,sBAAsB,SAAS;AACnE,WAAO;AAAA,MACH,GAAG;AAAA,MACH,eAAe;AAAA,QACX,MAAM,IAAI,QAAQ,KAAK;AAAA,MAC3B;AAAA,IACJ;AAAA,EACJ,aACS,SAAI,YAAJ,mBAAa,KAAK,cAAa,sBAAsB,cAC1D,IAAI,QAAQ,KAAK,KAAK,KAAK,aAAa,sBAAsB,eAC9D,SAAI,QAAQ,KAAK,KAAK,KAAK,WAA3B,mBAAmC,SAAQ;AAC3C,UAAM,EAAE,MAAM,GAAG,QAAQ,IAAI,gBAAgB,IAAI,QAAQ,MAAM,IAAI;AACnE,WAAO;AAAA,MACH,GAAG;AAAA,MACH,eAAe;AAAA,IACnB;AAAA,EACJ;AACA,SAAO;AACX;;;ACzDO,SAAS,YAAY,KAAK,MAAM;AACnC,MAAI,KAAK,gBAAgB,UAAU;AAC/B,WAAO,eAAe,KAAK,IAAI;AAAA,EACnC;AACA,QAAM,OAAO,SAAS,IAAI,QAAQ,MAAM;AAAA,IACpC,GAAG;AAAA,IACH,aAAa,CAAC,GAAG,KAAK,aAAa,SAAS,SAAS,GAAG;AAAA,EAC5D,CAAC,KAAK,CAAC;AACP,QAAM,SAAS,SAAS,IAAI,UAAU,MAAM;AAAA,IACxC,GAAG;AAAA,IACH,aAAa,CAAC,GAAG,KAAK,aAAa,SAAS,SAAS,GAAG;AAAA,EAC5D,CAAC,KAAK,CAAC;AACP,SAAO;AAAA,IACH,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,MACH,MAAM;AAAA,MACN,OAAO,CAAC,MAAM,MAAM;AAAA,MACpB,UAAU;AAAA,MACV,UAAU;AAAA,IACd;AAAA,EACJ;AACJ;;;ACxBO,SAAS,mBAAmB,KAAK;AACpC,QAAM,SAAS,IAAI;AACnB,QAAM,aAAa,OAAO,KAAK,IAAI,MAAM,EAAE,OAAO,CAAC,QAAQ;AACvD,WAAO,OAAO,OAAO,OAAO,GAAG,CAAC,MAAM;AAAA,EAC1C,CAAC;AACD,QAAM,eAAe,WAAW,IAAI,CAAC,QAAQ,OAAO,GAAG,CAAC;AACxD,QAAM,cAAc,MAAM,KAAK,IAAI,IAAI,aAAa,IAAI,CAAC,WAAW,OAAO,MAAM,CAAC,CAAC;AACnF,SAAO;AAAA,IACH,MAAM,YAAY,WAAW,IACvB,YAAY,CAAC,MAAM,WACf,WACA,WACJ,CAAC,UAAU,QAAQ;AAAA,IACzB,MAAM;AAAA,EACV;AACJ;;;ACfO,SAAS,gBAAgB;AAC5B,SAAO;AAAA,IACH,KAAK,CAAC;AAAA,EACV;AACJ;;;ACJO,SAAS,aAAa,MAAM;AAC/B,SAAO,KAAK,WAAW,aACjB;AAAA,IACE,MAAM,CAAC,MAAM;AAAA,IACb,UAAU;AAAA,EACd,IACE;AAAA,IACE,MAAM;AAAA,EACV;AACR;;;ACRO,IAAM,oBAAoB;AAAA,EAC7B,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AACb;AACO,SAAS,cAAc,KAAK,MAAM;AACrC,MAAI,KAAK,WAAW;AAChB,WAAO,QAAQ,KAAK,IAAI;AAC5B,QAAM,UAAU,IAAI,mBAAmB,MAAM,MAAM,KAAK,IAAI,QAAQ,OAAO,CAAC,IAAI,IAAI;AAEpF,MAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,KAAK,YAAY,sBACvC,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,KAAK,OAAO,OAAO,GAAG;AAE5C,UAAM,QAAQ,QAAQ,OAAO,CAACA,QAAO,MAAM;AACvC,YAAM,OAAO,kBAAkB,EAAE,KAAK,QAAQ;AAC9C,aAAO,QAAQ,CAACA,OAAM,SAAS,IAAI,IAAI,CAAC,GAAGA,QAAO,IAAI,IAAIA;AAAA,IAC9D,GAAG,CAAC,CAAC;AACL,WAAO;AAAA,MACH,MAAM,MAAM,SAAS,IAAI,QAAQ,MAAM,CAAC;AAAA,IAC5C;AAAA,EACJ,WACS,QAAQ,MAAM,CAAC,MAAM,EAAE,KAAK,aAAa,gBAAgB,CAAC,EAAE,WAAW,GAAG;AAE/E,UAAM,QAAQ,QAAQ,OAAO,CAAC,KAAK,MAAM;AACrC,YAAM,OAAO,OAAO,EAAE,KAAK;AAC3B,cAAQ,MAAM;AAAA,QACV,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,iBAAO,CAAC,GAAG,KAAK,IAAI;AAAA,QACxB,KAAK;AACD,iBAAO,CAAC,GAAG,KAAK,SAAS;AAAA,QAC7B,KAAK;AACD,cAAI,EAAE,KAAK,UAAU;AACjB,mBAAO,CAAC,GAAG,KAAK,MAAM;AAAA,QAC9B,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AACI,iBAAO;AAAA,MACf;AAAA,IACJ,GAAG,CAAC,CAAC;AACL,QAAI,MAAM,WAAW,QAAQ,QAAQ;AAEjC,YAAM,cAAc,MAAM,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;AAChE,aAAO;AAAA,QACH,MAAM,YAAY,SAAS,IAAI,cAAc,YAAY,CAAC;AAAA,QAC1D,MAAM,QAAQ,OAAO,CAAC,KAAK,MAAM;AAC7B,iBAAO,IAAI,SAAS,EAAE,KAAK,KAAK,IAAI,MAAM,CAAC,GAAG,KAAK,EAAE,KAAK,KAAK;AAAA,QACnE,GAAG,CAAC,CAAC;AAAA,MACT;AAAA,IACJ;AAAA,EACJ,WACS,QAAQ,MAAM,CAAC,MAAM,EAAE,KAAK,aAAa,SAAS,GAAG;AAC1D,WAAO;AAAA,MACH,MAAM;AAAA,MACN,MAAM,QAAQ,OAAO,CAAC,KAAK,MAAM;AAAA,QAC7B,GAAG;AAAA,QACH,GAAG,EAAE,KAAK,OAAO,OAAO,CAACC,OAAM,CAAC,IAAI,SAASA,EAAC,CAAC;AAAA,MACnD,GAAG,CAAC,CAAC;AAAA,IACT;AAAA,EACJ;AACA,SAAO,QAAQ,KAAK,IAAI;AAC5B;AACA,IAAM,UAAU,CAAC,KAAK,SAAS;AAC3B,QAAM,SAAS,IAAI,mBAAmB,MAChC,MAAM,KAAK,IAAI,QAAQ,OAAO,CAAC,IAC/B,IAAI,SACL,IAAI,CAAC,GAAG,MAAM,SAAS,EAAE,MAAM;AAAA,IAChC,GAAG;AAAA,IACH,aAAa,CAAC,GAAG,KAAK,aAAa,SAAS,GAAG,CAAC,EAAE;AAAA,EACtD,CAAC,CAAC,EACG,OAAO,CAAC,MAAM,CAAC,CAAC,MAChB,CAAC,KAAK,gBACF,OAAO,MAAM,YAAY,OAAO,KAAK,CAAC,EAAE,SAAS,EAAG;AAC7D,SAAO,MAAM,SAAS,EAAE,MAAM,IAAI;AACtC;;;AC7EO,SAAS,iBAAiB,KAAK,MAAM;AACxC,MAAI,CAAC,aAAa,aAAa,aAAa,cAAc,SAAS,EAAE,SAAS,IAAI,UAAU,KAAK,QAAQ,MACpG,CAAC,IAAI,UAAU,KAAK,UAAU,CAAC,IAAI,UAAU,KAAK,OAAO,SAAS;AACnE,QAAI,KAAK,WAAW,YAAY;AAC5B,aAAO;AAAA,QACH,MAAM,kBAAkB,IAAI,UAAU,KAAK,QAAQ;AAAA,QACnD,UAAU;AAAA,MACd;AAAA,IACJ;AACA,WAAO;AAAA,MACH,MAAM;AAAA,QACF,kBAAkB,IAAI,UAAU,KAAK,QAAQ;AAAA,QAC7C;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,KAAK,WAAW,YAAY;AAC5B,UAAMC,QAAO,SAAS,IAAI,UAAU,MAAM;AAAA,MACtC,GAAG;AAAA,MACH,aAAa,CAAC,GAAG,KAAK,WAAW;AAAA,IACrC,CAAC;AACD,QAAIA,SAAQ,UAAUA;AAClB,aAAO,EAAE,OAAO,CAACA,KAAI,GAAG,UAAU,KAAK;AAC3C,WAAOA,SAAQ,EAAE,GAAGA,OAAM,UAAU,KAAK;AAAA,EAC7C;AACA,QAAM,OAAO,SAAS,IAAI,UAAU,MAAM;AAAA,IACtC,GAAG;AAAA,IACH,aAAa,CAAC,GAAG,KAAK,aAAa,SAAS,GAAG;AAAA,EACnD,CAAC;AACD,SAAO,QAAQ,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC,EAAE;AACrD;;;AC/BO,SAAS,eAAe,KAAK,MAAM;AACtC,QAAM,MAAM;AAAA,IACR,MAAM;AAAA,EACV;AACA,MAAI,CAAC,IAAI;AACL,WAAO;AACX,aAAW,SAAS,IAAI,QAAQ;AAC5B,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AACD,YAAI,OAAO;AACX,wBAAgB,KAAK,QAAQ,MAAM,SAAS,IAAI;AAChD;AAAA,MACJ,KAAK;AACD,YAAI,KAAK,WAAW,eAAe;AAC/B,cAAI,MAAM,WAAW;AACjB,sCAA0B,KAAK,WAAW,MAAM,OAAO,MAAM,SAAS,IAAI;AAAA,UAC9E,OACK;AACD,sCAA0B,KAAK,oBAAoB,MAAM,OAAO,MAAM,SAAS,IAAI;AAAA,UACvF;AAAA,QACJ,OACK;AACD,cAAI,CAAC,MAAM,WAAW;AAClB,gBAAI,mBAAmB;AAAA,UAC3B;AACA,oCAA0B,KAAK,WAAW,MAAM,OAAO,MAAM,SAAS,IAAI;AAAA,QAC9E;AACA;AAAA,MACJ,KAAK;AACD,YAAI,KAAK,WAAW,eAAe;AAC/B,cAAI,MAAM,WAAW;AACjB,sCAA0B,KAAK,WAAW,MAAM,OAAO,MAAM,SAAS,IAAI;AAAA,UAC9E,OACK;AACD,sCAA0B,KAAK,oBAAoB,MAAM,OAAO,MAAM,SAAS,IAAI;AAAA,UACvF;AAAA,QACJ,OACK;AACD,cAAI,CAAC,MAAM,WAAW;AAClB,gBAAI,mBAAmB;AAAA,UAC3B;AACA,oCAA0B,KAAK,WAAW,MAAM,OAAO,MAAM,SAAS,IAAI;AAAA,QAC9E;AACA;AAAA,MACJ,KAAK;AACD,kCAA0B,KAAK,cAAc,MAAM,OAAO,MAAM,SAAS,IAAI;AAC7E;AAAA,IACR;AAAA,EACJ;AACA,SAAO;AACX;;;ACjDO,SAAS,eAAe,KAAK,MAAM;AACtC,QAAM,4BAA4B,KAAK,WAAW;AAClD,QAAM,SAAS;AAAA,IACX,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,EACjB;AACA,QAAM,WAAW,CAAC;AAClB,QAAM,QAAQ,IAAI,MAAM;AACxB,aAAW,YAAY,OAAO;AAC1B,QAAI,UAAU,MAAM,QAAQ;AAC5B,QAAI,YAAY,UAAa,QAAQ,SAAS,QAAW;AACrD;AAAA,IACJ;AACA,QAAI,eAAe,eAAe,OAAO;AACzC,QAAI,gBAAgB,2BAA2B;AAC3C,UAAI,mBAAmB,aAAa;AAChC,kBAAU,QAAQ,KAAK;AAAA,MAC3B;AACA,UAAI,CAAC,QAAQ,WAAW,GAAG;AACvB,kBAAU,QAAQ,SAAS;AAAA,MAC/B;AACA,qBAAe;AAAA,IACnB;AACA,UAAM,YAAY,SAAS,QAAQ,MAAM;AAAA,MACrC,GAAG;AAAA,MACH,aAAa,CAAC,GAAG,KAAK,aAAa,cAAc,QAAQ;AAAA,MACzD,cAAc,CAAC,GAAG,KAAK,aAAa,cAAc,QAAQ;AAAA,IAC9D,CAAC;AACD,QAAI,cAAc,QAAW;AACzB;AAAA,IACJ;AACA,WAAO,WAAW,QAAQ,IAAI;AAC9B,QAAI,CAAC,cAAc;AACf,eAAS,KAAK,QAAQ;AAAA,IAC1B;AAAA,EACJ;AACA,MAAI,SAAS,QAAQ;AACjB,WAAO,WAAW;AAAA,EACtB;AACA,QAAM,uBAAuB,2BAA2B,KAAK,IAAI;AACjE,MAAI,yBAAyB,QAAW;AACpC,WAAO,uBAAuB;AAAA,EAClC;AACA,SAAO;AACX;AACA,SAAS,2BAA2B,KAAK,MAAM;AAC3C,MAAI,IAAI,SAAS,KAAK,aAAa,YAAY;AAC3C,WAAO,SAAS,IAAI,SAAS,MAAM;AAAA,MAC/B,GAAG;AAAA,MACH,aAAa,CAAC,GAAG,KAAK,aAAa,sBAAsB;AAAA,IAC7D,CAAC;AAAA,EACL;AACA,UAAQ,IAAI,aAAa;AAAA,IACrB,KAAK;AACD,aAAO,KAAK;AAAA,IAChB,KAAK;AACD,aAAO,KAAK;AAAA,IAChB,KAAK;AACD,aAAO,KAAK,6BAA6B,WACnC,KAAK,8BACL,KAAK;AAAA,EACnB;AACJ;AACA,SAAS,eAAe,QAAQ;AAC5B,MAAI;AACA,WAAO,OAAO,WAAW;AAAA,EAC7B,QACM;AACF,WAAO;AAAA,EACX;AACJ;;;ACvEO,IAAM,mBAAmB,CAAC,KAAK,SAAS;AAD/C;AAEI,MAAI,KAAK,YAAY,SAAS,QAAM,UAAK,iBAAL,mBAAmB,aAAY;AAC/D,WAAO,SAAS,IAAI,UAAU,MAAM,IAAI;AAAA,EAC5C;AACA,QAAM,cAAc,SAAS,IAAI,UAAU,MAAM;AAAA,IAC7C,GAAG;AAAA,IACH,aAAa,CAAC,GAAG,KAAK,aAAa,SAAS,GAAG;AAAA,EACnD,CAAC;AACD,SAAO,cACD;AAAA,IACE,OAAO;AAAA,MACH;AAAA,QACI,KAAK,CAAC;AAAA,MACV;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,IACE,CAAC;AACX;;;AClBO,IAAM,mBAAmB,CAAC,KAAK,SAAS;AAC3C,MAAI,KAAK,iBAAiB,SAAS;AAC/B,WAAO,SAAS,IAAI,GAAG,MAAM,IAAI;AAAA,EACrC,WACS,KAAK,iBAAiB,UAAU;AACrC,WAAO,SAAS,IAAI,IAAI,MAAM,IAAI;AAAA,EACtC;AACA,QAAM,IAAI,SAAS,IAAI,GAAG,MAAM;AAAA,IAC5B,GAAG;AAAA,IACH,aAAa,CAAC,GAAG,KAAK,aAAa,SAAS,GAAG;AAAA,EACnD,CAAC;AACD,QAAM,IAAI,SAAS,IAAI,IAAI,MAAM;AAAA,IAC7B,GAAG;AAAA,IACH,aAAa,CAAC,GAAG,KAAK,aAAa,SAAS,IAAI,MAAM,GAAG;AAAA,EAC7D,CAAC;AACD,SAAO;AAAA,IACH,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,MAAS;AAAA,EAC/C;AACJ;;;AClBO,SAAS,gBAAgB,KAAK,MAAM;AACvC,SAAO,SAAS,IAAI,KAAK,MAAM,IAAI;AACvC;;;ACDO,SAAS,YAAY,KAAK,MAAM;AACnC,QAAM,QAAQ,SAAS,IAAI,UAAU,MAAM;AAAA,IACvC,GAAG;AAAA,IACH,aAAa,CAAC,GAAG,KAAK,aAAa,OAAO;AAAA,EAC9C,CAAC;AACD,QAAM,SAAS;AAAA,IACX,MAAM;AAAA,IACN,aAAa;AAAA,IACb;AAAA,EACJ;AACA,MAAI,IAAI,SAAS;AACb,8BAA0B,QAAQ,YAAY,IAAI,QAAQ,OAAO,IAAI,QAAQ,SAAS,IAAI;AAAA,EAC9F;AACA,MAAI,IAAI,SAAS;AACb,8BAA0B,QAAQ,YAAY,IAAI,QAAQ,OAAO,IAAI,QAAQ,SAAS,IAAI;AAAA,EAC9F;AACA,SAAO;AACX;;;AClBO,SAAS,cAAc,KAAK,MAAM;AACrC,MAAI,IAAI,MAAM;AACV,WAAO;AAAA,MACH,MAAM;AAAA,MACN,UAAU,IAAI,MAAM;AAAA,MACpB,OAAO,IAAI,MACN,IAAI,CAAC,GAAG,MAAM,SAAS,EAAE,MAAM;AAAA,QAChC,GAAG;AAAA,QACH,aAAa,CAAC,GAAG,KAAK,aAAa,SAAS,GAAG,CAAC,EAAE;AAAA,MACtD,CAAC,CAAC,EACG,OAAO,CAAC,KAAK,MAAO,MAAM,SAAY,MAAM,CAAC,GAAG,KAAK,CAAC,GAAI,CAAC,CAAC;AAAA,MACjE,iBAAiB,SAAS,IAAI,KAAK,MAAM;AAAA,QACrC,GAAG;AAAA,QACH,aAAa,CAAC,GAAG,KAAK,aAAa,iBAAiB;AAAA,MACxD,CAAC;AAAA,IACL;AAAA,EACJ,OACK;AACD,WAAO;AAAA,MACH,MAAM;AAAA,MACN,UAAU,IAAI,MAAM;AAAA,MACpB,UAAU,IAAI,MAAM;AAAA,MACpB,OAAO,IAAI,MACN,IAAI,CAAC,GAAG,MAAM,SAAS,EAAE,MAAM;AAAA,QAChC,GAAG;AAAA,QACH,aAAa,CAAC,GAAG,KAAK,aAAa,SAAS,GAAG,CAAC,EAAE;AAAA,MACtD,CAAC,CAAC,EACG,OAAO,CAAC,KAAK,MAAO,MAAM,SAAY,MAAM,CAAC,GAAG,KAAK,CAAC,GAAI,CAAC,CAAC;AAAA,IACrE;AAAA,EACJ;AACJ;;;AC/BO,SAAS,oBAAoB;AAChC,SAAO;AAAA,IACH,KAAK,CAAC;AAAA,EACV;AACJ;;;ACJO,SAAS,kBAAkB;AAC9B,SAAO,CAAC;AACZ;;;ACDO,IAAM,mBAAmB,CAAC,KAAK,SAAS;AAC3C,SAAO,SAAS,IAAI,UAAU,MAAM,IAAI;AAC5C;;;AC4BO,IAAM,eAAe,CAAC,KAAK,UAAU,SAAS;AACjD,UAAQ,UAAU;AAAA,IACd,KAAK,sBAAsB;AACvB,aAAO,eAAe,KAAK,IAAI;AAAA,IACnC,KAAK,sBAAsB;AACvB,aAAO,eAAe,KAAK,IAAI;AAAA,IACnC,KAAK,sBAAsB;AACvB,aAAO,eAAe,KAAK,IAAI;AAAA,IACnC,KAAK,sBAAsB;AACvB,aAAO,eAAe,KAAK,IAAI;AAAA,IACnC,KAAK,sBAAsB;AACvB,aAAO,gBAAgB;AAAA,IAC3B,KAAK,sBAAsB;AACvB,aAAO,aAAa,KAAK,IAAI;AAAA,IACjC,KAAK,sBAAsB;AACvB,aAAO,kBAAkB;AAAA,IAC7B,KAAK,sBAAsB;AACvB,aAAO,aAAa,IAAI;AAAA,IAC5B,KAAK,sBAAsB;AACvB,aAAO,cAAc,KAAK,IAAI;AAAA,IAClC,KAAK,sBAAsB;AAAA,IAC3B,KAAK,sBAAsB;AACvB,aAAO,cAAc,KAAK,IAAI;AAAA,IAClC,KAAK,sBAAsB;AACvB,aAAO,qBAAqB,KAAK,IAAI;AAAA,IACzC,KAAK,sBAAsB;AACvB,aAAO,cAAc,KAAK,IAAI;AAAA,IAClC,KAAK,sBAAsB;AACvB,aAAO,eAAe,KAAK,IAAI;AAAA,IACnC,KAAK,sBAAsB;AACvB,aAAO,gBAAgB,KAAK,IAAI;AAAA,IACpC,KAAK,sBAAsB;AACvB,aAAO,aAAa,GAAG;AAAA,IAC3B,KAAK,sBAAsB;AACvB,aAAO,mBAAmB,GAAG;AAAA,IACjC,KAAK,sBAAsB;AACvB,aAAO,iBAAiB,KAAK,IAAI;AAAA,IACrC,KAAK,sBAAsB;AACvB,aAAO,iBAAiB,KAAK,IAAI;AAAA,IACrC,KAAK,sBAAsB;AACvB,aAAO,YAAY,KAAK,IAAI;AAAA,IAChC,KAAK,sBAAsB;AACvB,aAAO,YAAY,KAAK,IAAI;AAAA,IAChC,KAAK,sBAAsB;AACvB,aAAO,MAAM,IAAI,OAAO,EAAE;AAAA,IAC9B,KAAK,sBAAsB;AACvB,aAAO,gBAAgB,KAAK,IAAI;AAAA,IACpC,KAAK,sBAAsB;AAAA,IAC3B,KAAK,sBAAsB;AACvB,aAAO,cAAc;AAAA,IACzB,KAAK,sBAAsB;AACvB,aAAO,gBAAgB,KAAK,IAAI;AAAA,IACpC,KAAK,sBAAsB;AACvB,aAAO,YAAY;AAAA,IACvB,KAAK,sBAAsB;AACvB,aAAO,gBAAgB;AAAA,IAC3B,KAAK,sBAAsB;AACvB,aAAO,gBAAgB,KAAK,IAAI;AAAA,IACpC,KAAK,sBAAsB;AACvB,aAAO,gBAAgB,KAAK,IAAI;AAAA,IACpC,KAAK,sBAAsB;AACvB,aAAO,iBAAiB,KAAK,IAAI;AAAA,IACrC,KAAK,sBAAsB;AACvB,aAAO,cAAc,KAAK,IAAI;AAAA,IAClC,KAAK,sBAAsB;AACvB,aAAO,iBAAiB,KAAK,IAAI;AAAA,IACrC,KAAK,sBAAsB;AAAA,IAC3B,KAAK,sBAAsB;AAAA,IAC3B,KAAK,sBAAsB;AACvB,aAAO;AAAA,IACX;AAEI,aAAQ,kBAAC,MAAM,QAAW,QAAQ;AAAA,EAC1C;AACJ;;;ACvGO,SAAS,SAAS,KAAK,MAAM,kBAAkB,OAAO;AAF7D;AAGI,QAAM,WAAW,KAAK,KAAK,IAAI,GAAG;AAClC,MAAI,KAAK,UAAU;AACf,UAAM,kBAAiB,UAAK,aAAL,8BAAgB,KAAK,MAAM,UAAU;AAC5D,QAAI,mBAAmB,gBAAgB;AACnC,aAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,YAAY,CAAC,iBAAiB;AAC9B,UAAM,aAAa,QAAQ,UAAU,IAAI;AACzC,QAAI,eAAe,QAAW;AAC1B,aAAO;AAAA,IACX;AAAA,EACJ;AACA,QAAM,UAAU,EAAE,KAAK,MAAM,KAAK,aAAa,YAAY,OAAU;AACrE,OAAK,KAAK,IAAI,KAAK,OAAO;AAC1B,QAAM,qBAAqB,aAAa,KAAK,IAAI,UAAU,IAAI;AAE/D,QAAM,aAAa,OAAO,uBAAuB,aAC3C,SAAS,mBAAmB,GAAG,IAAI,IACnC;AACN,MAAI,YAAY;AACZ,YAAQ,KAAK,MAAM,UAAU;AAAA,EACjC;AACA,MAAI,KAAK,aAAa;AAClB,UAAM,oBAAoB,KAAK,YAAY,YAAY,KAAK,IAAI;AAChE,YAAQ,aAAa;AACrB,WAAO;AAAA,EACX;AACA,UAAQ,aAAa;AACrB,SAAO;AACX;AACA,IAAM,UAAU,CAAC,MAAM,SAAS;AAC5B,UAAQ,KAAK,cAAc;AAAA,IACvB,KAAK;AACD,aAAO,EAAE,MAAM,KAAK,KAAK,KAAK,GAAG,EAAE;AAAA,IACvC,KAAK;AACD,aAAO,EAAE,MAAM,gBAAgB,KAAK,aAAa,KAAK,IAAI,EAAE;AAAA,IAChE,KAAK;AAAA,IACL,KAAK,QAAQ;AACT,UAAI,KAAK,KAAK,SAAS,KAAK,YAAY,UACpC,KAAK,KAAK,MAAM,CAAC,OAAO,UAAU,KAAK,YAAY,KAAK,MAAM,KAAK,GAAG;AACtE,gBAAQ,KAAK,mCAAmC,KAAK,YAAY,KAAK,GAAG,CAAC,qBAAqB;AAC/F,eAAO,CAAC;AAAA,MACZ;AACA,aAAO,KAAK,iBAAiB,SAAS,CAAC,IAAI;AAAA,IAC/C;AAAA,EACJ;AACJ;AACA,IAAM,kBAAkB,CAAC,OAAO,UAAU;AACtC,MAAI,IAAI;AACR,SAAO,IAAI,MAAM,UAAU,IAAI,MAAM,QAAQ,KAAK;AAC9C,QAAI,MAAM,CAAC,MAAM,MAAM,CAAC;AACpB;AAAA,EACR;AACA,SAAO,EAAE,MAAM,SAAS,GAAG,SAAS,GAAG,GAAG,MAAM,MAAM,CAAC,CAAC,EAAE,KAAK,GAAG;AACtE;AACA,IAAM,UAAU,CAAC,KAAK,MAAM,eAAe;AACvC,MAAI,IAAI,aAAa;AACjB,eAAW,cAAc,IAAI;AAC7B,QAAI,KAAK,qBAAqB;AAC1B,iBAAW,sBAAsB,IAAI;AAAA,IACzC;AAAA,EACJ;AACA,SAAO;AACX;;;ACjEA,IAAM,kBAAkB,CAAC,QAAQ,YAAY;AACzC,QAAM,OAAO,QAAQ,OAAO;AAC5B,QAAM,cAAc,OAAO,YAAY,YAAY,QAAQ,cACrD,OAAO,QAAQ,QAAQ,WAAW,EAAE,OAAO,CAAC,KAAK,CAACC,OAAMC,OAAM,OAAO;AAAA,IACnE,GAAG;AAAA,IACH,CAACD,KAAI,GAAG,SAASC,QAAO,MAAM;AAAA,MAC1B,GAAG;AAAA,MACH,aAAa,CAAC,GAAG,KAAK,UAAU,KAAK,gBAAgBD,KAAI;AAAA,IAC7D,GAAG,IAAI,KAAK,CAAC;AAAA,EACjB,IAAI,CAAC,CAAC,IACJ;AACN,QAAM,OAAO,OAAO,YAAY,WAC1B,WACA,mCAAS,kBAAiB,UACtB,SACA,mCAAS;AACnB,QAAM,OAAO,SAAS,OAAO,MAAM,SAAS,SACtC,OACA;AAAA,IACE,GAAG;AAAA,IACH,aAAa,CAAC,GAAG,KAAK,UAAU,KAAK,gBAAgB,IAAI;AAAA,EAC7D,GAAG,KAAK,KAAK,CAAC;AAClB,QAAM,QAAQ,OAAO,YAAY,YAC7B,QAAQ,SAAS,UACjB,QAAQ,iBAAiB,UACvB,QAAQ,OACR;AACN,MAAI,UAAU,QAAW;AACrB,SAAK,QAAQ;AAAA,EACjB;AACA,QAAM,WAAW,SAAS,SACpB,cACI;AAAA,IACE,GAAG;AAAA,IACH,CAAC,KAAK,cAAc,GAAG;AAAA,EAC3B,IACE,OACJ;AAAA,IACE,MAAM;AAAA,MACF,GAAI,KAAK,iBAAiB,aAAa,CAAC,IAAI,KAAK;AAAA,MACjD,KAAK;AAAA,MACL;AAAA,IACJ,EAAE,KAAK,GAAG;AAAA,IACV,CAAC,KAAK,cAAc,GAAG;AAAA,MACnB,GAAG;AAAA,MACH,CAAC,IAAI,GAAG;AAAA,IACZ;AAAA,EACJ;AACJ,MAAI,KAAK,WAAW,eAAe;AAC/B,aAAS,UAAU;AAAA,EACvB,WACS,KAAK,WAAW,uBAAuB,KAAK,WAAW,UAAU;AACtE,aAAS,UAAU;AAAA,EACvB;AACA,MAAI,KAAK,WAAW,aACf,WAAW,YACR,WAAW,YACX,WAAW,YACV,UAAU,YAAY,MAAM,QAAQ,SAAS,IAAI,IAAK;AAC3D,YAAQ,KAAK,sGAAsG;AAAA,EACvH;AACA,SAAO;AACX;;;AC1BA,IAAO,cAAQ;", "names": ["types", "x", "base", "name", "schema"]}