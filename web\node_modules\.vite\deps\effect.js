import {
  Arbitrary_exports,
  BigDecimal_exports,
  BigInt_exports,
  Brand_exports,
  Cache_exports,
  Channel_exports,
  ChildExecutorDecision_exports,
  ConfigError_exports,
  ConfigProviderPathPatch_exports,
  ConfigProvider_exports,
  Config_exports,
  Console_exports,
  DateTime_exports,
  DefaultServices_exports,
  Encoding_exports,
  FastCheck_exports,
  FiberHandle_exports,
  FiberMap_exports,
  FiberRef_exports,
  FiberSet_exports,
  GroupBy_exports,
  HKT_exports,
  JSONSchema_exports,
  KeyedPool_exports,
  LayerMap_exports,
  Layer_exports,
  Logger_exports,
  Mailbox_exports,
  ManagedRuntime_exports,
  Match_exports,
  MergeDecision_exports,
  MergeState_exports,
  MergeStrategy_exports,
  MetricBoundaries_exports,
  MetricHook_exports,
  MetricKeyType_exports,
  MetricKey_exports,
  MetricLabel_exports,
  MetricPair_exports,
  MetricPolling_exports,
  MetricRegistry_exports,
  MetricState_exports,
  Metric_exports,
  ModuleVersion_exports,
  MutableHashSet_exports,
  NonEmptyIterable_exports,
  Ordering_exports,
  Pool_exports,
  Pretty_exports,
  PrimaryKey_exports,
  PubSub_exports,
  Queue_exports,
  RateLimiter_exports,
  RcMap_exports,
  RcRef_exports,
  Redacted_exports,
  Reloadable_exports,
  RequestBlock_exports,
  RequestResolver_exports,
  Resource_exports,
  RuntimeFlags_exports,
  Runtime_exports,
  STM_exports,
  Schedule_exports,
  Schema_exports,
  ScopedCache_exports,
  ScopedRef_exports,
  Secret_exports,
  SingleProducerAsyncInput_exports,
  Sink_exports,
  SortedMap_exports,
  StreamEmit_exports,
  StreamHaltStrategy_exports,
  Stream_exports,
  Streamable_exports,
  Struct_exports,
  Subscribable_exports,
  SubscriptionRef_exports,
  Supervisor_exports,
  Symbol_exports,
  SynchronizedRef_exports,
  TArray_exports,
  TDeferred_exports,
  TMap_exports,
  TPriorityQueue_exports,
  TPubSub_exports,
  TQueue_exports,
  TRandom_exports,
  TReentrantLock_exports,
  TRef_exports,
  TSemaphore_exports,
  TSet_exports,
  TSubscriptionRef_exports,
  Take_exports,
  TestAnnotationMap_exports,
  TestAnnotation_exports,
  TestAnnotations_exports,
  TestClock_exports,
  TestConfig_exports,
  TestContext_exports,
  TestLive_exports,
  TestServices_exports,
  TestSized_exports,
  Trie_exports,
  Types_exports,
  Unify_exports,
  UpstreamPullRequest_exports,
  UpstreamPullStrategy_exports
} from "./chunk-KSJAPN47.js";
import {
  Array_exports,
  Boolean_exports,
  Cause_exports,
  Chunk_exports,
  Clock_exports,
  Context_exports,
  Cron_exports,
  Data_exports,
  Deferred_exports,
  Differ_exports,
  Duration_exports,
  Effect_exports,
  Effectable_exports,
  Either_exports,
  Equal_exports,
  Equivalence_exports,
  ExecutionStrategy_exports,
  Exit_exports,
  FiberId_exports,
  FiberRefsPatch_exports,
  FiberRefs_exports,
  FiberStatus_exports,
  Fiber_exports,
  Function_exports,
  GlobalValue_exports,
  HashMap_exports,
  HashSet_exports,
  Hash_exports,
  Inspectable_exports,
  Iterable_exports,
  List_exports,
  LogLevel_exports,
  LogSpan_exports,
  Micro_exports,
  MutableHashMap_exports,
  MutableList_exports,
  MutableQueue_exports,
  MutableRef_exports,
  Number_exports,
  Option_exports,
  Order_exports,
  ParseResult_exports,
  Pipeable_exports,
  Predicate_exports,
  Random_exports,
  Readable_exports,
  Record_exports,
  RedBlackTree_exports,
  Ref_exports,
  RegExp_exports,
  Request_exports,
  RuntimeFlagsPatch_exports,
  ScheduleDecision_exports,
  ScheduleInterval_exports,
  ScheduleIntervals_exports,
  Scheduler_exports,
  SchemaAST_exports,
  Scope_exports,
  SortedSet_exports,
  String_exports,
  Tracer_exports,
  Tuple_exports,
  Utils_exports,
  absurd,
  flow,
  hole,
  identity,
  pipe,
  unsafeCoerce
} from "./chunk-JUBHK7DH.js";
import "./chunk-KWPVD4H7.js";
export {
  Arbitrary_exports as Arbitrary,
  Array_exports as Array,
  BigDecimal_exports as BigDecimal,
  BigInt_exports as BigInt,
  Boolean_exports as Boolean,
  Brand_exports as Brand,
  Cache_exports as Cache,
  Cause_exports as Cause,
  Channel_exports as Channel,
  ChildExecutorDecision_exports as ChildExecutorDecision,
  Chunk_exports as Chunk,
  Clock_exports as Clock,
  Config_exports as Config,
  ConfigError_exports as ConfigError,
  ConfigProvider_exports as ConfigProvider,
  ConfigProviderPathPatch_exports as ConfigProviderPathPatch,
  Console_exports as Console,
  Context_exports as Context,
  Cron_exports as Cron,
  Data_exports as Data,
  DateTime_exports as DateTime,
  DefaultServices_exports as DefaultServices,
  Deferred_exports as Deferred,
  Differ_exports as Differ,
  Duration_exports as Duration,
  Effect_exports as Effect,
  Effectable_exports as Effectable,
  Either_exports as Either,
  Encoding_exports as Encoding,
  Equal_exports as Equal,
  Equivalence_exports as Equivalence,
  ExecutionStrategy_exports as ExecutionStrategy,
  Exit_exports as Exit,
  FastCheck_exports as FastCheck,
  Fiber_exports as Fiber,
  FiberHandle_exports as FiberHandle,
  FiberId_exports as FiberId,
  FiberMap_exports as FiberMap,
  FiberRef_exports as FiberRef,
  FiberRefs_exports as FiberRefs,
  FiberRefsPatch_exports as FiberRefsPatch,
  FiberSet_exports as FiberSet,
  FiberStatus_exports as FiberStatus,
  Function_exports as Function,
  GlobalValue_exports as GlobalValue,
  GroupBy_exports as GroupBy,
  HKT_exports as HKT,
  Hash_exports as Hash,
  HashMap_exports as HashMap,
  HashSet_exports as HashSet,
  Inspectable_exports as Inspectable,
  Iterable_exports as Iterable,
  JSONSchema_exports as JSONSchema,
  KeyedPool_exports as KeyedPool,
  Layer_exports as Layer,
  LayerMap_exports as LayerMap,
  List_exports as List,
  LogLevel_exports as LogLevel,
  LogSpan_exports as LogSpan,
  Logger_exports as Logger,
  Mailbox_exports as Mailbox,
  ManagedRuntime_exports as ManagedRuntime,
  Match_exports as Match,
  MergeDecision_exports as MergeDecision,
  MergeState_exports as MergeState,
  MergeStrategy_exports as MergeStrategy,
  Metric_exports as Metric,
  MetricBoundaries_exports as MetricBoundaries,
  MetricHook_exports as MetricHook,
  MetricKey_exports as MetricKey,
  MetricKeyType_exports as MetricKeyType,
  MetricLabel_exports as MetricLabel,
  MetricPair_exports as MetricPair,
  MetricPolling_exports as MetricPolling,
  MetricRegistry_exports as MetricRegistry,
  MetricState_exports as MetricState,
  Micro_exports as Micro,
  ModuleVersion_exports as ModuleVersion,
  MutableHashMap_exports as MutableHashMap,
  MutableHashSet_exports as MutableHashSet,
  MutableList_exports as MutableList,
  MutableQueue_exports as MutableQueue,
  MutableRef_exports as MutableRef,
  NonEmptyIterable_exports as NonEmptyIterable,
  Number_exports as Number,
  Option_exports as Option,
  Order_exports as Order,
  Ordering_exports as Ordering,
  ParseResult_exports as ParseResult,
  Pipeable_exports as Pipeable,
  Pool_exports as Pool,
  Predicate_exports as Predicate,
  Pretty_exports as Pretty,
  PrimaryKey_exports as PrimaryKey,
  PubSub_exports as PubSub,
  Queue_exports as Queue,
  Random_exports as Random,
  RateLimiter_exports as RateLimiter,
  RcMap_exports as RcMap,
  RcRef_exports as RcRef,
  Readable_exports as Readable,
  Record_exports as Record,
  RedBlackTree_exports as RedBlackTree,
  Redacted_exports as Redacted,
  Ref_exports as Ref,
  RegExp_exports as RegExp,
  Reloadable_exports as Reloadable,
  Request_exports as Request,
  RequestBlock_exports as RequestBlock,
  RequestResolver_exports as RequestResolver,
  Resource_exports as Resource,
  Runtime_exports as Runtime,
  RuntimeFlags_exports as RuntimeFlags,
  RuntimeFlagsPatch_exports as RuntimeFlagsPatch,
  STM_exports as STM,
  Schedule_exports as Schedule,
  ScheduleDecision_exports as ScheduleDecision,
  ScheduleInterval_exports as ScheduleInterval,
  ScheduleIntervals_exports as ScheduleIntervals,
  Scheduler_exports as Scheduler,
  Schema_exports as Schema,
  SchemaAST_exports as SchemaAST,
  Scope_exports as Scope,
  ScopedCache_exports as ScopedCache,
  ScopedRef_exports as ScopedRef,
  Secret_exports as Secret,
  SingleProducerAsyncInput_exports as SingleProducerAsyncInput,
  Sink_exports as Sink,
  SortedMap_exports as SortedMap,
  SortedSet_exports as SortedSet,
  Stream_exports as Stream,
  StreamEmit_exports as StreamEmit,
  StreamHaltStrategy_exports as StreamHaltStrategy,
  Streamable_exports as Streamable,
  String_exports as String,
  Struct_exports as Struct,
  Subscribable_exports as Subscribable,
  SubscriptionRef_exports as SubscriptionRef,
  Supervisor_exports as Supervisor,
  Symbol_exports as Symbol,
  SynchronizedRef_exports as SynchronizedRef,
  TArray_exports as TArray,
  TDeferred_exports as TDeferred,
  TMap_exports as TMap,
  TPriorityQueue_exports as TPriorityQueue,
  TPubSub_exports as TPubSub,
  TQueue_exports as TQueue,
  TRandom_exports as TRandom,
  TReentrantLock_exports as TReentrantLock,
  TRef_exports as TRef,
  TSemaphore_exports as TSemaphore,
  TSet_exports as TSet,
  TSubscriptionRef_exports as TSubscriptionRef,
  Take_exports as Take,
  TestAnnotation_exports as TestAnnotation,
  TestAnnotationMap_exports as TestAnnotationMap,
  TestAnnotations_exports as TestAnnotations,
  TestClock_exports as TestClock,
  TestConfig_exports as TestConfig,
  TestContext_exports as TestContext,
  TestLive_exports as TestLive,
  TestServices_exports as TestServices,
  TestSized_exports as TestSized,
  Tracer_exports as Tracer,
  Trie_exports as Trie,
  Tuple_exports as Tuple,
  Types_exports as Types,
  Unify_exports as Unify,
  UpstreamPullRequest_exports as UpstreamPullRequest,
  UpstreamPullStrategy_exports as UpstreamPullStrategy,
  Utils_exports as Utils,
  absurd,
  flow,
  hole,
  identity,
  pipe,
  unsafeCoerce
};
//# sourceMappingURL=effect.js.map
