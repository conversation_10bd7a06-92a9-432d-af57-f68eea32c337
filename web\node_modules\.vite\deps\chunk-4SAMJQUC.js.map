{"version": 3, "sources": ["../../d3-dsv/src/dsv.js", "../../d3-dsv/src/csv.js", "../../d3-dsv/src/tsv.js", "../../d3-dsv/src/autoType.js"], "sourcesContent": ["var EOL = {},\n    EOF = {},\n    QUOTE = 34,\n    NEWLINE = 10,\n    RETURN = 13;\n\nfunction objectConverter(columns) {\n  return new Function(\"d\", \"return {\" + columns.map(function(name, i) {\n    return JSON.stringify(name) + \": d[\" + i + \"] || \\\"\\\"\";\n  }).join(\",\") + \"}\");\n}\n\nfunction customConverter(columns, f) {\n  var object = objectConverter(columns);\n  return function(row, i) {\n    return f(object(row), i, columns);\n  };\n}\n\n// Compute unique columns in order of discovery.\nfunction inferColumns(rows) {\n  var columnSet = Object.create(null),\n      columns = [];\n\n  rows.forEach(function(row) {\n    for (var column in row) {\n      if (!(column in columnSet)) {\n        columns.push(columnSet[column] = column);\n      }\n    }\n  });\n\n  return columns;\n}\n\nfunction pad(value, width) {\n  var s = value + \"\", length = s.length;\n  return length < width ? new Array(width - length + 1).join(0) + s : s;\n}\n\nfunction formatYear(year) {\n  return year < 0 ? \"-\" + pad(-year, 6)\n    : year > 9999 ? \"+\" + pad(year, 6)\n    : pad(year, 4);\n}\n\nfunction formatDate(date) {\n  var hours = date.getUTCHours(),\n      minutes = date.getUTCMinutes(),\n      seconds = date.getUTCSeconds(),\n      milliseconds = date.getUTCMilliseconds();\n  return isNaN(date) ? \"Invalid Date\"\n      : formatYear(date.getUTCFullYear(), 4) + \"-\" + pad(date.getUTCMonth() + 1, 2) + \"-\" + pad(date.getUTCDate(), 2)\n      + (milliseconds ? \"T\" + pad(hours, 2) + \":\" + pad(minutes, 2) + \":\" + pad(seconds, 2) + \".\" + pad(milliseconds, 3) + \"Z\"\n      : seconds ? \"T\" + pad(hours, 2) + \":\" + pad(minutes, 2) + \":\" + pad(seconds, 2) + \"Z\"\n      : minutes || hours ? \"T\" + pad(hours, 2) + \":\" + pad(minutes, 2) + \"Z\"\n      : \"\");\n}\n\nexport default function(delimiter) {\n  var reFormat = new RegExp(\"[\\\"\" + delimiter + \"\\n\\r]\"),\n      DELIMITER = delimiter.charCodeAt(0);\n\n  function parse(text, f) {\n    var convert, columns, rows = parseRows(text, function(row, i) {\n      if (convert) return convert(row, i - 1);\n      columns = row, convert = f ? customConverter(row, f) : objectConverter(row);\n    });\n    rows.columns = columns || [];\n    return rows;\n  }\n\n  function parseRows(text, f) {\n    var rows = [], // output rows\n        N = text.length,\n        I = 0, // current character index\n        n = 0, // current line number\n        t, // current token\n        eof = N <= 0, // current token followed by EOF?\n        eol = false; // current token followed by EOL?\n\n    // Strip the trailing newline.\n    if (text.charCodeAt(N - 1) === NEWLINE) --N;\n    if (text.charCodeAt(N - 1) === RETURN) --N;\n\n    function token() {\n      if (eof) return EOF;\n      if (eol) return eol = false, EOL;\n\n      // Unescape quotes.\n      var i, j = I, c;\n      if (text.charCodeAt(j) === QUOTE) {\n        while (I++ < N && text.charCodeAt(I) !== QUOTE || text.charCodeAt(++I) === QUOTE);\n        if ((i = I) >= N) eof = true;\n        else if ((c = text.charCodeAt(I++)) === NEWLINE) eol = true;\n        else if (c === RETURN) { eol = true; if (text.charCodeAt(I) === NEWLINE) ++I; }\n        return text.slice(j + 1, i - 1).replace(/\"\"/g, \"\\\"\");\n      }\n\n      // Find next delimiter or newline.\n      while (I < N) {\n        if ((c = text.charCodeAt(i = I++)) === NEWLINE) eol = true;\n        else if (c === RETURN) { eol = true; if (text.charCodeAt(I) === NEWLINE) ++I; }\n        else if (c !== DELIMITER) continue;\n        return text.slice(j, i);\n      }\n\n      // Return last token before EOF.\n      return eof = true, text.slice(j, N);\n    }\n\n    while ((t = token()) !== EOF) {\n      var row = [];\n      while (t !== EOL && t !== EOF) row.push(t), t = token();\n      if (f && (row = f(row, n++)) == null) continue;\n      rows.push(row);\n    }\n\n    return rows;\n  }\n\n  function preformatBody(rows, columns) {\n    return rows.map(function(row) {\n      return columns.map(function(column) {\n        return formatValue(row[column]);\n      }).join(delimiter);\n    });\n  }\n\n  function format(rows, columns) {\n    if (columns == null) columns = inferColumns(rows);\n    return [columns.map(formatValue).join(delimiter)].concat(preformatBody(rows, columns)).join(\"\\n\");\n  }\n\n  function formatBody(rows, columns) {\n    if (columns == null) columns = inferColumns(rows);\n    return preformatBody(rows, columns).join(\"\\n\");\n  }\n\n  function formatRows(rows) {\n    return rows.map(formatRow).join(\"\\n\");\n  }\n\n  function formatRow(row) {\n    return row.map(formatValue).join(delimiter);\n  }\n\n  function formatValue(value) {\n    return value == null ? \"\"\n        : value instanceof Date ? formatDate(value)\n        : reFormat.test(value += \"\") ? \"\\\"\" + value.replace(/\"/g, \"\\\"\\\"\") + \"\\\"\"\n        : value;\n  }\n\n  return {\n    parse: parse,\n    parseRows: parseRows,\n    format: format,\n    formatBody: formatBody,\n    formatRows: formatRows,\n    formatRow: formatRow,\n    formatValue: formatValue\n  };\n}\n", "import dsv from \"./dsv.js\";\n\nvar csv = dsv(\",\");\n\nexport var csvParse = csv.parse;\nexport var csvParseRows = csv.parseRows;\nexport var csvFormat = csv.format;\nexport var csvFormatBody = csv.formatBody;\nexport var csvFormatRows = csv.formatRows;\nexport var csvFormatRow = csv.formatRow;\nexport var csvFormatValue = csv.formatValue;\n", "import dsv from \"./dsv.js\";\n\nvar tsv = dsv(\"\\t\");\n\nexport var tsvParse = tsv.parse;\nexport var tsvParseRows = tsv.parseRows;\nexport var tsvFormat = tsv.format;\nexport var tsvFormatBody = tsv.formatBody;\nexport var tsvFormatRows = tsv.formatRows;\nexport var tsvFormatRow = tsv.formatRow;\nexport var tsvFormatValue = tsv.formatValue;\n", "export default function autoType(object) {\n  for (var key in object) {\n    var value = object[key].trim(), number, m;\n    if (!value) value = null;\n    else if (value === \"true\") value = true;\n    else if (value === \"false\") value = false;\n    else if (value === \"NaN\") value = NaN;\n    else if (!isNaN(number = +value)) value = number;\n    else if (m = value.match(/^([-+]\\d{2})?\\d{4}(-\\d{2}(-\\d{2})?)?(T\\d{2}:\\d{2}(:\\d{2}(\\.\\d{3})?)?(Z|[-+]\\d{2}:\\d{2})?)?$/)) {\n      if (fixtz && !!m[4] && !m[7]) value = value.replace(/-/g, \"/\").replace(/T/, \" \");\n      value = new Date(value);\n    }\n    else continue;\n    object[key] = value;\n  }\n  return object;\n}\n\n// https://github.com/d3/d3-dsv/issues/45\nconst fixtz = new Date(\"2019-01-01T00:00\").getHours() || new Date(\"2019-07-01T00:00\").getHours();"], "mappings": ";AAAA,IAAI,MAAM,CAAC;AAAX,IACI,MAAM,CAAC;AADX,IAEI,QAAQ;AAFZ,IAGI,UAAU;AAHd,IAII,SAAS;AAEb,SAAS,gBAAgB,SAAS;AAChC,SAAO,IAAI,SAAS,KAAK,aAAa,QAAQ,IAAI,SAAS,MAAM,GAAG;AAClE,WAAO,KAAK,UAAU,IAAI,IAAI,SAAS,IAAI;AAAA,EAC7C,CAAC,EAAE,KAAK,GAAG,IAAI,GAAG;AACpB;AAEA,SAAS,gBAAgB,SAAS,GAAG;AACnC,MAAI,SAAS,gBAAgB,OAAO;AACpC,SAAO,SAAS,KAAK,GAAG;AACtB,WAAO,EAAE,OAAO,GAAG,GAAG,GAAG,OAAO;AAAA,EAClC;AACF;AAGA,SAAS,aAAa,MAAM;AAC1B,MAAI,YAAY,uBAAO,OAAO,IAAI,GAC9B,UAAU,CAAC;AAEf,OAAK,QAAQ,SAAS,KAAK;AACzB,aAAS,UAAU,KAAK;AACtB,UAAI,EAAE,UAAU,YAAY;AAC1B,gBAAQ,KAAK,UAAU,MAAM,IAAI,MAAM;AAAA,MACzC;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAEA,SAAS,IAAI,OAAO,OAAO;AACzB,MAAI,IAAI,QAAQ,IAAI,SAAS,EAAE;AAC/B,SAAO,SAAS,QAAQ,IAAI,MAAM,QAAQ,SAAS,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI;AACtE;AAEA,SAAS,WAAW,MAAM;AACxB,SAAO,OAAO,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,IAChC,OAAO,OAAO,MAAM,IAAI,MAAM,CAAC,IAC/B,IAAI,MAAM,CAAC;AACjB;AAEA,SAAS,WAAW,MAAM;AACxB,MAAI,QAAQ,KAAK,YAAY,GACzB,UAAU,KAAK,cAAc,GAC7B,UAAU,KAAK,cAAc,GAC7B,eAAe,KAAK,mBAAmB;AAC3C,SAAO,MAAM,IAAI,IAAI,iBACf,WAAW,KAAK,eAAe,GAAG,CAAC,IAAI,MAAM,IAAI,KAAK,YAAY,IAAI,GAAG,CAAC,IAAI,MAAM,IAAI,KAAK,WAAW,GAAG,CAAC,KAC3G,eAAe,MAAM,IAAI,OAAO,CAAC,IAAI,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,IAAI,cAAc,CAAC,IAAI,MACnH,UAAU,MAAM,IAAI,OAAO,CAAC,IAAI,MAAM,IAAI,SAAS,CAAC,IAAI,MAAM,IAAI,SAAS,CAAC,IAAI,MAChF,WAAW,QAAQ,MAAM,IAAI,OAAO,CAAC,IAAI,MAAM,IAAI,SAAS,CAAC,IAAI,MACjE;AACR;AAEe,SAAR,YAAiB,WAAW;AACjC,MAAI,WAAW,IAAI,OAAO,OAAQ,YAAY,OAAO,GACjD,YAAY,UAAU,WAAW,CAAC;AAEtC,WAAS,MAAM,MAAM,GAAG;AACtB,QAAI,SAAS,SAAS,OAAO,UAAU,MAAM,SAAS,KAAK,GAAG;AAC5D,UAAI,QAAS,QAAO,QAAQ,KAAK,IAAI,CAAC;AACtC,gBAAU,KAAK,UAAU,IAAI,gBAAgB,KAAK,CAAC,IAAI,gBAAgB,GAAG;AAAA,IAC5E,CAAC;AACD,SAAK,UAAU,WAAW,CAAC;AAC3B,WAAO;AAAA,EACT;AAEA,WAAS,UAAU,MAAM,GAAG;AAC1B,QAAI,OAAO,CAAC,GACR,IAAI,KAAK,QACT,IAAI,GACJ,IAAI,GACJ,GACA,MAAM,KAAK,GACX,MAAM;AAGV,QAAI,KAAK,WAAW,IAAI,CAAC,MAAM,QAAS,GAAE;AAC1C,QAAI,KAAK,WAAW,IAAI,CAAC,MAAM,OAAQ,GAAE;AAEzC,aAAS,QAAQ;AACf,UAAI,IAAK,QAAO;AAChB,UAAI,IAAK,QAAO,MAAM,OAAO;AAG7B,UAAI,GAAG,IAAI,GAAG;AACd,UAAI,KAAK,WAAW,CAAC,MAAM,OAAO;AAChC,eAAO,MAAM,KAAK,KAAK,WAAW,CAAC,MAAM,SAAS,KAAK,WAAW,EAAE,CAAC,MAAM,MAAM;AACjF,aAAK,IAAI,MAAM,EAAG,OAAM;AAAA,kBACd,IAAI,KAAK,WAAW,GAAG,OAAO,QAAS,OAAM;AAAA,iBAC9C,MAAM,QAAQ;AAAE,gBAAM;AAAM,cAAI,KAAK,WAAW,CAAC,MAAM,QAAS,GAAE;AAAA,QAAG;AAC9E,eAAO,KAAK,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,QAAQ,OAAO,GAAI;AAAA,MACrD;AAGA,aAAO,IAAI,GAAG;AACZ,aAAK,IAAI,KAAK,WAAW,IAAI,GAAG,OAAO,QAAS,OAAM;AAAA,iBAC7C,MAAM,QAAQ;AAAE,gBAAM;AAAM,cAAI,KAAK,WAAW,CAAC,MAAM,QAAS,GAAE;AAAA,QAAG,WACrE,MAAM,UAAW;AAC1B,eAAO,KAAK,MAAM,GAAG,CAAC;AAAA,MACxB;AAGA,aAAO,MAAM,MAAM,KAAK,MAAM,GAAG,CAAC;AAAA,IACpC;AAEA,YAAQ,IAAI,MAAM,OAAO,KAAK;AAC5B,UAAI,MAAM,CAAC;AACX,aAAO,MAAM,OAAO,MAAM,IAAK,KAAI,KAAK,CAAC,GAAG,IAAI,MAAM;AACtD,UAAI,MAAM,MAAM,EAAE,KAAK,GAAG,MAAM,KAAM;AACtC,WAAK,KAAK,GAAG;AAAA,IACf;AAEA,WAAO;AAAA,EACT;AAEA,WAAS,cAAc,MAAM,SAAS;AACpC,WAAO,KAAK,IAAI,SAAS,KAAK;AAC5B,aAAO,QAAQ,IAAI,SAAS,QAAQ;AAClC,eAAO,YAAY,IAAI,MAAM,CAAC;AAAA,MAChC,CAAC,EAAE,KAAK,SAAS;AAAA,IACnB,CAAC;AAAA,EACH;AAEA,WAAS,OAAO,MAAM,SAAS;AAC7B,QAAI,WAAW,KAAM,WAAU,aAAa,IAAI;AAChD,WAAO,CAAC,QAAQ,IAAI,WAAW,EAAE,KAAK,SAAS,CAAC,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC,EAAE,KAAK,IAAI;AAAA,EAClG;AAEA,WAAS,WAAW,MAAM,SAAS;AACjC,QAAI,WAAW,KAAM,WAAU,aAAa,IAAI;AAChD,WAAO,cAAc,MAAM,OAAO,EAAE,KAAK,IAAI;AAAA,EAC/C;AAEA,WAAS,WAAW,MAAM;AACxB,WAAO,KAAK,IAAI,SAAS,EAAE,KAAK,IAAI;AAAA,EACtC;AAEA,WAAS,UAAU,KAAK;AACtB,WAAO,IAAI,IAAI,WAAW,EAAE,KAAK,SAAS;AAAA,EAC5C;AAEA,WAAS,YAAY,OAAO;AAC1B,WAAO,SAAS,OAAO,KACjB,iBAAiB,OAAO,WAAW,KAAK,IACxC,SAAS,KAAK,SAAS,EAAE,IAAI,MAAO,MAAM,QAAQ,MAAM,IAAM,IAAI,MAClE;AAAA,EACR;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACjKA,IAAI,MAAM,YAAI,GAAG;AAEV,IAAI,WAAW,IAAI;AACnB,IAAI,eAAe,IAAI;AACvB,IAAI,YAAY,IAAI;AACpB,IAAI,gBAAgB,IAAI;AACxB,IAAI,gBAAgB,IAAI;AACxB,IAAI,eAAe,IAAI;AACvB,IAAI,iBAAiB,IAAI;;;ACRhC,IAAI,MAAM,YAAI,GAAI;AAEX,IAAI,WAAW,IAAI;AACnB,IAAI,eAAe,IAAI;AACvB,IAAI,YAAY,IAAI;AACpB,IAAI,gBAAgB,IAAI;AACxB,IAAI,gBAAgB,IAAI;AACxB,IAAI,eAAe,IAAI;AACvB,IAAI,iBAAiB,IAAI;;;ACVjB,SAAR,SAA0B,QAAQ;AACvC,WAAS,OAAO,QAAQ;AACtB,QAAI,QAAQ,OAAO,GAAG,EAAE,KAAK,GAAG,QAAQ;AACxC,QAAI,CAAC,MAAO,SAAQ;AAAA,aACX,UAAU,OAAQ,SAAQ;AAAA,aAC1B,UAAU,QAAS,SAAQ;AAAA,aAC3B,UAAU,MAAO,SAAQ;AAAA,aACzB,CAAC,MAAM,SAAS,CAAC,KAAK,EAAG,SAAQ;AAAA,aACjC,IAAI,MAAM,MAAM,6FAA6F,GAAG;AACvH,UAAI,SAAS,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,EAAG,SAAQ,MAAM,QAAQ,MAAM,GAAG,EAAE,QAAQ,KAAK,GAAG;AAC/E,cAAQ,IAAI,KAAK,KAAK;AAAA,IACxB,MACK;AACL,WAAO,GAAG,IAAI;AAAA,EAChB;AACA,SAAO;AACT;AAGA,IAAM,SAAQ,oBAAI,KAAK,kBAAkB,GAAE,SAAS,MAAK,oBAAI,KAAK,kBAAkB,GAAE,SAAS;", "names": []}