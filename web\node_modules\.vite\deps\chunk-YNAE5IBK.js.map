{"version": 3, "sources": ["../../@dagrejs/graphlib/lib/graph.js", "../../@dagrejs/graphlib/lib/version.js", "../../@dagrejs/graphlib/lib/index.js", "../../@dagrejs/graphlib/lib/json.js", "../../@dagrejs/graphlib/lib/alg/components.js", "../../@dagrejs/graphlib/lib/data/priority-queue.js", "../../@dagrejs/graphlib/lib/alg/dijkstra.js", "../../@dagrejs/graphlib/lib/alg/dijkstra-all.js", "../../@dagrejs/graphlib/lib/alg/tarjan.js", "../../@dagrejs/graphlib/lib/alg/find-cycles.js", "../../@dagrejs/graphlib/lib/alg/floyd-warshall.js", "../../@dagrejs/graphlib/lib/alg/topsort.js", "../../@dagrejs/graphlib/lib/alg/is-acyclic.js", "../../@dagrejs/graphlib/lib/alg/dfs.js", "../../@dagrejs/graphlib/lib/alg/postorder.js", "../../@dagrejs/graphlib/lib/alg/preorder.js", "../../@dagrejs/graphlib/lib/alg/prim.js", "../../@dagrejs/graphlib/lib/alg/index.js", "../../@dagrejs/graphlib/index.js", "../../@dagrejs/dagre/lib/data/list.js", "../../@dagrejs/dagre/lib/greedy-fas.js", "../../@dagrejs/dagre/lib/util.js", "../../@dagrejs/dagre/lib/acyclic.js", "../../@dagrejs/dagre/lib/normalize.js", "../../@dagrejs/dagre/lib/rank/util.js", "../../@dagrejs/dagre/lib/rank/feasible-tree.js", "../../@dagrejs/dagre/lib/rank/network-simplex.js", "../../@dagrejs/dagre/lib/rank/index.js", "../../@dagrejs/dagre/lib/parent-dummy-chains.js", "../../@dagrejs/dagre/lib/nesting-graph.js", "../../@dagrejs/dagre/lib/add-border-segments.js", "../../@dagrejs/dagre/lib/coordinate-system.js", "../../@dagrejs/dagre/lib/order/init-order.js", "../../@dagrejs/dagre/lib/order/cross-count.js", "../../@dagrejs/dagre/lib/order/barycenter.js", "../../@dagrejs/dagre/lib/order/resolve-conflicts.js", "../../@dagrejs/dagre/lib/order/sort.js", "../../@dagrejs/dagre/lib/order/sort-subgraph.js", "../../@dagrejs/dagre/lib/order/build-layer-graph.js", "../../@dagrejs/dagre/lib/order/add-subgraph-constraints.js", "../../@dagrejs/dagre/lib/order/index.js", "../../@dagrejs/dagre/lib/position/bk.js", "../../@dagrejs/dagre/lib/position/index.js", "../../@dagrejs/dagre/lib/layout.js", "../../@dagrejs/dagre/lib/debug.js", "../../@dagrejs/dagre/lib/version.js", "../../@dagrejs/dagre/index.js"], "sourcesContent": ["\"use strict\";\n\nvar DEFAULT_EDGE_NAME = \"\\x00\";\nvar GRAPH_NODE = \"\\x00\";\nvar EDGE_KEY_DELIM = \"\\x01\";\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\n\nclass Graph {\n  _isDirected = true;\n  _isMultigraph = false;\n  _isCompound = false;\n\n  // Label for the graph itself\n  _label;\n\n  // Defaults to be set when creating a new node\n  _defaultNodeLabelFn = () => undefined;\n\n  // Defaults to be set when creating a new edge\n  _defaultEdgeLabelFn = () => undefined;\n\n  // v -> label\n  _nodes = {};\n\n  // v -> edgeObj\n  _in = {};\n\n  // u -> v -> Number\n  _preds = {};\n\n  // v -> edgeObj\n  _out = {};\n\n  // v -> w -> Number\n  _sucs = {};\n\n  // e -> edgeObj\n  _edgeObjs = {};\n\n  // e -> label\n  _edgeLabels = {};\n\n  /* Number of nodes in the graph. Should only be changed by the implementation. */\n  _nodeCount = 0;\n\n  /* Number of edges in the graph. Should only be changed by the implementation. */\n  _edgeCount = 0;\n\n  _parent;\n\n  _children;\n\n  constructor(opts) {\n    if (opts) {\n      this._isDirected = Object.hasOwn(opts, \"directed\") ? opts.directed : true;\n      this._isMultigraph = Object.hasOwn(opts, \"multigraph\") ? opts.multigraph : false;\n      this._isCompound = Object.hasOwn(opts, \"compound\") ? opts.compound : false;\n    }\n\n    if (this._isCompound) {\n      // v -> parent\n      this._parent = {};\n\n      // v -> children\n      this._children = {};\n      this._children[GRAPH_NODE] = {};\n    }\n  }\n\n  /* === Graph functions ========= */\n\n  /**\n   * Whether graph was created with 'directed' flag set to true or not.\n   */\n  isDirected() {\n    return this._isDirected;\n  }\n\n  /**\n   * Whether graph was created with 'multigraph' flag set to true or not.\n   */\n  isMultigraph() {\n    return this._isMultigraph;\n  }\n\n  /**\n   * Whether graph was created with 'compound' flag set to true or not.\n   */\n  isCompound() {\n    return this._isCompound;\n  }\n\n  /**\n   * Sets the label of the graph.\n   */\n  setGraph(label) {\n    this._label = label;\n    return this;\n  }\n\n  /**\n   * Gets the graph label.\n   */\n  graph() {\n    return this._label;\n  }\n\n\n  /* === Node functions ========== */\n\n  /**\n   * Sets the default node label. If newDefault is a function, it will be\n   * invoked ach time when setting a label for a node. Otherwise, this label\n   * will be assigned as default label in case if no label was specified while\n   * setting a node.\n   * Complexity: O(1).\n   */\n  setDefaultNodeLabel(newDefault) {\n    this._defaultNodeLabelFn = newDefault;\n    if (typeof newDefault !== 'function') {\n      this._defaultNodeLabelFn = () => newDefault;\n    }\n\n    return this;\n  }\n\n  /**\n   * Gets the number of nodes in the graph.\n   * Complexity: O(1).\n   */\n  nodeCount() {\n    return this._nodeCount;\n  }\n\n  /**\n   * Gets all nodes of the graph. Note, the in case of compound graph subnodes are\n   * not included in list.\n   * Complexity: O(1).\n   */\n  nodes() {\n    return Object.keys(this._nodes);\n  }\n\n  /**\n   * Gets list of nodes without in-edges.\n   * Complexity: O(|V|).\n   */\n  sources() {\n    var self = this;\n    return this.nodes().filter(v => Object.keys(self._in[v]).length === 0);\n  }\n\n  /**\n   * Gets list of nodes without out-edges.\n   * Complexity: O(|V|).\n   */\n  sinks() {\n    var self = this;\n    return this.nodes().filter(v => Object.keys(self._out[v]).length === 0);\n  }\n\n  /**\n   * Invokes setNode method for each node in names list.\n   * Complexity: O(|names|).\n   */\n  setNodes(vs, value) {\n    var args = arguments;\n    var self = this;\n    vs.forEach(function(v) {\n      if (args.length > 1) {\n        self.setNode(v, value);\n      } else {\n        self.setNode(v);\n      }\n    });\n    return this;\n  }\n\n  /**\n   * Creates or updates the value for the node v in the graph. If label is supplied\n   * it is set as the value for the node. If label is not supplied and the node was\n   * created by this call then the default node label will be assigned.\n   * Complexity: O(1).\n   */\n  setNode(v, value) {\n    if (Object.hasOwn(this._nodes, v)) {\n      if (arguments.length > 1) {\n        this._nodes[v] = value;\n      }\n      return this;\n    }\n\n    this._nodes[v] = arguments.length > 1 ? value : this._defaultNodeLabelFn(v);\n    if (this._isCompound) {\n      this._parent[v] = GRAPH_NODE;\n      this._children[v] = {};\n      this._children[GRAPH_NODE][v] = true;\n    }\n    this._in[v] = {};\n    this._preds[v] = {};\n    this._out[v] = {};\n    this._sucs[v] = {};\n    ++this._nodeCount;\n    return this;\n  }\n\n  /**\n   * Gets the label of node with specified name.\n   * Complexity: O(|V|).\n   */\n  node(v) {\n    return this._nodes[v];\n  }\n\n  /**\n   * Detects whether graph has a node with specified name or not.\n   */\n  hasNode(v) {\n    return Object.hasOwn(this._nodes, v);\n  }\n\n  /**\n   * Remove the node with the name from the graph or do nothing if the node is not in\n   * the graph. If the node was removed this function also removes any incident\n   * edges.\n   * Complexity: O(1).\n   */\n  removeNode(v) {\n    var self = this;\n    if (Object.hasOwn(this._nodes, v)) {\n      var removeEdge = e => self.removeEdge(self._edgeObjs[e]);\n      delete this._nodes[v];\n      if (this._isCompound) {\n        this._removeFromParentsChildList(v);\n        delete this._parent[v];\n        this.children(v).forEach(function(child) {\n          self.setParent(child);\n        });\n        delete this._children[v];\n      }\n      Object.keys(this._in[v]).forEach(removeEdge);\n      delete this._in[v];\n      delete this._preds[v];\n      Object.keys(this._out[v]).forEach(removeEdge);\n      delete this._out[v];\n      delete this._sucs[v];\n      --this._nodeCount;\n    }\n    return this;\n  }\n\n  /**\n   * Sets node p as a parent for node v if it is defined, or removes the\n   * parent for v if p is undefined. Method throws an exception in case of\n   * invoking it in context of noncompound graph.\n   * Average-case complexity: O(1).\n   */\n  setParent(v, parent) {\n    if (!this._isCompound) {\n      throw new Error(\"Cannot set parent in a non-compound graph\");\n    }\n\n    if (parent === undefined) {\n      parent = GRAPH_NODE;\n    } else {\n      // Coerce parent to string\n      parent += \"\";\n      for (var ancestor = parent; ancestor !== undefined; ancestor = this.parent(ancestor)) {\n        if (ancestor === v) {\n          throw new Error(\"Setting \" + parent+ \" as parent of \" + v +\n              \" would create a cycle\");\n        }\n      }\n\n      this.setNode(parent);\n    }\n\n    this.setNode(v);\n    this._removeFromParentsChildList(v);\n    this._parent[v] = parent;\n    this._children[parent][v] = true;\n    return this;\n  }\n\n  _removeFromParentsChildList(v) {\n    delete this._children[this._parent[v]][v];\n  }\n\n  /**\n   * Gets parent node for node v.\n   * Complexity: O(1).\n   */\n  parent(v) {\n    if (this._isCompound) {\n      var parent = this._parent[v];\n      if (parent !== GRAPH_NODE) {\n        return parent;\n      }\n    }\n  }\n\n  /**\n   * Gets list of direct children of node v.\n   * Complexity: O(1).\n   */\n  children(v = GRAPH_NODE) {\n    if (this._isCompound) {\n      var children = this._children[v];\n      if (children) {\n        return Object.keys(children);\n      }\n    } else if (v === GRAPH_NODE) {\n      return this.nodes();\n    } else if (this.hasNode(v)) {\n      return [];\n    }\n  }\n\n  /**\n   * Return all nodes that are predecessors of the specified node or undefined if node v is not in\n   * the graph. Behavior is undefined for undirected graphs - use neighbors instead.\n   * Complexity: O(|V|).\n   */\n  predecessors(v) {\n    var predsV = this._preds[v];\n    if (predsV) {\n      return Object.keys(predsV);\n    }\n  }\n\n  /**\n   * Return all nodes that are successors of the specified node or undefined if node v is not in\n   * the graph. Behavior is undefined for undirected graphs - use neighbors instead.\n   * Complexity: O(|V|).\n   */\n  successors(v) {\n    var sucsV = this._sucs[v];\n    if (sucsV) {\n      return Object.keys(sucsV);\n    }\n  }\n\n  /**\n   * Return all nodes that are predecessors or successors of the specified node or undefined if\n   * node v is not in the graph.\n   * Complexity: O(|V|).\n   */\n  neighbors(v) {\n    var preds = this.predecessors(v);\n    if (preds) {\n      const union = new Set(preds);\n      for (var succ of this.successors(v)) {\n        union.add(succ);\n      }\n\n      return Array.from(union.values());\n    }\n  }\n\n  isLeaf(v) {\n    var neighbors;\n    if (this.isDirected()) {\n      neighbors = this.successors(v);\n    } else {\n      neighbors = this.neighbors(v);\n    }\n    return neighbors.length === 0;\n  }\n\n  /**\n   * Creates new graph with nodes filtered via filter. Edges incident to rejected node\n   * are also removed. In case of compound graph, if parent is rejected by filter,\n   * than all its children are rejected too.\n   * Average-case complexity: O(|E|+|V|).\n   */\n  filterNodes(filter) {\n    var copy = new this.constructor({\n      directed: this._isDirected,\n      multigraph: this._isMultigraph,\n      compound: this._isCompound\n    });\n\n    copy.setGraph(this.graph());\n\n    var self = this;\n    Object.entries(this._nodes).forEach(function([v, value]) {\n      if (filter(v)) {\n        copy.setNode(v, value);\n      }\n    });\n\n    Object.values(this._edgeObjs).forEach(function(e) {\n      if (copy.hasNode(e.v) && copy.hasNode(e.w)) {\n        copy.setEdge(e, self.edge(e));\n      }\n    });\n\n    var parents = {};\n    function findParent(v) {\n      var parent = self.parent(v);\n      if (parent === undefined || copy.hasNode(parent)) {\n        parents[v] = parent;\n        return parent;\n      } else if (parent in parents) {\n        return parents[parent];\n      } else {\n        return findParent(parent);\n      }\n    }\n\n    if (this._isCompound) {\n      copy.nodes().forEach(v => copy.setParent(v, findParent(v)));\n    }\n\n    return copy;\n  }\n\n  /* === Edge functions ========== */\n\n  /**\n   * Sets the default edge label or factory function. This label will be\n   * assigned as default label in case if no label was specified while setting\n   * an edge or this function will be invoked each time when setting an edge\n   * with no label specified and returned value * will be used as a label for edge.\n   * Complexity: O(1).\n   */\n  setDefaultEdgeLabel(newDefault) {\n    this._defaultEdgeLabelFn = newDefault;\n    if (typeof newDefault !== 'function') {\n      this._defaultEdgeLabelFn = () => newDefault;\n    }\n\n    return this;\n  }\n\n  /**\n   * Gets the number of edges in the graph.\n   * Complexity: O(1).\n   */\n  edgeCount() {\n    return this._edgeCount;\n  }\n\n  /**\n   * Gets edges of the graph. In case of compound graph subgraphs are not considered.\n   * Complexity: O(|E|).\n   */\n  edges() {\n    return Object.values(this._edgeObjs);\n  }\n\n  /**\n   * Establish an edges path over the nodes in nodes list. If some edge is already\n   * exists, it will update its label, otherwise it will create an edge between pair\n   * of nodes with label provided or default label if no label provided.\n   * Complexity: O(|nodes|).\n   */\n  setPath(vs, value) {\n    var self = this;\n    var args = arguments;\n    vs.reduce(function(v, w) {\n      if (args.length > 1) {\n        self.setEdge(v, w, value);\n      } else {\n        self.setEdge(v, w);\n      }\n      return w;\n    });\n    return this;\n  }\n\n  /**\n   * Creates or updates the label for the edge (v, w) with the optionally supplied\n   * name. If label is supplied it is set as the value for the edge. If label is not\n   * supplied and the edge was created by this call then the default edge label will\n   * be assigned. The name parameter is only useful with multigraphs.\n   */\n  setEdge() {\n    var v, w, name, value;\n    var valueSpecified = false;\n    var arg0 = arguments[0];\n\n    if (typeof arg0 === \"object\" && arg0 !== null && \"v\" in arg0) {\n      v = arg0.v;\n      w = arg0.w;\n      name = arg0.name;\n      if (arguments.length === 2) {\n        value = arguments[1];\n        valueSpecified = true;\n      }\n    } else {\n      v = arg0;\n      w = arguments[1];\n      name = arguments[3];\n      if (arguments.length > 2) {\n        value = arguments[2];\n        valueSpecified = true;\n      }\n    }\n\n    v = \"\" + v;\n    w = \"\" + w;\n    if (name !== undefined) {\n      name = \"\" + name;\n    }\n\n    var e = edgeArgsToId(this._isDirected, v, w, name);\n    if (Object.hasOwn(this._edgeLabels, e)) {\n      if (valueSpecified) {\n        this._edgeLabels[e] = value;\n      }\n      return this;\n    }\n\n    if (name !== undefined && !this._isMultigraph) {\n      throw new Error(\"Cannot set a named edge when isMultigraph = false\");\n    }\n\n    // It didn't exist, so we need to create it.\n    // First ensure the nodes exist.\n    this.setNode(v);\n    this.setNode(w);\n\n    this._edgeLabels[e] = valueSpecified ? value : this._defaultEdgeLabelFn(v, w, name);\n\n    var edgeObj = edgeArgsToObj(this._isDirected, v, w, name);\n    // Ensure we add undirected edges in a consistent way.\n    v = edgeObj.v;\n    w = edgeObj.w;\n\n    Object.freeze(edgeObj);\n    this._edgeObjs[e] = edgeObj;\n    incrementOrInitEntry(this._preds[w], v);\n    incrementOrInitEntry(this._sucs[v], w);\n    this._in[w][e] = edgeObj;\n    this._out[v][e] = edgeObj;\n    this._edgeCount++;\n    return this;\n  }\n\n  /**\n   * Gets the label for the specified edge.\n   * Complexity: O(1).\n   */\n  edge(v, w, name) {\n    var e = (arguments.length === 1\n      ? edgeObjToId(this._isDirected, arguments[0])\n      : edgeArgsToId(this._isDirected, v, w, name));\n    return this._edgeLabels[e];\n  }\n\n  /**\n   * Gets the label for the specified edge and converts it to an object.\n   * Complexity: O(1)\n   */\n  edgeAsObj() {\n    const edge = this.edge(...arguments);\n    if (typeof edge !== \"object\") {\n      return {label: edge};\n    }\n\n    return edge;\n  }\n\n  /**\n   * Detects whether the graph contains specified edge or not. No subgraphs are considered.\n   * Complexity: O(1).\n   */\n  hasEdge(v, w, name) {\n    var e = (arguments.length === 1\n      ? edgeObjToId(this._isDirected, arguments[0])\n      : edgeArgsToId(this._isDirected, v, w, name));\n    return Object.hasOwn(this._edgeLabels, e);\n  }\n\n  /**\n   * Removes the specified edge from the graph. No subgraphs are considered.\n   * Complexity: O(1).\n   */\n  removeEdge(v, w, name) {\n    var e = (arguments.length === 1\n      ? edgeObjToId(this._isDirected, arguments[0])\n      : edgeArgsToId(this._isDirected, v, w, name));\n    var edge = this._edgeObjs[e];\n    if (edge) {\n      v = edge.v;\n      w = edge.w;\n      delete this._edgeLabels[e];\n      delete this._edgeObjs[e];\n      decrementOrRemoveEntry(this._preds[w], v);\n      decrementOrRemoveEntry(this._sucs[v], w);\n      delete this._in[w][e];\n      delete this._out[v][e];\n      this._edgeCount--;\n    }\n    return this;\n  }\n\n  /**\n   * Return all edges that point to the node v. Optionally filters those edges down to just those\n   * coming from node u. Behavior is undefined for undirected graphs - use nodeEdges instead.\n   * Complexity: O(|E|).\n   */\n  inEdges(v, u) {\n    var inV = this._in[v];\n    if (inV) {\n      var edges = Object.values(inV);\n      if (!u) {\n        return edges;\n      }\n      return edges.filter(edge => edge.v === u);\n    }\n  }\n\n  /**\n   * Return all edges that are pointed at by node v. Optionally filters those edges down to just\n   * those point to w. Behavior is undefined for undirected graphs - use nodeEdges instead.\n   * Complexity: O(|E|).\n   */\n  outEdges(v, w) {\n    var outV = this._out[v];\n    if (outV) {\n      var edges = Object.values(outV);\n      if (!w) {\n        return edges;\n      }\n      return edges.filter(edge => edge.w === w);\n    }\n  }\n\n  /**\n   * Returns all edges to or from node v regardless of direction. Optionally filters those edges\n   * down to just those between nodes v and w regardless of direction.\n   * Complexity: O(|E|).\n   */\n  nodeEdges(v, w) {\n    var inEdges = this.inEdges(v, w);\n    if (inEdges) {\n      return inEdges.concat(this.outEdges(v, w));\n    }\n  }\n}\n\nfunction incrementOrInitEntry(map, k) {\n  if (map[k]) {\n    map[k]++;\n  } else {\n    map[k] = 1;\n  }\n}\n\nfunction decrementOrRemoveEntry(map, k) {\n  if (!--map[k]) { delete map[k]; }\n}\n\nfunction edgeArgsToId(isDirected, v_, w_, name) {\n  var v = \"\" + v_;\n  var w = \"\" + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return v + EDGE_KEY_DELIM + w + EDGE_KEY_DELIM +\n             (name === undefined ? DEFAULT_EDGE_NAME : name);\n}\n\nfunction edgeArgsToObj(isDirected, v_, w_, name) {\n  var v = \"\" + v_;\n  var w = \"\" + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  var edgeObj =  { v: v, w: w };\n  if (name) {\n    edgeObj.name = name;\n  }\n  return edgeObj;\n}\n\nfunction edgeObjToId(isDirected, edgeObj) {\n  return edgeArgsToId(isDirected, edgeObj.v, edgeObj.w, edgeObj.name);\n}\n\nmodule.exports = Graph;\n", "module.exports = '2.2.4';\n", "// Includes only the \"core\" of graphlib\nmodule.exports = {\n  Graph: require(\"./graph\"),\n  version: require(\"./version\")\n};\n", "var Graph = require(\"./graph\");\n\nmodule.exports = {\n  write: write,\n  read: read\n};\n\n/**\n * Creates a JSON representation of the graph that can be serialized to a string with\n * JSON.stringify. The graph can later be restored using json.read.\n */\nfunction write(g) {\n  var json = {\n    options: {\n      directed: g.isDirected(),\n      multigraph: g.isMultigraph(),\n      compound: g.isCompound()\n    },\n    nodes: writeNodes(g),\n    edges: writeEdges(g)\n  };\n\n  if (g.graph() !== undefined) {\n    json.value = structuredClone(g.graph());\n  }\n  return json;\n}\n\nfunction writeNodes(g) {\n  return g.nodes().map(function(v) {\n    var nodeValue = g.node(v);\n    var parent = g.parent(v);\n    var node = { v: v };\n    if (nodeValue !== undefined) {\n      node.value = nodeValue;\n    }\n    if (parent !== undefined) {\n      node.parent = parent;\n    }\n    return node;\n  });\n}\n\nfunction writeEdges(g) {\n  return g.edges().map(function(e) {\n    var edgeValue = g.edge(e);\n    var edge = { v: e.v, w: e.w };\n    if (e.name !== undefined) {\n      edge.name = e.name;\n    }\n    if (edgeValue !== undefined) {\n      edge.value = edgeValue;\n    }\n    return edge;\n  });\n}\n\n/**\n * Takes JSON as input and returns the graph representation.\n *\n * @example\n * var g2 = graphlib.json.read(JSON.parse(str));\n * g2.nodes();\n * // ['a', 'b']\n * g2.edges()\n * // [ { v: 'a', w: 'b' } ]\n */\nfunction read(json) {\n  var g = new Graph(json.options).setGraph(json.value);\n  json.nodes.forEach(function(entry) {\n    g.setNode(entry.v, entry.value);\n    if (entry.parent) {\n      g.setParent(entry.v, entry.parent);\n    }\n  });\n  json.edges.forEach(function(entry) {\n    g.setEdge({ v: entry.v, w: entry.w, name: entry.name }, entry.value);\n  });\n  return g;\n}\n", "module.exports = components;\n\nfunction components(g) {\n  var visited = {};\n  var cmpts = [];\n  var cmpt;\n\n  function dfs(v) {\n    if (Object.hasOwn(visited, v)) return;\n    visited[v] = true;\n    cmpt.push(v);\n    g.successors(v).forEach(dfs);\n    g.predecessors(v).forEach(dfs);\n  }\n\n  g.nodes().forEach(function(v) {\n    cmpt = [];\n    dfs(v);\n    if (cmpt.length) {\n      cmpts.push(cmpt);\n    }\n  });\n\n  return cmpts;\n}\n", "/**\n * A min-priority queue data structure. This algorithm is derived from <PERSON><PERSON><PERSON>,\n * et al., \"Introduction to Algorithms\". The basic idea of a min-priority\n * queue is that you can efficiently (in O(1) time) get the smallest key in\n * the queue. Adding and removing elements takes O(log n) time. A key can\n * have its priority decreased in O(log n) time.\n */\nclass PriorityQueue {\n  _arr = [];\n  _keyIndices = {};\n\n  /**\n   * Returns the number of elements in the queue. Takes `O(1)` time.\n   */\n  size() {\n    return this._arr.length;\n  }\n\n  /**\n   * Returns the keys that are in the queue. Takes `O(n)` time.\n   */\n  keys() {\n    return this._arr.map(function(x) { return x.key; });\n  }\n\n  /**\n   * Returns `true` if **key** is in the queue and `false` if not.\n   */\n  has(key) {\n    return Object.hasOwn(this._keyIndices, key);\n  }\n\n  /**\n   * Returns the priority for **key**. If **key** is not present in the queue\n   * then this function returns `undefined`. Takes `O(1)` time.\n   *\n   * @param {Object} key\n   */\n  priority(key) {\n    var index = this._keyIndices[key];\n    if (index !== undefined) {\n      return this._arr[index].priority;\n    }\n  }\n\n  /**\n   * Returns the key for the minimum element in this queue. If the queue is\n   * empty this function throws an Error. Takes `O(1)` time.\n   */\n  min() {\n    if (this.size() === 0) {\n      throw new Error(\"Queue underflow\");\n    }\n    return this._arr[0].key;\n  }\n\n  /**\n   * Inserts a new key into the priority queue. If the key already exists in\n   * the queue this function returns `false`; otherwise it will return `true`.\n   * Takes `O(n)` time.\n   *\n   * @param {Object} key the key to add\n   * @param {Number} priority the initial priority for the key\n   */\n  add(key, priority) {\n    var keyIndices = this._keyIndices;\n    key = String(key);\n    if (!Object.hasOwn(keyIndices, key)) {\n      var arr = this._arr;\n      var index = arr.length;\n      keyIndices[key] = index;\n      arr.push({key: key, priority: priority});\n      this._decrease(index);\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Removes and returns the smallest key in the queue. Takes `O(log n)` time.\n   */\n  removeMin() {\n    this._swap(0, this._arr.length - 1);\n    var min = this._arr.pop();\n    delete this._keyIndices[min.key];\n    this._heapify(0);\n    return min.key;\n  }\n\n  /**\n   * Decreases the priority for **key** to **priority**. If the new priority is\n   * greater than the previous priority, this function will throw an Error.\n   *\n   * @param {Object} key the key for which to raise priority\n   * @param {Number} priority the new priority for the key\n   */\n  decrease(key, priority) {\n    var index = this._keyIndices[key];\n    if (priority > this._arr[index].priority) {\n      throw new Error(\"New priority is greater than current priority. \" +\n          \"Key: \" + key + \" Old: \" + this._arr[index].priority + \" New: \" + priority);\n    }\n    this._arr[index].priority = priority;\n    this._decrease(index);\n  }\n\n  _heapify(i) {\n    var arr = this._arr;\n    var l = 2 * i;\n    var r = l + 1;\n    var largest = i;\n    if (l < arr.length) {\n      largest = arr[l].priority < arr[largest].priority ? l : largest;\n      if (r < arr.length) {\n        largest = arr[r].priority < arr[largest].priority ? r : largest;\n      }\n      if (largest !== i) {\n        this._swap(i, largest);\n        this._heapify(largest);\n      }\n    }\n  }\n\n  _decrease(index) {\n    var arr = this._arr;\n    var priority = arr[index].priority;\n    var parent;\n    while (index !== 0) {\n      parent = index >> 1;\n      if (arr[parent].priority < priority) {\n        break;\n      }\n      this._swap(index, parent);\n      index = parent;\n    }\n  }\n\n  _swap(i, j) {\n    var arr = this._arr;\n    var keyIndices = this._keyIndices;\n    var origArrI = arr[i];\n    var origArrJ = arr[j];\n    arr[i] = origArrJ;\n    arr[j] = origArrI;\n    keyIndices[origArrJ.key] = i;\n    keyIndices[origArrI.key] = j;\n  }\n}\n\nmodule.exports = PriorityQueue;\n", "var PriorityQueue = require(\"../data/priority-queue\");\n\nmodule.exports = dijkstra;\n\nvar DEFAULT_WEIGHT_FUNC = () => 1;\n\nfunction dijkstra(g, source, weightFn, edgeFn) {\n  return runDijkstra(g, String(source),\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn || function(v) { return g.outEdges(v); });\n}\n\nfunction runDijkstra(g, source, weightFn, edgeFn) {\n  var results = {};\n  var pq = new PriorityQueue();\n  var v, vEntry;\n\n  var updateNeighbors = function(edge) {\n    var w = edge.v !== v ? edge.v : edge.w;\n    var wEntry = results[w];\n    var weight = weightFn(edge);\n    var distance = vEntry.distance + weight;\n\n    if (weight < 0) {\n      throw new Error(\"dijkstra does not allow negative edge weights. \" +\n                      \"Bad edge: \" + edge + \" Weight: \" + weight);\n    }\n\n    if (distance < wEntry.distance) {\n      wEntry.distance = distance;\n      wEntry.predecessor = v;\n      pq.decrease(w, distance);\n    }\n  };\n\n  g.nodes().forEach(function(v) {\n    var distance = v === source ? 0 : Number.POSITIVE_INFINITY;\n    results[v] = { distance: distance };\n    pq.add(v, distance);\n  });\n\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    vEntry = results[v];\n    if (vEntry.distance === Number.POSITIVE_INFINITY) {\n      break;\n    }\n\n    edgeFn(v).forEach(updateNeighbors);\n  }\n\n  return results;\n}\n", "var dijkstra = require(\"./dijkstra\");\n\nmodule.exports = dijkstraAll;\n\nfunction dijkstraAll(g, weightFunc, edgeFunc) {\n  return g.nodes().reduce(function(acc, v) {\n    acc[v] = dijkstra(g, v, weightFunc, edgeFunc);\n    return acc;\n  }, {});\n}\n", "module.exports = tarjan;\n\nfunction tarjan(g) {\n  var index = 0;\n  var stack = [];\n  var visited = {}; // node id -> { onStack, lowlink, index }\n  var results = [];\n\n  function dfs(v) {\n    var entry = visited[v] = {\n      onStack: true,\n      lowlink: index,\n      index: index++\n    };\n    stack.push(v);\n\n    g.successors(v).forEach(function(w) {\n      if (!Object.hasOwn(visited, w)) {\n        dfs(w);\n        entry.lowlink = Math.min(entry.lowlink, visited[w].lowlink);\n      } else if (visited[w].onStack) {\n        entry.lowlink = Math.min(entry.lowlink, visited[w].index);\n      }\n    });\n\n    if (entry.lowlink === entry.index) {\n      var cmpt = [];\n      var w;\n      do {\n        w = stack.pop();\n        visited[w].onStack = false;\n        cmpt.push(w);\n      } while (v !== w);\n      results.push(cmpt);\n    }\n  }\n\n  g.nodes().forEach(function(v) {\n    if (!Object.hasOwn(visited, v)) {\n      dfs(v);\n    }\n  });\n\n  return results;\n}\n", "var tarjan = require(\"./tarjan\");\n\nmodule.exports = findCycles;\n\nfunction findCycles(g) {\n  return tarjan(g).filter(function(cmpt) {\n    return cmpt.length > 1 || (cmpt.length === 1 && g.hasEdge(cmpt[0], cmpt[0]));\n  });\n}\n", "module.exports = floyd<PERSON>arshall;\n\nvar DEFAULT_WEIGHT_FUNC = () => 1;\n\nfunction floydWarshall(g, weightFn, edgeFn) {\n  return runFloydWarshall(g,\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn || function(v) { return g.outEdges(v); });\n}\n\nfunction runFloydWarshall(g, weightFn, edgeFn) {\n  var results = {};\n  var nodes = g.nodes();\n\n  nodes.forEach(function(v) {\n    results[v] = {};\n    results[v][v] = { distance: 0 };\n    nodes.forEach(function(w) {\n      if (v !== w) {\n        results[v][w] = { distance: Number.POSITIVE_INFINITY };\n      }\n    });\n    edgeFn(v).forEach(function(edge) {\n      var w = edge.v === v ? edge.w : edge.v;\n      var d = weightFn(edge);\n      results[v][w] = { distance: d, predecessor: v };\n    });\n  });\n\n  nodes.forEach(function(k) {\n    var rowK = results[k];\n    nodes.forEach(function(i) {\n      var rowI = results[i];\n      nodes.forEach(function(j) {\n        var ik = rowI[k];\n        var kj = rowK[j];\n        var ij = rowI[j];\n        var altDistance = ik.distance + kj.distance;\n        if (altDistance < ij.distance) {\n          ij.distance = altDistance;\n          ij.predecessor = kj.predecessor;\n        }\n      });\n    });\n  });\n\n  return results;\n}\n", "function topsort(g) {\n  var visited = {};\n  var stack = {};\n  var results = [];\n\n  function visit(node) {\n    if (Object.hasOwn(stack, node)) {\n      throw new CycleException();\n    }\n\n    if (!Object.hasOwn(visited, node)) {\n      stack[node] = true;\n      visited[node] = true;\n      g.predecessors(node).forEach(visit);\n      delete stack[node];\n      results.push(node);\n    }\n  }\n\n  g.sinks().forEach(visit);\n\n  if (Object.keys(visited).length !== g.nodeCount()) {\n    throw new CycleException();\n  }\n\n  return results;\n}\n\nclass CycleException extends Error {\n  constructor() {\n    super(...arguments);\n  }\n}\n\nmodule.exports = topsort;\ntopsort.CycleException = CycleException;\n", "var topsort = require(\"./topsort\");\n\nmodule.exports = isAcyclic;\n\nfunction isAcyclic(g) {\n  try {\n    topsort(g);\n  } catch (e) {\n    if (e instanceof topsort.CycleException) {\n      return false;\n    }\n    throw e;\n  }\n  return true;\n}\n", "module.exports = dfs;\n\n/*\n * A helper that preforms a pre- or post-order traversal on the input graph\n * and returns the nodes in the order they were visited. If the graph is\n * undirected then this algorithm will navigate using neighbors. If the graph\n * is directed then this algorithm will navigate using successors.\n *\n * If the order is not \"post\", it will be treated as \"pre\".\n */\nfunction dfs(g, vs, order) {\n  if (!Array.isArray(vs)) {\n    vs = [vs];\n  }\n\n  var navigation = g.isDirected() ? v => g.successors(v) : v => g.neighbors(v);\n  var orderFunc = order === \"post\" ? postOrderDfs : preOrderDfs;\n\n  var acc = [];\n  var visited = {};\n  vs.forEach(v => {\n    if (!g.hasNode(v)) {\n      throw new Error(\"Graph does not have node: \" + v);\n    }\n\n    orderFunc(v, navigation, visited, acc);\n  });\n\n  return acc;\n}\n\nfunction postOrderDfs(v, navigation, visited, acc) {\n  var stack = [[v, false]];\n  while (stack.length > 0) {\n    var curr = stack.pop();\n    if (curr[1]) {\n      acc.push(curr[0]);\n    } else {\n      if (!Object.hasOwn(visited, curr[0])) {\n        visited[curr[0]] = true;\n        stack.push([curr[0], true]);\n        forEachRight(navigation(curr[0]), w => stack.push([w, false]));\n      }\n    }\n  }\n}\n\nfunction preOrderDfs(v, navigation, visited, acc) {\n  var stack = [v];\n  while (stack.length > 0) {\n    var curr = stack.pop();\n    if (!Object.hasOwn(visited, curr)) {\n      visited[curr] = true;\n      acc.push(curr);\n      forEachRight(navigation(curr), w => stack.push(w));\n    }\n  }\n}\n\nfunction forEachRight(array, iteratee) {\n  var length = array.length;\n  while (length--) {\n    iteratee(array[length], length, array);\n  }\n\n  return array;\n}\n", "var dfs = require(\"./dfs\");\n\nmodule.exports = postorder;\n\nfunction postorder(g, vs) {\n  return dfs(g, vs, \"post\");\n}\n", "var dfs = require(\"./dfs\");\n\nmodule.exports = preorder;\n\nfunction preorder(g, vs) {\n  return dfs(g, vs, \"pre\");\n}\n", "var Graph = require(\"../graph\");\nvar PriorityQueue = require(\"../data/priority-queue\");\n\nmodule.exports = prim;\n\nfunction prim(g, weightFunc) {\n  var result = new Graph();\n  var parents = {};\n  var pq = new PriorityQueue();\n  var v;\n\n  function updateNeighbors(edge) {\n    var w = edge.v === v ? edge.w : edge.v;\n    var pri = pq.priority(w);\n    if (pri !== undefined) {\n      var edgeWeight = weightFunc(edge);\n      if (edgeWeight < pri) {\n        parents[w] = v;\n        pq.decrease(w, edgeWeight);\n      }\n    }\n  }\n\n  if (g.nodeCount() === 0) {\n    return result;\n  }\n\n  g.nodes().forEach(function(v) {\n    pq.add(v, Number.POSITIVE_INFINITY);\n    result.setNode(v);\n  });\n\n  // Start from an arbitrary node\n  pq.decrease(g.nodes()[0], 0);\n\n  var init = false;\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    if (Object.hasOwn(parents, v)) {\n      result.setEdge(v, parents[v]);\n    } else if (init) {\n      throw new Error(\"Input graph is not connected: \" + g);\n    } else {\n      init = true;\n    }\n\n    g.nodeEdges(v).forEach(updateNeighbors);\n  }\n\n  return result;\n}\n", "module.exports = {\n  components: require(\"./components\"),\n  dijkstra: require(\"./dijkstra\"),\n  dijkstraAll: require(\"./dijkstra-all\"),\n  findCycles: require(\"./find-cycles\"),\n  floydWarshall: require(\"./floyd-warshall\"),\n  isAcyclic: require(\"./is-acyclic\"),\n  postorder: require(\"./postorder\"),\n  preorder: require(\"./preorder\"),\n  prim: require(\"./prim\"),\n  tarjan: require(\"./tarjan\"),\n  topsort: require(\"./topsort\")\n};\n", "/**\n * Copyright (c) 2014, <PERSON>\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice, this\n * list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of the copyright holder nor the names of its contributors\n * may be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\n * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL\n * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\n * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\n * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\nvar lib = require(\"./lib\");\n\nmodule.exports = {\n  Graph: lib.Graph,\n  json: require(\"./lib/json\"),\n  alg: require(\"./lib/alg\"),\n  version: lib.version\n};\n", "/*\n * Simple doubly linked list implementation derived from <PERSON><PERSON><PERSON>, et al.,\n * \"Introduction to Algorithms\".\n */\n\nclass List {\n  constructor() {\n    let sentinel = {};\n    sentinel._next = sentinel._prev = sentinel;\n    this._sentinel = sentinel;\n  }\n\n  dequeue() {\n    let sentinel = this._sentinel;\n    let entry = sentinel._prev;\n    if (entry !== sentinel) {\n      unlink(entry);\n      return entry;\n    }\n  }\n\n  enqueue(entry) {\n    let sentinel = this._sentinel;\n    if (entry._prev && entry._next) {\n      unlink(entry);\n    }\n    entry._next = sentinel._next;\n    sentinel._next._prev = entry;\n    sentinel._next = entry;\n    entry._prev = sentinel;\n  }\n\n  toString() {\n    let strs = [];\n    let sentinel = this._sentinel;\n    let curr = sentinel._prev;\n    while (curr !== sentinel) {\n      strs.push(JSON.stringify(curr, filterOutLinks));\n      curr = curr._prev;\n    }\n    return \"[\" + strs.join(\", \") + \"]\";\n  }\n}\n\nfunction unlink(entry) {\n  entry._prev._next = entry._next;\n  entry._next._prev = entry._prev;\n  delete entry._next;\n  delete entry._prev;\n}\n\nfunction filterOutLinks(k, v) {\n  if (k !== \"_next\" && k !== \"_prev\") {\n    return v;\n  }\n}\n\nmodule.exports = List;\n", "let Graph = require(\"@dagrejs/graphlib\").Graph;\nlet List = require(\"./data/list\");\n\n/*\n * A greedy heuristic for finding a feedback arc set for a graph. A feedback\n * arc set is a set of edges that can be removed to make a graph acyclic.\n * The algorithm comes from: <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> <PERSON><PERSON>, \"A fast and\n * effective heuristic for the feedback arc set problem.\" This implementation\n * adjusts that from the paper to allow for weighted edges.\n */\nmodule.exports = greedyFAS;\n\nlet DEFAULT_WEIGHT_FN = () => 1;\n\nfunction greedyFAS(g, weightFn) {\n  if (g.nodeCount() <= 1) {\n    return [];\n  }\n  let state = buildState(g, weightFn || DEFAULT_WEIGHT_FN);\n  let results = doGreedyFAS(state.graph, state.buckets, state.zeroIdx);\n\n  // Expand multi-edges\n  return results.flatMap(e => g.outEdges(e.v, e.w));\n}\n\nfunction doGreedyFAS(g, buckets, zeroIdx) {\n  let results = [];\n  let sources = buckets[buckets.length - 1];\n  let sinks = buckets[0];\n\n  let entry;\n  while (g.nodeCount()) {\n    while ((entry = sinks.dequeue()))   { removeNode(g, buckets, zeroIdx, entry); }\n    while ((entry = sources.dequeue())) { removeNode(g, buckets, zeroIdx, entry); }\n    if (g.nodeCount()) {\n      for (let i = buckets.length - 2; i > 0; --i) {\n        entry = buckets[i].dequeue();\n        if (entry) {\n          results = results.concat(removeNode(g, buckets, zeroIdx, entry, true));\n          break;\n        }\n      }\n    }\n  }\n\n  return results;\n}\n\nfunction removeNode(g, buckets, zeroIdx, entry, collectPredecessors) {\n  let results = collectPredecessors ? [] : undefined;\n\n  g.inEdges(entry.v).forEach(edge => {\n    let weight = g.edge(edge);\n    let uEntry = g.node(edge.v);\n\n    if (collectPredecessors) {\n      results.push({ v: edge.v, w: edge.w });\n    }\n\n    uEntry.out -= weight;\n    assignBucket(buckets, zeroIdx, uEntry);\n  });\n\n  g.outEdges(entry.v).forEach(edge => {\n    let weight = g.edge(edge);\n    let w = edge.w;\n    let wEntry = g.node(w);\n    wEntry[\"in\"] -= weight;\n    assignBucket(buckets, zeroIdx, wEntry);\n  });\n\n  g.removeNode(entry.v);\n\n  return results;\n}\n\nfunction buildState(g, weightFn) {\n  let fasGraph = new Graph();\n  let maxIn = 0;\n  let maxOut = 0;\n\n  g.nodes().forEach(v => {\n    fasGraph.setNode(v, { v: v, \"in\": 0, out: 0 });\n  });\n\n  // Aggregate weights on nodes, but also sum the weights across multi-edges\n  // into a single edge for the fasGraph.\n  g.edges().forEach(e => {\n    let prevWeight = fasGraph.edge(e.v, e.w) || 0;\n    let weight = weightFn(e);\n    let edgeWeight = prevWeight + weight;\n    fasGraph.setEdge(e.v, e.w, edgeWeight);\n    maxOut = Math.max(maxOut, fasGraph.node(e.v).out += weight);\n    maxIn  = Math.max(maxIn,  fasGraph.node(e.w)[\"in\"]  += weight);\n  });\n\n  let buckets = range(maxOut + maxIn + 3).map(() => new List());\n  let zeroIdx = maxIn + 1;\n\n  fasGraph.nodes().forEach(v => {\n    assignBucket(buckets, zeroIdx, fasGraph.node(v));\n  });\n\n  return { graph: fasGraph, buckets: buckets, zeroIdx: zeroIdx };\n}\n\nfunction assignBucket(buckets, zeroIdx, entry) {\n  if (!entry.out) {\n    buckets[0].enqueue(entry);\n  } else if (!entry[\"in\"]) {\n    buckets[buckets.length - 1].enqueue(entry);\n  } else {\n    buckets[entry.out - entry[\"in\"] + zeroIdx].enqueue(entry);\n  }\n}\n\nfunction range(limit) {\n  const range = [];\n  for (let i = 0; i < limit; i++) {\n    range.push(i);\n  }\n\n  return range;\n}\n", "/* eslint \"no-console\": off */\n\n\"use strict\";\n\nlet Graph = require(\"@dagrejs/graphlib\").Graph;\n\nmodule.exports = {\n  addBorderNode,\n  addDummyNode,\n  applyWithChunking,\n  asNonCompoundGraph,\n  buildLayerMatrix,\n  intersectRect,\n  mapValues,\n  maxRank,\n  normalizeRanks,\n  notime,\n  partition,\n  pick,\n  predecessorWeights,\n  range,\n  removeEmptyRanks,\n  simplify,\n  successorWeights,\n  time,\n  uniqueId,\n  zipObject,\n};\n\n/*\n * Adds a dummy node to the graph and return v.\n */\nfunction addDummyNode(g, type, attrs, name) {\n  let v;\n  do {\n    v = uniqueId(name);\n  } while (g.hasNode(v));\n\n  attrs.dummy = type;\n  g.setNode(v, attrs);\n  return v;\n}\n\n/*\n * Returns a new graph with only simple edges. Handles aggregation of data\n * associated with multi-edges.\n */\nfunction simplify(g) {\n  let simplified = new Graph().setGraph(g.graph());\n  g.nodes().forEach(v => simplified.setNode(v, g.node(v)));\n  g.edges().forEach(e => {\n    let simpleLabel = simplified.edge(e.v, e.w) || { weight: 0, minlen: 1 };\n    let label = g.edge(e);\n    simplified.setEdge(e.v, e.w, {\n      weight: simpleLabel.weight + label.weight,\n      minlen: Math.max(simpleLabel.minlen, label.minlen)\n    });\n  });\n  return simplified;\n}\n\nfunction asNonCompoundGraph(g) {\n  let simplified = new Graph({ multigraph: g.isMultigraph() }).setGraph(g.graph());\n  g.nodes().forEach(v => {\n    if (!g.children(v).length) {\n      simplified.setNode(v, g.node(v));\n    }\n  });\n  g.edges().forEach(e => {\n    simplified.setEdge(e, g.edge(e));\n  });\n  return simplified;\n}\n\nfunction successorWeights(g) {\n  let weightMap = g.nodes().map(v => {\n    let sucs = {};\n    g.outEdges(v).forEach(e => {\n      sucs[e.w] = (sucs[e.w] || 0) + g.edge(e).weight;\n    });\n    return sucs;\n  });\n  return zipObject(g.nodes(), weightMap);\n}\n\nfunction predecessorWeights(g) {\n  let weightMap = g.nodes().map(v => {\n    let preds = {};\n    g.inEdges(v).forEach(e => {\n      preds[e.v] = (preds[e.v] || 0) + g.edge(e).weight;\n    });\n    return preds;\n  });\n  return zipObject(g.nodes(), weightMap);\n}\n\n/*\n * Finds where a line starting at point ({x, y}) would intersect a rectangle\n * ({x, y, width, height}) if it were pointing at the rectangle's center.\n */\nfunction intersectRect(rect, point) {\n  let x = rect.x;\n  let y = rect.y;\n\n  // Rectangle intersection algorithm from:\n  // http://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  let dx = point.x - x;\n  let dy = point.y - y;\n  let w = rect.width / 2;\n  let h = rect.height / 2;\n\n  if (!dx && !dy) {\n    throw new Error(\"Not possible to find intersection inside of the rectangle\");\n  }\n\n  let sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = h * dx / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = w * dy / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n}\n\n/*\n * Given a DAG with each node assigned \"rank\" and \"order\" properties, this\n * function will produce a matrix with the ids of each node.\n */\nfunction buildLayerMatrix(g) {\n  let layering = range(maxRank(g) + 1).map(() => []);\n  g.nodes().forEach(v => {\n    let node = g.node(v);\n    let rank = node.rank;\n    if (rank !== undefined) {\n      layering[rank][node.order] = v;\n    }\n  });\n  return layering;\n}\n\n/*\n * Adjusts the ranks for all nodes in the graph such that all nodes v have\n * rank(v) >= 0 and at least one node w has rank(w) = 0.\n */\nfunction normalizeRanks(g) {\n  let nodeRanks = g.nodes().map(v => {\n    let rank = g.node(v).rank;\n    if (rank === undefined) {\n      return Number.MAX_VALUE;\n    }\n\n    return rank;\n  });\n  let min = applyWithChunking(Math.min, nodeRanks);\n  g.nodes().forEach(v => {\n    let node = g.node(v);\n    if (Object.hasOwn(node, \"rank\")) {\n      node.rank -= min;\n    }\n  });\n}\n\nfunction removeEmptyRanks(g) {\n  // Ranks may not start at 0, so we need to offset them\n  let nodeRanks = g.nodes().map(v => g.node(v).rank);\n  let offset = applyWithChunking(Math.min, nodeRanks);\n\n  let layers = [];\n  g.nodes().forEach(v => {\n    let rank = g.node(v).rank - offset;\n    if (!layers[rank]) {\n      layers[rank] = [];\n    }\n    layers[rank].push(v);\n  });\n\n  let delta = 0;\n  let nodeRankFactor = g.graph().nodeRankFactor;\n  Array.from(layers).forEach((vs, i) => {\n    if (vs === undefined && i % nodeRankFactor !== 0) {\n      --delta;\n    } else if (vs !== undefined && delta) {\n      vs.forEach(v => g.node(v).rank += delta);\n    }\n  });\n}\n\nfunction addBorderNode(g, prefix, rank, order) {\n  let node = {\n    width: 0,\n    height: 0\n  };\n  if (arguments.length >= 4) {\n    node.rank = rank;\n    node.order = order;\n  }\n  return addDummyNode(g, \"border\", node, prefix);\n}\n\nfunction splitToChunks(array, chunkSize = CHUNKING_THRESHOLD) {\n  const chunks = [];\n  for (let i = 0; i < array.length; i += chunkSize) {\n    const chunk = array.slice(i, i + chunkSize);\n    chunks.push(chunk);\n  }\n  return chunks;\n}\n\nconst CHUNKING_THRESHOLD = 65535;\n\nfunction applyWithChunking(fn, argsArray) {\n  if(argsArray.length > CHUNKING_THRESHOLD) {\n    const chunks = splitToChunks(argsArray);\n    return fn.apply(null, chunks.map(chunk => fn.apply(null, chunk)));\n  } else {\n    return fn.apply(null, argsArray);\n  }\n}\n\nfunction maxRank(g) {\n  const nodes = g.nodes();\n  const nodeRanks = nodes.map(v => {\n    let rank = g.node(v).rank;\n    if (rank === undefined) {\n      return Number.MIN_VALUE;\n    }\n    return rank;\n  });\n\n  return applyWithChunking(Math.max, nodeRanks);\n}\n\n/*\n * Partition a collection into two groups: `lhs` and `rhs`. If the supplied\n * function returns true for an entry it goes into `lhs`. Otherwise it goes\n * into `rhs.\n */\nfunction partition(collection, fn) {\n  let result = { lhs: [], rhs: [] };\n  collection.forEach(value => {\n    if (fn(value)) {\n      result.lhs.push(value);\n    } else {\n      result.rhs.push(value);\n    }\n  });\n  return result;\n}\n\n/*\n * Returns a new function that wraps `fn` with a timer. The wrapper logs the\n * time it takes to execute the function.\n */\nfunction time(name, fn) {\n  let start = Date.now();\n  try {\n    return fn();\n  } finally {\n    console.log(name + \" time: \" + (Date.now() - start) + \"ms\");\n  }\n}\n\nfunction notime(name, fn) {\n  return fn();\n}\n\nlet idCounter = 0;\nfunction uniqueId(prefix) {\n  var id = ++idCounter;\n  return toString(prefix) + id;\n}\n\nfunction range(start, limit, step = 1) {\n  if (limit == null) {\n    limit = start;\n    start = 0;\n  }\n\n  let endCon = (i) => i < limit;\n  if (step < 0) {\n    endCon = (i) => limit < i;\n  }\n\n  const range = [];\n  for (let i = start; endCon(i); i += step) {\n    range.push(i);\n  }\n\n  return range;\n}\n\nfunction pick(source, keys) {\n  const dest = {};\n  for (const key of keys) {\n    if (source[key] !== undefined) {\n      dest[key] = source[key];\n    }\n  }\n\n  return dest;\n}\n\nfunction mapValues(obj, funcOrProp) {\n  let func = funcOrProp;\n  if (typeof funcOrProp === 'string') {\n    func = (val) => val[funcOrProp];\n  }\n\n  return Object.entries(obj).reduce((acc, [k, v]) => {\n    acc[k] = func(v, k);\n    return acc;\n  }, {});\n}\n\nfunction zipObject(props, values) {\n  return props.reduce((acc, key, i) => {\n    acc[key] = values[i];\n    return acc;\n  }, {});\n}\n", "\"use strict\";\n\nlet greedyFAS = require(\"./greedy-fas\");\nlet uniqueId = require(\"./util\").uniqueId;\n\nmodule.exports = {\n  run: run,\n  undo: undo\n};\n\nfunction run(g) {\n  let fas = (g.graph().acyclicer === \"greedy\"\n    ? greedyFAS(g, weightFn(g))\n    : dfsFAS(g));\n  fas.forEach(e => {\n    let label = g.edge(e);\n    g.removeEdge(e);\n    label.forwardName = e.name;\n    label.reversed = true;\n    g.setEdge(e.w, e.v, label, uniqueId(\"rev\"));\n  });\n\n  function weightFn(g) {\n    return e => {\n      return g.edge(e).weight;\n    };\n  }\n}\n\nfunction dfsFAS(g) {\n  let fas = [];\n  let stack = {};\n  let visited = {};\n\n  function dfs(v) {\n    if (Object.hasOwn(visited, v)) {\n      return;\n    }\n    visited[v] = true;\n    stack[v] = true;\n    g.outEdges(v).forEach(e => {\n      if (Object.hasOwn(stack, e.w)) {\n        fas.push(e);\n      } else {\n        dfs(e.w);\n      }\n    });\n    delete stack[v];\n  }\n\n  g.nodes().forEach(dfs);\n  return fas;\n}\n\nfunction undo(g) {\n  g.edges().forEach(e => {\n    let label = g.edge(e);\n    if (label.reversed) {\n      g.removeEdge(e);\n\n      let forwardName = label.forwardName;\n      delete label.reversed;\n      delete label.forwardName;\n      g.setEdge(e.w, e.v, label, forwardName);\n    }\n  });\n}\n", "\"use strict\";\n\nlet util = require(\"./util\");\n\nmodule.exports = {\n  run: run,\n  undo: undo\n};\n\n/*\n * Breaks any long edges in the graph into short segments that span 1 layer\n * each. This operation is undoable with the denormalize function.\n *\n * Pre-conditions:\n *\n *    1. The input graph is a DAG.\n *    2. Each node in the graph has a \"rank\" property.\n *\n * Post-condition:\n *\n *    1. All edges in the graph have a length of 1.\n *    2. Dummy nodes are added where edges have been split into segments.\n *    3. The graph is augmented with a \"dummyChains\" attribute which contains\n *       the first dummy in each chain of dummy nodes produced.\n */\nfunction run(g) {\n  g.graph().dummyChains = [];\n  g.edges().forEach(edge => normalizeEdge(g, edge));\n}\n\nfunction normalizeEdge(g, e) {\n  let v = e.v;\n  let vRank = g.node(v).rank;\n  let w = e.w;\n  let wRank = g.node(w).rank;\n  let name = e.name;\n  let edgeLabel = g.edge(e);\n  let labelRank = edgeLabel.labelRank;\n\n  if (wRank === vRank + 1) return;\n\n  g.removeEdge(e);\n\n  let dummy, attrs, i;\n  for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {\n    edgeLabel.points = [];\n    attrs = {\n      width: 0, height: 0,\n      edgeLabel: edgeLabel, edgeObj: e,\n      rank: vRank\n    };\n    dummy = util.addDummyNode(g, \"edge\", attrs, \"_d\");\n    if (vRank === labelRank) {\n      attrs.width = edgeLabel.width;\n      attrs.height = edgeLabel.height;\n      attrs.dummy = \"edge-label\";\n      attrs.labelpos = edgeLabel.labelpos;\n    }\n    g.setEdge(v, dummy, { weight: edgeLabel.weight }, name);\n    if (i === 0) {\n      g.graph().dummyChains.push(dummy);\n    }\n    v = dummy;\n  }\n\n  g.setEdge(v, w, { weight: edgeLabel.weight }, name);\n}\n\nfunction undo(g) {\n  g.graph().dummyChains.forEach(v => {\n    let node = g.node(v);\n    let origLabel = node.edgeLabel;\n    let w;\n    g.setEdge(node.edgeObj, origLabel);\n    while (node.dummy) {\n      w = g.successors(v)[0];\n      g.removeNode(v);\n      origLabel.points.push({ x: node.x, y: node.y });\n      if (node.dummy === \"edge-label\") {\n        origLabel.x = node.x;\n        origLabel.y = node.y;\n        origLabel.width = node.width;\n        origLabel.height = node.height;\n      }\n      v = w;\n      node = g.node(v);\n    }\n  });\n}\n", "\"use strict\";\n\nconst { applyWithChunking } = require(\"../util\");\n\nmodule.exports = {\n  longestPath: longestPath,\n  slack: slack\n};\n\n/*\n * Initializes ranks for the input graph using the longest path algorithm. This\n * algorithm scales well and is fast in practice, it yields rather poor\n * solutions. Nodes are pushed to the lowest layer possible, leaving the bottom\n * ranks wide and leaving edges longer than necessary. However, due to its\n * speed, this algorithm is good for getting an initial ranking that can be fed\n * into other algorithms.\n *\n * This algorithm does not normalize layers because it will be used by other\n * algorithms in most cases. If using this algorithm directly, be sure to\n * run normalize at the end.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG.\n *    2. Input graph node labels can be assigned properties.\n *\n * Post-conditions:\n *\n *    1. Each node will be assign an (unnormalized) \"rank\" property.\n */\nfunction longestPath(g) {\n  var visited = {};\n\n  function dfs(v) {\n    var label = g.node(v);\n    if (Object.hasOwn(visited, v)) {\n      return label.rank;\n    }\n    visited[v] = true;\n\n    let outEdgesMinLens = g.outEdges(v).map(e => {\n      if (e == null) {\n        return Number.POSITIVE_INFINITY;\n      }\n\n      return dfs(e.w) - g.edge(e).minlen;\n    });\n\n    var rank = applyWithChunking(Math.min, outEdgesMinLens);\n\n    if (rank === Number.POSITIVE_INFINITY) {\n      rank = 0;\n    }\n\n    return (label.rank = rank);\n  }\n\n  g.sources().forEach(dfs);\n}\n\n/*\n * Returns the amount of slack for the given edge. The slack is defined as the\n * difference between the length of the edge and its minimum length.\n */\nfunction slack(g, e) {\n  return g.node(e.w).rank - g.node(e.v).rank - g.edge(e).minlen;\n}\n", "\"use strict\";\n\nvar Graph = require(\"@dagrejs/graphlib\").Graph;\nvar slack = require(\"./util\").slack;\n\nmodule.exports = feasibleTree;\n\n/*\n * Constructs a spanning tree with tight edges and adjusted the input node's\n * ranks to achieve this. A tight edge is one that is has a length that matches\n * its \"minlen\" attribute.\n *\n * The basic structure for this function is derived from <PERSON><PERSON><PERSON>, et al., \"A\n * Technique for Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a DAG.\n *    2. Graph must be connected.\n *    3. Graph must have at least one node.\n *    5. Graph nodes must have been previously assigned a \"rank\" property that\n *       respects the \"minlen\" property of incident edges.\n *    6. Graph edges must have a \"minlen\" property.\n *\n * Post-conditions:\n *\n *    - Graph nodes will have their rank adjusted to ensure that all edges are\n *      tight.\n *\n * Returns a tree (undirected graph) that is constructed using only \"tight\"\n * edges.\n */\nfunction feasibleTree(g) {\n  var t = new Graph({ directed: false });\n\n  // Choose arbitrary node from which to start our tree\n  var start = g.nodes()[0];\n  var size = g.nodeCount();\n  t.setNode(start, {});\n\n  var edge, delta;\n  while (tightTree(t, g) < size) {\n    edge = findMinSlackEdge(t, g);\n    delta = t.hasNode(edge.v) ? slack(g, edge) : -slack(g, edge);\n    shiftRanks(t, g, delta);\n  }\n\n  return t;\n}\n\n/*\n * Finds a maximal tree of tight edges and returns the number of nodes in the\n * tree.\n */\nfunction tightTree(t, g) {\n  function dfs(v) {\n    g.nodeEdges(v).forEach(e => {\n      var edgeV = e.v,\n        w = (v === edgeV) ? e.w : edgeV;\n      if (!t.hasNode(w) && !slack(g, e)) {\n        t.setNode(w, {});\n        t.setEdge(v, w, {});\n        dfs(w);\n      }\n    });\n  }\n\n  t.nodes().forEach(dfs);\n  return t.nodeCount();\n}\n\n/*\n * Finds the edge with the smallest slack that is incident on tree and returns\n * it.\n */\nfunction findMinSlackEdge(t, g) {\n  const edges = g.edges();\n\n  return edges.reduce((acc, edge) => {\n    let edgeSlack = Number.POSITIVE_INFINITY;\n    if (t.hasNode(edge.v) !== t.hasNode(edge.w)) {\n      edgeSlack = slack(g, edge);\n    }\n\n    if (edgeSlack < acc[0]) {\n      return [edgeSlack, edge];\n    }\n\n    return acc;\n  }, [Number.POSITIVE_INFINITY, null])[1];\n}\n\nfunction shiftRanks(t, g, delta) {\n  t.nodes().forEach(v => g.node(v).rank += delta);\n}\n", "\"use strict\";\n\nvar feasibleTree = require(\"./feasible-tree\");\nvar slack = require(\"./util\").slack;\nvar initRank = require(\"./util\").longestPath;\nvar preorder = require(\"@dagrejs/graphlib\").alg.preorder;\nvar postorder = require(\"@dagrejs/graphlib\").alg.postorder;\nvar simplify = require(\"../util\").simplify;\n\nmodule.exports = networkSimplex;\n\n// Expose some internals for testing purposes\nnetworkSimplex.initLowLimValues = initLowLimValues;\nnetworkSimplex.initCutValues = initCutValues;\nnetworkSimplex.calcCutValue = calcCutValue;\nnetworkSimplex.leaveEdge = leaveEdge;\nnetworkSimplex.enterEdge = enterEdge;\nnetworkSimplex.exchangeEdges = exchangeEdges;\n\n/*\n * The network simplex algorithm assigns ranks to each node in the input graph\n * and iteratively improves the ranking to reduce the length of edges.\n *\n * Preconditions:\n *\n *    1. The input graph must be a DAG.\n *    2. All nodes in the graph must have an object value.\n *    3. All edges in the graph must have \"minlen\" and \"weight\" attributes.\n *\n * Postconditions:\n *\n *    1. All nodes in the graph will have an assigned \"rank\" attribute that has\n *       been optimized by the network simplex algorithm. Ranks start at 0.\n *\n *\n * A rough sketch of the algorithm is as follows:\n *\n *    1. Assign initial ranks to each node. We use the longest path algorithm,\n *       which assigns ranks to the lowest position possible. In general this\n *       leads to very wide bottom ranks and unnecessarily long edges.\n *    2. Construct a feasible tight tree. A tight tree is one such that all\n *       edges in the tree have no slack (difference between length of edge\n *       and minlen for the edge). This by itself greatly improves the assigned\n *       rankings by shorting edges.\n *    3. Iteratively find edges that have negative cut values. Generally a\n *       negative cut value indicates that the edge could be removed and a new\n *       tree edge could be added to produce a more compact graph.\n *\n * Much of the algorithms here are derived from Gansner, et al., \"A Technique\n * for Drawing Directed Graphs.\" The structure of the file roughly follows the\n * structure of the overall algorithm.\n */\nfunction networkSimplex(g) {\n  g = simplify(g);\n  initRank(g);\n  var t = feasibleTree(g);\n  initLowLimValues(t);\n  initCutValues(t, g);\n\n  var e, f;\n  while ((e = leaveEdge(t))) {\n    f = enterEdge(t, g, e);\n    exchangeEdges(t, g, e, f);\n  }\n}\n\n/*\n * Initializes cut values for all edges in the tree.\n */\nfunction initCutValues(t, g) {\n  var vs = postorder(t, t.nodes());\n  vs = vs.slice(0, vs.length - 1);\n  vs.forEach(v => assignCutValue(t, g, v));\n}\n\nfunction assignCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  t.edge(child, parent).cutvalue = calcCutValue(t, g, child);\n}\n\n/*\n * Given the tight tree, its graph, and a child in the graph calculate and\n * return the cut value for the edge between the child and its parent.\n */\nfunction calcCutValue(t, g, child) {\n  var childLab = t.node(child);\n  var parent = childLab.parent;\n  // True if the child is on the tail end of the edge in the directed graph\n  var childIsTail = true;\n  // The graph's view of the tree edge we're inspecting\n  var graphEdge = g.edge(child, parent);\n  // The accumulated cut value for the edge between this node and its parent\n  var cutValue = 0;\n\n  if (!graphEdge) {\n    childIsTail = false;\n    graphEdge = g.edge(parent, child);\n  }\n\n  cutValue = graphEdge.weight;\n\n  g.nodeEdges(child).forEach(e => {\n    var isOutEdge = e.v === child,\n      other = isOutEdge ? e.w : e.v;\n\n    if (other !== parent) {\n      var pointsToHead = isOutEdge === childIsTail,\n        otherWeight = g.edge(e).weight;\n\n      cutValue += pointsToHead ? otherWeight : -otherWeight;\n      if (isTreeEdge(t, child, other)) {\n        var otherCutValue = t.edge(child, other).cutvalue;\n        cutValue += pointsToHead ? -otherCutValue : otherCutValue;\n      }\n    }\n  });\n\n  return cutValue;\n}\n\nfunction initLowLimValues(tree, root) {\n  if (arguments.length < 2) {\n    root = tree.nodes()[0];\n  }\n  dfsAssignLowLim(tree, {}, 1, root);\n}\n\nfunction dfsAssignLowLim(tree, visited, nextLim, v, parent) {\n  var low = nextLim;\n  var label = tree.node(v);\n\n  visited[v] = true;\n  tree.neighbors(v).forEach(w => {\n    if (!Object.hasOwn(visited, w)) {\n      nextLim = dfsAssignLowLim(tree, visited, nextLim, w, v);\n    }\n  });\n\n  label.low = low;\n  label.lim = nextLim++;\n  if (parent) {\n    label.parent = parent;\n  } else {\n    // TODO should be able to remove this when we incrementally update low lim\n    delete label.parent;\n  }\n\n  return nextLim;\n}\n\nfunction leaveEdge(tree) {\n  return tree.edges().find(e => tree.edge(e).cutvalue < 0);\n}\n\nfunction enterEdge(t, g, edge) {\n  var v = edge.v;\n  var w = edge.w;\n\n  // For the rest of this function we assume that v is the tail and w is the\n  // head, so if we don't have this edge in the graph we should flip it to\n  // match the correct orientation.\n  if (!g.hasEdge(v, w)) {\n    v = edge.w;\n    w = edge.v;\n  }\n\n  var vLabel = t.node(v);\n  var wLabel = t.node(w);\n  var tailLabel = vLabel;\n  var flip = false;\n\n  // If the root is in the tail of the edge then we need to flip the logic that\n  // checks for the head and tail nodes in the candidates function below.\n  if (vLabel.lim > wLabel.lim) {\n    tailLabel = wLabel;\n    flip = true;\n  }\n\n  var candidates = g.edges().filter(edge => {\n    return flip === isDescendant(t, t.node(edge.v), tailLabel) &&\n           flip !== isDescendant(t, t.node(edge.w), tailLabel);\n  });\n\n  return candidates.reduce((acc, edge) => {\n    if (slack(g, edge) < slack(g, acc)) {\n      return edge;\n    }\n\n    return acc;\n  });\n}\n\nfunction exchangeEdges(t, g, e, f) {\n  var v = e.v;\n  var w = e.w;\n  t.removeEdge(v, w);\n  t.setEdge(f.v, f.w, {});\n  initLowLimValues(t);\n  initCutValues(t, g);\n  updateRanks(t, g);\n}\n\nfunction updateRanks(t, g) {\n  var root = t.nodes().find(v => !g.node(v).parent);\n  var vs = preorder(t, root);\n  vs = vs.slice(1);\n  vs.forEach(v => {\n    var parent = t.node(v).parent,\n      edge = g.edge(v, parent),\n      flipped = false;\n\n    if (!edge) {\n      edge = g.edge(parent, v);\n      flipped = true;\n    }\n\n    g.node(v).rank = g.node(parent).rank + (flipped ? edge.minlen : -edge.minlen);\n  });\n}\n\n/*\n * Returns true if the edge is in the tree.\n */\nfunction isTreeEdge(tree, u, v) {\n  return tree.hasEdge(u, v);\n}\n\n/*\n * Returns true if the specified node is descendant of the root node per the\n * assigned low and lim attributes in the tree.\n */\nfunction isDescendant(tree, vLabel, rootLabel) {\n  return rootLabel.low <= vLabel.lim && vLabel.lim <= rootLabel.lim;\n}\n", "\"use strict\";\n\nvar rankUtil = require(\"./util\");\nvar longestPath = rankUtil.longestPath;\nvar feasibleTree = require(\"./feasible-tree\");\nvar networkSimplex = require(\"./network-simplex\");\n\nmodule.exports = rank;\n\n/*\n * Assigns a rank to each node in the input graph that respects the \"minlen\"\n * constraint specified on edges between nodes.\n *\n * This basic structure is derived from <PERSON><PERSON><PERSON>, et al., \"A Technique for\n * Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a connected DAG\n *    2. Graph nodes must be objects\n *    3. Graph edges must have \"weight\" and \"minlen\" attributes\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have a \"rank\" attribute based on the results of the\n *       algorithm. Ranks can start at any index (including negative), we'll\n *       fix them up later.\n */\nfunction rank(g) {\n  switch(g.graph().ranker) {\n  case \"network-simplex\": networkSimplexRanker(g); break;\n  case \"tight-tree\": tightTreeRanker(g); break;\n  case \"longest-path\": longestPathRanker(g); break;\n  default: networkSimplexRanker(g);\n  }\n}\n\n// A fast and simple ranker, but results are far from optimal.\nvar longestPathRanker = longestPath;\n\nfunction tightTreeRanker(g) {\n  longestPath(g);\n  feasibleTree(g);\n}\n\nfunction networkSimplexRanker(g) {\n  networkSimplex(g);\n}\n", "module.exports = parentDummyChains;\n\nfunction parentDummyChains(g) {\n  let postorderNums = postorder(g);\n\n  g.graph().dummyChains.forEach(v => {\n    let node = g.node(v);\n    let edgeObj = node.edgeObj;\n    let pathData = findPath(g, postorderNums, edgeObj.v, edgeObj.w);\n    let path = pathData.path;\n    let lca = pathData.lca;\n    let pathIdx = 0;\n    let pathV = path[pathIdx];\n    let ascending = true;\n\n    while (v !== edgeObj.w) {\n      node = g.node(v);\n\n      if (ascending) {\n        while ((pathV = path[pathIdx]) !== lca &&\n               g.node(pathV).maxRank < node.rank) {\n          pathIdx++;\n        }\n\n        if (pathV === lca) {\n          ascending = false;\n        }\n      }\n\n      if (!ascending) {\n        while (pathIdx < path.length - 1 &&\n               g.node(pathV = path[pathIdx + 1]).minRank <= node.rank) {\n          pathIdx++;\n        }\n        pathV = path[pathIdx];\n      }\n\n      g.setParent(v, pathV);\n      v = g.successors(v)[0];\n    }\n  });\n}\n\n// Find a path from v to w through the lowest common ancestor (LCA). Return the\n// full path and the LCA.\nfunction findPath(g, postorderNums, v, w) {\n  let vPath = [];\n  let wPath = [];\n  let low = Math.min(postorderNums[v].low, postorderNums[w].low);\n  let lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);\n  let parent;\n  let lca;\n\n  // Traverse up from v to find the LCA\n  parent = v;\n  do {\n    parent = g.parent(parent);\n    vPath.push(parent);\n  } while (parent &&\n           (postorderNums[parent].low > low || lim > postorderNums[parent].lim));\n  lca = parent;\n\n  // Traverse from w to LCA\n  parent = w;\n  while ((parent = g.parent(parent)) !== lca) {\n    wPath.push(parent);\n  }\n\n  return { path: vPath.concat(wPath.reverse()), lca: lca };\n}\n\nfunction postorder(g) {\n  let result = {};\n  let lim = 0;\n\n  function dfs(v) {\n    let low = lim;\n    g.children(v).forEach(dfs);\n    result[v] = { low: low, lim: lim++ };\n  }\n  g.children().forEach(dfs);\n\n  return result;\n}\n", "let util = require(\"./util\");\n\nmodule.exports = {\n  run,\n  cleanup,\n};\n\n/*\n * A nesting graph creates dummy nodes for the tops and bottoms of subgraphs,\n * adds appropriate edges to ensure that all cluster nodes are placed between\n * these boundaries, and ensures that the graph is connected.\n *\n * In addition we ensure, through the use of the minlen property, that nodes\n * and subgraph border nodes to not end up on the same rank.\n *\n * Preconditions:\n *\n *    1. Input graph is a DAG\n *    2. Nodes in the input graph has a minlen attribute\n *\n * Postconditions:\n *\n *    1. Input graph is connected.\n *    2. Dummy nodes are added for the tops and bottoms of subgraphs.\n *    3. The minlen attribute for nodes is adjusted to ensure nodes do not\n *       get placed on the same rank as subgraph border nodes.\n *\n * The nesting graph idea comes from <PERSON><PERSON>, \"Layout of Compound Directed\n * Graphs.\"\n */\nfunction run(g) {\n  let root = util.addDummyNode(g, \"root\", {}, \"_root\");\n  let depths = treeDepths(g);\n  let depthsArr = Object.values(depths);\n  let height = util.applyWithChunking(Math.max, depthsArr) - 1; // Note: depths is an Object not an array\n  let nodeSep = 2 * height + 1;\n\n  g.graph().nestingRoot = root;\n\n  // Multiply minlen by nodeSep to align nodes on non-border ranks.\n  g.edges().forEach(e => g.edge(e).minlen *= nodeSep);\n\n  // Calculate a weight that is sufficient to keep subgraphs vertically compact\n  let weight = sumWeights(g) + 1;\n\n  // Create border nodes and link them up\n  g.children().forEach(child => dfs(g, root, nodeSep, weight, height, depths, child));\n\n  // Save the multiplier for node layers for later removal of empty border\n  // layers.\n  g.graph().nodeRankFactor = nodeSep;\n}\n\nfunction dfs(g, root, nodeSep, weight, height, depths, v) {\n  let children = g.children(v);\n  if (!children.length) {\n    if (v !== root) {\n      g.setEdge(root, v, { weight: 0, minlen: nodeSep });\n    }\n    return;\n  }\n\n  let top = util.addBorderNode(g, \"_bt\");\n  let bottom = util.addBorderNode(g, \"_bb\");\n  let label = g.node(v);\n\n  g.setParent(top, v);\n  label.borderTop = top;\n  g.setParent(bottom, v);\n  label.borderBottom = bottom;\n\n  children.forEach(child => {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n\n    let childNode = g.node(child);\n    let childTop = childNode.borderTop ? childNode.borderTop : child;\n    let childBottom = childNode.borderBottom ? childNode.borderBottom : child;\n    let thisWeight = childNode.borderTop ? weight : 2 * weight;\n    let minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;\n\n    g.setEdge(top, childTop, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true\n    });\n\n    g.setEdge(childBottom, bottom, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true\n    });\n  });\n\n  if (!g.parent(v)) {\n    g.setEdge(root, top, { weight: 0, minlen: height + depths[v] });\n  }\n}\n\nfunction treeDepths(g) {\n  var depths = {};\n  function dfs(v, depth) {\n    var children = g.children(v);\n    if (children && children.length) {\n      children.forEach(child => dfs(child, depth + 1));\n    }\n    depths[v] = depth;\n  }\n  g.children().forEach(v => dfs(v, 1));\n  return depths;\n}\n\nfunction sumWeights(g) {\n  return g.edges().reduce((acc, e) => acc + g.edge(e).weight, 0);\n}\n\nfunction cleanup(g) {\n  var graphLabel = g.graph();\n  g.removeNode(graphLabel.nestingRoot);\n  delete graphLabel.nestingRoot;\n  g.edges().forEach(e => {\n    var edge = g.edge(e);\n    if (edge.nestingEdge) {\n      g.removeEdge(e);\n    }\n  });\n}\n", "let util = require(\"./util\");\n\nmodule.exports = addBorderSegments;\n\nfunction addBorderSegments(g) {\n  function dfs(v) {\n    let children = g.children(v);\n    let node = g.node(v);\n    if (children.length) {\n      children.forEach(dfs);\n    }\n\n    if (Object.hasOwn(node, \"minRank\")) {\n      node.borderLeft = [];\n      node.borderRight = [];\n      for (let rank = node.minRank, maxRank = node.maxRank + 1;\n        rank < maxRank;\n        ++rank) {\n        addBorderNode(g, \"borderLeft\", \"_bl\", v, node, rank);\n        addBorderNode(g, \"borderRight\", \"_br\", v, node, rank);\n      }\n    }\n  }\n\n  g.children().forEach(dfs);\n}\n\nfunction addBorderNode(g, prop, prefix, sg, sgNode, rank) {\n  let label = { width: 0, height: 0, rank: rank, borderType: prop };\n  let prev = sgNode[prop][rank - 1];\n  let curr = util.addDummyNode(g, \"border\", label, prefix);\n  sgNode[prop][rank] = curr;\n  g.setParent(curr, sg);\n  if (prev) {\n    g.setEdge(prev, curr, { weight: 1 });\n  }\n}\n", "\"use strict\";\n\nmodule.exports = {\n  adjust: adjust,\n  undo: undo\n};\n\nfunction adjust(g) {\n  let rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === \"lr\" || rankDir === \"rl\") {\n    swapWidthHeight(g);\n  }\n}\n\nfunction undo(g) {\n  let rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === \"bt\" || rankDir === \"rl\") {\n    reverseY(g);\n  }\n\n  if (rankDir === \"lr\" || rankDir === \"rl\") {\n    swapXY(g);\n    swapWidthHeight(g);\n  }\n}\n\nfunction swapWidthHeight(g) {\n  g.nodes().forEach(v => swapWidthHeightOne(g.node(v)));\n  g.edges().forEach(e => swapWidthHeightOne(g.edge(e)));\n}\n\nfunction swapWidthHeightOne(attrs) {\n  let w = attrs.width;\n  attrs.width = attrs.height;\n  attrs.height = w;\n}\n\nfunction reverseY(g) {\n  g.nodes().forEach(v => reverseYOne(g.node(v)));\n\n  g.edges().forEach(e => {\n    let edge = g.edge(e);\n    edge.points.forEach(reverseYOne);\n    if (Object.hasOwn(edge, \"y\")) {\n      reverseYOne(edge);\n    }\n  });\n}\n\nfunction reverseYOne(attrs) {\n  attrs.y = -attrs.y;\n}\n\nfunction swapXY(g) {\n  g.nodes().forEach(v => swapXYOne(g.node(v)));\n\n  g.edges().forEach(e => {\n    let edge = g.edge(e);\n    edge.points.forEach(swapXYOne);\n    if (Object.hasOwn(edge, \"x\")) {\n      swapXYOne(edge);\n    }\n  });\n}\n\nfunction swapXYOne(attrs) {\n  let x = attrs.x;\n  attrs.x = attrs.y;\n  attrs.y = x;\n}\n", "\"use strict\";\n\nlet util = require(\"../util\");\n\nmodule.exports = initOrder;\n\n/*\n * Assigns an initial order value for each node by performing a DFS search\n * starting from nodes in the first rank. Nodes are assigned an order in their\n * rank as they are first visited.\n *\n * This approach comes from <PERSON><PERSON><PERSON>, et al., \"A Technique for Drawing Directed\n * Graphs.\"\n *\n * Returns a layering matrix with an array per layer and each layer sorted by\n * the order of its nodes.\n */\nfunction initOrder(g) {\n  let visited = {};\n  let simpleNodes = g.nodes().filter(v => !g.children(v).length);\n  let simpleNodesRanks = simpleNodes.map(v => g.node(v).rank);\n  let maxRank = util.applyWithChunking(Math.max, simpleNodesRanks);\n  let layers = util.range(maxRank + 1).map(() => []);\n\n  function dfs(v) {\n    if (visited[v]) return;\n    visited[v] = true;\n    let node = g.node(v);\n    layers[node.rank].push(v);\n    g.successors(v).forEach(dfs);\n  }\n\n  let orderedVs = simpleNodes.sort((a, b) => g.node(a).rank - g.node(b).rank);\n  orderedVs.forEach(dfs);\n\n  return layers;\n}\n", "\"use strict\";\n\nlet zipObject = require(\"../util\").zipObject;\n\nmodule.exports = crossCount;\n\n/*\n * A function that takes a layering (an array of layers, each with an array of\n * ordererd nodes) and a graph and returns a weighted crossing count.\n *\n * Pre-conditions:\n *\n *    1. Input graph must be simple (not a multigraph), directed, and include\n *       only simple edges.\n *    2. Edges in the input graph must have assigned weights.\n *\n * Post-conditions:\n *\n *    1. The graph and layering matrix are left unchanged.\n *\n * This algorithm is derived from <PERSON><PERSON>, et al., \"Bilayer Cross Counting.\"\n */\nfunction crossCount(g, layering) {\n  let cc = 0;\n  for (let i = 1; i < layering.length; ++i) {\n    cc += twoLayerCrossCount(g, layering[i-1], layering[i]);\n  }\n  return cc;\n}\n\nfunction twoLayerCrossCount(g, northLayer, southLayer) {\n  // Sort all of the edges between the north and south layers by their position\n  // in the north layer and then the south. Map these edges to the position of\n  // their head in the south layer.\n  let southPos = zipObject(southLayer, southLayer.map((v, i) => i));\n  let southEntries = northLayer.flatMap(v => {\n    return g.outEdges(v).map(e => {\n      return { pos: southPos[e.w], weight: g.edge(e).weight };\n    }).sort((a, b) => a.pos - b.pos);\n  });\n\n  // Build the accumulator tree\n  let firstIndex = 1;\n  while (firstIndex < southLayer.length) firstIndex <<= 1;\n  let treeSize = 2 * firstIndex - 1;\n  firstIndex -= 1;\n  let tree = new Array(treeSize).fill(0);\n\n  // Calculate the weighted crossings\n  let cc = 0;\n  southEntries.forEach(entry => {\n    let index = entry.pos + firstIndex;\n    tree[index] += entry.weight;\n    let weightSum = 0;\n    while (index > 0) {\n      if (index % 2) {\n        weightSum += tree[index + 1];\n      }\n      index = (index - 1) >> 1;\n      tree[index] += entry.weight;\n    }\n    cc += entry.weight * weightSum;\n  });\n\n  return cc;\n}\n", "module.exports = barycenter;\n\nfunction barycenter(g, movable = []) {\n  return movable.map(v => {\n    let inV = g.inEdges(v);\n    if (!inV.length) {\n      return { v: v };\n    } else {\n      let result = inV.reduce((acc, e) => {\n        let edge = g.edge(e),\n          nodeU = g.node(e.v);\n        return {\n          sum: acc.sum + (edge.weight * nodeU.order),\n          weight: acc.weight + edge.weight\n        };\n      }, { sum: 0, weight: 0 });\n\n      return {\n        v: v,\n        barycenter: result.sum / result.weight,\n        weight: result.weight\n      };\n    }\n  });\n}\n\n", "\"use strict\";\n\nlet util = require(\"../util\");\n\nmodule.exports = resolveConflicts;\n\n/*\n * Given a list of entries of the form {v, barycenter, weight} and a\n * constraint graph this function will resolve any conflicts between the\n * constraint graph and the barycenters for the entries. If the barycenters for\n * an entry would violate a constraint in the constraint graph then we coalesce\n * the nodes in the conflict into a new node that respects the contraint and\n * aggregates barycenter and weight information.\n *\n * This implementation is based on the description in Forster, \"A Fast and\n * Simple Hueristic for Constrained Two-Level Crossing Reduction,\" thought it\n * differs in some specific details.\n *\n * Pre-conditions:\n *\n *    1. Each entry has the form {v, barycenter, weight}, or if the node has\n *       no barycenter, then {v}.\n *\n * Returns:\n *\n *    A new list of entries of the form {vs, i, barycenter, weight}. The list\n *    `vs` may either be a singleton or it may be an aggregation of nodes\n *    ordered such that they do not violate constraints from the constraint\n *    graph. The property `i` is the lowest original index of any of the\n *    elements in `vs`.\n */\nfunction resolveConflicts(entries, cg) {\n  let mappedEntries = {};\n  entries.forEach((entry, i) => {\n    let tmp = mappedEntries[entry.v] = {\n      indegree: 0,\n      \"in\": [],\n      out: [],\n      vs: [entry.v],\n      i: i\n    };\n    if (entry.barycenter !== undefined) {\n      tmp.barycenter = entry.barycenter;\n      tmp.weight = entry.weight;\n    }\n  });\n\n  cg.edges().forEach(e => {\n    let entryV = mappedEntries[e.v];\n    let entryW = mappedEntries[e.w];\n    if (entryV !== undefined && entryW !== undefined) {\n      entryW.indegree++;\n      entryV.out.push(mappedEntries[e.w]);\n    }\n  });\n\n  let sourceSet = Object.values(mappedEntries).filter(entry => !entry.indegree);\n\n  return doResolveConflicts(sourceSet);\n}\n\nfunction doResolveConflicts(sourceSet) {\n  let entries = [];\n\n  function handleIn(vEntry) {\n    return uEntry => {\n      if (uEntry.merged) {\n        return;\n      }\n      if (uEntry.barycenter === undefined ||\n          vEntry.barycenter === undefined ||\n          uEntry.barycenter >= vEntry.barycenter) {\n        mergeEntries(vEntry, uEntry);\n      }\n    };\n  }\n\n  function handleOut(vEntry) {\n    return wEntry => {\n      wEntry[\"in\"].push(vEntry);\n      if (--wEntry.indegree === 0) {\n        sourceSet.push(wEntry);\n      }\n    };\n  }\n\n  while (sourceSet.length) {\n    let entry = sourceSet.pop();\n    entries.push(entry);\n    entry[\"in\"].reverse().forEach(handleIn(entry));\n    entry.out.forEach(handleOut(entry));\n  }\n\n  return entries.filter(entry => !entry.merged).map(entry => {\n    return util.pick(entry, [\"vs\", \"i\", \"barycenter\", \"weight\"]);\n  });\n}\n\nfunction mergeEntries(target, source) {\n  let sum = 0;\n  let weight = 0;\n\n  if (target.weight) {\n    sum += target.barycenter * target.weight;\n    weight += target.weight;\n  }\n\n  if (source.weight) {\n    sum += source.barycenter * source.weight;\n    weight += source.weight;\n  }\n\n  target.vs = source.vs.concat(target.vs);\n  target.barycenter = sum / weight;\n  target.weight = weight;\n  target.i = Math.min(source.i, target.i);\n  source.merged = true;\n}\n", "let util = require(\"../util\");\n\nmodule.exports = sort;\n\nfunction sort(entries, biasRight) {\n  let parts = util.partition(entries, entry => {\n    return Object.hasOwn(entry, \"barycenter\");\n  });\n  let sortable = parts.lhs,\n    unsortable = parts.rhs.sort((a, b) => b.i - a.i),\n    vs = [],\n    sum = 0,\n    weight = 0,\n    vsIndex = 0;\n\n  sortable.sort(compareWithBias(!!biasRight));\n\n  vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n\n  sortable.forEach(entry => {\n    vsIndex += entry.vs.length;\n    vs.push(entry.vs);\n    sum += entry.barycenter * entry.weight;\n    weight += entry.weight;\n    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  });\n\n  let result = { vs: vs.flat(true) };\n  if (weight) {\n    result.barycenter = sum / weight;\n    result.weight = weight;\n  }\n  return result;\n}\n\nfunction consumeUnsortable(vs, unsortable, index) {\n  let last;\n  while (unsortable.length && (last = unsortable[unsortable.length - 1]).i <= index) {\n    unsortable.pop();\n    vs.push(last.vs);\n    index++;\n  }\n  return index;\n}\n\nfunction compareWithBias(bias) {\n  return (entryV, entryW) => {\n    if (entryV.barycenter < entryW.barycenter) {\n      return -1;\n    } else if (entryV.barycenter > entryW.barycenter) {\n      return 1;\n    }\n\n    return !bias ? entryV.i - entryW.i : entryW.i - entryV.i;\n  };\n}\n", "let barycenter = require(\"./barycenter\");\nlet resolveConflicts = require(\"./resolve-conflicts\");\nlet sort = require(\"./sort\");\n\nmodule.exports = sortSubgraph;\n\nfunction sortSubgraph(g, v, cg, biasRight) {\n  let movable = g.children(v);\n  let node = g.node(v);\n  let bl = node ? node.borderLeft : undefined;\n  let br = node ? node.borderRight: undefined;\n  let subgraphs = {};\n\n  if (bl) {\n    movable = movable.filter(w => w !== bl && w !== br);\n  }\n\n  let barycenters = barycenter(g, movable);\n  barycenters.forEach(entry => {\n    if (g.children(entry.v).length) {\n      let subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);\n      subgraphs[entry.v] = subgraphResult;\n      if (Object.hasOwn(subgraphResult, \"barycenter\")) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n\n  let entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n\n  let result = sort(entries, biasRight);\n\n  if (bl) {\n    result.vs = [bl, result.vs, br].flat(true);\n    if (g.predecessors(bl).length) {\n      let blPred = g.node(g.predecessors(bl)[0]),\n        brPred = g.node(g.predecessors(br)[0]);\n      if (!Object.hasOwn(result, \"barycenter\")) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter = (result.barycenter * result.weight +\n                           blPred.order + brPred.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n\n  return result;\n}\n\nfunction expandSubgraphs(entries, subgraphs) {\n  entries.forEach(entry => {\n    entry.vs = entry.vs.flatMap(v => {\n      if (subgraphs[v]) {\n        return subgraphs[v].vs;\n      }\n      return v;\n    });\n  });\n}\n\nfunction mergeBarycenters(target, other) {\n  if (target.barycenter !== undefined) {\n    target.barycenter = (target.barycenter * target.weight +\n                         other.barycenter * other.weight) /\n                        (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n}\n", "let Graph = require(\"@dagrejs/graphlib\").Graph;\nlet util = require(\"../util\");\n\nmodule.exports = buildLayerGraph;\n\n/*\n * Constructs a graph that can be used to sort a layer of nodes. The graph will\n * contain all base and subgraph nodes from the request layer in their original\n * hierarchy and any edges that are incident on these nodes and are of the type\n * requested by the \"relationship\" parameter.\n *\n * Nodes from the requested rank that do not have parents are assigned a root\n * node in the output graph, which is set in the root graph attribute. This\n * makes it easy to walk the hierarchy of movable nodes during ordering.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG\n *    2. Base nodes in the input graph have a rank attribute\n *    3. Subgraph nodes in the input graph has minRank and maxRank attributes\n *    4. Edges have an assigned weight\n *\n * Post-conditions:\n *\n *    1. Output graph has all nodes in the movable rank with preserved\n *       hierarchy.\n *    2. Root nodes in the movable layer are made children of the node\n *       indicated by the root attribute of the graph.\n *    3. Non-movable nodes incident on movable nodes, selected by the\n *       relationship parameter, are included in the graph (without hierarchy).\n *    4. Edges incident on movable nodes, selected by the relationship\n *       parameter, are added to the output graph.\n *    5. The weights for copied edges are aggregated as need, since the output\n *       graph is not a multi-graph.\n */\nfunction buildLayerGraph(g, rank, relationship) {\n  let root = createRootNode(g),\n    result = new Graph({ compound: true }).setGraph({ root: root })\n      .setDefaultNodeLabel(v => g.node(v));\n\n  g.nodes().forEach(v => {\n    let node = g.node(v),\n      parent = g.parent(v);\n\n    if (node.rank === rank || node.minRank <= rank && rank <= node.maxRank) {\n      result.setNode(v);\n      result.setParent(v, parent || root);\n\n      // This assumes we have only short edges!\n      g[relationship](v).forEach(e => {\n        let u = e.v === v ? e.w : e.v,\n          edge = result.edge(u, v),\n          weight = edge !== undefined ? edge.weight : 0;\n        result.setEdge(u, v, { weight: g.edge(e).weight + weight });\n      });\n\n      if (Object.hasOwn(node, \"minRank\")) {\n        result.setNode(v, {\n          borderLeft: node.borderLeft[rank],\n          borderRight: node.borderRight[rank]\n        });\n      }\n    }\n  });\n\n  return result;\n}\n\nfunction createRootNode(g) {\n  var v;\n  while (g.hasNode((v = util.uniqueId(\"_root\"))));\n  return v;\n}\n", "module.exports = addSubgraphConstraints;\n\nfunction addSubgraphConstraints(g, cg, vs) {\n  let prev = {},\n    rootPrev;\n\n  vs.forEach(v => {\n    let child = g.parent(v),\n      parent,\n      prevChild;\n    while (child) {\n      parent = g.parent(child);\n      if (parent) {\n        prevChild = prev[parent];\n        prev[parent] = child;\n      } else {\n        prevChild = rootPrev;\n        rootPrev = child;\n      }\n      if (prevChild && prevChild !== child) {\n        cg.setEdge(prevChild, child);\n        return;\n      }\n      child = parent;\n    }\n  });\n\n  /*\n  function dfs(v) {\n    var children = v ? g.children(v) : g.children();\n    if (children.length) {\n      var min = Number.POSITIVE_INFINITY,\n          subgraphs = [];\n      children.forEach(function(child) {\n        var childMin = dfs(child);\n        if (g.children(child).length) {\n          subgraphs.push({ v: child, order: childMin });\n        }\n        min = Math.min(min, childMin);\n      });\n      _.sortBy(subgraphs, \"order\").reduce(function(prev, curr) {\n        cg.setEdge(prev.v, curr.v);\n        return curr;\n      });\n      return min;\n    }\n    return g.node(v).order;\n  }\n  dfs(undefined);\n  */\n}\n", "\"use strict\";\n\nlet initOrder = require(\"./init-order\");\nlet crossCount = require(\"./cross-count\");\nlet sortSubgraph = require(\"./sort-subgraph\");\nlet buildLayerGraph = require(\"./build-layer-graph\");\nlet addSubgraphConstraints = require(\"./add-subgraph-constraints\");\nlet Graph = require(\"@dagrejs/graphlib\").Graph;\nlet util = require(\"../util\");\n\nmodule.exports = order;\n\n/*\n * Applies heuristics to minimize edge crossings in the graph and sets the best\n * order solution as an order attribute on each node.\n *\n * Pre-conditions:\n *\n *    1. Graph must be DAG\n *    2. Graph nodes must be objects with a \"rank\" attribute\n *    3. Graph edges must have the \"weight\" attribute\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have an \"order\" attribute based on the results of the\n *       algorithm.\n */\nfunction order(g, opts) {\n  if (opts && typeof opts.customOrder === 'function') {\n    opts.customOrder(g, order);\n    return;\n  }\n\n  let maxRank = util.maxRank(g),\n    downLayerGraphs = buildLayerGraphs(g, util.range(1, maxRank + 1), \"inEdges\"),\n    upLayerGraphs = buildLayerGraphs(g, util.range(maxRank - 1, -1, -1), \"outEdges\");\n\n  let layering = initOrder(g);\n  assignOrder(g, layering);\n\n  if (opts && opts.disableOptimalOrderHeuristic) {\n    return;\n  }\n\n  let bestCC = Number.POSITIVE_INFINITY,\n    best;\n\n  for (let i = 0, lastBest = 0; lastBest < 4; ++i, ++lastBest) {\n    sweepLayerGraphs(i % 2 ? downLayerGraphs : upLayerGraphs, i % 4 >= 2);\n\n    layering = util.buildLayerMatrix(g);\n    let cc = crossCount(g, layering);\n    if (cc < bestCC) {\n      lastBest = 0;\n      best = Object.assign({}, layering);\n      bestCC = cc;\n    }\n  }\n\n  assignOrder(g, best);\n}\n\nfunction buildLayerGraphs(g, ranks, relationship) {\n  return ranks.map(function(rank) {\n    return buildLayerGraph(g, rank, relationship);\n  });\n}\n\nfunction sweepLayerGraphs(layerGraphs, biasRight) {\n  let cg = new Graph();\n  layerGraphs.forEach(function(lg) {\n    let root = lg.graph().root;\n    let sorted = sortSubgraph(lg, root, cg, biasRight);\n    sorted.vs.forEach((v, i) => lg.node(v).order = i);\n    addSubgraphConstraints(lg, cg, sorted.vs);\n  });\n}\n\nfunction assignOrder(g, layering) {\n  Object.values(layering).forEach(layer => layer.forEach((v, i) => g.node(v).order = i));\n}\n", "\"use strict\";\n\nlet Graph = require(\"@dagrejs/graphlib\").Graph;\nlet util = require(\"../util\");\n\n/*\n * This module provides coordinate assignment based on <PERSON><PERSON> and <PERSON>, \"Fast\n * and Simple Horizontal Coordinate Assignment.\"\n */\n\nmodule.exports = {\n  positionX: positionX,\n  findType1Conflicts: findType1Conflicts,\n  findType2Conflicts: findType2Conflicts,\n  addConflict: addConflict,\n  hasConflict: hasConflict,\n  verticalAlignment: verticalAlignment,\n  horizontalCompaction: horizontalCompaction,\n  alignCoordinates: alignCoordinates,\n  findSmallestWidthAlignment: findSmallestWidthAlignment,\n  balance: balance\n};\n\n/*\n * Marks all edges in the graph with a type-1 conflict with the \"type1Conflict\"\n * property. A type-1 conflict is one where a non-inner segment crosses an\n * inner segment. An inner segment is an edge with both incident nodes marked\n * with the \"dummy\" property.\n *\n * This algorithm scans layer by layer, starting with the second, for type-1\n * conflicts between the current layer and the previous layer. For each layer\n * it scans the nodes from left to right until it reaches one that is incident\n * on an inner segment. It then scans predecessors to determine if they have\n * edges that cross that inner segment. At the end a final scan is done for all\n * nodes on the current rank to see if they cross the last visited inner\n * segment.\n *\n * This algorithm (safely) assumes that a dummy node will only be incident on a\n * single node in the layers being scanned.\n */\nfunction findType1Conflicts(g, layering) {\n  let conflicts = {};\n\n  function visitLayer(prevLayer, layer) {\n    let\n      // last visited node in the previous layer that is incident on an inner\n      // segment.\n      k0 = 0,\n      // Tracks the last node in this layer scanned for crossings with a type-1\n      // segment.\n      scanPos = 0,\n      prevLayerLength = prevLayer.length,\n      lastNode = layer[layer.length - 1];\n\n    layer.forEach((v, i) => {\n      let w = findOtherInnerSegmentNode(g, v),\n        k1 = w ? g.node(w).order : prevLayerLength;\n\n      if (w || v === lastNode) {\n        layer.slice(scanPos, i+1).forEach(scanNode => {\n          g.predecessors(scanNode).forEach(u => {\n            let uLabel = g.node(u),\n              uPos = uLabel.order;\n            if ((uPos < k0 || k1 < uPos) &&\n                !(uLabel.dummy && g.node(scanNode).dummy)) {\n              addConflict(conflicts, u, scanNode);\n            }\n          });\n        });\n        scanPos = i + 1;\n        k0 = k1;\n      }\n    });\n\n    return layer;\n  }\n\n  layering.length && layering.reduce(visitLayer);\n\n  return conflicts;\n}\n\nfunction findType2Conflicts(g, layering) {\n  let conflicts = {};\n\n  function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {\n    let v;\n    util.range(southPos, southEnd).forEach(i => {\n      v = south[i];\n      if (g.node(v).dummy) {\n        g.predecessors(v).forEach(u => {\n          let uNode = g.node(u);\n          if (uNode.dummy &&\n              (uNode.order < prevNorthBorder || uNode.order > nextNorthBorder)) {\n            addConflict(conflicts, u, v);\n          }\n        });\n      }\n    });\n  }\n\n\n  function visitLayer(north, south) {\n    let prevNorthPos = -1,\n      nextNorthPos,\n      southPos = 0;\n\n    south.forEach((v, southLookahead) => {\n      if (g.node(v).dummy === \"border\") {\n        let predecessors = g.predecessors(v);\n        if (predecessors.length) {\n          nextNorthPos = g.node(predecessors[0]).order;\n          scan(south, southPos, southLookahead, prevNorthPos, nextNorthPos);\n          southPos = southLookahead;\n          prevNorthPos = nextNorthPos;\n        }\n      }\n      scan(south, southPos, south.length, nextNorthPos, north.length);\n    });\n\n    return south;\n  }\n\n  layering.length && layering.reduce(visitLayer);\n\n  return conflicts;\n}\n\nfunction findOtherInnerSegmentNode(g, v) {\n  if (g.node(v).dummy) {\n    return g.predecessors(v).find(u => g.node(u).dummy);\n  }\n}\n\nfunction addConflict(conflicts, v, w) {\n  if (v > w) {\n    let tmp = v;\n    v = w;\n    w = tmp;\n  }\n\n  let conflictsV = conflicts[v];\n  if (!conflictsV) {\n    conflicts[v] = conflictsV = {};\n  }\n  conflictsV[w] = true;\n}\n\nfunction hasConflict(conflicts, v, w) {\n  if (v > w) {\n    let tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return !!conflicts[v] && Object.hasOwn(conflicts[v], w);\n}\n\n/*\n * Try to align nodes into vertical \"blocks\" where possible. This algorithm\n * attempts to align a node with one of its median neighbors. If the edge\n * connecting a neighbor is a type-1 conflict then we ignore that possibility.\n * If a previous node has already formed a block with a node after the node\n * we're trying to form a block with, we also ignore that possibility - our\n * blocks would be split in that scenario.\n */\nfunction verticalAlignment(g, layering, conflicts, neighborFn) {\n  let root = {},\n    align = {},\n    pos = {};\n\n  // We cache the position here based on the layering because the graph and\n  // layering may be out of sync. The layering matrix is manipulated to\n  // generate different extreme alignments.\n  layering.forEach(layer => {\n    layer.forEach((v, order) => {\n      root[v] = v;\n      align[v] = v;\n      pos[v] = order;\n    });\n  });\n\n  layering.forEach(layer => {\n    let prevIdx = -1;\n    layer.forEach(v => {\n      let ws = neighborFn(v);\n      if (ws.length) {\n        ws = ws.sort((a, b) => pos[a] - pos[b]);\n        let mp = (ws.length - 1) / 2;\n        for (let i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {\n          let w = ws[i];\n          if (align[v] === v &&\n              prevIdx < pos[w] &&\n              !hasConflict(conflicts, v, w)) {\n            align[w] = v;\n            align[v] = root[v] = root[w];\n            prevIdx = pos[w];\n          }\n        }\n      }\n    });\n  });\n\n  return { root: root, align: align };\n}\n\nfunction horizontalCompaction(g, layering, root, align, reverseSep) {\n  // This portion of the algorithm differs from BK due to a number of problems.\n  // Instead of their algorithm we construct a new block graph and do two\n  // sweeps. The first sweep places blocks with the smallest possible\n  // coordinates. The second sweep removes unused space by moving blocks to the\n  // greatest coordinates without violating separation.\n  let xs = {},\n    blockG = buildBlockGraph(g, layering, root, reverseSep),\n    borderType = reverseSep ? \"borderLeft\" : \"borderRight\";\n\n  function iterate(setXsFunc, nextNodesFunc) {\n    let stack = blockG.nodes();\n    let elem = stack.pop();\n    let visited = {};\n    while (elem) {\n      if (visited[elem]) {\n        setXsFunc(elem);\n      } else {\n        visited[elem] = true;\n        stack.push(elem);\n        stack = stack.concat(nextNodesFunc(elem));\n      }\n\n      elem = stack.pop();\n    }\n  }\n\n  // First pass, assign smallest coordinates\n  function pass1(elem) {\n    xs[elem] = blockG.inEdges(elem).reduce((acc, e) => {\n      return Math.max(acc, xs[e.v] + blockG.edge(e));\n    }, 0);\n  }\n\n  // Second pass, assign greatest coordinates\n  function pass2(elem) {\n    let min = blockG.outEdges(elem).reduce((acc, e) => {\n      return Math.min(acc, xs[e.w] - blockG.edge(e));\n    }, Number.POSITIVE_INFINITY);\n\n    let node = g.node(elem);\n    if (min !== Number.POSITIVE_INFINITY && node.borderType !== borderType) {\n      xs[elem] = Math.max(xs[elem], min);\n    }\n  }\n\n  iterate(pass1, blockG.predecessors.bind(blockG));\n  iterate(pass2, blockG.successors.bind(blockG));\n\n  // Assign x coordinates to all nodes\n  Object.keys(align).forEach(v => xs[v] = xs[root[v]]);\n\n  return xs;\n}\n\n\nfunction buildBlockGraph(g, layering, root, reverseSep) {\n  let blockGraph = new Graph(),\n    graphLabel = g.graph(),\n    sepFn = sep(graphLabel.nodesep, graphLabel.edgesep, reverseSep);\n\n  layering.forEach(layer => {\n    let u;\n    layer.forEach(v => {\n      let vRoot = root[v];\n      blockGraph.setNode(vRoot);\n      if (u) {\n        var uRoot = root[u],\n          prevMax = blockGraph.edge(uRoot, vRoot);\n        blockGraph.setEdge(uRoot, vRoot, Math.max(sepFn(g, v, u), prevMax || 0));\n      }\n      u = v;\n    });\n  });\n\n  return blockGraph;\n}\n\n/*\n * Returns the alignment that has the smallest width of the given alignments.\n */\nfunction findSmallestWidthAlignment(g, xss) {\n  return Object.values(xss).reduce((currentMinAndXs, xs) => {\n    let max = Number.NEGATIVE_INFINITY;\n    let min = Number.POSITIVE_INFINITY;\n\n    Object.entries(xs).forEach(([v, x]) => {\n      let halfWidth = width(g, v) / 2;\n\n      max = Math.max(x + halfWidth, max);\n      min = Math.min(x - halfWidth, min);\n    });\n\n    const newMin = max - min;\n    if (newMin < currentMinAndXs[0]) {\n      currentMinAndXs = [newMin, xs];\n    }\n    return currentMinAndXs;\n  }, [Number.POSITIVE_INFINITY, null])[1];\n}\n\n/*\n * Align the coordinates of each of the layout alignments such that\n * left-biased alignments have their minimum coordinate at the same point as\n * the minimum coordinate of the smallest width alignment and right-biased\n * alignments have their maximum coordinate at the same point as the maximum\n * coordinate of the smallest width alignment.\n */\nfunction alignCoordinates(xss, alignTo) {\n  let alignToVals = Object.values(alignTo),\n    alignToMin = util.applyWithChunking(Math.min, alignToVals),\n    alignToMax = util.applyWithChunking(Math.max, alignToVals);\n\n  [\"u\", \"d\"].forEach(vert => {\n    [\"l\", \"r\"].forEach(horiz => {\n      let alignment = vert + horiz,\n        xs = xss[alignment];\n\n      if (xs === alignTo) return;\n\n      let xsVals = Object.values(xs);\n      let delta = alignToMin - util.applyWithChunking(Math.min, xsVals);\n      if (horiz !== \"l\") {\n        delta = alignToMax - util.applyWithChunking(Math.max,xsVals);\n      }\n\n      if (delta) {\n        xss[alignment] = util.mapValues(xs, x => x + delta);\n      }\n    });\n  });\n}\n\nfunction balance(xss, align) {\n  return util.mapValues(xss.ul, (num, v) => {\n    if (align) {\n      return xss[align.toLowerCase()][v];\n    } else {\n      let xs = Object.values(xss).map(xs => xs[v]).sort((a, b) => a - b);\n      return (xs[1] + xs[2]) / 2;\n    }\n  });\n}\n\nfunction positionX(g) {\n  let layering = util.buildLayerMatrix(g);\n  let conflicts = Object.assign(\n    findType1Conflicts(g, layering),\n    findType2Conflicts(g, layering));\n\n  let xss = {};\n  let adjustedLayering;\n  [\"u\", \"d\"].forEach(vert => {\n    adjustedLayering = vert === \"u\" ? layering : Object.values(layering).reverse();\n    [\"l\", \"r\"].forEach(horiz => {\n      if (horiz === \"r\") {\n        adjustedLayering = adjustedLayering.map(inner => {\n          return Object.values(inner).reverse();\n        });\n      }\n\n      let neighborFn = (vert === \"u\" ? g.predecessors : g.successors).bind(g);\n      let align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);\n      let xs = horizontalCompaction(g, adjustedLayering,\n        align.root, align.align, horiz === \"r\");\n      if (horiz === \"r\") {\n        xs = util.mapValues(xs, x => -x);\n      }\n      xss[vert + horiz] = xs;\n    });\n  });\n\n\n  let smallestWidth = findSmallestWidthAlignment(g, xss);\n  alignCoordinates(xss, smallestWidth);\n  return balance(xss, g.graph().align);\n}\n\nfunction sep(nodeSep, edgeSep, reverseSep) {\n  return (g, v, w) => {\n    let vLabel = g.node(v);\n    let wLabel = g.node(w);\n    let sum = 0;\n    let delta;\n\n    sum += vLabel.width / 2;\n    if (Object.hasOwn(vLabel, \"labelpos\")) {\n      switch (vLabel.labelpos.toLowerCase()) {\n      case \"l\": delta = -vLabel.width / 2; break;\n      case \"r\": delta = vLabel.width / 2; break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    sum += (vLabel.dummy ? edgeSep : nodeSep) / 2;\n    sum += (wLabel.dummy ? edgeSep : nodeSep) / 2;\n\n    sum += wLabel.width / 2;\n    if (Object.hasOwn(wLabel, \"labelpos\")) {\n      switch (wLabel.labelpos.toLowerCase()) {\n      case \"l\": delta = wLabel.width / 2; break;\n      case \"r\": delta = -wLabel.width / 2; break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    return sum;\n  };\n}\n\nfunction width(g, v) {\n  return g.node(v).width;\n}\n", "\"use strict\";\n\nlet util = require(\"../util\");\nlet positionX = require(\"./bk\").positionX;\n\nmodule.exports = position;\n\nfunction position(g) {\n  g = util.asNonCompoundGraph(g);\n\n  positionY(g);\n  Object.entries(positionX(g)).forEach(([v, x]) => g.node(v).x = x);\n}\n\nfunction positionY(g) {\n  let layering = util.buildLayerMatrix(g);\n  let rankSep = g.graph().ranksep;\n  let prevY = 0;\n  layering.forEach(layer => {\n    const maxHeight = layer.reduce((acc, v) => {\n      const height = g.node(v).height;\n      if (acc > height) {\n        return acc;\n      } else {\n        return height;\n      }\n    }, 0);\n    layer.forEach(v => g.node(v).y = prevY + maxHeight / 2);\n    prevY += maxHeight + rankSep;\n  });\n}\n\n", "\"use strict\";\n\nlet acyclic = require(\"./acyclic\");\nlet normalize = require(\"./normalize\");\nlet rank = require(\"./rank\");\nlet normalizeRanks = require(\"./util\").normalizeRanks;\nlet parentDummyChains = require(\"./parent-dummy-chains\");\nlet removeEmptyRanks = require(\"./util\").removeEmptyRanks;\nlet nestingGraph = require(\"./nesting-graph\");\nlet addBorderSegments = require(\"./add-border-segments\");\nlet coordinateSystem = require(\"./coordinate-system\");\nlet order = require(\"./order\");\nlet position = require(\"./position\");\nlet util = require(\"./util\");\nlet Graph = require(\"@dagrejs/graphlib\").Graph;\n\nmodule.exports = layout;\n\nfunction layout(g, opts) {\n  let time = opts && opts.debugTiming ? util.time : util.notime;\n  time(\"layout\", () => {\n    let layoutGraph =\n      time(\"  buildLayoutGraph\", () => buildLayoutGraph(g));\n    time(\"  runLayout\",        () => runLayout(layoutGraph, time, opts));\n    time(\"  updateInputGraph\", () => updateInputGraph(g, layoutGraph));\n  });\n}\n\nfunction runLayout(g, time, opts) {\n  time(\"    makeSpaceForEdgeLabels\", () => makeSpaceForEdgeLabels(g));\n  time(\"    removeSelfEdges\",        () => removeSelfEdges(g));\n  time(\"    acyclic\",                () => acyclic.run(g));\n  time(\"    nestingGraph.run\",       () => nestingGraph.run(g));\n  time(\"    rank\",                   () => rank(util.asNonCompoundGraph(g)));\n  time(\"    injectEdgeLabelProxies\", () => injectEdgeLabelProxies(g));\n  time(\"    removeEmptyRanks\",       () => removeEmptyRanks(g));\n  time(\"    nestingGraph.cleanup\",   () => nestingGraph.cleanup(g));\n  time(\"    normalizeRanks\",         () => normalizeRanks(g));\n  time(\"    assignRankMinMax\",       () => assignRankMinMax(g));\n  time(\"    removeEdgeLabelProxies\", () => removeEdgeLabelProxies(g));\n  time(\"    normalize.run\",          () => normalize.run(g));\n  time(\"    parentDummyChains\",      () => parentDummyChains(g));\n  time(\"    addBorderSegments\",      () => addBorderSegments(g));\n  time(\"    order\",                  () => order(g, opts));\n  time(\"    insertSelfEdges\",        () => insertSelfEdges(g));\n  time(\"    adjustCoordinateSystem\", () => coordinateSystem.adjust(g));\n  time(\"    position\",               () => position(g));\n  time(\"    positionSelfEdges\",      () => positionSelfEdges(g));\n  time(\"    removeBorderNodes\",      () => removeBorderNodes(g));\n  time(\"    normalize.undo\",         () => normalize.undo(g));\n  time(\"    fixupEdgeLabelCoords\",   () => fixupEdgeLabelCoords(g));\n  time(\"    undoCoordinateSystem\",   () => coordinateSystem.undo(g));\n  time(\"    translateGraph\",         () => translateGraph(g));\n  time(\"    assignNodeIntersects\",   () => assignNodeIntersects(g));\n  time(\"    reversePoints\",          () => reversePointsForReversedEdges(g));\n  time(\"    acyclic.undo\",           () => acyclic.undo(g));\n}\n\n/*\n * Copies final layout information from the layout graph back to the input\n * graph. This process only copies whitelisted attributes from the layout graph\n * to the input graph, so it serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction updateInputGraph(inputGraph, layoutGraph) {\n  inputGraph.nodes().forEach(v => {\n    let inputLabel = inputGraph.node(v);\n    let layoutLabel = layoutGraph.node(v);\n\n    if (inputLabel) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n      inputLabel.rank = layoutLabel.rank;\n\n      if (layoutGraph.children(v).length) {\n        inputLabel.width = layoutLabel.width;\n        inputLabel.height = layoutLabel.height;\n      }\n    }\n  });\n\n  inputGraph.edges().forEach(e => {\n    let inputLabel = inputGraph.edge(e);\n    let layoutLabel = layoutGraph.edge(e);\n\n    inputLabel.points = layoutLabel.points;\n    if (Object.hasOwn(layoutLabel, \"x\")) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n    }\n  });\n\n  inputGraph.graph().width = layoutGraph.graph().width;\n  inputGraph.graph().height = layoutGraph.graph().height;\n}\n\nlet graphNumAttrs = [\"nodesep\", \"edgesep\", \"ranksep\", \"marginx\", \"marginy\"];\nlet graphDefaults = { ranksep: 50, edgesep: 20, nodesep: 50, rankdir: \"tb\" };\nlet graphAttrs = [\"acyclicer\", \"ranker\", \"rankdir\", \"align\"];\nlet nodeNumAttrs = [\"width\", \"height\"];\nlet nodeDefaults = { width: 0, height: 0 };\nlet edgeNumAttrs = [\"minlen\", \"weight\", \"width\", \"height\", \"labeloffset\"];\nlet edgeDefaults = {\n  minlen: 1, weight: 1, width: 0, height: 0,\n  labeloffset: 10, labelpos: \"r\"\n};\nlet edgeAttrs = [\"labelpos\"];\n\n/*\n * Constructs a new graph from the input graph, which can be used for layout.\n * This process copies only whitelisted attributes from the input graph to the\n * layout graph. Thus this function serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction buildLayoutGraph(inputGraph) {\n  let g = new Graph({ multigraph: true, compound: true });\n  let graph = canonicalize(inputGraph.graph());\n\n  g.setGraph(Object.assign({},\n    graphDefaults,\n    selectNumberAttrs(graph, graphNumAttrs),\n    util.pick(graph, graphAttrs)));\n\n  inputGraph.nodes().forEach(v => {\n    let node = canonicalize(inputGraph.node(v));\n    const newNode = selectNumberAttrs(node, nodeNumAttrs);\n    Object.keys(nodeDefaults).forEach(k => {\n      if (newNode[k] === undefined) {\n        newNode[k] = nodeDefaults[k];\n      }\n    });\n\n    g.setNode(v, newNode);\n    g.setParent(v, inputGraph.parent(v));\n  });\n\n  inputGraph.edges().forEach(e => {\n    let edge = canonicalize(inputGraph.edge(e));\n    g.setEdge(e, Object.assign({},\n      edgeDefaults,\n      selectNumberAttrs(edge, edgeNumAttrs),\n      util.pick(edge, edgeAttrs)));\n  });\n\n  return g;\n}\n\n/*\n * This idea comes from the Gansner paper: to account for edge labels in our\n * layout we split each rank in half by doubling minlen and halving ranksep.\n * Then we can place labels at these mid-points between nodes.\n *\n * We also add some minimal padding to the width to push the label for the edge\n * away from the edge itself a bit.\n */\nfunction makeSpaceForEdgeLabels(g) {\n  let graph = g.graph();\n  graph.ranksep /= 2;\n  g.edges().forEach(e => {\n    let edge = g.edge(e);\n    edge.minlen *= 2;\n    if (edge.labelpos.toLowerCase() !== \"c\") {\n      if (graph.rankdir === \"TB\" || graph.rankdir === \"BT\") {\n        edge.width += edge.labeloffset;\n      } else {\n        edge.height += edge.labeloffset;\n      }\n    }\n  });\n}\n\n/*\n * Creates temporary dummy nodes that capture the rank in which each edge's\n * label is going to, if it has one of non-zero width and height. We do this\n * so that we can safely remove empty ranks while preserving balance for the\n * label's position.\n */\nfunction injectEdgeLabelProxies(g) {\n  g.edges().forEach(e => {\n    let edge = g.edge(e);\n    if (edge.width && edge.height) {\n      let v = g.node(e.v);\n      let w = g.node(e.w);\n      let label = { rank: (w.rank - v.rank) / 2 + v.rank, e: e };\n      util.addDummyNode(g, \"edge-proxy\", label, \"_ep\");\n    }\n  });\n}\n\nfunction assignRankMinMax(g) {\n  let maxRank = 0;\n  g.nodes().forEach(v => {\n    let node = g.node(v);\n    if (node.borderTop) {\n      node.minRank = g.node(node.borderTop).rank;\n      node.maxRank = g.node(node.borderBottom).rank;\n      maxRank = Math.max(maxRank, node.maxRank);\n    }\n  });\n  g.graph().maxRank = maxRank;\n}\n\nfunction removeEdgeLabelProxies(g) {\n  g.nodes().forEach(v => {\n    let node = g.node(v);\n    if (node.dummy === \"edge-proxy\") {\n      g.edge(node.e).labelRank = node.rank;\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction translateGraph(g) {\n  let minX = Number.POSITIVE_INFINITY;\n  let maxX = 0;\n  let minY = Number.POSITIVE_INFINITY;\n  let maxY = 0;\n  let graphLabel = g.graph();\n  let marginX = graphLabel.marginx || 0;\n  let marginY = graphLabel.marginy || 0;\n\n  function getExtremes(attrs) {\n    let x = attrs.x;\n    let y = attrs.y;\n    let w = attrs.width;\n    let h = attrs.height;\n    minX = Math.min(minX, x - w / 2);\n    maxX = Math.max(maxX, x + w / 2);\n    minY = Math.min(minY, y - h / 2);\n    maxY = Math.max(maxY, y + h / 2);\n  }\n\n  g.nodes().forEach(v => getExtremes(g.node(v)));\n  g.edges().forEach(e => {\n    let edge = g.edge(e);\n    if (Object.hasOwn(edge, \"x\")) {\n      getExtremes(edge);\n    }\n  });\n\n  minX -= marginX;\n  minY -= marginY;\n\n  g.nodes().forEach(v => {\n    let node = g.node(v);\n    node.x -= minX;\n    node.y -= minY;\n  });\n\n  g.edges().forEach(e => {\n    let edge = g.edge(e);\n    edge.points.forEach(p => {\n      p.x -= minX;\n      p.y -= minY;\n    });\n    if (Object.hasOwn(edge, \"x\")) { edge.x -= minX; }\n    if (Object.hasOwn(edge, \"y\")) { edge.y -= minY; }\n  });\n\n  graphLabel.width = maxX - minX + marginX;\n  graphLabel.height = maxY - minY + marginY;\n}\n\nfunction assignNodeIntersects(g) {\n  g.edges().forEach(e => {\n    let edge = g.edge(e);\n    let nodeV = g.node(e.v);\n    let nodeW = g.node(e.w);\n    let p1, p2;\n    if (!edge.points) {\n      edge.points = [];\n      p1 = nodeW;\n      p2 = nodeV;\n    } else {\n      p1 = edge.points[0];\n      p2 = edge.points[edge.points.length - 1];\n    }\n    edge.points.unshift(util.intersectRect(nodeV, p1));\n    edge.points.push(util.intersectRect(nodeW, p2));\n  });\n}\n\nfunction fixupEdgeLabelCoords(g) {\n  g.edges().forEach(e => {\n    let edge = g.edge(e);\n    if (Object.hasOwn(edge, \"x\")) {\n      if (edge.labelpos === \"l\" || edge.labelpos === \"r\") {\n        edge.width -= edge.labeloffset;\n      }\n      switch (edge.labelpos) {\n      case \"l\": edge.x -= edge.width / 2 + edge.labeloffset; break;\n      case \"r\": edge.x += edge.width / 2 + edge.labeloffset; break;\n      }\n    }\n  });\n}\n\nfunction reversePointsForReversedEdges(g) {\n  g.edges().forEach(e => {\n    let edge = g.edge(e);\n    if (edge.reversed) {\n      edge.points.reverse();\n    }\n  });\n}\n\nfunction removeBorderNodes(g) {\n  g.nodes().forEach(v => {\n    if (g.children(v).length) {\n      let node = g.node(v);\n      let t = g.node(node.borderTop);\n      let b = g.node(node.borderBottom);\n      let l = g.node(node.borderLeft[node.borderLeft.length - 1]);\n      let r = g.node(node.borderRight[node.borderRight.length - 1]);\n\n      node.width = Math.abs(r.x - l.x);\n      node.height = Math.abs(b.y - t.y);\n      node.x = l.x + node.width / 2;\n      node.y = t.y + node.height / 2;\n    }\n  });\n\n  g.nodes().forEach(v => {\n    if (g.node(v).dummy === \"border\") {\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction removeSelfEdges(g) {\n  g.edges().forEach(e => {\n    if (e.v === e.w) {\n      var node = g.node(e.v);\n      if (!node.selfEdges) {\n        node.selfEdges = [];\n      }\n      node.selfEdges.push({ e: e, label: g.edge(e) });\n      g.removeEdge(e);\n    }\n  });\n}\n\nfunction insertSelfEdges(g) {\n  var layers = util.buildLayerMatrix(g);\n  layers.forEach(layer => {\n    var orderShift = 0;\n    layer.forEach((v, i) => {\n      var node = g.node(v);\n      node.order = i + orderShift;\n      (node.selfEdges || []).forEach(selfEdge => {\n        util.addDummyNode(g, \"selfedge\", {\n          width: selfEdge.label.width,\n          height: selfEdge.label.height,\n          rank: node.rank,\n          order: i + (++orderShift),\n          e: selfEdge.e,\n          label: selfEdge.label\n        }, \"_se\");\n      });\n      delete node.selfEdges;\n    });\n  });\n}\n\nfunction positionSelfEdges(g) {\n  g.nodes().forEach(v => {\n    var node = g.node(v);\n    if (node.dummy === \"selfedge\") {\n      var selfNode = g.node(node.e.v);\n      var x = selfNode.x + selfNode.width / 2;\n      var y = selfNode.y;\n      var dx = node.x - x;\n      var dy = selfNode.height / 2;\n      g.setEdge(node.e, node.label);\n      g.removeNode(v);\n      node.label.points = [\n        { x: x + 2 * dx / 3, y: y - dy },\n        { x: x + 5 * dx / 6, y: y - dy },\n        { x: x +     dx    , y: y },\n        { x: x + 5 * dx / 6, y: y + dy },\n        { x: x + 2 * dx / 3, y: y + dy }\n      ];\n      node.label.x = node.x;\n      node.label.y = node.y;\n    }\n  });\n}\n\nfunction selectNumberAttrs(obj, attrs) {\n  return util.mapValues(util.pick(obj, attrs), Number);\n}\n\nfunction canonicalize(attrs) {\n  var newAttrs = {};\n  if (attrs) {\n    Object.entries(attrs).forEach(([k, v]) => {\n      if (typeof k === \"string\") {\n        k = k.toLowerCase();\n      }\n\n      newAttrs[k] = v;\n    });\n  }\n  return newAttrs;\n}\n", "let util = require(\"./util\");\nlet Graph = require(\"@dagrejs/graphlib\").Graph;\n\nmodule.exports = {\n  debugOrdering: debugOrdering\n};\n\n/* istanbul ignore next */\nfunction debugOrdering(g) {\n  let layerMatrix = util.buildLayerMatrix(g);\n\n  let h = new Graph({ compound: true, multigraph: true }).setGraph({});\n\n  g.nodes().forEach(v => {\n    h.setNode(v, { label: v });\n    h.setParent(v, \"layer\" + g.node(v).rank);\n  });\n\n  g.edges().forEach(e => h.setEdge(e.v, e.w, {}, e.name));\n\n  layerMatrix.forEach((layer, i) => {\n    let layerV = \"layer\" + i;\n    h.setNode(layerV, { rank: \"same\" });\n    layer.reduce((u, v) => {\n      h.setEdge(u, v, { style: \"invis\" });\n      return v;\n    });\n  });\n\n  return h;\n}\n", "module.exports = \"1.1.4\";\n", "/*\nCopyright (c) 2012-2014 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n*/\n\nmodule.exports = {\n  graphlib: require(\"@dagrejs/graphlib\"),\n\n  layout: require(\"./lib/layout\"),\n  debug: require(\"./lib/debug\"),\n  util: {\n    time: require(\"./lib/util\").time,\n    notime: require(\"./lib/util\").notime\n  },\n  version: require(\"./lib/version\")\n};\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,oBAAoB;AACxB,QAAI,aAAa;AACjB,QAAI,iBAAiB;AAYrB,QAAM,QAAN,MAAY;AAAA,MA6CV,YAAY,MAAM;AA5ClB,2CAAc;AACd,6CAAgB;AAChB,2CAAc;AAGd;AAAA;AAGA;AAAA,mDAAsB,MAAM;AAG5B;AAAA,mDAAsB,MAAM;AAG5B;AAAA,sCAAS,CAAC;AAGV;AAAA,mCAAM,CAAC;AAGP;AAAA,sCAAS,CAAC;AAGV;AAAA,oCAAO,CAAC;AAGR;AAAA,qCAAQ,CAAC;AAGT;AAAA,yCAAY,CAAC;AAGb;AAAA,2CAAc,CAAC;AAGf;AAAA,0CAAa;AAGb;AAAA,0CAAa;AAEb;AAEA;AAGE,YAAI,MAAM;AACR,eAAK,cAAc,OAAO,OAAO,MAAM,UAAU,IAAI,KAAK,WAAW;AACrE,eAAK,gBAAgB,OAAO,OAAO,MAAM,YAAY,IAAI,KAAK,aAAa;AAC3E,eAAK,cAAc,OAAO,OAAO,MAAM,UAAU,IAAI,KAAK,WAAW;AAAA,QACvE;AAEA,YAAI,KAAK,aAAa;AAEpB,eAAK,UAAU,CAAC;AAGhB,eAAK,YAAY,CAAC;AAClB,eAAK,UAAU,UAAU,IAAI,CAAC;AAAA,QAChC;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,aAAa;AACX,eAAO,KAAK;AAAA,MACd;AAAA;AAAA;AAAA;AAAA,MAKA,eAAe;AACb,eAAO,KAAK;AAAA,MACd;AAAA;AAAA;AAAA;AAAA,MAKA,aAAa;AACX,eAAO,KAAK;AAAA,MACd;AAAA;AAAA;AAAA;AAAA,MAKA,SAAS,OAAO;AACd,aAAK,SAAS;AACd,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAKA,QAAQ;AACN,eAAO,KAAK;AAAA,MACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYA,oBAAoB,YAAY;AAC9B,aAAK,sBAAsB;AAC3B,YAAI,OAAO,eAAe,YAAY;AACpC,eAAK,sBAAsB,MAAM;AAAA,QACnC;AAEA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,YAAY;AACV,eAAO,KAAK;AAAA,MACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,QAAQ;AACN,eAAO,OAAO,KAAK,KAAK,MAAM;AAAA,MAChC;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,UAAU;AACR,YAAI,OAAO;AACX,eAAO,KAAK,MAAM,EAAE,OAAO,OAAK,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC;AAAA,MACvE;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,QAAQ;AACN,YAAI,OAAO;AACX,eAAO,KAAK,MAAM,EAAE,OAAO,OAAK,OAAO,KAAK,KAAK,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC;AAAA,MACxE;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,SAAS,IAAI,OAAO;AAClB,YAAI,OAAO;AACX,YAAI,OAAO;AACX,WAAG,QAAQ,SAAS,GAAG;AACrB,cAAI,KAAK,SAAS,GAAG;AACnB,iBAAK,QAAQ,GAAG,KAAK;AAAA,UACvB,OAAO;AACL,iBAAK,QAAQ,CAAC;AAAA,UAChB;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,QAAQ,GAAG,OAAO;AAChB,YAAI,OAAO,OAAO,KAAK,QAAQ,CAAC,GAAG;AACjC,cAAI,UAAU,SAAS,GAAG;AACxB,iBAAK,OAAO,CAAC,IAAI;AAAA,UACnB;AACA,iBAAO;AAAA,QACT;AAEA,aAAK,OAAO,CAAC,IAAI,UAAU,SAAS,IAAI,QAAQ,KAAK,oBAAoB,CAAC;AAC1E,YAAI,KAAK,aAAa;AACpB,eAAK,QAAQ,CAAC,IAAI;AAClB,eAAK,UAAU,CAAC,IAAI,CAAC;AACrB,eAAK,UAAU,UAAU,EAAE,CAAC,IAAI;AAAA,QAClC;AACA,aAAK,IAAI,CAAC,IAAI,CAAC;AACf,aAAK,OAAO,CAAC,IAAI,CAAC;AAClB,aAAK,KAAK,CAAC,IAAI,CAAC;AAChB,aAAK,MAAM,CAAC,IAAI,CAAC;AACjB,UAAE,KAAK;AACP,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,KAAK,GAAG;AACN,eAAO,KAAK,OAAO,CAAC;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA,MAKA,QAAQ,GAAG;AACT,eAAO,OAAO,OAAO,KAAK,QAAQ,CAAC;AAAA,MACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,WAAW,GAAG;AACZ,YAAI,OAAO;AACX,YAAI,OAAO,OAAO,KAAK,QAAQ,CAAC,GAAG;AACjC,cAAI,aAAa,OAAK,KAAK,WAAW,KAAK,UAAU,CAAC,CAAC;AACvD,iBAAO,KAAK,OAAO,CAAC;AACpB,cAAI,KAAK,aAAa;AACpB,iBAAK,4BAA4B,CAAC;AAClC,mBAAO,KAAK,QAAQ,CAAC;AACrB,iBAAK,SAAS,CAAC,EAAE,QAAQ,SAAS,OAAO;AACvC,mBAAK,UAAU,KAAK;AAAA,YACtB,CAAC;AACD,mBAAO,KAAK,UAAU,CAAC;AAAA,UACzB;AACA,iBAAO,KAAK,KAAK,IAAI,CAAC,CAAC,EAAE,QAAQ,UAAU;AAC3C,iBAAO,KAAK,IAAI,CAAC;AACjB,iBAAO,KAAK,OAAO,CAAC;AACpB,iBAAO,KAAK,KAAK,KAAK,CAAC,CAAC,EAAE,QAAQ,UAAU;AAC5C,iBAAO,KAAK,KAAK,CAAC;AAClB,iBAAO,KAAK,MAAM,CAAC;AACnB,YAAE,KAAK;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,UAAU,GAAG,QAAQ;AACnB,YAAI,CAAC,KAAK,aAAa;AACrB,gBAAM,IAAI,MAAM,2CAA2C;AAAA,QAC7D;AAEA,YAAI,WAAW,QAAW;AACxB,mBAAS;AAAA,QACX,OAAO;AAEL,oBAAU;AACV,mBAAS,WAAW,QAAQ,aAAa,QAAW,WAAW,KAAK,OAAO,QAAQ,GAAG;AACpF,gBAAI,aAAa,GAAG;AAClB,oBAAM,IAAI,MAAM,aAAa,SAAQ,mBAAmB,IACpD,uBAAuB;AAAA,YAC7B;AAAA,UACF;AAEA,eAAK,QAAQ,MAAM;AAAA,QACrB;AAEA,aAAK,QAAQ,CAAC;AACd,aAAK,4BAA4B,CAAC;AAClC,aAAK,QAAQ,CAAC,IAAI;AAClB,aAAK,UAAU,MAAM,EAAE,CAAC,IAAI;AAC5B,eAAO;AAAA,MACT;AAAA,MAEA,4BAA4B,GAAG;AAC7B,eAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,CAAC,EAAE,CAAC;AAAA,MAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,OAAO,GAAG;AACR,YAAI,KAAK,aAAa;AACpB,cAAI,SAAS,KAAK,QAAQ,CAAC;AAC3B,cAAI,WAAW,YAAY;AACzB,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,SAAS,IAAI,YAAY;AACvB,YAAI,KAAK,aAAa;AACpB,cAAI,WAAW,KAAK,UAAU,CAAC;AAC/B,cAAI,UAAU;AACZ,mBAAO,OAAO,KAAK,QAAQ;AAAA,UAC7B;AAAA,QACF,WAAW,MAAM,YAAY;AAC3B,iBAAO,KAAK,MAAM;AAAA,QACpB,WAAW,KAAK,QAAQ,CAAC,GAAG;AAC1B,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,aAAa,GAAG;AACd,YAAI,SAAS,KAAK,OAAO,CAAC;AAC1B,YAAI,QAAQ;AACV,iBAAO,OAAO,KAAK,MAAM;AAAA,QAC3B;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,WAAW,GAAG;AACZ,YAAI,QAAQ,KAAK,MAAM,CAAC;AACxB,YAAI,OAAO;AACT,iBAAO,OAAO,KAAK,KAAK;AAAA,QAC1B;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,UAAU,GAAG;AACX,YAAI,QAAQ,KAAK,aAAa,CAAC;AAC/B,YAAI,OAAO;AACT,gBAAM,QAAQ,IAAI,IAAI,KAAK;AAC3B,mBAAS,QAAQ,KAAK,WAAW,CAAC,GAAG;AACnC,kBAAM,IAAI,IAAI;AAAA,UAChB;AAEA,iBAAO,MAAM,KAAK,MAAM,OAAO,CAAC;AAAA,QAClC;AAAA,MACF;AAAA,MAEA,OAAO,GAAG;AACR,YAAI;AACJ,YAAI,KAAK,WAAW,GAAG;AACrB,sBAAY,KAAK,WAAW,CAAC;AAAA,QAC/B,OAAO;AACL,sBAAY,KAAK,UAAU,CAAC;AAAA,QAC9B;AACA,eAAO,UAAU,WAAW;AAAA,MAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,YAAY,QAAQ;AAClB,YAAI,OAAO,IAAI,KAAK,YAAY;AAAA,UAC9B,UAAU,KAAK;AAAA,UACf,YAAY,KAAK;AAAA,UACjB,UAAU,KAAK;AAAA,QACjB,CAAC;AAED,aAAK,SAAS,KAAK,MAAM,CAAC;AAE1B,YAAI,OAAO;AACX,eAAO,QAAQ,KAAK,MAAM,EAAE,QAAQ,SAAS,CAAC,GAAG,KAAK,GAAG;AACvD,cAAI,OAAO,CAAC,GAAG;AACb,iBAAK,QAAQ,GAAG,KAAK;AAAA,UACvB;AAAA,QACF,CAAC;AAED,eAAO,OAAO,KAAK,SAAS,EAAE,QAAQ,SAAS,GAAG;AAChD,cAAI,KAAK,QAAQ,EAAE,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC,GAAG;AAC1C,iBAAK,QAAQ,GAAG,KAAK,KAAK,CAAC,CAAC;AAAA,UAC9B;AAAA,QACF,CAAC;AAED,YAAI,UAAU,CAAC;AACf,iBAAS,WAAW,GAAG;AACrB,cAAI,SAAS,KAAK,OAAO,CAAC;AAC1B,cAAI,WAAW,UAAa,KAAK,QAAQ,MAAM,GAAG;AAChD,oBAAQ,CAAC,IAAI;AACb,mBAAO;AAAA,UACT,WAAW,UAAU,SAAS;AAC5B,mBAAO,QAAQ,MAAM;AAAA,UACvB,OAAO;AACL,mBAAO,WAAW,MAAM;AAAA,UAC1B;AAAA,QACF;AAEA,YAAI,KAAK,aAAa;AACpB,eAAK,MAAM,EAAE,QAAQ,OAAK,KAAK,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC;AAAA,QAC5D;AAEA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAWA,oBAAoB,YAAY;AAC9B,aAAK,sBAAsB;AAC3B,YAAI,OAAO,eAAe,YAAY;AACpC,eAAK,sBAAsB,MAAM;AAAA,QACnC;AAEA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,YAAY;AACV,eAAO,KAAK;AAAA,MACd;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,QAAQ;AACN,eAAO,OAAO,OAAO,KAAK,SAAS;AAAA,MACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,QAAQ,IAAI,OAAO;AACjB,YAAI,OAAO;AACX,YAAI,OAAO;AACX,WAAG,OAAO,SAAS,GAAG,GAAG;AACvB,cAAI,KAAK,SAAS,GAAG;AACnB,iBAAK,QAAQ,GAAG,GAAG,KAAK;AAAA,UAC1B,OAAO;AACL,iBAAK,QAAQ,GAAG,CAAC;AAAA,UACnB;AACA,iBAAO;AAAA,QACT,CAAC;AACD,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,UAAU;AACR,YAAI,GAAG,GAAG,MAAM;AAChB,YAAI,iBAAiB;AACrB,YAAI,OAAO,UAAU,CAAC;AAEtB,YAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,MAAM;AAC5D,cAAI,KAAK;AACT,cAAI,KAAK;AACT,iBAAO,KAAK;AACZ,cAAI,UAAU,WAAW,GAAG;AAC1B,oBAAQ,UAAU,CAAC;AACnB,6BAAiB;AAAA,UACnB;AAAA,QACF,OAAO;AACL,cAAI;AACJ,cAAI,UAAU,CAAC;AACf,iBAAO,UAAU,CAAC;AAClB,cAAI,UAAU,SAAS,GAAG;AACxB,oBAAQ,UAAU,CAAC;AACnB,6BAAiB;AAAA,UACnB;AAAA,QACF;AAEA,YAAI,KAAK;AACT,YAAI,KAAK;AACT,YAAI,SAAS,QAAW;AACtB,iBAAO,KAAK;AAAA,QACd;AAEA,YAAI,IAAI,aAAa,KAAK,aAAa,GAAG,GAAG,IAAI;AACjD,YAAI,OAAO,OAAO,KAAK,aAAa,CAAC,GAAG;AACtC,cAAI,gBAAgB;AAClB,iBAAK,YAAY,CAAC,IAAI;AAAA,UACxB;AACA,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,UAAa,CAAC,KAAK,eAAe;AAC7C,gBAAM,IAAI,MAAM,mDAAmD;AAAA,QACrE;AAIA,aAAK,QAAQ,CAAC;AACd,aAAK,QAAQ,CAAC;AAEd,aAAK,YAAY,CAAC,IAAI,iBAAiB,QAAQ,KAAK,oBAAoB,GAAG,GAAG,IAAI;AAElF,YAAI,UAAU,cAAc,KAAK,aAAa,GAAG,GAAG,IAAI;AAExD,YAAI,QAAQ;AACZ,YAAI,QAAQ;AAEZ,eAAO,OAAO,OAAO;AACrB,aAAK,UAAU,CAAC,IAAI;AACpB,6BAAqB,KAAK,OAAO,CAAC,GAAG,CAAC;AACtC,6BAAqB,KAAK,MAAM,CAAC,GAAG,CAAC;AACrC,aAAK,IAAI,CAAC,EAAE,CAAC,IAAI;AACjB,aAAK,KAAK,CAAC,EAAE,CAAC,IAAI;AAClB,aAAK;AACL,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,KAAK,GAAG,GAAG,MAAM;AACf,YAAI,IAAK,UAAU,WAAW,IAC1B,YAAY,KAAK,aAAa,UAAU,CAAC,CAAC,IAC1C,aAAa,KAAK,aAAa,GAAG,GAAG,IAAI;AAC7C,eAAO,KAAK,YAAY,CAAC;AAAA,MAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,YAAY;AACV,cAAM,OAAO,KAAK,KAAK,GAAG,SAAS;AACnC,YAAI,OAAO,SAAS,UAAU;AAC5B,iBAAO,EAAC,OAAO,KAAI;AAAA,QACrB;AAEA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,QAAQ,GAAG,GAAG,MAAM;AAClB,YAAI,IAAK,UAAU,WAAW,IAC1B,YAAY,KAAK,aAAa,UAAU,CAAC,CAAC,IAC1C,aAAa,KAAK,aAAa,GAAG,GAAG,IAAI;AAC7C,eAAO,OAAO,OAAO,KAAK,aAAa,CAAC;AAAA,MAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,WAAW,GAAG,GAAG,MAAM;AACrB,YAAI,IAAK,UAAU,WAAW,IAC1B,YAAY,KAAK,aAAa,UAAU,CAAC,CAAC,IAC1C,aAAa,KAAK,aAAa,GAAG,GAAG,IAAI;AAC7C,YAAI,OAAO,KAAK,UAAU,CAAC;AAC3B,YAAI,MAAM;AACR,cAAI,KAAK;AACT,cAAI,KAAK;AACT,iBAAO,KAAK,YAAY,CAAC;AACzB,iBAAO,KAAK,UAAU,CAAC;AACvB,iCAAuB,KAAK,OAAO,CAAC,GAAG,CAAC;AACxC,iCAAuB,KAAK,MAAM,CAAC,GAAG,CAAC;AACvC,iBAAO,KAAK,IAAI,CAAC,EAAE,CAAC;AACpB,iBAAO,KAAK,KAAK,CAAC,EAAE,CAAC;AACrB,eAAK;AAAA,QACP;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,QAAQ,GAAG,GAAG;AACZ,YAAI,MAAM,KAAK,IAAI,CAAC;AACpB,YAAI,KAAK;AACP,cAAI,QAAQ,OAAO,OAAO,GAAG;AAC7B,cAAI,CAAC,GAAG;AACN,mBAAO;AAAA,UACT;AACA,iBAAO,MAAM,OAAO,UAAQ,KAAK,MAAM,CAAC;AAAA,QAC1C;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,SAAS,GAAG,GAAG;AACb,YAAI,OAAO,KAAK,KAAK,CAAC;AACtB,YAAI,MAAM;AACR,cAAI,QAAQ,OAAO,OAAO,IAAI;AAC9B,cAAI,CAAC,GAAG;AACN,mBAAO;AAAA,UACT;AACA,iBAAO,MAAM,OAAO,UAAQ,KAAK,MAAM,CAAC;AAAA,QAC1C;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,UAAU,GAAG,GAAG;AACd,YAAI,UAAU,KAAK,QAAQ,GAAG,CAAC;AAC/B,YAAI,SAAS;AACX,iBAAO,QAAQ,OAAO,KAAK,SAAS,GAAG,CAAC,CAAC;AAAA,QAC3C;AAAA,MACF;AAAA,IACF;AAEA,aAAS,qBAAqB,KAAK,GAAG;AACpC,UAAI,IAAI,CAAC,GAAG;AACV,YAAI,CAAC;AAAA,MACP,OAAO;AACL,YAAI,CAAC,IAAI;AAAA,MACX;AAAA,IACF;AAEA,aAAS,uBAAuB,KAAK,GAAG;AACtC,UAAI,CAAC,EAAE,IAAI,CAAC,GAAG;AAAE,eAAO,IAAI,CAAC;AAAA,MAAG;AAAA,IAClC;AAEA,aAAS,aAAa,YAAY,IAAI,IAAI,MAAM;AAC9C,UAAI,IAAI,KAAK;AACb,UAAI,IAAI,KAAK;AACb,UAAI,CAAC,cAAc,IAAI,GAAG;AACxB,YAAI,MAAM;AACV,YAAI;AACJ,YAAI;AAAA,MACN;AACA,aAAO,IAAI,iBAAiB,IAAI,kBACpB,SAAS,SAAY,oBAAoB;AAAA,IACvD;AAEA,aAAS,cAAc,YAAY,IAAI,IAAI,MAAM;AAC/C,UAAI,IAAI,KAAK;AACb,UAAI,IAAI,KAAK;AACb,UAAI,CAAC,cAAc,IAAI,GAAG;AACxB,YAAI,MAAM;AACV,YAAI;AACJ,YAAI;AAAA,MACN;AACA,UAAI,UAAW,EAAE,GAAM,EAAK;AAC5B,UAAI,MAAM;AACR,gBAAQ,OAAO;AAAA,MACjB;AACA,aAAO;AAAA,IACT;AAEA,aAAS,YAAY,YAAY,SAAS;AACxC,aAAO,aAAa,YAAY,QAAQ,GAAG,QAAQ,GAAG,QAAQ,IAAI;AAAA,IACpE;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACvrBjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AACA,WAAO,UAAU;AAAA,MACf,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAAA;AAAA;;;ACJA;AAAA;AAAA,QAAI,QAAQ;AAEZ,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAMA,aAAS,MAAM,GAAG;AAChB,UAAI,OAAO;AAAA,QACT,SAAS;AAAA,UACP,UAAU,EAAE,WAAW;AAAA,UACvB,YAAY,EAAE,aAAa;AAAA,UAC3B,UAAU,EAAE,WAAW;AAAA,QACzB;AAAA,QACA,OAAO,WAAW,CAAC;AAAA,QACnB,OAAO,WAAW,CAAC;AAAA,MACrB;AAEA,UAAI,EAAE,MAAM,MAAM,QAAW;AAC3B,aAAK,QAAQ,gBAAgB,EAAE,MAAM,CAAC;AAAA,MACxC;AACA,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,GAAG;AACrB,aAAO,EAAE,MAAM,EAAE,IAAI,SAAS,GAAG;AAC/B,YAAI,YAAY,EAAE,KAAK,CAAC;AACxB,YAAI,SAAS,EAAE,OAAO,CAAC;AACvB,YAAI,OAAO,EAAE,EAAK;AAClB,YAAI,cAAc,QAAW;AAC3B,eAAK,QAAQ;AAAA,QACf;AACA,YAAI,WAAW,QAAW;AACxB,eAAK,SAAS;AAAA,QAChB;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,aAAS,WAAW,GAAG;AACrB,aAAO,EAAE,MAAM,EAAE,IAAI,SAAS,GAAG;AAC/B,YAAI,YAAY,EAAE,KAAK,CAAC;AACxB,YAAI,OAAO,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE;AAC5B,YAAI,EAAE,SAAS,QAAW;AACxB,eAAK,OAAO,EAAE;AAAA,QAChB;AACA,YAAI,cAAc,QAAW;AAC3B,eAAK,QAAQ;AAAA,QACf;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAYA,aAAS,KAAK,MAAM;AAClB,UAAI,IAAI,IAAI,MAAM,KAAK,OAAO,EAAE,SAAS,KAAK,KAAK;AACnD,WAAK,MAAM,QAAQ,SAAS,OAAO;AACjC,UAAE,QAAQ,MAAM,GAAG,MAAM,KAAK;AAC9B,YAAI,MAAM,QAAQ;AAChB,YAAE,UAAU,MAAM,GAAG,MAAM,MAAM;AAAA,QACnC;AAAA,MACF,CAAC;AACD,WAAK,MAAM,QAAQ,SAAS,OAAO;AACjC,UAAE,QAAQ,EAAE,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,MAAM,MAAM,KAAK,GAAG,MAAM,KAAK;AAAA,MACrE,CAAC;AACD,aAAO;AAAA,IACT;AAAA;AAAA;;;AC/EA;AAAA;AAAA,WAAO,UAAU;AAEjB,aAAS,WAAW,GAAG;AACrB,UAAI,UAAU,CAAC;AACf,UAAI,QAAQ,CAAC;AACb,UAAI;AAEJ,eAAS,IAAI,GAAG;AACd,YAAI,OAAO,OAAO,SAAS,CAAC,EAAG;AAC/B,gBAAQ,CAAC,IAAI;AACb,aAAK,KAAK,CAAC;AACX,UAAE,WAAW,CAAC,EAAE,QAAQ,GAAG;AAC3B,UAAE,aAAa,CAAC,EAAE,QAAQ,GAAG;AAAA,MAC/B;AAEA,QAAE,MAAM,EAAE,QAAQ,SAAS,GAAG;AAC5B,eAAO,CAAC;AACR,YAAI,CAAC;AACL,YAAI,KAAK,QAAQ;AACf,gBAAM,KAAK,IAAI;AAAA,QACjB;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAAA;AAAA;;;ACxBA;AAAA;AAOA,QAAM,gBAAN,MAAoB;AAAA,MAApB;AACE,oCAAO,CAAC;AACR,2CAAc,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKf,OAAO;AACL,eAAO,KAAK,KAAK;AAAA,MACnB;AAAA;AAAA;AAAA;AAAA,MAKA,OAAO;AACL,eAAO,KAAK,KAAK,IAAI,SAAS,GAAG;AAAE,iBAAO,EAAE;AAAA,QAAK,CAAC;AAAA,MACpD;AAAA;AAAA;AAAA;AAAA,MAKA,IAAI,KAAK;AACP,eAAO,OAAO,OAAO,KAAK,aAAa,GAAG;AAAA,MAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,SAAS,KAAK;AACZ,YAAI,QAAQ,KAAK,YAAY,GAAG;AAChC,YAAI,UAAU,QAAW;AACvB,iBAAO,KAAK,KAAK,KAAK,EAAE;AAAA,QAC1B;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,MAAM;AACJ,YAAI,KAAK,KAAK,MAAM,GAAG;AACrB,gBAAM,IAAI,MAAM,iBAAiB;AAAA,QACnC;AACA,eAAO,KAAK,KAAK,CAAC,EAAE;AAAA,MACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,IAAI,KAAK,UAAU;AACjB,YAAI,aAAa,KAAK;AACtB,cAAM,OAAO,GAAG;AAChB,YAAI,CAAC,OAAO,OAAO,YAAY,GAAG,GAAG;AACnC,cAAI,MAAM,KAAK;AACf,cAAI,QAAQ,IAAI;AAChB,qBAAW,GAAG,IAAI;AAClB,cAAI,KAAK,EAAC,KAAU,SAAkB,CAAC;AACvC,eAAK,UAAU,KAAK;AACpB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAKA,YAAY;AACV,aAAK,MAAM,GAAG,KAAK,KAAK,SAAS,CAAC;AAClC,YAAI,MAAM,KAAK,KAAK,IAAI;AACxB,eAAO,KAAK,YAAY,IAAI,GAAG;AAC/B,aAAK,SAAS,CAAC;AACf,eAAO,IAAI;AAAA,MACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,SAAS,KAAK,UAAU;AACtB,YAAI,QAAQ,KAAK,YAAY,GAAG;AAChC,YAAI,WAAW,KAAK,KAAK,KAAK,EAAE,UAAU;AACxC,gBAAM,IAAI,MAAM,yDACF,MAAM,WAAW,KAAK,KAAK,KAAK,EAAE,WAAW,WAAW,QAAQ;AAAA,QAChF;AACA,aAAK,KAAK,KAAK,EAAE,WAAW;AAC5B,aAAK,UAAU,KAAK;AAAA,MACtB;AAAA,MAEA,SAAS,GAAG;AACV,YAAI,MAAM,KAAK;AACf,YAAI,IAAI,IAAI;AACZ,YAAI,IAAI,IAAI;AACZ,YAAI,UAAU;AACd,YAAI,IAAI,IAAI,QAAQ;AAClB,oBAAU,IAAI,CAAC,EAAE,WAAW,IAAI,OAAO,EAAE,WAAW,IAAI;AACxD,cAAI,IAAI,IAAI,QAAQ;AAClB,sBAAU,IAAI,CAAC,EAAE,WAAW,IAAI,OAAO,EAAE,WAAW,IAAI;AAAA,UAC1D;AACA,cAAI,YAAY,GAAG;AACjB,iBAAK,MAAM,GAAG,OAAO;AACrB,iBAAK,SAAS,OAAO;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAAA,MAEA,UAAU,OAAO;AACf,YAAI,MAAM,KAAK;AACf,YAAI,WAAW,IAAI,KAAK,EAAE;AAC1B,YAAI;AACJ,eAAO,UAAU,GAAG;AAClB,mBAAS,SAAS;AAClB,cAAI,IAAI,MAAM,EAAE,WAAW,UAAU;AACnC;AAAA,UACF;AACA,eAAK,MAAM,OAAO,MAAM;AACxB,kBAAQ;AAAA,QACV;AAAA,MACF;AAAA,MAEA,MAAM,GAAG,GAAG;AACV,YAAI,MAAM,KAAK;AACf,YAAI,aAAa,KAAK;AACtB,YAAI,WAAW,IAAI,CAAC;AACpB,YAAI,WAAW,IAAI,CAAC;AACpB,YAAI,CAAC,IAAI;AACT,YAAI,CAAC,IAAI;AACT,mBAAW,SAAS,GAAG,IAAI;AAC3B,mBAAW,SAAS,GAAG,IAAI;AAAA,MAC7B;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrJjB;AAAA;AAAA,QAAI,gBAAgB;AAEpB,WAAO,UAAU;AAEjB,QAAI,sBAAsB,MAAM;AAEhC,aAAS,SAAS,GAAG,QAAQ,UAAU,QAAQ;AAC7C,aAAO;AAAA,QAAY;AAAA,QAAG,OAAO,MAAM;AAAA,QACjC,YAAY;AAAA,QACZ,UAAU,SAAS,GAAG;AAAE,iBAAO,EAAE,SAAS,CAAC;AAAA,QAAG;AAAA,MAAC;AAAA,IACnD;AAEA,aAAS,YAAY,GAAG,QAAQ,UAAU,QAAQ;AAChD,UAAI,UAAU,CAAC;AACf,UAAI,KAAK,IAAI,cAAc;AAC3B,UAAI,GAAG;AAEP,UAAI,kBAAkB,SAAS,MAAM;AACnC,YAAI,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AACrC,YAAI,SAAS,QAAQ,CAAC;AACtB,YAAI,SAAS,SAAS,IAAI;AAC1B,YAAI,WAAW,OAAO,WAAW;AAEjC,YAAI,SAAS,GAAG;AACd,gBAAM,IAAI,MAAM,8DACe,OAAO,cAAc,MAAM;AAAA,QAC5D;AAEA,YAAI,WAAW,OAAO,UAAU;AAC9B,iBAAO,WAAW;AAClB,iBAAO,cAAc;AACrB,aAAG,SAAS,GAAG,QAAQ;AAAA,QACzB;AAAA,MACF;AAEA,QAAE,MAAM,EAAE,QAAQ,SAASA,IAAG;AAC5B,YAAI,WAAWA,OAAM,SAAS,IAAI,OAAO;AACzC,gBAAQA,EAAC,IAAI,EAAE,SAAmB;AAClC,WAAG,IAAIA,IAAG,QAAQ;AAAA,MACpB,CAAC;AAED,aAAO,GAAG,KAAK,IAAI,GAAG;AACpB,YAAI,GAAG,UAAU;AACjB,iBAAS,QAAQ,CAAC;AAClB,YAAI,OAAO,aAAa,OAAO,mBAAmB;AAChD;AAAA,QACF;AAEA,eAAO,CAAC,EAAE,QAAQ,eAAe;AAAA,MACnC;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACpDA;AAAA;AAAA,QAAI,WAAW;AAEf,WAAO,UAAU;AAEjB,aAAS,YAAY,GAAG,YAAY,UAAU;AAC5C,aAAO,EAAE,MAAM,EAAE,OAAO,SAAS,KAAK,GAAG;AACvC,YAAI,CAAC,IAAI,SAAS,GAAG,GAAG,YAAY,QAAQ;AAC5C,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP;AAAA;AAAA;;;ACTA;AAAA;AAAA,WAAO,UAAU;AAEjB,aAAS,OAAO,GAAG;AACjB,UAAI,QAAQ;AACZ,UAAI,QAAQ,CAAC;AACb,UAAI,UAAU,CAAC;AACf,UAAI,UAAU,CAAC;AAEf,eAAS,IAAI,GAAG;AACd,YAAI,QAAQ,QAAQ,CAAC,IAAI;AAAA,UACvB,SAAS;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AACA,cAAM,KAAK,CAAC;AAEZ,UAAE,WAAW,CAAC,EAAE,QAAQ,SAASC,IAAG;AAClC,cAAI,CAAC,OAAO,OAAO,SAASA,EAAC,GAAG;AAC9B,gBAAIA,EAAC;AACL,kBAAM,UAAU,KAAK,IAAI,MAAM,SAAS,QAAQA,EAAC,EAAE,OAAO;AAAA,UAC5D,WAAW,QAAQA,EAAC,EAAE,SAAS;AAC7B,kBAAM,UAAU,KAAK,IAAI,MAAM,SAAS,QAAQA,EAAC,EAAE,KAAK;AAAA,UAC1D;AAAA,QACF,CAAC;AAED,YAAI,MAAM,YAAY,MAAM,OAAO;AACjC,cAAI,OAAO,CAAC;AACZ,cAAI;AACJ,aAAG;AACD,gBAAI,MAAM,IAAI;AACd,oBAAQ,CAAC,EAAE,UAAU;AACrB,iBAAK,KAAK,CAAC;AAAA,UACb,SAAS,MAAM;AACf,kBAAQ,KAAK,IAAI;AAAA,QACnB;AAAA,MACF;AAEA,QAAE,MAAM,EAAE,QAAQ,SAAS,GAAG;AAC5B,YAAI,CAAC,OAAO,OAAO,SAAS,CAAC,GAAG;AAC9B,cAAI,CAAC;AAAA,QACP;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAAA;AAAA;;;AC5CA;AAAA;AAAA,QAAI,SAAS;AAEb,WAAO,UAAU;AAEjB,aAAS,WAAW,GAAG;AACrB,aAAO,OAAO,CAAC,EAAE,OAAO,SAAS,MAAM;AACrC,eAAO,KAAK,SAAS,KAAM,KAAK,WAAW,KAAK,EAAE,QAAQ,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,MAC5E,CAAC;AAAA,IACH;AAAA;AAAA;;;ACRA;AAAA;AAAA,WAAO,UAAU;AAEjB,QAAI,sBAAsB,MAAM;AAEhC,aAAS,cAAc,GAAG,UAAU,QAAQ;AAC1C,aAAO;AAAA,QAAiB;AAAA,QACtB,YAAY;AAAA,QACZ,UAAU,SAAS,GAAG;AAAE,iBAAO,EAAE,SAAS,CAAC;AAAA,QAAG;AAAA,MAAC;AAAA,IACnD;AAEA,aAAS,iBAAiB,GAAG,UAAU,QAAQ;AAC7C,UAAI,UAAU,CAAC;AACf,UAAI,QAAQ,EAAE,MAAM;AAEpB,YAAM,QAAQ,SAAS,GAAG;AACxB,gBAAQ,CAAC,IAAI,CAAC;AACd,gBAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,EAAE;AAC9B,cAAM,QAAQ,SAAS,GAAG;AACxB,cAAI,MAAM,GAAG;AACX,oBAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,OAAO,kBAAkB;AAAA,UACvD;AAAA,QACF,CAAC;AACD,eAAO,CAAC,EAAE,QAAQ,SAAS,MAAM;AAC/B,cAAI,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AACrC,cAAI,IAAI,SAAS,IAAI;AACrB,kBAAQ,CAAC,EAAE,CAAC,IAAI,EAAE,UAAU,GAAG,aAAa,EAAE;AAAA,QAChD,CAAC;AAAA,MACH,CAAC;AAED,YAAM,QAAQ,SAAS,GAAG;AACxB,YAAI,OAAO,QAAQ,CAAC;AACpB,cAAM,QAAQ,SAAS,GAAG;AACxB,cAAI,OAAO,QAAQ,CAAC;AACpB,gBAAM,QAAQ,SAAS,GAAG;AACxB,gBAAI,KAAK,KAAK,CAAC;AACf,gBAAI,KAAK,KAAK,CAAC;AACf,gBAAI,KAAK,KAAK,CAAC;AACf,gBAAI,cAAc,GAAG,WAAW,GAAG;AACnC,gBAAI,cAAc,GAAG,UAAU;AAC7B,iBAAG,WAAW;AACd,iBAAG,cAAc,GAAG;AAAA,YACtB;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,IACT;AAAA;AAAA;;;AC/CA;AAAA;AAAA,aAAS,QAAQ,GAAG;AAClB,UAAI,UAAU,CAAC;AACf,UAAI,QAAQ,CAAC;AACb,UAAI,UAAU,CAAC;AAEf,eAAS,MAAM,MAAM;AACnB,YAAI,OAAO,OAAO,OAAO,IAAI,GAAG;AAC9B,gBAAM,IAAI,eAAe;AAAA,QAC3B;AAEA,YAAI,CAAC,OAAO,OAAO,SAAS,IAAI,GAAG;AACjC,gBAAM,IAAI,IAAI;AACd,kBAAQ,IAAI,IAAI;AAChB,YAAE,aAAa,IAAI,EAAE,QAAQ,KAAK;AAClC,iBAAO,MAAM,IAAI;AACjB,kBAAQ,KAAK,IAAI;AAAA,QACnB;AAAA,MACF;AAEA,QAAE,MAAM,EAAE,QAAQ,KAAK;AAEvB,UAAI,OAAO,KAAK,OAAO,EAAE,WAAW,EAAE,UAAU,GAAG;AACjD,cAAM,IAAI,eAAe;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AAEA,QAAM,iBAAN,cAA6B,MAAM;AAAA,MACjC,cAAc;AACZ,cAAM,GAAG,SAAS;AAAA,MACpB;AAAA,IACF;AAEA,WAAO,UAAU;AACjB,YAAQ,iBAAiB;AAAA;AAAA;;;ACnCzB;AAAA;AAAA,QAAI,UAAU;AAEd,WAAO,UAAU;AAEjB,aAAS,UAAU,GAAG;AACpB,UAAI;AACF,gBAAQ,CAAC;AAAA,MACX,SAAS,GAAG;AACV,YAAI,aAAa,QAAQ,gBAAgB;AACvC,iBAAO;AAAA,QACT;AACA,cAAM;AAAA,MACR;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACdA;AAAA;AAAA,WAAO,UAAU;AAUjB,aAAS,IAAI,GAAG,IAAI,OAAO;AACzB,UAAI,CAAC,MAAM,QAAQ,EAAE,GAAG;AACtB,aAAK,CAAC,EAAE;AAAA,MACV;AAEA,UAAI,aAAa,EAAE,WAAW,IAAI,OAAK,EAAE,WAAW,CAAC,IAAI,OAAK,EAAE,UAAU,CAAC;AAC3E,UAAI,YAAY,UAAU,SAAS,eAAe;AAElD,UAAI,MAAM,CAAC;AACX,UAAI,UAAU,CAAC;AACf,SAAG,QAAQ,OAAK;AACd,YAAI,CAAC,EAAE,QAAQ,CAAC,GAAG;AACjB,gBAAM,IAAI,MAAM,+BAA+B,CAAC;AAAA,QAClD;AAEA,kBAAU,GAAG,YAAY,SAAS,GAAG;AAAA,MACvC,CAAC;AAED,aAAO;AAAA,IACT;AAEA,aAAS,aAAa,GAAG,YAAY,SAAS,KAAK;AACjD,UAAI,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;AACvB,aAAO,MAAM,SAAS,GAAG;AACvB,YAAI,OAAO,MAAM,IAAI;AACrB,YAAI,KAAK,CAAC,GAAG;AACX,cAAI,KAAK,KAAK,CAAC,CAAC;AAAA,QAClB,OAAO;AACL,cAAI,CAAC,OAAO,OAAO,SAAS,KAAK,CAAC,CAAC,GAAG;AACpC,oBAAQ,KAAK,CAAC,CAAC,IAAI;AACnB,kBAAM,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAC1B,yBAAa,WAAW,KAAK,CAAC,CAAC,GAAG,OAAK,MAAM,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,UAC/D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,aAAS,YAAY,GAAG,YAAY,SAAS,KAAK;AAChD,UAAI,QAAQ,CAAC,CAAC;AACd,aAAO,MAAM,SAAS,GAAG;AACvB,YAAI,OAAO,MAAM,IAAI;AACrB,YAAI,CAAC,OAAO,OAAO,SAAS,IAAI,GAAG;AACjC,kBAAQ,IAAI,IAAI;AAChB,cAAI,KAAK,IAAI;AACb,uBAAa,WAAW,IAAI,GAAG,OAAK,MAAM,KAAK,CAAC,CAAC;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAEA,aAAS,aAAa,OAAO,UAAU;AACrC,UAAI,SAAS,MAAM;AACnB,aAAO,UAAU;AACf,iBAAS,MAAM,MAAM,GAAG,QAAQ,KAAK;AAAA,MACvC;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AClEA;AAAA;AAAA,QAAI,MAAM;AAEV,WAAO,UAAU;AAEjB,aAAS,UAAU,GAAG,IAAI;AACxB,aAAO,IAAI,GAAG,IAAI,MAAM;AAAA,IAC1B;AAAA;AAAA;;;ACNA;AAAA;AAAA,QAAI,MAAM;AAEV,WAAO,UAAU;AAEjB,aAAS,SAAS,GAAG,IAAI;AACvB,aAAO,IAAI,GAAG,IAAI,KAAK;AAAA,IACzB;AAAA;AAAA;;;ACNA;AAAA;AAAA,QAAI,QAAQ;AACZ,QAAI,gBAAgB;AAEpB,WAAO,UAAU;AAEjB,aAAS,KAAK,GAAG,YAAY;AAC3B,UAAI,SAAS,IAAI,MAAM;AACvB,UAAI,UAAU,CAAC;AACf,UAAI,KAAK,IAAI,cAAc;AAC3B,UAAI;AAEJ,eAAS,gBAAgB,MAAM;AAC7B,YAAI,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AACrC,YAAI,MAAM,GAAG,SAAS,CAAC;AACvB,YAAI,QAAQ,QAAW;AACrB,cAAI,aAAa,WAAW,IAAI;AAChC,cAAI,aAAa,KAAK;AACpB,oBAAQ,CAAC,IAAI;AACb,eAAG,SAAS,GAAG,UAAU;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAEA,UAAI,EAAE,UAAU,MAAM,GAAG;AACvB,eAAO;AAAA,MACT;AAEA,QAAE,MAAM,EAAE,QAAQ,SAASC,IAAG;AAC5B,WAAG,IAAIA,IAAG,OAAO,iBAAiB;AAClC,eAAO,QAAQA,EAAC;AAAA,MAClB,CAAC;AAGD,SAAG,SAAS,EAAE,MAAM,EAAE,CAAC,GAAG,CAAC;AAE3B,UAAI,OAAO;AACX,aAAO,GAAG,KAAK,IAAI,GAAG;AACpB,YAAI,GAAG,UAAU;AACjB,YAAI,OAAO,OAAO,SAAS,CAAC,GAAG;AAC7B,iBAAO,QAAQ,GAAG,QAAQ,CAAC,CAAC;AAAA,QAC9B,WAAW,MAAM;AACf,gBAAM,IAAI,MAAM,mCAAmC,CAAC;AAAA,QACtD,OAAO;AACL,iBAAO;AAAA,QACT;AAEA,UAAE,UAAU,CAAC,EAAE,QAAQ,eAAe;AAAA,MACxC;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AClDA;AAAA;AAAA,WAAO,UAAU;AAAA,MACf,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AAAA;AAAA;;;ACZA;AAAA;AA8BA,QAAI,MAAM;AAEV,WAAO,UAAU;AAAA,MACf,OAAO,IAAI;AAAA,MACX,MAAM;AAAA,MACN,KAAK;AAAA,MACL,SAAS,IAAI;AAAA,IACf;AAAA;AAAA;;;ACrCA;AAAA;AAKA,QAAM,OAAN,MAAW;AAAA,MACT,cAAc;AACZ,YAAI,WAAW,CAAC;AAChB,iBAAS,QAAQ,SAAS,QAAQ;AAClC,aAAK,YAAY;AAAA,MACnB;AAAA,MAEA,UAAU;AACR,YAAI,WAAW,KAAK;AACpB,YAAI,QAAQ,SAAS;AACrB,YAAI,UAAU,UAAU;AACtB,iBAAO,KAAK;AACZ,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MAEA,QAAQ,OAAO;AACb,YAAI,WAAW,KAAK;AACpB,YAAI,MAAM,SAAS,MAAM,OAAO;AAC9B,iBAAO,KAAK;AAAA,QACd;AACA,cAAM,QAAQ,SAAS;AACvB,iBAAS,MAAM,QAAQ;AACvB,iBAAS,QAAQ;AACjB,cAAM,QAAQ;AAAA,MAChB;AAAA,MAEA,WAAW;AACT,YAAI,OAAO,CAAC;AACZ,YAAI,WAAW,KAAK;AACpB,YAAI,OAAO,SAAS;AACpB,eAAO,SAAS,UAAU;AACxB,eAAK,KAAK,KAAK,UAAU,MAAM,cAAc,CAAC;AAC9C,iBAAO,KAAK;AAAA,QACd;AACA,eAAO,MAAM,KAAK,KAAK,IAAI,IAAI;AAAA,MACjC;AAAA,IACF;AAEA,aAAS,OAAO,OAAO;AACrB,YAAM,MAAM,QAAQ,MAAM;AAC1B,YAAM,MAAM,QAAQ,MAAM;AAC1B,aAAO,MAAM;AACb,aAAO,MAAM;AAAA,IACf;AAEA,aAAS,eAAe,GAAG,GAAG;AAC5B,UAAI,MAAM,WAAW,MAAM,SAAS;AAClC,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzDjB;AAAA;AAAA,QAAI,QAAQ,mBAA6B;AACzC,QAAI,OAAO;AASX,WAAO,UAAU;AAEjB,QAAI,oBAAoB,MAAM;AAE9B,aAAS,UAAU,GAAG,UAAU;AAC9B,UAAI,EAAE,UAAU,KAAK,GAAG;AACtB,eAAO,CAAC;AAAA,MACV;AACA,UAAI,QAAQ,WAAW,GAAG,YAAY,iBAAiB;AACvD,UAAI,UAAU,YAAY,MAAM,OAAO,MAAM,SAAS,MAAM,OAAO;AAGnE,aAAO,QAAQ,QAAQ,OAAK,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;AAAA,IAClD;AAEA,aAAS,YAAY,GAAG,SAAS,SAAS;AACxC,UAAI,UAAU,CAAC;AACf,UAAI,UAAU,QAAQ,QAAQ,SAAS,CAAC;AACxC,UAAI,QAAQ,QAAQ,CAAC;AAErB,UAAI;AACJ,aAAO,EAAE,UAAU,GAAG;AACpB,eAAQ,QAAQ,MAAM,QAAQ,GAAM;AAAE,qBAAW,GAAG,SAAS,SAAS,KAAK;AAAA,QAAG;AAC9E,eAAQ,QAAQ,QAAQ,QAAQ,GAAI;AAAE,qBAAW,GAAG,SAAS,SAAS,KAAK;AAAA,QAAG;AAC9E,YAAI,EAAE,UAAU,GAAG;AACjB,mBAAS,IAAI,QAAQ,SAAS,GAAG,IAAI,GAAG,EAAE,GAAG;AAC3C,oBAAQ,QAAQ,CAAC,EAAE,QAAQ;AAC3B,gBAAI,OAAO;AACT,wBAAU,QAAQ,OAAO,WAAW,GAAG,SAAS,SAAS,OAAO,IAAI,CAAC;AACrE;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,GAAG,SAAS,SAAS,OAAO,qBAAqB;AACnE,UAAI,UAAU,sBAAsB,CAAC,IAAI;AAEzC,QAAE,QAAQ,MAAM,CAAC,EAAE,QAAQ,UAAQ;AACjC,YAAI,SAAS,EAAE,KAAK,IAAI;AACxB,YAAI,SAAS,EAAE,KAAK,KAAK,CAAC;AAE1B,YAAI,qBAAqB;AACvB,kBAAQ,KAAK,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,EAAE,CAAC;AAAA,QACvC;AAEA,eAAO,OAAO;AACd,qBAAa,SAAS,SAAS,MAAM;AAAA,MACvC,CAAC;AAED,QAAE,SAAS,MAAM,CAAC,EAAE,QAAQ,UAAQ;AAClC,YAAI,SAAS,EAAE,KAAK,IAAI;AACxB,YAAI,IAAI,KAAK;AACb,YAAI,SAAS,EAAE,KAAK,CAAC;AACrB,eAAO,IAAI,KAAK;AAChB,qBAAa,SAAS,SAAS,MAAM;AAAA,MACvC,CAAC;AAED,QAAE,WAAW,MAAM,CAAC;AAEpB,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,GAAG,UAAU;AAC/B,UAAI,WAAW,IAAI,MAAM;AACzB,UAAI,QAAQ;AACZ,UAAI,SAAS;AAEb,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,iBAAS,QAAQ,GAAG,EAAE,GAAM,MAAM,GAAG,KAAK,EAAE,CAAC;AAAA,MAC/C,CAAC;AAID,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,aAAa,SAAS,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK;AAC5C,YAAI,SAAS,SAAS,CAAC;AACvB,YAAI,aAAa,aAAa;AAC9B,iBAAS,QAAQ,EAAE,GAAG,EAAE,GAAG,UAAU;AACrC,iBAAS,KAAK,IAAI,QAAQ,SAAS,KAAK,EAAE,CAAC,EAAE,OAAO,MAAM;AAC1D,gBAAS,KAAK,IAAI,OAAQ,SAAS,KAAK,EAAE,CAAC,EAAE,IAAI,KAAM,MAAM;AAAA,MAC/D,CAAC;AAED,UAAI,UAAU,MAAM,SAAS,QAAQ,CAAC,EAAE,IAAI,MAAM,IAAI,KAAK,CAAC;AAC5D,UAAI,UAAU,QAAQ;AAEtB,eAAS,MAAM,EAAE,QAAQ,OAAK;AAC5B,qBAAa,SAAS,SAAS,SAAS,KAAK,CAAC,CAAC;AAAA,MACjD,CAAC;AAED,aAAO,EAAE,OAAO,UAAU,SAAkB,QAAiB;AAAA,IAC/D;AAEA,aAAS,aAAa,SAAS,SAAS,OAAO;AAC7C,UAAI,CAAC,MAAM,KAAK;AACd,gBAAQ,CAAC,EAAE,QAAQ,KAAK;AAAA,MAC1B,WAAW,CAAC,MAAM,IAAI,GAAG;AACvB,gBAAQ,QAAQ,SAAS,CAAC,EAAE,QAAQ,KAAK;AAAA,MAC3C,OAAO;AACL,gBAAQ,MAAM,MAAM,MAAM,IAAI,IAAI,OAAO,EAAE,QAAQ,KAAK;AAAA,MAC1D;AAAA,IACF;AAEA,aAAS,MAAM,OAAO;AACpB,YAAMC,SAAQ,CAAC;AACf,eAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,QAAAA,OAAM,KAAK,CAAC;AAAA,MACd;AAEA,aAAOA;AAAA,IACT;AAAA;AAAA;;;AC3HA;AAAA;AAAA;AAIA,QAAI,QAAQ,mBAA6B;AAEzC,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAKA,aAAS,aAAa,GAAG,MAAM,OAAO,MAAM;AAC1C,UAAI;AACJ,SAAG;AACD,YAAI,SAAS,IAAI;AAAA,MACnB,SAAS,EAAE,QAAQ,CAAC;AAEpB,YAAM,QAAQ;AACd,QAAE,QAAQ,GAAG,KAAK;AAClB,aAAO;AAAA,IACT;AAMA,aAAS,SAAS,GAAG;AACnB,UAAI,aAAa,IAAI,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC;AAC/C,QAAE,MAAM,EAAE,QAAQ,OAAK,WAAW,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AACvD,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,cAAc,WAAW,KAAK,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,QAAQ,GAAG,QAAQ,EAAE;AACtE,YAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,mBAAW,QAAQ,EAAE,GAAG,EAAE,GAAG;AAAA,UAC3B,QAAQ,YAAY,SAAS,MAAM;AAAA,UACnC,QAAQ,KAAK,IAAI,YAAY,QAAQ,MAAM,MAAM;AAAA,QACnD,CAAC;AAAA,MACH,CAAC;AACD,aAAO;AAAA,IACT;AAEA,aAAS,mBAAmB,GAAG;AAC7B,UAAI,aAAa,IAAI,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC;AAC/E,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ;AACzB,qBAAW,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;AAAA,QACjC;AAAA,MACF,CAAC;AACD,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,mBAAW,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;AAAA,MACjC,CAAC;AACD,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB,GAAG;AAC3B,UAAI,YAAY,EAAE,MAAM,EAAE,IAAI,OAAK;AACjC,YAAI,OAAO,CAAC;AACZ,UAAE,SAAS,CAAC,EAAE,QAAQ,OAAK;AACzB,eAAK,EAAE,CAAC,KAAK,KAAK,EAAE,CAAC,KAAK,KAAK,EAAE,KAAK,CAAC,EAAE;AAAA,QAC3C,CAAC;AACD,eAAO;AAAA,MACT,CAAC;AACD,aAAO,UAAU,EAAE,MAAM,GAAG,SAAS;AAAA,IACvC;AAEA,aAAS,mBAAmB,GAAG;AAC7B,UAAI,YAAY,EAAE,MAAM,EAAE,IAAI,OAAK;AACjC,YAAI,QAAQ,CAAC;AACb,UAAE,QAAQ,CAAC,EAAE,QAAQ,OAAK;AACxB,gBAAM,EAAE,CAAC,KAAK,MAAM,EAAE,CAAC,KAAK,KAAK,EAAE,KAAK,CAAC,EAAE;AAAA,QAC7C,CAAC;AACD,eAAO;AAAA,MACT,CAAC;AACD,aAAO,UAAU,EAAE,MAAM,GAAG,SAAS;AAAA,IACvC;AAMA,aAAS,cAAc,MAAM,OAAO;AAClC,UAAI,IAAI,KAAK;AACb,UAAI,IAAI,KAAK;AAIb,UAAI,KAAK,MAAM,IAAI;AACnB,UAAI,KAAK,MAAM,IAAI;AACnB,UAAI,IAAI,KAAK,QAAQ;AACrB,UAAI,IAAI,KAAK,SAAS;AAEtB,UAAI,CAAC,MAAM,CAAC,IAAI;AACd,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,UAAI,IAAI;AACR,UAAI,KAAK,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,GAAG;AAEvC,YAAI,KAAK,GAAG;AACV,cAAI,CAAC;AAAA,QACP;AACA,aAAK,IAAI,KAAK;AACd,aAAK;AAAA,MACP,OAAO;AAEL,YAAI,KAAK,GAAG;AACV,cAAI,CAAC;AAAA,QACP;AACA,aAAK;AACL,aAAK,IAAI,KAAK;AAAA,MAChB;AAEA,aAAO,EAAE,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG;AAAA,IAChC;AAMA,aAAS,iBAAiB,GAAG;AAC3B,UAAI,WAAW,MAAM,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,MAAM,CAAC,CAAC;AACjD,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,OAAO,KAAK;AAChB,YAAI,SAAS,QAAW;AACtB,mBAAS,IAAI,EAAE,KAAK,KAAK,IAAI;AAAA,QAC/B;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAMA,aAAS,eAAe,GAAG;AACzB,UAAI,YAAY,EAAE,MAAM,EAAE,IAAI,OAAK;AACjC,YAAI,OAAO,EAAE,KAAK,CAAC,EAAE;AACrB,YAAI,SAAS,QAAW;AACtB,iBAAO,OAAO;AAAA,QAChB;AAEA,eAAO;AAAA,MACT,CAAC;AACD,UAAI,MAAM,kBAAkB,KAAK,KAAK,SAAS;AAC/C,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,OAAO,OAAO,MAAM,MAAM,GAAG;AAC/B,eAAK,QAAQ;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,iBAAiB,GAAG;AAE3B,UAAI,YAAY,EAAE,MAAM,EAAE,IAAI,OAAK,EAAE,KAAK,CAAC,EAAE,IAAI;AACjD,UAAI,SAAS,kBAAkB,KAAK,KAAK,SAAS;AAElD,UAAI,SAAS,CAAC;AACd,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC,EAAE,OAAO;AAC5B,YAAI,CAAC,OAAO,IAAI,GAAG;AACjB,iBAAO,IAAI,IAAI,CAAC;AAAA,QAClB;AACA,eAAO,IAAI,EAAE,KAAK,CAAC;AAAA,MACrB,CAAC;AAED,UAAI,QAAQ;AACZ,UAAI,iBAAiB,EAAE,MAAM,EAAE;AAC/B,YAAM,KAAK,MAAM,EAAE,QAAQ,CAAC,IAAI,MAAM;AACpC,YAAI,OAAO,UAAa,IAAI,mBAAmB,GAAG;AAChD,YAAE;AAAA,QACJ,WAAW,OAAO,UAAa,OAAO;AACpC,aAAG,QAAQ,OAAK,EAAE,KAAK,CAAC,EAAE,QAAQ,KAAK;AAAA,QACzC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,cAAc,GAAG,QAAQ,MAAM,OAAO;AAC7C,UAAI,OAAO;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AACA,UAAI,UAAU,UAAU,GAAG;AACzB,aAAK,OAAO;AACZ,aAAK,QAAQ;AAAA,MACf;AACA,aAAO,aAAa,GAAG,UAAU,MAAM,MAAM;AAAA,IAC/C;AAEA,aAAS,cAAc,OAAO,YAAY,oBAAoB;AAC5D,YAAM,SAAS,CAAC;AAChB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,WAAW;AAChD,cAAM,QAAQ,MAAM,MAAM,GAAG,IAAI,SAAS;AAC1C,eAAO,KAAK,KAAK;AAAA,MACnB;AACA,aAAO;AAAA,IACT;AAEA,QAAM,qBAAqB;AAE3B,aAAS,kBAAkB,IAAI,WAAW;AACxC,UAAG,UAAU,SAAS,oBAAoB;AACxC,cAAM,SAAS,cAAc,SAAS;AACtC,eAAO,GAAG,MAAM,MAAM,OAAO,IAAI,WAAS,GAAG,MAAM,MAAM,KAAK,CAAC,CAAC;AAAA,MAClE,OAAO;AACL,eAAO,GAAG,MAAM,MAAM,SAAS;AAAA,MACjC;AAAA,IACF;AAEA,aAAS,QAAQ,GAAG;AAClB,YAAM,QAAQ,EAAE,MAAM;AACtB,YAAM,YAAY,MAAM,IAAI,OAAK;AAC/B,YAAI,OAAO,EAAE,KAAK,CAAC,EAAE;AACrB,YAAI,SAAS,QAAW;AACtB,iBAAO,OAAO;AAAA,QAChB;AACA,eAAO;AAAA,MACT,CAAC;AAED,aAAO,kBAAkB,KAAK,KAAK,SAAS;AAAA,IAC9C;AAOA,aAAS,UAAU,YAAY,IAAI;AACjC,UAAI,SAAS,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE;AAChC,iBAAW,QAAQ,WAAS;AAC1B,YAAI,GAAG,KAAK,GAAG;AACb,iBAAO,IAAI,KAAK,KAAK;AAAA,QACvB,OAAO;AACL,iBAAO,IAAI,KAAK,KAAK;AAAA,QACvB;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAMA,aAAS,KAAK,MAAM,IAAI;AACtB,UAAI,QAAQ,KAAK,IAAI;AACrB,UAAI;AACF,eAAO,GAAG;AAAA,MACZ,UAAE;AACA,gBAAQ,IAAI,OAAO,aAAa,KAAK,IAAI,IAAI,SAAS,IAAI;AAAA,MAC5D;AAAA,IACF;AAEA,aAAS,OAAO,MAAM,IAAI;AACxB,aAAO,GAAG;AAAA,IACZ;AAEA,QAAI,YAAY;AAChB,aAAS,SAAS,QAAQ;AACxB,UAAI,KAAK,EAAE;AACX,aAAO,SAAS,MAAM,IAAI;AAAA,IAC5B;AAEA,aAAS,MAAM,OAAO,OAAO,OAAO,GAAG;AACrC,UAAI,SAAS,MAAM;AACjB,gBAAQ;AACR,gBAAQ;AAAA,MACV;AAEA,UAAI,SAAS,CAAC,MAAM,IAAI;AACxB,UAAI,OAAO,GAAG;AACZ,iBAAS,CAAC,MAAM,QAAQ;AAAA,MAC1B;AAEA,YAAMC,SAAQ,CAAC;AACf,eAAS,IAAI,OAAO,OAAO,CAAC,GAAG,KAAK,MAAM;AACxC,QAAAA,OAAM,KAAK,CAAC;AAAA,MACd;AAEA,aAAOA;AAAA,IACT;AAEA,aAAS,KAAK,QAAQ,MAAM;AAC1B,YAAM,OAAO,CAAC;AACd,iBAAW,OAAO,MAAM;AACtB,YAAI,OAAO,GAAG,MAAM,QAAW;AAC7B,eAAK,GAAG,IAAI,OAAO,GAAG;AAAA,QACxB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,KAAK,YAAY;AAClC,UAAI,OAAO;AACX,UAAI,OAAO,eAAe,UAAU;AAClC,eAAO,CAAC,QAAQ,IAAI,UAAU;AAAA,MAChC;AAEA,aAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM;AACjD,YAAI,CAAC,IAAI,KAAK,GAAG,CAAC;AAClB,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP;AAEA,aAAS,UAAU,OAAO,QAAQ;AAChC,aAAO,MAAM,OAAO,CAAC,KAAK,KAAK,MAAM;AACnC,YAAI,GAAG,IAAI,OAAO,CAAC;AACnB,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AAAA,IACP;AAAA;AAAA;;;AC1UA;AAAA;AAAA;AAEA,QAAI,YAAY;AAChB,QAAI,WAAW,eAAkB;AAEjC,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAEA,aAAS,IAAI,GAAG;AACd,UAAI,MAAO,EAAE,MAAM,EAAE,cAAc,WAC/B,UAAU,GAAG,SAAS,CAAC,CAAC,IACxB,OAAO,CAAC;AACZ,UAAI,QAAQ,OAAK;AACf,YAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,UAAE,WAAW,CAAC;AACd,cAAM,cAAc,EAAE;AACtB,cAAM,WAAW;AACjB,UAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,OAAO,SAAS,KAAK,CAAC;AAAA,MAC5C,CAAC;AAED,eAAS,SAASC,IAAG;AACnB,eAAO,OAAK;AACV,iBAAOA,GAAE,KAAK,CAAC,EAAE;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAEA,aAAS,OAAO,GAAG;AACjB,UAAI,MAAM,CAAC;AACX,UAAI,QAAQ,CAAC;AACb,UAAI,UAAU,CAAC;AAEf,eAAS,IAAI,GAAG;AACd,YAAI,OAAO,OAAO,SAAS,CAAC,GAAG;AAC7B;AAAA,QACF;AACA,gBAAQ,CAAC,IAAI;AACb,cAAM,CAAC,IAAI;AACX,UAAE,SAAS,CAAC,EAAE,QAAQ,OAAK;AACzB,cAAI,OAAO,OAAO,OAAO,EAAE,CAAC,GAAG;AAC7B,gBAAI,KAAK,CAAC;AAAA,UACZ,OAAO;AACL,gBAAI,EAAE,CAAC;AAAA,UACT;AAAA,QACF,CAAC;AACD,eAAO,MAAM,CAAC;AAAA,MAChB;AAEA,QAAE,MAAM,EAAE,QAAQ,GAAG;AACrB,aAAO;AAAA,IACT;AAEA,aAAS,KAAK,GAAG;AACf,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,YAAI,MAAM,UAAU;AAClB,YAAE,WAAW,CAAC;AAEd,cAAI,cAAc,MAAM;AACxB,iBAAO,MAAM;AACb,iBAAO,MAAM;AACb,YAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,OAAO,WAAW;AAAA,QACxC;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;;;AClEA;AAAA;AAAA;AAEA,QAAI,OAAO;AAEX,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAkBA,aAAS,IAAI,GAAG;AACd,QAAE,MAAM,EAAE,cAAc,CAAC;AACzB,QAAE,MAAM,EAAE,QAAQ,UAAQ,cAAc,GAAG,IAAI,CAAC;AAAA,IAClD;AAEA,aAAS,cAAc,GAAG,GAAG;AAC3B,UAAI,IAAI,EAAE;AACV,UAAI,QAAQ,EAAE,KAAK,CAAC,EAAE;AACtB,UAAI,IAAI,EAAE;AACV,UAAI,QAAQ,EAAE,KAAK,CAAC,EAAE;AACtB,UAAI,OAAO,EAAE;AACb,UAAI,YAAY,EAAE,KAAK,CAAC;AACxB,UAAI,YAAY,UAAU;AAE1B,UAAI,UAAU,QAAQ,EAAG;AAEzB,QAAE,WAAW,CAAC;AAEd,UAAI,OAAO,OAAO;AAClB,WAAK,IAAI,GAAG,EAAE,OAAO,QAAQ,OAAO,EAAE,GAAG,EAAE,OAAO;AAChD,kBAAU,SAAS,CAAC;AACpB,gBAAQ;AAAA,UACN,OAAO;AAAA,UAAG,QAAQ;AAAA,UAClB;AAAA,UAAsB,SAAS;AAAA,UAC/B,MAAM;AAAA,QACR;AACA,gBAAQ,KAAK,aAAa,GAAG,QAAQ,OAAO,IAAI;AAChD,YAAI,UAAU,WAAW;AACvB,gBAAM,QAAQ,UAAU;AACxB,gBAAM,SAAS,UAAU;AACzB,gBAAM,QAAQ;AACd,gBAAM,WAAW,UAAU;AAAA,QAC7B;AACA,UAAE,QAAQ,GAAG,OAAO,EAAE,QAAQ,UAAU,OAAO,GAAG,IAAI;AACtD,YAAI,MAAM,GAAG;AACX,YAAE,MAAM,EAAE,YAAY,KAAK,KAAK;AAAA,QAClC;AACA,YAAI;AAAA,MACN;AAEA,QAAE,QAAQ,GAAG,GAAG,EAAE,QAAQ,UAAU,OAAO,GAAG,IAAI;AAAA,IACpD;AAEA,aAAS,KAAK,GAAG;AACf,QAAE,MAAM,EAAE,YAAY,QAAQ,OAAK;AACjC,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,YAAY,KAAK;AACrB,YAAI;AACJ,UAAE,QAAQ,KAAK,SAAS,SAAS;AACjC,eAAO,KAAK,OAAO;AACjB,cAAI,EAAE,WAAW,CAAC,EAAE,CAAC;AACrB,YAAE,WAAW,CAAC;AACd,oBAAU,OAAO,KAAK,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,EAAE,CAAC;AAC9C,cAAI,KAAK,UAAU,cAAc;AAC/B,sBAAU,IAAI,KAAK;AACnB,sBAAU,IAAI,KAAK;AACnB,sBAAU,QAAQ,KAAK;AACvB,sBAAU,SAAS,KAAK;AAAA,UAC1B;AACA,cAAI;AACJ,iBAAO,EAAE,KAAK,CAAC;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;;;ACxFA,IAAAC,gBAAA;AAAA;AAAA;AAEA,QAAM,EAAE,kBAAkB,IAAI;AAE9B,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAuBA,aAAS,YAAY,GAAG;AACtB,UAAI,UAAU,CAAC;AAEf,eAAS,IAAI,GAAG;AACd,YAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,YAAI,OAAO,OAAO,SAAS,CAAC,GAAG;AAC7B,iBAAO,MAAM;AAAA,QACf;AACA,gBAAQ,CAAC,IAAI;AAEb,YAAI,kBAAkB,EAAE,SAAS,CAAC,EAAE,IAAI,OAAK;AAC3C,cAAI,KAAK,MAAM;AACb,mBAAO,OAAO;AAAA,UAChB;AAEA,iBAAO,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;AAAA,QAC9B,CAAC;AAED,YAAI,OAAO,kBAAkB,KAAK,KAAK,eAAe;AAEtD,YAAI,SAAS,OAAO,mBAAmB;AACrC,iBAAO;AAAA,QACT;AAEA,eAAQ,MAAM,OAAO;AAAA,MACvB;AAEA,QAAE,QAAQ,EAAE,QAAQ,GAAG;AAAA,IACzB;AAMA,aAAS,MAAM,GAAG,GAAG;AACnB,aAAO,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE;AAAA,IACzD;AAAA;AAAA;;;AClEA;AAAA;AAAA;AAEA,QAAI,QAAQ,mBAA6B;AACzC,QAAI,QAAQ,gBAAkB;AAE9B,WAAO,UAAU;AA2BjB,aAAS,aAAa,GAAG;AACvB,UAAI,IAAI,IAAI,MAAM,EAAE,UAAU,MAAM,CAAC;AAGrC,UAAI,QAAQ,EAAE,MAAM,EAAE,CAAC;AACvB,UAAI,OAAO,EAAE,UAAU;AACvB,QAAE,QAAQ,OAAO,CAAC,CAAC;AAEnB,UAAI,MAAM;AACV,aAAO,UAAU,GAAG,CAAC,IAAI,MAAM;AAC7B,eAAO,iBAAiB,GAAG,CAAC;AAC5B,gBAAQ,EAAE,QAAQ,KAAK,CAAC,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI;AAC3D,mBAAW,GAAG,GAAG,KAAK;AAAA,MACxB;AAEA,aAAO;AAAA,IACT;AAMA,aAAS,UAAU,GAAG,GAAG;AACvB,eAAS,IAAI,GAAG;AACd,UAAE,UAAU,CAAC,EAAE,QAAQ,OAAK;AAC1B,cAAI,QAAQ,EAAE,GACZ,IAAK,MAAM,QAAS,EAAE,IAAI;AAC5B,cAAI,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG;AACjC,cAAE,QAAQ,GAAG,CAAC,CAAC;AACf,cAAE,QAAQ,GAAG,GAAG,CAAC,CAAC;AAClB,gBAAI,CAAC;AAAA,UACP;AAAA,QACF,CAAC;AAAA,MACH;AAEA,QAAE,MAAM,EAAE,QAAQ,GAAG;AACrB,aAAO,EAAE,UAAU;AAAA,IACrB;AAMA,aAAS,iBAAiB,GAAG,GAAG;AAC9B,YAAM,QAAQ,EAAE,MAAM;AAEtB,aAAO,MAAM,OAAO,CAAC,KAAK,SAAS;AACjC,YAAI,YAAY,OAAO;AACvB,YAAI,EAAE,QAAQ,KAAK,CAAC,MAAM,EAAE,QAAQ,KAAK,CAAC,GAAG;AAC3C,sBAAY,MAAM,GAAG,IAAI;AAAA,QAC3B;AAEA,YAAI,YAAY,IAAI,CAAC,GAAG;AACtB,iBAAO,CAAC,WAAW,IAAI;AAAA,QACzB;AAEA,eAAO;AAAA,MACT,GAAG,CAAC,OAAO,mBAAmB,IAAI,CAAC,EAAE,CAAC;AAAA,IACxC;AAEA,aAAS,WAAW,GAAG,GAAG,OAAO;AAC/B,QAAE,MAAM,EAAE,QAAQ,OAAK,EAAE,KAAK,CAAC,EAAE,QAAQ,KAAK;AAAA,IAChD;AAAA;AAAA;;;AC9FA;AAAA;AAAA;AAEA,QAAI,eAAe;AACnB,QAAI,QAAQ,gBAAkB;AAC9B,QAAI,WAAW,gBAAkB;AACjC,QAAI,WAAW,mBAA6B,IAAI;AAChD,QAAI,YAAY,mBAA6B,IAAI;AACjD,QAAI,WAAW,eAAmB;AAElC,WAAO,UAAU;AAGjB,mBAAe,mBAAmB;AAClC,mBAAe,gBAAgB;AAC/B,mBAAe,eAAe;AAC9B,mBAAe,YAAY;AAC3B,mBAAe,YAAY;AAC3B,mBAAe,gBAAgB;AAmC/B,aAAS,eAAe,GAAG;AACzB,UAAI,SAAS,CAAC;AACd,eAAS,CAAC;AACV,UAAI,IAAI,aAAa,CAAC;AACtB,uBAAiB,CAAC;AAClB,oBAAc,GAAG,CAAC;AAElB,UAAI,GAAG;AACP,aAAQ,IAAI,UAAU,CAAC,GAAI;AACzB,YAAI,UAAU,GAAG,GAAG,CAAC;AACrB,sBAAc,GAAG,GAAG,GAAG,CAAC;AAAA,MAC1B;AAAA,IACF;AAKA,aAAS,cAAc,GAAG,GAAG;AAC3B,UAAI,KAAK,UAAU,GAAG,EAAE,MAAM,CAAC;AAC/B,WAAK,GAAG,MAAM,GAAG,GAAG,SAAS,CAAC;AAC9B,SAAG,QAAQ,OAAK,eAAe,GAAG,GAAG,CAAC,CAAC;AAAA,IACzC;AAEA,aAAS,eAAe,GAAG,GAAG,OAAO;AACnC,UAAI,WAAW,EAAE,KAAK,KAAK;AAC3B,UAAI,SAAS,SAAS;AACtB,QAAE,KAAK,OAAO,MAAM,EAAE,WAAW,aAAa,GAAG,GAAG,KAAK;AAAA,IAC3D;AAMA,aAAS,aAAa,GAAG,GAAG,OAAO;AACjC,UAAI,WAAW,EAAE,KAAK,KAAK;AAC3B,UAAI,SAAS,SAAS;AAEtB,UAAI,cAAc;AAElB,UAAI,YAAY,EAAE,KAAK,OAAO,MAAM;AAEpC,UAAI,WAAW;AAEf,UAAI,CAAC,WAAW;AACd,sBAAc;AACd,oBAAY,EAAE,KAAK,QAAQ,KAAK;AAAA,MAClC;AAEA,iBAAW,UAAU;AAErB,QAAE,UAAU,KAAK,EAAE,QAAQ,OAAK;AAC9B,YAAI,YAAY,EAAE,MAAM,OACtB,QAAQ,YAAY,EAAE,IAAI,EAAE;AAE9B,YAAI,UAAU,QAAQ;AACpB,cAAI,eAAe,cAAc,aAC/B,cAAc,EAAE,KAAK,CAAC,EAAE;AAE1B,sBAAY,eAAe,cAAc,CAAC;AAC1C,cAAI,WAAW,GAAG,OAAO,KAAK,GAAG;AAC/B,gBAAI,gBAAgB,EAAE,KAAK,OAAO,KAAK,EAAE;AACzC,wBAAY,eAAe,CAAC,gBAAgB;AAAA,UAC9C;AAAA,QACF;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAEA,aAAS,iBAAiB,MAAM,MAAM;AACpC,UAAI,UAAU,SAAS,GAAG;AACxB,eAAO,KAAK,MAAM,EAAE,CAAC;AAAA,MACvB;AACA,sBAAgB,MAAM,CAAC,GAAG,GAAG,IAAI;AAAA,IACnC;AAEA,aAAS,gBAAgB,MAAM,SAAS,SAAS,GAAG,QAAQ;AAC1D,UAAI,MAAM;AACV,UAAI,QAAQ,KAAK,KAAK,CAAC;AAEvB,cAAQ,CAAC,IAAI;AACb,WAAK,UAAU,CAAC,EAAE,QAAQ,OAAK;AAC7B,YAAI,CAAC,OAAO,OAAO,SAAS,CAAC,GAAG;AAC9B,oBAAU,gBAAgB,MAAM,SAAS,SAAS,GAAG,CAAC;AAAA,QACxD;AAAA,MACF,CAAC;AAED,YAAM,MAAM;AACZ,YAAM,MAAM;AACZ,UAAI,QAAQ;AACV,cAAM,SAAS;AAAA,MACjB,OAAO;AAEL,eAAO,MAAM;AAAA,MACf;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,MAAM;AACvB,aAAO,KAAK,MAAM,EAAE,KAAK,OAAK,KAAK,KAAK,CAAC,EAAE,WAAW,CAAC;AAAA,IACzD;AAEA,aAAS,UAAU,GAAG,GAAG,MAAM;AAC7B,UAAI,IAAI,KAAK;AACb,UAAI,IAAI,KAAK;AAKb,UAAI,CAAC,EAAE,QAAQ,GAAG,CAAC,GAAG;AACpB,YAAI,KAAK;AACT,YAAI,KAAK;AAAA,MACX;AAEA,UAAI,SAAS,EAAE,KAAK,CAAC;AACrB,UAAI,SAAS,EAAE,KAAK,CAAC;AACrB,UAAI,YAAY;AAChB,UAAI,OAAO;AAIX,UAAI,OAAO,MAAM,OAAO,KAAK;AAC3B,oBAAY;AACZ,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,EAAE,MAAM,EAAE,OAAO,CAAAC,UAAQ;AACxC,eAAO,SAAS,aAAa,GAAG,EAAE,KAAKA,MAAK,CAAC,GAAG,SAAS,KAClD,SAAS,aAAa,GAAG,EAAE,KAAKA,MAAK,CAAC,GAAG,SAAS;AAAA,MAC3D,CAAC;AAED,aAAO,WAAW,OAAO,CAAC,KAAKA,UAAS;AACtC,YAAI,MAAM,GAAGA,KAAI,IAAI,MAAM,GAAG,GAAG,GAAG;AAClC,iBAAOA;AAAA,QACT;AAEA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,aAAS,cAAc,GAAG,GAAG,GAAG,GAAG;AACjC,UAAI,IAAI,EAAE;AACV,UAAI,IAAI,EAAE;AACV,QAAE,WAAW,GAAG,CAAC;AACjB,QAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACtB,uBAAiB,CAAC;AAClB,oBAAc,GAAG,CAAC;AAClB,kBAAY,GAAG,CAAC;AAAA,IAClB;AAEA,aAAS,YAAY,GAAG,GAAG;AACzB,UAAI,OAAO,EAAE,MAAM,EAAE,KAAK,OAAK,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM;AAChD,UAAI,KAAK,SAAS,GAAG,IAAI;AACzB,WAAK,GAAG,MAAM,CAAC;AACf,SAAG,QAAQ,OAAK;AACd,YAAI,SAAS,EAAE,KAAK,CAAC,EAAE,QACrB,OAAO,EAAE,KAAK,GAAG,MAAM,GACvB,UAAU;AAEZ,YAAI,CAAC,MAAM;AACT,iBAAO,EAAE,KAAK,QAAQ,CAAC;AACvB,oBAAU;AAAA,QACZ;AAEA,UAAE,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,MAAM,EAAE,QAAQ,UAAU,KAAK,SAAS,CAAC,KAAK;AAAA,MACxE,CAAC;AAAA,IACH;AAKA,aAAS,WAAW,MAAM,GAAG,GAAG;AAC9B,aAAO,KAAK,QAAQ,GAAG,CAAC;AAAA,IAC1B;AAMA,aAAS,aAAa,MAAM,QAAQ,WAAW;AAC7C,aAAO,UAAU,OAAO,OAAO,OAAO,OAAO,OAAO,UAAU;AAAA,IAChE;AAAA;AAAA;;;AC1OA;AAAA;AAAA;AAEA,QAAI,WAAW;AACf,QAAI,cAAc,SAAS;AAC3B,QAAI,eAAe;AACnB,QAAI,iBAAiB;AAErB,WAAO,UAAU;AAqBjB,aAAS,KAAK,GAAG;AACf,cAAO,EAAE,MAAM,EAAE,QAAQ;AAAA,QACzB,KAAK;AAAmB,+BAAqB,CAAC;AAAG;AAAA,QACjD,KAAK;AAAc,0BAAgB,CAAC;AAAG;AAAA,QACvC,KAAK;AAAgB,4BAAkB,CAAC;AAAG;AAAA,QAC3C;AAAS,+BAAqB,CAAC;AAAA,MAC/B;AAAA,IACF;AAGA,QAAI,oBAAoB;AAExB,aAAS,gBAAgB,GAAG;AAC1B,kBAAY,CAAC;AACb,mBAAa,CAAC;AAAA,IAChB;AAEA,aAAS,qBAAqB,GAAG;AAC/B,qBAAe,CAAC;AAAA,IAClB;AAAA;AAAA;;;AC/CA;AAAA;AAAA,WAAO,UAAU;AAEjB,aAAS,kBAAkB,GAAG;AAC5B,UAAI,gBAAgB,UAAU,CAAC;AAE/B,QAAE,MAAM,EAAE,YAAY,QAAQ,OAAK;AACjC,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,UAAU,KAAK;AACnB,YAAI,WAAW,SAAS,GAAG,eAAe,QAAQ,GAAG,QAAQ,CAAC;AAC9D,YAAI,OAAO,SAAS;AACpB,YAAI,MAAM,SAAS;AACnB,YAAI,UAAU;AACd,YAAI,QAAQ,KAAK,OAAO;AACxB,YAAI,YAAY;AAEhB,eAAO,MAAM,QAAQ,GAAG;AACtB,iBAAO,EAAE,KAAK,CAAC;AAEf,cAAI,WAAW;AACb,oBAAQ,QAAQ,KAAK,OAAO,OAAO,OAC5B,EAAE,KAAK,KAAK,EAAE,UAAU,KAAK,MAAM;AACxC;AAAA,YACF;AAEA,gBAAI,UAAU,KAAK;AACjB,0BAAY;AAAA,YACd;AAAA,UACF;AAEA,cAAI,CAAC,WAAW;AACd,mBAAO,UAAU,KAAK,SAAS,KACxB,EAAE,KAAK,QAAQ,KAAK,UAAU,CAAC,CAAC,EAAE,WAAW,KAAK,MAAM;AAC7D;AAAA,YACF;AACA,oBAAQ,KAAK,OAAO;AAAA,UACtB;AAEA,YAAE,UAAU,GAAG,KAAK;AACpB,cAAI,EAAE,WAAW,CAAC,EAAE,CAAC;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH;AAIA,aAAS,SAAS,GAAG,eAAe,GAAG,GAAG;AACxC,UAAI,QAAQ,CAAC;AACb,UAAI,QAAQ,CAAC;AACb,UAAI,MAAM,KAAK,IAAI,cAAc,CAAC,EAAE,KAAK,cAAc,CAAC,EAAE,GAAG;AAC7D,UAAI,MAAM,KAAK,IAAI,cAAc,CAAC,EAAE,KAAK,cAAc,CAAC,EAAE,GAAG;AAC7D,UAAI;AACJ,UAAI;AAGJ,eAAS;AACT,SAAG;AACD,iBAAS,EAAE,OAAO,MAAM;AACxB,cAAM,KAAK,MAAM;AAAA,MACnB,SAAS,WACC,cAAc,MAAM,EAAE,MAAM,OAAO,MAAM,cAAc,MAAM,EAAE;AACzE,YAAM;AAGN,eAAS;AACT,cAAQ,SAAS,EAAE,OAAO,MAAM,OAAO,KAAK;AAC1C,cAAM,KAAK,MAAM;AAAA,MACnB;AAEA,aAAO,EAAE,MAAM,MAAM,OAAO,MAAM,QAAQ,CAAC,GAAG,IAAS;AAAA,IACzD;AAEA,aAAS,UAAU,GAAG;AACpB,UAAI,SAAS,CAAC;AACd,UAAI,MAAM;AAEV,eAAS,IAAI,GAAG;AACd,YAAI,MAAM;AACV,UAAE,SAAS,CAAC,EAAE,QAAQ,GAAG;AACzB,eAAO,CAAC,IAAI,EAAE,KAAU,KAAK,MAAM;AAAA,MACrC;AACA,QAAE,SAAS,EAAE,QAAQ,GAAG;AAExB,aAAO;AAAA,IACT;AAAA;AAAA;;;ACnFA;AAAA;AAAA,QAAI,OAAO;AAEX,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAyBA,aAAS,IAAI,GAAG;AACd,UAAI,OAAO,KAAK,aAAa,GAAG,QAAQ,CAAC,GAAG,OAAO;AACnD,UAAI,SAAS,WAAW,CAAC;AACzB,UAAI,YAAY,OAAO,OAAO,MAAM;AACpC,UAAI,SAAS,KAAK,kBAAkB,KAAK,KAAK,SAAS,IAAI;AAC3D,UAAI,UAAU,IAAI,SAAS;AAE3B,QAAE,MAAM,EAAE,cAAc;AAGxB,QAAE,MAAM,EAAE,QAAQ,OAAK,EAAE,KAAK,CAAC,EAAE,UAAU,OAAO;AAGlD,UAAI,SAAS,WAAW,CAAC,IAAI;AAG7B,QAAE,SAAS,EAAE,QAAQ,WAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ,KAAK,CAAC;AAIlF,QAAE,MAAM,EAAE,iBAAiB;AAAA,IAC7B;AAEA,aAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ,GAAG;AACxD,UAAI,WAAW,EAAE,SAAS,CAAC;AAC3B,UAAI,CAAC,SAAS,QAAQ;AACpB,YAAI,MAAM,MAAM;AACd,YAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,GAAG,QAAQ,QAAQ,CAAC;AAAA,QACnD;AACA;AAAA,MACF;AAEA,UAAI,MAAM,KAAK,cAAc,GAAG,KAAK;AACrC,UAAI,SAAS,KAAK,cAAc,GAAG,KAAK;AACxC,UAAI,QAAQ,EAAE,KAAK,CAAC;AAEpB,QAAE,UAAU,KAAK,CAAC;AAClB,YAAM,YAAY;AAClB,QAAE,UAAU,QAAQ,CAAC;AACrB,YAAM,eAAe;AAErB,eAAS,QAAQ,WAAS;AACxB,YAAI,GAAG,MAAM,SAAS,QAAQ,QAAQ,QAAQ,KAAK;AAEnD,YAAI,YAAY,EAAE,KAAK,KAAK;AAC5B,YAAI,WAAW,UAAU,YAAY,UAAU,YAAY;AAC3D,YAAI,cAAc,UAAU,eAAe,UAAU,eAAe;AACpE,YAAI,aAAa,UAAU,YAAY,SAAS,IAAI;AACpD,YAAI,SAAS,aAAa,cAAc,IAAI,SAAS,OAAO,CAAC,IAAI;AAEjE,UAAE,QAAQ,KAAK,UAAU;AAAA,UACvB,QAAQ;AAAA,UACR;AAAA,UACA,aAAa;AAAA,QACf,CAAC;AAED,UAAE,QAAQ,aAAa,QAAQ;AAAA,UAC7B,QAAQ;AAAA,UACR;AAAA,UACA,aAAa;AAAA,QACf,CAAC;AAAA,MACH,CAAC;AAED,UAAI,CAAC,EAAE,OAAO,CAAC,GAAG;AAChB,UAAE,QAAQ,MAAM,KAAK,EAAE,QAAQ,GAAG,QAAQ,SAAS,OAAO,CAAC,EAAE,CAAC;AAAA,MAChE;AAAA,IACF;AAEA,aAAS,WAAW,GAAG;AACrB,UAAI,SAAS,CAAC;AACd,eAASC,KAAI,GAAG,OAAO;AACrB,YAAI,WAAW,EAAE,SAAS,CAAC;AAC3B,YAAI,YAAY,SAAS,QAAQ;AAC/B,mBAAS,QAAQ,WAASA,KAAI,OAAO,QAAQ,CAAC,CAAC;AAAA,QACjD;AACA,eAAO,CAAC,IAAI;AAAA,MACd;AACA,QAAE,SAAS,EAAE,QAAQ,OAAKA,KAAI,GAAG,CAAC,CAAC;AACnC,aAAO;AAAA,IACT;AAEA,aAAS,WAAW,GAAG;AACrB,aAAO,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,KAAK,CAAC,EAAE,QAAQ,CAAC;AAAA,IAC/D;AAEA,aAAS,QAAQ,GAAG;AAClB,UAAI,aAAa,EAAE,MAAM;AACzB,QAAE,WAAW,WAAW,WAAW;AACnC,aAAO,WAAW;AAClB,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,KAAK,aAAa;AACpB,YAAE,WAAW,CAAC;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;;;AC7HA;AAAA;AAAA,QAAI,OAAO;AAEX,WAAO,UAAU;AAEjB,aAAS,kBAAkB,GAAG;AAC5B,eAAS,IAAI,GAAG;AACd,YAAI,WAAW,EAAE,SAAS,CAAC;AAC3B,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,SAAS,QAAQ;AACnB,mBAAS,QAAQ,GAAG;AAAA,QACtB;AAEA,YAAI,OAAO,OAAO,MAAM,SAAS,GAAG;AAClC,eAAK,aAAa,CAAC;AACnB,eAAK,cAAc,CAAC;AACpB,mBAAS,OAAO,KAAK,SAAS,UAAU,KAAK,UAAU,GACrD,OAAO,SACP,EAAE,MAAM;AACR,0BAAc,GAAG,cAAc,OAAO,GAAG,MAAM,IAAI;AACnD,0BAAc,GAAG,eAAe,OAAO,GAAG,MAAM,IAAI;AAAA,UACtD;AAAA,QACF;AAAA,MACF;AAEA,QAAE,SAAS,EAAE,QAAQ,GAAG;AAAA,IAC1B;AAEA,aAAS,cAAc,GAAG,MAAM,QAAQ,IAAI,QAAQ,MAAM;AACxD,UAAI,QAAQ,EAAE,OAAO,GAAG,QAAQ,GAAG,MAAY,YAAY,KAAK;AAChE,UAAI,OAAO,OAAO,IAAI,EAAE,OAAO,CAAC;AAChC,UAAI,OAAO,KAAK,aAAa,GAAG,UAAU,OAAO,MAAM;AACvD,aAAO,IAAI,EAAE,IAAI,IAAI;AACrB,QAAE,UAAU,MAAM,EAAE;AACpB,UAAI,MAAM;AACR,UAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,EAAE,CAAC;AAAA,MACrC;AAAA,IACF;AAAA;AAAA;;;ACpCA;AAAA;AAAA;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,IACF;AAEA,aAAS,OAAO,GAAG;AACjB,UAAI,UAAU,EAAE,MAAM,EAAE,QAAQ,YAAY;AAC5C,UAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,wBAAgB,CAAC;AAAA,MACnB;AAAA,IACF;AAEA,aAAS,KAAK,GAAG;AACf,UAAI,UAAU,EAAE,MAAM,EAAE,QAAQ,YAAY;AAC5C,UAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,iBAAS,CAAC;AAAA,MACZ;AAEA,UAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,eAAO,CAAC;AACR,wBAAgB,CAAC;AAAA,MACnB;AAAA,IACF;AAEA,aAAS,gBAAgB,GAAG;AAC1B,QAAE,MAAM,EAAE,QAAQ,OAAK,mBAAmB,EAAE,KAAK,CAAC,CAAC,CAAC;AACpD,QAAE,MAAM,EAAE,QAAQ,OAAK,mBAAmB,EAAE,KAAK,CAAC,CAAC,CAAC;AAAA,IACtD;AAEA,aAAS,mBAAmB,OAAO;AACjC,UAAI,IAAI,MAAM;AACd,YAAM,QAAQ,MAAM;AACpB,YAAM,SAAS;AAAA,IACjB;AAEA,aAAS,SAAS,GAAG;AACnB,QAAE,MAAM,EAAE,QAAQ,OAAK,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC;AAE7C,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,aAAK,OAAO,QAAQ,WAAW;AAC/B,YAAI,OAAO,OAAO,MAAM,GAAG,GAAG;AAC5B,sBAAY,IAAI;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,YAAY,OAAO;AAC1B,YAAM,IAAI,CAAC,MAAM;AAAA,IACnB;AAEA,aAAS,OAAO,GAAG;AACjB,QAAE,MAAM,EAAE,QAAQ,OAAK,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;AAE3C,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,aAAK,OAAO,QAAQ,SAAS;AAC7B,YAAI,OAAO,OAAO,MAAM,GAAG,GAAG;AAC5B,oBAAU,IAAI;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,UAAU,OAAO;AACxB,UAAI,IAAI,MAAM;AACd,YAAM,IAAI,MAAM;AAChB,YAAM,IAAI;AAAA,IACZ;AAAA;AAAA;;;ACrEA;AAAA;AAAA;AAEA,QAAI,OAAO;AAEX,WAAO,UAAU;AAajB,aAAS,UAAU,GAAG;AACpB,UAAI,UAAU,CAAC;AACf,UAAI,cAAc,EAAE,MAAM,EAAE,OAAO,OAAK,CAAC,EAAE,SAAS,CAAC,EAAE,MAAM;AAC7D,UAAI,mBAAmB,YAAY,IAAI,OAAK,EAAE,KAAK,CAAC,EAAE,IAAI;AAC1D,UAAI,UAAU,KAAK,kBAAkB,KAAK,KAAK,gBAAgB;AAC/D,UAAI,SAAS,KAAK,MAAM,UAAU,CAAC,EAAE,IAAI,MAAM,CAAC,CAAC;AAEjD,eAAS,IAAI,GAAG;AACd,YAAI,QAAQ,CAAC,EAAG;AAChB,gBAAQ,CAAC,IAAI;AACb,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,eAAO,KAAK,IAAI,EAAE,KAAK,CAAC;AACxB,UAAE,WAAW,CAAC,EAAE,QAAQ,GAAG;AAAA,MAC7B;AAEA,UAAI,YAAY,YAAY,KAAK,CAAC,GAAG,MAAM,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE,IAAI;AAC1E,gBAAU,QAAQ,GAAG;AAErB,aAAO;AAAA,IACT;AAAA;AAAA;;;ACpCA;AAAA;AAAA;AAEA,QAAI,YAAY,eAAmB;AAEnC,WAAO,UAAU;AAkBjB,aAAS,WAAW,GAAG,UAAU;AAC/B,UAAI,KAAK;AACT,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,EAAE,GAAG;AACxC,cAAM,mBAAmB,GAAG,SAAS,IAAE,CAAC,GAAG,SAAS,CAAC,CAAC;AAAA,MACxD;AACA,aAAO;AAAA,IACT;AAEA,aAAS,mBAAmB,GAAG,YAAY,YAAY;AAIrD,UAAI,WAAW,UAAU,YAAY,WAAW,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AAChE,UAAI,eAAe,WAAW,QAAQ,OAAK;AACzC,eAAO,EAAE,SAAS,CAAC,EAAE,IAAI,OAAK;AAC5B,iBAAO,EAAE,KAAK,SAAS,EAAE,CAAC,GAAG,QAAQ,EAAE,KAAK,CAAC,EAAE,OAAO;AAAA,QACxD,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG;AAAA,MACjC,CAAC;AAGD,UAAI,aAAa;AACjB,aAAO,aAAa,WAAW,OAAQ,gBAAe;AACtD,UAAI,WAAW,IAAI,aAAa;AAChC,oBAAc;AACd,UAAI,OAAO,IAAI,MAAM,QAAQ,EAAE,KAAK,CAAC;AAGrC,UAAI,KAAK;AACT,mBAAa,QAAQ,WAAS;AAC5B,YAAI,QAAQ,MAAM,MAAM;AACxB,aAAK,KAAK,KAAK,MAAM;AACrB,YAAI,YAAY;AAChB,eAAO,QAAQ,GAAG;AAChB,cAAI,QAAQ,GAAG;AACb,yBAAa,KAAK,QAAQ,CAAC;AAAA,UAC7B;AACA,kBAAS,QAAQ,KAAM;AACvB,eAAK,KAAK,KAAK,MAAM;AAAA,QACvB;AACA,cAAM,MAAM,SAAS;AAAA,MACvB,CAAC;AAED,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjEA;AAAA;AAAA,WAAO,UAAU;AAEjB,aAAS,WAAW,GAAG,UAAU,CAAC,GAAG;AACnC,aAAO,QAAQ,IAAI,OAAK;AACtB,YAAI,MAAM,EAAE,QAAQ,CAAC;AACrB,YAAI,CAAC,IAAI,QAAQ;AACf,iBAAO,EAAE,EAAK;AAAA,QAChB,OAAO;AACL,cAAI,SAAS,IAAI,OAAO,CAAC,KAAK,MAAM;AAClC,gBAAI,OAAO,EAAE,KAAK,CAAC,GACjB,QAAQ,EAAE,KAAK,EAAE,CAAC;AACpB,mBAAO;AAAA,cACL,KAAK,IAAI,MAAO,KAAK,SAAS,MAAM;AAAA,cACpC,QAAQ,IAAI,SAAS,KAAK;AAAA,YAC5B;AAAA,UACF,GAAG,EAAE,KAAK,GAAG,QAAQ,EAAE,CAAC;AAExB,iBAAO;AAAA,YACL;AAAA,YACA,YAAY,OAAO,MAAM,OAAO;AAAA,YAChC,QAAQ,OAAO;AAAA,UACjB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;;;ACxBA;AAAA;AAAA;AAEA,QAAI,OAAO;AAEX,WAAO,UAAU;AA2BjB,aAAS,iBAAiB,SAAS,IAAI;AACrC,UAAI,gBAAgB,CAAC;AACrB,cAAQ,QAAQ,CAAC,OAAO,MAAM;AAC5B,YAAI,MAAM,cAAc,MAAM,CAAC,IAAI;AAAA,UACjC,UAAU;AAAA,UACV,MAAM,CAAC;AAAA,UACP,KAAK,CAAC;AAAA,UACN,IAAI,CAAC,MAAM,CAAC;AAAA,UACZ;AAAA,QACF;AACA,YAAI,MAAM,eAAe,QAAW;AAClC,cAAI,aAAa,MAAM;AACvB,cAAI,SAAS,MAAM;AAAA,QACrB;AAAA,MACF,CAAC;AAED,SAAG,MAAM,EAAE,QAAQ,OAAK;AACtB,YAAI,SAAS,cAAc,EAAE,CAAC;AAC9B,YAAI,SAAS,cAAc,EAAE,CAAC;AAC9B,YAAI,WAAW,UAAa,WAAW,QAAW;AAChD,iBAAO;AACP,iBAAO,IAAI,KAAK,cAAc,EAAE,CAAC,CAAC;AAAA,QACpC;AAAA,MACF,CAAC;AAED,UAAI,YAAY,OAAO,OAAO,aAAa,EAAE,OAAO,WAAS,CAAC,MAAM,QAAQ;AAE5E,aAAO,mBAAmB,SAAS;AAAA,IACrC;AAEA,aAAS,mBAAmB,WAAW;AACrC,UAAI,UAAU,CAAC;AAEf,eAAS,SAAS,QAAQ;AACxB,eAAO,YAAU;AACf,cAAI,OAAO,QAAQ;AACjB;AAAA,UACF;AACA,cAAI,OAAO,eAAe,UACtB,OAAO,eAAe,UACtB,OAAO,cAAc,OAAO,YAAY;AAC1C,yBAAa,QAAQ,MAAM;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AAEA,eAAS,UAAU,QAAQ;AACzB,eAAO,YAAU;AACf,iBAAO,IAAI,EAAE,KAAK,MAAM;AACxB,cAAI,EAAE,OAAO,aAAa,GAAG;AAC3B,sBAAU,KAAK,MAAM;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAEA,aAAO,UAAU,QAAQ;AACvB,YAAI,QAAQ,UAAU,IAAI;AAC1B,gBAAQ,KAAK,KAAK;AAClB,cAAM,IAAI,EAAE,QAAQ,EAAE,QAAQ,SAAS,KAAK,CAAC;AAC7C,cAAM,IAAI,QAAQ,UAAU,KAAK,CAAC;AAAA,MACpC;AAEA,aAAO,QAAQ,OAAO,WAAS,CAAC,MAAM,MAAM,EAAE,IAAI,WAAS;AACzD,eAAO,KAAK,KAAK,OAAO,CAAC,MAAM,KAAK,cAAc,QAAQ,CAAC;AAAA,MAC7D,CAAC;AAAA,IACH;AAEA,aAAS,aAAa,QAAQ,QAAQ;AACpC,UAAI,MAAM;AACV,UAAI,SAAS;AAEb,UAAI,OAAO,QAAQ;AACjB,eAAO,OAAO,aAAa,OAAO;AAClC,kBAAU,OAAO;AAAA,MACnB;AAEA,UAAI,OAAO,QAAQ;AACjB,eAAO,OAAO,aAAa,OAAO;AAClC,kBAAU,OAAO;AAAA,MACnB;AAEA,aAAO,KAAK,OAAO,GAAG,OAAO,OAAO,EAAE;AACtC,aAAO,aAAa,MAAM;AAC1B,aAAO,SAAS;AAChB,aAAO,IAAI,KAAK,IAAI,OAAO,GAAG,OAAO,CAAC;AACtC,aAAO,SAAS;AAAA,IAClB;AAAA;AAAA;;;ACrHA;AAAA;AAAA,QAAI,OAAO;AAEX,WAAO,UAAU;AAEjB,aAAS,KAAK,SAAS,WAAW;AAChC,UAAI,QAAQ,KAAK,UAAU,SAAS,WAAS;AAC3C,eAAO,OAAO,OAAO,OAAO,YAAY;AAAA,MAC1C,CAAC;AACD,UAAI,WAAW,MAAM,KACnB,aAAa,MAAM,IAAI,KAAK,CAAC,GAAG,MAAM,EAAE,IAAI,EAAE,CAAC,GAC/C,KAAK,CAAC,GACN,MAAM,GACN,SAAS,GACT,UAAU;AAEZ,eAAS,KAAK,gBAAgB,CAAC,CAAC,SAAS,CAAC;AAE1C,gBAAU,kBAAkB,IAAI,YAAY,OAAO;AAEnD,eAAS,QAAQ,WAAS;AACxB,mBAAW,MAAM,GAAG;AACpB,WAAG,KAAK,MAAM,EAAE;AAChB,eAAO,MAAM,aAAa,MAAM;AAChC,kBAAU,MAAM;AAChB,kBAAU,kBAAkB,IAAI,YAAY,OAAO;AAAA,MACrD,CAAC;AAED,UAAI,SAAS,EAAE,IAAI,GAAG,KAAK,IAAI,EAAE;AACjC,UAAI,QAAQ;AACV,eAAO,aAAa,MAAM;AAC1B,eAAO,SAAS;AAAA,MAClB;AACA,aAAO;AAAA,IACT;AAEA,aAAS,kBAAkB,IAAI,YAAY,OAAO;AAChD,UAAI;AACJ,aAAO,WAAW,WAAW,OAAO,WAAW,WAAW,SAAS,CAAC,GAAG,KAAK,OAAO;AACjF,mBAAW,IAAI;AACf,WAAG,KAAK,KAAK,EAAE;AACf;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,gBAAgB,MAAM;AAC7B,aAAO,CAAC,QAAQ,WAAW;AACzB,YAAI,OAAO,aAAa,OAAO,YAAY;AACzC,iBAAO;AAAA,QACT,WAAW,OAAO,aAAa,OAAO,YAAY;AAChD,iBAAO;AAAA,QACT;AAEA,eAAO,CAAC,OAAO,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,OAAO;AAAA,MACzD;AAAA,IACF;AAAA;AAAA;;;ACvDA;AAAA;AAAA,QAAI,aAAa;AACjB,QAAI,mBAAmB;AACvB,QAAI,OAAO;AAEX,WAAO,UAAU;AAEjB,aAAS,aAAa,GAAG,GAAG,IAAI,WAAW;AACzC,UAAI,UAAU,EAAE,SAAS,CAAC;AAC1B,UAAI,OAAO,EAAE,KAAK,CAAC;AACnB,UAAI,KAAK,OAAO,KAAK,aAAa;AAClC,UAAI,KAAK,OAAO,KAAK,cAAa;AAClC,UAAI,YAAY,CAAC;AAEjB,UAAI,IAAI;AACN,kBAAU,QAAQ,OAAO,OAAK,MAAM,MAAM,MAAM,EAAE;AAAA,MACpD;AAEA,UAAI,cAAc,WAAW,GAAG,OAAO;AACvC,kBAAY,QAAQ,WAAS;AAC3B,YAAI,EAAE,SAAS,MAAM,CAAC,EAAE,QAAQ;AAC9B,cAAI,iBAAiB,aAAa,GAAG,MAAM,GAAG,IAAI,SAAS;AAC3D,oBAAU,MAAM,CAAC,IAAI;AACrB,cAAI,OAAO,OAAO,gBAAgB,YAAY,GAAG;AAC/C,6BAAiB,OAAO,cAAc;AAAA,UACxC;AAAA,QACF;AAAA,MACF,CAAC;AAED,UAAI,UAAU,iBAAiB,aAAa,EAAE;AAC9C,sBAAgB,SAAS,SAAS;AAElC,UAAI,SAAS,KAAK,SAAS,SAAS;AAEpC,UAAI,IAAI;AACN,eAAO,KAAK,CAAC,IAAI,OAAO,IAAI,EAAE,EAAE,KAAK,IAAI;AACzC,YAAI,EAAE,aAAa,EAAE,EAAE,QAAQ;AAC7B,cAAI,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC,GACvC,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;AACvC,cAAI,CAAC,OAAO,OAAO,QAAQ,YAAY,GAAG;AACxC,mBAAO,aAAa;AACpB,mBAAO,SAAS;AAAA,UAClB;AACA,iBAAO,cAAc,OAAO,aAAa,OAAO,SAC3B,OAAO,QAAQ,OAAO,UAAU,OAAO,SAAS;AACrE,iBAAO,UAAU;AAAA,QACnB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,gBAAgB,SAAS,WAAW;AAC3C,cAAQ,QAAQ,WAAS;AACvB,cAAM,KAAK,MAAM,GAAG,QAAQ,OAAK;AAC/B,cAAI,UAAU,CAAC,GAAG;AAChB,mBAAO,UAAU,CAAC,EAAE;AAAA,UACtB;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,aAAS,iBAAiB,QAAQ,OAAO;AACvC,UAAI,OAAO,eAAe,QAAW;AACnC,eAAO,cAAc,OAAO,aAAa,OAAO,SAC3B,MAAM,aAAa,MAAM,WACzB,OAAO,SAAS,MAAM;AAC3C,eAAO,UAAU,MAAM;AAAA,MACzB,OAAO;AACL,eAAO,aAAa,MAAM;AAC1B,eAAO,SAAS,MAAM;AAAA,MACxB;AAAA,IACF;AAAA;AAAA;;;ACxEA;AAAA;AAAA,QAAI,QAAQ,mBAA6B;AACzC,QAAI,OAAO;AAEX,WAAO,UAAU;AAgCjB,aAAS,gBAAgB,GAAG,MAAM,cAAc;AAC9C,UAAI,OAAO,eAAe,CAAC,GACzB,SAAS,IAAI,MAAM,EAAE,UAAU,KAAK,CAAC,EAAE,SAAS,EAAE,KAAW,CAAC,EAC3D,oBAAoB,OAAK,EAAE,KAAK,CAAC,CAAC;AAEvC,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC,GACjB,SAAS,EAAE,OAAO,CAAC;AAErB,YAAI,KAAK,SAAS,QAAQ,KAAK,WAAW,QAAQ,QAAQ,KAAK,SAAS;AACtE,iBAAO,QAAQ,CAAC;AAChB,iBAAO,UAAU,GAAG,UAAU,IAAI;AAGlC,YAAE,YAAY,EAAE,CAAC,EAAE,QAAQ,OAAK;AAC9B,gBAAI,IAAI,EAAE,MAAM,IAAI,EAAE,IAAI,EAAE,GAC1B,OAAO,OAAO,KAAK,GAAG,CAAC,GACvB,SAAS,SAAS,SAAY,KAAK,SAAS;AAC9C,mBAAO,QAAQ,GAAG,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,SAAS,OAAO,CAAC;AAAA,UAC5D,CAAC;AAED,cAAI,OAAO,OAAO,MAAM,SAAS,GAAG;AAClC,mBAAO,QAAQ,GAAG;AAAA,cAChB,YAAY,KAAK,WAAW,IAAI;AAAA,cAChC,aAAa,KAAK,YAAY,IAAI;AAAA,YACpC,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAEA,aAAS,eAAe,GAAG;AACzB,UAAI;AACJ,aAAO,EAAE,QAAS,IAAI,KAAK,SAAS,OAAO,CAAE,EAAE;AAC/C,aAAO;AAAA,IACT;AAAA;AAAA;;;ACxEA;AAAA;AAAA,WAAO,UAAU;AAEjB,aAAS,uBAAuB,GAAG,IAAI,IAAI;AACzC,UAAI,OAAO,CAAC,GACV;AAEF,SAAG,QAAQ,OAAK;AACd,YAAI,QAAQ,EAAE,OAAO,CAAC,GACpB,QACA;AACF,eAAO,OAAO;AACZ,mBAAS,EAAE,OAAO,KAAK;AACvB,cAAI,QAAQ;AACV,wBAAY,KAAK,MAAM;AACvB,iBAAK,MAAM,IAAI;AAAA,UACjB,OAAO;AACL,wBAAY;AACZ,uBAAW;AAAA,UACb;AACA,cAAI,aAAa,cAAc,OAAO;AACpC,eAAG,QAAQ,WAAW,KAAK;AAC3B;AAAA,UACF;AACA,kBAAQ;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IAyBH;AAAA;AAAA;;;AClDA;AAAA;AAAA;AAEA,QAAI,YAAY;AAChB,QAAI,aAAa;AACjB,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAI,yBAAyB;AAC7B,QAAI,QAAQ,mBAA6B;AACzC,QAAI,OAAO;AAEX,WAAO,UAAU;AAiBjB,aAAS,MAAM,GAAG,MAAM;AACtB,UAAI,QAAQ,OAAO,KAAK,gBAAgB,YAAY;AAClD,aAAK,YAAY,GAAG,KAAK;AACzB;AAAA,MACF;AAEA,UAAI,UAAU,KAAK,QAAQ,CAAC,GAC1B,kBAAkB,iBAAiB,GAAG,KAAK,MAAM,GAAG,UAAU,CAAC,GAAG,SAAS,GAC3E,gBAAgB,iBAAiB,GAAG,KAAK,MAAM,UAAU,GAAG,IAAI,EAAE,GAAG,UAAU;AAEjF,UAAI,WAAW,UAAU,CAAC;AAC1B,kBAAY,GAAG,QAAQ;AAEvB,UAAI,QAAQ,KAAK,8BAA8B;AAC7C;AAAA,MACF;AAEA,UAAI,SAAS,OAAO,mBAClB;AAEF,eAAS,IAAI,GAAG,WAAW,GAAG,WAAW,GAAG,EAAE,GAAG,EAAE,UAAU;AAC3D,yBAAiB,IAAI,IAAI,kBAAkB,eAAe,IAAI,KAAK,CAAC;AAEpE,mBAAW,KAAK,iBAAiB,CAAC;AAClC,YAAI,KAAK,WAAW,GAAG,QAAQ;AAC/B,YAAI,KAAK,QAAQ;AACf,qBAAW;AACX,iBAAO,OAAO,OAAO,CAAC,GAAG,QAAQ;AACjC,mBAAS;AAAA,QACX;AAAA,MACF;AAEA,kBAAY,GAAG,IAAI;AAAA,IACrB;AAEA,aAAS,iBAAiB,GAAG,OAAO,cAAc;AAChD,aAAO,MAAM,IAAI,SAAS,MAAM;AAC9B,eAAO,gBAAgB,GAAG,MAAM,YAAY;AAAA,MAC9C,CAAC;AAAA,IACH;AAEA,aAAS,iBAAiB,aAAa,WAAW;AAChD,UAAI,KAAK,IAAI,MAAM;AACnB,kBAAY,QAAQ,SAAS,IAAI;AAC/B,YAAI,OAAO,GAAG,MAAM,EAAE;AACtB,YAAI,SAAS,aAAa,IAAI,MAAM,IAAI,SAAS;AACjD,eAAO,GAAG,QAAQ,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC,EAAE,QAAQ,CAAC;AAChD,+BAAuB,IAAI,IAAI,OAAO,EAAE;AAAA,MAC1C,CAAC;AAAA,IACH;AAEA,aAAS,YAAY,GAAG,UAAU;AAChC,aAAO,OAAO,QAAQ,EAAE,QAAQ,WAAS,MAAM,QAAQ,CAAC,GAAG,MAAM,EAAE,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC;AAAA,IACvF;AAAA;AAAA;;;AChFA;AAAA;AAAA;AAEA,QAAI,QAAQ,mBAA6B;AACzC,QAAI,OAAO;AAOX,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAmBA,aAAS,mBAAmB,GAAG,UAAU;AACvC,UAAI,YAAY,CAAC;AAEjB,eAAS,WAAW,WAAW,OAAO;AACpC,YAGE,KAAK,GAGL,UAAU,GACV,kBAAkB,UAAU,QAC5B,WAAW,MAAM,MAAM,SAAS,CAAC;AAEnC,cAAM,QAAQ,CAAC,GAAG,MAAM;AACtB,cAAI,IAAI,0BAA0B,GAAG,CAAC,GACpC,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE,QAAQ;AAE7B,cAAI,KAAK,MAAM,UAAU;AACvB,kBAAM,MAAM,SAAS,IAAE,CAAC,EAAE,QAAQ,cAAY;AAC5C,gBAAE,aAAa,QAAQ,EAAE,QAAQ,OAAK;AACpC,oBAAI,SAAS,EAAE,KAAK,CAAC,GACnB,OAAO,OAAO;AAChB,qBAAK,OAAO,MAAM,KAAK,SACnB,EAAE,OAAO,SAAS,EAAE,KAAK,QAAQ,EAAE,QAAQ;AAC7C,8BAAY,WAAW,GAAG,QAAQ;AAAA,gBACpC;AAAA,cACF,CAAC;AAAA,YACH,CAAC;AACD,sBAAU,IAAI;AACd,iBAAK;AAAA,UACP;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAEA,eAAS,UAAU,SAAS,OAAO,UAAU;AAE7C,aAAO;AAAA,IACT;AAEA,aAAS,mBAAmB,GAAG,UAAU;AACvC,UAAI,YAAY,CAAC;AAEjB,eAAS,KAAK,OAAO,UAAU,UAAU,iBAAiB,iBAAiB;AACzE,YAAI;AACJ,aAAK,MAAM,UAAU,QAAQ,EAAE,QAAQ,OAAK;AAC1C,cAAI,MAAM,CAAC;AACX,cAAI,EAAE,KAAK,CAAC,EAAE,OAAO;AACnB,cAAE,aAAa,CAAC,EAAE,QAAQ,OAAK;AAC7B,kBAAI,QAAQ,EAAE,KAAK,CAAC;AACpB,kBAAI,MAAM,UACL,MAAM,QAAQ,mBAAmB,MAAM,QAAQ,kBAAkB;AACpE,4BAAY,WAAW,GAAG,CAAC;AAAA,cAC7B;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AAAA,MACH;AAGA,eAAS,WAAW,OAAO,OAAO;AAChC,YAAI,eAAe,IACjB,cACA,WAAW;AAEb,cAAM,QAAQ,CAAC,GAAG,mBAAmB;AACnC,cAAI,EAAE,KAAK,CAAC,EAAE,UAAU,UAAU;AAChC,gBAAI,eAAe,EAAE,aAAa,CAAC;AACnC,gBAAI,aAAa,QAAQ;AACvB,6BAAe,EAAE,KAAK,aAAa,CAAC,CAAC,EAAE;AACvC,mBAAK,OAAO,UAAU,gBAAgB,cAAc,YAAY;AAChE,yBAAW;AACX,6BAAe;AAAA,YACjB;AAAA,UACF;AACA,eAAK,OAAO,UAAU,MAAM,QAAQ,cAAc,MAAM,MAAM;AAAA,QAChE,CAAC;AAED,eAAO;AAAA,MACT;AAEA,eAAS,UAAU,SAAS,OAAO,UAAU;AAE7C,aAAO;AAAA,IACT;AAEA,aAAS,0BAA0B,GAAG,GAAG;AACvC,UAAI,EAAE,KAAK,CAAC,EAAE,OAAO;AACnB,eAAO,EAAE,aAAa,CAAC,EAAE,KAAK,OAAK,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,MACpD;AAAA,IACF;AAEA,aAAS,YAAY,WAAW,GAAG,GAAG;AACpC,UAAI,IAAI,GAAG;AACT,YAAI,MAAM;AACV,YAAI;AACJ,YAAI;AAAA,MACN;AAEA,UAAI,aAAa,UAAU,CAAC;AAC5B,UAAI,CAAC,YAAY;AACf,kBAAU,CAAC,IAAI,aAAa,CAAC;AAAA,MAC/B;AACA,iBAAW,CAAC,IAAI;AAAA,IAClB;AAEA,aAAS,YAAY,WAAW,GAAG,GAAG;AACpC,UAAI,IAAI,GAAG;AACT,YAAI,MAAM;AACV,YAAI;AACJ,YAAI;AAAA,MACN;AACA,aAAO,CAAC,CAAC,UAAU,CAAC,KAAK,OAAO,OAAO,UAAU,CAAC,GAAG,CAAC;AAAA,IACxD;AAUA,aAAS,kBAAkB,GAAG,UAAU,WAAW,YAAY;AAC7D,UAAI,OAAO,CAAC,GACV,QAAQ,CAAC,GACT,MAAM,CAAC;AAKT,eAAS,QAAQ,WAAS;AACxB,cAAM,QAAQ,CAAC,GAAG,UAAU;AAC1B,eAAK,CAAC,IAAI;AACV,gBAAM,CAAC,IAAI;AACX,cAAI,CAAC,IAAI;AAAA,QACX,CAAC;AAAA,MACH,CAAC;AAED,eAAS,QAAQ,WAAS;AACxB,YAAI,UAAU;AACd,cAAM,QAAQ,OAAK;AACjB,cAAI,KAAK,WAAW,CAAC;AACrB,cAAI,GAAG,QAAQ;AACb,iBAAK,GAAG,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;AACtC,gBAAI,MAAM,GAAG,SAAS,KAAK;AAC3B,qBAAS,IAAI,KAAK,MAAM,EAAE,GAAG,KAAK,KAAK,KAAK,EAAE,GAAG,KAAK,IAAI,EAAE,GAAG;AAC7D,kBAAI,IAAI,GAAG,CAAC;AACZ,kBAAI,MAAM,CAAC,MAAM,KACb,UAAU,IAAI,CAAC,KACf,CAAC,YAAY,WAAW,GAAG,CAAC,GAAG;AACjC,sBAAM,CAAC,IAAI;AACX,sBAAM,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC;AAC3B,0BAAU,IAAI,CAAC;AAAA,cACjB;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,aAAO,EAAE,MAAY,MAAa;AAAA,IACpC;AAEA,aAAS,qBAAqB,GAAG,UAAU,MAAM,OAAO,YAAY;AAMlE,UAAI,KAAK,CAAC,GACR,SAAS,gBAAgB,GAAG,UAAU,MAAM,UAAU,GACtD,aAAa,aAAa,eAAe;AAE3C,eAAS,QAAQ,WAAW,eAAe;AACzC,YAAI,QAAQ,OAAO,MAAM;AACzB,YAAI,OAAO,MAAM,IAAI;AACrB,YAAI,UAAU,CAAC;AACf,eAAO,MAAM;AACX,cAAI,QAAQ,IAAI,GAAG;AACjB,sBAAU,IAAI;AAAA,UAChB,OAAO;AACL,oBAAQ,IAAI,IAAI;AAChB,kBAAM,KAAK,IAAI;AACf,oBAAQ,MAAM,OAAO,cAAc,IAAI,CAAC;AAAA,UAC1C;AAEA,iBAAO,MAAM,IAAI;AAAA,QACnB;AAAA,MACF;AAGA,eAAS,MAAM,MAAM;AACnB,WAAG,IAAI,IAAI,OAAO,QAAQ,IAAI,EAAE,OAAO,CAAC,KAAK,MAAM;AACjD,iBAAO,KAAK,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;AAAA,QAC/C,GAAG,CAAC;AAAA,MACN;AAGA,eAAS,MAAM,MAAM;AACnB,YAAI,MAAM,OAAO,SAAS,IAAI,EAAE,OAAO,CAAC,KAAK,MAAM;AACjD,iBAAO,KAAK,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC;AAAA,QAC/C,GAAG,OAAO,iBAAiB;AAE3B,YAAI,OAAO,EAAE,KAAK,IAAI;AACtB,YAAI,QAAQ,OAAO,qBAAqB,KAAK,eAAe,YAAY;AACtE,aAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG;AAAA,QACnC;AAAA,MACF;AAEA,cAAQ,OAAO,OAAO,aAAa,KAAK,MAAM,CAAC;AAC/C,cAAQ,OAAO,OAAO,WAAW,KAAK,MAAM,CAAC;AAG7C,aAAO,KAAK,KAAK,EAAE,QAAQ,OAAK,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;AAEnD,aAAO;AAAA,IACT;AAGA,aAAS,gBAAgB,GAAG,UAAU,MAAM,YAAY;AACtD,UAAI,aAAa,IAAI,MAAM,GACzB,aAAa,EAAE,MAAM,GACrB,QAAQ,IAAI,WAAW,SAAS,WAAW,SAAS,UAAU;AAEhE,eAAS,QAAQ,WAAS;AACxB,YAAI;AACJ,cAAM,QAAQ,OAAK;AACjB,cAAI,QAAQ,KAAK,CAAC;AAClB,qBAAW,QAAQ,KAAK;AACxB,cAAI,GAAG;AACL,gBAAI,QAAQ,KAAK,CAAC,GAChB,UAAU,WAAW,KAAK,OAAO,KAAK;AACxC,uBAAW,QAAQ,OAAO,OAAO,KAAK,IAAI,MAAM,GAAG,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,UACzE;AACA,cAAI;AAAA,QACN,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,IACT;AAKA,aAAS,2BAA2B,GAAG,KAAK;AAC1C,aAAO,OAAO,OAAO,GAAG,EAAE,OAAO,CAAC,iBAAiB,OAAO;AACxD,YAAI,MAAM,OAAO;AACjB,YAAI,MAAM,OAAO;AAEjB,eAAO,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACrC,cAAI,YAAY,MAAM,GAAG,CAAC,IAAI;AAE9B,gBAAM,KAAK,IAAI,IAAI,WAAW,GAAG;AACjC,gBAAM,KAAK,IAAI,IAAI,WAAW,GAAG;AAAA,QACnC,CAAC;AAED,cAAM,SAAS,MAAM;AACrB,YAAI,SAAS,gBAAgB,CAAC,GAAG;AAC/B,4BAAkB,CAAC,QAAQ,EAAE;AAAA,QAC/B;AACA,eAAO;AAAA,MACT,GAAG,CAAC,OAAO,mBAAmB,IAAI,CAAC,EAAE,CAAC;AAAA,IACxC;AASA,aAAS,iBAAiB,KAAK,SAAS;AACtC,UAAI,cAAc,OAAO,OAAO,OAAO,GACrC,aAAa,KAAK,kBAAkB,KAAK,KAAK,WAAW,GACzD,aAAa,KAAK,kBAAkB,KAAK,KAAK,WAAW;AAE3D,OAAC,KAAK,GAAG,EAAE,QAAQ,UAAQ;AACzB,SAAC,KAAK,GAAG,EAAE,QAAQ,WAAS;AAC1B,cAAI,YAAY,OAAO,OACrB,KAAK,IAAI,SAAS;AAEpB,cAAI,OAAO,QAAS;AAEpB,cAAI,SAAS,OAAO,OAAO,EAAE;AAC7B,cAAI,QAAQ,aAAa,KAAK,kBAAkB,KAAK,KAAK,MAAM;AAChE,cAAI,UAAU,KAAK;AACjB,oBAAQ,aAAa,KAAK,kBAAkB,KAAK,KAAI,MAAM;AAAA,UAC7D;AAEA,cAAI,OAAO;AACT,gBAAI,SAAS,IAAI,KAAK,UAAU,IAAI,OAAK,IAAI,KAAK;AAAA,UACpD;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,aAAS,QAAQ,KAAK,OAAO;AAC3B,aAAO,KAAK,UAAU,IAAI,IAAI,CAAC,KAAK,MAAM;AACxC,YAAI,OAAO;AACT,iBAAO,IAAI,MAAM,YAAY,CAAC,EAAE,CAAC;AAAA,QACnC,OAAO;AACL,cAAI,KAAK,OAAO,OAAO,GAAG,EAAE,IAAI,CAAAC,QAAMA,IAAG,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACjE,kBAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK;AAAA,QAC3B;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,UAAU,GAAG;AACpB,UAAI,WAAW,KAAK,iBAAiB,CAAC;AACtC,UAAI,YAAY,OAAO;AAAA,QACrB,mBAAmB,GAAG,QAAQ;AAAA,QAC9B,mBAAmB,GAAG,QAAQ;AAAA,MAAC;AAEjC,UAAI,MAAM,CAAC;AACX,UAAI;AACJ,OAAC,KAAK,GAAG,EAAE,QAAQ,UAAQ;AACzB,2BAAmB,SAAS,MAAM,WAAW,OAAO,OAAO,QAAQ,EAAE,QAAQ;AAC7E,SAAC,KAAK,GAAG,EAAE,QAAQ,WAAS;AAC1B,cAAI,UAAU,KAAK;AACjB,+BAAmB,iBAAiB,IAAI,WAAS;AAC/C,qBAAO,OAAO,OAAO,KAAK,EAAE,QAAQ;AAAA,YACtC,CAAC;AAAA,UACH;AAEA,cAAI,cAAc,SAAS,MAAM,EAAE,eAAe,EAAE,YAAY,KAAK,CAAC;AACtE,cAAI,QAAQ,kBAAkB,GAAG,kBAAkB,WAAW,UAAU;AACxE,cAAI,KAAK;AAAA,YAAqB;AAAA,YAAG;AAAA,YAC/B,MAAM;AAAA,YAAM,MAAM;AAAA,YAAO,UAAU;AAAA,UAAG;AACxC,cAAI,UAAU,KAAK;AACjB,iBAAK,KAAK,UAAU,IAAI,OAAK,CAAC,CAAC;AAAA,UACjC;AACA,cAAI,OAAO,KAAK,IAAI;AAAA,QACtB,CAAC;AAAA,MACH,CAAC;AAGD,UAAI,gBAAgB,2BAA2B,GAAG,GAAG;AACrD,uBAAiB,KAAK,aAAa;AACnC,aAAO,QAAQ,KAAK,EAAE,MAAM,EAAE,KAAK;AAAA,IACrC;AAEA,aAAS,IAAI,SAAS,SAAS,YAAY;AACzC,aAAO,CAAC,GAAG,GAAG,MAAM;AAClB,YAAI,SAAS,EAAE,KAAK,CAAC;AACrB,YAAI,SAAS,EAAE,KAAK,CAAC;AACrB,YAAI,MAAM;AACV,YAAI;AAEJ,eAAO,OAAO,QAAQ;AACtB,YAAI,OAAO,OAAO,QAAQ,UAAU,GAAG;AACrC,kBAAQ,OAAO,SAAS,YAAY,GAAG;AAAA,YACvC,KAAK;AAAK,sBAAQ,CAAC,OAAO,QAAQ;AAAG;AAAA,YACrC,KAAK;AAAK,sBAAQ,OAAO,QAAQ;AAAG;AAAA,UACpC;AAAA,QACF;AACA,YAAI,OAAO;AACT,iBAAO,aAAa,QAAQ,CAAC;AAAA,QAC/B;AACA,gBAAQ;AAER,gBAAQ,OAAO,QAAQ,UAAU,WAAW;AAC5C,gBAAQ,OAAO,QAAQ,UAAU,WAAW;AAE5C,eAAO,OAAO,QAAQ;AACtB,YAAI,OAAO,OAAO,QAAQ,UAAU,GAAG;AACrC,kBAAQ,OAAO,SAAS,YAAY,GAAG;AAAA,YACvC,KAAK;AAAK,sBAAQ,OAAO,QAAQ;AAAG;AAAA,YACpC,KAAK;AAAK,sBAAQ,CAAC,OAAO,QAAQ;AAAG;AAAA,UACrC;AAAA,QACF;AACA,YAAI,OAAO;AACT,iBAAO,aAAa,QAAQ,CAAC;AAAA,QAC/B;AACA,gBAAQ;AAER,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,MAAM,GAAG,GAAG;AACnB,aAAO,EAAE,KAAK,CAAC,EAAE;AAAA,IACnB;AAAA;AAAA;;;ACvaA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,YAAY,aAAgB;AAEhC,WAAO,UAAU;AAEjB,aAAS,SAAS,GAAG;AACnB,UAAI,KAAK,mBAAmB,CAAC;AAE7B,gBAAU,CAAC;AACX,aAAO,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;AAAA,IAClE;AAEA,aAAS,UAAU,GAAG;AACpB,UAAI,WAAW,KAAK,iBAAiB,CAAC;AACtC,UAAI,UAAU,EAAE,MAAM,EAAE;AACxB,UAAI,QAAQ;AACZ,eAAS,QAAQ,WAAS;AACxB,cAAM,YAAY,MAAM,OAAO,CAAC,KAAK,MAAM;AACzC,gBAAM,SAAS,EAAE,KAAK,CAAC,EAAE;AACzB,cAAI,MAAM,QAAQ;AAChB,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,GAAG,CAAC;AACJ,cAAM,QAAQ,OAAK,EAAE,KAAK,CAAC,EAAE,IAAI,QAAQ,YAAY,CAAC;AACtD,iBAAS,YAAY;AAAA,MACvB,CAAC;AAAA,IACH;AAAA;AAAA;;;AC9BA;AAAA;AAAA;AAEA,QAAI,UAAU;AACd,QAAI,YAAY;AAChB,QAAI,OAAO;AACX,QAAI,iBAAiB,eAAkB;AACvC,QAAI,oBAAoB;AACxB,QAAI,mBAAmB,eAAkB;AACzC,QAAI,eAAe;AACnB,QAAI,oBAAoB;AACxB,QAAI,mBAAmB;AACvB,QAAI,QAAQ;AACZ,QAAI,WAAW;AACf,QAAI,OAAO;AACX,QAAI,QAAQ,mBAA6B;AAEzC,WAAO,UAAU;AAEjB,aAAS,OAAO,GAAG,MAAM;AACvB,UAAI,OAAO,QAAQ,KAAK,cAAc,KAAK,OAAO,KAAK;AACvD,WAAK,UAAU,MAAM;AACnB,YAAI,cACF,KAAK,sBAAsB,MAAM,iBAAiB,CAAC,CAAC;AACtD,aAAK,eAAsB,MAAM,UAAU,aAAa,MAAM,IAAI,CAAC;AACnE,aAAK,sBAAsB,MAAM,iBAAiB,GAAG,WAAW,CAAC;AAAA,MACnE,CAAC;AAAA,IACH;AAEA,aAAS,UAAU,GAAG,MAAM,MAAM;AAChC,WAAK,8BAA8B,MAAM,uBAAuB,CAAC,CAAC;AAClE,WAAK,uBAA8B,MAAM,gBAAgB,CAAC,CAAC;AAC3D,WAAK,eAA8B,MAAM,QAAQ,IAAI,CAAC,CAAC;AACvD,WAAK,wBAA8B,MAAM,aAAa,IAAI,CAAC,CAAC;AAC5D,WAAK,YAA8B,MAAM,KAAK,KAAK,mBAAmB,CAAC,CAAC,CAAC;AACzE,WAAK,8BAA8B,MAAM,uBAAuB,CAAC,CAAC;AAClE,WAAK,wBAA8B,MAAM,iBAAiB,CAAC,CAAC;AAC5D,WAAK,4BAA8B,MAAM,aAAa,QAAQ,CAAC,CAAC;AAChE,WAAK,sBAA8B,MAAM,eAAe,CAAC,CAAC;AAC1D,WAAK,wBAA8B,MAAM,iBAAiB,CAAC,CAAC;AAC5D,WAAK,8BAA8B,MAAM,uBAAuB,CAAC,CAAC;AAClE,WAAK,qBAA8B,MAAM,UAAU,IAAI,CAAC,CAAC;AACzD,WAAK,yBAA8B,MAAM,kBAAkB,CAAC,CAAC;AAC7D,WAAK,yBAA8B,MAAM,kBAAkB,CAAC,CAAC;AAC7D,WAAK,aAA8B,MAAM,MAAM,GAAG,IAAI,CAAC;AACvD,WAAK,uBAA8B,MAAM,gBAAgB,CAAC,CAAC;AAC3D,WAAK,8BAA8B,MAAM,iBAAiB,OAAO,CAAC,CAAC;AACnE,WAAK,gBAA8B,MAAM,SAAS,CAAC,CAAC;AACpD,WAAK,yBAA8B,MAAM,kBAAkB,CAAC,CAAC;AAC7D,WAAK,yBAA8B,MAAM,kBAAkB,CAAC,CAAC;AAC7D,WAAK,sBAA8B,MAAM,UAAU,KAAK,CAAC,CAAC;AAC1D,WAAK,4BAA8B,MAAM,qBAAqB,CAAC,CAAC;AAChE,WAAK,4BAA8B,MAAM,iBAAiB,KAAK,CAAC,CAAC;AACjE,WAAK,sBAA8B,MAAM,eAAe,CAAC,CAAC;AAC1D,WAAK,4BAA8B,MAAM,qBAAqB,CAAC,CAAC;AAChE,WAAK,qBAA8B,MAAM,8BAA8B,CAAC,CAAC;AACzE,WAAK,oBAA8B,MAAM,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC1D;AAQA,aAAS,iBAAiB,YAAY,aAAa;AACjD,iBAAW,MAAM,EAAE,QAAQ,OAAK;AAC9B,YAAI,aAAa,WAAW,KAAK,CAAC;AAClC,YAAI,cAAc,YAAY,KAAK,CAAC;AAEpC,YAAI,YAAY;AACd,qBAAW,IAAI,YAAY;AAC3B,qBAAW,IAAI,YAAY;AAC3B,qBAAW,OAAO,YAAY;AAE9B,cAAI,YAAY,SAAS,CAAC,EAAE,QAAQ;AAClC,uBAAW,QAAQ,YAAY;AAC/B,uBAAW,SAAS,YAAY;AAAA,UAClC;AAAA,QACF;AAAA,MACF,CAAC;AAED,iBAAW,MAAM,EAAE,QAAQ,OAAK;AAC9B,YAAI,aAAa,WAAW,KAAK,CAAC;AAClC,YAAI,cAAc,YAAY,KAAK,CAAC;AAEpC,mBAAW,SAAS,YAAY;AAChC,YAAI,OAAO,OAAO,aAAa,GAAG,GAAG;AACnC,qBAAW,IAAI,YAAY;AAC3B,qBAAW,IAAI,YAAY;AAAA,QAC7B;AAAA,MACF,CAAC;AAED,iBAAW,MAAM,EAAE,QAAQ,YAAY,MAAM,EAAE;AAC/C,iBAAW,MAAM,EAAE,SAAS,YAAY,MAAM,EAAE;AAAA,IAClD;AAEA,QAAI,gBAAgB,CAAC,WAAW,WAAW,WAAW,WAAW,SAAS;AAC1E,QAAI,gBAAgB,EAAE,SAAS,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,KAAK;AAC3E,QAAI,aAAa,CAAC,aAAa,UAAU,WAAW,OAAO;AAC3D,QAAI,eAAe,CAAC,SAAS,QAAQ;AACrC,QAAI,eAAe,EAAE,OAAO,GAAG,QAAQ,EAAE;AACzC,QAAI,eAAe,CAAC,UAAU,UAAU,SAAS,UAAU,aAAa;AACxE,QAAI,eAAe;AAAA,MACjB,QAAQ;AAAA,MAAG,QAAQ;AAAA,MAAG,OAAO;AAAA,MAAG,QAAQ;AAAA,MACxC,aAAa;AAAA,MAAI,UAAU;AAAA,IAC7B;AACA,QAAI,YAAY,CAAC,UAAU;AAQ3B,aAAS,iBAAiB,YAAY;AACpC,UAAI,IAAI,IAAI,MAAM,EAAE,YAAY,MAAM,UAAU,KAAK,CAAC;AACtD,UAAI,QAAQ,aAAa,WAAW,MAAM,CAAC;AAE3C,QAAE,SAAS,OAAO;AAAA,QAAO,CAAC;AAAA,QACxB;AAAA,QACA,kBAAkB,OAAO,aAAa;AAAA,QACtC,KAAK,KAAK,OAAO,UAAU;AAAA,MAAC,CAAC;AAE/B,iBAAW,MAAM,EAAE,QAAQ,OAAK;AAC9B,YAAI,OAAO,aAAa,WAAW,KAAK,CAAC,CAAC;AAC1C,cAAM,UAAU,kBAAkB,MAAM,YAAY;AACpD,eAAO,KAAK,YAAY,EAAE,QAAQ,OAAK;AACrC,cAAI,QAAQ,CAAC,MAAM,QAAW;AAC5B,oBAAQ,CAAC,IAAI,aAAa,CAAC;AAAA,UAC7B;AAAA,QACF,CAAC;AAED,UAAE,QAAQ,GAAG,OAAO;AACpB,UAAE,UAAU,GAAG,WAAW,OAAO,CAAC,CAAC;AAAA,MACrC,CAAC;AAED,iBAAW,MAAM,EAAE,QAAQ,OAAK;AAC9B,YAAI,OAAO,aAAa,WAAW,KAAK,CAAC,CAAC;AAC1C,UAAE,QAAQ,GAAG,OAAO;AAAA,UAAO,CAAC;AAAA,UAC1B;AAAA,UACA,kBAAkB,MAAM,YAAY;AAAA,UACpC,KAAK,KAAK,MAAM,SAAS;AAAA,QAAC,CAAC;AAAA,MAC/B,CAAC;AAED,aAAO;AAAA,IACT;AAUA,aAAS,uBAAuB,GAAG;AACjC,UAAI,QAAQ,EAAE,MAAM;AACpB,YAAM,WAAW;AACjB,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,aAAK,UAAU;AACf,YAAI,KAAK,SAAS,YAAY,MAAM,KAAK;AACvC,cAAI,MAAM,YAAY,QAAQ,MAAM,YAAY,MAAM;AACpD,iBAAK,SAAS,KAAK;AAAA,UACrB,OAAO;AACL,iBAAK,UAAU,KAAK;AAAA,UACtB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAQA,aAAS,uBAAuB,GAAG;AACjC,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,KAAK,SAAS,KAAK,QAAQ;AAC7B,cAAI,IAAI,EAAE,KAAK,EAAE,CAAC;AAClB,cAAI,IAAI,EAAE,KAAK,EAAE,CAAC;AAClB,cAAI,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,IAAI,EAAE,MAAM,EAAK;AACzD,eAAK,aAAa,GAAG,cAAc,OAAO,KAAK;AAAA,QACjD;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,iBAAiB,GAAG;AAC3B,UAAI,UAAU;AACd,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,KAAK,WAAW;AAClB,eAAK,UAAU,EAAE,KAAK,KAAK,SAAS,EAAE;AACtC,eAAK,UAAU,EAAE,KAAK,KAAK,YAAY,EAAE;AACzC,oBAAU,KAAK,IAAI,SAAS,KAAK,OAAO;AAAA,QAC1C;AAAA,MACF,CAAC;AACD,QAAE,MAAM,EAAE,UAAU;AAAA,IACtB;AAEA,aAAS,uBAAuB,GAAG;AACjC,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,KAAK,UAAU,cAAc;AAC/B,YAAE,KAAK,KAAK,CAAC,EAAE,YAAY,KAAK;AAChC,YAAE,WAAW,CAAC;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,eAAe,GAAG;AACzB,UAAI,OAAO,OAAO;AAClB,UAAI,OAAO;AACX,UAAI,OAAO,OAAO;AAClB,UAAI,OAAO;AACX,UAAI,aAAa,EAAE,MAAM;AACzB,UAAI,UAAU,WAAW,WAAW;AACpC,UAAI,UAAU,WAAW,WAAW;AAEpC,eAAS,YAAY,OAAO;AAC1B,YAAI,IAAI,MAAM;AACd,YAAI,IAAI,MAAM;AACd,YAAI,IAAI,MAAM;AACd,YAAI,IAAI,MAAM;AACd,eAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAC/B,eAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAC/B,eAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAC/B,eAAO,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,MACjC;AAEA,QAAE,MAAM,EAAE,QAAQ,OAAK,YAAY,EAAE,KAAK,CAAC,CAAC,CAAC;AAC7C,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,OAAO,OAAO,MAAM,GAAG,GAAG;AAC5B,sBAAY,IAAI;AAAA,QAClB;AAAA,MACF,CAAC;AAED,cAAQ;AACR,cAAQ;AAER,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,aAAK,KAAK;AACV,aAAK,KAAK;AAAA,MACZ,CAAC;AAED,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,aAAK,OAAO,QAAQ,OAAK;AACvB,YAAE,KAAK;AACP,YAAE,KAAK;AAAA,QACT,CAAC;AACD,YAAI,OAAO,OAAO,MAAM,GAAG,GAAG;AAAE,eAAK,KAAK;AAAA,QAAM;AAChD,YAAI,OAAO,OAAO,MAAM,GAAG,GAAG;AAAE,eAAK,KAAK;AAAA,QAAM;AAAA,MAClD,CAAC;AAED,iBAAW,QAAQ,OAAO,OAAO;AACjC,iBAAW,SAAS,OAAO,OAAO;AAAA,IACpC;AAEA,aAAS,qBAAqB,GAAG;AAC/B,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,QAAQ,EAAE,KAAK,EAAE,CAAC;AACtB,YAAI,QAAQ,EAAE,KAAK,EAAE,CAAC;AACtB,YAAI,IAAI;AACR,YAAI,CAAC,KAAK,QAAQ;AAChB,eAAK,SAAS,CAAC;AACf,eAAK;AACL,eAAK;AAAA,QACP,OAAO;AACL,eAAK,KAAK,OAAO,CAAC;AAClB,eAAK,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAAA,QACzC;AACA,aAAK,OAAO,QAAQ,KAAK,cAAc,OAAO,EAAE,CAAC;AACjD,aAAK,OAAO,KAAK,KAAK,cAAc,OAAO,EAAE,CAAC;AAAA,MAChD,CAAC;AAAA,IACH;AAEA,aAAS,qBAAqB,GAAG;AAC/B,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,OAAO,OAAO,MAAM,GAAG,GAAG;AAC5B,cAAI,KAAK,aAAa,OAAO,KAAK,aAAa,KAAK;AAClD,iBAAK,SAAS,KAAK;AAAA,UACrB;AACA,kBAAQ,KAAK,UAAU;AAAA,YACvB,KAAK;AAAK,mBAAK,KAAK,KAAK,QAAQ,IAAI,KAAK;AAAa;AAAA,YACvD,KAAK;AAAK,mBAAK,KAAK,KAAK,QAAQ,IAAI,KAAK;AAAa;AAAA,UACvD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,8BAA8B,GAAG;AACxC,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,KAAK,UAAU;AACjB,eAAK,OAAO,QAAQ;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,kBAAkB,GAAG;AAC5B,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,EAAE,SAAS,CAAC,EAAE,QAAQ;AACxB,cAAI,OAAO,EAAE,KAAK,CAAC;AACnB,cAAI,IAAI,EAAE,KAAK,KAAK,SAAS;AAC7B,cAAI,IAAI,EAAE,KAAK,KAAK,YAAY;AAChC,cAAI,IAAI,EAAE,KAAK,KAAK,WAAW,KAAK,WAAW,SAAS,CAAC,CAAC;AAC1D,cAAI,IAAI,EAAE,KAAK,KAAK,YAAY,KAAK,YAAY,SAAS,CAAC,CAAC;AAE5D,eAAK,QAAQ,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC;AAC/B,eAAK,SAAS,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC;AAChC,eAAK,IAAI,EAAE,IAAI,KAAK,QAAQ;AAC5B,eAAK,IAAI,EAAE,IAAI,KAAK,SAAS;AAAA,QAC/B;AAAA,MACF,CAAC;AAED,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,EAAE,KAAK,CAAC,EAAE,UAAU,UAAU;AAChC,YAAE,WAAW,CAAC;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,gBAAgB,GAAG;AAC1B,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,EAAE,MAAM,EAAE,GAAG;AACf,cAAI,OAAO,EAAE,KAAK,EAAE,CAAC;AACrB,cAAI,CAAC,KAAK,WAAW;AACnB,iBAAK,YAAY,CAAC;AAAA,UACpB;AACA,eAAK,UAAU,KAAK,EAAE,GAAM,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC;AAC9C,YAAE,WAAW,CAAC;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,gBAAgB,GAAG;AAC1B,UAAI,SAAS,KAAK,iBAAiB,CAAC;AACpC,aAAO,QAAQ,WAAS;AACtB,YAAI,aAAa;AACjB,cAAM,QAAQ,CAAC,GAAG,MAAM;AACtB,cAAI,OAAO,EAAE,KAAK,CAAC;AACnB,eAAK,QAAQ,IAAI;AACjB,WAAC,KAAK,aAAa,CAAC,GAAG,QAAQ,cAAY;AACzC,iBAAK,aAAa,GAAG,YAAY;AAAA,cAC/B,OAAO,SAAS,MAAM;AAAA,cACtB,QAAQ,SAAS,MAAM;AAAA,cACvB,MAAM,KAAK;AAAA,cACX,OAAO,IAAK,EAAE;AAAA,cACd,GAAG,SAAS;AAAA,cACZ,OAAO,SAAS;AAAA,YAClB,GAAG,KAAK;AAAA,UACV,CAAC;AACD,iBAAO,KAAK;AAAA,QACd,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAEA,aAAS,kBAAkB,GAAG;AAC5B,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,YAAI,OAAO,EAAE,KAAK,CAAC;AACnB,YAAI,KAAK,UAAU,YAAY;AAC7B,cAAI,WAAW,EAAE,KAAK,KAAK,EAAE,CAAC;AAC9B,cAAI,IAAI,SAAS,IAAI,SAAS,QAAQ;AACtC,cAAI,IAAI,SAAS;AACjB,cAAI,KAAK,KAAK,IAAI;AAClB,cAAI,KAAK,SAAS,SAAS;AAC3B,YAAE,QAAQ,KAAK,GAAG,KAAK,KAAK;AAC5B,YAAE,WAAW,CAAC;AACd,eAAK,MAAM,SAAS;AAAA,YAClB,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG;AAAA,YAC/B,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG;AAAA,YAC/B,EAAE,GAAG,IAAQ,IAAQ,EAAK;AAAA,YAC1B,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG;AAAA,YAC/B,EAAE,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG,IAAI,GAAG;AAAA,UACjC;AACA,eAAK,MAAM,IAAI,KAAK;AACpB,eAAK,MAAM,IAAI,KAAK;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,aAAS,kBAAkB,KAAK,OAAO;AACrC,aAAO,KAAK,UAAU,KAAK,KAAK,KAAK,KAAK,GAAG,MAAM;AAAA,IACrD;AAEA,aAAS,aAAa,OAAO;AAC3B,UAAI,WAAW,CAAC;AAChB,UAAI,OAAO;AACT,eAAO,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM;AACxC,cAAI,OAAO,MAAM,UAAU;AACzB,gBAAI,EAAE,YAAY;AAAA,UACpB;AAEA,mBAAS,CAAC,IAAI;AAAA,QAChB,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACpZA;AAAA;AAAA,QAAI,OAAO;AACX,QAAI,QAAQ,mBAA6B;AAEzC,WAAO,UAAU;AAAA,MACf;AAAA,IACF;AAGA,aAAS,cAAc,GAAG;AACxB,UAAI,cAAc,KAAK,iBAAiB,CAAC;AAEzC,UAAI,IAAI,IAAI,MAAM,EAAE,UAAU,MAAM,YAAY,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC;AAEnE,QAAE,MAAM,EAAE,QAAQ,OAAK;AACrB,UAAE,QAAQ,GAAG,EAAE,OAAO,EAAE,CAAC;AACzB,UAAE,UAAU,GAAG,UAAU,EAAE,KAAK,CAAC,EAAE,IAAI;AAAA,MACzC,CAAC;AAED,QAAE,MAAM,EAAE,QAAQ,OAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;AAEtD,kBAAY,QAAQ,CAAC,OAAO,MAAM;AAChC,YAAI,SAAS,UAAU;AACvB,UAAE,QAAQ,QAAQ,EAAE,MAAM,OAAO,CAAC;AAClC,cAAM,OAAO,CAAC,GAAG,MAAM;AACrB,YAAE,QAAQ,GAAG,GAAG,EAAE,OAAO,QAAQ,CAAC;AAClC,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,IACT;AAAA;AAAA;;;AC9BA,IAAAC,mBAAA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAsBA,WAAO,UAAU;AAAA,MACf,UAAU;AAAA,MAEV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,QACJ,MAAM,eAAsB;AAAA,QAC5B,QAAQ,eAAsB;AAAA,MAChC;AAAA,MACA,SAAS;AAAA,IACX;AAAA;AAAA;", "names": ["v", "w", "v", "range", "range", "g", "require_util", "edge", "dfs", "xs", "require_version"]}