import {
  __commonJS
} from "./chunk-KWPVD4H7.js";

// node_modules/d3-tile/dist/d3-tile.js
var require_d3_tile = __commonJS({
  "node_modules/d3-tile/dist/d3-tile.js"(exports, module) {
    (function(global, factory) {
      typeof exports === "object" && typeof module !== "undefined" ? factory(exports) : typeof define === "function" && define.amd ? define(["exports"], factory) : (global = global || self, factory(global.d3 = global.d3 || {}));
    })(exports, function(exports2) {
      "use strict";
      function defaultScale(t) {
        return t.k;
      }
      function defaultTranslate(t) {
        return [t.x, t.y];
      }
      function constant(x) {
        return function() {
          return x;
        };
      }
      function tile() {
        let x0 = 0, y0 = 0, x1 = 960, y1 = 500;
        let clampX = true, clampY = true;
        let tileSize = 256;
        let scale = defaultScale;
        let translate = defaultTranslate;
        let zoomDelta = 0;
        function tile2() {
          const scale_ = +scale.apply(this, arguments);
          const translate_ = translate.apply(this, arguments);
          const z = Math.log2(scale_ / tileSize);
          const z0 = Math.round(Math.max(z + zoomDelta, 0));
          const k = Math.pow(2, z - z0) * tileSize;
          const x = +translate_[0] - scale_ / 2;
          const y = +translate_[1] - scale_ / 2;
          const xmin = Math.max(clampX ? 0 : -Infinity, Math.floor((x0 - x) / k));
          const xmax = Math.min(clampX ? 1 << z0 : Infinity, Math.ceil((x1 - x) / k));
          const ymin = Math.max(clampY ? 0 : -Infinity, Math.floor((y0 - y) / k));
          const ymax = Math.min(clampY ? 1 << z0 : Infinity, Math.ceil((y1 - y) / k));
          const tiles = [];
          for (let y2 = ymin; y2 < ymax; ++y2) {
            for (let x2 = xmin; x2 < xmax; ++x2) {
              tiles.push([x2, y2, z0]);
            }
          }
          tiles.translate = [x / k, y / k];
          tiles.scale = k;
          return tiles;
        }
        tile2.size = function(_) {
          return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], tile2) : [x1 - x0, y1 - y0];
        };
        tile2.extent = function(_) {
          return arguments.length ? (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1], tile2) : [[x0, y0], [x1, y1]];
        };
        tile2.scale = function(_) {
          return arguments.length ? (scale = typeof _ === "function" ? _ : constant(+_), tile2) : scale;
        };
        tile2.translate = function(_) {
          return arguments.length ? (translate = typeof _ === "function" ? _ : constant([+_[0], +_[1]]), tile2) : translate;
        };
        tile2.zoomDelta = function(_) {
          return arguments.length ? (zoomDelta = +_, tile2) : zoomDelta;
        };
        tile2.tileSize = function(_) {
          return arguments.length ? (tileSize = +_, tile2) : tileSize;
        };
        tile2.clamp = function(_) {
          return arguments.length ? (clampX = clampY = !!_, tile2) : clampX && clampY;
        };
        tile2.clampX = function(_) {
          return arguments.length ? (clampX = !!_, tile2) : clampX;
        };
        tile2.clampY = function(_) {
          return arguments.length ? (clampY = !!_, tile2) : clampY;
        };
        return tile2;
      }
      function tileWrap([x, y, z]) {
        const j = 1 << z;
        return [x - Math.floor(x / j) * j, y - Math.floor(y / j) * j, z];
      }
      exports2.tile = tile;
      exports2.tileWrap = tileWrap;
      Object.defineProperty(exports2, "__esModule", { value: true });
    });
  }
});

export {
  require_d3_tile
};
//# sourceMappingURL=chunk-R7PW65UN.js.map
