{"version": 3, "sources": ["../../d3-geo-voronoi/src/math.js", "../../d3-geo-voronoi/src/cartesian.js", "../../d3-geo-voronoi/src/delaunay.js", "../../d3-geo-voronoi/src/voronoi.js", "../../d3-tricontour/src/extent.js", "../../d3-tricontour/src/merge.js", "../../d3-tricontour/src/contains.js", "../../d3-tricontour/src/area.js", "../../d3-tricontour/src/ringsort.js", "../../d3-tricontour/src/tricontour.js", "../../d3-geo-voronoi/src/contour.js"], "sourcesContent": ["export const epsilon = 1e-6;\nexport const epsilon2 = 1e-12;\nexport const pi = Math.PI;\nexport const halfPi = pi / 2;\nexport const quarterPi = pi / 4;\nexport const tau = pi * 2;\n\nexport const degrees = 180 / pi;\nexport const radians = pi / 180;\n\nexport const abs = Math.abs;\nexport const atan = Math.atan;\nexport const atan2 = Math.atan2;\nexport const cos = Math.cos;\nexport const ceil = Math.ceil;\nexport const exp = Math.exp;\nexport const floor = Math.floor;\nexport const log = Math.log;\nexport const max = Math.max;\nexport const min = Math.min;\nexport const pow = Math.pow;\nexport const sin = Math.sin;\nexport const sign =\n  Math.sign ||\n  function (x) {\n    return x > 0 ? 1 : x < 0 ? -1 : 0;\n  };\nexport const sqrt = Math.sqrt;\nexport const tan = Math.tan;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);\n}\n\nexport function haversin(x) {\n  return (x = sin(x / 2)) * x;\n}\n", "import { asin, atan2, cos, sin, sqrt } from \"./math.js\";\n\nexport function spherical(cartesian) {\n  return [atan2(cartesian[1], cartesian[0]), asin(cartesian[2])];\n}\n\nexport function cartesian(spherical) {\n  const lambda = spherical[0],\n    phi = spherical[1],\n    cosPhi = cos(phi);\n  return [cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi)];\n}\n\nexport function cartesianDot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\n\nexport function cartesianCross(a, b) {\n  return [\n    a[1] * b[2] - a[2] * b[1],\n    a[2] * b[0] - a[0] * b[2],\n    a[0] * b[1] - a[1] * b[0],\n  ];\n}\n\n// TODO return a\nexport function cartesianAddInPlace(a, b) {\n  (a[0] += b[0]), (a[1] += b[1]), (a[2] += b[2]);\n}\n\nexport function cartesianAdd(a, b) {\n  return [a[0] + b[0], a[1] + b[1], a[2] + b[2]];\n}\n\nexport function cartesianScale(vector, k) {\n  return [vector[0] * k, vector[1] * k, vector[2] * k];\n}\n\n// TODO return d\nexport function cartesianNormalizeInPlace(d) {\n  var l = sqrt(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n  (d[0] /= l), (d[1] /= l), (d[2] /= l);\n}\n\nexport function cartesianNormalize(d) {\n  var l = sqrt(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n  return [d[0] / l, d[1] / l, d[2] / l];\n}\n", "import { Delaunay } from \"d3-delaunay\";\nimport { geoRotation, geoStereographic } from \"d3-geo\";\nimport { extent } from \"d3-array\";\nimport {\n  asin,\n  atan2,\n  cos,\n  degrees,\n  max,\n  min,\n  radians,\n  sign,\n  sin,\n  sqrt,\n} from \"./math.js\";\nimport {\n  cartesianNormalize as normalize,\n  cartesian<PERSON>ross as cross,\n  cartesianDot as dot,\n  cartesianAdd,\n} from \"./cartesian.js\";\n\n// Converts 3D Cartesian to spherical coordinates (degrees).\nfunction spherical(cartesian) {\n  return [\n    atan2(cartesian[1], cartesian[0]) * degrees,\n    asin(max(-1, min(1, cartesian[2]))) * degrees,\n  ];\n}\n\n// Converts spherical coordinates (degrees) to 3D Cartesian.\nfunction cartesian(coordinates) {\n  const lambda = coordinates[0] * radians,\n    phi = coordinates[1] * radians,\n    cosphi = cos(phi);\n  return [cosphi * cos(lambda), cosphi * sin(lambda), sin(phi)];\n}\n\n// Spherical excess of a triangle (in spherical coordinates)\nexport function excess(triangle) {\n  triangle = triangle.map((p) => cartesian(p));\n  return dot(triangle[0], cross(triangle[2], triangle[1]));\n}\n\nexport function geoDelaunay(points) {\n  const delaunay = geo_delaunay_from(points),\n    triangles = geo_triangles(delaunay),\n    edges = geo_edges(triangles, points),\n    neighbors = geo_neighbors(triangles, points.length),\n    find = geo_find(neighbors, points),\n    // Voronoi ; could take a center function as an argument\n    circumcenters = geo_circumcenters(triangles, points),\n    { polygons, centers } = geo_polygons(circumcenters, triangles, points),\n    mesh = geo_mesh(polygons),\n    hull = geo_hull(triangles, points),\n    // Urquhart ; returns a function that takes a distance array as argument.\n    urquhart = geo_urquhart(edges, triangles);\n  return {\n    delaunay,\n    edges,\n    triangles,\n    centers,\n    neighbors,\n    polygons,\n    mesh,\n    hull,\n    urquhart,\n    find,\n  };\n}\n\nfunction geo_find(neighbors, points) {\n  function distance2(a, b) {\n    let x = a[0] - b[0],\n      y = a[1] - b[1],\n      z = a[2] - b[2];\n    return x * x + y * y + z * z;\n  }\n\n  return function find(x, y, next) {\n    if (next === undefined) next = 0;\n    let cell,\n      dist,\n      found = next;\n    const xyz = cartesian([x, y]);\n    do {\n      cell = next;\n      next = null;\n      dist = distance2(xyz, cartesian(points[cell]));\n      neighbors[cell].forEach((i) => {\n        let ndist = distance2(xyz, cartesian(points[i]));\n        if (ndist < dist) {\n          dist = ndist;\n          next = i;\n          found = i;\n          return;\n        }\n      });\n    } while (next !== null);\n\n    return found;\n  };\n}\n\nfunction geo_delaunay_from(points) {\n  if (points.length < 2) return {};\n\n  // find a valid point to send to infinity\n  let pivot = 0;\n  while (isNaN(points[pivot][0] + points[pivot][1]) && pivot++ < points.length);\n\n  const r = geoRotation(points[pivot]),\n    projection = geoStereographic()\n      .translate([0, 0])\n      .scale(1)\n      .rotate(r.invert([180, 0]));\n  points = points.map(projection);\n\n  const zeros = [];\n  let max2 = 1;\n  for (let i = 0, n = points.length; i < n; i++) {\n    let m = points[i][0] ** 2 + points[i][1] ** 2;\n    if (!isFinite(m) || m > 1e32) zeros.push(i);\n    else if (m > max2) max2 = m;\n  }\n\n  const FAR = 1e6 * sqrt(max2);\n\n  zeros.forEach((i) => (points[i] = [FAR, 0]));\n\n  // Add infinite horizon points\n  points.push([0, FAR]);\n  points.push([-FAR, 0]);\n  points.push([0, -FAR]);\n\n  const delaunay = Delaunay.from(points);\n\n  delaunay.projection = projection;\n\n  // clean up the triangulation\n  const { triangles, halfedges, inedges } = delaunay;\n  const degenerate = [];\n  for (let i = 0, l = halfedges.length; i < l; i++) {\n    if (halfedges[i] < 0) {\n      const j = i % 3 == 2 ? i - 2 : i + 1;\n      const k = i % 3 == 0 ? i + 2 : i - 1;\n      const a = halfedges[j];\n      const b = halfedges[k];\n      halfedges[a] = b;\n      halfedges[b] = a;\n      halfedges[j] = halfedges[k] = -1;\n      triangles[i] = triangles[j] = triangles[k] = pivot;\n      inedges[triangles[a]] = a % 3 == 0 ? a + 2 : a - 1;\n      inedges[triangles[b]] = b % 3 == 0 ? b + 2 : b - 1;\n      degenerate.push(Math.min(i, j, k));\n      i += 2 - (i % 3);\n    } else if (triangles[i] > points.length - 3 - 1) {\n      triangles[i] = pivot;\n    }\n  }\n\n  // there should always be 4 degenerate triangles\n  // console.warn(degenerate);\n  return delaunay;\n}\n\nfunction geo_edges(triangles, points) {\n  const _index = new Set();\n  if (points.length === 2) return [[0, 1]];\n  triangles.forEach((tri) => {\n    if (tri[0] === tri[1]) return;\n    if (excess(tri.map((i) => points[i])) < 0) return;\n    for (let i = 0, j; i < 3; i++) {\n      j = (i + 1) % 3;\n      _index.add(extent([tri[i], tri[j]]).join(\"-\"));\n    }\n  });\n  return Array.from(_index, (d) => d.split(\"-\").map(Number));\n}\n\nfunction geo_triangles(delaunay) {\n  const { triangles } = delaunay;\n  if (!triangles) return [];\n\n  const geo_triangles = [];\n  for (let i = 0, n = triangles.length / 3; i < n; i++) {\n    const a = triangles[3 * i],\n      b = triangles[3 * i + 1],\n      c = triangles[3 * i + 2];\n    if (a !== b && b !== c) {\n      geo_triangles.push([a, c, b]);\n    }\n  }\n  return geo_triangles;\n}\n\nfunction geo_circumcenters(triangles, points) {\n  // if (!use_centroids) {\n  return triangles.map((tri) => {\n    const c = tri.map((i) => points[i]).map(cartesian),\n      V = cartesianAdd(\n        cartesianAdd(cross(c[1], c[0]), cross(c[2], c[1])),\n        cross(c[0], c[2])\n      );\n    return spherical(normalize(V));\n  });\n  /*} else {\n    return triangles.map(tri => {\n      return d3.geoCentroid({\n        type: \"MultiPoint\",\n        coordinates: tri.map(i => points[i])\n      });\n    });\n  }*/\n}\n\nfunction geo_neighbors(triangles, npoints) {\n  const neighbors = [];\n  triangles.forEach((tri) => {\n    for (let j = 0; j < 3; j++) {\n      const a = tri[j],\n        b = tri[(j + 1) % 3];\n      neighbors[a] = neighbors[a] || [];\n      neighbors[a].push(b);\n    }\n  });\n\n  // degenerate cases\n  if (triangles.length === 0) {\n    if (npoints === 2) (neighbors[0] = [1]), (neighbors[1] = [0]);\n    else if (npoints === 1) neighbors[0] = [];\n  }\n\n  return neighbors;\n}\n\nfunction geo_polygons(circumcenters, triangles, points) {\n  const polygons = [];\n\n  const centers = circumcenters.slice();\n\n  if (triangles.length === 0) {\n    if (points.length < 2) return { polygons, centers };\n    if (points.length === 2) {\n      // two hemispheres\n      const a = cartesian(points[0]),\n        b = cartesian(points[1]),\n        m = normalize(cartesianAdd(a, b)),\n        d = normalize(cross(a, b)),\n        c = cross(m, d);\n      const poly = [\n        m,\n        cross(m, c),\n        cross(cross(m, c), c),\n        cross(cross(cross(m, c), c), c),\n      ]\n        .map(spherical)\n        .map(supplement);\n      return (\n        polygons.push(poly),\n        polygons.push(poly.slice().reverse()),\n        { polygons, centers }\n      );\n    }\n  }\n\n  triangles.forEach((tri, t) => {\n    for (let j = 0; j < 3; j++) {\n      const a = tri[j],\n        b = tri[(j + 1) % 3],\n        c = tri[(j + 2) % 3];\n      polygons[a] = polygons[a] || [];\n      polygons[a].push([b, c, t, [a, b, c]]);\n    }\n  });\n\n  // reorder each polygon\n  const reordered = polygons.map((poly) => {\n    const p = [poly[0][2]]; // t\n    let k = poly[0][1]; // k = c\n    for (let i = 1; i < poly.length; i++) {\n      // look for b = k\n      for (let j = 0; j < poly.length; j++) {\n        if (poly[j][0] == k) {\n          k = poly[j][1];\n          p.push(poly[j][2]);\n          break;\n        }\n      }\n    }\n\n    if (p.length > 2) {\n      return p;\n    } else if (p.length == 2) {\n      const R0 = o_midpoint(\n          points[poly[0][3][0]],\n          points[poly[0][3][1]],\n          centers[p[0]]\n        ),\n        R1 = o_midpoint(\n          points[poly[0][3][2]],\n          points[poly[0][3][0]],\n          centers[p[0]]\n        );\n      const i0 = supplement(R0),\n        i1 = supplement(R1);\n      return [p[0], i1, p[1], i0];\n    }\n  });\n\n  function supplement(point) {\n    let f = -1;\n    centers.slice(triangles.length, Infinity).forEach((p, i) => {\n      if (p[0] === point[0] && p[1] === point[1]) f = i + triangles.length;\n    });\n    if (f < 0) (f = centers.length), centers.push(point);\n    return f;\n  }\n\n  return { polygons: reordered, centers };\n}\n\nfunction o_midpoint(a, b, c) {\n  a = cartesian(a);\n  b = cartesian(b);\n  c = cartesian(c);\n  const s = sign(dot(cross(b, a), c));\n  return spherical(normalize(cartesianAdd(a, b)).map((d) => s * d));\n}\n\nfunction geo_mesh(polygons) {\n  const mesh = [];\n  polygons.forEach((poly) => {\n    if (!poly) return;\n    let p = poly[poly.length - 1];\n    for (let q of poly) {\n      if (q > p) mesh.push([p, q]);\n      p = q;\n    }\n  });\n  return mesh;\n}\n\nfunction geo_urquhart(edges, triangles) {\n  return function (distances) {\n    const _lengths = new Map(),\n      _urquhart = new Map();\n    edges.forEach((edge, i) => {\n      const u = edge.join(\"-\");\n      _lengths.set(u, distances[i]);\n      _urquhart.set(u, true);\n    });\n\n    triangles.forEach((tri) => {\n      let l = 0,\n        remove = -1;\n      for (let j = 0; j < 3; j++) {\n        let u = extent([tri[j], tri[(j + 1) % 3]]).join(\"-\");\n        if (_lengths.get(u) > l) {\n          l = _lengths.get(u);\n          remove = u;\n        }\n      }\n      _urquhart.set(remove, false);\n    });\n\n    return edges.map((edge) => _urquhart.get(edge.join(\"-\")));\n  };\n}\n\nfunction geo_hull(triangles, points) {\n  const _hull = new Set(),\n    hull = [];\n  triangles.map((tri) => {\n    if (excess(tri.map((i) => points[i > points.length ? 0 : i])) > 1e-12)\n      return;\n    for (let i = 0; i < 3; i++) {\n      let e = [tri[i], tri[(i + 1) % 3]],\n        code = `${e[0]}-${e[1]}`;\n      if (_hull.has(code)) _hull.delete(code);\n      else _hull.add(`${e[1]}-${e[0]}`);\n    }\n  });\n\n  const _index = new Map();\n  let start;\n  _hull.forEach((e) => {\n    e = e.split(\"-\").map(Number);\n    _index.set(e[0], e[1]);\n    start = e[0];\n  });\n\n  if (start === undefined) return hull;\n\n  let next = start;\n  do {\n    hull.push(next);\n    let n = _index.get(next);\n    _index.set(next, -1);\n    next = n;\n  } while (next > -1 && next !== start);\n\n  return hull;\n}\n", "import { geoCentroid, geoDistance } from \"d3-geo\";\nimport { geoDelaunay, excess } from \"./delaunay.js\";\n\nexport function geoVoronoi(data) {\n  const v = function (data) {\n    v.delaunay = null;\n    v._data = data;\n\n    if (typeof v._data === \"object\" && v._data.type === \"FeatureCollection\") {\n      v._data = v._data.features;\n    }\n    if (typeof v._data === \"object\") {\n      const temp = v._data\n        .map((d) => [v._vx(d), v._vy(d), d])\n        .filter((d) => isFinite(d[0] + d[1]));\n      v.points = temp.map((d) => [d[0], d[1]]);\n      v.valid = temp.map((d) => d[2]);\n      v.delaunay = geoDelaunay(v.points);\n    }\n    return v;\n  };\n\n  v._vx = function (d) {\n    if (typeof d == \"object\" && \"type\" in d) {\n      return geoCentroid(d)[0];\n    }\n    if (0 in d) return d[0];\n  };\n  v._vy = function (d) {\n    if (typeof d == \"object\" && \"type\" in d) {\n      return geoCentroid(d)[1];\n    }\n    if (1 in d) return d[1];\n  };\n\n  v.x = function (f) {\n    if (!f) return v._vx;\n    v._vx = f;\n    return v;\n  };\n  v.y = function (f) {\n    if (!f) return v._vy;\n    v._vy = f;\n    return v;\n  };\n\n  v.polygons = function (data) {\n    if (data !== undefined) {\n      v(data);\n    }\n\n    if (!v.delaunay) return false;\n    const coll = {\n      type: \"FeatureCollection\",\n      features: [],\n    };\n    if (v.valid.length === 0) return coll;\n    v.delaunay.polygons.forEach((poly, i) =>\n      coll.features.push({\n        type: \"Feature\",\n        geometry: !poly\n          ? null\n          : {\n              type: \"Polygon\",\n              coordinates: [\n                [...poly, poly[0]].map((i) => v.delaunay.centers[i]),\n              ],\n            },\n        properties: {\n          site: v.valid[i],\n          sitecoordinates: v.points[i],\n          neighbours: v.delaunay.neighbors[i], // not part of the public API\n        },\n      })\n    );\n    if (v.valid.length === 1)\n      coll.features.push({\n        type: \"Feature\",\n        geometry: { type: \"Sphere\" },\n        properties: {\n          site: v.valid[0],\n          sitecoordinates: v.points[0],\n          neighbours: [],\n        },\n      });\n    return coll;\n  };\n\n  v.triangles = function (data) {\n    if (data !== undefined) {\n      v(data);\n    }\n    if (!v.delaunay) return false;\n\n    return {\n      type: \"FeatureCollection\",\n      features: v.delaunay.triangles\n        .map((tri, index) => {\n          tri = tri.map((i) => v.points[i]);\n          tri.center = v.delaunay.centers[index];\n          return tri;\n        })\n        .filter((tri) => excess(tri) > 0)\n        .map((tri) => ({\n          type: \"Feature\",\n          properties: {\n            circumcenter: tri.center,\n          },\n          geometry: {\n            type: \"Polygon\",\n            coordinates: [[...tri, tri[0]]],\n          },\n        })),\n    };\n  };\n\n  v.links = function (data) {\n    if (data !== undefined) {\n      v(data);\n    }\n    if (!v.delaunay) return false;\n    const _distances = v.delaunay.edges.map((e) =>\n        geoDistance(v.points[e[0]], v.points[e[1]])\n      ),\n      _urquart = v.delaunay.urquhart(_distances);\n    return {\n      type: \"FeatureCollection\",\n      features: v.delaunay.edges.map((e, i) => ({\n        type: \"Feature\",\n        properties: {\n          source: v.valid[e[0]],\n          target: v.valid[e[1]],\n          length: _distances[i],\n          urquhart: !!_urquart[i],\n        },\n        geometry: {\n          type: \"LineString\",\n          coordinates: [v.points[e[0]], v.points[e[1]]],\n        },\n      })),\n    };\n  };\n\n  v.mesh = function (data) {\n    if (data !== undefined) {\n      v(data);\n    }\n    if (!v.delaunay) return false;\n    return {\n      type: \"MultiLineString\",\n      coordinates: v.delaunay.edges.map((e) => [\n        v.points[e[0]],\n        v.points[e[1]],\n      ]),\n    };\n  };\n\n  v.cellMesh = function (data) {\n    if (data !== undefined) {\n      v(data);\n    }\n    if (!v.delaunay) return false;\n    const { centers, polygons } = v.delaunay;\n    const coordinates = [];\n    for (const p of polygons) {\n      if (!p) continue;\n      for (\n        let n = p.length, p0 = p[n - 1], p1 = p[0], i = 0;\n        i < n;\n        p0 = p1, p1 = p[++i]\n      ) {\n        if (p1 > p0) {\n          coordinates.push([centers[p0], centers[p1]]);\n        }\n      }\n    }\n    return {\n      type: \"MultiLineString\",\n      coordinates,\n    };\n  };\n\n  v._found = undefined;\n  v.find = function (x, y, radius) {\n    v._found = v.delaunay.find(x, y, v._found);\n    if (!radius || geoDistance([x, y], v.points[v._found]) < radius)\n      return v._found;\n  };\n\n  v.hull = function (data) {\n    if (data !== undefined) {\n      v(data);\n    }\n    const hull = v.delaunay.hull,\n      points = v.points;\n    return hull.length === 0\n      ? null\n      : {\n          type: \"Polygon\",\n          coordinates: [[...hull.map((i) => points[i]), points[hull[0]]]],\n        };\n  };\n\n  return data ? v(data) : v;\n}\n", "// https://github.com/d3/d3-array/blob/master/src/extent.js\nexport default function(values) {\n  let min, max;\n  for (const value of values) {\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value;\n      } else {\n        if (min > value) min = value;\n        if (max < value) max = value;\n      }\n    }\n  }\n  return [min, max];\n}\n", "// https://github.com/d3/d3-array/blob/master/src/merge.js\nfunction* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\n\nexport default function(arrays) {\n  return Array.from(flatten(arrays));\n}\n", "// https://github.com/d3/d3-contour/blob/master/src/contains.js\nexport default function(ring, hole) {\n  const n = hole.length;\n  let i = -1;\n  while (++i < n) {\n    const c = ringContains(ring, hole[i]);\n    if (c) return c;\n  }\n  return 0;\n}\n\nfunction ringContains(ring, point) {\n  let x = point[0], y = point[1], contains = -1;\n  for (let i = 0, n = ring.length, j = n - 1; i < n; j = i++) {\n    const pi = ring[i], xi = pi[0], yi = pi[1], pj = ring[j], xj = pj[0], yj = pj[1];\n    if (segmentContains(pi, pj, point)) return 0;\n    if (((yi > y) !== (yj > y)) && ((x < (xj - xi) * (y - yi) / (yj - yi) + xi))) contains = -contains;\n  }\n  return contains;\n}\n\nfunction segmentContains(a, b, c) {\n  let i; return collinear(a, b, c) && within(a[i = +(a[0] === b[0])], c[i], b[i]);\n}\n\nfunction collinear(a, b, c) {\n  return (b[0] - a[0]) * (c[1] - a[1]) === (c[0] - a[0]) * (b[1] - a[1]);\n}\n\nfunction within(p, q, r) {\n  return p <= q && q <= r || r <= q && q <= p;\n}\n", "// https://github.com/d3/d3-contour/blob/master/src/area.js\nexport default function(ring) {\n  let i = 0, n = ring.length, area = ring[n - 1][1] * ring[0][0] - ring[n - 1][0] * ring[0][1];\n  while (++i < n) area += ring[i - 1][1] * ring[i][0] - ring[i - 1][0] * ring[i][1];\n  return area;\n}\n", "// sorts the polygons so that the holes are grouped with their parent polygon\n// https://github.com/d3/d3-contour/blob/master/src/contours.js\nimport contains from \"./contains.js\";\nimport area from \"./area.js\";\n\nexport default function(rings) {\n  const polygons = [];\n  const holes = [];\n\n  for (const ring of rings) {\n    if (area(ring) > 0) polygons.push([ring]);\n    else holes.push(ring);\n  }\n\n  holes.forEach(function(hole) {\n    for (let i = 0, n = polygons.length, polygon; i < n; ++i) {\n      if (contains((polygon = polygons[i])[0], hole) !== -1) {\n        polygon.push(hole);\n        return;\n      }\n    }\n  });\n\n  return polygons;\n}", "import {Delaunay} from \"d3-delaunay\";\nimport {scaleLinear} from \"d3-scale\";\nimport extent from \"./extent.js\";\nimport merge from \"./merge.js\";\nimport planarRingsort from \"./ringsort.js\";\n\nexport default function() {\n  // accessors\n  let x = d => d[0],\n    y = d => d[1],\n    value = d => (isFinite(+d[2]) ? +d[2] : 0),\n    triangulate = Delaunay.from,\n    pointInterpolate = (i, j, a) => {\n      const { points } = triangulation;\n      const A = [points[2 * i], points[2 * i + 1]],\n        B = [points[2 * j], points[2 * j + 1]];\n      return [a * B[0] + (1 - a) * A[0], a * B[1] + (1 - a) * A[1]];\n    },\n    ringsort = planarRingsort;\n\n  let thresholds, values, triangulation;\n\n  function init(points) {\n    triangulation = triangulate(points, x, y);\n    values = Array.from(points, value);\n    if (typeof thresholds !== \"object\") {\n      thresholds = scaleLinear()\n        .domain(extent(values))\n        .nice()\n        .ticks(thresholds);\n    }\n  }\n\n  function* tricontours(points) {\n    init(points);\n\n    for (const threshold of thresholds) {\n      const polygon = tricontour(triangulation, values, threshold);\n      yield {\n        type: \"MultiPolygon\",\n        coordinates: polygon,\n        value: threshold\n      };\n    }\n  }\n\n  function contour(points, threshold) {\n    init(points);\n\n    return {\n      type: \"MultiPolygon\",\n      coordinates: tricontour(triangulation, values, threshold),\n      value: threshold\n    };\n  }\n\n  function* isobands(points) {\n    init(points);\n\n    let p0, p1, th0;\n    for (const th of thresholds) {\n      if (p1) p0 = p1;\n      p1 = merge(tricontour(triangulation, values, th));\n      if (p0) {\n        yield {\n          type: \"MultiPolygon\",\n          coordinates: ringsort(\n            p0.concat(p1.map(ring => ring.slice().reverse()))\n          ),\n          value: th0,\n          valueMax: th\n        };\n      }\n      th0 = th;\n    }\n  }\n\n  const contours = function(data) {\n    return [...tricontours(data)];\n  };\n\n  // API\n  contours.x = _ => (_ ? ((x = _), contours) : x);\n  contours.y = _ => (_ ? ((y = _), contours) : y);\n  contours.value = _ => (_ ? ((value = _), contours) : value);\n  contours.thresholds = _ => (_ ? ((thresholds = _), contours) : thresholds);\n  contours.triangulate = _ => (_ ? ((triangulate = _), contours) : triangulate);\n  contours.pointInterpolate = _ =>\n    _ ? ((pointInterpolate = _), contours) : pointInterpolate;\n  contours.ringsort = _ =>\n    _ ? ((ringsort = _), contours) : ringsort;\n  contours.contours = tricontours;\n  contours.contour = contour;\n  contours.isobands = isobands;\n\n  // expose the internals (useful for debugging, not part of the API)\n  contours._values = () => values;\n  contours._triangulation = () => triangulation;\n\n  return contours;\n\n  // navigate a triangle\n  function next(i) {\n    return i % 3 === 2 ? i - 2 : i + 1;\n  }\n  function prev(i) {\n    return i % 3 === 0 ? i + 2 : i - 1;\n  }\n\n  function tricontour(triangulation, values, v0 = 0) {\n    // sanity check\n    for (const d of values) if (!isFinite(d)) throw [\"Invalid value\", d];\n\n    const { halfedges, hull, inedges, triangles } = triangulation,\n      n = values.length;\n\n    function edgealpha(i) {\n      return alpha(triangles[i], triangles[next(i)]);\n    }\n    function alpha(i, j) {\n      const u = values[i],\n        v = values[j];\n      if (u <= v0 && v >= v0 && u < v) {\n        return (v0 - u) / (v - u);\n      }\n    }\n\n    // create the path from the first exit; cancel visited halfedges\n    const rings = [],\n      visited = new Uint8Array(halfedges.length).fill(0);\n    let path, i, j, k, a;\n    for (k = 0; k < halfedges.length; k++) {\n      if (visited[k]) continue;\n\n      i = k;\n      path = [];\n\n      while ((a = edgealpha(i)) > 0) {\n        const [ti, tj] = [triangles[i], triangles[(j = next(i))]];\n\n        // is our tour done?\n        if (\n          (path.length && (ti === path[0].ti && tj === path[0].tj)) ||\n          path.length > 2 * n\n        )\n          break;\n\n        visited[i] = 1;\n        path.push({ ti, tj, a });\n\n        // jump into the adjacent triangle\n        if ((j = halfedges[i]) > -1) {\n          if (edgealpha((j = next(j))) > 0) {\n            i = j;\n            continue;\n          }\n          if (edgealpha((j = next(j))) > 0) {\n            i = j;\n            continue;\n          }\n          // debugger;\n        }\n\n        // or follow the hull\n        else {\n          let h = (hull.indexOf(triangles[i]) + 1) % hull.length;\n\n          while (values[hull[h]] < v0) {\n            // debugger;\n            h = (h + 1) % hull.length;\n          }\n\n          while (values[hull[h]] >= v0) {\n            path.push({ ti: hull[h], tj: hull[h], a: 0 });\n            h = (h + 1) % hull.length;\n          }\n\n          // take that entry\n          j = inedges[hull[h]];\n          path.push({\n            ti: hull[h],\n            tj: triangles[j],\n            a: alpha(hull[h], triangles[j])\n          });\n\n          if (edgealpha((i = next(j))) > 0) continue;\n          if (edgealpha((i = prev(j))) > 0) continue;\n        }\n      }\n\n      if (path.length) {\n        path.push(path[0]);\n        rings.push(path.map(({ ti, tj, a }) => pointInterpolate(ti, tj, a)));\n      }\n    }\n\n    // special case all values on the hull are >=v0, add the hull\n    if (hull.every(d => values[d] >= v0)) {\n      rings.unshift(\n        Array.from(hull)\n          .concat([hull[0]])\n          .map(i => pointInterpolate(i, i, 0))\n      );\n    }\n\n    return ringsort(rings); // return [rings] if we don't need to sort\n  }\n}", "import { geoD<PERSON><PERSON>y } from \"./delaunay.js\";\nimport { geoInterpolate } from \"d3-geo\";\nimport { tricontour } from \"d3-tricontour\";\n\nexport function geoContour() {\n  let v;\n  const contour = tricontour()\n    .triangulate((data, x, y) => {\n      v = geoDelaunay(data.map((d, i) => [x(d, i), y(d, i)]));\n      return v.delaunay;\n    })\n    .pointInterpolate((i, j, a) => {\n      const { points, projection } = v.delaunay;\n      const A = projection.invert([points[2 * i], points[2 * i + 1]]),\n        B = projection.invert([points[2 * j], points[2 * j + 1]]);\n      return geoInterpolate(A, B)(a);\n    })\n    .ringsort((rings) => {\n      // tricky thing: in isobands this function is called twice,\n      // we want to reverse the polygons’s winding order only in tricontour()\n      // not in isoband()\n      if (rings.length && !rings[0].reversed) {\n        rings.forEach((ring) => ring.reverse());\n        rings[0].reversed = true;\n      }\n      return [rings];\n    });\n\n  return contour;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAEO,IAAM,KAAK,KAAK;AAChB,IAAM,SAAS,KAAK;AACpB,IAAM,YAAY,KAAK;AACvB,IAAM,MAAM,KAAK;AAEjB,IAAM,UAAU,MAAM;AACtB,IAAM,UAAU,KAAK;AAIrB,IAAM,QAAQ,KAAK;AACnB,IAAM,MAAM,KAAK;AAKjB,IAAM,MAAM,KAAK;AACjB,IAAM,MAAM,KAAK;AAEjB,IAAM,MAAM,KAAK;AACjB,IAAM,OACX,KAAK,QACL,SAAU,GAAG;AACX,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AAClC;AACK,IAAM,OAAO,KAAK;AAOlB,SAAS,KAAK,GAAG;AACtB,SAAO,IAAI,IAAI,SAAS,IAAI,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC;AACxD;;;ACvBO,SAAS,aAAa,GAAG,GAAG;AACjC,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAC/C;AAEO,SAAS,eAAe,GAAG,GAAG;AACnC,SAAO;AAAA,IACL,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IACxB,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IACxB,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EAC1B;AACF;AAOO,SAAS,aAAa,GAAG,GAAG;AACjC,SAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAC/C;AAYO,SAAS,mBAAmB,GAAG;AACpC,MAAI,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AACpD,SAAO,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACtC;;;ACxBA,SAAS,UAAUA,YAAW;AAC5B,SAAO;AAAA,IACL,MAAMA,WAAU,CAAC,GAAGA,WAAU,CAAC,CAAC,IAAI;AAAA,IACpC,KAAK,IAAI,IAAI,IAAI,GAAGA,WAAU,CAAC,CAAC,CAAC,CAAC,IAAI;AAAA,EACxC;AACF;AAGA,SAAS,UAAU,aAAa;AAC9B,QAAM,SAAS,YAAY,CAAC,IAAI,SAC9B,MAAM,YAAY,CAAC,IAAI,SACvB,SAAS,IAAI,GAAG;AAClB,SAAO,CAAC,SAAS,IAAI,MAAM,GAAG,SAAS,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC;AAC9D;AAGO,SAAS,OAAO,UAAU;AAC/B,aAAW,SAAS,IAAI,CAAC,MAAM,UAAU,CAAC,CAAC;AAC3C,SAAO,aAAI,SAAS,CAAC,GAAG,eAAM,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;AACzD;AAEO,SAAS,YAAY,QAAQ;AAClC,QAAM,WAAW,kBAAkB,MAAM,GACvC,YAAY,cAAc,QAAQ,GAClC,QAAQ,UAAU,WAAW,MAAM,GACnC,YAAY,cAAc,WAAW,OAAO,MAAM,GAClD,OAAO,SAAS,WAAW,MAAM,GAEjC,gBAAgB,kBAAkB,WAAW,MAAM,GACnD,EAAE,UAAU,QAAQ,IAAI,aAAa,eAAe,WAAW,MAAM,GACrE,OAAO,SAAS,QAAQ,GACxB,OAAO,SAAS,WAAW,MAAM,GAEjC,WAAW,aAAa,OAAO,SAAS;AAC1C,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,SAAS,WAAW,QAAQ;AACnC,WAAS,UAAU,GAAG,GAAG;AACvB,QAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAChB,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GACd,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAChB,WAAO,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,EAC7B;AAEA,SAAO,SAAS,KAAK,GAAG,GAAG,MAAM;AAC/B,QAAI,SAAS,OAAW,QAAO;AAC/B,QAAI,MACF,MACA,QAAQ;AACV,UAAM,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC;AAC5B,OAAG;AACD,aAAO;AACP,aAAO;AACP,aAAO,UAAU,KAAK,UAAU,OAAO,IAAI,CAAC,CAAC;AAC7C,gBAAU,IAAI,EAAE,QAAQ,CAAC,MAAM;AAC7B,YAAI,QAAQ,UAAU,KAAK,UAAU,OAAO,CAAC,CAAC,CAAC;AAC/C,YAAI,QAAQ,MAAM;AAChB,iBAAO;AACP,iBAAO;AACP,kBAAQ;AACR;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,SAAS,SAAS;AAElB,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAkB,QAAQ;AACjC,MAAI,OAAO,SAAS,EAAG,QAAO,CAAC;AAG/B,MAAI,QAAQ;AACZ,SAAO,MAAM,OAAO,KAAK,EAAE,CAAC,IAAI,OAAO,KAAK,EAAE,CAAC,CAAC,KAAK,UAAU,OAAO,OAAO;AAE7E,QAAM,IAAI,iBAAY,OAAO,KAAK,CAAC,GACjC,aAAa,sBAAiB,EAC3B,UAAU,CAAC,GAAG,CAAC,CAAC,EAChB,MAAM,CAAC,EACP,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9B,WAAS,OAAO,IAAI,UAAU;AAE9B,QAAM,QAAQ,CAAC;AACf,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,QAAI,IAAI,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,OAAO,CAAC,EAAE,CAAC,KAAK;AAC5C,QAAI,CAAC,SAAS,CAAC,KAAK,IAAI,KAAM,OAAM,KAAK,CAAC;AAAA,aACjC,IAAI,KAAM,QAAO;AAAA,EAC5B;AAEA,QAAM,MAAM,MAAM,KAAK,IAAI;AAE3B,QAAM,QAAQ,CAAC,MAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAE;AAG3C,SAAO,KAAK,CAAC,GAAG,GAAG,CAAC;AACpB,SAAO,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;AACrB,SAAO,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;AAErB,QAAM,WAAW,SAAS,KAAK,MAAM;AAErC,WAAS,aAAa;AAGtB,QAAM,EAAE,WAAW,WAAW,QAAQ,IAAI;AAC1C,QAAM,aAAa,CAAC;AACpB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAChD,QAAI,UAAU,CAAC,IAAI,GAAG;AACpB,YAAM,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI;AACnC,YAAM,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI;AACnC,YAAM,IAAI,UAAU,CAAC;AACrB,YAAM,IAAI,UAAU,CAAC;AACrB,gBAAU,CAAC,IAAI;AACf,gBAAU,CAAC,IAAI;AACf,gBAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AAC9B,gBAAU,CAAC,IAAI,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AAC7C,cAAQ,UAAU,CAAC,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI;AACjD,cAAQ,UAAU,CAAC,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI;AACjD,iBAAW,KAAK,KAAK,IAAI,GAAG,GAAG,CAAC,CAAC;AACjC,WAAK,IAAK,IAAI;AAAA,IAChB,WAAW,UAAU,CAAC,IAAI,OAAO,SAAS,IAAI,GAAG;AAC/C,gBAAU,CAAC,IAAI;AAAA,IACjB;AAAA,EACF;AAIA,SAAO;AACT;AAEA,SAAS,UAAU,WAAW,QAAQ;AACpC,QAAM,SAAS,oBAAI,IAAI;AACvB,MAAI,OAAO,WAAW,EAAG,QAAO,CAAC,CAAC,GAAG,CAAC,CAAC;AACvC,YAAU,QAAQ,CAAC,QAAQ;AACzB,QAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAG;AACvB,QAAI,OAAO,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,IAAI,EAAG;AAC3C,aAAS,IAAI,GAAG,GAAG,IAAI,GAAG,KAAK;AAC7B,WAAK,IAAI,KAAK;AACd,aAAO,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,IAC/C;AAAA,EACF,CAAC;AACD,SAAO,MAAM,KAAK,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,IAAI,MAAM,CAAC;AAC3D;AAEA,SAAS,cAAc,UAAU;AAC/B,QAAM,EAAE,UAAU,IAAI;AACtB,MAAI,CAAC,UAAW,QAAO,CAAC;AAExB,QAAMC,iBAAgB,CAAC;AACvB,WAAS,IAAI,GAAG,IAAI,UAAU,SAAS,GAAG,IAAI,GAAG,KAAK;AACpD,UAAM,IAAI,UAAU,IAAI,CAAC,GACvB,IAAI,UAAU,IAAI,IAAI,CAAC,GACvB,IAAI,UAAU,IAAI,IAAI,CAAC;AACzB,QAAI,MAAM,KAAK,MAAM,GAAG;AACtB,MAAAA,eAAc,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF;AACA,SAAOA;AACT;AAEA,SAAS,kBAAkB,WAAW,QAAQ;AAE5C,SAAO,UAAU,IAAI,CAAC,QAAQ;AAC5B,UAAM,IAAI,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,IAAI,SAAS,GAC/C,IAAI;AAAA,MACF,aAAa,eAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,eAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAAA,MACjD,eAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IAClB;AACF,WAAO,UAAU,mBAAU,CAAC,CAAC;AAAA,EAC/B,CAAC;AASH;AAEA,SAAS,cAAc,WAAW,SAAS;AACzC,QAAM,YAAY,CAAC;AACnB,YAAU,QAAQ,CAAC,QAAQ;AACzB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,IAAI,IAAI,CAAC,GACb,IAAI,KAAK,IAAI,KAAK,CAAC;AACrB,gBAAU,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC;AAChC,gBAAU,CAAC,EAAE,KAAK,CAAC;AAAA,IACrB;AAAA,EACF,CAAC;AAGD,MAAI,UAAU,WAAW,GAAG;AAC1B,QAAI,YAAY,EAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAK,UAAU,CAAC,IAAI,CAAC,CAAC;AAAA,aAClD,YAAY,EAAG,WAAU,CAAC,IAAI,CAAC;AAAA,EAC1C;AAEA,SAAO;AACT;AAEA,SAAS,aAAa,eAAe,WAAW,QAAQ;AACtD,QAAM,WAAW,CAAC;AAElB,QAAM,UAAU,cAAc,MAAM;AAEpC,MAAI,UAAU,WAAW,GAAG;AAC1B,QAAI,OAAO,SAAS,EAAG,QAAO,EAAE,UAAU,QAAQ;AAClD,QAAI,OAAO,WAAW,GAAG;AAEvB,YAAM,IAAI,UAAU,OAAO,CAAC,CAAC,GAC3B,IAAI,UAAU,OAAO,CAAC,CAAC,GACvB,IAAI,mBAAU,aAAa,GAAG,CAAC,CAAC,GAChC,IAAI,mBAAU,eAAM,GAAG,CAAC,CAAC,GACzB,IAAI,eAAM,GAAG,CAAC;AAChB,YAAM,OAAO;AAAA,QACX;AAAA,QACA,eAAM,GAAG,CAAC;AAAA,QACV,eAAM,eAAM,GAAG,CAAC,GAAG,CAAC;AAAA,QACpB,eAAM,eAAM,eAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AAAA,MAChC,EACG,IAAI,SAAS,EACb,IAAI,UAAU;AACjB,aACE,SAAS,KAAK,IAAI,GAClB,SAAS,KAAK,KAAK,MAAM,EAAE,QAAQ,CAAC,GACpC,EAAE,UAAU,QAAQ;AAAA,IAExB;AAAA,EACF;AAEA,YAAU,QAAQ,CAAC,KAAK,MAAM;AAC5B,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,IAAI,IAAI,CAAC,GACb,IAAI,KAAK,IAAI,KAAK,CAAC,GACnB,IAAI,KAAK,IAAI,KAAK,CAAC;AACrB,eAAS,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC;AAC9B,eAAS,CAAC,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAAA,IACvC;AAAA,EACF,CAAC;AAGD,QAAM,YAAY,SAAS,IAAI,CAAC,SAAS;AACvC,UAAM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACrB,QAAI,IAAI,KAAK,CAAC,EAAE,CAAC;AACjB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAEpC,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAI,KAAK,CAAC,EAAE,CAAC,KAAK,GAAG;AACnB,cAAI,KAAK,CAAC,EAAE,CAAC;AACb,YAAE,KAAK,KAAK,CAAC,EAAE,CAAC,CAAC;AACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,EAAE,SAAS,GAAG;AAChB,aAAO;AAAA,IACT,WAAW,EAAE,UAAU,GAAG;AACxB,YAAM,KAAK;AAAA,QACP,OAAO,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,QACpB,OAAO,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,QACpB,QAAQ,EAAE,CAAC,CAAC;AAAA,MACd,GACA,KAAK;AAAA,QACH,OAAO,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,QACpB,OAAO,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAAA,QACpB,QAAQ,EAAE,CAAC,CAAC;AAAA,MACd;AACF,YAAM,KAAK,WAAW,EAAE,GACtB,KAAK,WAAW,EAAE;AACpB,aAAO,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,EAAE;AAAA,IAC5B;AAAA,EACF,CAAC;AAED,WAAS,WAAW,OAAO;AACzB,QAAI,IAAI;AACR,YAAQ,MAAM,UAAU,QAAQ,QAAQ,EAAE,QAAQ,CAAC,GAAG,MAAM;AAC1D,UAAI,EAAE,CAAC,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,MAAM,MAAM,CAAC,EAAG,KAAI,IAAI,UAAU;AAAA,IAChE,CAAC;AACD,QAAI,IAAI,EAAG,CAAC,IAAI,QAAQ,QAAS,QAAQ,KAAK,KAAK;AACnD,WAAO;AAAA,EACT;AAEA,SAAO,EAAE,UAAU,WAAW,QAAQ;AACxC;AAEA,SAAS,WAAW,GAAG,GAAG,GAAG;AAC3B,MAAI,UAAU,CAAC;AACf,MAAI,UAAU,CAAC;AACf,MAAI,UAAU,CAAC;AACf,QAAM,IAAI,KAAK,aAAI,eAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AAClC,SAAO,UAAU,mBAAU,aAAa,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;AAClE;AAEA,SAAS,SAAS,UAAU;AAC1B,QAAM,OAAO,CAAC;AACd,WAAS,QAAQ,CAAC,SAAS;AACzB,QAAI,CAAC,KAAM;AACX,QAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC5B,aAAS,KAAK,MAAM;AAClB,UAAI,IAAI,EAAG,MAAK,KAAK,CAAC,GAAG,CAAC,CAAC;AAC3B,UAAI;AAAA,IACN;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,aAAa,OAAO,WAAW;AACtC,SAAO,SAAU,WAAW;AAC1B,UAAM,WAAW,oBAAI,IAAI,GACvB,YAAY,oBAAI,IAAI;AACtB,UAAM,QAAQ,CAAC,MAAM,MAAM;AACzB,YAAM,IAAI,KAAK,KAAK,GAAG;AACvB,eAAS,IAAI,GAAG,UAAU,CAAC,CAAC;AAC5B,gBAAU,IAAI,GAAG,IAAI;AAAA,IACvB,CAAC;AAED,cAAU,QAAQ,CAAC,QAAQ;AACzB,UAAI,IAAI,GACN,SAAS;AACX,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAI,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG;AACnD,YAAI,SAAS,IAAI,CAAC,IAAI,GAAG;AACvB,cAAI,SAAS,IAAI,CAAC;AAClB,mBAAS;AAAA,QACX;AAAA,MACF;AACA,gBAAU,IAAI,QAAQ,KAAK;AAAA,IAC7B,CAAC;AAED,WAAO,MAAM,IAAI,CAAC,SAAS,UAAU,IAAI,KAAK,KAAK,GAAG,CAAC,CAAC;AAAA,EAC1D;AACF;AAEA,SAAS,SAAS,WAAW,QAAQ;AACnC,QAAM,QAAQ,oBAAI,IAAI,GACpB,OAAO,CAAC;AACV,YAAU,IAAI,CAAC,QAAQ;AACrB,QAAI,OAAO,IAAI,IAAI,CAAC,MAAM,OAAO,IAAI,OAAO,SAAS,IAAI,CAAC,CAAC,CAAC,IAAI;AAC9D;AACF,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAI,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,KAAK,CAAC,CAAC,GAC/B,OAAO,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AACxB,UAAI,MAAM,IAAI,IAAI,EAAG,OAAM,OAAO,IAAI;AAAA,UACjC,OAAM,IAAI,GAAG,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE;AAAA,IAClC;AAAA,EACF,CAAC;AAED,QAAM,SAAS,oBAAI,IAAI;AACvB,MAAI;AACJ,QAAM,QAAQ,CAAC,MAAM;AACnB,QAAI,EAAE,MAAM,GAAG,EAAE,IAAI,MAAM;AAC3B,WAAO,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACrB,YAAQ,EAAE,CAAC;AAAA,EACb,CAAC;AAED,MAAI,UAAU,OAAW,QAAO;AAEhC,MAAI,OAAO;AACX,KAAG;AACD,SAAK,KAAK,IAAI;AACd,QAAI,IAAI,OAAO,IAAI,IAAI;AACvB,WAAO,IAAI,MAAM,EAAE;AACnB,WAAO;AAAA,EACT,SAAS,OAAO,MAAM,SAAS;AAE/B,SAAO;AACT;;;AChZO,SAAS,WAAW,MAAM;AAC/B,QAAM,IAAI,SAAUC,OAAM;AACxB,MAAE,WAAW;AACb,MAAE,QAAQA;AAEV,QAAI,OAAO,EAAE,UAAU,YAAY,EAAE,MAAM,SAAS,qBAAqB;AACvE,QAAE,QAAQ,EAAE,MAAM;AAAA,IACpB;AACA,QAAI,OAAO,EAAE,UAAU,UAAU;AAC/B,YAAM,OAAO,EAAE,MACZ,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAClC,OAAO,CAAC,MAAM,SAAS,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACtC,QAAE,SAAS,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACvC,QAAE,QAAQ,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;AAC9B,QAAE,WAAW,YAAY,EAAE,MAAM;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAEA,IAAE,MAAM,SAAU,GAAG;AACnB,QAAI,OAAO,KAAK,YAAY,UAAU,GAAG;AACvC,aAAO,iBAAY,CAAC,EAAE,CAAC;AAAA,IACzB;AACA,QAAI,KAAK,EAAG,QAAO,EAAE,CAAC;AAAA,EACxB;AACA,IAAE,MAAM,SAAU,GAAG;AACnB,QAAI,OAAO,KAAK,YAAY,UAAU,GAAG;AACvC,aAAO,iBAAY,CAAC,EAAE,CAAC;AAAA,IACzB;AACA,QAAI,KAAK,EAAG,QAAO,EAAE,CAAC;AAAA,EACxB;AAEA,IAAE,IAAI,SAAU,GAAG;AACjB,QAAI,CAAC,EAAG,QAAO,EAAE;AACjB,MAAE,MAAM;AACR,WAAO;AAAA,EACT;AACA,IAAE,IAAI,SAAU,GAAG;AACjB,QAAI,CAAC,EAAG,QAAO,EAAE;AACjB,MAAE,MAAM;AACR,WAAO;AAAA,EACT;AAEA,IAAE,WAAW,SAAUA,OAAM;AAC3B,QAAIA,UAAS,QAAW;AACtB,QAAEA,KAAI;AAAA,IACR;AAEA,QAAI,CAAC,EAAE,SAAU,QAAO;AACxB,UAAM,OAAO;AAAA,MACX,MAAM;AAAA,MACN,UAAU,CAAC;AAAA,IACb;AACA,QAAI,EAAE,MAAM,WAAW,EAAG,QAAO;AACjC,MAAE,SAAS,SAAS;AAAA,MAAQ,CAAC,MAAM,MACjC,KAAK,SAAS,KAAK;AAAA,QACjB,MAAM;AAAA,QACN,UAAU,CAAC,OACP,OACA;AAAA,UACE,MAAM;AAAA,UACN,aAAa;AAAA,YACX,CAAC,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,IAAI,CAACC,OAAM,EAAE,SAAS,QAAQA,EAAC,CAAC;AAAA,UACrD;AAAA,QACF;AAAA,QACJ,YAAY;AAAA,UACV,MAAM,EAAE,MAAM,CAAC;AAAA,UACf,iBAAiB,EAAE,OAAO,CAAC;AAAA,UAC3B,YAAY,EAAE,SAAS,UAAU,CAAC;AAAA;AAAA,QACpC;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,EAAE,MAAM,WAAW;AACrB,WAAK,SAAS,KAAK;AAAA,QACjB,MAAM;AAAA,QACN,UAAU,EAAE,MAAM,SAAS;AAAA,QAC3B,YAAY;AAAA,UACV,MAAM,EAAE,MAAM,CAAC;AAAA,UACf,iBAAiB,EAAE,OAAO,CAAC;AAAA,UAC3B,YAAY,CAAC;AAAA,QACf;AAAA,MACF,CAAC;AACH,WAAO;AAAA,EACT;AAEA,IAAE,YAAY,SAAUD,OAAM;AAC5B,QAAIA,UAAS,QAAW;AACtB,QAAEA,KAAI;AAAA,IACR;AACA,QAAI,CAAC,EAAE,SAAU,QAAO;AAExB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU,EAAE,SAAS,UAClB,IAAI,CAAC,KAAK,UAAU;AACnB,cAAM,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAChC,YAAI,SAAS,EAAE,SAAS,QAAQ,KAAK;AACrC,eAAO;AAAA,MACT,CAAC,EACA,OAAO,CAAC,QAAQ,OAAO,GAAG,IAAI,CAAC,EAC/B,IAAI,CAAC,SAAS;AAAA,QACb,MAAM;AAAA,QACN,YAAY;AAAA,UACV,cAAc,IAAI;AAAA,QACpB;AAAA,QACA,UAAU;AAAA,UACR,MAAM;AAAA,UACN,aAAa,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC;AAAA,QAChC;AAAA,MACF,EAAE;AAAA,IACN;AAAA,EACF;AAEA,IAAE,QAAQ,SAAUA,OAAM;AACxB,QAAIA,UAAS,QAAW;AACtB,QAAEA,KAAI;AAAA,IACR;AACA,QAAI,CAAC,EAAE,SAAU,QAAO;AACxB,UAAM,aAAa,EAAE,SAAS,MAAM;AAAA,MAAI,CAAC,MACrC,iBAAY,EAAE,OAAO,EAAE,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAAA,IAC5C,GACA,WAAW,EAAE,SAAS,SAAS,UAAU;AAC3C,WAAO;AAAA,MACL,MAAM;AAAA,MACN,UAAU,EAAE,SAAS,MAAM,IAAI,CAAC,GAAG,OAAO;AAAA,QACxC,MAAM;AAAA,QACN,YAAY;AAAA,UACV,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;AAAA,UACpB,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;AAAA,UACpB,QAAQ,WAAW,CAAC;AAAA,UACpB,UAAU,CAAC,CAAC,SAAS,CAAC;AAAA,QACxB;AAAA,QACA,UAAU;AAAA,UACR,MAAM;AAAA,UACN,aAAa,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAAA,QAC9C;AAAA,MACF,EAAE;AAAA,IACJ;AAAA,EACF;AAEA,IAAE,OAAO,SAAUA,OAAM;AACvB,QAAIA,UAAS,QAAW;AACtB,QAAEA,KAAI;AAAA,IACR;AACA,QAAI,CAAC,EAAE,SAAU,QAAO;AACxB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,aAAa,EAAE,SAAS,MAAM,IAAI,CAAC,MAAM;AAAA,QACvC,EAAE,OAAO,EAAE,CAAC,CAAC;AAAA,QACb,EAAE,OAAO,EAAE,CAAC,CAAC;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF;AAEA,IAAE,WAAW,SAAUA,OAAM;AAC3B,QAAIA,UAAS,QAAW;AACtB,QAAEA,KAAI;AAAA,IACR;AACA,QAAI,CAAC,EAAE,SAAU,QAAO;AACxB,UAAM,EAAE,SAAS,SAAS,IAAI,EAAE;AAChC,UAAM,cAAc,CAAC;AACrB,eAAW,KAAK,UAAU;AACxB,UAAI,CAAC,EAAG;AACR,eACM,IAAI,EAAE,QAAQ,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,IAAI,GAChD,IAAI,GACJ,KAAK,IAAI,KAAK,EAAE,EAAE,CAAC,GACnB;AACA,YAAI,KAAK,IAAI;AACX,sBAAY,KAAK,CAAC,QAAQ,EAAE,GAAG,QAAQ,EAAE,CAAC,CAAC;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,IACF;AAAA,EACF;AAEA,IAAE,SAAS;AACX,IAAE,OAAO,SAAU,GAAG,GAAG,QAAQ;AAC/B,MAAE,SAAS,EAAE,SAAS,KAAK,GAAG,GAAG,EAAE,MAAM;AACzC,QAAI,CAAC,UAAU,iBAAY,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,IAAI;AACvD,aAAO,EAAE;AAAA,EACb;AAEA,IAAE,OAAO,SAAUA,OAAM;AACvB,QAAIA,UAAS,QAAW;AACtB,QAAEA,KAAI;AAAA,IACR;AACA,UAAM,OAAO,EAAE,SAAS,MACtB,SAAS,EAAE;AACb,WAAO,KAAK,WAAW,IACnB,OACA;AAAA,MACE,MAAM;AAAA,MACN,aAAa,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,GAAG,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;AAAA,IAChE;AAAA,EACN;AAEA,SAAO,OAAO,EAAE,IAAI,IAAI;AAC1B;;;AC3Me,SAAR,eAAiB,QAAQ;AAC9B,MAAIE,MAAKC;AACT,aAAW,SAAS,QAAQ;AAC1B,QAAI,SAAS,MAAM;AACjB,UAAID,SAAQ,QAAW;AACrB,YAAI,SAAS,MAAO,CAAAA,OAAMC,OAAM;AAAA,MAClC,OAAO;AACL,YAAID,OAAM,MAAO,CAAAA,OAAM;AACvB,YAAIC,OAAM,MAAO,CAAAA,OAAM;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAACD,MAAKC,IAAG;AAClB;;;ACbA,UAAU,QAAQ,QAAQ;AACxB,aAAW,SAAS,QAAQ;AAC1B,WAAO;AAAA,EACT;AACF;AAEe,SAAR,cAAiB,QAAQ;AAC9B,SAAO,MAAM,KAAK,QAAQ,MAAM,CAAC;AACnC;;;ACRe,SAAR,iBAAiB,MAAM,MAAM;AAClC,QAAM,IAAI,KAAK;AACf,MAAI,IAAI;AACR,SAAO,EAAE,IAAI,GAAG;AACd,UAAM,IAAI,aAAa,MAAM,KAAK,CAAC,CAAC;AACpC,QAAI,EAAG,QAAO;AAAA,EAChB;AACA,SAAO;AACT;AAEA,SAAS,aAAa,MAAM,OAAO;AACjC,MAAI,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,WAAW;AAC3C,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK;AAC1D,UAAMC,MAAK,KAAK,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC;AAC/E,QAAI,gBAAgBA,KAAI,IAAI,KAAK,EAAG,QAAO;AAC3C,QAAM,KAAK,MAAQ,KAAK,KAAS,KAAK,KAAK,OAAO,IAAI,OAAO,KAAK,MAAM,GAAM,YAAW,CAAC;AAAA,EAC5F;AACA,SAAO;AACT;AAEA,SAAS,gBAAgB,GAAG,GAAG,GAAG;AAChC,MAAI;AAAG,SAAO,UAAU,GAAG,GAAG,CAAC,KAAK,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAChF;AAEA,SAAS,UAAU,GAAG,GAAG,GAAG;AAC1B,UAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;AACtE;AAEA,SAAS,OAAO,GAAG,GAAG,GAAG;AACvB,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C;;;AC9Be,SAAR,aAAiB,MAAM;AAC5B,MAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,OAAO,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAC3F,SAAO,EAAE,IAAI,EAAG,SAAQ,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;AAChF,SAAO;AACT;;;ACAe,SAAR,iBAAiB,OAAO;AAC7B,QAAM,WAAW,CAAC;AAClB,QAAM,QAAQ,CAAC;AAEf,aAAW,QAAQ,OAAO;AACxB,QAAI,aAAK,IAAI,IAAI,EAAG,UAAS,KAAK,CAAC,IAAI,CAAC;AAAA,QACnC,OAAM,KAAK,IAAI;AAAA,EACtB;AAEA,QAAM,QAAQ,SAAS,MAAM;AAC3B,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,SAAS,IAAI,GAAG,EAAE,GAAG;AACxD,UAAI,kBAAU,UAAU,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,MAAM,IAAI;AACrD,gBAAQ,KAAK,IAAI;AACjB;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;;;AClBe,SAAR,qBAAmB;AAExB,MAAI,IAAI,OAAK,EAAE,CAAC,GACd,IAAI,OAAK,EAAE,CAAC,GACZ,QAAQ,OAAM,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GACxC,cAAc,SAAS,MACvB,mBAAmB,CAAC,GAAG,GAAG,MAAM;AAC9B,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,GACzC,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC;AACvC,WAAO,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,CAAC;AAAA,EAC9D,GACA,WAAW;AAEb,MAAI,YAAY,QAAQ;AAExB,WAAS,KAAK,QAAQ;AACpB,oBAAgB,YAAY,QAAQ,GAAG,CAAC;AACxC,aAAS,MAAM,KAAK,QAAQ,KAAK;AACjC,QAAI,OAAO,eAAe,UAAU;AAClC,mBAAa,OAAY,EACtB,OAAO,eAAO,MAAM,CAAC,EACrB,KAAK,EACL,MAAM,UAAU;AAAA,IACrB;AAAA,EACF;AAEA,YAAU,YAAY,QAAQ;AAC5B,SAAK,MAAM;AAEX,eAAW,aAAa,YAAY;AAClC,YAAM,UAAU,WAAW,eAAe,QAAQ,SAAS;AAC3D,YAAM;AAAA,QACJ,MAAM;AAAA,QACN,aAAa;AAAA,QACb,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,WAAS,QAAQ,QAAQ,WAAW;AAClC,SAAK,MAAM;AAEX,WAAO;AAAA,MACL,MAAM;AAAA,MACN,aAAa,WAAW,eAAe,QAAQ,SAAS;AAAA,MACxD,OAAO;AAAA,IACT;AAAA,EACF;AAEA,YAAU,SAAS,QAAQ;AACzB,SAAK,MAAM;AAEX,QAAI,IAAI,IAAI;AACZ,eAAW,MAAM,YAAY;AAC3B,UAAI,GAAI,MAAK;AACb,WAAK,cAAM,WAAW,eAAe,QAAQ,EAAE,CAAC;AAChD,UAAI,IAAI;AACN,cAAM;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,YACX,GAAG,OAAO,GAAG,IAAI,UAAQ,KAAK,MAAM,EAAE,QAAQ,CAAC,CAAC;AAAA,UAClD;AAAA,UACA,OAAO;AAAA,UACP,UAAU;AAAA,QACZ;AAAA,MACF;AACA,YAAM;AAAA,IACR;AAAA,EACF;AAEA,QAAM,WAAW,SAAS,MAAM;AAC9B,WAAO,CAAC,GAAG,YAAY,IAAI,CAAC;AAAA,EAC9B;AAGA,WAAS,IAAI,OAAM,KAAM,IAAI,GAAI,YAAY;AAC7C,WAAS,IAAI,OAAM,KAAM,IAAI,GAAI,YAAY;AAC7C,WAAS,QAAQ,OAAM,KAAM,QAAQ,GAAI,YAAY;AACrD,WAAS,aAAa,OAAM,KAAM,aAAa,GAAI,YAAY;AAC/D,WAAS,cAAc,OAAM,KAAM,cAAc,GAAI,YAAY;AACjE,WAAS,mBAAmB,OAC1B,KAAM,mBAAmB,GAAI,YAAY;AAC3C,WAAS,WAAW,OAClB,KAAM,WAAW,GAAI,YAAY;AACnC,WAAS,WAAW;AACpB,WAAS,UAAU;AACnB,WAAS,WAAW;AAGpB,WAAS,UAAU,MAAM;AACzB,WAAS,iBAAiB,MAAM;AAEhC,SAAO;AAGP,WAAS,KAAK,GAAG;AACf,WAAO,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI;AAAA,EACnC;AACA,WAAS,KAAK,GAAG;AACf,WAAO,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI;AAAA,EACnC;AAEA,WAAS,WAAWC,gBAAeC,SAAQ,KAAK,GAAG;AAEjD,eAAW,KAAKA,QAAQ,KAAI,CAAC,SAAS,CAAC,EAAG,OAAM,CAAC,iBAAiB,CAAC;AAEnE,UAAM,EAAE,WAAW,MAAM,SAAS,UAAU,IAAID,gBAC9C,IAAIC,QAAO;AAEb,aAAS,UAAUC,IAAG;AACpB,aAAO,MAAM,UAAUA,EAAC,GAAG,UAAU,KAAKA,EAAC,CAAC,CAAC;AAAA,IAC/C;AACA,aAAS,MAAMA,IAAGC,IAAG;AACnB,YAAM,IAAIF,QAAOC,EAAC,GAChB,IAAID,QAAOE,EAAC;AACd,UAAI,KAAK,MAAM,KAAK,MAAM,IAAI,GAAG;AAC/B,gBAAQ,KAAK,MAAM,IAAI;AAAA,MACzB;AAAA,IACF;AAGA,UAAM,QAAQ,CAAC,GACb,UAAU,IAAI,WAAW,UAAU,MAAM,EAAE,KAAK,CAAC;AACnD,QAAI,MAAM,GAAG,GAAG,GAAG;AACnB,SAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACrC,UAAI,QAAQ,CAAC,EAAG;AAEhB,UAAI;AACJ,aAAO,CAAC;AAER,cAAQ,IAAI,UAAU,CAAC,KAAK,GAAG;AAC7B,cAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,UAAW,IAAI,KAAK,CAAC,CAAE,CAAC;AAGxD,YACG,KAAK,WAAW,OAAO,KAAK,CAAC,EAAE,MAAM,OAAO,KAAK,CAAC,EAAE,OACrD,KAAK,SAAS,IAAI;AAElB;AAEF,gBAAQ,CAAC,IAAI;AACb,aAAK,KAAK,EAAE,IAAI,IAAI,EAAE,CAAC;AAGvB,aAAK,IAAI,UAAU,CAAC,KAAK,IAAI;AAC3B,cAAI,UAAW,IAAI,KAAK,CAAC,CAAE,IAAI,GAAG;AAChC,gBAAI;AACJ;AAAA,UACF;AACA,cAAI,UAAW,IAAI,KAAK,CAAC,CAAE,IAAI,GAAG;AAChC,gBAAI;AACJ;AAAA,UACF;AAAA,QAEF,OAGK;AACH,cAAI,KAAK,KAAK,QAAQ,UAAU,CAAC,CAAC,IAAI,KAAK,KAAK;AAEhD,iBAAOF,QAAO,KAAK,CAAC,CAAC,IAAI,IAAI;AAE3B,iBAAK,IAAI,KAAK,KAAK;AAAA,UACrB;AAEA,iBAAOA,QAAO,KAAK,CAAC,CAAC,KAAK,IAAI;AAC5B,iBAAK,KAAK,EAAE,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;AAC5C,iBAAK,IAAI,KAAK,KAAK;AAAA,UACrB;AAGA,cAAI,QAAQ,KAAK,CAAC,CAAC;AACnB,eAAK,KAAK;AAAA,YACR,IAAI,KAAK,CAAC;AAAA,YACV,IAAI,UAAU,CAAC;AAAA,YACf,GAAG,MAAM,KAAK,CAAC,GAAG,UAAU,CAAC,CAAC;AAAA,UAChC,CAAC;AAED,cAAI,UAAW,IAAI,KAAK,CAAC,CAAE,IAAI,EAAG;AAClC,cAAI,UAAW,IAAI,KAAK,CAAC,CAAE,IAAI,EAAG;AAAA,QACpC;AAAA,MACF;AAEA,UAAI,KAAK,QAAQ;AACf,aAAK,KAAK,KAAK,CAAC,CAAC;AACjB,cAAM,KAAK,KAAK,IAAI,CAAC,EAAE,IAAI,IAAI,GAAAG,GAAE,MAAM,iBAAiB,IAAI,IAAIA,EAAC,CAAC,CAAC;AAAA,MACrE;AAAA,IACF;AAGA,QAAI,KAAK,MAAM,OAAKH,QAAO,CAAC,KAAK,EAAE,GAAG;AACpC,YAAM;AAAA,QACJ,MAAM,KAAK,IAAI,EACZ,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAChB,IAAI,CAAAC,OAAK,iBAAiBA,IAAGA,IAAG,CAAC,CAAC;AAAA,MACvC;AAAA,IACF;AAEA,WAAO,SAAS,KAAK;AAAA,EACvB;AACF;;;AC3MO,SAAS,aAAa;AAC3B,MAAI;AACJ,QAAM,UAAU,mBAAW,EACxB,YAAY,CAAC,MAAM,GAAG,MAAM;AAC3B,QAAI,YAAY,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACtD,WAAO,EAAE;AAAA,EACX,CAAC,EACA,iBAAiB,CAAC,GAAG,GAAG,MAAM;AAC7B,UAAM,EAAE,QAAQ,WAAW,IAAI,EAAE;AACjC,UAAM,IAAI,WAAW,OAAO,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,GAC5D,IAAI,WAAW,OAAO,CAAC,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC;AAC1D,WAAO,oBAAe,GAAG,CAAC,EAAE,CAAC;AAAA,EAC/B,CAAC,EACA,SAAS,CAAC,UAAU;AAInB,QAAI,MAAM,UAAU,CAAC,MAAM,CAAC,EAAE,UAAU;AACtC,YAAM,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC;AACtC,YAAM,CAAC,EAAE,WAAW;AAAA,IACtB;AACA,WAAO,CAAC,KAAK;AAAA,EACf,CAAC;AAEH,SAAO;AACT;", "names": ["cartesian", "geo_triangles", "data", "i", "min", "max", "pi", "triangulation", "values", "i", "j", "a"]}