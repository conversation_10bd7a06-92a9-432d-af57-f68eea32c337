import {
  Icon_default
} from "./chunk-6AKCZ4LB.js";
import "./chunk-C5KNTEDU.js";
import {
  check_target,
  hmr,
  legacy_api,
  rest_props,
  snippet,
  spread_props,
  wrap_snippet
} from "./chunk-CRCQ7E27.js";
import "./chunk-U7P2NEEE.js";
import {
  append,
  comment
} from "./chunk-OSNF6FE7.js";
import {
  FILENAME,
  HMR,
  first_child,
  noop,
  pop,
  push,
  set
} from "./chunk-PJ2X7CWE.js";
import "./chunk-VIZMNZTH.js";
import "./chunk-HNWPC2PS.js";
import "./chunk-RVAV4ZRS.js";
import "./chunk-KWPVD4H7.js";

// node_modules/@lucide/svelte/dist/icons/grip-vertical.svelte
Grip_vertical[FILENAME] = "node_modules/@lucide/svelte/dist/icons/grip-vertical.svelte";
function Grip_vertical($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Grip_vertical);
  let props = rest_props($$props, ["$$slots", "$$events", "$$legacy"], "props");
  const iconNode = [
    ["circle", { "cx": "9", "cy": "12", "r": "1" }],
    ["circle", { "cx": "9", "cy": "5", "r": "1" }],
    ["circle", { "cx": "9", "cy": "19", "r": "1" }],
    [
      "circle",
      { "cx": "15", "cy": "12", "r": "1" }
    ],
    ["circle", { "cx": "15", "cy": "5", "r": "1" }],
    [
      "circle",
      { "cx": "15", "cy": "19", "r": "1" }
    ]
  ];
  var fragment = comment();
  var node = first_child(fragment);
  Icon_default(node, spread_props({ name: "grip-vertical" }, () => props, {
    iconNode,
    children: wrap_snippet(Grip_vertical, ($$anchor2, $$slotProps) => {
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      snippet(node_1, () => $$props.children ?? noop);
      append($$anchor2, fragment_1);
    }),
    $$slots: { default: true }
  }));
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Grip_vertical = hmr(Grip_vertical, () => Grip_vertical[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Grip_vertical[HMR].source;
    set(Grip_vertical[HMR].source, module.default[HMR].original);
  });
}
var grip_vertical_default = Grip_vertical;
export {
  grip_vertical_default as default
};
/*! Bundled license information:

@lucide/svelte/dist/icons/grip-vertical.svelte:
  (**
   * @license @lucide/svelte v0.482.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   *)

@lucide/svelte/dist/icons/grip-vertical.js:
  (**
   * @license @lucide/svelte v0.482.0 - ISC
   *
   * ISC License
   * 
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   * 
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   * 
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   * 
   *)
*/
//# sourceMappingURL=@lucide_svelte_icons_grip-vertical.js.map
