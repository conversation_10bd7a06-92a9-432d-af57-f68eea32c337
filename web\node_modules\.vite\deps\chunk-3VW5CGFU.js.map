{"version": 3, "sources": ["../../d3-array/src/ascending.js", "../../d3-array/src/descending.js", "../../d3-array/src/bisector.js", "../../d3-array/src/number.js", "../../d3-array/src/bisect.js", "../../d3-array/src/blur.js", "../../d3-array/src/count.js", "../../d3-array/src/cross.js", "../../d3-array/src/cumsum.js", "../../d3-array/src/variance.js", "../../d3-array/src/deviation.js", "../../d3-array/src/extent.js", "../../d3-array/src/fsum.js", "../../internmap/src/index.js", "../../d3-array/src/identity.js", "../../d3-array/src/group.js", "../../d3-array/src/permute.js", "../../d3-array/src/sort.js", "../../d3-array/src/groupSort.js", "../../d3-array/src/array.js", "../../d3-array/src/constant.js", "../../d3-array/src/ticks.js", "../../d3-array/src/nice.js", "../../d3-array/src/threshold/sturges.js", "../../d3-array/src/bin.js", "../../d3-array/src/max.js", "../../d3-array/src/maxIndex.js", "../../d3-array/src/min.js", "../../d3-array/src/minIndex.js", "../../d3-array/src/quickselect.js", "../../d3-array/src/greatest.js", "../../d3-array/src/quantile.js", "../../d3-array/src/threshold/freedmanDiaconis.js", "../../d3-array/src/threshold/scott.js", "../../d3-array/src/mean.js", "../../d3-array/src/median.js", "../../d3-array/src/merge.js", "../../d3-array/src/mode.js", "../../d3-array/src/pairs.js", "../../d3-array/src/range.js", "../../d3-array/src/rank.js", "../../d3-array/src/least.js", "../../d3-array/src/leastIndex.js", "../../d3-array/src/greatestIndex.js", "../../d3-array/src/scan.js", "../../d3-array/src/shuffle.js", "../../d3-array/src/sum.js", "../../d3-array/src/transpose.js", "../../d3-array/src/zip.js", "../../d3-array/src/every.js", "../../d3-array/src/some.js", "../../d3-array/src/filter.js", "../../d3-array/src/map.js", "../../d3-array/src/reduce.js", "../../d3-array/src/reverse.js", "../../d3-array/src/difference.js", "../../d3-array/src/disjoint.js", "../../d3-array/src/intersection.js", "../../d3-array/src/superset.js", "../../d3-array/src/subset.js", "../../d3-array/src/union.js"], "sourcesContent": ["export default function ascending(a, b) {\n  return a == null || b == null ? NaN : a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "export default function descending(a, b) {\n  return a == null || b == null ? NaN\n    : b < a ? -1\n    : b > a ? 1\n    : b >= a ? 0\n    : NaN;\n}\n", "import ascending from \"./ascending.js\";\nimport descending from \"./descending.js\";\n\nexport default function bisector(f) {\n  let compare1, compare2, delta;\n\n  // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n  if (f.length !== 2) {\n    compare1 = ascending;\n    compare2 = (d, x) => ascending(f(d), x);\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === ascending || f === descending ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n\n  function left(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function right(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function center(a, x, lo = 0, hi = a.length) {\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n\nfunction zero() {\n  return 0;\n}\n", "export default function number(x) {\n  return x === null ? NaN : +x;\n}\n\nexport function* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}\n", "import ascending from \"./ascending.js\";\nimport bisector from \"./bisector.js\";\nimport number from \"./number.js\";\n\nconst ascendingBisect = bisector(ascending);\nexport const bisectRight = ascendingBisect.right;\nexport const bisectLeft = ascendingBisect.left;\nexport const bisectCenter = bisector(number).center;\nexport default bisectRight;\n", "export function blur(values, r) {\n  if (!((r = +r) >= 0)) throw new RangeError(\"invalid r\");\n  let length = values.length;\n  if (!((length = Math.floor(length)) >= 0)) throw new RangeError(\"invalid length\");\n  if (!length || !r) return values;\n  const blur = blurf(r);\n  const temp = values.slice();\n  blur(values, temp, 0, length, 1);\n  blur(temp, values, 0, length, 1);\n  blur(values, temp, 0, length, 1);\n  return values;\n}\n\nexport const blur2 = Blur2(blurf);\n\nexport const blurImage = Blur2(blurfImage);\n\nfunction Blur2(blur) {\n  return function(data, rx, ry = rx) {\n    if (!((rx = +rx) >= 0)) throw new RangeError(\"invalid rx\");\n    if (!((ry = +ry) >= 0)) throw new RangeError(\"invalid ry\");\n    let {data: values, width, height} = data;\n    if (!((width = Math.floor(width)) >= 0)) throw new RangeError(\"invalid width\");\n    if (!((height = Math.floor(height !== undefined ? height : values.length / width)) >= 0)) throw new RangeError(\"invalid height\");\n    if (!width || !height || (!rx && !ry)) return data;\n    const blurx = rx && blur(rx);\n    const blury = ry && blur(ry);\n    const temp = values.slice();\n    if (blurx && blury) {\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    } else if (blurx) {\n      blurh(blurx, values, temp, width, height);\n      blurh(blurx, temp, values, width, height);\n      blurh(blurx, values, temp, width, height);\n    } else if (blury) {\n      blurv(blury, values, temp, width, height);\n      blurv(blury, temp, values, width, height);\n      blurv(blury, values, temp, width, height);\n    }\n    return data;\n  };\n}\n\nfunction blurh(blur, T, S, w, h) {\n  for (let y = 0, n = w * h; y < n;) {\n    blur(T, S, y, y += w, 1);\n  }\n}\n\nfunction blurv(blur, T, S, w, h) {\n  for (let x = 0, n = w * h; x < w; ++x) {\n    blur(T, S, x, x + n, w);\n  }\n}\n\nfunction blurfImage(radius) {\n  const blur = blurf(radius);\n  return (T, S, start, stop, step) => {\n    start <<= 2, stop <<= 2, step <<= 2;\n    blur(T, S, start + 0, stop + 0, step);\n    blur(T, S, start + 1, stop + 1, step);\n    blur(T, S, start + 2, stop + 2, step);\n    blur(T, S, start + 3, stop + 3, step);\n  };\n}\n\n// Given a target array T, a source array S, sets each value T[i] to the average\n// of {S[i - r], …, S[i], …, S[i + r]}, where r = ⌊radius⌋, start <= i < stop,\n// for each i, i + step, i + 2 * step, etc., and where S[j] is clamped between\n// S[start] (inclusive) and S[stop] (exclusive). If the given radius is not an\n// integer, S[i - r - 1] and S[i + r + 1] are added to the sum, each weighted\n// according to r - ⌊radius⌋.\nfunction blurf(radius) {\n  const radius0 = Math.floor(radius);\n  if (radius0 === radius) return bluri(radius);\n  const t = radius - radius0;\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => { // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius0 * S[start];\n    const s0 = step * radius0;\n    const s1 = s0 + step;\n    for (let i = start, j = start + s0; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s0)];\n      T[i] = (sum + t * (S[Math.max(start, i - s1)] + S[Math.min(stop, i + s1)])) / w;\n      sum -= S[Math.max(start, i - s0)];\n    }\n  };\n}\n\n// Like blurf, but optimized for integer radius.\nfunction bluri(radius) {\n  const w = 2 * radius + 1;\n  return (T, S, start, stop, step) => { // stop must be aligned!\n    if (!((stop -= step) >= start)) return; // inclusive stop\n    let sum = radius * S[start];\n    const s = step * radius;\n    for (let i = start, j = start + s; i < j; i += step) {\n      sum += S[Math.min(stop, i)];\n    }\n    for (let i = start, j = stop; i <= j; i += step) {\n      sum += S[Math.min(stop, i + s)];\n      T[i] = sum / w;\n      sum -= S[Math.max(start, i - s)];\n    }\n  };\n}\n", "export default function count(values, valueof) {\n  let count = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  }\n  return count;\n}\n", "function length(array) {\n  return array.length | 0;\n}\n\nfunction empty(length) {\n  return !(length > 0);\n}\n\nfunction arrayify(values) {\n  return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\n\nfunction reducer(reduce) {\n  return values => reduce(...values);\n}\n\nexport default function cross(...values) {\n  const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n  values = values.map(arrayify);\n  const lengths = values.map(length);\n  const j = values.length - 1;\n  const index = new Array(j + 1).fill(0);\n  const product = [];\n  if (j < 0 || lengths.some(empty)) return product;\n  while (true) {\n    product.push(index.map((j, i) => values[i][j]));\n    let i = j;\n    while (++index[i] === lengths[i]) {\n      if (i === 0) return reduce ? product.map(reduce) : product;\n      index[i--] = 0;\n    }\n  }\n}\n", "export default function cumsum(values, valueof) {\n  var sum = 0, index = 0;\n  return Float64Array.from(values, valueof === undefined\n    ? v => (sum += +v || 0)\n    : v => (sum += +valueof(v, index++, values) || 0));\n}", "export default function variance(values, valueof) {\n  let count = 0;\n  let delta;\n  let mean = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  }\n  if (count > 1) return sum / (count - 1);\n}\n", "import variance from \"./variance.js\";\n\nexport default function deviation(values, valueof) {\n  const v = variance(values, valueof);\n  return v ? Math.sqrt(v) : v;\n}\n", "export default function extent(values, valueof) {\n  let min;\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  }\n  return [min, max];\n}\n", "// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nexport class Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n, x, y, lo, hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && ((lo < 0 && p[n - 1] < 0) || (lo > 0 && p[n - 1] > 0))) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\n\nexport function fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\n\nexport function fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined\n      ? v => adder.add(+v || 0)\n      : v => adder.add(+valueof(v, ++index, values) || 0)\n  );\n}\n", "export class InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\n\nexport class InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\n\nfunction intern_get({_intern, _key}, value) {\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\n\nfunction intern_set({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\n\nfunction intern_delete({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(key);\n    _intern.delete(key);\n  }\n  return value;\n}\n\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n", "export default function identity(x) {\n  return x;\n}\n", "import {InternMap} from \"internmap\";\nimport identity from \"./identity.js\";\n\nexport default function group(values, ...keys) {\n  return nest(values, identity, identity, keys);\n}\n\nexport function groups(values, ...keys) {\n  return nest(values, Array.from, identity, keys);\n}\n\nfunction flatten(groups, keys) {\n  for (let i = 1, n = keys.length; i < n; ++i) {\n    groups = groups.flatMap(g => g.pop().map(([key, value]) => [...g, key, value]));\n  }\n  return groups;\n}\n\nexport function flatGroup(values, ...keys) {\n  return flatten(groups(values, ...keys), keys);\n}\n\nexport function flatRollup(values, reduce, ...keys) {\n  return flatten(rollups(values, reduce, ...keys), keys);\n}\n\nexport function rollup(values, reduce, ...keys) {\n  return nest(values, identity, reduce, keys);\n}\n\nexport function rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\n\nexport function index(values, ...keys) {\n  return nest(values, identity, unique, keys);\n}\n\nexport function indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\n\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\n\nfunction nest(values, map, reduce, keys) {\n  return (function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);\n      else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  })(values, 0);\n}\n", "export default function permute(source, keys) {\n  return Array.from(keys, key => source[key]);\n}\n", "import ascending from \"./ascending.js\";\nimport permute from \"./permute.js\";\n\nexport default function sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f] = F;\n  if ((f && f.length !== 2) || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascendingDefined(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascendingDefined(f[i], f[j]));\n    }\n    return permute(values, index);\n  }\n  return values.sort(compareDefined(f));\n}\n\nexport function compareDefined(compare = ascending) {\n  if (compare === ascending) return ascendingDefined;\n  if (typeof compare !== \"function\") throw new TypeError(\"compare is not a function\");\n  return (a, b) => {\n    const x = compare(a, b);\n    if (x || x === 0) return x;\n    return (compare(b, b) === 0) - (compare(a, a) === 0);\n  };\n}\n\nexport function ascendingDefined(a, b) {\n  return (a == null || !(a >= a)) - (b == null || !(b >= b)) || (a < b ? -1 : a > b ? 1 : 0);\n}\n", "import ascending from \"./ascending.js\";\nimport group, {rollup} from \"./group.js\";\nimport sort from \"./sort.js\";\n\nexport default function groupSort(values, reduce, key) {\n  return (reduce.length !== 2\n    ? sort(rollup(values, reduce, key), (([ak, av], [bk, bv]) => ascending(av, bv) || ascending(ak, bk)))\n    : sort(group(values, key), (([ak, av], [bk, bv]) => reduce(av, bv) || ascending(ak, bk))))\n    .map(([key]) => key);\n}\n", "var array = Array.prototype;\n\nexport var slice = array.slice;\nexport var map = array.map;\n", "export default function constant(x) {\n  return () => x;\n}\n", "const e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nfunction tickSpec(start, stop, count) {\n  const step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log10(step)),\n      error = step / Math.pow(10, power),\n      factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;\n  let i1, i2, inc;\n  if (power < 0) {\n    inc = Math.pow(10, -power) / factor;\n    i1 = Math.round(start * inc);\n    i2 = Math.round(stop * inc);\n    if (i1 / inc < start) ++i1;\n    if (i2 / inc > stop) --i2;\n    inc = -inc;\n  } else {\n    inc = Math.pow(10, power) * factor;\n    i1 = Math.round(start / inc);\n    i2 = Math.round(stop / inc);\n    if (i1 * inc < start) ++i1;\n    if (i2 * inc > stop) --i2;\n  }\n  if (i2 < i1 && 0.5 <= count && count < 2) return tickSpec(start, stop, count * 2);\n  return [i1, i2, inc];\n}\n\nexport default function ticks(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  if (!(count > 0)) return [];\n  if (start === stop) return [start];\n  const reverse = stop < start, [i1, i2, inc] = reverse ? tickSpec(stop, start, count) : tickSpec(start, stop, count);\n  if (!(i2 >= i1)) return [];\n  const n = i2 - i1 + 1, ticks = new Array(n);\n  if (reverse) {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i2 - i) * inc;\n  } else {\n    if (inc < 0) for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) / -inc;\n    else for (let i = 0; i < n; ++i) ticks[i] = (i1 + i) * inc;\n  }\n  return ticks;\n}\n\nexport function tickIncrement(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  return tickSpec(start, stop, count)[2];\n}\n\nexport function tickStep(start, stop, count) {\n  stop = +stop, start = +start, count = +count;\n  const reverse = stop < start, inc = reverse ? tickIncrement(stop, start, count) : tickIncrement(start, stop, count);\n  return (reverse ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);\n}\n", "import {tickIncrement} from \"./ticks.js\";\n\nexport default function nice(start, stop, count) {\n  let prestep;\n  while (true) {\n    const step = tickIncrement(start, stop, count);\n    if (step === prestep || step === 0 || !isFinite(step)) {\n      return [start, stop];\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    }\n    prestep = step;\n  }\n}\n", "import count from \"../count.js\";\n\nexport default function thresholdSturges(values) {\n  return Math.max(1, Math.ceil(Math.log(count(values)) / Math.LN2) + 1);\n}\n", "import {slice} from \"./array.js\";\nimport bisect from \"./bisect.js\";\nimport constant from \"./constant.js\";\nimport extent from \"./extent.js\";\nimport identity from \"./identity.js\";\nimport nice from \"./nice.js\";\nimport ticks, {tickIncrement} from \"./ticks.js\";\nimport sturges from \"./threshold/sturges.js\";\n\nexport default function bin() {\n  var value = identity,\n      domain = extent,\n      threshold = sturges;\n\n  function histogram(data) {\n    if (!Array.isArray(data)) data = Array.from(data);\n\n    var i,\n        n = data.length,\n        x,\n        step,\n        values = new Array(n);\n\n    for (i = 0; i < n; ++i) {\n      values[i] = value(data[i], i, data);\n    }\n\n    var xz = domain(values),\n        x0 = xz[0],\n        x1 = xz[1],\n        tz = threshold(values, x0, x1);\n\n    // Convert number of thresholds into uniform thresholds, and nice the\n    // default domain accordingly.\n    if (!Array.isArray(tz)) {\n      const max = x1, tn = +tz;\n      if (domain === extent) [x0, x1] = nice(x0, x1, tn);\n      tz = ticks(x0, x1, tn);\n\n      // If the domain is aligned with the first tick (which it will by\n      // default), then we can use quantization rather than bisection to bin\n      // values, which is substantially faster.\n      if (tz[0] <= x0) step = tickIncrement(x0, x1, tn);\n\n      // If the last threshold is coincident with the domain’s upper bound, the\n      // last bin will be zero-width. If the default domain is used, and this\n      // last threshold is coincident with the maximum input value, we can\n      // extend the niced upper bound by one tick to ensure uniform bin widths;\n      // otherwise, we simply remove the last threshold. Note that we don’t\n      // coerce values or the domain to numbers, and thus must be careful to\n      // compare order (>=) rather than strict equality (===)!\n      if (tz[tz.length - 1] >= x1) {\n        if (max >= x1 && domain === extent) {\n          const step = tickIncrement(x0, x1, tn);\n          if (isFinite(step)) {\n            if (step > 0) {\n              x1 = (Math.floor(x1 / step) + 1) * step;\n            } else if (step < 0) {\n              x1 = (Math.ceil(x1 * -step) + 1) / -step;\n            }\n          }\n        } else {\n          tz.pop();\n        }\n      }\n    }\n\n    // Remove any thresholds outside the domain.\n    // Be careful not to mutate an array owned by the user!\n    var m = tz.length, a = 0, b = m;\n    while (tz[a] <= x0) ++a;\n    while (tz[b - 1] > x1) --b;\n    if (a || b < m) tz = tz.slice(a, b), m = b - a;\n\n    var bins = new Array(m + 1),\n        bin;\n\n    // Initialize bins.\n    for (i = 0; i <= m; ++i) {\n      bin = bins[i] = [];\n      bin.x0 = i > 0 ? tz[i - 1] : x0;\n      bin.x1 = i < m ? tz[i] : x1;\n    }\n\n    // Assign data to bins by value, ignoring any outside the domain.\n    if (isFinite(step)) {\n      if (step > 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            bins[Math.min(m, Math.floor((x - x0) / step))].push(data[i]);\n          }\n        }\n      } else if (step < 0) {\n        for (i = 0; i < n; ++i) {\n          if ((x = values[i]) != null && x0 <= x && x <= x1) {\n            const j = Math.floor((x0 - x) * step);\n            bins[Math.min(m, j + (tz[j] <= x))].push(data[i]); // handle off-by-one due to rounding\n          }\n        }\n      }\n    } else {\n      for (i = 0; i < n; ++i) {\n        if ((x = values[i]) != null && x0 <= x && x <= x1) {\n          bins[bisect(tz, x, 0, m)].push(data[i]);\n        }\n      }\n    }\n\n    return bins;\n  }\n\n  histogram.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(_), histogram) : value;\n  };\n\n  histogram.domain = function(_) {\n    return arguments.length ? (domain = typeof _ === \"function\" ? _ : constant([_[0], _[1]]), histogram) : domain;\n  };\n\n  histogram.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : constant(Array.isArray(_) ? slice.call(_) : _), histogram) : threshold;\n  };\n\n  return histogram;\n}\n", "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n  return maxIndex;\n}\n", "export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "export default function minIndex(values, valueof) {\n  let min;\n  let minIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  }\n  return minIndex;\n}\n", "import {ascendingDefined, compareDefined} from \"./sort.js\";\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nexport default function quickselect(array, k, left = 0, right = Infinity, compare) {\n  k = Math.floor(k);\n  left = Math.floor(Math.max(0, left));\n  right = Math.floor(Math.min(array.length - 1, right));\n\n  if (!(left <= k && k <= right)) return array;\n\n  compare = compare === undefined ? ascendingDefined : compareDefined(compare);\n\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n\n    const t = array[k];\n    let i = left;\n    let j = right;\n\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n\n    if (compare(array[left], t) === 0) swap(array, left, j);\n    else ++j, swap(array, j, right);\n\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n\n  return array;\n}\n\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function greatest(values, compare = ascending) {\n  let max;\n  let defined = false;\n  if (compare.length === 1) {\n    let maxValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, maxValue) > 0\n          : ascending(value, value) === 0) {\n        max = element;\n        maxValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, max) > 0\n          : compare(value, value) === 0) {\n        max = value;\n        defined = true;\n      }\n    }\n  }\n  return max;\n}\n", "import max from \"./max.js\";\nimport maxIndex from \"./maxIndex.js\";\nimport min from \"./min.js\";\nimport minIndex from \"./minIndex.js\";\nimport quickselect from \"./quickselect.js\";\nimport number, {numbers} from \"./number.js\";\nimport {ascendingDefined} from \"./sort.js\";\nimport greatest from \"./greatest.js\";\n\nexport default function quantile(values, p, valueof) {\n  values = Float64Array.from(numbers(values, valueof));\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return min(values);\n  if (p >= 1) return max(values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = max(quickselect(values, i0).subarray(0, i0 + 1)),\n      value1 = min(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nexport function quantileSorted(values, p, valueof = number) {\n  if (!(n = values.length) || isNaN(p = +p)) return;\n  if (p <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = +valueof(values[i0], i0, values),\n      value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nexport function quantileIndex(values, p, valueof = number) {\n  if (isNaN(p = +p)) return;\n  numbers = Float64Array.from(values, (_, i) => number(valueof(values[i], i, values)));\n  if (p <= 0) return minIndex(numbers);\n  if (p >= 1) return maxIndex(numbers);\n  var numbers,\n      index = Uint32Array.from(values, (_, i) => i),\n      j = numbers.length - 1,\n      i = Math.floor(j * p);\n  quickselect(index, i, 0, j, (i, j) => ascendingDefined(numbers[i], numbers[j]));\n  i = greatest(index.subarray(0, i + 1), (i) => numbers[i]);\n  return i >= 0 ? i : -1;\n}\n", "import count from \"../count.js\";\nimport quantile from \"../quantile.js\";\n\nexport default function thresholdFreedmanDiaconis(values, min, max) {\n  const c = count(values), d = quantile(values, 0.75) - quantile(values, 0.25);\n  return c && d ? Math.ceil((max - min) / (2 * d * Math.pow(c, -1 / 3))) : 1;\n}\n", "import count from \"../count.js\";\nimport deviation from \"../deviation.js\";\n\nexport default function thresholdScott(values, min, max) {\n  const c = count(values), d = deviation(values);\n  return c && d ? Math.ceil((max - min) * Math.cbrt(c) / (3.49 * d)) : 1;\n}\n", "export default function mean(values, valueof) {\n  let count = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  }\n  if (count) return sum / count;\n}\n", "import quantile, {quantileIndex} from \"./quantile.js\";\n\nexport default function median(values, valueof) {\n  return quantile(values, 0.5, valueof);\n}\n\nexport function medianIndex(values, valueof) {\n  return quantileIndex(values, 0.5, valueof);\n}\n", "function* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\n\nexport default function merge(arrays) {\n  return Array.from(flatten(arrays));\n}\n", "import {InternMap} from \"internmap\";\n\nexport default function mode(values, valueof) {\n  const counts = new InternMap();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && value >= value) {\n        counts.set(value, (counts.get(value) || 0) + 1);\n      }\n    }\n  }\n  let modeValue;\n  let modeCount = 0;\n  for (const [value, count] of counts) {\n    if (count > modeCount) {\n      modeCount = count;\n      modeValue = value;\n    }\n  }\n  return modeValue;\n}\n", "export default function pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\n\nexport function pair(a, b) {\n  return [a, b];\n}\n", "export default function range(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n\n  var i = -1,\n      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n      range = new Array(n);\n\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n\n  return range;\n}\n", "import ascending from \"./ascending.js\";\nimport {ascendingDefined, compareDefined} from \"./sort.js\";\n\nexport default function rank(values, valueof = ascending) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = ascending;\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n  let k, r;\n  values = Uint32Array.from(V, (_, i) => i);\n  // Risky chaining due to Safari 14 https://github.com/d3/d3-array/issues/123\n  values.sort(valueof === ascending ? (i, j) => ascendingDefined(V[i], V[j]) : compareDefined(compareIndex));\n  values.forEach((j, i) => {\n      const c = compareIndex(j, k === undefined ? j : k);\n      if (c >= 0) {\n        if (k === undefined || c > 0) k = j, r = i;\n        R[j] = r;\n      } else {\n        R[j] = NaN;\n      }\n    });\n  return R;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function least(values, compare = ascending) {\n  let min;\n  let defined = false;\n  if (compare.length === 1) {\n    let minValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, minValue) < 0\n          : ascending(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, min) < 0\n          : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n  return min;\n}\n", "import ascending from \"./ascending.js\";\nimport minIndex from \"./minIndex.js\";\n\nexport default function leastIndex(values, compare = ascending) {\n  if (compare.length === 1) return minIndex(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (min < 0\n        ? compare(value, value) === 0\n        : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n  return min;\n}\n", "import ascending from \"./ascending.js\";\nimport maxIndex from \"./maxIndex.js\";\n\nexport default function greatestIndex(values, compare = ascending) {\n  if (compare.length === 1) return maxIndex(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (max < 0\n        ? compare(value, value) === 0\n        : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n  return max;\n}\n", "import leastIndex from \"./leastIndex.js\";\n\nexport default function scan(values, compare) {\n  const index = leastIndex(values, compare);\n  return index < 0 ? undefined : index;\n}\n", "export default shuffler(Math.random);\n\nexport function shuffler(random) {\n  return function shuffle(array, i0 = 0, i1 = array.length) {\n    let m = i1 - (i0 = +i0);\n    while (m) {\n      const i = random() * m-- | 0, t = array[m + i0];\n      array[m + i0] = array[i + i0];\n      array[i + i0] = t;\n    }\n    return array;\n  };\n}\n", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n", "import min from \"./min.js\";\n\nexport default function transpose(matrix) {\n  if (!(n = matrix.length)) return [];\n  for (var i = -1, m = min(matrix, length), transpose = new Array(m); ++i < m;) {\n    for (var j = -1, n, row = transpose[i] = new Array(n); ++j < n;) {\n      row[j] = matrix[j][i];\n    }\n  }\n  return transpose;\n}\n\nfunction length(d) {\n  return d.length;\n}\n", "import transpose from \"./transpose.js\";\n\nexport default function zip() {\n  return transpose(arguments);\n}\n", "export default function every(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (!test(value, ++index, values)) {\n      return false;\n    }\n  }\n  return true;\n}\n", "export default function some(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      return true;\n    }\n  }\n  return false;\n}\n", "export default function filter(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  const array = [];\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      array.push(value);\n    }\n  }\n  return array;\n}\n", "export default function map(values, mapper) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n  return Array.from(values, (value, index) => mapper(value, index, values));\n}\n", "export default function reduce(values, reducer, value) {\n  if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n  const iterator = values[Symbol.iterator]();\n  let done, next, index = -1;\n  if (arguments.length < 3) {\n    ({done, value} = iterator.next());\n    if (done) return;\n    ++index;\n  }\n  while (({done, value: next} = iterator.next()), !done) {\n    value = reducer(value, next, ++index, values);\n  }\n  return value;\n}\n", "export default function reverse(values) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  return Array.from(values).reverse();\n}\n", "import {InternSet} from \"internmap\";\n\nexport default function difference(values, ...others) {\n  values = new InternSet(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}\n", "import {InternSet} from \"internmap\";\n\nexport default function disjoint(values, other) {\n  const iterator = other[Symbol.iterator](), set = new InternSet();\n  for (const v of values) {\n    if (set.has(v)) return false;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) break;\n      if (Object.is(v, value)) return false;\n      set.add(value);\n    }\n  }\n  return true;\n}\n", "import {InternSet} from \"internmap\";\n\nexport default function intersection(values, ...others) {\n  values = new InternSet(values);\n  others = others.map(set);\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n  return values;\n}\n\nfunction set(values) {\n  return values instanceof InternSet ? values : new InternSet(values);\n}\n", "export default function superset(values, other) {\n  const iterator = values[Symbol.iterator](), set = new Set();\n  for (const o of other) {\n    const io = intern(o);\n    if (set.has(io)) continue;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) return false;\n      const ivalue = intern(value);\n      set.add(ivalue);\n      if (Object.is(io, ivalue)) break;\n    }\n  }\n  return true;\n}\n\nfunction intern(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n", "import superset from \"./superset.js\";\n\nexport default function subset(values, other) {\n  return superset(other, values);\n}\n", "import {InternSet} from \"internmap\";\n\nexport default function union(...others) {\n  const set = new InternSet();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}\n"], "mappings": ";AAAe,SAAR,UAA2B,GAAG,GAAG;AACtC,SAAO,KAAK,QAAQ,KAAK,OAAO,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAC9E;;;ACFe,SAAR,WAA4B,GAAG,GAAG;AACvC,SAAO,KAAK,QAAQ,KAAK,OAAO,MAC5B,IAAI,IAAI,KACR,IAAI,IAAI,IACR,KAAK,IAAI,IACT;AACN;;;ACHe,SAAR,SAA0B,GAAG;AAClC,MAAI,UAAU,UAAU;AAOxB,MAAI,EAAE,WAAW,GAAG;AAClB,eAAW;AACX,eAAW,CAAC,GAAG,MAAM,UAAU,EAAE,CAAC,GAAG,CAAC;AACtC,YAAQ,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI;AAAA,EAC3B,OAAO;AACL,eAAW,MAAM,aAAa,MAAM,aAAa,IAAI;AACrD,eAAW;AACX,YAAQ;AAAA,EACV;AAEA,WAAS,KAAK,GAAG,GAAG,KAAK,GAAG,KAAK,EAAE,QAAQ;AACzC,QAAI,KAAK,IAAI;AACX,UAAI,SAAS,GAAG,CAAC,MAAM,EAAG,QAAO;AACjC,SAAG;AACD,cAAM,MAAO,KAAK,OAAQ;AAC1B,YAAI,SAAS,EAAE,GAAG,GAAG,CAAC,IAAI,EAAG,MAAK,MAAM;AAAA,YACnC,MAAK;AAAA,MACZ,SAAS,KAAK;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAEA,WAAS,MAAM,GAAG,GAAG,KAAK,GAAG,KAAK,EAAE,QAAQ;AAC1C,QAAI,KAAK,IAAI;AACX,UAAI,SAAS,GAAG,CAAC,MAAM,EAAG,QAAO;AACjC,SAAG;AACD,cAAM,MAAO,KAAK,OAAQ;AAC1B,YAAI,SAAS,EAAE,GAAG,GAAG,CAAC,KAAK,EAAG,MAAK,MAAM;AAAA,YACpC,MAAK;AAAA,MACZ,SAAS,KAAK;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAEA,WAAS,OAAO,GAAG,GAAG,KAAK,GAAG,KAAK,EAAE,QAAQ;AAC3C,UAAM,IAAI,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC;AAC/B,WAAO,IAAI,MAAM,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI;AAAA,EAClE;AAEA,SAAO,EAAC,MAAM,QAAQ,MAAK;AAC7B;AAEA,SAAS,OAAO;AACd,SAAO;AACT;;;ACvDe,SAAR,OAAwB,GAAG;AAChC,SAAO,MAAM,OAAO,MAAM,CAAC;AAC7B;AAEO,UAAU,QAAQ,QAAQ,SAAS;AACxC,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,SAAS,SAAS,QAAQ,CAAC,UAAU,OAAO;AAC9C,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIA,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SAAS,QAAQ,CAAC,UAAU,OAAO;AAClF,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;;;ACfA,IAAM,kBAAkB,SAAS,SAAS;AACnC,IAAM,cAAc,gBAAgB;AACpC,IAAM,aAAa,gBAAgB;AACnC,IAAM,eAAe,SAAS,MAAM,EAAE;AAC7C,IAAO,iBAAQ;;;ACRR,SAAS,KAAK,QAAQ,GAAG;AAC9B,MAAI,GAAG,IAAI,CAAC,MAAM,GAAI,OAAM,IAAI,WAAW,WAAW;AACtD,MAAIC,UAAS,OAAO;AACpB,MAAI,GAAGA,UAAS,KAAK,MAAMA,OAAM,MAAM,GAAI,OAAM,IAAI,WAAW,gBAAgB;AAChF,MAAI,CAACA,WAAU,CAAC,EAAG,QAAO;AAC1B,QAAMC,QAAO,MAAM,CAAC;AACpB,QAAM,OAAO,OAAO,MAAM;AAC1B,EAAAA,MAAK,QAAQ,MAAM,GAAGD,SAAQ,CAAC;AAC/B,EAAAC,MAAK,MAAM,QAAQ,GAAGD,SAAQ,CAAC;AAC/B,EAAAC,MAAK,QAAQ,MAAM,GAAGD,SAAQ,CAAC;AAC/B,SAAO;AACT;AAEO,IAAM,QAAQ,MAAM,KAAK;AAEzB,IAAM,YAAY,MAAM,UAAU;AAEzC,SAAS,MAAMC,OAAM;AACnB,SAAO,SAAS,MAAM,IAAI,KAAK,IAAI;AACjC,QAAI,GAAG,KAAK,CAAC,OAAO,GAAI,OAAM,IAAI,WAAW,YAAY;AACzD,QAAI,GAAG,KAAK,CAAC,OAAO,GAAI,OAAM,IAAI,WAAW,YAAY;AACzD,QAAI,EAAC,MAAM,QAAQ,OAAO,OAAM,IAAI;AACpC,QAAI,GAAG,QAAQ,KAAK,MAAM,KAAK,MAAM,GAAI,OAAM,IAAI,WAAW,eAAe;AAC7E,QAAI,GAAG,SAAS,KAAK,MAAM,WAAW,SAAY,SAAS,OAAO,SAAS,KAAK,MAAM,GAAI,OAAM,IAAI,WAAW,gBAAgB;AAC/H,QAAI,CAAC,SAAS,CAAC,UAAW,CAAC,MAAM,CAAC,GAAK,QAAO;AAC9C,UAAM,QAAQ,MAAMA,MAAK,EAAE;AAC3B,UAAM,QAAQ,MAAMA,MAAK,EAAE;AAC3B,UAAM,OAAO,OAAO,MAAM;AAC1B,QAAI,SAAS,OAAO;AAClB,YAAM,OAAO,MAAM,QAAQ,OAAO,MAAM;AACxC,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AACxC,YAAM,OAAO,MAAM,QAAQ,OAAO,MAAM;AACxC,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AACxC,YAAM,OAAO,MAAM,QAAQ,OAAO,MAAM;AACxC,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AAAA,IAC1C,WAAW,OAAO;AAChB,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AACxC,YAAM,OAAO,MAAM,QAAQ,OAAO,MAAM;AACxC,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AAAA,IAC1C,WAAW,OAAO;AAChB,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AACxC,YAAM,OAAO,MAAM,QAAQ,OAAO,MAAM;AACxC,YAAM,OAAO,QAAQ,MAAM,OAAO,MAAM;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,MAAMA,OAAM,GAAG,GAAG,GAAG,GAAG;AAC/B,WAAS,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,KAAI;AACjC,IAAAA,MAAK,GAAG,GAAG,GAAG,KAAK,GAAG,CAAC;AAAA,EACzB;AACF;AAEA,SAAS,MAAMA,OAAM,GAAG,GAAG,GAAG,GAAG;AAC/B,WAAS,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrC,IAAAA,MAAK,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC;AAAA,EACxB;AACF;AAEA,SAAS,WAAW,QAAQ;AAC1B,QAAMA,QAAO,MAAM,MAAM;AACzB,SAAO,CAAC,GAAG,GAAG,OAAO,MAAM,SAAS;AAClC,cAAU,GAAG,SAAS,GAAG,SAAS;AAClC,IAAAA,MAAK,GAAG,GAAG,QAAQ,GAAG,OAAO,GAAG,IAAI;AACpC,IAAAA,MAAK,GAAG,GAAG,QAAQ,GAAG,OAAO,GAAG,IAAI;AACpC,IAAAA,MAAK,GAAG,GAAG,QAAQ,GAAG,OAAO,GAAG,IAAI;AACpC,IAAAA,MAAK,GAAG,GAAG,QAAQ,GAAG,OAAO,GAAG,IAAI;AAAA,EACtC;AACF;AAQA,SAAS,MAAM,QAAQ;AACrB,QAAM,UAAU,KAAK,MAAM,MAAM;AACjC,MAAI,YAAY,OAAQ,QAAO,MAAM,MAAM;AAC3C,QAAM,IAAI,SAAS;AACnB,QAAM,IAAI,IAAI,SAAS;AACvB,SAAO,CAAC,GAAG,GAAG,OAAO,MAAM,SAAS;AAClC,QAAI,GAAG,QAAQ,SAAS,OAAQ;AAChC,QAAIC,OAAM,UAAU,EAAE,KAAK;AAC3B,UAAM,KAAK,OAAO;AAClB,UAAM,KAAK,KAAK;AAChB,aAAS,IAAI,OAAO,IAAI,QAAQ,IAAI,IAAI,GAAG,KAAK,MAAM;AACpD,MAAAA,QAAO,EAAE,KAAK,IAAI,MAAM,CAAC,CAAC;AAAA,IAC5B;AACA,aAAS,IAAI,OAAO,IAAI,MAAM,KAAK,GAAG,KAAK,MAAM;AAC/C,MAAAA,QAAO,EAAE,KAAK,IAAI,MAAM,IAAI,EAAE,CAAC;AAC/B,QAAE,CAAC,KAAKA,OAAM,KAAK,EAAE,KAAK,IAAI,OAAO,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,IAAI,MAAM,IAAI,EAAE,CAAC,MAAM;AAC9E,MAAAA,QAAO,EAAE,KAAK,IAAI,OAAO,IAAI,EAAE,CAAC;AAAA,IAClC;AAAA,EACF;AACF;AAGA,SAAS,MAAM,QAAQ;AACrB,QAAM,IAAI,IAAI,SAAS;AACvB,SAAO,CAAC,GAAG,GAAG,OAAO,MAAM,SAAS;AAClC,QAAI,GAAG,QAAQ,SAAS,OAAQ;AAChC,QAAIA,OAAM,SAAS,EAAE,KAAK;AAC1B,UAAM,IAAI,OAAO;AACjB,aAAS,IAAI,OAAO,IAAI,QAAQ,GAAG,IAAI,GAAG,KAAK,MAAM;AACnD,MAAAA,QAAO,EAAE,KAAK,IAAI,MAAM,CAAC,CAAC;AAAA,IAC5B;AACA,aAAS,IAAI,OAAO,IAAI,MAAM,KAAK,GAAG,KAAK,MAAM;AAC/C,MAAAA,QAAO,EAAE,KAAK,IAAI,MAAM,IAAI,CAAC,CAAC;AAC9B,QAAE,CAAC,IAAIA,OAAM;AACb,MAAAA,QAAO,EAAE,KAAK,IAAI,OAAO,IAAI,CAAC,CAAC;AAAA,IACjC;AAAA,EACF;AACF;;;AClHe,SAAR,MAAuB,QAAQ,SAAS;AAC7C,MAAIC,SAAQ;AACZ,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,SAAS,SAAS,QAAQ,CAAC,UAAU,OAAO;AAC9C,UAAEA;AAAA,MACJ;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SAAS,QAAQ,CAAC,UAAU,OAAO;AAClF,UAAED;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;;;ACjBA,SAAS,OAAOE,QAAO;AACrB,SAAOA,OAAM,SAAS;AACxB;AAEA,SAAS,MAAMC,SAAQ;AACrB,SAAO,EAAEA,UAAS;AACpB;AAEA,SAAS,SAAS,QAAQ;AACxB,SAAO,OAAO,WAAW,YAAY,YAAY,SAAS,SAAS,MAAM,KAAK,MAAM;AACtF;AAEA,SAAS,QAAQC,SAAQ;AACvB,SAAO,YAAUA,QAAO,GAAG,MAAM;AACnC;AAEe,SAAR,SAA0B,QAAQ;AACvC,QAAMA,UAAS,OAAO,OAAO,OAAO,SAAS,CAAC,MAAM,cAAc,QAAQ,OAAO,IAAI,CAAC;AACtF,WAAS,OAAO,IAAI,QAAQ;AAC5B,QAAM,UAAU,OAAO,IAAI,MAAM;AACjC,QAAM,IAAI,OAAO,SAAS;AAC1B,QAAMC,SAAQ,IAAI,MAAM,IAAI,CAAC,EAAE,KAAK,CAAC;AACrC,QAAM,UAAU,CAAC;AACjB,MAAI,IAAI,KAAK,QAAQ,KAAK,KAAK,EAAG,QAAO;AACzC,SAAO,MAAM;AACX,YAAQ,KAAKA,OAAM,IAAI,CAACC,IAAGC,OAAM,OAAOA,EAAC,EAAED,EAAC,CAAC,CAAC;AAC9C,QAAI,IAAI;AACR,WAAO,EAAED,OAAM,CAAC,MAAM,QAAQ,CAAC,GAAG;AAChC,UAAI,MAAM,EAAG,QAAOD,UAAS,QAAQ,IAAIA,OAAM,IAAI;AACnD,MAAAC,OAAM,GAAG,IAAI;AAAA,IACf;AAAA,EACF;AACF;;;AChCe,SAAR,OAAwB,QAAQ,SAAS;AAC9C,MAAIG,OAAM,GAAGC,SAAQ;AACrB,SAAO,aAAa,KAAK,QAAQ,YAAY,SACzC,OAAMD,QAAO,CAAC,KAAK,IACnB,OAAMA,QAAO,CAAC,QAAQ,GAAGC,UAAS,MAAM,KAAK,CAAE;AACrD;;;ACLe,SAAR,SAA0B,QAAQ,SAAS;AAChD,MAAIC,SAAQ;AACZ,MAAI;AACJ,MAAIC,QAAO;AACX,MAAIC,OAAM;AACV,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,SAAS,SAAS,QAAQ,CAAC,UAAU,OAAO;AAC9C,gBAAQ,QAAQD;AAChB,QAAAA,SAAQ,QAAQ,EAAED;AAClB,QAAAE,QAAO,SAAS,QAAQD;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIE,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SAAS,QAAQ,CAAC,UAAU,OAAO;AAClF,gBAAQ,QAAQF;AAChB,QAAAA,SAAQ,QAAQ,EAAED;AAClB,QAAAE,QAAO,SAAS,QAAQD;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,MAAID,SAAQ,EAAG,QAAOE,QAAOF,SAAQ;AACvC;;;ACtBe,SAAR,UAA2B,QAAQ,SAAS;AACjD,QAAM,IAAI,SAAS,QAAQ,OAAO;AAClC,SAAO,IAAI,KAAK,KAAK,CAAC,IAAI;AAC5B;;;ACLe,SAAR,OAAwB,QAAQ,SAAS;AAC9C,MAAII;AACJ,MAAIC;AACJ,MAAI,YAAY,QAAW;AACzB,eAAW,SAAS,QAAQ;AAC1B,UAAI,SAAS,MAAM;AACjB,YAAID,SAAQ,QAAW;AACrB,cAAI,SAAS,MAAO,CAAAA,OAAMC,OAAM;AAAA,QAClC,OAAO;AACL,cAAID,OAAM,MAAO,CAAAA,OAAM;AACvB,cAAIC,OAAM,MAAO,CAAAA,OAAM;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,MAAM;AACrD,YAAIF,SAAQ,QAAW;AACrB,cAAI,SAAS,MAAO,CAAAA,OAAMC,OAAM;AAAA,QAClC,OAAO;AACL,cAAID,OAAM,MAAO,CAAAA,OAAM;AACvB,cAAIC,OAAM,MAAO,CAAAA,OAAM;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAACD,MAAKC,IAAG;AAClB;;;AC3BO,IAAM,QAAN,MAAY;AAAA,EACjB,cAAc;AACZ,SAAK,YAAY,IAAI,aAAa,EAAE;AACpC,SAAK,KAAK;AAAA,EACZ;AAAA,EACA,IAAI,GAAG;AACL,UAAM,IAAI,KAAK;AACf,QAAI,IAAI;AACR,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK;AAC1C,YAAM,IAAI,EAAE,CAAC,GACX,KAAK,IAAI,GACT,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5D,UAAI,GAAI,GAAE,GAAG,IAAI;AACjB,UAAI;AAAA,IACN;AACA,MAAE,CAAC,IAAI;AACP,SAAK,KAAK,IAAI;AACd,WAAO;AAAA,EACT;AAAA,EACA,UAAU;AACR,UAAM,IAAI,KAAK;AACf,QAAI,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI,KAAK;AAChC,QAAI,IAAI,GAAG;AACT,WAAK,EAAE,EAAE,CAAC;AACV,aAAO,IAAI,GAAG;AACZ,YAAI;AACJ,YAAI,EAAE,EAAE,CAAC;AACT,aAAK,IAAI;AACT,aAAK,KAAK,KAAK;AACf,YAAI,GAAI;AAAA,MACV;AACA,UAAI,IAAI,MAAO,KAAK,KAAK,EAAE,IAAI,CAAC,IAAI,KAAO,KAAK,KAAK,EAAE,IAAI,CAAC,IAAI,IAAK;AACnE,YAAI,KAAK;AACT,YAAI,KAAK;AACT,YAAI,KAAK,IAAI,GAAI,MAAK;AAAA,MACxB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEO,SAAS,KAAK,QAAQ,SAAS;AACpC,QAAM,QAAQ,IAAI,MAAM;AACxB,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,QAAQ,CAAC,OAAO;AAClB,cAAM,IAAI,KAAK;AAAA,MACjB;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIE,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,UAAI,QAAQ,CAAC,QAAQ,OAAO,EAAEA,QAAO,MAAM,GAAG;AAC5C,cAAM,IAAI,KAAK;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC;AACV;AAEO,SAAS,QAAQ,QAAQ,SAAS;AACvC,QAAM,QAAQ,IAAI,MAAM;AACxB,MAAIA,SAAQ;AACZ,SAAO,aAAa;AAAA,IAAK;AAAA,IAAQ,YAAY,SACvC,OAAK,MAAM,IAAI,CAAC,KAAK,CAAC,IACtB,OAAK,MAAM,IAAI,CAAC,QAAQ,GAAG,EAAEA,QAAO,MAAM,KAAK,CAAC;AAAA,EACtD;AACF;;;ACpEO,IAAM,YAAN,cAAwB,IAAI;AAAA,EACjC,YAAY,SAAS,MAAM,OAAO;AAChC,UAAM;AACN,WAAO,iBAAiB,MAAM,EAAC,SAAS,EAAC,OAAO,oBAAI,IAAI,EAAC,GAAG,MAAM,EAAC,OAAO,IAAG,EAAC,CAAC;AAC/E,QAAI,WAAW,KAAM,YAAW,CAACC,MAAK,KAAK,KAAK,QAAS,MAAK,IAAIA,MAAK,KAAK;AAAA,EAC9E;AAAA,EACA,IAAI,KAAK;AACP,WAAO,MAAM,IAAI,WAAW,MAAM,GAAG,CAAC;AAAA,EACxC;AAAA,EACA,IAAI,KAAK;AACP,WAAO,MAAM,IAAI,WAAW,MAAM,GAAG,CAAC;AAAA,EACxC;AAAA,EACA,IAAI,KAAK,OAAO;AACd,WAAO,MAAM,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK;AAAA,EAC/C;AAAA,EACA,OAAO,KAAK;AACV,WAAO,MAAM,OAAO,cAAc,MAAM,GAAG,CAAC;AAAA,EAC9C;AACF;AAEO,IAAM,YAAN,cAAwB,IAAI;AAAA,EACjC,YAAY,QAAQ,MAAM,OAAO;AAC/B,UAAM;AACN,WAAO,iBAAiB,MAAM,EAAC,SAAS,EAAC,OAAO,oBAAI,IAAI,EAAC,GAAG,MAAM,EAAC,OAAO,IAAG,EAAC,CAAC;AAC/E,QAAI,UAAU,KAAM,YAAW,SAAS,OAAQ,MAAK,IAAI,KAAK;AAAA,EAChE;AAAA,EACA,IAAI,OAAO;AACT,WAAO,MAAM,IAAI,WAAW,MAAM,KAAK,CAAC;AAAA,EAC1C;AAAA,EACA,IAAI,OAAO;AACT,WAAO,MAAM,IAAI,WAAW,MAAM,KAAK,CAAC;AAAA,EAC1C;AAAA,EACA,OAAO,OAAO;AACZ,WAAO,MAAM,OAAO,cAAc,MAAM,KAAK,CAAC;AAAA,EAChD;AACF;AAEA,SAAS,WAAW,EAAC,SAAS,KAAI,GAAG,OAAO;AAC1C,QAAM,MAAM,KAAK,KAAK;AACtB,SAAO,QAAQ,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,IAAI;AAC/C;AAEA,SAAS,WAAW,EAAC,SAAS,KAAI,GAAG,OAAO;AAC1C,QAAM,MAAM,KAAK,KAAK;AACtB,MAAI,QAAQ,IAAI,GAAG,EAAG,QAAO,QAAQ,IAAI,GAAG;AAC5C,UAAQ,IAAI,KAAK,KAAK;AACtB,SAAO;AACT;AAEA,SAAS,cAAc,EAAC,SAAS,KAAI,GAAG,OAAO;AAC7C,QAAM,MAAM,KAAK,KAAK;AACtB,MAAI,QAAQ,IAAI,GAAG,GAAG;AACpB,YAAQ,QAAQ,IAAI,GAAG;AACvB,YAAQ,OAAO,GAAG;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,MAAM,OAAO;AACpB,SAAO,UAAU,QAAQ,OAAO,UAAU,WAAW,MAAM,QAAQ,IAAI;AACzE;;;AC5De,SAAR,SAA0B,GAAG;AAClC,SAAO;AACT;;;ACCe,SAAR,MAAuB,WAAW,MAAM;AAC7C,SAAO,KAAK,QAAQ,UAAU,UAAU,IAAI;AAC9C;AAEO,SAAS,OAAO,WAAW,MAAM;AACtC,SAAO,KAAK,QAAQ,MAAM,MAAM,UAAU,IAAI;AAChD;AAEA,SAAS,QAAQC,SAAQ,MAAM;AAC7B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,IAAAA,UAASA,QAAO,QAAQ,OAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,CAAC;AAAA,EAChF;AACA,SAAOA;AACT;AAEO,SAAS,UAAU,WAAW,MAAM;AACzC,SAAO,QAAQ,OAAO,QAAQ,GAAG,IAAI,GAAG,IAAI;AAC9C;AAEO,SAAS,WAAW,QAAQC,YAAW,MAAM;AAClD,SAAO,QAAQ,QAAQ,QAAQA,SAAQ,GAAG,IAAI,GAAG,IAAI;AACvD;AAEO,SAAS,OAAO,QAAQA,YAAW,MAAM;AAC9C,SAAO,KAAK,QAAQ,UAAUA,SAAQ,IAAI;AAC5C;AAEO,SAAS,QAAQ,QAAQA,YAAW,MAAM;AAC/C,SAAO,KAAK,QAAQ,MAAM,MAAMA,SAAQ,IAAI;AAC9C;AAEO,SAAS,MAAM,WAAW,MAAM;AACrC,SAAO,KAAK,QAAQ,UAAU,QAAQ,IAAI;AAC5C;AAEO,SAAS,QAAQ,WAAW,MAAM;AACvC,SAAO,KAAK,QAAQ,MAAM,MAAM,QAAQ,IAAI;AAC9C;AAEA,SAAS,OAAO,QAAQ;AACtB,MAAI,OAAO,WAAW,EAAG,OAAM,IAAI,MAAM,eAAe;AACxD,SAAO,OAAO,CAAC;AACjB;AAEA,SAAS,KAAK,QAAQC,MAAKD,SAAQ,MAAM;AACvC,SAAQ,SAAS,QAAQE,SAAQ,GAAG;AAClC,QAAI,KAAK,KAAK,OAAQ,QAAOF,QAAOE,OAAM;AAC1C,UAAMH,UAAS,IAAI,UAAU;AAC7B,UAAMI,SAAQ,KAAK,GAAG;AACtB,QAAIC,SAAQ;AACZ,eAAW,SAASF,SAAQ;AAC1B,YAAM,MAAMC,OAAM,OAAO,EAAEC,QAAOF,OAAM;AACxC,YAAMG,SAAQN,QAAO,IAAI,GAAG;AAC5B,UAAIM,OAAO,CAAAA,OAAM,KAAK,KAAK;AAAA,UACtB,CAAAN,QAAO,IAAI,KAAK,CAAC,KAAK,CAAC;AAAA,IAC9B;AACA,eAAW,CAAC,KAAKG,OAAM,KAAKH,SAAQ;AAClC,MAAAA,QAAO,IAAI,KAAK,QAAQG,SAAQ,CAAC,CAAC;AAAA,IACpC;AACA,WAAOD,KAAIF,OAAM;AAAA,EACnB,EAAG,QAAQ,CAAC;AACd;;;AChEe,SAAR,QAAyB,QAAQ,MAAM;AAC5C,SAAO,MAAM,KAAK,MAAM,SAAO,OAAO,GAAG,CAAC;AAC5C;;;ACCe,SAAR,KAAsB,WAAW,GAAG;AACzC,MAAI,OAAO,OAAO,OAAO,QAAQ,MAAM,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/F,WAAS,MAAM,KAAK,MAAM;AAC1B,MAAI,CAAC,CAAC,IAAI;AACV,MAAK,KAAK,EAAE,WAAW,KAAM,EAAE,SAAS,GAAG;AACzC,UAAMO,SAAQ,YAAY,KAAK,QAAQ,CAAC,GAAG,MAAM,CAAC;AAClD,QAAI,EAAE,SAAS,GAAG;AAChB,UAAI,EAAE,IAAI,CAAAC,OAAK,OAAO,IAAIA,EAAC,CAAC;AAC5B,MAAAD,OAAM,KAAK,CAAC,GAAG,MAAM;AACnB,mBAAWC,MAAK,GAAG;AACjB,gBAAM,IAAI,iBAAiBA,GAAE,CAAC,GAAGA,GAAE,CAAC,CAAC;AACrC,cAAI,EAAG,QAAO;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,UAAI,OAAO,IAAI,CAAC;AAChB,MAAAD,OAAM,KAAK,CAAC,GAAG,MAAM,iBAAiB,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAAA,IACnD;AACA,WAAO,QAAQ,QAAQA,MAAK;AAAA,EAC9B;AACA,SAAO,OAAO,KAAK,eAAe,CAAC,CAAC;AACtC;AAEO,SAAS,eAAe,UAAU,WAAW;AAClD,MAAI,YAAY,UAAW,QAAO;AAClC,MAAI,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,2BAA2B;AAClF,SAAO,CAAC,GAAG,MAAM;AACf,UAAM,IAAI,QAAQ,GAAG,CAAC;AACtB,QAAI,KAAK,MAAM,EAAG,QAAO;AACzB,YAAQ,QAAQ,GAAG,CAAC,MAAM,MAAM,QAAQ,GAAG,CAAC,MAAM;AAAA,EACpD;AACF;AAEO,SAAS,iBAAiB,GAAG,GAAG;AACrC,UAAQ,KAAK,QAAQ,EAAE,KAAK,OAAO,KAAK,QAAQ,EAAE,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAC1F;;;AClCe,SAAR,UAA2B,QAAQE,SAAQ,KAAK;AACrD,UAAQA,QAAO,WAAW,IACtB,KAAK,OAAO,QAAQA,SAAQ,GAAG,GAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAM,UAAU,IAAI,EAAE,KAAK,UAAU,IAAI,EAAE,CAAE,IAClG,KAAK,MAAM,QAAQ,GAAG,GAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,MAAMA,QAAO,IAAI,EAAE,KAAK,UAAU,IAAI,EAAE,CAAE,GACvF,IAAI,CAAC,CAACC,IAAG,MAAMA,IAAG;AACvB;;;ACTA,IAAI,QAAQ,MAAM;AAEX,IAAI,QAAQ,MAAM;AAClB,IAAI,MAAM,MAAM;;;ACHR,SAAR,SAA0B,GAAG;AAClC,SAAO,MAAM;AACf;;;ACFA,IAAM,MAAM,KAAK,KAAK,EAAE;AAAxB,IACI,KAAK,KAAK,KAAK,EAAE;AADrB,IAEI,KAAK,KAAK,KAAK,CAAC;AAEpB,SAAS,SAAS,OAAO,MAAMC,QAAO;AACpC,QAAM,QAAQ,OAAO,SAAS,KAAK,IAAI,GAAGA,MAAK,GAC3C,QAAQ,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC,GACnC,QAAQ,OAAO,KAAK,IAAI,IAAI,KAAK,GACjC,SAAS,SAAS,MAAM,KAAK,SAAS,KAAK,IAAI,SAAS,KAAK,IAAI;AACrE,MAAI,IAAI,IAAI;AACZ,MAAI,QAAQ,GAAG;AACb,UAAM,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI;AAC7B,SAAK,KAAK,MAAM,QAAQ,GAAG;AAC3B,SAAK,KAAK,MAAM,OAAO,GAAG;AAC1B,QAAI,KAAK,MAAM,MAAO,GAAE;AACxB,QAAI,KAAK,MAAM,KAAM,GAAE;AACvB,UAAM,CAAC;AAAA,EACT,OAAO;AACL,UAAM,KAAK,IAAI,IAAI,KAAK,IAAI;AAC5B,SAAK,KAAK,MAAM,QAAQ,GAAG;AAC3B,SAAK,KAAK,MAAM,OAAO,GAAG;AAC1B,QAAI,KAAK,MAAM,MAAO,GAAE;AACxB,QAAI,KAAK,MAAM,KAAM,GAAE;AAAA,EACzB;AACA,MAAI,KAAK,MAAM,OAAOA,UAASA,SAAQ,EAAG,QAAO,SAAS,OAAO,MAAMA,SAAQ,CAAC;AAChF,SAAO,CAAC,IAAI,IAAI,GAAG;AACrB;AAEe,SAAR,MAAuB,OAAO,MAAMA,QAAO;AAChD,SAAO,CAAC,MAAM,QAAQ,CAAC,OAAOA,SAAQ,CAACA;AACvC,MAAI,EAAEA,SAAQ,GAAI,QAAO,CAAC;AAC1B,MAAI,UAAU,KAAM,QAAO,CAAC,KAAK;AACjC,QAAMC,WAAU,OAAO,OAAO,CAAC,IAAI,IAAI,GAAG,IAAIA,WAAU,SAAS,MAAM,OAAOD,MAAK,IAAI,SAAS,OAAO,MAAMA,MAAK;AAClH,MAAI,EAAE,MAAM,IAAK,QAAO,CAAC;AACzB,QAAM,IAAI,KAAK,KAAK,GAAGE,SAAQ,IAAI,MAAM,CAAC;AAC1C,MAAID,UAAS;AACX,QAAI,MAAM,EAAG,UAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAC,OAAM,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,QAC3D,UAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,OAAM,CAAC,KAAK,KAAK,KAAK;AAAA,EACzD,OAAO;AACL,QAAI,MAAM,EAAG,UAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,OAAM,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,QAC3D,UAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,OAAM,CAAC,KAAK,KAAK,KAAK;AAAA,EACzD;AACA,SAAOA;AACT;AAEO,SAAS,cAAc,OAAO,MAAMF,QAAO;AAChD,SAAO,CAAC,MAAM,QAAQ,CAAC,OAAOA,SAAQ,CAACA;AACvC,SAAO,SAAS,OAAO,MAAMA,MAAK,EAAE,CAAC;AACvC;AAEO,SAAS,SAAS,OAAO,MAAMA,QAAO;AAC3C,SAAO,CAAC,MAAM,QAAQ,CAAC,OAAOA,SAAQ,CAACA;AACvC,QAAMC,WAAU,OAAO,OAAO,MAAMA,WAAU,cAAc,MAAM,OAAOD,MAAK,IAAI,cAAc,OAAO,MAAMA,MAAK;AAClH,UAAQC,WAAU,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,MAAM;AACpD;;;ACpDe,SAAR,KAAsB,OAAO,MAAME,QAAO;AAC/C,MAAI;AACJ,SAAO,MAAM;AACX,UAAM,OAAO,cAAc,OAAO,MAAMA,MAAK;AAC7C,QAAI,SAAS,WAAW,SAAS,KAAK,CAAC,SAAS,IAAI,GAAG;AACrD,aAAO,CAAC,OAAO,IAAI;AAAA,IACrB,WAAW,OAAO,GAAG;AACnB,cAAQ,KAAK,MAAM,QAAQ,IAAI,IAAI;AACnC,aAAO,KAAK,KAAK,OAAO,IAAI,IAAI;AAAA,IAClC,WAAW,OAAO,GAAG;AACnB,cAAQ,KAAK,KAAK,QAAQ,IAAI,IAAI;AAClC,aAAO,KAAK,MAAM,OAAO,IAAI,IAAI;AAAA,IACnC;AACA,cAAU;AAAA,EACZ;AACF;;;ACfe,SAAR,iBAAkC,QAAQ;AAC/C,SAAO,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK,IAAI,MAAM,MAAM,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC;AACtE;;;ACKe,SAAR,MAAuB;AAC5B,MAAI,QAAQ,UACR,SAAS,QACT,YAAY;AAEhB,WAAS,UAAU,MAAM;AACvB,QAAI,CAAC,MAAM,QAAQ,IAAI,EAAG,QAAO,MAAM,KAAK,IAAI;AAEhD,QAAI,GACA,IAAI,KAAK,QACT,GACA,MACA,SAAS,IAAI,MAAM,CAAC;AAExB,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,aAAO,CAAC,IAAI,MAAM,KAAK,CAAC,GAAG,GAAG,IAAI;AAAA,IACpC;AAEA,QAAI,KAAK,OAAO,MAAM,GAClB,KAAK,GAAG,CAAC,GACT,KAAK,GAAG,CAAC,GACT,KAAK,UAAU,QAAQ,IAAI,EAAE;AAIjC,QAAI,CAAC,MAAM,QAAQ,EAAE,GAAG;AACtB,YAAMC,OAAM,IAAI,KAAK,CAAC;AACtB,UAAI,WAAW,OAAQ,EAAC,IAAI,EAAE,IAAI,KAAK,IAAI,IAAI,EAAE;AACjD,WAAK,MAAM,IAAI,IAAI,EAAE;AAKrB,UAAI,GAAG,CAAC,KAAK,GAAI,QAAO,cAAc,IAAI,IAAI,EAAE;AAShD,UAAI,GAAG,GAAG,SAAS,CAAC,KAAK,IAAI;AAC3B,YAAIA,QAAO,MAAM,WAAW,QAAQ;AAClC,gBAAMC,QAAO,cAAc,IAAI,IAAI,EAAE;AACrC,cAAI,SAASA,KAAI,GAAG;AAClB,gBAAIA,QAAO,GAAG;AACZ,oBAAM,KAAK,MAAM,KAAKA,KAAI,IAAI,KAAKA;AAAA,YACrC,WAAWA,QAAO,GAAG;AACnB,oBAAM,KAAK,KAAK,KAAK,CAACA,KAAI,IAAI,KAAK,CAACA;AAAA,YACtC;AAAA,UACF;AAAA,QACF,OAAO;AACL,aAAG,IAAI;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAIA,QAAI,IAAI,GAAG,QAAQ,IAAI,GAAG,IAAI;AAC9B,WAAO,GAAG,CAAC,KAAK,GAAI,GAAE;AACtB,WAAO,GAAG,IAAI,CAAC,IAAI,GAAI,GAAE;AACzB,QAAI,KAAK,IAAI,EAAG,MAAK,GAAG,MAAM,GAAG,CAAC,GAAG,IAAI,IAAI;AAE7C,QAAI,OAAO,IAAI,MAAM,IAAI,CAAC,GACtBC;AAGJ,SAAK,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AACvB,MAAAA,OAAM,KAAK,CAAC,IAAI,CAAC;AACjB,MAAAA,KAAI,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;AAC7B,MAAAA,KAAI,KAAK,IAAI,IAAI,GAAG,CAAC,IAAI;AAAA,IAC3B;AAGA,QAAI,SAAS,IAAI,GAAG;AAClB,UAAI,OAAO,GAAG;AACZ,aAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,eAAK,IAAI,OAAO,CAAC,MAAM,QAAQ,MAAM,KAAK,KAAK,IAAI;AACjD,iBAAK,KAAK,IAAI,GAAG,KAAK,OAAO,IAAI,MAAM,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAAA,UAC7D;AAAA,QACF;AAAA,MACF,WAAW,OAAO,GAAG;AACnB,aAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,eAAK,IAAI,OAAO,CAAC,MAAM,QAAQ,MAAM,KAAK,KAAK,IAAI;AACjD,kBAAM,IAAI,KAAK,OAAO,KAAK,KAAK,IAAI;AACpC,iBAAK,KAAK,IAAI,GAAG,KAAK,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAAA,UAClD;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,aAAK,IAAI,OAAO,CAAC,MAAM,QAAQ,MAAM,KAAK,KAAK,IAAI;AACjD,eAAK,eAAO,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC;AAAA,QACxC;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,YAAU,QAAQ,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,QAAQ,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,aAAa;AAAA,EAC7F;AAEA,YAAU,SAAS,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,aAAa;AAAA,EACzG;AAEA,YAAU,aAAa,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAI,SAAS,MAAM,QAAQ,CAAC,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,aAAa;AAAA,EACpI;AAEA,SAAO;AACT;;;AC5He,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIC;AACJ,MAAI,YAAY,QAAW;AACzB,eAAW,SAAS,QAAQ;AAC1B,UAAI,SAAS,SACLA,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SACzCD,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;;;ACnBe,SAAR,SAA0B,QAAQ,SAAS;AAChD,MAAIE;AACJ,MAAIC,YAAW;AACf,MAAIC,SAAQ;AACZ,MAAI,YAAY,QAAW;AACzB,eAAW,SAAS,QAAQ;AAC1B,QAAEA;AACF,UAAI,SAAS,SACLF,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM,OAAOC,YAAWC;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,OAAO;AACL,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SACzCF,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM,OAAOC,YAAWC;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAOD;AACT;;;ACrBe,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIE;AACJ,MAAI,YAAY,QAAW;AACzB,eAAW,SAAS,QAAQ;AAC1B,UAAI,SAAS,SACLA,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SACzCD,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;;;ACnBe,SAAR,SAA0B,QAAQ,SAAS;AAChD,MAAIE;AACJ,MAAIC,YAAW;AACf,MAAIC,SAAQ;AACZ,MAAI,YAAY,QAAW;AACzB,eAAW,SAAS,QAAQ;AAC1B,QAAEA;AACF,UAAI,SAAS,SACLF,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM,OAAOC,YAAWC;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,OAAO;AACL,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SACzCF,OAAM,SAAUA,SAAQ,UAAa,SAAS,QAAS;AAC7D,QAAAA,OAAM,OAAOC,YAAWC;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAOD;AACT;;;ACjBe,SAAR,YAA6BE,QAAO,GAAG,OAAO,GAAG,QAAQ,UAAU,SAAS;AACjF,MAAI,KAAK,MAAM,CAAC;AAChB,SAAO,KAAK,MAAM,KAAK,IAAI,GAAG,IAAI,CAAC;AACnC,UAAQ,KAAK,MAAM,KAAK,IAAIA,OAAM,SAAS,GAAG,KAAK,CAAC;AAEpD,MAAI,EAAE,QAAQ,KAAK,KAAK,OAAQ,QAAOA;AAEvC,YAAU,YAAY,SAAY,mBAAmB,eAAe,OAAO;AAE3E,SAAO,QAAQ,MAAM;AACnB,QAAI,QAAQ,OAAO,KAAK;AACtB,YAAM,IAAI,QAAQ,OAAO;AACzB,YAAM,IAAI,IAAI,OAAO;AACrB,YAAM,IAAI,KAAK,IAAI,CAAC;AACpB,YAAM,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC;AAClC,YAAM,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,KAAK;AACxE,YAAM,UAAU,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;AAC7D,YAAM,WAAW,KAAK,IAAI,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AACrE,kBAAYA,QAAO,GAAG,SAAS,UAAU,OAAO;AAAA,IAClD;AAEA,UAAM,IAAIA,OAAM,CAAC;AACjB,QAAI,IAAI;AACR,QAAI,IAAI;AAER,SAAKA,QAAO,MAAM,CAAC;AACnB,QAAI,QAAQA,OAAM,KAAK,GAAG,CAAC,IAAI,EAAG,MAAKA,QAAO,MAAM,KAAK;AAEzD,WAAO,IAAI,GAAG;AACZ,WAAKA,QAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE;AAC1B,aAAO,QAAQA,OAAM,CAAC,GAAG,CAAC,IAAI,EAAG,GAAE;AACnC,aAAO,QAAQA,OAAM,CAAC,GAAG,CAAC,IAAI,EAAG,GAAE;AAAA,IACrC;AAEA,QAAI,QAAQA,OAAM,IAAI,GAAG,CAAC,MAAM,EAAG,MAAKA,QAAO,MAAM,CAAC;AAAA,QACjD,GAAE,GAAG,KAAKA,QAAO,GAAG,KAAK;AAE9B,QAAI,KAAK,EAAG,QAAO,IAAI;AACvB,QAAI,KAAK,EAAG,SAAQ,IAAI;AAAA,EAC1B;AAEA,SAAOA;AACT;AAEA,SAAS,KAAKA,QAAO,GAAG,GAAG;AACzB,QAAM,IAAIA,OAAM,CAAC;AACjB,EAAAA,OAAM,CAAC,IAAIA,OAAM,CAAC;AAClB,EAAAA,OAAM,CAAC,IAAI;AACb;;;AClDe,SAAR,SAA0B,QAAQ,UAAU,WAAW;AAC5D,MAAIC;AACJ,MAAI,UAAU;AACd,MAAI,QAAQ,WAAW,GAAG;AACxB,QAAI;AACJ,eAAW,WAAW,QAAQ;AAC5B,YAAM,QAAQ,QAAQ,OAAO;AAC7B,UAAI,UACE,UAAU,OAAO,QAAQ,IAAI,IAC7B,UAAU,OAAO,KAAK,MAAM,GAAG;AACnC,QAAAA,OAAM;AACN,mBAAW;AACX,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,OAAO;AACL,eAAW,SAAS,QAAQ;AAC1B,UAAI,UACE,QAAQ,OAAOA,IAAG,IAAI,IACtB,QAAQ,OAAO,KAAK,MAAM,GAAG;AACjC,QAAAA,OAAM;AACN,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;;;ACnBe,SAAR,SAA0B,QAAQ,GAAG,SAAS;AACnD,WAAS,aAAa,KAAK,QAAQ,QAAQ,OAAO,CAAC;AACnD,MAAI,EAAE,IAAI,OAAO,WAAW,MAAM,IAAI,CAAC,CAAC,EAAG;AAC3C,MAAI,KAAK,KAAK,IAAI,EAAG,QAAO,IAAI,MAAM;AACtC,MAAI,KAAK,EAAG,QAAO,IAAI,MAAM;AAC7B,MAAI,GACA,KAAK,IAAI,KAAK,GACd,KAAK,KAAK,MAAM,CAAC,GACjB,SAAS,IAAI,YAAY,QAAQ,EAAE,EAAE,SAAS,GAAG,KAAK,CAAC,CAAC,GACxD,SAAS,IAAI,OAAO,SAAS,KAAK,CAAC,CAAC;AACxC,SAAO,UAAU,SAAS,WAAW,IAAI;AAC3C;AAEO,SAAS,eAAe,QAAQ,GAAG,UAAU,QAAQ;AAC1D,MAAI,EAAE,IAAI,OAAO,WAAW,MAAM,IAAI,CAAC,CAAC,EAAG;AAC3C,MAAI,KAAK,KAAK,IAAI,EAAG,QAAO,CAAC,QAAQ,OAAO,CAAC,GAAG,GAAG,MAAM;AACzD,MAAI,KAAK,EAAG,QAAO,CAAC,QAAQ,OAAO,IAAI,CAAC,GAAG,IAAI,GAAG,MAAM;AACxD,MAAI,GACA,KAAK,IAAI,KAAK,GACd,KAAK,KAAK,MAAM,CAAC,GACjB,SAAS,CAAC,QAAQ,OAAO,EAAE,GAAG,IAAI,MAAM,GACxC,SAAS,CAAC,QAAQ,OAAO,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM;AACpD,SAAO,UAAU,SAAS,WAAW,IAAI;AAC3C;AAEO,SAAS,cAAc,QAAQ,GAAG,UAAU,QAAQ;AACzD,MAAI,MAAM,IAAI,CAAC,CAAC,EAAG;AACnB,EAAAC,WAAU,aAAa,KAAK,QAAQ,CAAC,GAAGC,OAAM,OAAO,QAAQ,OAAOA,EAAC,GAAGA,IAAG,MAAM,CAAC,CAAC;AACnF,MAAI,KAAK,EAAG,QAAO,SAASD,QAAO;AACnC,MAAI,KAAK,EAAG,QAAO,SAASA,QAAO;AACnC,MAAIA,UACAE,SAAQ,YAAY,KAAK,QAAQ,CAAC,GAAGD,OAAMA,EAAC,GAC5C,IAAID,SAAQ,SAAS,GACrB,IAAI,KAAK,MAAM,IAAI,CAAC;AACxB,cAAYE,QAAO,GAAG,GAAG,GAAG,CAACD,IAAGE,OAAM,iBAAiBH,SAAQC,EAAC,GAAGD,SAAQG,EAAC,CAAC,CAAC;AAC9E,MAAI,SAASD,OAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAACD,OAAMD,SAAQC,EAAC,CAAC;AACxD,SAAO,KAAK,IAAI,IAAI;AACtB;;;AC3Ce,SAAR,0BAA2C,QAAQG,MAAKC,MAAK;AAClE,QAAM,IAAI,MAAM,MAAM,GAAG,IAAI,SAAS,QAAQ,IAAI,IAAI,SAAS,QAAQ,IAAI;AAC3E,SAAO,KAAK,IAAI,KAAK,MAAMA,OAAMD,SAAQ,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,EAAE,IAAI;AAC3E;;;ACHe,SAAR,eAAgC,QAAQE,MAAKC,MAAK;AACvD,QAAM,IAAI,MAAM,MAAM,GAAG,IAAI,UAAU,MAAM;AAC7C,SAAO,KAAK,IAAI,KAAK,MAAMA,OAAMD,QAAO,KAAK,KAAK,CAAC,KAAK,OAAO,EAAE,IAAI;AACvE;;;ACNe,SAAR,KAAsB,QAAQ,SAAS;AAC5C,MAAIE,SAAQ;AACZ,MAAIC,OAAM;AACV,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,SAAS,SAAS,QAAQ,CAAC,UAAU,OAAO;AAC9C,UAAED,QAAOC,QAAO;AAAA,MAClB;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,SAAS,QAAQ,CAAC,UAAU,OAAO;AAClF,UAAEF,QAAOC,QAAO;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACA,MAAID,OAAO,QAAOC,OAAMD;AAC1B;;;AChBe,SAAR,OAAwB,QAAQ,SAAS;AAC9C,SAAO,SAAS,QAAQ,KAAK,OAAO;AACtC;AAEO,SAAS,YAAY,QAAQ,SAAS;AAC3C,SAAO,cAAc,QAAQ,KAAK,OAAO;AAC3C;;;ACRA,UAAUG,SAAQ,QAAQ;AACxB,aAAWC,UAAS,QAAQ;AAC1B,WAAOA;AAAA,EACT;AACF;AAEe,SAAR,MAAuB,QAAQ;AACpC,SAAO,MAAM,KAAKD,SAAQ,MAAM,CAAC;AACnC;;;ACNe,SAAR,KAAsB,QAAQ,SAAS;AAC5C,QAAM,SAAS,IAAI,UAAU;AAC7B,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,SAAS,QAAQ,SAAS,OAAO;AACnC,eAAO,IAAI,QAAQ,OAAO,IAAI,KAAK,KAAK,KAAK,CAAC;AAAA,MAChD;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIE,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,WAAK,QAAQ,QAAQ,OAAO,EAAEA,QAAO,MAAM,MAAM,QAAQ,SAAS,OAAO;AACvE,eAAO,IAAI,QAAQ,OAAO,IAAI,KAAK,KAAK,KAAK,CAAC;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AACA,MAAI;AACJ,MAAI,YAAY;AAChB,aAAW,CAAC,OAAOC,MAAK,KAAK,QAAQ;AACnC,QAAIA,SAAQ,WAAW;AACrB,kBAAYA;AACZ,kBAAY;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;;;AC3Be,SAAR,MAAuB,QAAQ,SAAS,MAAM;AACnD,QAAMC,SAAQ,CAAC;AACf,MAAI;AACJ,MAAI,QAAQ;AACZ,aAAW,SAAS,QAAQ;AAC1B,QAAI,MAAO,CAAAA,OAAM,KAAK,OAAO,UAAU,KAAK,CAAC;AAC7C,eAAW;AACX,YAAQ;AAAA,EACV;AACA,SAAOA;AACT;AAEO,SAAS,KAAK,GAAG,GAAG;AACzB,SAAO,CAAC,GAAG,CAAC;AACd;;;ACde,SAAR,MAAuB,OAAO,MAAM,MAAM;AAC/C,UAAQ,CAAC,OAAO,OAAO,CAAC,MAAM,QAAQ,IAAI,UAAU,UAAU,KAAK,OAAO,OAAO,QAAQ,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC;AAE9G,MAAI,IAAI,IACJ,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,OAAO,SAAS,IAAI,CAAC,IAAI,GACpDC,SAAQ,IAAI,MAAM,CAAC;AAEvB,SAAO,EAAE,IAAI,GAAG;AACd,IAAAA,OAAM,CAAC,IAAI,QAAQ,IAAI;AAAA,EACzB;AAEA,SAAOA;AACT;;;ACTe,SAAR,KAAsB,QAAQ,UAAU,WAAW;AACxD,MAAI,OAAO,OAAO,OAAO,QAAQ,MAAM,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/F,MAAI,IAAI,MAAM,KAAK,MAAM;AACzB,QAAM,IAAI,IAAI,aAAa,EAAE,MAAM;AACnC,MAAI,QAAQ,WAAW,EAAG,KAAI,EAAE,IAAI,OAAO,GAAG,UAAU;AACxD,QAAM,eAAe,CAAC,GAAG,MAAM,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACjD,MAAI,GAAG;AACP,WAAS,YAAY,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC;AAExC,SAAO,KAAK,YAAY,YAAY,CAAC,GAAG,MAAM,iBAAiB,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,eAAe,YAAY,CAAC;AACzG,SAAO,QAAQ,CAAC,GAAG,MAAM;AACrB,UAAM,IAAI,aAAa,GAAG,MAAM,SAAY,IAAI,CAAC;AACjD,QAAI,KAAK,GAAG;AACV,UAAI,MAAM,UAAa,IAAI,EAAG,KAAI,GAAG,IAAI;AACzC,QAAE,CAAC,IAAI;AAAA,IACT,OAAO;AACL,QAAE,CAAC,IAAI;AAAA,IACT;AAAA,EACF,CAAC;AACH,SAAO;AACT;;;ACrBe,SAAR,MAAuB,QAAQ,UAAU,WAAW;AACzD,MAAIC;AACJ,MAAI,UAAU;AACd,MAAI,QAAQ,WAAW,GAAG;AACxB,QAAI;AACJ,eAAW,WAAW,QAAQ;AAC5B,YAAM,QAAQ,QAAQ,OAAO;AAC7B,UAAI,UACE,UAAU,OAAO,QAAQ,IAAI,IAC7B,UAAU,OAAO,KAAK,MAAM,GAAG;AACnC,QAAAA,OAAM;AACN,mBAAW;AACX,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,OAAO;AACL,eAAW,SAAS,QAAQ;AAC1B,UAAI,UACE,QAAQ,OAAOA,IAAG,IAAI,IACtB,QAAQ,OAAO,KAAK,MAAM,GAAG;AACjC,QAAAA,OAAM;AACN,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;;;ACzBe,SAAR,WAA4B,QAAQ,UAAU,WAAW;AAC9D,MAAI,QAAQ,WAAW,EAAG,QAAO,SAAS,QAAQ,OAAO;AACzD,MAAI;AACJ,MAAIC,OAAM;AACV,MAAIC,SAAQ;AACZ,aAAW,SAAS,QAAQ;AAC1B,MAAEA;AACF,QAAID,OAAM,IACJ,QAAQ,OAAO,KAAK,MAAM,IAC1B,QAAQ,OAAO,QAAQ,IAAI,GAAG;AAClC,iBAAW;AACX,MAAAA,OAAMC;AAAA,IACR;AAAA,EACF;AACA,SAAOD;AACT;;;ACfe,SAAR,cAA+B,QAAQ,UAAU,WAAW;AACjE,MAAI,QAAQ,WAAW,EAAG,QAAO,SAAS,QAAQ,OAAO;AACzD,MAAI;AACJ,MAAIE,OAAM;AACV,MAAIC,SAAQ;AACZ,aAAW,SAAS,QAAQ;AAC1B,MAAEA;AACF,QAAID,OAAM,IACJ,QAAQ,OAAO,KAAK,MAAM,IAC1B,QAAQ,OAAO,QAAQ,IAAI,GAAG;AAClC,iBAAW;AACX,MAAAA,OAAMC;AAAA,IACR;AAAA,EACF;AACA,SAAOD;AACT;;;AChBe,SAAR,KAAsB,QAAQ,SAAS;AAC5C,QAAME,SAAQ,WAAW,QAAQ,OAAO;AACxC,SAAOA,SAAQ,IAAI,SAAYA;AACjC;;;ACLA,IAAO,kBAAQ,SAAS,KAAK,MAAM;AAE5B,SAAS,SAAS,QAAQ;AAC/B,SAAO,SAAS,QAAQC,QAAO,KAAK,GAAG,KAAKA,OAAM,QAAQ;AACxD,QAAI,IAAI,MAAM,KAAK,CAAC;AACpB,WAAO,GAAG;AACR,YAAM,IAAI,OAAO,IAAI,MAAM,GAAG,IAAIA,OAAM,IAAI,EAAE;AAC9C,MAAAA,OAAM,IAAI,EAAE,IAAIA,OAAM,IAAI,EAAE;AAC5B,MAAAA,OAAM,IAAI,EAAE,IAAI;AAAA,IAClB;AACA,WAAOA;AAAA,EACT;AACF;;;ACZe,SAAR,IAAqB,QAAQ,SAAS;AAC3C,MAAIC,OAAM;AACV,MAAI,YAAY,QAAW;AACzB,aAAS,SAAS,QAAQ;AACxB,UAAI,QAAQ,CAAC,OAAO;AAClB,QAAAA,QAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAIC,SAAQ;AACZ,aAAS,SAAS,QAAQ;AACxB,UAAI,QAAQ,CAAC,QAAQ,OAAO,EAAEA,QAAO,MAAM,GAAG;AAC5C,QAAAD,QAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAOA;AACT;;;ACfe,SAAR,UAA2B,QAAQ;AACxC,MAAI,EAAE,IAAI,OAAO,QAAS,QAAO,CAAC;AAClC,WAAS,IAAI,IAAI,IAAI,IAAI,QAAQE,OAAM,GAAGC,aAAY,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,KAAI;AAC5E,aAAS,IAAI,IAAI,GAAG,MAAMA,WAAU,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,KAAI;AAC/D,UAAI,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC;AAAA,IACtB;AAAA,EACF;AACA,SAAOA;AACT;AAEA,SAASD,QAAO,GAAG;AACjB,SAAO,EAAE;AACX;;;ACZe,SAAR,MAAuB;AAC5B,SAAO,UAAU,SAAS;AAC5B;;;ACJe,SAAR,MAAuB,QAAQ,MAAM;AAC1C,MAAI,OAAO,SAAS,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC5E,MAAIE,SAAQ;AACZ,aAAW,SAAS,QAAQ;AAC1B,QAAI,CAAC,KAAK,OAAO,EAAEA,QAAO,MAAM,GAAG;AACjC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;ACTe,SAAR,KAAsB,QAAQ,MAAM;AACzC,MAAI,OAAO,SAAS,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC5E,MAAIC,SAAQ;AACZ,aAAW,SAAS,QAAQ;AAC1B,QAAI,KAAK,OAAO,EAAEA,QAAO,MAAM,GAAG;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;ACTe,SAAR,OAAwB,QAAQ,MAAM;AAC3C,MAAI,OAAO,SAAS,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC5E,QAAMC,SAAQ,CAAC;AACf,MAAIC,SAAQ;AACZ,aAAW,SAAS,QAAQ;AAC1B,QAAI,KAAK,OAAO,EAAEA,QAAO,MAAM,GAAG;AAChC,MAAAD,OAAM,KAAK,KAAK;AAAA,IAClB;AAAA,EACF;AACA,SAAOA;AACT;;;ACVe,SAARE,KAAqB,QAAQ,QAAQ;AAC1C,MAAI,OAAO,OAAO,OAAO,QAAQ,MAAM,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/F,MAAI,OAAO,WAAW,WAAY,OAAM,IAAI,UAAU,0BAA0B;AAChF,SAAO,MAAM,KAAK,QAAQ,CAAC,OAAOC,WAAU,OAAO,OAAOA,QAAO,MAAM,CAAC;AAC1E;;;ACJe,SAAR,OAAwB,QAAQC,UAAS,OAAO;AACrD,MAAI,OAAOA,aAAY,WAAY,OAAM,IAAI,UAAU,2BAA2B;AAClF,QAAM,WAAW,OAAO,OAAO,QAAQ,EAAE;AACzC,MAAI,MAAM,MAAMC,SAAQ;AACxB,MAAI,UAAU,SAAS,GAAG;AACxB,KAAC,EAAC,MAAM,MAAK,IAAI,SAAS,KAAK;AAC/B,QAAI,KAAM;AACV,MAAEA;AAAA,EACJ;AACA,SAAQ,EAAC,MAAM,OAAO,KAAI,IAAI,SAAS,KAAK,GAAI,CAAC,MAAM;AACrD,YAAQD,SAAQ,OAAO,MAAM,EAAEC,QAAO,MAAM;AAAA,EAC9C;AACA,SAAO;AACT;;;ACbe,SAAR,QAAyB,QAAQ;AACtC,MAAI,OAAO,OAAO,OAAO,QAAQ,MAAM,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/F,SAAO,MAAM,KAAK,MAAM,EAAE,QAAQ;AACpC;;;ACDe,SAAR,WAA4B,WAAW,QAAQ;AACpD,WAAS,IAAI,UAAU,MAAM;AAC7B,aAAW,SAAS,QAAQ;AAC1B,eAAW,SAAS,OAAO;AACzB,aAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AACT;;;ACRe,SAAR,SAA0B,QAAQ,OAAO;AAC9C,QAAM,WAAW,MAAM,OAAO,QAAQ,EAAE,GAAGC,OAAM,IAAI,UAAU;AAC/D,aAAW,KAAK,QAAQ;AACtB,QAAIA,KAAI,IAAI,CAAC,EAAG,QAAO;AACvB,QAAI,OAAO;AACX,WAAQ,EAAC,OAAO,KAAI,IAAI,SAAS,KAAK,GAAI;AACxC,UAAI,KAAM;AACV,UAAI,OAAO,GAAG,GAAG,KAAK,EAAG,QAAO;AAChC,MAAAA,KAAI,IAAI,KAAK;AAAA,IACf;AAAA,EACF;AACA,SAAO;AACT;;;ACZe,SAAR,aAA8B,WAAW,QAAQ;AACtD,WAAS,IAAI,UAAU,MAAM;AAC7B,WAAS,OAAO,IAAI,GAAG;AACvB,MAAK,YAAW,SAAS,QAAQ;AAC/B,eAAW,SAAS,QAAQ;AAC1B,UAAI,CAAC,MAAM,IAAI,KAAK,GAAG;AACrB,eAAO,OAAO,KAAK;AACnB,iBAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,IAAI,QAAQ;AACnB,SAAO,kBAAkB,YAAY,SAAS,IAAI,UAAU,MAAM;AACpE;;;AClBe,SAAR,SAA0B,QAAQ,OAAO;AAC9C,QAAM,WAAW,OAAO,OAAO,QAAQ,EAAE,GAAGC,OAAM,oBAAI,IAAI;AAC1D,aAAW,KAAK,OAAO;AACrB,UAAM,KAAK,OAAO,CAAC;AACnB,QAAIA,KAAI,IAAI,EAAE,EAAG;AACjB,QAAI,OAAO;AACX,WAAQ,EAAC,OAAO,KAAI,IAAI,SAAS,KAAK,GAAI;AACxC,UAAI,KAAM,QAAO;AACjB,YAAM,SAAS,OAAO,KAAK;AAC3B,MAAAA,KAAI,IAAI,MAAM;AACd,UAAI,OAAO,GAAG,IAAI,MAAM,EAAG;AAAA,IAC7B;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,OAAO,OAAO;AACrB,SAAO,UAAU,QAAQ,OAAO,UAAU,WAAW,MAAM,QAAQ,IAAI;AACzE;;;AChBe,SAAR,OAAwB,QAAQ,OAAO;AAC5C,SAAO,SAAS,OAAO,MAAM;AAC/B;;;ACFe,SAAR,SAA0B,QAAQ;AACvC,QAAMC,OAAM,IAAI,UAAU;AAC1B,aAAW,SAAS,QAAQ;AAC1B,eAAW,KAAK,OAAO;AACrB,MAAAA,KAAI,IAAI,CAAC;AAAA,IACX;AAAA,EACF;AACA,SAAOA;AACT;", "names": ["index", "length", "blur", "sum", "count", "index", "array", "length", "reduce", "index", "j", "i", "sum", "index", "count", "mean", "sum", "index", "min", "max", "index", "index", "key", "groups", "reduce", "map", "values", "keyof", "index", "group", "index", "f", "reduce", "key", "count", "reverse", "ticks", "count", "max", "step", "bin", "max", "index", "max", "maxIndex", "index", "min", "index", "min", "minIndex", "index", "array", "max", "numbers", "i", "index", "j", "min", "max", "min", "max", "count", "sum", "index", "flatten", "array", "index", "count", "pairs", "range", "min", "min", "index", "max", "index", "index", "array", "sum", "index", "length", "transpose", "index", "index", "array", "index", "map", "index", "reducer", "index", "set", "set", "set"]}