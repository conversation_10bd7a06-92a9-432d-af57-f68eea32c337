{"version": 3, "sources": ["../../sveltekit-superforms/dist/stringPath.js", "../../sveltekit-superforms/dist/justClone.js", "../../sveltekit-superforms/dist/traversal.js", "../../sveltekit-superforms/dist/jsonSchema/schemaInfo.js", "../../sveltekit-superforms/dist/jsonSchema/schemaDefaults.js", "../../sveltekit-superforms/dist/errors.js", "../../sveltekit-superforms/dist/utils.js", "../../sveltekit-superforms/dist/jsonSchema/schemaShape.js", "../../sveltekit-superforms/dist/jsonSchema/constraints.js", "../../sveltekit-superforms/dist/jsonSchema/schemaHash.js", "../../sveltekit-superforms/dist/adapters/simple-schema/index.js", "../../sveltekit-superforms/dist/adapters/adapters.js"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nexport function splitPath(path) {\n    return path\n        .toString()\n        .split(/[[\\].]+/)\n        .filter((p) => p);\n}\nexport function mergePath(path) {\n    return path.reduce((acc, next) => {\n        const key = String(next);\n        if (typeof next === 'number' || /^\\d+$/.test(key))\n            acc += `[${key}]`;\n        else if (!acc)\n            acc += key;\n        else\n            acc += `.${key}`;\n        return acc;\n    }, '');\n}\n", "/*\n  Deep clones all properties except functions\n\n  var arr = [1, 2, 3];\n  var subObj = {aa: 1};\n  var obj = {a: 3, b: 5, c: arr, d: subObj};\n  var objClone = clone(obj);\n  arr.push(4);\n  subObj.bb = 2;\n  obj; // {a: 3, b: 5, c: [1, 2, 3, 4], d: {aa: 1}}\n  objClone; // {a: 3, b: 5, c: [1, 2, 3], d: {aa: 1, bb: 2}}\n*/\nexport function clone(obj) {\n    const type = {}.toString.call(obj).slice(8, -1);\n    if (type == 'Set') {\n        // @ts-expect-error Known type\n        return new Set([...obj].map((value) => clone(value)));\n    }\n    if (type == 'Map') {\n        // @ts-expect-error Known type\n        return new Map([...obj].map((kv) => [clone(kv[0]), clone(kv[1])]));\n    }\n    if (type == 'Date') {\n        // @ts-expect-error Known type\n        return new Date(obj.getTime());\n    }\n    if (type == 'RegExp') {\n        // @ts-expect-error Known type\n        return RegExp(obj.source, obj.flags);\n    }\n    if (type == 'Array' || type == 'Object') {\n        const result = type == 'Object' ? Object.create(Object.getPrototypeOf(obj)) : [];\n        for (const key in obj) {\n            result[key] = clone(obj[key]);\n        }\n        return result;\n    }\n    // primitives and non-supported objects (e.g. functions) land here\n    return obj;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\nfunction setPath(parent, key, value) {\n    parent[key] = value;\n    return 'skip';\n}\nfunction isInvalidPath(originalPath, pathData) {\n    return (pathData.value !== undefined &&\n        typeof pathData.value !== 'object' &&\n        pathData.path.length < originalPath.length);\n}\nexport function pathExists(obj, path, options = {}) {\n    if (!options.modifier) {\n        options.modifier = (pathData) => (isInvalidPath(path, pathData) ? undefined : pathData.value);\n    }\n    const exists = traversePath(obj, path, options.modifier);\n    if (!exists)\n        return undefined;\n    if (options.value === undefined)\n        return exists;\n    return options.value(exists.value) ? exists : undefined;\n}\nexport function traversePath(obj, realPath, modifier) {\n    if (!realPath.length)\n        return undefined;\n    const path = [realPath[0]];\n    let parent = obj;\n    while (parent && path.length < realPath.length) {\n        const key = path[path.length - 1];\n        const value = modifier\n            ? modifier({\n                parent,\n                key: String(key),\n                value: parent[key],\n                path: path.map((p) => String(p)),\n                isLeaf: false,\n                set: (v) => setPath(parent, key, v)\n            })\n            : parent[key];\n        if (value === undefined)\n            return undefined;\n        else\n            parent = value;\n        path.push(realPath[path.length]);\n    }\n    if (!parent)\n        return undefined;\n    const key = realPath[realPath.length - 1];\n    return {\n        parent,\n        key: String(key),\n        value: parent[key],\n        path: realPath.map((p) => String(p)),\n        isLeaf: true,\n        set: (v) => setPath(parent, key, v)\n    };\n}\nexport function traversePaths(parent, modifier, path = []) {\n    for (const key in parent) {\n        const value = parent[key];\n        const isLeaf = value === null || typeof value !== 'object';\n        const pathData = {\n            parent,\n            key,\n            value,\n            path: path.concat([key]), // path.map(String).concat([key])\n            isLeaf,\n            set: (v) => setPath(parent, key, v)\n        };\n        const status = modifier(pathData);\n        if (status === 'abort')\n            return status;\n        else if (status === 'skip')\n            continue;\n        else if (!isLeaf) {\n            const status = traversePaths(value, modifier, pathData.path);\n            if (status === 'abort')\n                return status;\n        }\n    }\n}\n// Thanks to https://stackoverflow.com/a/31129384/70894\nfunction eqSet(xs, ys) {\n    return xs === ys || (xs.size === ys.size && [...xs].every((x) => ys.has(x)));\n}\n/**\n * Compare two objects and return the differences as paths.\n */\nexport function comparePaths(newObj, oldObj) {\n    const diffPaths = new Map();\n    function builtInDiff(one, other) {\n        if (one instanceof Date && other instanceof Date && one.getTime() !== other.getTime())\n            return true;\n        if (one instanceof Set && other instanceof Set && !eqSet(one, other))\n            return true;\n        if (one instanceof File && other instanceof File && one !== other)\n            return true;\n        return false;\n    }\n    function isBuiltin(data) {\n        return data instanceof Date || data instanceof Set || data instanceof File;\n    }\n    function checkPath(data, compareTo) {\n        const otherData = compareTo ? traversePath(compareTo, data.path) : undefined;\n        //console.log('Compare', data.path, data.value, 'to', otherData?.path, otherData?.value);\n        function addDiff() {\n            //console.log('Diff', data.path);\n            diffPaths.set(data.path.join(' '), data.path);\n            return 'skip';\n        }\n        if (isBuiltin(data.value)) {\n            if (!isBuiltin(otherData?.value) || builtInDiff(data.value, otherData.value)) {\n                return addDiff();\n            }\n        }\n        if (data.isLeaf) {\n            if (!otherData || data.value !== otherData.value) {\n                addDiff();\n            }\n        }\n    }\n    traversePaths(newObj, (data) => checkPath(data, oldObj));\n    traversePaths(oldObj, (data) => checkPath(data, newObj));\n    // Need to sort the list so the shortest paths comes first\n    const output = Array.from(diffPaths.values());\n    output.sort((a, b) => a.length - b.length);\n    return output;\n}\nexport function setPaths(obj, paths, value) {\n    const isFunction = typeof value === 'function';\n    for (const path of paths) {\n        const leaf = traversePath(obj, path, ({ parent, key, value }) => {\n            if (value === undefined || typeof value !== 'object') {\n                // If a previous check tainted the node, but the search goes deeper,\n                // so it needs to be replaced with a (parent) node\n                parent[key] = {};\n            }\n            return parent[key];\n        });\n        if (leaf)\n            leaf.parent[leaf.key] = isFunction ? value(path, leaf) : value;\n    }\n}\n", "import { assertSchema } from '../utils.js';\nimport { merge } from 'ts-deepmerge';\nconst conversionFormatTypes = ['unix-time', 'bigint', 'any', 'symbol', 'set', 'int64'];\n/**\n * Normalizes the different kind of schema variations (anyOf, union, const null, etc)\n * to figure out the field type, optional, nullable, etc.\n */\nexport function schemaInfo(schema, isOptional, path) {\n    assertSchema(schema, path);\n    const types = schemaTypes(schema, path);\n    const array = schema.items && types.includes('array')\n        ? (Array.isArray(schema.items) ? schema.items : [schema.items]).filter((s) => typeof s !== 'boolean')\n        : undefined;\n    const additionalProperties = schema.additionalProperties &&\n        typeof schema.additionalProperties === 'object' &&\n        types.includes('object')\n        ? Object.fromEntries(Object.entries(schema.additionalProperties).filter(([, value]) => typeof value !== 'boolean'))\n        : undefined;\n    const properties = schema.properties && types.includes('object')\n        ? Object.fromEntries(Object.entries(schema.properties).filter(([, value]) => typeof value !== 'boolean'))\n        : undefined;\n    const union = unionInfo(schema)?.filter((u) => u.type !== 'null' && u.const !== null);\n    const result = {\n        types: types.filter((s) => s !== 'null'),\n        isOptional,\n        isNullable: types.includes('null'),\n        schema,\n        union: union?.length ? union : undefined,\n        array,\n        properties,\n        additionalProperties,\n        required: schema.required\n    };\n    if (!schema.allOf || !schema.allOf.length) {\n        return result;\n    }\n    return {\n        ...merge.withOptions({ allowUndefinedOverrides: false }, result, ...schema.allOf.map((s) => schemaInfo(s, false, []))),\n        schema\n    };\n}\nfunction schemaTypes(schema, path) {\n    assertSchema(schema, path);\n    let types = schema.const === null ? ['null'] : [];\n    if (schema.type) {\n        types = Array.isArray(schema.type) ? schema.type : [schema.type];\n    }\n    if (schema.anyOf) {\n        types = schema.anyOf.flatMap((s) => schemaTypes(s, path));\n    }\n    if (types.includes('array') && schema.uniqueItems) {\n        const i = types.findIndex((t) => t != 'array');\n        types[i] = 'set';\n    }\n    else if (schema.format && conversionFormatTypes.includes(schema.format)) {\n        types.unshift(schema.format);\n        // Remove the integer type, as the schema format will be used\n        // instead in the following cases\n        if (schema.format == 'unix-time' || schema.format == 'int64') {\n            const i = types.findIndex((t) => t == 'integer');\n            types.splice(i, 1);\n        }\n    }\n    if (schema.const && schema.const !== null && typeof schema.const !== 'function') {\n        types.push(typeof schema.const);\n    }\n    return Array.from(new Set(types));\n}\nfunction unionInfo(schema) {\n    if (!schema.anyOf || !schema.anyOf.length)\n        return undefined;\n    return schema.anyOf.filter((s) => typeof s !== 'boolean');\n}\n", "import { SchemaError } from '../errors.js';\nimport { assertSchema } from '../utils.js';\nimport { merge } from 'ts-deepmerge';\nimport { schemaInfo } from './schemaInfo.js';\nexport function defaultValues(schema, isOptional = false, path = []) {\n    return _defaultValues(schema, isOptional, path);\n}\nfunction _defaultValues(schema, isOptional, path) {\n    if (!schema) {\n        throw new SchemaError('Schema was undefined', path);\n    }\n    const info = schemaInfo(schema, isOptional, path);\n    if (!info)\n        return undefined;\n    //if (schema.type == 'object') console.log('--- OBJECT ---');\n    //else console.dir({ path, schema, isOptional }, { depth: 10 });\n    let objectDefaults = undefined;\n    // Default takes (early) priority.\n    if ('default' in schema) {\n        // Test for object defaults.\n        // Cannot be returned directly, since undefined fields\n        // may have to be replaced with correct default values.\n        if (info.types.includes('object') &&\n            schema.default &&\n            typeof schema.default == 'object' &&\n            !Array.isArray(schema.default)) {\n            objectDefaults = schema.default;\n        }\n        else {\n            if (info.types.length > 1) {\n                if (info.types.includes('unix-time') &&\n                    (info.types.includes('integer') || info.types.includes('number')))\n                    throw new SchemaError('Cannot resolve a default value with a union that includes a date and a number/integer.', path);\n            }\n            const [type] = info.types;\n            return formatDefaultValue(type, schema.default);\n        }\n    }\n    let _multiType;\n    const isMultiTypeUnion = () => {\n        if (!info.union || info.union.length < 2)\n            return false;\n        if (info.union.some((i) => i.enum))\n            return true;\n        if (!_multiType) {\n            _multiType = new Set(info.types.map((i) => {\n                return ['integer', 'unix-time'].includes(i) ? 'number' : i;\n            }));\n        }\n        return _multiType.size > 1;\n    };\n    let output = undefined;\n    // Check unions first, so default values can take precedence over nullable and optional\n    if (!objectDefaults && info.union) {\n        const singleDefault = info.union.filter((s) => typeof s !== 'boolean' && s.default !== undefined);\n        if (singleDefault.length == 1) {\n            return _defaultValues(singleDefault[0], isOptional, path);\n        }\n        else if (singleDefault.length > 1) {\n            throw new SchemaError('Only one default value can exist in a union, or set a default value for the whole union.', path);\n        }\n        else {\n            // Null takes priority over undefined\n            if (info.isNullable)\n                return null;\n            if (info.isOptional)\n                return undefined;\n            if (isMultiTypeUnion()) {\n                throw new SchemaError('Multi-type unions must have a default value, or exactly one of the union types must have.', path);\n            }\n            // Objects must have default values to avoid setting undefined properties on nested data\n            if (info.union.length && info.types[0] == 'object') {\n                if (output === undefined)\n                    output = {};\n                output =\n                    info.union.length > 1\n                        ? merge.withOptions({ allowUndefinedOverrides: true }, ...info.union.map((s) => _defaultValues(s, isOptional, path)))\n                        : _defaultValues(info.union[0], isOptional, path);\n            }\n        }\n    }\n    if (!objectDefaults) {\n        // Null takes priority over undefined\n        if (info.isNullable)\n            return null;\n        if (info.isOptional)\n            return undefined;\n    }\n    // Objects\n    if (info.properties) {\n        for (const [key, objSchema] of Object.entries(info.properties)) {\n            assertSchema(objSchema, [...path, key]);\n            const def = objectDefaults && objectDefaults[key] !== undefined\n                ? objectDefaults[key]\n                : _defaultValues(objSchema, !info.required?.includes(key), [...path, key]);\n            //if (def !== undefined) output[key] = def;\n            if (output === undefined)\n                output = {};\n            output[key] = def;\n        }\n    }\n    else if (objectDefaults) {\n        return objectDefaults;\n    }\n    // TODO: [v3] Handle default values for array elements\n    // if (info.array && info.array.length) {\n    // \tconsole.log('===== Array default =====');\n    // \tconsole.dir(info.array, { depth: 10 }); //debug\n    // \t//if (info.array.length > 1) throw new SchemaError('Only one array type is supported.', path);\n    // \tconsole.dir(_defaultValues(info.array[0], info.isOptional, path), { depth: 10 }); //debug\n    // }\n    // Enums, return the first value so it can be a required field\n    if (schema.enum) {\n        return schema.enum[0];\n    }\n    // Basic type\n    if (isMultiTypeUnion()) {\n        throw new SchemaError('Default values cannot have more than one type.', path);\n    }\n    else if (info.types.length == 0) {\n        //console.warn('No type or format for property:', path); //debug\n        //console.dir(schema, { depth: 10 }); //debug\n        return undefined;\n    }\n    const [formatType] = info.types;\n    return output ?? defaultValue(formatType, schema.enum);\n}\nfunction formatDefaultValue(type, value) {\n    switch (type) {\n        case 'set':\n            return Array.isArray(value) ? new Set(value) : value;\n        case 'Date':\n        case 'date':\n        case 'unix-time':\n            if (typeof value === 'string' || typeof value === 'number')\n                return new Date(value);\n            break;\n        case 'bigint':\n            if (typeof value === 'string' || typeof value === 'number')\n                return BigInt(value);\n            break;\n        case 'symbol':\n            if (typeof value === 'string' || typeof value === 'number')\n                return Symbol(value);\n            break;\n    }\n    return value;\n}\nexport function defaultValue(type, enumType) {\n    switch (type) {\n        case 'string':\n            return enumType && enumType.length > 0 ? enumType[0] : '';\n        case 'number':\n        case 'integer':\n            return enumType && enumType.length > 0 ? enumType[0] : 0;\n        case 'boolean':\n            return false;\n        case 'array':\n            return [];\n        case 'object':\n            return {};\n        case 'null':\n            return null;\n        case 'Date':\n        case 'date':\n        case 'unix-time':\n            // Cannot add default for Date due to https://github.com/Rich-Harris/devalue/issues/51\n            return undefined;\n        case 'int64':\n        case 'bigint':\n            return BigInt(0);\n        case 'set':\n            return new Set();\n        case 'symbol':\n            return Symbol();\n        case 'undefined':\n        case 'any':\n            return undefined;\n        default:\n            throw new SchemaError('Schema type or format not supported, requires explicit default value: ' + type);\n    }\n}\n////////////////////////////////////////////////////////////////////////////\nexport function defaultTypes(schema, path = []) {\n    return _defaultTypes(schema, false, path);\n}\nfunction _defaultTypes(schema, isOptional, path) {\n    if (!schema) {\n        throw new SchemaError('Schema was undefined', path);\n    }\n    const info = schemaInfo(schema, isOptional, path);\n    const output = {\n        __types: info.types\n    };\n    //if (schema.type == 'object') console.log('--- OBJECT ---'); //debug\n    //else console.dir({ path, info }, { depth: 10 }); //debug\n    // schema.items cannot be an array according to\n    // https://www.learnjsonschema.com/2020-12/applicator/items/\n    if (info.schema.items &&\n        typeof info.schema.items == 'object' &&\n        !Array.isArray(info.schema.items)) {\n        output.__items = _defaultTypes(info.schema.items, info.isOptional, path);\n    }\n    if (info.properties) {\n        for (const [key, value] of Object.entries(info.properties)) {\n            assertSchema(value, [...path, key]);\n            output[key] = _defaultTypes(info.properties[key], !info.required?.includes(key), [\n                ...path,\n                key\n            ]);\n        }\n    }\n    // Check if a Record type is used for additionalProperties\n    if (info.additionalProperties && info.types.includes('object')) {\n        const additionalInfo = schemaInfo(info.additionalProperties, info.isOptional, path);\n        if (additionalInfo.properties && additionalInfo.types.includes('object')) {\n            for (const [key] of Object.entries(additionalInfo.properties)) {\n                output[key] = _defaultTypes(additionalInfo.properties[key], !additionalInfo.required?.includes(key), [...path, key]);\n            }\n        }\n    }\n    if (info.isNullable && !output.__types.includes('null')) {\n        output.__types.push('null');\n    }\n    if (info.isOptional && !output.__types.includes('undefined')) {\n        output.__types.push('undefined');\n    }\n    return output;\n}\n", "import { pathExists, setPaths, traversePath, traversePaths } from './traversal.js';\nimport { mergePath } from './stringPath.js';\nimport { defaultTypes, defaultValue } from './jsonSchema/schemaDefaults.js';\nimport { clone } from './utils.js';\nimport { merge } from 'ts-deepmerge';\nimport { schemaInfo } from './jsonSchema/schemaInfo.js';\nexport class SuperFormError extends Error {\n    constructor(message) {\n        super(message);\n        Object.setPrototypeOf(this, SuperFormError.prototype);\n    }\n}\nexport class SchemaError extends SuperFormError {\n    path;\n    constructor(message, path) {\n        super((path && path.length ? `[${Array.isArray(path) ? path.join('.') : path}] ` : '') + message);\n        this.path = Array.isArray(path) ? path.join('.') : path;\n        Object.setPrototypeOf(this, SchemaError.prototype);\n    }\n}\nexport function mapErrors(errors, shape) {\n    //console.log('===', errors.length, 'errors', shape);\n    const output = {};\n    function addFormLevelError(error) {\n        if (!('_errors' in output))\n            output._errors = [];\n        if (!Array.isArray(output._errors)) {\n            if (typeof output._errors === 'string')\n                output._errors = [output._errors];\n            else\n                throw new SuperFormError('Form-level error was not an array.');\n        }\n        output._errors.push(error.message);\n    }\n    for (const error of errors) {\n        // Form-level error\n        if (!error.path || (error.path.length == 1 && !error.path[0])) {\n            addFormLevelError(error);\n            continue;\n        }\n        // Path must filter away number indices, since the object shape doesn't contain these.\n        // Except the last, since otherwise any error in an array will count as an object error.\n        const isLastIndexNumeric = /^\\d$/.test(String(error.path[error.path.length - 1]));\n        const objectError = !isLastIndexNumeric &&\n            pathExists(shape, error.path.filter((p) => /\\D/.test(String(p))))?.value;\n        //console.log(error.path, error.message, objectError ? '[OBJ]' : '');\n        const leaf = traversePath(output, error.path, ({ value, parent, key }) => {\n            if (value === undefined)\n                parent[key] = {};\n            return parent[key];\n        });\n        if (!leaf) {\n            addFormLevelError(error);\n            continue;\n        }\n        const { parent, key } = leaf;\n        if (objectError) {\n            if (!(key in parent))\n                parent[key] = {};\n            if (!('_errors' in parent[key]))\n                parent[key]._errors = [error.message];\n            else\n                parent[key]._errors.push(error.message);\n        }\n        else {\n            if (!(key in parent))\n                parent[key] = [error.message];\n            else\n                parent[key].push(error.message);\n        }\n    }\n    return output;\n}\n/**\n * Filter errors based on validation method.\n * auto = Requires the existence of errors and tainted (field in store) to show\n * oninput = Set directly\n */\nexport function updateErrors(New, Previous, force) {\n    if (force)\n        return New;\n    // Set previous errors to undefined,\n    // which signifies that an error can be displayed there again.\n    traversePaths(Previous, (errors) => {\n        if (!Array.isArray(errors.value))\n            return;\n        errors.set(undefined);\n    });\n    traversePaths(New, (error) => {\n        if (!Array.isArray(error.value) && error.value !== undefined)\n            return;\n        setPaths(Previous, [error.path], error.value);\n    });\n    return Previous;\n}\nexport function flattenErrors(errors) {\n    return _flattenErrors(errors, []);\n}\nfunction _flattenErrors(errors, path) {\n    const entries = Object.entries(errors);\n    return entries\n        .filter(([, value]) => value !== undefined)\n        .flatMap(([key, messages]) => {\n        if (Array.isArray(messages) && messages.length > 0) {\n            const currPath = path.concat([key]);\n            return { path: mergePath(currPath), messages };\n        }\n        else {\n            return _flattenErrors(errors[key], path.concat([key]));\n        }\n    });\n}\n/**\n * Merge defaults with parsed data.\n */\nexport function mergeDefaults(parsedData, defaults) {\n    if (!parsedData)\n        return clone(defaults);\n    return merge.withOptions({ mergeArrays: false }, defaults, parsedData);\n}\n/**\n * Merge defaults with (important!) *already validated and merged data*.\n * @DCI-context\n */\nexport function replaceInvalidDefaults(Data, Defaults, _schema, Errors, preprocessed) {\n    const defaultType = _schema.additionalProperties && typeof _schema.additionalProperties == 'object'\n        ? { __types: schemaInfo(_schema.additionalProperties, false, []).types }\n        : undefined; // Will throw if a field does not exist\n    ///// Roles ///////////////////////////////////////////////////////\n    //#region Types\n    const Types = defaultTypes(_schema);\n    function Types_correctValue(dataValue, defValue, type) {\n        const types = type.__types;\n        if (!types.length || types.every((t) => t == 'undefined' || t == 'null' || t == 'any')) {\n            // No types counts as an \"any\" type\n            return dataValue;\n        }\n        else if (types.length == 1 && types[0] == 'array' && !type.__items) {\n            /*\n            No type info for array exists.\n            Keep the value even though it may not be the correct type, but validation\n            won't fail and the failed data is usually returned to the form without UX problems.\n            */\n            return dataValue;\n        }\n        const dateTypes = ['unix-time', 'Date', 'date'];\n        for (const schemaType of types) {\n            const defaultTypeValue = defaultValue(schemaType, undefined);\n            const sameType = typeof dataValue === typeof defaultTypeValue ||\n                (dateTypes.includes(schemaType) && dataValue instanceof Date);\n            const sameExistance = sameType && (dataValue === null) === (defaultTypeValue === null);\n            if (sameType && sameExistance) {\n                return dataValue;\n            }\n            else if (type.__items) {\n                // Parse array type\n                return Types_correctValue(dataValue, defValue, type.__items);\n            }\n        }\n        // null takes preference over undefined\n        if (defValue === undefined && types.includes('null')) {\n            return null;\n        }\n        return defValue;\n    }\n    //#endregion\n    //#region Data\n    function Data_traverse() {\n        traversePaths(Defaults, Defaults_traverseAndReplace);\n        Errors_traverseAndReplace();\n        return Data;\n    }\n    function Data_setValue(currentPath, newValue) {\n        setPaths(Data, [currentPath], newValue);\n    }\n    //#endregion\n    //#region Errors\n    function Errors_traverseAndReplace() {\n        for (const error of Errors) {\n            if (!error.path)\n                continue;\n            Defaults_traverseAndReplace({\n                path: error.path,\n                value: pathExists(Defaults, error.path)?.value\n            }, true);\n        }\n    }\n    //#endregion\n    //#region Defaults\n    function Defaults_traverseAndReplace(defaultPath, traversingErrors = false) {\n        const currentPath = defaultPath.path;\n        if (!currentPath || !currentPath[0])\n            return;\n        if (typeof currentPath[0] === 'string' && preprocessed?.includes(currentPath[0]))\n            return;\n        const dataPath = pathExists(Data, currentPath);\n        //let newValue = defValue;\n        if ((!dataPath && defaultPath.value !== undefined) ||\n            (dataPath && dataPath.value === undefined)) {\n            Data_setValue(currentPath, defaultPath.value);\n        }\n        else if (dataPath) {\n            const defValue = defaultPath.value;\n            const dataValue = dataPath.value;\n            // Check for same JS type with an existing default value.\n            if (defValue !== undefined &&\n                typeof dataValue === typeof defValue &&\n                (dataValue === null) === (defValue === null)) {\n                return;\n            }\n            const typePath = currentPath.filter((p) => /\\D/.test(String(p)));\n            const pathTypes = traversePath(Types, typePath, (path) => {\n                //console.log(path.path, path.value); //debug\n                return path.value && '__items' in path.value ? path.value.__items : path.value;\n            });\n            if (!pathTypes) {\n                // Return if checking for errors, as there may be deep errors that doesn't exist in the defaults.\n                if (traversingErrors)\n                    return;\n                throw new SchemaError('No types found for defaults', currentPath);\n            }\n            const fieldType = pathTypes.value ?? defaultType;\n            if (fieldType) {\n                Data_setValue(currentPath, Types_correctValue(dataValue, defValue, fieldType));\n            }\n        }\n    }\n    //#endregion\n    {\n        return Data_traverse();\n    }\n}\n", "import { clone as justClone } from './justClone.js';\nimport { SchemaError } from './errors.js';\nexport function clone(data) {\n    return data && typeof data === 'object' ? justClone(data) : data;\n}\nexport function assertSchema(schema, path) {\n    if (typeof schema === 'boolean') {\n        throw new SchemaError('Schema property cannot be defined as boolean.', path);\n    }\n}\n/**\n * Casts a Svelte store of a Record<string, unknown> type to a merged type of its unions.\n * @param store A Svelte store of a Record<string, unknown> type\n * @returns The same store but casted to a merged type of its unions.\n */\nexport function mergeFormUnion(store) {\n    return store;\n}\n", "import { SchemaError } from '../errors.js';\nimport { schemaInfo } from './schemaInfo.js';\nimport { assertSchema } from '../utils.js';\nexport function schemaShape(schema, path = []) {\n    const output = _schemaShape(schema, path);\n    if (!output)\n        throw new SchemaError('No shape could be created for schema.', path);\n    return output;\n}\nfunction _schemaShape(schema, path) {\n    assertSchema(schema, path);\n    const info = schemaInfo(schema, false, path);\n    if (info.array || info.union) {\n        const arr = info.array || [];\n        const union = info.union || [];\n        return arr.concat(union).reduce((shape, next) => {\n            const nextShape = _schemaShape(next, path);\n            if (nextShape)\n                shape = { ...(shape ?? {}), ...nextShape };\n            return shape;\n        }, arr.length ? {} : undefined);\n    }\n    if (info.properties) {\n        const output = {};\n        for (const [key, prop] of Object.entries(info.properties)) {\n            const shape = _schemaShape(prop, [...path, key]);\n            if (shape)\n                output[key] = shape;\n        }\n        return output;\n    }\n    return info.types.includes('array') || info.types.includes('object') ? {} : undefined;\n}\nexport function shapeFromObject(obj) {\n    let output = {};\n    const isArray = Array.isArray(obj);\n    for (const [key, value] of Object.entries(obj)) {\n        if (!value || typeof value !== 'object')\n            continue;\n        if (isArray)\n            output = { ...output, ...shapeFromObject(value) };\n        else\n            output[key] = shapeFromObject(value);\n    }\n    return output;\n}\n", "import { schemaInfo } from './schemaInfo.js';\nimport { merge as deepMerge } from 'ts-deepmerge';\nexport function constraints(schema) {\n    return _constraints(schemaInfo(schema, false, []), []);\n}\nfunction merge(...constraints) {\n    const filtered = constraints.filter((c) => !!c);\n    if (!filtered.length)\n        return undefined;\n    if (filtered.length == 1)\n        return filtered[0];\n    return deepMerge(...filtered);\n}\nfunction _constraints(info, path) {\n    if (!info)\n        return undefined;\n    let output = undefined;\n    // Union\n    if (info.union && info.union.length) {\n        const infos = info.union.map((s) => schemaInfo(s, info.isOptional, path));\n        const merged = infos.map((i) => _constraints(i, path));\n        output = merge(output, ...merged);\n        // Delete required if any part of the union is optional\n        if (output &&\n            (info.isNullable || info.isOptional || infos.some((i) => i?.isNullable || i?.isOptional))) {\n            delete output.required;\n        }\n    }\n    // Arrays\n    if (info.array) {\n        output = merge(output, ...info.array.map((i) => _constraints(schemaInfo(i, info.isOptional, path), path)));\n    }\n    // Objects\n    if (info.properties) {\n        const obj = {};\n        for (const [key, prop] of Object.entries(info.properties)) {\n            const propInfo = schemaInfo(prop, !info.required?.includes(key) || prop.default !== undefined, [key]);\n            const propConstraint = _constraints(propInfo, [...path, key]);\n            if (typeof propConstraint === 'object' && Object.values(propConstraint).length > 0) {\n                obj[key] = propConstraint;\n            }\n        }\n        output = merge(output, obj);\n    }\n    return output ?? constraint(info);\n}\nfunction constraint(info) {\n    const output = {};\n    const schema = info.schema;\n    const type = schema.type;\n    const format = schema.format;\n    // Must be before type check\n    if (type == 'integer' &&\n        format == 'unix-time' //||\n    //format == 'date-time' ||\n    //format == 'date' ||\n    //format == 'time'\n    ) {\n        const date = schema;\n        if (date.minimum !== undefined)\n            output.min = new Date(date.minimum).toISOString();\n        if (date.maximum !== undefined)\n            output.max = new Date(date.maximum).toISOString();\n    }\n    else if (type == 'string') {\n        const str = schema;\n        const patterns = [\n            str.pattern,\n            ...(str.allOf ? str.allOf.map((s) => (typeof s == 'boolean' ? undefined : s.pattern)) : [])\n        ].filter((s) => s !== undefined);\n        if (patterns.length > 0)\n            output.pattern = patterns[0];\n        if (str.minLength !== undefined)\n            output.minlength = str.minLength;\n        if (str.maxLength !== undefined)\n            output.maxlength = str.maxLength;\n    }\n    else if (type == 'number' || type == 'integer') {\n        const num = schema;\n        if (num.minimum !== undefined)\n            output.min = num.minimum;\n        else if (num.exclusiveMinimum !== undefined)\n            output.min = num.exclusiveMinimum + (type == 'integer' ? 1 : Number.MIN_VALUE);\n        if (num.maximum !== undefined)\n            output.max = num.maximum;\n        else if (num.exclusiveMaximum !== undefined)\n            output.max = num.exclusiveMaximum - (type == 'integer' ? 1 : Number.MIN_VALUE);\n        if (num.multipleOf !== undefined)\n            output.step = num.multipleOf;\n    }\n    else if (type == 'array') {\n        const arr = schema;\n        if (arr.minItems !== undefined)\n            output.min = arr.minItems;\n        if (arr.maxItems !== undefined)\n            output.max = arr.maxItems;\n    }\n    if (!info.isNullable && !info.isOptional) {\n        output.required = true;\n    }\n    return Object.keys(output).length > 0 ? output : undefined;\n}\n", "import { schemaInfo } from './schemaInfo.js';\nexport function schemaHash(schema) {\n    return hashCode(_schemaHash(schemaInfo(schema, false, []), 0, []));\n}\nfunction _schemaHash(info, depth, path) {\n    if (!info)\n        return '';\n    function tab() {\n        return '  '.repeat(depth);\n    }\n    function mapSchemas(schemas) {\n        return schemas\n            .map((s) => _schemaHash(schemaInfo(s, info?.isOptional ?? false, path), depth + 1, path))\n            .filter((s) => s)\n            .join('|');\n    }\n    function nullish() {\n        const output = [];\n        if (info?.isNullable)\n            output.push('null');\n        if (info?.isOptional)\n            output.push('undefined');\n        return !output.length ? '' : '|' + output.join('|');\n    }\n    // Union\n    if (info.union) {\n        return 'Union {\\n  ' + tab() + mapSchemas(info.union) + '\\n' + tab() + '}' + nullish();\n    }\n    // Objects\n    if (info.properties) {\n        const output = [];\n        for (const [key, prop] of Object.entries(info.properties)) {\n            const propInfo = schemaInfo(prop, !info.required?.includes(key) || prop.default !== undefined, [key]);\n            output.push(key + ': ' + _schemaHash(propInfo, depth + 1, path));\n        }\n        return 'Object {\\n  ' + tab() + output.join(',\\n  ') + '\\n' + tab() + '}' + nullish();\n    }\n    // Arrays\n    if (info.array) {\n        return 'Array[' + mapSchemas(info.array) + ']' + nullish();\n    }\n    return info.types.join('|') + nullish();\n}\n// https://stackoverflow.com/a/8831937/70894\nfunction hashCode(str) {\n    let hash = 0;\n    for (let i = 0, len = str.length; i < len; i++) {\n        const chr = str.charCodeAt(i);\n        hash = (hash << 5) - hash + chr;\n        hash |= 0; // Convert to 32bit integer\n    }\n    // Make it unsigned, for the hash appearance\n    if (hash < 0)\n        hash = hash >>> 0;\n    return hash.toString(36);\n}\n", "/**\n * Simple JSON Schema generator for validation libraries without introspection.\n */\nexport function simpleSchema(value) {\n    if (value === null || value === undefined) {\n        return {};\n    }\n    switch (typeof value) {\n        case 'object': {\n            if (value instanceof Date) {\n                return { type: 'integer', format: 'unix-time' };\n            }\n            if (Array.isArray(value)) {\n                const output = { type: 'array' };\n                output.items = value.length ? simpleSchema(value[0]) : {};\n                return output;\n            }\n            else {\n                const obj = value;\n                return {\n                    type: 'object',\n                    properties: Object.fromEntries(Object.entries(obj).map(([key, value]) => [key, simpleSchema(value)])),\n                    required: Object.keys(obj).filter((key) => (!obj[key] && obj[key] !== undefined && obj[key] !== null) ||\n                        (Array.isArray(obj[key]) && !obj[key].length)),\n                    additionalProperties: false\n                };\n            }\n        }\n    }\n    return { type: typeof value };\n}\n", "import { constraints as schemaConstraints } from '../jsonSchema/constraints.js';\nimport { defaultValues } from '../jsonSchema/schemaDefaults.js';\nimport { schemaShape } from '../jsonSchema/schemaShape.js';\nimport { schemaHash } from '../jsonSchema/schemaHash.js';\nimport { SuperFormError } from '../errors.js';\nimport { simpleSchema } from './simple-schema/index.js';\n/**\n * If the adapter options doesn't have a \"defaults\" or \"jsonSchema\" fields,\n * this is a convenient function for creating a JSON schema.\n * If no transformer exist for the adapter, use RequiredDefaultsOptions.\n * @see {AdapterOptions}\n * @see {RequiredDefaultsOptions}\n * @__NO_SIDE_EFFECTS__\n */\nexport function createJsonSchema(options, transformer) {\n    return 'jsonSchema' in options && options.jsonSchema\n        ? options.jsonSchema\n        : !transformer && 'defaults' in options && options.defaults\n            ? simpleSchema(options.defaults)\n            : transformer\n                ? /* @__PURE__ */ transformer()\n                : () => {\n                    throw new SuperFormError('The \"defaults\" option is required for this adapter.');\n                };\n}\n/* @__NO_SIDE_EFFECTS__ */\nexport function createAdapter(adapter, jsonSchema) {\n    if (!adapter || !('superFormValidationLibrary' in adapter)) {\n        throw new SuperFormError('Superforms v2 requires a validation adapter for the schema. ' +\n            'Import one of your choice from \"sveltekit-superforms/adapters\" and wrap the schema with it.');\n    }\n    if (!jsonSchema)\n        jsonSchema = adapter.jsonSchema;\n    return {\n        ...adapter,\n        constraints: adapter.constraints ?? schemaConstraints(jsonSchema),\n        defaults: adapter.defaults ?? defaultValues(jsonSchema),\n        shape: schemaShape(jsonSchema),\n        id: schemaHash(jsonSchema)\n    };\n}\n"], "mappings": ";;;;;;;;AACO,SAAS,UAAU,MAAM;AAC5B,SAAO,KACF,SAAS,EACT,MAAM,SAAS,EACf,OAAO,CAAC,MAAM,CAAC;AACxB;AACO,SAAS,UAAU,MAAM;AAC5B,SAAO,KAAK,OAAO,CAAC,KAAK,SAAS;AAC9B,UAAM,MAAM,OAAO,IAAI;AACvB,QAAI,OAAO,SAAS,YAAY,QAAQ,KAAK,GAAG;AAC5C,aAAO,IAAI,GAAG;AAAA,aACT,CAAC;AACN,aAAO;AAAA;AAEP,aAAO,IAAI,GAAG;AAClB,WAAO;AAAA,EACX,GAAG,EAAE;AACT;;;ACNO,SAAS,MAAM,KAAK;AACvB,QAAM,OAAO,CAAC,EAAE,SAAS,KAAK,GAAG,EAAE,MAAM,GAAG,EAAE;AAC9C,MAAI,QAAQ,OAAO;AAEf,WAAO,IAAI,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,UAAU,MAAM,KAAK,CAAC,CAAC;AAAA,EACxD;AACA,MAAI,QAAQ,OAAO;AAEf,WAAO,IAAI,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA,EACrE;AACA,MAAI,QAAQ,QAAQ;AAEhB,WAAO,IAAI,KAAK,IAAI,QAAQ,CAAC;AAAA,EACjC;AACA,MAAI,QAAQ,UAAU;AAElB,WAAO,OAAO,IAAI,QAAQ,IAAI,KAAK;AAAA,EACvC;AACA,MAAI,QAAQ,WAAW,QAAQ,UAAU;AACrC,UAAM,SAAS,QAAQ,WAAW,OAAO,OAAO,OAAO,eAAe,GAAG,CAAC,IAAI,CAAC;AAC/E,eAAW,OAAO,KAAK;AACnB,aAAO,GAAG,IAAI,MAAM,IAAI,GAAG,CAAC;AAAA,IAChC;AACA,WAAO;AAAA,EACX;AAEA,SAAO;AACX;;;ACtCA,SAAS,QAAQ,QAAQ,KAAK,OAAO;AACjC,SAAO,GAAG,IAAI;AACd,SAAO;AACX;AACA,SAAS,cAAc,cAAc,UAAU;AAC3C,SAAQ,SAAS,UAAU,UACvB,OAAO,SAAS,UAAU,YAC1B,SAAS,KAAK,SAAS,aAAa;AAC5C;AACO,SAAS,WAAW,KAAK,MAAM,UAAU,CAAC,GAAG;AAChD,MAAI,CAAC,QAAQ,UAAU;AACnB,YAAQ,WAAW,CAAC,aAAc,cAAc,MAAM,QAAQ,IAAI,SAAY,SAAS;AAAA,EAC3F;AACA,QAAM,SAAS,aAAa,KAAK,MAAM,QAAQ,QAAQ;AACvD,MAAI,CAAC;AACD,WAAO;AACX,MAAI,QAAQ,UAAU;AAClB,WAAO;AACX,SAAO,QAAQ,MAAM,OAAO,KAAK,IAAI,SAAS;AAClD;AACO,SAAS,aAAa,KAAK,UAAU,UAAU;AAClD,MAAI,CAAC,SAAS;AACV,WAAO;AACX,QAAM,OAAO,CAAC,SAAS,CAAC,CAAC;AACzB,MAAI,SAAS;AACb,SAAO,UAAU,KAAK,SAAS,SAAS,QAAQ;AAC5C,UAAMA,OAAM,KAAK,KAAK,SAAS,CAAC;AAChC,UAAM,QAAQ,WACR,SAAS;AAAA,MACP;AAAA,MACA,KAAK,OAAOA,IAAG;AAAA,MACf,OAAO,OAAOA,IAAG;AAAA,MACjB,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;AAAA,MAC/B,QAAQ;AAAA,MACR,KAAK,CAAC,MAAM,QAAQ,QAAQA,MAAK,CAAC;AAAA,IACtC,CAAC,IACC,OAAOA,IAAG;AAChB,QAAI,UAAU;AACV,aAAO;AAAA;AAEP,eAAS;AACb,SAAK,KAAK,SAAS,KAAK,MAAM,CAAC;AAAA,EACnC;AACA,MAAI,CAAC;AACD,WAAO;AACX,QAAM,MAAM,SAAS,SAAS,SAAS,CAAC;AACxC,SAAO;AAAA,IACH;AAAA,IACA,KAAK,OAAO,GAAG;AAAA,IACf,OAAO,OAAO,GAAG;AAAA,IACjB,MAAM,SAAS,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;AAAA,IACnC,QAAQ;AAAA,IACR,KAAK,CAAC,MAAM,QAAQ,QAAQ,KAAK,CAAC;AAAA,EACtC;AACJ;AACO,SAAS,cAAc,QAAQ,UAAU,OAAO,CAAC,GAAG;AACvD,aAAW,OAAO,QAAQ;AACtB,UAAM,QAAQ,OAAO,GAAG;AACxB,UAAM,SAAS,UAAU,QAAQ,OAAO,UAAU;AAClD,UAAM,WAAW;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,KAAK,OAAO,CAAC,GAAG,CAAC;AAAA;AAAA,MACvB;AAAA,MACA,KAAK,CAAC,MAAM,QAAQ,QAAQ,KAAK,CAAC;AAAA,IACtC;AACA,UAAM,SAAS,SAAS,QAAQ;AAChC,QAAI,WAAW;AACX,aAAO;AAAA,aACF,WAAW;AAChB;AAAA,aACK,CAAC,QAAQ;AACd,YAAMC,UAAS,cAAc,OAAO,UAAU,SAAS,IAAI;AAC3D,UAAIA,YAAW;AACX,eAAOA;AAAA,IACf;AAAA,EACJ;AACJ;AAEA,SAAS,MAAM,IAAI,IAAI;AACnB,SAAO,OAAO,MAAO,GAAG,SAAS,GAAG,QAAQ,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;AAC9E;AAIO,SAAS,aAAa,QAAQ,QAAQ;AACzC,QAAM,YAAY,oBAAI,IAAI;AAC1B,WAAS,YAAY,KAAK,OAAO;AAC7B,QAAI,eAAe,QAAQ,iBAAiB,QAAQ,IAAI,QAAQ,MAAM,MAAM,QAAQ;AAChF,aAAO;AACX,QAAI,eAAe,OAAO,iBAAiB,OAAO,CAAC,MAAM,KAAK,KAAK;AAC/D,aAAO;AACX,QAAI,eAAe,QAAQ,iBAAiB,QAAQ,QAAQ;AACxD,aAAO;AACX,WAAO;AAAA,EACX;AACA,WAAS,UAAU,MAAM;AACrB,WAAO,gBAAgB,QAAQ,gBAAgB,OAAO,gBAAgB;AAAA,EAC1E;AACA,WAAS,UAAU,MAAM,WAAW;AAChC,UAAM,YAAY,YAAY,aAAa,WAAW,KAAK,IAAI,IAAI;AAEnE,aAAS,UAAU;AAEf,gBAAU,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,KAAK,IAAI;AAC5C,aAAO;AAAA,IACX;AACA,QAAI,UAAU,KAAK,KAAK,GAAG;AACvB,UAAI,CAAC,UAAU,uCAAW,KAAK,KAAK,YAAY,KAAK,OAAO,UAAU,KAAK,GAAG;AAC1E,eAAO,QAAQ;AAAA,MACnB;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ;AACb,UAAI,CAAC,aAAa,KAAK,UAAU,UAAU,OAAO;AAC9C,gBAAQ;AAAA,MACZ;AAAA,IACJ;AAAA,EACJ;AACA,gBAAc,QAAQ,CAAC,SAAS,UAAU,MAAM,MAAM,CAAC;AACvD,gBAAc,QAAQ,CAAC,SAAS,UAAU,MAAM,MAAM,CAAC;AAEvD,QAAM,SAAS,MAAM,KAAK,UAAU,OAAO,CAAC;AAC5C,SAAO,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM;AACzC,SAAO;AACX;AACO,SAAS,SAAS,KAAK,OAAO,OAAO;AACxC,QAAM,aAAa,OAAO,UAAU;AACpC,aAAW,QAAQ,OAAO;AACtB,UAAM,OAAO,aAAa,KAAK,MAAM,CAAC,EAAE,QAAQ,KAAK,OAAAC,OAAM,MAAM;AAC7D,UAAIA,WAAU,UAAa,OAAOA,WAAU,UAAU;AAGlD,eAAO,GAAG,IAAI,CAAC;AAAA,MACnB;AACA,aAAO,OAAO,GAAG;AAAA,IACrB,CAAC;AACD,QAAI;AACA,WAAK,OAAO,KAAK,GAAG,IAAI,aAAa,MAAM,MAAM,IAAI,IAAI;AAAA,EACjE;AACJ;;;AC3IA,IAAM,wBAAwB,CAAC,aAAa,UAAU,OAAO,UAAU,OAAO,OAAO;AAK9E,SAAS,WAAW,QAAQ,YAAY,MAAM;AAPrD;AAQI,eAAa,QAAQ,IAAI;AACzB,QAAM,QAAQ,YAAY,QAAQ,IAAI;AACtC,QAAM,QAAQ,OAAO,SAAS,MAAM,SAAS,OAAO,KAC7C,MAAM,QAAQ,OAAO,KAAK,IAAI,OAAO,QAAQ,CAAC,OAAO,KAAK,GAAG,OAAO,CAAC,MAAM,OAAO,MAAM,SAAS,IAClG;AACN,QAAM,uBAAuB,OAAO,wBAChC,OAAO,OAAO,yBAAyB,YACvC,MAAM,SAAS,QAAQ,IACrB,OAAO,YAAY,OAAO,QAAQ,OAAO,oBAAoB,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,OAAO,UAAU,SAAS,CAAC,IAChH;AACN,QAAM,aAAa,OAAO,cAAc,MAAM,SAAS,QAAQ,IACzD,OAAO,YAAY,OAAO,QAAQ,OAAO,UAAU,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,OAAO,UAAU,SAAS,CAAC,IACtG;AACN,QAAM,SAAQ,eAAU,MAAM,MAAhB,mBAAmB,OAAO,CAAC,MAAM,EAAE,SAAS,UAAU,EAAE,UAAU;AAChF,QAAM,SAAS;AAAA,IACX,OAAO,MAAM,OAAO,CAAC,MAAM,MAAM,MAAM;AAAA,IACvC;AAAA,IACA,YAAY,MAAM,SAAS,MAAM;AAAA,IACjC;AAAA,IACA,QAAO,+BAAO,UAAS,QAAQ;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,OAAO;AAAA,EACrB;AACA,MAAI,CAAC,OAAO,SAAS,CAAC,OAAO,MAAM,QAAQ;AACvC,WAAO;AAAA,EACX;AACA,SAAO;AAAA,IACH,GAAG,MAAM,YAAY,EAAE,yBAAyB,MAAM,GAAG,QAAQ,GAAG,OAAO,MAAM,IAAI,CAAC,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAAA,IACrH;AAAA,EACJ;AACJ;AACA,SAAS,YAAY,QAAQ,MAAM;AAC/B,eAAa,QAAQ,IAAI;AACzB,MAAI,QAAQ,OAAO,UAAU,OAAO,CAAC,MAAM,IAAI,CAAC;AAChD,MAAI,OAAO,MAAM;AACb,YAAQ,MAAM,QAAQ,OAAO,IAAI,IAAI,OAAO,OAAO,CAAC,OAAO,IAAI;AAAA,EACnE;AACA,MAAI,OAAO,OAAO;AACd,YAAQ,OAAO,MAAM,QAAQ,CAAC,MAAM,YAAY,GAAG,IAAI,CAAC;AAAA,EAC5D;AACA,MAAI,MAAM,SAAS,OAAO,KAAK,OAAO,aAAa;AAC/C,UAAM,IAAI,MAAM,UAAU,CAAC,MAAM,KAAK,OAAO;AAC7C,UAAM,CAAC,IAAI;AAAA,EACf,WACS,OAAO,UAAU,sBAAsB,SAAS,OAAO,MAAM,GAAG;AACrE,UAAM,QAAQ,OAAO,MAAM;AAG3B,QAAI,OAAO,UAAU,eAAe,OAAO,UAAU,SAAS;AAC1D,YAAM,IAAI,MAAM,UAAU,CAAC,MAAM,KAAK,SAAS;AAC/C,YAAM,OAAO,GAAG,CAAC;AAAA,IACrB;AAAA,EACJ;AACA,MAAI,OAAO,SAAS,OAAO,UAAU,QAAQ,OAAO,OAAO,UAAU,YAAY;AAC7E,UAAM,KAAK,OAAO,OAAO,KAAK;AAAA,EAClC;AACA,SAAO,MAAM,KAAK,IAAI,IAAI,KAAK,CAAC;AACpC;AACA,SAAS,UAAU,QAAQ;AACvB,MAAI,CAAC,OAAO,SAAS,CAAC,OAAO,MAAM;AAC/B,WAAO;AACX,SAAO,OAAO,MAAM,OAAO,CAAC,MAAM,OAAO,MAAM,SAAS;AAC5D;;;ACpEO,SAAS,cAAc,QAAQ,aAAa,OAAO,OAAO,CAAC,GAAG;AACjE,SAAO,eAAe,QAAQ,YAAY,IAAI;AAClD;AACA,SAAS,eAAe,QAAQ,YAAY,MAAM;AAPlD;AAQI,MAAI,CAAC,QAAQ;AACT,UAAM,IAAI,YAAY,wBAAwB,IAAI;AAAA,EACtD;AACA,QAAM,OAAO,WAAW,QAAQ,YAAY,IAAI;AAChD,MAAI,CAAC;AACD,WAAO;AAGX,MAAI,iBAAiB;AAErB,MAAI,aAAa,QAAQ;AAIrB,QAAI,KAAK,MAAM,SAAS,QAAQ,KAC5B,OAAO,WACP,OAAO,OAAO,WAAW,YACzB,CAAC,MAAM,QAAQ,OAAO,OAAO,GAAG;AAChC,uBAAiB,OAAO;AAAA,IAC5B,OACK;AACD,UAAI,KAAK,MAAM,SAAS,GAAG;AACvB,YAAI,KAAK,MAAM,SAAS,WAAW,MAC9B,KAAK,MAAM,SAAS,SAAS,KAAK,KAAK,MAAM,SAAS,QAAQ;AAC/D,gBAAM,IAAI,YAAY,0FAA0F,IAAI;AAAA,MAC5H;AACA,YAAM,CAAC,IAAI,IAAI,KAAK;AACpB,aAAO,mBAAmB,MAAM,OAAO,OAAO;AAAA,IAClD;AAAA,EACJ;AACA,MAAI;AACJ,QAAM,mBAAmB,MAAM;AAC3B,QAAI,CAAC,KAAK,SAAS,KAAK,MAAM,SAAS;AACnC,aAAO;AACX,QAAI,KAAK,MAAM,KAAK,CAAC,MAAM,EAAE,IAAI;AAC7B,aAAO;AACX,QAAI,CAAC,YAAY;AACb,mBAAa,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,MAAM;AACvC,eAAO,CAAC,WAAW,WAAW,EAAE,SAAS,CAAC,IAAI,WAAW;AAAA,MAC7D,CAAC,CAAC;AAAA,IACN;AACA,WAAO,WAAW,OAAO;AAAA,EAC7B;AACA,MAAI,SAAS;AAEb,MAAI,CAAC,kBAAkB,KAAK,OAAO;AAC/B,UAAM,gBAAgB,KAAK,MAAM,OAAO,CAAC,MAAM,OAAO,MAAM,aAAa,EAAE,YAAY,MAAS;AAChG,QAAI,cAAc,UAAU,GAAG;AAC3B,aAAO,eAAe,cAAc,CAAC,GAAG,YAAY,IAAI;AAAA,IAC5D,WACS,cAAc,SAAS,GAAG;AAC/B,YAAM,IAAI,YAAY,4FAA4F,IAAI;AAAA,IAC1H,OACK;AAED,UAAI,KAAK;AACL,eAAO;AACX,UAAI,KAAK;AACL,eAAO;AACX,UAAI,iBAAiB,GAAG;AACpB,cAAM,IAAI,YAAY,6FAA6F,IAAI;AAAA,MAC3H;AAEA,UAAI,KAAK,MAAM,UAAU,KAAK,MAAM,CAAC,KAAK,UAAU;AAChD,YAAI,WAAW;AACX,mBAAS,CAAC;AACd,iBACI,KAAK,MAAM,SAAS,IACd,MAAM,YAAY,EAAE,yBAAyB,KAAK,GAAG,GAAG,KAAK,MAAM,IAAI,CAAC,MAAM,eAAe,GAAG,YAAY,IAAI,CAAC,CAAC,IAClH,eAAe,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI;AAAA,MAC5D;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,CAAC,gBAAgB;AAEjB,QAAI,KAAK;AACL,aAAO;AACX,QAAI,KAAK;AACL,aAAO;AAAA,EACf;AAEA,MAAI,KAAK,YAAY;AACjB,eAAW,CAAC,KAAK,SAAS,KAAK,OAAO,QAAQ,KAAK,UAAU,GAAG;AAC5D,mBAAa,WAAW,CAAC,GAAG,MAAM,GAAG,CAAC;AACtC,YAAM,MAAM,kBAAkB,eAAe,GAAG,MAAM,SAChD,eAAe,GAAG,IAClB,eAAe,WAAW,GAAC,UAAK,aAAL,mBAAe,SAAS,OAAM,CAAC,GAAG,MAAM,GAAG,CAAC;AAE7E,UAAI,WAAW;AACX,iBAAS,CAAC;AACd,aAAO,GAAG,IAAI;AAAA,IAClB;AAAA,EACJ,WACS,gBAAgB;AACrB,WAAO;AAAA,EACX;AASA,MAAI,OAAO,MAAM;AACb,WAAO,OAAO,KAAK,CAAC;AAAA,EACxB;AAEA,MAAI,iBAAiB,GAAG;AACpB,UAAM,IAAI,YAAY,kDAAkD,IAAI;AAAA,EAChF,WACS,KAAK,MAAM,UAAU,GAAG;AAG7B,WAAO;AAAA,EACX;AACA,QAAM,CAAC,UAAU,IAAI,KAAK;AAC1B,SAAO,UAAU,aAAa,YAAY,OAAO,IAAI;AACzD;AACA,SAAS,mBAAmB,MAAM,OAAO;AACrC,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO,MAAM,QAAQ,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI;AAAA,IACnD,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU;AAC9C,eAAO,IAAI,KAAK,KAAK;AACzB;AAAA,IACJ,KAAK;AACD,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU;AAC9C,eAAO,OAAO,KAAK;AACvB;AAAA,IACJ,KAAK;AACD,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU;AAC9C,eAAO,OAAO,KAAK;AACvB;AAAA,EACR;AACA,SAAO;AACX;AACO,SAAS,aAAa,MAAM,UAAU;AACzC,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO,YAAY,SAAS,SAAS,IAAI,SAAS,CAAC,IAAI;AAAA,IAC3D,KAAK;AAAA,IACL,KAAK;AACD,aAAO,YAAY,SAAS,SAAS,IAAI,SAAS,CAAC,IAAI;AAAA,IAC3D,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO,CAAC;AAAA,IACZ,KAAK;AACD,aAAO,CAAC;AAAA,IACZ,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAED,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO,OAAO,CAAC;AAAA,IACnB,KAAK;AACD,aAAO,oBAAI,IAAI;AAAA,IACnB,KAAK;AACD,aAAO,OAAO;AAAA,IAClB,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,YAAY,2EAA2E,IAAI;AAAA,EAC7G;AACJ;AAEO,SAAS,aAAa,QAAQ,OAAO,CAAC,GAAG;AAC5C,SAAO,cAAc,QAAQ,OAAO,IAAI;AAC5C;AACA,SAAS,cAAc,QAAQ,YAAY,MAAM;AA1LjD;AA2LI,MAAI,CAAC,QAAQ;AACT,UAAM,IAAI,YAAY,wBAAwB,IAAI;AAAA,EACtD;AACA,QAAM,OAAO,WAAW,QAAQ,YAAY,IAAI;AAChD,QAAM,SAAS;AAAA,IACX,SAAS,KAAK;AAAA,EAClB;AAKA,MAAI,KAAK,OAAO,SACZ,OAAO,KAAK,OAAO,SAAS,YAC5B,CAAC,MAAM,QAAQ,KAAK,OAAO,KAAK,GAAG;AACnC,WAAO,UAAU,cAAc,KAAK,OAAO,OAAO,KAAK,YAAY,IAAI;AAAA,EAC3E;AACA,MAAI,KAAK,YAAY;AACjB,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,KAAK,UAAU,GAAG;AACxD,mBAAa,OAAO,CAAC,GAAG,MAAM,GAAG,CAAC;AAClC,aAAO,GAAG,IAAI,cAAc,KAAK,WAAW,GAAG,GAAG,GAAC,UAAK,aAAL,mBAAe,SAAS,OAAM;AAAA,QAC7E,GAAG;AAAA,QACH;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAEA,MAAI,KAAK,wBAAwB,KAAK,MAAM,SAAS,QAAQ,GAAG;AAC5D,UAAM,iBAAiB,WAAW,KAAK,sBAAsB,KAAK,YAAY,IAAI;AAClF,QAAI,eAAe,cAAc,eAAe,MAAM,SAAS,QAAQ,GAAG;AACtE,iBAAW,CAAC,GAAG,KAAK,OAAO,QAAQ,eAAe,UAAU,GAAG;AAC3D,eAAO,GAAG,IAAI,cAAc,eAAe,WAAW,GAAG,GAAG,GAAC,oBAAe,aAAf,mBAAyB,SAAS,OAAM,CAAC,GAAG,MAAM,GAAG,CAAC;AAAA,MACvH;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,KAAK,cAAc,CAAC,OAAO,QAAQ,SAAS,MAAM,GAAG;AACrD,WAAO,QAAQ,KAAK,MAAM;AAAA,EAC9B;AACA,MAAI,KAAK,cAAc,CAAC,OAAO,QAAQ,SAAS,WAAW,GAAG;AAC1D,WAAO,QAAQ,KAAK,WAAW;AAAA,EACnC;AACA,SAAO;AACX;;;AC9NO,IAAM,iBAAN,MAAM,wBAAuB,MAAM;AAAA,EACtC,YAAY,SAAS;AACjB,UAAM,OAAO;AACb,WAAO,eAAe,MAAM,gBAAe,SAAS;AAAA,EACxD;AACJ;AACO,IAAM,cAAN,MAAM,qBAAoB,eAAe;AAAA,EAE5C,YAAY,SAAS,MAAM;AACvB,WAAO,QAAQ,KAAK,SAAS,IAAI,MAAM,QAAQ,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI,OAAO,MAAM,OAAO;AAFpG;AAGI,SAAK,OAAO,MAAM,QAAQ,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI;AACnD,WAAO,eAAe,MAAM,aAAY,SAAS;AAAA,EACrD;AACJ;AACO,SAAS,UAAU,QAAQ,OAAO;AApBzC;AAsBI,QAAM,SAAS,CAAC;AAChB,WAAS,kBAAkB,OAAO;AAC9B,QAAI,EAAE,aAAa;AACf,aAAO,UAAU,CAAC;AACtB,QAAI,CAAC,MAAM,QAAQ,OAAO,OAAO,GAAG;AAChC,UAAI,OAAO,OAAO,YAAY;AAC1B,eAAO,UAAU,CAAC,OAAO,OAAO;AAAA;AAEhC,cAAM,IAAI,eAAe,oCAAoC;AAAA,IACrE;AACA,WAAO,QAAQ,KAAK,MAAM,OAAO;AAAA,EACrC;AACA,aAAW,SAAS,QAAQ;AAExB,QAAI,CAAC,MAAM,QAAS,MAAM,KAAK,UAAU,KAAK,CAAC,MAAM,KAAK,CAAC,GAAI;AAC3D,wBAAkB,KAAK;AACvB;AAAA,IACJ;AAGA,UAAM,qBAAqB,OAAO,KAAK,OAAO,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC;AAChF,UAAM,cAAc,CAAC,wBACjB,gBAAW,OAAO,MAAM,KAAK,OAAO,CAAC,MAAM,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,MAAhE,mBAAmE;AAEvE,UAAM,OAAO,aAAa,QAAQ,MAAM,MAAM,CAAC,EAAE,OAAO,QAAAC,SAAQ,KAAAC,KAAI,MAAM;AACtE,UAAI,UAAU;AACV,QAAAD,QAAOC,IAAG,IAAI,CAAC;AACnB,aAAOD,QAAOC,IAAG;AAAA,IACrB,CAAC;AACD,QAAI,CAAC,MAAM;AACP,wBAAkB,KAAK;AACvB;AAAA,IACJ;AACA,UAAM,EAAE,QAAQ,IAAI,IAAI;AACxB,QAAI,aAAa;AACb,UAAI,EAAE,OAAO;AACT,eAAO,GAAG,IAAI,CAAC;AACnB,UAAI,EAAE,aAAa,OAAO,GAAG;AACzB,eAAO,GAAG,EAAE,UAAU,CAAC,MAAM,OAAO;AAAA;AAEpC,eAAO,GAAG,EAAE,QAAQ,KAAK,MAAM,OAAO;AAAA,IAC9C,OACK;AACD,UAAI,EAAE,OAAO;AACT,eAAO,GAAG,IAAI,CAAC,MAAM,OAAO;AAAA;AAE5B,eAAO,GAAG,EAAE,KAAK,MAAM,OAAO;AAAA,IACtC;AAAA,EACJ;AACA,SAAO;AACX;AAMO,SAAS,aAAa,KAAK,UAAU,OAAO;AAC/C,MAAI;AACA,WAAO;AAGX,gBAAc,UAAU,CAAC,WAAW;AAChC,QAAI,CAAC,MAAM,QAAQ,OAAO,KAAK;AAC3B;AACJ,WAAO,IAAI,MAAS;AAAA,EACxB,CAAC;AACD,gBAAc,KAAK,CAAC,UAAU;AAC1B,QAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,KAAK,MAAM,UAAU;AAC/C;AACJ,aAAS,UAAU,CAAC,MAAM,IAAI,GAAG,MAAM,KAAK;AAAA,EAChD,CAAC;AACD,SAAO;AACX;AACO,SAAS,cAAc,QAAQ;AAClC,SAAO,eAAe,QAAQ,CAAC,CAAC;AACpC;AACA,SAAS,eAAe,QAAQ,MAAM;AAClC,QAAM,UAAU,OAAO,QAAQ,MAAM;AACrC,SAAO,QACF,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,UAAU,MAAS,EACzC,QAAQ,CAAC,CAAC,KAAK,QAAQ,MAAM;AAC9B,QAAI,MAAM,QAAQ,QAAQ,KAAK,SAAS,SAAS,GAAG;AAChD,YAAM,WAAW,KAAK,OAAO,CAAC,GAAG,CAAC;AAClC,aAAO,EAAE,MAAM,UAAU,QAAQ,GAAG,SAAS;AAAA,IACjD,OACK;AACD,aAAO,eAAe,OAAO,GAAG,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC;AAAA,IACzD;AAAA,EACJ,CAAC;AACL;AAIO,SAAS,cAAc,YAAY,UAAU;AAChD,MAAI,CAAC;AACD,WAAOC,OAAM,QAAQ;AACzB,SAAO,MAAM,YAAY,EAAE,aAAa,MAAM,GAAG,UAAU,UAAU;AACzE;AAKO,SAAS,uBAAuB,MAAM,UAAU,SAAS,QAAQ,cAAc;AAClF,QAAM,cAAc,QAAQ,wBAAwB,OAAO,QAAQ,wBAAwB,WACrF,EAAE,SAAS,WAAW,QAAQ,sBAAsB,OAAO,CAAC,CAAC,EAAE,MAAM,IACrE;AAGN,QAAM,QAAQ,aAAa,OAAO;AAClC,WAAS,mBAAmB,WAAW,UAAU,MAAM;AACnD,UAAM,QAAQ,KAAK;AACnB,QAAI,CAAC,MAAM,UAAU,MAAM,MAAM,CAAC,MAAM,KAAK,eAAe,KAAK,UAAU,KAAK,KAAK,GAAG;AAEpF,aAAO;AAAA,IACX,WACS,MAAM,UAAU,KAAK,MAAM,CAAC,KAAK,WAAW,CAAC,KAAK,SAAS;AAMhE,aAAO;AAAA,IACX;AACA,UAAM,YAAY,CAAC,aAAa,QAAQ,MAAM;AAC9C,eAAW,cAAc,OAAO;AAC5B,YAAM,mBAAmB,aAAa,YAAY,MAAS;AAC3D,YAAM,WAAW,OAAO,cAAc,OAAO,oBACxC,UAAU,SAAS,UAAU,KAAK,qBAAqB;AAC5D,YAAM,gBAAgB,YAAa,cAAc,UAAW,qBAAqB;AACjF,UAAI,YAAY,eAAe;AAC3B,eAAO;AAAA,MACX,WACS,KAAK,SAAS;AAEnB,eAAO,mBAAmB,WAAW,UAAU,KAAK,OAAO;AAAA,MAC/D;AAAA,IACJ;AAEA,QAAI,aAAa,UAAa,MAAM,SAAS,MAAM,GAAG;AAClD,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAGA,WAAS,gBAAgB;AACrB,kBAAc,UAAU,2BAA2B;AACnD,8BAA0B;AAC1B,WAAO;AAAA,EACX;AACA,WAAS,cAAc,aAAa,UAAU;AAC1C,aAAS,MAAM,CAAC,WAAW,GAAG,QAAQ;AAAA,EAC1C;AAGA,WAAS,4BAA4B;AAjLzC;AAkLQ,eAAW,SAAS,QAAQ;AACxB,UAAI,CAAC,MAAM;AACP;AACJ,kCAA4B;AAAA,QACxB,MAAM,MAAM;AAAA,QACZ,QAAO,gBAAW,UAAU,MAAM,IAAI,MAA/B,mBAAkC;AAAA,MAC7C,GAAG,IAAI;AAAA,IACX;AAAA,EACJ;AAGA,WAAS,4BAA4B,aAAa,mBAAmB,OAAO;AACxE,UAAM,cAAc,YAAY;AAChC,QAAI,CAAC,eAAe,CAAC,YAAY,CAAC;AAC9B;AACJ,QAAI,OAAO,YAAY,CAAC,MAAM,aAAY,6CAAc,SAAS,YAAY,CAAC;AAC1E;AACJ,UAAM,WAAW,WAAW,MAAM,WAAW;AAE7C,QAAK,CAAC,YAAY,YAAY,UAAU,UACnC,YAAY,SAAS,UAAU,QAAY;AAC5C,oBAAc,aAAa,YAAY,KAAK;AAAA,IAChD,WACS,UAAU;AACf,YAAM,WAAW,YAAY;AAC7B,YAAM,YAAY,SAAS;AAE3B,UAAI,aAAa,UACb,OAAO,cAAc,OAAO,YAC3B,cAAc,UAAW,aAAa,OAAO;AAC9C;AAAA,MACJ;AACA,YAAM,WAAW,YAAY,OAAO,CAAC,MAAM,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC;AAC/D,YAAM,YAAY,aAAa,OAAO,UAAU,CAAC,SAAS;AAEtD,eAAO,KAAK,SAAS,aAAa,KAAK,QAAQ,KAAK,MAAM,UAAU,KAAK;AAAA,MAC7E,CAAC;AACD,UAAI,CAAC,WAAW;AAEZ,YAAI;AACA;AACJ,cAAM,IAAI,YAAY,+BAA+B,WAAW;AAAA,MACpE;AACA,YAAM,YAAY,UAAU,SAAS;AACrC,UAAI,WAAW;AACX,sBAAc,aAAa,mBAAmB,WAAW,UAAU,SAAS,CAAC;AAAA,MACjF;AAAA,IACJ;AAAA,EACJ;AAEA;AACI,WAAO,cAAc;AAAA,EACzB;AACJ;;;ACrOO,SAASC,OAAM,MAAM;AACxB,SAAO,QAAQ,OAAO,SAAS,WAAW,MAAU,IAAI,IAAI;AAChE;AACO,SAAS,aAAa,QAAQ,MAAM;AACvC,MAAI,OAAO,WAAW,WAAW;AAC7B,UAAM,IAAI,YAAY,iDAAiD,IAAI;AAAA,EAC/E;AACJ;AAMO,SAAS,eAAe,OAAO;AAClC,SAAO;AACX;;;ACdO,SAAS,YAAY,QAAQ,OAAO,CAAC,GAAG;AAC3C,QAAM,SAAS,aAAa,QAAQ,IAAI;AACxC,MAAI,CAAC;AACD,UAAM,IAAI,YAAY,yCAAyC,IAAI;AACvE,SAAO;AACX;AACA,SAAS,aAAa,QAAQ,MAAM;AAChC,eAAa,QAAQ,IAAI;AACzB,QAAM,OAAO,WAAW,QAAQ,OAAO,IAAI;AAC3C,MAAI,KAAK,SAAS,KAAK,OAAO;AAC1B,UAAM,MAAM,KAAK,SAAS,CAAC;AAC3B,UAAM,QAAQ,KAAK,SAAS,CAAC;AAC7B,WAAO,IAAI,OAAO,KAAK,EAAE,OAAO,CAAC,OAAO,SAAS;AAC7C,YAAM,YAAY,aAAa,MAAM,IAAI;AACzC,UAAI;AACA,gBAAQ,EAAE,GAAI,SAAS,CAAC,GAAI,GAAG,UAAU;AAC7C,aAAO;AAAA,IACX,GAAG,IAAI,SAAS,CAAC,IAAI,MAAS;AAAA,EAClC;AACA,MAAI,KAAK,YAAY;AACjB,UAAM,SAAS,CAAC;AAChB,eAAW,CAAC,KAAK,IAAI,KAAK,OAAO,QAAQ,KAAK,UAAU,GAAG;AACvD,YAAM,QAAQ,aAAa,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC;AAC/C,UAAI;AACA,eAAO,GAAG,IAAI;AAAA,IACtB;AACA,WAAO;AAAA,EACX;AACA,SAAO,KAAK,MAAM,SAAS,OAAO,KAAK,KAAK,MAAM,SAAS,QAAQ,IAAI,CAAC,IAAI;AAChF;AACO,SAAS,gBAAgB,KAAK;AACjC,MAAI,SAAS,CAAC;AACd,QAAM,UAAU,MAAM,QAAQ,GAAG;AACjC,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,GAAG,GAAG;AAC5C,QAAI,CAAC,SAAS,OAAO,UAAU;AAC3B;AACJ,QAAI;AACA,eAAS,EAAE,GAAG,QAAQ,GAAG,gBAAgB,KAAK,EAAE;AAAA;AAEhD,aAAO,GAAG,IAAI,gBAAgB,KAAK;AAAA,EAC3C;AACA,SAAO;AACX;;;AC3CO,SAAS,YAAY,QAAQ;AAChC,SAAO,aAAa,WAAW,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;AACzD;AACA,SAASC,UAASC,cAAa;AAC3B,QAAM,WAAWA,aAAY,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9C,MAAI,CAAC,SAAS;AACV,WAAO;AACX,MAAI,SAAS,UAAU;AACnB,WAAO,SAAS,CAAC;AACrB,SAAO,MAAU,GAAG,QAAQ;AAChC;AACA,SAAS,aAAa,MAAM,MAAM;AAblC;AAcI,MAAI,CAAC;AACD,WAAO;AACX,MAAI,SAAS;AAEb,MAAI,KAAK,SAAS,KAAK,MAAM,QAAQ;AACjC,UAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,MAAM,WAAW,GAAG,KAAK,YAAY,IAAI,CAAC;AACxE,UAAM,SAAS,MAAM,IAAI,CAAC,MAAM,aAAa,GAAG,IAAI,CAAC;AACrD,aAASD,OAAM,QAAQ,GAAG,MAAM;AAEhC,QAAI,WACC,KAAK,cAAc,KAAK,cAAc,MAAM,KAAK,CAAC,OAAM,uBAAG,gBAAc,uBAAG,WAAU,IAAI;AAC3F,aAAO,OAAO;AAAA,IAClB;AAAA,EACJ;AAEA,MAAI,KAAK,OAAO;AACZ,aAASA,OAAM,QAAQ,GAAG,KAAK,MAAM,IAAI,CAAC,MAAM,aAAa,WAAW,GAAG,KAAK,YAAY,IAAI,GAAG,IAAI,CAAC,CAAC;AAAA,EAC7G;AAEA,MAAI,KAAK,YAAY;AACjB,UAAM,MAAM,CAAC;AACb,eAAW,CAAC,KAAK,IAAI,KAAK,OAAO,QAAQ,KAAK,UAAU,GAAG;AACvD,YAAM,WAAW,WAAW,MAAM,GAAC,UAAK,aAAL,mBAAe,SAAS,SAAQ,KAAK,YAAY,QAAW,CAAC,GAAG,CAAC;AACpG,YAAM,iBAAiB,aAAa,UAAU,CAAC,GAAG,MAAM,GAAG,CAAC;AAC5D,UAAI,OAAO,mBAAmB,YAAY,OAAO,OAAO,cAAc,EAAE,SAAS,GAAG;AAChF,YAAI,GAAG,IAAI;AAAA,MACf;AAAA,IACJ;AACA,aAASA,OAAM,QAAQ,GAAG;AAAA,EAC9B;AACA,SAAO,UAAU,WAAW,IAAI;AACpC;AACA,SAAS,WAAW,MAAM;AACtB,QAAM,SAAS,CAAC;AAChB,QAAM,SAAS,KAAK;AACpB,QAAM,OAAO,OAAO;AACpB,QAAM,SAAS,OAAO;AAEtB,MAAI,QAAQ,aACR,UAAU,aAIZ;AACE,UAAM,OAAO;AACb,QAAI,KAAK,YAAY;AACjB,aAAO,MAAM,IAAI,KAAK,KAAK,OAAO,EAAE,YAAY;AACpD,QAAI,KAAK,YAAY;AACjB,aAAO,MAAM,IAAI,KAAK,KAAK,OAAO,EAAE,YAAY;AAAA,EACxD,WACS,QAAQ,UAAU;AACvB,UAAM,MAAM;AACZ,UAAM,WAAW;AAAA,MACb,IAAI;AAAA,MACJ,GAAI,IAAI,QAAQ,IAAI,MAAM,IAAI,CAAC,MAAO,OAAO,KAAK,YAAY,SAAY,EAAE,OAAQ,IAAI,CAAC;AAAA,IAC7F,EAAE,OAAO,CAAC,MAAM,MAAM,MAAS;AAC/B,QAAI,SAAS,SAAS;AAClB,aAAO,UAAU,SAAS,CAAC;AAC/B,QAAI,IAAI,cAAc;AAClB,aAAO,YAAY,IAAI;AAC3B,QAAI,IAAI,cAAc;AAClB,aAAO,YAAY,IAAI;AAAA,EAC/B,WACS,QAAQ,YAAY,QAAQ,WAAW;AAC5C,UAAM,MAAM;AACZ,QAAI,IAAI,YAAY;AAChB,aAAO,MAAM,IAAI;AAAA,aACZ,IAAI,qBAAqB;AAC9B,aAAO,MAAM,IAAI,oBAAoB,QAAQ,YAAY,IAAI,OAAO;AACxE,QAAI,IAAI,YAAY;AAChB,aAAO,MAAM,IAAI;AAAA,aACZ,IAAI,qBAAqB;AAC9B,aAAO,MAAM,IAAI,oBAAoB,QAAQ,YAAY,IAAI,OAAO;AACxE,QAAI,IAAI,eAAe;AACnB,aAAO,OAAO,IAAI;AAAA,EAC1B,WACS,QAAQ,SAAS;AACtB,UAAM,MAAM;AACZ,QAAI,IAAI,aAAa;AACjB,aAAO,MAAM,IAAI;AACrB,QAAI,IAAI,aAAa;AACjB,aAAO,MAAM,IAAI;AAAA,EACzB;AACA,MAAI,CAAC,KAAK,cAAc,CAAC,KAAK,YAAY;AACtC,WAAO,WAAW;AAAA,EACtB;AACA,SAAO,OAAO,KAAK,MAAM,EAAE,SAAS,IAAI,SAAS;AACrD;;;ACpGO,SAAS,WAAW,QAAQ;AAC/B,SAAO,SAAS,YAAY,WAAW,QAAQ,OAAO,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACrE;AACA,SAAS,YAAY,MAAM,OAAO,MAAM;AAJxC;AAKI,MAAI,CAAC;AACD,WAAO;AACX,WAAS,MAAM;AACX,WAAO,KAAK,OAAO,KAAK;AAAA,EAC5B;AACA,WAAS,WAAW,SAAS;AACzB,WAAO,QACF,IAAI,CAAC,MAAM,YAAY,WAAW,IAAG,6BAAM,eAAc,OAAO,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC,EACvF,OAAO,CAAC,MAAM,CAAC,EACf,KAAK,GAAG;AAAA,EACjB;AACA,WAAS,UAAU;AACf,UAAM,SAAS,CAAC;AAChB,QAAI,6BAAM;AACN,aAAO,KAAK,MAAM;AACtB,QAAI,6BAAM;AACN,aAAO,KAAK,WAAW;AAC3B,WAAO,CAAC,OAAO,SAAS,KAAK,MAAM,OAAO,KAAK,GAAG;AAAA,EACtD;AAEA,MAAI,KAAK,OAAO;AACZ,WAAO,gBAAgB,IAAI,IAAI,WAAW,KAAK,KAAK,IAAI,OAAO,IAAI,IAAI,MAAM,QAAQ;AAAA,EACzF;AAEA,MAAI,KAAK,YAAY;AACjB,UAAM,SAAS,CAAC;AAChB,eAAW,CAAC,KAAK,IAAI,KAAK,OAAO,QAAQ,KAAK,UAAU,GAAG;AACvD,YAAM,WAAW,WAAW,MAAM,GAAC,UAAK,aAAL,mBAAe,SAAS,SAAQ,KAAK,YAAY,QAAW,CAAC,GAAG,CAAC;AACpG,aAAO,KAAK,MAAM,OAAO,YAAY,UAAU,QAAQ,GAAG,IAAI,CAAC;AAAA,IACnE;AACA,WAAO,iBAAiB,IAAI,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,IAAI,IAAI,MAAM,QAAQ;AAAA,EACxF;AAEA,MAAI,KAAK,OAAO;AACZ,WAAO,WAAW,WAAW,KAAK,KAAK,IAAI,MAAM,QAAQ;AAAA,EAC7D;AACA,SAAO,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ;AAC1C;AAEA,SAAS,SAAS,KAAK;AACnB,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC5C,UAAM,MAAM,IAAI,WAAW,CAAC;AAC5B,YAAQ,QAAQ,KAAK,OAAO;AAC5B,YAAQ;AAAA,EACZ;AAEA,MAAI,OAAO;AACP,WAAO,SAAS;AACpB,SAAO,KAAK,SAAS,EAAE;AAC3B;;;ACpDO,SAAS,aAAa,OAAO;AAChC,MAAI,UAAU,QAAQ,UAAU,QAAW;AACvC,WAAO,CAAC;AAAA,EACZ;AACA,UAAQ,OAAO,OAAO;AAAA,IAClB,KAAK,UAAU;AACX,UAAI,iBAAiB,MAAM;AACvB,eAAO,EAAE,MAAM,WAAW,QAAQ,YAAY;AAAA,MAClD;AACA,UAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,cAAM,SAAS,EAAE,MAAM,QAAQ;AAC/B,eAAO,QAAQ,MAAM,SAAS,aAAa,MAAM,CAAC,CAAC,IAAI,CAAC;AACxD,eAAO;AAAA,MACX,OACK;AACD,cAAM,MAAM;AACZ,eAAO;AAAA,UACH,MAAM;AAAA,UACN,YAAY,OAAO,YAAY,OAAO,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,KAAKE,MAAK,MAAM,CAAC,KAAK,aAAaA,MAAK,CAAC,CAAC,CAAC;AAAA,UACpG,UAAU,OAAO,KAAK,GAAG,EAAE,OAAO,CAAC,QAAS,CAAC,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM,UAAa,IAAI,GAAG,MAAM,QAC3F,MAAM,QAAQ,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,MAAO;AAAA,UACjD,sBAAsB;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,MAAM,OAAO,MAAM;AAChC;;;AChBO,SAAS,iBAAiB,SAAS,aAAa;AACnD,SAAO,gBAAgB,WAAW,QAAQ,aACpC,QAAQ,aACR,CAAC,eAAe,cAAc,WAAW,QAAQ,WAC7C,aAAa,QAAQ,QAAQ,IAC7B,cACoB,YAAY,IAC5B,MAAM;AACJ,UAAM,IAAI,eAAe,qDAAqD;AAAA,EAClF;AAChB;AAEO,SAAS,cAAc,SAAS,YAAY;AAC/C,MAAI,CAAC,WAAW,EAAE,gCAAgC,UAAU;AACxD,UAAM,IAAI,eAAe,yJACwE;AAAA,EACrG;AACA,MAAI,CAAC;AACD,iBAAa,QAAQ;AACzB,SAAO;AAAA,IACH,GAAG;AAAA,IACH,aAAa,QAAQ,eAAe,YAAkB,UAAU;AAAA,IAChE,UAAU,QAAQ,YAAY,cAAc,UAAU;AAAA,IACtD,OAAO,YAAY,UAAU;AAAA,IAC7B,IAAI,WAAW,UAAU;AAAA,EAC7B;AACJ;", "names": ["key", "status", "value", "parent", "key", "clone", "clone", "merge", "constraints", "value"]}