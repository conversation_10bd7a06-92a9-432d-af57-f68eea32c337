<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import { Button } from '$lib/components/ui/button/index.js';
  import { Input } from '$lib/components/ui/input/index.js';
  import { Label } from '$lib/components/ui/label/index.js';
  import { Loader2, RefreshCw, Download, Search, AlertCircle, Database } from 'lucide-svelte';
  import SEO from '$components/shared/SEO.svelte';
  import { FEATURES } from '$lib/models/features';
  import { toast } from 'svelte-sonner';
  import * as Chart from '$lib/components/ui/chart';
  import { BarChart } from 'layerchart';

  // State using Svelte 5 runes
  let usageData = $state([]);
  let summaryData = $state([]);
  let loading = $state(false); // Set to false for mock data
  let summaryLoading = $state(false); // Set to false for mock data
  let error = $state(null);
  let summaryError = $state(null);
  let tablesExist = $state(true); // Set to true for mock data
  let checkingTables = $state(false); // Set to false for mock data

  // Mock data for testing
  const mockUsageData = [
    {
      id: 'usage1',
      userId: 'user1',
      user: {
        id: 'user1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        plan: { name: 'Pro Plan' },
      },
      featureId: 'job_search',
      featureName: 'Job Search',
      limitId: 'job_searches_per_month',
      limitName: 'Job Searches Per Month',
      used: 45,
      limit: 100,
      remaining: 55,
      percentUsed: 45,
      period: '2023-11',
      updatedAt: new Date().toISOString(),
    },
    {
      id: 'usage2',
      userId: 'user2',
      user: {
        id: 'user2',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        plan: { name: 'Basic Plan' },
      },
      featureId: 'resume_scanner',
      featureName: 'Resume Scanner',
      limitId: 'resume_scans_per_month',
      limitName: 'Resume Scans Per Month',
      used: 18,
      limit: 20,
      remaining: 2,
      percentUsed: 90,
      period: '2023-11',
      updatedAt: new Date().toISOString(),
    },
    {
      id: 'usage3',
      userId: 'user3',
      user: {
        id: 'user3',
        firstName: 'Robert',
        lastName: 'Johnson',
        email: '<EMAIL>',
        plan: { name: 'Premium Plan' },
      },
      featureId: 'job_save',
      featureName: 'Job Save',
      limitId: 'saved_jobs',
      limitName: 'Saved Jobs',
      used: 75,
      limit: 200,
      remaining: 125,
      percentUsed: 37.5,
      period: '2023-11',
      updatedAt: new Date().toISOString(),
    },
    {
      id: 'usage4',
      userId: 'user4',
      user: {
        id: 'user4',
        firstName: 'Emily',
        lastName: 'Williams',
        email: '<EMAIL>',
        plan: { name: 'Free Plan' },
      },
      featureId: 'resume_builder',
      featureName: 'Resume Builder',
      limitId: 'resume_versions',
      limitName: 'Resume Versions',
      used: 3,
      limit: 3,
      remaining: 0,
      percentUsed: 100,
      period: '2023-11',
      updatedAt: new Date().toISOString(),
    },
    {
      id: 'usage5',
      userId: 'user5',
      user: {
        id: 'user5',
        firstName: 'Michael',
        lastName: 'Brown',
        email: '<EMAIL>',
        plan: { name: 'Pro Plan' },
      },
      featureId: 'application_tracker',
      featureName: 'Application Tracker',
      limitId: 'applications_per_month',
      limitName: 'Applications Per Month',
      used: 12,
      limit: 50,
      remaining: 38,
      percentUsed: 24,
      period: '2023-11',
      updatedAt: new Date().toISOString(),
    },
  ];

  const mockSummaryData = [
    {
      id: 'job_search',
      name: 'Job Search',
      totalUsed: 145,
      userCount: 28,
      periods: [{ period: '2023-11', used: 145 }],
    },
    {
      id: 'resume_scanner',
      name: 'Resume Scanner',
      totalUsed: 87,
      userCount: 15,
      periods: [{ period: '2023-11', used: 87 }],
    },
    {
      id: 'job_save',
      name: 'Job Save',
      totalUsed: 320,
      userCount: 42,
      periods: [{ period: '2023-11', used: 320 }],
    },
    {
      id: 'resume_builder',
      name: 'Resume Builder',
      totalUsed: 56,
      userCount: 22,
      periods: [{ period: '2023-11', used: 56 }],
    },
    {
      id: 'application_tracker',
      name: 'Application Tracker',
      totalUsed: 98,
      userCount: 18,
      periods: [{ period: '2023-11', used: 98 }],
    },
  ];

  // Filters
  let featureId = $state('');
  let limitId = $state('');
  let period = $state('');
  let userId = $state('');
  let planId = $state('');
  let page = $state(1);
  let limit = $state(50);
  let groupBy = $state('feature');

  // Pagination
  let pagination = $state({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
    hasMore: false,
  });

  // Derived state
  let selectedFeature = $derived(FEATURES.find((f) => f.id === featureId));
  let availableLimits = $derived(selectedFeature?.limits || []);

  // Initialize data on component mount
  $effect(() => {
    // Check if feature tables exist and load data
    checkFeatureTables();
  });

  // Check if feature tables exist
  async function checkFeatureTables() {
    checkingTables = true;
    error = null;
    summaryError = null;

    try {
      const response = await fetch('/api/admin/feature-usage/check');
      if (!response.ok) throw new Error('Failed to check feature tables');

      const data = await response.json();
      tablesExist = data.tablesExist;

      if (tablesExist) {
        // If tables exist, fetch the data in parallel
        await Promise.all([fetchUsageData(), fetchSummaryData()]);
      } else {
        const errorMsg =
          'Feature usage tables do not exist yet. Start using features to generate usage data.';
        error = errorMsg;
        summaryError = errorMsg;
        loading = false;
        summaryLoading = false;
      }
    } catch (err) {
      console.error('Error checking feature tables:', err);
      const errorMsg = err.message || 'Failed to check feature tables';
      error = errorMsg;
      summaryError = errorMsg;
      loading = false;
      summaryLoading = false;
    } finally {
      checkingTables = false;
    }
  }

  // Build query params for API requests
  function buildQueryParams() {
    const params = new URLSearchParams();
    if (featureId) params.append('featureId', featureId);
    if (limitId) params.append('limitId', limitId);
    if (period) params.append('period', period);
    if (userId) params.append('userId', userId);
    if (planId) params.append('planId', planId);
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    return params;
  }

  // Fetch usage data from the API
  async function fetchUsageData() {
    loading = true;
    error = null;

    try {
      // Build query params
      const params = buildQueryParams();

      // Fetch usage data from the API
      const response = await fetch(`/api/admin/feature-usage?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch usage data');
      }

      const data = await response.json();
      usageData = data;

      // Set up pagination
      pagination = {
        page,
        limit,
        total: data.length, // This should come from the API in a real implementation
        totalPages: Math.ceil(data.length / limit),
        hasMore: data.length > limit,
      };
    } catch (err) {
      console.error('Error fetching usage data:', err);
      error = err.message || 'Failed to fetch usage data';

      // Use mock data as fallback for development
      if (process.env.NODE_ENV !== 'production') {
        console.log('Using mock data as fallback');
        usageData = mockUsageData;
        pagination = {
          page: 1,
          limit: 50,
          total: mockUsageData.length,
          totalPages: Math.ceil(mockUsageData.length / 50),
          hasMore: mockUsageData.length > 50,
        };
      }
    } finally {
      loading = false;
    }
  }

  // Fetch summary data from the API
  async function fetchSummaryData() {
    summaryLoading = true;
    summaryError = null;

    try {
      // Build query params
      const params = new URLSearchParams();
      params.append('groupBy', groupBy);

      // Fetch summary data from the API
      const response = await fetch(`/api/admin/feature-usage/summary?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch summary data');
      }

      const data = await response.json();
      summaryData = data;
    } catch (err) {
      console.error('Error fetching summary data:', err);
      summaryError = err.message || 'Failed to fetch summary data';

      // Use mock data as fallback for development
      if (process.env.NODE_ENV !== 'production') {
        console.log('Using mock summary data as fallback');
        summaryData = mockSummaryData;
      }
    } finally {
      summaryLoading = false;
    }
  }

  // Apply filters and refresh data
  function applyFilters() {
    page = 1;
    // Fetch data with the new filters
    fetchUsageData();
    fetchSummaryData();
  }

  // Reset all filters to default values
  function resetFilters() {
    featureId = '';
    limitId = '';
    period = '';
    userId = '';
    planId = '';
    page = 1;
    applyFilters();
  }

  // Export data in specified format
  function exportData(format = 'csv') {
    if (!tablesExist) {
      toast.error('Feature usage tables do not exist yet. Cannot export data.');
      return;
    }

    const params = buildQueryParams();
    params.append('format', format);
    window.open(`/api/admin/feature-usage/export?${params.toString()}`, '_blank');
  }

  // Format date for display
  function formatDate(dateString: string) {
    const date = new Date(dateString);
    return date.toLocaleString();
  }

  // Format period for display
  function formatPeriod(period: string) {
    if (!period) return 'N/A';

    const [year, month] = period.split('-');
    if (month) {
      const date = new Date(parseInt(year), parseInt(month) - 1, 1);
      return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    }

    return year;
  }

  // Get user display name
  function getUserName(user: { firstName?: string; lastName?: string; email?: string }) {
    if (user.firstName || user.lastName) {
      return `${user.firstName || ''} ${user.lastName || ''}`.trim();
    }
    return user.email;
  }

  // Pagination controls
  function goToPage(newPage: number) {
    page = newPage;
    fetchUsageData();
  }

  function nextPage() {
    if (page < pagination.totalPages) {
      page += 1;
      fetchUsageData();
    }
  }

  function prevPage() {
    if (page > 1) {
      page -= 1;
      fetchUsageData();
    }
  }

  // Chart configuration
  const chartConfig = {
    totalUsed: {
      label: 'Total Usage',
      color: 'var(--chart-1)',
    },
    userCount: {
      label: 'User Count',
      color: 'var(--chart-2)',
    },
  } satisfies Chart.ChartConfig;

  // Transform summary data for charts
  const usageChartData = $derived(() => {
    return summaryData.map((item) => ({
      name: item.name,
      totalUsed: item.totalUsed,
    }));
  });

  const userChartData = $derived(() => {
    return summaryData.map((item) => ({
      name: item.name,
      userCount: item.userCount,
    }));
  });

  // Initialize feature data
  async function initializeFeatureData() {
    try {
      toast.loading('Initializing feature data...');

      const response = await fetch('/api/admin/initialize-features', {
        method: 'POST',
      });

      const result = await response.json();

      if (result.success) {
        toast.dismiss();
        toast.success('Feature data initialized successfully!');
        console.log('Feature initialization results:', result.results);

        // Check feature tables again to refresh the UI
        await checkFeatureTables();
      } else {
        toast.dismiss();
        toast.error(`Failed to initialize feature data: ${result.error}`);
      }
    } catch (error) {
      console.error('Error initializing feature data:', error);
      toast.dismiss();
      toast.error(`Error initializing feature data: ${error.message}`);
    }
  }
</script>

<SEO title="Feature Usage Admin" />

<div class="border-border flex flex-col gap-1 border-b p-4">
  <div class="flex items-center justify-between">
    <div class="flex flex-col">
      <h2 class="text-lg font-semibold">Feature Usage Admin</h2>
      <p class="text-foreground/70">Monitor and analyze feature usage across all users.</p>
    </div>
    <Button variant="outline" size="sm" onclick={initializeFeatureData}>
      <Database class="mr-2 h-4 w-4" />
      Initialize Feature Data
    </Button>
  </div>
</div>

{#if checkingTables}
  <div class="flex justify-center py-16">
    <Loader2 class="text-primary h-12 w-12 animate-spin" />
  </div>
{:else if !tablesExist}
  <Card.Root>
    <Card.Header>
      <Card.Title>Feature Usage Not Available</Card.Title>
      <Card.Description>Feature usage tracking is not set up yet.</Card.Description>
    </Card.Header>
    <Card.Content>
      <div class="flex flex-col items-center justify-center py-8 text-center">
        <AlertCircle class="text-muted-foreground mb-4 h-12 w-12" />
        <h3 class="mb-2 text-xl font-semibold">No Feature Usage Data</h3>
        <p class="text-muted-foreground mb-6 max-w-md">
          Feature usage tables have not been created yet. You can initialize feature data to start
          tracking usage.
        </p>
        <div class="flex flex-col gap-2 sm:flex-row">
          <Button variant="default" onclick={initializeFeatureData}>
            <Database class="mr-2 h-4 w-4" />
            Initialize Feature Data
          </Button>
          <Button variant="outline" onclick={checkFeatureTables}>
            <RefreshCw class="mr-2 h-4 w-4" />
            Check Again
          </Button>
        </div>
      </div>
    </Card.Content>
  </Card.Root>
{:else}
  <div class="space-y-8">
    <!-- Filters -->
    <Card.Root>
      <Card.Header>
        <Card.Title>Filters</Card.Title>
        <Card.Description>Filter feature usage data by various criteria.</Card.Description>
      </Card.Header>
      <Card.Content>
        <div class="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <!-- Feature Selection -->
          <div class="space-y-2">
            <Label for="feature">Feature</Label>
            <div class="relative">
              <select
                id="feature"
                bind:value={featureId}
                class="border-input bg-background w-full appearance-none rounded-md border px-3 py-2 pr-8 text-sm">
                <option value="">All Features</option>
                {#each FEATURES as feature}
                  <option value={feature.id}>{feature.name}</option>
                {/each}
              </select>
              <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2">
                <svg
                  class="h-4 w-4 opacity-50"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 9l4 4 4-4" />
                </svg>
              </div>
            </div>
          </div>

          <!-- Limit Selection -->
          <div class="space-y-2">
            <Label for="limit">Limit</Label>
            <div class="relative">
              <select
                id="limit"
                bind:value={limitId}
                disabled={!featureId}
                class="border-input bg-background w-full appearance-none rounded-md border px-3 py-2 pr-8 text-sm">
                <option value="">All Limits</option>
                {#each availableLimits as limit}
                  <option value={limit.id}>{limit.name}</option>
                {/each}
              </select>
              <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2">
                <svg
                  class="h-4 w-4 opacity-50"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 9l4 4 4-4" />
                </svg>
              </div>
            </div>
          </div>

          <!-- Period Selection -->
          <div class="space-y-2">
            <Label for="period">Period</Label>
            <Input id="period" type="month" bind:value={period} placeholder="YYYY-MM" />
          </div>

          <!-- User ID Input -->
          <div class="space-y-2">
            <Label for="userId">User ID</Label>
            <Input id="userId" bind:value={userId} placeholder="User ID" />
          </div>
        </div>

        <div class="mt-4 flex justify-end space-x-2">
          <Button variant="outline" onclick={resetFilters}>Reset</Button>
          <Button onclick={applyFilters}>
            <Search class="mr-2 h-4 w-4" />
            Apply Filters
          </Button>
        </div>
      </Card.Content>
    </Card.Root>

    <!-- Summary Charts -->
    <Card.Root>
      <Card.Header>
        <div class="flex items-center justify-between">
          <div>
            <Card.Title>Usage Summary</Card.Title>
            <Card.Description>Overview of feature usage across all users.</Card.Description>
          </div>
          <div class="flex items-center space-x-2">
            <div class="relative w-[140px]">
              <select
                bind:value={groupBy}
                onchange={() => fetchSummaryData()}
                class="border-input bg-background w-full appearance-none rounded-md border px-3 py-2 pr-8 text-sm">
                <option value="feature">By Feature</option>
                <option value="limit">By Limit</option>
              </select>
              <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2">
                <svg
                  class="h-4 w-4 opacity-50"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 9l4 4 4-4" />
                </svg>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onclick={fetchSummaryData}
              disabled={summaryLoading}>
              <RefreshCw class={summaryLoading ? 'h-4 w-4 animate-spin' : 'h-4 w-4'} />
              <span class="sr-only">Refresh</span>
            </Button>
          </div>
        </div>
      </Card.Header>
      <Card.Content>
        {#if summaryLoading}
          <div class="flex justify-center py-8">
            <Loader2 class="text-primary h-8 w-8 animate-spin" />
          </div>
        {:else if summaryError}
          <div class="bg-destructive/10 text-destructive rounded-md p-4 text-sm">
            <p>Error loading summary data: {summaryError}</p>
          </div>
        {:else if summaryData.length === 0}
          <div class="rounded-md border border-dashed p-8 text-center">
            <p class="text-muted-foreground">No usage data available.</p>
          </div>
        {:else}
          <div class="grid gap-6 md:grid-cols-2">
            <!-- Total Usage Chart -->
            <div class="rounded-md border p-4">
              <h3 class="mb-4 text-lg font-medium">
                Total Usage by {groupBy === 'feature' ? 'Feature' : 'Limit'}
              </h3>
              {#if usageChartData().length === 0}
                <div class="flex h-[300px] items-center justify-center">
                  <div class="text-muted-foreground text-sm">No data available</div>
                </div>
              {:else}
                <Chart.Container config={chartConfig} class="h-[300px] w-full">
                  <BarChart
                    data={usageChartData()}
                    x="name"
                    axis="x"
                    series={[
                      {
                        key: 'totalUsed',
                        label: chartConfig.totalUsed.label,
                        color: chartConfig.totalUsed.color,
                      },
                    ]}
                    props={{
                      xAxis: {
                        format: (d: string) => d.slice(0, 10),
                      },
                    }}>
                    {#snippet tooltip()}
                      <Chart.Tooltip />
                    {/snippet}
                  </BarChart>
                </Chart.Container>
              {/if}
            </div>

            <!-- User Count Chart -->
            <div class="rounded-md border p-4">
              <h3 class="mb-4 text-lg font-medium">
                User Count by {groupBy === 'feature' ? 'Feature' : 'Limit'}
              </h3>
              {#if userChartData().length === 0}
                <div class="flex h-[300px] items-center justify-center">
                  <div class="text-muted-foreground text-sm">No data available</div>
                </div>
              {:else}
                <Chart.Container config={chartConfig} class="h-[300px] w-full">
                  <BarChart
                    data={userChartData()}
                    x="name"
                    axis="x"
                    series={[
                      {
                        key: 'userCount',
                        label: chartConfig.userCount.label,
                        color: chartConfig.userCount.color,
                      },
                    ]}
                    props={{
                      xAxis: {
                        format: (d: string) => d.slice(0, 10),
                      },
                    }}>
                    {#snippet tooltip()}
                      <Chart.Tooltip />
                    {/snippet}
                  </BarChart>
                </Chart.Container>
              {/if}
            </div>
          </div>
        {/if}
      </Card.Content>
    </Card.Root>

    <!-- Usage Data Table -->
    <Card.Root>
      <Card.Header>
        <div class="flex items-center justify-between">
          <div>
            <Card.Title>Usage Data</Card.Title>
            <Card.Description>Detailed feature usage data for all users.</Card.Description>
          </div>
          <div class="flex flex-wrap items-center gap-2">
            <Button variant="outline" size="sm" onclick={() => exportData('csv')}>
              <Download class="mr-2 h-4 w-4" />
              Export CSV
            </Button>
            <Button variant="outline" size="sm" onclick={() => exportData('json')}>
              <Download class="mr-2 h-4 w-4" />
              Export JSON
            </Button>
            <Button variant="outline" size="sm" onclick={fetchUsageData} disabled={loading}>
              <RefreshCw class={loading ? 'h-4 w-4 animate-spin' : 'h-4 w-4'} />
              <span class="sr-only">Refresh</span>
            </Button>
          </div>
        </div>
      </Card.Header>
      <Card.Content>
        {#if loading}
          <div class="flex justify-center py-8">
            <Loader2 class="text-primary h-8 w-8 animate-spin" />
          </div>
        {:else if error}
          <div class="bg-destructive/10 text-destructive rounded-md p-4 text-sm">
            <p>Error loading usage data: {error}</p>
          </div>
        {:else if usageData.length === 0}
          <div class="rounded-md border border-dashed p-8 text-center">
            <p class="text-muted-foreground">No usage data available.</p>
          </div>
        {:else}
          <div class="overflow-x-auto">
            <table class="w-full border-collapse">
              <thead>
                <tr class="border-b">
                  <th class="px-4 py-2 text-left">User</th>
                  <th class="px-4 py-2 text-left">Feature</th>
                  <th class="px-4 py-2 text-left">Limit</th>
                  <th class="px-4 py-2 text-right">Used</th>
                  <th class="px-4 py-2 text-left">Period</th>
                  <th class="px-4 py-2 text-left">Last Updated</th>
                </tr>
              </thead>
              <tbody>
                {#each usageData as usage}
                  <tr class="hover:bg-muted/50 border-b">
                    <td class="px-4 py-2">
                      <div class="flex flex-col">
                        <span class="font-medium">{getUserName(usage.user)}</span>
                        <span class="text-muted-foreground text-xs">{usage.user.email}</span>
                        <span class="text-muted-foreground text-xs">
                          Plan: {usage.user.plan?.name || 'No Plan'}
                        </span>
                      </div>
                    </td>
                    <td class="px-4 py-2">{usage.featureName}</td>
                    <td class="px-4 py-2">{usage.limitName}</td>
                    <td class="px-4 py-2 text-right font-medium">{usage.used}</td>
                    <td class="px-4 py-2">{formatPeriod(usage.period)}</td>
                    <td class="px-4 py-2">{formatDate(usage.updatedAt)}</td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          {#if pagination.totalPages > 0}
            <div class="mt-4 flex flex-wrap items-center justify-between gap-4">
              <div class="text-muted-foreground text-sm">
                Showing {(pagination.page - 1) * pagination.limit + 1} to {Math.min(
                  pagination.page * pagination.limit,
                  pagination.total
                )} of {pagination.total} entries
              </div>
              <div class="flex flex-wrap items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onclick={prevPage}
                  disabled={pagination.page === 1}>
                  Previous
                </Button>

                {#each Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                  const pageNum = pagination.page <= 3 ? i + 1 : pagination.page >= pagination.totalPages - 2 ? pagination.totalPages - 4 + i : pagination.page - 2 + i;

                  return pageNum <= pagination.totalPages ? pageNum : null;
                }).filter(Boolean) as pageNum}
                  <Button
                    variant={pageNum === pagination.page ? 'default' : 'outline'}
                    size="sm"
                    onclick={() => goToPage(pageNum)}>
                    {pageNum}
                  </Button>
                {/each}

                <Button
                  variant="outline"
                  size="sm"
                  onclick={nextPage}
                  disabled={pagination.page === pagination.totalPages}>
                  Next
                </Button>
              </div>
            </div>
          {/if}
        {/if}
      </Card.Content>
    </Card.Root>
  </div>
{/if}
