{"version": 3, "sources": ["../../d3-geo/src/math.js", "../../d3-geo/src/noop.js", "../../d3-geo/src/stream.js", "../../d3-geo/src/area.js", "../../d3-geo/src/cartesian.js", "../../d3-geo/src/bounds.js", "../../d3-geo/src/centroid.js", "../../d3-geo/src/constant.js", "../../d3-geo/src/compose.js", "../../d3-geo/src/rotation.js", "../../d3-geo/src/circle.js", "../../d3-geo/src/clip/buffer.js", "../../d3-geo/src/pointEqual.js", "../../d3-geo/src/clip/rejoin.js", "../../d3-geo/src/polygonContains.js", "../../d3-geo/src/clip/index.js", "../../d3-geo/src/clip/antimeridian.js", "../../d3-geo/src/clip/circle.js", "../../d3-geo/src/clip/line.js", "../../d3-geo/src/clip/rectangle.js", "../../d3-geo/src/clip/extent.js", "../../d3-geo/src/length.js", "../../d3-geo/src/distance.js", "../../d3-geo/src/contains.js", "../../d3-geo/src/graticule.js", "../../d3-geo/src/interpolate.js", "../../d3-geo/src/identity.js", "../../d3-geo/src/path/area.js", "../../d3-geo/src/path/bounds.js", "../../d3-geo/src/path/centroid.js", "../../d3-geo/src/path/context.js", "../../d3-geo/src/path/measure.js", "../../d3-geo/src/path/string.js", "../../d3-geo/src/path/index.js", "../../d3-geo/src/transform.js", "../../d3-geo/src/projection/fit.js", "../../d3-geo/src/projection/resample.js", "../../d3-geo/src/projection/index.js", "../../d3-geo/src/projection/conic.js", "../../d3-geo/src/projection/cylindricalEqualArea.js", "../../d3-geo/src/projection/conicEqualArea.js", "../../d3-geo/src/projection/albers.js", "../../d3-geo/src/projection/albersUsa.js", "../../d3-geo/src/projection/azimuthal.js", "../../d3-geo/src/projection/azimuthalEqualArea.js", "../../d3-geo/src/projection/azimuthalEquidistant.js", "../../d3-geo/src/projection/mercator.js", "../../d3-geo/src/projection/conicConformal.js", "../../d3-geo/src/projection/equirectangular.js", "../../d3-geo/src/projection/conicEquidistant.js", "../../d3-geo/src/projection/equalEarth.js", "../../d3-geo/src/projection/gnomonic.js", "../../d3-geo/src/projection/identity.js", "../../d3-geo/src/projection/naturalEarth1.js", "../../d3-geo/src/projection/orthographic.js", "../../d3-geo/src/projection/stereographic.js", "../../d3-geo/src/projection/transverseMercator.js"], "sourcesContent": ["export var epsilon = 1e-6;\nexport var epsilon2 = 1e-12;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var quarterPi = pi / 4;\nexport var tau = pi * 2;\n\nexport var degrees = 180 / pi;\nexport var radians = pi / 180;\n\nexport var abs = Math.abs;\nexport var atan = Math.atan;\nexport var atan2 = Math.atan2;\nexport var cos = Math.cos;\nexport var ceil = Math.ceil;\nexport var exp = Math.exp;\nexport var floor = Math.floor;\nexport var hypot = Math.hypot;\nexport var log = Math.log;\nexport var pow = Math.pow;\nexport var sin = Math.sin;\nexport var sign = Math.sign || function(x) { return x > 0 ? 1 : x < 0 ? -1 : 0; };\nexport var sqrt = Math.sqrt;\nexport var tan = Math.tan;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);\n}\n\nexport function haversin(x) {\n  return (x = sin(x / 2)) * x;\n}\n", "export default function noop() {}\n", "function streamGeometry(geometry, stream) {\n  if (geometry && streamGeometryType.hasOwnProperty(geometry.type)) {\n    streamGeometryType[geometry.type](geometry, stream);\n  }\n}\n\nvar streamObjectType = {\n  Feature: function(object, stream) {\n    streamGeometry(object.geometry, stream);\n  },\n  FeatureCollection: function(object, stream) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) streamGeometry(features[i].geometry, stream);\n  }\n};\n\nvar streamGeometryType = {\n  Sphere: function(object, stream) {\n    stream.sphere();\n  },\n  Point: function(object, stream) {\n    object = object.coordinates;\n    stream.point(object[0], object[1], object[2]);\n  },\n  MultiPoint: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) object = coordinates[i], stream.point(object[0], object[1], object[2]);\n  },\n  LineString: function(object, stream) {\n    streamLine(object.coordinates, stream, 0);\n  },\n  MultiLineString: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamLine(coordinates[i], stream, 0);\n  },\n  Polygon: function(object, stream) {\n    streamPolygon(object.coordinates, stream);\n  },\n  MultiPolygon: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamPolygon(coordinates[i], stream);\n  },\n  GeometryCollection: function(object, stream) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) streamGeometry(geometries[i], stream);\n  }\n};\n\nfunction streamLine(coordinates, stream, closed) {\n  var i = -1, n = coordinates.length - closed, coordinate;\n  stream.lineStart();\n  while (++i < n) coordinate = coordinates[i], stream.point(coordinate[0], coordinate[1], coordinate[2]);\n  stream.lineEnd();\n}\n\nfunction streamPolygon(coordinates, stream) {\n  var i = -1, n = coordinates.length;\n  stream.polygonStart();\n  while (++i < n) streamLine(coordinates[i], stream, 1);\n  stream.polygonEnd();\n}\n\nexport default function(object, stream) {\n  if (object && streamObjectType.hasOwnProperty(object.type)) {\n    streamObjectType[object.type](object, stream);\n  } else {\n    streamGeometry(object, stream);\n  }\n}\n", "import {Adder} from \"d3-array\";\nimport {atan2, cos, quarterPi, radians, sin, tau} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nexport var areaRingSum = new Adder();\n\n// hello?\n\nvar areaSum = new Adder(),\n    lambda00,\n    phi00,\n    lambda0,\n    cosPhi0,\n    sinPhi0;\n\nexport var areaStream = {\n  point: noop,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: function() {\n    areaRingSum = new Adder();\n    areaStream.lineStart = areaRingStart;\n    areaStream.lineEnd = areaRingEnd;\n  },\n  polygonEnd: function() {\n    var areaRing = +areaRingSum;\n    areaSum.add(areaRing < 0 ? tau + areaRing : areaRing);\n    this.lineStart = this.lineEnd = this.point = noop;\n  },\n  sphere: function() {\n    areaSum.add(tau);\n  }\n};\n\nfunction areaRingStart() {\n  areaStream.point = areaPointFirst;\n}\n\nfunction areaRingEnd() {\n  areaPoint(lambda00, phi00);\n}\n\nfunction areaPointFirst(lambda, phi) {\n  areaStream.point = areaPoint;\n  lambda00 = lambda, phi00 = phi;\n  lambda *= radians, phi *= radians;\n  lambda0 = lambda, cosPhi0 = cos(phi = phi / 2 + quarterPi), sinPhi0 = sin(phi);\n}\n\nfunction areaPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  phi = phi / 2 + quarterPi; // half the angular distance from south pole\n\n  // Spherical excess E for a spherical triangle with vertices: south pole,\n  // previous point, current point.  Uses a formula derived from Cagnoli’s\n  // theorem.  See Todhunter, Spherical Trig. (1871), Sec. 103, Eq. (2).\n  var dLambda = lambda - lambda0,\n      sdLambda = dLambda >= 0 ? 1 : -1,\n      adLambda = sdLambda * dLambda,\n      cosPhi = cos(phi),\n      sinPhi = sin(phi),\n      k = sinPhi0 * sinPhi,\n      u = cosPhi0 * cosPhi + k * cos(adLambda),\n      v = k * sdLambda * sin(adLambda);\n  areaRingSum.add(atan2(v, u));\n\n  // Advance the previous points.\n  lambda0 = lambda, cosPhi0 = cosPhi, sinPhi0 = sinPhi;\n}\n\nexport default function(object) {\n  areaSum = new Adder();\n  stream(object, areaStream);\n  return areaSum * 2;\n}\n", "import {asin, atan2, cos, sin, sqrt} from \"./math.js\";\n\nexport function spherical(cartesian) {\n  return [atan2(cartesian[1], cartesian[0]), asin(cartesian[2])];\n}\n\nexport function cartesian(spherical) {\n  var lambda = spherical[0], phi = spherical[1], cosPhi = cos(phi);\n  return [cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi)];\n}\n\nexport function cartesianDot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\n\nexport function cartesianCross(a, b) {\n  return [a[1] * b[2] - a[2] * b[1], a[2] * b[0] - a[0] * b[2], a[0] * b[1] - a[1] * b[0]];\n}\n\n// TODO return a\nexport function cartesianAddInPlace(a, b) {\n  a[0] += b[0], a[1] += b[1], a[2] += b[2];\n}\n\nexport function cartesianScale(vector, k) {\n  return [vector[0] * k, vector[1] * k, vector[2] * k];\n}\n\n// TODO return d\nexport function cartesianNormalizeInPlace(d) {\n  var l = sqrt(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n  d[0] /= l, d[1] /= l, d[2] /= l;\n}\n", "import {Adder} from \"d3-array\";\nimport {areaStream, areaRingSum} from \"./area.js\";\nimport {cartesian, cartesianCross, cartesianNormalizeInPlace, spherical} from \"./cartesian.js\";\nimport {abs, degrees, epsilon, radians} from \"./math.js\";\nimport stream from \"./stream.js\";\n\nvar lambda0, phi0, lambda1, phi1, // bounds\n    lambda2, // previous lambda-coordinate\n    lambda00, phi00, // first point\n    p0, // previous 3D point\n    deltaSum,\n    ranges,\n    range;\n\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: boundsLineStart,\n  lineEnd: boundsLineEnd,\n  polygonStart: function() {\n    boundsStream.point = boundsRingPoint;\n    boundsStream.lineStart = boundsRingStart;\n    boundsStream.lineEnd = boundsRingEnd;\n    deltaSum = new Adder();\n    areaStream.polygonStart();\n  },\n  polygonEnd: function() {\n    areaStream.polygonEnd();\n    boundsStream.point = boundsPoint;\n    boundsStream.lineStart = boundsLineStart;\n    boundsStream.lineEnd = boundsLineEnd;\n    if (areaRingSum < 0) lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n    else if (deltaSum > epsilon) phi1 = 90;\n    else if (deltaSum < -epsilon) phi0 = -90;\n    range[0] = lambda0, range[1] = lambda1;\n  },\n  sphere: function() {\n    lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n  }\n};\n\nfunction boundsPoint(lambda, phi) {\n  ranges.push(range = [lambda0 = lambda, lambda1 = lambda]);\n  if (phi < phi0) phi0 = phi;\n  if (phi > phi1) phi1 = phi;\n}\n\nfunction linePoint(lambda, phi) {\n  var p = cartesian([lambda * radians, phi * radians]);\n  if (p0) {\n    var normal = cartesianCross(p0, p),\n        equatorial = [normal[1], -normal[0], 0],\n        inflection = cartesianCross(equatorial, normal);\n    cartesianNormalizeInPlace(inflection);\n    inflection = spherical(inflection);\n    var delta = lambda - lambda2,\n        sign = delta > 0 ? 1 : -1,\n        lambdai = inflection[0] * degrees * sign,\n        phii,\n        antimeridian = abs(delta) > 180;\n    if (antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n      phii = inflection[1] * degrees;\n      if (phii > phi1) phi1 = phii;\n    } else if (lambdai = (lambdai + 360) % 360 - 180, antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n      phii = -inflection[1] * degrees;\n      if (phii < phi0) phi0 = phii;\n    } else {\n      if (phi < phi0) phi0 = phi;\n      if (phi > phi1) phi1 = phi;\n    }\n    if (antimeridian) {\n      if (lambda < lambda2) {\n        if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n      } else {\n        if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n      }\n    } else {\n      if (lambda1 >= lambda0) {\n        if (lambda < lambda0) lambda0 = lambda;\n        if (lambda > lambda1) lambda1 = lambda;\n      } else {\n        if (lambda > lambda2) {\n          if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n        } else {\n          if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n        }\n      }\n    }\n  } else {\n    ranges.push(range = [lambda0 = lambda, lambda1 = lambda]);\n  }\n  if (phi < phi0) phi0 = phi;\n  if (phi > phi1) phi1 = phi;\n  p0 = p, lambda2 = lambda;\n}\n\nfunction boundsLineStart() {\n  boundsStream.point = linePoint;\n}\n\nfunction boundsLineEnd() {\n  range[0] = lambda0, range[1] = lambda1;\n  boundsStream.point = boundsPoint;\n  p0 = null;\n}\n\nfunction boundsRingPoint(lambda, phi) {\n  if (p0) {\n    var delta = lambda - lambda2;\n    deltaSum.add(abs(delta) > 180 ? delta + (delta > 0 ? 360 : -360) : delta);\n  } else {\n    lambda00 = lambda, phi00 = phi;\n  }\n  areaStream.point(lambda, phi);\n  linePoint(lambda, phi);\n}\n\nfunction boundsRingStart() {\n  areaStream.lineStart();\n}\n\nfunction boundsRingEnd() {\n  boundsRingPoint(lambda00, phi00);\n  areaStream.lineEnd();\n  if (abs(deltaSum) > epsilon) lambda0 = -(lambda1 = 180);\n  range[0] = lambda0, range[1] = lambda1;\n  p0 = null;\n}\n\n// Finds the left-right distance between two longitudes.\n// This is almost the same as (lambda1 - lambda0 + 360°) % 360°, except that we want\n// the distance between ±180° to be 360°.\nfunction angle(lambda0, lambda1) {\n  return (lambda1 -= lambda0) < 0 ? lambda1 + 360 : lambda1;\n}\n\nfunction rangeCompare(a, b) {\n  return a[0] - b[0];\n}\n\nfunction rangeContains(range, x) {\n  return range[0] <= range[1] ? range[0] <= x && x <= range[1] : x < range[0] || range[1] < x;\n}\n\nexport default function(feature) {\n  var i, n, a, b, merged, deltaMax, delta;\n\n  phi1 = lambda1 = -(lambda0 = phi0 = Infinity);\n  ranges = [];\n  stream(feature, boundsStream);\n\n  // First, sort ranges by their minimum longitudes.\n  if (n = ranges.length) {\n    ranges.sort(rangeCompare);\n\n    // Then, merge any ranges that overlap.\n    for (i = 1, a = ranges[0], merged = [a]; i < n; ++i) {\n      b = ranges[i];\n      if (rangeContains(a, b[0]) || rangeContains(a, b[1])) {\n        if (angle(a[0], b[1]) > angle(a[0], a[1])) a[1] = b[1];\n        if (angle(b[0], a[1]) > angle(a[0], a[1])) a[0] = b[0];\n      } else {\n        merged.push(a = b);\n      }\n    }\n\n    // Finally, find the largest gap between the merged ranges.\n    // The final bounding box will be the inverse of this gap.\n    for (deltaMax = -Infinity, n = merged.length - 1, i = 0, a = merged[n]; i <= n; a = b, ++i) {\n      b = merged[i];\n      if ((delta = angle(a[1], b[0])) > deltaMax) deltaMax = delta, lambda0 = b[0], lambda1 = a[1];\n    }\n  }\n\n  ranges = range = null;\n\n  return lambda0 === Infinity || phi0 === Infinity\n      ? [[NaN, NaN], [NaN, NaN]]\n      : [[lambda0, phi0], [lambda1, phi1]];\n}\n", "import {Adder} from \"d3-array\";\nimport {asin, atan2, cos, degrees, epsilon, epsilon2, hypot, radians, sin, sqrt} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nvar W0, W1,\n    X0, Y0, Z0,\n    X1, Y1, Z1,\n    X2, Y2, Z2,\n    lambda00, phi00, // first point\n    x0, y0, z0; // previous point\n\nvar centroidStream = {\n  sphere: noop,\n  point: centroidPoint,\n  lineStart: centroidLineStart,\n  lineEnd: centroidLineEnd,\n  polygonStart: function() {\n    centroidStream.lineStart = centroidRingStart;\n    centroidStream.lineEnd = centroidRingEnd;\n  },\n  polygonEnd: function() {\n    centroidStream.lineStart = centroidLineStart;\n    centroidStream.lineEnd = centroidLineEnd;\n  }\n};\n\n// Arithmetic mean of Cartesian vectors.\nfunction centroidPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi);\n  centroidPointCartesian(cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi));\n}\n\nfunction centroidPointCartesian(x, y, z) {\n  ++W0;\n  X0 += (x - X0) / W0;\n  Y0 += (y - Y0) / W0;\n  Z0 += (z - Z0) / W0;\n}\n\nfunction centroidLineStart() {\n  centroidStream.point = centroidLinePointFirst;\n}\n\nfunction centroidLinePointFirst(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi);\n  x0 = cosPhi * cos(lambda);\n  y0 = cosPhi * sin(lambda);\n  z0 = sin(phi);\n  centroidStream.point = centroidLinePoint;\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidLinePoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi),\n      x = cosPhi * cos(lambda),\n      y = cosPhi * sin(lambda),\n      z = sin(phi),\n      w = atan2(sqrt((w = y0 * z - z0 * y) * w + (w = z0 * x - x0 * z) * w + (w = x0 * y - y0 * x) * w), x0 * x + y0 * y + z0 * z);\n  W1 += w;\n  X1 += w * (x0 + (x0 = x));\n  Y1 += w * (y0 + (y0 = y));\n  Z1 += w * (z0 + (z0 = z));\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidLineEnd() {\n  centroidStream.point = centroidPoint;\n}\n\n// See J. E. Brock, The Inertia Tensor for a Spherical Triangle,\n// J. Applied Mechanics 42, 239 (1975).\nfunction centroidRingStart() {\n  centroidStream.point = centroidRingPointFirst;\n}\n\nfunction centroidRingEnd() {\n  centroidRingPoint(lambda00, phi00);\n  centroidStream.point = centroidPoint;\n}\n\nfunction centroidRingPointFirst(lambda, phi) {\n  lambda00 = lambda, phi00 = phi;\n  lambda *= radians, phi *= radians;\n  centroidStream.point = centroidRingPoint;\n  var cosPhi = cos(phi);\n  x0 = cosPhi * cos(lambda);\n  y0 = cosPhi * sin(lambda);\n  z0 = sin(phi);\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidRingPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi),\n      x = cosPhi * cos(lambda),\n      y = cosPhi * sin(lambda),\n      z = sin(phi),\n      cx = y0 * z - z0 * y,\n      cy = z0 * x - x0 * z,\n      cz = x0 * y - y0 * x,\n      m = hypot(cx, cy, cz),\n      w = asin(m), // line weight = angle\n      v = m && -w / m; // area weight multiplier\n  X2.add(v * cx);\n  Y2.add(v * cy);\n  Z2.add(v * cz);\n  W1 += w;\n  X1 += w * (x0 + (x0 = x));\n  Y1 += w * (y0 + (y0 = y));\n  Z1 += w * (z0 + (z0 = z));\n  centroidPointCartesian(x0, y0, z0);\n}\n\nexport default function(object) {\n  W0 = W1 =\n  X0 = Y0 = Z0 =\n  X1 = Y1 = Z1 = 0;\n  X2 = new Adder();\n  Y2 = new Adder();\n  Z2 = new Adder();\n  stream(object, centroidStream);\n\n  var x = +X2,\n      y = +Y2,\n      z = +Z2,\n      m = hypot(x, y, z);\n\n  // If the area-weighted ccentroid is undefined, fall back to length-weighted ccentroid.\n  if (m < epsilon2) {\n    x = X1, y = Y1, z = Z1;\n    // If the feature has zero length, fall back to arithmetic mean of point vectors.\n    if (W1 < epsilon) x = X0, y = Y0, z = Z0;\n    m = hypot(x, y, z);\n    // If the feature still has an undefined ccentroid, then return.\n    if (m < epsilon2) return [NaN, NaN];\n  }\n\n  return [atan2(y, x) * degrees, asin(z / m) * degrees];\n}\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function(a, b) {\n\n  function compose(x, y) {\n    return x = a(x, y), b(x[0], x[1]);\n  }\n\n  if (a.invert && b.invert) compose.invert = function(x, y) {\n    return x = b.invert(x, y), x && a.invert(x[0], x[1]);\n  };\n\n  return compose;\n}\n", "import compose from \"./compose.js\";\nimport {abs, asin, atan2, cos, degrees, pi, radians, sin, tau} from \"./math.js\";\n\nfunction rotationIdentity(lambda, phi) {\n  if (abs(lambda) > pi) lambda -= Math.round(lambda / tau) * tau;\n  return [lambda, phi];\n}\n\nrotationIdentity.invert = rotationIdentity;\n\nexport function rotateRadians(deltaLambda, deltaPhi, deltaGamma) {\n  return (deltaLambda %= tau) ? (deltaPhi || deltaGamma ? compose(rotationLambda(deltaLambda), rotationPhiGamma(deltaPhi, deltaGamma))\n    : rotationLambda(deltaLambda))\n    : (deltaPhi || deltaGamma ? rotationPhiGamma(deltaPhi, deltaGamma)\n    : rotationIdentity);\n}\n\nfunction forwardRotationLambda(deltaLambda) {\n  return function(lambda, phi) {\n    lambda += deltaLambda;\n    if (abs(lambda) > pi) lambda -= Math.round(lambda / tau) * tau;\n    return [lambda, phi];\n  };\n}\n\nfunction rotationLambda(deltaLambda) {\n  var rotation = forwardRotationLambda(deltaLambda);\n  rotation.invert = forwardRotationLambda(-deltaLambda);\n  return rotation;\n}\n\nfunction rotationPhiGamma(deltaPhi, deltaGamma) {\n  var cosDeltaPhi = cos(deltaPhi),\n      sinDeltaPhi = sin(deltaPhi),\n      cosDeltaGamma = cos(deltaGamma),\n      sinDeltaGamma = sin(deltaGamma);\n\n  function rotation(lambda, phi) {\n    var cosPhi = cos(phi),\n        x = cos(lambda) * cosPhi,\n        y = sin(lambda) * cosPhi,\n        z = sin(phi),\n        k = z * cosDeltaPhi + x * sinDeltaPhi;\n    return [\n      atan2(y * cosDeltaGamma - k * sinDeltaGamma, x * cosDeltaPhi - z * sinDeltaPhi),\n      asin(k * cosDeltaGamma + y * sinDeltaGamma)\n    ];\n  }\n\n  rotation.invert = function(lambda, phi) {\n    var cosPhi = cos(phi),\n        x = cos(lambda) * cosPhi,\n        y = sin(lambda) * cosPhi,\n        z = sin(phi),\n        k = z * cosDeltaGamma - y * sinDeltaGamma;\n    return [\n      atan2(y * cosDeltaGamma + z * sinDeltaGamma, x * cosDeltaPhi + k * sinDeltaPhi),\n      asin(k * cosDeltaPhi - x * sinDeltaPhi)\n    ];\n  };\n\n  return rotation;\n}\n\nexport default function(rotate) {\n  rotate = rotateRadians(rotate[0] * radians, rotate[1] * radians, rotate.length > 2 ? rotate[2] * radians : 0);\n\n  function forward(coordinates) {\n    coordinates = rotate(coordinates[0] * radians, coordinates[1] * radians);\n    return coordinates[0] *= degrees, coordinates[1] *= degrees, coordinates;\n  }\n\n  forward.invert = function(coordinates) {\n    coordinates = rotate.invert(coordinates[0] * radians, coordinates[1] * radians);\n    return coordinates[0] *= degrees, coordinates[1] *= degrees, coordinates;\n  };\n\n  return forward;\n}\n", "import {cartesian, cartesianNormalizeInPlace, spherical} from \"./cartesian.js\";\nimport constant from \"./constant.js\";\nimport {acos, cos, degrees, epsilon, radians, sin, tau} from \"./math.js\";\nimport {rotateRadians} from \"./rotation.js\";\n\n// Generates a circle centered at [0°, 0°], with a given radius and precision.\nexport function circleStream(stream, radius, delta, direction, t0, t1) {\n  if (!delta) return;\n  var cosRadius = cos(radius),\n      sinRadius = sin(radius),\n      step = direction * delta;\n  if (t0 == null) {\n    t0 = radius + direction * tau;\n    t1 = radius - step / 2;\n  } else {\n    t0 = circleRadius(cosRadius, t0);\n    t1 = circleRadius(cosRadius, t1);\n    if (direction > 0 ? t0 < t1 : t0 > t1) t0 += direction * tau;\n  }\n  for (var point, t = t0; direction > 0 ? t > t1 : t < t1; t -= step) {\n    point = spherical([cosRadius, -sinRadius * cos(t), -sinRadius * sin(t)]);\n    stream.point(point[0], point[1]);\n  }\n}\n\n// Returns the signed angle of a cartesian point relative to [cosRadius, 0, 0].\nfunction circleRadius(cosRadius, point) {\n  point = cartesian(point), point[0] -= cosRadius;\n  cartesianNormalizeInPlace(point);\n  var radius = acos(-point[1]);\n  return ((-point[2] < 0 ? -radius : radius) + tau - epsilon) % tau;\n}\n\nexport default function() {\n  var center = constant([0, 0]),\n      radius = constant(90),\n      precision = constant(2),\n      ring,\n      rotate,\n      stream = {point: point};\n\n  function point(x, y) {\n    ring.push(x = rotate(x, y));\n    x[0] *= degrees, x[1] *= degrees;\n  }\n\n  function circle() {\n    var c = center.apply(this, arguments),\n        r = radius.apply(this, arguments) * radians,\n        p = precision.apply(this, arguments) * radians;\n    ring = [];\n    rotate = rotateRadians(-c[0] * radians, -c[1] * radians, 0).invert;\n    circleStream(stream, r, p, 1);\n    c = {type: \"Polygon\", coordinates: [ring]};\n    ring = rotate = null;\n    return c;\n  }\n\n  circle.center = function(_) {\n    return arguments.length ? (center = typeof _ === \"function\" ? _ : constant([+_[0], +_[1]]), circle) : center;\n  };\n\n  circle.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), circle) : radius;\n  };\n\n  circle.precision = function(_) {\n    return arguments.length ? (precision = typeof _ === \"function\" ? _ : constant(+_), circle) : precision;\n  };\n\n  return circle;\n}\n", "import noop from \"../noop.js\";\n\nexport default function() {\n  var lines = [],\n      line;\n  return {\n    point: function(x, y, m) {\n      line.push([x, y, m]);\n    },\n    lineStart: function() {\n      lines.push(line = []);\n    },\n    lineEnd: noop,\n    rejoin: function() {\n      if (lines.length > 1) lines.push(lines.pop().concat(lines.shift()));\n    },\n    result: function() {\n      var result = lines;\n      lines = [];\n      line = null;\n      return result;\n    }\n  };\n}\n", "import {abs, epsilon} from \"./math.js\";\n\nexport default function(a, b) {\n  return abs(a[0] - b[0]) < epsilon && abs(a[1] - b[1]) < epsilon;\n}\n", "import pointEqual from \"../pointEqual.js\";\nimport {epsilon} from \"../math.js\";\n\nfunction Intersection(point, points, other, entry) {\n  this.x = point;\n  this.z = points;\n  this.o = other; // another intersection\n  this.e = entry; // is an entry?\n  this.v = false; // visited\n  this.n = this.p = null; // next & previous\n}\n\n// A generalized polygon clipping algorithm: given a polygon that has been cut\n// into its visible line segments, and rejoins the segments by interpolating\n// along the clip edge.\nexport default function(segments, compareIntersection, startInside, interpolate, stream) {\n  var subject = [],\n      clip = [],\n      i,\n      n;\n\n  segments.forEach(function(segment) {\n    if ((n = segment.length - 1) <= 0) return;\n    var n, p0 = segment[0], p1 = segment[n], x;\n\n    if (pointEqual(p0, p1)) {\n      if (!p0[2] && !p1[2]) {\n        stream.lineStart();\n        for (i = 0; i < n; ++i) stream.point((p0 = segment[i])[0], p0[1]);\n        stream.lineEnd();\n        return;\n      }\n      // handle degenerate cases by moving the point\n      p1[0] += 2 * epsilon;\n    }\n\n    subject.push(x = new Intersection(p0, segment, null, true));\n    clip.push(x.o = new Intersection(p0, null, x, false));\n    subject.push(x = new Intersection(p1, segment, null, false));\n    clip.push(x.o = new Intersection(p1, null, x, true));\n  });\n\n  if (!subject.length) return;\n\n  clip.sort(compareIntersection);\n  link(subject);\n  link(clip);\n\n  for (i = 0, n = clip.length; i < n; ++i) {\n    clip[i].e = startInside = !startInside;\n  }\n\n  var start = subject[0],\n      points,\n      point;\n\n  while (1) {\n    // Find first unvisited intersection.\n    var current = start,\n        isSubject = true;\n    while (current.v) if ((current = current.n) === start) return;\n    points = current.z;\n    stream.lineStart();\n    do {\n      current.v = current.o.v = true;\n      if (current.e) {\n        if (isSubject) {\n          for (i = 0, n = points.length; i < n; ++i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.n.x, 1, stream);\n        }\n        current = current.n;\n      } else {\n        if (isSubject) {\n          points = current.p.z;\n          for (i = points.length - 1; i >= 0; --i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.p.x, -1, stream);\n        }\n        current = current.p;\n      }\n      current = current.o;\n      points = current.z;\n      isSubject = !isSubject;\n    } while (!current.v);\n    stream.lineEnd();\n  }\n}\n\nfunction link(array) {\n  if (!(n = array.length)) return;\n  var n,\n      i = 0,\n      a = array[0],\n      b;\n  while (++i < n) {\n    a.n = b = array[i];\n    b.p = a;\n    a = b;\n  }\n  a.n = b = array[0];\n  b.p = a;\n}\n", "import {<PERSON>der} from \"d3-array\";\nimport {cartesian, cartesianCross, cartesianNormalizeInPlace} from \"./cartesian.js\";\nimport {abs, asin, atan2, cos, epsilon, epsilon2, halfPi, pi, quarterPi, sign, sin, tau} from \"./math.js\";\n\nfunction longitude(point) {\n  return abs(point[0]) <= pi ? point[0] : sign(point[0]) * ((abs(point[0]) + pi) % tau - pi);\n}\n\nexport default function(polygon, point) {\n  var lambda = longitude(point),\n      phi = point[1],\n      sinPhi = sin(phi),\n      normal = [sin(lambda), -cos(lambda), 0],\n      angle = 0,\n      winding = 0;\n\n  var sum = new Adder();\n\n  if (sinPhi === 1) phi = halfPi + epsilon;\n  else if (sinPhi === -1) phi = -halfPi - epsilon;\n\n  for (var i = 0, n = polygon.length; i < n; ++i) {\n    if (!(m = (ring = polygon[i]).length)) continue;\n    var ring,\n        m,\n        point0 = ring[m - 1],\n        lambda0 = longitude(point0),\n        phi0 = point0[1] / 2 + quarterPi,\n        sinPhi0 = sin(phi0),\n        cosPhi0 = cos(phi0);\n\n    for (var j = 0; j < m; ++j, lambda0 = lambda1, sinPhi0 = sinPhi1, cosPhi0 = cosPhi1, point0 = point1) {\n      var point1 = ring[j],\n          lambda1 = longitude(point1),\n          phi1 = point1[1] / 2 + quarterPi,\n          sinPhi1 = sin(phi1),\n          cosPhi1 = cos(phi1),\n          delta = lambda1 - lambda0,\n          sign = delta >= 0 ? 1 : -1,\n          absDelta = sign * delta,\n          antimeridian = absDelta > pi,\n          k = sinPhi0 * sinPhi1;\n\n      sum.add(atan2(k * sign * sin(absDelta), cosPhi0 * cosPhi1 + k * cos(absDelta)));\n      angle += antimeridian ? delta + sign * tau : delta;\n\n      // Are the longitudes either side of the point’s meridian (lambda),\n      // and are the latitudes smaller than the parallel (phi)?\n      if (antimeridian ^ lambda0 >= lambda ^ lambda1 >= lambda) {\n        var arc = cartesianCross(cartesian(point0), cartesian(point1));\n        cartesianNormalizeInPlace(arc);\n        var intersection = cartesianCross(normal, arc);\n        cartesianNormalizeInPlace(intersection);\n        var phiArc = (antimeridian ^ delta >= 0 ? -1 : 1) * asin(intersection[2]);\n        if (phi > phiArc || phi === phiArc && (arc[0] || arc[1])) {\n          winding += antimeridian ^ delta >= 0 ? 1 : -1;\n        }\n      }\n    }\n  }\n\n  // First, determine whether the South pole is inside or outside:\n  //\n  // It is inside if:\n  // * the polygon winds around it in a clockwise direction.\n  // * the polygon does not (cumulatively) wind around it, but has a negative\n  //   (counter-clockwise) area.\n  //\n  // Second, count the (signed) number of times a segment crosses a lambda\n  // from the point to the South pole.  If it is zero, then the point is the\n  // same side as the South pole.\n\n  return (angle < -epsilon || angle < epsilon && sum < -epsilon2) ^ (winding & 1);\n}\n", "import clipBuffer from \"./buffer.js\";\nimport clipRejoin from \"./rejoin.js\";\nimport {epsilon, halfPi} from \"../math.js\";\nimport polygonContains from \"../polygonContains.js\";\nimport {merge} from \"d3-array\";\n\nexport default function(pointVisible, clipLine, interpolate, start) {\n  return function(sink) {\n    var line = clipLine(sink),\n        ringBuffer = clipBuffer(),\n        ringSink = clipLine(ringBuffer),\n        polygonStarted = false,\n        polygon,\n        segments,\n        ring;\n\n    var clip = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() {\n        clip.point = pointRing;\n        clip.lineStart = ringStart;\n        clip.lineEnd = ringEnd;\n        segments = [];\n        polygon = [];\n      },\n      polygonEnd: function() {\n        clip.point = point;\n        clip.lineStart = lineStart;\n        clip.lineEnd = lineEnd;\n        segments = merge(segments);\n        var startInside = polygonContains(polygon, start);\n        if (segments.length) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          clipRejoin(segments, compareIntersection, startInside, interpolate, sink);\n        } else if (startInside) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          interpolate(null, null, 1, sink);\n          sink.lineEnd();\n        }\n        if (polygonStarted) sink.polygonEnd(), polygonStarted = false;\n        segments = polygon = null;\n      },\n      sphere: function() {\n        sink.polygonStart();\n        sink.lineStart();\n        interpolate(null, null, 1, sink);\n        sink.lineEnd();\n        sink.polygonEnd();\n      }\n    };\n\n    function point(lambda, phi) {\n      if (pointVisible(lambda, phi)) sink.point(lambda, phi);\n    }\n\n    function pointLine(lambda, phi) {\n      line.point(lambda, phi);\n    }\n\n    function lineStart() {\n      clip.point = pointLine;\n      line.lineStart();\n    }\n\n    function lineEnd() {\n      clip.point = point;\n      line.lineEnd();\n    }\n\n    function pointRing(lambda, phi) {\n      ring.push([lambda, phi]);\n      ringSink.point(lambda, phi);\n    }\n\n    function ringStart() {\n      ringSink.lineStart();\n      ring = [];\n    }\n\n    function ringEnd() {\n      pointRing(ring[0][0], ring[0][1]);\n      ringSink.lineEnd();\n\n      var clean = ringSink.clean(),\n          ringSegments = ringBuffer.result(),\n          i, n = ringSegments.length, m,\n          segment,\n          point;\n\n      ring.pop();\n      polygon.push(ring);\n      ring = null;\n\n      if (!n) return;\n\n      // No intersections.\n      if (clean & 1) {\n        segment = ringSegments[0];\n        if ((m = segment.length - 1) > 0) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          for (i = 0; i < m; ++i) sink.point((point = segment[i])[0], point[1]);\n          sink.lineEnd();\n        }\n        return;\n      }\n\n      // Rejoin connected segments.\n      // TODO reuse ringBuffer.rejoin()?\n      if (n > 1 && clean & 2) ringSegments.push(ringSegments.pop().concat(ringSegments.shift()));\n\n      segments.push(ringSegments.filter(validSegment));\n    }\n\n    return clip;\n  };\n}\n\nfunction validSegment(segment) {\n  return segment.length > 1;\n}\n\n// Intersections are sorted along the clip edge. For both antimeridian cutting\n// and circle clipping, the same comparison is used.\nfunction compareIntersection(a, b) {\n  return ((a = a.x)[0] < 0 ? a[1] - halfPi - epsilon : halfPi - a[1])\n       - ((b = b.x)[0] < 0 ? b[1] - halfPi - epsilon : halfPi - b[1]);\n}\n", "import clip from \"./index.js\";\nimport {abs, atan, cos, epsilon, halfPi, pi, sin} from \"../math.js\";\n\nexport default clip(\n  function() { return true; },\n  clipAntimeridianLine,\n  clipAntimeridianInterpolate,\n  [-pi, -halfPi]\n);\n\n// Takes a line and cuts into visible segments. Return values: 0 - there were\n// intersections or the line was empty; 1 - no intersections; 2 - there were\n// intersections, and the first and last segments should be rejoined.\nfunction clipAntimeridianLine(stream) {\n  var lambda0 = NaN,\n      phi0 = NaN,\n      sign0 = NaN,\n      clean; // no intersections\n\n  return {\n    lineStart: function() {\n      stream.lineStart();\n      clean = 1;\n    },\n    point: function(lambda1, phi1) {\n      var sign1 = lambda1 > 0 ? pi : -pi,\n          delta = abs(lambda1 - lambda0);\n      if (abs(delta - pi) < epsilon) { // line crosses a pole\n        stream.point(lambda0, phi0 = (phi0 + phi1) / 2 > 0 ? halfPi : -halfPi);\n        stream.point(sign0, phi0);\n        stream.lineEnd();\n        stream.lineStart();\n        stream.point(sign1, phi0);\n        stream.point(lambda1, phi0);\n        clean = 0;\n      } else if (sign0 !== sign1 && delta >= pi) { // line crosses antimeridian\n        if (abs(lambda0 - sign0) < epsilon) lambda0 -= sign0 * epsilon; // handle degeneracies\n        if (abs(lambda1 - sign1) < epsilon) lambda1 -= sign1 * epsilon;\n        phi0 = clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1);\n        stream.point(sign0, phi0);\n        stream.lineEnd();\n        stream.lineStart();\n        stream.point(sign1, phi0);\n        clean = 0;\n      }\n      stream.point(lambda0 = lambda1, phi0 = phi1);\n      sign0 = sign1;\n    },\n    lineEnd: function() {\n      stream.lineEnd();\n      lambda0 = phi0 = NaN;\n    },\n    clean: function() {\n      return 2 - clean; // if intersections, rejoin first and last segments\n    }\n  };\n}\n\nfunction clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1) {\n  var cosPhi0,\n      cosPhi1,\n      sinLambda0Lambda1 = sin(lambda0 - lambda1);\n  return abs(sinLambda0Lambda1) > epsilon\n      ? atan((sin(phi0) * (cosPhi1 = cos(phi1)) * sin(lambda1)\n          - sin(phi1) * (cosPhi0 = cos(phi0)) * sin(lambda0))\n          / (cosPhi0 * cosPhi1 * sinLambda0Lambda1))\n      : (phi0 + phi1) / 2;\n}\n\nfunction clipAntimeridianInterpolate(from, to, direction, stream) {\n  var phi;\n  if (from == null) {\n    phi = direction * halfPi;\n    stream.point(-pi, phi);\n    stream.point(0, phi);\n    stream.point(pi, phi);\n    stream.point(pi, 0);\n    stream.point(pi, -phi);\n    stream.point(0, -phi);\n    stream.point(-pi, -phi);\n    stream.point(-pi, 0);\n    stream.point(-pi, phi);\n  } else if (abs(from[0] - to[0]) > epsilon) {\n    var lambda = from[0] < to[0] ? pi : -pi;\n    phi = direction * lambda / 2;\n    stream.point(-lambda, phi);\n    stream.point(0, phi);\n    stream.point(lambda, phi);\n  } else {\n    stream.point(to[0], to[1]);\n  }\n}\n", "import {cartesian, cartesianAddInPlace, cartesianCross, cartesianDot, cartesianScale, spherical} from \"../cartesian.js\";\nimport {circleStream} from \"../circle.js\";\nimport {abs, cos, epsilon, pi, radians, sqrt} from \"../math.js\";\nimport pointEqual from \"../pointEqual.js\";\nimport clip from \"./index.js\";\n\nexport default function(radius) {\n  var cr = cos(radius),\n      delta = 2 * radians,\n      smallRadius = cr > 0,\n      notHemisphere = abs(cr) > epsilon; // TODO optimise for this common case\n\n  function interpolate(from, to, direction, stream) {\n    circleStream(stream, radius, delta, direction, from, to);\n  }\n\n  function visible(lambda, phi) {\n    return cos(lambda) * cos(phi) > cr;\n  }\n\n  // Takes a line and cuts into visible segments. Return values used for polygon\n  // clipping: 0 - there were intersections or the line was empty; 1 - no\n  // intersections 2 - there were intersections, and the first and last segments\n  // should be rejoined.\n  function clipLine(stream) {\n    var point0, // previous point\n        c0, // code for previous point\n        v0, // visibility of previous point\n        v00, // visibility of first point\n        clean; // no intersections\n    return {\n      lineStart: function() {\n        v00 = v0 = false;\n        clean = 1;\n      },\n      point: function(lambda, phi) {\n        var point1 = [lambda, phi],\n            point2,\n            v = visible(lambda, phi),\n            c = smallRadius\n              ? v ? 0 : code(lambda, phi)\n              : v ? code(lambda + (lambda < 0 ? pi : -pi), phi) : 0;\n        if (!point0 && (v00 = v0 = v)) stream.lineStart();\n        if (v !== v0) {\n          point2 = intersect(point0, point1);\n          if (!point2 || pointEqual(point0, point2) || pointEqual(point1, point2))\n            point1[2] = 1;\n        }\n        if (v !== v0) {\n          clean = 0;\n          if (v) {\n            // outside going in\n            stream.lineStart();\n            point2 = intersect(point1, point0);\n            stream.point(point2[0], point2[1]);\n          } else {\n            // inside going out\n            point2 = intersect(point0, point1);\n            stream.point(point2[0], point2[1], 2);\n            stream.lineEnd();\n          }\n          point0 = point2;\n        } else if (notHemisphere && point0 && smallRadius ^ v) {\n          var t;\n          // If the codes for two points are different, or are both zero,\n          // and there this segment intersects with the small circle.\n          if (!(c & c0) && (t = intersect(point1, point0, true))) {\n            clean = 0;\n            if (smallRadius) {\n              stream.lineStart();\n              stream.point(t[0][0], t[0][1]);\n              stream.point(t[1][0], t[1][1]);\n              stream.lineEnd();\n            } else {\n              stream.point(t[1][0], t[1][1]);\n              stream.lineEnd();\n              stream.lineStart();\n              stream.point(t[0][0], t[0][1], 3);\n            }\n          }\n        }\n        if (v && (!point0 || !pointEqual(point0, point1))) {\n          stream.point(point1[0], point1[1]);\n        }\n        point0 = point1, v0 = v, c0 = c;\n      },\n      lineEnd: function() {\n        if (v0) stream.lineEnd();\n        point0 = null;\n      },\n      // Rejoin first and last segments if there were intersections and the first\n      // and last points were visible.\n      clean: function() {\n        return clean | ((v00 && v0) << 1);\n      }\n    };\n  }\n\n  // Intersects the great circle between a and b with the clip circle.\n  function intersect(a, b, two) {\n    var pa = cartesian(a),\n        pb = cartesian(b);\n\n    // We have two planes, n1.p = d1 and n2.p = d2.\n    // Find intersection line p(t) = c1 n1 + c2 n2 + t (n1 ⨯ n2).\n    var n1 = [1, 0, 0], // normal\n        n2 = cartesianCross(pa, pb),\n        n2n2 = cartesianDot(n2, n2),\n        n1n2 = n2[0], // cartesianDot(n1, n2),\n        determinant = n2n2 - n1n2 * n1n2;\n\n    // Two polar points.\n    if (!determinant) return !two && a;\n\n    var c1 =  cr * n2n2 / determinant,\n        c2 = -cr * n1n2 / determinant,\n        n1xn2 = cartesianCross(n1, n2),\n        A = cartesianScale(n1, c1),\n        B = cartesianScale(n2, c2);\n    cartesianAddInPlace(A, B);\n\n    // Solve |p(t)|^2 = 1.\n    var u = n1xn2,\n        w = cartesianDot(A, u),\n        uu = cartesianDot(u, u),\n        t2 = w * w - uu * (cartesianDot(A, A) - 1);\n\n    if (t2 < 0) return;\n\n    var t = sqrt(t2),\n        q = cartesianScale(u, (-w - t) / uu);\n    cartesianAddInPlace(q, A);\n    q = spherical(q);\n\n    if (!two) return q;\n\n    // Two intersection points.\n    var lambda0 = a[0],\n        lambda1 = b[0],\n        phi0 = a[1],\n        phi1 = b[1],\n        z;\n\n    if (lambda1 < lambda0) z = lambda0, lambda0 = lambda1, lambda1 = z;\n\n    var delta = lambda1 - lambda0,\n        polar = abs(delta - pi) < epsilon,\n        meridian = polar || delta < epsilon;\n\n    if (!polar && phi1 < phi0) z = phi0, phi0 = phi1, phi1 = z;\n\n    // Check that the first point is between a and b.\n    if (meridian\n        ? polar\n          ? phi0 + phi1 > 0 ^ q[1] < (abs(q[0] - lambda0) < epsilon ? phi0 : phi1)\n          : phi0 <= q[1] && q[1] <= phi1\n        : delta > pi ^ (lambda0 <= q[0] && q[0] <= lambda1)) {\n      var q1 = cartesianScale(u, (-w + t) / uu);\n      cartesianAddInPlace(q1, A);\n      return [q, spherical(q1)];\n    }\n  }\n\n  // Generates a 4-bit vector representing the location of a point relative to\n  // the small circle's bounding box.\n  function code(lambda, phi) {\n    var r = smallRadius ? radius : pi - radius,\n        code = 0;\n    if (lambda < -r) code |= 1; // left\n    else if (lambda > r) code |= 2; // right\n    if (phi < -r) code |= 4; // below\n    else if (phi > r) code |= 8; // above\n    return code;\n  }\n\n  return clip(visible, clipLine, interpolate, smallRadius ? [0, -radius] : [-pi, radius - pi]);\n}\n", "export default function(a, b, x0, y0, x1, y1) {\n  var ax = a[0],\n      ay = a[1],\n      bx = b[0],\n      by = b[1],\n      t0 = 0,\n      t1 = 1,\n      dx = bx - ax,\n      dy = by - ay,\n      r;\n\n  r = x0 - ax;\n  if (!dx && r > 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dx > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = x1 - ax;\n  if (!dx && r < 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dx > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  r = y0 - ay;\n  if (!dy && r > 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dy > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = y1 - ay;\n  if (!dy && r < 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dy > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  if (t0 > 0) a[0] = ax + t0 * dx, a[1] = ay + t0 * dy;\n  if (t1 < 1) b[0] = ax + t1 * dx, b[1] = ay + t1 * dy;\n  return true;\n}\n", "import {abs, epsilon} from \"../math.js\";\nimport clipBuffer from \"./buffer.js\";\nimport clipLine from \"./line.js\";\nimport clipRejoin from \"./rejoin.js\";\nimport {merge} from \"d3-array\";\n\nvar clipMax = 1e9, clipMin = -clipMax;\n\n// TODO Use d3-polygon’s polygonContains here for the ring check?\n// TODO Eliminate duplicate buffering in clipBuffer and polygon.push?\n\nexport default function clipRectangle(x0, y0, x1, y1) {\n\n  function visible(x, y) {\n    return x0 <= x && x <= x1 && y0 <= y && y <= y1;\n  }\n\n  function interpolate(from, to, direction, stream) {\n    var a = 0, a1 = 0;\n    if (from == null\n        || (a = corner(from, direction)) !== (a1 = corner(to, direction))\n        || comparePoint(from, to) < 0 ^ direction > 0) {\n      do stream.point(a === 0 || a === 3 ? x0 : x1, a > 1 ? y1 : y0);\n      while ((a = (a + direction + 4) % 4) !== a1);\n    } else {\n      stream.point(to[0], to[1]);\n    }\n  }\n\n  function corner(p, direction) {\n    return abs(p[0] - x0) < epsilon ? direction > 0 ? 0 : 3\n        : abs(p[0] - x1) < epsilon ? direction > 0 ? 2 : 1\n        : abs(p[1] - y0) < epsilon ? direction > 0 ? 1 : 0\n        : direction > 0 ? 3 : 2; // abs(p[1] - y1) < epsilon\n  }\n\n  function compareIntersection(a, b) {\n    return comparePoint(a.x, b.x);\n  }\n\n  function comparePoint(a, b) {\n    var ca = corner(a, 1),\n        cb = corner(b, 1);\n    return ca !== cb ? ca - cb\n        : ca === 0 ? b[1] - a[1]\n        : ca === 1 ? a[0] - b[0]\n        : ca === 2 ? a[1] - b[1]\n        : b[0] - a[0];\n  }\n\n  return function(stream) {\n    var activeStream = stream,\n        bufferStream = clipBuffer(),\n        segments,\n        polygon,\n        ring,\n        x__, y__, v__, // first point\n        x_, y_, v_, // previous point\n        first,\n        clean;\n\n    var clipStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: polygonStart,\n      polygonEnd: polygonEnd\n    };\n\n    function point(x, y) {\n      if (visible(x, y)) activeStream.point(x, y);\n    }\n\n    function polygonInside() {\n      var winding = 0;\n\n      for (var i = 0, n = polygon.length; i < n; ++i) {\n        for (var ring = polygon[i], j = 1, m = ring.length, point = ring[0], a0, a1, b0 = point[0], b1 = point[1]; j < m; ++j) {\n          a0 = b0, a1 = b1, point = ring[j], b0 = point[0], b1 = point[1];\n          if (a1 <= y1) { if (b1 > y1 && (b0 - a0) * (y1 - a1) > (b1 - a1) * (x0 - a0)) ++winding; }\n          else { if (b1 <= y1 && (b0 - a0) * (y1 - a1) < (b1 - a1) * (x0 - a0)) --winding; }\n        }\n      }\n\n      return winding;\n    }\n\n    // Buffer geometry within a polygon and then clip it en masse.\n    function polygonStart() {\n      activeStream = bufferStream, segments = [], polygon = [], clean = true;\n    }\n\n    function polygonEnd() {\n      var startInside = polygonInside(),\n          cleanInside = clean && startInside,\n          visible = (segments = merge(segments)).length;\n      if (cleanInside || visible) {\n        stream.polygonStart();\n        if (cleanInside) {\n          stream.lineStart();\n          interpolate(null, null, 1, stream);\n          stream.lineEnd();\n        }\n        if (visible) {\n          clipRejoin(segments, compareIntersection, startInside, interpolate, stream);\n        }\n        stream.polygonEnd();\n      }\n      activeStream = stream, segments = polygon = ring = null;\n    }\n\n    function lineStart() {\n      clipStream.point = linePoint;\n      if (polygon) polygon.push(ring = []);\n      first = true;\n      v_ = false;\n      x_ = y_ = NaN;\n    }\n\n    // TODO rather than special-case polygons, simply handle them separately.\n    // Ideally, coincident intersection points should be jittered to avoid\n    // clipping issues.\n    function lineEnd() {\n      if (segments) {\n        linePoint(x__, y__);\n        if (v__ && v_) bufferStream.rejoin();\n        segments.push(bufferStream.result());\n      }\n      clipStream.point = point;\n      if (v_) activeStream.lineEnd();\n    }\n\n    function linePoint(x, y) {\n      var v = visible(x, y);\n      if (polygon) ring.push([x, y]);\n      if (first) {\n        x__ = x, y__ = y, v__ = v;\n        first = false;\n        if (v) {\n          activeStream.lineStart();\n          activeStream.point(x, y);\n        }\n      } else {\n        if (v && v_) activeStream.point(x, y);\n        else {\n          var a = [x_ = Math.max(clipMin, Math.min(clipMax, x_)), y_ = Math.max(clipMin, Math.min(clipMax, y_))],\n              b = [x = Math.max(clipMin, Math.min(clipMax, x)), y = Math.max(clipMin, Math.min(clipMax, y))];\n          if (clipLine(a, b, x0, y0, x1, y1)) {\n            if (!v_) {\n              activeStream.lineStart();\n              activeStream.point(a[0], a[1]);\n            }\n            activeStream.point(b[0], b[1]);\n            if (!v) activeStream.lineEnd();\n            clean = false;\n          } else if (v) {\n            activeStream.lineStart();\n            activeStream.point(x, y);\n            clean = false;\n          }\n        }\n      }\n      x_ = x, y_ = y, v_ = v;\n    }\n\n    return clipStream;\n  };\n}\n", "import clipRectangle from \"./rectangle.js\";\n\nexport default function() {\n  var x0 = 0,\n      y0 = 0,\n      x1 = 960,\n      y1 = 500,\n      cache,\n      cacheStream,\n      clip;\n\n  return clip = {\n    stream: function(stream) {\n      return cache && cacheStream === stream ? cache : cache = clipRectangle(x0, y0, x1, y1)(cacheStream = stream);\n    },\n    extent: function(_) {\n      return arguments.length ? (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1], cache = cacheStream = null, clip) : [[x0, y0], [x1, y1]];\n    }\n  };\n}\n", "import {Adder} from \"d3-array\";\nimport {abs, atan2, cos, radians, sin, sqrt} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nvar lengthSum,\n    lambda0,\n    sinPhi0,\n    cosPhi0;\n\nvar lengthStream = {\n  sphere: noop,\n  point: noop,\n  lineStart: lengthLineStart,\n  lineEnd: noop,\n  polygonStart: noop,\n  polygonEnd: noop\n};\n\nfunction lengthLineStart() {\n  lengthStream.point = lengthPointFirst;\n  lengthStream.lineEnd = lengthLineEnd;\n}\n\nfunction lengthLineEnd() {\n  lengthStream.point = lengthStream.lineEnd = noop;\n}\n\nfunction lengthPointFirst(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  lambda0 = lambda, sinPhi0 = sin(phi), cosPhi0 = cos(phi);\n  lengthStream.point = lengthPoint;\n}\n\nfunction lengthPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var sinPhi = sin(phi),\n      cosPhi = cos(phi),\n      delta = abs(lambda - lambda0),\n      cosDelta = cos(delta),\n      sinDelta = sin(delta),\n      x = cosPhi * sinDelta,\n      y = cosPhi0 * sinPhi - sinPhi0 * cosPhi * cosDelta,\n      z = sinPhi0 * sinPhi + cosPhi0 * cosPhi * cosDelta;\n  lengthSum.add(atan2(sqrt(x * x + y * y), z));\n  lambda0 = lambda, sinPhi0 = sinPhi, cosPhi0 = cosPhi;\n}\n\nexport default function(object) {\n  lengthSum = new Adder();\n  stream(object, lengthStream);\n  return +lengthSum;\n}\n", "import length from \"./length.js\";\n\nvar coordinates = [null, null],\n    object = {type: \"LineString\", coordinates: coordinates};\n\nexport default function(a, b) {\n  coordinates[0] = a;\n  coordinates[1] = b;\n  return length(object);\n}\n", "import {default as polygonContains} from \"./polygonContains.js\";\nimport {default as distance} from \"./distance.js\";\nimport {epsilon2, radians} from \"./math.js\";\n\nvar containsObjectType = {\n  Feature: function(object, point) {\n    return containsGeometry(object.geometry, point);\n  },\n  FeatureCollection: function(object, point) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) if (containsGeometry(features[i].geometry, point)) return true;\n    return false;\n  }\n};\n\nvar containsGeometryType = {\n  Sphere: function() {\n    return true;\n  },\n  Point: function(object, point) {\n    return containsPoint(object.coordinates, point);\n  },\n  MultiPoint: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsPoint(coordinates[i], point)) return true;\n    return false;\n  },\n  LineString: function(object, point) {\n    return containsLine(object.coordinates, point);\n  },\n  MultiLineString: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsLine(coordinates[i], point)) return true;\n    return false;\n  },\n  Polygon: function(object, point) {\n    return containsPolygon(object.coordinates, point);\n  },\n  MultiPolygon: function(object, point) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) if (containsPolygon(coordinates[i], point)) return true;\n    return false;\n  },\n  GeometryCollection: function(object, point) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) if (containsGeometry(geometries[i], point)) return true;\n    return false;\n  }\n};\n\nfunction containsGeometry(geometry, point) {\n  return geometry && containsGeometryType.hasOwnProperty(geometry.type)\n      ? containsGeometryType[geometry.type](geometry, point)\n      : false;\n}\n\nfunction containsPoint(coordinates, point) {\n  return distance(coordinates, point) === 0;\n}\n\nfunction containsLine(coordinates, point) {\n  var ao, bo, ab;\n  for (var i = 0, n = coordinates.length; i < n; i++) {\n    bo = distance(coordinates[i], point);\n    if (bo === 0) return true;\n    if (i > 0) {\n      ab = distance(coordinates[i], coordinates[i - 1]);\n      if (\n        ab > 0 &&\n        ao <= ab &&\n        bo <= ab &&\n        (ao + bo - ab) * (1 - Math.pow((ao - bo) / ab, 2)) < epsilon2 * ab\n      )\n        return true;\n    }\n    ao = bo;\n  }\n  return false;\n}\n\nfunction containsPolygon(coordinates, point) {\n  return !!polygonContains(coordinates.map(ringRadians), pointRadians(point));\n}\n\nfunction ringRadians(ring) {\n  return ring = ring.map(pointRadians), ring.pop(), ring;\n}\n\nfunction pointRadians(point) {\n  return [point[0] * radians, point[1] * radians];\n}\n\nexport default function(object, point) {\n  return (object && containsObjectType.hasOwnProperty(object.type)\n      ? containsObjectType[object.type]\n      : containsGeometry)(object, point);\n}\n", "import {range} from \"d3-array\";\nimport {abs, ceil, epsilon} from \"./math.js\";\n\nfunction graticuleX(y0, y1, dy) {\n  var y = range(y0, y1 - epsilon, dy).concat(y1);\n  return function(x) { return y.map(function(y) { return [x, y]; }); };\n}\n\nfunction graticuleY(x0, x1, dx) {\n  var x = range(x0, x1 - epsilon, dx).concat(x1);\n  return function(y) { return x.map(function(x) { return [x, y]; }); };\n}\n\nexport default function graticule() {\n  var x1, x0, X1, X0,\n      y1, y0, Y1, Y0,\n      dx = 10, dy = dx, DX = 90, DY = 360,\n      x, y, X, Y,\n      precision = 2.5;\n\n  function graticule() {\n    return {type: \"MultiLineString\", coordinates: lines()};\n  }\n\n  function lines() {\n    return range(ceil(X0 / DX) * DX, X1, DX).map(X)\n        .concat(range(ceil(Y0 / DY) * DY, Y1, DY).map(Y))\n        .concat(range(ceil(x0 / dx) * dx, x1, dx).filter(function(x) { return abs(x % DX) > epsilon; }).map(x))\n        .concat(range(ceil(y0 / dy) * dy, y1, dy).filter(function(y) { return abs(y % DY) > epsilon; }).map(y));\n  }\n\n  graticule.lines = function() {\n    return lines().map(function(coordinates) { return {type: \"LineString\", coordinates: coordinates}; });\n  };\n\n  graticule.outline = function() {\n    return {\n      type: \"Polygon\",\n      coordinates: [\n        X(X0).concat(\n        Y(Y1).slice(1),\n        X(X1).reverse().slice(1),\n        Y(Y0).reverse().slice(1))\n      ]\n    };\n  };\n\n  graticule.extent = function(_) {\n    if (!arguments.length) return graticule.extentMinor();\n    return graticule.extentMajor(_).extentMinor(_);\n  };\n\n  graticule.extentMajor = function(_) {\n    if (!arguments.length) return [[X0, Y0], [X1, Y1]];\n    X0 = +_[0][0], X1 = +_[1][0];\n    Y0 = +_[0][1], Y1 = +_[1][1];\n    if (X0 > X1) _ = X0, X0 = X1, X1 = _;\n    if (Y0 > Y1) _ = Y0, Y0 = Y1, Y1 = _;\n    return graticule.precision(precision);\n  };\n\n  graticule.extentMinor = function(_) {\n    if (!arguments.length) return [[x0, y0], [x1, y1]];\n    x0 = +_[0][0], x1 = +_[1][0];\n    y0 = +_[0][1], y1 = +_[1][1];\n    if (x0 > x1) _ = x0, x0 = x1, x1 = _;\n    if (y0 > y1) _ = y0, y0 = y1, y1 = _;\n    return graticule.precision(precision);\n  };\n\n  graticule.step = function(_) {\n    if (!arguments.length) return graticule.stepMinor();\n    return graticule.stepMajor(_).stepMinor(_);\n  };\n\n  graticule.stepMajor = function(_) {\n    if (!arguments.length) return [DX, DY];\n    DX = +_[0], DY = +_[1];\n    return graticule;\n  };\n\n  graticule.stepMinor = function(_) {\n    if (!arguments.length) return [dx, dy];\n    dx = +_[0], dy = +_[1];\n    return graticule;\n  };\n\n  graticule.precision = function(_) {\n    if (!arguments.length) return precision;\n    precision = +_;\n    x = graticuleX(y0, y1, 90);\n    y = graticuleY(x0, x1, precision);\n    X = graticuleX(Y0, Y1, 90);\n    Y = graticuleY(X0, X1, precision);\n    return graticule;\n  };\n\n  return graticule\n      .extentMajor([[-180, -90 + epsilon], [180, 90 - epsilon]])\n      .extentMinor([[-180, -80 - epsilon], [180, 80 + epsilon]]);\n}\n\nexport function graticule10() {\n  return graticule()();\n}\n", "import {asin, atan2, cos, degrees, haversin, radians, sin, sqrt} from \"./math.js\";\n\nexport default function(a, b) {\n  var x0 = a[0] * radians,\n      y0 = a[1] * radians,\n      x1 = b[0] * radians,\n      y1 = b[1] * radians,\n      cy0 = cos(y0),\n      sy0 = sin(y0),\n      cy1 = cos(y1),\n      sy1 = sin(y1),\n      kx0 = cy0 * cos(x0),\n      ky0 = cy0 * sin(x0),\n      kx1 = cy1 * cos(x1),\n      ky1 = cy1 * sin(x1),\n      d = 2 * asin(sqrt(haversin(y1 - y0) + cy0 * cy1 * haversin(x1 - x0))),\n      k = sin(d);\n\n  var interpolate = d ? function(t) {\n    var B = sin(t *= d) / k,\n        A = sin(d - t) / k,\n        x = A * kx0 + B * kx1,\n        y = A * ky0 + B * ky1,\n        z = A * sy0 + B * sy1;\n    return [\n      atan2(y, x) * degrees,\n      atan2(z, sqrt(x * x + y * y)) * degrees\n    ];\n  } : function() {\n    return [x0 * degrees, y0 * degrees];\n  };\n\n  interpolate.distance = d;\n\n  return interpolate;\n}\n", "export default x => x;\n", "import {Adder} from \"d3-array\";\nimport {abs} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nvar areaSum = new Adder(),\n    areaRingSum = new Adder(),\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar areaStream = {\n  point: noop,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: function() {\n    areaStream.lineStart = areaRingStart;\n    areaStream.lineEnd = areaRingEnd;\n  },\n  polygonEnd: function() {\n    areaStream.lineStart = areaStream.lineEnd = areaStream.point = noop;\n    areaSum.add(abs(areaRingSum));\n    areaRingSum = new Adder();\n  },\n  result: function() {\n    var area = areaSum / 2;\n    areaSum = new Adder();\n    return area;\n  }\n};\n\nfunction areaRingStart() {\n  areaStream.point = areaPointFirst;\n}\n\nfunction areaPointFirst(x, y) {\n  areaStream.point = areaPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\n\nfunction areaPoint(x, y) {\n  areaRingSum.add(y0 * x - x0 * y);\n  x0 = x, y0 = y;\n}\n\nfunction areaRingEnd() {\n  areaPoint(x00, y00);\n}\n\nexport default areaStream;\n", "import noop from \"../noop.js\";\n\nvar x0 = Infinity,\n    y0 = x0,\n    x1 = -x0,\n    y1 = x1;\n\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: noop,\n  polygonEnd: noop,\n  result: function() {\n    var bounds = [[x0, y0], [x1, y1]];\n    x1 = y1 = -(y0 = x0 = Infinity);\n    return bounds;\n  }\n};\n\nfunction boundsPoint(x, y) {\n  if (x < x0) x0 = x;\n  if (x > x1) x1 = x;\n  if (y < y0) y0 = y;\n  if (y > y1) y1 = y;\n}\n\nexport default boundsStream;\n", "import {sqrt} from \"../math.js\";\n\n// TODO Enforce positive area for exterior, negative area for interior?\n\nvar X0 = 0,\n    Y0 = 0,\n    Z0 = 0,\n    X1 = 0,\n    Y1 = 0,\n    Z1 = 0,\n    X2 = 0,\n    Y2 = 0,\n    Z2 = 0,\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar centroidStream = {\n  point: centroidPoint,\n  lineStart: centroidLineStart,\n  lineEnd: centroidLineEnd,\n  polygonStart: function() {\n    centroidStream.lineStart = centroidRingStart;\n    centroidStream.lineEnd = centroidRingEnd;\n  },\n  polygonEnd: function() {\n    centroidStream.point = centroidPoint;\n    centroidStream.lineStart = centroidLineStart;\n    centroidStream.lineEnd = centroidLineEnd;\n  },\n  result: function() {\n    var centroid = Z2 ? [X2 / Z2, Y2 / Z2]\n        : Z1 ? [X1 / Z1, Y1 / Z1]\n        : Z0 ? [X0 / Z0, Y0 / Z0]\n        : [NaN, NaN];\n    X0 = Y0 = Z0 =\n    X1 = Y1 = Z1 =\n    X2 = Y2 = Z2 = 0;\n    return centroid;\n  }\n};\n\nfunction centroidPoint(x, y) {\n  X0 += x;\n  Y0 += y;\n  ++Z0;\n}\n\nfunction centroidLineStart() {\n  centroidStream.point = centroidPointFirstLine;\n}\n\nfunction centroidPointFirstLine(x, y) {\n  centroidStream.point = centroidPointLine;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nfunction centroidPointLine(x, y) {\n  var dx = x - x0, dy = y - y0, z = sqrt(dx * dx + dy * dy);\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nfunction centroidLineEnd() {\n  centroidStream.point = centroidPoint;\n}\n\nfunction centroidRingStart() {\n  centroidStream.point = centroidPointFirstRing;\n}\n\nfunction centroidRingEnd() {\n  centroidPointRing(x00, y00);\n}\n\nfunction centroidPointFirstRing(x, y) {\n  centroidStream.point = centroidPointRing;\n  centroidPoint(x00 = x0 = x, y00 = y0 = y);\n}\n\nfunction centroidPointRing(x, y) {\n  var dx = x - x0,\n      dy = y - y0,\n      z = sqrt(dx * dx + dy * dy);\n\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n\n  z = y0 * x - x0 * y;\n  X2 += z * (x0 + x);\n  Y2 += z * (y0 + y);\n  Z2 += z * 3;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nexport default centroidStream;\n", "import {tau} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nexport default function PathContext(context) {\n  this._context = context;\n}\n\nPathContext.prototype = {\n  _radius: 4.5,\n  pointRadius: function(_) {\n    return this._radius = _, this;\n  },\n  polygonStart: function() {\n    this._line = 0;\n  },\n  polygonEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line === 0) this._context.closePath();\n    this._point = NaN;\n  },\n  point: function(x, y) {\n    switch (this._point) {\n      case 0: {\n        this._context.moveTo(x, y);\n        this._point = 1;\n        break;\n      }\n      case 1: {\n        this._context.lineTo(x, y);\n        break;\n      }\n      default: {\n        this._context.moveTo(x + this._radius, y);\n        this._context.arc(x, y, this._radius, 0, tau);\n        break;\n      }\n    }\n  },\n  result: noop\n};\n", "import {Adder} from \"d3-array\";\nimport {sqrt} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nvar lengthSum = new Adder(),\n    lengthRing,\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar lengthStream = {\n  point: noop,\n  lineStart: function() {\n    lengthStream.point = lengthPointFirst;\n  },\n  lineEnd: function() {\n    if (lengthRing) lengthPoint(x00, y00);\n    lengthStream.point = noop;\n  },\n  polygonStart: function() {\n    lengthRing = true;\n  },\n  polygonEnd: function() {\n    lengthRing = null;\n  },\n  result: function() {\n    var length = +lengthSum;\n    lengthSum = new Adder();\n    return length;\n  }\n};\n\nfunction lengthPointFirst(x, y) {\n  lengthStream.point = lengthPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\n\nfunction lengthPoint(x, y) {\n  x0 -= x, y0 -= y;\n  lengthSum.add(sqrt(x0 * x0 + y0 * y0));\n  x0 = x, y0 = y;\n}\n\nexport default lengthStream;\n", "// Simple caching for constant-radius points.\nlet cacheDigits, cacheAppend, cacheRadius, cacheCircle;\n\nexport default class PathString {\n  constructor(digits) {\n    this._append = digits == null ? append : appendRound(digits);\n    this._radius = 4.5;\n    this._ = \"\";\n  }\n  pointRadius(_) {\n    this._radius = +_;\n    return this;\n  }\n  polygonStart() {\n    this._line = 0;\n  }\n  polygonEnd() {\n    this._line = NaN;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {\n    if (this._line === 0) this._ += \"Z\";\n    this._point = NaN;\n  }\n  point(x, y) {\n    switch (this._point) {\n      case 0: {\n        this._append`M${x},${y}`;\n        this._point = 1;\n        break;\n      }\n      case 1: {\n        this._append`L${x},${y}`;\n        break;\n      }\n      default: {\n        this._append`M${x},${y}`;\n        if (this._radius !== cacheRadius || this._append !== cacheAppend) {\n          const r = this._radius;\n          const s = this._;\n          this._ = \"\"; // stash the old string so we can cache the circle path fragment\n          this._append`m0,${r}a${r},${r} 0 1,1 0,${-2 * r}a${r},${r} 0 1,1 0,${2 * r}z`;\n          cacheRadius = r;\n          cacheAppend = this._append;\n          cacheCircle = this._;\n          this._ = s;\n        }\n        this._ += cacheCircle;\n        break;\n      }\n    }\n  }\n  result() {\n    const result = this._;\n    this._ = \"\";\n    return result.length ? result : null;\n  }\n}\n\nfunction append(strings) {\n  let i = 1;\n  this._ += strings[0];\n  for (const j = strings.length; i < j; ++i) {\n    this._ += arguments[i] + strings[i];\n  }\n}\n\nfunction appendRound(digits) {\n  const d = Math.floor(digits);\n  if (!(d >= 0)) throw new RangeError(`invalid digits: ${digits}`);\n  if (d > 15) return append;\n  if (d !== cacheDigits) {\n    const k = 10 ** d;\n    cacheDigits = d;\n    cacheAppend = function append(strings) {\n      let i = 1;\n      this._ += strings[0];\n      for (const j = strings.length; i < j; ++i) {\n        this._ += Math.round(arguments[i] * k) / k + strings[i];\n      }\n    };\n  }\n  return cacheAppend;\n}\n", "import identity from \"../identity.js\";\nimport stream from \"../stream.js\";\nimport pathArea from \"./area.js\";\nimport pathBounds from \"./bounds.js\";\nimport pathCentroid from \"./centroid.js\";\nimport PathContext from \"./context.js\";\nimport pathMeasure from \"./measure.js\";\nimport PathString from \"./string.js\";\n\nexport default function(projection, context) {\n  let digits = 3,\n      pointRadius = 4.5,\n      projectionStream,\n      contextStream;\n\n  function path(object) {\n    if (object) {\n      if (typeof pointRadius === \"function\") contextStream.pointRadius(+pointRadius.apply(this, arguments));\n      stream(object, projectionStream(contextStream));\n    }\n    return contextStream.result();\n  }\n\n  path.area = function(object) {\n    stream(object, projectionStream(pathArea));\n    return pathArea.result();\n  };\n\n  path.measure = function(object) {\n    stream(object, projectionStream(pathMeasure));\n    return pathMeasure.result();\n  };\n\n  path.bounds = function(object) {\n    stream(object, projectionStream(pathBounds));\n    return pathBounds.result();\n  };\n\n  path.centroid = function(object) {\n    stream(object, projectionStream(pathCentroid));\n    return pathCentroid.result();\n  };\n\n  path.projection = function(_) {\n    if (!arguments.length) return projection;\n    projectionStream = _ == null ? (projection = null, identity) : (projection = _).stream;\n    return path;\n  };\n\n  path.context = function(_) {\n    if (!arguments.length) return context;\n    contextStream = _ == null ? (context = null, new PathString(digits)) : new PathContext(context = _);\n    if (typeof pointRadius !== \"function\") contextStream.pointRadius(pointRadius);\n    return path;\n  };\n\n  path.pointRadius = function(_) {\n    if (!arguments.length) return pointRadius;\n    pointRadius = typeof _ === \"function\" ? _ : (contextStream.pointRadius(+_), +_);\n    return path;\n  };\n\n  path.digits = function(_) {\n    if (!arguments.length) return digits;\n    if (_ == null) digits = null;\n    else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    if (context === null) contextStream = new PathString(digits);\n    return path;\n  };\n\n  return path.projection(projection).digits(digits).context(context);\n}\n", "export default function(methods) {\n  return {\n    stream: transformer(methods)\n  };\n}\n\nexport function transformer(methods) {\n  return function(stream) {\n    var s = new TransformStream;\n    for (var key in methods) s[key] = methods[key];\n    s.stream = stream;\n    return s;\n  };\n}\n\nfunction TransformStream() {}\n\nTransformStream.prototype = {\n  constructor: TransformStream,\n  point: function(x, y) { this.stream.point(x, y); },\n  sphere: function() { this.stream.sphere(); },\n  lineStart: function() { this.stream.lineStart(); },\n  lineEnd: function() { this.stream.lineEnd(); },\n  polygonStart: function() { this.stream.polygonStart(); },\n  polygonEnd: function() { this.stream.polygonEnd(); }\n};\n", "import {default as geoStream} from \"../stream.js\";\nimport boundsStream from \"../path/bounds.js\";\n\nfunction fit(projection, fitBounds, object) {\n  var clip = projection.clipExtent && projection.clipExtent();\n  projection.scale(150).translate([0, 0]);\n  if (clip != null) projection.clipExtent(null);\n  geoStream(object, projection.stream(boundsStream));\n  fitBounds(boundsStream.result());\n  if (clip != null) projection.clipExtent(clip);\n  return projection;\n}\n\nexport function fitExtent(projection, extent, object) {\n  return fit(projection, function(b) {\n    var w = extent[1][0] - extent[0][0],\n        h = extent[1][1] - extent[0][1],\n        k = Math.min(w / (b[1][0] - b[0][0]), h / (b[1][1] - b[0][1])),\n        x = +extent[0][0] + (w - k * (b[1][0] + b[0][0])) / 2,\n        y = +extent[0][1] + (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nexport function fitSize(projection, size, object) {\n  return fitExtent(projection, [[0, 0], size], object);\n}\n\nexport function fitWidth(projection, width, object) {\n  return fit(projection, function(b) {\n    var w = +width,\n        k = w / (b[1][0] - b[0][0]),\n        x = (w - k * (b[1][0] + b[0][0])) / 2,\n        y = -k * b[0][1];\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nexport function fitHeight(projection, height, object) {\n  return fit(projection, function(b) {\n    var h = +height,\n        k = h / (b[1][1] - b[0][1]),\n        x = -k * b[0][0],\n        y = (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n", "import {cartesian} from \"../cartesian.js\";\nimport {abs, asin, atan2, cos, epsilon, radians, sqrt} from \"../math.js\";\nimport {transformer} from \"../transform.js\";\n\nvar maxDepth = 16, // maximum depth of subdivision\n    cosMinDistance = cos(30 * radians); // cos(minimum angular distance)\n\nexport default function(project, delta2) {\n  return +delta2 ? resample(project, delta2) : resampleNone(project);\n}\n\nfunction resampleNone(project) {\n  return transformer({\n    point: function(x, y) {\n      x = project(x, y);\n      this.stream.point(x[0], x[1]);\n    }\n  });\n}\n\nfunction resample(project, delta2) {\n\n  function resampleLineTo(x0, y0, lambda0, a0, b0, c0, x1, y1, lambda1, a1, b1, c1, depth, stream) {\n    var dx = x1 - x0,\n        dy = y1 - y0,\n        d2 = dx * dx + dy * dy;\n    if (d2 > 4 * delta2 && depth--) {\n      var a = a0 + a1,\n          b = b0 + b1,\n          c = c0 + c1,\n          m = sqrt(a * a + b * b + c * c),\n          phi2 = asin(c /= m),\n          lambda2 = abs(abs(c) - 1) < epsilon || abs(lambda0 - lambda1) < epsilon ? (lambda0 + lambda1) / 2 : atan2(b, a),\n          p = project(lambda2, phi2),\n          x2 = p[0],\n          y2 = p[1],\n          dx2 = x2 - x0,\n          dy2 = y2 - y0,\n          dz = dy * dx2 - dx * dy2;\n      if (dz * dz / d2 > delta2 // perpendicular projected distance\n          || abs((dx * dx2 + dy * dy2) / d2 - 0.5) > 0.3 // midpoint close to an end\n          || a0 * a1 + b0 * b1 + c0 * c1 < cosMinDistance) { // angular distance\n        resampleLineTo(x0, y0, lambda0, a0, b0, c0, x2, y2, lambda2, a /= m, b /= m, c, depth, stream);\n        stream.point(x2, y2);\n        resampleLineTo(x2, y2, lambda2, a, b, c, x1, y1, lambda1, a1, b1, c1, depth, stream);\n      }\n    }\n  }\n  return function(stream) {\n    var lambda00, x00, y00, a00, b00, c00, // first point\n        lambda0, x0, y0, a0, b0, c0; // previous point\n\n    var resampleStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() { stream.polygonStart(); resampleStream.lineStart = ringStart; },\n      polygonEnd: function() { stream.polygonEnd(); resampleStream.lineStart = lineStart; }\n    };\n\n    function point(x, y) {\n      x = project(x, y);\n      stream.point(x[0], x[1]);\n    }\n\n    function lineStart() {\n      x0 = NaN;\n      resampleStream.point = linePoint;\n      stream.lineStart();\n    }\n\n    function linePoint(lambda, phi) {\n      var c = cartesian([lambda, phi]), p = project(lambda, phi);\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x0 = p[0], y0 = p[1], lambda0 = lambda, a0 = c[0], b0 = c[1], c0 = c[2], maxDepth, stream);\n      stream.point(x0, y0);\n    }\n\n    function lineEnd() {\n      resampleStream.point = point;\n      stream.lineEnd();\n    }\n\n    function ringStart() {\n      lineStart();\n      resampleStream.point = ringPoint;\n      resampleStream.lineEnd = ringEnd;\n    }\n\n    function ringPoint(lambda, phi) {\n      linePoint(lambda00 = lambda, phi), x00 = x0, y00 = y0, a00 = a0, b00 = b0, c00 = c0;\n      resampleStream.point = linePoint;\n    }\n\n    function ringEnd() {\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x00, y00, lambda00, a00, b00, c00, maxDepth, stream);\n      resampleStream.lineEnd = lineEnd;\n      lineEnd();\n    }\n\n    return resampleStream;\n  };\n}\n", "import clipAntimeridian from \"../clip/antimeridian.js\";\nimport clipCircle from \"../clip/circle.js\";\nimport clipRectangle from \"../clip/rectangle.js\";\nimport compose from \"../compose.js\";\nimport identity from \"../identity.js\";\nimport {cos, degrees, radians, sin, sqrt} from \"../math.js\";\nimport {rotateRadians} from \"../rotation.js\";\nimport {transformer} from \"../transform.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\nimport resample from \"./resample.js\";\n\nvar transformRadians = transformer({\n  point: function(x, y) {\n    this.stream.point(x * radians, y * radians);\n  }\n});\n\nfunction transformRotate(rotate) {\n  return transformer({\n    point: function(x, y) {\n      var r = rotate(x, y);\n      return this.stream.point(r[0], r[1]);\n    }\n  });\n}\n\nfunction scaleTranslate(k, dx, dy, sx, sy) {\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [dx + k * x, dy - k * y];\n  }\n  transform.invert = function(x, y) {\n    return [(x - dx) / k * sx, (dy - y) / k * sy];\n  };\n  return transform;\n}\n\nfunction scaleTranslateRotate(k, dx, dy, sx, sy, alpha) {\n  if (!alpha) return scaleTranslate(k, dx, dy, sx, sy);\n  var cosAlpha = cos(alpha),\n      sinAlpha = sin(alpha),\n      a = cosAlpha * k,\n      b = sinAlpha * k,\n      ai = cosAlpha / k,\n      bi = sinAlpha / k,\n      ci = (sinAlpha * dy - cosAlpha * dx) / k,\n      fi = (sinAlpha * dx + cosAlpha * dy) / k;\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [a * x - b * y + dx, dy - b * x - a * y];\n  }\n  transform.invert = function(x, y) {\n    return [sx * (ai * x - bi * y + ci), sy * (fi - bi * x - ai * y)];\n  };\n  return transform;\n}\n\nexport default function projection(project) {\n  return projectionMutator(function() { return project; })();\n}\n\nexport function projectionMutator(projectAt) {\n  var project,\n      k = 150, // scale\n      x = 480, y = 250, // translate\n      lambda = 0, phi = 0, // center\n      deltaLambda = 0, deltaPhi = 0, deltaGamma = 0, rotate, // pre-rotate\n      alpha = 0, // post-rotate angle\n      sx = 1, // reflectX\n      sy = 1, // reflectX\n      theta = null, preclip = clipAntimeridian, // pre-clip angle\n      x0 = null, y0, x1, y1, postclip = identity, // post-clip extent\n      delta2 = 0.5, // precision\n      projectResample,\n      projectTransform,\n      projectRotateTransform,\n      cache,\n      cacheStream;\n\n  function projection(point) {\n    return projectRotateTransform(point[0] * radians, point[1] * radians);\n  }\n\n  function invert(point) {\n    point = projectRotateTransform.invert(point[0], point[1]);\n    return point && [point[0] * degrees, point[1] * degrees];\n  }\n\n  projection.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = transformRadians(transformRotate(rotate)(preclip(projectResample(postclip(cacheStream = stream)))));\n  };\n\n  projection.preclip = function(_) {\n    return arguments.length ? (preclip = _, theta = undefined, reset()) : preclip;\n  };\n\n  projection.postclip = function(_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n\n  projection.clipAngle = function(_) {\n    return arguments.length ? (preclip = +_ ? clipCircle(theta = _ * radians) : (theta = null, clipAntimeridian), reset()) : theta * degrees;\n  };\n\n  projection.clipExtent = function(_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, identity) : clipRectangle(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n\n  projection.scale = function(_) {\n    return arguments.length ? (k = +_, recenter()) : k;\n  };\n\n  projection.translate = function(_) {\n    return arguments.length ? (x = +_[0], y = +_[1], recenter()) : [x, y];\n  };\n\n  projection.center = function(_) {\n    return arguments.length ? (lambda = _[0] % 360 * radians, phi = _[1] % 360 * radians, recenter()) : [lambda * degrees, phi * degrees];\n  };\n\n  projection.rotate = function(_) {\n    return arguments.length ? (deltaLambda = _[0] % 360 * radians, deltaPhi = _[1] % 360 * radians, deltaGamma = _.length > 2 ? _[2] % 360 * radians : 0, recenter()) : [deltaLambda * degrees, deltaPhi * degrees, deltaGamma * degrees];\n  };\n\n  projection.angle = function(_) {\n    return arguments.length ? (alpha = _ % 360 * radians, recenter()) : alpha * degrees;\n  };\n\n  projection.reflectX = function(_) {\n    return arguments.length ? (sx = _ ? -1 : 1, recenter()) : sx < 0;\n  };\n\n  projection.reflectY = function(_) {\n    return arguments.length ? (sy = _ ? -1 : 1, recenter()) : sy < 0;\n  };\n\n  projection.precision = function(_) {\n    return arguments.length ? (projectResample = resample(projectTransform, delta2 = _ * _), reset()) : sqrt(delta2);\n  };\n\n  projection.fitExtent = function(extent, object) {\n    return fitExtent(projection, extent, object);\n  };\n\n  projection.fitSize = function(size, object) {\n    return fitSize(projection, size, object);\n  };\n\n  projection.fitWidth = function(width, object) {\n    return fitWidth(projection, width, object);\n  };\n\n  projection.fitHeight = function(height, object) {\n    return fitHeight(projection, height, object);\n  };\n\n  function recenter() {\n    var center = scaleTranslateRotate(k, 0, 0, sx, sy, alpha).apply(null, project(lambda, phi)),\n        transform = scaleTranslateRotate(k, x - center[0], y - center[1], sx, sy, alpha);\n    rotate = rotateRadians(deltaLambda, deltaPhi, deltaGamma);\n    projectTransform = compose(project, transform);\n    projectRotateTransform = compose(rotate, projectTransform);\n    projectResample = resample(projectTransform, delta2);\n    return reset();\n  }\n\n  function reset() {\n    cache = cacheStream = null;\n    return projection;\n  }\n\n  return function() {\n    project = projectAt.apply(this, arguments);\n    projection.invert = project.invert && invert;\n    return recenter();\n  };\n}\n", "import {degrees, pi, radians} from \"../math.js\";\nimport {projectionMutator} from \"./index.js\";\n\nexport function conicProjection(projectAt) {\n  var phi0 = 0,\n      phi1 = pi / 3,\n      m = projectionMutator(projectAt),\n      p = m(phi0, phi1);\n\n  p.parallels = function(_) {\n    return arguments.length ? m(phi0 = _[0] * radians, phi1 = _[1] * radians) : [phi0 * degrees, phi1 * degrees];\n  };\n\n  return p;\n}\n", "import {asin, cos, sin} from \"../math.js\";\n\nexport function cylindricalEqualAreaRaw(phi0) {\n  var cosPhi0 = cos(phi0);\n\n  function forward(lambda, phi) {\n    return [lambda * cosPhi0, sin(phi) / cosPhi0];\n  }\n\n  forward.invert = function(x, y) {\n    return [x / cosPhi0, asin(y * cosPhi0)];\n  };\n\n  return forward;\n}\n", "import {abs, asin, atan2, cos, epsilon, pi, sign, sin, sqrt} from \"../math.js\";\nimport {conicProjection} from \"./conic.js\";\nimport {cylindricalEqualAreaRaw} from \"./cylindricalEqualArea.js\";\n\nexport function conicEqualAreaRaw(y0, y1) {\n  var sy0 = sin(y0), n = (sy0 + sin(y1)) / 2;\n\n  // Are the parallels symmetrical around the Equator?\n  if (abs(n) < epsilon) return cylindricalEqualAreaRaw(y0);\n\n  var c = 1 + sy0 * (2 * n - sy0), r0 = sqrt(c) / n;\n\n  function project(x, y) {\n    var r = sqrt(c - 2 * n * sin(y)) / n;\n    return [r * sin(x *= n), r0 - r * cos(x)];\n  }\n\n  project.invert = function(x, y) {\n    var r0y = r0 - y,\n        l = atan2(x, abs(r0y)) * sign(r0y);\n    if (r0y * n < 0)\n      l -= pi * sign(x) * sign(r0y);\n    return [l / n, asin((c - (x * x + r0y * r0y) * n * n) / (2 * n))];\n  };\n\n  return project;\n}\n\nexport default function() {\n  return conicProjection(conicEqualAreaRaw)\n      .scale(155.424)\n      .center([0, 33.6442]);\n}\n", "import conicEqualArea from \"./conicEqualArea.js\";\n\nexport default function() {\n  return conicEqualArea()\n      .parallels([29.5, 45.5])\n      .scale(1070)\n      .translate([480, 250])\n      .rotate([96, 0])\n      .center([-0.6, 38.7]);\n}\n", "import {epsilon} from \"../math.js\";\nimport albers from \"./albers.js\";\nimport conicEqualArea from \"./conicEqualArea.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\n\n// The projections must have mutually exclusive clip regions on the sphere,\n// as this will avoid emitting interleaving lines and polygons.\nfunction multiplex(streams) {\n  var n = streams.length;\n  return {\n    point: function(x, y) { var i = -1; while (++i < n) streams[i].point(x, y); },\n    sphere: function() { var i = -1; while (++i < n) streams[i].sphere(); },\n    lineStart: function() { var i = -1; while (++i < n) streams[i].lineStart(); },\n    lineEnd: function() { var i = -1; while (++i < n) streams[i].lineEnd(); },\n    polygonStart: function() { var i = -1; while (++i < n) streams[i].polygonStart(); },\n    polygonEnd: function() { var i = -1; while (++i < n) streams[i].polygonEnd(); }\n  };\n}\n\n// A composite projection for the United States, configured by default for\n// 960×500. The projection also works quite well at 960×600 if you change the\n// scale to 1285 and adjust the translate accordingly. The set of standard\n// parallels for each region comes from USGS, which is published here:\n// http://egsc.usgs.gov/isb/pubs/MapProjections/projections.html#albers\nexport default function() {\n  var cache,\n      cacheStream,\n      lower48 = albers(), lower48Point,\n      alaska = conicEqualArea().rotate([154, 0]).center([-2, 58.5]).parallels([55, 65]), alaskaPoint, // EPSG:3338\n      hawaii = conicEqualArea().rotate([157, 0]).center([-3, 19.9]).parallels([8, 18]), hawaiiPoint, // ESRI:102007\n      point, pointStream = {point: function(x, y) { point = [x, y]; }};\n\n  function albersUsa(coordinates) {\n    var x = coordinates[0], y = coordinates[1];\n    return point = null,\n        (lower48Point.point(x, y), point)\n        || (alaskaPoint.point(x, y), point)\n        || (hawaiiPoint.point(x, y), point);\n  }\n\n  albersUsa.invert = function(coordinates) {\n    var k = lower48.scale(),\n        t = lower48.translate(),\n        x = (coordinates[0] - t[0]) / k,\n        y = (coordinates[1] - t[1]) / k;\n    return (y >= 0.120 && y < 0.234 && x >= -0.425 && x < -0.214 ? alaska\n        : y >= 0.166 && y < 0.234 && x >= -0.214 && x < -0.115 ? hawaii\n        : lower48).invert(coordinates);\n  };\n\n  albersUsa.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = multiplex([lower48.stream(cacheStream = stream), alaska.stream(stream), hawaii.stream(stream)]);\n  };\n\n  albersUsa.precision = function(_) {\n    if (!arguments.length) return lower48.precision();\n    lower48.precision(_), alaska.precision(_), hawaii.precision(_);\n    return reset();\n  };\n\n  albersUsa.scale = function(_) {\n    if (!arguments.length) return lower48.scale();\n    lower48.scale(_), alaska.scale(_ * 0.35), hawaii.scale(_);\n    return albersUsa.translate(lower48.translate());\n  };\n\n  albersUsa.translate = function(_) {\n    if (!arguments.length) return lower48.translate();\n    var k = lower48.scale(), x = +_[0], y = +_[1];\n\n    lower48Point = lower48\n        .translate(_)\n        .clipExtent([[x - 0.455 * k, y - 0.238 * k], [x + 0.455 * k, y + 0.238 * k]])\n        .stream(pointStream);\n\n    alaskaPoint = alaska\n        .translate([x - 0.307 * k, y + 0.201 * k])\n        .clipExtent([[x - 0.425 * k + epsilon, y + 0.120 * k + epsilon], [x - 0.214 * k - epsilon, y + 0.234 * k - epsilon]])\n        .stream(pointStream);\n\n    hawaiiPoint = hawaii\n        .translate([x - 0.205 * k, y + 0.212 * k])\n        .clipExtent([[x - 0.214 * k + epsilon, y + 0.166 * k + epsilon], [x - 0.115 * k - epsilon, y + 0.234 * k - epsilon]])\n        .stream(pointStream);\n\n    return reset();\n  };\n\n  albersUsa.fitExtent = function(extent, object) {\n    return fitExtent(albersUsa, extent, object);\n  };\n\n  albersUsa.fitSize = function(size, object) {\n    return fitSize(albersUsa, size, object);\n  };\n\n  albersUsa.fitWidth = function(width, object) {\n    return fitWidth(albersUsa, width, object);\n  };\n\n  albersUsa.fitHeight = function(height, object) {\n    return fitHeight(albersUsa, height, object);\n  };\n\n  function reset() {\n    cache = cacheStream = null;\n    return albersUsa;\n  }\n\n  return albersUsa.scale(1070);\n}\n", "import {asin, atan2, cos, sin, sqrt} from \"../math.js\";\n\nexport function azimuthalRaw(scale) {\n  return function(x, y) {\n    var cx = cos(x),\n        cy = cos(y),\n        k = scale(cx * cy);\n        if (k === Infinity) return [2, 0];\n    return [\n      k * cy * sin(x),\n      k * sin(y)\n    ];\n  }\n}\n\nexport function azimuthalInvert(angle) {\n  return function(x, y) {\n    var z = sqrt(x * x + y * y),\n        c = angle(z),\n        sc = sin(c),\n        cc = cos(c);\n    return [\n      atan2(x * sc, z * cc),\n      asin(z && y * sc / z)\n    ];\n  }\n}\n", "import {asin, sqrt} from \"../math.js\";\nimport {azimuthalRaw, azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport var azimuthalEqualAreaRaw = azimuthalRaw(function(cxcy) {\n  return sqrt(2 / (1 + cxcy));\n});\n\nazimuthalEqualAreaRaw.invert = azimuthalInvert(function(z) {\n  return 2 * asin(z / 2);\n});\n\nexport default function() {\n  return projection(azimuthalEqualAreaRaw)\n      .scale(124.75)\n      .clipAngle(180 - 1e-3);\n}\n", "import {acos, sin} from \"../math.js\";\nimport {azimuthalRaw, azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport var azimuthalEquidistantRaw = azimuthalRaw(function(c) {\n  return (c = acos(c)) && c / sin(c);\n});\n\nazimuthalEquidistantRaw.invert = azimuthalInvert(function(z) {\n  return z;\n});\n\nexport default function() {\n  return projection(azimuthalEquidistantRaw)\n      .scale(79.4188)\n      .clipAngle(180 - 1e-3);\n}\n", "import {atan, exp, halfPi, log, pi, tan, tau} from \"../math.js\";\nimport rotation from \"../rotation.js\";\nimport projection from \"./index.js\";\n\nexport function mercatorRaw(lambda, phi) {\n  return [lambda, log(tan((halfPi + phi) / 2))];\n}\n\nmercatorRaw.invert = function(x, y) {\n  return [x, 2 * atan(exp(y)) - halfPi];\n};\n\nexport default function() {\n  return mercatorProjection(mercatorRaw)\n      .scale(961 / tau);\n}\n\nexport function mercatorProjection(project) {\n  var m = projection(project),\n      center = m.center,\n      scale = m.scale,\n      translate = m.translate,\n      clipExtent = m.clipExtent,\n      x0 = null, y0, x1, y1; // clip extent\n\n  m.scale = function(_) {\n    return arguments.length ? (scale(_), reclip()) : scale();\n  };\n\n  m.translate = function(_) {\n    return arguments.length ? (translate(_), reclip()) : translate();\n  };\n\n  m.center = function(_) {\n    return arguments.length ? (center(_), reclip()) : center();\n  };\n\n  m.clipExtent = function(_) {\n    return arguments.length ? ((_ == null ? x0 = y0 = x1 = y1 = null : (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1])), reclip()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n\n  function reclip() {\n    var k = pi * scale(),\n        t = m(rotation(m.rotate()).invert([0, 0]));\n    return clipExtent(x0 == null\n        ? [[t[0] - k, t[1] - k], [t[0] + k, t[1] + k]] : project === mercatorRaw\n        ? [[Math.max(t[0] - k, x0), y0], [Math.min(t[0] + k, x1), y1]]\n        : [[x0, Math.max(t[1] - k, y0)], [x1, Math.min(t[1] + k, y1)]]);\n  }\n\n  return reclip();\n}\n", "import {abs, atan, atan2, cos, epsilon, halfPi, log, pi, pow, sign, sin, sqrt, tan} from \"../math.js\";\nimport {conicProjection} from \"./conic.js\";\nimport {mercatorRaw} from \"./mercator.js\";\n\nfunction tany(y) {\n  return tan((halfPi + y) / 2);\n}\n\nexport function conicConformalRaw(y0, y1) {\n  var cy0 = cos(y0),\n      n = y0 === y1 ? sin(y0) : log(cy0 / cos(y1)) / log(tany(y1) / tany(y0)),\n      f = cy0 * pow(tany(y0), n) / n;\n\n  if (!n) return mercatorRaw;\n\n  function project(x, y) {\n    if (f > 0) { if (y < -halfPi + epsilon) y = -halfPi + epsilon; }\n    else { if (y > halfPi - epsilon) y = halfPi - epsilon; }\n    var r = f / pow(tany(y), n);\n    return [r * sin(n * x), f - r * cos(n * x)];\n  }\n\n  project.invert = function(x, y) {\n    var fy = f - y, r = sign(n) * sqrt(x * x + fy * fy),\n      l = atan2(x, abs(fy)) * sign(fy);\n    if (fy * n < 0)\n      l -= pi * sign(x) * sign(fy);\n    return [l / n, 2 * atan(pow(f / r, 1 / n)) - halfPi];\n  };\n\n  return project;\n}\n\nexport default function() {\n  return conicProjection(conicConformalRaw)\n      .scale(109.5)\n      .parallels([30, 30]);\n}\n", "import projection from \"./index.js\";\n\nexport function equirectangularRaw(lambda, phi) {\n  return [lambda, phi];\n}\n\nequirectangularRaw.invert = equirectangularRaw;\n\nexport default function() {\n  return projection(equirectangularRaw)\n      .scale(152.63);\n}\n", "import {abs, atan2, cos, epsilon, pi, sign, sin, sqrt} from \"../math.js\";\nimport {conicProjection} from \"./conic.js\";\nimport {equirectangularRaw} from \"./equirectangular.js\";\n\nexport function conicEquidistantRaw(y0, y1) {\n  var cy0 = cos(y0),\n      n = y0 === y1 ? sin(y0) : (cy0 - cos(y1)) / (y1 - y0),\n      g = cy0 / n + y0;\n\n  if (abs(n) < epsilon) return equirectangularRaw;\n\n  function project(x, y) {\n    var gy = g - y, nx = n * x;\n    return [gy * sin(nx), g - gy * cos(nx)];\n  }\n\n  project.invert = function(x, y) {\n    var gy = g - y,\n        l = atan2(x, abs(gy)) * sign(gy);\n    if (gy * n < 0)\n      l -= pi * sign(x) * sign(gy);\n    return [l / n, g - sign(n) * sqrt(x * x + gy * gy)];\n  };\n\n  return project;\n}\n\nexport default function() {\n  return conicProjection(conicEquidistantRaw)\n      .scale(131.154)\n      .center([0, 13.9389]);\n}\n", "import projection from \"./index.js\";\nimport {abs, asin, cos, epsilon2, sin, sqrt} from \"../math.js\";\n\nvar A1 = 1.340264,\n    A2 = -0.081106,\n    A3 = 0.000893,\n    A4 = 0.003796,\n    M = sqrt(3) / 2,\n    iterations = 12;\n\nexport function equalEarthRaw(lambda, phi) {\n  var l = asin(M * sin(phi)), l2 = l * l, l6 = l2 * l2 * l2;\n  return [\n    lambda * cos(l) / (M * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2))),\n    l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2))\n  ];\n}\n\nequalEarthRaw.invert = function(x, y) {\n  var l = y, l2 = l * l, l6 = l2 * l2 * l2;\n  for (var i = 0, delta, fy, fpy; i < iterations; ++i) {\n    fy = l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2)) - y;\n    fpy = A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2);\n    l -= delta = fy / fpy, l2 = l * l, l6 = l2 * l2 * l2;\n    if (abs(delta) < epsilon2) break;\n  }\n  return [\n    M * x * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2)) / cos(l),\n    asin(sin(l) / M)\n  ];\n};\n\nexport default function() {\n  return projection(equalEarthRaw)\n      .scale(177.158);\n}\n", "import {atan, cos, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function gnomonicRaw(x, y) {\n  var cy = cos(y), k = cos(x) * cy;\n  return [cy * sin(x) / k, sin(y) / k];\n}\n\ngnomonicRaw.invert = azimuthalInvert(atan);\n\nexport default function() {\n  return projection(gnomonicRaw)\n      .scale(144.049)\n      .clipAngle(60);\n}\n", "import clipRectangle from \"../clip/rectangle.js\";\nimport identity from \"../identity.js\";\nimport {transformer} from \"../transform.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\nimport {cos, degrees, radians, sin} from \"../math.js\";\n\nexport default function() {\n  var k = 1, tx = 0, ty = 0, sx = 1, sy = 1, // scale, translate and reflect\n      alpha = 0, ca, sa, // angle\n      x0 = null, y0, x1, y1, // clip extent\n      kx = 1, ky = 1,\n      transform = transformer({\n        point: function(x, y) {\n          var p = projection([x, y])\n          this.stream.point(p[0], p[1]);\n        }\n      }),\n      postclip = identity,\n      cache,\n      cacheStream;\n\n  function reset() {\n    kx = k * sx;\n    ky = k * sy;\n    cache = cacheStream = null;\n    return projection;\n  }\n\n  function projection (p) {\n    var x = p[0] * kx, y = p[1] * ky;\n    if (alpha) {\n      var t = y * ca - x * sa;\n      x = x * ca + y * sa;\n      y = t;\n    }    \n    return [x + tx, y + ty];\n  }\n  projection.invert = function(p) {\n    var x = p[0] - tx, y = p[1] - ty;\n    if (alpha) {\n      var t = y * ca + x * sa;\n      x = x * ca - y * sa;\n      y = t;\n    }\n    return [x / kx, y / ky];\n  };\n  projection.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = transform(postclip(cacheStream = stream));\n  };\n  projection.postclip = function(_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n  projection.clipExtent = function(_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, identity) : clipRectangle(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n  projection.scale = function(_) {\n    return arguments.length ? (k = +_, reset()) : k;\n  };\n  projection.translate = function(_) {\n    return arguments.length ? (tx = +_[0], ty = +_[1], reset()) : [tx, ty];\n  }\n  projection.angle = function(_) {\n    return arguments.length ? (alpha = _ % 360 * radians, sa = sin(alpha), ca = cos(alpha), reset()) : alpha * degrees;\n  };\n  projection.reflectX = function(_) {\n    return arguments.length ? (sx = _ ? -1 : 1, reset()) : sx < 0;\n  };\n  projection.reflectY = function(_) {\n    return arguments.length ? (sy = _ ? -1 : 1, reset()) : sy < 0;\n  };\n  projection.fitExtent = function(extent, object) {\n    return fitExtent(projection, extent, object);\n  };\n  projection.fitSize = function(size, object) {\n    return fitSize(projection, size, object);\n  };\n  projection.fitWidth = function(width, object) {\n    return fitWidth(projection, width, object);\n  };\n  projection.fitHeight = function(height, object) {\n    return fitHeight(projection, height, object);\n  };\n\n  return projection;\n}\n", "import projection from \"./index.js\";\nimport {abs, epsilon} from \"../math.js\";\n\nexport function naturalEarth1Raw(lambda, phi) {\n  var phi2 = phi * phi, phi4 = phi2 * phi2;\n  return [\n    lambda * (0.8707 - 0.131979 * phi2 + phi4 * (-0.013791 + phi4 * (0.003971 * phi2 - 0.001529 * phi4))),\n    phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4)))\n  ];\n}\n\nnaturalEarth1Raw.invert = function(x, y) {\n  var phi = y, i = 25, delta;\n  do {\n    var phi2 = phi * phi, phi4 = phi2 * phi2;\n    phi -= delta = (phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4))) - y) /\n        (1.007226 + phi2 * (0.015085 * 3 + phi4 * (-0.044475 * 7 + 0.028874 * 9 * phi2 - 0.005916 * 11 * phi4)));\n  } while (abs(delta) > epsilon && --i > 0);\n  return [\n    x / (0.8707 + (phi2 = phi * phi) * (-0.131979 + phi2 * (-0.013791 + phi2 * phi2 * phi2 * (0.003971 - 0.001529 * phi2)))),\n    phi\n  ];\n};\n\nexport default function() {\n  return projection(naturalEarth1Raw)\n      .scale(175.295);\n}\n", "import {asin, cos, epsilon, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function orthographicRaw(x, y) {\n  return [cos(y) * sin(x), sin(y)];\n}\n\northographicRaw.invert = azimuthalInvert(asin);\n\nexport default function() {\n  return projection(orthographicRaw)\n      .scale(249.5)\n      .clipAngle(90 + epsilon);\n}\n", "import {atan, cos, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function stereographicRaw(x, y) {\n  var cy = cos(y), k = 1 + cos(x) * cy;\n  return [cy * sin(x) / k, sin(y) / k];\n}\n\nstereographicRaw.invert = azimuthalInvert(function(z) {\n  return 2 * atan(z);\n});\n\nexport default function() {\n  return projection(stereographicRaw)\n      .scale(250)\n      .clipAngle(142);\n}\n", "import {atan, exp, halfPi, log, tan} from \"../math.js\";\nimport {mercatorProjection} from \"./mercator.js\";\n\nexport function transverseMercatorRaw(lambda, phi) {\n  return [log(tan((halfPi + phi) / 2)), -lambda];\n}\n\ntransverseMercatorRaw.invert = function(x, y) {\n  return [-y, 2 * atan(exp(x)) - halfPi];\n};\n\nexport default function() {\n  var m = mercatorProjection(transverseMercatorRaw),\n      center = m.center,\n      rotate = m.rotate;\n\n  m.center = function(_) {\n    return arguments.length ? center([-_[1], _[0]]) : (_ = center(), [_[1], -_[0]]);\n  };\n\n  m.rotate = function(_) {\n    return arguments.length ? rotate([_[0], _[1], _.length > 2 ? _[2] + 90 : 90]) : (_ = rotate(), [_[0], _[1], _[2] - 90]);\n  };\n\n  return rotate([0, 0, 90])\n      .scale(159.155);\n}\n"], "mappings": ";;;;;;;AAAO,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,KAAK,KAAK;AACd,IAAI,SAAS,KAAK;AAClB,IAAI,YAAY,KAAK;AACrB,IAAI,MAAM,KAAK;AAEf,IAAI,UAAU,MAAM;AACpB,IAAI,UAAU,KAAK;AAEnB,IAAI,MAAM,KAAK;AACf,IAAI,OAAO,KAAK;AAChB,IAAI,QAAQ,KAAK;AACjB,IAAI,MAAM,KAAK;AACf,IAAI,OAAO,KAAK;AAChB,IAAI,MAAM,KAAK;AAEf,IAAI,QAAQ,KAAK;AACjB,IAAI,MAAM,KAAK;AACf,IAAI,MAAM,KAAK;AACf,IAAI,MAAM,KAAK;AACf,IAAI,OAAO,KAAK,QAAQ,SAAS,GAAG;AAAE,SAAO,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AAAG;AACzE,IAAI,OAAO,KAAK;AAChB,IAAI,MAAM,KAAK;AAEf,SAAS,KAAK,GAAG;AACtB,SAAO,IAAI,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,CAAC;AAC9C;AAEO,SAAS,KAAK,GAAG;AACtB,SAAO,IAAI,IAAI,SAAS,IAAI,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC;AACxD;AAEO,SAAS,SAAS,GAAG;AAC1B,UAAQ,IAAI,IAAI,IAAI,CAAC,KAAK;AAC5B;;;ACnCe,SAAR,OAAwB;AAAC;;;ACAhC,SAAS,eAAe,UAAU,QAAQ;AACxC,MAAI,YAAY,mBAAmB,eAAe,SAAS,IAAI,GAAG;AAChE,uBAAmB,SAAS,IAAI,EAAE,UAAU,MAAM;AAAA,EACpD;AACF;AAEA,IAAI,mBAAmB;AAAA,EACrB,SAAS,SAASA,SAAQ,QAAQ;AAChC,mBAAeA,QAAO,UAAU,MAAM;AAAA,EACxC;AAAA,EACA,mBAAmB,SAASA,SAAQ,QAAQ;AAC1C,QAAI,WAAWA,QAAO,UAAU,IAAI,IAAI,IAAI,SAAS;AACrD,WAAO,EAAE,IAAI,EAAG,gBAAe,SAAS,CAAC,EAAE,UAAU,MAAM;AAAA,EAC7D;AACF;AAEA,IAAI,qBAAqB;AAAA,EACvB,QAAQ,SAASA,SAAQ,QAAQ;AAC/B,WAAO,OAAO;AAAA,EAChB;AAAA,EACA,OAAO,SAASA,SAAQ,QAAQ;AAC9B,IAAAA,UAASA,QAAO;AAChB,WAAO,MAAMA,QAAO,CAAC,GAAGA,QAAO,CAAC,GAAGA,QAAO,CAAC,CAAC;AAAA,EAC9C;AAAA,EACA,YAAY,SAASA,SAAQ,QAAQ;AACnC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI,EAAG,CAAAD,UAASC,aAAY,CAAC,GAAG,OAAO,MAAMD,QAAO,CAAC,GAAGA,QAAO,CAAC,GAAGA,QAAO,CAAC,CAAC;AAAA,EACvF;AAAA,EACA,YAAY,SAASA,SAAQ,QAAQ;AACnC,eAAWA,QAAO,aAAa,QAAQ,CAAC;AAAA,EAC1C;AAAA,EACA,iBAAiB,SAASA,SAAQ,QAAQ;AACxC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI,EAAG,YAAWA,aAAY,CAAC,GAAG,QAAQ,CAAC;AAAA,EACtD;AAAA,EACA,SAAS,SAASD,SAAQ,QAAQ;AAChC,kBAAcA,QAAO,aAAa,MAAM;AAAA,EAC1C;AAAA,EACA,cAAc,SAASA,SAAQ,QAAQ;AACrC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI,EAAG,eAAcA,aAAY,CAAC,GAAG,MAAM;AAAA,EACtD;AAAA,EACA,oBAAoB,SAASD,SAAQ,QAAQ;AAC3C,QAAI,aAAaA,QAAO,YAAY,IAAI,IAAI,IAAI,WAAW;AAC3D,WAAO,EAAE,IAAI,EAAG,gBAAe,WAAW,CAAC,GAAG,MAAM;AAAA,EACtD;AACF;AAEA,SAAS,WAAWC,cAAa,QAAQ,QAAQ;AAC/C,MAAI,IAAI,IAAI,IAAIA,aAAY,SAAS,QAAQ;AAC7C,SAAO,UAAU;AACjB,SAAO,EAAE,IAAI,EAAG,cAAaA,aAAY,CAAC,GAAG,OAAO,MAAM,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,WAAW,CAAC,CAAC;AACrG,SAAO,QAAQ;AACjB;AAEA,SAAS,cAAcA,cAAa,QAAQ;AAC1C,MAAI,IAAI,IAAI,IAAIA,aAAY;AAC5B,SAAO,aAAa;AACpB,SAAO,EAAE,IAAI,EAAG,YAAWA,aAAY,CAAC,GAAG,QAAQ,CAAC;AACpD,SAAO,WAAW;AACpB;AAEe,SAAR,eAAiBD,SAAQ,QAAQ;AACtC,MAAIA,WAAU,iBAAiB,eAAeA,QAAO,IAAI,GAAG;AAC1D,qBAAiBA,QAAO,IAAI,EAAEA,SAAQ,MAAM;AAAA,EAC9C,OAAO;AACL,mBAAeA,SAAQ,MAAM;AAAA,EAC/B;AACF;;;AC/DO,IAAI,cAAc,IAAI,MAAM;AAInC,IAAI,UAAU,IAAI,MAAM;AAAxB,IACI;AADJ,IAEI;AAFJ,IAGI;AAHJ,IAII;AAJJ,IAKI;AAEG,IAAI,aAAa;AAAA,EACtB,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc,WAAW;AACvB,kBAAc,IAAI,MAAM;AACxB,eAAW,YAAY;AACvB,eAAW,UAAU;AAAA,EACvB;AAAA,EACA,YAAY,WAAW;AACrB,QAAI,WAAW,CAAC;AAChB,YAAQ,IAAI,WAAW,IAAI,MAAM,WAAW,QAAQ;AACpD,SAAK,YAAY,KAAK,UAAU,KAAK,QAAQ;AAAA,EAC/C;AAAA,EACA,QAAQ,WAAW;AACjB,YAAQ,IAAI,GAAG;AAAA,EACjB;AACF;AAEA,SAAS,gBAAgB;AACvB,aAAW,QAAQ;AACrB;AAEA,SAAS,cAAc;AACrB,YAAU,UAAU,KAAK;AAC3B;AAEA,SAAS,eAAe,QAAQ,KAAK;AACnC,aAAW,QAAQ;AACnB,aAAW,QAAQ,QAAQ;AAC3B,YAAU,SAAS,OAAO;AAC1B,YAAU,QAAQ,UAAU,IAAI,MAAM,MAAM,IAAI,SAAS,GAAG,UAAU,IAAI,GAAG;AAC/E;AAEA,SAAS,UAAU,QAAQ,KAAK;AAC9B,YAAU,SAAS,OAAO;AAC1B,QAAM,MAAM,IAAI;AAKhB,MAAI,UAAU,SAAS,SACnB,WAAW,WAAW,IAAI,IAAI,IAC9B,WAAW,WAAW,SACtB,SAAS,IAAI,GAAG,GAChB,SAAS,IAAI,GAAG,GAChB,IAAI,UAAU,QACd,IAAI,UAAU,SAAS,IAAI,IAAI,QAAQ,GACvC,IAAI,IAAI,WAAW,IAAI,QAAQ;AACnC,cAAY,IAAI,MAAM,GAAG,CAAC,CAAC;AAG3B,YAAU,QAAQ,UAAU,QAAQ,UAAU;AAChD;AAEe,SAAR,aAAiBE,SAAQ;AAC9B,YAAU,IAAI,MAAM;AACpB,iBAAOA,SAAQ,UAAU;AACzB,SAAO,UAAU;AACnB;;;ACzEO,SAAS,UAAUC,YAAW;AACnC,SAAO,CAAC,MAAMA,WAAU,CAAC,GAAGA,WAAU,CAAC,CAAC,GAAG,KAAKA,WAAU,CAAC,CAAC,CAAC;AAC/D;AAEO,SAAS,UAAUC,YAAW;AACnC,MAAI,SAASA,WAAU,CAAC,GAAG,MAAMA,WAAU,CAAC,GAAG,SAAS,IAAI,GAAG;AAC/D,SAAO,CAAC,SAAS,IAAI,MAAM,GAAG,SAAS,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC;AAC9D;AAEO,SAAS,aAAa,GAAG,GAAG;AACjC,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAC/C;AAEO,SAAS,eAAe,GAAG,GAAG;AACnC,SAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AACzF;AAGO,SAAS,oBAAoB,GAAG,GAAG;AACxC,IAAE,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AACzC;AAEO,SAAS,eAAe,QAAQ,GAAG;AACxC,SAAO,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AACrD;AAGO,SAAS,0BAA0B,GAAG;AAC3C,MAAI,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AACpD,IAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK;AAChC;;;AC1BA,IAAIC;AAAJ,IAAa;AAAb,IAAmB;AAAnB,IAA4B;AAA5B,IACI;AADJ,IAEIC;AAFJ,IAEcC;AAFd,IAGI;AAHJ,IAII;AAJJ,IAKI;AALJ,IAMIC;AAEJ,IAAI,eAAe;AAAA,EACjB,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc,WAAW;AACvB,iBAAa,QAAQ;AACrB,iBAAa,YAAY;AACzB,iBAAa,UAAU;AACvB,eAAW,IAAI,MAAM;AACrB,eAAW,aAAa;AAAA,EAC1B;AAAA,EACA,YAAY,WAAW;AACrB,eAAW,WAAW;AACtB,iBAAa,QAAQ;AACrB,iBAAa,YAAY;AACzB,iBAAa,UAAU;AACvB,QAAI,cAAc,EAAG,CAAAH,WAAU,EAAE,UAAU,MAAM,OAAO,EAAE,OAAO;AAAA,aACxD,WAAW,QAAS,QAAO;AAAA,aAC3B,WAAW,CAAC,QAAS,QAAO;AACrC,IAAAG,OAAM,CAAC,IAAIH,UAASG,OAAM,CAAC,IAAI;AAAA,EACjC;AAAA,EACA,QAAQ,WAAW;AACjB,IAAAH,WAAU,EAAE,UAAU,MAAM,OAAO,EAAE,OAAO;AAAA,EAC9C;AACF;AAEA,SAAS,YAAY,QAAQ,KAAK;AAChC,SAAO,KAAKG,SAAQ,CAACH,WAAU,QAAQ,UAAU,MAAM,CAAC;AACxD,MAAI,MAAM,KAAM,QAAO;AACvB,MAAI,MAAM,KAAM,QAAO;AACzB;AAEA,SAAS,UAAU,QAAQ,KAAK;AAC9B,MAAI,IAAI,UAAU,CAAC,SAAS,SAAS,MAAM,OAAO,CAAC;AACnD,MAAI,IAAI;AACN,QAAI,SAAS,eAAe,IAAI,CAAC,GAC7B,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GACtC,aAAa,eAAe,YAAY,MAAM;AAClD,8BAA0B,UAAU;AACpC,iBAAa,UAAU,UAAU;AACjC,QAAI,QAAQ,SAAS,SACjBI,QAAO,QAAQ,IAAI,IAAI,IACvB,UAAU,WAAW,CAAC,IAAI,UAAUA,OACpC,MACA,eAAe,IAAI,KAAK,IAAI;AAChC,QAAI,gBAAgBA,QAAO,UAAU,WAAW,UAAUA,QAAO,SAAS;AACxE,aAAO,WAAW,CAAC,IAAI;AACvB,UAAI,OAAO,KAAM,QAAO;AAAA,IAC1B,WAAW,WAAW,UAAU,OAAO,MAAM,KAAK,gBAAgBA,QAAO,UAAU,WAAW,UAAUA,QAAO,SAAS;AACtH,aAAO,CAAC,WAAW,CAAC,IAAI;AACxB,UAAI,OAAO,KAAM,QAAO;AAAA,IAC1B,OAAO;AACL,UAAI,MAAM,KAAM,QAAO;AACvB,UAAI,MAAM,KAAM,QAAO;AAAA,IACzB;AACA,QAAI,cAAc;AAChB,UAAI,SAAS,SAAS;AACpB,YAAI,MAAMJ,UAAS,MAAM,IAAI,MAAMA,UAAS,OAAO,EAAG,WAAU;AAAA,MAClE,OAAO;AACL,YAAI,MAAM,QAAQ,OAAO,IAAI,MAAMA,UAAS,OAAO,EAAG,CAAAA,WAAU;AAAA,MAClE;AAAA,IACF,OAAO;AACL,UAAI,WAAWA,UAAS;AACtB,YAAI,SAASA,SAAS,CAAAA,WAAU;AAChC,YAAI,SAAS,QAAS,WAAU;AAAA,MAClC,OAAO;AACL,YAAI,SAAS,SAAS;AACpB,cAAI,MAAMA,UAAS,MAAM,IAAI,MAAMA,UAAS,OAAO,EAAG,WAAU;AAAA,QAClE,OAAO;AACL,cAAI,MAAM,QAAQ,OAAO,IAAI,MAAMA,UAAS,OAAO,EAAG,CAAAA,WAAU;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO,KAAKG,SAAQ,CAACH,WAAU,QAAQ,UAAU,MAAM,CAAC;AAAA,EAC1D;AACA,MAAI,MAAM,KAAM,QAAO;AACvB,MAAI,MAAM,KAAM,QAAO;AACvB,OAAK,GAAG,UAAU;AACpB;AAEA,SAAS,kBAAkB;AACzB,eAAa,QAAQ;AACvB;AAEA,SAAS,gBAAgB;AACvB,EAAAG,OAAM,CAAC,IAAIH,UAASG,OAAM,CAAC,IAAI;AAC/B,eAAa,QAAQ;AACrB,OAAK;AACP;AAEA,SAAS,gBAAgB,QAAQ,KAAK;AACpC,MAAI,IAAI;AACN,QAAI,QAAQ,SAAS;AACrB,aAAS,IAAI,IAAI,KAAK,IAAI,MAAM,SAAS,QAAQ,IAAI,MAAM,QAAQ,KAAK;AAAA,EAC1E,OAAO;AACL,IAAAF,YAAW,QAAQC,SAAQ;AAAA,EAC7B;AACA,aAAW,MAAM,QAAQ,GAAG;AAC5B,YAAU,QAAQ,GAAG;AACvB;AAEA,SAAS,kBAAkB;AACzB,aAAW,UAAU;AACvB;AAEA,SAAS,gBAAgB;AACvB,kBAAgBD,WAAUC,MAAK;AAC/B,aAAW,QAAQ;AACnB,MAAI,IAAI,QAAQ,IAAI,QAAS,CAAAF,WAAU,EAAE,UAAU;AACnD,EAAAG,OAAM,CAAC,IAAIH,UAASG,OAAM,CAAC,IAAI;AAC/B,OAAK;AACP;AAKA,SAAS,MAAMH,UAASK,UAAS;AAC/B,UAAQA,YAAWL,YAAW,IAAIK,WAAU,MAAMA;AACpD;AAEA,SAAS,aAAa,GAAG,GAAG;AAC1B,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AACnB;AAEA,SAAS,cAAcF,QAAO,GAAG;AAC/B,SAAOA,OAAM,CAAC,KAAKA,OAAM,CAAC,IAAIA,OAAM,CAAC,KAAK,KAAK,KAAKA,OAAM,CAAC,IAAI,IAAIA,OAAM,CAAC,KAAKA,OAAM,CAAC,IAAI;AAC5F;AAEe,SAAR,eAAiB,SAAS;AAC/B,MAAI,GAAG,GAAG,GAAG,GAAG,QAAQ,UAAU;AAElC,SAAO,UAAU,EAAEH,WAAU,OAAO;AACpC,WAAS,CAAC;AACV,iBAAO,SAAS,YAAY;AAG5B,MAAI,IAAI,OAAO,QAAQ;AACrB,WAAO,KAAK,YAAY;AAGxB,SAAK,IAAI,GAAG,IAAI,OAAO,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AACnD,UAAI,OAAO,CAAC;AACZ,UAAI,cAAc,GAAG,EAAE,CAAC,CAAC,KAAK,cAAc,GAAG,EAAE,CAAC,CAAC,GAAG;AACpD,YAAI,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AACrD,YAAI,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,MACvD,OAAO;AACL,eAAO,KAAK,IAAI,CAAC;AAAA,MACnB;AAAA,IACF;AAIA,SAAK,WAAW,WAAW,IAAI,OAAO,SAAS,GAAG,IAAI,GAAG,IAAI,OAAO,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,EAAE,GAAG;AAC1F,UAAI,OAAO,CAAC;AACZ,WAAK,QAAQ,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,SAAU,YAAW,OAAOA,WAAU,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC;AAAA,IAC7F;AAAA,EACF;AAEA,WAASG,SAAQ;AAEjB,SAAOH,aAAY,YAAY,SAAS,WAClC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,IACvB,CAAC,CAACA,UAAS,IAAI,GAAG,CAAC,SAAS,IAAI,CAAC;AACzC;;;AC7KA,IAAI;AAAJ,IAAQ;AAAR,IACI;AADJ,IACQ;AADR,IACY;AADZ,IAEI;AAFJ,IAEQ;AAFR,IAEY;AAFZ,IAGI;AAHJ,IAGQ;AAHR,IAGY;AAHZ,IAIIM;AAJJ,IAIcC;AAJd,IAKI;AALJ,IAKQ;AALR,IAKY;AAEZ,IAAI,iBAAiB;AAAA,EACnB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc,WAAW;AACvB,mBAAe,YAAY;AAC3B,mBAAe,UAAU;AAAA,EAC3B;AAAA,EACA,YAAY,WAAW;AACrB,mBAAe,YAAY;AAC3B,mBAAe,UAAU;AAAA,EAC3B;AACF;AAGA,SAAS,cAAc,QAAQ,KAAK;AAClC,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG;AACpB,yBAAuB,SAAS,IAAI,MAAM,GAAG,SAAS,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC;AAC7E;AAEA,SAAS,uBAAuB,GAAG,GAAG,GAAG;AACvC,IAAE;AACF,SAAO,IAAI,MAAM;AACjB,SAAO,IAAI,MAAM;AACjB,SAAO,IAAI,MAAM;AACnB;AAEA,SAAS,oBAAoB;AAC3B,iBAAe,QAAQ;AACzB;AAEA,SAAS,uBAAuB,QAAQ,KAAK;AAC3C,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG;AACpB,OAAK,SAAS,IAAI,MAAM;AACxB,OAAK,SAAS,IAAI,MAAM;AACxB,OAAK,IAAI,GAAG;AACZ,iBAAe,QAAQ;AACvB,yBAAuB,IAAI,IAAI,EAAE;AACnC;AAEA,SAAS,kBAAkB,QAAQ,KAAK;AACtC,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG,GAChB,IAAI,SAAS,IAAI,MAAM,GACvB,IAAI,SAAS,IAAI,MAAM,GACvB,IAAI,IAAI,GAAG,GACX,IAAI,MAAM,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC;AAC/H,QAAM;AACN,QAAM,KAAK,MAAM,KAAK;AACtB,QAAM,KAAK,MAAM,KAAK;AACtB,QAAM,KAAK,MAAM,KAAK;AACtB,yBAAuB,IAAI,IAAI,EAAE;AACnC;AAEA,SAAS,kBAAkB;AACzB,iBAAe,QAAQ;AACzB;AAIA,SAAS,oBAAoB;AAC3B,iBAAe,QAAQ;AACzB;AAEA,SAAS,kBAAkB;AACzB,oBAAkBD,WAAUC,MAAK;AACjC,iBAAe,QAAQ;AACzB;AAEA,SAAS,uBAAuB,QAAQ,KAAK;AAC3C,EAAAD,YAAW,QAAQC,SAAQ;AAC3B,YAAU,SAAS,OAAO;AAC1B,iBAAe,QAAQ;AACvB,MAAI,SAAS,IAAI,GAAG;AACpB,OAAK,SAAS,IAAI,MAAM;AACxB,OAAK,SAAS,IAAI,MAAM;AACxB,OAAK,IAAI,GAAG;AACZ,yBAAuB,IAAI,IAAI,EAAE;AACnC;AAEA,SAAS,kBAAkB,QAAQ,KAAK;AACtC,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG,GAChB,IAAI,SAAS,IAAI,MAAM,GACvB,IAAI,SAAS,IAAI,MAAM,GACvB,IAAI,IAAI,GAAG,GACX,KAAK,KAAK,IAAI,KAAK,GACnB,KAAK,KAAK,IAAI,KAAK,GACnB,KAAK,KAAK,IAAI,KAAK,GACnB,IAAI,MAAM,IAAI,IAAI,EAAE,GACpB,IAAI,KAAK,CAAC,GACV,IAAI,KAAK,CAAC,IAAI;AAClB,KAAG,IAAI,IAAI,EAAE;AACb,KAAG,IAAI,IAAI,EAAE;AACb,KAAG,IAAI,IAAI,EAAE;AACb,QAAM;AACN,QAAM,KAAK,MAAM,KAAK;AACtB,QAAM,KAAK,MAAM,KAAK;AACtB,QAAM,KAAK,MAAM,KAAK;AACtB,yBAAuB,IAAI,IAAI,EAAE;AACnC;AAEe,SAAR,iBAAiBC,SAAQ;AAC9B,OAAK,KACL,KAAK,KAAK,KACV,KAAK,KAAK,KAAK;AACf,OAAK,IAAI,MAAM;AACf,OAAK,IAAI,MAAM;AACf,OAAK,IAAI,MAAM;AACf,iBAAOA,SAAQ,cAAc;AAE7B,MAAI,IAAI,CAAC,IACL,IAAI,CAAC,IACL,IAAI,CAAC,IACL,IAAI,MAAM,GAAG,GAAG,CAAC;AAGrB,MAAI,IAAI,UAAU;AAChB,QAAI,IAAI,IAAI,IAAI,IAAI;AAEpB,QAAI,KAAK,QAAS,KAAI,IAAI,IAAI,IAAI,IAAI;AACtC,QAAI,MAAM,GAAG,GAAG,CAAC;AAEjB,QAAI,IAAI,SAAU,QAAO,CAAC,KAAK,GAAG;AAAA,EACpC;AAEA,SAAO,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,KAAK,IAAI,CAAC,IAAI,OAAO;AACtD;;;AC9Ie,SAAR,iBAAiB,GAAG;AACzB,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;;;ACJe,SAAR,gBAAiB,GAAG,GAAG;AAE5B,WAAS,QAAQ,GAAG,GAAG;AACrB,WAAO,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EAClC;AAEA,MAAI,EAAE,UAAU,EAAE,OAAQ,SAAQ,SAAS,SAAS,GAAG,GAAG;AACxD,WAAO,IAAI,EAAE,OAAO,GAAG,CAAC,GAAG,KAAK,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACrD;AAEA,SAAO;AACT;;;ACRA,SAAS,iBAAiB,QAAQ,KAAK;AACrC,MAAI,IAAI,MAAM,IAAI,GAAI,WAAU,KAAK,MAAM,SAAS,GAAG,IAAI;AAC3D,SAAO,CAAC,QAAQ,GAAG;AACrB;AAEA,iBAAiB,SAAS;AAEnB,SAAS,cAAc,aAAa,UAAU,YAAY;AAC/D,UAAQ,eAAe,OAAQ,YAAY,aAAa,gBAAQ,eAAe,WAAW,GAAG,iBAAiB,UAAU,UAAU,CAAC,IAC/H,eAAe,WAAW,IACzB,YAAY,aAAa,iBAAiB,UAAU,UAAU,IAC/D;AACN;AAEA,SAAS,sBAAsB,aAAa;AAC1C,SAAO,SAAS,QAAQ,KAAK;AAC3B,cAAU;AACV,QAAI,IAAI,MAAM,IAAI,GAAI,WAAU,KAAK,MAAM,SAAS,GAAG,IAAI;AAC3D,WAAO,CAAC,QAAQ,GAAG;AAAA,EACrB;AACF;AAEA,SAAS,eAAe,aAAa;AACnC,MAAI,WAAW,sBAAsB,WAAW;AAChD,WAAS,SAAS,sBAAsB,CAAC,WAAW;AACpD,SAAO;AACT;AAEA,SAAS,iBAAiB,UAAU,YAAY;AAC9C,MAAI,cAAc,IAAI,QAAQ,GAC1B,cAAc,IAAI,QAAQ,GAC1B,gBAAgB,IAAI,UAAU,GAC9B,gBAAgB,IAAI,UAAU;AAElC,WAAS,SAAS,QAAQ,KAAK;AAC7B,QAAI,SAAS,IAAI,GAAG,GAChB,IAAI,IAAI,MAAM,IAAI,QAClB,IAAI,IAAI,MAAM,IAAI,QAClB,IAAI,IAAI,GAAG,GACX,IAAI,IAAI,cAAc,IAAI;AAC9B,WAAO;AAAA,MACL,MAAM,IAAI,gBAAgB,IAAI,eAAe,IAAI,cAAc,IAAI,WAAW;AAAA,MAC9E,KAAK,IAAI,gBAAgB,IAAI,aAAa;AAAA,IAC5C;AAAA,EACF;AAEA,WAAS,SAAS,SAAS,QAAQ,KAAK;AACtC,QAAI,SAAS,IAAI,GAAG,GAChB,IAAI,IAAI,MAAM,IAAI,QAClB,IAAI,IAAI,MAAM,IAAI,QAClB,IAAI,IAAI,GAAG,GACX,IAAI,IAAI,gBAAgB,IAAI;AAChC,WAAO;AAAA,MACL,MAAM,IAAI,gBAAgB,IAAI,eAAe,IAAI,cAAc,IAAI,WAAW;AAAA,MAC9E,KAAK,IAAI,cAAc,IAAI,WAAW;AAAA,IACxC;AAAA,EACF;AAEA,SAAO;AACT;AAEe,SAAR,iBAAiB,QAAQ;AAC9B,WAAS,cAAc,OAAO,CAAC,IAAI,SAAS,OAAO,CAAC,IAAI,SAAS,OAAO,SAAS,IAAI,OAAO,CAAC,IAAI,UAAU,CAAC;AAE5G,WAAS,QAAQC,cAAa;AAC5B,IAAAA,eAAc,OAAOA,aAAY,CAAC,IAAI,SAASA,aAAY,CAAC,IAAI,OAAO;AACvE,WAAOA,aAAY,CAAC,KAAK,SAASA,aAAY,CAAC,KAAK,SAASA;AAAA,EAC/D;AAEA,UAAQ,SAAS,SAASA,cAAa;AACrC,IAAAA,eAAc,OAAO,OAAOA,aAAY,CAAC,IAAI,SAASA,aAAY,CAAC,IAAI,OAAO;AAC9E,WAAOA,aAAY,CAAC,KAAK,SAASA,aAAY,CAAC,KAAK,SAASA;AAAA,EAC/D;AAEA,SAAO;AACT;;;ACxEO,SAAS,aAAa,QAAQ,QAAQ,OAAO,WAAW,IAAI,IAAI;AACrE,MAAI,CAAC,MAAO;AACZ,MAAI,YAAY,IAAI,MAAM,GACtB,YAAY,IAAI,MAAM,GACtB,OAAO,YAAY;AACvB,MAAI,MAAM,MAAM;AACd,SAAK,SAAS,YAAY;AAC1B,SAAK,SAAS,OAAO;AAAA,EACvB,OAAO;AACL,SAAK,aAAa,WAAW,EAAE;AAC/B,SAAK,aAAa,WAAW,EAAE;AAC/B,QAAI,YAAY,IAAI,KAAK,KAAK,KAAK,GAAI,OAAM,YAAY;AAAA,EAC3D;AACA,WAAS,OAAO,IAAI,IAAI,YAAY,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM;AAClE,YAAQ,UAAU,CAAC,WAAW,CAAC,YAAY,IAAI,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC;AACvE,WAAO,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,EACjC;AACF;AAGA,SAAS,aAAa,WAAW,OAAO;AACtC,UAAQ,UAAU,KAAK,GAAG,MAAM,CAAC,KAAK;AACtC,4BAA0B,KAAK;AAC/B,MAAI,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC;AAC3B,WAAS,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,UAAU,MAAM,WAAW;AAChE;AAEe,SAAR,iBAAmB;AACxB,MAAI,SAAS,iBAAS,CAAC,GAAG,CAAC,CAAC,GACxB,SAAS,iBAAS,EAAE,GACpB,YAAY,iBAAS,CAAC,GACtB,MACA,QACA,SAAS,EAAC,MAAY;AAE1B,WAAS,MAAM,GAAG,GAAG;AACnB,SAAK,KAAK,IAAI,OAAO,GAAG,CAAC,CAAC;AAC1B,MAAE,CAAC,KAAK,SAAS,EAAE,CAAC,KAAK;AAAA,EAC3B;AAEA,WAAS,SAAS;AAChB,QAAI,IAAI,OAAO,MAAM,MAAM,SAAS,GAChC,IAAI,OAAO,MAAM,MAAM,SAAS,IAAI,SACpC,IAAI,UAAU,MAAM,MAAM,SAAS,IAAI;AAC3C,WAAO,CAAC;AACR,aAAS,cAAc,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,EAAE,CAAC,IAAI,SAAS,CAAC,EAAE;AAC5D,iBAAa,QAAQ,GAAG,GAAG,CAAC;AAC5B,QAAI,EAAC,MAAM,WAAW,aAAa,CAAC,IAAI,EAAC;AACzC,WAAO,SAAS;AAChB,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU;AAAA,EACxG;AAEA,SAAO,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,SAAS,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,UAAU;AAAA,EAC5F;AAEA,SAAO,YAAY,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,UAAU;AAAA,EAC/F;AAEA,SAAO;AACT;;;ACrEe,SAAR,iBAAmB;AACxB,MAAI,QAAQ,CAAC,GACT;AACJ,SAAO;AAAA,IACL,OAAO,SAAS,GAAG,GAAG,GAAG;AACvB,WAAK,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,IACrB;AAAA,IACA,WAAW,WAAW;AACpB,YAAM,KAAK,OAAO,CAAC,CAAC;AAAA,IACtB;AAAA,IACA,SAAS;AAAA,IACT,QAAQ,WAAW;AACjB,UAAI,MAAM,SAAS,EAAG,OAAM,KAAK,MAAM,IAAI,EAAE,OAAO,MAAM,MAAM,CAAC,CAAC;AAAA,IACpE;AAAA,IACA,QAAQ,WAAW;AACjB,UAAI,SAAS;AACb,cAAQ,CAAC;AACT,aAAO;AACP,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;ACrBe,SAAR,mBAAiB,GAAG,GAAG;AAC5B,SAAO,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,WAAW,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI;AAC1D;;;ACDA,SAAS,aAAa,OAAO,QAAQ,OAAO,OAAO;AACjD,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI,KAAK,IAAI;AACpB;AAKe,SAAR,eAAiB,UAAUC,sBAAqB,aAAa,aAAa,QAAQ;AACvF,MAAI,UAAU,CAAC,GACX,OAAO,CAAC,GACR,GACA;AAEJ,WAAS,QAAQ,SAAS,SAAS;AACjC,SAAKC,KAAI,QAAQ,SAAS,MAAM,EAAG;AACnC,QAAIA,IAAGC,MAAK,QAAQ,CAAC,GAAG,KAAK,QAAQD,EAAC,GAAG;AAEzC,QAAI,mBAAWC,KAAI,EAAE,GAAG;AACtB,UAAI,CAACA,IAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;AACpB,eAAO,UAAU;AACjB,aAAK,IAAI,GAAG,IAAID,IAAG,EAAE,EAAG,QAAO,OAAOC,MAAK,QAAQ,CAAC,GAAG,CAAC,GAAGA,IAAG,CAAC,CAAC;AAChE,eAAO,QAAQ;AACf;AAAA,MACF;AAEA,SAAG,CAAC,KAAK,IAAI;AAAA,IACf;AAEA,YAAQ,KAAK,IAAI,IAAI,aAAaA,KAAI,SAAS,MAAM,IAAI,CAAC;AAC1D,SAAK,KAAK,EAAE,IAAI,IAAI,aAAaA,KAAI,MAAM,GAAG,KAAK,CAAC;AACpD,YAAQ,KAAK,IAAI,IAAI,aAAa,IAAI,SAAS,MAAM,KAAK,CAAC;AAC3D,SAAK,KAAK,EAAE,IAAI,IAAI,aAAa,IAAI,MAAM,GAAG,IAAI,CAAC;AAAA,EACrD,CAAC;AAED,MAAI,CAAC,QAAQ,OAAQ;AAErB,OAAK,KAAKF,oBAAmB;AAC7B,OAAK,OAAO;AACZ,OAAK,IAAI;AAET,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AACvC,SAAK,CAAC,EAAE,IAAI,cAAc,CAAC;AAAA,EAC7B;AAEA,MAAI,QAAQ,QAAQ,CAAC,GACjB,QACA;AAEJ,SAAO,GAAG;AAER,QAAI,UAAU,OACV,YAAY;AAChB,WAAO,QAAQ,EAAG,MAAK,UAAU,QAAQ,OAAO,MAAO;AACvD,aAAS,QAAQ;AACjB,WAAO,UAAU;AACjB,OAAG;AACD,cAAQ,IAAI,QAAQ,EAAE,IAAI;AAC1B,UAAI,QAAQ,GAAG;AACb,YAAI,WAAW;AACb,eAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,EAAG,QAAO,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,QAC1F,OAAO;AACL,sBAAY,QAAQ,GAAG,QAAQ,EAAE,GAAG,GAAG,MAAM;AAAA,QAC/C;AACA,kBAAU,QAAQ;AAAA,MACpB,OAAO;AACL,YAAI,WAAW;AACb,mBAAS,QAAQ,EAAE;AACnB,eAAK,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,EAAE,EAAG,QAAO,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AAAA,QACxF,OAAO;AACL,sBAAY,QAAQ,GAAG,QAAQ,EAAE,GAAG,IAAI,MAAM;AAAA,QAChD;AACA,kBAAU,QAAQ;AAAA,MACpB;AACA,gBAAU,QAAQ;AAClB,eAAS,QAAQ;AACjB,kBAAY,CAAC;AAAA,IACf,SAAS,CAAC,QAAQ;AAClB,WAAO,QAAQ;AAAA,EACjB;AACF;AAEA,SAAS,KAAK,OAAO;AACnB,MAAI,EAAE,IAAI,MAAM,QAAS;AACzB,MAAI,GACA,IAAI,GACJ,IAAI,MAAM,CAAC,GACX;AACJ,SAAO,EAAE,IAAI,GAAG;AACd,MAAE,IAAI,IAAI,MAAM,CAAC;AACjB,MAAE,IAAI;AACN,QAAI;AAAA,EACN;AACA,IAAE,IAAI,IAAI,MAAM,CAAC;AACjB,IAAE,IAAI;AACR;;;AClGA,SAAS,UAAU,OAAO;AACxB,SAAO,IAAI,MAAM,CAAC,CAAC,KAAK,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,IAAI,MAAM,MAAM;AACzF;AAEe,SAAR,wBAAiB,SAAS,OAAO;AACtC,MAAI,SAAS,UAAU,KAAK,GACxB,MAAM,MAAM,CAAC,GACb,SAAS,IAAI,GAAG,GAChB,SAAS,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,GACtCG,SAAQ,GACR,UAAU;AAEd,MAAI,MAAM,IAAI,MAAM;AAEpB,MAAI,WAAW,EAAG,OAAM,SAAS;AAAA,WACxB,WAAW,GAAI,OAAM,CAAC,SAAS;AAExC,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,QAAI,EAAE,KAAK,OAAO,QAAQ,CAAC,GAAG,QAAS;AACvC,QAAI,MACA,GACA,SAAS,KAAK,IAAI,CAAC,GACnBC,WAAU,UAAU,MAAM,GAC1BC,QAAO,OAAO,CAAC,IAAI,IAAI,WACvBC,WAAU,IAAID,KAAI,GAClBE,WAAU,IAAIF,KAAI;AAEtB,aAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAGD,WAAUI,UAASF,WAAU,SAASC,WAAU,SAAS,SAAS,QAAQ;AACpG,UAAI,SAAS,KAAK,CAAC,GACfC,WAAU,UAAU,MAAM,GAC1BC,QAAO,OAAO,CAAC,IAAI,IAAI,WACvB,UAAU,IAAIA,KAAI,GAClB,UAAU,IAAIA,KAAI,GAClB,QAAQD,WAAUJ,UAClBM,QAAO,SAAS,IAAI,IAAI,IACxB,WAAWA,QAAO,OAClB,eAAe,WAAW,IAC1B,IAAIJ,WAAU;AAElB,UAAI,IAAI,MAAM,IAAII,QAAO,IAAI,QAAQ,GAAGH,WAAU,UAAU,IAAI,IAAI,QAAQ,CAAC,CAAC;AAC9E,MAAAJ,UAAS,eAAe,QAAQO,QAAO,MAAM;AAI7C,UAAI,eAAeN,YAAW,SAASI,YAAW,QAAQ;AACxD,YAAI,MAAM,eAAe,UAAU,MAAM,GAAG,UAAU,MAAM,CAAC;AAC7D,kCAA0B,GAAG;AAC7B,YAAI,eAAe,eAAe,QAAQ,GAAG;AAC7C,kCAA0B,YAAY;AACtC,YAAI,UAAU,eAAe,SAAS,IAAI,KAAK,KAAK,KAAK,aAAa,CAAC,CAAC;AACxE,YAAI,MAAM,UAAU,QAAQ,WAAW,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI;AACxD,qBAAW,eAAe,SAAS,IAAI,IAAI;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAaA,UAAQL,SAAQ,CAAC,WAAWA,SAAQ,WAAW,MAAM,CAAC,YAAa,UAAU;AAC/E;;;ACnEe,SAAR,aAAiB,cAAc,UAAU,aAAa,OAAO;AAClE,SAAO,SAAS,MAAM;AACpB,QAAI,OAAO,SAAS,IAAI,GACpB,aAAa,eAAW,GACxB,WAAW,SAAS,UAAU,GAC9B,iBAAiB,OACjB,SACA,UACA;AAEJ,QAAI,OAAO;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,WAAW;AACvB,aAAK,QAAQ;AACb,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,mBAAW,CAAC;AACZ,kBAAU,CAAC;AAAA,MACb;AAAA,MACA,YAAY,WAAW;AACrB,aAAK,QAAQ;AACb,aAAK,YAAY;AACjB,aAAK,UAAU;AACf,mBAAW,MAAM,QAAQ;AACzB,YAAI,cAAc,wBAAgB,SAAS,KAAK;AAChD,YAAI,SAAS,QAAQ;AACnB,cAAI,CAAC,eAAgB,MAAK,aAAa,GAAG,iBAAiB;AAC3D,yBAAW,UAAU,qBAAqB,aAAa,aAAa,IAAI;AAAA,QAC1E,WAAW,aAAa;AACtB,cAAI,CAAC,eAAgB,MAAK,aAAa,GAAG,iBAAiB;AAC3D,eAAK,UAAU;AACf,sBAAY,MAAM,MAAM,GAAG,IAAI;AAC/B,eAAK,QAAQ;AAAA,QACf;AACA,YAAI,eAAgB,MAAK,WAAW,GAAG,iBAAiB;AACxD,mBAAW,UAAU;AAAA,MACvB;AAAA,MACA,QAAQ,WAAW;AACjB,aAAK,aAAa;AAClB,aAAK,UAAU;AACf,oBAAY,MAAM,MAAM,GAAG,IAAI;AAC/B,aAAK,QAAQ;AACb,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAEA,aAAS,MAAM,QAAQ,KAAK;AAC1B,UAAI,aAAa,QAAQ,GAAG,EAAG,MAAK,MAAM,QAAQ,GAAG;AAAA,IACvD;AAEA,aAAS,UAAU,QAAQ,KAAK;AAC9B,WAAK,MAAM,QAAQ,GAAG;AAAA,IACxB;AAEA,aAAS,YAAY;AACnB,WAAK,QAAQ;AACb,WAAK,UAAU;AAAA,IACjB;AAEA,aAAS,UAAU;AACjB,WAAK,QAAQ;AACb,WAAK,QAAQ;AAAA,IACf;AAEA,aAAS,UAAU,QAAQ,KAAK;AAC9B,WAAK,KAAK,CAAC,QAAQ,GAAG,CAAC;AACvB,eAAS,MAAM,QAAQ,GAAG;AAAA,IAC5B;AAEA,aAAS,YAAY;AACnB,eAAS,UAAU;AACnB,aAAO,CAAC;AAAA,IACV;AAEA,aAAS,UAAU;AACjB,gBAAU,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;AAChC,eAAS,QAAQ;AAEjB,UAAI,QAAQ,SAAS,MAAM,GACvB,eAAe,WAAW,OAAO,GACjC,GAAG,IAAI,aAAa,QAAQ,GAC5B,SACAQ;AAEJ,WAAK,IAAI;AACT,cAAQ,KAAK,IAAI;AACjB,aAAO;AAEP,UAAI,CAAC,EAAG;AAGR,UAAI,QAAQ,GAAG;AACb,kBAAU,aAAa,CAAC;AACxB,aAAK,IAAI,QAAQ,SAAS,KAAK,GAAG;AAChC,cAAI,CAAC,eAAgB,MAAK,aAAa,GAAG,iBAAiB;AAC3D,eAAK,UAAU;AACf,eAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,MAAK,OAAOA,SAAQ,QAAQ,CAAC,GAAG,CAAC,GAAGA,OAAM,CAAC,CAAC;AACpE,eAAK,QAAQ;AAAA,QACf;AACA;AAAA,MACF;AAIA,UAAI,IAAI,KAAK,QAAQ,EAAG,cAAa,KAAK,aAAa,IAAI,EAAE,OAAO,aAAa,MAAM,CAAC,CAAC;AAEzF,eAAS,KAAK,aAAa,OAAO,YAAY,CAAC;AAAA,IACjD;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,SAAS;AAC7B,SAAO,QAAQ,SAAS;AAC1B;AAIA,SAAS,oBAAoB,GAAG,GAAG;AACjC,WAAS,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,SAAS,UAAU,SAAS,EAAE,CAAC,OACxD,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,SAAS,UAAU,SAAS,EAAE,CAAC;AACnE;;;AC/HA,IAAO,uBAAQ;AAAA,EACb,WAAW;AAAE,WAAO;AAAA,EAAM;AAAA,EAC1B;AAAA,EACA;AAAA,EACA,CAAC,CAAC,IAAI,CAAC,MAAM;AACf;AAKA,SAAS,qBAAqB,QAAQ;AACpC,MAAIC,WAAU,KACVC,QAAO,KACP,QAAQ,KACR;AAEJ,SAAO;AAAA,IACL,WAAW,WAAW;AACpB,aAAO,UAAU;AACjB,cAAQ;AAAA,IACV;AAAA,IACA,OAAO,SAASC,UAASC,OAAM;AAC7B,UAAI,QAAQD,WAAU,IAAI,KAAK,CAAC,IAC5B,QAAQ,IAAIA,WAAUF,QAAO;AACjC,UAAI,IAAI,QAAQ,EAAE,IAAI,SAAS;AAC7B,eAAO,MAAMA,UAASC,SAAQA,QAAOE,SAAQ,IAAI,IAAI,SAAS,CAAC,MAAM;AACrE,eAAO,MAAM,OAAOF,KAAI;AACxB,eAAO,QAAQ;AACf,eAAO,UAAU;AACjB,eAAO,MAAM,OAAOA,KAAI;AACxB,eAAO,MAAMC,UAASD,KAAI;AAC1B,gBAAQ;AAAA,MACV,WAAW,UAAU,SAAS,SAAS,IAAI;AACzC,YAAI,IAAID,WAAU,KAAK,IAAI,QAAS,CAAAA,YAAW,QAAQ;AACvD,YAAI,IAAIE,WAAU,KAAK,IAAI,QAAS,CAAAA,YAAW,QAAQ;AACvD,QAAAD,QAAO,0BAA0BD,UAASC,OAAMC,UAASC,KAAI;AAC7D,eAAO,MAAM,OAAOF,KAAI;AACxB,eAAO,QAAQ;AACf,eAAO,UAAU;AACjB,eAAO,MAAM,OAAOA,KAAI;AACxB,gBAAQ;AAAA,MACV;AACA,aAAO,MAAMD,WAAUE,UAASD,QAAOE,KAAI;AAC3C,cAAQ;AAAA,IACV;AAAA,IACA,SAAS,WAAW;AAClB,aAAO,QAAQ;AACf,MAAAH,WAAUC,QAAO;AAAA,IACnB;AAAA,IACA,OAAO,WAAW;AAChB,aAAO,IAAI;AAAA,IACb;AAAA,EACF;AACF;AAEA,SAAS,0BAA0BD,UAASC,OAAMC,UAASC,OAAM;AAC/D,MAAIC,UACA,SACA,oBAAoB,IAAIJ,WAAUE,QAAO;AAC7C,SAAO,IAAI,iBAAiB,IAAI,UAC1B,MAAM,IAAID,KAAI,KAAK,UAAU,IAAIE,KAAI,KAAK,IAAID,QAAO,IACjD,IAAIC,KAAI,KAAKC,WAAU,IAAIH,KAAI,KAAK,IAAID,QAAO,MAC9CI,WAAU,UAAU,kBAAkB,KAC1CH,QAAOE,SAAQ;AACxB;AAEA,SAAS,4BAA4B,MAAM,IAAI,WAAW,QAAQ;AAChE,MAAI;AACJ,MAAI,QAAQ,MAAM;AAChB,UAAM,YAAY;AAClB,WAAO,MAAM,CAAC,IAAI,GAAG;AACrB,WAAO,MAAM,GAAG,GAAG;AACnB,WAAO,MAAM,IAAI,GAAG;AACpB,WAAO,MAAM,IAAI,CAAC;AAClB,WAAO,MAAM,IAAI,CAAC,GAAG;AACrB,WAAO,MAAM,GAAG,CAAC,GAAG;AACpB,WAAO,MAAM,CAAC,IAAI,CAAC,GAAG;AACtB,WAAO,MAAM,CAAC,IAAI,CAAC;AACnB,WAAO,MAAM,CAAC,IAAI,GAAG;AAAA,EACvB,WAAW,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,SAAS;AACzC,QAAI,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC;AACrC,UAAM,YAAY,SAAS;AAC3B,WAAO,MAAM,CAAC,QAAQ,GAAG;AACzB,WAAO,MAAM,GAAG,GAAG;AACnB,WAAO,MAAM,QAAQ,GAAG;AAAA,EAC1B,OAAO;AACL,WAAO,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,EAC3B;AACF;;;ACrFe,SAARE,gBAAiB,QAAQ;AAC9B,MAAI,KAAK,IAAI,MAAM,GACf,QAAQ,IAAI,SACZ,cAAc,KAAK,GACnB,gBAAgB,IAAI,EAAE,IAAI;AAE9B,WAAS,YAAY,MAAM,IAAI,WAAW,QAAQ;AAChD,iBAAa,QAAQ,QAAQ,OAAO,WAAW,MAAM,EAAE;AAAA,EACzD;AAEA,WAAS,QAAQ,QAAQ,KAAK;AAC5B,WAAO,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI;AAAA,EAClC;AAMA,WAAS,SAAS,QAAQ;AACxB,QAAI,QACA,IACA,IACA,KACA;AACJ,WAAO;AAAA,MACL,WAAW,WAAW;AACpB,cAAM,KAAK;AACX,gBAAQ;AAAA,MACV;AAAA,MACA,OAAO,SAAS,QAAQ,KAAK;AAC3B,YAAI,SAAS,CAAC,QAAQ,GAAG,GACrB,QACA,IAAI,QAAQ,QAAQ,GAAG,GACvB,IAAI,cACA,IAAI,IAAI,KAAK,QAAQ,GAAG,IACxB,IAAI,KAAK,UAAU,SAAS,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI;AAC1D,YAAI,CAAC,WAAW,MAAM,KAAK,GAAI,QAAO,UAAU;AAChD,YAAI,MAAM,IAAI;AACZ,mBAAS,UAAU,QAAQ,MAAM;AACjC,cAAI,CAAC,UAAU,mBAAW,QAAQ,MAAM,KAAK,mBAAW,QAAQ,MAAM;AACpE,mBAAO,CAAC,IAAI;AAAA,QAChB;AACA,YAAI,MAAM,IAAI;AACZ,kBAAQ;AACR,cAAI,GAAG;AAEL,mBAAO,UAAU;AACjB,qBAAS,UAAU,QAAQ,MAAM;AACjC,mBAAO,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,UACnC,OAAO;AAEL,qBAAS,UAAU,QAAQ,MAAM;AACjC,mBAAO,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;AACpC,mBAAO,QAAQ;AAAA,UACjB;AACA,mBAAS;AAAA,QACX,WAAW,iBAAiB,UAAU,cAAc,GAAG;AACrD,cAAI;AAGJ,cAAI,EAAE,IAAI,QAAQ,IAAI,UAAU,QAAQ,QAAQ,IAAI,IAAI;AACtD,oBAAQ;AACR,gBAAI,aAAa;AACf,qBAAO,UAAU;AACjB,qBAAO,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,qBAAO,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,qBAAO,QAAQ;AAAA,YACjB,OAAO;AACL,qBAAO,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7B,qBAAO,QAAQ;AACf,qBAAO,UAAU;AACjB,qBAAO,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,YAClC;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAM,CAAC,UAAU,CAAC,mBAAW,QAAQ,MAAM,IAAI;AACjD,iBAAO,MAAM,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,QACnC;AACA,iBAAS,QAAQ,KAAK,GAAG,KAAK;AAAA,MAChC;AAAA,MACA,SAAS,WAAW;AAClB,YAAI,GAAI,QAAO,QAAQ;AACvB,iBAAS;AAAA,MACX;AAAA;AAAA;AAAA,MAGA,OAAO,WAAW;AAChB,eAAO,SAAU,OAAO,OAAO;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAGA,WAAS,UAAU,GAAG,GAAG,KAAK;AAC5B,QAAI,KAAK,UAAU,CAAC,GAChB,KAAK,UAAU,CAAC;AAIpB,QAAI,KAAK,CAAC,GAAG,GAAG,CAAC,GACb,KAAK,eAAe,IAAI,EAAE,GAC1B,OAAO,aAAa,IAAI,EAAE,GAC1B,OAAO,GAAG,CAAC,GACX,cAAc,OAAO,OAAO;AAGhC,QAAI,CAAC,YAAa,QAAO,CAAC,OAAO;AAEjC,QAAI,KAAM,KAAK,OAAO,aAClB,KAAK,CAAC,KAAK,OAAO,aAClB,QAAQ,eAAe,IAAI,EAAE,GAC7B,IAAI,eAAe,IAAI,EAAE,GACzB,IAAI,eAAe,IAAI,EAAE;AAC7B,wBAAoB,GAAG,CAAC;AAGxB,QAAI,IAAI,OACJ,IAAI,aAAa,GAAG,CAAC,GACrB,KAAK,aAAa,GAAG,CAAC,GACtB,KAAK,IAAI,IAAI,MAAM,aAAa,GAAG,CAAC,IAAI;AAE5C,QAAI,KAAK,EAAG;AAEZ,QAAI,IAAI,KAAK,EAAE,GACX,IAAI,eAAe,IAAI,CAAC,IAAI,KAAK,EAAE;AACvC,wBAAoB,GAAG,CAAC;AACxB,QAAI,UAAU,CAAC;AAEf,QAAI,CAAC,IAAK,QAAO;AAGjB,QAAIC,WAAU,EAAE,CAAC,GACbC,WAAU,EAAE,CAAC,GACbC,QAAO,EAAE,CAAC,GACVC,QAAO,EAAE,CAAC,GACV;AAEJ,QAAIF,WAAUD,SAAS,KAAIA,UAASA,WAAUC,UAASA,WAAU;AAEjE,QAAIG,SAAQH,WAAUD,UAClB,QAAQ,IAAII,SAAQ,EAAE,IAAI,SAC1B,WAAW,SAASA,SAAQ;AAEhC,QAAI,CAAC,SAASD,QAAOD,MAAM,KAAIA,OAAMA,QAAOC,OAAMA,QAAO;AAGzD,QAAI,WACE,QACED,QAAOC,QAAO,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,IAAIH,QAAO,IAAI,UAAUE,QAAOC,SACjED,SAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,KAAKC,QAC1BC,SAAQ,MAAMJ,YAAW,EAAE,CAAC,KAAK,EAAE,CAAC,KAAKC,WAAU;AACvD,UAAI,KAAK,eAAe,IAAI,CAAC,IAAI,KAAK,EAAE;AACxC,0BAAoB,IAAI,CAAC;AACzB,aAAO,CAAC,GAAG,UAAU,EAAE,CAAC;AAAA,IAC1B;AAAA,EACF;AAIA,WAAS,KAAK,QAAQ,KAAK;AACzB,QAAI,IAAI,cAAc,SAAS,KAAK,QAChCI,QAAO;AACX,QAAI,SAAS,CAAC,EAAG,CAAAA,SAAQ;AAAA,aAChB,SAAS,EAAG,CAAAA,SAAQ;AAC7B,QAAI,MAAM,CAAC,EAAG,CAAAA,SAAQ;AAAA,aACb,MAAM,EAAG,CAAAA,SAAQ;AAC1B,WAAOA;AAAA,EACT;AAEA,SAAO,aAAK,SAAS,UAAU,aAAa,cAAc,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,SAAS,EAAE,CAAC;AAC7F;;;AChLe,SAAR,aAAiB,GAAG,GAAGC,KAAIC,KAAIC,KAAIC,KAAI;AAC5C,MAAI,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,KAAK,GACL,KAAK,GACL,KAAK,KAAK,IACV,KAAK,KAAK,IACV;AAEJ,MAAIH,MAAK;AACT,MAAI,CAAC,MAAM,IAAI,EAAG;AAClB,OAAK;AACL,MAAI,KAAK,GAAG;AACV,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB,WAAW,KAAK,GAAG;AACjB,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB;AAEA,MAAIE,MAAK;AACT,MAAI,CAAC,MAAM,IAAI,EAAG;AAClB,OAAK;AACL,MAAI,KAAK,GAAG;AACV,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB,WAAW,KAAK,GAAG;AACjB,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB;AAEA,MAAID,MAAK;AACT,MAAI,CAAC,MAAM,IAAI,EAAG;AAClB,OAAK;AACL,MAAI,KAAK,GAAG;AACV,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB,WAAW,KAAK,GAAG;AACjB,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB;AAEA,MAAIE,MAAK;AACT,MAAI,CAAC,MAAM,IAAI,EAAG;AAClB,OAAK;AACL,MAAI,KAAK,GAAG;AACV,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB,WAAW,KAAK,GAAG;AACjB,QAAI,IAAI,GAAI;AACZ,QAAI,IAAI,GAAI,MAAK;AAAA,EACnB;AAEA,MAAI,KAAK,EAAG,GAAE,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,KAAK;AAClD,MAAI,KAAK,EAAG,GAAE,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,IAAI,KAAK,KAAK;AAClD,SAAO;AACT;;;ACpDA,IAAI,UAAU;AAAd,IAAmB,UAAU,CAAC;AAKf,SAAR,cAA+BC,KAAIC,KAAIC,KAAIC,KAAI;AAEpD,WAAS,QAAQ,GAAG,GAAG;AACrB,WAAOH,OAAM,KAAK,KAAKE,OAAMD,OAAM,KAAK,KAAKE;AAAA,EAC/C;AAEA,WAAS,YAAY,MAAM,IAAI,WAAW,QAAQ;AAChD,QAAI,IAAI,GAAG,KAAK;AAChB,QAAI,QAAQ,SACJ,IAAI,OAAO,MAAM,SAAS,QAAQ,KAAK,OAAO,IAAI,SAAS,MAC5D,aAAa,MAAM,EAAE,IAAI,IAAI,YAAY,GAAG;AACjD;AAAG,eAAO,MAAM,MAAM,KAAK,MAAM,IAAIH,MAAKE,KAAI,IAAI,IAAIC,MAAKF,GAAE;AAAA,cACrD,KAAK,IAAI,YAAY,KAAK,OAAO;AAAA,IAC3C,OAAO;AACL,aAAO,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,IAC3B;AAAA,EACF;AAEA,WAAS,OAAO,GAAG,WAAW;AAC5B,WAAO,IAAI,EAAE,CAAC,IAAID,GAAE,IAAI,UAAU,YAAY,IAAI,IAAI,IAChD,IAAI,EAAE,CAAC,IAAIE,GAAE,IAAI,UAAU,YAAY,IAAI,IAAI,IAC/C,IAAI,EAAE,CAAC,IAAID,GAAE,IAAI,UAAU,YAAY,IAAI,IAAI,IAC/C,YAAY,IAAI,IAAI;AAAA,EAC5B;AAEA,WAASG,qBAAoB,GAAG,GAAG;AACjC,WAAO,aAAa,EAAE,GAAG,EAAE,CAAC;AAAA,EAC9B;AAEA,WAAS,aAAa,GAAG,GAAG;AAC1B,QAAI,KAAK,OAAO,GAAG,CAAC,GAChB,KAAK,OAAO,GAAG,CAAC;AACpB,WAAO,OAAO,KAAK,KAAK,KAClB,OAAO,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IACrB,OAAO,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IACrB,OAAO,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IACrB,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EAClB;AAEA,SAAO,SAAS,QAAQ;AACtB,QAAI,eAAe,QACf,eAAe,eAAW,GAC1B,UACA,SACA,MACA,KAAK,KAAK,KACV,IAAI,IAAI,IACR,OACA;AAEJ,QAAI,aAAa;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,aAAS,MAAM,GAAG,GAAG;AACnB,UAAI,QAAQ,GAAG,CAAC,EAAG,cAAa,MAAM,GAAG,CAAC;AAAA,IAC5C;AAEA,aAAS,gBAAgB;AACvB,UAAI,UAAU;AAEd,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC9C,iBAASC,QAAO,QAAQ,CAAC,GAAG,IAAI,GAAG,IAAIA,MAAK,QAAQC,SAAQD,MAAK,CAAC,GAAG,IAAI,IAAI,KAAKC,OAAM,CAAC,GAAG,KAAKA,OAAM,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AACrH,eAAK,IAAI,KAAK,IAAIA,SAAQD,MAAK,CAAC,GAAG,KAAKC,OAAM,CAAC,GAAG,KAAKA,OAAM,CAAC;AAC9D,cAAI,MAAMH,KAAI;AAAE,gBAAI,KAAKA,QAAO,KAAK,OAAOA,MAAK,OAAO,KAAK,OAAOH,MAAK,IAAK,GAAE;AAAA,UAAS,OACpF;AAAE,gBAAI,MAAMG,QAAO,KAAK,OAAOA,MAAK,OAAO,KAAK,OAAOH,MAAK,IAAK,GAAE;AAAA,UAAS;AAAA,QACnF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAGA,aAAS,eAAe;AACtB,qBAAe,cAAc,WAAW,CAAC,GAAG,UAAU,CAAC,GAAG,QAAQ;AAAA,IACpE;AAEA,aAAS,aAAa;AACpB,UAAI,cAAc,cAAc,GAC5B,cAAc,SAAS,aACvBO,YAAW,WAAW,MAAM,QAAQ,GAAG;AAC3C,UAAI,eAAeA,UAAS;AAC1B,eAAO,aAAa;AACpB,YAAI,aAAa;AACf,iBAAO,UAAU;AACjB,sBAAY,MAAM,MAAM,GAAG,MAAM;AACjC,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAIA,UAAS;AACX,yBAAW,UAAUH,sBAAqB,aAAa,aAAa,MAAM;AAAA,QAC5E;AACA,eAAO,WAAW;AAAA,MACpB;AACA,qBAAe,QAAQ,WAAW,UAAU,OAAO;AAAA,IACrD;AAEA,aAAS,YAAY;AACnB,iBAAW,QAAQI;AACnB,UAAI,QAAS,SAAQ,KAAK,OAAO,CAAC,CAAC;AACnC,cAAQ;AACR,WAAK;AACL,WAAK,KAAK;AAAA,IACZ;AAKA,aAAS,UAAU;AACjB,UAAI,UAAU;AACZ,QAAAA,WAAU,KAAK,GAAG;AAClB,YAAI,OAAO,GAAI,cAAa,OAAO;AACnC,iBAAS,KAAK,aAAa,OAAO,CAAC;AAAA,MACrC;AACA,iBAAW,QAAQ;AACnB,UAAI,GAAI,cAAa,QAAQ;AAAA,IAC/B;AAEA,aAASA,WAAU,GAAG,GAAG;AACvB,UAAI,IAAI,QAAQ,GAAG,CAAC;AACpB,UAAI,QAAS,MAAK,KAAK,CAAC,GAAG,CAAC,CAAC;AAC7B,UAAI,OAAO;AACT,cAAM,GAAG,MAAM,GAAG,MAAM;AACxB,gBAAQ;AACR,YAAI,GAAG;AACL,uBAAa,UAAU;AACvB,uBAAa,MAAM,GAAG,CAAC;AAAA,QACzB;AAAA,MACF,OAAO;AACL,YAAI,KAAK,GAAI,cAAa,MAAM,GAAG,CAAC;AAAA,aAC/B;AACH,cAAI,IAAI,CAAC,KAAK,KAAK,IAAI,SAAS,KAAK,IAAI,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,IAAI,SAAS,KAAK,IAAI,SAAS,EAAE,CAAC,CAAC,GACjG,IAAI,CAAC,IAAI,KAAK,IAAI,SAAS,KAAK,IAAI,SAAS,CAAC,CAAC,GAAG,IAAI,KAAK,IAAI,SAAS,KAAK,IAAI,SAAS,CAAC,CAAC,CAAC;AACjG,cAAI,aAAS,GAAG,GAAGR,KAAIC,KAAIC,KAAIC,GAAE,GAAG;AAClC,gBAAI,CAAC,IAAI;AACP,2BAAa,UAAU;AACvB,2BAAa,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,YAC/B;AACA,yBAAa,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC7B,gBAAI,CAAC,EAAG,cAAa,QAAQ;AAC7B,oBAAQ;AAAA,UACV,WAAW,GAAG;AACZ,yBAAa,UAAU;AACvB,yBAAa,MAAM,GAAG,CAAC;AACvB,oBAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF;AACA,WAAK,GAAG,KAAK,GAAG,KAAK;AAAA,IACvB;AAEA,WAAO;AAAA,EACT;AACF;;;ACrKe,SAAR,iBAAmB;AACxB,MAAIM,MAAK,GACLC,MAAK,GACLC,MAAK,KACLC,MAAK,KACL,OACA,aACA;AAEJ,SAAO,OAAO;AAAA,IACZ,QAAQ,SAAS,QAAQ;AACvB,aAAO,SAAS,gBAAgB,SAAS,QAAQ,QAAQ,cAAcH,KAAIC,KAAIC,KAAIC,GAAE,EAAE,cAAc,MAAM;AAAA,IAC7G;AAAA,IACA,QAAQ,SAAS,GAAG;AAClB,aAAO,UAAU,UAAUH,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,QAAQ,cAAc,MAAM,QAAQ,CAAC,CAACH,KAAIC,GAAE,GAAG,CAACC,KAAIC,GAAE,CAAC;AAAA,IAChJ;AAAA,EACF;AACF;;;ACdA,IAAI;AAAJ,IACIC;AADJ,IAEIC;AAFJ,IAGIC;AAEJ,IAAI,eAAe;AAAA,EACjB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,YAAY;AACd;AAEA,SAAS,kBAAkB;AACzB,eAAa,QAAQ;AACrB,eAAa,UAAU;AACzB;AAEA,SAAS,gBAAgB;AACvB,eAAa,QAAQ,aAAa,UAAU;AAC9C;AAEA,SAAS,iBAAiB,QAAQ,KAAK;AACrC,YAAU,SAAS,OAAO;AAC1B,EAAAF,WAAU,QAAQC,WAAU,IAAI,GAAG,GAAGC,WAAU,IAAI,GAAG;AACvD,eAAa,QAAQ;AACvB;AAEA,SAAS,YAAY,QAAQ,KAAK;AAChC,YAAU,SAAS,OAAO;AAC1B,MAAI,SAAS,IAAI,GAAG,GAChB,SAAS,IAAI,GAAG,GAChB,QAAQ,IAAI,SAASF,QAAO,GAC5B,WAAW,IAAI,KAAK,GACpB,WAAW,IAAI,KAAK,GACpB,IAAI,SAAS,UACb,IAAIE,WAAU,SAASD,WAAU,SAAS,UAC1C,IAAIA,WAAU,SAASC,WAAU,SAAS;AAC9C,YAAU,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3C,EAAAF,WAAU,QAAQC,WAAU,QAAQC,WAAU;AAChD;AAEe,SAAR,eAAiBC,SAAQ;AAC9B,cAAY,IAAI,MAAM;AACtB,iBAAOA,SAAQ,YAAY;AAC3B,SAAO,CAAC;AACV;;;AClDA,IAAI,cAAc,CAAC,MAAM,IAAI;AAA7B,IACI,SAAS,EAAC,MAAM,cAAc,YAAwB;AAE3C,SAAR,iBAAiB,GAAG,GAAG;AAC5B,cAAY,CAAC,IAAI;AACjB,cAAY,CAAC,IAAI;AACjB,SAAO,eAAO,MAAM;AACtB;;;ACLA,IAAI,qBAAqB;AAAA,EACvB,SAAS,SAASC,SAAQ,OAAO;AAC/B,WAAO,iBAAiBA,QAAO,UAAU,KAAK;AAAA,EAChD;AAAA,EACA,mBAAmB,SAASA,SAAQ,OAAO;AACzC,QAAI,WAAWA,QAAO,UAAU,IAAI,IAAI,IAAI,SAAS;AACrD,WAAO,EAAE,IAAI,EAAG,KAAI,iBAAiB,SAAS,CAAC,EAAE,UAAU,KAAK,EAAG,QAAO;AAC1E,WAAO;AAAA,EACT;AACF;AAEA,IAAI,uBAAuB;AAAA,EACzB,QAAQ,WAAW;AACjB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAASA,SAAQ,OAAO;AAC7B,WAAO,cAAcA,QAAO,aAAa,KAAK;AAAA,EAChD;AAAA,EACA,YAAY,SAASA,SAAQ,OAAO;AAClC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI,EAAG,KAAI,cAAcA,aAAY,CAAC,GAAG,KAAK,EAAG,QAAO;AACjE,WAAO;AAAA,EACT;AAAA,EACA,YAAY,SAASD,SAAQ,OAAO;AAClC,WAAO,aAAaA,QAAO,aAAa,KAAK;AAAA,EAC/C;AAAA,EACA,iBAAiB,SAASA,SAAQ,OAAO;AACvC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI,EAAG,KAAI,aAAaA,aAAY,CAAC,GAAG,KAAK,EAAG,QAAO;AAChE,WAAO;AAAA,EACT;AAAA,EACA,SAAS,SAASD,SAAQ,OAAO;AAC/B,WAAO,gBAAgBA,QAAO,aAAa,KAAK;AAAA,EAClD;AAAA,EACA,cAAc,SAASA,SAAQ,OAAO;AACpC,QAAIC,eAAcD,QAAO,aAAa,IAAI,IAAI,IAAIC,aAAY;AAC9D,WAAO,EAAE,IAAI,EAAG,KAAI,gBAAgBA,aAAY,CAAC,GAAG,KAAK,EAAG,QAAO;AACnE,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,SAASD,SAAQ,OAAO;AAC1C,QAAI,aAAaA,QAAO,YAAY,IAAI,IAAI,IAAI,WAAW;AAC3D,WAAO,EAAE,IAAI,EAAG,KAAI,iBAAiB,WAAW,CAAC,GAAG,KAAK,EAAG,QAAO;AACnE,WAAO;AAAA,EACT;AACF;AAEA,SAAS,iBAAiB,UAAU,OAAO;AACzC,SAAO,YAAY,qBAAqB,eAAe,SAAS,IAAI,IAC9D,qBAAqB,SAAS,IAAI,EAAE,UAAU,KAAK,IACnD;AACR;AAEA,SAAS,cAAcC,cAAa,OAAO;AACzC,SAAO,iBAASA,cAAa,KAAK,MAAM;AAC1C;AAEA,SAAS,aAAaA,cAAa,OAAO;AACxC,MAAI,IAAI,IAAI;AACZ,WAAS,IAAI,GAAG,IAAIA,aAAY,QAAQ,IAAI,GAAG,KAAK;AAClD,SAAK,iBAASA,aAAY,CAAC,GAAG,KAAK;AACnC,QAAI,OAAO,EAAG,QAAO;AACrB,QAAI,IAAI,GAAG;AACT,WAAK,iBAASA,aAAY,CAAC,GAAGA,aAAY,IAAI,CAAC,CAAC;AAChD,UACE,KAAK,KACL,MAAM,MACN,MAAM,OACL,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,KAAK,MAAM,IAAI,CAAC,KAAK,WAAW;AAEhE,eAAO;AAAA,IACX;AACA,SAAK;AAAA,EACP;AACA,SAAO;AACT;AAEA,SAAS,gBAAgBA,cAAa,OAAO;AAC3C,SAAO,CAAC,CAAC,wBAAgBA,aAAY,IAAI,WAAW,GAAG,aAAa,KAAK,CAAC;AAC5E;AAEA,SAAS,YAAY,MAAM;AACzB,SAAO,OAAO,KAAK,IAAI,YAAY,GAAG,KAAK,IAAI,GAAG;AACpD;AAEA,SAAS,aAAa,OAAO;AAC3B,SAAO,CAAC,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI,OAAO;AAChD;AAEe,SAAR,iBAAiBD,SAAQ,OAAO;AACrC,UAAQA,WAAU,mBAAmB,eAAeA,QAAO,IAAI,IACzD,mBAAmBA,QAAO,IAAI,IAC9B,kBAAkBA,SAAQ,KAAK;AACvC;;;AC7FA,SAAS,WAAWE,KAAIC,KAAI,IAAI;AAC9B,MAAI,IAAI,MAAMD,KAAIC,MAAK,SAAS,EAAE,EAAE,OAAOA,GAAE;AAC7C,SAAO,SAAS,GAAG;AAAE,WAAO,EAAE,IAAI,SAASC,IAAG;AAAE,aAAO,CAAC,GAAGA,EAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AACrE;AAEA,SAAS,WAAWC,KAAIC,KAAI,IAAI;AAC9B,MAAI,IAAI,MAAMD,KAAIC,MAAK,SAAS,EAAE,EAAE,OAAOA,GAAE;AAC7C,SAAO,SAAS,GAAG;AAAE,WAAO,EAAE,IAAI,SAASC,IAAG;AAAE,aAAO,CAACA,IAAG,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AACrE;AAEe,SAAR,YAA6B;AAClC,MAAID,KAAID,KAAIG,KAAIC,KACZN,KAAID,KAAIQ,KAAIC,KACZ,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,KAChC,GAAG,GAAG,GAAG,GACT,YAAY;AAEhB,WAASC,aAAY;AACnB,WAAO,EAAC,MAAM,mBAAmB,aAAa,MAAM,EAAC;AAAA,EACvD;AAEA,WAAS,QAAQ;AACf,WAAO,MAAM,KAAKH,MAAK,EAAE,IAAI,IAAID,KAAI,EAAE,EAAE,IAAI,CAAC,EACzC,OAAO,MAAM,KAAKG,MAAK,EAAE,IAAI,IAAID,KAAI,EAAE,EAAE,IAAI,CAAC,CAAC,EAC/C,OAAO,MAAM,KAAKL,MAAK,EAAE,IAAI,IAAIC,KAAI,EAAE,EAAE,OAAO,SAASC,IAAG;AAAE,aAAO,IAAIA,KAAI,EAAE,IAAI;AAAA,IAAS,CAAC,EAAE,IAAI,CAAC,CAAC,EACrG,OAAO,MAAM,KAAKL,MAAK,EAAE,IAAI,IAAIC,KAAI,EAAE,EAAE,OAAO,SAASC,IAAG;AAAE,aAAO,IAAIA,KAAI,EAAE,IAAI;AAAA,IAAS,CAAC,EAAE,IAAI,CAAC,CAAC;AAAA,EAC5G;AAEA,EAAAQ,WAAU,QAAQ,WAAW;AAC3B,WAAO,MAAM,EAAE,IAAI,SAASC,cAAa;AAAE,aAAO,EAAC,MAAM,cAAc,aAAaA,aAAW;AAAA,IAAG,CAAC;AAAA,EACrG;AAEA,EAAAD,WAAU,UAAU,WAAW;AAC7B,WAAO;AAAA,MACL,MAAM;AAAA,MACN,aAAa;AAAA,QACX,EAAEH,GAAE,EAAE;AAAA,UACN,EAAEC,GAAE,EAAE,MAAM,CAAC;AAAA,UACb,EAAEF,GAAE,EAAE,QAAQ,EAAE,MAAM,CAAC;AAAA,UACvB,EAAEG,GAAE,EAAE,QAAQ,EAAE,MAAM,CAAC;AAAA,QAAC;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAEA,EAAAC,WAAU,SAAS,SAAS,GAAG;AAC7B,QAAI,CAAC,UAAU,OAAQ,QAAOA,WAAU,YAAY;AACpD,WAAOA,WAAU,YAAY,CAAC,EAAE,YAAY,CAAC;AAAA,EAC/C;AAEA,EAAAA,WAAU,cAAc,SAAS,GAAG;AAClC,QAAI,CAAC,UAAU,OAAQ,QAAO,CAAC,CAACH,KAAIE,GAAE,GAAG,CAACH,KAAIE,GAAE,CAAC;AACjD,IAAAD,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGD,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,IAAAG,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGD,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,QAAID,MAAKD,IAAI,KAAIC,KAAIA,MAAKD,KAAIA,MAAK;AACnC,QAAIG,MAAKD,IAAI,KAAIC,KAAIA,MAAKD,KAAIA,MAAK;AACnC,WAAOE,WAAU,UAAU,SAAS;AAAA,EACtC;AAEA,EAAAA,WAAU,cAAc,SAAS,GAAG;AAClC,QAAI,CAAC,UAAU,OAAQ,QAAO,CAAC,CAACP,KAAIH,GAAE,GAAG,CAACI,KAAIH,GAAE,CAAC;AACjD,IAAAE,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,IAAAJ,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3B,QAAIE,MAAKC,IAAI,KAAID,KAAIA,MAAKC,KAAIA,MAAK;AACnC,QAAIJ,MAAKC,IAAI,KAAID,KAAIA,MAAKC,KAAIA,MAAK;AACnC,WAAOS,WAAU,UAAU,SAAS;AAAA,EACtC;AAEA,EAAAA,WAAU,OAAO,SAAS,GAAG;AAC3B,QAAI,CAAC,UAAU,OAAQ,QAAOA,WAAU,UAAU;AAClD,WAAOA,WAAU,UAAU,CAAC,EAAE,UAAU,CAAC;AAAA,EAC3C;AAEA,EAAAA,WAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU,OAAQ,QAAO,CAAC,IAAI,EAAE;AACrC,SAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;AACrB,WAAOA;AAAA,EACT;AAEA,EAAAA,WAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU,OAAQ,QAAO,CAAC,IAAI,EAAE;AACrC,SAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;AACrB,WAAOA;AAAA,EACT;AAEA,EAAAA,WAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU,OAAQ,QAAO;AAC9B,gBAAY,CAAC;AACb,QAAI,WAAWV,KAAIC,KAAI,EAAE;AACzB,QAAI,WAAWE,KAAIC,KAAI,SAAS;AAChC,QAAI,WAAWK,KAAID,KAAI,EAAE;AACzB,QAAI,WAAWD,KAAID,KAAI,SAAS;AAChC,WAAOI;AAAA,EACT;AAEA,SAAOA,WACF,YAAY,CAAC,CAAC,MAAM,MAAM,OAAO,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC,EACxD,YAAY,CAAC,CAAC,MAAM,MAAM,OAAO,GAAG,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC;AAC/D;AAEO,SAAS,cAAc;AAC5B,SAAO,UAAU,EAAE;AACrB;;;ACtGe,SAAR,oBAAiB,GAAG,GAAG;AAC5B,MAAIE,MAAK,EAAE,CAAC,IAAI,SACZC,MAAK,EAAE,CAAC,IAAI,SACZC,MAAK,EAAE,CAAC,IAAI,SACZC,MAAK,EAAE,CAAC,IAAI,SACZ,MAAM,IAAIF,GAAE,GACZ,MAAM,IAAIA,GAAE,GACZ,MAAM,IAAIE,GAAE,GACZ,MAAM,IAAIA,GAAE,GACZ,MAAM,MAAM,IAAIH,GAAE,GAClB,MAAM,MAAM,IAAIA,GAAE,GAClB,MAAM,MAAM,IAAIE,GAAE,GAClB,MAAM,MAAM,IAAIA,GAAE,GAClB,IAAI,IAAI,KAAK,KAAK,SAASC,MAAKF,GAAE,IAAI,MAAM,MAAM,SAASC,MAAKF,GAAE,CAAC,CAAC,GACpE,IAAI,IAAI,CAAC;AAEb,MAAI,cAAc,IAAI,SAAS,GAAG;AAChC,QAAI,IAAI,IAAI,KAAK,CAAC,IAAI,GAClB,IAAI,IAAI,IAAI,CAAC,IAAI,GACjB,IAAI,IAAI,MAAM,IAAI,KAClB,IAAI,IAAI,MAAM,IAAI,KAClB,IAAI,IAAI,MAAM,IAAI;AACtB,WAAO;AAAA,MACL,MAAM,GAAG,CAAC,IAAI;AAAA,MACd,MAAM,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI;AAAA,IAClC;AAAA,EACF,IAAI,WAAW;AACb,WAAO,CAACA,MAAK,SAASC,MAAK,OAAO;AAAA,EACpC;AAEA,cAAY,WAAW;AAEvB,SAAO;AACT;;;ACnCA,IAAO,mBAAQ,OAAK;;;ACIpB,IAAIG,WAAU,IAAI,MAAM;AAAxB,IACIC,eAAc,IAAI,MAAM;AAD5B,IAEI;AAFJ,IAGI;AAHJ,IAIIC;AAJJ,IAKIC;AAEJ,IAAIC,cAAa;AAAA,EACf,OAAO;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc,WAAW;AACvB,IAAAA,YAAW,YAAYC;AACvB,IAAAD,YAAW,UAAUE;AAAA,EACvB;AAAA,EACA,YAAY,WAAW;AACrB,IAAAF,YAAW,YAAYA,YAAW,UAAUA,YAAW,QAAQ;AAC/D,IAAAJ,SAAQ,IAAI,IAAIC,YAAW,CAAC;AAC5B,IAAAA,eAAc,IAAI,MAAM;AAAA,EAC1B;AAAA,EACA,QAAQ,WAAW;AACjB,QAAI,OAAOD,WAAU;AACrB,IAAAA,WAAU,IAAI,MAAM;AACpB,WAAO;AAAA,EACT;AACF;AAEA,SAASK,iBAAgB;AACvB,EAAAD,YAAW,QAAQG;AACrB;AAEA,SAASA,gBAAe,GAAG,GAAG;AAC5B,EAAAH,YAAW,QAAQI;AACnB,QAAMN,MAAK,GAAG,MAAMC,MAAK;AAC3B;AAEA,SAASK,WAAU,GAAG,GAAG;AACvB,EAAAP,aAAY,IAAIE,MAAK,IAAID,MAAK,CAAC;AAC/B,EAAAA,MAAK,GAAGC,MAAK;AACf;AAEA,SAASG,eAAc;AACrB,EAAAE,WAAU,KAAK,GAAG;AACpB;AAEA,IAAOC,gBAAQL;;;AC/Cf,IAAIM,MAAK;AAAT,IACIC,MAAKD;AADT,IAEI,KAAK,CAACA;AAFV,IAGI,KAAK;AAET,IAAIE,gBAAe;AAAA,EACjB,OAAOC;AAAA,EACP,WAAW;AAAA,EACX,SAAS;AAAA,EACT,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,QAAQ,WAAW;AACjB,QAAI,SAAS,CAAC,CAACH,KAAIC,GAAE,GAAG,CAAC,IAAI,EAAE,CAAC;AAChC,SAAK,KAAK,EAAEA,MAAKD,MAAK;AACtB,WAAO;AAAA,EACT;AACF;AAEA,SAASG,aAAY,GAAG,GAAG;AACzB,MAAI,IAAIH,IAAI,CAAAA,MAAK;AACjB,MAAI,IAAI,GAAI,MAAK;AACjB,MAAI,IAAIC,IAAI,CAAAA,MAAK;AACjB,MAAI,IAAI,GAAI,MAAK;AACnB;AAEA,IAAOG,kBAAQF;;;ACvBf,IAAIG,MAAK;AAAT,IACIC,MAAK;AADT,IAEIC,MAAK;AAFT,IAGIC,MAAK;AAHT,IAIIC,MAAK;AAJT,IAKIC,MAAK;AALT,IAMIC,MAAK;AANT,IAOIC,MAAK;AAPT,IAQIC,MAAK;AART,IASIC;AATJ,IAUIC;AAVJ,IAWIC;AAXJ,IAYIC;AAEJ,IAAIC,kBAAiB;AAAA,EACnB,OAAOC;AAAA,EACP,WAAWC;AAAA,EACX,SAASC;AAAA,EACT,cAAc,WAAW;AACvB,IAAAH,gBAAe,YAAYI;AAC3B,IAAAJ,gBAAe,UAAUK;AAAA,EAC3B;AAAA,EACA,YAAY,WAAW;AACrB,IAAAL,gBAAe,QAAQC;AACvB,IAAAD,gBAAe,YAAYE;AAC3B,IAAAF,gBAAe,UAAUG;AAAA,EAC3B;AAAA,EACA,QAAQ,WAAW;AACjB,QAAI,WAAWR,MAAK,CAACF,MAAKE,KAAID,MAAKC,GAAE,IAC/BH,MAAK,CAACF,MAAKE,KAAID,MAAKC,GAAE,IACtBH,MAAK,CAACF,MAAKE,KAAID,MAAKC,GAAE,IACtB,CAAC,KAAK,GAAG;AACf,IAAAF,MAAKC,MAAKC,MACVC,MAAKC,MAAKC,MACVC,MAAKC,MAAKC,MAAK;AACf,WAAO;AAAA,EACT;AACF;AAEA,SAASM,eAAc,GAAG,GAAG;AAC3B,EAAAd,OAAM;AACN,EAAAC,OAAM;AACN,IAAEC;AACJ;AAEA,SAASa,qBAAoB;AAC3B,EAAAF,gBAAe,QAAQ;AACzB;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,EAAAA,gBAAe,QAAQ;AACvB,EAAAC,eAAcH,MAAK,GAAGC,MAAK,CAAC;AAC9B;AAEA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,MAAI,KAAK,IAAID,KAAI,KAAK,IAAIC,KAAI,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE;AACxD,EAAAT,OAAM,KAAKQ,MAAK,KAAK;AACrB,EAAAP,OAAM,KAAKQ,MAAK,KAAK;AACrB,EAAAP,OAAM;AACN,EAAAS,eAAcH,MAAK,GAAGC,MAAK,CAAC;AAC9B;AAEA,SAASI,mBAAkB;AACzB,EAAAH,gBAAe,QAAQC;AACzB;AAEA,SAASG,qBAAoB;AAC3B,EAAAJ,gBAAe,QAAQ;AACzB;AAEA,SAASK,mBAAkB;AACzB,oBAAkBT,MAAKC,IAAG;AAC5B;AAEA,SAAS,uBAAuB,GAAG,GAAG;AACpC,EAAAG,gBAAe,QAAQ;AACvB,EAAAC,eAAcL,OAAME,MAAK,GAAGD,OAAME,MAAK,CAAC;AAC1C;AAEA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,MAAI,KAAK,IAAID,KACT,KAAK,IAAIC,KACT,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE;AAE9B,EAAAT,OAAM,KAAKQ,MAAK,KAAK;AACrB,EAAAP,OAAM,KAAKQ,MAAK,KAAK;AACrB,EAAAP,OAAM;AAEN,MAAIO,MAAK,IAAID,MAAK;AAClB,EAAAL,OAAM,KAAKK,MAAK;AAChB,EAAAJ,OAAM,KAAKK,MAAK;AAChB,EAAAJ,OAAM,IAAI;AACV,EAAAM,eAAcH,MAAK,GAAGC,MAAK,CAAC;AAC9B;AAEA,IAAOO,oBAAQN;;;AChGA,SAAR,YAA6B,SAAS;AAC3C,OAAK,WAAW;AAClB;AAEA,YAAY,YAAY;AAAA,EACtB,SAAS;AAAA,EACT,aAAa,SAAS,GAAG;AACvB,WAAO,KAAK,UAAU,GAAG;AAAA,EAC3B;AAAA,EACA,cAAc,WAAW;AACvB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,YAAY,WAAW;AACrB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,WAAW,WAAW;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,KAAK,UAAU,EAAG,MAAK,SAAS,UAAU;AAC9C,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO,SAAS,GAAG,GAAG;AACpB,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,GAAG,CAAC;AACzB,aAAK,SAAS;AACd;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,SAAS,OAAO,GAAG,CAAC;AACzB;AAAA,MACF;AAAA,MACA,SAAS;AACP,aAAK,SAAS,OAAO,IAAI,KAAK,SAAS,CAAC;AACxC,aAAK,SAAS,IAAI,GAAG,GAAG,KAAK,SAAS,GAAG,GAAG;AAC5C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,QAAQ;AACV;;;ACxCA,IAAIO,aAAY,IAAI,MAAM;AAA1B,IACI;AADJ,IAEIC;AAFJ,IAGIC;AAHJ,IAIIC;AAJJ,IAKIC;AAEJ,IAAIC,gBAAe;AAAA,EACjB,OAAO;AAAA,EACP,WAAW,WAAW;AACpB,IAAAA,cAAa,QAAQC;AAAA,EACvB;AAAA,EACA,SAAS,WAAW;AAClB,QAAI,WAAY,CAAAC,aAAYN,MAAKC,IAAG;AACpC,IAAAG,cAAa,QAAQ;AAAA,EACvB;AAAA,EACA,cAAc,WAAW;AACvB,iBAAa;AAAA,EACf;AAAA,EACA,YAAY,WAAW;AACrB,iBAAa;AAAA,EACf;AAAA,EACA,QAAQ,WAAW;AACjB,QAAI,SAAS,CAACL;AACd,IAAAA,aAAY,IAAI,MAAM;AACtB,WAAO;AAAA,EACT;AACF;AAEA,SAASM,kBAAiB,GAAG,GAAG;AAC9B,EAAAD,cAAa,QAAQE;AACrB,EAAAN,OAAME,MAAK,GAAGD,OAAME,MAAK;AAC3B;AAEA,SAASG,aAAY,GAAG,GAAG;AACzB,EAAAJ,OAAM,GAAGC,OAAM;AACf,EAAAJ,WAAU,IAAI,KAAKG,MAAKA,MAAKC,MAAKA,GAAE,CAAC;AACrC,EAAAD,MAAK,GAAGC,MAAK;AACf;AAEA,IAAO,kBAAQC;;;AC3Cf,IAAI;AAAJ,IAAiB;AAAjB,IAA8B;AAA9B,IAA2C;AAE3C,IAAqB,aAArB,MAAgC;AAAA,EAC9B,YAAY,QAAQ;AAClB,SAAK,UAAU,UAAU,OAAO,SAAS,YAAY,MAAM;AAC3D,SAAK,UAAU;AACf,SAAK,IAAI;AAAA,EACX;AAAA,EACA,YAAY,GAAG;AACb,SAAK,UAAU,CAAC;AAChB,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,aAAa;AACX,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,YAAY;AACV,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,UAAU;AACR,QAAI,KAAK,UAAU,EAAG,MAAK,KAAK;AAChC,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,MAAM,GAAG,GAAG;AACV,YAAQ,KAAK,QAAQ;AAAA,MACnB,KAAK,GAAG;AACN,aAAK,WAAW,CAAC,IAAI,CAAC;AACtB,aAAK,SAAS;AACd;AAAA,MACF;AAAA,MACA,KAAK,GAAG;AACN,aAAK,WAAW,CAAC,IAAI,CAAC;AACtB;AAAA,MACF;AAAA,MACA,SAAS;AACP,aAAK,WAAW,CAAC,IAAI,CAAC;AACtB,YAAI,KAAK,YAAY,eAAe,KAAK,YAAY,aAAa;AAChE,gBAAM,IAAI,KAAK;AACf,gBAAM,IAAI,KAAK;AACf,eAAK,IAAI;AACT,eAAK,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC;AAC1E,wBAAc;AACd,wBAAc,KAAK;AACnB,wBAAc,KAAK;AACnB,eAAK,IAAI;AAAA,QACX;AACA,aAAK,KAAK;AACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS;AACP,UAAM,SAAS,KAAK;AACpB,SAAK,IAAI;AACT,WAAO,OAAO,SAAS,SAAS;AAAA,EAClC;AACF;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI,IAAI;AACR,OAAK,KAAK,QAAQ,CAAC;AACnB,aAAW,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AACzC,SAAK,KAAK,UAAU,CAAC,IAAI,QAAQ,CAAC;AAAA,EACpC;AACF;AAEA,SAAS,YAAY,QAAQ;AAC3B,QAAM,IAAI,KAAK,MAAM,MAAM;AAC3B,MAAI,EAAE,KAAK,GAAI,OAAM,IAAI,WAAW,mBAAmB,MAAM,EAAE;AAC/D,MAAI,IAAI,GAAI,QAAO;AACnB,MAAI,MAAM,aAAa;AACrB,UAAM,IAAI,MAAM;AAChB,kBAAc;AACd,kBAAc,SAASG,QAAO,SAAS;AACrC,UAAI,IAAI;AACR,WAAK,KAAK,QAAQ,CAAC;AACnB,iBAAW,IAAI,QAAQ,QAAQ,IAAI,GAAG,EAAE,GAAG;AACzC,aAAK,KAAK,KAAK,MAAM,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC;AAAA,MACxD;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;AC5Ee,SAAR,aAAiBC,aAAY,SAAS;AAC3C,MAAI,SAAS,GACT,cAAc,KACd,kBACA;AAEJ,WAAS,KAAKC,SAAQ;AACpB,QAAIA,SAAQ;AACV,UAAI,OAAO,gBAAgB,WAAY,eAAc,YAAY,CAAC,YAAY,MAAM,MAAM,SAAS,CAAC;AACpG,qBAAOA,SAAQ,iBAAiB,aAAa,CAAC;AAAA,IAChD;AACA,WAAO,cAAc,OAAO;AAAA,EAC9B;AAEA,OAAK,OAAO,SAASA,SAAQ;AAC3B,mBAAOA,SAAQ,iBAAiBC,aAAQ,CAAC;AACzC,WAAOA,cAAS,OAAO;AAAA,EACzB;AAEA,OAAK,UAAU,SAASD,SAAQ;AAC9B,mBAAOA,SAAQ,iBAAiB,eAAW,CAAC;AAC5C,WAAO,gBAAY,OAAO;AAAA,EAC5B;AAEA,OAAK,SAAS,SAASA,SAAQ;AAC7B,mBAAOA,SAAQ,iBAAiBE,eAAU,CAAC;AAC3C,WAAOA,gBAAW,OAAO;AAAA,EAC3B;AAEA,OAAK,WAAW,SAASF,SAAQ;AAC/B,mBAAOA,SAAQ,iBAAiBG,iBAAY,CAAC;AAC7C,WAAOA,kBAAa,OAAO;AAAA,EAC7B;AAEA,OAAK,aAAa,SAAS,GAAG;AAC5B,QAAI,CAAC,UAAU,OAAQ,QAAOJ;AAC9B,uBAAmB,KAAK,QAAQA,cAAa,MAAM,qBAAaA,cAAa,GAAG;AAChF,WAAO;AAAA,EACT;AAEA,OAAK,UAAU,SAAS,GAAG;AACzB,QAAI,CAAC,UAAU,OAAQ,QAAO;AAC9B,oBAAgB,KAAK,QAAQ,UAAU,MAAM,IAAI,WAAW,MAAM,KAAK,IAAI,YAAY,UAAU,CAAC;AAClG,QAAI,OAAO,gBAAgB,WAAY,eAAc,YAAY,WAAW;AAC5E,WAAO;AAAA,EACT;AAEA,OAAK,cAAc,SAAS,GAAG;AAC7B,QAAI,CAAC,UAAU,OAAQ,QAAO;AAC9B,kBAAc,OAAO,MAAM,aAAa,KAAK,cAAc,YAAY,CAAC,CAAC,GAAG,CAAC;AAC7E,WAAO;AAAA,EACT;AAEA,OAAK,SAAS,SAAS,GAAG;AACxB,QAAI,CAAC,UAAU,OAAQ,QAAO;AAC9B,QAAI,KAAK,KAAM,UAAS;AAAA,SACnB;AACH,YAAM,IAAI,KAAK,MAAM,CAAC;AACtB,UAAI,EAAE,KAAK,GAAI,OAAM,IAAI,WAAW,mBAAmB,CAAC,EAAE;AAC1D,eAAS;AAAA,IACX;AACA,QAAI,YAAY,KAAM,iBAAgB,IAAI,WAAW,MAAM;AAC3D,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,WAAWA,WAAU,EAAE,OAAO,MAAM,EAAE,QAAQ,OAAO;AACnE;;;AC3Ee,SAAR,kBAAiB,SAAS;AAC/B,SAAO;AAAA,IACL,QAAQ,YAAY,OAAO;AAAA,EAC7B;AACF;AAEO,SAAS,YAAY,SAAS;AACnC,SAAO,SAAS,QAAQ;AACtB,QAAI,IAAI,IAAI;AACZ,aAAS,OAAO,QAAS,GAAE,GAAG,IAAI,QAAQ,GAAG;AAC7C,MAAE,SAAS;AACX,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAkB;AAAC;AAE5B,gBAAgB,YAAY;AAAA,EAC1B,aAAa;AAAA,EACb,OAAO,SAAS,GAAG,GAAG;AAAE,SAAK,OAAO,MAAM,GAAG,CAAC;AAAA,EAAG;AAAA,EACjD,QAAQ,WAAW;AAAE,SAAK,OAAO,OAAO;AAAA,EAAG;AAAA,EAC3C,WAAW,WAAW;AAAE,SAAK,OAAO,UAAU;AAAA,EAAG;AAAA,EACjD,SAAS,WAAW;AAAE,SAAK,OAAO,QAAQ;AAAA,EAAG;AAAA,EAC7C,cAAc,WAAW;AAAE,SAAK,OAAO,aAAa;AAAA,EAAG;AAAA,EACvD,YAAY,WAAW;AAAE,SAAK,OAAO,WAAW;AAAA,EAAG;AACrD;;;ACtBA,SAAS,IAAIK,aAAY,WAAWC,SAAQ;AAC1C,MAAI,OAAOD,YAAW,cAAcA,YAAW,WAAW;AAC1D,EAAAA,YAAW,MAAM,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AACtC,MAAI,QAAQ,KAAM,CAAAA,YAAW,WAAW,IAAI;AAC5C,iBAAUC,SAAQD,YAAW,OAAOE,eAAY,CAAC;AACjD,YAAUA,gBAAa,OAAO,CAAC;AAC/B,MAAI,QAAQ,KAAM,CAAAF,YAAW,WAAW,IAAI;AAC5C,SAAOA;AACT;AAEO,SAAS,UAAUA,aAAY,QAAQC,SAAQ;AACpD,SAAO,IAAID,aAAY,SAAS,GAAG;AACjC,QAAI,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,GAC9B,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,GAC9B,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAC7D,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM,GACpD,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM;AACxD,IAAAA,YAAW,MAAM,MAAM,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA,EAC5C,GAAGC,OAAM;AACX;AAEO,SAAS,QAAQD,aAAY,MAAMC,SAAQ;AAChD,SAAO,UAAUD,aAAY,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,GAAGC,OAAM;AACrD;AAEO,SAAS,SAASD,aAAY,OAAOC,SAAQ;AAClD,SAAO,IAAID,aAAY,SAAS,GAAG;AACjC,QAAI,IAAI,CAAC,OACL,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IACzB,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM,GACpC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AACnB,IAAAA,YAAW,MAAM,MAAM,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA,EAC5C,GAAGC,OAAM;AACX;AAEO,SAAS,UAAUD,aAAY,QAAQC,SAAQ;AACpD,SAAO,IAAID,aAAY,SAAS,GAAG;AACjC,QAAI,IAAI,CAAC,QACL,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,IACzB,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GACf,KAAK,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM;AACxC,IAAAA,YAAW,MAAM,MAAM,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA,EAC5C,GAAGC,OAAM;AACX;;;AC1CA,IAAI,WAAW;AAAf,IACI,iBAAiB,IAAI,KAAK,OAAO;AAEtB,SAAR,iBAAiB,SAAS,QAAQ;AACvC,SAAO,CAAC,SAAS,SAAS,SAAS,MAAM,IAAI,aAAa,OAAO;AACnE;AAEA,SAAS,aAAa,SAAS;AAC7B,SAAO,YAAY;AAAA,IACjB,OAAO,SAAS,GAAG,GAAG;AACpB,UAAI,QAAQ,GAAG,CAAC;AAChB,WAAK,OAAO,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF,CAAC;AACH;AAEA,SAAS,SAAS,SAAS,QAAQ;AAEjC,WAAS,eAAeE,KAAIC,KAAIC,UAAS,IAAI,IAAI,IAAIC,KAAIC,KAAIC,UAAS,IAAI,IAAI,IAAI,OAAO,QAAQ;AAC/F,QAAI,KAAKF,MAAKH,KACV,KAAKI,MAAKH,KACV,KAAK,KAAK,KAAK,KAAK;AACxB,QAAI,KAAK,IAAI,UAAU,SAAS;AAC9B,UAAI,IAAI,KAAK,IACT,IAAI,KAAK,IACT,IAAI,KAAK,IACT,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,GAC9B,OAAO,KAAK,KAAK,CAAC,GAClBK,WAAU,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,WAAW,IAAIJ,WAAUG,QAAO,IAAI,WAAWH,WAAUG,YAAW,IAAI,MAAM,GAAG,CAAC,GAC9G,IAAI,QAAQC,UAAS,IAAI,GACzB,KAAK,EAAE,CAAC,GACR,KAAK,EAAE,CAAC,GACR,MAAM,KAAKN,KACX,MAAM,KAAKC,KACX,KAAK,KAAK,MAAM,KAAK;AACzB,UAAI,KAAK,KAAK,KAAK,UACZ,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,GAAG,IAAI,OACxC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,gBAAgB;AACnD,uBAAeD,KAAIC,KAAIC,UAAS,IAAI,IAAI,IAAI,IAAI,IAAII,UAAS,KAAK,GAAG,KAAK,GAAG,GAAG,OAAO,MAAM;AAC7F,eAAO,MAAM,IAAI,EAAE;AACnB,uBAAe,IAAI,IAAIA,UAAS,GAAG,GAAG,GAAGH,KAAIC,KAAIC,UAAS,IAAI,IAAI,IAAI,OAAO,MAAM;AAAA,MACrF;AAAA,IACF;AAAA,EACF;AACA,SAAO,SAAS,QAAQ;AACtB,QAAIE,WAAUC,MAAKC,MAAK,KAAK,KAAK,KAC9BP,UAASF,KAAIC,KAAI,IAAI,IAAI;AAE7B,QAAI,iBAAiB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,WAAW;AAAE,eAAO,aAAa;AAAG,uBAAe,YAAY;AAAA,MAAW;AAAA,MACxF,YAAY,WAAW;AAAE,eAAO,WAAW;AAAG,uBAAe,YAAY;AAAA,MAAW;AAAA,IACtF;AAEA,aAAS,MAAM,GAAG,GAAG;AACnB,UAAI,QAAQ,GAAG,CAAC;AAChB,aAAO,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACzB;AAEA,aAAS,YAAY;AACnB,MAAAD,MAAK;AACL,qBAAe,QAAQU;AACvB,aAAO,UAAU;AAAA,IACnB;AAEA,aAASA,WAAU,QAAQ,KAAK;AAC9B,UAAI,IAAI,UAAU,CAAC,QAAQ,GAAG,CAAC,GAAG,IAAI,QAAQ,QAAQ,GAAG;AACzD,qBAAeV,KAAIC,KAAIC,UAAS,IAAI,IAAI,IAAIF,MAAK,EAAE,CAAC,GAAGC,MAAK,EAAE,CAAC,GAAGC,WAAU,QAAQ,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,UAAU,MAAM;AACrI,aAAO,MAAMF,KAAIC,GAAE;AAAA,IACrB;AAEA,aAAS,UAAU;AACjB,qBAAe,QAAQ;AACvB,aAAO,QAAQ;AAAA,IACjB;AAEA,aAAS,YAAY;AACnB,gBAAU;AACV,qBAAe,QAAQ;AACvB,qBAAe,UAAU;AAAA,IAC3B;AAEA,aAAS,UAAU,QAAQ,KAAK;AAC9B,MAAAS,WAAUH,YAAW,QAAQ,GAAG,GAAGC,OAAMR,KAAIS,OAAMR,KAAI,MAAM,IAAI,MAAM,IAAI,MAAM;AACjF,qBAAe,QAAQS;AAAA,IACzB;AAEA,aAAS,UAAU;AACjB,qBAAeV,KAAIC,KAAIC,UAAS,IAAI,IAAI,IAAIM,MAAKC,MAAKF,WAAU,KAAK,KAAK,KAAK,UAAU,MAAM;AAC/F,qBAAe,UAAU;AACzB,cAAQ;AAAA,IACV;AAEA,WAAO;AAAA,EACT;AACF;;;AC1FA,IAAI,mBAAmB,YAAY;AAAA,EACjC,OAAO,SAAS,GAAG,GAAG;AACpB,SAAK,OAAO,MAAM,IAAI,SAAS,IAAI,OAAO;AAAA,EAC5C;AACF,CAAC;AAED,SAAS,gBAAgB,QAAQ;AAC/B,SAAO,YAAY;AAAA,IACjB,OAAO,SAAS,GAAG,GAAG;AACpB,UAAI,IAAI,OAAO,GAAG,CAAC;AACnB,aAAO,KAAK,OAAO,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACrC;AAAA,EACF,CAAC;AACH;AAEA,SAAS,eAAe,GAAG,IAAI,IAAI,IAAI,IAAI;AACzC,WAAS,UAAU,GAAG,GAAG;AACvB,SAAK;AAAI,SAAK;AACd,WAAO,CAAC,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC;AAAA,EAChC;AACA,YAAU,SAAS,SAAS,GAAG,GAAG;AAChC,WAAO,EAAE,IAAI,MAAM,IAAI,KAAK,KAAK,KAAK,IAAI,EAAE;AAAA,EAC9C;AACA,SAAO;AACT;AAEA,SAAS,qBAAqB,GAAG,IAAI,IAAI,IAAI,IAAI,OAAO;AACtD,MAAI,CAAC,MAAO,QAAO,eAAe,GAAG,IAAI,IAAI,IAAI,EAAE;AACnD,MAAI,WAAW,IAAI,KAAK,GACpB,WAAW,IAAI,KAAK,GACpB,IAAI,WAAW,GACf,IAAI,WAAW,GACf,KAAK,WAAW,GAChB,KAAK,WAAW,GAChB,MAAM,WAAW,KAAK,WAAW,MAAM,GACvC,MAAM,WAAW,KAAK,WAAW,MAAM;AAC3C,WAAS,UAAU,GAAG,GAAG;AACvB,SAAK;AAAI,SAAK;AACd,WAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC;AAAA,EAChD;AACA,YAAU,SAAS,SAAS,GAAG,GAAG;AAChC,WAAO,CAAC,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI,KAAK,EAAE;AAAA,EAClE;AACA,SAAO;AACT;AAEe,SAAR,WAA4B,SAAS;AAC1C,SAAO,kBAAkB,WAAW;AAAE,WAAO;AAAA,EAAS,CAAC,EAAE;AAC3D;AAEO,SAAS,kBAAkB,WAAW;AAC3C,MAAI,SACA,IAAI,KACJ,IAAI,KAAK,IAAI,KACb,SAAS,GAAG,MAAM,GAClB,cAAc,GAAG,WAAW,GAAG,aAAa,GAAG,QAC/C,QAAQ,GACR,KAAK,GACL,KAAK,GACL,QAAQ,MAAM,UAAU,sBACxBI,MAAK,MAAMC,KAAIC,KAAIC,KAAI,WAAW,kBAClC,SAAS,KACT,iBACA,kBACA,wBACA,OACA;AAEJ,WAASC,YAAW,OAAO;AACzB,WAAO,uBAAuB,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI,OAAO;AAAA,EACtE;AAEA,WAAS,OAAO,OAAO;AACrB,YAAQ,uBAAuB,OAAO,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AACxD,WAAO,SAAS,CAAC,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,IAAI,OAAO;AAAA,EACzD;AAEA,EAAAA,YAAW,SAAS,SAAS,QAAQ;AACnC,WAAO,SAAS,gBAAgB,SAAS,QAAQ,QAAQ,iBAAiB,gBAAgB,MAAM,EAAE,QAAQ,gBAAgB,SAAS,cAAc,MAAM,CAAC,CAAC,CAAC,CAAC;AAAA,EAC7J;AAEA,EAAAA,YAAW,UAAU,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,UAAU,GAAG,QAAQ,QAAW,MAAM,KAAK;AAAA,EACxE;AAEA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,WAAW,GAAGJ,MAAKC,MAAKC,MAAKC,MAAK,MAAM,MAAM,KAAK;AAAA,EAChF;AAEA,EAAAC,YAAW,YAAY,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,UAAU,CAAC,IAAIC,gBAAW,QAAQ,IAAI,OAAO,KAAK,QAAQ,MAAM,uBAAmB,MAAM,KAAK,QAAQ;AAAA,EACnI;AAEA,EAAAD,YAAW,aAAa,SAAS,GAAG;AAClC,WAAO,UAAU,UAAU,WAAW,KAAK,QAAQJ,MAAKC,MAAKC,MAAKC,MAAK,MAAM,oBAAY,cAAcH,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,KAAKH,OAAM,OAAO,OAAO,CAAC,CAACA,KAAIC,GAAE,GAAG,CAACC,KAAIC,GAAE,CAAC;AAAA,EACxN;AAEA,EAAAC,YAAW,QAAQ,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,IAAI,CAAC,GAAG,SAAS,KAAK;AAAA,EACnD;AAEA,EAAAA,YAAW,YAAY,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,KAAK,CAAC,GAAG,CAAC;AAAA,EACtE;AAEA,EAAAA,YAAW,SAAS,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,SAAS,EAAE,CAAC,IAAI,MAAM,SAAS,MAAM,EAAE,CAAC,IAAI,MAAM,SAAS,SAAS,KAAK,CAAC,SAAS,SAAS,MAAM,OAAO;AAAA,EACtI;AAEA,EAAAA,YAAW,SAAS,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,cAAc,EAAE,CAAC,IAAI,MAAM,SAAS,WAAW,EAAE,CAAC,IAAI,MAAM,SAAS,aAAa,EAAE,SAAS,IAAI,EAAE,CAAC,IAAI,MAAM,UAAU,GAAG,SAAS,KAAK,CAAC,cAAc,SAAS,WAAW,SAAS,aAAa,OAAO;AAAA,EACtO;AAEA,EAAAA,YAAW,QAAQ,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,QAAQ,IAAI,MAAM,SAAS,SAAS,KAAK,QAAQ;AAAA,EAC9E;AAEA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,KAAK,IAAI,KAAK,GAAG,SAAS,KAAK,KAAK;AAAA,EACjE;AAEA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,KAAK,IAAI,KAAK,GAAG,SAAS,KAAK,KAAK;AAAA,EACjE;AAEA,EAAAA,YAAW,YAAY,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,kBAAkB,iBAAS,kBAAkB,SAAS,IAAI,CAAC,GAAG,MAAM,KAAK,KAAK,MAAM;AAAA,EACjH;AAEA,EAAAA,YAAW,YAAY,SAAS,QAAQE,SAAQ;AAC9C,WAAO,UAAUF,aAAY,QAAQE,OAAM;AAAA,EAC7C;AAEA,EAAAF,YAAW,UAAU,SAAS,MAAME,SAAQ;AAC1C,WAAO,QAAQF,aAAY,MAAME,OAAM;AAAA,EACzC;AAEA,EAAAF,YAAW,WAAW,SAAS,OAAOE,SAAQ;AAC5C,WAAO,SAASF,aAAY,OAAOE,OAAM;AAAA,EAC3C;AAEA,EAAAF,YAAW,YAAY,SAAS,QAAQE,SAAQ;AAC9C,WAAO,UAAUF,aAAY,QAAQE,OAAM;AAAA,EAC7C;AAEA,WAAS,WAAW;AAClB,QAAI,SAAS,qBAAqB,GAAG,GAAG,GAAG,IAAI,IAAI,KAAK,EAAE,MAAM,MAAM,QAAQ,QAAQ,GAAG,CAAC,GACtF,YAAY,qBAAqB,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,KAAK;AACnF,aAAS,cAAc,aAAa,UAAU,UAAU;AACxD,uBAAmB,gBAAQ,SAAS,SAAS;AAC7C,6BAAyB,gBAAQ,QAAQ,gBAAgB;AACzD,sBAAkB,iBAAS,kBAAkB,MAAM;AACnD,WAAO,MAAM;AAAA,EACf;AAEA,WAAS,QAAQ;AACf,YAAQ,cAAc;AACtB,WAAOF;AAAA,EACT;AAEA,SAAO,WAAW;AAChB,cAAU,UAAU,MAAM,MAAM,SAAS;AACzC,IAAAA,YAAW,SAAS,QAAQ,UAAU;AACtC,WAAO,SAAS;AAAA,EAClB;AACF;;;AC7KO,SAAS,gBAAgB,WAAW;AACzC,MAAIG,QAAO,GACPC,QAAO,KAAK,GACZ,IAAI,kBAAkB,SAAS,GAC/B,IAAI,EAAED,OAAMC,KAAI;AAEpB,IAAE,YAAY,SAAS,GAAG;AACxB,WAAO,UAAU,SAAS,EAAED,QAAO,EAAE,CAAC,IAAI,SAASC,QAAO,EAAE,CAAC,IAAI,OAAO,IAAI,CAACD,QAAO,SAASC,QAAO,OAAO;AAAA,EAC7G;AAEA,SAAO;AACT;;;ACZO,SAAS,wBAAwBC,OAAM;AAC5C,MAAIC,WAAU,IAAID,KAAI;AAEtB,WAAS,QAAQ,QAAQ,KAAK;AAC5B,WAAO,CAAC,SAASC,UAAS,IAAI,GAAG,IAAIA,QAAO;AAAA,EAC9C;AAEA,UAAQ,SAAS,SAAS,GAAG,GAAG;AAC9B,WAAO,CAAC,IAAIA,UAAS,KAAK,IAAIA,QAAO,CAAC;AAAA,EACxC;AAEA,SAAO;AACT;;;ACVO,SAAS,kBAAkBC,KAAIC,KAAI;AACxC,MAAI,MAAM,IAAID,GAAE,GAAG,KAAK,MAAM,IAAIC,GAAE,KAAK;AAGzC,MAAI,IAAI,CAAC,IAAI,QAAS,QAAO,wBAAwBD,GAAE;AAEvD,MAAI,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI;AAEhD,WAAS,QAAQ,GAAG,GAAG;AACrB,QAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI;AACnC,WAAO,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC;AAAA,EAC1C;AAEA,UAAQ,SAAS,SAAS,GAAG,GAAG;AAC9B,QAAI,MAAM,KAAK,GACX,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,GAAG;AACrC,QAAI,MAAM,IAAI;AACZ,WAAK,KAAK,KAAK,CAAC,IAAI,KAAK,GAAG;AAC9B,WAAO,CAAC,IAAI,GAAG,MAAM,KAAK,IAAI,IAAI,MAAM,OAAO,IAAI,MAAM,IAAI,EAAE,CAAC;AAAA,EAClE;AAEA,SAAO;AACT;AAEe,SAAR,yBAAmB;AACxB,SAAO,gBAAgB,iBAAiB,EACnC,MAAM,OAAO,EACb,OAAO,CAAC,GAAG,OAAO,CAAC;AAC1B;;;AC9Be,SAAR,iBAAmB;AACxB,SAAO,uBAAe,EACjB,UAAU,CAAC,MAAM,IAAI,CAAC,EACtB,MAAM,IAAI,EACV,UAAU,CAAC,KAAK,GAAG,CAAC,EACpB,OAAO,CAAC,IAAI,CAAC,CAAC,EACd,OAAO,CAAC,MAAM,IAAI,CAAC;AAC1B;;;ACFA,SAAS,UAAU,SAAS;AAC1B,MAAI,IAAI,QAAQ;AAChB,SAAO;AAAA,IACL,OAAO,SAAS,GAAG,GAAG;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI,EAAG,SAAQ,CAAC,EAAE,MAAM,GAAG,CAAC;AAAA,IAAG;AAAA,IAC5E,QAAQ,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI,EAAG,SAAQ,CAAC,EAAE,OAAO;AAAA,IAAG;AAAA,IACtE,WAAW,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI,EAAG,SAAQ,CAAC,EAAE,UAAU;AAAA,IAAG;AAAA,IAC5E,SAAS,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI,EAAG,SAAQ,CAAC,EAAE,QAAQ;AAAA,IAAG;AAAA,IACxE,cAAc,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI,EAAG,SAAQ,CAAC,EAAE,aAAa;AAAA,IAAG;AAAA,IAClF,YAAY,WAAW;AAAE,UAAI,IAAI;AAAI,aAAO,EAAE,IAAI,EAAG,SAAQ,CAAC,EAAE,WAAW;AAAA,IAAG;AAAA,EAChF;AACF;AAOe,SAAR,oBAAmB;AACxB,MAAI,OACA,aACA,UAAU,eAAO,GAAG,cACpB,SAAS,uBAAe,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC,GAAG,aACnF,SAAS,uBAAe,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,aAClF,OAAO,cAAc,EAAC,OAAO,SAAS,GAAG,GAAG;AAAE,YAAQ,CAAC,GAAG,CAAC;AAAA,EAAG,EAAC;AAEnE,WAAS,UAAUE,cAAa;AAC9B,QAAI,IAAIA,aAAY,CAAC,GAAG,IAAIA,aAAY,CAAC;AACzC,WAAO,QAAQ,OACV,aAAa,MAAM,GAAG,CAAC,GAAG,WACvB,YAAY,MAAM,GAAG,CAAC,GAAG,WACzB,YAAY,MAAM,GAAG,CAAC,GAAG;AAAA,EACnC;AAEA,YAAU,SAAS,SAASA,cAAa;AACvC,QAAI,IAAI,QAAQ,MAAM,GAClB,IAAI,QAAQ,UAAU,GACtB,KAAKA,aAAY,CAAC,IAAI,EAAE,CAAC,KAAK,GAC9B,KAAKA,aAAY,CAAC,IAAI,EAAE,CAAC,KAAK;AAClC,YAAQ,KAAK,QAAS,IAAI,SAAS,KAAK,UAAU,IAAI,SAAS,SACzD,KAAK,SAAS,IAAI,SAAS,KAAK,UAAU,IAAI,SAAS,SACvD,SAAS,OAAOA,YAAW;AAAA,EACnC;AAEA,YAAU,SAAS,SAAS,QAAQ;AAClC,WAAO,SAAS,gBAAgB,SAAS,QAAQ,QAAQ,UAAU,CAAC,QAAQ,OAAO,cAAc,MAAM,GAAG,OAAO,OAAO,MAAM,GAAG,OAAO,OAAO,MAAM,CAAC,CAAC;AAAA,EACzJ;AAEA,YAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU,OAAQ,QAAO,QAAQ,UAAU;AAChD,YAAQ,UAAU,CAAC,GAAG,OAAO,UAAU,CAAC,GAAG,OAAO,UAAU,CAAC;AAC7D,WAAO,MAAM;AAAA,EACf;AAEA,YAAU,QAAQ,SAAS,GAAG;AAC5B,QAAI,CAAC,UAAU,OAAQ,QAAO,QAAQ,MAAM;AAC5C,YAAQ,MAAM,CAAC,GAAG,OAAO,MAAM,IAAI,IAAI,GAAG,OAAO,MAAM,CAAC;AACxD,WAAO,UAAU,UAAU,QAAQ,UAAU,CAAC;AAAA,EAChD;AAEA,YAAU,YAAY,SAAS,GAAG;AAChC,QAAI,CAAC,UAAU,OAAQ,QAAO,QAAQ,UAAU;AAChD,QAAI,IAAI,QAAQ,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AAE5C,mBAAe,QACV,UAAU,CAAC,EACX,WAAW,CAAC,CAAC,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,CAAC,EAC3E,OAAO,WAAW;AAEvB,kBAAc,OACT,UAAU,CAAC,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,EACxC,WAAW,CAAC,CAAC,IAAI,QAAQ,IAAI,SAAS,IAAI,OAAQ,IAAI,OAAO,GAAG,CAAC,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,IAAI,OAAO,CAAC,CAAC,EACnH,OAAO,WAAW;AAEvB,kBAAc,OACT,UAAU,CAAC,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,CAAC,EACxC,WAAW,CAAC,CAAC,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,IAAI,OAAO,GAAG,CAAC,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,IAAI,OAAO,CAAC,CAAC,EACnH,OAAO,WAAW;AAEvB,WAAO,MAAM;AAAA,EACf;AAEA,YAAU,YAAY,SAAS,QAAQC,SAAQ;AAC7C,WAAO,UAAU,WAAW,QAAQA,OAAM;AAAA,EAC5C;AAEA,YAAU,UAAU,SAAS,MAAMA,SAAQ;AACzC,WAAO,QAAQ,WAAW,MAAMA,OAAM;AAAA,EACxC;AAEA,YAAU,WAAW,SAAS,OAAOA,SAAQ;AAC3C,WAAO,SAAS,WAAW,OAAOA,OAAM;AAAA,EAC1C;AAEA,YAAU,YAAY,SAAS,QAAQA,SAAQ;AAC7C,WAAO,UAAU,WAAW,QAAQA,OAAM;AAAA,EAC5C;AAEA,WAAS,QAAQ;AACf,YAAQ,cAAc;AACtB,WAAO;AAAA,EACT;AAEA,SAAO,UAAU,MAAM,IAAI;AAC7B;;;AC5GO,SAAS,aAAa,OAAO;AAClC,SAAO,SAAS,GAAG,GAAG;AACpB,QAAI,KAAK,IAAI,CAAC,GACV,KAAK,IAAI,CAAC,GACV,IAAI,MAAM,KAAK,EAAE;AACjB,QAAI,MAAM,SAAU,QAAO,CAAC,GAAG,CAAC;AACpC,WAAO;AAAA,MACL,IAAI,KAAK,IAAI,CAAC;AAAA,MACd,IAAI,IAAI,CAAC;AAAA,IACX;AAAA,EACF;AACF;AAEO,SAAS,gBAAgBC,QAAO;AACrC,SAAO,SAAS,GAAG,GAAG;AACpB,QAAI,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,GACtB,IAAIA,OAAM,CAAC,GACX,KAAK,IAAI,CAAC,GACV,KAAK,IAAI,CAAC;AACd,WAAO;AAAA,MACL,MAAM,IAAI,IAAI,IAAI,EAAE;AAAA,MACpB,KAAK,KAAK,IAAI,KAAK,CAAC;AAAA,IACtB;AAAA,EACF;AACF;;;ACtBO,IAAI,wBAAwB,aAAa,SAAS,MAAM;AAC7D,SAAO,KAAK,KAAK,IAAI,KAAK;AAC5B,CAAC;AAED,sBAAsB,SAAS,gBAAgB,SAAS,GAAG;AACzD,SAAO,IAAI,KAAK,IAAI,CAAC;AACvB,CAAC;AAEc,SAAR,6BAAmB;AACxB,SAAO,WAAW,qBAAqB,EAClC,MAAM,MAAM,EACZ,UAAU,MAAM,IAAI;AAC3B;;;ACZO,IAAI,0BAA0B,aAAa,SAAS,GAAG;AAC5D,UAAQ,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC;AACnC,CAAC;AAED,wBAAwB,SAAS,gBAAgB,SAAS,GAAG;AAC3D,SAAO;AACT,CAAC;AAEc,SAAR,+BAAmB;AACxB,SAAO,WAAW,uBAAuB,EACpC,MAAM,OAAO,EACb,UAAU,MAAM,IAAI;AAC3B;;;ACZO,SAAS,YAAY,QAAQ,KAAK;AACvC,SAAO,CAAC,QAAQ,IAAI,KAAK,SAAS,OAAO,CAAC,CAAC,CAAC;AAC9C;AAEA,YAAY,SAAS,SAAS,GAAG,GAAG;AAClC,SAAO,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,MAAM;AACtC;AAEe,SAAR,mBAAmB;AACxB,SAAO,mBAAmB,WAAW,EAChC,MAAM,MAAM,GAAG;AACtB;AAEO,SAAS,mBAAmB,SAAS;AAC1C,MAAI,IAAI,WAAW,OAAO,GACtB,SAAS,EAAE,QACX,QAAQ,EAAE,OACV,YAAY,EAAE,WACd,aAAa,EAAE,YACfC,MAAK,MAAMC,KAAIC,KAAIC;AAEvB,IAAE,QAAQ,SAAS,GAAG;AACpB,WAAO,UAAU,UAAU,MAAM,CAAC,GAAG,OAAO,KAAK,MAAM;AAAA,EACzD;AAEA,IAAE,YAAY,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,UAAU,CAAC,GAAG,OAAO,KAAK,UAAU;AAAA,EACjE;AAEA,IAAE,SAAS,SAAS,GAAG;AACrB,WAAO,UAAU,UAAU,OAAO,CAAC,GAAG,OAAO,KAAK,OAAO;AAAA,EAC3D;AAEA,IAAE,aAAa,SAAS,GAAG;AACzB,WAAO,UAAU,UAAW,KAAK,OAAOH,MAAKC,MAAKC,MAAKC,MAAK,QAAQH,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,IAAK,OAAO,KAAKH,OAAM,OAAO,OAAO,CAAC,CAACA,KAAIC,GAAE,GAAG,CAACC,KAAIC,GAAE,CAAC;AAAA,EACvL;AAEA,WAAS,SAAS;AAChB,QAAI,IAAI,KAAK,MAAM,GACf,IAAI,EAAE,iBAAS,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7C,WAAO,WAAWH,OAAM,OAClB,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,YAAY,cAC3D,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,GAAGA,GAAE,GAAGC,GAAE,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,IAAI,GAAGC,GAAE,GAAGC,GAAE,CAAC,IAC3D,CAAC,CAACH,KAAI,KAAK,IAAI,EAAE,CAAC,IAAI,GAAGC,GAAE,CAAC,GAAG,CAACC,KAAI,KAAK,IAAI,EAAE,CAAC,IAAI,GAAGC,GAAE,CAAC,CAAC,CAAC;AAAA,EACpE;AAEA,SAAO,OAAO;AAChB;;;AC/CA,SAAS,KAAK,GAAG;AACf,SAAO,KAAK,SAAS,KAAK,CAAC;AAC7B;AAEO,SAAS,kBAAkBC,KAAIC,KAAI;AACxC,MAAI,MAAM,IAAID,GAAE,GACZ,IAAIA,QAAOC,MAAK,IAAID,GAAE,IAAI,IAAI,MAAM,IAAIC,GAAE,CAAC,IAAI,IAAI,KAAKA,GAAE,IAAI,KAAKD,GAAE,CAAC,GACtE,IAAI,MAAM,IAAI,KAAKA,GAAE,GAAG,CAAC,IAAI;AAEjC,MAAI,CAAC,EAAG,QAAO;AAEf,WAAS,QAAQ,GAAG,GAAG;AACrB,QAAI,IAAI,GAAG;AAAE,UAAI,IAAI,CAAC,SAAS,QAAS,KAAI,CAAC,SAAS;AAAA,IAAS,OAC1D;AAAE,UAAI,IAAI,SAAS,QAAS,KAAI,SAAS;AAAA,IAAS;AACvD,QAAI,IAAI,IAAI,IAAI,KAAK,CAAC,GAAG,CAAC;AAC1B,WAAO,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC;AAAA,EAC5C;AAEA,UAAQ,SAAS,SAAS,GAAG,GAAG;AAC9B,QAAI,KAAK,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,EAAE,GAChD,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE;AACjC,QAAI,KAAK,IAAI;AACX,WAAK,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,WAAO,CAAC,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,IAAI,MAAM;AAAA,EACrD;AAEA,SAAO;AACT;AAEe,SAAR,yBAAmB;AACxB,SAAO,gBAAgB,iBAAiB,EACnC,MAAM,KAAK,EACX,UAAU,CAAC,IAAI,EAAE,CAAC;AACzB;;;ACnCO,SAAS,mBAAmB,QAAQ,KAAK;AAC9C,SAAO,CAAC,QAAQ,GAAG;AACrB;AAEA,mBAAmB,SAAS;AAEb,SAAR,0BAAmB;AACxB,SAAO,WAAW,kBAAkB,EAC/B,MAAM,MAAM;AACnB;;;ACPO,SAAS,oBAAoBE,KAAIC,KAAI;AAC1C,MAAI,MAAM,IAAID,GAAE,GACZ,IAAIA,QAAOC,MAAK,IAAID,GAAE,KAAK,MAAM,IAAIC,GAAE,MAAMA,MAAKD,MAClD,IAAI,MAAM,IAAIA;AAElB,MAAI,IAAI,CAAC,IAAI,QAAS,QAAO;AAE7B,WAAS,QAAQ,GAAG,GAAG;AACrB,QAAI,KAAK,IAAI,GAAG,KAAK,IAAI;AACzB,WAAO,CAAC,KAAK,IAAI,EAAE,GAAG,IAAI,KAAK,IAAI,EAAE,CAAC;AAAA,EACxC;AAEA,UAAQ,SAAS,SAAS,GAAG,GAAG;AAC9B,QAAI,KAAK,IAAI,GACT,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE;AACnC,QAAI,KAAK,IAAI;AACX,WAAK,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE;AAC7B,WAAO,CAAC,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,EAAE,CAAC;AAAA,EACpD;AAEA,SAAO;AACT;AAEe,SAAR,2BAAmB;AACxB,SAAO,gBAAgB,mBAAmB,EACrC,MAAM,OAAO,EACb,OAAO,CAAC,GAAG,OAAO,CAAC;AAC1B;;;AC5BA,IAAI,KAAK;AAAT,IACI,KAAK;AADT,IAEI,KAAK;AAFT,IAGI,KAAK;AAHT,IAII,IAAI,KAAK,CAAC,IAAI;AAJlB,IAKI,aAAa;AAEV,SAAS,cAAc,QAAQ,KAAK;AACzC,MAAI,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK;AACvD,SAAO;AAAA,IACL,SAAS,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AAAA,IACnE,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK;AAAA,EACtC;AACF;AAEA,cAAc,SAAS,SAAS,GAAG,GAAG;AACpC,MAAI,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK;AACtC,WAAS,IAAI,GAAG,OAAO,IAAI,KAAK,IAAI,YAAY,EAAE,GAAG;AACnD,SAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,OAAO;AAChD,UAAM,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AACjD,SAAK,QAAQ,KAAK,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,KAAK;AAClD,QAAI,IAAI,KAAK,IAAI,SAAU;AAAA,EAC7B;AACA,SAAO;AAAA,IACL,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,CAAC;AAAA,IAChE,KAAK,IAAI,CAAC,IAAI,CAAC;AAAA,EACjB;AACF;AAEe,SAAR,qBAAmB;AACxB,SAAO,WAAW,aAAa,EAC1B,MAAM,OAAO;AACpB;;;AC/BO,SAAS,YAAY,GAAG,GAAG;AAChC,MAAI,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI;AAC9B,SAAO,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrC;AAEA,YAAY,SAAS,gBAAgB,IAAI;AAE1B,SAAR,mBAAmB;AACxB,SAAO,WAAW,WAAW,EACxB,MAAM,OAAO,EACb,UAAU,EAAE;AACnB;;;ACTe,SAARE,oBAAmB;AACxB,MAAI,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GACpC,QAAQ,GAAG,IAAI,IACfC,MAAK,MAAMC,KAAIC,KAAIC,KACnB,KAAK,GAAG,KAAK,GACb,YAAY,YAAY;AAAA,IACtB,OAAO,SAAS,GAAG,GAAG;AACpB,UAAI,IAAIC,YAAW,CAAC,GAAG,CAAC,CAAC;AACzB,WAAK,OAAO,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IAC9B;AAAA,EACF,CAAC,GACD,WAAW,kBACX,OACA;AAEJ,WAAS,QAAQ;AACf,SAAK,IAAI;AACT,SAAK,IAAI;AACT,YAAQ,cAAc;AACtB,WAAOA;AAAA,EACT;AAEA,WAASA,YAAY,GAAG;AACtB,QAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI;AAC9B,QAAI,OAAO;AACT,UAAI,IAAI,IAAI,KAAK,IAAI;AACrB,UAAI,IAAI,KAAK,IAAI;AACjB,UAAI;AAAA,IACN;AACA,WAAO,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,EACxB;AACA,EAAAA,YAAW,SAAS,SAAS,GAAG;AAC9B,QAAI,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI;AAC9B,QAAI,OAAO;AACT,UAAI,IAAI,IAAI,KAAK,IAAI;AACrB,UAAI,IAAI,KAAK,IAAI;AACjB,UAAI;AAAA,IACN;AACA,WAAO,CAAC,IAAI,IAAI,IAAI,EAAE;AAAA,EACxB;AACA,EAAAA,YAAW,SAAS,SAAS,QAAQ;AACnC,WAAO,SAAS,gBAAgB,SAAS,QAAQ,QAAQ,UAAU,SAAS,cAAc,MAAM,CAAC;AAAA,EACnG;AACA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,WAAW,GAAGJ,MAAKC,MAAKC,MAAKC,MAAK,MAAM,MAAM,KAAK;AAAA,EAChF;AACA,EAAAC,YAAW,aAAa,SAAS,GAAG;AAClC,WAAO,UAAU,UAAU,WAAW,KAAK,QAAQJ,MAAKC,MAAKC,MAAKC,MAAK,MAAM,oBAAY,cAAcH,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGC,MAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,KAAKH,OAAM,OAAO,OAAO,CAAC,CAACA,KAAIC,GAAE,GAAG,CAACC,KAAIC,GAAE,CAAC;AAAA,EACxN;AACA,EAAAC,YAAW,QAAQ,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,IAAI,CAAC,GAAG,MAAM,KAAK;AAAA,EAChD;AACA,EAAAA,YAAW,YAAY,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE;AAAA,EACvE;AACA,EAAAA,YAAW,QAAQ,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,QAAQ,IAAI,MAAM,SAAS,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,MAAM,KAAK,QAAQ;AAAA,EAC7G;AACA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,KAAK,IAAI,KAAK,GAAG,MAAM,KAAK,KAAK;AAAA,EAC9D;AACA,EAAAA,YAAW,WAAW,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,KAAK,IAAI,KAAK,GAAG,MAAM,KAAK,KAAK;AAAA,EAC9D;AACA,EAAAA,YAAW,YAAY,SAAS,QAAQC,SAAQ;AAC9C,WAAO,UAAUD,aAAY,QAAQC,OAAM;AAAA,EAC7C;AACA,EAAAD,YAAW,UAAU,SAAS,MAAMC,SAAQ;AAC1C,WAAO,QAAQD,aAAY,MAAMC,OAAM;AAAA,EACzC;AACA,EAAAD,YAAW,WAAW,SAAS,OAAOC,SAAQ;AAC5C,WAAO,SAASD,aAAY,OAAOC,OAAM;AAAA,EAC3C;AACA,EAAAD,YAAW,YAAY,SAAS,QAAQC,SAAQ;AAC9C,WAAO,UAAUD,aAAY,QAAQC,OAAM;AAAA,EAC7C;AAEA,SAAOD;AACT;;;ACjFO,SAAS,iBAAiB,QAAQ,KAAK;AAC5C,MAAI,OAAO,MAAM,KAAK,OAAO,OAAO;AACpC,SAAO;AAAA,IACL,UAAU,SAAS,WAAW,OAAO,QAAQ,YAAY,QAAQ,UAAW,OAAO,UAAW;AAAA,IAC9F,OAAO,WAAW,QAAQ,WAAW,QAAQ,YAAY,WAAW,OAAO,UAAW;AAAA,EACxF;AACF;AAEA,iBAAiB,SAAS,SAAS,GAAG,GAAG;AACvC,MAAI,MAAM,GAAG,IAAI,IAAI;AACrB,KAAG;AACD,QAAI,OAAO,MAAM,KAAK,OAAO,OAAO;AACpC,WAAO,SAAS,OAAO,WAAW,QAAQ,WAAW,QAAQ,YAAY,WAAW,OAAO,UAAW,UAAU,MAC3G,WAAW,QAAQ,WAAW,IAAI,QAAQ,YAAY,IAAI,WAAW,IAAI,OAAO,UAAW,KAAK;AAAA,EACvG,SAAS,IAAI,KAAK,IAAI,WAAW,EAAE,IAAI;AACvC,SAAO;AAAA,IACL,KAAK,UAAU,OAAO,MAAM,QAAQ,YAAY,QAAQ,YAAY,OAAO,OAAO,QAAQ,UAAW,UAAW;AAAA,IAChH;AAAA,EACF;AACF;AAEe,SAAR,wBAAmB;AACxB,SAAO,WAAW,gBAAgB,EAC7B,MAAM,OAAO;AACpB;;;ACvBO,SAAS,gBAAgB,GAAG,GAAG;AACpC,SAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AACjC;AAEA,gBAAgB,SAAS,gBAAgB,IAAI;AAE9B,SAAR,uBAAmB;AACxB,SAAO,WAAW,eAAe,EAC5B,MAAM,KAAK,EACX,UAAU,KAAK,OAAO;AAC7B;;;ACVO,SAAS,iBAAiB,GAAG,GAAG;AACrC,MAAI,KAAK,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI;AAClC,SAAO,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrC;AAEA,iBAAiB,SAAS,gBAAgB,SAAS,GAAG;AACpD,SAAO,IAAI,KAAK,CAAC;AACnB,CAAC;AAEc,SAAR,wBAAmB;AACxB,SAAO,WAAW,gBAAgB,EAC7B,MAAM,GAAG,EACT,UAAU,GAAG;AACpB;;;ACdO,SAAS,sBAAsB,QAAQ,KAAK;AACjD,SAAO,CAAC,IAAI,KAAK,SAAS,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM;AAC/C;AAEA,sBAAsB,SAAS,SAAS,GAAG,GAAG;AAC5C,SAAO,CAAC,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,MAAM;AACvC;AAEe,SAAR,6BAAmB;AACxB,MAAI,IAAI,mBAAmB,qBAAqB,GAC5C,SAAS,EAAE,QACX,SAAS,EAAE;AAEf,IAAE,SAAS,SAAS,GAAG;AACrB,WAAO,UAAU,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAAA,EAC/E;AAEA,IAAE,SAAS,SAAS,GAAG;AACrB,WAAO,UAAU,SAAS,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,SAAS,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE;AAAA,EACvH;AAEA,SAAO,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC,EACnB,MAAM,OAAO;AACpB;", "names": ["object", "coordinates", "object", "cartesian", "spherical", "lambda0", "lambda00", "phi00", "range", "sign", "lambda1", "lambda00", "phi00", "object", "coordinates", "compareIntersection", "n", "p0", "angle", "lambda0", "phi0", "sinPhi0", "cosPhi0", "lambda1", "phi1", "sign", "point", "lambda0", "phi0", "lambda1", "phi1", "cosPhi0", "circle_default", "lambda0", "lambda1", "phi0", "phi1", "delta", "code", "x0", "y0", "x1", "y1", "x0", "y0", "x1", "y1", "compareIntersection", "ring", "point", "visible", "linePoint", "x0", "y0", "x1", "y1", "lambda0", "sinPhi0", "cosPhi0", "object", "object", "coordinates", "y0", "y1", "y", "x0", "x1", "x", "X1", "X0", "Y1", "Y0", "graticule", "coordinates", "x0", "y0", "x1", "y1", "areaSum", "areaRingSum", "x0", "y0", "areaStream", "areaRingStart", "areaRingEnd", "areaPointFirst", "areaPoint", "area_default", "x0", "y0", "boundsStream", "boundsPoint", "bounds_default", "X0", "Y0", "Z0", "X1", "Y1", "Z1", "X2", "Y2", "Z2", "x00", "y00", "x0", "y0", "centroidStream", "centroidPoint", "centroidLineStart", "centroidLineEnd", "centroidRingStart", "centroidRingEnd", "centroid_default", "lengthSum", "x00", "y00", "x0", "y0", "lengthStream", "lengthPointFirst", "lengthPoint", "append", "projection", "object", "area_default", "bounds_default", "centroid_default", "projection", "object", "bounds_default", "x0", "y0", "lambda0", "x1", "y1", "lambda1", "lambda2", "lambda00", "x00", "y00", "linePoint", "x0", "y0", "x1", "y1", "projection", "circle_default", "object", "phi0", "phi1", "phi0", "cosPhi0", "y0", "y1", "coordinates", "object", "angle", "x0", "y0", "x1", "y1", "y0", "y1", "y0", "y1", "identity_default", "x0", "y0", "x1", "y1", "projection", "object"]}