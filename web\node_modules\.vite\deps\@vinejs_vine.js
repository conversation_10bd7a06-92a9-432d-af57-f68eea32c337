import {
  require_escape,
  require_isAlpha,
  require_isAlphanumeric,
  require_isAscii,
  require_isCreditCard,
  require_isDecimal,
  require_isEmail,
  require_isHexColor,
  require_isIBAN,
  require_isIP,
  require_isJWT,
  require_isLatLong,
  require_isMobilePhone,
  require_isPassportNumber,
  require_isPostalCode,
  require_isSlug,
  require_isURL,
  require_isUUID,
  require_normalizeEmail
} from "./chunk-NMCYMVZX.js";
import {
  __commonJS,
  __privateAdd,
  __privateGet,
  __privateMethod,
  __privateSet,
  __publicField,
  __toESM
} from "./chunk-KWPVD4H7.js";

// node_modules/dlv/dist/dlv.js
var require_dlv = __commonJS({
  "node_modules/dlv/dist/dlv.js"(exports, module) {
    module.exports = function(t, e, l, n, o) {
      for (e = e.split ? e.split(".") : e, n = 0; n < e.length; n++) t = t ? t[e[n]] : o;
      return t === o ? l : t;
    };
  }
});

// node_modules/dayjs/dayjs.min.js
var require_dayjs_min = __commonJS({
  "node_modules/dayjs/dayjs.min.js"(exports, module) {
    !function(t, e) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define(e) : (t = "undefined" != typeof globalThis ? globalThis : t || self).dayjs = e();
    }(exports, function() {
      "use strict";
      var t = 1e3, e = 6e4, n = 36e5, r = "millisecond", i = "second", s = "minute", u = "hour", a = "day", o = "week", c = "month", f = "quarter", h = "year", d = "date", l = "Invalid Date", $ = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/, y = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g, M = { name: "en", weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"), months: "January_February_March_April_May_June_July_August_September_October_November_December".split("_"), ordinal: function(t2) {
        var e2 = ["th", "st", "nd", "rd"], n2 = t2 % 100;
        return "[" + t2 + (e2[(n2 - 20) % 10] || e2[n2] || e2[0]) + "]";
      } }, m = function(t2, e2, n2) {
        var r2 = String(t2);
        return !r2 || r2.length >= e2 ? t2 : "" + Array(e2 + 1 - r2.length).join(n2) + t2;
      }, v = { s: m, z: function(t2) {
        var e2 = -t2.utcOffset(), n2 = Math.abs(e2), r2 = Math.floor(n2 / 60), i2 = n2 % 60;
        return (e2 <= 0 ? "+" : "-") + m(r2, 2, "0") + ":" + m(i2, 2, "0");
      }, m: function t2(e2, n2) {
        if (e2.date() < n2.date()) return -t2(n2, e2);
        var r2 = 12 * (n2.year() - e2.year()) + (n2.month() - e2.month()), i2 = e2.clone().add(r2, c), s2 = n2 - i2 < 0, u2 = e2.clone().add(r2 + (s2 ? -1 : 1), c);
        return +(-(r2 + (n2 - i2) / (s2 ? i2 - u2 : u2 - i2)) || 0);
      }, a: function(t2) {
        return t2 < 0 ? Math.ceil(t2) || 0 : Math.floor(t2);
      }, p: function(t2) {
        return { M: c, y: h, w: o, d: a, D: d, h: u, m: s, s: i, ms: r, Q: f }[t2] || String(t2 || "").toLowerCase().replace(/s$/, "");
      }, u: function(t2) {
        return void 0 === t2;
      } }, g = "en", D = {};
      D[g] = M;
      var p = "$isDayjsObject", S = function(t2) {
        return t2 instanceof _ || !(!t2 || !t2[p]);
      }, w = function t2(e2, n2, r2) {
        var i2;
        if (!e2) return g;
        if ("string" == typeof e2) {
          var s2 = e2.toLowerCase();
          D[s2] && (i2 = s2), n2 && (D[s2] = n2, i2 = s2);
          var u2 = e2.split("-");
          if (!i2 && u2.length > 1) return t2(u2[0]);
        } else {
          var a2 = e2.name;
          D[a2] = e2, i2 = a2;
        }
        return !r2 && i2 && (g = i2), i2 || !r2 && g;
      }, O = function(t2, e2) {
        if (S(t2)) return t2.clone();
        var n2 = "object" == typeof e2 ? e2 : {};
        return n2.date = t2, n2.args = arguments, new _(n2);
      }, b = v;
      b.l = w, b.i = S, b.w = function(t2, e2) {
        return O(t2, { locale: e2.$L, utc: e2.$u, x: e2.$x, $offset: e2.$offset });
      };
      var _ = function() {
        function M2(t2) {
          this.$L = w(t2.locale, null, true), this.parse(t2), this.$x = this.$x || t2.x || {}, this[p] = true;
        }
        var m2 = M2.prototype;
        return m2.parse = function(t2) {
          this.$d = function(t3) {
            var e2 = t3.date, n2 = t3.utc;
            if (null === e2) return /* @__PURE__ */ new Date(NaN);
            if (b.u(e2)) return /* @__PURE__ */ new Date();
            if (e2 instanceof Date) return new Date(e2);
            if ("string" == typeof e2 && !/Z$/i.test(e2)) {
              var r2 = e2.match($);
              if (r2) {
                var i2 = r2[2] - 1 || 0, s2 = (r2[7] || "0").substring(0, 3);
                return n2 ? new Date(Date.UTC(r2[1], i2, r2[3] || 1, r2[4] || 0, r2[5] || 0, r2[6] || 0, s2)) : new Date(r2[1], i2, r2[3] || 1, r2[4] || 0, r2[5] || 0, r2[6] || 0, s2);
              }
            }
            return new Date(e2);
          }(t2), this.init();
        }, m2.init = function() {
          var t2 = this.$d;
          this.$y = t2.getFullYear(), this.$M = t2.getMonth(), this.$D = t2.getDate(), this.$W = t2.getDay(), this.$H = t2.getHours(), this.$m = t2.getMinutes(), this.$s = t2.getSeconds(), this.$ms = t2.getMilliseconds();
        }, m2.$utils = function() {
          return b;
        }, m2.isValid = function() {
          return !(this.$d.toString() === l);
        }, m2.isSame = function(t2, e2) {
          var n2 = O(t2);
          return this.startOf(e2) <= n2 && n2 <= this.endOf(e2);
        }, m2.isAfter = function(t2, e2) {
          return O(t2) < this.startOf(e2);
        }, m2.isBefore = function(t2, e2) {
          return this.endOf(e2) < O(t2);
        }, m2.$g = function(t2, e2, n2) {
          return b.u(t2) ? this[e2] : this.set(n2, t2);
        }, m2.unix = function() {
          return Math.floor(this.valueOf() / 1e3);
        }, m2.valueOf = function() {
          return this.$d.getTime();
        }, m2.startOf = function(t2, e2) {
          var n2 = this, r2 = !!b.u(e2) || e2, f2 = b.p(t2), l2 = function(t3, e3) {
            var i2 = b.w(n2.$u ? Date.UTC(n2.$y, e3, t3) : new Date(n2.$y, e3, t3), n2);
            return r2 ? i2 : i2.endOf(a);
          }, $2 = function(t3, e3) {
            return b.w(n2.toDate()[t3].apply(n2.toDate("s"), (r2 ? [0, 0, 0, 0] : [23, 59, 59, 999]).slice(e3)), n2);
          }, y2 = this.$W, M3 = this.$M, m3 = this.$D, v2 = "set" + (this.$u ? "UTC" : "");
          switch (f2) {
            case h:
              return r2 ? l2(1, 0) : l2(31, 11);
            case c:
              return r2 ? l2(1, M3) : l2(0, M3 + 1);
            case o:
              var g2 = this.$locale().weekStart || 0, D2 = (y2 < g2 ? y2 + 7 : y2) - g2;
              return l2(r2 ? m3 - D2 : m3 + (6 - D2), M3);
            case a:
            case d:
              return $2(v2 + "Hours", 0);
            case u:
              return $2(v2 + "Minutes", 1);
            case s:
              return $2(v2 + "Seconds", 2);
            case i:
              return $2(v2 + "Milliseconds", 3);
            default:
              return this.clone();
          }
        }, m2.endOf = function(t2) {
          return this.startOf(t2, false);
        }, m2.$set = function(t2, e2) {
          var n2, o2 = b.p(t2), f2 = "set" + (this.$u ? "UTC" : ""), l2 = (n2 = {}, n2[a] = f2 + "Date", n2[d] = f2 + "Date", n2[c] = f2 + "Month", n2[h] = f2 + "FullYear", n2[u] = f2 + "Hours", n2[s] = f2 + "Minutes", n2[i] = f2 + "Seconds", n2[r] = f2 + "Milliseconds", n2)[o2], $2 = o2 === a ? this.$D + (e2 - this.$W) : e2;
          if (o2 === c || o2 === h) {
            var y2 = this.clone().set(d, 1);
            y2.$d[l2]($2), y2.init(), this.$d = y2.set(d, Math.min(this.$D, y2.daysInMonth())).$d;
          } else l2 && this.$d[l2]($2);
          return this.init(), this;
        }, m2.set = function(t2, e2) {
          return this.clone().$set(t2, e2);
        }, m2.get = function(t2) {
          return this[b.p(t2)]();
        }, m2.add = function(r2, f2) {
          var d2, l2 = this;
          r2 = Number(r2);
          var $2 = b.p(f2), y2 = function(t2) {
            var e2 = O(l2);
            return b.w(e2.date(e2.date() + Math.round(t2 * r2)), l2);
          };
          if ($2 === c) return this.set(c, this.$M + r2);
          if ($2 === h) return this.set(h, this.$y + r2);
          if ($2 === a) return y2(1);
          if ($2 === o) return y2(7);
          var M3 = (d2 = {}, d2[s] = e, d2[u] = n, d2[i] = t, d2)[$2] || 1, m3 = this.$d.getTime() + r2 * M3;
          return b.w(m3, this);
        }, m2.subtract = function(t2, e2) {
          return this.add(-1 * t2, e2);
        }, m2.format = function(t2) {
          var e2 = this, n2 = this.$locale();
          if (!this.isValid()) return n2.invalidDate || l;
          var r2 = t2 || "YYYY-MM-DDTHH:mm:ssZ", i2 = b.z(this), s2 = this.$H, u2 = this.$m, a2 = this.$M, o2 = n2.weekdays, c2 = n2.months, f2 = n2.meridiem, h2 = function(t3, n3, i3, s3) {
            return t3 && (t3[n3] || t3(e2, r2)) || i3[n3].slice(0, s3);
          }, d2 = function(t3) {
            return b.s(s2 % 12 || 12, t3, "0");
          }, $2 = f2 || function(t3, e3, n3) {
            var r3 = t3 < 12 ? "AM" : "PM";
            return n3 ? r3.toLowerCase() : r3;
          };
          return r2.replace(y, function(t3, r3) {
            return r3 || function(t4) {
              switch (t4) {
                case "YY":
                  return String(e2.$y).slice(-2);
                case "YYYY":
                  return b.s(e2.$y, 4, "0");
                case "M":
                  return a2 + 1;
                case "MM":
                  return b.s(a2 + 1, 2, "0");
                case "MMM":
                  return h2(n2.monthsShort, a2, c2, 3);
                case "MMMM":
                  return h2(c2, a2);
                case "D":
                  return e2.$D;
                case "DD":
                  return b.s(e2.$D, 2, "0");
                case "d":
                  return String(e2.$W);
                case "dd":
                  return h2(n2.weekdaysMin, e2.$W, o2, 2);
                case "ddd":
                  return h2(n2.weekdaysShort, e2.$W, o2, 3);
                case "dddd":
                  return o2[e2.$W];
                case "H":
                  return String(s2);
                case "HH":
                  return b.s(s2, 2, "0");
                case "h":
                  return d2(1);
                case "hh":
                  return d2(2);
                case "a":
                  return $2(s2, u2, true);
                case "A":
                  return $2(s2, u2, false);
                case "m":
                  return String(u2);
                case "mm":
                  return b.s(u2, 2, "0");
                case "s":
                  return String(e2.$s);
                case "ss":
                  return b.s(e2.$s, 2, "0");
                case "SSS":
                  return b.s(e2.$ms, 3, "0");
                case "Z":
                  return i2;
              }
              return null;
            }(t3) || i2.replace(":", "");
          });
        }, m2.utcOffset = function() {
          return 15 * -Math.round(this.$d.getTimezoneOffset() / 15);
        }, m2.diff = function(r2, d2, l2) {
          var $2, y2 = this, M3 = b.p(d2), m3 = O(r2), v2 = (m3.utcOffset() - this.utcOffset()) * e, g2 = this - m3, D2 = function() {
            return b.m(y2, m3);
          };
          switch (M3) {
            case h:
              $2 = D2() / 12;
              break;
            case c:
              $2 = D2();
              break;
            case f:
              $2 = D2() / 3;
              break;
            case o:
              $2 = (g2 - v2) / 6048e5;
              break;
            case a:
              $2 = (g2 - v2) / 864e5;
              break;
            case u:
              $2 = g2 / n;
              break;
            case s:
              $2 = g2 / e;
              break;
            case i:
              $2 = g2 / t;
              break;
            default:
              $2 = g2;
          }
          return l2 ? $2 : b.a($2);
        }, m2.daysInMonth = function() {
          return this.endOf(c).$D;
        }, m2.$locale = function() {
          return D[this.$L];
        }, m2.locale = function(t2, e2) {
          if (!t2) return this.$L;
          var n2 = this.clone(), r2 = w(t2, e2, true);
          return r2 && (n2.$L = r2), n2;
        }, m2.clone = function() {
          return b.w(this.$d, this);
        }, m2.toDate = function() {
          return new Date(this.valueOf());
        }, m2.toJSON = function() {
          return this.isValid() ? this.toISOString() : null;
        }, m2.toISOString = function() {
          return this.$d.toISOString();
        }, m2.toString = function() {
          return this.$d.toUTCString();
        }, M2;
      }(), k = _.prototype;
      return O.prototype = k, [["$ms", r], ["$s", i], ["$m", s], ["$H", u], ["$W", a], ["$M", c], ["$y", h], ["$D", d]].forEach(function(t2) {
        k[t2[1]] = function(e2) {
          return this.$g(e2, t2[0], t2[1]);
        };
      }), O.extend = function(t2, e2) {
        return t2.$i || (t2(e2, _, O), t2.$i = true), O;
      }, O.locale = w, O.isDayjs = S, O.unix = function(t2) {
        return O(1e3 * t2);
      }, O.en = D[g], O.Ls = D, O.p = {}, O;
    });
  }
});

// node_modules/dayjs/plugin/isSameOrAfter.js
var require_isSameOrAfter = __commonJS({
  "node_modules/dayjs/plugin/isSameOrAfter.js"(exports, module) {
    !function(e, t) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : (e = "undefined" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isSameOrAfter = t();
    }(exports, function() {
      "use strict";
      return function(e, t) {
        t.prototype.isSameOrAfter = function(e2, t2) {
          return this.isSame(e2, t2) || this.isAfter(e2, t2);
        };
      };
    });
  }
});

// node_modules/dayjs/plugin/isSameOrBefore.js
var require_isSameOrBefore = __commonJS({
  "node_modules/dayjs/plugin/isSameOrBefore.js"(exports, module) {
    !function(e, i) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = i() : "function" == typeof define && define.amd ? define(i) : (e = "undefined" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isSameOrBefore = i();
    }(exports, function() {
      "use strict";
      return function(e, i) {
        i.prototype.isSameOrBefore = function(e2, i2) {
          return this.isSame(e2, i2) || this.isBefore(e2, i2);
        };
      };
    });
  }
});

// node_modules/dayjs/plugin/customParseFormat.js
var require_customParseFormat = __commonJS({
  "node_modules/dayjs/plugin/customParseFormat.js"(exports, module) {
    !function(e, t) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : (e = "undefined" != typeof globalThis ? globalThis : e || self).dayjs_plugin_customParseFormat = t();
    }(exports, function() {
      "use strict";
      var e = { LTS: "h:mm:ss A", LT: "h:mm A", L: "MM/DD/YYYY", LL: "MMMM D, YYYY", LLL: "MMMM D, YYYY h:mm A", LLLL: "dddd, MMMM D, YYYY h:mm A" }, t = /(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g, n = /\d/, r = /\d\d/, i = /\d\d?/, o = /\d*[^-_:/,()\s\d]+/, s = {}, a = function(e2) {
        return (e2 = +e2) + (e2 > 68 ? 1900 : 2e3);
      };
      var f = function(e2) {
        return function(t2) {
          this[e2] = +t2;
        };
      }, h = [/[+-]\d\d:?(\d\d)?|Z/, function(e2) {
        (this.zone || (this.zone = {})).offset = function(e3) {
          if (!e3) return 0;
          if ("Z" === e3) return 0;
          var t2 = e3.match(/([+-]|\d\d)/g), n2 = 60 * t2[1] + (+t2[2] || 0);
          return 0 === n2 ? 0 : "+" === t2[0] ? -n2 : n2;
        }(e2);
      }], u = function(e2) {
        var t2 = s[e2];
        return t2 && (t2.indexOf ? t2 : t2.s.concat(t2.f));
      }, d = function(e2, t2) {
        var n2, r2 = s.meridiem;
        if (r2) {
          for (var i2 = 1; i2 <= 24; i2 += 1) if (e2.indexOf(r2(i2, 0, t2)) > -1) {
            n2 = i2 > 12;
            break;
          }
        } else n2 = e2 === (t2 ? "pm" : "PM");
        return n2;
      }, c = { A: [o, function(e2) {
        this.afternoon = d(e2, false);
      }], a: [o, function(e2) {
        this.afternoon = d(e2, true);
      }], Q: [n, function(e2) {
        this.month = 3 * (e2 - 1) + 1;
      }], S: [n, function(e2) {
        this.milliseconds = 100 * +e2;
      }], SS: [r, function(e2) {
        this.milliseconds = 10 * +e2;
      }], SSS: [/\d{3}/, function(e2) {
        this.milliseconds = +e2;
      }], s: [i, f("seconds")], ss: [i, f("seconds")], m: [i, f("minutes")], mm: [i, f("minutes")], H: [i, f("hours")], h: [i, f("hours")], HH: [i, f("hours")], hh: [i, f("hours")], D: [i, f("day")], DD: [r, f("day")], Do: [o, function(e2) {
        var t2 = s.ordinal, n2 = e2.match(/\d+/);
        if (this.day = n2[0], t2) for (var r2 = 1; r2 <= 31; r2 += 1) t2(r2).replace(/\[|\]/g, "") === e2 && (this.day = r2);
      }], w: [i, f("week")], ww: [r, f("week")], M: [i, f("month")], MM: [r, f("month")], MMM: [o, function(e2) {
        var t2 = u("months"), n2 = (u("monthsShort") || t2.map(function(e3) {
          return e3.slice(0, 3);
        })).indexOf(e2) + 1;
        if (n2 < 1) throw new Error();
        this.month = n2 % 12 || n2;
      }], MMMM: [o, function(e2) {
        var t2 = u("months").indexOf(e2) + 1;
        if (t2 < 1) throw new Error();
        this.month = t2 % 12 || t2;
      }], Y: [/[+-]?\d+/, f("year")], YY: [r, function(e2) {
        this.year = a(e2);
      }], YYYY: [/\d{4}/, f("year")], Z: h, ZZ: h };
      function l(n2) {
        var r2, i2;
        r2 = n2, i2 = s && s.formats;
        for (var o2 = (n2 = r2.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g, function(t2, n3, r3) {
          var o3 = r3 && r3.toUpperCase();
          return n3 || i2[r3] || e[r3] || i2[o3].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g, function(e2, t3, n4) {
            return t3 || n4.slice(1);
          });
        })).match(t), a2 = o2.length, f2 = 0; f2 < a2; f2 += 1) {
          var h2 = o2[f2], u2 = c[h2], d2 = u2 && u2[0], l2 = u2 && u2[1];
          o2[f2] = l2 ? { regex: d2, parser: l2 } : h2.replace(/^\[|\]$/g, "");
        }
        return function(e2) {
          for (var t2 = {}, n3 = 0, r3 = 0; n3 < a2; n3 += 1) {
            var i3 = o2[n3];
            if ("string" == typeof i3) r3 += i3.length;
            else {
              var s2 = i3.regex, f3 = i3.parser, h3 = e2.slice(r3), u3 = s2.exec(h3)[0];
              f3.call(t2, u3), e2 = e2.replace(u3, "");
            }
          }
          return function(e3) {
            var t3 = e3.afternoon;
            if (void 0 !== t3) {
              var n4 = e3.hours;
              t3 ? n4 < 12 && (e3.hours += 12) : 12 === n4 && (e3.hours = 0), delete e3.afternoon;
            }
          }(t2), t2;
        };
      }
      return function(e2, t2, n2) {
        n2.p.customParseFormat = true, e2 && e2.parseTwoDigitYear && (a = e2.parseTwoDigitYear);
        var r2 = t2.prototype, i2 = r2.parse;
        r2.parse = function(e3) {
          var t3 = e3.date, r3 = e3.utc, o2 = e3.args;
          this.$u = r3;
          var a2 = o2[1];
          if ("string" == typeof a2) {
            var f2 = true === o2[2], h2 = true === o2[3], u2 = f2 || h2, d2 = o2[2];
            h2 && (d2 = o2[2]), s = this.$locale(), !f2 && d2 && (s = n2.Ls[d2]), this.$d = function(e4, t4, n3, r4) {
              try {
                if (["x", "X"].indexOf(t4) > -1) return new Date(("X" === t4 ? 1e3 : 1) * e4);
                var i3 = l(t4)(e4), o3 = i3.year, s2 = i3.month, a3 = i3.day, f3 = i3.hours, h3 = i3.minutes, u3 = i3.seconds, d3 = i3.milliseconds, c3 = i3.zone, m2 = i3.week, M2 = /* @__PURE__ */ new Date(), Y = a3 || (o3 || s2 ? 1 : M2.getDate()), p = o3 || M2.getFullYear(), v = 0;
                o3 && !s2 || (v = s2 > 0 ? s2 - 1 : M2.getMonth());
                var D, w = f3 || 0, g = h3 || 0, y = u3 || 0, L = d3 || 0;
                return c3 ? new Date(Date.UTC(p, v, Y, w, g, y, L + 60 * c3.offset * 1e3)) : n3 ? new Date(Date.UTC(p, v, Y, w, g, y, L)) : (D = new Date(p, v, Y, w, g, y, L), m2 && (D = r4(D).week(m2).toDate()), D);
              } catch (e5) {
                return /* @__PURE__ */ new Date("");
              }
            }(t3, a2, r3, n2), this.init(), d2 && true !== d2 && (this.$L = this.locale(d2).$L), u2 && t3 != this.format(a2) && (this.$d = /* @__PURE__ */ new Date("")), s = {};
          } else if (a2 instanceof Array) for (var c2 = a2.length, m = 1; m <= c2; m += 1) {
            o2[1] = a2[m - 1];
            var M = n2.apply(this, o2);
            if (M.isValid()) {
              this.$d = M.$d, this.$L = M.$L, this.init();
              break;
            }
            m === c2 && (this.$d = /* @__PURE__ */ new Date(""));
          }
          else i2.call(this, e3);
        };
      };
    });
  }
});

// node_modules/@vinejs/vine/build/chunk-MLKGABMK.js
var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};

// node_modules/@vinejs/vine/build/chunk-YXNUTVGP.js
var import_dlv = __toESM(require_dlv(), 1);
var import_isIP = __toESM(require_isIP(), 1);
var import_isJWT = __toESM(require_isJWT(), 1);
var import_isURL = __toESM(require_isURL(), 1);
var import_isSlug = __toESM(require_isSlug(), 1);
var import_isIBAN = __toESM(require_isIBAN(), 1);
var import_isUUID = __toESM(require_isUUID(), 1);
var import_isAscii = __toESM(require_isAscii(), 1);
var import_isEmail = __toESM(require_isEmail(), 1);
var import_isAlpha = __toESM(require_isAlpha(), 1);
var import_isLatLong = __toESM(require_isLatLong(), 1);
var import_isDecimal = __toESM(require_isDecimal(), 1);
var import_isHexColor = __toESM(require_isHexColor(), 1);
var import_isCreditCard = __toESM(require_isCreditCard(), 1);
var import_isAlphanumeric = __toESM(require_isAlphanumeric(), 1);
var import_isPassportNumber = __toESM(require_isPassportNumber(), 1);
var import_isPostalCode = __toESM(require_isPostalCode(), 1);
var import_isMobilePhone = __toESM(require_isMobilePhone(), 1);
var import_isMobilePhone2 = __toESM(require_isMobilePhone(), 1);
var import_isPostalCode2 = __toESM(require_isPostalCode(), 1);
var _messages, _fields, _SimpleMessagesProvider_instances, interpolate_fn, _a;
var SimpleMessagesProvider = (_a = class {
  constructor(messages2, fields2) {
    __privateAdd(this, _SimpleMessagesProvider_instances);
    __privateAdd(this, _messages);
    __privateAdd(this, _fields);
    __privateSet(this, _messages, messages2);
    __privateSet(this, _fields, fields2 || {});
  }
  /**
   * Returns a validation message for a given field + rule.
   */
  getMessage(rawMessage, rule, field, args) {
    const fieldName = __privateGet(this, _fields)[field.name] || field.name;
    const fieldMessage = __privateGet(this, _messages)[`${field.getFieldPath()}.${rule}`];
    if (fieldMessage) {
      return __privateMethod(this, _SimpleMessagesProvider_instances, interpolate_fn).call(this, fieldMessage, {
        field: fieldName,
        ...args
      });
    }
    const wildcardMessage = __privateGet(this, _messages)[`${field.wildCardPath}.${rule}`];
    if (wildcardMessage) {
      return __privateMethod(this, _SimpleMessagesProvider_instances, interpolate_fn).call(this, wildcardMessage, {
        field: fieldName,
        ...args
      });
    }
    const ruleMessage = __privateGet(this, _messages)[rule];
    if (ruleMessage) {
      return __privateMethod(this, _SimpleMessagesProvider_instances, interpolate_fn).call(this, ruleMessage, {
        field: fieldName,
        ...args
      });
    }
    return __privateMethod(this, _SimpleMessagesProvider_instances, interpolate_fn).call(this, rawMessage, {
      field: fieldName,
      ...args
    });
  }
  toJSON() {
    return {
      messages: __privateGet(this, _messages),
      fields: __privateGet(this, _fields)
    };
  }
}, _messages = new WeakMap(), _fields = new WeakMap(), _SimpleMessagesProvider_instances = new WeakSet(), /**
 * Interpolates place holders within error messages
 */
interpolate_fn = function(message, data) {
  if (!message.includes("{{")) {
    return message;
  }
  return message.replace(/(\\)?{{(.*?)}}/g, (_, __, key) => {
    const tokens = key.trim().split(".");
    let output = data;
    while (tokens.length) {
      if (output === null || typeof output !== "object") {
        return;
      }
      const token = tokens.shift();
      output = Object.hasOwn(output, token) ? output[token] : void 0;
    }
    return output;
  });
}, _a);
var main_exports = {};
__export(main_exports, {
  E_VALIDATION_ERROR: () => E_VALIDATION_ERROR
});
var ValidationError = class extends Error {
  constructor(messages2, options) {
    super("Validation failure", options);
    /**
     * Http status code for the validation error
     */
    __publicField(this, "status", 422);
    /**
     * Internal code for handling the validation error
     * exception
     */
    __publicField(this, "code", "E_VALIDATION_ERROR");
    this.messages = messages2;
    const ErrorConstructor = this.constructor;
    if ("captureStackTrace" in Error) {
      Error.captureStackTrace(this, ErrorConstructor);
    }
  }
  get [Symbol.toStringTag]() {
    return this.constructor.name;
  }
  toString() {
    return `${this.name} [${this.code}]: ${this.message}`;
  }
};
var E_VALIDATION_ERROR = ValidationError;
var SimpleErrorReporter = class {
  constructor() {
    /**
     * Boolean to know one or more errors have been reported
     */
    __publicField(this, "hasErrors", false);
    /**
     * Collection of errors
     */
    __publicField(this, "errors", []);
  }
  /**
   * Report an error.
   */
  report(message, rule, field, meta) {
    const error = {
      message,
      rule,
      field: field.getFieldPath()
    };
    if (meta) {
      error.meta = meta;
    }
    if (field.isArrayMember) {
      error.index = field.name;
    }
    this.hasErrors = true;
    this.errors.push(error);
  }
  /**
   * Returns an instance of the validation error
   */
  createError() {
    return new E_VALIDATION_ERROR(this.errors);
  }
};
var BOOLEAN_POSITIVES = ["1", 1, "true", true, "on"];
var BOOLEAN_NEGATIVES = ["0", 0, "false", false];
var ULID = /^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/;
var helpers = {
  /**
   * Returns true when value is not null and neither
   * undefined
   */
  exists(value) {
    return value !== null && value !== void 0;
  },
  /**
   * Returns true when value is null or value is undefined
   */
  isMissing(value) {
    return !this.exists(value);
  },
  /**
   * Returns true when the value is one of the following.
   *
   * true
   * 1
   * "1"
   * "true"
   * "on"
   */
  isTrue(value) {
    return BOOLEAN_POSITIVES.includes(value);
  },
  /**
   * Returns true when the value is one of the following.
   *
   * false
   * 0
   * "0"
   * "false"
   */
  isFalse(value) {
    return BOOLEAN_NEGATIVES.includes(value);
  },
  /**
   * Check if the value is a valid string. This method narrows
   * the type of value to string.
   */
  isString(value) {
    return typeof value === "string";
  },
  /**
   * Check if the value is a plain JavaScript object. This method
   * filters out null and Arrays and does not consider them as Objects.
   */
  isObject(value) {
    return !!(value && typeof value === "object" && !Array.isArray(value));
  },
  /**
   * Check if an object has all the mentioned keys
   */
  hasKeys(value, keys) {
    for (let key of keys) {
      if (key in value === false) {
        return false;
      }
    }
    return true;
  },
  /**
   * Check if the value is an Array.
   */
  isArray(value) {
    return Array.isArray(value);
  },
  /**
   * Check if the value is a number or a string representation of a number.
   */
  isNumeric(value) {
    return !Number.isNaN(Number(value));
  },
  /**
   * Casts the value to a number using the Number method.
   * Returns NaN when unable to cast.
   */
  asNumber(value) {
    return value === null ? Number.NaN : Number(value);
  },
  /**
   * Casts the value to a boolean.
   *
   * - [true, 1, "1", "true", "on"] will be converted to true.
   * - [false, 0, "0", "false"] will be converted to false.
   * - Everything else will return null. So make sure to handle that case.
   */
  asBoolean(value) {
    if (this.isTrue(value)) {
      return true;
    }
    if (this.isFalse(value)) {
      return false;
    }
    return null;
  },
  isEmail: import_isEmail.default.default,
  isURL: import_isURL.default.default,
  isAlpha: import_isAlpha.default.default,
  isAlphaNumeric: import_isAlphanumeric.default.default,
  isIP: import_isIP.default.default,
  isUUID: import_isUUID.default.default,
  isAscii: import_isAscii.default.default,
  isCreditCard: import_isCreditCard.default.default,
  isIBAN: import_isIBAN.default.default,
  isJWT: import_isJWT.default.default,
  isLatLong: import_isLatLong.default.default,
  isMobilePhone: import_isMobilePhone.default.default,
  isPassportNumber: import_isPassportNumber.default.default,
  isPostalCode: import_isPostalCode.default.default,
  isSlug: import_isSlug.default.default,
  isDecimal: import_isDecimal.default.default,
  mobileLocales: import_isMobilePhone2.locales,
  postalCountryCodes: import_isPostalCode2.locales,
  passportCountryCodes: [
    "AM",
    "AR",
    "AT",
    "AU",
    "AZ",
    "BE",
    "BG",
    "BR",
    "BY",
    "CA",
    "CH",
    "CY",
    "CZ",
    "DE",
    "DK",
    "DZ",
    "ES",
    "FI",
    "FR",
    "GB",
    "GR",
    "HR",
    "HU",
    "IE",
    "IN",
    "ID",
    "IR",
    "IS",
    "IT",
    "JM",
    "JP",
    "KR",
    "KZ",
    "LI",
    "LT",
    "LU",
    "LV",
    "LY",
    "MT",
    "MZ",
    "MY",
    "MX",
    "NL",
    "NZ",
    "PH",
    "PK",
    "PL",
    "PT",
    "RO",
    "RU",
    "SE",
    "SL",
    "SK",
    "TH",
    "TR",
    "UA",
    "US"
  ],
  /**
   * Check if the value is a valid ULID
   */
  isULID(value) {
    if (typeof value !== "string") {
      return false;
    }
    if (value[0] > "7") {
      return false;
    }
    return ULID.test(value);
  },
  /**
   * Check if the value is a valid color hexcode
   */
  isHexColor: (value) => {
    if (!value.startsWith("#")) {
      return false;
    }
    return import_isHexColor.default.default(value);
  },
  /**
   * Check if a URL has valid `A` or `AAAA` DNS records
   */
  isActiveURL: async (url) => {
    const { resolve4, resolve6 } = await import("./promises-BIUHY4SO.js");
    try {
      const { hostname } = new URL(url);
      const v6Addresses = await resolve6(hostname);
      if (v6Addresses.length) {
        return true;
      } else {
        const v4Addresses = await resolve4(hostname);
        return v4Addresses.length > 0;
      }
    } catch {
      return false;
    }
  },
  /**
   * Check if all the elements inside the dataset are unique.
   *
   * In case of an array of objects, you must provide one or more keys
   * for the fields that must be unique across the objects.
   *
   * ```ts
   * helpers.isDistinct([1, 2, 4, 5]) // true
   *
   * // Null and undefined values are ignored
   * helpers.isDistinct([1, null, 2, null, 4, 5]) // true
   *
   * helpers.isDistinct([
   *   {
   *     email: '<EMAIL>',
   *     name: 'foo'
   *   },
   *   {
   *     email: '<EMAIL>',
   *     name: 'baz'
   *   }
   * ], 'email') // true
   *
   * helpers.isDistinct([
   *   {
   *     email: '<EMAIL>',
   *     tenant_id: 1,
   *     name: 'foo'
   *   },
   *   {
   *     email: '<EMAIL>',
   *     tenant_id: 2,
   *     name: 'baz'
   *   }
   * ], ['email', 'tenant_id']) // true
   * ```
   */
  isDistinct: (dataSet, fields2) => {
    const uniqueItems = /* @__PURE__ */ new Set();
    if (!fields2) {
      for (let item of dataSet) {
        if (helpers.exists(item)) {
          if (uniqueItems.has(item)) {
            return false;
          } else {
            uniqueItems.add(item);
          }
        }
      }
      return true;
    }
    const fieldsList = Array.isArray(fields2) ? fields2 : [fields2];
    for (let item of dataSet) {
      if (helpers.isObject(item) && helpers.hasKeys(item, fieldsList)) {
        const element = fieldsList.map((field) => item[field]).join("_");
        if (uniqueItems.has(element)) {
          return false;
        } else {
          uniqueItems.add(element);
        }
      }
    }
    return true;
  },
  /**
   * Returns the nested value from the field root
   * object or the sibling value from the field
   * parent object
   */
  getNestedValue(key, field) {
    if (key.indexOf(".") > -1) {
      return (0, import_dlv.default)(field.data, key);
    }
    return field.parent[key];
  }
};

// node_modules/@vinejs/vine/build/chunk-M2DOTJGC.js
var messages = {
  "required": "The {{ field }} field must be defined",
  "string": "The {{ field }} field must be a string",
  "email": "The {{ field }} field must be a valid email address",
  "mobile": "The {{ field }} field must be a valid mobile phone number",
  "creditCard": "The {{ field }} field must be a valid {{ providersList }} card number",
  "passport": "The {{ field }} field must be a valid passport number",
  "postalCode": "The {{ field }} field must be a valid postal code",
  "regex": "The {{ field }} field format is invalid",
  "ascii": "The {{ field }} field must only contain ASCII characters",
  "iban": "The {{ field }} field must be a valid IBAN number",
  "jwt": "The {{ field }} field must be a valid JWT token",
  "coordinates": "The {{ field }} field must contain latitude and longitude coordinates",
  "url": "The {{ field }} field must be a valid URL",
  "activeUrl": "The {{ field }} field must be a valid URL",
  "alpha": "The {{ field }} field must contain only letters",
  "alphaNumeric": "The {{ field }} field must contain only letters and numbers",
  "minLength": "The {{ field }} field must have at least {{ min }} characters",
  "maxLength": "The {{ field }} field must not be greater than {{ max }} characters",
  "fixedLength": "The {{ field }} field must be {{ size }} characters long",
  "confirmed": "The {{ field }} field and {{ otherField }} field must be the same",
  "endsWith": "The {{ field }} field must end with {{ substring }}",
  "startsWith": "The {{ field }} field must start with {{ substring }}",
  "sameAs": "The {{ field }} field and {{ otherField }} field must be the same",
  "notSameAs": "The {{ field }} field and {{ otherField }} field must be different",
  "in": "The selected {{ field }} is invalid",
  "notIn": "The selected {{ field }} is invalid",
  "ipAddress": "The {{ field }} field must be a valid IP address",
  "uuid": "The {{ field }} field must be a valid UUID",
  "ulid": "The {{ field }} field must be a valid ULID",
  "hexCode": "The {{ field }} field must be a valid hex color code",
  "boolean": "The value must be a boolean",
  "number": "The {{ field }} field must be a number",
  "number.in": "The selected {{ field }} is not in {{ values }}",
  "min": "The {{ field }} field must be at least {{ min }}",
  "max": "The {{ field }} field must not be greater than {{ max }}",
  "range": "The {{ field }} field must be between {{ min }} and {{ max }}",
  "positive": "The {{ field }} field must be positive",
  "negative": "The {{ field }} field must be negative",
  "decimal": "The {{ field }} field must have {{ digits }} decimal places",
  "withoutDecimals": "The {{ field }} field must be an integer",
  "accepted": "The {{ field }} field must be accepted",
  "enum": "The selected {{ field }} is invalid",
  "literal": "The {{ field }} field must be {{ expectedValue }}",
  "object": "The {{ field }} field must be an object",
  "array": "The {{ field }} field must be an array",
  "array.minLength": "The {{ field }} field must have at least {{ min }} items",
  "array.maxLength": "The {{ field }} field must not have more than {{ max }} items",
  "array.fixedLength": "The {{ field }} field must contain {{ size }} items",
  "notEmpty": "The {{ field }} field must not be empty",
  "distinct": "The {{ field }} field has duplicate values",
  "record": "The {{ field }} field must be an object",
  "record.minLength": "The {{ field }} field must have at least {{ min }} items",
  "record.maxLength": "The {{ field }} field must not have more than {{ max }} items",
  "record.fixedLength": "The {{ field }} field must contain {{ size }} items",
  "tuple": "The {{ field }} field must be an array",
  "union": "Invalid value provided for {{ field }} field",
  "unionGroup": "Invalid value provided for {{ field }} field",
  "unionOfTypes": "Invalid value provided for {{ field }} field",
  "date": "The {{ field }} field must be a datetime value",
  "date.equals": "The {{ field }} field must be a date equal to {{ expectedValue }}",
  "date.after": "The {{ field }} field must be a date after {{ expectedValue }}",
  "date.before": "The {{ field }} field must be a date before {{ expectedValue }}",
  "date.afterOrEqual": "The {{ field }} field must be a date after or equal to {{ expectedValue }}",
  "date.beforeOrEqual": "The {{ field }} field must be a date before or equal to {{ expectedValue }}",
  "date.sameAs": "The {{ field }} field and {{ otherField }} field must be the same",
  "date.notSameAs": "The {{ field }} field and {{ otherField }} field must be different",
  "date.afterField": "The {{ field }} field must be a date after {{ otherField }}",
  "date.afterOrSameAs": "The {{ field }} field must be a date after or same as {{ otherField }}",
  "date.beforeField": "The {{ field }} field must be a date before {{ otherField }}",
  "date.beforeOrSameAs": "The {{ field }} field must be a date before or same as {{ otherField }}",
  "date.weekend": "The {{ field }} field is not a weekend",
  "date.weekday": "The {{ field }} field is not a weekday"
};
var fields = {
  "": "data"
};

// node_modules/@poppinss/macroable/build/index.js
var Macroable = class {
  /**
   *
   * Macros are standard properties that gets added to the class prototype.
   *
   * ```ts
   * MyClass.macro('foo', 'bar')
   * ```
   */
  static macro(name, value) {
    this.prototype[name] = value;
  }
  /**
   *
   * Getters are added to the class prototype using the Object.defineProperty.
   *
   * ```ts
   * MyClass.getter('foo', function foo () {
   *   return 'bar'
   * })
   * ```
   *
   * You can add a singleton getter by enabling the `singleton` flag.
   *
   * ```ts
   * const singleton = true
   *
   * MyClass.getter('foo', function foo () {
   *   return 'bar'
   * }, singleton)
   * ```
   */
  static getter(name, accumulator, singleton = false) {
    Object.defineProperty(this.prototype, name, {
      get() {
        const value = accumulator.call(this);
        if (singleton) {
          Object.defineProperty(this, name, {
            configurable: false,
            enumerable: false,
            value,
            writable: false
          });
        }
        return value;
      },
      configurable: true,
      enumerable: false
    });
  }
};

// node_modules/camelcase/index.js
var UPPERCASE = /[\p{Lu}]/u;
var LOWERCASE = /[\p{Ll}]/u;
var LEADING_CAPITAL = /^[\p{Lu}](?![\p{Lu}])/gu;
var IDENTIFIER = /([\p{Alpha}\p{N}_]|$)/u;
var SEPARATORS = /[_.\- ]+/;
var LEADING_SEPARATORS = new RegExp("^" + SEPARATORS.source);
var SEPARATORS_AND_IDENTIFIER = new RegExp(SEPARATORS.source + IDENTIFIER.source, "gu");
var NUMBERS_AND_IDENTIFIER = new RegExp("\\d+" + IDENTIFIER.source, "gu");
var preserveCamelCase = (string, toLowerCase, toUpperCase, preserveConsecutiveUppercase2) => {
  let isLastCharLower = false;
  let isLastCharUpper = false;
  let isLastLastCharUpper = false;
  let isLastLastCharPreserved = false;
  for (let index = 0; index < string.length; index++) {
    const character = string[index];
    isLastLastCharPreserved = index > 2 ? string[index - 3] === "-" : true;
    if (isLastCharLower && UPPERCASE.test(character)) {
      string = string.slice(0, index) + "-" + string.slice(index);
      isLastCharLower = false;
      isLastLastCharUpper = isLastCharUpper;
      isLastCharUpper = true;
      index++;
    } else if (isLastCharUpper && isLastLastCharUpper && LOWERCASE.test(character) && (!isLastLastCharPreserved || preserveConsecutiveUppercase2)) {
      string = string.slice(0, index - 1) + "-" + string.slice(index - 1);
      isLastLastCharUpper = isLastCharUpper;
      isLastCharUpper = false;
      isLastCharLower = true;
    } else {
      isLastCharLower = toLowerCase(character) === character && toUpperCase(character) !== character;
      isLastLastCharUpper = isLastCharUpper;
      isLastCharUpper = toUpperCase(character) === character && toLowerCase(character) !== character;
    }
  }
  return string;
};
var preserveConsecutiveUppercase = (input, toLowerCase) => {
  LEADING_CAPITAL.lastIndex = 0;
  return input.replaceAll(LEADING_CAPITAL, (match) => toLowerCase(match));
};
var postProcess = (input, toUpperCase) => {
  SEPARATORS_AND_IDENTIFIER.lastIndex = 0;
  NUMBERS_AND_IDENTIFIER.lastIndex = 0;
  return input.replaceAll(NUMBERS_AND_IDENTIFIER, (match, pattern, offset) => ["_", "-"].includes(input.charAt(offset + match.length)) ? match : toUpperCase(match)).replaceAll(SEPARATORS_AND_IDENTIFIER, (_, identifier) => toUpperCase(identifier));
};
function camelCase(input, options) {
  if (!(typeof input === "string" || Array.isArray(input))) {
    throw new TypeError("Expected the input to be `string | string[]`");
  }
  options = {
    pascalCase: false,
    preserveConsecutiveUppercase: false,
    ...options
  };
  if (Array.isArray(input)) {
    input = input.map((x) => x.trim()).filter((x) => x.length).join("-");
  } else {
    input = input.trim();
  }
  if (input.length === 0) {
    return "";
  }
  const toLowerCase = options.locale === false ? (string) => string.toLowerCase() : (string) => string.toLocaleLowerCase(options.locale);
  const toUpperCase = options.locale === false ? (string) => string.toUpperCase() : (string) => string.toLocaleUpperCase(options.locale);
  if (input.length === 1) {
    if (SEPARATORS.test(input)) {
      return "";
    }
    return options.pascalCase ? toUpperCase(input) : toLowerCase(input);
  }
  const hasUpperCase = input !== toLowerCase(input);
  if (hasUpperCase) {
    input = preserveCamelCase(input, toLowerCase, toUpperCase, options.preserveConsecutiveUppercase);
  }
  input = input.replace(LEADING_SEPARATORS, "");
  input = options.preserveConsecutiveUppercase ? preserveConsecutiveUppercase(input, toLowerCase) : toLowerCase(input);
  if (options.pascalCase) {
    input = toUpperCase(input.charAt(0)) + input.slice(1);
  }
  return postProcess(input, toUpperCase);
}

// node_modules/@vinejs/vine/build/chunk-FED7BU4B.js
var import_dayjs = __toESM(require_dayjs_min(), 1);
var import_dayjs2 = __toESM(require_dayjs_min(), 1);
var import_isSameOrAfter = __toESM(require_isSameOrAfter(), 1);
var import_isSameOrBefore = __toESM(require_isSameOrBefore(), 1);
var import_customParseFormat = __toESM(require_customParseFormat(), 1);

// node_modules/normalize-url/index.js
var DATA_URL_DEFAULT_MIME_TYPE = "text/plain";
var DATA_URL_DEFAULT_CHARSET = "us-ascii";
var testParameter = (name, filters) => filters.some((filter) => filter instanceof RegExp ? filter.test(name) : filter === name);
var supportedProtocols = /* @__PURE__ */ new Set([
  "https:",
  "http:",
  "file:"
]);
var hasCustomProtocol = (urlString) => {
  try {
    const { protocol } = new URL(urlString);
    return protocol.endsWith(":") && !protocol.includes(".") && !supportedProtocols.has(protocol);
  } catch {
    return false;
  }
};
var normalizeDataURL = (urlString, { stripHash }) => {
  var _a36;
  const match = /^data:(?<type>[^,]*?),(?<data>[^#]*?)(?:#(?<hash>.*))?$/.exec(urlString);
  if (!match) {
    throw new Error(`Invalid URL: ${urlString}`);
  }
  let { type, data, hash } = match.groups;
  const mediaType = type.split(";");
  hash = stripHash ? "" : hash;
  let isBase64 = false;
  if (mediaType[mediaType.length - 1] === "base64") {
    mediaType.pop();
    isBase64 = true;
  }
  const mimeType = ((_a36 = mediaType.shift()) == null ? void 0 : _a36.toLowerCase()) ?? "";
  const attributes = mediaType.map((attribute) => {
    let [key, value = ""] = attribute.split("=").map((string) => string.trim());
    if (key === "charset") {
      value = value.toLowerCase();
      if (value === DATA_URL_DEFAULT_CHARSET) {
        return "";
      }
    }
    return `${key}${value ? `=${value}` : ""}`;
  }).filter(Boolean);
  const normalizedMediaType = [
    ...attributes
  ];
  if (isBase64) {
    normalizedMediaType.push("base64");
  }
  if (normalizedMediaType.length > 0 || mimeType && mimeType !== DATA_URL_DEFAULT_MIME_TYPE) {
    normalizedMediaType.unshift(mimeType);
  }
  return `data:${normalizedMediaType.join(";")},${isBase64 ? data.trim() : data}${hash ? `#${hash}` : ""}`;
};
function normalizeUrl(urlString, options) {
  options = {
    defaultProtocol: "http",
    normalizeProtocol: true,
    forceHttp: false,
    forceHttps: false,
    stripAuthentication: true,
    stripHash: false,
    stripTextFragment: true,
    stripWWW: true,
    removeQueryParameters: [/^utm_\w+/i],
    removeTrailingSlash: true,
    removeSingleSlash: true,
    removeDirectoryIndex: false,
    removeExplicitPort: false,
    sortQueryParameters: true,
    ...options
  };
  if (typeof options.defaultProtocol === "string" && !options.defaultProtocol.endsWith(":")) {
    options.defaultProtocol = `${options.defaultProtocol}:`;
  }
  urlString = urlString.trim();
  if (/^data:/i.test(urlString)) {
    return normalizeDataURL(urlString, options);
  }
  if (hasCustomProtocol(urlString)) {
    return urlString;
  }
  const hasRelativeProtocol = urlString.startsWith("//");
  const isRelativeUrl = !hasRelativeProtocol && /^\.*\//.test(urlString);
  if (!isRelativeUrl) {
    urlString = urlString.replace(/^(?!(?:\w+:)?\/\/)|^\/\//, options.defaultProtocol);
  }
  const urlObject = new URL(urlString);
  if (options.forceHttp && options.forceHttps) {
    throw new Error("The `forceHttp` and `forceHttps` options cannot be used together");
  }
  if (options.forceHttp && urlObject.protocol === "https:") {
    urlObject.protocol = "http:";
  }
  if (options.forceHttps && urlObject.protocol === "http:") {
    urlObject.protocol = "https:";
  }
  if (options.stripAuthentication) {
    urlObject.username = "";
    urlObject.password = "";
  }
  if (options.stripHash) {
    urlObject.hash = "";
  } else if (options.stripTextFragment) {
    urlObject.hash = urlObject.hash.replace(/#?:~:text.*?$/i, "");
  }
  if (urlObject.pathname) {
    const protocolRegex = /\b[a-z][a-z\d+\-.]{1,50}:\/\//g;
    let lastIndex = 0;
    let result = "";
    for (; ; ) {
      const match = protocolRegex.exec(urlObject.pathname);
      if (!match) {
        break;
      }
      const protocol = match[0];
      const protocolAtIndex = match.index;
      const intermediate = urlObject.pathname.slice(lastIndex, protocolAtIndex);
      result += intermediate.replace(/\/{2,}/g, "/");
      result += protocol;
      lastIndex = protocolAtIndex + protocol.length;
    }
    const remnant = urlObject.pathname.slice(lastIndex, urlObject.pathname.length);
    result += remnant.replace(/\/{2,}/g, "/");
    urlObject.pathname = result;
  }
  if (urlObject.pathname) {
    try {
      urlObject.pathname = decodeURI(urlObject.pathname);
    } catch {
    }
  }
  if (options.removeDirectoryIndex === true) {
    options.removeDirectoryIndex = [/^index\.[a-z]+$/];
  }
  if (Array.isArray(options.removeDirectoryIndex) && options.removeDirectoryIndex.length > 0) {
    let pathComponents = urlObject.pathname.split("/");
    const lastComponent = pathComponents[pathComponents.length - 1];
    if (testParameter(lastComponent, options.removeDirectoryIndex)) {
      pathComponents = pathComponents.slice(0, -1);
      urlObject.pathname = pathComponents.slice(1).join("/") + "/";
    }
  }
  if (urlObject.hostname) {
    urlObject.hostname = urlObject.hostname.replace(/\.$/, "");
    if (options.stripWWW && /^www\.(?!www\.)[a-z\-\d]{1,63}\.[a-z.\-\d]{2,63}$/.test(urlObject.hostname)) {
      urlObject.hostname = urlObject.hostname.replace(/^www\./, "");
    }
  }
  if (Array.isArray(options.removeQueryParameters)) {
    for (const key of [...urlObject.searchParams.keys()]) {
      if (testParameter(key, options.removeQueryParameters)) {
        urlObject.searchParams.delete(key);
      }
    }
  }
  if (!Array.isArray(options.keepQueryParameters) && options.removeQueryParameters === true) {
    urlObject.search = "";
  }
  if (Array.isArray(options.keepQueryParameters) && options.keepQueryParameters.length > 0) {
    for (const key of [...urlObject.searchParams.keys()]) {
      if (!testParameter(key, options.keepQueryParameters)) {
        urlObject.searchParams.delete(key);
      }
    }
  }
  if (options.sortQueryParameters) {
    urlObject.searchParams.sort();
    try {
      urlObject.search = decodeURIComponent(urlObject.search);
    } catch {
    }
  }
  if (options.removeTrailingSlash) {
    urlObject.pathname = urlObject.pathname.replace(/\/$/, "");
  }
  if (options.removeExplicitPort && urlObject.port) {
    urlObject.port = "";
  }
  const oldUrlString = urlString;
  urlString = urlObject.toString();
  if (!options.removeSingleSlash && urlObject.pathname === "/" && !oldUrlString.endsWith("/") && urlObject.hash === "") {
    urlString = urlString.replace(/\/$/, "");
  }
  if ((options.removeTrailingSlash || urlObject.pathname === "/") && urlObject.hash === "" && options.removeSingleSlash) {
    urlString = urlString.replace(/\/$/, "");
  }
  if (hasRelativeProtocol && !options.normalizeProtocol) {
    urlString = urlString.replace(/^http:\/\//, "//");
  }
  if (options.stripProtocol) {
    urlString = urlString.replace(/^(?:https?:)?\/\//, "");
  }
  return urlString;
}

// node_modules/@vinejs/vine/build/chunk-FED7BU4B.js
var import_escape = __toESM(require_escape(), 1);
var import_normalizeEmail = __toESM(require_normalizeEmail(), 1);

// node_modules/@vinejs/compiler/build/chunk-K5F7IOJS.js
var _content, _a2;
var CompilerBuffer = (_a2 = class {
  constructor() {
    __privateAdd(this, _content, "");
    /**
     * The character used to create a new line
     */
    __publicField(this, "newLine", "\n");
  }
  /**
   * Write statement ot the output
   */
  writeStatement(statement) {
    __privateSet(this, _content, `${__privateGet(this, _content)}${this.newLine}${statement}`);
  }
  /**
   * Creates a child buffer
   */
  child() {
    return new _a2();
  }
  /**
   * Returns the buffer contents as string
   */
  toString() {
    return __privateGet(this, _content);
  }
  /**
   * Flush in-memory string
   */
  flush() {
    __privateSet(this, _content, "");
  }
}, _content = new WeakMap(), _a2);
function defineFieldVariables({
  parseFnRefId,
  variableName,
  wildCardPath,
  isArrayMember,
  valueExpression,
  parentExpression,
  fieldNameExpression,
  parentValueExpression
}) {
  const inValueExpression = parseFnRefId ? `refs['${parseFnRefId}'](${valueExpression}, {
      data: root,
      meta: meta,
      parent: ${parentValueExpression}
    })` : valueExpression;
  let fieldPathOutputExpression = "";
  if (parentExpression === "root" || parentExpression === "root_item") {
    fieldPathOutputExpression = fieldNameExpression;
  } else if (fieldNameExpression !== "''") {
    fieldPathOutputExpression = `${parentExpression}.getFieldPath() + '.' + ${fieldNameExpression}`;
  }
  return `const ${variableName} = defineValue(${inValueExpression}, {
  data: root,
  meta: meta,
  name: ${fieldNameExpression},
  wildCardPath: '${wildCardPath}',
  getFieldPath() {
    return ${fieldPathOutputExpression};
  },
  mutate: defineValue,
  report: report,
  isValid: true,
  parent: ${parentValueExpression},
  isArrayMember: ${isArrayMember},
});`;
}
var _node, _parentField, _a3;
var BaseNode = (_a3 = class {
  constructor(node, compiler, parent, parentField) {
    __privateAdd(this, _node);
    __privateAdd(this, _parentField);
    __publicField(this, "field");
    __privateSet(this, _parentField, parentField);
    __privateSet(this, _node, node);
    if (__privateGet(this, _parentField)) {
      this.field = __privateGet(this, _parentField);
    } else {
      compiler.variablesCounter++;
      this.field = compiler.createFieldFor(node, parent);
    }
  }
  defineField(buffer) {
    if (!__privateGet(this, _parentField)) {
      buffer.writeStatement(
        defineFieldVariables({
          fieldNameExpression: this.field.fieldNameExpression,
          isArrayMember: this.field.isArrayMember,
          parentExpression: this.field.parentExpression,
          parentValueExpression: this.field.parentValueExpression,
          valueExpression: this.field.valueExpression,
          variableName: this.field.variableName,
          wildCardPath: this.field.wildCardPath,
          parseFnRefId: "parseFnId" in __privateGet(this, _node) ? __privateGet(this, _node).parseFnId : void 0
        })
      );
    }
  }
}, _node = new WeakMap(), _parentField = new WeakMap(), _a3);
function defineArrayGuard({ variableName, guardedCodeSnippet }) {
  return `if (${variableName}_is_array) {
${guardedCodeSnippet}
}`;
}
function defineIsValidGuard({ variableName, bail, guardedCodeSnippet }) {
  if (!bail) {
    return guardedCodeSnippet;
  }
  return `if (${variableName}.isValid) {
${guardedCodeSnippet}
}`;
}
function defineFieldNullOutput({
  allowNull,
  conditional,
  variableName,
  outputExpression,
  transformFnRefId
}) {
  if (!allowNull) {
    return "";
  }
  return `${conditional || "if"}(${variableName}.value === null) {
  ${outputExpression} = ${transformFnRefId ? `refs['${transformFnRefId}'](null, ${variableName});` : "null;"}
}`;
}
function wrapInConditional(conditions, wrappingCode) {
  const [first, second] = conditions;
  if (first && second) {
    return `if (${first} && ${second}) {
  ${wrappingCode}
}`;
  }
  if (first) {
    return `if (${first}) {
  ${wrappingCode}
}`;
  }
  if (second) {
    return `if (${second}) {
  ${wrappingCode}
}`;
  }
  return wrappingCode;
}
function emitValidationSnippet({ isAsync, implicit, ruleFnId }, variableName, bail, dropMissingCheck, existenceCheckExpression) {
  const rule = `refs['${ruleFnId}']`;
  const callable = `${rule}.validator(${variableName}.value, ${rule}.options, ${variableName});`;
  existenceCheckExpression = existenceCheckExpression || `${variableName}.isDefined`;
  const bailCondition = bail ? `${variableName}.isValid` : "";
  const implicitCondition = implicit || dropMissingCheck ? "" : existenceCheckExpression;
  return wrapInConditional(
    [bailCondition, implicitCondition],
    isAsync ? `await ${callable}` : `${callable}`
  );
}
function defineFieldValidations({
  bail,
  validations,
  variableName,
  dropMissingCheck,
  existenceCheckExpression
}) {
  return `${validations.map(
    (one) => emitValidationSnippet(one, variableName, bail, dropMissingCheck, existenceCheckExpression)
  ).join("\n")}`;
}
function defineArrayInitialOutput({
  variableName,
  outputExpression,
  outputValueExpression
}) {
  return `const ${variableName}_out = ${outputValueExpression};
${outputExpression} = ${variableName}_out;`;
}
function defineFieldExistenceValidations({
  allowNull,
  isOptional,
  variableName
}) {
  if (isOptional === false) {
    if (allowNull === false) {
      return `ensureExists(${variableName});`;
    } else {
      return `ensureIsDefined(${variableName});`;
    }
  }
  return "";
}
function defineArrayVariables({ variableName }) {
  return `const ${variableName}_is_array = ensureIsArray(${variableName});`;
}
var _node2, _buffer, _compiler, _TupleNodeCompiler_instances, compileTupleChildren_fn, _a4;
var TupleNodeCompiler = (_a4 = class extends BaseNode {
  constructor(node, buffer, compiler, parent, parentField) {
    super(node, compiler, parent, parentField);
    __privateAdd(this, _TupleNodeCompiler_instances);
    __privateAdd(this, _node2);
    __privateAdd(this, _buffer);
    __privateAdd(this, _compiler);
    __privateSet(this, _node2, node);
    __privateSet(this, _buffer, buffer);
    __privateSet(this, _compiler, compiler);
  }
  compile() {
    this.defineField(__privateGet(this, _buffer));
    __privateGet(this, _buffer).writeStatement(
      defineFieldExistenceValidations({
        allowNull: __privateGet(this, _node2).allowNull,
        isOptional: __privateGet(this, _node2).isOptional,
        variableName: this.field.variableName
      })
    );
    __privateGet(this, _buffer).writeStatement(
      defineArrayVariables({
        variableName: this.field.variableName
      })
    );
    __privateGet(this, _buffer).writeStatement(
      defineFieldValidations({
        variableName: this.field.variableName,
        validations: __privateGet(this, _node2).validations,
        bail: __privateGet(this, _node2).bail,
        dropMissingCheck: false,
        existenceCheckExpression: `${this.field.variableName}_is_array`
      })
    );
    const isArrayValidBlock = defineArrayGuard({
      variableName: this.field.variableName,
      guardedCodeSnippet: `${__privateGet(this, _buffer).newLine}${defineIsValidGuard({
        variableName: this.field.variableName,
        bail: __privateGet(this, _node2).bail,
        guardedCodeSnippet: `${defineArrayInitialOutput({
          variableName: this.field.variableName,
          outputExpression: this.field.outputExpression,
          outputValueExpression: __privateGet(this, _node2).allowUnknownProperties ? `copyProperties(${this.field.variableName}.value)` : `[]`
        })}${__privateGet(this, _buffer).newLine}${__privateMethod(this, _TupleNodeCompiler_instances, compileTupleChildren_fn).call(this)}`
      })}`
    });
    __privateGet(this, _buffer).writeStatement(
      `${isArrayValidBlock}${__privateGet(this, _buffer).newLine}${defineFieldNullOutput({
        allowNull: __privateGet(this, _node2).allowNull,
        outputExpression: this.field.outputExpression,
        variableName: this.field.variableName,
        conditional: "else if"
      })}`
    );
  }
}, _node2 = new WeakMap(), _buffer = new WeakMap(), _compiler = new WeakMap(), _TupleNodeCompiler_instances = new WeakSet(), /**
 * Compiles the tuple children to a JS fragment
 */
compileTupleChildren_fn = function() {
  const buffer = __privateGet(this, _buffer).child();
  const parent = {
    type: "tuple",
    fieldPathExpression: this.field.fieldPathExpression,
    outputExpression: this.field.outputExpression,
    variableName: this.field.variableName,
    wildCardPath: this.field.wildCardPath
  };
  __privateGet(this, _node2).properties.forEach((child) => {
    __privateGet(this, _compiler).compileNode(child, buffer, parent);
  });
  return buffer.toString();
}, _a4);
function defineArrayLoop({
  variableName,
  loopCodeSnippet,
  startingIndex
}) {
  startingIndex = startingIndex || 0;
  return `const ${variableName}_items_size = ${variableName}.value.length;
for (let ${variableName}_i = ${startingIndex}; ${variableName}_i < ${variableName}_items_size; ${variableName}_i++) {
${loopCodeSnippet}
}`;
}
var _node3, _buffer2, _compiler2, _ArrayNodeCompiler_instances, compileArrayElements_fn, _a5;
var ArrayNodeCompiler = (_a5 = class extends BaseNode {
  constructor(node, buffer, compiler, parent, parentField) {
    super(node, compiler, parent, parentField);
    __privateAdd(this, _ArrayNodeCompiler_instances);
    __privateAdd(this, _node3);
    __privateAdd(this, _buffer2);
    __privateAdd(this, _compiler2);
    __privateSet(this, _node3, node);
    __privateSet(this, _buffer2, buffer);
    __privateSet(this, _compiler2, compiler);
  }
  compile() {
    this.defineField(__privateGet(this, _buffer2));
    __privateGet(this, _buffer2).writeStatement(
      defineFieldExistenceValidations({
        allowNull: __privateGet(this, _node3).allowNull,
        isOptional: __privateGet(this, _node3).isOptional,
        variableName: this.field.variableName
      })
    );
    __privateGet(this, _buffer2).writeStatement(
      defineArrayVariables({
        variableName: this.field.variableName
      })
    );
    __privateGet(this, _buffer2).writeStatement(
      defineFieldValidations({
        variableName: this.field.variableName,
        validations: __privateGet(this, _node3).validations,
        bail: __privateGet(this, _node3).bail,
        dropMissingCheck: false,
        existenceCheckExpression: `${this.field.variableName}_is_array`
      })
    );
    const isArrayValidBlock = defineArrayGuard({
      variableName: this.field.variableName,
      guardedCodeSnippet: `${__privateGet(this, _buffer2).newLine}${defineIsValidGuard({
        variableName: this.field.variableName,
        bail: __privateGet(this, _node3).bail,
        guardedCodeSnippet: `${defineArrayInitialOutput({
          variableName: this.field.variableName,
          outputExpression: this.field.outputExpression,
          outputValueExpression: `[]`
        })}${__privateGet(this, _buffer2).newLine}${__privateMethod(this, _ArrayNodeCompiler_instances, compileArrayElements_fn).call(this)}`
      })}`
    });
    __privateGet(this, _buffer2).writeStatement(
      `${isArrayValidBlock}${__privateGet(this, _buffer2).newLine}${defineFieldNullOutput({
        allowNull: __privateGet(this, _node3).allowNull,
        outputExpression: this.field.outputExpression,
        variableName: this.field.variableName,
        conditional: "else if"
      })}`
    );
  }
}, _node3 = new WeakMap(), _buffer2 = new WeakMap(), _compiler2 = new WeakMap(), _ArrayNodeCompiler_instances = new WeakSet(), /**
 * Compiles the array elements to a JS fragment
 */
compileArrayElements_fn = function() {
  const arrayElementsBuffer = __privateGet(this, _buffer2).child();
  __privateGet(this, _compiler2).compileNode(__privateGet(this, _node3).each, arrayElementsBuffer, {
    type: "array",
    fieldPathExpression: this.field.fieldPathExpression,
    outputExpression: this.field.outputExpression,
    variableName: this.field.variableName,
    wildCardPath: this.field.wildCardPath
  });
  const buffer = __privateGet(this, _buffer2).child();
  buffer.writeStatement(
    defineArrayLoop({
      variableName: this.field.variableName,
      startingIndex: 0,
      loopCodeSnippet: arrayElementsBuffer.toString()
    })
  );
  arrayElementsBuffer.flush();
  return buffer.toString();
}, _a5);
function callParseFunction({ parseFnRefId, variableName }) {
  if (parseFnRefId) {
    return `${variableName}.value = refs['${parseFnRefId}'](${variableName}.value);`;
  }
  return "";
}
function defineElseCondition({ variableName, conditionalFnRefId }) {
  return `else {
refs['${conditionalFnRefId}'](${variableName}.value, ${variableName});
}`;
}
function defineConditionalGuard({
  conditional,
  variableName,
  conditionalFnRefId,
  guardedCodeSnippet
}) {
  return `${conditional}(refs['${conditionalFnRefId}'](${variableName}.value, ${variableName})) {
${guardedCodeSnippet}
}`;
}
var _compiler3, _node4, _buffer3, _parent, _UnionNodeCompiler_instances, compileUnionChildren_fn, _a6;
var UnionNodeCompiler = (_a6 = class extends BaseNode {
  constructor(node, buffer, compiler, parent, parentField) {
    super(node, compiler, parent, parentField);
    __privateAdd(this, _UnionNodeCompiler_instances);
    __privateAdd(this, _compiler3);
    __privateAdd(this, _node4);
    __privateAdd(this, _buffer3);
    __privateAdd(this, _parent);
    __privateSet(this, _node4, node);
    __privateSet(this, _buffer3, buffer);
    __privateSet(this, _parent, parent);
    __privateSet(this, _compiler3, compiler);
  }
  compile() {
    this.defineField(__privateGet(this, _buffer3));
    __privateGet(this, _buffer3).writeStatement(__privateMethod(this, _UnionNodeCompiler_instances, compileUnionChildren_fn).call(this));
  }
}, _compiler3 = new WeakMap(), _node4 = new WeakMap(), _buffer3 = new WeakMap(), _parent = new WeakMap(), _UnionNodeCompiler_instances = new WeakSet(), /**
 * Compiles union children by wrapping each conditon inside a conditional
 * guard block
 */
compileUnionChildren_fn = function() {
  const childrenBuffer = __privateGet(this, _buffer3).child();
  __privateGet(this, _node4).conditions.forEach((child, index) => {
    const conditionalBuffer = __privateGet(this, _buffer3).child();
    if ("parseFnId" in child.schema) {
      conditionalBuffer.writeStatement(
        callParseFunction({
          parseFnRefId: child.schema.parseFnId,
          variableName: this.field.variableName
        })
      );
    }
    __privateGet(this, _compiler3).compileNode(child.schema, conditionalBuffer, __privateGet(this, _parent), this.field);
    childrenBuffer.writeStatement(
      defineConditionalGuard({
        conditional: index === 0 ? "if" : "else if",
        variableName: this.field.variableName,
        conditionalFnRefId: child.conditionalFnRefId,
        guardedCodeSnippet: conditionalBuffer.toString()
      })
    );
    conditionalBuffer.flush();
  });
  if (__privateGet(this, _node4).elseConditionalFnRefId && __privateGet(this, _node4).conditions.length) {
    childrenBuffer.writeStatement(
      defineElseCondition({
        variableName: this.field.variableName,
        conditionalFnRefId: __privateGet(this, _node4).elseConditionalFnRefId
      })
    );
  }
  return childrenBuffer.toString();
}, _a6);
function defineRecordLoop({ variableName, loopCodeSnippet }) {
  return `const ${variableName}_keys = Object.keys(${variableName}.value);
const ${variableName}_keys_size = ${variableName}_keys.length;
for (let ${variableName}_key_i = 0; ${variableName}_key_i < ${variableName}_keys_size; ${variableName}_key_i++) {
const ${variableName}_i = ${variableName}_keys[${variableName}_key_i];
${loopCodeSnippet}
}`;
}
function defineObjectGuard({ variableName, guardedCodeSnippet }) {
  return `if (${variableName}_is_object) {
${guardedCodeSnippet}
}`;
}
function defineObjectInitialOutput({
  variableName,
  outputExpression,
  outputValueExpression
}) {
  return `const ${variableName}_out = ${outputValueExpression};
${outputExpression} = ${variableName}_out;`;
}
function defineObjectVariables({ variableName }) {
  return `const ${variableName}_is_object = ensureIsObject(${variableName});`;
}
var _node5, _buffer4, _compiler4, _RecordNodeCompiler_instances, compileRecordElements_fn, _a7;
var RecordNodeCompiler = (_a7 = class extends BaseNode {
  constructor(node, buffer, compiler, parent, parentField) {
    super(node, compiler, parent, parentField);
    __privateAdd(this, _RecordNodeCompiler_instances);
    __privateAdd(this, _node5);
    __privateAdd(this, _buffer4);
    __privateAdd(this, _compiler4);
    __privateSet(this, _node5, node);
    __privateSet(this, _buffer4, buffer);
    __privateSet(this, _compiler4, compiler);
  }
  compile() {
    this.defineField(__privateGet(this, _buffer4));
    __privateGet(this, _buffer4).writeStatement(
      defineFieldExistenceValidations({
        allowNull: __privateGet(this, _node5).allowNull,
        isOptional: __privateGet(this, _node5).isOptional,
        variableName: this.field.variableName
      })
    );
    __privateGet(this, _buffer4).writeStatement(
      defineObjectVariables({
        variableName: this.field.variableName
      })
    );
    __privateGet(this, _buffer4).writeStatement(
      defineFieldValidations({
        variableName: this.field.variableName,
        validations: __privateGet(this, _node5).validations,
        bail: __privateGet(this, _node5).bail,
        dropMissingCheck: false,
        existenceCheckExpression: `${this.field.variableName}_is_object`
      })
    );
    const isObjectValidBlock = defineIsValidGuard({
      variableName: this.field.variableName,
      bail: __privateGet(this, _node5).bail,
      guardedCodeSnippet: `${defineObjectInitialOutput({
        variableName: this.field.variableName,
        outputExpression: this.field.outputExpression,
        outputValueExpression: `{}`
      })}${__privateMethod(this, _RecordNodeCompiler_instances, compileRecordElements_fn).call(this)}`
    });
    const isValueAnObjectBlock = defineObjectGuard({
      variableName: this.field.variableName,
      guardedCodeSnippet: `${__privateGet(this, _buffer4).newLine}${isObjectValidBlock}`
    });
    __privateGet(this, _buffer4).writeStatement(
      `${isValueAnObjectBlock}${__privateGet(this, _buffer4).newLine}${defineFieldNullOutput({
        allowNull: __privateGet(this, _node5).allowNull,
        outputExpression: this.field.outputExpression,
        variableName: this.field.variableName,
        conditional: "else if"
      })}`
    );
  }
}, _node5 = new WeakMap(), _buffer4 = new WeakMap(), _compiler4 = new WeakMap(), _RecordNodeCompiler_instances = new WeakSet(), /**
 * Compiles the record elements to a JS fragment
 */
compileRecordElements_fn = function() {
  const buffer = __privateGet(this, _buffer4).child();
  const recordElementsBuffer = __privateGet(this, _buffer4).child();
  __privateGet(this, _compiler4).compileNode(__privateGet(this, _node5).each, recordElementsBuffer, {
    type: "record",
    fieldPathExpression: this.field.fieldPathExpression,
    outputExpression: this.field.outputExpression,
    variableName: this.field.variableName,
    wildCardPath: this.field.wildCardPath
  });
  buffer.writeStatement(
    defineRecordLoop({
      variableName: this.field.variableName,
      loopCodeSnippet: recordElementsBuffer.toString()
    })
  );
  recordElementsBuffer.flush();
  return buffer.toString();
}, _a7);
function arrayToString(arr) {
  return `[${arr.map((str) => `"${str}"`).join(", ")}]`;
}
function defineMoveProperties({
  variableName,
  fieldsToIgnore,
  allowUnknownProperties
}) {
  if (!allowUnknownProperties) {
    return "";
  }
  const serializedFieldsToIgnore = arrayToString(fieldsToIgnore);
  return `moveProperties(${variableName}.value, ${variableName}_out, ${serializedFieldsToIgnore});`;
}
var _node6, _buffer5, _compiler5, _ObjectNodeCompiler_instances, getFieldNames_fn, getGroupFieldNames_fn, compileObjectChildren_fn, compileObjectGroups_fn, compileObjectGroup_fn, _a8;
var ObjectNodeCompiler = (_a8 = class extends BaseNode {
  constructor(node, buffer, compiler, parent, parentField) {
    super(node, compiler, parent, parentField);
    __privateAdd(this, _ObjectNodeCompiler_instances);
    __privateAdd(this, _node6);
    __privateAdd(this, _buffer5);
    __privateAdd(this, _compiler5);
    __privateSet(this, _node6, node);
    __privateSet(this, _buffer5, buffer);
    __privateSet(this, _compiler5, compiler);
  }
  compile() {
    this.defineField(__privateGet(this, _buffer5));
    __privateGet(this, _buffer5).writeStatement(
      defineFieldExistenceValidations({
        allowNull: __privateGet(this, _node6).allowNull,
        isOptional: __privateGet(this, _node6).isOptional,
        variableName: this.field.variableName
      })
    );
    __privateGet(this, _buffer5).writeStatement(
      defineObjectVariables({
        variableName: this.field.variableName
      })
    );
    __privateGet(this, _buffer5).writeStatement(
      defineFieldValidations({
        variableName: this.field.variableName,
        validations: __privateGet(this, _node6).validations,
        bail: __privateGet(this, _node6).bail,
        dropMissingCheck: false,
        existenceCheckExpression: `${this.field.variableName}_is_object`
      })
    );
    const isObjectValidBlock = defineIsValidGuard({
      variableName: this.field.variableName,
      bail: __privateGet(this, _node6).bail,
      guardedCodeSnippet: `${defineObjectInitialOutput({
        variableName: this.field.variableName,
        outputExpression: this.field.outputExpression,
        outputValueExpression: "{}"
      })}${__privateGet(this, _buffer5).newLine}${__privateMethod(this, _ObjectNodeCompiler_instances, compileObjectChildren_fn).call(this)}${__privateGet(this, _buffer5).newLine}${__privateMethod(this, _ObjectNodeCompiler_instances, compileObjectGroups_fn).call(this)}${__privateGet(this, _buffer5).newLine}${defineMoveProperties({
        variableName: this.field.variableName,
        allowUnknownProperties: __privateGet(this, _node6).allowUnknownProperties,
        fieldsToIgnore: __privateGet(this, _node6).allowUnknownProperties ? __privateMethod(this, _ObjectNodeCompiler_instances, getFieldNames_fn).call(this, __privateGet(this, _node6)) : []
      })}`
    });
    const isValueAnObject = defineObjectGuard({
      variableName: this.field.variableName,
      guardedCodeSnippet: `${isObjectValidBlock}`
    });
    __privateGet(this, _buffer5).writeStatement(
      `${isValueAnObject}${__privateGet(this, _buffer5).newLine}${defineFieldNullOutput({
        variableName: this.field.variableName,
        allowNull: __privateGet(this, _node6).allowNull,
        outputExpression: this.field.outputExpression,
        conditional: "else if"
      })}`
    );
  }
}, _node6 = new WeakMap(), _buffer5 = new WeakMap(), _compiler5 = new WeakMap(), _ObjectNodeCompiler_instances = new WeakSet(), /**
 * Returns known field names for the object
 */
getFieldNames_fn = function(node) {
  let fieldNames = node.properties.map((child) => child.fieldName);
  const groupsFieldNames = node.groups.flatMap((group2) => __privateMethod(this, _ObjectNodeCompiler_instances, getGroupFieldNames_fn).call(this, group2));
  return fieldNames.concat(groupsFieldNames);
}, /**
 * Returns field names of a group.
 */
getGroupFieldNames_fn = function(group2) {
  return group2.conditions.flatMap((condition) => {
    return __privateMethod(this, _ObjectNodeCompiler_instances, getFieldNames_fn).call(this, condition.schema);
  });
}, /**
 * Compiles object children to JS output
 */
compileObjectChildren_fn = function() {
  const buffer = __privateGet(this, _buffer5).child();
  const parent = {
    type: "object",
    fieldPathExpression: this.field.fieldPathExpression,
    outputExpression: this.field.outputExpression,
    variableName: this.field.variableName,
    wildCardPath: this.field.wildCardPath
  };
  __privateGet(this, _node6).properties.forEach((child) => __privateGet(this, _compiler5).compileNode(child, buffer, parent));
  return buffer.toString();
}, /**
 * Compiles object groups with conditions to JS output.
 */
compileObjectGroups_fn = function() {
  const buffer = __privateGet(this, _buffer5).child();
  const parent = {
    type: "object",
    fieldPathExpression: this.field.fieldPathExpression,
    outputExpression: this.field.outputExpression,
    variableName: this.field.variableName,
    wildCardPath: this.field.wildCardPath
  };
  __privateGet(this, _node6).groups.forEach((group2) => __privateMethod(this, _ObjectNodeCompiler_instances, compileObjectGroup_fn).call(this, group2, buffer, parent));
  return buffer.toString();
}, /**
 * Compiles an object groups recursively
 */
compileObjectGroup_fn = function(group2, buffer, parent) {
  group2.conditions.forEach((condition, index) => {
    const guardBuffer = buffer.child();
    condition.schema.properties.forEach((child) => {
      __privateGet(this, _compiler5).compileNode(child, guardBuffer, parent);
    });
    condition.schema.groups.forEach((child) => {
      __privateMethod(this, _ObjectNodeCompiler_instances, compileObjectGroup_fn).call(this, child, guardBuffer, parent);
    });
    buffer.writeStatement(
      defineConditionalGuard({
        variableName: this.field.variableName,
        conditional: index === 0 ? "if" : "else if",
        conditionalFnRefId: condition.conditionalFnRefId,
        guardedCodeSnippet: guardBuffer.toString()
      })
    );
  });
  if (group2.elseConditionalFnRefId && group2.conditions.length) {
    buffer.writeStatement(
      defineElseCondition({
        variableName: this.field.variableName,
        conditionalFnRefId: group2.elseConditionalFnRefId
      })
    );
  }
}, _a8);
function createRootField(parent) {
  return {
    parentExpression: parent.variableName,
    parentValueExpression: parent.variableName,
    fieldNameExpression: `''`,
    fieldPathExpression: `''`,
    wildCardPath: "",
    variableName: `${parent.variableName}_item`,
    valueExpression: "root",
    outputExpression: parent.outputExpression,
    isArrayMember: false
  };
}
function defineFieldValueOutput({
  variableName,
  outputExpression,
  transformFnRefId
}) {
  const outputValueExpression = transformFnRefId ? `refs['${transformFnRefId}'](${variableName}.value, ${variableName})` : `${variableName}.value`;
  return `if (${variableName}.isDefined && ${variableName}.isValid) {
  ${outputExpression} = ${outputValueExpression};
}`;
}
var _node7, _buffer6, _a9;
var LiteralNodeCompiler = (_a9 = class extends BaseNode {
  constructor(node, buffer, compiler, parent, parentField) {
    super(node, compiler, parent, parentField);
    __privateAdd(this, _node7);
    __privateAdd(this, _buffer6);
    __privateSet(this, _node7, node);
    __privateSet(this, _buffer6, buffer);
  }
  compile() {
    this.defineField(__privateGet(this, _buffer6));
    __privateGet(this, _buffer6).writeStatement(
      defineFieldExistenceValidations({
        allowNull: __privateGet(this, _node7).allowNull,
        isOptional: __privateGet(this, _node7).isOptional,
        variableName: this.field.variableName
      })
    );
    __privateGet(this, _buffer6).writeStatement(
      defineFieldValidations({
        variableName: this.field.variableName,
        validations: __privateGet(this, _node7).validations,
        bail: __privateGet(this, _node7).bail,
        dropMissingCheck: false
      })
    );
    __privateGet(this, _buffer6).writeStatement(
      `${defineFieldValueOutput({
        variableName: this.field.variableName,
        outputExpression: this.field.outputExpression,
        transformFnRefId: __privateGet(this, _node7).transformFnId
      })}${__privateGet(this, _buffer6).newLine}${defineFieldNullOutput({
        variableName: this.field.variableName,
        allowNull: __privateGet(this, _node7).allowNull,
        outputExpression: this.field.outputExpression,
        transformFnRefId: __privateGet(this, _node7).transformFnId,
        conditional: "else if"
      })}`
    );
  }
}, _node7 = new WeakMap(), _buffer6 = new WeakMap(), _a9);
function createArrayField(parent) {
  const wildCardPath = parent.wildCardPath !== "" ? `${parent.wildCardPath}.*` : `*`;
  return {
    parentExpression: parent.variableName,
    parentValueExpression: `${parent.variableName}.value`,
    fieldNameExpression: `${parent.variableName}_i`,
    fieldPathExpression: wildCardPath,
    wildCardPath,
    variableName: `${parent.variableName}_item`,
    valueExpression: `${parent.variableName}.value[${parent.variableName}_i]`,
    outputExpression: `${parent.variableName}_out[${parent.variableName}_i]`,
    isArrayMember: true
  };
}
function createTupleField(node, parent) {
  const wildCardPath = parent.wildCardPath !== "" ? `${parent.wildCardPath}.${node.fieldName}` : node.fieldName;
  return {
    parentExpression: parent.variableName,
    parentValueExpression: `${parent.variableName}.value`,
    fieldNameExpression: `${node.fieldName}`,
    fieldPathExpression: wildCardPath,
    wildCardPath,
    variableName: `${parent.variableName}_item_${node.fieldName}`,
    valueExpression: `${parent.variableName}.value[${node.fieldName}]`,
    outputExpression: `${parent.variableName}_out[${node.propertyName}]`,
    isArrayMember: true
  };
}
function reportErrors() {
  return `if(errorReporter.hasErrors) {
  throw errorReporter.createError();
}`;
}
var NUMBER_CHAR_RE = /\d/;
var VALID_CHARS = /[A-Za-z0-9]+/;
function isUppercase(char = "") {
  if (NUMBER_CHAR_RE.test(char)) {
    return void 0;
  }
  return char !== char.toLowerCase();
}
function upperFirst(value) {
  return value ? value[0].toUpperCase() + value.slice(1) : "";
}
function lowerFirst(value) {
  return value ? value[0].toLowerCase() + value.slice(1) : "";
}
function splitByCase(value) {
  const parts = [];
  if (!value || typeof value !== "string") {
    return parts;
  }
  let buff = "";
  let previousUpper;
  let previousSplitter;
  for (const char of value) {
    const isSplitter = !VALID_CHARS.test(char);
    if (isSplitter === true) {
      parts.push(buff);
      buff = "";
      previousUpper = void 0;
      continue;
    }
    const isUpper = isUppercase(char);
    if (previousSplitter === false) {
      if (previousUpper === false && isUpper === true) {
        parts.push(buff);
        buff = char;
        previousUpper = isUpper;
        continue;
      }
      if (previousUpper === true && isUpper === false && buff.length > 1) {
        const lastChar = buff.at(-1);
        parts.push(buff.slice(0, Math.max(0, buff.length - 1)));
        buff = lastChar + char;
        previousUpper = isUpper;
        continue;
      }
    }
    buff += char;
    previousUpper = isUpper;
    previousSplitter = isSplitter;
  }
  parts.push(buff);
  return parts;
}
function toVariableName(value) {
  const pascalCase = splitByCase(value).map((p) => upperFirst(p.toLowerCase())).join("");
  return /^[0-9]+/.test(pascalCase) ? `var_${pascalCase}` : lowerFirst(pascalCase);
}
function createObjectField(node, variablesCounter, parent) {
  const wildCardPath = parent.wildCardPath !== "" ? `${parent.wildCardPath}.${node.fieldName}` : node.fieldName;
  return {
    parentExpression: parent.variableName,
    parentValueExpression: `${parent.variableName}.value`,
    fieldNameExpression: `'${node.fieldName}'`,
    fieldPathExpression: wildCardPath,
    wildCardPath,
    variableName: `${toVariableName(node.propertyName)}_${variablesCounter}`,
    valueExpression: `${parent.variableName}.value['${node.fieldName}']`,
    outputExpression: `${parent.variableName}_out['${node.propertyName}']`,
    isArrayMember: false
  };
}
function createRecordField(parent) {
  const wildCardPath = parent.wildCardPath !== "" ? `${parent.wildCardPath}.*` : `*`;
  return {
    parentExpression: parent.variableName,
    parentValueExpression: `${parent.variableName}.value`,
    fieldNameExpression: `${parent.variableName}_i`,
    fieldPathExpression: wildCardPath,
    wildCardPath,
    variableName: `${parent.variableName}_item`,
    valueExpression: `${parent.variableName}.value[${parent.variableName}_i]`,
    outputExpression: `${parent.variableName}_out[${parent.variableName}_i]`,
    isArrayMember: false
  };
}
function defineInlineFunctions(options) {
  return `function report(message, rule, field, args) {
  field.isValid = false;
  errorReporter.report(messagesProvider.getMessage(message, rule, field, args), rule, field, args);
};
function defineValue(value, field) {
  ${options.convertEmptyStringsToNull ? `if (value === '') { value = null; }` : ""}
  field.value = value;
  field.isDefined = value !== undefined && value !== null;
  return field;
};
function ensureExists(field) {
  if (field.value === undefined || field.value === null) {
    field.report(REQUIRED, 'required', field);
    return false;
  }
  return true;
};
function ensureIsDefined(field) {
  if (field.value === undefined) {
    field.report(REQUIRED, 'required', field);
    return false;
  }
  return true;
};
function ensureIsObject(field) {
  if (!field.isDefined) {
    return false;
  }
  if (typeof field.value == 'object' && !Array.isArray(field.value)) {
    return true;
  }
  field.report(NOT_AN_OBJECT, 'object', field);
  return false;
};
function ensureIsArray(field) {
  if (!field.isDefined) {
    return false;
  }
  if (Array.isArray(field.value)) {
    return true;
  }
  field.report(NOT_AN_ARRAY, 'array', field);
  return false;
};
function copyProperties(val) {
  let k, out, tmp;

  if (Array.isArray(val)) {
    out = Array((k = val.length))
    while (k--) out[k] = (tmp = val[k]) && typeof tmp == 'object' ? copyProperties(tmp) : tmp
    return out
  }

  if (Object.prototype.toString.call(val) === '[object Object]') {
    out = {} // null
    for (k in val) {
      out[k] = (tmp = val[k]) && typeof tmp == 'object' ? copyProperties(tmp) : tmp
    }
    return out
  }
  return val
};
function moveProperties(source, destination, ignoreKeys) {
  for (let key in source) {
    if (!ignoreKeys.includes(key)) {
      const value = source[key]
      destination[key] = copyProperties(value)
    }
  }
};`;
}
function defineInlineErrorMessages(messages2) {
  return `const REQUIRED = '${messages2.required}';
const NOT_AN_OBJECT = '${messages2.object}';
const NOT_AN_ARRAY = '${messages2.array}';`;
}
var AsyncFunction = Object.getPrototypeOf(async function() {
}).constructor;
var _rootNode, _options, _buffer7, _Compiler_instances, initiateJSOutput_fn, finishJSOutput_fn, compileNodes_fn, toAsyncFunction_fn, _a10;
var Compiler = (_a10 = class {
  constructor(rootNode, options) {
    __privateAdd(this, _Compiler_instances);
    /**
     * Variables counter is used to generate unique variable
     * names with a counter suffix.
     */
    __publicField(this, "variablesCounter", 0);
    /**
     * An array of nodes to process
     */
    __privateAdd(this, _rootNode);
    /**
     * Options to configure the compiler behavior
     */
    __privateAdd(this, _options);
    /**
     * Buffer for collection the JS output string
     */
    __privateAdd(this, _buffer7, new CompilerBuffer());
    __privateSet(this, _rootNode, rootNode);
    __privateSet(this, _options, options || { convertEmptyStringsToNull: false });
  }
  /**
   * Converts a node to a field. Optionally accepts a parent node to create
   * a field for a specific parent type.
   */
  createFieldFor(node, parent) {
    switch (parent.type) {
      case "array":
        return createArrayField(parent);
      case "root":
        return createRootField(parent);
      case "object":
        return createObjectField(node, this.variablesCounter, parent);
      case "tuple":
        return createTupleField(node, parent);
      case "record":
        return createRecordField(parent);
    }
  }
  /**
   * Compiles a given compiler node
   */
  compileNode(node, buffer, parent, parentField) {
    switch (node.type) {
      case "literal":
        return new LiteralNodeCompiler(node, buffer, this, parent, parentField).compile();
      case "array":
        return new ArrayNodeCompiler(node, buffer, this, parent, parentField).compile();
      case "record":
        return new RecordNodeCompiler(node, buffer, this, parent, parentField).compile();
      case "object":
        return new ObjectNodeCompiler(node, buffer, this, parent, parentField).compile();
      case "tuple":
        return new TupleNodeCompiler(node, buffer, this, parent, parentField).compile();
      case "union":
        return new UnionNodeCompiler(node, buffer, this, parent, parentField).compile();
    }
  }
  /**
   * Compile schema nodes to an async function
   */
  compile() {
    __privateMethod(this, _Compiler_instances, initiateJSOutput_fn).call(this);
    __privateMethod(this, _Compiler_instances, compileNodes_fn).call(this);
    __privateMethod(this, _Compiler_instances, finishJSOutput_fn).call(this);
    const outputFunction = __privateMethod(this, _Compiler_instances, toAsyncFunction_fn).call(this);
    this.variablesCounter = 0;
    __privateGet(this, _buffer7).flush();
    return outputFunction;
  }
}, _rootNode = new WeakMap(), _options = new WeakMap(), _buffer7 = new WeakMap(), _Compiler_instances = new WeakSet(), /**
 * Initiates the JS output
 */
initiateJSOutput_fn = function() {
  __privateGet(this, _buffer7).writeStatement(
    defineInlineErrorMessages({
      required: "value is required",
      object: "value is not a valid object",
      array: "value is not a valid array",
      ...__privateGet(this, _options).messages
    })
  );
  __privateGet(this, _buffer7).writeStatement(defineInlineFunctions(__privateGet(this, _options)));
  __privateGet(this, _buffer7).writeStatement("let out;");
}, /**
 * Finished the JS output
 */
finishJSOutput_fn = function() {
  __privateGet(this, _buffer7).writeStatement(reportErrors());
  __privateGet(this, _buffer7).writeStatement("return out;");
}, /**
 * Compiles all the nodes
 */
compileNodes_fn = function() {
  this.compileNode(__privateGet(this, _rootNode).schema, __privateGet(this, _buffer7), {
    type: "root",
    variableName: "root",
    outputExpression: "out",
    fieldPathExpression: "out",
    wildCardPath: ""
  });
}, /**
 * Returns compiled output as a function
 */
toAsyncFunction_fn = function() {
  return new AsyncFunction(
    "root",
    "meta",
    "refs",
    "messagesProvider",
    "errorReporter",
    __privateGet(this, _buffer7).toString()
  );
}, _a10);

// node_modules/@vinejs/compiler/build/index.js
function refsBuilder() {
  let counter = 0;
  const refs = {};
  return {
    toJSON() {
      return refs;
    },
    /**
     * Track a value inside refs
     */
    track(value) {
      counter++;
      const ref = `ref://${counter}`;
      refs[ref] = value;
      return ref;
    },
    /**
     * Track a validation inside refs
     */
    trackValidation(validation) {
      return this.track(validation);
    },
    /**
     * Track input value parser inside refs
     */
    trackParser(fn) {
      return this.track(fn);
    },
    /**
     * Track output value transformer inside refs
     */
    trackTransformer(fn) {
      return this.track(fn);
    },
    /**
     * Track a conditional inside refs
     */
    trackConditional(fn) {
      return this.track(fn);
    }
  };
}

// node_modules/@vinejs/vine/build/chunk-FED7BU4B.js
function createRule(validator, metaData) {
  const rule = {
    validator,
    isAsync: (metaData == null ? void 0 : metaData.isAsync) || validator.constructor.name === "AsyncFunction",
    implicit: (metaData == null ? void 0 : metaData.implicit) ?? false
  };
  return function(...options) {
    return {
      rule,
      options: options[0]
    };
  };
}
var symbols_exports = {};
__export(symbols_exports, {
  COTYPE: () => COTYPE,
  IS_OF_TYPE: () => IS_OF_TYPE,
  ITYPE: () => ITYPE,
  OTYPE: () => OTYPE,
  PARSE: () => PARSE,
  SUBTYPE: () => SUBTYPE,
  UNIQUE_NAME: () => UNIQUE_NAME,
  VALIDATION: () => VALIDATION
});
var UNIQUE_NAME = Symbol.for("schema_name");
var IS_OF_TYPE = Symbol.for("is_of_type");
var PARSE = Symbol.for("parse");
var ITYPE = Symbol.for("opaque_input_type");
var OTYPE = Symbol.for("opaque_type");
var COTYPE = Symbol.for("camelcase_opaque_type");
var VALIDATION = Symbol.for("to_validation");
var SUBTYPE = Symbol.for("subtype");
var requiredWhen = createRule(
  (_, checker, field) => {
    const shouldBeRequired = checker(field);
    if (!field.isDefined && shouldBeRequired) {
      field.report(messages.required, "required", field);
    }
  },
  {
    implicit: true
  }
);
var BaseModifiersType = class extends Macroable {
  /**
   * Mark the field under validation as optional. An optional
   * field allows both null and undefined values.
   */
  optional(validations) {
    return new OptionalModifier(this, validations);
  }
  /**
   * Mark the field under validation to be null. The null value will
   * be written to the output as well.
   *
   * If `optional` and `nullable` are used together, then both undefined
   * and null values will be allowed.
   */
  nullable() {
    return new NullableModifier(this);
  }
  /**
   * Apply transform on the final validated value. The transform method may
   * convert the value to any new datatype.
   */
  transform(transformer) {
    return new TransformModifier(transformer, this);
  }
};
var _parent2, _a11;
var NullableModifier = (_a11 = class extends BaseModifiersType {
  constructor(parent) {
    super();
    __privateAdd(this, _parent2);
    __privateSet(this, _parent2, parent);
  }
  /**
   * Creates a fresh instance of the underlying schema type
   * and wraps it inside the nullable modifier
   */
  clone() {
    return new _a11(__privateGet(this, _parent2).clone());
  }
  /**
   * Compiles to compiler node
   */
  [PARSE](propertyName, refs, options) {
    const output = __privateGet(this, _parent2)[PARSE](propertyName, refs, options);
    output.allowNull = true;
    return output;
  }
}, _parent2 = new WeakMap(), _a11);
var _parent3, _a12;
var OptionalModifier = (_a12 = class extends BaseModifiersType {
  constructor(parent, validations) {
    super();
    __privateAdd(this, _parent3);
    /**
     * Optional modifier validations list
     */
    __publicField(this, "validations");
    __privateSet(this, _parent3, parent);
    this.validations = validations || [];
  }
  /**
   * Shallow clones the validations. Since, there are no API's to mutate
   * the validation options, we can safely copy them by reference.
   */
  cloneValidations() {
    return this.validations.map((validation) => {
      return {
        options: validation.options,
        rule: validation.rule
      };
    });
  }
  /**
   * Compiles validations
   */
  compileValidations(refs) {
    return this.validations.map((validation) => {
      return {
        ruleFnId: refs.track({
          validator: validation.rule.validator,
          options: validation.options
        }),
        implicit: validation.rule.implicit,
        isAsync: validation.rule.isAsync
      };
    });
  }
  /**
   * Push a validation to the validations chain.
   */
  use(validation) {
    this.validations.push(VALIDATION in validation ? validation[VALIDATION]() : validation);
    return this;
  }
  requiredWhen(otherField, operator, expectedValue) {
    if (typeof otherField === "function") {
      return this.use(requiredWhen(otherField));
    }
    let checker;
    switch (operator) {
      case "=":
        checker = (value) => value === expectedValue;
        break;
      case "!=":
        checker = (value) => value !== expectedValue;
        break;
      case "in":
        checker = (value) => expectedValue.includes(value);
        break;
      case "notIn":
        checker = (value) => !expectedValue.includes(value);
        break;
      case ">":
        checker = (value) => value > expectedValue;
        break;
      case "<":
        checker = (value) => value < expectedValue;
        break;
      case ">=":
        checker = (value) => value >= expectedValue;
        break;
      case "<=":
        checker = (value) => value <= expectedValue;
    }
    return this.use(
      requiredWhen((field) => {
        const otherFieldValue = helpers.getNestedValue(otherField, field);
        return checker(otherFieldValue);
      })
    );
  }
  /**
   * Mark the field under validation as required when all
   * the other fields are present with value other
   * than `undefined` or `null`.
   */
  requiredIfExists(fields2) {
    const fieldsToExist = Array.isArray(fields2) ? fields2 : [fields2];
    return this.use(
      requiredWhen((field) => {
        return fieldsToExist.every(
          (otherField) => helpers.exists(helpers.getNestedValue(otherField, field))
        );
      })
    );
  }
  /**
   * Mark the field under validation as required when any
   * one of the other fields are present with non-nullable
   * value.
   */
  requiredIfAnyExists(fields2) {
    return this.use(
      requiredWhen((field) => {
        return fields2.some(
          (otherField) => helpers.exists(helpers.getNestedValue(otherField, field))
        );
      })
    );
  }
  /**
   * Mark the field under validation as required when all
   * the other fields are missing or their value is
   * `undefined` or `null`.
   */
  requiredIfMissing(fields2) {
    const fieldsToExist = Array.isArray(fields2) ? fields2 : [fields2];
    return this.use(
      requiredWhen((field) => {
        return fieldsToExist.every(
          (otherField) => helpers.isMissing(helpers.getNestedValue(otherField, field))
        );
      })
    );
  }
  /**
   * Mark the field under validation as required when any
   * one of the other fields are missing.
   */
  requiredIfAnyMissing(fields2) {
    return this.use(
      requiredWhen((field) => {
        return fields2.some(
          (otherField) => helpers.isMissing(helpers.getNestedValue(otherField, field))
        );
      })
    );
  }
  /**
   * Creates a fresh instance of the underlying schema type
   * and wraps it inside the optional modifier
   */
  clone() {
    return new _a12(__privateGet(this, _parent3).clone(), this.cloneValidations());
  }
  /**
   * Compiles to compiler node
   */
  [PARSE](propertyName, refs, options) {
    const output = __privateGet(this, _parent3)[PARSE](propertyName, refs, options);
    output.isOptional = true;
    output.validations = output.validations.concat(this.compileValidations(refs));
    return output;
  }
}, _parent3 = new WeakMap(), _a12);
var _parent4, _transform, _a13;
var TransformModifier = (_a13 = class extends BaseModifiersType {
  constructor(transform, parent) {
    super();
    __privateAdd(this, _parent4);
    __privateAdd(this, _transform);
    __privateSet(this, _transform, transform);
    __privateSet(this, _parent4, parent);
  }
  /**
   * Creates a fresh instance of the underlying schema type
   * and wraps it inside the transform modifier.
   */
  clone() {
    return new _a13(__privateGet(this, _transform), __privateGet(this, _parent4).clone());
  }
  /**
   * Compiles to compiler node
   */
  [PARSE](propertyName, refs, options) {
    const output = __privateGet(this, _parent4)[PARSE](propertyName, refs, options);
    output.transformFnId = refs.trackTransformer(__privateGet(this, _transform));
    return output;
  }
}, _parent4 = new WeakMap(), _transform = new WeakMap(), _a13);
var BaseLiteralType = class extends BaseModifiersType {
  constructor(options, validations) {
    super();
    /**
     * Field options
     */
    __publicField(this, "options");
    /**
     * Set of validations to run
     */
    __publicField(this, "validations");
    this.options = {
      bail: true,
      allowNull: false,
      isOptional: false,
      ...options
    };
    this.validations = validations || [];
  }
  /**
   * Shallow clones the validations. Since, there are no API's to mutate
   * the validation options, we can safely copy them by reference.
   */
  cloneValidations() {
    return this.validations.map((validation) => {
      return {
        options: validation.options,
        rule: validation.rule
      };
    });
  }
  /**
   * Shallow clones the options
   */
  cloneOptions() {
    return { ...this.options };
  }
  /**
   * Compiles validations
   */
  compileValidations(refs) {
    return this.validations.map((validation) => {
      return {
        ruleFnId: refs.track({
          validator: validation.rule.validator,
          options: validation.options
        }),
        implicit: validation.rule.implicit,
        isAsync: validation.rule.isAsync
      };
    });
  }
  /**
   * Define a method to parse the input value. The method
   * is invoked before any validation and hence you must
   * perform type-checking to know the value you are
   * working it.
   */
  parse(callback) {
    this.options.parse = callback;
    return this;
  }
  /**
   * Push a validation to the validations chain.
   */
  use(validation) {
    this.validations.push(VALIDATION in validation ? validation[VALIDATION]() : validation);
    return this;
  }
  /**
   * Enable/disable the bail mode. In bail mode, the field validations
   * are stopped after the first error.
   */
  bail(state) {
    this.options.bail = state;
    return this;
  }
  /**
   * Compiles the schema type to a compiler node
   */
  [PARSE](propertyName, refs, options) {
    return {
      type: "literal",
      subtype: this[SUBTYPE],
      fieldName: propertyName,
      propertyName: options.toCamelCase ? camelCase(propertyName) : propertyName,
      bail: this.options.bail,
      allowNull: this.options.allowNull,
      isOptional: this.options.isOptional,
      parseFnId: this.options.parse ? refs.trackParser(this.options.parse) : void 0,
      validations: this.compileValidations(refs)
    };
  }
};
var _a14, _b;
var VineAny = class _VineAny extends (_b = BaseLiteralType, _a14 = SUBTYPE, _b) {
  constructor(options, validations) {
    super(options, validations);
    /**
     * The subtype of the literal schema field
     */
    __publicField(this, _a14, "any");
  }
  /**
   * Clones the VineAny schema type. The applied options
   * and validations are copied to the new instance
   */
  clone() {
    return new _VineAny(this.cloneOptions(), this.cloneValidations());
  }
};
var enumRule = createRule((value, options, field) => {
  const choices = typeof options.choices === "function" ? options.choices(field) : options.choices;
  if (!choices.includes(value)) {
    field.report(messages.enum, "enum", field, { choices });
  }
});
var _a15, _b2, _c, _values;
var VineEnum = (_c = class extends (_b2 = BaseLiteralType, _a15 = SUBTYPE, _b2) {
  constructor(values, options, validations) {
    super(options, validations || [enumRule({ choices: values })]);
    __privateAdd(this, _values);
    /**
     * The subtype of the literal schema field
     */
    __publicField(this, _a15, "enum");
    __privateSet(this, _values, values);
  }
  /**
   * Returns the enum choices
   */
  getChoices() {
    return __privateGet(this, _values);
  }
  /**
   * Clones the VineEnum schema type. The applied options
   * and validations are copied to the new instance
   */
  clone() {
    return new _c(__privateGet(this, _values), this.cloneOptions(), this.cloneValidations());
  }
}, _values = new WeakMap(), /**
 * Default collection of enum rules
 */
__publicField(_c, "rules", {
  enum: enumRule
}), _c);
var DEFAULT_DATE_FORMATS = ["YYYY-MM-DD", "YYYY-MM-DD HH:mm:ss"];
import_dayjs2.default.extend(import_customParseFormat.default);
import_dayjs2.default.extend(import_isSameOrAfter.default);
import_dayjs2.default.extend(import_isSameOrBefore.default);
var dateRule = createRule((value, options, field) => {
  if (typeof value !== "string" && typeof value !== "number") {
    field.report(messages.date, "date", field);
    return;
  }
  let isTimestampAllowed = false;
  let isISOAllowed = false;
  let formats = options.formats || DEFAULT_DATE_FORMATS;
  if (Array.isArray(formats)) {
    formats = [...formats];
    isTimestampAllowed = formats.includes("x");
    isISOAllowed = formats.includes("iso8601");
  } else if (typeof formats !== "string") {
    formats = { ...formats };
    isTimestampAllowed = formats.format === "x";
    isISOAllowed = formats.format === "iso";
  }
  const valueAsNumber = isTimestampAllowed ? helpers.asNumber(value) : value;
  let dateTime;
  if (isTimestampAllowed && !Number.isNaN(valueAsNumber)) {
    dateTime = (0, import_dayjs2.default)(valueAsNumber);
  } else {
    dateTime = (0, import_dayjs2.default)(value, formats, true);
  }
  if (!dateTime.isValid() && isISOAllowed) {
    dateTime = (0, import_dayjs2.default)(value);
  }
  if (!dateTime.isValid()) {
    field.report(messages.date, "date", field);
    return;
  }
  field.meta.$value = dateTime;
  field.meta.$formats = formats;
  field.mutate(dateTime.toDate(), field);
});
var equalsRule = createRule((_, options, field) => {
  if (!field.meta.$value) {
    return;
  }
  const compare = options.compare || "day";
  const format = options.format || DEFAULT_DATE_FORMATS;
  const dateTime = field.meta.$value;
  const expectedValue = typeof options.expectedValue === "function" ? options.expectedValue(field) : options.expectedValue;
  const expectedDateTime = (0, import_dayjs2.default)(expectedValue, format, true);
  if (!expectedDateTime.isValid()) {
    throw new Error(`Invalid datetime value "${expectedValue}" provided to the equals rule`);
  }
  if (!dateTime.isSame(expectedDateTime, compare)) {
    field.report(messages["date.equals"], "date.equals", field, {
      expectedValue,
      compare
    });
  }
});
var afterRule = createRule((_, options, field) => {
  if (!field.meta.$value) {
    return;
  }
  const compare = options.compare || "day";
  const format = options.format || DEFAULT_DATE_FORMATS;
  const dateTime = field.meta.$value;
  const expectedValue = typeof options.expectedValue === "function" ? options.expectedValue(field) : options.expectedValue;
  const expectedDateTime = expectedValue === "today" ? (0, import_dayjs2.default)() : expectedValue === "tomorrow" ? (0, import_dayjs2.default)().add(1, "day") : (0, import_dayjs2.default)(expectedValue, format, true);
  if (!expectedDateTime.isValid()) {
    throw new Error(`Invalid datetime value "${expectedValue}" provided to the after rule`);
  }
  if (!dateTime.isAfter(expectedDateTime, compare)) {
    field.report(messages["date.after"], "date.after", field, {
      expectedValue,
      compare
    });
  }
});
var afterOrEqualRule = createRule((_, options, field) => {
  if (!field.meta.$value) {
    return;
  }
  const compare = options.compare || "day";
  const format = options.format || DEFAULT_DATE_FORMATS;
  const dateTime = field.meta.$value;
  const expectedValue = typeof options.expectedValue === "function" ? options.expectedValue(field) : options.expectedValue;
  const expectedDateTime = expectedValue === "today" ? (0, import_dayjs2.default)() : expectedValue === "tomorrow" ? (0, import_dayjs2.default)().add(1, "day") : (0, import_dayjs2.default)(expectedValue, format, true);
  if (!expectedDateTime.isValid()) {
    throw new Error(`Invalid datetime value "${expectedValue}" provided to the afterOrEqual rule`);
  }
  if (!dateTime.isSameOrAfter(expectedDateTime, compare)) {
    field.report(messages["date.afterOrEqual"], "date.afterOrEqual", field, {
      expectedValue,
      compare
    });
  }
});
var beforeRule = createRule((_, options, field) => {
  if (!field.meta.$value) {
    return;
  }
  const compare = options.compare || "day";
  const format = options.format || DEFAULT_DATE_FORMATS;
  const dateTime = field.meta.$value;
  const expectedValue = typeof options.expectedValue === "function" ? options.expectedValue(field) : options.expectedValue;
  const expectedDateTime = expectedValue === "today" ? (0, import_dayjs2.default)() : expectedValue === "yesterday" ? (0, import_dayjs2.default)().subtract(1, "day") : (0, import_dayjs2.default)(expectedValue, format, true);
  if (!expectedDateTime.isValid()) {
    throw new Error(`Invalid datetime value "${expectedValue}" provided to the before rule`);
  }
  if (!dateTime.isBefore(expectedDateTime, compare)) {
    field.report(messages["date.before"], "date.before", field, {
      expectedValue,
      compare
    });
  }
});
var beforeOrEqualRule = createRule((_, options, field) => {
  if (!field.meta.$value) {
    return;
  }
  const compare = options.compare || "day";
  const format = options.format || DEFAULT_DATE_FORMATS;
  const dateTime = field.meta.$value;
  const expectedValue = typeof options.expectedValue === "function" ? options.expectedValue(field) : options.expectedValue;
  const expectedDateTime = expectedValue === "today" ? (0, import_dayjs2.default)() : expectedValue === "yesterday" ? (0, import_dayjs2.default)().subtract(1, "day") : (0, import_dayjs2.default)(expectedValue, format, true);
  if (!expectedDateTime.isValid()) {
    throw new Error(`Invalid datetime value "${expectedValue}" provided to the beforeOrEqual rule`);
  }
  if (!dateTime.isSameOrBefore(expectedDateTime, compare)) {
    field.report(messages["date.beforeOrEqual"], "date.beforeOrEqual", field, {
      expectedValue,
      compare
    });
  }
});
var sameAsRule = createRule((_, options, field) => {
  if (!field.meta.$value) {
    return;
  }
  const compare = options.compare || "day";
  const dateTime = field.meta.$value;
  const format = options.format || field.meta.$formats;
  const expectedValue = helpers.getNestedValue(options.otherField, field);
  const expectedDateTime = (0, import_dayjs2.default)(expectedValue, format, true);
  if (!expectedDateTime.isValid()) {
    return;
  }
  if (!dateTime.isSame(expectedDateTime, compare)) {
    field.report(messages["date.sameAs"], "date.sameAs", field, {
      otherField: options.otherField,
      expectedValue,
      compare
    });
  }
});
var notSameAsRule = createRule((_, options, field) => {
  if (!field.meta.$value) {
    return;
  }
  const compare = options.compare || "day";
  const dateTime = field.meta.$value;
  const format = options.format || field.meta.$formats;
  const expectedValue = helpers.getNestedValue(options.otherField, field);
  const expectedDateTime = (0, import_dayjs2.default)(expectedValue, format, true);
  if (!expectedDateTime.isValid()) {
    return;
  }
  if (dateTime.isSame(expectedDateTime, compare)) {
    field.report(messages["date.notSameAs"], "date.notSameAs", field, {
      otherField: options.otherField,
      expectedValue,
      compare
    });
  }
});
var afterFieldRule = createRule((_, options, field) => {
  if (!field.meta.$value) {
    return;
  }
  const compare = options.compare || "day";
  const dateTime = field.meta.$value;
  const format = options.format || field.meta.$formats;
  const expectedValue = helpers.getNestedValue(options.otherField, field);
  const expectedDateTime = (0, import_dayjs2.default)(expectedValue, format, true);
  if (!expectedDateTime.isValid()) {
    return;
  }
  if (!dateTime.isAfter(expectedDateTime, compare)) {
    field.report(messages["date.afterField"], "date.afterField", field, {
      otherField: options.otherField,
      expectedValue,
      compare
    });
  }
});
var afterOrSameAsRule = createRule((_, options, field) => {
  if (!field.meta.$value) {
    return;
  }
  const compare = options.compare || "day";
  const dateTime = field.meta.$value;
  const format = options.format || field.meta.$formats;
  const expectedValue = helpers.getNestedValue(options.otherField, field);
  const expectedDateTime = (0, import_dayjs2.default)(expectedValue, format, true);
  if (!expectedDateTime.isValid()) {
    return;
  }
  if (!dateTime.isSameOrAfter(expectedDateTime, compare)) {
    field.report(messages["date.afterOrSameAs"], "date.afterOrSameAs", field, {
      otherField: options.otherField,
      expectedValue,
      compare
    });
  }
});
var beforeFieldRule = createRule((_, options, field) => {
  if (!field.meta.$value) {
    return;
  }
  const compare = options.compare || "day";
  const dateTime = field.meta.$value;
  const format = options.format || field.meta.$formats;
  const expectedValue = helpers.getNestedValue(options.otherField, field);
  const expectedDateTime = (0, import_dayjs2.default)(expectedValue, format, true);
  if (!expectedDateTime.isValid()) {
    return;
  }
  if (!dateTime.isBefore(expectedDateTime, compare)) {
    field.report(messages["date.beforeField"], "date.beforeField", field, {
      otherField: options.otherField,
      expectedValue,
      compare
    });
  }
});
var beforeOrSameAsRule = createRule((_, options, field) => {
  if (!field.meta.$value) {
    return;
  }
  const compare = options.compare || "day";
  const dateTime = field.meta.$value;
  const format = options.format || field.meta.$formats;
  const expectedValue = helpers.getNestedValue(options.otherField, field);
  const expectedDateTime = (0, import_dayjs2.default)(expectedValue, format, true);
  if (!expectedDateTime.isValid()) {
    return;
  }
  if (!dateTime.isSameOrBefore(expectedDateTime, compare)) {
    field.report(messages["date.beforeOrSameAs"], "date.beforeOrSameAs", field, {
      otherField: options.otherField,
      expectedValue,
      compare
    });
  }
});
var weekendRule = createRule((_, __, field) => {
  if (!field.meta.$value) {
    return;
  }
  const dateTime = field.meta.$value;
  const day = dateTime.day();
  if (day !== 0 && day !== 6) {
    field.report(messages["date.weekend"], "date.weekend", field);
  }
});
var weekdayRule = createRule((_, __, field) => {
  if (!field.meta.$value) {
    return;
  }
  const dateTime = field.meta.$value;
  const day = dateTime.day();
  if (day === 0 || day === 6) {
    field.report(messages["date.weekday"], "date.weekday", field);
  }
});
var _a16, _b3, _c2, _d, _e;
var VineDate = (_e = class extends (_d = BaseLiteralType, _c2 = UNIQUE_NAME, _b3 = SUBTYPE, _a16 = IS_OF_TYPE, _d) {
  constructor(options, validations) {
    super(options, validations || [dateRule(options || {})]);
    /**
     * The property must be implemented for "unionOfTypes"
     */
    __publicField(this, _c2, "vine.date");
    /**
     * The subtype of the literal schema field
     */
    __publicField(this, _b3, "date");
    /**
     * Checks if the value is of date type. The method must be
     * implemented for "unionOfTypes"
     */
    __publicField(this, _a16, (value) => {
      if (typeof value !== "string") {
        return false;
      }
      return (0, import_dayjs.default)(value, this.options.formats || DEFAULT_DATE_FORMATS, true).isValid();
    });
  }
  /**
   * The equals rule compares the input value to be same
   * as the expected value.
   *
   * By default, the comparions of day, month and years are performed.
   */
  equals(expectedValue, options) {
    return this.use(equalsRule({ expectedValue, ...options }));
  }
  /**
   * The after rule compares the input value to be after
   * the expected value.
   *
   * By default, the comparions of day, month and years are performed.
   */
  after(expectedValue, options) {
    return this.use(afterRule({ expectedValue, ...options }));
  }
  /**
   * The after or equal rule compares the input value to be
   * after or equal to the expected value.
   *
   * By default, the comparions of day, month and years are performed.
   */
  afterOrEqual(expectedValue, options) {
    return this.use(afterOrEqualRule({ expectedValue, ...options }));
  }
  /**
   * The before rule compares the input value to be before
   * the expected value.
   *
   * By default, the comparions of day, month and years are performed.
   */
  before(expectedValue, options) {
    return this.use(beforeRule({ expectedValue, ...options }));
  }
  /**
   * The before rule compares the input value to be before
   * the expected value.
   *
   * By default, the comparions of day, month and years are performed.
   */
  beforeOrEqual(expectedValue, options) {
    return this.use(beforeOrEqualRule({ expectedValue, ...options }));
  }
  /**
   * The sameAs rule expects the input value to be same
   * as the value of the other field.
   *
   * By default, the comparions of day, month and years are performed
   */
  sameAs(otherField, options) {
    return this.use(sameAsRule({ otherField, ...options }));
  }
  /**
   * The notSameAs rule expects the input value to be different
   * from the other field's value
   *
   * By default, the comparions of day, month and years are performed
   */
  notSameAs(otherField, options) {
    return this.use(notSameAsRule({ otherField, ...options }));
  }
  /**
   * The afterField rule expects the input value to be after
   * the other field's value.
   *
   * By default, the comparions of day, month and years are performed
   */
  afterField(otherField, options) {
    return this.use(afterFieldRule({ otherField, ...options }));
  }
  /**
   * The afterOrSameAs rule expects the input value to be after
   * or equal to the other field's value.
   *
   * By default, the comparions of day, month and years are performed
   */
  afterOrSameAs(otherField, options) {
    return this.use(afterOrSameAsRule({ otherField, ...options }));
  }
  /**
   * The beforeField rule expects the input value to be before
   * the other field's value.
   *
   * By default, the comparions of day, month and years are performed
   */
  beforeField(otherField, options) {
    return this.use(beforeFieldRule({ otherField, ...options }));
  }
  /**
   * The beforeOrSameAs rule expects the input value to be before
   * or same as the other field's value.
   *
   * By default, the comparions of day, month and years are performed
   */
  beforeOrSameAs(otherField, options) {
    return this.use(beforeOrSameAsRule({ otherField, ...options }));
  }
  /**
   * The weekend rule ensures the date falls on a weekend
   */
  weekend() {
    return this.use(weekendRule());
  }
  /**
   * The weekday rule ensures the date falls on a weekday
   */
  weekday() {
    return this.use(weekdayRule());
  }
  /**
   * Clones the VineDate schema type. The applied options
   * and validations are copied to the new instance
   */
  clone() {
    return new _e(this.cloneOptions(), this.cloneValidations());
  }
}, /**
 * Available VineDate rules
 */
__publicField(_e, "rules", {
  equals: equalsRule,
  after: afterRule,
  afterOrEqual: afterOrEqualRule,
  before: beforeRule,
  beforeOrEqual: beforeOrEqualRule,
  sameAs: sameAsRule,
  notSameAs: notSameAsRule,
  afterField: afterFieldRule,
  afterOrSameAs: afterOrSameAsRule,
  beforeField: beforeFieldRule,
  beforeOrSameAs: beforeOrSameAsRule,
  weekend: weekendRule,
  weekday: weekdayRule
}), _e);
var _conditionals, _otherwiseCallback, _a17;
var VineUnion = (_a17 = class {
  constructor(conditionals) {
    __privateAdd(this, _conditionals);
    __privateAdd(this, _otherwiseCallback, (_, field) => {
      field.report(messages.union, "union", field);
    });
    __privateSet(this, _conditionals, conditionals);
  }
  /**
   * Define a fallback method to invoke when all of the union conditions
   * fail. You may use this method to report an error.
   */
  otherwise(callback) {
    __privateSet(this, _otherwiseCallback, callback);
    return this;
  }
  /**
   * Clones the VineUnion schema type.
   */
  clone() {
    const cloned = new _a17(__privateGet(this, _conditionals));
    cloned.otherwise(__privateGet(this, _otherwiseCallback));
    return cloned;
  }
  /**
   * Compiles to a union
   */
  [PARSE](propertyName, refs, options) {
    return {
      type: "union",
      fieldName: propertyName,
      propertyName: options.toCamelCase ? camelCase(propertyName) : propertyName,
      elseConditionalFnRefId: refs.trackConditional(__privateGet(this, _otherwiseCallback)),
      conditions: __privateGet(this, _conditionals).map(
        (conditional) => conditional[PARSE](propertyName, refs, options)
      )
    };
  }
}, _conditionals = new WeakMap(), _otherwiseCallback = new WeakMap(), _a17);
var _schema, _conditional, _a18;
var UnionConditional = (_a18 = class {
  constructor(conditional, schema) {
    /**
     * Properties to merge when conditonal is true
     */
    __privateAdd(this, _schema);
    /**
     * Conditional to evaluate
     */
    __privateAdd(this, _conditional);
    __privateSet(this, _schema, schema);
    __privateSet(this, _conditional, conditional);
  }
  /**
   * Compiles to a union conditional
   */
  [PARSE](propertyName, refs, options) {
    return {
      conditionalFnRefId: refs.trackConditional(__privateGet(this, _conditional)),
      schema: __privateGet(this, _schema)[PARSE](propertyName, refs, options)
    };
  }
}, _schema = new WeakMap(), _conditional = new WeakMap(), _a18);
function union(conditionals) {
  return new VineUnion(conditionals);
}
union.if = function unionIf(conditon, schema) {
  return new UnionConditional(conditon, schema);
};
union.else = function unionElse(schema) {
  return new UnionConditional(() => true, schema);
};
var BaseModifiersType2 = class extends Macroable {
  /**
   * Mark the field under validation as optional. An optional
   * field allows both null and undefined values.
   */
  optional() {
    return new OptionalModifier2(this);
  }
  /**
   * Mark the field under validation to be null. The null value will
   * be written to the output as well.
   *
   * If `optional` and `nullable` are used together, then both undefined
   * and null values will be allowed.
   */
  nullable() {
    return new NullableModifier2(this);
  }
};
var _parent5, _a19;
var NullableModifier2 = (_a19 = class extends BaseModifiersType2 {
  constructor(parent) {
    super();
    __privateAdd(this, _parent5);
    __privateSet(this, _parent5, parent);
  }
  /**
   * Creates a fresh instance of the underlying schema type
   * and wraps it inside the nullable modifier
   */
  clone() {
    return new _a19(__privateGet(this, _parent5).clone());
  }
  /**
   * Compiles to compiler node
   */
  [PARSE](propertyName, refs, options) {
    const output = __privateGet(this, _parent5)[PARSE](propertyName, refs, options);
    if (output.type !== "union") {
      output.allowNull = true;
    }
    return output;
  }
}, _parent5 = new WeakMap(), _a19);
var _parent6, _a20;
var OptionalModifier2 = (_a20 = class extends BaseModifiersType2 {
  constructor(parent, validations) {
    super();
    __privateAdd(this, _parent6);
    /**
     * Optional modifier validations list
     */
    __publicField(this, "validations");
    __privateSet(this, _parent6, parent);
    this.validations = validations || [];
  }
  /**
   * Shallow clones the validations. Since, there are no API's to mutate
   * the validation options, we can safely copy them by reference.
   */
  cloneValidations() {
    return this.validations.map((validation) => {
      return {
        options: validation.options,
        rule: validation.rule
      };
    });
  }
  /**
   * Compiles validations
   */
  compileValidations(refs) {
    return this.validations.map((validation) => {
      return {
        ruleFnId: refs.track({
          validator: validation.rule.validator,
          options: validation.options
        }),
        implicit: validation.rule.implicit,
        isAsync: validation.rule.isAsync
      };
    });
  }
  /**
   * Push a validation to the validations chain.
   */
  use(validation) {
    this.validations.push(VALIDATION in validation ? validation[VALIDATION]() : validation);
    return this;
  }
  requiredWhen(otherField, operator, expectedValue) {
    if (typeof otherField === "function") {
      return this.use(requiredWhen(otherField));
    }
    let checker;
    switch (operator) {
      case "=":
        checker = (value) => value === expectedValue;
        break;
      case "!=":
        checker = (value) => value !== expectedValue;
        break;
      case "in":
        checker = (value) => expectedValue.includes(value);
        break;
      case "notIn":
        checker = (value) => !expectedValue.includes(value);
        break;
      case ">":
        checker = (value) => value > expectedValue;
        break;
      case "<":
        checker = (value) => value < expectedValue;
        break;
      case ">=":
        checker = (value) => value >= expectedValue;
        break;
      case "<=":
        checker = (value) => value <= expectedValue;
    }
    return this.use(
      requiredWhen((field) => {
        const otherFieldValue = helpers.getNestedValue(otherField, field);
        return checker(otherFieldValue);
      })
    );
  }
  /**
   * Mark the field under validation as required when all
   * the other fields are present with value other
   * than `undefined` or `null`.
   */
  requiredIfExists(fields2) {
    const fieldsToExist = Array.isArray(fields2) ? fields2 : [fields2];
    return this.use(
      requiredWhen((field) => {
        return fieldsToExist.every((otherField) => {
          return helpers.exists(helpers.getNestedValue(otherField, field));
        });
      })
    );
  }
  /**
   * Mark the field under validation as required when any
   * one of the other fields are present with non-nullable
   * value.
   */
  requiredIfAnyExists(fields2) {
    return this.use(
      requiredWhen((field) => {
        return fields2.some(
          (otherField) => helpers.exists(helpers.getNestedValue(otherField, field))
        );
      })
    );
  }
  /**
   * Mark the field under validation as required when all
   * the other fields are missing or their value is
   * `undefined` or `null`.
   */
  requiredIfMissing(fields2) {
    const fieldsToExist = Array.isArray(fields2) ? fields2 : [fields2];
    return this.use(
      requiredWhen((field) => {
        return fieldsToExist.every(
          (otherField) => helpers.isMissing(helpers.getNestedValue(otherField, field))
        );
      })
    );
  }
  /**
   * Mark the field under validation as required when any
   * one of the other fields are missing.
   */
  requiredIfAnyMissing(fields2) {
    return this.use(
      requiredWhen((field) => {
        return fields2.some(
          (otherField) => helpers.isMissing(helpers.getNestedValue(otherField, field))
        );
      })
    );
  }
  /**
   * Creates a fresh instance of the underlying schema type
   * and wraps it inside the optional modifier
   */
  clone() {
    return new _a20(__privateGet(this, _parent6).clone(), this.cloneValidations());
  }
  /**
   * Compiles to compiler node
   */
  [PARSE](propertyName, refs, options) {
    const output = __privateGet(this, _parent6)[PARSE](propertyName, refs, options);
    if (output.type !== "union") {
      output.isOptional = true;
      output.validations = output.validations.concat(this.compileValidations(refs));
    }
    return output;
  }
}, _parent6 = new WeakMap(), _a20);
var BaseType = class extends BaseModifiersType2 {
  constructor(options, validations) {
    super();
    /**
     * Field options
     */
    __publicField(this, "options");
    /**
     * Set of validations to run
     */
    __publicField(this, "validations");
    this.options = options || {
      bail: true,
      allowNull: false,
      isOptional: false
    };
    this.validations = validations || [];
  }
  /**
   * Shallow clones the validations. Since, there are no API's to mutate
   * the validation options, we can safely copy them by reference.
   */
  cloneValidations() {
    return this.validations.map((validation) => {
      return {
        options: validation.options,
        rule: validation.rule
      };
    });
  }
  /**
   * Shallow clones the options
   */
  cloneOptions() {
    return { ...this.options };
  }
  /**
   * Compiles validations
   */
  compileValidations(refs) {
    return this.validations.map((validation) => {
      return {
        ruleFnId: refs.track({
          validator: validation.rule.validator,
          options: validation.options
        }),
        implicit: validation.rule.implicit,
        isAsync: validation.rule.isAsync
      };
    });
  }
  /**
   * Define a method to parse the input value. The method
   * is invoked before any validation and hence you must
   * perform type-checking to know the value you are
   * working it.
   */
  parse(callback) {
    this.options.parse = callback;
    return this;
  }
  /**
   * Push a validation to the validations chain.
   */
  use(validation) {
    this.validations.push(VALIDATION in validation ? validation[VALIDATION]() : validation);
    return this;
  }
  /**
   * Enable/disable the bail mode. In bail mode, the field validations
   * are stopped after the first error.
   */
  bail(state) {
    this.options.bail = state;
    return this;
  }
};
var _a21, _b4, _schemas, _allowUnknownProperties, _c3;
var VineTuple = (_c3 = class extends BaseType {
  constructor(schemas, options, validations) {
    super(options, validations);
    __privateAdd(this, _schemas);
    /**
     * Whether or not to allow unknown properties
     */
    __privateAdd(this, _allowUnknownProperties, false);
    /**
     * The property must be implemented for "unionOfTypes"
     */
    __publicField(this, _b4, "vine.array");
    /**
     * Checks if the value is of array type. The method must be
     * implemented for "unionOfTypes"
     */
    __publicField(this, _a21, (value) => {
      return Array.isArray(value);
    });
    __privateSet(this, _schemas, schemas);
  }
  /**
   * Copy unknown properties to the final output.
   */
  allowUnknownProperties() {
    __privateSet(this, _allowUnknownProperties, true);
    return this;
  }
  /**
   * Clone object
   */
  clone() {
    const cloned = new _c3(
      __privateGet(this, _schemas).map((schema) => schema.clone()),
      this.cloneOptions(),
      this.cloneValidations()
    );
    if (__privateGet(this, _allowUnknownProperties)) {
      cloned.allowUnknownProperties();
    }
    return cloned;
  }
  /**
   * Compiles to array data type
   */
  [(_b4 = UNIQUE_NAME, _a21 = IS_OF_TYPE, PARSE)](propertyName, refs, options) {
    return {
      type: "tuple",
      fieldName: propertyName,
      propertyName: options.toCamelCase ? camelCase(propertyName) : propertyName,
      bail: this.options.bail,
      allowNull: this.options.allowNull,
      isOptional: this.options.isOptional,
      allowUnknownProperties: __privateGet(this, _allowUnknownProperties),
      parseFnId: this.options.parse ? refs.trackParser(this.options.parse) : void 0,
      validations: this.compileValidations(refs),
      properties: __privateGet(this, _schemas).map((schema, index) => schema[PARSE](String(index), refs, options))
    };
  }
}, _schemas = new WeakMap(), _allowUnknownProperties = new WeakMap(), _c3);
var minLengthRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (value.length < options.min) {
    field.report(messages["array.minLength"], "array.minLength", field, options);
  }
});
var maxLengthRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (value.length > options.max) {
    field.report(messages["array.maxLength"], "array.maxLength", field, options);
  }
});
var fixedLengthRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (value.length !== options.size) {
    field.report(messages["array.fixedLength"], "array.fixedLength", field, options);
  }
});
var notEmptyRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  if (value.length <= 0) {
    field.report(messages.notEmpty, "notEmpty", field);
  }
});
var distinctRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (!helpers.isDistinct(value, options.fields)) {
    field.report(messages.distinct, "distinct", field, options);
  }
});
var compactRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  field.mutate(
    value.filter((item) => helpers.exists(item) && item !== ""),
    field
  );
});
var _a22, _b5, _c4, _schema2;
var VineArray = (_c4 = class extends BaseType {
  constructor(schema, options, validations) {
    super(options, validations);
    __privateAdd(this, _schema2);
    /**
     * The property must be implemented for "unionOfTypes"
     */
    __publicField(this, _b5, "vine.array");
    /**
     * Checks if the value is of array type. The method must be
     * implemented for "unionOfTypes"
     */
    __publicField(this, _a22, (value) => {
      return Array.isArray(value);
    });
    __privateSet(this, _schema2, schema);
  }
  /**
   * Enforce a minimum length on an array field
   */
  minLength(expectedLength) {
    return this.use(minLengthRule({ min: expectedLength }));
  }
  /**
   * Enforce a maximum length on an array field
   */
  maxLength(expectedLength) {
    return this.use(maxLengthRule({ max: expectedLength }));
  }
  /**
   * Enforce a fixed length on an array field
   */
  fixedLength(expectedLength) {
    return this.use(fixedLengthRule({ size: expectedLength }));
  }
  /**
   * Ensure the array is not empty
   */
  notEmpty() {
    return this.use(notEmptyRule());
  }
  /**
   * Ensure array elements are distinct/unique
   */
  distinct(fields2) {
    return this.use(distinctRule({ fields: fields2 }));
  }
  /**
   * Removes empty strings, null and undefined values from the array
   */
  compact() {
    return this.use(compactRule());
  }
  /**
   * Clones the VineArray schema type. The applied options
   * and validations are copied to the new instance
   */
  clone() {
    return new _c4(__privateGet(this, _schema2).clone(), this.cloneOptions(), this.cloneValidations());
  }
  /**
   * Compiles to array data type
   */
  [(_b5 = UNIQUE_NAME, _a22 = IS_OF_TYPE, PARSE)](propertyName, refs, options) {
    return {
      type: "array",
      fieldName: propertyName,
      propertyName: options.toCamelCase ? camelCase(propertyName) : propertyName,
      bail: this.options.bail,
      allowNull: this.options.allowNull,
      isOptional: this.options.isOptional,
      each: __privateGet(this, _schema2)[PARSE]("*", refs, options),
      parseFnId: this.options.parse ? refs.trackParser(this.options.parse) : void 0,
      validations: this.compileValidations(refs)
    };
  }
}, _schema2 = new WeakMap(), /**
 * Default collection of array rules
 */
__publicField(_c4, "rules", {
  compact: compactRule,
  notEmpty: notEmptyRule,
  distinct: distinctRule,
  minLength: minLengthRule,
  maxLength: maxLengthRule,
  fixedLength: fixedLengthRule
}), _c4);
var _a23, _b6, _schema3, _c5;
var VineCamelCaseObject = (_c5 = class extends BaseModifiersType2 {
  constructor(schema) {
    super();
    __privateAdd(this, _schema3);
    /**
     * The property must be implemented for "unionOfTypes"
     */
    __publicField(this, _b6, "types.object");
    /**
     * Checks if the value is of object type. The method must be
     * implemented for "unionOfTypes"
     */
    __publicField(this, _a23, (value) => {
      return value !== null && typeof value === "object" && !Array.isArray(value);
    });
    __privateSet(this, _schema3, schema);
  }
  /**
   * Clone object
   */
  clone() {
    return new _c5(__privateGet(this, _schema3).clone());
  }
  /**
   * Compiles the schema type to a compiler node
   */
  [(_b6 = UNIQUE_NAME, _a23 = IS_OF_TYPE, PARSE)](propertyName, refs, options) {
    options.toCamelCase = true;
    return __privateGet(this, _schema3)[PARSE](propertyName, refs, options);
  }
}, _schema3 = new WeakMap(), _c5);
var _a24, _b7, _properties, _groups, _allowUnknownProperties2, _c6;
var VineObject = (_c6 = class extends BaseType {
  constructor(properties, options, validations) {
    if (!properties) {
      throw new Error(
        'Missing properties for "vine.object". Use an empty object if you do not want to validate any specific fields'
      );
    }
    super(options, validations);
    /**
     * Object properties
     */
    __privateAdd(this, _properties);
    /**
     * Object groups to merge based on conditionals
     */
    __privateAdd(this, _groups, []);
    /**
     * Whether or not to allow unknown properties
     */
    __privateAdd(this, _allowUnknownProperties2, false);
    /**
     * The property must be implemented for "unionOfTypes"
     */
    __publicField(this, _b7, "vine.object");
    /**
     * Checks if the value is of object type. The method must be
     * implemented for "unionOfTypes"
     */
    __publicField(this, _a24, (value) => {
      return value !== null && typeof value === "object" && !Array.isArray(value);
    });
    __privateSet(this, _properties, properties);
  }
  /**
   * Returns a clone copy of the object properties. The object groups
   * are not copied to keep the implementations simple and easy to
   * reason about.
   */
  getProperties() {
    return Object.keys(__privateGet(this, _properties)).reduce((result, key) => {
      result[key] = __privateGet(this, _properties)[key].clone();
      return result;
    }, {});
  }
  /**
   * Copy unknown properties to the final output.
   */
  allowUnknownProperties() {
    __privateSet(this, _allowUnknownProperties2, true);
    return this;
  }
  /**
   * Merge a union to the object groups. The union can be a "vine.union"
   * with objects, or a "vine.object.union" with properties.
   */
  merge(group2) {
    __privateGet(this, _groups).push(group2);
    return this;
  }
  /**
   * Clone object
   */
  clone() {
    const cloned = new _c6(
      this.getProperties(),
      this.cloneOptions(),
      this.cloneValidations()
    );
    __privateGet(this, _groups).forEach((group2) => cloned.merge(group2));
    if (__privateGet(this, _allowUnknownProperties2)) {
      cloned.allowUnknownProperties();
    }
    return cloned;
  }
  /**
   * Applies camelcase transform
   */
  toCamelCase() {
    return new VineCamelCaseObject(this);
  }
  /**
   * Compiles the schema type to a compiler node
   */
  [(_b7 = UNIQUE_NAME, _a24 = IS_OF_TYPE, PARSE)](propertyName, refs, options) {
    return {
      type: "object",
      fieldName: propertyName,
      propertyName: options.toCamelCase ? camelCase(propertyName) : propertyName,
      bail: this.options.bail,
      allowNull: this.options.allowNull,
      isOptional: this.options.isOptional,
      parseFnId: this.options.parse ? refs.trackParser(this.options.parse) : void 0,
      allowUnknownProperties: __privateGet(this, _allowUnknownProperties2),
      validations: this.compileValidations(refs),
      properties: Object.keys(__privateGet(this, _properties)).map((property) => {
        return __privateGet(this, _properties)[property][PARSE](property, refs, options);
      }),
      groups: __privateGet(this, _groups).map((group2) => {
        return group2[PARSE](refs, options);
      })
    };
  }
}, _properties = new WeakMap(), _groups = new WeakMap(), _allowUnknownProperties2 = new WeakMap(), _c6);
var minLengthRule2 = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (Object.keys(value).length < options.min) {
    field.report(messages["record.minLength"], "record.minLength", field, options);
  }
});
var maxLengthRule2 = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (Object.keys(value).length > options.max) {
    field.report(messages["record.maxLength"], "record.maxLength", field, options);
  }
});
var fixedLengthRule2 = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (Object.keys(value).length !== options.size) {
    field.report(messages["record.fixedLength"], "record.fixedLength", field, options);
  }
});
var validateKeysRule = createRule(
  (value, callback, field) => {
    if (!field.isValid) {
      return;
    }
    callback(Object.keys(value), field);
  }
);
var _a25, _b8, _c7, _schema4;
var VineRecord = (_c7 = class extends BaseType {
  constructor(schema, options, validations) {
    super(options, validations);
    __privateAdd(this, _schema4);
    /**
     * The property must be implemented for "unionOfTypes"
     */
    __publicField(this, _b8, "vine.object");
    /**
     * Checks if the value is of object type. The method must be
     * implemented for "unionOfTypes"
     */
    __publicField(this, _a25, (value) => {
      return value !== null && typeof value === "object" && !Array.isArray(value);
    });
    __privateSet(this, _schema4, schema);
  }
  /**
   * Enforce a minimum length on an object field
   */
  minLength(expectedLength) {
    return this.use(minLengthRule2({ min: expectedLength }));
  }
  /**
   * Enforce a maximum length on an object field
   */
  maxLength(expectedLength) {
    return this.use(maxLengthRule2({ max: expectedLength }));
  }
  /**
   * Enforce a fixed length on an object field
   */
  fixedLength(expectedLength) {
    return this.use(fixedLengthRule2({ size: expectedLength }));
  }
  /**
   * Register a callback to validate the object keys
   */
  validateKeys(...args) {
    return this.use(validateKeysRule(...args));
  }
  /**
   * Clones the VineRecord schema type. The applied options
   * and validations are copied to the new instance
   */
  clone() {
    return new _c7(
      __privateGet(this, _schema4).clone(),
      this.cloneOptions(),
      this.cloneValidations()
    );
  }
  /**
   * Compiles to record data type
   */
  [(_b8 = UNIQUE_NAME, _a25 = IS_OF_TYPE, PARSE)](propertyName, refs, options) {
    return {
      type: "record",
      fieldName: propertyName,
      propertyName: options.toCamelCase ? camelCase(propertyName) : propertyName,
      bail: this.options.bail,
      allowNull: this.options.allowNull,
      isOptional: this.options.isOptional,
      each: __privateGet(this, _schema4)[PARSE]("*", refs, options),
      parseFnId: this.options.parse ? refs.trackParser(this.options.parse) : void 0,
      validations: this.compileValidations(refs)
    };
  }
}, _schema4 = new WeakMap(), /**
 * Default collection of record rules
 */
__publicField(_c7, "rules", {
  maxLength: maxLengthRule2,
  minLength: minLengthRule2,
  fixedLength: fixedLengthRule2,
  validateKeys: validateKeysRule
}), _c7);
var stringRule = createRule((value, _, field) => {
  if (typeof value !== "string") {
    field.report(messages.string, "string", field);
  }
});
var emailRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (!helpers.isEmail(value, options)) {
    field.report(messages.email, "email", field);
  }
});
var mobileRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  const normalizedOptions = options && typeof options === "function" ? options(field) : options;
  const locales = (normalizedOptions == null ? void 0 : normalizedOptions.locale) || "any";
  if (!helpers.isMobilePhone(value, locales, normalizedOptions)) {
    field.report(messages.mobile, "mobile", field);
  }
});
var ipAddressRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (!helpers.isIP(value, options == null ? void 0 : options.version)) {
    field.report(messages.ipAddress, "ipAddress", field);
  }
});
var regexRule = createRule((value, expression, field) => {
  if (!field.isValid) {
    return;
  }
  if (!expression.test(value)) {
    field.report(messages.regex, "regex", field);
  }
});
var hexCodeRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  if (!helpers.isHexColor(value)) {
    field.report(messages.hexCode, "hexCode", field);
  }
});
var urlRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (!helpers.isURL(value, options)) {
    field.report(messages.url, "url", field);
  }
});
var activeUrlRule = createRule(async (value, _, field) => {
  if (!field.isValid) {
    return;
  }
  if (!await helpers.isActiveURL(value)) {
    field.report(messages.activeUrl, "activeUrl", field);
  }
});
var alphaRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  let characterSet = "a-zA-Z";
  if (options) {
    if (options.allowSpaces) {
      characterSet += "\\s";
    }
    if (options.allowDashes) {
      characterSet += "-";
    }
    if (options.allowUnderscores) {
      characterSet += "_";
    }
  }
  const expression = new RegExp(`^[${characterSet}]+$`);
  if (!expression.test(value)) {
    field.report(messages.alpha, "alpha", field);
  }
});
var alphaNumericRule = createRule(
  (value, options, field) => {
    if (!field.isValid) {
      return;
    }
    let characterSet = "a-zA-Z0-9";
    if (options) {
      if (options.allowSpaces) {
        characterSet += "\\s";
      }
      if (options.allowDashes) {
        characterSet += "-";
      }
      if (options.allowUnderscores) {
        characterSet += "_";
      }
    }
    const expression = new RegExp(`^[${characterSet}]+$`);
    if (!expression.test(value)) {
      field.report(messages.alphaNumeric, "alphaNumeric", field);
    }
  }
);
var minLengthRule3 = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (value.length < options.min) {
    field.report(messages.minLength, "minLength", field, options);
  }
});
var maxLengthRule3 = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (value.length > options.max) {
    field.report(messages.maxLength, "maxLength", field, options);
  }
});
var fixedLengthRule3 = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (value.length !== options.size) {
    field.report(messages.fixedLength, "fixedLength", field, options);
  }
});
var endsWithRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (!value.endsWith(options.substring)) {
    field.report(messages.endsWith, "endsWith", field, options);
  }
});
var startsWithRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (!value.startsWith(options.substring)) {
    field.report(messages.startsWith, "startsWith", field, options);
  }
});
var sameAsRule2 = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  const input = helpers.getNestedValue(options.otherField, field);
  if (input !== value) {
    field.report(messages.sameAs, "sameAs", field, options);
    return;
  }
});
var notSameAsRule2 = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  const input = helpers.getNestedValue(options.otherField, field);
  if (input === value) {
    field.report(messages.notSameAs, "notSameAs", field, options);
    return;
  }
});
var confirmedRule = createRule(
  (value, options, field) => {
    if (!field.isValid) {
      return;
    }
    const otherField = (options == null ? void 0 : options.confirmationField) || `${field.name}_confirmation`;
    const input = field.parent[otherField];
    if (input !== value) {
      field.report(messages.confirmed, "confirmed", field, { otherField });
      return;
    }
  }
);
var trimRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  field.mutate(value.trim(), field);
});
var normalizeEmailRule = createRule(
  (value, options, field) => {
    if (!field.isValid) {
      return;
    }
    field.mutate(import_normalizeEmail.default.default(value, options), field);
  }
);
var toUpperCaseRule = createRule(
  (value, locales, field) => {
    if (!field.isValid) {
      return;
    }
    field.mutate(value.toLocaleUpperCase(locales), field);
  }
);
var toLowerCaseRule = createRule(
  (value, locales, field) => {
    if (!field.isValid) {
      return;
    }
    field.mutate(value.toLocaleLowerCase(locales), field);
  }
);
var toCamelCaseRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  field.mutate(camelCase(value), field);
});
var escapeRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  field.mutate(import_escape.default.default(value), field);
});
var normalizeUrlRule = createRule(
  (value, options, field) => {
    if (!field.isValid) {
      return;
    }
    field.mutate(normalizeUrl(value, options), field);
  }
);
var inRule = createRule(
  (value, options, field) => {
    if (!field.isValid) {
      return;
    }
    const choices = typeof options.choices === "function" ? options.choices(field) : options.choices;
    if (!choices.includes(value)) {
      field.report(messages.in, "in", field, options);
      return;
    }
  }
);
var notInRule = createRule(
  (value, options, field) => {
    if (!field.isValid) {
      return;
    }
    const list = typeof options.list === "function" ? options.list(field) : options.list;
    if (list.includes(value)) {
      field.report(messages.notIn, "notIn", field, options);
      return;
    }
  }
);
var creditCardRule = createRule((value, options, field) => {
  var _a36;
  if (!field.isValid) {
    return;
  }
  const providers = options ? typeof options === "function" ? ((_a36 = options(field)) == null ? void 0 : _a36.provider) || [] : options.provider : [];
  if (!providers.length) {
    if (!helpers.isCreditCard(value)) {
      field.report(messages.creditCard, "creditCard", field, {
        providersList: "credit"
      });
    }
  } else {
    const matchesAnyProvider = providers.find(
      (provider) => helpers.isCreditCard(value, { provider })
    );
    if (!matchesAnyProvider) {
      field.report(messages.creditCard, "creditCard", field, {
        providers,
        providersList: providers.join("/")
      });
    }
  }
});
var passportRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  const countryCodes = typeof options === "function" ? options(field).countryCode : options.countryCode;
  const matchesAnyCountryCode = countryCodes.find(
    (countryCode) => helpers.isPassportNumber(value, countryCode)
  );
  if (!matchesAnyCountryCode) {
    field.report(messages.passport, "passport", field, { countryCodes });
  }
});
var postalCodeRule = createRule((value, options, field) => {
  var _a36;
  if (!field.isValid) {
    return;
  }
  const countryCodes = options ? typeof options === "function" ? ((_a36 = options(field)) == null ? void 0 : _a36.countryCode) || [] : options.countryCode : [];
  if (!countryCodes.length) {
    if (!helpers.isPostalCode(value, "any")) {
      field.report(messages.postalCode, "postalCode", field);
    }
  } else {
    const matchesAnyCountryCode = countryCodes.find(
      (countryCode) => helpers.isPostalCode(value, countryCode)
    );
    if (!matchesAnyCountryCode) {
      field.report(messages.postalCode, "postalCode", field, { countryCodes });
    }
  }
});
var uuidRule = createRule(
  (value, options, field) => {
    if (!field.isValid) {
      return;
    }
    if (!options || !options.version) {
      if (!helpers.isUUID(value)) {
        field.report(messages.uuid, "uuid", field);
      }
    } else {
      const matchesAnyVersion = options.version.find(
        (version) => helpers.isUUID(value, version)
      );
      if (!matchesAnyVersion) {
        field.report(messages.uuid, "uuid", field, options);
      }
    }
  }
);
var ulidRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  if (!helpers.isULID(value)) {
    field.report(messages.ulid, "ulid", field);
  }
});
var asciiRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  if (!helpers.isAscii(value)) {
    field.report(messages.ascii, "ascii", field);
  }
});
var ibanRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  if (!helpers.isIBAN(value)) {
    field.report(messages.iban, "iban", field);
  }
});
var jwtRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  if (!helpers.isJWT(value)) {
    field.report(messages.jwt, "jwt", field);
  }
});
var coordinatesRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  if (!helpers.isLatLong(value)) {
    field.report(messages.coordinates, "coordinates", field);
  }
});
var _a26, _b9, _c8, _d2, _e2;
var VineString = (_e2 = class extends (_d2 = BaseLiteralType, _c8 = SUBTYPE, _b9 = UNIQUE_NAME, _a26 = IS_OF_TYPE, _d2) {
  constructor(options, validations) {
    super(options, validations || [stringRule()]);
    /**
     * The subtype of the literal schema field
     */
    __publicField(this, _c8, "string");
    /**
     * The property must be implemented for "unionOfTypes"
     */
    __publicField(this, _b9, "vine.string");
    /**
     * Checks if the value is of string type. The method must be
     * implemented for "unionOfTypes"
     */
    __publicField(this, _a26, (value) => {
      return typeof value === "string";
    });
  }
  /**
   * Validates the value to be a valid URL
   */
  url(...args) {
    return this.use(urlRule(...args));
  }
  /**
   * Validates the value to be an active URL
   */
  activeUrl() {
    return this.use(activeUrlRule());
  }
  /**
   * Validates the value to be a valid email address
   */
  email(...args) {
    return this.use(emailRule(...args));
  }
  /**
   * Validates the value to be a valid mobile number
   */
  mobile(...args) {
    return this.use(mobileRule(...args));
  }
  /**
   * Validates the value to be a valid IP address.
   */
  ipAddress(version) {
    return this.use(ipAddressRule(version ? { version } : void 0));
  }
  /**
   * Validates the value to be a valid hex color code
   */
  hexCode() {
    return this.use(hexCodeRule());
  }
  /**
   * Validates the value against a regular expression
   */
  regex(expression) {
    return this.use(regexRule(expression));
  }
  /**
   * Validates the value to contain only letters
   */
  alpha(options) {
    return this.use(alphaRule(options));
  }
  /**
   * Validates the value to contain only letters and
   * numbers
   */
  alphaNumeric(options) {
    return this.use(alphaNumericRule(options));
  }
  /**
   * Enforce a minimum length on a string field
   */
  minLength(expectedLength) {
    return this.use(minLengthRule3({ min: expectedLength }));
  }
  /**
   * Enforce a maximum length on a string field
   */
  maxLength(expectedLength) {
    return this.use(maxLengthRule3({ max: expectedLength }));
  }
  /**
   * Enforce a fixed length on a string field
   */
  fixedLength(expectedLength) {
    return this.use(fixedLengthRule3({ size: expectedLength }));
  }
  /**
   * Ensure the field under validation is confirmed by
   * having another field with the same name.
   */
  confirmed(options) {
    return this.use(confirmedRule(options));
  }
  /**
   * Trims whitespaces around the string value
   */
  trim() {
    return this.use(trimRule());
  }
  /**
   * Normalizes the email address
   */
  normalizeEmail(options) {
    return this.use(normalizeEmailRule(options));
  }
  /**
   * Converts the field value to UPPERCASE.
   */
  toUpperCase() {
    return this.use(toUpperCaseRule());
  }
  /**
   * Converts the field value to lowercase.
   */
  toLowerCase() {
    return this.use(toLowerCaseRule());
  }
  /**
   * Converts the field value to camelCase.
   */
  toCamelCase() {
    return this.use(toCamelCaseRule());
  }
  /**
   * Escape string for HTML entities
   */
  escape() {
    return this.use(escapeRule());
  }
  /**
   * Normalize a URL
   */
  normalizeUrl(...args) {
    return this.use(normalizeUrlRule(...args));
  }
  /**
   * Ensure the value starts with the pre-defined substring
   */
  startsWith(substring) {
    return this.use(startsWithRule({ substring }));
  }
  /**
   * Ensure the value ends with the pre-defined substring
   */
  endsWith(substring) {
    return this.use(endsWithRule({ substring }));
  }
  /**
   * Ensure the value ends with the pre-defined substring
   */
  sameAs(otherField) {
    return this.use(sameAsRule2({ otherField }));
  }
  /**
   * Ensure the value ends with the pre-defined substring
   */
  notSameAs(otherField) {
    return this.use(notSameAsRule2({ otherField }));
  }
  /**
   * Ensure the field's value under validation is a subset of the pre-defined list.
   */
  in(choices) {
    return this.use(inRule({ choices }));
  }
  /**
   * Ensure the field's value under validation is not inside the pre-defined list.
   */
  notIn(list) {
    return this.use(notInRule({ list }));
  }
  /**
   * Validates the value to be a valid credit card number
   */
  creditCard(...args) {
    return this.use(creditCardRule(...args));
  }
  /**
   * Validates the value to be a valid passport number
   */
  passport(...args) {
    return this.use(passportRule(...args));
  }
  /**
   * Validates the value to be a valid postal code
   */
  postalCode(...args) {
    return this.use(postalCodeRule(...args));
  }
  /**
   * Validates the value to be a valid UUID
   */
  uuid(...args) {
    return this.use(uuidRule(...args));
  }
  /**
   * Validates the value to be a valid ULID
   */
  ulid() {
    return this.use(ulidRule());
  }
  /**
   * Validates the value contains ASCII characters only
   */
  ascii() {
    return this.use(asciiRule());
  }
  /**
   * Validates the value to be a valid IBAN number
   */
  iban() {
    return this.use(ibanRule());
  }
  /**
   * Validates the value to be a valid JWT token
   */
  jwt() {
    return this.use(jwtRule());
  }
  /**
   * Ensure the value is a string with latitude and longitude coordinates
   */
  coordinates() {
    return this.use(coordinatesRule());
  }
  /**
   * Clones the VineString schema type. The applied options
   * and validations are copied to the new instance
   */
  clone() {
    return new _e2(this.cloneOptions(), this.cloneValidations());
  }
}, __publicField(_e2, "rules", {
  in: inRule,
  jwt: jwtRule,
  url: urlRule,
  iban: ibanRule,
  uuid: uuidRule,
  ulid: ulidRule,
  trim: trimRule,
  email: emailRule,
  alpha: alphaRule,
  ascii: asciiRule,
  notIn: notInRule,
  regex: regexRule,
  escape: escapeRule,
  sameAs: sameAsRule2,
  mobile: mobileRule,
  string: stringRule,
  hexCode: hexCodeRule,
  passport: passportRule,
  endsWith: endsWithRule,
  confirmed: confirmedRule,
  activeUrl: activeUrlRule,
  minLength: minLengthRule3,
  notSameAs: notSameAsRule2,
  maxLength: maxLengthRule3,
  ipAddress: ipAddressRule,
  creditCard: creditCardRule,
  postalCode: postalCodeRule,
  startsWith: startsWithRule,
  toUpperCase: toUpperCaseRule,
  toLowerCase: toLowerCaseRule,
  toCamelCase: toCamelCaseRule,
  fixedLength: fixedLengthRule3,
  coordinates: coordinatesRule,
  normalizeUrl: normalizeUrlRule,
  alphaNumeric: alphaNumericRule,
  normalizeEmail: normalizeEmailRule
}), _e2);
var numberRule = createRule((value, options, field) => {
  const valueAsNumber = options.strict ? value : helpers.asNumber(value);
  if (typeof valueAsNumber !== "number" || Number.isNaN(valueAsNumber) || valueAsNumber === Number.POSITIVE_INFINITY || valueAsNumber === Number.NEGATIVE_INFINITY) {
    field.report(messages.number, "number", field);
    return;
  }
  field.mutate(valueAsNumber, field);
});
var minRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (value < options.min) {
    field.report(messages.min, "min", field, options);
  }
});
var maxRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (value > options.max) {
    field.report(messages.max, "max", field, options);
  }
});
var rangeRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (value < options.min || value > options.max) {
    field.report(messages.range, "range", field, options);
  }
});
var positiveRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  if (value < 0) {
    field.report(messages.positive, "positive", field);
  }
});
var negativeRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  if (value >= 0) {
    field.report(messages.negative, "negative", field);
  }
});
var decimalRule = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (!helpers.isDecimal(String(value), {
    force_decimal: options.range[0] !== 0,
    decimal_digits: options.range.join(",")
  })) {
    field.report(messages.decimal, "decimal", field, { digits: options.range.join("-") });
  }
});
var withoutDecimalsRule = createRule((value, _, field) => {
  if (!field.isValid) {
    return;
  }
  if (!Number.isInteger(value)) {
    field.report(messages.withoutDecimals, "withoutDecimals", field);
  }
});
var inRule2 = createRule((value, options, field) => {
  if (!field.isValid) {
    return;
  }
  if (!options.values.includes(value)) {
    field.report(messages["number.in"], "in", field, options);
  }
});
var _a27, _b10, _c9, _d3, _e3;
var VineNumber = (_e3 = class extends (_d3 = BaseLiteralType, _c9 = SUBTYPE, _b10 = UNIQUE_NAME, _a27 = IS_OF_TYPE, _d3) {
  constructor(options, validations) {
    super(options, validations || [numberRule(options || {})]);
    /**
     * The subtype of the literal schema field
     */
    __publicField(this, _c9, "number");
    /**
     * The property must be implemented for "unionOfTypes"
     */
    __publicField(this, _b10, "vine.number");
    /**
     * Checks if the value is of number type. The method must be
     * implemented for "unionOfTypes"
     */
    __publicField(this, _a27, (value) => {
      const valueAsNumber = helpers.asNumber(value);
      return !Number.isNaN(valueAsNumber);
    });
  }
  /**
   * Enforce a minimum value for the number input
   */
  min(value) {
    return this.use(minRule({ min: value }));
  }
  /**
   * Enforce a maximum value for the number input
   */
  max(value) {
    return this.use(maxRule({ max: value }));
  }
  /**
   * Enforce value to be within the range of minimum and maximum output.
   */
  range(value) {
    return this.use(rangeRule({ min: value[0], max: value[1] }));
  }
  /**
   * Enforce the value be a positive number
   */
  positive() {
    return this.use(positiveRule());
  }
  /**
   * Enforce the value be a negative number
   */
  negative() {
    return this.use(negativeRule());
  }
  /**
   * Enforce the value to have fixed or range
   * of decimal places
   */
  decimal(range) {
    return this.use(decimalRule({ range: Array.isArray(range) ? range : [range] }));
  }
  /**
   * Enforce the value to be an integer (aka without decimals)
   */
  withoutDecimals() {
    return this.use(withoutDecimalsRule());
  }
  /**
   * Clones the VineNumber schema type. The applied options
   * and validations are copied to the new instance
   */
  clone() {
    return new _e3(this.cloneOptions(), this.cloneValidations());
  }
  /**
   * Enforce the value to be in a list of allowed values
   */
  in(values) {
    return this.use(inRule2({ values }));
  }
}, /**
 * Default collection of number rules
 */
__publicField(_e3, "rules", {
  in: inRule2,
  max: maxRule,
  min: minRule,
  range: rangeRule,
  number: numberRule,
  decimal: decimalRule,
  negative: negativeRule,
  positive: positiveRule,
  withoutDecimals: withoutDecimalsRule
}), _e3);
var booleanRule = createRule((value, options, field) => {
  const valueAsBoolean = options.strict === true ? value : helpers.asBoolean(value);
  if (typeof valueAsBoolean !== "boolean") {
    field.report(messages.boolean, "boolean", field);
    return;
  }
  field.mutate(valueAsBoolean, field);
});
var _a28, _b11, _c10, _d4, _e4;
var VineBoolean = (_e4 = class extends (_d4 = BaseLiteralType, _c10 = SUBTYPE, _b11 = UNIQUE_NAME, _a28 = IS_OF_TYPE, _d4) {
  constructor(options, validations) {
    super(options, validations || [booleanRule(options || {})]);
    /**
     * The subtype of the literal schema field
     */
    __publicField(this, _c10, "boolean");
    /**
     * The property must be implemented for "unionOfTypes"
     */
    __publicField(this, _b11, "vine.boolean");
    /**
     * Checks if the value is of boolean type. The method must be
     * implemented for "unionOfTypes"
     */
    __publicField(this, _a28, (value) => {
      const valueAsBoolean = this.options.strict === true ? value : helpers.asBoolean(value);
      return typeof valueAsBoolean === "boolean";
    });
  }
  /**
   * Clones the VineBoolean schema type. The applied options
   * and validations are copied to the new instance
   */
  clone() {
    return new _e4(this.cloneOptions(), this.cloneValidations());
  }
}, /**
 * Default collection of boolean rules
 */
__publicField(_e4, "rules", {
  boolean: booleanRule
}), _e4);
var equalsRule2 = createRule((value, options, field) => {
  let input = value;
  if (typeof options.expectedValue === "boolean") {
    input = helpers.asBoolean(value);
  } else if (typeof options.expectedValue === "number") {
    input = helpers.asNumber(value);
  }
  if (input !== options.expectedValue) {
    field.report(messages.literal, "literal", field, options);
    return;
  }
  field.mutate(input, field);
});
var _a29, _b12, _c11, _value;
var VineLiteral = (_c11 = class extends (_b12 = BaseLiteralType, _a29 = SUBTYPE, _b12) {
  constructor(value, options, validations) {
    super(options, validations || [equalsRule2({ expectedValue: value })]);
    __privateAdd(this, _value);
    /**
     * The subtype of the literal schema field
     */
    __publicField(this, _a29, "literal");
    __privateSet(this, _value, value);
  }
  /**
   * Clones the VineLiteral schema type. The applied options
   * and validations are copied to the new instance
   */
  clone() {
    return new _c11(__privateGet(this, _value), this.cloneOptions(), this.cloneValidations());
  }
}, _value = new WeakMap(), /**
 * Default collection of literal rules
 */
__publicField(_c11, "rules", {
  equals: equalsRule2
}), _c11);
var ACCEPTED_VALUES = ["on", "1", "yes", "true", true, 1];
var acceptedRule = createRule((value, _, field) => {
  if (!ACCEPTED_VALUES.includes(value)) {
    field.report(messages.accepted, "accepted", field);
  }
});
var _a30, _b13, _c12;
var VineAccepted = (_c12 = class extends (_b13 = BaseLiteralType, _a30 = SUBTYPE, _b13) {
  constructor(options, validations) {
    super(options, validations || [acceptedRule()]);
    /**
     * The subtype of the literal schema field
     */
    __publicField(this, _a30, "checkbox");
  }
  /**
   * Clones the VineAccepted schema type. The applied options
   * and validations are copied to the new instance
   */
  clone() {
    return new _c12(this.cloneOptions(), this.cloneValidations());
  }
}, /**
 * Default collection of accepted rules
 */
__publicField(_c12, "rules", {
  accepted: acceptedRule
}), _c12);
var _conditionals2, _otherwiseCallback2, _a31;
var ObjectGroup = (_a31 = class {
  constructor(conditionals) {
    __privateAdd(this, _conditionals2);
    __privateAdd(this, _otherwiseCallback2, (_, field) => {
      field.report(messages.unionGroup, "unionGroup", field);
    });
    __privateSet(this, _conditionals2, conditionals);
  }
  /**
   * Clones the ObjectGroup schema type.
   */
  clone() {
    const cloned = new _a31(__privateGet(this, _conditionals2));
    cloned.otherwise(__privateGet(this, _otherwiseCallback2));
    return cloned;
  }
  /**
   * Define a fallback method to invoke when all of the group conditions
   * fail. You may use this method to report an error.
   */
  otherwise(callback) {
    __privateSet(this, _otherwiseCallback2, callback);
    return this;
  }
  /**
   * Compiles the group
   */
  [PARSE](refs, options) {
    return {
      type: "group",
      elseConditionalFnRefId: refs.trackConditional(__privateGet(this, _otherwiseCallback2)),
      conditions: __privateGet(this, _conditionals2).map((conditional) => conditional[PARSE](refs, options))
    };
  }
}, _conditionals2 = new WeakMap(), _otherwiseCallback2 = new WeakMap(), _a31);
var _properties2, _conditional2, _a32;
var GroupConditional = (_a32 = class {
  constructor(conditional, properties) {
    /**
     * Properties to merge when conditonal is true
     */
    __privateAdd(this, _properties2);
    /**
     * Conditional to evaluate
     */
    __privateAdd(this, _conditional2);
    __privateSet(this, _properties2, properties);
    __privateSet(this, _conditional2, conditional);
  }
  /**
   * Compiles to a union conditional
   */
  [PARSE](refs, options) {
    return {
      schema: {
        type: "sub_object",
        properties: Object.keys(__privateGet(this, _properties2)).map((property) => {
          return __privateGet(this, _properties2)[property][PARSE](property, refs, options);
        }),
        groups: []
        // Compiler allows nested groups, but we are not implementing it
      },
      conditionalFnRefId: refs.trackConditional(__privateGet(this, _conditional2))
    };
  }
}, _properties2 = new WeakMap(), _conditional2 = new WeakMap(), _a32);
function group(conditionals) {
  return new ObjectGroup(conditionals);
}
group.if = function groupIf(conditon, properties) {
  return new GroupConditional(conditon, properties);
};
group.else = function groupElse(properties) {
  return new GroupConditional(() => true, properties);
};
var _a33, _b14, _c13, _values2;
var VineNativeEnum = (_c13 = class extends (_b14 = BaseLiteralType, _a33 = SUBTYPE, _b14) {
  constructor(values, options, validations) {
    super(options, validations || [enumRule({ choices: Object.values(values) })]);
    __privateAdd(this, _values2);
    /**
     * The subtype of the literal schema field
     */
    __publicField(this, _a33, "enum");
    __privateSet(this, _values2, values);
  }
  /**
   * Clones the VineNativeEnum schema type. The applied options
   * and validations are copied to the new instance
   */
  clone() {
    return new _c13(__privateGet(this, _values2), this.cloneOptions(), this.cloneValidations());
  }
}, _values2 = new WeakMap(), /**
 * Default collection of enum rules
 */
__publicField(_c13, "rules", {
  enum: enumRule
}), _c13);
var _schemas2, _otherwiseCallback3, _a34;
var VineUnionOfTypes = (_a34 = class {
  constructor(schemas) {
    __privateAdd(this, _schemas2);
    __privateAdd(this, _otherwiseCallback3, (_, field) => {
      field.report(messages.unionOfTypes, "unionOfTypes", field);
    });
    __privateSet(this, _schemas2, schemas);
  }
  /**
   * Define a fallback method to invoke when all of the union conditions
   * fail. You may use this method to report an error.
   */
  otherwise(callback) {
    __privateSet(this, _otherwiseCallback3, callback);
    return this;
  }
  /**
   * Clones the VineUnionOfTypes schema type.
   */
  clone() {
    const cloned = new _a34(__privateGet(this, _schemas2));
    cloned.otherwise(__privateGet(this, _otherwiseCallback3));
    return cloned;
  }
  /**
   * Compiles to a union
   */
  [PARSE](propertyName, refs, options) {
    return {
      type: "union",
      fieldName: propertyName,
      propertyName: options.toCamelCase ? camelCase(propertyName) : propertyName,
      elseConditionalFnRefId: refs.trackConditional(__privateGet(this, _otherwiseCallback3)),
      conditions: __privateGet(this, _schemas2).map((schema) => {
        return {
          conditionalFnRefId: refs.trackConditional((value, field) => {
            return schema[IS_OF_TYPE](value, field);
          }),
          schema: schema[PARSE](propertyName, refs, options)
        };
      })
    };
  }
}, _schemas2 = new WeakMap(), _otherwiseCallback3 = new WeakMap(), _a34);
var SchemaBuilder = class extends Macroable {
  constructor() {
    super(...arguments);
    /**
     * Define a sub-object as a union
     */
    __publicField(this, "group", group);
    /**
     * Define a union value
     */
    __publicField(this, "union", union);
  }
  /**
   * Define a string value
   */
  string() {
    return new VineString();
  }
  /**
   * Define a boolean value
   */
  boolean(options) {
    return new VineBoolean(options);
  }
  /**
   * Validate a checkbox to be checked
   */
  accepted() {
    return new VineAccepted();
  }
  /**
   * Define a number value
   */
  number(options) {
    return new VineNumber(options);
  }
  /**
   * Define a datetime value
   */
  date(options) {
    return new VineDate(options);
  }
  /**
   * Define a schema type in which the input value
   * matches the pre-defined value
   */
  literal(value) {
    return new VineLiteral(value);
  }
  /**
   * Define an object with known properties. You may call "allowUnknownProperties"
   * to merge unknown properties.
   */
  object(properties) {
    return new VineObject(properties);
  }
  /**
   * Define an array field and validate its children elements.
   */
  array(schema) {
    return new VineArray(schema);
  }
  /**
   * Define an array field with known length and each children
   * element may have its own schema.
   */
  tuple(schemas) {
    return new VineTuple(schemas);
  }
  /**
   * Define an object field with key-value pair. The keys in
   * a record are unknown and values can be of a specific
   * schema type.
   */
  record(schema) {
    return new VineRecord(schema);
  }
  enum(values) {
    if (Array.isArray(values) || typeof values === "function") {
      return new VineEnum(values);
    }
    return new VineNativeEnum(values);
  }
  /**
   * Allow the field value to be anything
   */
  any() {
    return new VineAny();
  }
  /**
   * Define a union of unique schema types.
   */
  unionOfTypes(schemas) {
    const schemasInUse = /* @__PURE__ */ new Set();
    schemas.forEach((schema) => {
      if (!schema[IS_OF_TYPE] || !schema[UNIQUE_NAME]) {
        throw new Error(
          `Cannot use "${schema.constructor.name}". The schema type is not compatible for use with "vine.unionOfTypes"`
        );
      }
      if (schemasInUse.has(schema[UNIQUE_NAME])) {
        throw new Error(
          `Cannot use duplicate schema "${schema[UNIQUE_NAME]}". "vine.unionOfTypes" needs distinct schema types only`
        );
      }
      schemasInUse.add(schema[UNIQUE_NAME]);
    });
    schemasInUse.clear();
    return new VineUnionOfTypes(schemas);
  }
};
var COMPILER_ERROR_MESSAGES = {
  required: messages.required,
  array: messages.array,
  object: messages.object
};
var _compiled, _VineValidator_instances, parse_fn, _a35;
var VineValidator = (_a35 = class {
  constructor(schema, options) {
    __privateAdd(this, _VineValidator_instances);
    /**
     * Reference to the compiled schema
     */
    __privateAdd(this, _compiled);
    /**
     * Messages provider to use on the validator
     */
    __publicField(this, "messagesProvider");
    /**
     * Error reporter to use on the validator
     */
    __publicField(this, "errorReporter");
    const { compilerNode, refs } = __privateMethod(this, _VineValidator_instances, parse_fn).call(this, schema);
    __privateSet(this, _compiled, { schema: compilerNode, refs });
    const metaDataValidator = options.metaDataValidator;
    const validateFn = new Compiler(compilerNode, {
      convertEmptyStringsToNull: options.convertEmptyStringsToNull,
      messages: COMPILER_ERROR_MESSAGES
    }).compile();
    this.errorReporter = options.errorReporter;
    this.messagesProvider = options.messagesProvider;
    if (metaDataValidator) {
      this.validate = (data, validateOptions) => {
        let normalizedOptions = validateOptions ?? {};
        const meta = normalizedOptions.meta ?? {};
        const errorReporter = normalizedOptions.errorReporter ?? this.errorReporter;
        const messagesProvider = normalizedOptions.messagesProvider ?? this.messagesProvider;
        metaDataValidator(meta);
        return validateFn(data, meta, refs, messagesProvider, errorReporter());
      };
    } else {
      this.validate = (data, validateOptions) => {
        let normalizedOptions = validateOptions ?? {};
        const meta = normalizedOptions.meta ?? {};
        const errorReporter = normalizedOptions.errorReporter ?? this.errorReporter;
        const messagesProvider = normalizedOptions.messagesProvider ?? this.messagesProvider;
        return validateFn(data, meta, refs, messagesProvider, errorReporter());
      };
    }
  }
  /**
   * Performs validation without throwing the validation
   * exception. Instead, the validation errors are
   * returned as the first argument.
   *
   *
   * ```ts
   * await validator.tryValidate(data)
   * await validator.tryValidate(data, { meta: {} })
   *
   * await validator.tryValidate(data, {
   *   meta: { userId: auth.user.id },
   *   errorReporter,
   *   messagesProvider
   * })
   * ```
   *
   */
  async tryValidate(data, ...[options]) {
    try {
      const result = await this.validate(data, options);
      return [null, result];
    } catch (error) {
      if (error instanceof ValidationError) {
        return [error, null];
      }
      throw error;
    }
  }
  /**
   * Returns the compiled schema and refs.
   */
  toJSON() {
    const { schema, refs } = __privateGet(this, _compiled);
    return {
      schema: structuredClone(schema),
      refs
    };
  }
}, _compiled = new WeakMap(), _VineValidator_instances = new WeakSet(), /**
 * Parses schema to compiler nodes.
 */
parse_fn = function(schema) {
  const refs = refsBuilder();
  return {
    compilerNode: {
      type: "root",
      schema: schema[PARSE]("", refs, { toCamelCase: false })
    },
    refs: refs.toJSON()
  };
}, _a35);
var Vine = class extends SchemaBuilder {
  constructor() {
    super(...arguments);
    /**
     * Messages provider to use on the validator
     */
    __publicField(this, "messagesProvider", new SimpleMessagesProvider(messages, fields));
    /**
     * Error reporter to use on the validator
     */
    __publicField(this, "errorReporter", () => new SimpleErrorReporter());
    /**
     * Control whether or not to convert empty strings to null
     */
    __publicField(this, "convertEmptyStringsToNull", false);
    /**
     * Helpers to perform type-checking or cast types keeping
     * HTML forms serialization behavior in mind.
     */
    __publicField(this, "helpers", helpers);
    /**
     * Convert a validation function to a Vine schema rule
     */
    __publicField(this, "createRule", createRule);
  }
  /**
   * Pre-compiles a schema into a validation function.
   *
   * ```ts
   * const validate = vine.compile(schema)
   * await validate({ data })
   * ```
   */
  compile(schema) {
    return new VineValidator(schema, {
      convertEmptyStringsToNull: this.convertEmptyStringsToNull,
      messagesProvider: this.messagesProvider,
      errorReporter: this.errorReporter
    });
  }
  /**
   * Define a callback to validate the metadata given to the validator
   * at runtime
   */
  withMetaData(callback) {
    return {
      compile: (schema) => {
        return new VineValidator(schema, {
          convertEmptyStringsToNull: this.convertEmptyStringsToNull,
          messagesProvider: this.messagesProvider,
          errorReporter: this.errorReporter,
          metaDataValidator: callback
        });
      }
    };
  }
  /**
   * Validate data against a schema. Optionally, you can define
   * error messages, fields, a custom messages provider,
   * or an error reporter.
   *
   * ```ts
   * await vine.validate({ schema, data })
   * await vine.validate({ schema, data, messages, fields })
   *
   * await vine.validate({ schema, data, messages, fields }, {
   *   errorReporter
   * })
   * ```
   */
  validate(options) {
    const validator = this.compile(options.schema);
    return validator.validate(options.data, options);
  }
  /**
   * Validate data against a schema without throwing the
   * "ValidationError" exception. Instead the validation
   * errors are returned within the return value.
   *
   * ```ts
   * await vine.tryValidate({ schema, data })
   * await vine.tryValidate({ schema, data, messages, fields })
   *
   * await vine.tryValidate({ schema, data, messages, fields }, {
   *   errorReporter
   * })
   * ```
   */
  tryValidate(options) {
    const validator = this.compile(options.schema);
    return validator.tryValidate(options.data, options);
  }
};
var vine = new Vine();
var index_default = vine;
export {
  BaseLiteralType,
  BaseModifiersType2 as BaseModifiersType,
  BaseType,
  SimpleErrorReporter,
  SimpleMessagesProvider,
  Vine,
  VineAccepted,
  VineAny,
  VineArray,
  VineBoolean,
  VineDate,
  VineEnum,
  VineLiteral,
  VineNativeEnum,
  VineNumber,
  VineObject,
  VineRecord,
  VineString,
  VineTuple,
  VineUnion,
  VineValidator,
  index_default as default,
  main_exports as errors,
  symbols_exports as symbols
};
//# sourceMappingURL=@vinejs_vine.js.map
