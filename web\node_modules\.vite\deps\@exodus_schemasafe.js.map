{"version": 3, "sources": ["../../@exodus/schemasafe/src/safe-format.js", "../../@exodus/schemasafe/src/scope-utils.js", "../../@exodus/schemasafe/src/scope-functions.js", "../../@exodus/schemasafe/src/javascript.js", "../../@exodus/schemasafe/src/generate-function.js", "../../@exodus/schemasafe/src/known-keywords.js", "../../@exodus/schemasafe/src/pointer.js", "../../@exodus/schemasafe/src/formats.js", "../../@exodus/schemasafe/src/tracing.js", "../../@exodus/schemasafe/src/compile.js", "../../@exodus/schemasafe/src/index.js"], "sourcesContent": ["'use strict'\n\nclass SafeString extends String {} // used for instanceof checks\n\nconst compares = new Set(['<', '>', '<=', '>='])\nconst escapeCode = (code) => `\\\\u${code.toString(16).padStart(4, '0')}`\n\n// Supports simple js variables only, i.e. constants and JSON-stringifiable\n// Converts a variable to be safe for inclusion in JS context\n// This works on top of JSON.stringify with minor fixes to negate the JS/JSON parsing differences\nconst jsval = (val) => {\n  if ([Infinity, -Infinity, NaN, undefined, null].includes(val)) return `${val}`\n  const primitive = ['string', 'boolean', 'number'].includes(typeof val)\n  if (!primitive) {\n    if (typeof val !== 'object') throw new Error('Unexpected value type')\n    const proto = Object.getPrototypeOf(val)\n    const ok = (proto === Array.prototype && Array.isArray(val)) || proto === Object.prototype\n    if (!ok) throw new Error('Unexpected object given as value')\n  }\n  return (\n    JSON.stringify(val)\n      // JSON context and JS eval context have different handling of __proto__ property name\n      // Refs: https://www.ecma-international.org/ecma-262/#sec-json.parse\n      // Refs: https://www.ecma-international.org/ecma-262/#sec-__proto__-property-names-in-object-initializers\n      // Replacement is safe because it's the only way that encodes __proto__ property in JSON and\n      // it can't occur inside strings or other properties, due to the leading `\"` and traling `\":`\n      .replace(/([{,])\"__proto__\":/g, '$1[\"__proto__\"]:')\n      // The above line should cover all `\"__proto__\":` occurances except for `\"...\\\"__proto__\":`\n      .replace(/[^\\\\]\"__proto__\":/g, () => {\n        /* c8 ignore next */\n        throw new Error('Unreachable')\n      })\n      // https://v8.dev/features/subsume-json#security, e.g. {'\\u2028':0} on Node.js 8\n      .replace(/[\\u2028\\u2029]/g, (char) => escapeCode(char.charCodeAt(0)))\n  )\n}\n\nconst format = (fmt, ...args) => {\n  const res = fmt.replace(/%[%drscjw]/g, (match) => {\n    if (match === '%%') return '%'\n    if (args.length === 0) throw new Error('Unexpected arguments count')\n    const val = args.shift()\n    switch (match) {\n      case '%d':\n        if (typeof val === 'number') return val\n        throw new Error('Expected a number')\n      case '%r':\n        // String(regex) is not ok on Node.js 10 and below: console.log(String(new RegExp('\\n')))\n        if (val instanceof RegExp) return format('new RegExp(%j, %j)', val.source, val.flags)\n        throw new Error('Expected a RegExp instance')\n      case '%s':\n        if (val instanceof SafeString) return val\n        throw new Error('Expected a safe string')\n      case '%c':\n        if (compares.has(val)) return val\n        throw new Error('Expected a compare op')\n      case '%j':\n        return jsval(val)\n      case '%w':\n        if (Number.isInteger(val) && val >= 0) return ' '.repeat(val)\n        throw new Error('Expected a non-negative integer for indentation')\n    }\n    /* c8 ignore next */\n    throw new Error('Unreachable')\n  })\n  if (args.length !== 0) throw new Error('Unexpected arguments count')\n  return new SafeString(res)\n}\n\nconst safe = (string) => {\n  if (!/^[a-z][a-z0-9_]*$/i.test(string)) throw new Error('Does not look like a safe id')\n  return new SafeString(string)\n}\n\n// too dangereous to export, use with care\nconst safewrap = (fun) => (...args) => {\n  if (!args.every((arg) => arg instanceof SafeString)) throw new Error('Unsafe arguments')\n  return new SafeString(fun(...args))\n}\n\nconst safepriority = (arg) =>\n  // simple expression and single brackets can not break priority\n  /^[a-z][a-z0-9_().]*$/i.test(arg) || /^\\([^()]+\\)$/i.test(arg) ? arg : format('(%s)', arg)\nconst safeor = safewrap(\n  (...args) => (args.some((arg) => `${arg}` === 'true') ? 'true' : args.join(' || ') || 'false')\n)\nconst safeand = safewrap(\n  (...args) => (args.some((arg) => `${arg}` === 'false') ? 'false' : args.join(' && ') || 'true')\n)\nconst safenot = (arg) => {\n  if (`${arg}` === 'true') return safe('false')\n  if (`${arg}` === 'false') return safe('true')\n  return format('!%s', safepriority(arg))\n}\n// this function is priority-safe, unlike safeor, hence it's exported and safeor is not atm\nconst safenotor = (...args) => safenot(safeor(...args))\n\nmodule.exports = { format, safe, safeand, safenot, safenotor }\n", "'use strict'\n\nconst { safe } = require('./safe-format')\n\nconst caches = new WeakMap()\n\n// Given a scope object, generates new symbol/loop/pattern/format/ref variable names,\n// also stores in-scope format/ref mapping to variable names\n\nconst scopeMethods = (scope) => {\n  // cache meta info for known scope variables, per meta type\n  if (!caches.has(scope))\n    caches.set(scope, { sym: new Map(), ref: new Map(), format: new Map(), pattern: new Map() })\n  const cache = caches.get(scope)\n\n  // Generic variable names, requires a base name aka prefix\n  const gensym = (name) => {\n    if (!cache.sym.get(name)) cache.sym.set(name, 0)\n    const index = cache.sym.get(name)\n    cache.sym.set(name, index + 1)\n    return safe(`${name}${index}`)\n  }\n\n  // Regexp pattern names\n  const genpattern = (p) => {\n    if (cache.pattern.has(p)) return cache.pattern.get(p)\n    const n = gensym('pattern')\n    scope[n] = new RegExp(p, 'u')\n    cache.pattern.set(p, n)\n    return n\n  }\n\n  // Loop variable names\n  if (!cache.loop) cache.loop = 'ijklmnopqrstuvxyz'.split('')\n  const genloop = () => {\n    const v = cache.loop.shift()\n    cache.loop.push(`${v}${v[0]}`)\n    return safe(v)\n  }\n\n  // Reference (validator function) names\n  const getref = (sub) => cache.ref.get(sub)\n  const genref = (sub) => {\n    const n = gensym('ref')\n    cache.ref.set(sub, n)\n    return n\n  }\n\n  // Format validation function names\n  const genformat = (impl) => {\n    let n = cache.format.get(impl)\n    if (!n) {\n      n = gensym('format')\n      scope[n] = impl\n      cache.format.set(impl, n)\n    }\n    return n\n  }\n\n  return { gensym, genpattern, genloop, getref, genref, genformat }\n}\n\nmodule.exports = { scopeMethods }\n", "'use strict'\n\n// for correct Unicode code points processing\n// https://mathiasbynens.be/notes/javascript-unicode#accounting-for-astral-symbols\nconst stringLength = (string) =>\n  /[\\uD800-\\uDFFF]/.test(string) ? [...string].length : string.length\n\n// A isMultipleOf B: shortest decimal denoted as A % shortest decimal denoted as B === 0\n// Optimized, coherence checks and precomputation are outside of this method\n// If we get an Infinity when we multiply by the factor (which is always a power of 10), we just undo that instead of always returning false\nconst isMultipleOf = (value, divisor, factor, factorMultiple) => {\n  if (value % divisor === 0) return true\n  let multiple = value * factor\n  if (multiple === Infinity || multiple === -Infinity) multiple = value\n  if (multiple % factorMultiple === 0) return true\n  const normal = Math.floor(multiple + 0.5)\n  return normal / factor === value && normal % factorMultiple === 0\n}\n\n// supports only JSON-stringifyable objects, defaults to false for unsupported\n// also uses ===, not Object.is, i.e. 0 === -0, NaN !== NaN\n// symbols and non-enumerable properties are ignored!\nconst deepEqual = (obj, obj2) => {\n  if (obj === obj2) return true\n  if (!obj || !obj2 || typeof obj !== typeof obj2) return false\n  if (obj !== obj2 && typeof obj !== 'object') return false\n\n  const proto = Object.getPrototypeOf(obj)\n  if (proto !== Object.getPrototypeOf(obj2)) return false\n\n  if (proto === Array.prototype) {\n    if (!Array.isArray(obj) || !Array.isArray(obj2)) return false\n    if (obj.length !== obj2.length) return false\n    return obj.every((x, i) => deepEqual(x, obj2[i]))\n  } else if (proto === Object.prototype) {\n    const [keys, keys2] = [Object.keys(obj), Object.keys(obj2)]\n    if (keys.length !== keys2.length) return false\n    const keyset2 = new Set([...keys, ...keys2])\n    return keyset2.size === keys.length && keys.every((key) => deepEqual(obj[key], obj2[key]))\n  }\n  return false\n}\n\nconst unique = (array) => {\n  if (array.length < 2) return true\n  if (array.length === 2) return !deepEqual(array[0], array[1])\n  const objects = []\n  const primitives = array.length > 20 ? new Set() : null\n  let primitivesCount = 0\n  let pos = 0\n  for (const item of array) {\n    if (typeof item === 'object') {\n      objects.push(item)\n    } else if (primitives) {\n      primitives.add(item)\n      if (primitives.size !== ++primitivesCount) return false\n    } else {\n      if (array.indexOf(item, pos + 1) !== -1) return false\n    }\n    pos++\n  }\n  for (let i = 1; i < objects.length; i++)\n    for (let j = 0; j < i; j++) if (deepEqual(objects[i], objects[j])) return false\n  return true\n}\n\nconst deBase64 = (string) => {\n  if (typeof Buffer !== 'undefined') return Buffer.from(string, 'base64').toString('utf-8')\n  const b = atob(string)\n  return new TextDecoder('utf-8').decode(new Uint8Array(b.length).map((_, i) => b.charCodeAt(i)))\n}\n\nconst hasOwn = Function.prototype.call.bind(Object.prototype.hasOwnProperty)\n// special handling for stringification\nhasOwn[Symbol.for('toJayString')] = 'Function.prototype.call.bind(Object.prototype.hasOwnProperty)'\n\n// Used for error generation. Affects error performance, optimized\nconst pointerPart = (s) => (/~\\//.test(s) ? `${s}`.replace(/~/g, '~0').replace(/\\//g, '~1') : s)\nconst toPointer = (path) => (path.length === 0 ? '#' : `#/${path.map(pointerPart).join('/')}`)\n\nconst errorMerge = ({ keywordLocation, instanceLocation }, schemaBase, dataBase) => ({\n  keywordLocation: `${schemaBase}${keywordLocation.slice(1)}`,\n  instanceLocation: `${dataBase}${instanceLocation.slice(1)}`,\n})\n\nconst propertyIn = (key, [properties, patterns]) =>\n  properties.includes(true) ||\n  properties.some((prop) => prop === key) ||\n  patterns.some((pattern) => new RegExp(pattern, 'u').test(key))\n\n// id is verified to start with '#' at compile time, hence using plain objects is safe\nconst dynamicResolve = (anchors, id) => (anchors.filter((x) => x[id])[0] || {})[id]\n\nconst extraUtils = { toPointer, pointerPart, errorMerge, propertyIn, dynamicResolve }\nmodule.exports = { stringLength, isMultipleOf, deepEqual, unique, deBase64, hasOwn, ...extraUtils }\n", "'use strict'\n\nconst { format, safe } = require('./safe-format')\nconst { scopeMethods } = require('./scope-utils')\nconst functions = require('./scope-functions')\n\n// for building into the validation function\nconst types = new Map(\n  Object.entries({\n    null: (name) => format('%s === null', name),\n    boolean: (name) => format('typeof %s === \"boolean\"', name),\n    array: (name) => format('Array.isArray(%s)', name),\n    object: (n) => format('typeof %s === \"object\" && %s && !Array.isArray(%s)', n, n, n),\n    number: (name) => format('typeof %s === \"number\"', name),\n    integer: (name) => format('Number.isInteger(%s)', name),\n    string: (name) => format('typeof %s === \"string\"', name),\n  })\n)\n\nconst buildName = ({ name, parent, keyval, keyname }) => {\n  if (name) {\n    if (parent || keyval || keyname) throw new Error('name can be used only stand-alone')\n    return name // top-level\n  }\n  if (!parent) throw new Error('Can not use property of undefined parent!')\n  const parentName = buildName(parent)\n  if (keyval !== undefined) {\n    if (keyname) throw new Error('Can not use key value and name together')\n    if (!['string', 'number'].includes(typeof keyval)) throw new Error('Invalid property path')\n    if (/^[a-z][a-z0-9_]*$/i.test(keyval)) return format('%s.%s', parentName, safe(keyval))\n    return format('%s[%j]', parentName, keyval)\n  } else if (keyname) {\n    return format('%s[%s]', parentName, keyname)\n  }\n  /* c8 ignore next */\n  throw new Error('Unreachable')\n}\n\nconst jsonProtoKeys = new Set(\n  [].concat(\n    ...[Object, Array, String, Number, Boolean].map((c) => Object.getOwnPropertyNames(c.prototype))\n  )\n)\n\nconst jsHelpers = (fun, scope, propvar, { unmodifiedPrototypes, isJSON }, noopRegExps) => {\n  const { gensym, genpattern, genloop } = scopeMethods(scope, propvar)\n\n  const present = (obj) => {\n    const name = buildName(obj) // also checks for coherence, do not remove\n    const { parent, keyval, keyname, inKeys, checked } = obj\n    /* c8 ignore next */\n    if (checked || (inKeys && isJSON)) throw new Error('Unreachable: useless check for undefined')\n    if (inKeys) return format('%s !== undefined', name)\n    if (parent && keyname) {\n      scope.hasOwn = functions.hasOwn\n      const pname = buildName(parent)\n      if (isJSON) return format('%s !== undefined && hasOwn(%s, %s)', name, pname, keyname)\n      return format('%s in %s && hasOwn(%s, %s)', keyname, pname, pname, keyname)\n    } else if (parent && keyval !== undefined) {\n      // numbers must be converted to strings for this check, hence `${keyval}` in check below\n      if (unmodifiedPrototypes && isJSON && !jsonProtoKeys.has(`${keyval}`))\n        return format('%s !== undefined', name)\n      scope.hasOwn = functions.hasOwn\n      const pname = buildName(parent)\n      if (isJSON) return format('%s !== undefined && hasOwn(%s, %j)', name, pname, keyval)\n      return format('%j in %s && hasOwn(%s, %j)', keyval, pname, pname, keyval)\n    }\n    /* c8 ignore next */\n    throw new Error('Unreachable: present() check without parent')\n  }\n\n  const forObjectKeys = (obj, writeBody) => {\n    const key = gensym('key')\n    fun.block(format('for (const %s of Object.keys(%s))', key, buildName(obj)), () => {\n      writeBody(propvar(obj, key, true), key) // always own property here\n    })\n  }\n\n  const forArray = (obj, start, writeBody) => {\n    const i = genloop()\n    const name = buildName(obj)\n    fun.block(format('for (let %s = %s; %s < %s.length; %s++)', i, start, i, name, i), () => {\n      writeBody(propvar(obj, i, unmodifiedPrototypes, true), i) // own property in Array if proto not mangled\n    })\n  }\n\n  const patternTest = (pat, key) => {\n    // Convert common patterns to string checks, makes generated code easier to read (and a tiny perf bump)\n    const r = pat.replace(/[.^$|*+?(){}[\\]\\\\]/gu, '') // Special symbols: .^$|*+?(){}[]\\\n    if (pat === `^${r}$`) return format('(%s === %j)', key, pat.slice(1, -1)) // ^abc$ -> === abc\n    if (noopRegExps.has(pat)) return format('true') // known noop\n\n    // All of the below will cause warnings in enforced string validation mode, but let's make what they actually do more visible\n    // note that /^.*$/u.test('\\n') is false, so don't combine .* with anchors here!\n    if ([r, `${r}+`, `${r}.*`, `.*${r}.*`].includes(pat)) return format('%s.includes(%j)', key, r)\n    if ([`^${r}`, `^${r}+`, `^${r}.*`].includes(pat)) return format('%s.startsWith(%j)', key, r)\n    if ([`${r}$`, `.*${r}$`].includes(pat)) return format('%s.endsWith(%j)', key, r)\n\n    const subr = [...r].slice(0, -1).join('') // without the last symbol, astral plane aware\n    if ([`${r}*`, `${r}?`].includes(pat))\n      return subr.length === 0 ? format('true') : format('%s.includes(%j)', key, subr) // abc*, abc? -> includes(ab)\n    if ([`^${r}*`, `^${r}?`].includes(pat))\n      return subr.length === 0 ? format('true') : format('%s.startsWith(%j)', key, subr) // ^abc*, ^abc? -> startsWith(ab)\n\n    // A normal reg-exp test\n    return format('%s.test(%s)', genpattern(pat), key)\n  }\n\n  const compare = (name, val) => {\n    if (!val || typeof val !== 'object') return format('%s === %j', name, val)\n\n    let type // type is needed for speedup only, deepEqual rechecks that\n    // small plain object/arrays are fast cases and we inline those instead of calling deepEqual\n    const shouldInline = (arr) => arr.length <= 3 && arr.every((x) => !x || typeof x !== 'object')\n    if (Array.isArray(val)) {\n      type = types.get('array')(name)\n      if (shouldInline(val)) {\n        let k = format('%s.length === %d', name, val.length)\n        for (let i = 0; i < val.length; i++) k = format('%s && %s[%d] === %j', k, name, i, val[i])\n        return format('%s && %s', type, k)\n      }\n    } else {\n      type = types.get('object')(name)\n      const [keys, values] = [Object.keys(val), Object.values(val)]\n      if (shouldInline(values)) {\n        let k = format('Object.keys(%s).length === %d', name, keys.length)\n        if (keys.length > 0) scope.hasOwn = functions.hasOwn\n        for (const key of keys) k = format('%s && hasOwn(%s, %j)', k, name, key)\n        for (const key of keys) k = format('%s && %s[%j] === %j', k, name, key, val[key])\n        return format('%s && %s', type, k)\n      }\n    }\n\n    scope.deepEqual = functions.deepEqual\n    return format('%s && deepEqual(%s, %j)', type, name, val)\n  }\n\n  return { present, forObjectKeys, forArray, patternTest, compare, propvar }\n}\n\n// Stringifcation of functions and regexps, for scope\nconst isArrowFnWithParensRegex = /^\\([^)]*\\) *=>/\nconst isArrowFnWithoutParensRegex = /^[^=]*=>/\nconst toJayString = Symbol.for('toJayString')\nfunction jaystring(item) {\n  if (typeof item === 'function') {\n    if (item[toJayString]) return item[toJayString] // this is supported only for functions\n\n    if (Object.getPrototypeOf(item) !== Function.prototype)\n      throw new Error('Can not stringify: a function with unexpected prototype')\n\n    const stringified = `${item}`\n    if (item.prototype) {\n      if (!/^function[ (]/.test(stringified)) throw new Error('Unexpected function')\n      return stringified // normal function\n    }\n    if (isArrowFnWithParensRegex.test(stringified) || isArrowFnWithoutParensRegex.test(stringified))\n      return stringified // Arrow function\n\n    // Shortened ES6 object method declaration\n    throw new Error('Can not stringify: only either normal or arrow functions are supported')\n  } else if (typeof item === 'object') {\n    const proto = Object.getPrototypeOf(item)\n    if (item instanceof RegExp && proto === RegExp.prototype) return format('%r', item)\n    throw new Error('Can not stringify: an object with unexpected prototype')\n  }\n  throw new Error(`Can not stringify: unknown type ${typeof item}`)\n}\n\nmodule.exports = { types, buildName, jsHelpers, jaystring }\n", "'use strict'\n\nconst { format, safe, safenot } = require('./safe-format')\nconst { jaystring } = require('./javascript')\n\n/*\n * Construct a function from lines/blocks/if conditions.\n *\n * Returns a Function instance (makeFunction) or code in text format (makeModule).\n */\n\nconst INDENT_START = /[{[]/\nconst INDENT_END = /[}\\]]/\n\nmodule.exports = () => {\n  const lines = []\n  let indent = 0\n\n  const pushLine = (line) => {\n    if (INDENT_END.test(line.trim()[0])) indent--\n    lines.push({ indent, code: line })\n    if (INDENT_START.test(line[line.length - 1])) indent++\n  }\n\n  const build = () => {\n    if (indent !== 0) throw new Error('Unexpected indent at build()')\n    const joined = lines.map((line) => format('%w%s', line.indent * 2, line.code)).join('\\n')\n    return /^[a-z][a-z0-9]*$/i.test(joined) ? `return ${joined}` : `return (${joined})`\n  }\n\n  const processScope = (scope) => {\n    const entries = Object.entries(scope)\n    for (const [key, value] of entries) {\n      if (!/^[a-z][a-z0-9]*$/i.test(key)) throw new Error('Unexpected scope key!')\n      if (!(typeof value === 'function' || value instanceof RegExp))\n        throw new Error('Unexpected scope value!')\n    }\n    return entries\n  }\n\n  return {\n    optimizedOut: false, // some branch of code has been optimized out\n    size: () => lines.length,\n\n    write(fmt, ...args) {\n      if (typeof fmt !== 'string') throw new Error('Format must be a string!')\n      if (fmt.includes('\\n')) throw new Error('Only single lines are supported')\n      pushLine(format(fmt, ...args))\n      return true // code was written\n    },\n\n    block(prefix, writeBody, noInline = false) {\n      const oldIndent = indent\n      this.write('%s {', prefix)\n      const length = lines.length\n      writeBody()\n      if (length === lines.length) {\n        // no lines inside block, unwind the block\n        lines.pop()\n        indent = oldIndent\n        return false // nothing written\n      } else if (length === lines.length - 1 && !noInline) {\n        // a single line has been written, inline it if opt-in allows\n        const { code } = lines[lines.length - 1]\n        // check below is just for generating more readable code, it's safe to inline all !noInline\n        if (!/^(if|for) /.test(code)) {\n          lines.length -= 2\n          indent = oldIndent\n          return this.write('%s %s', prefix, code)\n        }\n      }\n      return this.write('}')\n    },\n\n    if(condition, writeBody, writeElse) {\n      if (`${condition}` === 'false') {\n        if (writeElse) writeElse()\n        if (writeBody) this.optimizedOut = true\n      } else if (`${condition}` === 'true') {\n        if (writeBody) writeBody()\n        if (writeElse) this.optimizedOut = true\n      } else if (writeBody && this.block(format('if (%s)', condition), writeBody, !!writeElse)) {\n        if (writeElse) this.block(format('else'), writeElse) // !!writeElse above ensures {} wrapping before `else`\n      } else if (writeElse) {\n        this.if(safenot(condition), writeElse)\n      }\n    },\n\n    makeModule(scope = {}) {\n      const scopeDefs = processScope(scope).map(\n        ([key, val]) => `const ${safe(key)} = ${jaystring(val)};`\n      )\n      return `(function() {\\n'use strict'\\n${scopeDefs.join('\\n')}\\n${build()}})()`\n    },\n\n    makeFunction(scope = {}) {\n      const scopeEntries = processScope(scope)\n      const keys = scopeEntries.map((entry) => entry[0])\n      const vals = scopeEntries.map((entry) => entry[1])\n      // eslint-disable-next-line no-new-func\n      return Function(...keys, `'use strict'\\n${build()}`)(...vals)\n    },\n  }\n}\n", "'use strict'\n\nconst knownKeywords = [\n  ...['$schema', '$vocabulary'], // version\n  ...['id', '$id', '$anchor', '$ref', 'definitions', '$defs'], // pointers\n  ...['$recursiveRef', '$recursiveAnchor', '$dynamicAnchor', '$dynamicRef'],\n  ...['type', 'required', 'default'], // generic\n  ...['enum', 'const'], // constant values\n  ...['not', 'allOf', 'anyOf', 'oneOf', 'if', 'then', 'else'], // logical checks\n  ...['maximum', 'minimum', 'exclusiveMaximum', 'exclusiveMinimum', 'multipleOf', 'divisibleBy'], // numbers\n  ...['items', 'maxItems', 'minItems', 'additionalItems', 'prefixItems'], // arrays, basic\n  ...['contains', 'minContains', 'maxContains', 'uniqueItems'], // arrays, complex\n  ...['maxLength', 'minLength', 'format', 'pattern'], // strings\n  ...['contentEncoding', 'contentMediaType', 'contentSchema'], // strings content\n  ...['properties', 'maxProperties', 'minProperties', 'additionalProperties', 'patternProperties'], // objects\n  ...['propertyNames'], // objects\n  ...['dependencies', 'dependentRequired', 'dependentSchemas', 'propertyDependencies'], // objects (dependencies)\n  ...['unevaluatedProperties', 'unevaluatedItems'], // see-through\n  // Unused meta keywords not affecting validation (annotations and comments)\n  // https://json-schema.org/understanding-json-schema/reference/generic.html\n  // https://json-schema.org/draft/2019-09/json-schema-validation.html#rfc.section.9\n  ...['title', 'description', 'deprecated', 'readOnly', 'writeOnly', 'examples', '$comment'], // unused meta\n  ...['example'], // unused meta, OpenAPI\n  'discriminator', // optimization hint and error filtering only, does not affect validation result\n  'removeAdditional', // optional keyword for { removeAdditional: 'keyword' } config, to target specific objects\n]\n\n// Order is important, newer first!\nconst schemaDrafts = [\n  ...['draft/next'], // not recommended to use, might change / break in an unexpected way\n  ...['draft/2020-12', 'draft/2019-09'], // new\n  ...['draft-07', 'draft-06', 'draft-04', 'draft-03'], // historic\n]\nconst schemaVersions = schemaDrafts.map((draft) => `https://json-schema.org/${draft}/schema`)\n\nconst vocab2019 = ['core', 'applicator', 'validation', 'meta-data', 'format', 'content']\nconst vocab2020 = [\n  ...['core', 'applicator', 'unevaluated', 'validation'],\n  ...['meta-data', 'format-annotation', 'format-assertion', 'content'],\n]\nconst knownVocabularies = [\n  ...vocab2019.map((v) => `https://json-schema.org/draft/2019-09/vocab/${v}`),\n  ...vocab2020.map((v) => `https://json-schema.org/draft/2020-12/vocab/${v}`),\n]\n\nmodule.exports = { knownKeywords, schemaVersions, knownVocabularies }\n", "'use strict'\n\nconst { knownKeywords } = require('./known-keywords')\n\n/*\n * JSON pointer collection/resolution logic\n */\n\nfunction safeSet(map, key, value, comment = 'keys') {\n  if (!map.has(key)) return map.set(key, value)\n  if (map.get(key) !== value) throw new Error(`Conflicting duplicate ${comment}: ${key}`)\n}\n\nfunction untilde(string) {\n  if (!string.includes('~')) return string\n  return string.replace(/~[01]/g, (match) => {\n    switch (match) {\n      case '~1':\n        return '/'\n      case '~0':\n        return '~'\n    }\n    /* c8 ignore next */\n    throw new Error('Unreachable')\n  })\n}\n\nfunction get(obj, pointer, objpath) {\n  if (typeof obj !== 'object') throw new Error('Invalid input object')\n  if (typeof pointer !== 'string') throw new Error('Invalid JSON pointer')\n  const parts = pointer.split('/')\n  if (!['', '#'].includes(parts.shift())) throw new Error('Invalid JSON pointer')\n  if (parts.length === 0) return obj\n\n  let curr = obj\n  for (const part of parts) {\n    if (typeof part !== 'string') throw new Error('Invalid JSON pointer')\n    if (objpath) objpath.push(curr) // does not include target itself, but includes head\n    const prop = untilde(part)\n    if (typeof curr !== 'object') return undefined\n    if (!Object.prototype.hasOwnProperty.call(curr, prop)) return undefined\n    curr = curr[prop]\n  }\n  return curr\n}\n\nconst protocolRegex = /^https?:\\/\\//\n\nfunction joinPath(baseFull, sub) {\n  if (typeof baseFull !== 'string' || typeof sub !== 'string') throw new Error('Unexpected path!')\n  if (sub.length === 0) return baseFull\n  const base = baseFull.replace(/#.*/, '')\n  if (sub.startsWith('#')) return `${base}${sub}`\n  if (!base.includes('/') || protocolRegex.test(sub)) return sub\n  if (protocolRegex.test(base)) return `${new URL(sub, base)}`\n  if (sub.startsWith('/')) return sub\n  return [...base.split('/').slice(0, -1), sub].join('/')\n}\n\nfunction objpath2path(objpath) {\n  const ids = objpath.map((obj) => (obj && (obj.$id || obj.id)) || '')\n  return ids.filter((id) => id && typeof id === 'string').reduce(joinPath, '')\n}\n\nconst withSpecialChilds = ['properties', 'patternProperties', '$defs', 'definitions']\nconst skipChilds = ['const', 'enum', 'examples', 'example', 'comment']\nconst sSkip = Symbol('skip')\n\nfunction traverse(schema, work) {\n  const visit = (sub, specialChilds = false) => {\n    if (!sub || typeof sub !== 'object') return\n    const res = work(sub)\n    if (res !== undefined) return res === sSkip ? undefined : res\n    for (const k of Object.keys(sub)) {\n      if (!specialChilds && !Array.isArray(sub) && !knownKeywords.includes(k)) continue\n      if (!specialChilds && skipChilds.includes(k)) continue\n      const kres = visit(sub[k], !specialChilds && withSpecialChilds.includes(k))\n      if (kres !== undefined) return kres\n    }\n  }\n  return visit(schema)\n}\n\n// Returns a list of resolved entries, in a form: [schema, root, basePath]\n// basePath doesn't contain the target object $id itself\nfunction resolveReference(root, schemas, ref, base = '') {\n  const ptr = joinPath(base, ref)\n  const results = []\n\n  const [main, hash = ''] = ptr.split('#')\n  const local = decodeURI(hash)\n\n  // Find in self by id path\n  const visit = (sub, oldPath, specialChilds = false, dynamic = false) => {\n    if (!sub || typeof sub !== 'object') return\n\n    const id = sub.$id || sub.id\n    let path = oldPath\n    if (id && typeof id === 'string') {\n      path = joinPath(path, id)\n      if (path === ptr || (path === main && local === '')) {\n        results.push([sub, root, oldPath])\n      } else if (path === main && local[0] === '/') {\n        const objpath = []\n        const res = get(sub, local, objpath)\n        if (res !== undefined) results.push([res, root, joinPath(oldPath, objpath2path(objpath))])\n      }\n    }\n    const anchor = dynamic ? sub.$dynamicAnchor : sub.$anchor\n    if (anchor && typeof anchor === 'string') {\n      if (anchor.includes('#')) throw new Error(\"$anchor can't include '#'\")\n      if (anchor.startsWith('/')) throw new Error(\"$anchor can't start with '/'\")\n      path = joinPath(path, `#${anchor}`)\n      if (path === ptr) results.push([sub, root, oldPath])\n    }\n\n    for (const k of Object.keys(sub)) {\n      if (!specialChilds && !Array.isArray(sub) && !knownKeywords.includes(k)) continue\n      if (!specialChilds && skipChilds.includes(k)) continue\n      visit(sub[k], path, !specialChilds && withSpecialChilds.includes(k))\n    }\n    if (!dynamic && sub.$dynamicAnchor) visit(sub, oldPath, specialChilds, true)\n  }\n  visit(root, main)\n\n  // Find in self by pointer\n  if (main === base.replace(/#$/, '') && (local[0] === '/' || local === '')) {\n    const objpath = []\n    const res = get(root, local, objpath)\n    if (res !== undefined) results.push([res, root, objpath2path(objpath)])\n  }\n\n  // Find in additional schemas\n  if (schemas.has(main) && schemas.get(main) !== root) {\n    const additional = resolveReference(schemas.get(main), schemas, `#${hash}`, main)\n    results.push(...additional.map(([res, rRoot, rPath]) => [res, rRoot, joinPath(main, rPath)]))\n  }\n\n  // Full refs to additional schemas\n  if (schemas.has(ptr)) results.push([schemas.get(ptr), schemas.get(ptr), ptr])\n\n  return results\n}\n\nfunction getDynamicAnchors(schema) {\n  const results = new Map()\n  traverse(schema, (sub) => {\n    if (sub !== schema && (sub.$id || sub.id)) return sSkip // base changed, no longer in the same resource\n    const anchor = sub.$dynamicAnchor\n    if (anchor && typeof anchor === 'string') {\n      if (anchor.includes('#')) throw new Error(\"$dynamicAnchor can't include '#'\")\n      if (!/^[a-zA-Z0-9_-]+$/.test(anchor)) throw new Error(`Unsupported $dynamicAnchor: ${anchor}`)\n      safeSet(results, anchor, sub, '$dynamicAnchor')\n    }\n  })\n  return results\n}\n\nconst hasKeywords = (schema, keywords) =>\n  traverse(schema, (s) => Object.keys(s).some((k) => keywords.includes(k)) || undefined) || false\n\nconst addSchemasArrayToMap = (schemas, input, optional = false) => {\n  if (!Array.isArray(input)) throw new Error('Expected an array of schemas')\n  // schema ids are extracted from the schemas themselves\n  for (const schema of input) {\n    traverse(schema, (sub) => {\n      const idRaw = sub.$id || sub.id\n      const id = idRaw && typeof idRaw === 'string' ? idRaw.replace(/#$/, '') : null // # is allowed only as the last symbol here\n      if (id && id.includes('://') && !id.includes('#')) {\n        safeSet(schemas, id, sub, \"schema $id in 'schemas'\")\n      } else if (sub === schema && !optional) {\n        throw new Error(\"Schema with missing or invalid $id in 'schemas'\")\n      }\n    })\n  }\n  return schemas\n}\n\nconst buildSchemas = (input, extra) => {\n  if (extra) return addSchemasArrayToMap(buildSchemas(input), extra, true)\n  if (input) {\n    switch (Object.getPrototypeOf(input)) {\n      case Object.prototype:\n        return new Map(Object.entries(input))\n      case Map.prototype:\n        return new Map(input)\n      case Array.prototype:\n        return addSchemasArrayToMap(new Map(), input)\n    }\n  }\n  throw new Error(\"Unexpected value for 'schemas' option\")\n}\n\nmodule.exports = { get, joinPath, resolveReference, getDynamicAnchors, hasKeywords, buildSchemas }\n", "'use strict'\n\nconst core = {\n  // matches ajv + length checks + does not start with a dot\n  // note that quoted emails are deliberately unsupported (as in ajv), who would want \\x01 in email\n  // first check is an additional fast path with lengths: 20+(1+21)*2 = 64, (1+61+1)+((1+60+1)+1)*3 = 252 < 253, that should cover most valid emails\n  // max length is 64 (name) + 1 (@) + 253 (host), we want to ensure that prior to feeding to the fast regex\n  // the second regex checks for quoted, starting-leading dot in name, and two dots anywhere\n  email: (input) => {\n    if (input.length > 318) return false\n    const fast = /^[a-z0-9!#$%&'*+/=?^_`{|}~-]{1,20}(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]{1,21}){0,2}@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\\.[a-z0-9](?:[a-z0-9-]{0,60}[a-z0-9])?){0,3}$/i\n    if (fast.test(input)) return true\n    if (!input.includes('@') || /(^\\.|^\"|\\.@|\\.\\.)/.test(input)) return false\n    const [name, host, ...rest] = input.split('@')\n    if (!name || !host || rest.length !== 0 || name.length > 64 || host.length > 253) return false\n    if (!/^[a-z0-9.-]+$/i.test(host) || !/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+$/i.test(name)) return false\n    return host.split('.').every((part) => /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$/i.test(part))\n  },\n  // matches ajv + length checks\n  hostname: (input) => {\n    if (input.length > (input.endsWith('.') ? 254 : 253)) return false\n    const hostname = /^[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*\\.?$/i\n    return hostname.test(input)\n  },\n\n  // 'time' matches ajv + length checks, 'date' matches ajv full\n  // date: https://tools.ietf.org/html/rfc3339#section-5.6\n  // date-time: https://tools.ietf.org/html/rfc3339#section-5.6\n  // leap year: https://tools.ietf.org/html/rfc3339#appendix-C\n  // 11: 1990-01-01, 1: T, 9: 00:00:00., 12: maxiumum fraction length (non-standard), 6: +00:00\n  date: (input) => {\n    if (input.length !== 10) return false\n    if (input[5] === '0' && input[6] === '2') {\n      if (/^\\d\\d\\d\\d-02-(?:[012][1-8]|[12]0|[01]9)$/.test(input)) return true\n      const matches = input.match(/^(\\d\\d\\d\\d)-02-29$/)\n      if (!matches) return false\n      const year = matches[1] | 0\n      return year % 16 === 0 || (year % 4 === 0 && year % 25 !== 0)\n    }\n    if (input.endsWith('31')) return /^\\d\\d\\d\\d-(?:0[13578]|1[02])-31$/.test(input)\n    return /^\\d\\d\\d\\d-(?:0[13-9]|1[012])-(?:[012][1-9]|[123]0)$/.test(input)\n  },\n  // leap second handling is special, we check it's 23:59:60.*\n  time: (input) => {\n    if (input.length > 9 + 12 + 6) return false\n    const time = /^(?:2[0-3]|[0-1]\\d):[0-5]\\d:(?:[0-5]\\d|60)(?:\\.\\d+)?(?:z|[+-](?:2[0-3]|[0-1]\\d)(?::?[0-5]\\d)?)?$/i\n    if (!time.test(input)) return false\n    if (!/:60/.test(input)) return true\n    const p = input.match(/([0-9.]+|[^0-9.])/g)\n    let hm = Number(p[0]) * 60 + Number(p[2])\n    if (p[5] === '+') hm += 24 * 60 - Number(p[6] || 0) * 60 - Number(p[8] || 0)\n    else if (p[5] === '-') hm += Number(p[6] || 0) * 60 + Number(p[8] || 0)\n    return hm % (24 * 60) === 23 * 60 + 59\n  },\n  // first two lines specific to date-time, then tests for unanchored (at end) date, code identical to 'date' above\n  // input[17] === '6' is a check for :60\n  'date-time': (input) => {\n    if (input.length > 10 + 1 + 9 + 12 + 6) return false\n    const full = /^\\d\\d\\d\\d-(?:0[1-9]|1[0-2])-(?:[0-2]\\d|3[01])[t\\s](?:2[0-3]|[0-1]\\d):[0-5]\\d:(?:[0-5]\\d|60)(?:\\.\\d+)?(?:z|[+-](?:2[0-3]|[0-1]\\d)(?::?[0-5]\\d)?)$/i\n    const feb = input[5] === '0' && input[6] === '2'\n    if ((feb && input[8] === '3') || !full.test(input)) return false\n    if (input[17] === '6') {\n      const p = input.slice(11).match(/([0-9.]+|[^0-9.])/g)\n      let hm = Number(p[0]) * 60 + Number(p[2])\n      if (p[5] === '+') hm += 24 * 60 - Number(p[6] || 0) * 60 - Number(p[8] || 0)\n      else if (p[5] === '-') hm += Number(p[6] || 0) * 60 + Number(p[8] || 0)\n      if (hm % (24 * 60) !== 23 * 60 + 59) return false\n    }\n    if (feb) {\n      if (/^\\d\\d\\d\\d-02-(?:[012][1-8]|[12]0|[01]9)/.test(input)) return true\n      const matches = input.match(/^(\\d\\d\\d\\d)-02-29/)\n      if (!matches) return false\n      const year = matches[1] | 0\n      return year % 16 === 0 || (year % 4 === 0 && year % 25 !== 0)\n    }\n    if (input[8] === '3' && input[9] === '1') return /^\\d\\d\\d\\d-(?:0[13578]|1[02])-31/.test(input)\n    return /^\\d\\d\\d\\d-(?:0[13-9]|1[012])-(?:[012][1-9]|[123]0)/.test(input)\n  },\n\n  /* ipv4 and ipv6 are from ajv with length restriction */\n  // optimized https://www.safaribooksonline.com/library/view/regular-expressions-cookbook/9780596802837/ch07s16.html\n  ipv4: (ip) =>\n    ip.length <= 15 &&\n    /^(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d\\d?)$/.test(ip),\n  // optimized http://stackoverflow.com/questions/53497/regular-expression-that-matches-valid-ipv6-addresses\n  // max length: 1000:1000:1000:1000:1000:1000:***************\n  // we parse ip6 format with a simple scan, leaving embedded ipv4 validation to a regex\n  // s0=count(:), s1=count(.), hex=count(a-zA-Z0-9), short=count(::)>0\n  // 48-57: '0'-'9', 97-102, 65-70: 'a'-'f', 'A'-'F', 58: ':', 46: '.'\n  /* eslint-disable one-var */\n  // prettier-ignore\n  ipv6: (input) => {\n    if (input.length > 45 || input.length < 2) return false\n    let s0 = 0, s1 = 0, hex = 0, short = false, letters = false, last = 0, start = true\n    for (let i = 0; i < input.length; i++) {\n      const c = input.charCodeAt(i)\n      if (i === 1 && last === 58 && c !== 58) return false\n      if (c >= 48 && c <= 57) {\n        if (++hex > 4) return false\n      } else if (c === 46) {\n        if (s0 > 6 || s1 >= 3 || hex === 0 || letters) return false\n        s1++\n        hex = 0\n      } else if (c === 58) {\n        if (s1 > 0 || s0 >= 7) return false\n        if (last === 58) {\n          if (short) return false\n          short = true\n        } else if (i === 0) start = false\n        s0++\n        hex = 0\n        letters = false\n      } else if ((c >= 97 && c <= 102) || (c >= 65 && c <= 70)) {\n        if (s1 > 0) return false\n        if (++hex > 4) return false\n        letters = true\n      } else return false\n      last = c\n    }\n    if (s0 < 2 || (s1 > 0 && (s1 !== 3 || hex === 0))) return false\n    if (short && input.length === 2) return true\n    if (s1 > 0 && !/(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}$/.test(input)) return false\n    const spaces = s1 > 0 ? 6 : 7\n    if (!short) return s0 === spaces && start && hex > 0\n    return (start || hex > 0) && s0 < spaces\n  },\n  /* eslint-enable one-var */\n  // matches ajv with optimization\n  uri: /^[a-z][a-z0-9+\\-.]*:(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|v[0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\\/?(?:(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?)(?:\\?(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,\n  // matches ajv with optimization\n  'uri-reference': /^(?:[a-z][a-z0-9+\\-.]*:)?(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|v[0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\\/?(?:(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?)?(?:\\?(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,\n  // ajv has /^(([^\\x00-\\x20\"'<>%\\\\^`{|}]|%[0-9a-f]{2})|\\{[+#./;?&=,!@|]?([a-z0-9_]|%[0-9a-f]{2})+(:[1-9][0-9]{0,3}|\\*)?(,([a-z0-9_]|%[0-9a-f]{2})+(:[1-9][0-9]{0,3}|\\*)?)*\\})*$/i\n  // this is equivalent\n  // uri-template: https://tools.ietf.org/html/rfc6570\n  // eslint-disable-next-line no-control-regex\n  'uri-template': /^(?:[^\\x00-\\x20\"'<>%\\\\^`{|}]|%[0-9a-f]{2}|\\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?)*\\})*$/i,\n\n  // ajv has /^(\\/([^~/]|~0|~1)*)*$/, this is equivalent\n  // JSON-pointer: https://tools.ietf.org/html/rfc6901\n  'json-pointer': /^(?:|\\/(?:[^~]|~0|~1)*)$/,\n  // ajv has /^(0|[1-9][0-9]*)(#|(\\/([^~/]|~0|~1)*)*)$/, this is equivalent\n  // relative JSON-pointer: http://tools.ietf.org/html/draft-luff-relative-json-pointer-00\n  'relative-json-pointer': /^(?:0|[1-9][0-9]*)(?:|#|\\/(?:[^~]|~0|~1)*)$/,\n\n  // uuid: http://tools.ietf.org/html/rfc4122\n  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,\n\n  // length restriction is an arbitrary safeguard\n  // first regex checks if this a week duration (can't be combined with others)\n  // second regex verifies symbols, no more than one fraction, at least 1 block is present, and T is not last\n  // third regex verifies structure\n  duration: (input) =>\n    input.length > 1 &&\n    input.length < 80 &&\n    (/^P\\d+([.,]\\d+)?W$/.test(input) ||\n      (/^P[\\dYMDTHS]*(\\d[.,]\\d+)?[YMDHS]$/.test(input) &&\n        /^P([.,\\d]+Y)?([.,\\d]+M)?([.,\\d]+D)?(T([.,\\d]+H)?([.,\\d]+M)?([.,\\d]+S)?)?$/.test(input))),\n\n  // TODO: iri, iri-reference, idn-email, idn-hostname\n}\n\nconst extra = {\n  // basic\n  alpha: /^[a-zA-Z]+$/,\n  alphanumeric: /^[a-zA-Z0-9]+$/,\n\n  // hex\n  'hex-digits': /^[0-9a-f]+$/i,\n  'hex-digits-prefixed': /^0x[0-9a-f]+$/i,\n  'hex-bytes': /^([0-9a-f][0-9a-f])+$/i,\n  'hex-bytes-prefixed': /^0x([0-9a-f][0-9a-f])+$/i,\n\n  base64: (input) => input.length % 4 === 0 && /^[a-z0-9+/]*={0,3}$/i.test(input),\n\n  // ajv has /^#(\\/([a-z0-9_\\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i, this is equivalent\n  // uri fragment: https://tools.ietf.org/html/rfc3986#appendix-A\n  'json-pointer-uri-fragment': /^#(|\\/(\\/|[a-z0-9_\\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)$/i,\n\n  // draft3 backwards compat\n  'host-name': core.hostname,\n  'ip-address': core.ipv4,\n\n  // manually cleaned up from is-my-json-valid, CSS 2.1 colors only per draft03 spec\n  color: /^(#[0-9A-Fa-f]{3,6}|aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow|rgb\\(\\s*([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\s*,\\s*([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\s*,\\s*([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\\s*\\)|rgb\\(\\s*(\\d?\\d%|100%)\\s*,\\s*(\\d?\\d%|100%)\\s*,\\s*(\\d?\\d%|100%)\\s*\\))$/,\n\n  // style is deliberately unsupported, don't accept untrusted styles\n}\n\nconst weak = {\n  // In weak because don't accept regexes from untrusted sources, using them can cause DoS\n  // matches ajv + length checks\n  // eslint comment outside because we don't want comments in functions, those affect output\n  /* eslint-disable no-new */\n  regex: (str) => {\n    if (str.length > 1e5) return false\n    const Z_ANCHOR = /[^\\\\]\\\\Z/\n    if (Z_ANCHOR.test(str)) return false\n    try {\n      new RegExp(str, 'u')\n      return true\n    } catch (e) {\n      return false\n    }\n  },\n  /* eslint-enable no-new */\n}\n\nmodule.exports = { core, extra, weak }\n", "'use strict'\n\n/* This file implements operations for static tracing of evaluated items/properties, which is also\n * used to determine whether dynamic evaluated tracing is required or the schema can be compiled\n * with only statical checks.\n *\n * That is done by keeping track of evaluated and potentially evaluated and accounting to that\n * while doing merges and intersections.\n *\n * isDynamic() checks that all potentially evaluated are also definitely evaluated, seperately\n * for items and properties, for use with unevaluatedItems and unevaluatedProperties.\n *\n * WARNING: it is important that this doesn't produce invalid information. i.e.:\n *  * Extra properties or patterns, too high items\n *  * Missing dyn.properties or dyn.patterns, too low dyn.items\n *  * Extra fullstring flag or required entries\n *  * Missing types, if type is present\n *  * Missing unknown or dyn.item\n *\n * The other way around is non-optimal but safe.\n *\n * null means any type (i.e. any type is possible, not validated)\n * true in properties means any property (i.e. all properties were evaluated)\n * fullstring means that the object is not an unvalidated string (i.e. is either validated or not a string)\n * unknown means that there could be evaluated items or properties unknown to both top-level or dyn\n * dyn.item (bool) means there could be possible specific evaluated items, e.g. from \"contains\".\n *\n * For normalization:\n *   1. If type is applicable:\n *     * dyn.items >= items,\n *     * dyn.properties includes properties\n *     * dyn.patterns includes patterns.\n *   2. If type is not applicable, the following rules apply:\n *     * `fullstring = true` if `string` type is not applicable\n *     * `items = Infinity`, `dyn.item = false`, `dyn.items = 0` if `array` type is not applicable\n *     * `properties = [true]`, `dyn.properties = []` if `object` type is not applicable\n *     * `patterns = dyn.patterns = []` if `object` type is not applicable\n *     * `required = []` if `object` type is not applicable\n *\n * That allows to simplify the `or` operation.\n */\n\nconst merge = (a, b) => [...new Set([...a, ...b])].sort()\nconst intersect = (a, b) => a.filter((x) => b.includes(x))\nconst wrapArgs = (f) => (...args) => f(...args.map(normalize))\nconst wrapFull = (f) => (...args) => normalize(f(...args.map(normalize)))\nconst typeIsNot = (type, t) => type && !type.includes(t) // type=null means any and includes anything\n\nconst normalize = ({ type = null, dyn: d = {}, ...A }) => ({\n  type: type ? [...type].sort() : type,\n  items: typeIsNot(type, 'array') ? Infinity : A.items || 0,\n  properties: typeIsNot(type, 'object') ? [true] : [...(A.properties || [])].sort(),\n  patterns: typeIsNot(type, 'object') ? [] : [...(A.patterns || [])].sort(),\n  required: typeIsNot(type, 'object') ? [] : [...(A.required || [])].sort(),\n  fullstring: typeIsNot(type, 'string') || A.fullstring || false,\n  dyn: {\n    item: typeIsNot(type, 'array') ? false : d.item || false,\n    items: typeIsNot(type, 'array') ? 0 : Math.max(A.items || 0, d.items || 0),\n    properties: typeIsNot(type, 'object') ? [] : merge(A.properties || [], d.properties || []),\n    patterns: typeIsNot(type, 'object') ? [] : merge(A.patterns || [], d.patterns || []),\n  },\n  unknown: (A.unknown && !(typeIsNot(type, 'object') && typeIsNot(type, 'array'))) || false,\n})\n\nconst initTracing = () => normalize({})\n\n// Result means that both sets A and B are correct\n// type is intersected, lists of known properties are merged\nconst andDelta = wrapFull((A, B) => ({\n  type: A.type && B.type ? intersect(A.type, B.type) : A.type || B.type || null,\n  items: Math.max(A.items, B.items),\n  properties: merge(A.properties, B.properties),\n  patterns: merge(A.patterns, B.patterns),\n  required: merge(A.required, B.required),\n  fullstring: A.fullstring || B.fullstring,\n  dyn: {\n    item: A.dyn.item || B.dyn.item,\n    items: Math.max(A.dyn.items, B.dyn.items),\n    properties: merge(A.dyn.properties, B.dyn.properties),\n    patterns: merge(A.dyn.patterns, B.dyn.patterns),\n  },\n  unknown: A.unknown || B.unknown,\n}))\n\nconst regtest = (pattern, value) => value !== true && new RegExp(pattern, 'u').test(value)\n\nconst intersectProps = ({ properties: a, patterns: rega }, { properties: b, patterns: regb }) => {\n  // properties\n  const af = a.filter((x) => b.includes(x) || b.includes(true) || regb.some((p) => regtest(p, x)))\n  const bf = b.filter((x) => a.includes(x) || a.includes(true) || rega.some((p) => regtest(p, x)))\n  // patterns\n  const ar = rega.filter((x) => regb.includes(x) || b.includes(true))\n  const br = regb.filter((x) => rega.includes(x) || a.includes(true))\n  return { properties: merge(af, bf), patterns: merge(ar, br) }\n}\n\nconst inProperties = ({ properties: a, patterns: rega }, { properties: b, patterns: regb }) =>\n  b.every((x) => a.includes(x) || a.includes(true) || rega.some((p) => regtest(p, x))) &&\n  regb.every((x) => rega.includes(x) || a.includes(true))\n\n// Result means that at least one of sets A and B is correct\n// type is merged, lists of known properties are intersected, lists of dynamic properties are merged\nconst orDelta = wrapFull((A, B) => ({\n  type: A.type && B.type ? merge(A.type, B.type) : null,\n  items: Math.min(A.items, B.items),\n  ...intersectProps(A, B),\n  required:\n    (typeIsNot(A.type, 'object') && B.required) ||\n    (typeIsNot(B.type, 'object') && A.required) ||\n    intersect(A.required, B.required),\n  fullstring: A.fullstring && B.fullstring,\n  dyn: {\n    item: A.dyn.item || B.dyn.item,\n    items: Math.max(A.dyn.items, B.dyn.items),\n    properties: merge(A.dyn.properties, B.dyn.properties),\n    patterns: merge(A.dyn.patterns, B.dyn.patterns),\n  },\n  unknown: A.unknown || B.unknown,\n}))\n\nconst applyDelta = (stat, delta) => Object.assign(stat, andDelta(stat, delta))\n\nconst isDynamic = wrapArgs(({ unknown, items, dyn, ...stat }) => ({\n  items: items !== Infinity && (unknown || dyn.items > items || dyn.item),\n  properties: !stat.properties.includes(true) && (unknown || !inProperties(stat, dyn)),\n}))\n\nmodule.exports = { initTracing, andDelta, orDelta, applyDelta, isDynamic, inProperties }\n", "'use strict'\n\nconst { format, safe, safeand, safenot, safenotor } = require('./safe-format')\nconst genfun = require('./generate-function')\nconst { resolveReference, joinPath, getDynamicAnchors, hasKeywords } = require('./pointer')\nconst formats = require('./formats')\nconst { toPointer, ...functions } = require('./scope-functions')\nconst { scopeMethods } = require('./scope-utils')\nconst { buildName, types, jsHelpers } = require('./javascript')\nconst { knownKeywords, schemaVersions, knownVocabularies } = require('./known-keywords')\nconst { initTracing, andDelta, orDelta, applyDelta, isDynamic, inProperties } = require('./tracing')\n\nconst noopRegExps = new Set(['^[\\\\s\\\\S]*$', '^[\\\\S\\\\s]*$', '^[^]*$', '', '.*', '^', '$'])\nconst primitiveTypes = ['null', 'boolean', 'number', 'integer', 'string']\n\n// for checking schema parts in consume()\nconst schemaTypes = new Map(\n  Object.entries({\n    boolean: (arg) => typeof arg === 'boolean',\n    array: (arg) => Array.isArray(arg) && Object.getPrototypeOf(arg) === Array.prototype,\n    object: (arg) => arg && Object.getPrototypeOf(arg) === Object.prototype,\n    finite: (arg) => Number.isFinite(arg),\n    natural: (arg) => Number.isInteger(arg) && arg >= 0,\n    string: (arg) => typeof arg === 'string',\n    jsonval: (arg) => functions.deepEqual(arg, JSON.parse(JSON.stringify(arg))),\n  })\n)\nconst isPlainObject = schemaTypes.get('object')\nconst isSchemaish = (arg) => isPlainObject(arg) || typeof arg === 'boolean'\nconst deltaEmpty = (delta) => functions.deepEqual(delta, { type: [] })\n\nconst schemaIsOlderThan = ($schema, ver) =>\n  schemaVersions.indexOf($schema) > schemaVersions.indexOf(`https://json-schema.org/${ver}/schema`)\n\nconst schemaIsUnkownOrOlder = ($schema, ver) => {\n  const normalized = `${$schema}`.replace(/^http:\\/\\//, 'https://').replace(/#$/, '')\n  if (!schemaVersions.includes(normalized)) return true\n  return schemaIsOlderThan(normalized, ver)\n}\n\n// Helper methods for semi-structured paths\nconst propvar = (parent, keyname, inKeys = false, number = false) =>\n  Object.freeze({ parent, keyname, inKeys, number }) // property by variable\nconst propimm = (parent, keyval, checked = false) => Object.freeze({ parent, keyval, checked }) // property by immediate value\n\nconst evaluatedStatic = Symbol('evaluatedStatic')\nconst optDynamic = Symbol('optDynamic')\nconst optDynAnchors = Symbol('optDynAnchors')\nconst optRecAnchors = Symbol('optRecAnchors')\n\nconst constantValue = (schema) => {\n  if (typeof schema === 'boolean') return schema\n  if (isPlainObject(schema) && Object.keys(schema).length === 0) return true\n  return undefined\n}\n\nconst refsNeedFullValidation = new Set() // cleared before and after each full compilation\nconst rootMeta = new Map() // cleared before and after each full compilation\nconst generateMeta = (root, $schema, enforce, requireSchema) => {\n  if ($schema) {\n    const version = $schema.replace(/^http:\\/\\//, 'https://').replace(/#$/, '')\n    enforce(schemaVersions.includes(version), 'Unexpected schema version:', version)\n    rootMeta.set(root, {\n      exclusiveRefs: schemaIsOlderThan(version, 'draft/2019-09'),\n      contentValidation: schemaIsOlderThan(version, 'draft/2019-09'),\n      dependentUnsupported: schemaIsOlderThan(version, 'draft/2019-09'),\n      newItemsSyntax: !schemaIsOlderThan(version, 'draft/2020-12'),\n      containsEvaluates: !schemaIsOlderThan(version, 'draft/2020-12'),\n      objectContains: !schemaIsOlderThan(version, 'draft/next'),\n      bookending: schemaIsOlderThan(version, 'draft/next'),\n    })\n  } else {\n    enforce(!requireSchema, '[requireSchema] $schema is required')\n    rootMeta.set(root, {})\n  }\n}\n\nconst compileSchema = (schema, root, opts, scope, basePathRoot = '') => {\n  const {\n    mode = 'default',\n    useDefaults = false,\n    removeAdditional = false, // supports additionalProperties: false and additionalItems: false\n    includeErrors = false,\n    allErrors = false,\n    contentValidation,\n    dryRun, // unused, just for rest siblings\n    lint: lintOnly = false,\n    allowUnusedKeywords = opts.mode === 'lax' || opts.mode === 'spec',\n    allowUnreachable = opts.mode === 'lax' || opts.mode === 'spec',\n    requireSchema = opts.mode === 'strong',\n    requireValidation = opts.mode === 'strong',\n    requireStringValidation = opts.mode === 'strong',\n    forbidNoopValues = opts.mode === 'strong', // e.g. $recursiveAnchor: false (it's false by default)\n    complexityChecks = opts.mode === 'strong',\n    unmodifiedPrototypes = false, // assumes no mangled Object/Array prototypes\n    isJSON = false, // assume input to be JSON, which e.g. makes undefined impossible\n    $schemaDefault = null,\n    formatAssertion = opts.mode !== 'spec' || schemaIsUnkownOrOlder(root.$schema, 'draft/2019-09'),\n    formats: optFormats = {},\n    weakFormats = opts.mode !== 'strong',\n    extraFormats = false,\n    schemas, // always a Map, produced at wrapper\n    ...unknown\n  } = opts\n  const fmts = {\n    ...formats.core,\n    ...(weakFormats ? formats.weak : {}),\n    ...(extraFormats ? formats.extra : {}),\n    ...optFormats,\n  }\n  if (Object.keys(unknown).length !== 0)\n    throw new Error(`Unknown options: ${Object.keys(unknown).join(', ')}`)\n\n  if (!['strong', 'lax', 'default', 'spec'].includes(mode)) throw new Error(`Invalid mode: ${mode}`)\n  if (!includeErrors && allErrors) throw new Error('allErrors requires includeErrors to be enabled')\n  if (requireSchema && $schemaDefault) throw new Error('requireSchema forbids $schemaDefault')\n  if (mode === 'strong') {\n    const validation = { requireValidation, requireStringValidation }\n    const strong = { ...validation, formatAssertion, complexityChecks, requireSchema }\n    const weak = { weakFormats, allowUnusedKeywords }\n    for (const [k, v] of Object.entries(strong)) if (!v) throw new Error(`Strong mode demands ${k}`)\n    for (const [k, v] of Object.entries(weak)) if (v) throw new Error(`Strong mode forbids ${k}`)\n  }\n\n  const { gensym, getref, genref, genformat } = scopeMethods(scope)\n\n  const buildPath = (prop) => {\n    const path = []\n    let curr = prop\n    while (curr) {\n      if (!curr.name) path.unshift(curr)\n      curr = curr.parent || curr.errorParent\n    }\n\n    // fast case when there are no variables inside path\n    if (path.every((part) => part.keyval !== undefined))\n      return format('%j', toPointer(path.map((part) => part.keyval)))\n\n    // Be very careful while refactoring, this code significantly affects includeErrors performance\n    // It attempts to construct fast code presentation for paths, e.g. \"#/abc/\"+pointerPart(key0)+\"/items/\"+i0\n    const stringParts = ['#']\n    const stringJoined = () => {\n      const value = stringParts.map(functions.pointerPart).join('/')\n      stringParts.length = 0\n      return value\n    }\n    let res = null\n    for (const { keyname, keyval, number } of path) {\n      if (keyname) {\n        if (!number) scope.pointerPart = functions.pointerPart\n        const value = number ? keyname : format('pointerPart(%s)', keyname)\n        const str = `${stringJoined()}/`\n        res = res ? format('%s+%j+%s', res, str, value) : format('%j+%s', str, value)\n      } else if (keyval) stringParts.push(keyval)\n    }\n    return stringParts.length > 0 ? format('%s+%j', res, `/${stringJoined()}`) : res\n  }\n\n  const funname = genref(schema)\n  let validate = null // resolve cyclic dependencies\n  const wrap = (...args) => {\n    const res = validate(...args)\n    wrap.errors = validate.errors\n    return res\n  }\n  scope[funname] = wrap\n\n  const hasRefs = hasKeywords(schema, ['$ref', '$recursiveRef', '$dynamicRef'])\n  const hasDynAnchors = opts[optDynAnchors] && hasRefs && hasKeywords(schema, ['$dynamicAnchor'])\n  const dynAnchorsHead = () => {\n    if (!opts[optDynAnchors]) return format('')\n    return hasDynAnchors ? format(', dynAnchors = []') : format(', dynAnchors')\n  }\n  const recAnchorsHead = opts[optRecAnchors] ? format(', recursive') : format('')\n\n  const fun = genfun()\n  fun.write('function validate(data%s%s) {', recAnchorsHead, dynAnchorsHead())\n  if (includeErrors) fun.write('validate.errors = null')\n  if (allErrors) fun.write('let errorCount = 0')\n  if (opts[optDynamic]) fun.write('validate.evaluatedDynamic = null')\n\n  let dynamicAnchorsNext = opts[optDynAnchors] ? format(', dynAnchors') : format('')\n  if (hasDynAnchors) {\n    fun.write('const dynLocal = [{}]')\n    dynamicAnchorsNext = format(', [...dynAnchors, dynLocal[0] || []]')\n  }\n\n  const helpers = jsHelpers(fun, scope, propvar, { unmodifiedPrototypes, isJSON }, noopRegExps)\n  const { present, forObjectKeys, forArray, patternTest, compare } = helpers\n\n  const recursiveLog = []\n  const getMeta = () => rootMeta.get(root)\n  const basePathStack = basePathRoot ? [basePathRoot] : []\n  const visit = (errors, history, current, node, schemaPath, trace = {}, { constProp } = {}) => {\n    // e.g. top-level data and property names, OR already checked by present() in history, OR in keys and not undefined\n    const isSub = history.length > 0 && history[history.length - 1].prop === current\n    const queryCurrent = () => history.filter((h) => h.prop === current)\n    const definitelyPresent =\n      !current.parent || current.checked || (current.inKeys && isJSON) || queryCurrent().length > 0\n\n    const name = buildName(current)\n    const currPropImm = (...args) => propimm(current, ...args)\n\n    const error = ({ path = [], prop = current, source, suberr }) => {\n      const schemaP = toPointer([...schemaPath, ...path])\n      const dataP = includeErrors ? buildPath(prop) : null\n      if (includeErrors === true && errors && source) {\n        // we can include absoluteKeywordLocation later, perhaps\n        scope.errorMerge = functions.errorMerge\n        const args = [source, schemaP, dataP]\n        if (allErrors) {\n          fun.write('if (validate.errors === null) validate.errors = []')\n          fun.write('validate.errors.push(...%s.map(e => errorMerge(e, %j, %s)))', ...args)\n        } else fun.write('validate.errors = [errorMerge(%s[0], %j, %s)]', ...args)\n      } else if (includeErrors === true && errors) {\n        const errorJS = format('{ keywordLocation: %j, instanceLocation: %s }', schemaP, dataP)\n        if (allErrors) {\n          fun.write('if (%s === null) %s = []', errors, errors)\n          fun.write('%s.push(%s)', errors, errorJS)\n        } else fun.write('%s = [%s]', errors, errorJS) // Array assignment is significantly faster, do not refactor the two branches\n      }\n      if (suberr) mergeerror(suberr) // can only happen in allErrors\n      if (allErrors) fun.write('errorCount++')\n      else fun.write('return false')\n    }\n    const errorIf = (condition, errorArgs) => fun.if(condition, () => error(errorArgs))\n\n    if (lintOnly && !scope.lintErrors) scope.lintErrors = [] // we can do this as we don't build functions in lint-only mode\n    const fail = (msg, value) => {\n      const comment = value !== undefined ? ` ${JSON.stringify(value)}` : ''\n      const keywordLocation = joinPath(basePathRoot, toPointer(schemaPath))\n      const message = `${msg}${comment} at ${keywordLocation}`\n      if (lintOnly) return scope.lintErrors.push({ message, keywordLocation, schema }) // don't fail if we are just collecting all errors\n      throw new Error(message)\n    }\n    const patternTestSafe = (pat, key) => {\n      try {\n        return patternTest(pat, key)\n      } catch (e) {\n        fail(e.message)\n        return format('false') // for lint-only mode\n      }\n    }\n    const enforce = (ok, ...args) => ok || fail(...args)\n    const laxMode = (ok, ...args) => enforce(mode === 'lax' || mode === 'spec' || ok, ...args)\n    const enforceMinMax = (a, b) => laxMode(!(node[b] < node[a]), `Invalid ${a} / ${b} combination`)\n    const enforceValidation = (msg, suffix = 'should be specified') =>\n      enforce(!requireValidation, `[requireValidation] ${msg} ${suffix}`)\n    const subPath = (...args) => [...schemaPath, ...args]\n    const uncertain = (msg) =>\n      enforce(!removeAdditional && !useDefaults, `[removeAdditional/useDefaults] uncertain: ${msg}`)\n    const complex = (msg, arg) => enforce(!complexityChecks, `[complexityChecks] ${msg}`, arg)\n    const saveMeta = ($sch) => generateMeta(root, $sch || $schemaDefault, enforce, requireSchema)\n\n    // evaluated tracing\n    const stat = initTracing()\n    const evaluateDelta = (delta) => applyDelta(stat, delta)\n\n    if (typeof node === 'boolean') {\n      if (node === true) {\n        enforceValidation('schema = true', 'is not allowed') // any is valid here\n        return { stat } // nothing is evaluated for true\n      }\n      errorIf(definitelyPresent || current.inKeys ? true : present(current), {}) // node === false\n      evaluateDelta({ type: [] }) // everything is evaluated for false\n      return { stat }\n    }\n\n    enforce(isPlainObject(node), 'Schema is not an object')\n    for (const key of Object.keys(node))\n      enforce(knownKeywords.includes(key) || allowUnusedKeywords, 'Keyword not supported:', key)\n\n    if (Object.keys(node).length === 0) {\n      enforceValidation('empty rules node', 'is not allowed')\n      return { stat } // nothing to validate here, basically the same as node === true\n    }\n\n    const unused = new Set(Object.keys(node))\n    const multiConsumable = new Set()\n    const consume = (prop, ...ruleTypes) => {\n      enforce(multiConsumable.has(prop) || unused.has(prop), 'Unexpected double consumption:', prop)\n      enforce(functions.hasOwn(node, prop), 'Is not an own property:', prop)\n      enforce(ruleTypes.every((t) => schemaTypes.has(t)), 'Invalid type used in consume')\n      enforce(ruleTypes.some((t) => schemaTypes.get(t)(node[prop])), 'Unexpected type for', prop)\n      unused.delete(prop)\n    }\n    const get = (prop, ...ruleTypes) => {\n      if (node[prop] !== undefined) consume(prop, ...ruleTypes)\n      return node[prop]\n    }\n    const handle = (prop, ruleTypes, handler, errorArgs = {}) => {\n      if (node[prop] === undefined) return false\n      // opt-out on null is explicit in both places here, don't set default\n      consume(prop, ...ruleTypes)\n      if (handler !== null) {\n        try {\n          const condition = handler(node[prop])\n          if (condition !== null) errorIf(condition, { path: [prop], ...errorArgs })\n        } catch (e) {\n          if (lintOnly && !e.message.startsWith('[opt] ')) {\n            fail(e.message) // for lint-only mode, but not processing special re-run errors\n          } else {\n            throw e\n          }\n        }\n      }\n      return true\n    }\n\n    if (node === root) {\n      saveMeta(get('$schema', 'string'))\n      handle('$vocabulary', ['object'], ($vocabulary) => {\n        for (const [vocab, flag] of Object.entries($vocabulary)) {\n          if (flag === false) continue\n          enforce(flag === true && knownVocabularies.includes(vocab), 'Unknown vocabulary:', vocab)\n        }\n        return null\n      })\n    } else if (!getMeta()) saveMeta(root.$schema)\n\n    if (getMeta().objectContains) {\n      // When object contains is enabled, contains-related keywords can be consumed two times: in object branch and in array branch\n      for (const prop of ['contains', 'minContains', 'maxContains']) multiConsumable.add(prop)\n    }\n\n    handle('examples', ['array'], null) // unused, meta-only\n    handle('example', ['jsonval'], null) // unused, meta-only, OpenAPI\n    for (const ignore of ['title', 'description', '$comment']) handle(ignore, ['string'], null) // unused, meta-only strings\n    for (const ignore of ['deprecated', 'readOnly', 'writeOnly']) handle(ignore, ['boolean'], null) // unused, meta-only flags\n\n    handle('$defs', ['object'], null) || handle('definitions', ['object'], null) // defs are allowed, those are validated on usage\n\n    const compileSub = (sub, subR, path) =>\n      sub === schema ? safe('validate') : getref(sub) || compileSchema(sub, subR, opts, scope, path)\n    const basePath = () => (basePathStack.length > 0 ? basePathStack[basePathStack.length - 1] : '')\n    const basePathStackLength = basePathStack.length // to restore at exit\n    const setId = ($id) => {\n      basePathStack.push(joinPath(basePath(), $id))\n      return null\n    }\n\n    // None of the below should be handled if an exlusive pre-2019-09 $ref is present\n    if (!getMeta().exclusiveRefs || !node.$ref) {\n      handle('$id', ['string'], setId) || handle('id', ['string'], setId)\n      handle('$anchor', ['string'], null) // $anchor is used only for ref resolution, on usage\n      handle('$dynamicAnchor', ['string'], null) // handled separately and on ref resolution\n\n      if (node.$recursiveAnchor || !forbidNoopValues) {\n        handle('$recursiveAnchor', ['boolean'], (isRecursive) => {\n          if (isRecursive) recursiveLog.push([node, root, basePath()])\n          return null\n        })\n      }\n    }\n\n    // handle schema-wide dynamic anchors\n    const isDynScope = hasDynAnchors && (node === schema || node.id || node.$id)\n    if (isDynScope) {\n      const allDynamic = getDynamicAnchors(node)\n      if (node !== schema) fun.write('dynLocal.unshift({})') // inlined at top level\n      for (const [key, subcheck] of allDynamic) {\n        const resolved = resolveReference(root, schemas, `#${key}`, basePath())\n        const [sub, subRoot, path] = resolved[0] || []\n        enforce(sub === subcheck, `Unexpected $dynamicAnchor resolution: ${key}`)\n        const n = compileSub(sub, subRoot, path)\n        fun.write('dynLocal[0][%j] = %s', `#${key}`, n)\n      }\n    }\n\n    // evaluated: declare dynamic\n    const needUnevaluated = (rule) =>\n      opts[optDynamic] && (node[rule] || node[rule] === false || node === schema)\n    const local = Object.freeze({\n      item: needUnevaluated('unevaluatedItems') ? gensym('evaluatedItem') : null,\n      items: needUnevaluated('unevaluatedItems') ? gensym('evaluatedItems') : null,\n      props: needUnevaluated('unevaluatedProperties') ? gensym('evaluatedProps') : null,\n    })\n    const dyn = Object.freeze({\n      item: local.item || trace.item,\n      items: local.items || trace.items,\n      props: local.props || trace.props,\n    })\n    const canSkipDynamic = () =>\n      (!dyn.items || stat.items === Infinity) && (!dyn.props || stat.properties.includes(true))\n    const evaluateDeltaDynamic = (delta) => {\n      // Skips applying those that have already been proved statically\n      if (dyn.item && delta.item && stat.items !== Infinity)\n        fun.write('%s.push(%s)', dyn.item, delta.item)\n      if (dyn.items && delta.items > stat.items) fun.write('%s.push(%d)', dyn.items, delta.items)\n      if (dyn.props && (delta.properties || []).includes(true) && !stat.properties.includes(true)) {\n        fun.write('%s[0].push(true)', dyn.props)\n      } else if (dyn.props) {\n        const inStat = (properties, patterns) => inProperties(stat, { properties, patterns })\n        const properties = (delta.properties || []).filter((x) => !inStat([x], []))\n        const patterns = (delta.patterns || []).filter((x) => !inStat([], [x]))\n        if (properties.length > 0) fun.write('%s[0].push(...%j)', dyn.props, properties)\n        if (patterns.length > 0) fun.write('%s[1].push(...%j)', dyn.props, patterns)\n        for (const sym of delta.propertiesVars || []) fun.write('%s[0].push(%s)', dyn.props, sym)\n      }\n    }\n    const applyDynamicToDynamic = (target, item, items, props) => {\n      if (isDynamic(stat).items && target.item && item)\n        fun.write('%s.push(...%s)', target.item, item)\n      if (isDynamic(stat).items && target.items && items)\n        fun.write('%s.push(...%s)', target.items, items)\n      if (isDynamic(stat).properties && target.props && props) {\n        fun.write('%s[0].push(...%s[0])', target.props, props)\n        fun.write('%s[1].push(...%s[1])', target.props, props)\n      }\n    }\n\n    const makeRecursive = () => {\n      if (!opts[optRecAnchors]) return format('') // recursive anchors disabled\n      if (recursiveLog.length === 0) return format(', recursive') // no recursive default, i.e. no $recursiveAnchor has been set in this schema\n      return format(', recursive || %s', compileSub(...recursiveLog[0]))\n    }\n    const applyRef = (n, errorArgs) => {\n      // evaluated: propagate static from ref to current, skips cyclic.\n      // Can do this before the call as the call is just a write\n      const delta = (scope[n] && scope[n][evaluatedStatic]) || { unknown: true } // assume unknown if ref is cyclic\n      evaluateDelta(delta)\n      const call = format('%s(%s%s%s)', n, name, makeRecursive(), dynamicAnchorsNext)\n      if (!includeErrors && canSkipDynamic()) return format('!%s', call) // simple case\n      const res = gensym('res')\n      const err = gensym('err') // Save and restore errors in case of recursion (if needed)\n      const suberr = gensym('suberr')\n      if (includeErrors) fun.write('const %s = validate.errors', err)\n      fun.write('const %s = %s', res, call)\n      if (includeErrors) fun.write('const %s = %s.errors', suberr, n)\n      if (includeErrors) fun.write('validate.errors = %s', err)\n      errorIf(safenot(res), { ...errorArgs, source: suberr })\n      // evaluated: propagate dynamic from ref to current\n      fun.if(res, () => {\n        const item = isDynamic(delta).items ? format('%s.evaluatedDynamic[0]', n) : null\n        const items = isDynamic(delta).items ? format('%s.evaluatedDynamic[1]', n) : null\n        const props = isDynamic(delta).properties ? format('%s.evaluatedDynamic[2]', n) : null\n        applyDynamicToDynamic(dyn, item, items, props)\n      })\n\n      return null\n    }\n\n    /* Preparation and methods, post-$ref validation will begin at the end of the function */\n\n    // This is used for typechecks, null means * here\n    const allIn = (arr, valid) => arr && arr.every((s) => valid.includes(s)) // all arr entries are in valid\n    const someIn = (arr, possible) => possible.some((x) => arr === null || arr.includes(x)) // all possible are in arrs\n\n    const parentCheckedType = (...valid) => queryCurrent().some((h) => allIn(h.stat.type, valid))\n    const definitelyType = (...valid) => allIn(stat.type, valid) || parentCheckedType(...valid)\n    const typeApplicable = (...possible) =>\n      someIn(stat.type, possible) && queryCurrent().every((h) => someIn(h.stat.type, possible))\n\n    const enforceRegex = (source, target = node) => {\n      enforce(typeof source === 'string', 'Invalid pattern:', source)\n      if (requireValidation || requireStringValidation)\n        enforce(/^\\^.*\\$$/.test(source), 'Should start with ^ and end with $:', source)\n      if (/([{+*].*[{+*]|\\)[{+*]|^[^^].*[{+*].)/.test(source) && target.maxLength === undefined)\n        complex('maxLength should be specified for pattern:', source)\n    }\n\n    // Those checks will need to be skipped if another error is set in this block before those ones\n    const havePattern = node.pattern && !noopRegExps.has(node.pattern) // we won't generate code for noop\n    const haveComplex = node.uniqueItems || havePattern || node.patternProperties || node.format\n    const prev = allErrors && haveComplex ? gensym('prev') : null\n    const prevWrap = (shouldWrap, writeBody) =>\n      fun.if(shouldWrap && prev !== null ? format('errorCount === %s', prev) : true, writeBody)\n\n    const nexthistory = () => [...history, { stat, prop: current }]\n    // Can not be used before undefined check! The one performed by present()\n    const rule = (...args) => visit(errors, nexthistory(), ...args).stat\n    const subrule = (suberr, ...args) => {\n      if (args[0] === current) {\n        const constval = constantValue(args[1])\n        if (constval === true) return { sub: format('true'), delta: {} }\n        if (constval === false) return { sub: format('false'), delta: { type: [] } }\n      }\n      const sub = gensym('sub')\n      fun.write('const %s = (() => {', sub)\n      if (allErrors) fun.write('let errorCount = 0') // scoped error counter\n      const { stat: delta } = visit(suberr, nexthistory(), ...args)\n      if (allErrors) {\n        fun.write('return errorCount === 0')\n      } else fun.write('return true')\n      fun.write('})()')\n      return { sub, delta }\n    }\n\n    const suberror = () => {\n      const suberr = includeErrors && allErrors ? gensym('suberr') : null\n      if (suberr) fun.write('let %s = null', suberr)\n      return suberr\n    }\n    const mergeerror = (suberr) => {\n      if (errors === null || suberr === null) return // suberror can be null e.g. on failed empty contains, errors can be null in e.g. not or if\n      fun.if(suberr, () => fun.write('%s.push(...%s)', errors, suberr))\n    }\n\n    // Extracted single additional(Items/Properties) rules, for reuse with unevaluated(Items/Properties)\n    const willRemoveAdditional = () => {\n      if (!removeAdditional) return false\n      if (removeAdditional === true) return true\n      if (removeAdditional === 'keyword') {\n        if (!node.removeAdditional) return false\n        consume('removeAdditional', 'boolean')\n        return true\n      }\n      throw new Error(`Invalid removeAdditional: ${removeAdditional}`)\n    }\n    const additionalItems = (rulePath, limit, extra) => {\n      const handled = handle(rulePath, ['object', 'boolean'], (ruleValue) => {\n        if (ruleValue === false && willRemoveAdditional()) {\n          fun.write('if (%s.length > %s) %s.length = %s', name, limit, name, limit)\n          return null\n        }\n        if (ruleValue === false && !extra) return format('%s.length > %s', name, limit)\n        forArray(current, limit, (prop, i) => {\n          if (extra) fun.write('if (%s) continue', extra(i))\n          return rule(prop, ruleValue, subPath(rulePath))\n        })\n        return null\n      })\n      if (handled) evaluateDelta({ items: Infinity })\n    }\n    const additionalProperties = (rulePath, condition) => {\n      const handled = handle(rulePath, ['object', 'boolean'], (ruleValue) => {\n        forObjectKeys(current, (sub, key) => {\n          fun.if(condition(key), () => {\n            if (ruleValue === false && willRemoveAdditional()) fun.write('delete %s[%s]', name, key)\n            else rule(sub, ruleValue, subPath(rulePath))\n          })\n        })\n        return null\n      })\n      if (handled) evaluateDelta({ properties: [true] })\n    }\n    const additionalCondition = (key, properties, patternProperties) =>\n      safeand(\n        ...properties.map((p) => format('%s !== %j', key, p)),\n        ...patternProperties.map((p) => safenot(patternTestSafe(p, key)))\n      )\n    const lintRequired = (properties, patterns) => {\n      const regexps = patterns.map((p) => new RegExp(p, 'u'))\n      const known = (key) => properties.includes(key) || regexps.some((r) => r.test(key))\n      for (const key of stat.required) enforce(known(key), `Unknown required property:`, key)\n    }\n    const finalLint = []\n\n    /* Checks inside blocks are independent, they are happening on the same code depth */\n\n    const checkNumbers = () => {\n      const minMax = (value, operator) => format('!(%d %c %s)', value, operator, name) // don't remove negation, accounts for NaN\n\n      if (Number.isFinite(node.exclusiveMinimum)) {\n        handle('exclusiveMinimum', ['finite'], (min) => minMax(min, '<'))\n      } else {\n        handle('minimum', ['finite'], (min) => minMax(min, node.exclusiveMinimum ? '<' : '<='))\n        handle('exclusiveMinimum', ['boolean'], null) // handled above\n      }\n\n      if (Number.isFinite(node.exclusiveMaximum)) {\n        handle('exclusiveMaximum', ['finite'], (max) => minMax(max, '>'))\n        enforceMinMax('minimum', 'exclusiveMaximum')\n        enforceMinMax('exclusiveMinimum', 'exclusiveMaximum')\n      } else if (node.maximum !== undefined) {\n        handle('maximum', ['finite'], (max) => minMax(max, node.exclusiveMaximum ? '>' : '>='))\n        handle('exclusiveMaximum', ['boolean'], null) // handled above\n        enforceMinMax('minimum', 'maximum')\n        enforceMinMax('exclusiveMinimum', 'maximum')\n      }\n\n      const multipleOf = node.multipleOf === undefined ? 'divisibleBy' : 'multipleOf' // draft3 support\n      handle(multipleOf, ['finite'], (value) => {\n        enforce(value > 0, `Invalid ${multipleOf}:`, value)\n        const [part, exp] = `${value}`.split('e-')\n        const frac = `${part}.`.split('.')[1]\n        const e = frac.length + (exp ? Number(exp) : 0)\n        if (Number.isInteger(value * 2 ** e)) return format('%s %% %d !== 0', name, value) // exact\n        scope.isMultipleOf = functions.isMultipleOf\n        const args = [name, value, e, Math.round(value * Math.pow(10, e))] // precompute for performance\n        return format('!isMultipleOf(%s, %d, 1e%d, %d)', ...args)\n      })\n    }\n\n    const checkStrings = () => {\n      handle('maxLength', ['natural'], (max) => {\n        scope.stringLength = functions.stringLength\n        return format('%s.length > %d && stringLength(%s) > %d', name, max, name, max)\n      })\n      handle('minLength', ['natural'], (min) => {\n        scope.stringLength = functions.stringLength\n        return format('%s.length < %d || stringLength(%s) < %d', name, min, name, min)\n      })\n      enforceMinMax('minLength', 'maxLength')\n\n      prevWrap(true, () => {\n        const checkFormat = (fmtname, target, formatsObj = fmts) => {\n          const known = typeof fmtname === 'string' && functions.hasOwn(formatsObj, fmtname)\n          enforce(known, 'Unrecognized format used:', fmtname)\n          const formatImpl = formatsObj[fmtname]\n          const valid = formatImpl instanceof RegExp || typeof formatImpl === 'function'\n          enforce(valid, 'Invalid format used:', fmtname)\n          if (!formatAssertion) return null\n          if (formatImpl instanceof RegExp) {\n            // built-in formats are fine, check only ones from options\n            if (functions.hasOwn(optFormats, fmtname)) enforceRegex(formatImpl.source)\n            return format('!%s.test(%s)', genformat(formatImpl), target)\n          }\n          return format('!%s(%s)', genformat(formatImpl), target)\n        }\n\n        handle('format', ['string'], (value) => {\n          evaluateDelta({ fullstring: true })\n          return checkFormat(value, name)\n        })\n\n        handle('pattern', ['string'], (pattern) => {\n          enforceRegex(pattern)\n          evaluateDelta({ fullstring: true })\n          return noopRegExps.has(pattern) ? null : safenot(patternTestSafe(pattern, name))\n        })\n\n        enforce(node.contentSchema !== false, 'contentSchema cannot be set to false')\n        const cV = contentValidation === undefined ? getMeta().contentValidation : contentValidation\n        const haveContent = node.contentEncoding || node.contentMediaType || node.contentSchema\n        const contentErr =\n          '\"content*\" keywords are disabled by default per spec, enable with { contentValidation = true } option (see doc/Options.md for more info)'\n        enforce(!haveContent || cV || allowUnusedKeywords, contentErr)\n        if (haveContent && cV) {\n          const dec = gensym('dec')\n          if (node.contentMediaType) fun.write('let %s = %s', dec, name)\n\n          if (node.contentEncoding === 'base64') {\n            errorIf(checkFormat('base64', name, formats.extra), { path: ['contentEncoding'] })\n            if (node.contentMediaType) {\n              scope.deBase64 = functions.deBase64\n              fun.write('try {')\n              fun.write('%s = deBase64(%s)', dec, dec)\n            }\n            consume('contentEncoding', 'string')\n          } else enforce(!node.contentEncoding, 'Unknown contentEncoding:', node.contentEncoding)\n\n          let json = false\n          if (node.contentMediaType === 'application/json') {\n            fun.write('try {')\n            fun.write('%s = JSON.parse(%s)', dec, dec)\n            json = true\n            consume('contentMediaType', 'string')\n          } else enforce(!node.contentMediaType, 'Unknown contentMediaType:', node.contentMediaType)\n\n          if (node.contentSchema) {\n            enforce(json, 'contentSchema requires contentMediaType application/json')\n            const decprop = Object.freeze({ name: dec, errorParent: current })\n            rule(decprop, node.contentSchema, subPath('contentSchema')) // TODO: isJSON true for speed?\n            consume('contentSchema', 'object', 'array')\n            evaluateDelta({ fullstring: true })\n          }\n          if (node.contentMediaType) {\n            fun.write('} catch (e) {')\n            error({ path: ['contentMediaType'] })\n            fun.write('}')\n            if (node.contentEncoding) {\n              fun.write('} catch (e) {')\n              error({ path: ['contentEncoding'] })\n              fun.write('}')\n            }\n          }\n        }\n      })\n    }\n\n    const checkArrays = () => {\n      handle('maxItems', ['natural'], (max) => {\n        const prefixItemsName = getMeta().newItemsSyntax ? 'prefixItems' : 'items'\n        if (Array.isArray(node[prefixItemsName]) && node[prefixItemsName].length > max)\n          fail(`Invalid maxItems: ${max} is less than ${prefixItemsName} array length`)\n        return format('%s.length > %d', name, max)\n      })\n      handle('minItems', ['natural'], (min) => format('%s.length < %d', name, min)) // can be higher that .items length with additionalItems\n      enforceMinMax('minItems', 'maxItems')\n\n      const checkItemsArray = (items) => {\n        for (let p = 0; p < items.length; p++) rule(currPropImm(p), items[p], subPath(`${p}`))\n        evaluateDelta({ items: items.length })\n        return null\n      }\n      if (getMeta().newItemsSyntax) {\n        handle('prefixItems', ['array'], checkItemsArray)\n        additionalItems('items', format('%d', (node.prefixItems || []).length))\n      } else if (Array.isArray(node.items)) {\n        handle('items', ['array'], checkItemsArray)\n        additionalItems('additionalItems', format('%d', node.items.length))\n      } else {\n        handle('items', ['object', 'boolean'], (items) => {\n          forArray(current, format('0'), (prop) => rule(prop, items, subPath('items')))\n          evaluateDelta({ items: Infinity })\n          return null\n        })\n        // If items is not an array, additionalItems is allowed, but ignored per some spec tests!\n        // We do nothing and let it throw except for in allowUnusedKeywords mode\n        // As a result, omitting .items is not allowed by default, only in allowUnusedKeywords mode\n      }\n\n      checkContains((run) => {\n        forArray(current, format('0'), (prop, i) => {\n          run(prop, () => {\n            evaluateDelta({ dyn: { item: true } })\n            evaluateDeltaDynamic({ item: i })\n          })\n        })\n      })\n\n      const itemsSimple = (ischema) => {\n        if (!isPlainObject(ischema)) return false\n        if (ischema.enum || functions.hasOwn(ischema, 'const')) return true\n        if (ischema.type) {\n          const itemTypes = Array.isArray(ischema.type) ? ischema.type : [ischema.type]\n          if (itemTypes.every((itemType) => primitiveTypes.includes(itemType))) return true\n        }\n        if (ischema.$ref) {\n          const [sub] = resolveReference(root, schemas, ischema.$ref, basePath())[0] || []\n          if (itemsSimple(sub)) return true\n        }\n        return false\n      }\n      const itemsSimpleOrFalse = (ischema) => ischema === false || itemsSimple(ischema)\n      const uniqueSimple = () => {\n        if (node.maxItems !== undefined || itemsSimpleOrFalse(node.items)) return true\n        // In old format, .additionalItems requires .items to have effect\n        if (Array.isArray(node.items) && itemsSimpleOrFalse(node.additionalItems)) return true\n        return false\n      }\n      prevWrap(true, () => {\n        handle('uniqueItems', ['boolean'], (uniqueItems) => {\n          if (uniqueItems === false) return null\n          if (!uniqueSimple()) complex('maxItems should be specified for non-primitive uniqueItems')\n          Object.assign(scope, { unique: functions.unique, deepEqual: functions.deepEqual })\n          return format('!unique(%s)', name)\n        })\n      })\n    }\n\n    // if allErrors is false, we can skip present check for required properties validated before\n    const checked = (p) =>\n      !allErrors &&\n      (stat.required.includes(p) || queryCurrent().some((h) => h.stat.required.includes(p)))\n\n    const checkObjects = () => {\n      const propertiesCount = format('Object.keys(%s).length', name)\n      handle('maxProperties', ['natural'], (max) => format('%s > %d', propertiesCount, max))\n      handle('minProperties', ['natural'], (min) => format('%s < %d', propertiesCount, min))\n      enforceMinMax('minProperties', 'maxProperties')\n\n      handle('propertyNames', ['object', 'boolean'], (s) => {\n        forObjectKeys(current, (sub, key) => {\n          // Add default type for non-ref schemas, so strong mode is fine with omitting it\n          const nameSchema = typeof s === 'object' && !s.$ref ? { type: 'string', ...s } : s\n          const nameprop = Object.freeze({ name: key, errorParent: sub, type: 'string' })\n          rule(nameprop, nameSchema, subPath('propertyNames'))\n        })\n        return null\n      })\n\n      handle('required', ['array'], (required) => {\n        for (const req of required) {\n          if (checked(req)) continue\n          const prop = currPropImm(req)\n          errorIf(safenot(present(prop)), { path: ['required'], prop })\n        }\n        evaluateDelta({ required })\n        return null\n      })\n\n      for (const dependencies of ['dependencies', 'dependentRequired', 'dependentSchemas']) {\n        if (dependencies !== 'dependencies' && getMeta().dependentUnsupported) continue\n        handle(dependencies, ['object'], (value) => {\n          for (const key of Object.keys(value)) {\n            const deps = typeof value[key] === 'string' ? [value[key]] : value[key]\n            const item = currPropImm(key, checked(key))\n            if (Array.isArray(deps) && dependencies !== 'dependentSchemas') {\n              const clauses = deps.filter((k) => !checked(k)).map((k) => present(currPropImm(k)))\n              const condition = safenot(safeand(...clauses))\n              const errorArgs = { path: [dependencies, key] }\n              if (clauses.length === 0) {\n                // nothing to do\n              } else if (item.checked) {\n                errorIf(condition, errorArgs)\n                evaluateDelta({ required: deps })\n              } else {\n                errorIf(safeand(present(item), condition), errorArgs)\n              }\n            } else if (isSchemaish(deps) && dependencies !== 'dependentRequired') {\n              uncertain(dependencies) // TODO: we don't always need this, remove when no uncertainity?\n              fun.if(item.checked ? true : present(item), () => {\n                const delta = rule(current, deps, subPath(dependencies, key), dyn)\n                evaluateDelta(orDelta({}, delta))\n                evaluateDeltaDynamic(delta)\n              })\n            } else fail(`Unexpected ${dependencies} entry`)\n          }\n          return null\n        })\n      }\n\n      handle('propertyDependencies', ['object'], (propertyDependencies) => {\n        for (const [key, variants] of Object.entries(propertyDependencies)) {\n          enforce(isPlainObject(variants), 'propertyDependencies must be an object')\n          uncertain('propertyDependencies') // TODO: we don't always need this, remove when no uncertainity?\n          const item = currPropImm(key, checked(key))\n          // NOTE: would it be useful to also check if it's a string?\n          fun.if(item.checked ? true : present(item), () => {\n            for (const [val, deps] of Object.entries(variants)) {\n              enforce(isSchemaish(deps), 'propertyDependencies must contain schemas')\n              fun.if(compare(buildName(item), val), () => {\n                // TODO: we already know that we have an object here, optimize?\n                const delta = rule(current, deps, subPath('propertyDependencies', key, val), dyn)\n                evaluateDelta(orDelta({}, delta))\n                evaluateDeltaDynamic(delta)\n              })\n            }\n          })\n        }\n        return null\n      })\n\n      handle('properties', ['object'], (properties) => {\n        for (const p of Object.keys(properties)) {\n          if (constProp === p) continue // checked in discriminator, avoid double-check\n          rule(currPropImm(p, checked(p)), properties[p], subPath('properties', p))\n        }\n        evaluateDelta({ properties: Object.keys(properties) })\n        return null\n      })\n\n      prevWrap(node.patternProperties, () => {\n        handle('patternProperties', ['object'], (patternProperties) => {\n          forObjectKeys(current, (sub, key) => {\n            for (const p of Object.keys(patternProperties)) {\n              enforceRegex(p, node.propertyNames || {})\n              fun.if(patternTestSafe(p, key), () => {\n                rule(sub, patternProperties[p], subPath('patternProperties', p))\n              })\n            }\n          })\n          evaluateDelta({ patterns: Object.keys(patternProperties) })\n          return null\n        })\n        if (node.additionalProperties || node.additionalProperties === false) {\n          const properties = Object.keys(node.properties || {})\n          const patternProperties = Object.keys(node.patternProperties || {})\n          if (node.additionalProperties === false) {\n            // Postpone the check to the end when all nested .required are collected\n            finalLint.push(() => lintRequired(properties, patternProperties))\n          }\n          const condition = (key) => additionalCondition(key, properties, patternProperties)\n          additionalProperties('additionalProperties', condition)\n        }\n      })\n\n      if (getMeta().objectContains) {\n        checkContains((run) => {\n          forObjectKeys(current, (prop, i) => {\n            run(prop, () => {\n              evaluateDelta({ dyn: { properties: [true] } })\n              evaluateDeltaDynamic({ propertiesVars: [i] })\n            })\n          })\n        })\n      }\n    }\n\n    const checkConst = () => {\n      const handledConst = handle('const', ['jsonval'], (val) => safenot(compare(name, val)))\n      if (handledConst && !allowUnusedKeywords) return true // enum can't be present, this is rechecked by allowUnusedKeywords\n      const handledEnum = handle('enum', ['array'], (vals) => {\n        const objects = vals.filter((value) => value && typeof value === 'object')\n        const primitive = vals.filter((value) => !(value && typeof value === 'object'))\n        return safenotor(...[...primitive, ...objects].map((value) => compare(name, value)))\n      })\n      return handledConst || handledEnum\n    }\n\n    const checkContains = (iterate) => {\n      // This can be called two times, 'object' and 'array' separately\n      handle('contains', ['object', 'boolean'], () => {\n        uncertain('contains')\n\n        if (getMeta().objectContains && typeApplicable('array') && typeApplicable('object')) {\n          enforceValidation(\"possible type confusion in 'contains',\", \"forbid 'object' or 'array'\")\n        }\n\n        const passes = gensym('passes')\n        fun.write('let %s = 0', passes)\n\n        const suberr = suberror()\n        iterate((prop, evaluate) => {\n          const { sub } = subrule(suberr, prop, node.contains, subPath('contains'))\n          fun.if(sub, () => {\n            fun.write('%s++', passes)\n            if (getMeta().containsEvaluates) {\n              enforce(!removeAdditional, 'Can\\'t use removeAdditional with draft2020+ \"contains\"')\n              evaluate()\n            }\n          })\n        })\n\n        if (!handle('minContains', ['natural'], (mn) => format('%s < %d', passes, mn), { suberr }))\n          errorIf(format('%s < 1', passes), { path: ['contains'], suberr })\n\n        handle('maxContains', ['natural'], (max) => format('%s > %d', passes, max))\n        enforceMinMax('minContains', 'maxContains')\n        return null\n      })\n    }\n\n    const checkGeneric = () => {\n      handle('not', ['object', 'boolean'], (not) => subrule(null, current, not, subPath('not')).sub)\n      if (node.not) uncertain('not')\n\n      const thenOrElse = node.then || node.then === false || node.else || node.else === false\n      // if we allow lone \"if\" to be present with allowUnusedKeywords, then we must process it to do the evaluation\n      // TODO: perhaps we can optimize this out if dynamic evaluation isn't needed _even with this if processed_\n      if (thenOrElse || allowUnusedKeywords)\n        handle('if', ['object', 'boolean'], (ifS) => {\n          uncertain('if/then/else')\n          const { sub, delta: deltaIf } = subrule(null, current, ifS, subPath('if'), dyn)\n          let handleElse, handleThen, deltaElse, deltaThen\n          handle('else', ['object', 'boolean'], (elseS) => {\n            handleElse = () => {\n              deltaElse = rule(current, elseS, subPath('else'), dyn)\n              evaluateDeltaDynamic(deltaElse)\n            }\n            return null\n          })\n          handle('then', ['object', 'boolean'], (thenS) => {\n            handleThen = () => {\n              deltaThen = rule(current, thenS, subPath('then'), dyn)\n              evaluateDeltaDynamic(andDelta(deltaIf, deltaThen))\n            }\n            return null\n          })\n          if (!handleThen && !deltaEmpty(deltaIf)) handleThen = () => evaluateDeltaDynamic(deltaIf)\n          fun.if(sub, handleThen, handleElse)\n          evaluateDelta(orDelta(deltaElse || {}, andDelta(deltaIf, deltaThen || {})))\n          return null\n        })\n\n      const performAllOf = (allOf, rulePath = 'allOf') => {\n        enforce(allOf.length > 0, `${rulePath} cannot be empty`)\n        for (const [key, sch] of Object.entries(allOf))\n          evaluateDelta(rule(current, sch, subPath(rulePath, key), dyn))\n        return null\n      }\n      handle('allOf', ['array'], (allOf) => performAllOf(allOf))\n\n      let handleDiscriminator = null\n      handle('discriminator', ['object'], (discriminator) => {\n        const seen = new Set()\n        const fix = (check, message, arg) => enforce(check, `[discriminator]: ${message}`, arg)\n        const { propertyName: pname, mapping: map, ...e0 } = discriminator\n        const prop = currPropImm(pname)\n        fix(pname && !node.oneOf !== !node.anyOf, 'need propertyName, oneOf OR anyOf')\n        fix(Object.keys(e0).length === 0, 'only \"propertyName\" and \"mapping\" are supported')\n        const keylen = (obj) => (isPlainObject(obj) ? Object.keys(obj).length : null)\n        handleDiscriminator = (branches, ruleName) => {\n          const runDiscriminator = () => {\n            fun.write('switch (%s) {', buildName(prop)) // we could also have used ifs for complex types\n            let delta\n            for (const [i, branch] of Object.entries(branches)) {\n              const { const: myval, enum: myenum, ...e1 } = (branch.properties || {})[pname] || {}\n              let vals = myval !== undefined ? [myval] : myenum\n              if (!vals && branch.$ref) {\n                const [sub] = resolveReference(root, schemas, branch.$ref, basePath())[0] || []\n                enforce(isPlainObject(sub), 'failed to resolve $ref:', branch.$ref)\n                const rprop = (sub.properties || {})[pname] || {}\n                vals = rprop.const !== undefined ? [rprop.const] : rprop.enum\n              }\n              const ok1 = Array.isArray(vals) && vals.length > 0\n              fix(ok1, 'branches should have unique string const or enum values for [propertyName]')\n              const ok2 = Object.keys(e1).length === 0 && (!myval || !myenum)\n              fix(ok2, 'only const OR enum rules are allowed on [propertyName] in branches')\n              for (const val of vals) {\n                const okMapping = !map || (functions.hasOwn(map, val) && map[val] === branch.$ref)\n                fix(okMapping, 'mismatching mapping for', val)\n                const valok = typeof val === 'string' && !seen.has(val)\n                fix(valok, 'const/enum values for [propertyName] should be unique strings')\n                seen.add(val)\n                fun.write('case %j:', val)\n              }\n              const subd = rule(current, branch, subPath(ruleName, i), dyn, { constProp: pname })\n              evaluateDeltaDynamic(subd)\n              delta = delta ? orDelta(delta, subd) : subd\n              fun.write('break')\n            }\n            fix(map === undefined || keylen(map) === seen.size, 'mismatching mapping size')\n            evaluateDelta(delta)\n            fun.write('default:')\n            error({ path: [ruleName] })\n            fun.write('}')\n          }\n          const propCheck = () => {\n            if (!checked(pname)) {\n              const errorPath = ['discriminator', 'propertyName']\n              fun.if(present(prop), runDiscriminator, () => error({ path: errorPath, prop }))\n            } else runDiscriminator()\n          }\n          if (allErrors || !functions.deepEqual(stat.type, ['object'])) {\n            fun.if(types.get('object')(name), propCheck, () => error({ path: ['discriminator'] }))\n          } else propCheck()\n          // can't evaluateDelta on type and required to not break the checks below, but discriminator\n          // is usually used with refs anyway so those won't be of much use\n          fix(functions.deepEqual(stat.type, ['object']), 'has to be checked for type:', 'object')\n          fix(stat.required.includes(pname), 'propertyName should be placed in required:', pname)\n          return null\n        }\n        return null\n      })\n\n      // Mark the schema as uncertain if the path taken is not determined solely by the branch type\n      const uncertainBranchTypes = (key, arr) => {\n        // In general, { const: [] } can interfere with other { type: 'array' }\n        // Same for { const: {} } and { type: 'object' }\n        // So this check doesn't treat those as non-conflicting, and instead labels those as uncertain conflicts\n        const btypes = arr.map((x) => x.type || (Array.isArray(x.const) ? 'array' : typeof x.const)) // typeof can be 'undefined', but we don't care\n        const maybeObj = btypes.filter((x) => !primitiveTypes.includes(x) && x !== 'array').length\n        const maybeArr = btypes.filter((x) => !primitiveTypes.includes(x) && x !== 'object').length\n        if (maybeObj > 1 || maybeArr > 1) uncertain(`${key}, use discriminator to make it certain`)\n      }\n\n      handle('anyOf', ['array'], (anyOf) => {\n        enforce(anyOf.length > 0, 'anyOf cannot be empty')\n        if (anyOf.length === 1) return performAllOf(anyOf)\n        if (handleDiscriminator) return handleDiscriminator(anyOf, 'anyOf')\n        const suberr = suberror()\n        if (!canSkipDynamic()) {\n          uncertainBranchTypes('anyOf', anyOf) // const sorting for removeAdditional is not supported in dynamic mode\n          // In this case, all have to be checked to gather evaluated properties\n          const entries = Object.entries(anyOf).map(([key, sch]) =>\n            subrule(suberr, current, sch, subPath('anyOf', key), dyn)\n          )\n          evaluateDelta(entries.map((x) => x.delta).reduce((acc, cur) => orDelta(acc, cur)))\n          errorIf(safenotor(...entries.map(({ sub }) => sub)), { path: ['anyOf'], suberr })\n          for (const { delta, sub } of entries) fun.if(sub, () => evaluateDeltaDynamic(delta))\n          return null\n        }\n        // We sort the variants to perform const comparisons first, then primitives/array/object/unknown\n        // This way, we can be sure that array/object + removeAdditional do not affect const evaluation\n        // Note that this _might_ e.g. remove all elements of an array in a 2nd branch _and_ fail with `const: []` in the 1st, but that's expected behavior\n        // This can be done because we can stop on the first match in anyOf if we don't need dynamic evaluation\n        const constBlocks = anyOf.filter((x) => functions.hasOwn(x, 'const'))\n        const otherBlocks = anyOf.filter((x) => !functions.hasOwn(x, 'const'))\n        uncertainBranchTypes('anyOf', otherBlocks)\n        const blocks = [...constBlocks, ...otherBlocks]\n        let delta\n\n        if (!getMeta().exclusiveRefs) {\n          // Under unevaluated* support, we can't optimize out branches using simple rules, see below\n          const entries = Object.entries(anyOf).map(([key, sch]) =>\n            subrule(suberr, current, sch, subPath('anyOf', key), dyn)\n          )\n          delta = entries.map((x) => x.delta).reduce((acc, cur) => orDelta(acc, cur))\n          errorIf(safenotor(...entries.map(({ sub }) => sub)), { path: ['anyOf'], suberr })\n        } else {\n          // Optimization logic below isn't stable under unevaluated* presence, as branches can be the sole reason of\n          // causing dynamic evaluation, and optimizing them out can miss the `if (!canSkipDynamic()) {` check above\n          let body = () => error({ path: ['anyOf'], suberr })\n          for (const [key, sch] of Object.entries(blocks).reverse()) {\n            const oldBody = body\n            body = () => {\n              const { sub, delta: deltaVar } = subrule(suberr, current, sch, subPath('anyOf', key))\n              fun.if(safenot(sub), oldBody) // this can exclude branches, see note above\n              delta = delta ? orDelta(delta, deltaVar) : deltaVar\n            }\n          }\n          body()\n        }\n\n        evaluateDelta(delta)\n        return null\n      })\n\n      handle('oneOf', ['array'], (oneOf) => {\n        enforce(oneOf.length > 0, 'oneOf cannot be empty')\n        if (oneOf.length === 1) return performAllOf(oneOf)\n        if (handleDiscriminator) return handleDiscriminator(oneOf, 'oneOf')\n        uncertainBranchTypes('oneOf', oneOf)\n        const passes = gensym('passes')\n        fun.write('let %s = 0', passes)\n        const suberr = suberror()\n        let delta\n        let i = 0\n        const entries = Object.entries(oneOf).map(([key, sch]) => {\n          if (!includeErrors && i++ > 1) errorIf(format('%s > 1', passes), { path: ['oneOf'] })\n          const entry = subrule(suberr, current, sch, subPath('oneOf', key), dyn)\n          fun.if(entry.sub, () => fun.write('%s++', passes))\n          delta = delta ? orDelta(delta, entry.delta) : entry.delta\n          return entry\n        })\n        evaluateDelta(delta)\n        errorIf(format('%s !== 1', passes), { path: ['oneOf'] })\n        fun.if(format('%s === 0', passes), () => mergeerror(suberr)) // if none matched, dump all errors\n        for (const entry of entries) fun.if(entry.sub, () => evaluateDeltaDynamic(entry.delta))\n        return null\n      })\n    }\n\n    const typeWrap = (checkBlock, validTypes, queryType) => {\n      const [funSize, unusedSize] = [fun.size(), unused.size]\n      fun.if(definitelyType(...validTypes) ? true : queryType, checkBlock)\n      // enforce check that non-applicable blocks are empty and no rules were applied\n      if (funSize !== fun.size() || unusedSize !== unused.size)\n        enforce(typeApplicable(...validTypes), `Unexpected rules in type`, node.type)\n    }\n\n    // Unevaluated validation\n    const checkArraysFinal = () => {\n      if (stat.items === Infinity) {\n        // Everything is statically evaluated, so this check is unreachable. Allow only 'false' rule here.\n        if (node.unevaluatedItems === false) consume('unevaluatedItems', 'boolean')\n      } else if (node.unevaluatedItems || node.unevaluatedItems === false) {\n        if (isDynamic(stat).items) {\n          if (!opts[optDynamic]) throw new Error('[opt] Dynamic unevaluated tracing not enabled')\n          const limit = format('Math.max(%d, ...%s)', stat.items, dyn.items)\n          const extra = (i) => format('%s.includes(%s)', dyn.item, i)\n          additionalItems('unevaluatedItems', limit, getMeta().containsEvaluates ? extra : null)\n        } else {\n          additionalItems('unevaluatedItems', format('%d', stat.items))\n        }\n      }\n    }\n    const checkObjectsFinal = () => {\n      prevWrap(stat.patterns.length > 0 || stat.dyn.patterns.length > 0 || stat.unknown, () => {\n        if (stat.properties.includes(true)) {\n          // Everything is statically evaluated, so this check is unreachable. Allow only 'false' rule here.\n          if (node.unevaluatedProperties === false) consume('unevaluatedProperties', 'boolean')\n        } else if (node.unevaluatedProperties || node.unevaluatedProperties === false) {\n          const notStatic = (key) => additionalCondition(key, stat.properties, stat.patterns)\n          if (isDynamic(stat).properties) {\n            if (!opts[optDynamic]) throw new Error('[opt] Dynamic unevaluated tracing not enabled')\n            scope.propertyIn = functions.propertyIn\n            const notDynamic = (key) => format('!propertyIn(%s, %s)', key, dyn.props)\n            const condition = (key) => safeand(notStatic(key), notDynamic(key))\n            additionalProperties('unevaluatedProperties', condition)\n          } else {\n            if (node.unevaluatedProperties === false) lintRequired(stat.properties, stat.patterns)\n            additionalProperties('unevaluatedProperties', notStatic)\n          }\n        }\n      })\n    }\n\n    const performValidation = () => {\n      if (prev !== null) fun.write('const %s = errorCount', prev)\n      if (checkConst()) {\n        const typeKeys = [...types.keys()] // we don't extract type from const/enum, it's enough that we know that it's present\n        evaluateDelta({ properties: [true], items: Infinity, type: typeKeys, fullstring: true }) // everything is evaluated for const\n        if (!allowUnusedKeywords) {\n          // const/enum shouldn't have any other validation rules except for already checked type/$ref\n          enforce(unused.size === 0, 'Unexpected keywords mixed with const or enum:', [...unused])\n          // If it does though, we should not short-circuit validation. This could be optimized by extracting types, but not significant\n          return\n        }\n      }\n\n      typeWrap(checkNumbers, ['number', 'integer'], types.get('number')(name))\n      typeWrap(checkStrings, ['string'], types.get('string')(name))\n      typeWrap(checkArrays, ['array'], types.get('array')(name))\n      typeWrap(checkObjects, ['object'], types.get('object')(name))\n\n      checkGeneric()\n\n      // evaluated: apply static + dynamic\n      typeWrap(checkArraysFinal, ['array'], types.get('array')(name))\n      typeWrap(checkObjectsFinal, ['object'], types.get('object')(name))\n\n      for (const lint of finalLint) lint()\n\n      // evaluated: propagate dynamic to parent dynamic (aka trace)\n      // static to parent is merged via return value\n      applyDynamicToDynamic(trace, local.item, local.items, local.props)\n    }\n\n    // main post-presence check validation function\n    const writeMain = () => {\n      if (local.item) fun.write('const %s = []', local.item)\n      if (local.items) fun.write('const %s = [0]', local.items)\n      if (local.props) fun.write('const %s = [[], []]', local.props)\n\n      // refs\n      handle('$ref', ['string'], ($ref) => {\n        const resolved = resolveReference(root, schemas, $ref, basePath())\n        const [sub, subRoot, path] = resolved[0] || []\n        if (!sub && sub !== false) {\n          fail('failed to resolve $ref:', $ref)\n          if (lintOnly) return null // failures are just collected in linter mode and don't throw, this makes a ref noop\n        }\n        const n = compileSub(sub, subRoot, path)\n        const rn = sub === schema ? funname : n // resolve to actual name\n        if (!scope[rn]) throw new Error('Unexpected: coherence check failed')\n        if (!scope[rn][evaluatedStatic] && sub.type) {\n          const type = Array.isArray(sub.type) ? sub.type : [sub.type]\n          evaluateDelta({ type })\n          if (requireValidation) {\n            // We are inside a cyclic ref, label it as a one that needs full validation to support assumption in next clause\n            refsNeedFullValidation.add(rn)\n            // If validation is required, then a cyclic $ref is guranteed to validate all items and properties\n            if (type.includes('array')) evaluateDelta({ items: Infinity })\n            if (type.includes('object')) evaluateDelta({ properties: [true] })\n          }\n          if (requireStringValidation && type.includes('string')) {\n            refsNeedFullValidation.add(rn)\n            evaluateDelta({ fullstring: true })\n          }\n        }\n        return applyRef(n, { path: ['$ref'] })\n      })\n      if (getMeta().exclusiveRefs) {\n        enforce(!opts[optDynamic], 'unevaluated* is supported only on draft2019-09 and above')\n        if (node.$ref) return // ref overrides any sibling keywords for older schemas\n      }\n      handle('$recursiveRef', ['string'], ($recursiveRef) => {\n        if (!opts[optRecAnchors]) throw new Error('[opt] Recursive anchors are not enabled')\n        enforce($recursiveRef === '#', 'Behavior of $recursiveRef is defined only for \"#\"')\n        // Resolve to recheck that recursive ref is enabled\n        const resolved = resolveReference(root, schemas, '#', basePath())\n        const [sub, subRoot, path] = resolved[0]\n        laxMode(sub.$recursiveAnchor, '$recursiveRef without $recursiveAnchor')\n        const n = compileSub(sub, subRoot, path)\n        // Apply deep recursion from here only if $recursiveAnchor is true, else just run self\n        const nrec = sub.$recursiveAnchor ? format('(recursive || %s)', n) : n\n        return applyRef(nrec, { path: ['$recursiveRef'] })\n      })\n      handle('$dynamicRef', ['string'], ($dynamicRef) => {\n        if (!opts[optDynAnchors]) throw new Error('[opt] Dynamic anchors are not enabled')\n        laxMode(/^[^#]*#[a-zA-Z0-9_-]+$/.test($dynamicRef), 'Unsupported $dynamicRef format')\n        const dynamicTail = $dynamicRef.replace(/^[^#]+/, '')\n        const resolved = resolveReference(root, schemas, $dynamicRef, basePath())\n        if (!resolved[0] && !getMeta().bookending) {\n          // TODO: this is draft/next only atm, recheck if dynamicResolve() can fail in runtime and what should happen\n          // We have this allowed in lax mode only for now\n          // Ref: https://github.com/json-schema-org/json-schema-spec/issues/1064#issuecomment-947223332\n          // Ref: https://github.com/json-schema-org/json-schema-spec/pull/1139\n          // Ref: https://github.com/json-schema-org/json-schema-spec/issues/1140 (unresolved)\n          laxMode(false, '$dynamicRef bookending resolution failed (even though not required)')\n          scope.dynamicResolve = functions.dynamicResolve\n          const nrec = format('dynamicResolve(dynAnchors || [], %j)', dynamicTail)\n          return applyRef(nrec, { path: ['$dynamicRef'] })\n        }\n        enforce(resolved[0], '$dynamicRef bookending resolution failed', $dynamicRef)\n        const [sub, subRoot, path] = resolved[0]\n        const ok = sub.$dynamicAnchor && `#${sub.$dynamicAnchor}` === dynamicTail\n        laxMode(ok, '$dynamicRef without $dynamicAnchor in the same scope')\n        const n = compileSub(sub, subRoot, path)\n        scope.dynamicResolve = functions.dynamicResolve\n        const nrec = ok ? format('(dynamicResolve(dynAnchors || [], %j) || %s)', dynamicTail, n) : n\n        return applyRef(nrec, { path: ['$dynamicRef'] })\n      })\n\n      // typecheck\n      let typeCheck = null\n      handle('type', ['string', 'array'], (type) => {\n        const typearr = Array.isArray(type) ? type : [type]\n        for (const t of typearr) enforce(typeof t === 'string' && types.has(t), 'Unknown type:', t)\n        if (current.type) {\n          enforce(functions.deepEqual(typearr, [current.type]), 'One type allowed:', current.type)\n          evaluateDelta({ type: [current.type] })\n          return null\n        }\n        if (parentCheckedType(...typearr)) return null\n        const filteredTypes = typearr.filter((t) => typeApplicable(t))\n        if (filteredTypes.length === 0) fail('No valid types possible')\n        evaluateDelta({ type: typearr }) // can be safely done here, filteredTypes already prepared\n        typeCheck = safenotor(...filteredTypes.map((t) => types.get(t)(name)))\n        return null\n      })\n\n      // main validation block\n      // if type validation was needed and did not return early, wrap this inside an else clause.\n      if (typeCheck && allErrors) {\n        fun.if(typeCheck, () => error({ path: ['type'] }), performValidation)\n      } else {\n        if (typeCheck) errorIf(typeCheck, { path: ['type'] })\n        performValidation()\n      }\n\n      // account for maxItems to recheck if they limit items. TODO: perhaps we could keep track of this in stat?\n      if (stat.items < Infinity && node.maxItems <= stat.items) evaluateDelta({ items: Infinity })\n    }\n\n    // presence check and call main validation block\n    if (node.default !== undefined && useDefaults) {\n      if (definitelyPresent) fail('Can not apply default value here (e.g. at root)')\n      const defvalue = get('default', 'jsonval')\n      fun.if(present(current), writeMain, () => fun.write('%s = %j', name, defvalue))\n    } else {\n      handle('default', ['jsonval'], null) // unused\n      fun.if(definitelyPresent ? true : present(current), writeMain)\n    }\n\n    basePathStack.length = basePathStackLength // restore basePath\n\n    // restore recursiveAnchor history if it's not empty and ends with current node\n    if (recursiveLog[0] && recursiveLog[recursiveLog.length - 1][0] === node) recursiveLog.pop()\n    if (isDynScope && node !== schema) fun.write('dynLocal.shift()') // restore dynamic scope, no need on top-level\n\n    // Checks related to static schema analysis\n    if (!allowUnreachable) enforce(!fun.optimizedOut, 'some checks are never reachable')\n    if (isSub) {\n      const logicalOp = ['not', 'if', 'then', 'else'].includes(schemaPath[schemaPath.length - 1])\n      const branchOp = ['oneOf', 'anyOf', 'allOf'].includes(schemaPath[schemaPath.length - 2])\n      const depOp = ['dependencies', 'dependentSchemas'].includes(schemaPath[schemaPath.length - 2])\n      const propDepOp = ['propertyDependencies'].includes(schemaPath[schemaPath.length - 3])\n      // Coherence check, unreachable, double-check that we came from expected path\n      enforce(logicalOp || branchOp || depOp || propDepOp, 'Unexpected logical path')\n    } else if (!schemaPath.includes('not')) {\n      // 'not' does not mark anything as evaluated (unlike even if/then/else), so it's safe to exclude from these\n      // checks, as we are sure that everything will be checked without it. It can be viewed as a pure add-on.\n      const isRefTop = schema !== root && node === schema // We are at the top-level of an opaque ref inside the schema object\n      if (!isRefTop || refsNeedFullValidation.has(funname)) {\n        refsNeedFullValidation.delete(funname)\n        if (!stat.type) enforceValidation('type')\n        // This can't be true for top-level schemas, only references with #/...\n        if (typeApplicable('array') && stat.items !== Infinity)\n          enforceValidation(node.items ? 'additionalItems or unevaluatedItems' : 'items rule')\n        if (typeApplicable('object') && !stat.properties.includes(true))\n          enforceValidation('additionalProperties or unevaluatedProperties')\n        if (!stat.fullstring && requireStringValidation) {\n          const stringWarning = 'pattern, format or contentSchema should be specified for strings'\n          fail(`[requireStringValidation] ${stringWarning}, use pattern: ^[\\\\s\\\\S]*$ to opt-out`)\n        }\n      }\n      if (typeof node.propertyNames !== 'object')\n        for (const sub of ['additionalProperties', 'unevaluatedProperties'])\n          if (node[sub]) enforceValidation(`wild-card ${sub}`, 'requires propertyNames')\n    }\n    if (node.properties && !node.required) enforceValidation('if properties is used, required')\n    enforce(unused.size === 0 || allowUnusedKeywords, 'Unprocessed keywords:', [...unused])\n\n    return { stat, local } // return statically evaluated\n  }\n\n  const { stat, local } = visit(format('validate.errors'), [], { name: safe('data') }, schema, [])\n  if (refsNeedFullValidation.has(funname)) throw new Error('Unexpected: unvalidated cyclic ref')\n\n  // evaluated: return dynamic for refs\n  if (opts[optDynamic] && (isDynamic(stat).items || isDynamic(stat).properties)) {\n    if (!local) throw new Error('Failed to trace dynamic properties') // Unreachable\n    fun.write('validate.evaluatedDynamic = [%s, %s, %s]', local.item, local.items, local.props)\n  }\n\n  if (allErrors) fun.write('return errorCount === 0')\n  else fun.write('return true')\n\n  fun.write('}')\n\n  if (!lintOnly) {\n    validate = fun.makeFunction(scope)\n    delete scope[funname] // more logical key order\n    scope[funname] = validate\n  }\n  scope[funname][evaluatedStatic] = stat // still needed even in non-compiled lint for recursive refs check\n  return funname\n}\n\nconst compile = (schemas, opts) => {\n  if (!Array.isArray(schemas)) throw new Error('Expected an array of schemas')\n  try {\n    const scope = Object.create(null)\n    const { getref } = scopeMethods(scope)\n    refsNeedFullValidation.clear() // for isolation/safeguard\n    rootMeta.clear() // for isolation/safeguard\n    const refs = schemas.map((s) => getref(s) || compileSchema(s, s, opts, scope))\n    if (refsNeedFullValidation.size !== 0) throw new Error('Unexpected: not all refs are validated')\n    return { scope, refs }\n  } catch (e) {\n    // For performance, we try to build the schema without dynamic tracing first, then re-run with\n    // it enabled if needed. Enabling it without need can give up to about 40% performance drop.\n    if (!opts[optDynamic] && e.message === '[opt] Dynamic unevaluated tracing not enabled')\n      return compile(schemas, { ...opts, [optDynamic]: true })\n    // Also enable dynamic and recursive refs only if needed\n    if (!opts[optDynAnchors] && e.message === '[opt] Dynamic anchors are not enabled')\n      return compile(schemas, { ...opts, [optDynAnchors]: true })\n    if (!opts[optRecAnchors] && e.message === '[opt] Recursive anchors are not enabled')\n      return compile(schemas, { ...opts, [optRecAnchors]: true })\n    throw e\n  } finally {\n    refsNeedFullValidation.clear() // for gc\n    rootMeta.clear() // for gc\n  }\n}\n\nmodule.exports = { compile }\n", "'use strict'\n\nconst genfun = require('./generate-function')\nconst { buildSchemas } = require('./pointer')\nconst { compile } = require('./compile')\nconst { deepEqual } = require('./scope-functions')\n\nconst jsonCheckWithErrors = (validate) =>\n  function validateIsJSON(data) {\n    if (!deepEqual(data, JSON.parse(JSON.stringify(data)))) {\n      validateIsJSON.errors = [{ instanceLocation: '#', error: 'not JSON compatible' }]\n      return false\n    }\n    const res = validate(data)\n    validateIsJSON.errors = validate.errors\n    return res\n  }\n\nconst jsonCheckWithoutErrors = (validate) => (data) =>\n  deepEqual(data, JSON.parse(JSON.stringify(data))) && validate(data)\n\nconst validator = (\n  schema,\n  { parse = false, multi = false, jsonCheck = false, isJSON = false, schemas = [], ...opts } = {}\n) => {\n  if (jsonCheck && isJSON) throw new Error('Can not specify both isJSON and jsonCheck options')\n  if (parse && (jsonCheck || isJSON))\n    throw new Error('jsonCheck and isJSON options are not applicable in parser mode')\n  const mode = parse ? 'strong' : 'default' // strong mode is default in parser, can be overriden\n  const willJSON = isJSON || jsonCheck || parse\n  const arg = multi ? schema : [schema]\n  const options = { mode, ...opts, schemas: buildSchemas(schemas, arg), isJSON: willJSON }\n  const { scope, refs } = compile(arg, options) // only a single ref\n  if (opts.dryRun) return\n  if (opts.lint) return scope.lintErrors\n  const fun = genfun()\n  if (parse) {\n    scope.parseWrap = opts.includeErrors ? parseWithErrors : parseWithoutErrors\n  } else if (jsonCheck) {\n    scope.deepEqual = deepEqual\n    scope.jsonCheckWrap = opts.includeErrors ? jsonCheckWithErrors : jsonCheckWithoutErrors\n  }\n  if (multi) {\n    fun.write('[')\n    for (const ref of refs.slice(0, -1)) fun.write('%s,', ref)\n    if (refs.length > 0) fun.write('%s', refs[refs.length - 1])\n    fun.write(']')\n    if (parse) fun.write('.map(parseWrap)')\n    else if (jsonCheck) fun.write('.map(jsonCheckWrap)')\n  } else {\n    if (parse) fun.write('parseWrap(%s)', refs[0])\n    else if (jsonCheck) fun.write('jsonCheckWrap(%s)', refs[0])\n    else fun.write('%s', refs[0])\n  }\n  const validate = fun.makeFunction(scope)\n  validate.toModule = ({ semi = true } = {}) => fun.makeModule(scope) + (semi ? ';' : '')\n  validate.toJSON = () => schema\n  return validate\n}\n\nconst parseWithErrors = (validate) => (src) => {\n  if (typeof src !== 'string') return { valid: false, error: 'Input is not a string' }\n  try {\n    const value = JSON.parse(src)\n    if (!validate(value)) {\n      const { keywordLocation, instanceLocation } = validate.errors[0]\n      const keyword = keywordLocation.slice(keywordLocation.lastIndexOf('/') + 1)\n      const error = `JSON validation failed for ${keyword} at ${instanceLocation}`\n      return { valid: false, error, errors: validate.errors }\n    }\n    return { valid: true, value }\n  } catch ({ message }) {\n    return { valid: false, error: message }\n  }\n}\n\nconst parseWithoutErrors = (validate) => (src) => {\n  if (typeof src !== 'string') return { valid: false }\n  try {\n    const value = JSON.parse(src)\n    if (!validate(value)) return { valid: false }\n    return { valid: true, value }\n  } catch (e) {\n    return { valid: false }\n  }\n}\n\nconst parser = function(schema, { parse = true, ...opts } = {}) {\n  if (!parse) throw new Error('can not disable parse in parser')\n  return validator(schema, { parse, ...opts })\n}\n\nconst lint = function(schema, { lint: lintOption = true, ...opts } = {}) {\n  if (!lintOption) throw new Error('can not disable lint option in lint()')\n  return validator(schema, { lint: lintOption, ...opts })\n}\n\nmodule.exports = { validator, parser, lint }\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,QAAM,aAAN,cAAyB,OAAO;AAAA,IAAC;AAEjC,QAAM,WAAW,oBAAI,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,CAAC;AAC/C,QAAM,aAAa,CAAC,SAAS,MAAM,KAAK,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AAKrE,QAAM,QAAQ,CAAC,QAAQ;AACrB,UAAI,CAAC,UAAU,WAAW,KAAK,QAAW,IAAI,EAAE,SAAS,GAAG,EAAG,QAAO,GAAG,GAAG;AAC5E,YAAM,YAAY,CAAC,UAAU,WAAW,QAAQ,EAAE,SAAS,OAAO,GAAG;AACrE,UAAI,CAAC,WAAW;AACd,YAAI,OAAO,QAAQ,SAAU,OAAM,IAAI,MAAM,uBAAuB;AACpE,cAAM,QAAQ,OAAO,eAAe,GAAG;AACvC,cAAM,KAAM,UAAU,MAAM,aAAa,MAAM,QAAQ,GAAG,KAAM,UAAU,OAAO;AACjF,YAAI,CAAC,GAAI,OAAM,IAAI,MAAM,kCAAkC;AAAA,MAC7D;AACA,aACE,KAAK,UAAU,GAAG,EAMf,QAAQ,uBAAuB,kBAAkB,EAEjD,QAAQ,sBAAsB,MAAM;AAEnC,cAAM,IAAI,MAAM,aAAa;AAAA,MAC/B,CAAC,EAEA,QAAQ,mBAAmB,CAAC,SAAS,WAAW,KAAK,WAAW,CAAC,CAAC,CAAC;AAAA,IAE1E;AAEA,QAAM,SAAS,CAAC,QAAQ,SAAS;AAC/B,YAAM,MAAM,IAAI,QAAQ,eAAe,CAAC,UAAU;AAChD,YAAI,UAAU,KAAM,QAAO;AAC3B,YAAI,KAAK,WAAW,EAAG,OAAM,IAAI,MAAM,4BAA4B;AACnE,cAAM,MAAM,KAAK,MAAM;AACvB,gBAAQ,OAAO;AAAA,UACb,KAAK;AACH,gBAAI,OAAO,QAAQ,SAAU,QAAO;AACpC,kBAAM,IAAI,MAAM,mBAAmB;AAAA,UACrC,KAAK;AAEH,gBAAI,eAAe,OAAQ,QAAO,OAAO,sBAAsB,IAAI,QAAQ,IAAI,KAAK;AACpF,kBAAM,IAAI,MAAM,4BAA4B;AAAA,UAC9C,KAAK;AACH,gBAAI,eAAe,WAAY,QAAO;AACtC,kBAAM,IAAI,MAAM,wBAAwB;AAAA,UAC1C,KAAK;AACH,gBAAI,SAAS,IAAI,GAAG,EAAG,QAAO;AAC9B,kBAAM,IAAI,MAAM,uBAAuB;AAAA,UACzC,KAAK;AACH,mBAAO,MAAM,GAAG;AAAA,UAClB,KAAK;AACH,gBAAI,OAAO,UAAU,GAAG,KAAK,OAAO,EAAG,QAAO,IAAI,OAAO,GAAG;AAC5D,kBAAM,IAAI,MAAM,iDAAiD;AAAA,QACrE;AAEA,cAAM,IAAI,MAAM,aAAa;AAAA,MAC/B,CAAC;AACD,UAAI,KAAK,WAAW,EAAG,OAAM,IAAI,MAAM,4BAA4B;AACnE,aAAO,IAAI,WAAW,GAAG;AAAA,IAC3B;AAEA,QAAM,OAAO,CAAC,WAAW;AACvB,UAAI,CAAC,qBAAqB,KAAK,MAAM,EAAG,OAAM,IAAI,MAAM,8BAA8B;AACtF,aAAO,IAAI,WAAW,MAAM;AAAA,IAC9B;AAGA,QAAM,WAAW,CAAC,QAAQ,IAAI,SAAS;AACrC,UAAI,CAAC,KAAK,MAAM,CAAC,QAAQ,eAAe,UAAU,EAAG,OAAM,IAAI,MAAM,kBAAkB;AACvF,aAAO,IAAI,WAAW,IAAI,GAAG,IAAI,CAAC;AAAA,IACpC;AAEA,QAAM,eAAe,CAAC;AAAA;AAAA,MAEpB,wBAAwB,KAAK,GAAG,KAAK,gBAAgB,KAAK,GAAG,IAAI,MAAM,OAAO,QAAQ,GAAG;AAAA;AAC3F,QAAM,SAAS;AAAA,MACb,IAAI,SAAU,KAAK,KAAK,CAAC,QAAQ,GAAG,GAAG,OAAO,MAAM,IAAI,SAAS,KAAK,KAAK,MAAM,KAAK;AAAA,IACxF;AACA,QAAM,UAAU;AAAA,MACd,IAAI,SAAU,KAAK,KAAK,CAAC,QAAQ,GAAG,GAAG,OAAO,OAAO,IAAI,UAAU,KAAK,KAAK,MAAM,KAAK;AAAA,IAC1F;AACA,QAAM,UAAU,CAAC,QAAQ;AACvB,UAAI,GAAG,GAAG,OAAO,OAAQ,QAAO,KAAK,OAAO;AAC5C,UAAI,GAAG,GAAG,OAAO,QAAS,QAAO,KAAK,MAAM;AAC5C,aAAO,OAAO,OAAO,aAAa,GAAG,CAAC;AAAA,IACxC;AAEA,QAAM,YAAY,IAAI,SAAS,QAAQ,OAAO,GAAG,IAAI,CAAC;AAEtD,WAAO,UAAU,EAAE,QAAQ,MAAM,SAAS,SAAS,UAAU;AAAA;AAAA;;;ACjG7D;AAAA;AAAA;AAEA,QAAM,EAAE,KAAK,IAAI;AAEjB,QAAM,SAAS,oBAAI,QAAQ;AAK3B,QAAM,eAAe,CAAC,UAAU;AAE9B,UAAI,CAAC,OAAO,IAAI,KAAK;AACnB,eAAO,IAAI,OAAO,EAAE,KAAK,oBAAI,IAAI,GAAG,KAAK,oBAAI,IAAI,GAAG,QAAQ,oBAAI,IAAI,GAAG,SAAS,oBAAI,IAAI,EAAE,CAAC;AAC7F,YAAM,QAAQ,OAAO,IAAI,KAAK;AAG9B,YAAM,SAAS,CAAC,SAAS;AACvB,YAAI,CAAC,MAAM,IAAI,IAAI,IAAI,EAAG,OAAM,IAAI,IAAI,MAAM,CAAC;AAC/C,cAAM,QAAQ,MAAM,IAAI,IAAI,IAAI;AAChC,cAAM,IAAI,IAAI,MAAM,QAAQ,CAAC;AAC7B,eAAO,KAAK,GAAG,IAAI,GAAG,KAAK,EAAE;AAAA,MAC/B;AAGA,YAAM,aAAa,CAAC,MAAM;AACxB,YAAI,MAAM,QAAQ,IAAI,CAAC,EAAG,QAAO,MAAM,QAAQ,IAAI,CAAC;AACpD,cAAM,IAAI,OAAO,SAAS;AAC1B,cAAM,CAAC,IAAI,IAAI,OAAO,GAAG,GAAG;AAC5B,cAAM,QAAQ,IAAI,GAAG,CAAC;AACtB,eAAO;AAAA,MACT;AAGA,UAAI,CAAC,MAAM,KAAM,OAAM,OAAO,oBAAoB,MAAM,EAAE;AAC1D,YAAM,UAAU,MAAM;AACpB,cAAM,IAAI,MAAM,KAAK,MAAM;AAC3B,cAAM,KAAK,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;AAC7B,eAAO,KAAK,CAAC;AAAA,MACf;AAGA,YAAM,SAAS,CAAC,QAAQ,MAAM,IAAI,IAAI,GAAG;AACzC,YAAM,SAAS,CAAC,QAAQ;AACtB,cAAM,IAAI,OAAO,KAAK;AACtB,cAAM,IAAI,IAAI,KAAK,CAAC;AACpB,eAAO;AAAA,MACT;AAGA,YAAM,YAAY,CAAC,SAAS;AAC1B,YAAI,IAAI,MAAM,OAAO,IAAI,IAAI;AAC7B,YAAI,CAAC,GAAG;AACN,cAAI,OAAO,QAAQ;AACnB,gBAAM,CAAC,IAAI;AACX,gBAAM,OAAO,IAAI,MAAM,CAAC;AAAA,QAC1B;AACA,eAAO;AAAA,MACT;AAEA,aAAO,EAAE,QAAQ,YAAY,SAAS,QAAQ,QAAQ,UAAU;AAAA,IAClE;AAEA,WAAO,UAAU,EAAE,aAAa;AAAA;AAAA;;;AC9DhC;AAAA;AAAA;AAIA,QAAM,eAAe,CAAC,WACpB,kBAAkB,KAAK,MAAM,IAAI,CAAC,GAAG,MAAM,EAAE,SAAS,OAAO;AAK/D,QAAM,eAAe,CAAC,OAAO,SAAS,QAAQ,mBAAmB;AAC/D,UAAI,QAAQ,YAAY,EAAG,QAAO;AAClC,UAAI,WAAW,QAAQ;AACvB,UAAI,aAAa,YAAY,aAAa,UAAW,YAAW;AAChE,UAAI,WAAW,mBAAmB,EAAG,QAAO;AAC5C,YAAM,SAAS,KAAK,MAAM,WAAW,GAAG;AACxC,aAAO,SAAS,WAAW,SAAS,SAAS,mBAAmB;AAAA,IAClE;AAKA,QAAM,YAAY,CAAC,KAAK,SAAS;AAC/B,UAAI,QAAQ,KAAM,QAAO;AACzB,UAAI,CAAC,OAAO,CAAC,QAAQ,OAAO,QAAQ,OAAO,KAAM,QAAO;AACxD,UAAI,QAAQ,QAAQ,OAAO,QAAQ,SAAU,QAAO;AAEpD,YAAM,QAAQ,OAAO,eAAe,GAAG;AACvC,UAAI,UAAU,OAAO,eAAe,IAAI,EAAG,QAAO;AAElD,UAAI,UAAU,MAAM,WAAW;AAC7B,YAAI,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,QAAQ,IAAI,EAAG,QAAO;AACxD,YAAI,IAAI,WAAW,KAAK,OAAQ,QAAO;AACvC,eAAO,IAAI,MAAM,CAAC,GAAG,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC;AAAA,MAClD,WAAW,UAAU,OAAO,WAAW;AACrC,cAAM,CAAC,MAAM,KAAK,IAAI,CAAC,OAAO,KAAK,GAAG,GAAG,OAAO,KAAK,IAAI,CAAC;AAC1D,YAAI,KAAK,WAAW,MAAM,OAAQ,QAAO;AACzC,cAAM,UAAU,oBAAI,IAAI,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC;AAC3C,eAAO,QAAQ,SAAS,KAAK,UAAU,KAAK,MAAM,CAAC,QAAQ,UAAU,IAAI,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC;AAAA,MAC3F;AACA,aAAO;AAAA,IACT;AAEA,QAAM,SAAS,CAAC,UAAU;AACxB,UAAI,MAAM,SAAS,EAAG,QAAO;AAC7B,UAAI,MAAM,WAAW,EAAG,QAAO,CAAC,UAAU,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AAC5D,YAAM,UAAU,CAAC;AACjB,YAAM,aAAa,MAAM,SAAS,KAAK,oBAAI,IAAI,IAAI;AACnD,UAAI,kBAAkB;AACtB,UAAI,MAAM;AACV,iBAAW,QAAQ,OAAO;AACxB,YAAI,OAAO,SAAS,UAAU;AAC5B,kBAAQ,KAAK,IAAI;AAAA,QACnB,WAAW,YAAY;AACrB,qBAAW,IAAI,IAAI;AACnB,cAAI,WAAW,SAAS,EAAE,gBAAiB,QAAO;AAAA,QACpD,OAAO;AACL,cAAI,MAAM,QAAQ,MAAM,MAAM,CAAC,MAAM,GAAI,QAAO;AAAA,QAClD;AACA;AAAA,MACF;AACA,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAClC,iBAAS,IAAI,GAAG,IAAI,GAAG,IAAK,KAAI,UAAU,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAG,QAAO;AAC5E,aAAO;AAAA,IACT;AAEA,QAAM,WAAW,CAAC,WAAW;AAC3B,UAAI,OAAO,WAAW,YAAa,QAAO,OAAO,KAAK,QAAQ,QAAQ,EAAE,SAAS,OAAO;AACxF,YAAM,IAAI,KAAK,MAAM;AACrB,aAAO,IAAI,YAAY,OAAO,EAAE,OAAO,IAAI,WAAW,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;AAAA,IAChG;AAEA,QAAM,SAAS,SAAS,UAAU,KAAK,KAAK,OAAO,UAAU,cAAc;AAE3E,WAAO,OAAO,IAAI,aAAa,CAAC,IAAI;AAGpC,QAAM,cAAc,CAAC,MAAO,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,QAAQ,MAAM,IAAI,EAAE,QAAQ,OAAO,IAAI,IAAI;AAC9F,QAAM,YAAY,CAAC,SAAU,KAAK,WAAW,IAAI,MAAM,KAAK,KAAK,IAAI,WAAW,EAAE,KAAK,GAAG,CAAC;AAE3F,QAAM,aAAa,CAAC,EAAE,iBAAiB,iBAAiB,GAAG,YAAY,cAAc;AAAA,MACnF,iBAAiB,GAAG,UAAU,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MACzD,kBAAkB,GAAG,QAAQ,GAAG,iBAAiB,MAAM,CAAC,CAAC;AAAA,IAC3D;AAEA,QAAM,aAAa,CAAC,KAAK,CAAC,YAAY,QAAQ,MAC5C,WAAW,SAAS,IAAI,KACxB,WAAW,KAAK,CAAC,SAAS,SAAS,GAAG,KACtC,SAAS,KAAK,CAAC,YAAY,IAAI,OAAO,SAAS,GAAG,EAAE,KAAK,GAAG,CAAC;AAG/D,QAAM,iBAAiB,CAAC,SAAS,QAAQ,QAAQ,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE;AAElF,QAAM,aAAa,EAAE,WAAW,aAAa,YAAY,YAAY,eAAe;AACpF,WAAO,UAAU,EAAE,cAAc,cAAc,WAAW,QAAQ,UAAU,QAAQ,GAAG,WAAW;AAAA;AAAA;;;AC9FlG;AAAA;AAAA;AAEA,QAAM,EAAE,QAAQ,KAAK,IAAI;AACzB,QAAM,EAAE,aAAa,IAAI;AACzB,QAAM,YAAY;AAGlB,QAAM,QAAQ,IAAI;AAAA,MAChB,OAAO,QAAQ;AAAA,QACb,MAAM,CAAC,SAAS,OAAO,eAAe,IAAI;AAAA,QAC1C,SAAS,CAAC,SAAS,OAAO,2BAA2B,IAAI;AAAA,QACzD,OAAO,CAAC,SAAS,OAAO,qBAAqB,IAAI;AAAA,QACjD,QAAQ,CAAC,MAAM,OAAO,sDAAsD,GAAG,GAAG,CAAC;AAAA,QACnF,QAAQ,CAAC,SAAS,OAAO,0BAA0B,IAAI;AAAA,QACvD,SAAS,CAAC,SAAS,OAAO,wBAAwB,IAAI;AAAA,QACtD,QAAQ,CAAC,SAAS,OAAO,0BAA0B,IAAI;AAAA,MACzD,CAAC;AAAA,IACH;AAEA,QAAM,YAAY,CAAC,EAAE,MAAM,QAAQ,QAAQ,QAAQ,MAAM;AACvD,UAAI,MAAM;AACR,YAAI,UAAU,UAAU,QAAS,OAAM,IAAI,MAAM,mCAAmC;AACpF,eAAO;AAAA,MACT;AACA,UAAI,CAAC,OAAQ,OAAM,IAAI,MAAM,2CAA2C;AACxE,YAAM,aAAa,UAAU,MAAM;AACnC,UAAI,WAAW,QAAW;AACxB,YAAI,QAAS,OAAM,IAAI,MAAM,yCAAyC;AACtE,YAAI,CAAC,CAAC,UAAU,QAAQ,EAAE,SAAS,OAAO,MAAM,EAAG,OAAM,IAAI,MAAM,uBAAuB;AAC1F,YAAI,qBAAqB,KAAK,MAAM,EAAG,QAAO,OAAO,SAAS,YAAY,KAAK,MAAM,CAAC;AACtF,eAAO,OAAO,UAAU,YAAY,MAAM;AAAA,MAC5C,WAAW,SAAS;AAClB,eAAO,OAAO,UAAU,YAAY,OAAO;AAAA,MAC7C;AAEA,YAAM,IAAI,MAAM,aAAa;AAAA,IAC/B;AAEA,QAAM,gBAAgB,IAAI;AAAA,MACxB,CAAC,EAAE;AAAA,QACD,GAAG,CAAC,QAAQ,OAAO,QAAQ,QAAQ,OAAO,EAAE,IAAI,CAAC,MAAM,OAAO,oBAAoB,EAAE,SAAS,CAAC;AAAA,MAChG;AAAA,IACF;AAEA,QAAM,YAAY,CAAC,KAAK,OAAO,SAAS,EAAE,sBAAsB,OAAO,GAAG,gBAAgB;AACxF,YAAM,EAAE,QAAQ,YAAY,QAAQ,IAAI,aAAa,OAAO,OAAO;AAEnE,YAAM,UAAU,CAAC,QAAQ;AACvB,cAAM,OAAO,UAAU,GAAG;AAC1B,cAAM,EAAE,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,IAAI;AAErD,YAAI,WAAY,UAAU,OAAS,OAAM,IAAI,MAAM,0CAA0C;AAC7F,YAAI,OAAQ,QAAO,OAAO,oBAAoB,IAAI;AAClD,YAAI,UAAU,SAAS;AACrB,gBAAM,SAAS,UAAU;AACzB,gBAAM,QAAQ,UAAU,MAAM;AAC9B,cAAI,OAAQ,QAAO,OAAO,sCAAsC,MAAM,OAAO,OAAO;AACpF,iBAAO,OAAO,8BAA8B,SAAS,OAAO,OAAO,OAAO;AAAA,QAC5E,WAAW,UAAU,WAAW,QAAW;AAEzC,cAAI,wBAAwB,UAAU,CAAC,cAAc,IAAI,GAAG,MAAM,EAAE;AAClE,mBAAO,OAAO,oBAAoB,IAAI;AACxC,gBAAM,SAAS,UAAU;AACzB,gBAAM,QAAQ,UAAU,MAAM;AAC9B,cAAI,OAAQ,QAAO,OAAO,sCAAsC,MAAM,OAAO,MAAM;AACnF,iBAAO,OAAO,8BAA8B,QAAQ,OAAO,OAAO,MAAM;AAAA,QAC1E;AAEA,cAAM,IAAI,MAAM,6CAA6C;AAAA,MAC/D;AAEA,YAAM,gBAAgB,CAAC,KAAK,cAAc;AACxC,cAAM,MAAM,OAAO,KAAK;AACxB,YAAI,MAAM,OAAO,qCAAqC,KAAK,UAAU,GAAG,CAAC,GAAG,MAAM;AAChF,oBAAU,QAAQ,KAAK,KAAK,IAAI,GAAG,GAAG;AAAA,QACxC,CAAC;AAAA,MACH;AAEA,YAAM,WAAW,CAAC,KAAK,OAAO,cAAc;AAC1C,cAAM,IAAI,QAAQ;AAClB,cAAM,OAAO,UAAU,GAAG;AAC1B,YAAI,MAAM,OAAO,2CAA2C,GAAG,OAAO,GAAG,MAAM,CAAC,GAAG,MAAM;AACvF,oBAAU,QAAQ,KAAK,GAAG,sBAAsB,IAAI,GAAG,CAAC;AAAA,QAC1D,CAAC;AAAA,MACH;AAEA,YAAM,cAAc,CAAC,KAAK,QAAQ;AAEhC,cAAM,IAAI,IAAI,QAAQ,wBAAwB,EAAE;AAChD,YAAI,QAAQ,IAAI,CAAC,IAAK,QAAO,OAAO,eAAe,KAAK,IAAI,MAAM,GAAG,EAAE,CAAC;AACxE,YAAI,YAAY,IAAI,GAAG,EAAG,QAAO,OAAO,MAAM;AAI9C,YAAI,CAAC,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,SAAS,GAAG,EAAG,QAAO,OAAO,mBAAmB,KAAK,CAAC;AAC7F,YAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE,SAAS,GAAG,EAAG,QAAO,OAAO,qBAAqB,KAAK,CAAC;AAC3F,YAAI,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,EAAE,SAAS,GAAG,EAAG,QAAO,OAAO,mBAAmB,KAAK,CAAC;AAE/E,cAAM,OAAO,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,EAAE,KAAK,EAAE;AACxC,YAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,EAAE,SAAS,GAAG;AACjC,iBAAO,KAAK,WAAW,IAAI,OAAO,MAAM,IAAI,OAAO,mBAAmB,KAAK,IAAI;AACjF,YAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,SAAS,GAAG;AACnC,iBAAO,KAAK,WAAW,IAAI,OAAO,MAAM,IAAI,OAAO,qBAAqB,KAAK,IAAI;AAGnF,eAAO,OAAO,eAAe,WAAW,GAAG,GAAG,GAAG;AAAA,MACnD;AAEA,YAAM,UAAU,CAAC,MAAM,QAAQ;AAC7B,YAAI,CAAC,OAAO,OAAO,QAAQ,SAAU,QAAO,OAAO,aAAa,MAAM,GAAG;AAEzE,YAAI;AAEJ,cAAM,eAAe,CAAC,QAAQ,IAAI,UAAU,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,OAAO,MAAM,QAAQ;AAC7F,YAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,iBAAO,MAAM,IAAI,OAAO,EAAE,IAAI;AAC9B,cAAI,aAAa,GAAG,GAAG;AACrB,gBAAI,IAAI,OAAO,oBAAoB,MAAM,IAAI,MAAM;AACnD,qBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAK,KAAI,OAAO,uBAAuB,GAAG,MAAM,GAAG,IAAI,CAAC,CAAC;AACzF,mBAAO,OAAO,YAAY,MAAM,CAAC;AAAA,UACnC;AAAA,QACF,OAAO;AACL,iBAAO,MAAM,IAAI,QAAQ,EAAE,IAAI;AAC/B,gBAAM,CAAC,MAAM,MAAM,IAAI,CAAC,OAAO,KAAK,GAAG,GAAG,OAAO,OAAO,GAAG,CAAC;AAC5D,cAAI,aAAa,MAAM,GAAG;AACxB,gBAAI,IAAI,OAAO,iCAAiC,MAAM,KAAK,MAAM;AACjE,gBAAI,KAAK,SAAS,EAAG,OAAM,SAAS,UAAU;AAC9C,uBAAW,OAAO,KAAM,KAAI,OAAO,wBAAwB,GAAG,MAAM,GAAG;AACvE,uBAAW,OAAO,KAAM,KAAI,OAAO,uBAAuB,GAAG,MAAM,KAAK,IAAI,GAAG,CAAC;AAChF,mBAAO,OAAO,YAAY,MAAM,CAAC;AAAA,UACnC;AAAA,QACF;AAEA,cAAM,YAAY,UAAU;AAC5B,eAAO,OAAO,2BAA2B,MAAM,MAAM,GAAG;AAAA,MAC1D;AAEA,aAAO,EAAE,SAAS,eAAe,UAAU,aAAa,SAAS,QAAQ;AAAA,IAC3E;AAGA,QAAM,2BAA2B;AACjC,QAAM,8BAA8B;AACpC,QAAM,cAAc,OAAO,IAAI,aAAa;AAC5C,aAAS,UAAU,MAAM;AACvB,UAAI,OAAO,SAAS,YAAY;AAC9B,YAAI,KAAK,WAAW,EAAG,QAAO,KAAK,WAAW;AAE9C,YAAI,OAAO,eAAe,IAAI,MAAM,SAAS;AAC3C,gBAAM,IAAI,MAAM,yDAAyD;AAE3E,cAAM,cAAc,GAAG,IAAI;AAC3B,YAAI,KAAK,WAAW;AAClB,cAAI,CAAC,gBAAgB,KAAK,WAAW,EAAG,OAAM,IAAI,MAAM,qBAAqB;AAC7E,iBAAO;AAAA,QACT;AACA,YAAI,yBAAyB,KAAK,WAAW,KAAK,4BAA4B,KAAK,WAAW;AAC5F,iBAAO;AAGT,cAAM,IAAI,MAAM,wEAAwE;AAAA,MAC1F,WAAW,OAAO,SAAS,UAAU;AACnC,cAAM,QAAQ,OAAO,eAAe,IAAI;AACxC,YAAI,gBAAgB,UAAU,UAAU,OAAO,UAAW,QAAO,OAAO,MAAM,IAAI;AAClF,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AACA,YAAM,IAAI,MAAM,mCAAmC,OAAO,IAAI,EAAE;AAAA,IAClE;AAEA,WAAO,UAAU,EAAE,OAAO,WAAW,WAAW,UAAU;AAAA;AAAA;;;ACzK1D;AAAA;AAAA;AAEA,QAAM,EAAE,QAAQ,MAAM,QAAQ,IAAI;AAClC,QAAM,EAAE,UAAU,IAAI;AAQtB,QAAM,eAAe;AACrB,QAAM,aAAa;AAEnB,WAAO,UAAU,MAAM;AACrB,YAAM,QAAQ,CAAC;AACf,UAAI,SAAS;AAEb,YAAM,WAAW,CAAC,SAAS;AACzB,YAAI,WAAW,KAAK,KAAK,KAAK,EAAE,CAAC,CAAC,EAAG;AACrC,cAAM,KAAK,EAAE,QAAQ,MAAM,KAAK,CAAC;AACjC,YAAI,aAAa,KAAK,KAAK,KAAK,SAAS,CAAC,CAAC,EAAG;AAAA,MAChD;AAEA,YAAM,QAAQ,MAAM;AAClB,YAAI,WAAW,EAAG,OAAM,IAAI,MAAM,8BAA8B;AAChE,cAAM,SAAS,MAAM,IAAI,CAAC,SAAS,OAAO,QAAQ,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC,EAAE,KAAK,IAAI;AACxF,eAAO,oBAAoB,KAAK,MAAM,IAAI,UAAU,MAAM,KAAK,WAAW,MAAM;AAAA,MAClF;AAEA,YAAM,eAAe,CAAC,UAAU;AAC9B,cAAM,UAAU,OAAO,QAAQ,KAAK;AACpC,mBAAW,CAAC,KAAK,KAAK,KAAK,SAAS;AAClC,cAAI,CAAC,oBAAoB,KAAK,GAAG,EAAG,OAAM,IAAI,MAAM,uBAAuB;AAC3E,cAAI,EAAE,OAAO,UAAU,cAAc,iBAAiB;AACpD,kBAAM,IAAI,MAAM,yBAAyB;AAAA,QAC7C;AACA,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,cAAc;AAAA;AAAA,QACd,MAAM,MAAM,MAAM;AAAA,QAElB,MAAM,QAAQ,MAAM;AAClB,cAAI,OAAO,QAAQ,SAAU,OAAM,IAAI,MAAM,0BAA0B;AACvE,cAAI,IAAI,SAAS,IAAI,EAAG,OAAM,IAAI,MAAM,iCAAiC;AACzE,mBAAS,OAAO,KAAK,GAAG,IAAI,CAAC;AAC7B,iBAAO;AAAA,QACT;AAAA,QAEA,MAAM,QAAQ,WAAW,WAAW,OAAO;AACzC,gBAAM,YAAY;AAClB,eAAK,MAAM,QAAQ,MAAM;AACzB,gBAAM,SAAS,MAAM;AACrB,oBAAU;AACV,cAAI,WAAW,MAAM,QAAQ;AAE3B,kBAAM,IAAI;AACV,qBAAS;AACT,mBAAO;AAAA,UACT,WAAW,WAAW,MAAM,SAAS,KAAK,CAAC,UAAU;AAEnD,kBAAM,EAAE,KAAK,IAAI,MAAM,MAAM,SAAS,CAAC;AAEvC,gBAAI,CAAC,aAAa,KAAK,IAAI,GAAG;AAC5B,oBAAM,UAAU;AAChB,uBAAS;AACT,qBAAO,KAAK,MAAM,SAAS,QAAQ,IAAI;AAAA,YACzC;AAAA,UACF;AACA,iBAAO,KAAK,MAAM,GAAG;AAAA,QACvB;AAAA,QAEA,GAAG,WAAW,WAAW,WAAW;AAClC,cAAI,GAAG,SAAS,OAAO,SAAS;AAC9B,gBAAI,UAAW,WAAU;AACzB,gBAAI,UAAW,MAAK,eAAe;AAAA,UACrC,WAAW,GAAG,SAAS,OAAO,QAAQ;AACpC,gBAAI,UAAW,WAAU;AACzB,gBAAI,UAAW,MAAK,eAAe;AAAA,UACrC,WAAW,aAAa,KAAK,MAAM,OAAO,WAAW,SAAS,GAAG,WAAW,CAAC,CAAC,SAAS,GAAG;AACxF,gBAAI,UAAW,MAAK,MAAM,OAAO,MAAM,GAAG,SAAS;AAAA,UACrD,WAAW,WAAW;AACpB,iBAAK,GAAG,QAAQ,SAAS,GAAG,SAAS;AAAA,UACvC;AAAA,QACF;AAAA,QAEA,WAAW,QAAQ,CAAC,GAAG;AACrB,gBAAM,YAAY,aAAa,KAAK,EAAE;AAAA,YACpC,CAAC,CAAC,KAAK,GAAG,MAAM,SAAS,KAAK,GAAG,CAAC,MAAM,UAAU,GAAG,CAAC;AAAA,UACxD;AACA,iBAAO;AAAA;AAAA,EAAgC,UAAU,KAAK,IAAI,CAAC;AAAA,EAAK,MAAM,CAAC;AAAA,QACzE;AAAA,QAEA,aAAa,QAAQ,CAAC,GAAG;AACvB,gBAAM,eAAe,aAAa,KAAK;AACvC,gBAAM,OAAO,aAAa,IAAI,CAAC,UAAU,MAAM,CAAC,CAAC;AACjD,gBAAM,OAAO,aAAa,IAAI,CAAC,UAAU,MAAM,CAAC,CAAC;AAEjD,iBAAO,SAAS,GAAG,MAAM;AAAA,EAAiB,MAAM,CAAC,EAAE,EAAE,GAAG,IAAI;AAAA,QAC9D;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACvGA;AAAA;AAAA;AAEA,QAAM,gBAAgB;AAAA,MACpB,GAAG,CAAC,WAAW,aAAa;AAAA;AAAA,MAC5B,GAAG,CAAC,MAAM,OAAO,WAAW,QAAQ,eAAe,OAAO;AAAA;AAAA,MAC1D,GAAG,CAAC,iBAAiB,oBAAoB,kBAAkB,aAAa;AAAA,MACxE,GAAG,CAAC,QAAQ,YAAY,SAAS;AAAA;AAAA,MACjC,GAAG,CAAC,QAAQ,OAAO;AAAA;AAAA,MACnB,GAAG,CAAC,OAAO,SAAS,SAAS,SAAS,MAAM,QAAQ,MAAM;AAAA;AAAA,MAC1D,GAAG,CAAC,WAAW,WAAW,oBAAoB,oBAAoB,cAAc,aAAa;AAAA;AAAA,MAC7F,GAAG,CAAC,SAAS,YAAY,YAAY,mBAAmB,aAAa;AAAA;AAAA,MACrE,GAAG,CAAC,YAAY,eAAe,eAAe,aAAa;AAAA;AAAA,MAC3D,GAAG,CAAC,aAAa,aAAa,UAAU,SAAS;AAAA;AAAA,MACjD,GAAG,CAAC,mBAAmB,oBAAoB,eAAe;AAAA;AAAA,MAC1D,GAAG,CAAC,cAAc,iBAAiB,iBAAiB,wBAAwB,mBAAmB;AAAA;AAAA,MAC/F,GAAG,CAAC,eAAe;AAAA;AAAA,MACnB,GAAG,CAAC,gBAAgB,qBAAqB,oBAAoB,sBAAsB;AAAA;AAAA,MACnF,GAAG,CAAC,yBAAyB,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA,MAI/C,GAAG,CAAC,SAAS,eAAe,cAAc,YAAY,aAAa,YAAY,UAAU;AAAA;AAAA,MACzF,GAAG,CAAC,SAAS;AAAA;AAAA,MACb;AAAA;AAAA,MACA;AAAA;AAAA,IACF;AAGA,QAAM,eAAe;AAAA,MACnB,GAAG,CAAC,YAAY;AAAA;AAAA,MAChB,GAAG,CAAC,iBAAiB,eAAe;AAAA;AAAA,MACpC,GAAG,CAAC,YAAY,YAAY,YAAY,UAAU;AAAA;AAAA,IACpD;AACA,QAAM,iBAAiB,aAAa,IAAI,CAAC,UAAU,2BAA2B,KAAK,SAAS;AAE5F,QAAM,YAAY,CAAC,QAAQ,cAAc,cAAc,aAAa,UAAU,SAAS;AACvF,QAAM,YAAY;AAAA,MAChB,GAAG,CAAC,QAAQ,cAAc,eAAe,YAAY;AAAA,MACrD,GAAG,CAAC,aAAa,qBAAqB,oBAAoB,SAAS;AAAA,IACrE;AACA,QAAM,oBAAoB;AAAA,MACxB,GAAG,UAAU,IAAI,CAAC,MAAM,+CAA+C,CAAC,EAAE;AAAA,MAC1E,GAAG,UAAU,IAAI,CAAC,MAAM,+CAA+C,CAAC,EAAE;AAAA,IAC5E;AAEA,WAAO,UAAU,EAAE,eAAe,gBAAgB,kBAAkB;AAAA;AAAA;;;AC7CpE;AAAA;AAAA;AAEA,QAAM,EAAE,cAAc,IAAI;AAM1B,aAAS,QAAQ,KAAK,KAAK,OAAO,UAAU,QAAQ;AAClD,UAAI,CAAC,IAAI,IAAI,GAAG,EAAG,QAAO,IAAI,IAAI,KAAK,KAAK;AAC5C,UAAI,IAAI,IAAI,GAAG,MAAM,MAAO,OAAM,IAAI,MAAM,yBAAyB,OAAO,KAAK,GAAG,EAAE;AAAA,IACxF;AAEA,aAAS,QAAQ,QAAQ;AACvB,UAAI,CAAC,OAAO,SAAS,GAAG,EAAG,QAAO;AAClC,aAAO,OAAO,QAAQ,UAAU,CAAC,UAAU;AACzC,gBAAQ,OAAO;AAAA,UACb,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,mBAAO;AAAA,QACX;AAEA,cAAM,IAAI,MAAM,aAAa;AAAA,MAC/B,CAAC;AAAA,IACH;AAEA,aAAS,IAAI,KAAK,SAAS,SAAS;AAClC,UAAI,OAAO,QAAQ,SAAU,OAAM,IAAI,MAAM,sBAAsB;AACnE,UAAI,OAAO,YAAY,SAAU,OAAM,IAAI,MAAM,sBAAsB;AACvE,YAAM,QAAQ,QAAQ,MAAM,GAAG;AAC/B,UAAI,CAAC,CAAC,IAAI,GAAG,EAAE,SAAS,MAAM,MAAM,CAAC,EAAG,OAAM,IAAI,MAAM,sBAAsB;AAC9E,UAAI,MAAM,WAAW,EAAG,QAAO;AAE/B,UAAI,OAAO;AACX,iBAAW,QAAQ,OAAO;AACxB,YAAI,OAAO,SAAS,SAAU,OAAM,IAAI,MAAM,sBAAsB;AACpE,YAAI,QAAS,SAAQ,KAAK,IAAI;AAC9B,cAAM,OAAO,QAAQ,IAAI;AACzB,YAAI,OAAO,SAAS,SAAU,QAAO;AACrC,YAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,EAAG,QAAO;AAC9D,eAAO,KAAK,IAAI;AAAA,MAClB;AACA,aAAO;AAAA,IACT;AAEA,QAAM,gBAAgB;AAEtB,aAAS,SAAS,UAAU,KAAK;AAC/B,UAAI,OAAO,aAAa,YAAY,OAAO,QAAQ,SAAU,OAAM,IAAI,MAAM,kBAAkB;AAC/F,UAAI,IAAI,WAAW,EAAG,QAAO;AAC7B,YAAM,OAAO,SAAS,QAAQ,OAAO,EAAE;AACvC,UAAI,IAAI,WAAW,GAAG,EAAG,QAAO,GAAG,IAAI,GAAG,GAAG;AAC7C,UAAI,CAAC,KAAK,SAAS,GAAG,KAAK,cAAc,KAAK,GAAG,EAAG,QAAO;AAC3D,UAAI,cAAc,KAAK,IAAI,EAAG,QAAO,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC;AAC1D,UAAI,IAAI,WAAW,GAAG,EAAG,QAAO;AAChC,aAAO,CAAC,GAAG,KAAK,MAAM,GAAG,EAAE,MAAM,GAAG,EAAE,GAAG,GAAG,EAAE,KAAK,GAAG;AAAA,IACxD;AAEA,aAAS,aAAa,SAAS;AAC7B,YAAM,MAAM,QAAQ,IAAI,CAAC,QAAS,QAAQ,IAAI,OAAO,IAAI,OAAQ,EAAE;AACnE,aAAO,IAAI,OAAO,CAAC,OAAO,MAAM,OAAO,OAAO,QAAQ,EAAE,OAAO,UAAU,EAAE;AAAA,IAC7E;AAEA,QAAM,oBAAoB,CAAC,cAAc,qBAAqB,SAAS,aAAa;AACpF,QAAM,aAAa,CAAC,SAAS,QAAQ,YAAY,WAAW,SAAS;AACrE,QAAM,QAAQ,OAAO,MAAM;AAE3B,aAAS,SAAS,QAAQ,MAAM;AAC9B,YAAM,QAAQ,CAAC,KAAK,gBAAgB,UAAU;AAC5C,YAAI,CAAC,OAAO,OAAO,QAAQ,SAAU;AACrC,cAAM,MAAM,KAAK,GAAG;AACpB,YAAI,QAAQ,OAAW,QAAO,QAAQ,QAAQ,SAAY;AAC1D,mBAAW,KAAK,OAAO,KAAK,GAAG,GAAG;AAChC,cAAI,CAAC,iBAAiB,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,SAAS,CAAC,EAAG;AACzE,cAAI,CAAC,iBAAiB,WAAW,SAAS,CAAC,EAAG;AAC9C,gBAAM,OAAO,MAAM,IAAI,CAAC,GAAG,CAAC,iBAAiB,kBAAkB,SAAS,CAAC,CAAC;AAC1E,cAAI,SAAS,OAAW,QAAO;AAAA,QACjC;AAAA,MACF;AACA,aAAO,MAAM,MAAM;AAAA,IACrB;AAIA,aAAS,iBAAiB,MAAM,SAAS,KAAK,OAAO,IAAI;AACvD,YAAM,MAAM,SAAS,MAAM,GAAG;AAC9B,YAAM,UAAU,CAAC;AAEjB,YAAM,CAAC,MAAM,OAAO,EAAE,IAAI,IAAI,MAAM,GAAG;AACvC,YAAM,QAAQ,UAAU,IAAI;AAG5B,YAAM,QAAQ,CAAC,KAAK,SAAS,gBAAgB,OAAO,UAAU,UAAU;AACtE,YAAI,CAAC,OAAO,OAAO,QAAQ,SAAU;AAErC,cAAM,KAAK,IAAI,OAAO,IAAI;AAC1B,YAAI,OAAO;AACX,YAAI,MAAM,OAAO,OAAO,UAAU;AAChC,iBAAO,SAAS,MAAM,EAAE;AACxB,cAAI,SAAS,OAAQ,SAAS,QAAQ,UAAU,IAAK;AACnD,oBAAQ,KAAK,CAAC,KAAK,MAAM,OAAO,CAAC;AAAA,UACnC,WAAW,SAAS,QAAQ,MAAM,CAAC,MAAM,KAAK;AAC5C,kBAAM,UAAU,CAAC;AACjB,kBAAM,MAAM,IAAI,KAAK,OAAO,OAAO;AACnC,gBAAI,QAAQ,OAAW,SAAQ,KAAK,CAAC,KAAK,MAAM,SAAS,SAAS,aAAa,OAAO,CAAC,CAAC,CAAC;AAAA,UAC3F;AAAA,QACF;AACA,cAAM,SAAS,UAAU,IAAI,iBAAiB,IAAI;AAClD,YAAI,UAAU,OAAO,WAAW,UAAU;AACxC,cAAI,OAAO,SAAS,GAAG,EAAG,OAAM,IAAI,MAAM,2BAA2B;AACrE,cAAI,OAAO,WAAW,GAAG,EAAG,OAAM,IAAI,MAAM,8BAA8B;AAC1E,iBAAO,SAAS,MAAM,IAAI,MAAM,EAAE;AAClC,cAAI,SAAS,IAAK,SAAQ,KAAK,CAAC,KAAK,MAAM,OAAO,CAAC;AAAA,QACrD;AAEA,mBAAW,KAAK,OAAO,KAAK,GAAG,GAAG;AAChC,cAAI,CAAC,iBAAiB,CAAC,MAAM,QAAQ,GAAG,KAAK,CAAC,cAAc,SAAS,CAAC,EAAG;AACzE,cAAI,CAAC,iBAAiB,WAAW,SAAS,CAAC,EAAG;AAC9C,gBAAM,IAAI,CAAC,GAAG,MAAM,CAAC,iBAAiB,kBAAkB,SAAS,CAAC,CAAC;AAAA,QACrE;AACA,YAAI,CAAC,WAAW,IAAI,eAAgB,OAAM,KAAK,SAAS,eAAe,IAAI;AAAA,MAC7E;AACA,YAAM,MAAM,IAAI;AAGhB,UAAI,SAAS,KAAK,QAAQ,MAAM,EAAE,MAAM,MAAM,CAAC,MAAM,OAAO,UAAU,KAAK;AACzE,cAAM,UAAU,CAAC;AACjB,cAAM,MAAM,IAAI,MAAM,OAAO,OAAO;AACpC,YAAI,QAAQ,OAAW,SAAQ,KAAK,CAAC,KAAK,MAAM,aAAa,OAAO,CAAC,CAAC;AAAA,MACxE;AAGA,UAAI,QAAQ,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,MAAM,MAAM;AACnD,cAAM,aAAa,iBAAiB,QAAQ,IAAI,IAAI,GAAG,SAAS,IAAI,IAAI,IAAI,IAAI;AAChF,gBAAQ,KAAK,GAAG,WAAW,IAAI,CAAC,CAAC,KAAK,OAAO,KAAK,MAAM,CAAC,KAAK,OAAO,SAAS,MAAM,KAAK,CAAC,CAAC,CAAC;AAAA,MAC9F;AAGA,UAAI,QAAQ,IAAI,GAAG,EAAG,SAAQ,KAAK,CAAC,QAAQ,IAAI,GAAG,GAAG,QAAQ,IAAI,GAAG,GAAG,GAAG,CAAC;AAE5E,aAAO;AAAA,IACT;AAEA,aAAS,kBAAkB,QAAQ;AACjC,YAAM,UAAU,oBAAI,IAAI;AACxB,eAAS,QAAQ,CAAC,QAAQ;AACxB,YAAI,QAAQ,WAAW,IAAI,OAAO,IAAI,IAAK,QAAO;AAClD,cAAM,SAAS,IAAI;AACnB,YAAI,UAAU,OAAO,WAAW,UAAU;AACxC,cAAI,OAAO,SAAS,GAAG,EAAG,OAAM,IAAI,MAAM,kCAAkC;AAC5E,cAAI,CAAC,mBAAmB,KAAK,MAAM,EAAG,OAAM,IAAI,MAAM,+BAA+B,MAAM,EAAE;AAC7F,kBAAQ,SAAS,QAAQ,KAAK,gBAAgB;AAAA,QAChD;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAEA,QAAM,cAAc,CAAC,QAAQ,aAC3B,SAAS,QAAQ,CAAC,MAAM,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,MAAM,SAAS,SAAS,CAAC,CAAC,KAAK,MAAS,KAAK;AAE5F,QAAM,uBAAuB,CAAC,SAAS,OAAO,WAAW,UAAU;AACjE,UAAI,CAAC,MAAM,QAAQ,KAAK,EAAG,OAAM,IAAI,MAAM,8BAA8B;AAEzE,iBAAW,UAAU,OAAO;AAC1B,iBAAS,QAAQ,CAAC,QAAQ;AACxB,gBAAM,QAAQ,IAAI,OAAO,IAAI;AAC7B,gBAAM,KAAK,SAAS,OAAO,UAAU,WAAW,MAAM,QAAQ,MAAM,EAAE,IAAI;AAC1E,cAAI,MAAM,GAAG,SAAS,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,GAAG;AACjD,oBAAQ,SAAS,IAAI,KAAK,yBAAyB;AAAA,UACrD,WAAW,QAAQ,UAAU,CAAC,UAAU;AACtC,kBAAM,IAAI,MAAM,iDAAiD;AAAA,UACnE;AAAA,QACF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAEA,QAAM,eAAe,CAAC,OAAO,UAAU;AACrC,UAAI,MAAO,QAAO,qBAAqB,aAAa,KAAK,GAAG,OAAO,IAAI;AACvE,UAAI,OAAO;AACT,gBAAQ,OAAO,eAAe,KAAK,GAAG;AAAA,UACpC,KAAK,OAAO;AACV,mBAAO,IAAI,IAAI,OAAO,QAAQ,KAAK,CAAC;AAAA,UACtC,KAAK,IAAI;AACP,mBAAO,IAAI,IAAI,KAAK;AAAA,UACtB,KAAK,MAAM;AACT,mBAAO,qBAAqB,oBAAI,IAAI,GAAG,KAAK;AAAA,QAChD;AAAA,MACF;AACA,YAAM,IAAI,MAAM,uCAAuC;AAAA,IACzD;AAEA,WAAO,UAAU,EAAE,KAAK,UAAU,kBAAkB,mBAAmB,aAAa,aAAa;AAAA;AAAA;;;ACjMjG;AAAA;AAAA;AAEA,QAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMX,OAAO,CAAC,UAAU;AAChB,YAAI,MAAM,SAAS,IAAK,QAAO;AAC/B,cAAM,OAAO;AACb,YAAI,KAAK,KAAK,KAAK,EAAG,QAAO;AAC7B,YAAI,CAAC,MAAM,SAAS,GAAG,KAAK,oBAAoB,KAAK,KAAK,EAAG,QAAO;AACpE,cAAM,CAAC,MAAM,MAAM,GAAG,IAAI,IAAI,MAAM,MAAM,GAAG;AAC7C,YAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,WAAW,KAAK,KAAK,SAAS,MAAM,KAAK,SAAS,IAAK,QAAO;AACzF,YAAI,CAAC,iBAAiB,KAAK,IAAI,KAAK,CAAC,mCAAmC,KAAK,IAAI,EAAG,QAAO;AAC3F,eAAO,KAAK,MAAM,GAAG,EAAE,MAAM,CAAC,SAAS,wCAAwC,KAAK,IAAI,CAAC;AAAA,MAC3F;AAAA;AAAA,MAEA,UAAU,CAAC,UAAU;AACnB,YAAI,MAAM,UAAU,MAAM,SAAS,GAAG,IAAI,MAAM,KAAM,QAAO;AAC7D,cAAM,WAAW;AACjB,eAAO,SAAS,KAAK,KAAK;AAAA,MAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,MAAM,CAAC,UAAU;AACf,YAAI,MAAM,WAAW,GAAI,QAAO;AAChC,YAAI,MAAM,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM,KAAK;AACxC,cAAI,2CAA2C,KAAK,KAAK,EAAG,QAAO;AACnE,gBAAM,UAAU,MAAM,MAAM,oBAAoB;AAChD,cAAI,CAAC,QAAS,QAAO;AACrB,gBAAM,OAAO,QAAQ,CAAC,IAAI;AAC1B,iBAAO,OAAO,OAAO,KAAM,OAAO,MAAM,KAAK,OAAO,OAAO;AAAA,QAC7D;AACA,YAAI,MAAM,SAAS,IAAI,EAAG,QAAO,mCAAmC,KAAK,KAAK;AAC9E,eAAO,sDAAsD,KAAK,KAAK;AAAA,MACzE;AAAA;AAAA,MAEA,MAAM,CAAC,UAAU;AACf,YAAI,MAAM,SAAS,IAAI,KAAK,EAAG,QAAO;AACtC,cAAM,OAAO;AACb,YAAI,CAAC,KAAK,KAAK,KAAK,EAAG,QAAO;AAC9B,YAAI,CAAC,MAAM,KAAK,KAAK,EAAG,QAAO;AAC/B,cAAM,IAAI,MAAM,MAAM,oBAAoB;AAC1C,YAAI,KAAK,OAAO,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC,CAAC;AACxC,YAAI,EAAE,CAAC,MAAM,IAAK,OAAM,KAAK,KAAK,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC,KAAK,CAAC;AAAA,iBAClE,EAAE,CAAC,MAAM,IAAK,OAAM,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC,KAAK,CAAC;AACtE,eAAO,MAAM,KAAK,QAAQ,KAAK,KAAK;AAAA,MACtC;AAAA;AAAA;AAAA,MAGA,aAAa,CAAC,UAAU;AACtB,YAAI,MAAM,SAAS,KAAK,IAAI,IAAI,KAAK,EAAG,QAAO;AAC/C,cAAM,OAAO;AACb,cAAM,MAAM,MAAM,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM;AAC7C,YAAK,OAAO,MAAM,CAAC,MAAM,OAAQ,CAAC,KAAK,KAAK,KAAK,EAAG,QAAO;AAC3D,YAAI,MAAM,EAAE,MAAM,KAAK;AACrB,gBAAM,IAAI,MAAM,MAAM,EAAE,EAAE,MAAM,oBAAoB;AACpD,cAAI,KAAK,OAAO,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC,CAAC;AACxC,cAAI,EAAE,CAAC,MAAM,IAAK,OAAM,KAAK,KAAK,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC,KAAK,CAAC;AAAA,mBAClE,EAAE,CAAC,MAAM,IAAK,OAAM,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC,KAAK,CAAC;AACtE,cAAI,MAAM,KAAK,QAAQ,KAAK,KAAK,GAAI,QAAO;AAAA,QAC9C;AACA,YAAI,KAAK;AACP,cAAI,0CAA0C,KAAK,KAAK,EAAG,QAAO;AAClE,gBAAM,UAAU,MAAM,MAAM,mBAAmB;AAC/C,cAAI,CAAC,QAAS,QAAO;AACrB,gBAAM,OAAO,QAAQ,CAAC,IAAI;AAC1B,iBAAO,OAAO,OAAO,KAAM,OAAO,MAAM,KAAK,OAAO,OAAO;AAAA,QAC7D;AACA,YAAI,MAAM,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM,IAAK,QAAO,kCAAkC,KAAK,KAAK;AAC7F,eAAO,qDAAqD,KAAK,KAAK;AAAA,MACxE;AAAA;AAAA;AAAA,MAIA,MAAM,CAAC,OACL,GAAG,UAAU,MACb,uFAAuF,KAAK,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQhG,MAAM,CAAC,UAAU;AACf,YAAI,MAAM,SAAS,MAAM,MAAM,SAAS,EAAG,QAAO;AAClD,YAAI,KAAK,GAAG,KAAK,GAAG,MAAM,GAAG,QAAQ,OAAO,UAAU,OAAO,OAAO,GAAG,QAAQ;AAC/E,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAM,IAAI,MAAM,WAAW,CAAC;AAC5B,cAAI,MAAM,KAAK,SAAS,MAAM,MAAM,GAAI,QAAO;AAC/C,cAAI,KAAK,MAAM,KAAK,IAAI;AACtB,gBAAI,EAAE,MAAM,EAAG,QAAO;AAAA,UACxB,WAAW,MAAM,IAAI;AACnB,gBAAI,KAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,QAAS,QAAO;AACtD;AACA,kBAAM;AAAA,UACR,WAAW,MAAM,IAAI;AACnB,gBAAI,KAAK,KAAK,MAAM,EAAG,QAAO;AAC9B,gBAAI,SAAS,IAAI;AACf,kBAAI,MAAO,QAAO;AAClB,sBAAQ;AAAA,YACV,WAAW,MAAM,EAAG,SAAQ;AAC5B;AACA,kBAAM;AACN,sBAAU;AAAA,UACZ,WAAY,KAAK,MAAM,KAAK,OAAS,KAAK,MAAM,KAAK,IAAK;AACxD,gBAAI,KAAK,EAAG,QAAO;AACnB,gBAAI,EAAE,MAAM,EAAG,QAAO;AACtB,sBAAU;AAAA,UACZ,MAAO,QAAO;AACd,iBAAO;AAAA,QACT;AACA,YAAI,KAAK,KAAM,KAAK,MAAM,OAAO,KAAK,QAAQ,GAAK,QAAO;AAC1D,YAAI,SAAS,MAAM,WAAW,EAAG,QAAO;AACxC,YAAI,KAAK,KAAK,CAAC,gDAAgD,KAAK,KAAK,EAAG,QAAO;AACnF,cAAM,SAAS,KAAK,IAAI,IAAI;AAC5B,YAAI,CAAC,MAAO,QAAO,OAAO,UAAU,SAAS,MAAM;AACnD,gBAAQ,SAAS,MAAM,MAAM,KAAK;AAAA,MACpC;AAAA;AAAA;AAAA,MAGA,KAAK;AAAA;AAAA,MAEL,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,MAKjB,gBAAgB;AAAA;AAAA;AAAA,MAIhB,gBAAgB;AAAA;AAAA;AAAA,MAGhB,yBAAyB;AAAA;AAAA,MAGzB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,MAMN,UAAU,CAAC,UACT,MAAM,SAAS,KACf,MAAM,SAAS,OACd,oBAAoB,KAAK,KAAK,KAC5B,oCAAoC,KAAK,KAAK,KAC7C,4EAA4E,KAAK,KAAK;AAAA;AAAA,IAG9F;AAEA,QAAM,QAAQ;AAAA;AAAA,MAEZ,OAAO;AAAA,MACP,cAAc;AAAA;AAAA,MAGd,cAAc;AAAA,MACd,uBAAuB;AAAA,MACvB,aAAa;AAAA,MACb,sBAAsB;AAAA,MAEtB,QAAQ,CAAC,UAAU,MAAM,SAAS,MAAM,KAAK,uBAAuB,KAAK,KAAK;AAAA;AAAA;AAAA,MAI9E,6BAA6B;AAAA;AAAA,MAG7B,aAAa,KAAK;AAAA,MAClB,cAAc,KAAK;AAAA;AAAA,MAGnB,OAAO;AAAA;AAAA,IAGT;AAEA,QAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,MAKX,OAAO,CAAC,QAAQ;AACd,YAAI,IAAI,SAAS,IAAK,QAAO;AAC7B,cAAM,WAAW;AACjB,YAAI,SAAS,KAAK,GAAG,EAAG,QAAO;AAC/B,YAAI;AACF,cAAI,OAAO,KAAK,GAAG;AACnB,iBAAO;AAAA,QACT,SAAS,GAAG;AACV,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,IAEF;AAEA,WAAO,UAAU,EAAE,MAAM,OAAO,KAAK;AAAA;AAAA;;;AC/MrC;AAAA;AAAA;AA0CA,QAAM,QAAQ,CAAC,GAAG,MAAM,CAAC,GAAG,oBAAI,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK;AACxD,QAAM,YAAY,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACzD,QAAM,WAAW,CAAC,MAAM,IAAI,SAAS,EAAE,GAAG,KAAK,IAAI,SAAS,CAAC;AAC7D,QAAM,WAAW,CAAC,MAAM,IAAI,SAAS,UAAU,EAAE,GAAG,KAAK,IAAI,SAAS,CAAC,CAAC;AACxE,QAAM,YAAY,CAAC,MAAM,MAAM,QAAQ,CAAC,KAAK,SAAS,CAAC;AAEvD,QAAM,YAAY,CAAC,EAAE,OAAO,MAAM,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,OAAO;AAAA,MACzD,MAAM,OAAO,CAAC,GAAG,IAAI,EAAE,KAAK,IAAI;AAAA,MAChC,OAAO,UAAU,MAAM,OAAO,IAAI,WAAW,EAAE,SAAS;AAAA,MACxD,YAAY,UAAU,MAAM,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC,GAAI,EAAE,cAAc,CAAC,CAAE,EAAE,KAAK;AAAA,MAChF,UAAU,UAAU,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAI,EAAE,YAAY,CAAC,CAAE,EAAE,KAAK;AAAA,MACxE,UAAU,UAAU,MAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,GAAI,EAAE,YAAY,CAAC,CAAE,EAAE,KAAK;AAAA,MACxE,YAAY,UAAU,MAAM,QAAQ,KAAK,EAAE,cAAc;AAAA,MACzD,KAAK;AAAA,QACH,MAAM,UAAU,MAAM,OAAO,IAAI,QAAQ,EAAE,QAAQ;AAAA,QACnD,OAAO,UAAU,MAAM,OAAO,IAAI,IAAI,KAAK,IAAI,EAAE,SAAS,GAAG,EAAE,SAAS,CAAC;AAAA,QACzE,YAAY,UAAU,MAAM,QAAQ,IAAI,CAAC,IAAI,MAAM,EAAE,cAAc,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;AAAA,QACzF,UAAU,UAAU,MAAM,QAAQ,IAAI,CAAC,IAAI,MAAM,EAAE,YAAY,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;AAAA,MACrF;AAAA,MACA,SAAU,EAAE,WAAW,EAAE,UAAU,MAAM,QAAQ,KAAK,UAAU,MAAM,OAAO,MAAO;AAAA,IACtF;AAEA,QAAM,cAAc,MAAM,UAAU,CAAC,CAAC;AAItC,QAAM,WAAW,SAAS,CAAC,GAAG,OAAO;AAAA,MACnC,MAAM,EAAE,QAAQ,EAAE,OAAO,UAAU,EAAE,MAAM,EAAE,IAAI,IAAI,EAAE,QAAQ,EAAE,QAAQ;AAAA,MACzE,OAAO,KAAK,IAAI,EAAE,OAAO,EAAE,KAAK;AAAA,MAChC,YAAY,MAAM,EAAE,YAAY,EAAE,UAAU;AAAA,MAC5C,UAAU,MAAM,EAAE,UAAU,EAAE,QAAQ;AAAA,MACtC,UAAU,MAAM,EAAE,UAAU,EAAE,QAAQ;AAAA,MACtC,YAAY,EAAE,cAAc,EAAE;AAAA,MAC9B,KAAK;AAAA,QACH,MAAM,EAAE,IAAI,QAAQ,EAAE,IAAI;AAAA,QAC1B,OAAO,KAAK,IAAI,EAAE,IAAI,OAAO,EAAE,IAAI,KAAK;AAAA,QACxC,YAAY,MAAM,EAAE,IAAI,YAAY,EAAE,IAAI,UAAU;AAAA,QACpD,UAAU,MAAM,EAAE,IAAI,UAAU,EAAE,IAAI,QAAQ;AAAA,MAChD;AAAA,MACA,SAAS,EAAE,WAAW,EAAE;AAAA,IAC1B,EAAE;AAEF,QAAM,UAAU,CAAC,SAAS,UAAU,UAAU,QAAQ,IAAI,OAAO,SAAS,GAAG,EAAE,KAAK,KAAK;AAEzF,QAAM,iBAAiB,CAAC,EAAE,YAAY,GAAG,UAAU,KAAK,GAAG,EAAE,YAAY,GAAG,UAAU,KAAK,MAAM;AAE/F,YAAM,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,KAAK,EAAE,SAAS,IAAI,KAAK,KAAK,KAAK,CAAC,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC;AAC/F,YAAM,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,KAAK,EAAE,SAAS,IAAI,KAAK,KAAK,KAAK,CAAC,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC;AAE/F,YAAM,KAAK,KAAK,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC;AAClE,YAAM,KAAK,KAAK,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC;AAClE,aAAO,EAAE,YAAY,MAAM,IAAI,EAAE,GAAG,UAAU,MAAM,IAAI,EAAE,EAAE;AAAA,IAC9D;AAEA,QAAM,eAAe,CAAC,EAAE,YAAY,GAAG,UAAU,KAAK,GAAG,EAAE,YAAY,GAAG,UAAU,KAAK,MACvF,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,KAAK,EAAE,SAAS,IAAI,KAAK,KAAK,KAAK,CAAC,MAAM,QAAQ,GAAG,CAAC,CAAC,CAAC,KACnF,KAAK,MAAM,CAAC,MAAM,KAAK,SAAS,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC;AAIxD,QAAM,UAAU,SAAS,CAAC,GAAG,OAAO;AAAA,MAClC,MAAM,EAAE,QAAQ,EAAE,OAAO,MAAM,EAAE,MAAM,EAAE,IAAI,IAAI;AAAA,MACjD,OAAO,KAAK,IAAI,EAAE,OAAO,EAAE,KAAK;AAAA,MAChC,GAAG,eAAe,GAAG,CAAC;AAAA,MACtB,UACG,UAAU,EAAE,MAAM,QAAQ,KAAK,EAAE,YACjC,UAAU,EAAE,MAAM,QAAQ,KAAK,EAAE,YAClC,UAAU,EAAE,UAAU,EAAE,QAAQ;AAAA,MAClC,YAAY,EAAE,cAAc,EAAE;AAAA,MAC9B,KAAK;AAAA,QACH,MAAM,EAAE,IAAI,QAAQ,EAAE,IAAI;AAAA,QAC1B,OAAO,KAAK,IAAI,EAAE,IAAI,OAAO,EAAE,IAAI,KAAK;AAAA,QACxC,YAAY,MAAM,EAAE,IAAI,YAAY,EAAE,IAAI,UAAU;AAAA,QACpD,UAAU,MAAM,EAAE,IAAI,UAAU,EAAE,IAAI,QAAQ;AAAA,MAChD;AAAA,MACA,SAAS,EAAE,WAAW,EAAE;AAAA,IAC1B,EAAE;AAEF,QAAM,aAAa,CAAC,MAAM,UAAU,OAAO,OAAO,MAAM,SAAS,MAAM,KAAK,CAAC;AAE7E,QAAM,YAAY,SAAS,CAAC,EAAE,SAAS,OAAO,KAAK,GAAG,KAAK,OAAO;AAAA,MAChE,OAAO,UAAU,aAAa,WAAW,IAAI,QAAQ,SAAS,IAAI;AAAA,MAClE,YAAY,CAAC,KAAK,WAAW,SAAS,IAAI,MAAM,WAAW,CAAC,aAAa,MAAM,GAAG;AAAA,IACpF,EAAE;AAEF,WAAO,UAAU,EAAE,aAAa,UAAU,SAAS,YAAY,WAAW,aAAa;AAAA;AAAA;;;AC/HvF;AAAA;AAAA;AAEA,QAAM,EAAE,QAAQ,MAAM,SAAS,SAAS,UAAU,IAAI;AACtD,QAAM,SAAS;AACf,QAAM,EAAE,kBAAkB,UAAU,mBAAmB,YAAY,IAAI;AACvE,QAAM,UAAU;AAChB,QAAM,EAAE,WAAW,GAAG,UAAU,IAAI;AACpC,QAAM,EAAE,aAAa,IAAI;AACzB,QAAM,EAAE,WAAW,OAAO,UAAU,IAAI;AACxC,QAAM,EAAE,eAAe,gBAAgB,kBAAkB,IAAI;AAC7D,QAAM,EAAE,aAAa,UAAU,SAAS,YAAY,WAAW,aAAa,IAAI;AAEhF,QAAM,cAAc,oBAAI,IAAI,CAAC,eAAe,eAAe,UAAU,IAAI,MAAM,KAAK,GAAG,CAAC;AACxF,QAAM,iBAAiB,CAAC,QAAQ,WAAW,UAAU,WAAW,QAAQ;AAGxE,QAAM,cAAc,IAAI;AAAA,MACtB,OAAO,QAAQ;AAAA,QACb,SAAS,CAAC,QAAQ,OAAO,QAAQ;AAAA,QACjC,OAAO,CAAC,QAAQ,MAAM,QAAQ,GAAG,KAAK,OAAO,eAAe,GAAG,MAAM,MAAM;AAAA,QAC3E,QAAQ,CAAC,QAAQ,OAAO,OAAO,eAAe,GAAG,MAAM,OAAO;AAAA,QAC9D,QAAQ,CAAC,QAAQ,OAAO,SAAS,GAAG;AAAA,QACpC,SAAS,CAAC,QAAQ,OAAO,UAAU,GAAG,KAAK,OAAO;AAAA,QAClD,QAAQ,CAAC,QAAQ,OAAO,QAAQ;AAAA,QAChC,SAAS,CAAC,QAAQ,UAAU,UAAU,KAAK,KAAK,MAAM,KAAK,UAAU,GAAG,CAAC,CAAC;AAAA,MAC5E,CAAC;AAAA,IACH;AACA,QAAM,gBAAgB,YAAY,IAAI,QAAQ;AAC9C,QAAM,cAAc,CAAC,QAAQ,cAAc,GAAG,KAAK,OAAO,QAAQ;AAClE,QAAM,aAAa,CAAC,UAAU,UAAU,UAAU,OAAO,EAAE,MAAM,CAAC,EAAE,CAAC;AAErE,QAAM,oBAAoB,CAAC,SAAS,QAClC,eAAe,QAAQ,OAAO,IAAI,eAAe,QAAQ,2BAA2B,GAAG,SAAS;AAElG,QAAM,wBAAwB,CAAC,SAAS,QAAQ;AAC9C,YAAM,aAAa,GAAG,OAAO,GAAG,QAAQ,cAAc,UAAU,EAAE,QAAQ,MAAM,EAAE;AAClF,UAAI,CAAC,eAAe,SAAS,UAAU,EAAG,QAAO;AACjD,aAAO,kBAAkB,YAAY,GAAG;AAAA,IAC1C;AAGA,QAAM,UAAU,CAAC,QAAQ,SAAS,SAAS,OAAO,SAAS,UACzD,OAAO,OAAO,EAAE,QAAQ,SAAS,QAAQ,OAAO,CAAC;AACnD,QAAM,UAAU,CAAC,QAAQ,QAAQ,UAAU,UAAU,OAAO,OAAO,EAAE,QAAQ,QAAQ,QAAQ,CAAC;AAE9F,QAAM,kBAAkB,OAAO,iBAAiB;AAChD,QAAM,aAAa,OAAO,YAAY;AACtC,QAAM,gBAAgB,OAAO,eAAe;AAC5C,QAAM,gBAAgB,OAAO,eAAe;AAE5C,QAAM,gBAAgB,CAAC,WAAW;AAChC,UAAI,OAAO,WAAW,UAAW,QAAO;AACxC,UAAI,cAAc,MAAM,KAAK,OAAO,KAAK,MAAM,EAAE,WAAW,EAAG,QAAO;AACtE,aAAO;AAAA,IACT;AAEA,QAAM,yBAAyB,oBAAI,IAAI;AACvC,QAAM,WAAW,oBAAI,IAAI;AACzB,QAAM,eAAe,CAAC,MAAM,SAAS,SAAS,kBAAkB;AAC9D,UAAI,SAAS;AACX,cAAM,UAAU,QAAQ,QAAQ,cAAc,UAAU,EAAE,QAAQ,MAAM,EAAE;AAC1E,gBAAQ,eAAe,SAAS,OAAO,GAAG,8BAA8B,OAAO;AAC/E,iBAAS,IAAI,MAAM;AAAA,UACjB,eAAe,kBAAkB,SAAS,eAAe;AAAA,UACzD,mBAAmB,kBAAkB,SAAS,eAAe;AAAA,UAC7D,sBAAsB,kBAAkB,SAAS,eAAe;AAAA,UAChE,gBAAgB,CAAC,kBAAkB,SAAS,eAAe;AAAA,UAC3D,mBAAmB,CAAC,kBAAkB,SAAS,eAAe;AAAA,UAC9D,gBAAgB,CAAC,kBAAkB,SAAS,YAAY;AAAA,UACxD,YAAY,kBAAkB,SAAS,YAAY;AAAA,QACrD,CAAC;AAAA,MACH,OAAO;AACL,gBAAQ,CAAC,eAAe,qCAAqC;AAC7D,iBAAS,IAAI,MAAM,CAAC,CAAC;AAAA,MACvB;AAAA,IACF;AAEA,QAAM,gBAAgB,CAAC,QAAQ,MAAM,MAAM,OAAO,eAAe,OAAO;AACtE,YAAM;AAAA,QACJ,OAAO;AAAA,QACP,cAAc;AAAA,QACd,mBAAmB;AAAA;AAAA,QACnB,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ;AAAA,QACA;AAAA;AAAA,QACA,MAAM,WAAW;AAAA,QACjB,sBAAsB,KAAK,SAAS,SAAS,KAAK,SAAS;AAAA,QAC3D,mBAAmB,KAAK,SAAS,SAAS,KAAK,SAAS;AAAA,QACxD,gBAAgB,KAAK,SAAS;AAAA,QAC9B,oBAAoB,KAAK,SAAS;AAAA,QAClC,0BAA0B,KAAK,SAAS;AAAA,QACxC,mBAAmB,KAAK,SAAS;AAAA;AAAA,QACjC,mBAAmB,KAAK,SAAS;AAAA,QACjC,uBAAuB;AAAA;AAAA,QACvB,SAAS;AAAA;AAAA,QACT,iBAAiB;AAAA,QACjB,kBAAkB,KAAK,SAAS,UAAU,sBAAsB,KAAK,SAAS,eAAe;AAAA,QAC7F,SAAS,aAAa,CAAC;AAAA,QACvB,cAAc,KAAK,SAAS;AAAA,QAC5B,eAAe;AAAA,QACf;AAAA;AAAA,QACA,GAAG;AAAA,MACL,IAAI;AACJ,YAAM,OAAO;AAAA,QACX,GAAG,QAAQ;AAAA,QACX,GAAI,cAAc,QAAQ,OAAO,CAAC;AAAA,QAClC,GAAI,eAAe,QAAQ,QAAQ,CAAC;AAAA,QACpC,GAAG;AAAA,MACL;AACA,UAAI,OAAO,KAAK,OAAO,EAAE,WAAW;AAClC,cAAM,IAAI,MAAM,oBAAoB,OAAO,KAAK,OAAO,EAAE,KAAK,IAAI,CAAC,EAAE;AAEvE,UAAI,CAAC,CAAC,UAAU,OAAO,WAAW,MAAM,EAAE,SAAS,IAAI,EAAG,OAAM,IAAI,MAAM,iBAAiB,IAAI,EAAE;AACjG,UAAI,CAAC,iBAAiB,UAAW,OAAM,IAAI,MAAM,gDAAgD;AACjG,UAAI,iBAAiB,eAAgB,OAAM,IAAI,MAAM,sCAAsC;AAC3F,UAAI,SAAS,UAAU;AACrB,cAAM,aAAa,EAAE,mBAAmB,wBAAwB;AAChE,cAAM,SAAS,EAAE,GAAG,YAAY,iBAAiB,kBAAkB,cAAc;AACjF,cAAM,OAAO,EAAE,aAAa,oBAAoB;AAChD,mBAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,MAAM,EAAG,KAAI,CAAC,EAAG,OAAM,IAAI,MAAM,uBAAuB,CAAC,EAAE;AAC/F,mBAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,IAAI,EAAG,KAAI,EAAG,OAAM,IAAI,MAAM,uBAAuB,CAAC,EAAE;AAAA,MAC9F;AAEA,YAAM,EAAE,QAAQ,QAAQ,QAAQ,UAAU,IAAI,aAAa,KAAK;AAEhE,YAAM,YAAY,CAAC,SAAS;AAC1B,cAAM,OAAO,CAAC;AACd,YAAI,OAAO;AACX,eAAO,MAAM;AACX,cAAI,CAAC,KAAK,KAAM,MAAK,QAAQ,IAAI;AACjC,iBAAO,KAAK,UAAU,KAAK;AAAA,QAC7B;AAGA,YAAI,KAAK,MAAM,CAAC,SAAS,KAAK,WAAW,MAAS;AAChD,iBAAO,OAAO,MAAM,UAAU,KAAK,IAAI,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC;AAIhE,cAAM,cAAc,CAAC,GAAG;AACxB,cAAM,eAAe,MAAM;AACzB,gBAAM,QAAQ,YAAY,IAAI,UAAU,WAAW,EAAE,KAAK,GAAG;AAC7D,sBAAY,SAAS;AACrB,iBAAO;AAAA,QACT;AACA,YAAI,MAAM;AACV,mBAAW,EAAE,SAAS,QAAQ,OAAO,KAAK,MAAM;AAC9C,cAAI,SAAS;AACX,gBAAI,CAAC,OAAQ,OAAM,cAAc,UAAU;AAC3C,kBAAM,QAAQ,SAAS,UAAU,OAAO,mBAAmB,OAAO;AAClE,kBAAM,MAAM,GAAG,aAAa,CAAC;AAC7B,kBAAM,MAAM,OAAO,YAAY,KAAK,KAAK,KAAK,IAAI,OAAO,SAAS,KAAK,KAAK;AAAA,UAC9E,WAAW,OAAQ,aAAY,KAAK,MAAM;AAAA,QAC5C;AACA,eAAO,YAAY,SAAS,IAAI,OAAO,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,IAAI;AAAA,MAC/E;AAEA,YAAM,UAAU,OAAO,MAAM;AAC7B,UAAI,WAAW;AACf,YAAM,OAAO,IAAI,SAAS;AACxB,cAAM,MAAM,SAAS,GAAG,IAAI;AAC5B,aAAK,SAAS,SAAS;AACvB,eAAO;AAAA,MACT;AACA,YAAM,OAAO,IAAI;AAEjB,YAAM,UAAU,YAAY,QAAQ,CAAC,QAAQ,iBAAiB,aAAa,CAAC;AAC5E,YAAM,gBAAgB,KAAK,aAAa,KAAK,WAAW,YAAY,QAAQ,CAAC,gBAAgB,CAAC;AAC9F,YAAM,iBAAiB,MAAM;AAC3B,YAAI,CAAC,KAAK,aAAa,EAAG,QAAO,OAAO,EAAE;AAC1C,eAAO,gBAAgB,OAAO,mBAAmB,IAAI,OAAO,cAAc;AAAA,MAC5E;AACA,YAAM,iBAAiB,KAAK,aAAa,IAAI,OAAO,aAAa,IAAI,OAAO,EAAE;AAE9E,YAAM,MAAM,OAAO;AACnB,UAAI,MAAM,iCAAiC,gBAAgB,eAAe,CAAC;AAC3E,UAAI,cAAe,KAAI,MAAM,wBAAwB;AACrD,UAAI,UAAW,KAAI,MAAM,oBAAoB;AAC7C,UAAI,KAAK,UAAU,EAAG,KAAI,MAAM,kCAAkC;AAElE,UAAI,qBAAqB,KAAK,aAAa,IAAI,OAAO,cAAc,IAAI,OAAO,EAAE;AACjF,UAAI,eAAe;AACjB,YAAI,MAAM,uBAAuB;AACjC,6BAAqB,OAAO,sCAAsC;AAAA,MACpE;AAEA,YAAM,UAAU,UAAU,KAAK,OAAO,SAAS,EAAE,sBAAsB,OAAO,GAAG,WAAW;AAC5F,YAAM,EAAE,SAAS,eAAe,UAAU,aAAa,QAAQ,IAAI;AAEnE,YAAM,eAAe,CAAC;AACtB,YAAM,UAAU,MAAM,SAAS,IAAI,IAAI;AACvC,YAAM,gBAAgB,eAAe,CAAC,YAAY,IAAI,CAAC;AACvD,YAAM,QAAQ,CAAC,QAAQ,SAAS,SAAS,MAAM,YAAY,QAAQ,CAAC,GAAG,EAAE,UAAU,IAAI,CAAC,MAAM;AAE5F,cAAM,QAAQ,QAAQ,SAAS,KAAK,QAAQ,QAAQ,SAAS,CAAC,EAAE,SAAS;AACzE,cAAM,eAAe,MAAM,QAAQ,OAAO,CAAC,MAAM,EAAE,SAAS,OAAO;AACnE,cAAM,oBACJ,CAAC,QAAQ,UAAU,QAAQ,WAAY,QAAQ,UAAU,UAAW,aAAa,EAAE,SAAS;AAE9F,cAAM,OAAO,UAAU,OAAO;AAC9B,cAAM,cAAc,IAAI,SAAS,QAAQ,SAAS,GAAG,IAAI;AAEzD,cAAM,QAAQ,CAAC,EAAE,OAAO,CAAC,GAAG,OAAO,SAAS,QAAQ,OAAO,MAAM;AAC/D,gBAAM,UAAU,UAAU,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC;AAClD,gBAAM,QAAQ,gBAAgB,UAAU,IAAI,IAAI;AAChD,cAAI,kBAAkB,QAAQ,UAAU,QAAQ;AAE9C,kBAAM,aAAa,UAAU;AAC7B,kBAAM,OAAO,CAAC,QAAQ,SAAS,KAAK;AACpC,gBAAI,WAAW;AACb,kBAAI,MAAM,oDAAoD;AAC9D,kBAAI,MAAM,+DAA+D,GAAG,IAAI;AAAA,YAClF,MAAO,KAAI,MAAM,iDAAiD,GAAG,IAAI;AAAA,UAC3E,WAAW,kBAAkB,QAAQ,QAAQ;AAC3C,kBAAM,UAAU,OAAO,iDAAiD,SAAS,KAAK;AACtF,gBAAI,WAAW;AACb,kBAAI,MAAM,4BAA4B,QAAQ,MAAM;AACpD,kBAAI,MAAM,eAAe,QAAQ,OAAO;AAAA,YAC1C,MAAO,KAAI,MAAM,aAAa,QAAQ,OAAO;AAAA,UAC/C;AACA,cAAI,OAAQ,YAAW,MAAM;AAC7B,cAAI,UAAW,KAAI,MAAM,cAAc;AAAA,cAClC,KAAI,MAAM,cAAc;AAAA,QAC/B;AACA,cAAM,UAAU,CAAC,WAAW,cAAc,IAAI,GAAG,WAAW,MAAM,MAAM,SAAS,CAAC;AAElF,YAAI,YAAY,CAAC,MAAM,WAAY,OAAM,aAAa,CAAC;AACvD,cAAM,OAAO,CAAC,KAAK,UAAU;AAC3B,gBAAM,UAAU,UAAU,SAAY,IAAI,KAAK,UAAU,KAAK,CAAC,KAAK;AACpE,gBAAM,kBAAkB,SAAS,cAAc,UAAU,UAAU,CAAC;AACpE,gBAAM,UAAU,GAAG,GAAG,GAAG,OAAO,OAAO,eAAe;AACtD,cAAI,SAAU,QAAO,MAAM,WAAW,KAAK,EAAE,SAAS,iBAAiB,OAAO,CAAC;AAC/E,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB;AACA,cAAM,kBAAkB,CAAC,KAAK,QAAQ;AACpC,cAAI;AACF,mBAAO,YAAY,KAAK,GAAG;AAAA,UAC7B,SAAS,GAAG;AACV,iBAAK,EAAE,OAAO;AACd,mBAAO,OAAO,OAAO;AAAA,UACvB;AAAA,QACF;AACA,cAAM,UAAU,CAAC,OAAO,SAAS,MAAM,KAAK,GAAG,IAAI;AACnD,cAAM,UAAU,CAAC,OAAO,SAAS,QAAQ,SAAS,SAAS,SAAS,UAAU,IAAI,GAAG,IAAI;AACzF,cAAM,gBAAgB,CAAC,GAAG,MAAM,QAAQ,EAAE,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,cAAc;AAC/F,cAAM,oBAAoB,CAAC,KAAK,SAAS,0BACvC,QAAQ,CAAC,mBAAmB,uBAAuB,GAAG,IAAI,MAAM,EAAE;AACpE,cAAM,UAAU,IAAI,SAAS,CAAC,GAAG,YAAY,GAAG,IAAI;AACpD,cAAM,YAAY,CAAC,QACjB,QAAQ,CAAC,oBAAoB,CAAC,aAAa,6CAA6C,GAAG,EAAE;AAC/F,cAAM,UAAU,CAAC,KAAK,QAAQ,QAAQ,CAAC,kBAAkB,sBAAsB,GAAG,IAAI,GAAG;AACzF,cAAM,WAAW,CAAC,SAAS,aAAa,MAAM,QAAQ,gBAAgB,SAAS,aAAa;AAG5F,cAAMA,QAAO,YAAY;AACzB,cAAM,gBAAgB,CAAC,UAAU,WAAWA,OAAM,KAAK;AAEvD,YAAI,OAAO,SAAS,WAAW;AAC7B,cAAI,SAAS,MAAM;AACjB,8BAAkB,iBAAiB,gBAAgB;AACnD,mBAAO,EAAE,MAAAA,MAAK;AAAA,UAChB;AACA,kBAAQ,qBAAqB,QAAQ,SAAS,OAAO,QAAQ,OAAO,GAAG,CAAC,CAAC;AACzE,wBAAc,EAAE,MAAM,CAAC,EAAE,CAAC;AAC1B,iBAAO,EAAE,MAAAA,MAAK;AAAA,QAChB;AAEA,gBAAQ,cAAc,IAAI,GAAG,yBAAyB;AACtD,mBAAW,OAAO,OAAO,KAAK,IAAI;AAChC,kBAAQ,cAAc,SAAS,GAAG,KAAK,qBAAqB,0BAA0B,GAAG;AAE3F,YAAI,OAAO,KAAK,IAAI,EAAE,WAAW,GAAG;AAClC,4BAAkB,oBAAoB,gBAAgB;AACtD,iBAAO,EAAE,MAAAA,MAAK;AAAA,QAChB;AAEA,cAAM,SAAS,IAAI,IAAI,OAAO,KAAK,IAAI,CAAC;AACxC,cAAM,kBAAkB,oBAAI,IAAI;AAChC,cAAM,UAAU,CAAC,SAAS,cAAc;AACtC,kBAAQ,gBAAgB,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,GAAG,kCAAkC,IAAI;AAC7F,kBAAQ,UAAU,OAAO,MAAM,IAAI,GAAG,2BAA2B,IAAI;AACrE,kBAAQ,UAAU,MAAM,CAAC,MAAM,YAAY,IAAI,CAAC,CAAC,GAAG,8BAA8B;AAClF,kBAAQ,UAAU,KAAK,CAAC,MAAM,YAAY,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,GAAG,uBAAuB,IAAI;AAC1F,iBAAO,OAAO,IAAI;AAAA,QACpB;AACA,cAAM,MAAM,CAAC,SAAS,cAAc;AAClC,cAAI,KAAK,IAAI,MAAM,OAAW,SAAQ,MAAM,GAAG,SAAS;AACxD,iBAAO,KAAK,IAAI;AAAA,QAClB;AACA,cAAM,SAAS,CAAC,MAAM,WAAW,SAAS,YAAY,CAAC,MAAM;AAC3D,cAAI,KAAK,IAAI,MAAM,OAAW,QAAO;AAErC,kBAAQ,MAAM,GAAG,SAAS;AAC1B,cAAI,YAAY,MAAM;AACpB,gBAAI;AACF,oBAAM,YAAY,QAAQ,KAAK,IAAI,CAAC;AACpC,kBAAI,cAAc,KAAM,SAAQ,WAAW,EAAE,MAAM,CAAC,IAAI,GAAG,GAAG,UAAU,CAAC;AAAA,YAC3E,SAAS,GAAG;AACV,kBAAI,YAAY,CAAC,EAAE,QAAQ,WAAW,QAAQ,GAAG;AAC/C,qBAAK,EAAE,OAAO;AAAA,cAChB,OAAO;AACL,sBAAM;AAAA,cACR;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,YAAI,SAAS,MAAM;AACjB,mBAAS,IAAI,WAAW,QAAQ,CAAC;AACjC,iBAAO,eAAe,CAAC,QAAQ,GAAG,CAAC,gBAAgB;AACjD,uBAAW,CAAC,OAAO,IAAI,KAAK,OAAO,QAAQ,WAAW,GAAG;AACvD,kBAAI,SAAS,MAAO;AACpB,sBAAQ,SAAS,QAAQ,kBAAkB,SAAS,KAAK,GAAG,uBAAuB,KAAK;AAAA,YAC1F;AACA,mBAAO;AAAA,UACT,CAAC;AAAA,QACH,WAAW,CAAC,QAAQ,EAAG,UAAS,KAAK,OAAO;AAE5C,YAAI,QAAQ,EAAE,gBAAgB;AAE5B,qBAAW,QAAQ,CAAC,YAAY,eAAe,aAAa,EAAG,iBAAgB,IAAI,IAAI;AAAA,QACzF;AAEA,eAAO,YAAY,CAAC,OAAO,GAAG,IAAI;AAClC,eAAO,WAAW,CAAC,SAAS,GAAG,IAAI;AACnC,mBAAW,UAAU,CAAC,SAAS,eAAe,UAAU,EAAG,QAAO,QAAQ,CAAC,QAAQ,GAAG,IAAI;AAC1F,mBAAW,UAAU,CAAC,cAAc,YAAY,WAAW,EAAG,QAAO,QAAQ,CAAC,SAAS,GAAG,IAAI;AAE9F,eAAO,SAAS,CAAC,QAAQ,GAAG,IAAI,KAAK,OAAO,eAAe,CAAC,QAAQ,GAAG,IAAI;AAE3E,cAAM,aAAa,CAAC,KAAK,MAAM,SAC7B,QAAQ,SAAS,KAAK,UAAU,IAAI,OAAO,GAAG,KAAK,cAAc,KAAK,MAAM,MAAM,OAAO,IAAI;AAC/F,cAAM,WAAW,MAAO,cAAc,SAAS,IAAI,cAAc,cAAc,SAAS,CAAC,IAAI;AAC7F,cAAM,sBAAsB,cAAc;AAC1C,cAAM,QAAQ,CAAC,QAAQ;AACrB,wBAAc,KAAK,SAAS,SAAS,GAAG,GAAG,CAAC;AAC5C,iBAAO;AAAA,QACT;AAGA,YAAI,CAAC,QAAQ,EAAE,iBAAiB,CAAC,KAAK,MAAM;AAC1C,iBAAO,OAAO,CAAC,QAAQ,GAAG,KAAK,KAAK,OAAO,MAAM,CAAC,QAAQ,GAAG,KAAK;AAClE,iBAAO,WAAW,CAAC,QAAQ,GAAG,IAAI;AAClC,iBAAO,kBAAkB,CAAC,QAAQ,GAAG,IAAI;AAEzC,cAAI,KAAK,oBAAoB,CAAC,kBAAkB;AAC9C,mBAAO,oBAAoB,CAAC,SAAS,GAAG,CAAC,gBAAgB;AACvD,kBAAI,YAAa,cAAa,KAAK,CAAC,MAAM,MAAM,SAAS,CAAC,CAAC;AAC3D,qBAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF;AAGA,cAAM,aAAa,kBAAkB,SAAS,UAAU,KAAK,MAAM,KAAK;AACxE,YAAI,YAAY;AACd,gBAAM,aAAa,kBAAkB,IAAI;AACzC,cAAI,SAAS,OAAQ,KAAI,MAAM,sBAAsB;AACrD,qBAAW,CAAC,KAAK,QAAQ,KAAK,YAAY;AACxC,kBAAM,WAAW,iBAAiB,MAAM,SAAS,IAAI,GAAG,IAAI,SAAS,CAAC;AACtE,kBAAM,CAAC,KAAK,SAAS,IAAI,IAAI,SAAS,CAAC,KAAK,CAAC;AAC7C,oBAAQ,QAAQ,UAAU,yCAAyC,GAAG,EAAE;AACxE,kBAAM,IAAI,WAAW,KAAK,SAAS,IAAI;AACvC,gBAAI,MAAM,wBAAwB,IAAI,GAAG,IAAI,CAAC;AAAA,UAChD;AAAA,QACF;AAGA,cAAM,kBAAkB,CAACC,UACvB,KAAK,UAAU,MAAM,KAAKA,KAAI,KAAK,KAAKA,KAAI,MAAM,SAAS,SAAS;AACtE,cAAMC,SAAQ,OAAO,OAAO;AAAA,UAC1B,MAAM,gBAAgB,kBAAkB,IAAI,OAAO,eAAe,IAAI;AAAA,UACtE,OAAO,gBAAgB,kBAAkB,IAAI,OAAO,gBAAgB,IAAI;AAAA,UACxE,OAAO,gBAAgB,uBAAuB,IAAI,OAAO,gBAAgB,IAAI;AAAA,QAC/E,CAAC;AACD,cAAM,MAAM,OAAO,OAAO;AAAA,UACxB,MAAMA,OAAM,QAAQ,MAAM;AAAA,UAC1B,OAAOA,OAAM,SAAS,MAAM;AAAA,UAC5B,OAAOA,OAAM,SAAS,MAAM;AAAA,QAC9B,CAAC;AACD,cAAM,iBAAiB,OACpB,CAAC,IAAI,SAASF,MAAK,UAAU,cAAc,CAAC,IAAI,SAASA,MAAK,WAAW,SAAS,IAAI;AACzF,cAAM,uBAAuB,CAAC,UAAU;AAEtC,cAAI,IAAI,QAAQ,MAAM,QAAQA,MAAK,UAAU;AAC3C,gBAAI,MAAM,eAAe,IAAI,MAAM,MAAM,IAAI;AAC/C,cAAI,IAAI,SAAS,MAAM,QAAQA,MAAK,MAAO,KAAI,MAAM,eAAe,IAAI,OAAO,MAAM,KAAK;AAC1F,cAAI,IAAI,UAAU,MAAM,cAAc,CAAC,GAAG,SAAS,IAAI,KAAK,CAACA,MAAK,WAAW,SAAS,IAAI,GAAG;AAC3F,gBAAI,MAAM,oBAAoB,IAAI,KAAK;AAAA,UACzC,WAAW,IAAI,OAAO;AACpB,kBAAM,SAAS,CAACG,aAAYC,cAAa,aAAaJ,OAAM,EAAE,YAAAG,aAAY,UAAAC,UAAS,CAAC;AACpF,kBAAM,cAAc,MAAM,cAAc,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1E,kBAAM,YAAY,MAAM,YAAY,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACtE,gBAAI,WAAW,SAAS,EAAG,KAAI,MAAM,qBAAqB,IAAI,OAAO,UAAU;AAC/E,gBAAI,SAAS,SAAS,EAAG,KAAI,MAAM,qBAAqB,IAAI,OAAO,QAAQ;AAC3E,uBAAW,OAAO,MAAM,kBAAkB,CAAC,EAAG,KAAI,MAAM,kBAAkB,IAAI,OAAO,GAAG;AAAA,UAC1F;AAAA,QACF;AACA,cAAM,wBAAwB,CAAC,QAAQ,MAAM,OAAO,UAAU;AAC5D,cAAI,UAAUJ,KAAI,EAAE,SAAS,OAAO,QAAQ;AAC1C,gBAAI,MAAM,kBAAkB,OAAO,MAAM,IAAI;AAC/C,cAAI,UAAUA,KAAI,EAAE,SAAS,OAAO,SAAS;AAC3C,gBAAI,MAAM,kBAAkB,OAAO,OAAO,KAAK;AACjD,cAAI,UAAUA,KAAI,EAAE,cAAc,OAAO,SAAS,OAAO;AACvD,gBAAI,MAAM,wBAAwB,OAAO,OAAO,KAAK;AACrD,gBAAI,MAAM,wBAAwB,OAAO,OAAO,KAAK;AAAA,UACvD;AAAA,QACF;AAEA,cAAM,gBAAgB,MAAM;AAC1B,cAAI,CAAC,KAAK,aAAa,EAAG,QAAO,OAAO,EAAE;AAC1C,cAAI,aAAa,WAAW,EAAG,QAAO,OAAO,aAAa;AAC1D,iBAAO,OAAO,qBAAqB,WAAW,GAAG,aAAa,CAAC,CAAC,CAAC;AAAA,QACnE;AACA,cAAM,WAAW,CAAC,GAAG,cAAc;AAGjC,gBAAM,QAAS,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE,eAAe,KAAM,EAAE,SAAS,KAAK;AACzE,wBAAc,KAAK;AACnB,gBAAM,OAAO,OAAO,cAAc,GAAG,MAAM,cAAc,GAAG,kBAAkB;AAC9E,cAAI,CAAC,iBAAiB,eAAe,EAAG,QAAO,OAAO,OAAO,IAAI;AACjE,gBAAM,MAAM,OAAO,KAAK;AACxB,gBAAM,MAAM,OAAO,KAAK;AACxB,gBAAM,SAAS,OAAO,QAAQ;AAC9B,cAAI,cAAe,KAAI,MAAM,8BAA8B,GAAG;AAC9D,cAAI,MAAM,iBAAiB,KAAK,IAAI;AACpC,cAAI,cAAe,KAAI,MAAM,wBAAwB,QAAQ,CAAC;AAC9D,cAAI,cAAe,KAAI,MAAM,wBAAwB,GAAG;AACxD,kBAAQ,QAAQ,GAAG,GAAG,EAAE,GAAG,WAAW,QAAQ,OAAO,CAAC;AAEtD,cAAI,GAAG,KAAK,MAAM;AAChB,kBAAM,OAAO,UAAU,KAAK,EAAE,QAAQ,OAAO,0BAA0B,CAAC,IAAI;AAC5E,kBAAM,QAAQ,UAAU,KAAK,EAAE,QAAQ,OAAO,0BAA0B,CAAC,IAAI;AAC7E,kBAAM,QAAQ,UAAU,KAAK,EAAE,aAAa,OAAO,0BAA0B,CAAC,IAAI;AAClF,kCAAsB,KAAK,MAAM,OAAO,KAAK;AAAA,UAC/C,CAAC;AAED,iBAAO;AAAA,QACT;AAKA,cAAM,QAAQ,CAAC,KAAK,UAAU,OAAO,IAAI,MAAM,CAAC,MAAM,MAAM,SAAS,CAAC,CAAC;AACvE,cAAM,SAAS,CAAC,KAAK,aAAa,SAAS,KAAK,CAAC,MAAM,QAAQ,QAAQ,IAAI,SAAS,CAAC,CAAC;AAEtF,cAAM,oBAAoB,IAAI,UAAU,aAAa,EAAE,KAAK,CAAC,MAAM,MAAM,EAAE,KAAK,MAAM,KAAK,CAAC;AAC5F,cAAM,iBAAiB,IAAI,UAAU,MAAMA,MAAK,MAAM,KAAK,KAAK,kBAAkB,GAAG,KAAK;AAC1F,cAAM,iBAAiB,IAAI,aACzB,OAAOA,MAAK,MAAM,QAAQ,KAAK,aAAa,EAAE,MAAM,CAAC,MAAM,OAAO,EAAE,KAAK,MAAM,QAAQ,CAAC;AAE1F,cAAM,eAAe,CAAC,QAAQ,SAAS,SAAS;AAC9C,kBAAQ,OAAO,WAAW,UAAU,oBAAoB,MAAM;AAC9D,cAAI,qBAAqB;AACvB,oBAAQ,WAAW,KAAK,MAAM,GAAG,uCAAuC,MAAM;AAChF,cAAI,uCAAuC,KAAK,MAAM,KAAK,OAAO,cAAc;AAC9E,oBAAQ,8CAA8C,MAAM;AAAA,QAChE;AAGA,cAAM,cAAc,KAAK,WAAW,CAAC,YAAY,IAAI,KAAK,OAAO;AACjE,cAAM,cAAc,KAAK,eAAe,eAAe,KAAK,qBAAqB,KAAK;AACtF,cAAM,OAAO,aAAa,cAAc,OAAO,MAAM,IAAI;AACzD,cAAM,WAAW,CAAC,YAAY,cAC5B,IAAI,GAAG,cAAc,SAAS,OAAO,OAAO,qBAAqB,IAAI,IAAI,MAAM,SAAS;AAE1F,cAAM,cAAc,MAAM,CAAC,GAAG,SAAS,EAAE,MAAAA,OAAM,MAAM,QAAQ,CAAC;AAE9D,cAAM,OAAO,IAAI,SAAS,MAAM,QAAQ,YAAY,GAAG,GAAG,IAAI,EAAE;AAChE,cAAM,UAAU,CAAC,WAAW,SAAS;AACnC,cAAI,KAAK,CAAC,MAAM,SAAS;AACvB,kBAAM,WAAW,cAAc,KAAK,CAAC,CAAC;AACtC,gBAAI,aAAa,KAAM,QAAO,EAAE,KAAK,OAAO,MAAM,GAAG,OAAO,CAAC,EAAE;AAC/D,gBAAI,aAAa,MAAO,QAAO,EAAE,KAAK,OAAO,OAAO,GAAG,OAAO,EAAE,MAAM,CAAC,EAAE,EAAE;AAAA,UAC7E;AACA,gBAAM,MAAM,OAAO,KAAK;AACxB,cAAI,MAAM,uBAAuB,GAAG;AACpC,cAAI,UAAW,KAAI,MAAM,oBAAoB;AAC7C,gBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,QAAQ,YAAY,GAAG,GAAG,IAAI;AAC5D,cAAI,WAAW;AACb,gBAAI,MAAM,yBAAyB;AAAA,UACrC,MAAO,KAAI,MAAM,aAAa;AAC9B,cAAI,MAAM,MAAM;AAChB,iBAAO,EAAE,KAAK,MAAM;AAAA,QACtB;AAEA,cAAM,WAAW,MAAM;AACrB,gBAAM,SAAS,iBAAiB,YAAY,OAAO,QAAQ,IAAI;AAC/D,cAAI,OAAQ,KAAI,MAAM,iBAAiB,MAAM;AAC7C,iBAAO;AAAA,QACT;AACA,cAAM,aAAa,CAAC,WAAW;AAC7B,cAAI,WAAW,QAAQ,WAAW,KAAM;AACxC,cAAI,GAAG,QAAQ,MAAM,IAAI,MAAM,kBAAkB,QAAQ,MAAM,CAAC;AAAA,QAClE;AAGA,cAAM,uBAAuB,MAAM;AACjC,cAAI,CAAC,iBAAkB,QAAO;AAC9B,cAAI,qBAAqB,KAAM,QAAO;AACtC,cAAI,qBAAqB,WAAW;AAClC,gBAAI,CAAC,KAAK,iBAAkB,QAAO;AACnC,oBAAQ,oBAAoB,SAAS;AACrC,mBAAO;AAAA,UACT;AACA,gBAAM,IAAI,MAAM,6BAA6B,gBAAgB,EAAE;AAAA,QACjE;AACA,cAAM,kBAAkB,CAAC,UAAU,OAAO,UAAU;AAClD,gBAAM,UAAU,OAAO,UAAU,CAAC,UAAU,SAAS,GAAG,CAAC,cAAc;AACrE,gBAAI,cAAc,SAAS,qBAAqB,GAAG;AACjD,kBAAI,MAAM,sCAAsC,MAAM,OAAO,MAAM,KAAK;AACxE,qBAAO;AAAA,YACT;AACA,gBAAI,cAAc,SAAS,CAAC,MAAO,QAAO,OAAO,kBAAkB,MAAM,KAAK;AAC9E,qBAAS,SAAS,OAAO,CAAC,MAAM,MAAM;AACpC,kBAAI,MAAO,KAAI,MAAM,oBAAoB,MAAM,CAAC,CAAC;AACjD,qBAAO,KAAK,MAAM,WAAW,QAAQ,QAAQ,CAAC;AAAA,YAChD,CAAC;AACD,mBAAO;AAAA,UACT,CAAC;AACD,cAAI,QAAS,eAAc,EAAE,OAAO,SAAS,CAAC;AAAA,QAChD;AACA,cAAM,uBAAuB,CAAC,UAAU,cAAc;AACpD,gBAAM,UAAU,OAAO,UAAU,CAAC,UAAU,SAAS,GAAG,CAAC,cAAc;AACrE,0BAAc,SAAS,CAAC,KAAK,QAAQ;AACnC,kBAAI,GAAG,UAAU,GAAG,GAAG,MAAM;AAC3B,oBAAI,cAAc,SAAS,qBAAqB,EAAG,KAAI,MAAM,iBAAiB,MAAM,GAAG;AAAA,oBAClF,MAAK,KAAK,WAAW,QAAQ,QAAQ,CAAC;AAAA,cAC7C,CAAC;AAAA,YACH,CAAC;AACD,mBAAO;AAAA,UACT,CAAC;AACD,cAAI,QAAS,eAAc,EAAE,YAAY,CAAC,IAAI,EAAE,CAAC;AAAA,QACnD;AACA,cAAM,sBAAsB,CAAC,KAAK,YAAY,sBAC5C;AAAA,UACE,GAAG,WAAW,IAAI,CAAC,MAAM,OAAO,aAAa,KAAK,CAAC,CAAC;AAAA,UACpD,GAAG,kBAAkB,IAAI,CAAC,MAAM,QAAQ,gBAAgB,GAAG,GAAG,CAAC,CAAC;AAAA,QAClE;AACF,cAAM,eAAe,CAAC,YAAY,aAAa;AAC7C,gBAAM,UAAU,SAAS,IAAI,CAAC,MAAM,IAAI,OAAO,GAAG,GAAG,CAAC;AACtD,gBAAM,QAAQ,CAAC,QAAQ,WAAW,SAAS,GAAG,KAAK,QAAQ,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC;AAClF,qBAAW,OAAOA,MAAK,SAAU,SAAQ,MAAM,GAAG,GAAG,8BAA8B,GAAG;AAAA,QACxF;AACA,cAAM,YAAY,CAAC;AAInB,cAAM,eAAe,MAAM;AACzB,gBAAM,SAAS,CAAC,OAAO,aAAa,OAAO,eAAe,OAAO,UAAU,IAAI;AAE/E,cAAI,OAAO,SAAS,KAAK,gBAAgB,GAAG;AAC1C,mBAAO,oBAAoB,CAAC,QAAQ,GAAG,CAAC,QAAQ,OAAO,KAAK,GAAG,CAAC;AAAA,UAClE,OAAO;AACL,mBAAO,WAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ,OAAO,KAAK,KAAK,mBAAmB,MAAM,IAAI,CAAC;AACtF,mBAAO,oBAAoB,CAAC,SAAS,GAAG,IAAI;AAAA,UAC9C;AAEA,cAAI,OAAO,SAAS,KAAK,gBAAgB,GAAG;AAC1C,mBAAO,oBAAoB,CAAC,QAAQ,GAAG,CAAC,QAAQ,OAAO,KAAK,GAAG,CAAC;AAChE,0BAAc,WAAW,kBAAkB;AAC3C,0BAAc,oBAAoB,kBAAkB;AAAA,UACtD,WAAW,KAAK,YAAY,QAAW;AACrC,mBAAO,WAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ,OAAO,KAAK,KAAK,mBAAmB,MAAM,IAAI,CAAC;AACtF,mBAAO,oBAAoB,CAAC,SAAS,GAAG,IAAI;AAC5C,0BAAc,WAAW,SAAS;AAClC,0BAAc,oBAAoB,SAAS;AAAA,UAC7C;AAEA,gBAAM,aAAa,KAAK,eAAe,SAAY,gBAAgB;AACnE,iBAAO,YAAY,CAAC,QAAQ,GAAG,CAAC,UAAU;AACxC,oBAAQ,QAAQ,GAAG,WAAW,UAAU,KAAK,KAAK;AAClD,kBAAM,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,GAAG,MAAM,IAAI;AACzC,kBAAM,OAAO,GAAG,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;AACpC,kBAAM,IAAI,KAAK,UAAU,MAAM,OAAO,GAAG,IAAI;AAC7C,gBAAI,OAAO,UAAU,QAAQ,KAAK,CAAC,EAAG,QAAO,OAAO,kBAAkB,MAAM,KAAK;AACjF,kBAAM,eAAe,UAAU;AAC/B,kBAAM,OAAO,CAAC,MAAM,OAAO,GAAG,KAAK,MAAM,QAAQ,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC;AACjE,mBAAO,OAAO,mCAAmC,GAAG,IAAI;AAAA,UAC1D,CAAC;AAAA,QACH;AAEA,cAAM,eAAe,MAAM;AACzB,iBAAO,aAAa,CAAC,SAAS,GAAG,CAAC,QAAQ;AACxC,kBAAM,eAAe,UAAU;AAC/B,mBAAO,OAAO,2CAA2C,MAAM,KAAK,MAAM,GAAG;AAAA,UAC/E,CAAC;AACD,iBAAO,aAAa,CAAC,SAAS,GAAG,CAAC,QAAQ;AACxC,kBAAM,eAAe,UAAU;AAC/B,mBAAO,OAAO,2CAA2C,MAAM,KAAK,MAAM,GAAG;AAAA,UAC/E,CAAC;AACD,wBAAc,aAAa,WAAW;AAEtC,mBAAS,MAAM,MAAM;AACnB,kBAAM,cAAc,CAAC,SAAS,QAAQ,aAAa,SAAS;AAC1D,oBAAM,QAAQ,OAAO,YAAY,YAAY,UAAU,OAAO,YAAY,OAAO;AACjF,sBAAQ,OAAO,6BAA6B,OAAO;AACnD,oBAAM,aAAa,WAAW,OAAO;AACrC,oBAAM,QAAQ,sBAAsB,UAAU,OAAO,eAAe;AACpE,sBAAQ,OAAO,wBAAwB,OAAO;AAC9C,kBAAI,CAAC,gBAAiB,QAAO;AAC7B,kBAAI,sBAAsB,QAAQ;AAEhC,oBAAI,UAAU,OAAO,YAAY,OAAO,EAAG,cAAa,WAAW,MAAM;AACzE,uBAAO,OAAO,gBAAgB,UAAU,UAAU,GAAG,MAAM;AAAA,cAC7D;AACA,qBAAO,OAAO,WAAW,UAAU,UAAU,GAAG,MAAM;AAAA,YACxD;AAEA,mBAAO,UAAU,CAAC,QAAQ,GAAG,CAAC,UAAU;AACtC,4BAAc,EAAE,YAAY,KAAK,CAAC;AAClC,qBAAO,YAAY,OAAO,IAAI;AAAA,YAChC,CAAC;AAED,mBAAO,WAAW,CAAC,QAAQ,GAAG,CAAC,YAAY;AACzC,2BAAa,OAAO;AACpB,4BAAc,EAAE,YAAY,KAAK,CAAC;AAClC,qBAAO,YAAY,IAAI,OAAO,IAAI,OAAO,QAAQ,gBAAgB,SAAS,IAAI,CAAC;AAAA,YACjF,CAAC;AAED,oBAAQ,KAAK,kBAAkB,OAAO,sCAAsC;AAC5E,kBAAM,KAAK,sBAAsB,SAAY,QAAQ,EAAE,oBAAoB;AAC3E,kBAAM,cAAc,KAAK,mBAAmB,KAAK,oBAAoB,KAAK;AAC1E,kBAAM,aACJ;AACF,oBAAQ,CAAC,eAAe,MAAM,qBAAqB,UAAU;AAC7D,gBAAI,eAAe,IAAI;AACrB,oBAAM,MAAM,OAAO,KAAK;AACxB,kBAAI,KAAK,iBAAkB,KAAI,MAAM,eAAe,KAAK,IAAI;AAE7D,kBAAI,KAAK,oBAAoB,UAAU;AACrC,wBAAQ,YAAY,UAAU,MAAM,QAAQ,KAAK,GAAG,EAAE,MAAM,CAAC,iBAAiB,EAAE,CAAC;AACjF,oBAAI,KAAK,kBAAkB;AACzB,wBAAM,WAAW,UAAU;AAC3B,sBAAI,MAAM,OAAO;AACjB,sBAAI,MAAM,qBAAqB,KAAK,GAAG;AAAA,gBACzC;AACA,wBAAQ,mBAAmB,QAAQ;AAAA,cACrC,MAAO,SAAQ,CAAC,KAAK,iBAAiB,4BAA4B,KAAK,eAAe;AAEtF,kBAAI,OAAO;AACX,kBAAI,KAAK,qBAAqB,oBAAoB;AAChD,oBAAI,MAAM,OAAO;AACjB,oBAAI,MAAM,uBAAuB,KAAK,GAAG;AACzC,uBAAO;AACP,wBAAQ,oBAAoB,QAAQ;AAAA,cACtC,MAAO,SAAQ,CAAC,KAAK,kBAAkB,6BAA6B,KAAK,gBAAgB;AAEzF,kBAAI,KAAK,eAAe;AACtB,wBAAQ,MAAM,0DAA0D;AACxE,sBAAM,UAAU,OAAO,OAAO,EAAE,MAAM,KAAK,aAAa,QAAQ,CAAC;AACjE,qBAAK,SAAS,KAAK,eAAe,QAAQ,eAAe,CAAC;AAC1D,wBAAQ,iBAAiB,UAAU,OAAO;AAC1C,8BAAc,EAAE,YAAY,KAAK,CAAC;AAAA,cACpC;AACA,kBAAI,KAAK,kBAAkB;AACzB,oBAAI,MAAM,eAAe;AACzB,sBAAM,EAAE,MAAM,CAAC,kBAAkB,EAAE,CAAC;AACpC,oBAAI,MAAM,GAAG;AACb,oBAAI,KAAK,iBAAiB;AACxB,sBAAI,MAAM,eAAe;AACzB,wBAAM,EAAE,MAAM,CAAC,iBAAiB,EAAE,CAAC;AACnC,sBAAI,MAAM,GAAG;AAAA,gBACf;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAEA,cAAM,cAAc,MAAM;AACxB,iBAAO,YAAY,CAAC,SAAS,GAAG,CAAC,QAAQ;AACvC,kBAAM,kBAAkB,QAAQ,EAAE,iBAAiB,gBAAgB;AACnE,gBAAI,MAAM,QAAQ,KAAK,eAAe,CAAC,KAAK,KAAK,eAAe,EAAE,SAAS;AACzE,mBAAK,qBAAqB,GAAG,iBAAiB,eAAe,eAAe;AAC9E,mBAAO,OAAO,kBAAkB,MAAM,GAAG;AAAA,UAC3C,CAAC;AACD,iBAAO,YAAY,CAAC,SAAS,GAAG,CAAC,QAAQ,OAAO,kBAAkB,MAAM,GAAG,CAAC;AAC5E,wBAAc,YAAY,UAAU;AAEpC,gBAAM,kBAAkB,CAAC,UAAU;AACjC,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,MAAK,YAAY,CAAC,GAAG,MAAM,CAAC,GAAG,QAAQ,GAAG,CAAC,EAAE,CAAC;AACrF,0BAAc,EAAE,OAAO,MAAM,OAAO,CAAC;AACrC,mBAAO;AAAA,UACT;AACA,cAAI,QAAQ,EAAE,gBAAgB;AAC5B,mBAAO,eAAe,CAAC,OAAO,GAAG,eAAe;AAChD,4BAAgB,SAAS,OAAO,OAAO,KAAK,eAAe,CAAC,GAAG,MAAM,CAAC;AAAA,UACxE,WAAW,MAAM,QAAQ,KAAK,KAAK,GAAG;AACpC,mBAAO,SAAS,CAAC,OAAO,GAAG,eAAe;AAC1C,4BAAgB,mBAAmB,OAAO,MAAM,KAAK,MAAM,MAAM,CAAC;AAAA,UACpE,OAAO;AACL,mBAAO,SAAS,CAAC,UAAU,SAAS,GAAG,CAAC,UAAU;AAChD,uBAAS,SAAS,OAAO,GAAG,GAAG,CAAC,SAAS,KAAK,MAAM,OAAO,QAAQ,OAAO,CAAC,CAAC;AAC5E,4BAAc,EAAE,OAAO,SAAS,CAAC;AACjC,qBAAO;AAAA,YACT,CAAC;AAAA,UAIH;AAEA,wBAAc,CAAC,QAAQ;AACrB,qBAAS,SAAS,OAAO,GAAG,GAAG,CAAC,MAAM,MAAM;AAC1C,kBAAI,MAAM,MAAM;AACd,8BAAc,EAAE,KAAK,EAAE,MAAM,KAAK,EAAE,CAAC;AACrC,qCAAqB,EAAE,MAAM,EAAE,CAAC;AAAA,cAClC,CAAC;AAAA,YACH,CAAC;AAAA,UACH,CAAC;AAED,gBAAM,cAAc,CAAC,YAAY;AAC/B,gBAAI,CAAC,cAAc,OAAO,EAAG,QAAO;AACpC,gBAAI,QAAQ,QAAQ,UAAU,OAAO,SAAS,OAAO,EAAG,QAAO;AAC/D,gBAAI,QAAQ,MAAM;AAChB,oBAAM,YAAY,MAAM,QAAQ,QAAQ,IAAI,IAAI,QAAQ,OAAO,CAAC,QAAQ,IAAI;AAC5E,kBAAI,UAAU,MAAM,CAAC,aAAa,eAAe,SAAS,QAAQ,CAAC,EAAG,QAAO;AAAA,YAC/E;AACA,gBAAI,QAAQ,MAAM;AAChB,oBAAM,CAAC,GAAG,IAAI,iBAAiB,MAAM,SAAS,QAAQ,MAAM,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;AAC/E,kBAAI,YAAY,GAAG,EAAG,QAAO;AAAA,YAC/B;AACA,mBAAO;AAAA,UACT;AACA,gBAAM,qBAAqB,CAAC,YAAY,YAAY,SAAS,YAAY,OAAO;AAChF,gBAAM,eAAe,MAAM;AACzB,gBAAI,KAAK,aAAa,UAAa,mBAAmB,KAAK,KAAK,EAAG,QAAO;AAE1E,gBAAI,MAAM,QAAQ,KAAK,KAAK,KAAK,mBAAmB,KAAK,eAAe,EAAG,QAAO;AAClF,mBAAO;AAAA,UACT;AACA,mBAAS,MAAM,MAAM;AACnB,mBAAO,eAAe,CAAC,SAAS,GAAG,CAAC,gBAAgB;AAClD,kBAAI,gBAAgB,MAAO,QAAO;AAClC,kBAAI,CAAC,aAAa,EAAG,SAAQ,4DAA4D;AACzF,qBAAO,OAAO,OAAO,EAAE,QAAQ,UAAU,QAAQ,WAAW,UAAU,UAAU,CAAC;AACjF,qBAAO,OAAO,eAAe,IAAI;AAAA,YACnC,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAGA,cAAM,UAAU,CAAC,MACf,CAAC,cACAA,MAAK,SAAS,SAAS,CAAC,KAAK,aAAa,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,SAAS,SAAS,CAAC,CAAC;AAEtF,cAAM,eAAe,MAAM;AACzB,gBAAM,kBAAkB,OAAO,0BAA0B,IAAI;AAC7D,iBAAO,iBAAiB,CAAC,SAAS,GAAG,CAAC,QAAQ,OAAO,WAAW,iBAAiB,GAAG,CAAC;AACrF,iBAAO,iBAAiB,CAAC,SAAS,GAAG,CAAC,QAAQ,OAAO,WAAW,iBAAiB,GAAG,CAAC;AACrF,wBAAc,iBAAiB,eAAe;AAE9C,iBAAO,iBAAiB,CAAC,UAAU,SAAS,GAAG,CAAC,MAAM;AACpD,0BAAc,SAAS,CAAC,KAAK,QAAQ;AAEnC,oBAAM,aAAa,OAAO,MAAM,YAAY,CAAC,EAAE,OAAO,EAAE,MAAM,UAAU,GAAG,EAAE,IAAI;AACjF,oBAAM,WAAW,OAAO,OAAO,EAAE,MAAM,KAAK,aAAa,KAAK,MAAM,SAAS,CAAC;AAC9E,mBAAK,UAAU,YAAY,QAAQ,eAAe,CAAC;AAAA,YACrD,CAAC;AACD,mBAAO;AAAA,UACT,CAAC;AAED,iBAAO,YAAY,CAAC,OAAO,GAAG,CAAC,aAAa;AAC1C,uBAAW,OAAO,UAAU;AAC1B,kBAAI,QAAQ,GAAG,EAAG;AAClB,oBAAM,OAAO,YAAY,GAAG;AAC5B,sBAAQ,QAAQ,QAAQ,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;AAAA,YAC9D;AACA,0BAAc,EAAE,SAAS,CAAC;AAC1B,mBAAO;AAAA,UACT,CAAC;AAED,qBAAW,gBAAgB,CAAC,gBAAgB,qBAAqB,kBAAkB,GAAG;AACpF,gBAAI,iBAAiB,kBAAkB,QAAQ,EAAE,qBAAsB;AACvE,mBAAO,cAAc,CAAC,QAAQ,GAAG,CAAC,UAAU;AAC1C,yBAAW,OAAO,OAAO,KAAK,KAAK,GAAG;AACpC,sBAAM,OAAO,OAAO,MAAM,GAAG,MAAM,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG;AACtE,sBAAM,OAAO,YAAY,KAAK,QAAQ,GAAG,CAAC;AAC1C,oBAAI,MAAM,QAAQ,IAAI,KAAK,iBAAiB,oBAAoB;AAC9D,wBAAM,UAAU,KAAK,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,QAAQ,YAAY,CAAC,CAAC,CAAC;AAClF,wBAAM,YAAY,QAAQ,QAAQ,GAAG,OAAO,CAAC;AAC7C,wBAAM,YAAY,EAAE,MAAM,CAAC,cAAc,GAAG,EAAE;AAC9C,sBAAI,QAAQ,WAAW,GAAG;AAAA,kBAE1B,WAAW,KAAK,SAAS;AACvB,4BAAQ,WAAW,SAAS;AAC5B,kCAAc,EAAE,UAAU,KAAK,CAAC;AAAA,kBAClC,OAAO;AACL,4BAAQ,QAAQ,QAAQ,IAAI,GAAG,SAAS,GAAG,SAAS;AAAA,kBACtD;AAAA,gBACF,WAAW,YAAY,IAAI,KAAK,iBAAiB,qBAAqB;AACpE,4BAAU,YAAY;AACtB,sBAAI,GAAG,KAAK,UAAU,OAAO,QAAQ,IAAI,GAAG,MAAM;AAChD,0BAAM,QAAQ,KAAK,SAAS,MAAM,QAAQ,cAAc,GAAG,GAAG,GAAG;AACjE,kCAAc,QAAQ,CAAC,GAAG,KAAK,CAAC;AAChC,yCAAqB,KAAK;AAAA,kBAC5B,CAAC;AAAA,gBACH,MAAO,MAAK,cAAc,YAAY,QAAQ;AAAA,cAChD;AACA,qBAAO;AAAA,YACT,CAAC;AAAA,UACH;AAEA,iBAAO,wBAAwB,CAAC,QAAQ,GAAG,CAAC,yBAAyB;AACnE,uBAAW,CAAC,KAAK,QAAQ,KAAK,OAAO,QAAQ,oBAAoB,GAAG;AAClE,sBAAQ,cAAc,QAAQ,GAAG,wCAAwC;AACzE,wBAAU,sBAAsB;AAChC,oBAAM,OAAO,YAAY,KAAK,QAAQ,GAAG,CAAC;AAE1C,kBAAI,GAAG,KAAK,UAAU,OAAO,QAAQ,IAAI,GAAG,MAAM;AAChD,2BAAW,CAAC,KAAK,IAAI,KAAK,OAAO,QAAQ,QAAQ,GAAG;AAClD,0BAAQ,YAAY,IAAI,GAAG,2CAA2C;AACtE,sBAAI,GAAG,QAAQ,UAAU,IAAI,GAAG,GAAG,GAAG,MAAM;AAE1C,0BAAM,QAAQ,KAAK,SAAS,MAAM,QAAQ,wBAAwB,KAAK,GAAG,GAAG,GAAG;AAChF,kCAAc,QAAQ,CAAC,GAAG,KAAK,CAAC;AAChC,yCAAqB,KAAK;AAAA,kBAC5B,CAAC;AAAA,gBACH;AAAA,cACF,CAAC;AAAA,YACH;AACA,mBAAO;AAAA,UACT,CAAC;AAED,iBAAO,cAAc,CAAC,QAAQ,GAAG,CAAC,eAAe;AAC/C,uBAAW,KAAK,OAAO,KAAK,UAAU,GAAG;AACvC,kBAAI,cAAc,EAAG;AACrB,mBAAK,YAAY,GAAG,QAAQ,CAAC,CAAC,GAAG,WAAW,CAAC,GAAG,QAAQ,cAAc,CAAC,CAAC;AAAA,YAC1E;AACA,0BAAc,EAAE,YAAY,OAAO,KAAK,UAAU,EAAE,CAAC;AACrD,mBAAO;AAAA,UACT,CAAC;AAED,mBAAS,KAAK,mBAAmB,MAAM;AACrC,mBAAO,qBAAqB,CAAC,QAAQ,GAAG,CAAC,sBAAsB;AAC7D,4BAAc,SAAS,CAAC,KAAK,QAAQ;AACnC,2BAAW,KAAK,OAAO,KAAK,iBAAiB,GAAG;AAC9C,+BAAa,GAAG,KAAK,iBAAiB,CAAC,CAAC;AACxC,sBAAI,GAAG,gBAAgB,GAAG,GAAG,GAAG,MAAM;AACpC,yBAAK,KAAK,kBAAkB,CAAC,GAAG,QAAQ,qBAAqB,CAAC,CAAC;AAAA,kBACjE,CAAC;AAAA,gBACH;AAAA,cACF,CAAC;AACD,4BAAc,EAAE,UAAU,OAAO,KAAK,iBAAiB,EAAE,CAAC;AAC1D,qBAAO;AAAA,YACT,CAAC;AACD,gBAAI,KAAK,wBAAwB,KAAK,yBAAyB,OAAO;AACpE,oBAAM,aAAa,OAAO,KAAK,KAAK,cAAc,CAAC,CAAC;AACpD,oBAAM,oBAAoB,OAAO,KAAK,KAAK,qBAAqB,CAAC,CAAC;AAClE,kBAAI,KAAK,yBAAyB,OAAO;AAEvC,0BAAU,KAAK,MAAM,aAAa,YAAY,iBAAiB,CAAC;AAAA,cAClE;AACA,oBAAM,YAAY,CAAC,QAAQ,oBAAoB,KAAK,YAAY,iBAAiB;AACjF,mCAAqB,wBAAwB,SAAS;AAAA,YACxD;AAAA,UACF,CAAC;AAED,cAAI,QAAQ,EAAE,gBAAgB;AAC5B,0BAAc,CAAC,QAAQ;AACrB,4BAAc,SAAS,CAAC,MAAM,MAAM;AAClC,oBAAI,MAAM,MAAM;AACd,gCAAc,EAAE,KAAK,EAAE,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC;AAC7C,uCAAqB,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC;AAAA,gBAC9C,CAAC;AAAA,cACH,CAAC;AAAA,YACH,CAAC;AAAA,UACH;AAAA,QACF;AAEA,cAAM,aAAa,MAAM;AACvB,gBAAM,eAAe,OAAO,SAAS,CAAC,SAAS,GAAG,CAAC,QAAQ,QAAQ,QAAQ,MAAM,GAAG,CAAC,CAAC;AACtF,cAAI,gBAAgB,CAAC,oBAAqB,QAAO;AACjD,gBAAM,cAAc,OAAO,QAAQ,CAAC,OAAO,GAAG,CAAC,SAAS;AACtD,kBAAM,UAAU,KAAK,OAAO,CAAC,UAAU,SAAS,OAAO,UAAU,QAAQ;AACzE,kBAAM,YAAY,KAAK,OAAO,CAAC,UAAU,EAAE,SAAS,OAAO,UAAU,SAAS;AAC9E,mBAAO,UAAU,GAAG,CAAC,GAAG,WAAW,GAAG,OAAO,EAAE,IAAI,CAAC,UAAU,QAAQ,MAAM,KAAK,CAAC,CAAC;AAAA,UACrF,CAAC;AACD,iBAAO,gBAAgB;AAAA,QACzB;AAEA,cAAM,gBAAgB,CAAC,YAAY;AAEjC,iBAAO,YAAY,CAAC,UAAU,SAAS,GAAG,MAAM;AAC9C,sBAAU,UAAU;AAEpB,gBAAI,QAAQ,EAAE,kBAAkB,eAAe,OAAO,KAAK,eAAe,QAAQ,GAAG;AACnF,gCAAkB,0CAA0C,4BAA4B;AAAA,YAC1F;AAEA,kBAAM,SAAS,OAAO,QAAQ;AAC9B,gBAAI,MAAM,cAAc,MAAM;AAE9B,kBAAM,SAAS,SAAS;AACxB,oBAAQ,CAAC,MAAM,aAAa;AAC1B,oBAAM,EAAE,IAAI,IAAI,QAAQ,QAAQ,MAAM,KAAK,UAAU,QAAQ,UAAU,CAAC;AACxE,kBAAI,GAAG,KAAK,MAAM;AAChB,oBAAI,MAAM,QAAQ,MAAM;AACxB,oBAAI,QAAQ,EAAE,mBAAmB;AAC/B,0BAAQ,CAAC,kBAAkB,uDAAwD;AACnF,2BAAS;AAAA,gBACX;AAAA,cACF,CAAC;AAAA,YACH,CAAC;AAED,gBAAI,CAAC,OAAO,eAAe,CAAC,SAAS,GAAG,CAAC,OAAO,OAAO,WAAW,QAAQ,EAAE,GAAG,EAAE,OAAO,CAAC;AACvF,sBAAQ,OAAO,UAAU,MAAM,GAAG,EAAE,MAAM,CAAC,UAAU,GAAG,OAAO,CAAC;AAElE,mBAAO,eAAe,CAAC,SAAS,GAAG,CAAC,QAAQ,OAAO,WAAW,QAAQ,GAAG,CAAC;AAC1E,0BAAc,eAAe,aAAa;AAC1C,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AAEA,cAAM,eAAe,MAAM;AACzB,iBAAO,OAAO,CAAC,UAAU,SAAS,GAAG,CAAC,QAAQ,QAAQ,MAAM,SAAS,KAAK,QAAQ,KAAK,CAAC,EAAE,GAAG;AAC7F,cAAI,KAAK,IAAK,WAAU,KAAK;AAE7B,gBAAM,aAAa,KAAK,QAAQ,KAAK,SAAS,SAAS,KAAK,QAAQ,KAAK,SAAS;AAGlF,cAAI,cAAc;AAChB,mBAAO,MAAM,CAAC,UAAU,SAAS,GAAG,CAAC,QAAQ;AAC3C,wBAAU,cAAc;AACxB,oBAAM,EAAE,KAAK,OAAO,QAAQ,IAAI,QAAQ,MAAM,SAAS,KAAK,QAAQ,IAAI,GAAG,GAAG;AAC9E,kBAAI,YAAY,YAAY,WAAW;AACvC,qBAAO,QAAQ,CAAC,UAAU,SAAS,GAAG,CAAC,UAAU;AAC/C,6BAAa,MAAM;AACjB,8BAAY,KAAK,SAAS,OAAO,QAAQ,MAAM,GAAG,GAAG;AACrD,uCAAqB,SAAS;AAAA,gBAChC;AACA,uBAAO;AAAA,cACT,CAAC;AACD,qBAAO,QAAQ,CAAC,UAAU,SAAS,GAAG,CAAC,UAAU;AAC/C,6BAAa,MAAM;AACjB,8BAAY,KAAK,SAAS,OAAO,QAAQ,MAAM,GAAG,GAAG;AACrD,uCAAqB,SAAS,SAAS,SAAS,CAAC;AAAA,gBACnD;AACA,uBAAO;AAAA,cACT,CAAC;AACD,kBAAI,CAAC,cAAc,CAAC,WAAW,OAAO,EAAG,cAAa,MAAM,qBAAqB,OAAO;AACxF,kBAAI,GAAG,KAAK,YAAY,UAAU;AAClC,4BAAc,QAAQ,aAAa,CAAC,GAAG,SAAS,SAAS,aAAa,CAAC,CAAC,CAAC,CAAC;AAC1E,qBAAO;AAAA,YACT,CAAC;AAEH,gBAAM,eAAe,CAAC,OAAO,WAAW,YAAY;AAClD,oBAAQ,MAAM,SAAS,GAAG,GAAG,QAAQ,kBAAkB;AACvD,uBAAW,CAAC,KAAK,GAAG,KAAK,OAAO,QAAQ,KAAK;AAC3C,4BAAc,KAAK,SAAS,KAAK,QAAQ,UAAU,GAAG,GAAG,GAAG,CAAC;AAC/D,mBAAO;AAAA,UACT;AACA,iBAAO,SAAS,CAAC,OAAO,GAAG,CAAC,UAAU,aAAa,KAAK,CAAC;AAEzD,cAAI,sBAAsB;AAC1B,iBAAO,iBAAiB,CAAC,QAAQ,GAAG,CAAC,kBAAkB;AACrD,kBAAM,OAAO,oBAAI,IAAI;AACrB,kBAAM,MAAM,CAAC,OAAO,SAAS,QAAQ,QAAQ,OAAO,oBAAoB,OAAO,IAAI,GAAG;AACtF,kBAAM,EAAE,cAAc,OAAO,SAAS,KAAK,GAAG,GAAG,IAAI;AACrD,kBAAM,OAAO,YAAY,KAAK;AAC9B,gBAAI,SAAS,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO,mCAAmC;AAC7E,gBAAI,OAAO,KAAK,EAAE,EAAE,WAAW,GAAG,iDAAiD;AACnF,kBAAM,SAAS,CAAC,QAAS,cAAc,GAAG,IAAI,OAAO,KAAK,GAAG,EAAE,SAAS;AACxE,kCAAsB,CAAC,UAAU,aAAa;AAC5C,oBAAM,mBAAmB,MAAM;AAC7B,oBAAI,MAAM,iBAAiB,UAAU,IAAI,CAAC;AAC1C,oBAAI;AACJ,2BAAW,CAAC,GAAG,MAAM,KAAK,OAAO,QAAQ,QAAQ,GAAG;AAClD,wBAAM,EAAE,OAAO,OAAO,MAAM,QAAQ,GAAG,GAAG,KAAK,OAAO,cAAc,CAAC,GAAG,KAAK,KAAK,CAAC;AACnF,sBAAI,OAAO,UAAU,SAAY,CAAC,KAAK,IAAI;AAC3C,sBAAI,CAAC,QAAQ,OAAO,MAAM;AACxB,0BAAM,CAAC,GAAG,IAAI,iBAAiB,MAAM,SAAS,OAAO,MAAM,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC;AAC9E,4BAAQ,cAAc,GAAG,GAAG,2BAA2B,OAAO,IAAI;AAClE,0BAAM,SAAS,IAAI,cAAc,CAAC,GAAG,KAAK,KAAK,CAAC;AAChD,2BAAO,MAAM,UAAU,SAAY,CAAC,MAAM,KAAK,IAAI,MAAM;AAAA,kBAC3D;AACA,wBAAM,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK,SAAS;AACjD,sBAAI,KAAK,4EAA4E;AACrF,wBAAM,MAAM,OAAO,KAAK,EAAE,EAAE,WAAW,MAAM,CAAC,SAAS,CAAC;AACxD,sBAAI,KAAK,oEAAoE;AAC7E,6BAAW,OAAO,MAAM;AACtB,0BAAM,YAAY,CAAC,OAAQ,UAAU,OAAO,KAAK,GAAG,KAAK,IAAI,GAAG,MAAM,OAAO;AAC7E,wBAAI,WAAW,2BAA2B,GAAG;AAC7C,0BAAM,QAAQ,OAAO,QAAQ,YAAY,CAAC,KAAK,IAAI,GAAG;AACtD,wBAAI,OAAO,+DAA+D;AAC1E,yBAAK,IAAI,GAAG;AACZ,wBAAI,MAAM,YAAY,GAAG;AAAA,kBAC3B;AACA,wBAAM,OAAO,KAAK,SAAS,QAAQ,QAAQ,UAAU,CAAC,GAAG,KAAK,EAAE,WAAW,MAAM,CAAC;AAClF,uCAAqB,IAAI;AACzB,0BAAQ,QAAQ,QAAQ,OAAO,IAAI,IAAI;AACvC,sBAAI,MAAM,OAAO;AAAA,gBACnB;AACA,oBAAI,QAAQ,UAAa,OAAO,GAAG,MAAM,KAAK,MAAM,0BAA0B;AAC9E,8BAAc,KAAK;AACnB,oBAAI,MAAM,UAAU;AACpB,sBAAM,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC;AAC1B,oBAAI,MAAM,GAAG;AAAA,cACf;AACA,oBAAM,YAAY,MAAM;AACtB,oBAAI,CAAC,QAAQ,KAAK,GAAG;AACnB,wBAAM,YAAY,CAAC,iBAAiB,cAAc;AAClD,sBAAI,GAAG,QAAQ,IAAI,GAAG,kBAAkB,MAAM,MAAM,EAAE,MAAM,WAAW,KAAK,CAAC,CAAC;AAAA,gBAChF,MAAO,kBAAiB;AAAA,cAC1B;AACA,kBAAI,aAAa,CAAC,UAAU,UAAUA,MAAK,MAAM,CAAC,QAAQ,CAAC,GAAG;AAC5D,oBAAI,GAAG,MAAM,IAAI,QAAQ,EAAE,IAAI,GAAG,WAAW,MAAM,MAAM,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;AAAA,cACvF,MAAO,WAAU;AAGjB,kBAAI,UAAU,UAAUA,MAAK,MAAM,CAAC,QAAQ,CAAC,GAAG,+BAA+B,QAAQ;AACvF,kBAAIA,MAAK,SAAS,SAAS,KAAK,GAAG,8CAA8C,KAAK;AACtF,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT,CAAC;AAGD,gBAAM,uBAAuB,CAAC,KAAK,QAAQ;AAIzC,kBAAM,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE,SAAS,MAAM,QAAQ,EAAE,KAAK,IAAI,UAAU,OAAO,EAAE,MAAM;AAC3F,kBAAM,WAAW,OAAO,OAAO,CAAC,MAAM,CAAC,eAAe,SAAS,CAAC,KAAK,MAAM,OAAO,EAAE;AACpF,kBAAM,WAAW,OAAO,OAAO,CAAC,MAAM,CAAC,eAAe,SAAS,CAAC,KAAK,MAAM,QAAQ,EAAE;AACrF,gBAAI,WAAW,KAAK,WAAW,EAAG,WAAU,GAAG,GAAG,wCAAwC;AAAA,UAC5F;AAEA,iBAAO,SAAS,CAAC,OAAO,GAAG,CAAC,UAAU;AACpC,oBAAQ,MAAM,SAAS,GAAG,uBAAuB;AACjD,gBAAI,MAAM,WAAW,EAAG,QAAO,aAAa,KAAK;AACjD,gBAAI,oBAAqB,QAAO,oBAAoB,OAAO,OAAO;AAClE,kBAAM,SAAS,SAAS;AACxB,gBAAI,CAAC,eAAe,GAAG;AACrB,mCAAqB,SAAS,KAAK;AAEnC,oBAAM,UAAU,OAAO,QAAQ,KAAK,EAAE;AAAA,gBAAI,CAAC,CAAC,KAAK,GAAG,MAClD,QAAQ,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG,GAAG,GAAG;AAAA,cAC1D;AACA,4BAAc,QAAQ,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,QAAQ,QAAQ,KAAK,GAAG,CAAC,CAAC;AACjF,sBAAQ,UAAU,GAAG,QAAQ,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;AAChF,yBAAW,EAAE,OAAAK,QAAO,IAAI,KAAK,QAAS,KAAI,GAAG,KAAK,MAAM,qBAAqBA,MAAK,CAAC;AACnF,qBAAO;AAAA,YACT;AAKA,kBAAM,cAAc,MAAM,OAAO,CAAC,MAAM,UAAU,OAAO,GAAG,OAAO,CAAC;AACpE,kBAAM,cAAc,MAAM,OAAO,CAAC,MAAM,CAAC,UAAU,OAAO,GAAG,OAAO,CAAC;AACrE,iCAAqB,SAAS,WAAW;AACzC,kBAAM,SAAS,CAAC,GAAG,aAAa,GAAG,WAAW;AAC9C,gBAAI;AAEJ,gBAAI,CAAC,QAAQ,EAAE,eAAe;AAE5B,oBAAM,UAAU,OAAO,QAAQ,KAAK,EAAE;AAAA,gBAAI,CAAC,CAAC,KAAK,GAAG,MAClD,QAAQ,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG,GAAG,GAAG;AAAA,cAC1D;AACA,sBAAQ,QAAQ,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,QAAQ,QAAQ,KAAK,GAAG,CAAC;AAC1E,sBAAQ,UAAU,GAAG,QAAQ,IAAI,CAAC,EAAE,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;AAAA,YAClF,OAAO;AAGL,kBAAI,OAAO,MAAM,MAAM,EAAE,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;AAClD,yBAAW,CAAC,KAAK,GAAG,KAAK,OAAO,QAAQ,MAAM,EAAE,QAAQ,GAAG;AACzD,sBAAM,UAAU;AAChB,uBAAO,MAAM;AACX,wBAAM,EAAE,KAAK,OAAO,SAAS,IAAI,QAAQ,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG,CAAC;AACpF,sBAAI,GAAG,QAAQ,GAAG,GAAG,OAAO;AAC5B,0BAAQ,QAAQ,QAAQ,OAAO,QAAQ,IAAI;AAAA,gBAC7C;AAAA,cACF;AACA,mBAAK;AAAA,YACP;AAEA,0BAAc,KAAK;AACnB,mBAAO;AAAA,UACT,CAAC;AAED,iBAAO,SAAS,CAAC,OAAO,GAAG,CAAC,UAAU;AACpC,oBAAQ,MAAM,SAAS,GAAG,uBAAuB;AACjD,gBAAI,MAAM,WAAW,EAAG,QAAO,aAAa,KAAK;AACjD,gBAAI,oBAAqB,QAAO,oBAAoB,OAAO,OAAO;AAClE,iCAAqB,SAAS,KAAK;AACnC,kBAAM,SAAS,OAAO,QAAQ;AAC9B,gBAAI,MAAM,cAAc,MAAM;AAC9B,kBAAM,SAAS,SAAS;AACxB,gBAAI;AACJ,gBAAI,IAAI;AACR,kBAAM,UAAU,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,KAAK,GAAG,MAAM;AACxD,kBAAI,CAAC,iBAAiB,MAAM,EAAG,SAAQ,OAAO,UAAU,MAAM,GAAG,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;AACpF,oBAAM,QAAQ,QAAQ,QAAQ,SAAS,KAAK,QAAQ,SAAS,GAAG,GAAG,GAAG;AACtE,kBAAI,GAAG,MAAM,KAAK,MAAM,IAAI,MAAM,QAAQ,MAAM,CAAC;AACjD,sBAAQ,QAAQ,QAAQ,OAAO,MAAM,KAAK,IAAI,MAAM;AACpD,qBAAO;AAAA,YACT,CAAC;AACD,0BAAc,KAAK;AACnB,oBAAQ,OAAO,YAAY,MAAM,GAAG,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;AACvD,gBAAI,GAAG,OAAO,YAAY,MAAM,GAAG,MAAM,WAAW,MAAM,CAAC;AAC3D,uBAAW,SAAS,QAAS,KAAI,GAAG,MAAM,KAAK,MAAM,qBAAqB,MAAM,KAAK,CAAC;AACtF,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AAEA,cAAM,WAAW,CAAC,YAAY,YAAY,cAAc;AACtD,gBAAM,CAAC,SAAS,UAAU,IAAI,CAAC,IAAI,KAAK,GAAG,OAAO,IAAI;AACtD,cAAI,GAAG,eAAe,GAAG,UAAU,IAAI,OAAO,WAAW,UAAU;AAEnE,cAAI,YAAY,IAAI,KAAK,KAAK,eAAe,OAAO;AAClD,oBAAQ,eAAe,GAAG,UAAU,GAAG,4BAA4B,KAAK,IAAI;AAAA,QAChF;AAGA,cAAM,mBAAmB,MAAM;AAC7B,cAAIL,MAAK,UAAU,UAAU;AAE3B,gBAAI,KAAK,qBAAqB,MAAO,SAAQ,oBAAoB,SAAS;AAAA,UAC5E,WAAW,KAAK,oBAAoB,KAAK,qBAAqB,OAAO;AACnE,gBAAI,UAAUA,KAAI,EAAE,OAAO;AACzB,kBAAI,CAAC,KAAK,UAAU,EAAG,OAAM,IAAI,MAAM,+CAA+C;AACtF,oBAAM,QAAQ,OAAO,uBAAuBA,MAAK,OAAO,IAAI,KAAK;AACjE,oBAAM,QAAQ,CAAC,MAAM,OAAO,mBAAmB,IAAI,MAAM,CAAC;AAC1D,8BAAgB,oBAAoB,OAAO,QAAQ,EAAE,oBAAoB,QAAQ,IAAI;AAAA,YACvF,OAAO;AACL,8BAAgB,oBAAoB,OAAO,MAAMA,MAAK,KAAK,CAAC;AAAA,YAC9D;AAAA,UACF;AAAA,QACF;AACA,cAAM,oBAAoB,MAAM;AAC9B,mBAASA,MAAK,SAAS,SAAS,KAAKA,MAAK,IAAI,SAAS,SAAS,KAAKA,MAAK,SAAS,MAAM;AACvF,gBAAIA,MAAK,WAAW,SAAS,IAAI,GAAG;AAElC,kBAAI,KAAK,0BAA0B,MAAO,SAAQ,yBAAyB,SAAS;AAAA,YACtF,WAAW,KAAK,yBAAyB,KAAK,0BAA0B,OAAO;AAC7E,oBAAM,YAAY,CAAC,QAAQ,oBAAoB,KAAKA,MAAK,YAAYA,MAAK,QAAQ;AAClF,kBAAI,UAAUA,KAAI,EAAE,YAAY;AAC9B,oBAAI,CAAC,KAAK,UAAU,EAAG,OAAM,IAAI,MAAM,+CAA+C;AACtF,sBAAM,aAAa,UAAU;AAC7B,sBAAM,aAAa,CAAC,QAAQ,OAAO,uBAAuB,KAAK,IAAI,KAAK;AACxE,sBAAM,YAAY,CAAC,QAAQ,QAAQ,UAAU,GAAG,GAAG,WAAW,GAAG,CAAC;AAClE,qCAAqB,yBAAyB,SAAS;AAAA,cACzD,OAAO;AACL,oBAAI,KAAK,0BAA0B,MAAO,cAAaA,MAAK,YAAYA,MAAK,QAAQ;AACrF,qCAAqB,yBAAyB,SAAS;AAAA,cACzD;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAEA,cAAM,oBAAoB,MAAM;AAC9B,cAAI,SAAS,KAAM,KAAI,MAAM,yBAAyB,IAAI;AAC1D,cAAI,WAAW,GAAG;AAChB,kBAAM,WAAW,CAAC,GAAG,MAAM,KAAK,CAAC;AACjC,0BAAc,EAAE,YAAY,CAAC,IAAI,GAAG,OAAO,UAAU,MAAM,UAAU,YAAY,KAAK,CAAC;AACvF,gBAAI,CAAC,qBAAqB;AAExB,sBAAQ,OAAO,SAAS,GAAG,iDAAiD,CAAC,GAAG,MAAM,CAAC;AAEvF;AAAA,YACF;AAAA,UACF;AAEA,mBAAS,cAAc,CAAC,UAAU,SAAS,GAAG,MAAM,IAAI,QAAQ,EAAE,IAAI,CAAC;AACvE,mBAAS,cAAc,CAAC,QAAQ,GAAG,MAAM,IAAI,QAAQ,EAAE,IAAI,CAAC;AAC5D,mBAAS,aAAa,CAAC,OAAO,GAAG,MAAM,IAAI,OAAO,EAAE,IAAI,CAAC;AACzD,mBAAS,cAAc,CAAC,QAAQ,GAAG,MAAM,IAAI,QAAQ,EAAE,IAAI,CAAC;AAE5D,uBAAa;AAGb,mBAAS,kBAAkB,CAAC,OAAO,GAAG,MAAM,IAAI,OAAO,EAAE,IAAI,CAAC;AAC9D,mBAAS,mBAAmB,CAAC,QAAQ,GAAG,MAAM,IAAI,QAAQ,EAAE,IAAI,CAAC;AAEjE,qBAAW,QAAQ,UAAW,MAAK;AAInC,gCAAsB,OAAOE,OAAM,MAAMA,OAAM,OAAOA,OAAM,KAAK;AAAA,QACnE;AAGA,cAAM,YAAY,MAAM;AACtB,cAAIA,OAAM,KAAM,KAAI,MAAM,iBAAiBA,OAAM,IAAI;AACrD,cAAIA,OAAM,MAAO,KAAI,MAAM,kBAAkBA,OAAM,KAAK;AACxD,cAAIA,OAAM,MAAO,KAAI,MAAM,uBAAuBA,OAAM,KAAK;AAG7D,iBAAO,QAAQ,CAAC,QAAQ,GAAG,CAAC,SAAS;AACnC,kBAAM,WAAW,iBAAiB,MAAM,SAAS,MAAM,SAAS,CAAC;AACjE,kBAAM,CAAC,KAAK,SAAS,IAAI,IAAI,SAAS,CAAC,KAAK,CAAC;AAC7C,gBAAI,CAAC,OAAO,QAAQ,OAAO;AACzB,mBAAK,2BAA2B,IAAI;AACpC,kBAAI,SAAU,QAAO;AAAA,YACvB;AACA,kBAAM,IAAI,WAAW,KAAK,SAAS,IAAI;AACvC,kBAAM,KAAK,QAAQ,SAAS,UAAU;AACtC,gBAAI,CAAC,MAAM,EAAE,EAAG,OAAM,IAAI,MAAM,oCAAoC;AACpE,gBAAI,CAAC,MAAM,EAAE,EAAE,eAAe,KAAK,IAAI,MAAM;AAC3C,oBAAM,OAAO,MAAM,QAAQ,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI,IAAI;AAC3D,4BAAc,EAAE,KAAK,CAAC;AACtB,kBAAI,mBAAmB;AAErB,uCAAuB,IAAI,EAAE;AAE7B,oBAAI,KAAK,SAAS,OAAO,EAAG,eAAc,EAAE,OAAO,SAAS,CAAC;AAC7D,oBAAI,KAAK,SAAS,QAAQ,EAAG,eAAc,EAAE,YAAY,CAAC,IAAI,EAAE,CAAC;AAAA,cACnE;AACA,kBAAI,2BAA2B,KAAK,SAAS,QAAQ,GAAG;AACtD,uCAAuB,IAAI,EAAE;AAC7B,8BAAc,EAAE,YAAY,KAAK,CAAC;AAAA,cACpC;AAAA,YACF;AACA,mBAAO,SAAS,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;AAAA,UACvC,CAAC;AACD,cAAI,QAAQ,EAAE,eAAe;AAC3B,oBAAQ,CAAC,KAAK,UAAU,GAAG,0DAA0D;AACrF,gBAAI,KAAK,KAAM;AAAA,UACjB;AACA,iBAAO,iBAAiB,CAAC,QAAQ,GAAG,CAAC,kBAAkB;AACrD,gBAAI,CAAC,KAAK,aAAa,EAAG,OAAM,IAAI,MAAM,yCAAyC;AACnF,oBAAQ,kBAAkB,KAAK,mDAAmD;AAElF,kBAAM,WAAW,iBAAiB,MAAM,SAAS,KAAK,SAAS,CAAC;AAChE,kBAAM,CAAC,KAAK,SAAS,IAAI,IAAI,SAAS,CAAC;AACvC,oBAAQ,IAAI,kBAAkB,wCAAwC;AACtE,kBAAM,IAAI,WAAW,KAAK,SAAS,IAAI;AAEvC,kBAAM,OAAO,IAAI,mBAAmB,OAAO,qBAAqB,CAAC,IAAI;AACrE,mBAAO,SAAS,MAAM,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC;AAAA,UACnD,CAAC;AACD,iBAAO,eAAe,CAAC,QAAQ,GAAG,CAAC,gBAAgB;AACjD,gBAAI,CAAC,KAAK,aAAa,EAAG,OAAM,IAAI,MAAM,uCAAuC;AACjF,oBAAQ,yBAAyB,KAAK,WAAW,GAAG,gCAAgC;AACpF,kBAAM,cAAc,YAAY,QAAQ,UAAU,EAAE;AACpD,kBAAM,WAAW,iBAAiB,MAAM,SAAS,aAAa,SAAS,CAAC;AACxE,gBAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,YAAY;AAMzC,sBAAQ,OAAO,qEAAqE;AACpF,oBAAM,iBAAiB,UAAU;AACjC,oBAAMI,QAAO,OAAO,wCAAwC,WAAW;AACvE,qBAAO,SAASA,OAAM,EAAE,MAAM,CAAC,aAAa,EAAE,CAAC;AAAA,YACjD;AACA,oBAAQ,SAAS,CAAC,GAAG,4CAA4C,WAAW;AAC5E,kBAAM,CAAC,KAAK,SAAS,IAAI,IAAI,SAAS,CAAC;AACvC,kBAAM,KAAK,IAAI,kBAAkB,IAAI,IAAI,cAAc,OAAO;AAC9D,oBAAQ,IAAI,sDAAsD;AAClE,kBAAM,IAAI,WAAW,KAAK,SAAS,IAAI;AACvC,kBAAM,iBAAiB,UAAU;AACjC,kBAAM,OAAO,KAAK,OAAO,gDAAgD,aAAa,CAAC,IAAI;AAC3F,mBAAO,SAAS,MAAM,EAAE,MAAM,CAAC,aAAa,EAAE,CAAC;AAAA,UACjD,CAAC;AAGD,cAAI,YAAY;AAChB,iBAAO,QAAQ,CAAC,UAAU,OAAO,GAAG,CAAC,SAAS;AAC5C,kBAAM,UAAU,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AAClD,uBAAW,KAAK,QAAS,SAAQ,OAAO,MAAM,YAAY,MAAM,IAAI,CAAC,GAAG,iBAAiB,CAAC;AAC1F,gBAAI,QAAQ,MAAM;AAChB,sBAAQ,UAAU,UAAU,SAAS,CAAC,QAAQ,IAAI,CAAC,GAAG,qBAAqB,QAAQ,IAAI;AACvF,4BAAc,EAAE,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;AACtC,qBAAO;AAAA,YACT;AACA,gBAAI,kBAAkB,GAAG,OAAO,EAAG,QAAO;AAC1C,kBAAM,gBAAgB,QAAQ,OAAO,CAAC,MAAM,eAAe,CAAC,CAAC;AAC7D,gBAAI,cAAc,WAAW,EAAG,MAAK,yBAAyB;AAC9D,0BAAc,EAAE,MAAM,QAAQ,CAAC;AAC/B,wBAAY,UAAU,GAAG,cAAc,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACrE,mBAAO;AAAA,UACT,CAAC;AAID,cAAI,aAAa,WAAW;AAC1B,gBAAI,GAAG,WAAW,MAAM,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,iBAAiB;AAAA,UACtE,OAAO;AACL,gBAAI,UAAW,SAAQ,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;AACpD,8BAAkB;AAAA,UACpB;AAGA,cAAIN,MAAK,QAAQ,YAAY,KAAK,YAAYA,MAAK,MAAO,eAAc,EAAE,OAAO,SAAS,CAAC;AAAA,QAC7F;AAGA,YAAI,KAAK,YAAY,UAAa,aAAa;AAC7C,cAAI,kBAAmB,MAAK,iDAAiD;AAC7E,gBAAM,WAAW,IAAI,WAAW,SAAS;AACzC,cAAI,GAAG,QAAQ,OAAO,GAAG,WAAW,MAAM,IAAI,MAAM,WAAW,MAAM,QAAQ,CAAC;AAAA,QAChF,OAAO;AACL,iBAAO,WAAW,CAAC,SAAS,GAAG,IAAI;AACnC,cAAI,GAAG,oBAAoB,OAAO,QAAQ,OAAO,GAAG,SAAS;AAAA,QAC/D;AAEA,sBAAc,SAAS;AAGvB,YAAI,aAAa,CAAC,KAAK,aAAa,aAAa,SAAS,CAAC,EAAE,CAAC,MAAM,KAAM,cAAa,IAAI;AAC3F,YAAI,cAAc,SAAS,OAAQ,KAAI,MAAM,kBAAkB;AAG/D,YAAI,CAAC,iBAAkB,SAAQ,CAAC,IAAI,cAAc,iCAAiC;AACnF,YAAI,OAAO;AACT,gBAAM,YAAY,CAAC,OAAO,MAAM,QAAQ,MAAM,EAAE,SAAS,WAAW,WAAW,SAAS,CAAC,CAAC;AAC1F,gBAAM,WAAW,CAAC,SAAS,SAAS,OAAO,EAAE,SAAS,WAAW,WAAW,SAAS,CAAC,CAAC;AACvF,gBAAM,QAAQ,CAAC,gBAAgB,kBAAkB,EAAE,SAAS,WAAW,WAAW,SAAS,CAAC,CAAC;AAC7F,gBAAM,YAAY,CAAC,sBAAsB,EAAE,SAAS,WAAW,WAAW,SAAS,CAAC,CAAC;AAErF,kBAAQ,aAAa,YAAY,SAAS,WAAW,yBAAyB;AAAA,QAChF,WAAW,CAAC,WAAW,SAAS,KAAK,GAAG;AAGtC,gBAAM,WAAW,WAAW,QAAQ,SAAS;AAC7C,cAAI,CAAC,YAAY,uBAAuB,IAAI,OAAO,GAAG;AACpD,mCAAuB,OAAO,OAAO;AACrC,gBAAI,CAACA,MAAK,KAAM,mBAAkB,MAAM;AAExC,gBAAI,eAAe,OAAO,KAAKA,MAAK,UAAU;AAC5C,gCAAkB,KAAK,QAAQ,wCAAwC,YAAY;AACrF,gBAAI,eAAe,QAAQ,KAAK,CAACA,MAAK,WAAW,SAAS,IAAI;AAC5D,gCAAkB,+CAA+C;AACnE,gBAAI,CAACA,MAAK,cAAc,yBAAyB;AAC/C,oBAAM,gBAAgB;AACtB,mBAAK,6BAA6B,aAAa,uCAAuC;AAAA,YACxF;AAAA,UACF;AACA,cAAI,OAAO,KAAK,kBAAkB;AAChC,uBAAW,OAAO,CAAC,wBAAwB,uBAAuB;AAChE,kBAAI,KAAK,GAAG,EAAG,mBAAkB,aAAa,GAAG,IAAI,wBAAwB;AAAA;AAAA,QACnF;AACA,YAAI,KAAK,cAAc,CAAC,KAAK,SAAU,mBAAkB,iCAAiC;AAC1F,gBAAQ,OAAO,SAAS,KAAK,qBAAqB,yBAAyB,CAAC,GAAG,MAAM,CAAC;AAEtF,eAAO,EAAE,MAAAA,OAAM,OAAAE,OAAM;AAAA,MACvB;AAEA,YAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,iBAAiB,GAAG,CAAC,GAAG,EAAE,MAAM,KAAK,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC;AAC/F,UAAI,uBAAuB,IAAI,OAAO,EAAG,OAAM,IAAI,MAAM,oCAAoC;AAG7F,UAAI,KAAK,UAAU,MAAM,UAAU,IAAI,EAAE,SAAS,UAAU,IAAI,EAAE,aAAa;AAC7E,YAAI,CAAC,MAAO,OAAM,IAAI,MAAM,oCAAoC;AAChE,YAAI,MAAM,4CAA4C,MAAM,MAAM,MAAM,OAAO,MAAM,KAAK;AAAA,MAC5F;AAEA,UAAI,UAAW,KAAI,MAAM,yBAAyB;AAAA,UAC7C,KAAI,MAAM,aAAa;AAE5B,UAAI,MAAM,GAAG;AAEb,UAAI,CAAC,UAAU;AACb,mBAAW,IAAI,aAAa,KAAK;AACjC,eAAO,MAAM,OAAO;AACpB,cAAM,OAAO,IAAI;AAAA,MACnB;AACA,YAAM,OAAO,EAAE,eAAe,IAAI;AAClC,aAAO;AAAA,IACT;AAEA,QAAM,UAAU,CAAC,SAAS,SAAS;AACjC,UAAI,CAAC,MAAM,QAAQ,OAAO,EAAG,OAAM,IAAI,MAAM,8BAA8B;AAC3E,UAAI;AACF,cAAM,QAAQ,uBAAO,OAAO,IAAI;AAChC,cAAM,EAAE,OAAO,IAAI,aAAa,KAAK;AACrC,+BAAuB,MAAM;AAC7B,iBAAS,MAAM;AACf,cAAM,OAAO,QAAQ,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,cAAc,GAAG,GAAG,MAAM,KAAK,CAAC;AAC7E,YAAI,uBAAuB,SAAS,EAAG,OAAM,IAAI,MAAM,wCAAwC;AAC/F,eAAO,EAAE,OAAO,KAAK;AAAA,MACvB,SAAS,GAAG;AAGV,YAAI,CAAC,KAAK,UAAU,KAAK,EAAE,YAAY;AACrC,iBAAO,QAAQ,SAAS,EAAE,GAAG,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;AAEzD,YAAI,CAAC,KAAK,aAAa,KAAK,EAAE,YAAY;AACxC,iBAAO,QAAQ,SAAS,EAAE,GAAG,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC;AAC5D,YAAI,CAAC,KAAK,aAAa,KAAK,EAAE,YAAY;AACxC,iBAAO,QAAQ,SAAS,EAAE,GAAG,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC;AAC5D,cAAM;AAAA,MACR,UAAE;AACA,+BAAuB,MAAM;AAC7B,iBAAS,MAAM;AAAA,MACjB;AAAA,IACF;AAEA,WAAO,UAAU,EAAE,QAAQ;AAAA;AAAA;;;AC/2C3B;AAAA;AAEA,QAAM,SAAS;AACf,QAAM,EAAE,aAAa,IAAI;AACzB,QAAM,EAAE,QAAQ,IAAI;AACpB,QAAM,EAAE,UAAU,IAAI;AAEtB,QAAM,sBAAsB,CAAC,aAC3B,SAAS,eAAe,MAAM;AAC5B,UAAI,CAAC,UAAU,MAAM,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,GAAG;AACtD,uBAAe,SAAS,CAAC,EAAE,kBAAkB,KAAK,OAAO,sBAAsB,CAAC;AAChF,eAAO;AAAA,MACT;AACA,YAAM,MAAM,SAAS,IAAI;AACzB,qBAAe,SAAS,SAAS;AACjC,aAAO;AAAA,IACT;AAEF,QAAM,yBAAyB,CAAC,aAAa,CAAC,SAC5C,UAAU,MAAM,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC,CAAC,KAAK,SAAS,IAAI;AAEpE,QAAM,YAAY,CAChB,QACA,EAAE,QAAQ,OAAO,QAAQ,OAAO,YAAY,OAAO,SAAS,OAAO,UAAU,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,MAC3F;AACH,UAAI,aAAa,OAAQ,OAAM,IAAI,MAAM,mDAAmD;AAC5F,UAAI,UAAU,aAAa;AACzB,cAAM,IAAI,MAAM,gEAAgE;AAClF,YAAM,OAAO,QAAQ,WAAW;AAChC,YAAM,WAAW,UAAU,aAAa;AACxC,YAAM,MAAM,QAAQ,SAAS,CAAC,MAAM;AACpC,YAAM,UAAU,EAAE,MAAM,GAAG,MAAM,SAAS,aAAa,SAAS,GAAG,GAAG,QAAQ,SAAS;AACvF,YAAM,EAAE,OAAO,KAAK,IAAI,QAAQ,KAAK,OAAO;AAC5C,UAAI,KAAK,OAAQ;AACjB,UAAI,KAAK,KAAM,QAAO,MAAM;AAC5B,YAAM,MAAM,OAAO;AACnB,UAAI,OAAO;AACT,cAAM,YAAY,KAAK,gBAAgB,kBAAkB;AAAA,MAC3D,WAAW,WAAW;AACpB,cAAM,YAAY;AAClB,cAAM,gBAAgB,KAAK,gBAAgB,sBAAsB;AAAA,MACnE;AACA,UAAI,OAAO;AACT,YAAI,MAAM,GAAG;AACb,mBAAW,OAAO,KAAK,MAAM,GAAG,EAAE,EAAG,KAAI,MAAM,OAAO,GAAG;AACzD,YAAI,KAAK,SAAS,EAAG,KAAI,MAAM,MAAM,KAAK,KAAK,SAAS,CAAC,CAAC;AAC1D,YAAI,MAAM,GAAG;AACb,YAAI,MAAO,KAAI,MAAM,iBAAiB;AAAA,iBAC7B,UAAW,KAAI,MAAM,qBAAqB;AAAA,MACrD,OAAO;AACL,YAAI,MAAO,KAAI,MAAM,iBAAiB,KAAK,CAAC,CAAC;AAAA,iBACpC,UAAW,KAAI,MAAM,qBAAqB,KAAK,CAAC,CAAC;AAAA,YACrD,KAAI,MAAM,MAAM,KAAK,CAAC,CAAC;AAAA,MAC9B;AACA,YAAM,WAAW,IAAI,aAAa,KAAK;AACvC,eAAS,WAAW,CAAC,EAAE,OAAO,KAAK,IAAI,CAAC,MAAM,IAAI,WAAW,KAAK,KAAK,OAAO,MAAM;AACpF,eAAS,SAAS,MAAM;AACxB,aAAO;AAAA,IACT;AAEA,QAAM,kBAAkB,CAAC,aAAa,CAAC,QAAQ;AAC7C,UAAI,OAAO,QAAQ,SAAU,QAAO,EAAE,OAAO,OAAO,OAAO,wBAAwB;AACnF,UAAI;AACF,cAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,YAAI,CAAC,SAAS,KAAK,GAAG;AACpB,gBAAM,EAAE,iBAAiB,iBAAiB,IAAI,SAAS,OAAO,CAAC;AAC/D,gBAAM,UAAU,gBAAgB,MAAM,gBAAgB,YAAY,GAAG,IAAI,CAAC;AAC1E,gBAAM,QAAQ,8BAA8B,OAAO,OAAO,gBAAgB;AAC1E,iBAAO,EAAE,OAAO,OAAO,OAAO,QAAQ,SAAS,OAAO;AAAA,QACxD;AACA,eAAO,EAAE,OAAO,MAAM,MAAM;AAAA,MAC9B,SAAS,EAAE,QAAQ,GAAG;AACpB,eAAO,EAAE,OAAO,OAAO,OAAO,QAAQ;AAAA,MACxC;AAAA,IACF;AAEA,QAAM,qBAAqB,CAAC,aAAa,CAAC,QAAQ;AAChD,UAAI,OAAO,QAAQ,SAAU,QAAO,EAAE,OAAO,MAAM;AACnD,UAAI;AACF,cAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,YAAI,CAAC,SAAS,KAAK,EAAG,QAAO,EAAE,OAAO,MAAM;AAC5C,eAAO,EAAE,OAAO,MAAM,MAAM;AAAA,MAC9B,SAAS,GAAG;AACV,eAAO,EAAE,OAAO,MAAM;AAAA,MACxB;AAAA,IACF;AAEA,QAAM,SAAS,SAAS,QAAQ,EAAE,QAAQ,MAAM,GAAG,KAAK,IAAI,CAAC,GAAG;AAC9D,UAAI,CAAC,MAAO,OAAM,IAAI,MAAM,iCAAiC;AAC7D,aAAO,UAAU,QAAQ,EAAE,OAAO,GAAG,KAAK,CAAC;AAAA,IAC7C;AAEA,QAAM,OAAO,SAAS,QAAQ,EAAE,MAAM,aAAa,MAAM,GAAG,KAAK,IAAI,CAAC,GAAG;AACvE,UAAI,CAAC,WAAY,OAAM,IAAI,MAAM,uCAAuC;AACxE,aAAO,UAAU,QAAQ,EAAE,MAAM,YAAY,GAAG,KAAK,CAAC;AAAA,IACxD;AAEA,WAAO,UAAU,EAAE,WAAW,QAAQ,KAAK;AAAA;AAAA;", "names": ["stat", "rule", "local", "properties", "patterns", "delta", "nrec"]}