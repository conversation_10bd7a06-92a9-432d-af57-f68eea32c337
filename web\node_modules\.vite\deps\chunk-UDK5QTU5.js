import {
  assertNever,
  entries,
  fromEntries,
  isEmptyObject,
  keys,
  object_exports,
  omitNil,
  propAccessor
} from "./chunk-MRAOJJXG.js";
import {
  addDays,
  addMonths,
  addQuarters,
  addWeeks,
  addYears,
  differenceInDays,
  differenceInMonths,
  differenceInQuarters,
  differenceInWeeks,
  differenceInYears,
  endOfDay,
  endOfMonth,
  endOfQuarter,
  endOfWeek,
  endOfYear,
  formatISO,
  isAfter,
  isBefore,
  isLeapYear,
  isSameDay,
  isSameMonth,
  isSameQuarter,
  isSameWeek,
  isSameYear,
  max,
  min,
  parseISO,
  startOfDay,
  startOfMonth,
  startOfQuarter,
  startOfWeek,
  startOfYear,
  subYears
} from "./chunk-LKELDSZT.js";
import {
  defaultsDeep_default,
  get_default,
  isFunction_default
} from "./chunk-LK7GAOJV.js";
import {
  greatest,
  rollup
} from "./chunk-3VW5CGFU.js";
import {
  __export,
  __privateAdd,
  __privateGet,
  __privateSet,
  __publicField
} from "./chunk-KWPVD4H7.js";

// node_modules/@layerstack/utils/dist/array.js
var array_exports = {};
__export(array_exports, {
  addItem: () => addItem,
  average: () => average,
  buildTree: () => buildTree,
  chunk: () => chunk,
  combine: () => combine,
  flatten: () => flatten,
  flattenTree: () => flattenTree,
  greatestAbs: () => greatestAbs,
  joinValues: () => joinValues,
  moveItem: () => moveItem,
  movingAverage: () => movingAverage,
  nestedFindByPath: () => nestedFindByPath,
  nestedFindByPredicate: () => nestedFindByPredicate,
  removeItem: () => removeItem,
  samples: () => samples,
  subtract: () => subtract,
  sum: () => sum,
  sumObjects: () => sumObjects,
  unique: () => unique,
  walk: () => walk
});
function flatten(items) {
  return items.reduce((prev, next) => prev.concat(next), []);
}
function combine(values, func) {
  if (values.every((x) => x == null)) {
    return null;
  }
  return values.reduce(func);
}
function sum(items, prop) {
  const getProp = propAccessor(prop);
  const values = items.map((x) => getProp(x));
  return combine(values, (total, operand) => (total || 0) + (operand || 0));
}
function sumObjects(items, prop) {
  const getProp = propAccessor(prop);
  const result = rollup(items.flatMap((x) => entries(x ?? {})), (values) => sum(values, (d) => {
    const value = Number(getProp(d[1]));
    return Number.isFinite(value) ? value : 0;
  }), (d) => d[0]);
  return items.every(Array.isArray) ? Array.from(result.values()) : fromEntries(result);
}
function subtract(items, prop) {
  const getProp = propAccessor(prop);
  const values = items.map((x) => getProp(x));
  return combine(values, (total, operand) => (total || 0) - (operand || 0));
}
function average(items, prop) {
  const total = sum(items, prop);
  return total !== null ? total / items.length : null;
}
function movingAverage(items, windowSize, prop) {
  const getProp = propAccessor(prop);
  let sum2 = 0;
  const means = items.map((item, i) => {
    const value = getProp(item);
    sum2 += value ?? 0;
    if (i >= windowSize - 1) {
      const mean = sum2 / windowSize;
      const oldestValue = getProp(items[i - windowSize + 1]);
      sum2 -= oldestValue ?? 0;
      return mean;
    } else {
      return null;
    }
  });
  return means;
}
function unique(values) {
  return Array.from(new Set(values));
}
function joinValues(values = [], max2 = 3, separator = ", ") {
  const total = values.length;
  if (total <= max2) {
    return values.join(separator);
  } else {
    if (max2 === 0) {
      if (values.length === 1) {
        return values[0];
      } else {
        return `(${total} total)`;
      }
    } else {
      return `${values.slice(0, max2).join(separator)}, ... (${total} total)`;
    }
  }
}
function nestedFindByPath(arr, path, props, depth = 0) {
  const getKeyProp = propAccessor((props == null ? void 0 : props.key) ?? "key");
  const getValuesProp = propAccessor((props == null ? void 0 : props.values) ?? "values");
  const item = arr.find((x) => getKeyProp(x) === path[depth]);
  if (depth === path.length - 1) {
    return item;
  } else {
    const children = getValuesProp(item);
    if (children) {
      return nestedFindByPath(getValuesProp(item), path, props, depth + 1);
    }
  }
}
function nestedFindByPredicate(arr, predicate, childrenProp) {
  const getChildrenProp = propAccessor(childrenProp ?? "children");
  let match = arr.find(predicate);
  if (match) {
    return match;
  } else {
    for (var item of arr) {
      const children = getChildrenProp(item);
      if (children) {
        match = nestedFindByPredicate(getChildrenProp(item), predicate, childrenProp);
        if (match) {
          return match;
        }
      }
    }
  }
  return void 0;
}
function buildTree(arr) {
  var levels = [{}];
  arr.forEach((o) => {
    var _a;
    levels.length = o.level;
    levels[o.level - 1].children = levels[o.level - 1].children || [];
    (_a = levels[o.level - 1].children) == null ? void 0 : _a.push(o);
    levels[o.level] = o;
  });
  return levels[0].children ?? [];
}
function walk(arr, children, callback) {
  arr.forEach((item) => {
    callback(item);
    if (children(item)) {
      walk(children(item), children, callback);
    }
  });
}
function flattenTree(arr, children) {
  const flatArray = [];
  walk(arr, children, (item) => flatArray.push(item));
  return flatArray;
}
function chunk(array, size) {
  return array.reduce((acc, item, index) => {
    const bucket = Math.floor(index / size);
    if (!acc[bucket]) {
      acc[bucket] = [];
    }
    acc[bucket].push(item);
    return acc;
  }, []);
}
function samples(array, size) {
  if (!((size = Math.floor(size)) > 0))
    return [];
  const n = array.length;
  if (!(n > size))
    return [...array];
  if (size === 1)
    return [array[n >> 1]];
  return Array.from({ length: size }, (_, i) => array[Math.round(i / (size - 1) * (n - 1))]);
}
function addItem(array, item, index) {
  array.splice(index, 0, item);
  return array;
}
function moveItem(array, from, to) {
  var item = array[from];
  array.splice(from, 1);
  array.splice(to, 0, item);
  return array;
}
function removeItem(array, index) {
  array.splice(index, 1);
  return array;
}
function greatestAbs(array) {
  return greatest(array, (a, b) => Math.abs(a) - Math.abs(b));
}

// node_modules/@layerstack/utils/dist/date.js
var date_exports = {};
__export(date_exports, {
  DateToken: () => DateToken,
  DayOfWeek: () => DayOfWeek,
  PeriodType: () => PeriodType,
  endOfBiWeek: () => endOfBiWeek,
  endOfFiscalYear: () => endOfFiscalYear,
  formatDate: () => formatDate,
  formatDateWithLocale: () => formatDateWithLocale,
  formatISODate: () => formatISODate,
  formatIntl: () => formatIntl,
  getDateFuncsByPeriodType: () => getDateFuncsByPeriodType,
  getDayOfWeek: () => getDayOfWeek,
  getDayOfWeekName: () => getDayOfWeekName,
  getFiscalYear: () => getFiscalYear,
  getFiscalYearRange: () => getFiscalYearRange,
  getMaxSelectedDate: () => getMaxSelectedDate,
  getMinSelectedDate: () => getMinSelectedDate,
  getMonthDaysByWeek: () => getMonthDaysByWeek,
  getMonths: () => getMonths,
  getPeriodTypeByCode: () => getPeriodTypeByCode,
  getPeriodTypeCode: () => getPeriodTypeCode,
  getPeriodTypeName: () => getPeriodTypeName,
  getPeriodTypeNameWithLocale: () => getPeriodTypeNameWithLocale,
  hasDayOfWeek: () => hasDayOfWeek,
  isSameFiscalYear: () => isSameFiscalYear,
  isStringDate: () => isStringDate,
  localToUtcDate: () => localToUtcDate,
  missingDayOfWeek: () => missingDayOfWeek,
  randomDate: () => randomDate,
  replaceDayOfWeek: () => replaceDayOfWeek,
  startOfBiWeek: () => startOfBiWeek,
  startOfFiscalYear: () => startOfFiscalYear,
  updatePeriodTypeWithWeekStartsOn: () => updatePeriodTypeWithWeekStartsOn,
  utcToLocalDate: () => utcToLocalDate
});

// node_modules/@layerstack/utils/dist/typeGuards.js
function hasKeyOf(object, key) {
  if (object) {
    return key in object;
  } else {
    return false;
  }
}
function hasProperty(o, name) {
  return name in o;
}
function nameof(key, instance) {
  return key;
}
function isNumber(val) {
  return typeof val === "number";
}
function notNull(value) {
  return value != null;
}
function isElement(elem) {
  return !!elem && elem instanceof Element;
}
function isSVGElement(elem) {
  return !!elem && (elem instanceof SVGElement || "ownerSVGElement" in elem);
}
function isSVGSVGElement(elem) {
  return !!elem && "createSVGPoint" in elem;
}
function isSVGGraphicsElement(elem) {
  return !!elem && "getScreenCTM" in elem;
}
function isTouchEvent(event) {
  return !!event && "changedTouches" in event;
}
function isEvent(event) {
  return !!event && (event instanceof Event || "nativeEvent" in event && event.nativeEvent instanceof Event);
}

// node_modules/@layerstack/utils/dist/date_types.js
var PeriodType;
(function(PeriodType2) {
  PeriodType2[PeriodType2["Custom"] = 1] = "Custom";
  PeriodType2[PeriodType2["Day"] = 10] = "Day";
  PeriodType2[PeriodType2["DayTime"] = 11] = "DayTime";
  PeriodType2[PeriodType2["TimeOnly"] = 15] = "TimeOnly";
  PeriodType2[PeriodType2["Week"] = 20] = "Week";
  PeriodType2[PeriodType2["WeekSun"] = 21] = "WeekSun";
  PeriodType2[PeriodType2["WeekMon"] = 22] = "WeekMon";
  PeriodType2[PeriodType2["WeekTue"] = 23] = "WeekTue";
  PeriodType2[PeriodType2["WeekWed"] = 24] = "WeekWed";
  PeriodType2[PeriodType2["WeekThu"] = 25] = "WeekThu";
  PeriodType2[PeriodType2["WeekFri"] = 26] = "WeekFri";
  PeriodType2[PeriodType2["WeekSat"] = 27] = "WeekSat";
  PeriodType2[PeriodType2["Month"] = 30] = "Month";
  PeriodType2[PeriodType2["MonthYear"] = 31] = "MonthYear";
  PeriodType2[PeriodType2["Quarter"] = 40] = "Quarter";
  PeriodType2[PeriodType2["CalendarYear"] = 50] = "CalendarYear";
  PeriodType2[PeriodType2["FiscalYearOctober"] = 60] = "FiscalYearOctober";
  PeriodType2[PeriodType2["BiWeek1"] = 70] = "BiWeek1";
  PeriodType2[PeriodType2["BiWeek1Sun"] = 71] = "BiWeek1Sun";
  PeriodType2[PeriodType2["BiWeek1Mon"] = 72] = "BiWeek1Mon";
  PeriodType2[PeriodType2["BiWeek1Tue"] = 73] = "BiWeek1Tue";
  PeriodType2[PeriodType2["BiWeek1Wed"] = 74] = "BiWeek1Wed";
  PeriodType2[PeriodType2["BiWeek1Thu"] = 75] = "BiWeek1Thu";
  PeriodType2[PeriodType2["BiWeek1Fri"] = 76] = "BiWeek1Fri";
  PeriodType2[PeriodType2["BiWeek1Sat"] = 77] = "BiWeek1Sat";
  PeriodType2[PeriodType2["BiWeek2"] = 80] = "BiWeek2";
  PeriodType2[PeriodType2["BiWeek2Sun"] = 81] = "BiWeek2Sun";
  PeriodType2[PeriodType2["BiWeek2Mon"] = 82] = "BiWeek2Mon";
  PeriodType2[PeriodType2["BiWeek2Tue"] = 83] = "BiWeek2Tue";
  PeriodType2[PeriodType2["BiWeek2Wed"] = 84] = "BiWeek2Wed";
  PeriodType2[PeriodType2["BiWeek2Thu"] = 85] = "BiWeek2Thu";
  PeriodType2[PeriodType2["BiWeek2Fri"] = 86] = "BiWeek2Fri";
  PeriodType2[PeriodType2["BiWeek2Sat"] = 87] = "BiWeek2Sat";
})(PeriodType || (PeriodType = {}));
var DayOfWeek;
(function(DayOfWeek2) {
  DayOfWeek2[DayOfWeek2["Sunday"] = 0] = "Sunday";
  DayOfWeek2[DayOfWeek2["Monday"] = 1] = "Monday";
  DayOfWeek2[DayOfWeek2["Tuesday"] = 2] = "Tuesday";
  DayOfWeek2[DayOfWeek2["Wednesday"] = 3] = "Wednesday";
  DayOfWeek2[DayOfWeek2["Thursday"] = 4] = "Thursday";
  DayOfWeek2[DayOfWeek2["Friday"] = 5] = "Friday";
  DayOfWeek2[DayOfWeek2["Saturday"] = 6] = "Saturday";
})(DayOfWeek || (DayOfWeek = {}));
var DateToken;
(function(DateToken2) {
  DateToken2["Year_numeric"] = "yyy";
  DateToken2["Year_2Digit"] = "yy";
  DateToken2["Month_long"] = "MMMM";
  DateToken2["Month_short"] = "MMM";
  DateToken2["Month_2Digit"] = "MM";
  DateToken2["Month_numeric"] = "M";
  DateToken2["Hour_numeric"] = "h";
  DateToken2["Hour_2Digit"] = "hh";
  DateToken2["Hour_wAMPM"] = "a";
  DateToken2["Hour_woAMPM"] = "aaaaaa";
  DateToken2["Minute_numeric"] = "m";
  DateToken2["Minute_2Digit"] = "mm";
  DateToken2["Second_numeric"] = "s";
  DateToken2["Second_2Digit"] = "ss";
  DateToken2["MiliSecond_3"] = "SSS";
  DateToken2["DayOfMonth_numeric"] = "d";
  DateToken2["DayOfMonth_2Digit"] = "dd";
  DateToken2["DayOfMonth_withOrdinal"] = "do";
  DateToken2["DayOfWeek_narrow"] = "eeeee";
  DateToken2["DayOfWeek_long"] = "eeee";
  DateToken2["DayOfWeek_short"] = "eee";
})(DateToken || (DateToken = {}));

// node_modules/@layerstack/utils/dist/dateInternal.js
function getWeekStartsOnFromIntl(locales) {
  var _a;
  if (!locales) {
    return DayOfWeek.Sunday;
  }
  const locale = new Intl.Locale(locales);
  const weekInfo = locale.weekInfo ?? ((_a = locale.getWeekInfo) == null ? void 0 : _a.call(locale));
  return ((weekInfo == null ? void 0 : weekInfo.firstDay) ?? 0) % 7;
}

// node_modules/@layerstack/utils/dist/locale.js
var defaultLocaleSettings = {
  locale: "en",
  dictionary: {
    Ok: "Ok",
    Cancel: "Cancel",
    Date: {
      Start: "Start",
      End: "End",
      Empty: "Empty",
      Day: "Day",
      DayTime: "Day Time",
      Time: "Time",
      Week: "Week",
      BiWeek: "Bi-Week",
      Month: "Month",
      Quarter: "Quarter",
      CalendarYear: "Calendar Year",
      FiscalYearOct: "Fiscal Year (Oct)",
      PeriodDay: {
        Current: "Today",
        Last: "Yesterday",
        LastX: "Last {0} days"
      },
      PeriodWeek: {
        Current: "This week",
        Last: "Last week",
        LastX: "Last {0} weeks"
      },
      PeriodBiWeek: {
        Current: "This bi-week",
        Last: "Last bi-week",
        LastX: "Last {0} bi-weeks"
      },
      PeriodMonth: {
        Current: "This month",
        Last: "Last month",
        LastX: "Last {0} months"
      },
      PeriodQuarter: {
        Current: "This quarter",
        Last: "Last quarter",
        LastX: "Last {0} quarters"
      },
      PeriodQuarterSameLastyear: "Same quarter last year",
      PeriodYear: {
        Current: "This year",
        Last: "Last year",
        LastX: "Last {0} years"
      },
      PeriodFiscalYear: {
        Current: "This fiscal year",
        Last: "Last fiscal year",
        LastX: "Last {0} fiscal years"
      }
    }
  },
  formats: {
    numbers: {
      defaults: {
        currency: "USD",
        fractionDigits: 2,
        currencyDisplay: "symbol"
      }
    },
    dates: {
      baseParsing: "MM/dd/yyyy",
      weekStartsOn: DayOfWeek.Sunday,
      ordinalSuffixes: {
        one: "st",
        two: "nd",
        few: "rd",
        other: "th"
      },
      presets: {
        day: {
          short: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric],
          default: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric],
          long: [DateToken.DayOfMonth_numeric, DateToken.Month_short, DateToken.Year_numeric]
        },
        dayTime: {
          short: [
            DateToken.DayOfMonth_numeric,
            DateToken.Month_numeric,
            DateToken.Year_numeric,
            DateToken.Hour_numeric,
            DateToken.Minute_numeric
          ],
          default: [
            DateToken.DayOfMonth_numeric,
            DateToken.Month_numeric,
            DateToken.Year_numeric,
            DateToken.Hour_2Digit,
            DateToken.Minute_2Digit
          ],
          long: [
            DateToken.DayOfMonth_numeric,
            DateToken.Month_numeric,
            DateToken.Year_numeric,
            DateToken.Hour_2Digit,
            DateToken.Minute_2Digit,
            DateToken.Second_2Digit
          ]
        },
        timeOnly: {
          short: [DateToken.Hour_numeric, DateToken.Minute_numeric],
          default: [DateToken.Hour_2Digit, DateToken.Minute_2Digit, DateToken.Second_2Digit],
          long: [
            DateToken.Hour_2Digit,
            DateToken.Minute_2Digit,
            DateToken.Second_2Digit,
            DateToken.MiliSecond_3
          ]
        },
        week: {
          short: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric],
          default: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric],
          long: [DateToken.DayOfMonth_numeric, DateToken.Month_numeric, DateToken.Year_numeric]
        },
        month: {
          short: DateToken.Month_short,
          default: DateToken.Month_short,
          long: DateToken.Month_long
        },
        monthsYear: {
          short: [DateToken.Month_short, DateToken.Year_2Digit],
          default: [DateToken.Month_long, DateToken.Year_numeric],
          long: [DateToken.Month_long, DateToken.Year_numeric]
        },
        year: {
          short: DateToken.Year_2Digit,
          default: DateToken.Year_numeric,
          long: DateToken.Year_numeric
        }
      }
    }
  }
};
function createLocaleSettings(localeSettings, base = defaultLocaleSettings) {
  var _a, _b, _c, _d;
  if ((_b = (_a = localeSettings.formats) == null ? void 0 : _a.dates) == null ? void 0 : _b.ordinalSuffixes) {
    localeSettings.formats.dates.ordinalSuffixes = {
      one: "",
      two: "",
      few: "",
      other: "",
      zero: "",
      many: "",
      ...localeSettings.formats.dates.ordinalSuffixes
    };
  }
  if (((_d = (_c = localeSettings.formats) == null ? void 0 : _c.dates) == null ? void 0 : _d.weekStartsOn) === void 0) {
    localeSettings = defaultsDeep_default(localeSettings, {
      formats: { dates: { weekStartsOn: getWeekStartsOnFromIntl(localeSettings.locale) } }
    });
  }
  return defaultsDeep_default(localeSettings, base);
}
var defaultLocale = createLocaleSettings({ locale: "en" });

// node_modules/@layerstack/utils/dist/date.js
function getDayOfWeekName(weekStartsOn, locales) {
  const date = new Date(2024, 0, 7 + weekStartsOn);
  const formatter = new Intl.DateTimeFormat(locales, { weekday: "short" });
  return formatter.format(date);
}
function getPeriodTypeName(periodType) {
  return getPeriodTypeNameWithLocale(defaultLocale, periodType);
}
function getPeriodTypeNameWithLocale(settings, periodType) {
  const { locale, dictionary: { Date: dico } } = settings;
  switch (periodType) {
    case PeriodType.Custom:
      return "Custom";
    case PeriodType.Day:
      return dico.Day;
    case PeriodType.DayTime:
      return dico.DayTime;
    case PeriodType.TimeOnly:
      return dico.Time;
    case PeriodType.WeekSun:
      return `${dico.Week} (${getDayOfWeekName(DayOfWeek.Sunday, locale)})`;
    case PeriodType.WeekMon:
      return `${dico.Week} (${getDayOfWeekName(1, locale)})`;
    case PeriodType.WeekTue:
      return `${dico.Week} (${getDayOfWeekName(2, locale)})`;
    case PeriodType.WeekWed:
      return `${dico.Week} (${getDayOfWeekName(3, locale)})`;
    case PeriodType.WeekThu:
      return `${dico.Week} (${getDayOfWeekName(4, locale)})`;
    case PeriodType.WeekFri:
      return `${dico.Week} (${getDayOfWeekName(5, locale)})`;
    case PeriodType.WeekSat:
      return `${dico.Week} (${getDayOfWeekName(6, locale)})`;
    case PeriodType.Week:
      return dico.Week;
    case PeriodType.Month:
      return dico.Month;
    case PeriodType.MonthYear:
      return dico.Month;
    case PeriodType.Quarter:
      return dico.Quarter;
    case PeriodType.CalendarYear:
      return dico.CalendarYear;
    case PeriodType.FiscalYearOctober:
      return dico.FiscalYearOct;
    case PeriodType.BiWeek1Sun:
      return `${dico.BiWeek} (${getDayOfWeekName(0, locale)})`;
    case PeriodType.BiWeek1Mon:
      return `${dico.BiWeek} (${getDayOfWeekName(1, locale)})`;
    case PeriodType.BiWeek1Tue:
      return `${dico.BiWeek} (${getDayOfWeekName(2, locale)})`;
    case PeriodType.BiWeek1Wed:
      return `${dico.BiWeek} (${getDayOfWeekName(3, locale)})`;
    case PeriodType.BiWeek1Thu:
      return `${dico.BiWeek} (${getDayOfWeekName(4, locale)})`;
    case PeriodType.BiWeek1Fri:
      return `${dico.BiWeek} (${getDayOfWeekName(5, locale)})`;
    case PeriodType.BiWeek1Sat:
      return `${dico.BiWeek} (${getDayOfWeekName(6, locale)})`;
    case PeriodType.BiWeek1:
      return dico.BiWeek;
    case PeriodType.BiWeek2Sun:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(0, locale)})`;
    case PeriodType.BiWeek2Mon:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(1, locale)})`;
    case PeriodType.BiWeek2Tue:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(2, locale)})`;
    case PeriodType.BiWeek2Wed:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(3, locale)})`;
    case PeriodType.BiWeek2Thu:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(4, locale)})`;
    case PeriodType.BiWeek2Fri:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(5, locale)})`;
    case PeriodType.BiWeek2Sat:
      return `${dico.BiWeek} 2 (${getDayOfWeekName(6, locale)})`;
    case PeriodType.BiWeek2:
      return `${dico.BiWeek} 2`;
    default:
      assertNever(periodType);
  }
}
var periodTypeMappings = {
  [PeriodType.Custom]: "CUSTOM",
  [PeriodType.Day]: "DAY",
  [PeriodType.DayTime]: "DAY-TIME",
  [PeriodType.TimeOnly]: "TIME",
  [PeriodType.WeekSun]: "WEEK-SUN",
  [PeriodType.WeekMon]: "WEEK-MON",
  [PeriodType.WeekTue]: "WEEK-TUE",
  [PeriodType.WeekWed]: "WEEK-WED",
  [PeriodType.WeekThu]: "WEEK-THU",
  [PeriodType.WeekFri]: "WEEK-FRI",
  [PeriodType.WeekSat]: "WEEK-SAT",
  [PeriodType.Week]: "WEEK",
  [PeriodType.Month]: "MTH",
  [PeriodType.MonthYear]: "MTH-CY",
  [PeriodType.Quarter]: "QTR",
  [PeriodType.CalendarYear]: "CY",
  [PeriodType.FiscalYearOctober]: "FY-OCT",
  [PeriodType.BiWeek1Sun]: "BIWEEK1-SUN",
  [PeriodType.BiWeek1Mon]: "BIWEEK1-MON",
  [PeriodType.BiWeek1Tue]: "BIWEEK1-TUE",
  [PeriodType.BiWeek1Wed]: "BIWEEK1-WED",
  [PeriodType.BiWeek1Thu]: "BIWEEK1-THU",
  [PeriodType.BiWeek1Fri]: "BIWEEK1-FRI",
  [PeriodType.BiWeek1Sat]: "BIWEEK1-SAT",
  [PeriodType.BiWeek1]: "BIWEEK1",
  [PeriodType.BiWeek2Sun]: "BIWEEK2-SUN",
  [PeriodType.BiWeek2Mon]: "BIWEEK2-MON",
  [PeriodType.BiWeek2Tue]: "BIWEEK2-TUE",
  [PeriodType.BiWeek2Wed]: "BIWEEK2-WED",
  [PeriodType.BiWeek2Thu]: "BIWEEK2-THU",
  [PeriodType.BiWeek2Fri]: "BIWEEK2-FRI",
  [PeriodType.BiWeek2Sat]: "BIWEEK2-SAT",
  [PeriodType.BiWeek2]: "BIWEEK2"
};
function getPeriodTypeCode(periodType) {
  return periodTypeMappings[periodType];
}
function getPeriodTypeByCode(code) {
  const element = entries(periodTypeMappings).find((c) => c[1] === code);
  return parseInt(String((element == null ? void 0 : element[0]) ?? "1"));
}
function getDayOfWeek(periodType) {
  if (periodType >= PeriodType.WeekSun && periodType <= PeriodType.WeekSat || periodType >= PeriodType.BiWeek1Sun && periodType <= PeriodType.BiWeek1Sat || periodType >= PeriodType.BiWeek2Sun && periodType <= PeriodType.BiWeek2Sat) {
    return periodType % 10 - 1;
  } else {
    return null;
  }
}
function replaceDayOfWeek(periodType, dayOfWeek) {
  if (hasDayOfWeek(periodType)) {
    return periodType - (getDayOfWeek(periodType) ?? 0) + dayOfWeek;
  } else if (missingDayOfWeek(periodType)) {
    return periodType + dayOfWeek + 1;
  } else {
    return periodType;
  }
}
function hasDayOfWeek(periodType) {
  if (periodType >= PeriodType.WeekSun && periodType <= PeriodType.WeekSat) {
    return true;
  }
  if (periodType >= PeriodType.BiWeek1Sun && periodType <= PeriodType.BiWeek1Sat) {
    return true;
  }
  if (periodType >= PeriodType.BiWeek2Sun && periodType <= PeriodType.BiWeek2Sat) {
    return true;
  }
  return false;
}
function missingDayOfWeek(periodType) {
  return [PeriodType.Week, PeriodType.BiWeek1, PeriodType.BiWeek2].includes(periodType);
}
function getMonths(year = (/* @__PURE__ */ new Date()).getFullYear()) {
  return Array.from({ length: 12 }, (_, i) => new Date(year, i, 1));
}
function getMonthDaysByWeek(dateInTheMonth, weekStartsOn = DayOfWeek.Sunday) {
  const startOfFirstWeek = startOfWeek(startOfMonth(dateInTheMonth), { weekStartsOn });
  const endOfLastWeek = endOfWeek(endOfMonth(dateInTheMonth), { weekStartsOn });
  const list = [];
  let valueToAdd = startOfFirstWeek;
  while (valueToAdd <= endOfLastWeek) {
    list.push(valueToAdd);
    valueToAdd = addDays(valueToAdd, 1);
  }
  return chunk(list, 7);
}
function getMinSelectedDate(date) {
  if (date instanceof Date) {
    return date;
  } else if (date instanceof Array) {
    return min(date);
  } else if (hasKeyOf(date, "from")) {
    return date.from;
  } else {
    return null;
  }
}
function getMaxSelectedDate(date) {
  if (date instanceof Date) {
    return date;
  } else if (date instanceof Array) {
    return max(date);
  } else if (hasKeyOf(date, "to")) {
    return date.to;
  } else {
    return null;
  }
}
function getFiscalYear(date = /* @__PURE__ */ new Date(), options) {
  if (date === null) {
    return NaN;
  }
  const startMonth = options && options.startMonth || 10;
  return date.getMonth() >= startMonth - 1 ? date.getFullYear() + 1 : date.getFullYear();
}
function getFiscalYearRange(date = /* @__PURE__ */ new Date(), options) {
  const fiscalYear = getFiscalYear(date, options);
  const startMonth = options && options.startMonth || 10;
  const numberOfMonths = options && options.numberOfMonths || 12;
  const startDate = new Date((fiscalYear || 0) - 1, startMonth - 1, 1);
  const endDate = endOfMonth(addMonths(startDate, numberOfMonths - 1));
  return { startDate, endDate };
}
function startOfFiscalYear(date, options) {
  return getFiscalYearRange(date, options).startDate;
}
function endOfFiscalYear(date, options) {
  return getFiscalYearRange(date, options).endDate;
}
function isSameFiscalYear(dateLeft, dateRight) {
  return getFiscalYear(dateLeft) === getFiscalYear(dateRight);
}
var biweekBaseDates = [/* @__PURE__ */ new Date("1799-12-22T00:00"), /* @__PURE__ */ new Date("1799-12-15T00:00")];
function startOfBiWeek(date, week, startOfWeek2) {
  var weekBaseDate = biweekBaseDates[week - 1];
  var baseDate = addDays(weekBaseDate, startOfWeek2);
  var periodsSince = Math.floor(differenceInDays(date, baseDate) / 14);
  return addDays(baseDate, periodsSince * 14);
}
function endOfBiWeek(date, week, startOfWeek2) {
  return addDays(startOfBiWeek(date, week, startOfWeek2), 13);
}
function getDateFuncsByPeriodType(settings, periodType) {
  if (settings) {
    periodType = updatePeriodTypeWithWeekStartsOn(settings.formats.dates.weekStartsOn, periodType);
  }
  switch (periodType) {
    case PeriodType.Day:
      return {
        start: startOfDay,
        end: endOfDay,
        add: addDays,
        difference: differenceInDays,
        isSame: isSameDay
      };
    case PeriodType.Week:
    case PeriodType.WeekSun:
      return {
        start: startOfWeek,
        end: endOfWeek,
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: isSameWeek
      };
    case PeriodType.WeekMon:
      return {
        start: (date) => startOfWeek(date, { weekStartsOn: 1 }),
        end: (date) => endOfWeek(date, { weekStartsOn: 1 }),
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 1 })
      };
    case PeriodType.WeekTue:
      return {
        start: (date) => startOfWeek(date, { weekStartsOn: 2 }),
        end: (date) => endOfWeek(date, { weekStartsOn: 2 }),
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 2 })
      };
    case PeriodType.WeekWed:
      return {
        start: (date) => startOfWeek(date, { weekStartsOn: 3 }),
        end: (date) => endOfWeek(date, { weekStartsOn: 3 }),
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 3 })
      };
    case PeriodType.WeekThu:
      return {
        start: (date) => startOfWeek(date, { weekStartsOn: 4 }),
        end: (date) => endOfWeek(date, { weekStartsOn: 4 }),
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 4 })
      };
    case PeriodType.WeekFri:
      return {
        start: (date) => startOfWeek(date, { weekStartsOn: 5 }),
        end: (date) => endOfWeek(date, { weekStartsOn: 5 }),
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 5 })
      };
    case PeriodType.WeekSat:
      return {
        start: (date) => startOfWeek(date, { weekStartsOn: 6 }),
        end: (date) => endOfWeek(date, { weekStartsOn: 6 }),
        add: addWeeks,
        difference: differenceInWeeks,
        isSame: (dateLeft, dateRight) => isSameWeek(dateLeft, dateRight, { weekStartsOn: 6 })
      };
    case PeriodType.Month:
      return {
        start: startOfMonth,
        end: endOfMonth,
        add: addMonths,
        difference: differenceInMonths,
        isSame: isSameMonth
      };
    case PeriodType.Quarter:
      return {
        start: startOfQuarter,
        end: endOfQuarter,
        add: addQuarters,
        difference: differenceInQuarters,
        isSame: isSameQuarter
      };
    case PeriodType.CalendarYear:
      return {
        start: startOfYear,
        end: endOfYear,
        add: addYears,
        difference: differenceInYears,
        isSame: isSameYear
      };
    case PeriodType.FiscalYearOctober:
      return {
        start: startOfFiscalYear,
        end: endOfFiscalYear,
        add: addYears,
        difference: differenceInYears,
        isSame: isSameFiscalYear
      };
    case PeriodType.BiWeek1:
    case PeriodType.BiWeek1Sun:
    case PeriodType.BiWeek1Mon:
    case PeriodType.BiWeek1Tue:
    case PeriodType.BiWeek1Wed:
    case PeriodType.BiWeek1Thu:
    case PeriodType.BiWeek1Fri:
    case PeriodType.BiWeek1Sat:
    case PeriodType.BiWeek2:
    case PeriodType.BiWeek2Sun:
    case PeriodType.BiWeek2Mon:
    case PeriodType.BiWeek2Tue:
    case PeriodType.BiWeek2Wed:
    case PeriodType.BiWeek2Thu:
    case PeriodType.BiWeek2Fri:
    case PeriodType.BiWeek2Sat: {
      const week = getPeriodTypeCode(periodType).startsWith("BIWEEK1") ? 1 : 2;
      const dayOfWeek = getDayOfWeek(periodType);
      return {
        start: (date) => startOfBiWeek(date, week, dayOfWeek),
        end: (date) => endOfBiWeek(date, week, dayOfWeek),
        add: (date, amount) => addWeeks(date, amount * 2),
        difference: (dateLeft, dateRight) => {
          return differenceInWeeks(dateLeft, dateRight) / 2;
        },
        isSame: (dateLeft, dateRight) => {
          return isSameDay(startOfBiWeek(dateLeft, week, dayOfWeek), startOfBiWeek(dateRight, week, dayOfWeek));
        }
      };
    }
    case PeriodType.Custom:
    case PeriodType.DayTime:
    case PeriodType.TimeOnly:
    case PeriodType.MonthYear:
    case null:
    case void 0:
      return {
        start: startOfDay,
        end: endOfDay,
        add: addDays,
        difference: differenceInDays,
        isSame: isSameDay
      };
    default:
      assertNever(periodType);
  }
}
function formatISODate(date, representation = "complete") {
  if (date == null) {
    return "";
  }
  if (typeof date === "string") {
    date = parseISO(date);
  }
  return formatISO(date, { representation });
}
function formatIntl(settings, dt, tokens_or_intlOptions) {
  const { locale, formats: { dates: { ordinalSuffixes: suffixes } } } = settings;
  function formatIntlOrdinal(formatter2, with_ordinal = false) {
    if (with_ordinal) {
      const rules = new Intl.PluralRules(locale, { type: "ordinal" });
      const splited = formatter2.formatToParts(dt);
      return splited.map((c) => {
        if (c.type === "day") {
          const ordinal = rules.select(parseInt(c.value, 10));
          const suffix = suffixes[ordinal];
          return `${c.value}${suffix}`;
        }
        return c.value;
      }).join("");
    }
    return formatter2.format(dt);
  }
  if (typeof tokens_or_intlOptions !== "string" && !Array.isArray(tokens_or_intlOptions)) {
    return formatIntlOrdinal(new Intl.DateTimeFormat(locale, tokens_or_intlOptions), tokens_or_intlOptions.withOrdinal);
  }
  const tokens = Array.isArray(tokens_or_intlOptions) ? tokens_or_intlOptions.join("") : tokens_or_intlOptions;
  const formatter = new Intl.DateTimeFormat(locale, {
    year: tokens.includes(DateToken.Year_numeric) ? "numeric" : tokens.includes(DateToken.Year_2Digit) ? "2-digit" : void 0,
    month: tokens.includes(DateToken.Month_long) ? "long" : tokens.includes(DateToken.Month_short) ? "short" : tokens.includes(DateToken.Month_2Digit) ? "2-digit" : tokens.includes(DateToken.Month_numeric) ? "numeric" : void 0,
    day: tokens.includes(DateToken.DayOfMonth_2Digit) ? "2-digit" : tokens.includes(DateToken.DayOfMonth_numeric) ? "numeric" : void 0,
    hour: tokens.includes(DateToken.Hour_2Digit) ? "2-digit" : tokens.includes(DateToken.Hour_numeric) ? "numeric" : void 0,
    hour12: tokens.includes(DateToken.Hour_woAMPM) ? false : tokens.includes(DateToken.Hour_wAMPM) ? true : void 0,
    minute: tokens.includes(DateToken.Minute_2Digit) ? "2-digit" : tokens.includes(DateToken.Minute_numeric) ? "numeric" : void 0,
    second: tokens.includes(DateToken.Second_2Digit) ? "2-digit" : tokens.includes(DateToken.Second_numeric) ? "numeric" : void 0,
    fractionalSecondDigits: tokens.includes(DateToken.MiliSecond_3) ? 3 : void 0,
    weekday: tokens.includes(DateToken.DayOfWeek_narrow) ? "narrow" : tokens.includes(DateToken.DayOfWeek_long) ? "long" : tokens.includes(DateToken.DayOfWeek_short) ? "short" : void 0
  });
  return formatIntlOrdinal(formatter, tokens.includes(DateToken.DayOfMonth_withOrdinal));
}
function range(settings, date, weekStartsOn, formatToUse, biWeek = void 0) {
  const start = biWeek === void 0 ? startOfWeek(date, { weekStartsOn }) : startOfBiWeek(date, biWeek, weekStartsOn);
  const end = biWeek === void 0 ? endOfWeek(date, { weekStartsOn }) : endOfBiWeek(date, biWeek, weekStartsOn);
  return formatIntl(settings, start, formatToUse) + " - " + formatIntl(settings, end, formatToUse);
}
function formatDate(date, periodType, options = {}) {
  return formatDateWithLocale(defaultLocale, date, periodType, options);
}
function updatePeriodTypeWithWeekStartsOn(weekStartsOn, periodType) {
  if (periodType === PeriodType.Week) {
    periodType = [
      PeriodType.WeekSun,
      PeriodType.WeekMon,
      PeriodType.WeekTue,
      PeriodType.WeekWed,
      PeriodType.WeekThu,
      PeriodType.WeekFri,
      PeriodType.WeekSat
    ][weekStartsOn];
  } else if (periodType === PeriodType.BiWeek1) {
    periodType = [
      PeriodType.BiWeek1Sun,
      PeriodType.BiWeek1Mon,
      PeriodType.BiWeek1Tue,
      PeriodType.BiWeek1Wed,
      PeriodType.BiWeek1Thu,
      PeriodType.BiWeek1Fri,
      PeriodType.BiWeek1Sat
    ][weekStartsOn];
  } else if (periodType === PeriodType.BiWeek2) {
    periodType = [
      PeriodType.BiWeek2Sun,
      PeriodType.BiWeek2Mon,
      PeriodType.BiWeek2Tue,
      PeriodType.BiWeek2Wed,
      PeriodType.BiWeek2Thu,
      PeriodType.BiWeek2Fri,
      PeriodType.BiWeek2Sat
    ][weekStartsOn];
  }
  return periodType;
}
function formatDateWithLocale(settings, date, periodType, options = {}) {
  if (typeof date === "string") {
    date = parseISO(date);
  }
  if (date == null || isNaN(date)) {
    return "";
  }
  const weekStartsOn = options.weekStartsOn ?? settings.formats.dates.weekStartsOn;
  const { day, dayTime, timeOnly, week, month, monthsYear, year } = settings.formats.dates.presets;
  periodType = updatePeriodTypeWithWeekStartsOn(weekStartsOn, periodType) ?? periodType;
  function rv(preset) {
    if (options.variant === "custom") {
      return options.custom ?? preset.default;
    } else if (options.custom && !options.variant) {
      return options.custom;
    }
    return preset[options.variant ?? "default"];
  }
  switch (periodType) {
    case PeriodType.Custom:
      return formatIntl(settings, date, options.custom);
    case PeriodType.Day:
      return formatIntl(settings, date, rv(day));
    case PeriodType.DayTime:
      return formatIntl(settings, date, rv(dayTime));
    case PeriodType.TimeOnly:
      return formatIntl(settings, date, rv(timeOnly));
    case PeriodType.Week:
    case PeriodType.WeekSun:
      return range(settings, date, 0, rv(week));
    case PeriodType.WeekMon:
      return range(settings, date, 1, rv(week));
    case PeriodType.WeekTue:
      return range(settings, date, 2, rv(week));
    case PeriodType.WeekWed:
      return range(settings, date, 3, rv(week));
    case PeriodType.WeekThu:
      return range(settings, date, 4, rv(week));
    case PeriodType.WeekFri:
      return range(settings, date, 5, rv(week));
    case PeriodType.WeekSat:
      return range(settings, date, 6, rv(week));
    case PeriodType.Month:
      return formatIntl(settings, date, rv(month));
    case PeriodType.MonthYear:
      return formatIntl(settings, date, rv(monthsYear));
    case PeriodType.Quarter:
      return [
        formatIntl(settings, startOfQuarter(date), rv(month)),
        formatIntl(settings, endOfQuarter(date), rv(monthsYear))
      ].join(" - ");
    case PeriodType.CalendarYear:
      return formatIntl(settings, date, rv(year));
    case PeriodType.FiscalYearOctober:
      const fDate = new Date(getFiscalYear(date), 0, 1);
      return formatIntl(settings, fDate, rv(year));
    case PeriodType.BiWeek1:
    case PeriodType.BiWeek1Sun:
      return range(settings, date, 0, rv(week), 1);
    case PeriodType.BiWeek1Mon:
      return range(settings, date, 1, rv(week), 1);
    case PeriodType.BiWeek1Tue:
      return range(settings, date, 2, rv(week), 1);
    case PeriodType.BiWeek1Wed:
      return range(settings, date, 3, rv(week), 1);
    case PeriodType.BiWeek1Thu:
      return range(settings, date, 4, rv(week), 1);
    case PeriodType.BiWeek1Fri:
      return range(settings, date, 5, rv(week), 1);
    case PeriodType.BiWeek1Sat:
      return range(settings, date, 6, rv(week), 1);
    case PeriodType.BiWeek2:
    case PeriodType.BiWeek2Sun:
      return range(settings, date, 0, rv(week), 2);
    case PeriodType.BiWeek2Mon:
      return range(settings, date, 1, rv(week), 2);
    case PeriodType.BiWeek2Tue:
      return range(settings, date, 2, rv(week), 2);
    case PeriodType.BiWeek2Wed:
      return range(settings, date, 3, rv(week), 2);
    case PeriodType.BiWeek2Thu:
      return range(settings, date, 4, rv(week), 2);
    case PeriodType.BiWeek2Fri:
      return range(settings, date, 5, rv(week), 2);
    case PeriodType.BiWeek2Sat:
      return range(settings, date, 6, rv(week), 2);
    default:
      return formatISO(date);
  }
}
function utcToLocalDate(date) {
  date = date instanceof Date ? date : typeof date === "string" ? new Date(date) : /* @__PURE__ */ new Date();
  const d = new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds());
  d.setUTCFullYear(date.getUTCFullYear());
  return d;
}
function localToUtcDate(date) {
  date = date instanceof Date ? date : typeof date === "string" ? new Date(date) : /* @__PURE__ */ new Date();
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds()));
  return d;
}
function randomDate(from, to) {
  const fromTime = from.getTime();
  const toTime = to.getTime();
  return new Date(fromTime + Math.random() * (toTime - fromTime));
}
var DATE_FORMAT = /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(.\d+|)(Z|(-|\+)\d{2}:\d{2}))?$/;
function isStringDate(value) {
  return DATE_FORMAT.test(value);
}

// node_modules/@layerstack/utils/dist/dom.js
function getScrollParent(node) {
  const isElement2 = node instanceof HTMLElement;
  const overflowX = isElement2 ? (window == null ? void 0 : window.getComputedStyle(node).overflowX) ?? "visible" : "unknown";
  const overflowY = isElement2 ? (window == null ? void 0 : window.getComputedStyle(node).overflowY) ?? "visible" : "unknown";
  const isHorizontalScrollable = !["visible", "hidden"].includes(overflowX) && node.scrollWidth > node.clientWidth;
  const isVerticalScrollable = !["visible", "hidden"].includes(overflowY) && node.scrollHeight > node.clientHeight;
  if (isHorizontalScrollable || isVerticalScrollable) {
    return node;
  } else if (node.parentElement) {
    return getScrollParent(node.parentElement);
  } else {
    return document.body;
  }
}
function scrollIntoView(node) {
  const scrollParent = getScrollParent(node);
  const removeScrollParentOffset = scrollParent != node.offsetParent;
  const nodeOffset = {
    top: node.offsetTop - (removeScrollParentOffset ? (scrollParent == null ? void 0 : scrollParent.offsetTop) ?? 0 : 0),
    left: node.offsetLeft - (removeScrollParentOffset ? (scrollParent == null ? void 0 : scrollParent.offsetLeft) ?? 0 : 0)
  };
  const optionCenter = {
    left: node.clientWidth / 2,
    top: node.clientHeight / 2
  };
  const containerCenter = {
    left: scrollParent.clientWidth / 2,
    top: scrollParent.clientHeight / 2
  };
  scrollParent.scroll({
    top: nodeOffset.top + optionCenter.top - containerCenter.top,
    left: nodeOffset.left + optionCenter.left - containerCenter.left,
    behavior: "smooth"
  });
}
function isVisibleInScrollParent(node) {
  const nodeRect = node.getBoundingClientRect();
  const scrollParent = getScrollParent(node);
  const parentRect = scrollParent.getBoundingClientRect();
  const isVisible = nodeRect.top > parentRect.top && nodeRect.bottom < parentRect.bottom;
  return isVisible;
}
function localPoint(event, node) {
  if (!node) {
    node = event.currentTarget ?? event.target;
  }
  if (!node || !event)
    return { x: 0, y: 0 };
  const coords = getPointFromEvent(event);
  const svg = isSVGElement(node) ? node.ownerSVGElement : node;
  const screenCTM = isSVGGraphicsElement(svg) ? svg.getScreenCTM() : null;
  if (isSVGSVGElement(svg) && screenCTM) {
    let point = svg.createSVGPoint();
    point.x = coords.x;
    point.y = coords.y;
    point = point.matrixTransform(screenCTM.inverse());
    return {
      x: point.x,
      y: point.y
    };
  }
  const rect = node.getBoundingClientRect();
  return {
    x: coords.x - rect.left - node.clientLeft,
    y: coords.y - rect.top - node.clientTop
  };
}
function getPointFromEvent(event) {
  if (!event)
    return { x: 0, y: 0 };
  if (isTouchEvent(event)) {
    return event.changedTouches.length > 0 ? {
      x: event.changedTouches[0].clientX,
      y: event.changedTouches[0].clientY
    } : { x: 0, y: 0 };
  }
  return {
    x: event.clientX,
    y: event.clientY
  };
}

// node_modules/@layerstack/utils/dist/duration.js
var duration_exports = {};
__export(duration_exports, {
  Duration: () => Duration,
  DurationUnits: () => DurationUnits
});
var DurationUnits;
(function(DurationUnits2) {
  DurationUnits2[DurationUnits2["Year"] = 0] = "Year";
  DurationUnits2[DurationUnits2["Day"] = 1] = "Day";
  DurationUnits2[DurationUnits2["Hour"] = 2] = "Hour";
  DurationUnits2[DurationUnits2["Minute"] = 3] = "Minute";
  DurationUnits2[DurationUnits2["Second"] = 4] = "Second";
  DurationUnits2[DurationUnits2["Millisecond"] = 5] = "Millisecond";
})(DurationUnits || (DurationUnits = {}));
var _milliseconds, _seconds, _minutes, _hours, _days, _years;
var Duration = class {
  constructor(options = {}) {
    __privateAdd(this, _milliseconds, 0);
    __privateAdd(this, _seconds, 0);
    __privateAdd(this, _minutes, 0);
    __privateAdd(this, _hours, 0);
    __privateAdd(this, _days, 0);
    __privateAdd(this, _years, 0);
    var _a, _b, _c, _d, _e, _f;
    const startDate = typeof options.start === "string" ? parseISO(options.start) : options.start;
    const endDate = typeof options.end === "string" ? parseISO(options.end) : options.end;
    const differenceInMs = startDate ? Math.abs(Number(endDate || /* @__PURE__ */ new Date()) - Number(startDate)) : void 0;
    if (!Number.isFinite(differenceInMs) && options.duration == null) {
      return;
    }
    __privateSet(this, _milliseconds, ((_a = options.duration) == null ? void 0 : _a.milliseconds) ?? differenceInMs ?? 0);
    __privateSet(this, _seconds, ((_b = options.duration) == null ? void 0 : _b.seconds) ?? 0);
    __privateSet(this, _minutes, ((_c = options.duration) == null ? void 0 : _c.minutes) ?? 0);
    __privateSet(this, _hours, ((_d = options.duration) == null ? void 0 : _d.hours) ?? 0);
    __privateSet(this, _days, ((_e = options.duration) == null ? void 0 : _e.days) ?? 0);
    __privateSet(this, _years, ((_f = options.duration) == null ? void 0 : _f.years) ?? 0);
    if (__privateGet(this, _milliseconds) >= 1e3) {
      const carrySeconds = (__privateGet(this, _milliseconds) - __privateGet(this, _milliseconds) % 1e3) / 1e3;
      __privateSet(this, _seconds, __privateGet(this, _seconds) + carrySeconds);
      __privateSet(this, _milliseconds, __privateGet(this, _milliseconds) - carrySeconds * 1e3);
    }
    if (__privateGet(this, _seconds) >= 60) {
      const carryMinutes = (__privateGet(this, _seconds) - __privateGet(this, _seconds) % 60) / 60;
      __privateSet(this, _minutes, __privateGet(this, _minutes) + carryMinutes);
      __privateSet(this, _seconds, __privateGet(this, _seconds) - carryMinutes * 60);
    }
    if (__privateGet(this, _minutes) >= 60) {
      const carryHours = (__privateGet(this, _minutes) - __privateGet(this, _minutes) % 60) / 60;
      __privateSet(this, _hours, __privateGet(this, _hours) + carryHours);
      __privateSet(this, _minutes, __privateGet(this, _minutes) - carryHours * 60);
    }
    if (__privateGet(this, _hours) >= 24) {
      const carryDays = (__privateGet(this, _hours) - __privateGet(this, _hours) % 24) / 24;
      __privateSet(this, _days, __privateGet(this, _days) + carryDays);
      __privateSet(this, _hours, __privateGet(this, _hours) - carryDays * 24);
    }
    if (__privateGet(this, _days) >= 365) {
      const carryYears = (__privateGet(this, _days) - __privateGet(this, _days) % 365) / 365;
      __privateSet(this, _years, __privateGet(this, _years) + carryYears);
      __privateSet(this, _days, __privateGet(this, _days) - carryYears * 365);
    }
  }
  get years() {
    return __privateGet(this, _years);
  }
  get days() {
    return __privateGet(this, _days);
  }
  get hours() {
    return __privateGet(this, _hours);
  }
  get minutes() {
    return __privateGet(this, _minutes);
  }
  get seconds() {
    return __privateGet(this, _seconds);
  }
  get milliseconds() {
    return __privateGet(this, _milliseconds);
  }
  valueOf() {
    return __privateGet(this, _milliseconds) + __privateGet(this, _seconds) * 1e3 + __privateGet(this, _minutes) * 60 * 1e3 + __privateGet(this, _hours) * 60 * 60 * 1e3 + __privateGet(this, _days) * 24 * 60 * 60 * 1e3 + __privateGet(this, _years) * 365 * 24 * 60 * 60 * 1e3;
  }
  toJSON() {
    return {
      years: __privateGet(this, _years),
      days: __privateGet(this, _days),
      hours: __privateGet(this, _hours),
      minutes: __privateGet(this, _minutes),
      seconds: __privateGet(this, _seconds),
      milliseconds: __privateGet(this, _milliseconds)
    };
  }
  format(options = {}) {
    const { minUnits, totalUnits = 99, variant = "short" } = options;
    var sentenceArr = [];
    var unitNames = variant === "short" ? ["y", "d", "h", "m", "s", "ms"] : ["years", "days", "hours", "minutes", "seconds", "milliseconds"];
    var unitNums = [
      this.years,
      this.days,
      this.hours,
      this.minutes,
      this.seconds,
      this.milliseconds
    ].filter((x, i2) => i2 <= (minUnits ?? 99));
    for (var i in unitNums) {
      if (sentenceArr.length >= totalUnits) {
        break;
      }
      const unitNum = unitNums[i];
      let unitName = unitNames[i];
      if (unitNum !== 0 || sentenceArr.length === 0 && Number(i) === unitNums.length - 1) {
        switch (variant) {
          case "short":
            sentenceArr.push(unitNum + unitName);
            break;
          case "long":
            if (unitNum === 1) {
              unitName = unitName.slice(0, -1);
            }
            sentenceArr.push(unitNum + " " + unitName);
            break;
        }
      }
    }
    const sentence = sentenceArr.join(variant === "long" ? " and " : " ");
    return sentence;
  }
  toString() {
    return this.format();
  }
};
_milliseconds = new WeakMap();
_seconds = new WeakMap();
_minutes = new WeakMap();
_hours = new WeakMap();
_days = new WeakMap();
_years = new WeakMap();

// node_modules/@layerstack/utils/dist/file.js
function saveAs(blob, fileName) {
  var a = document.createElement("a");
  document.body.appendChild(a);
  a.style.display = "none";
  var url2 = window.URL.createObjectURL(blob);
  a.href = url2;
  a.download = fileName;
  a.click();
  window.URL.revokeObjectURL(url2);
  document.body.removeChild(a);
}

// node_modules/@layerstack/utils/dist/number.js
var number_exports = {};
__export(number_exports, {
  clamp: () => clamp,
  decimalCount: () => decimalCount,
  formatNumber: () => formatNumber,
  formatNumberWithLocale: () => formatNumberWithLocale,
  modulo: () => modulo,
  randomInteger: () => randomInteger,
  round: () => round,
  step: () => step
});
function getFormatNumber(settings, style) {
  const { numbers } = settings.formats;
  const styleSettings = style && style != "none" ? numbers[style] : {};
  return {
    ...numbers.defaults,
    ...styleSettings
  };
}
function formatNumber(number, style, options) {
  return formatNumberWithLocale(defaultLocale, number, style, options);
}
function formatNumberWithLocale(settings, number, style, options = {}) {
  if (number == null) {
    return "";
  }
  if (style === "none") {
    return `${number}`;
  }
  if (style == null) {
    style = Number.isInteger(number) ? "integer" : "decimal";
  }
  const defaults = getFormatNumber(settings, style);
  const formatter = Intl.NumberFormat(settings.locale, {
    // Let's always starts with all defaults
    ...defaults,
    ...style !== "default" && {
      style
    },
    // Let's shorten min / max with fractionDigits
    ...{
      minimumFractionDigits: options.fractionDigits ?? defaults.fractionDigits,
      maximumFractionDigits: options.fractionDigits ?? defaults.fractionDigits
    },
    // now we bring in user specified options
    ...omitNil(options),
    ...style === "currencyRound" && {
      style: "currency",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    },
    // Let's overwrite for style=percentRound
    ...style === "percentRound" && {
      style: "percent",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    },
    // Let's overwrite for style=metric
    ...style === "metric" && {
      style: "decimal",
      notation: "compact",
      minimumFractionDigits: 0
    },
    // Let's overwrite for style=integer
    ...style === "integer" && {
      style: "decimal",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }
  });
  const value = formatter.format(number);
  let suffix = options.suffix ?? "";
  if (suffix && Math.abs(number) >= 2 && options.suffixExtraIfMany !== "") {
    suffix += options.suffixExtraIfMany ?? "s";
  }
  return `${value}${suffix}`;
}
function clamp(value, min2, max2) {
  return value < min2 ? min2 : value > max2 ? max2 : value;
}
function decimalCount(value) {
  var _a;
  return ((_a = value == null ? void 0 : value.toString().split(".")[1]) == null ? void 0 : _a.length) ?? 0;
}
function round(value, decimals) {
  return Number(value.toFixed(decimals));
}
function step(value, step2) {
  return round(value + step2, decimalCount(step2));
}
function randomInteger(min2, max2) {
  return Math.floor(Math.random() * (max2 - min2 + 1)) + min2;
}
function modulo(n, m) {
  return (n % m + m) % m;
}

// node_modules/@layerstack/utils/dist/format.js
function format(value, format2, options) {
  return formatWithLocale(defaultLocale, value, format2, options);
}
function formatWithLocale(settings, value, format2, options) {
  if (typeof format2 === "function") {
    return format2(value);
  } else if (value instanceof Date || isStringDate(value) || format2 && format2 in PeriodType) {
    return formatDateWithLocale(settings, value, format2 ?? PeriodType.Day, options);
  } else if (typeof value === "number") {
    return formatNumberWithLocale(settings, value, format2, options);
  } else if (typeof value === "string") {
    return value;
  } else if (value == null) {
    return "";
  } else {
    return `${value}`;
  }
}

// node_modules/@layerstack/utils/dist/json.js
function stringify(value) {
  return JSON.stringify(value, replacer);
}
function replacer(key, value) {
  if (value instanceof Map) {
    return {
      _type: "Map",
      value: Array.from(value.entries())
    };
  } else if (value instanceof Set) {
    return {
      _type: "Set",
      value: Array.from(value.values())
    };
  } else {
    return value;
  }
}
function parse(value) {
  let result;
  try {
    result = JSON.parse(value, reviver);
  } catch (e) {
    result = value;
  }
  return result;
}
function reviver(key, value) {
  if (typeof value === "string" && isStringDate(value)) {
    return parseISO(value);
  } else if (typeof value === "object" && value !== null) {
    if (value._type === "Map") {
      return new Map(value.value);
    } else if (value._type === "Set") {
      return new Set(value.value);
    }
  }
  return value;
}

// node_modules/@layerstack/utils/dist/env.js
var env_exports = {};
__export(env_exports, {
  browser: () => browser,
  ssr: () => ssr
});
var browser = typeof window !== "undefined";
var ssr = typeof window === "undefined";

// node_modules/@layerstack/utils/dist/logger.js
var logLevels = ["TRACE", "DEBUG", "INFO", "WARN", "ERROR"];
var Logger = class {
  constructor(name) {
    __publicField(this, "name");
    this.name = name;
  }
  trace(...message) {
    this.log("TRACE", ...message);
  }
  debug(...message) {
    this.log("DEBUG", ...message);
  }
  info(...message) {
    this.log("INFO", ...message);
  }
  warn(...message) {
    this.log("WARN", ...message);
  }
  error(...message) {
    this.log("ERROR", ...message);
  }
  log(level, ...message) {
    var _a;
    const enabledLoggers = browser ? ((_a = localStorage.getItem("logger")) == null ? void 0 : _a.split(",").map((x) => x.split(":"))) ?? [] : [];
    const enabledLogger = enabledLoggers.find((x) => x[0] === this.name);
    const shouldLog = enabledLogger != null && logLevels.indexOf(level) >= logLevels.indexOf(enabledLogger[1] ?? "DEBUG");
    if (shouldLog) {
      switch (level) {
        case "TRACE":
          console.trace(`%c${this.name} %c${level}`, "color: hsl(200deg, 10%, 50%)", "color: hsl(200deg, 40%, 50%)", ...message);
          break;
        case "DEBUG":
          console.log(`%c${this.name} %c${level}`, "color: hsl(200deg, 10%, 50%)", "color: hsl(200deg, 40%, 50%)", ...message);
          break;
        case "INFO":
          console.log(`%c${this.name} %c${level}`, "color: hsl(200deg, 10%, 50%)", "color: hsl(60deg, 100%, 50%)", ...message);
          break;
        case "WARN":
          console.warn(`%c${this.name} %c${level}`, "color: hsl(200deg, 10%, 50%)", "color: hsl(30deg, 100%, 50%)", ...message);
          break;
        case "ERROR":
          console.warn(`%c${this.name} %c${level}`, "color: hsl(200deg, 10%, 50%)", "color: hsl(0deg, 100%, 50%)", ...message);
          break;
      }
    }
  }
};

// node_modules/@layerstack/utils/dist/promise.js
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// node_modules/@layerstack/utils/dist/sort.js
function sortFunc(value, direction = "asc") {
  const sortDirection = direction === "asc" ? 1 : -1;
  return (a, b) => {
    const valueFn = propAccessor(value);
    const aValue = valueFn(a);
    const bValue = valueFn(b);
    if (aValue == null || bValue == null) {
      if (aValue == null && bValue != null) {
        return -sortDirection;
      } else if (aValue != null && bValue == null) {
        return sortDirection;
      } else {
        return 0;
      }
    }
    return aValue < bValue ? -sortDirection : aValue > bValue ? sortDirection : 0;
  };
}
function compoundSortFunc(...sortFns) {
  return (a, b) => {
    for (let i = 0; i < sortFns.length; i++) {
      let result = sortFns[i](a, b);
      if (result != 0) {
        return result;
      }
    }
    return 0;
  };
}
function sort(data, value, direction = "asc") {
  return [...data].sort(sortFunc(value, direction));
}
function nestedSort(data, sortFunc2, depth = 0) {
  data.sort((a, b) => sortFunc2(a, b, depth));
  data.forEach((d) => {
    if (d.values) {
      nestedSort(d.values, sortFunc2, depth + 1);
    }
  });
  return data;
}

// node_modules/@layerstack/utils/dist/string.js
function isUpperCase(str) {
  return /^[A-Z]*$/.test(str);
}
function toTitleCase(str, ignore = ["a", "an", "is", "the"]) {
  return str.toLowerCase().split(" ").map((word, index) => {
    if (index > 0 && ignore.includes(word)) {
      return word;
    } else {
      return word.charAt(0).toUpperCase() + word.slice(1);
    }
  }).join(" ");
}
var idMap = /* @__PURE__ */ new Map();
function uniqueId(prefix = "") {
  let id = (idMap.get(prefix) ?? 0) + 1;
  idMap.set(prefix, id);
  return prefix + id;
}
function truncate(text, totalChars, endChars = 0) {
  endChars = Math.min(endChars, totalChars);
  const start = text.slice(0, totalChars - endChars);
  const end = endChars > 0 ? text.slice(-endChars) : "";
  if (start.length + end.length < text.length) {
    return start + "…" + end;
  } else {
    return text;
  }
}
function romanize(value) {
  const lookup = {
    M: 1e3,
    CM: 900,
    D: 500,
    CD: 400,
    C: 100,
    XC: 90,
    L: 50,
    XL: 40,
    X: 10,
    IX: 9,
    V: 5,
    IV: 4,
    I: 1
  };
  let result = "";
  for (let [numeral, numeralValue] of entries(lookup)) {
    while (value >= numeralValue) {
      result += numeral;
      value -= numeralValue;
    }
  }
  return result;
}

// node_modules/@layerstack/utils/dist/dateRange.js
var dateRange_exports = {};
__export(dateRange_exports, {
  getDateRangePresets: () => getDateRangePresets,
  getPeriodComparisonOffset: () => getPeriodComparisonOffset,
  getPreviousYearPeriodOffset: () => getPreviousYearPeriodOffset
});
function formatMsg(settings, type, lastX) {
  return lastX === 0 ? settings.dictionary.Date[type].Current : lastX === 1 ? settings.dictionary.Date[type].Last : settings.dictionary.Date[type].LastX.replace("{0}", lastX.toString());
}
function getDateRangePresets(settings, periodType) {
  let now = /* @__PURE__ */ new Date();
  const today = startOfDay(now);
  if (settings) {
    periodType = updatePeriodTypeWithWeekStartsOn(settings.formats.dates.weekStartsOn, periodType) ?? periodType;
  }
  const { start, end, add } = getDateFuncsByPeriodType(settings, periodType);
  switch (periodType) {
    case PeriodType.Day: {
      const last = start(add(today, -1));
      return [0, 1, 3, 7, 14, 30].map((lastX) => {
        return {
          label: formatMsg(settings, "PeriodDay", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    case PeriodType.WeekSun:
    case PeriodType.WeekMon:
    case PeriodType.WeekTue:
    case PeriodType.WeekWed:
    case PeriodType.WeekThu:
    case PeriodType.WeekFri:
    case PeriodType.WeekSat: {
      const last = start(add(today, -1));
      return [0, 1, 2, 4, 6].map((lastX) => {
        return {
          label: formatMsg(settings, "PeriodWeek", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    case PeriodType.BiWeek1Sun:
    case PeriodType.BiWeek1Mon:
    case PeriodType.BiWeek1Tue:
    case PeriodType.BiWeek1Wed:
    case PeriodType.BiWeek1Thu:
    case PeriodType.BiWeek1Fri:
    case PeriodType.BiWeek1Sat:
    case PeriodType.BiWeek2Sun:
    case PeriodType.BiWeek2Mon:
    case PeriodType.BiWeek2Tue:
    case PeriodType.BiWeek2Wed:
    case PeriodType.BiWeek2Thu:
    case PeriodType.BiWeek2Fri:
    case PeriodType.BiWeek2Sat: {
      const last = start(add(today, -1));
      return [0, 1, 2, 4, 6].map((lastX) => {
        return {
          label: formatMsg(settings, "PeriodBiWeek", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    case PeriodType.Month: {
      const last = start(add(today, -1));
      return [0, 1, 2, 3, 6, 12].map((lastX) => {
        return {
          label: formatMsg(settings, "PeriodMonth", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    case PeriodType.Quarter: {
      const last = start(add(today, -1));
      return [0, 1, -1, 4, 12].map((lastX) => {
        if (lastX === -1) {
          return {
            label: settings.dictionary.Date.PeriodQuarterSameLastyear,
            value: {
              from: start(add(today, -4)),
              to: end(add(today, -4)),
              periodType
            }
          };
        }
        return {
          label: formatMsg(settings, "PeriodQuarter", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    case PeriodType.CalendarYear: {
      const last = start(add(today, -1));
      return [0, 1, 3, 5].map((lastX) => {
        return {
          label: formatMsg(settings, "PeriodYear", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    case PeriodType.FiscalYearOctober: {
      const last = start(add(today, -1));
      return [0, 1, 3, 5].map((lastX) => {
        return {
          label: formatMsg(settings, "PeriodFiscalYear", lastX),
          value: {
            from: add(last, -lastX + 1),
            to: lastX === 0 ? end(today) : end(last),
            periodType
          }
        };
      });
    }
    default: {
      return [];
    }
  }
}
function getPreviousYearPeriodOffset(periodType, options) {
  switch (periodType) {
    case PeriodType.Day:
      const adjustForLeapYear = (options == null ? void 0 : options.referenceDate) ? isLeapYear(options == null ? void 0 : options.referenceDate) && isAfter(options == null ? void 0 : options.referenceDate, new Date(
        options == null ? void 0 : options.referenceDate.getFullYear(),
        /*Feb*/
        1,
        28
      )) || isLeapYear(subYears(options == null ? void 0 : options.referenceDate, 1)) && isBefore(options == null ? void 0 : options.referenceDate, new Date(
        options == null ? void 0 : options.referenceDate.getFullYear(),
        /*Feb*/
        1,
        29
      )) : false;
      return (options == null ? void 0 : options.alignDayOfWeek) ? -364 : adjustForLeapYear ? -366 : -365;
    case PeriodType.WeekSun:
    case PeriodType.WeekMon:
    case PeriodType.WeekTue:
    case PeriodType.WeekWed:
    case PeriodType.WeekThu:
    case PeriodType.WeekFri:
    case PeriodType.WeekSat:
      return -52;
    case PeriodType.BiWeek1Sun:
    case PeriodType.BiWeek1Mon:
    case PeriodType.BiWeek1Tue:
    case PeriodType.BiWeek1Wed:
    case PeriodType.BiWeek1Thu:
    case PeriodType.BiWeek1Fri:
    case PeriodType.BiWeek1Sat:
    case PeriodType.BiWeek2Sun:
    case PeriodType.BiWeek2Mon:
    case PeriodType.BiWeek2Tue:
    case PeriodType.BiWeek2Wed:
    case PeriodType.BiWeek2Thu:
    case PeriodType.BiWeek2Fri:
    case PeriodType.BiWeek2Sat:
      return -26;
    case PeriodType.Month:
      return -12;
    case PeriodType.Quarter:
      return -4;
    case PeriodType.CalendarYear:
    case PeriodType.FiscalYearOctober:
      return -1;
  }
}
function getPeriodComparisonOffset(settings, view, period) {
  if (period == null || period.from == null || period.to == null || period.periodType == null) {
    throw new Error("Period must be defined to calculate offset");
  }
  switch (view) {
    case "prevPeriod":
      const dateFuncs = getDateFuncsByPeriodType(settings, period.periodType);
      return dateFuncs.difference(period.from, period.to) - 1;
    case "prevYear":
      return getPreviousYearPeriodOffset(period.periodType, {
        referenceDate: period.from
      });
    case "fiftyTwoWeeksAgo":
      return getPreviousYearPeriodOffset(period.periodType, {
        alignDayOfWeek: true
      });
    default:
      throw new Error("Unhandled period offset: " + view);
  }
}

// node_modules/@layerstack/utils/dist/map.js
var map_exports = {};
__export(map_exports, {
  get: () => get
});
function get(map, path) {
  let key = void 0;
  let value = map;
  const currentPath = [...path];
  while (key = currentPath.shift()) {
    if (value instanceof Map && value.has(key)) {
      value = value.get(key);
    } else {
      return void 0;
    }
  }
  return value;
}

// node_modules/@layerstack/utils/dist/rollup.js
var rollup_exports = {};
__export(rollup_exports, {
  default: () => rollup_default
});
function rollup_default(data, reduce, keys2 = [], emptyKey = "Unknown") {
  const keyFuncs = keys2.map((key) => {
    if (isFunction_default(key)) {
      return key;
    } else if (typeof key === "string") {
      return (d) => get_default(d, key) || emptyKey;
    } else {
      return () => "Overall";
    }
  });
  return rollup(data, reduce, ...keyFuncs);
}

// node_modules/@layerstack/utils/dist/routing.js
var routing_exports = {};
__export(routing_exports, {
  isActive: () => isActive,
  url: () => url
});
function url(currentUrl, path) {
  if (path == null) {
    return path;
  } else if (path.match(/^\.\.?\//)) {
    let [, breadcrumbs, relativePath] = path.match(/^([\.\/]+)(.*)/);
    let dir = currentUrl.pathname.replace(/\/$/, "");
    const traverse = breadcrumbs.match(/\.\.\//g) || [];
    traverse.forEach(() => dir = dir.replace(/\/[^\/]+\/?$/, ""));
    path = `${dir}/${relativePath}`.replace(/\/$/, "");
    path = path || "/";
  } else if (path.match(/^\//)) {
    return path;
  } else {
    return path;
  }
  return path;
}
function isActive(currentUrl, path) {
  if (path === "/") {
    return currentUrl.pathname === path;
  } else {
    return currentUrl.pathname.match(path + "($|\\/)") != null;
  }
}

// node_modules/@layerstack/utils/dist/serialize.js
var serialize_exports = {};
__export(serialize_exports, {
  decodeArray: () => decodeArray,
  decodeBoolean: () => decodeBoolean,
  decodeDate: () => decodeDate,
  decodeDateTime: () => decodeDateTime,
  decodeDelimitedArray: () => decodeDelimitedArray,
  decodeDelimitedNumericArray: () => decodeDelimitedNumericArray,
  decodeEnum: () => decodeEnum,
  decodeJson: () => decodeJson,
  decodeNumber: () => decodeNumber,
  decodeNumericArray: () => decodeNumericArray,
  decodeNumericObject: () => decodeNumericObject,
  decodeObject: () => decodeObject,
  decodeString: () => decodeString,
  encodeArray: () => encodeArray,
  encodeBoolean: () => encodeBoolean,
  encodeDate: () => encodeDate,
  encodeDateTime: () => encodeDateTime,
  encodeDelimitedArray: () => encodeDelimitedArray,
  encodeDelimitedNumericArray: () => encodeDelimitedNumericArray,
  encodeJson: () => encodeJson,
  encodeNumber: () => encodeNumber,
  encodeNumericArray: () => encodeNumericArray,
  encodeNumericObject: () => encodeNumericObject,
  encodeObject: () => encodeObject,
  encodeString: () => encodeString
});
function getEncodedValue(input, allowEmptyString) {
  if (input == null) {
    return input;
  }
  if (input.length === 0 && (!allowEmptyString || allowEmptyString && input !== "")) {
    return null;
  }
  const str = input instanceof Array ? input[0] : input;
  if (str == null) {
    return str;
  }
  if (!allowEmptyString && str === "") {
    return null;
  }
  return str;
}
function getEncodedValueArray(input) {
  if (input == null) {
    return input;
  }
  return input instanceof Array ? input : input === "" ? [] : [input];
}
function encodeDate(date) {
  if (date == null) {
    return date;
  }
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${year}-${month < 10 ? `0${month}` : month}-${day < 10 ? `0${day}` : day}`;
}
function decodeDate(input) {
  const dateString = getEncodedValue(input);
  if (dateString == null)
    return dateString;
  const parts = dateString.split("-");
  if (parts[1] != null) {
    parts[1] -= 1;
  } else {
    parts[1] = 0;
    parts[2] = 1;
  }
  const decoded = new Date(...parts);
  if (isNaN(decoded.getTime())) {
    return null;
  }
  return decoded;
}
function encodeDateTime(date) {
  if (date == null) {
    return date;
  }
  return date.toISOString();
}
function decodeDateTime(input) {
  const dateString = getEncodedValue(input);
  if (dateString == null)
    return dateString;
  const decoded = new Date(dateString);
  if (isNaN(decoded.getTime())) {
    return null;
  }
  return decoded;
}
function encodeBoolean(bool) {
  if (bool == null) {
    return bool;
  }
  return bool ? "1" : "0";
}
function decodeBoolean(input) {
  const boolStr = getEncodedValue(input);
  if (boolStr == null)
    return boolStr;
  if (boolStr === "1") {
    return true;
  } else if (boolStr === "0") {
    return false;
  }
  return null;
}
function encodeNumber(num) {
  if (num == null) {
    return num;
  }
  return String(num);
}
function decodeNumber(input) {
  const numStr = getEncodedValue(input);
  if (numStr == null)
    return numStr;
  if (numStr === "")
    return null;
  const result = +numStr;
  return result;
}
function encodeString(str) {
  if (str == null) {
    return str;
  }
  return String(str);
}
function decodeString(input) {
  const str = getEncodedValue(input, true);
  if (str == null)
    return str;
  return String(str);
}
function decodeEnum(input, enumValues) {
  const str = decodeString(input);
  if (str == null)
    return str;
  return enumValues.includes(str) ? str : void 0;
}
function encodeJson(any) {
  if (any == null) {
    return any;
  }
  return stringify(any);
}
function decodeJson(input) {
  const jsonStr = getEncodedValue(input);
  if (jsonStr == null)
    return jsonStr;
  let result = null;
  try {
    result = parse(jsonStr);
  } catch (e) {
  }
  return result;
}
function encodeArray(array) {
  if (array == null) {
    return array;
  }
  return array;
}
function decodeArray(input) {
  const arr = getEncodedValueArray(input);
  if (arr == null)
    return arr;
  return arr;
}
function encodeNumericArray(array) {
  if (array == null) {
    return array;
  }
  return array.map(String);
}
function decodeNumericArray(input) {
  const arr = decodeArray(input);
  if (arr == null)
    return arr;
  return arr.map((d) => d === "" || d == null ? null : +d);
}
function encodeDelimitedArray(array, entrySeparator = "_") {
  if (array == null) {
    return array;
  }
  return array.join(entrySeparator);
}
function decodeDelimitedArray(input, entrySeparator = "_") {
  const arrayStr = getEncodedValue(input, true);
  if (arrayStr == null)
    return arrayStr;
  if (arrayStr === "")
    return [];
  return arrayStr.split(entrySeparator);
}
var encodeDelimitedNumericArray = encodeDelimitedArray;
function decodeDelimitedNumericArray(arrayStr, entrySeparator = "_") {
  const decoded = decodeDelimitedArray(arrayStr, entrySeparator);
  if (decoded == null)
    return decoded;
  return decoded.map((d) => d === "" || d == null ? null : +d);
}
function encodeObject(obj, keyValSeparator = "-", entrySeparator = "_") {
  if (obj == null)
    return obj;
  if (isEmptyObject(obj))
    return "";
  return keys(obj).map((key) => {
    const value = encodeJson(obj[key]);
    return `${key}${keyValSeparator}${value}`;
  }).join(entrySeparator);
}
function decodeObject(input, keyValSeparator = "-", entrySeparator = "_") {
  const objStr = getEncodedValue(input, true);
  if (objStr == null)
    return objStr;
  if (objStr === "")
    return {};
  const obj = {};
  const keyValSeparatorRegExp = new RegExp(`${keyValSeparator}(.*)`);
  objStr.split(entrySeparator).forEach((entryStr) => {
    const [key, value] = entryStr.split(keyValSeparatorRegExp);
    obj[key] = decodeJson(value);
  });
  return obj;
}
var encodeNumericObject = encodeObject;
function decodeNumericObject(input, keyValSeparator = "-", entrySeparator = "_") {
  const decoded = decodeObject(input, keyValSeparator, entrySeparator);
  if (decoded == null)
    return decoded;
  const decodedNumberObj = {};
  for (const key of keys(decoded)) {
    decodedNumberObj[key] = decodeNumber(decoded[key]);
  }
  return decodedNumberObj;
}

// node_modules/@layerstack/utils/dist/styles.js
var styles_exports = {};
__export(styles_exports, {
  objectToString: () => objectToString
});
function objectToString(styleObj) {
  return entries(styleObj).map(([key, value]) => {
    if (value) {
      const propertyName = key.replace(/([A-Z])/g, "-$1").toLowerCase();
      return `${propertyName}: ${value};`;
    } else {
      return null;
    }
  }).filter((x) => x).join(" ");
}

export {
  flatten,
  unique,
  greatestAbs,
  array_exports,
  hasKeyOf,
  hasProperty,
  nameof,
  isNumber,
  notNull,
  isElement,
  isSVGElement,
  isSVGSVGElement,
  isSVGGraphicsElement,
  isTouchEvent,
  isEvent,
  PeriodType,
  DayOfWeek,
  DateToken,
  createLocaleSettings,
  defaultLocale,
  getDateFuncsByPeriodType,
  formatDate,
  date_exports,
  getScrollParent,
  scrollIntoView,
  isVisibleInScrollParent,
  localPoint,
  DurationUnits,
  Duration,
  duration_exports,
  saveAs,
  clamp,
  round,
  randomInteger,
  number_exports,
  format,
  formatWithLocale,
  stringify,
  replacer,
  parse,
  reviver,
  env_exports,
  Logger,
  delay,
  sortFunc,
  compoundSortFunc,
  sort,
  nestedSort,
  isUpperCase,
  toTitleCase,
  uniqueId,
  truncate,
  romanize,
  dateRange_exports,
  map_exports,
  rollup_exports,
  routing_exports,
  serialize_exports,
  styles_exports
};
//# sourceMappingURL=chunk-UDK5QTU5.js.map
