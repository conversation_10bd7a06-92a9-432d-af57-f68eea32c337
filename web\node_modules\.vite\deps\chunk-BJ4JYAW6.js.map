{"version": 3, "sources": ["../../d3-hierarchy/src/cluster.js", "../../d3-hierarchy/src/hierarchy/count.js", "../../d3-hierarchy/src/hierarchy/each.js", "../../d3-hierarchy/src/hierarchy/eachBefore.js", "../../d3-hierarchy/src/hierarchy/eachAfter.js", "../../d3-hierarchy/src/hierarchy/find.js", "../../d3-hierarchy/src/hierarchy/sum.js", "../../d3-hierarchy/src/hierarchy/sort.js", "../../d3-hierarchy/src/hierarchy/path.js", "../../d3-hierarchy/src/hierarchy/ancestors.js", "../../d3-hierarchy/src/hierarchy/descendants.js", "../../d3-hierarchy/src/hierarchy/leaves.js", "../../d3-hierarchy/src/hierarchy/links.js", "../../d3-hierarchy/src/hierarchy/iterator.js", "../../d3-hierarchy/src/hierarchy/index.js", "../../d3-hierarchy/src/accessors.js", "../../d3-hierarchy/src/constant.js", "../../d3-hierarchy/src/lcg.js", "../../d3-hierarchy/src/array.js", "../../d3-hierarchy/src/pack/enclose.js", "../../d3-hierarchy/src/pack/siblings.js", "../../d3-hierarchy/src/pack/index.js", "../../d3-hierarchy/src/treemap/round.js", "../../d3-hierarchy/src/treemap/dice.js", "../../d3-hierarchy/src/partition.js", "../../d3-hierarchy/src/stratify.js", "../../d3-hierarchy/src/tree.js", "../../d3-hierarchy/src/treemap/slice.js", "../../d3-hierarchy/src/treemap/squarify.js", "../../d3-hierarchy/src/treemap/index.js", "../../d3-hierarchy/src/treemap/binary.js", "../../d3-hierarchy/src/treemap/sliceDice.js", "../../d3-hierarchy/src/treemap/resquarify.js"], "sourcesContent": ["function defaultSeparation(a, b) {\n  return a.parent === b.parent ? 1 : 2;\n}\n\nfunction meanX(children) {\n  return children.reduce(meanXReduce, 0) / children.length;\n}\n\nfunction meanXReduce(x, c) {\n  return x + c.x;\n}\n\nfunction maxY(children) {\n  return 1 + children.reduce(maxYReduce, 0);\n}\n\nfunction maxYReduce(y, c) {\n  return Math.max(y, c.y);\n}\n\nfunction leafLeft(node) {\n  var children;\n  while (children = node.children) node = children[0];\n  return node;\n}\n\nfunction leafRight(node) {\n  var children;\n  while (children = node.children) node = children[children.length - 1];\n  return node;\n}\n\nexport default function() {\n  var separation = defaultSeparation,\n      dx = 1,\n      dy = 1,\n      nodeSize = false;\n\n  function cluster(root) {\n    var previousNode,\n        x = 0;\n\n    // First walk, computing the initial x & y values.\n    root.eachAfter(function(node) {\n      var children = node.children;\n      if (children) {\n        node.x = meanX(children);\n        node.y = maxY(children);\n      } else {\n        node.x = previousNode ? x += separation(node, previousNode) : 0;\n        node.y = 0;\n        previousNode = node;\n      }\n    });\n\n    var left = leafLeft(root),\n        right = leafRight(root),\n        x0 = left.x - separation(left, right) / 2,\n        x1 = right.x + separation(right, left) / 2;\n\n    // Second walk, normalizing x & y to the desired size.\n    return root.eachAfter(nodeSize ? function(node) {\n      node.x = (node.x - root.x) * dx;\n      node.y = (root.y - node.y) * dy;\n    } : function(node) {\n      node.x = (node.x - x0) / (x1 - x0) * dx;\n      node.y = (1 - (root.y ? node.y / root.y : 1)) * dy;\n    });\n  }\n\n  cluster.separation = function(x) {\n    return arguments.length ? (separation = x, cluster) : separation;\n  };\n\n  cluster.size = function(x) {\n    return arguments.length ? (nodeSize = false, dx = +x[0], dy = +x[1], cluster) : (nodeSize ? null : [dx, dy]);\n  };\n\n  cluster.nodeSize = function(x) {\n    return arguments.length ? (nodeSize = true, dx = +x[0], dy = +x[1], cluster) : (nodeSize ? [dx, dy] : null);\n  };\n\n  return cluster;\n}\n", "function count(node) {\n  var sum = 0,\n      children = node.children,\n      i = children && children.length;\n  if (!i) sum = 1;\n  else while (--i >= 0) sum += children[i].value;\n  node.value = sum;\n}\n\nexport default function() {\n  return this.eachAfter(count);\n}\n", "export default function(callback, that) {\n  let index = -1;\n  for (const node of this) {\n    callback.call(that, node, ++index, this);\n  }\n  return this;\n}\n", "export default function(callback, that) {\n  var node = this, nodes = [node], children, i, index = -1;\n  while (node = nodes.pop()) {\n    callback.call(that, node, ++index, this);\n    if (children = node.children) {\n      for (i = children.length - 1; i >= 0; --i) {\n        nodes.push(children[i]);\n      }\n    }\n  }\n  return this;\n}\n", "export default function(callback, that) {\n  var node = this, nodes = [node], next = [], children, i, n, index = -1;\n  while (node = nodes.pop()) {\n    next.push(node);\n    if (children = node.children) {\n      for (i = 0, n = children.length; i < n; ++i) {\n        nodes.push(children[i]);\n      }\n    }\n  }\n  while (node = next.pop()) {\n    callback.call(that, node, ++index, this);\n  }\n  return this;\n}\n", "export default function(callback, that) {\n  let index = -1;\n  for (const node of this) {\n    if (callback.call(that, node, ++index, this)) {\n      return node;\n    }\n  }\n}\n", "export default function(value) {\n  return this.eachAfter(function(node) {\n    var sum = +value(node.data) || 0,\n        children = node.children,\n        i = children && children.length;\n    while (--i >= 0) sum += children[i].value;\n    node.value = sum;\n  });\n}\n", "export default function(compare) {\n  return this.eachBefore(function(node) {\n    if (node.children) {\n      node.children.sort(compare);\n    }\n  });\n}\n", "export default function(end) {\n  var start = this,\n      ancestor = leastCommonAncestor(start, end),\n      nodes = [start];\n  while (start !== ancestor) {\n    start = start.parent;\n    nodes.push(start);\n  }\n  var k = nodes.length;\n  while (end !== ancestor) {\n    nodes.splice(k, 0, end);\n    end = end.parent;\n  }\n  return nodes;\n}\n\nfunction leastCommonAncestor(a, b) {\n  if (a === b) return a;\n  var aNodes = a.ancestors(),\n      bNodes = b.ancestors(),\n      c = null;\n  a = aNodes.pop();\n  b = bNodes.pop();\n  while (a === b) {\n    c = a;\n    a = aNodes.pop();\n    b = bNodes.pop();\n  }\n  return c;\n}\n", "export default function() {\n  var node = this, nodes = [node];\n  while (node = node.parent) {\n    nodes.push(node);\n  }\n  return nodes;\n}\n", "export default function() {\n  return Array.from(this);\n}\n", "export default function() {\n  var leaves = [];\n  this.eachBefore(function(node) {\n    if (!node.children) {\n      leaves.push(node);\n    }\n  });\n  return leaves;\n}\n", "export default function() {\n  var root = this, links = [];\n  root.each(function(node) {\n    if (node !== root) { // Don’t include the root’s parent, if any.\n      links.push({source: node.parent, target: node});\n    }\n  });\n  return links;\n}\n", "export default function*() {\n  var node = this, current, next = [node], children, i, n;\n  do {\n    current = next.reverse(), next = [];\n    while (node = current.pop()) {\n      yield node;\n      if (children = node.children) {\n        for (i = 0, n = children.length; i < n; ++i) {\n          next.push(children[i]);\n        }\n      }\n    }\n  } while (next.length);\n}\n", "import node_count from \"./count.js\";\nimport node_each from \"./each.js\";\nimport node_eachBefore from \"./eachBefore.js\";\nimport node_eachAfter from \"./eachAfter.js\";\nimport node_find from \"./find.js\";\nimport node_sum from \"./sum.js\";\nimport node_sort from \"./sort.js\";\nimport node_path from \"./path.js\";\nimport node_ancestors from \"./ancestors.js\";\nimport node_descendants from \"./descendants.js\";\nimport node_leaves from \"./leaves.js\";\nimport node_links from \"./links.js\";\nimport node_iterator from \"./iterator.js\";\n\nexport default function hierarchy(data, children) {\n  if (data instanceof Map) {\n    data = [undefined, data];\n    if (children === undefined) children = mapChildren;\n  } else if (children === undefined) {\n    children = objectChildren;\n  }\n\n  var root = new Node(data),\n      node,\n      nodes = [root],\n      child,\n      childs,\n      i,\n      n;\n\n  while (node = nodes.pop()) {\n    if ((childs = children(node.data)) && (n = (childs = Array.from(childs)).length)) {\n      node.children = childs;\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = childs[i] = new Node(childs[i]));\n        child.parent = node;\n        child.depth = node.depth + 1;\n      }\n    }\n  }\n\n  return root.eachBefore(computeHeight);\n}\n\nfunction node_copy() {\n  return hierarchy(this).eachBefore(copyData);\n}\n\nfunction objectChildren(d) {\n  return d.children;\n}\n\nfunction mapChildren(d) {\n  return Array.isArray(d) ? d[1] : null;\n}\n\nfunction copyData(node) {\n  if (node.data.value !== undefined) node.value = node.data.value;\n  node.data = node.data.data;\n}\n\nexport function computeHeight(node) {\n  var height = 0;\n  do node.height = height;\n  while ((node = node.parent) && (node.height < ++height));\n}\n\nexport function Node(data) {\n  this.data = data;\n  this.depth =\n  this.height = 0;\n  this.parent = null;\n}\n\nNode.prototype = hierarchy.prototype = {\n  constructor: Node,\n  count: node_count,\n  each: node_each,\n  eachAfter: node_eachAfter,\n  eachBefore: node_eachBefore,\n  find: node_find,\n  sum: node_sum,\n  sort: node_sort,\n  path: node_path,\n  ancestors: node_ancestors,\n  descendants: node_descendants,\n  leaves: node_leaves,\n  links: node_links,\n  copy: node_copy,\n  [Symbol.iterator]: node_iterator\n};\n", "export function optional(f) {\n  return f == null ? null : required(f);\n}\n\nexport function required(f) {\n  if (typeof f !== \"function\") throw new Error;\n  return f;\n}\n", "export function constantZero() {\n  return 0;\n}\n\nexport default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "// https://en.wikipedia.org/wiki/Linear_congruential_generator#Parameters_in_common_use\nconst a = 1664525;\nconst c = 1013904223;\nconst m = 4294967296; // 2^32\n\nexport default function() {\n  let s = 1;\n  return () => (s = (a * s + c) % m) / m;\n}\n", "export default function(x) {\n  return typeof x === \"object\" && \"length\" in x\n    ? x // Array, TypedArray, NodeList, array-like\n    : Array.from(x); // Map, Set, iterable, string, or anything else\n}\n\nexport function shuffle(array, random) {\n  let m = array.length,\n      t,\n      i;\n\n  while (m) {\n    i = random() * m-- | 0;\n    t = array[m];\n    array[m] = array[i];\n    array[i] = t;\n  }\n\n  return array;\n}\n", "import {shuffle} from \"../array.js\";\nimport lcg from \"../lcg.js\";\n\nexport default function(circles) {\n  return packEncloseRandom(circles, lcg());\n}\n\nexport function packEncloseRandom(circles, random) {\n  var i = 0, n = (circles = shuffle(Array.from(circles), random)).length, B = [], p, e;\n\n  while (i < n) {\n    p = circles[i];\n    if (e && enclosesWeak(e, p)) ++i;\n    else e = encloseBasis(B = extendBasis(B, p)), i = 0;\n  }\n\n  return e;\n}\n\nfunction extendBasis(B, p) {\n  var i, j;\n\n  if (enclosesWeakAll(p, B)) return [p];\n\n  // If we get here then B must have at least one element.\n  for (i = 0; i < B.length; ++i) {\n    if (enclosesNot(p, B[i])\n        && enclosesWeakAll(encloseBasis2(B[i], p), B)) {\n      return [B[i], p];\n    }\n  }\n\n  // If we get here then B must have at least two elements.\n  for (i = 0; i < B.length - 1; ++i) {\n    for (j = i + 1; j < B.length; ++j) {\n      if (enclosesNot(encloseBasis2(B[i], B[j]), p)\n          && enclosesNot(encloseBasis2(B[i], p), B[j])\n          && enclosesNot(encloseBasis2(B[j], p), B[i])\n          && enclosesWeakAll(encloseBasis3(B[i], B[j], p), B)) {\n        return [B[i], B[j], p];\n      }\n    }\n  }\n\n  // If we get here then something is very wrong.\n  throw new Error;\n}\n\nfunction enclosesNot(a, b) {\n  var dr = a.r - b.r, dx = b.x - a.x, dy = b.y - a.y;\n  return dr < 0 || dr * dr < dx * dx + dy * dy;\n}\n\nfunction enclosesWeak(a, b) {\n  var dr = a.r - b.r + Math.max(a.r, b.r, 1) * 1e-9, dx = b.x - a.x, dy = b.y - a.y;\n  return dr > 0 && dr * dr > dx * dx + dy * dy;\n}\n\nfunction enclosesWeakAll(a, B) {\n  for (var i = 0; i < B.length; ++i) {\n    if (!enclosesWeak(a, B[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction encloseBasis(B) {\n  switch (B.length) {\n    case 1: return encloseBasis1(B[0]);\n    case 2: return encloseBasis2(B[0], B[1]);\n    case 3: return encloseBasis3(B[0], B[1], B[2]);\n  }\n}\n\nfunction encloseBasis1(a) {\n  return {\n    x: a.x,\n    y: a.y,\n    r: a.r\n  };\n}\n\nfunction encloseBasis2(a, b) {\n  var x1 = a.x, y1 = a.y, r1 = a.r,\n      x2 = b.x, y2 = b.y, r2 = b.r,\n      x21 = x2 - x1, y21 = y2 - y1, r21 = r2 - r1,\n      l = Math.sqrt(x21 * x21 + y21 * y21);\n  return {\n    x: (x1 + x2 + x21 / l * r21) / 2,\n    y: (y1 + y2 + y21 / l * r21) / 2,\n    r: (l + r1 + r2) / 2\n  };\n}\n\nfunction encloseBasis3(a, b, c) {\n  var x1 = a.x, y1 = a.y, r1 = a.r,\n      x2 = b.x, y2 = b.y, r2 = b.r,\n      x3 = c.x, y3 = c.y, r3 = c.r,\n      a2 = x1 - x2,\n      a3 = x1 - x3,\n      b2 = y1 - y2,\n      b3 = y1 - y3,\n      c2 = r2 - r1,\n      c3 = r3 - r1,\n      d1 = x1 * x1 + y1 * y1 - r1 * r1,\n      d2 = d1 - x2 * x2 - y2 * y2 + r2 * r2,\n      d3 = d1 - x3 * x3 - y3 * y3 + r3 * r3,\n      ab = a3 * b2 - a2 * b3,\n      xa = (b2 * d3 - b3 * d2) / (ab * 2) - x1,\n      xb = (b3 * c2 - b2 * c3) / ab,\n      ya = (a3 * d2 - a2 * d3) / (ab * 2) - y1,\n      yb = (a2 * c3 - a3 * c2) / ab,\n      A = xb * xb + yb * yb - 1,\n      B = 2 * (r1 + xa * xb + ya * yb),\n      C = xa * xa + ya * ya - r1 * r1,\n      r = -(Math.abs(A) > 1e-6 ? (B + Math.sqrt(B * B - 4 * A * C)) / (2 * A) : C / B);\n  return {\n    x: x1 + xa + xb * r,\n    y: y1 + ya + yb * r,\n    r: r\n  };\n}\n", "import array from \"../array.js\";\nimport lcg from \"../lcg.js\";\nimport {packEncloseRandom} from \"./enclose.js\";\n\nfunction place(b, a, c) {\n  var dx = b.x - a.x, x, a2,\n      dy = b.y - a.y, y, b2,\n      d2 = dx * dx + dy * dy;\n  if (d2) {\n    a2 = a.r + c.r, a2 *= a2;\n    b2 = b.r + c.r, b2 *= b2;\n    if (a2 > b2) {\n      x = (d2 + b2 - a2) / (2 * d2);\n      y = Math.sqrt(Math.max(0, b2 / d2 - x * x));\n      c.x = b.x - x * dx - y * dy;\n      c.y = b.y - x * dy + y * dx;\n    } else {\n      x = (d2 + a2 - b2) / (2 * d2);\n      y = Math.sqrt(Math.max(0, a2 / d2 - x * x));\n      c.x = a.x + x * dx - y * dy;\n      c.y = a.y + x * dy + y * dx;\n    }\n  } else {\n    c.x = a.x + c.r;\n    c.y = a.y;\n  }\n}\n\nfunction intersects(a, b) {\n  var dr = a.r + b.r - 1e-6, dx = b.x - a.x, dy = b.y - a.y;\n  return dr > 0 && dr * dr > dx * dx + dy * dy;\n}\n\nfunction score(node) {\n  var a = node._,\n      b = node.next._,\n      ab = a.r + b.r,\n      dx = (a.x * b.r + b.x * a.r) / ab,\n      dy = (a.y * b.r + b.y * a.r) / ab;\n  return dx * dx + dy * dy;\n}\n\nfunction Node(circle) {\n  this._ = circle;\n  this.next = null;\n  this.previous = null;\n}\n\nexport function packSiblingsRandom(circles, random) {\n  if (!(n = (circles = array(circles)).length)) return 0;\n\n  var a, b, c, n, aa, ca, i, j, k, sj, sk;\n\n  // Place the first circle.\n  a = circles[0], a.x = 0, a.y = 0;\n  if (!(n > 1)) return a.r;\n\n  // Place the second circle.\n  b = circles[1], a.x = -b.r, b.x = a.r, b.y = 0;\n  if (!(n > 2)) return a.r + b.r;\n\n  // Place the third circle.\n  place(b, a, c = circles[2]);\n\n  // Initialize the front-chain using the first three circles a, b and c.\n  a = new Node(a), b = new Node(b), c = new Node(c);\n  a.next = c.previous = b;\n  b.next = a.previous = c;\n  c.next = b.previous = a;\n\n  // Attempt to place each remaining circle…\n  pack: for (i = 3; i < n; ++i) {\n    place(a._, b._, c = circles[i]), c = new Node(c);\n\n    // Find the closest intersecting circle on the front-chain, if any.\n    // “Closeness” is determined by linear distance along the front-chain.\n    // “Ahead” or “behind” is likewise determined by linear distance.\n    j = b.next, k = a.previous, sj = b._.r, sk = a._.r;\n    do {\n      if (sj <= sk) {\n        if (intersects(j._, c._)) {\n          b = j, a.next = b, b.previous = a, --i;\n          continue pack;\n        }\n        sj += j._.r, j = j.next;\n      } else {\n        if (intersects(k._, c._)) {\n          a = k, a.next = b, b.previous = a, --i;\n          continue pack;\n        }\n        sk += k._.r, k = k.previous;\n      }\n    } while (j !== k.next);\n\n    // Success! Insert the new circle c between a and b.\n    c.previous = a, c.next = b, a.next = b.previous = b = c;\n\n    // Compute the new closest circle pair to the centroid.\n    aa = score(a);\n    while ((c = c.next) !== b) {\n      if ((ca = score(c)) < aa) {\n        a = c, aa = ca;\n      }\n    }\n    b = a.next;\n  }\n\n  // Compute the enclosing circle of the front chain.\n  a = [b._], c = b; while ((c = c.next) !== b) a.push(c._); c = packEncloseRandom(a, random);\n\n  // Translate the circles to put the enclosing circle around the origin.\n  for (i = 0; i < n; ++i) a = circles[i], a.x -= c.x, a.y -= c.y;\n\n  return c.r;\n}\n\nexport default function(circles) {\n  packSiblingsRandom(circles, lcg());\n  return circles;\n}\n", "import {optional} from \"../accessors.js\";\nimport constant, {constant<PERSON>ero} from \"../constant.js\";\nimport lcg from \"../lcg.js\";\nimport {packSiblingsRandom} from \"./siblings.js\";\n\nfunction defaultRadius(d) {\n  return Math.sqrt(d.value);\n}\n\nexport default function() {\n  var radius = null,\n      dx = 1,\n      dy = 1,\n      padding = constantZero;\n\n  function pack(root) {\n    const random = lcg();\n    root.x = dx / 2, root.y = dy / 2;\n    if (radius) {\n      root.eachBefore(radiusLeaf(radius))\n          .eachAfter(packChildrenRandom(padding, 0.5, random))\n          .eachBefore(translateChild(1));\n    } else {\n      root.eachBefore(radiusLeaf(defaultRadius))\n          .eachAfter(packChildrenRandom(constantZero, 1, random))\n          .eachAfter(packChildrenRandom(padding, root.r / Math.min(dx, dy), random))\n          .eachBefore(translateChild(Math.min(dx, dy) / (2 * root.r)));\n    }\n    return root;\n  }\n\n  pack.radius = function(x) {\n    return arguments.length ? (radius = optional(x), pack) : radius;\n  };\n\n  pack.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], pack) : [dx, dy];\n  };\n\n  pack.padding = function(x) {\n    return arguments.length ? (padding = typeof x === \"function\" ? x : constant(+x), pack) : padding;\n  };\n\n  return pack;\n}\n\nfunction radiusLeaf(radius) {\n  return function(node) {\n    if (!node.children) {\n      node.r = Math.max(0, +radius(node) || 0);\n    }\n  };\n}\n\nfunction packChildrenRandom(padding, k, random) {\n  return function(node) {\n    if (children = node.children) {\n      var children,\n          i,\n          n = children.length,\n          r = padding(node) * k || 0,\n          e;\n\n      if (r) for (i = 0; i < n; ++i) children[i].r += r;\n      e = packSiblingsRandom(children, random);\n      if (r) for (i = 0; i < n; ++i) children[i].r -= r;\n      node.r = e + r;\n    }\n  };\n}\n\nfunction translateChild(k) {\n  return function(node) {\n    var parent = node.parent;\n    node.r *= k;\n    if (parent) {\n      node.x = parent.x + k * node.x;\n      node.y = parent.y + k * node.y;\n    }\n  };\n}\n", "export default function(node) {\n  node.x0 = Math.round(node.x0);\n  node.y0 = Math.round(node.y0);\n  node.x1 = Math.round(node.x1);\n  node.y1 = Math.round(node.y1);\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      node,\n      i = -1,\n      n = nodes.length,\n      k = parent.value && (x1 - x0) / parent.value;\n\n  while (++i < n) {\n    node = nodes[i], node.y0 = y0, node.y1 = y1;\n    node.x0 = x0, node.x1 = x0 += node.value * k;\n  }\n}\n", "import roundNode from \"./treemap/round.js\";\nimport treemapDice from \"./treemap/dice.js\";\n\nexport default function() {\n  var dx = 1,\n      dy = 1,\n      padding = 0,\n      round = false;\n\n  function partition(root) {\n    var n = root.height + 1;\n    root.x0 =\n    root.y0 = padding;\n    root.x1 = dx;\n    root.y1 = dy / n;\n    root.eachBefore(positionNode(dy, n));\n    if (round) root.eachBefore(roundNode);\n    return root;\n  }\n\n  function positionNode(dy, n) {\n    return function(node) {\n      if (node.children) {\n        treemapDice(node, node.x0, dy * (node.depth + 1) / n, node.x1, dy * (node.depth + 2) / n);\n      }\n      var x0 = node.x0,\n          y0 = node.y0,\n          x1 = node.x1 - padding,\n          y1 = node.y1 - padding;\n      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n      node.x0 = x0;\n      node.y0 = y0;\n      node.x1 = x1;\n      node.y1 = y1;\n    };\n  }\n\n  partition.round = function(x) {\n    return arguments.length ? (round = !!x, partition) : round;\n  };\n\n  partition.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], partition) : [dx, dy];\n  };\n\n  partition.padding = function(x) {\n    return arguments.length ? (padding = +x, partition) : padding;\n  };\n\n  return partition;\n}\n", "import {optional} from \"./accessors.js\";\nimport {Node, computeHeight} from \"./hierarchy/index.js\";\n\nvar preroot = {depth: -1},\n    ambiguous = {},\n    imputed = {};\n\nfunction defaultId(d) {\n  return d.id;\n}\n\nfunction defaultParentId(d) {\n  return d.parentId;\n}\n\nexport default function() {\n  var id = defaultId,\n      parentId = defaultParentId,\n      path;\n\n  function stratify(data) {\n    var nodes = Array.from(data),\n        currentId = id,\n        currentParentId = parentId,\n        n,\n        d,\n        i,\n        root,\n        parent,\n        node,\n        nodeId,\n        nodeKey,\n        nodeByKey = new Map;\n\n    if (path != null) {\n      const I = nodes.map((d, i) => normalize(path(d, i, data)));\n      const P = I.map(parentof);\n      const S = new Set(I).add(\"\");\n      for (const i of P) {\n        if (!S.has(i)) {\n          S.add(i);\n          I.push(i);\n          P.push(parentof(i));\n          nodes.push(imputed);\n        }\n      }\n      currentId = (_, i) => I[i];\n      currentParentId = (_, i) => P[i];\n    }\n\n    for (i = 0, n = nodes.length; i < n; ++i) {\n      d = nodes[i], node = nodes[i] = new Node(d);\n      if ((nodeId = currentId(d, i, data)) != null && (nodeId += \"\")) {\n        nodeKey = node.id = nodeId;\n        nodeByKey.set(nodeKey, nodeByKey.has(nodeKey) ? ambiguous : node);\n      }\n      if ((nodeId = currentParentId(d, i, data)) != null && (nodeId += \"\")) {\n        node.parent = nodeId;\n      }\n    }\n\n    for (i = 0; i < n; ++i) {\n      node = nodes[i];\n      if (nodeId = node.parent) {\n        parent = nodeByKey.get(nodeId);\n        if (!parent) throw new Error(\"missing: \" + nodeId);\n        if (parent === ambiguous) throw new Error(\"ambiguous: \" + nodeId);\n        if (parent.children) parent.children.push(node);\n        else parent.children = [node];\n        node.parent = parent;\n      } else {\n        if (root) throw new Error(\"multiple roots\");\n        root = node;\n      }\n    }\n\n    if (!root) throw new Error(\"no root\");\n\n    // When imputing internal nodes, only introduce roots if needed.\n    // Then replace the imputed marker data with null.\n    if (path != null) {\n      while (root.data === imputed && root.children.length === 1) {\n        root = root.children[0], --n;\n      }\n      for (let i = nodes.length - 1; i >= 0; --i) {\n        node = nodes[i];\n        if (node.data !== imputed) break;\n        node.data = null;\n      }\n    }\n\n    root.parent = preroot;\n    root.eachBefore(function(node) { node.depth = node.parent.depth + 1; --n; }).eachBefore(computeHeight);\n    root.parent = null;\n    if (n > 0) throw new Error(\"cycle\");\n\n    return root;\n  }\n\n  stratify.id = function(x) {\n    return arguments.length ? (id = optional(x), stratify) : id;\n  };\n\n  stratify.parentId = function(x) {\n    return arguments.length ? (parentId = optional(x), stratify) : parentId;\n  };\n\n  stratify.path = function(x) {\n    return arguments.length ? (path = optional(x), stratify) : path;\n  };\n\n  return stratify;\n}\n\n// To normalize a path, we coerce to a string, strip the trailing slash if any\n// (as long as the trailing slash is not immediately preceded by another slash),\n// and add leading slash if missing.\nfunction normalize(path) {\n  path = `${path}`;\n  let i = path.length;\n  if (slash(path, i - 1) && !slash(path, i - 2)) path = path.slice(0, -1);\n  return path[0] === \"/\" ? path : `/${path}`;\n}\n\n// Walk backwards to find the first slash that is not the leading slash, e.g.:\n// \"/foo/bar\" ⇥ \"/foo\", \"/foo\" ⇥ \"/\", \"/\" ↦ \"\". (The root is special-cased\n// because the id of the root must be a truthy value.)\nfunction parentof(path) {\n  let i = path.length;\n  if (i < 2) return \"\";\n  while (--i > 1) if (slash(path, i)) break;\n  return path.slice(0, i);\n}\n\n// Slashes can be escaped; to determine whether a slash is a path delimiter, we\n// count the number of preceding backslashes escaping the forward slash: an odd\n// number indicates an escaped forward slash.\nfunction slash(path, i) {\n  if (path[i] === \"/\") {\n    let k = 0;\n    while (i > 0 && path[--i] === \"\\\\\") ++k;\n    if ((k & 1) === 0) return true;\n  }\n  return false;\n}\n", "import {Node} from \"./hierarchy/index.js\";\n\nfunction defaultSeparation(a, b) {\n  return a.parent === b.parent ? 1 : 2;\n}\n\n// function radialSeparation(a, b) {\n//   return (a.parent === b.parent ? 1 : 2) / a.depth;\n// }\n\n// This function is used to traverse the left contour of a subtree (or\n// subforest). It returns the successor of v on this contour. This successor is\n// either given by the leftmost child of v or by the thread of v. The function\n// returns null if and only if v is on the highest level of its subtree.\nfunction nextLeft(v) {\n  var children = v.children;\n  return children ? children[0] : v.t;\n}\n\n// This function works analogously to nextLeft.\nfunction nextRight(v) {\n  var children = v.children;\n  return children ? children[children.length - 1] : v.t;\n}\n\n// Shifts the current subtree rooted at w+. This is done by increasing\n// prelim(w+) and mod(w+) by shift.\nfunction moveSubtree(wm, wp, shift) {\n  var change = shift / (wp.i - wm.i);\n  wp.c -= change;\n  wp.s += shift;\n  wm.c += change;\n  wp.z += shift;\n  wp.m += shift;\n}\n\n// All other shifts, applied to the smaller subtrees between w- and w+, are\n// performed by this function. To prepare the shifts, we have to adjust\n// change(w+), shift(w+), and change(w-).\nfunction executeShifts(v) {\n  var shift = 0,\n      change = 0,\n      children = v.children,\n      i = children.length,\n      w;\n  while (--i >= 0) {\n    w = children[i];\n    w.z += shift;\n    w.m += shift;\n    shift += w.s + (change += w.c);\n  }\n}\n\n// If vi-’s ancestor is a sibling of v, returns vi-’s ancestor. Otherwise,\n// returns the specified (default) ancestor.\nfunction nextAncestor(vim, v, ancestor) {\n  return vim.a.parent === v.parent ? vim.a : ancestor;\n}\n\nfunction TreeNode(node, i) {\n  this._ = node;\n  this.parent = null;\n  this.children = null;\n  this.A = null; // default ancestor\n  this.a = this; // ancestor\n  this.z = 0; // prelim\n  this.m = 0; // mod\n  this.c = 0; // change\n  this.s = 0; // shift\n  this.t = null; // thread\n  this.i = i; // number\n}\n\nTreeNode.prototype = Object.create(Node.prototype);\n\nfunction treeRoot(root) {\n  var tree = new TreeNode(root, 0),\n      node,\n      nodes = [tree],\n      child,\n      children,\n      i,\n      n;\n\n  while (node = nodes.pop()) {\n    if (children = node._.children) {\n      node.children = new Array(n = children.length);\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = node.children[i] = new TreeNode(children[i], i));\n        child.parent = node;\n      }\n    }\n  }\n\n  (tree.parent = new TreeNode(null, 0)).children = [tree];\n  return tree;\n}\n\n// Node-link tree diagram using the Reingold-Tilford \"tidy\" algorithm\nexport default function() {\n  var separation = defaultSeparation,\n      dx = 1,\n      dy = 1,\n      nodeSize = null;\n\n  function tree(root) {\n    var t = treeRoot(root);\n\n    // Compute the layout using Buchheim et al.’s algorithm.\n    t.eachAfter(firstWalk), t.parent.m = -t.z;\n    t.eachBefore(secondWalk);\n\n    // If a fixed node size is specified, scale x and y.\n    if (nodeSize) root.eachBefore(sizeNode);\n\n    // If a fixed tree size is specified, scale x and y based on the extent.\n    // Compute the left-most, right-most, and depth-most nodes for extents.\n    else {\n      var left = root,\n          right = root,\n          bottom = root;\n      root.eachBefore(function(node) {\n        if (node.x < left.x) left = node;\n        if (node.x > right.x) right = node;\n        if (node.depth > bottom.depth) bottom = node;\n      });\n      var s = left === right ? 1 : separation(left, right) / 2,\n          tx = s - left.x,\n          kx = dx / (right.x + s + tx),\n          ky = dy / (bottom.depth || 1);\n      root.eachBefore(function(node) {\n        node.x = (node.x + tx) * kx;\n        node.y = node.depth * ky;\n      });\n    }\n\n    return root;\n  }\n\n  // Computes a preliminary x-coordinate for v. Before that, FIRST WALK is\n  // applied recursively to the children of v, as well as the function\n  // APPORTION. After spacing out the children by calling EXECUTE SHIFTS, the\n  // node v is placed to the midpoint of its outermost children.\n  function firstWalk(v) {\n    var children = v.children,\n        siblings = v.parent.children,\n        w = v.i ? siblings[v.i - 1] : null;\n    if (children) {\n      executeShifts(v);\n      var midpoint = (children[0].z + children[children.length - 1].z) / 2;\n      if (w) {\n        v.z = w.z + separation(v._, w._);\n        v.m = v.z - midpoint;\n      } else {\n        v.z = midpoint;\n      }\n    } else if (w) {\n      v.z = w.z + separation(v._, w._);\n    }\n    v.parent.A = apportion(v, w, v.parent.A || siblings[0]);\n  }\n\n  // Computes all real x-coordinates by summing up the modifiers recursively.\n  function secondWalk(v) {\n    v._.x = v.z + v.parent.m;\n    v.m += v.parent.m;\n  }\n\n  // The core of the algorithm. Here, a new subtree is combined with the\n  // previous subtrees. Threads are used to traverse the inside and outside\n  // contours of the left and right subtree up to the highest common level. The\n  // vertices used for the traversals are vi+, vi-, vo-, and vo+, where the\n  // superscript o means outside and i means inside, the subscript - means left\n  // subtree and + means right subtree. For summing up the modifiers along the\n  // contour, we use respective variables si+, si-, so-, and so+. Whenever two\n  // nodes of the inside contours conflict, we compute the left one of the\n  // greatest uncommon ancestors using the function ANCESTOR and call MOVE\n  // SUBTREE to shift the subtree and prepare the shifts of smaller subtrees.\n  // Finally, we add a new thread (if necessary).\n  function apportion(v, w, ancestor) {\n    if (w) {\n      var vip = v,\n          vop = v,\n          vim = w,\n          vom = vip.parent.children[0],\n          sip = vip.m,\n          sop = vop.m,\n          sim = vim.m,\n          som = vom.m,\n          shift;\n      while (vim = nextRight(vim), vip = nextLeft(vip), vim && vip) {\n        vom = nextLeft(vom);\n        vop = nextRight(vop);\n        vop.a = v;\n        shift = vim.z + sim - vip.z - sip + separation(vim._, vip._);\n        if (shift > 0) {\n          moveSubtree(nextAncestor(vim, v, ancestor), v, shift);\n          sip += shift;\n          sop += shift;\n        }\n        sim += vim.m;\n        sip += vip.m;\n        som += vom.m;\n        sop += vop.m;\n      }\n      if (vim && !nextRight(vop)) {\n        vop.t = vim;\n        vop.m += sim - sop;\n      }\n      if (vip && !nextLeft(vom)) {\n        vom.t = vip;\n        vom.m += sip - som;\n        ancestor = v;\n      }\n    }\n    return ancestor;\n  }\n\n  function sizeNode(node) {\n    node.x *= dx;\n    node.y = node.depth * dy;\n  }\n\n  tree.separation = function(x) {\n    return arguments.length ? (separation = x, tree) : separation;\n  };\n\n  tree.size = function(x) {\n    return arguments.length ? (nodeSize = false, dx = +x[0], dy = +x[1], tree) : (nodeSize ? null : [dx, dy]);\n  };\n\n  tree.nodeSize = function(x) {\n    return arguments.length ? (nodeSize = true, dx = +x[0], dy = +x[1], tree) : (nodeSize ? [dx, dy] : null);\n  };\n\n  return tree;\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      node,\n      i = -1,\n      n = nodes.length,\n      k = parent.value && (y1 - y0) / parent.value;\n\n  while (++i < n) {\n    node = nodes[i], node.x0 = x0, node.x1 = x1;\n    node.y0 = y0, node.y1 = y0 += node.value * k;\n  }\n}\n", "import treemapDice from \"./dice.js\";\nimport treemapSlice from \"./slice.js\";\n\nexport var phi = (1 + Math.sqrt(5)) / 2;\n\nexport function squarifyRatio(ratio, parent, x0, y0, x1, y1) {\n  var rows = [],\n      nodes = parent.children,\n      row,\n      nodeValue,\n      i0 = 0,\n      i1 = 0,\n      n = nodes.length,\n      dx, dy,\n      value = parent.value,\n      sumValue,\n      minValue,\n      maxValue,\n      newRatio,\n      minRatio,\n      alpha,\n      beta;\n\n  while (i0 < n) {\n    dx = x1 - x0, dy = y1 - y0;\n\n    // Find the next non-empty node.\n    do sumValue = nodes[i1++].value; while (!sumValue && i1 < n);\n    minValue = maxValue = sumValue;\n    alpha = Math.max(dy / dx, dx / dy) / (value * ratio);\n    beta = sumValue * sumValue * alpha;\n    minRatio = Math.max(maxValue / beta, beta / minValue);\n\n    // Keep adding nodes while the aspect ratio maintains or improves.\n    for (; i1 < n; ++i1) {\n      sumValue += nodeValue = nodes[i1].value;\n      if (nodeValue < minValue) minValue = nodeValue;\n      if (nodeValue > maxValue) maxValue = nodeValue;\n      beta = sumValue * sumValue * alpha;\n      newRatio = Math.max(maxValue / beta, beta / minValue);\n      if (newRatio > minRatio) { sumValue -= nodeValue; break; }\n      minRatio = newRatio;\n    }\n\n    // Position and record the row orientation.\n    rows.push(row = {value: sumValue, dice: dx < dy, children: nodes.slice(i0, i1)});\n    if (row.dice) treemapDice(row, x0, y0, x1, value ? y0 += dy * sumValue / value : y1);\n    else treemapSlice(row, x0, y0, value ? x0 += dx * sumValue / value : x1, y1);\n    value -= sumValue, i0 = i1;\n  }\n\n  return rows;\n}\n\nexport default (function custom(ratio) {\n\n  function squarify(parent, x0, y0, x1, y1) {\n    squarifyRatio(ratio, parent, x0, y0, x1, y1);\n  }\n\n  squarify.ratio = function(x) {\n    return custom((x = +x) > 1 ? x : 1);\n  };\n\n  return squarify;\n})(phi);\n", "import roundNode from \"./round.js\";\nimport squarify from \"./squarify.js\";\nimport {required} from \"../accessors.js\";\nimport constant, {constantZero} from \"../constant.js\";\n\nexport default function() {\n  var tile = squarify,\n      round = false,\n      dx = 1,\n      dy = 1,\n      paddingStack = [0],\n      paddingInner = constantZero,\n      paddingTop = constantZero,\n      paddingRight = constantZero,\n      paddingBottom = constantZero,\n      paddingLeft = constantZero;\n\n  function treemap(root) {\n    root.x0 =\n    root.y0 = 0;\n    root.x1 = dx;\n    root.y1 = dy;\n    root.eachBefore(positionNode);\n    paddingStack = [0];\n    if (round) root.eachBefore(roundNode);\n    return root;\n  }\n\n  function positionNode(node) {\n    var p = paddingStack[node.depth],\n        x0 = node.x0 + p,\n        y0 = node.y0 + p,\n        x1 = node.x1 - p,\n        y1 = node.y1 - p;\n    if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n    if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n    node.x0 = x0;\n    node.y0 = y0;\n    node.x1 = x1;\n    node.y1 = y1;\n    if (node.children) {\n      p = paddingStack[node.depth + 1] = paddingInner(node) / 2;\n      x0 += paddingLeft(node) - p;\n      y0 += paddingTop(node) - p;\n      x1 -= paddingRight(node) - p;\n      y1 -= paddingBottom(node) - p;\n      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n      tile(node, x0, y0, x1, y1);\n    }\n  }\n\n  treemap.round = function(x) {\n    return arguments.length ? (round = !!x, treemap) : round;\n  };\n\n  treemap.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], treemap) : [dx, dy];\n  };\n\n  treemap.tile = function(x) {\n    return arguments.length ? (tile = required(x), treemap) : tile;\n  };\n\n  treemap.padding = function(x) {\n    return arguments.length ? treemap.paddingInner(x).paddingOuter(x) : treemap.paddingInner();\n  };\n\n  treemap.paddingInner = function(x) {\n    return arguments.length ? (paddingInner = typeof x === \"function\" ? x : constant(+x), treemap) : paddingInner;\n  };\n\n  treemap.paddingOuter = function(x) {\n    return arguments.length ? treemap.paddingTop(x).paddingRight(x).paddingBottom(x).paddingLeft(x) : treemap.paddingTop();\n  };\n\n  treemap.paddingTop = function(x) {\n    return arguments.length ? (paddingTop = typeof x === \"function\" ? x : constant(+x), treemap) : paddingTop;\n  };\n\n  treemap.paddingRight = function(x) {\n    return arguments.length ? (paddingRight = typeof x === \"function\" ? x : constant(+x), treemap) : paddingRight;\n  };\n\n  treemap.paddingBottom = function(x) {\n    return arguments.length ? (paddingBottom = typeof x === \"function\" ? x : constant(+x), treemap) : paddingBottom;\n  };\n\n  treemap.paddingLeft = function(x) {\n    return arguments.length ? (paddingLeft = typeof x === \"function\" ? x : constant(+x), treemap) : paddingLeft;\n  };\n\n  return treemap;\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      i, n = nodes.length,\n      sum, sums = new Array(n + 1);\n\n  for (sums[0] = sum = i = 0; i < n; ++i) {\n    sums[i + 1] = sum += nodes[i].value;\n  }\n\n  partition(0, n, parent.value, x0, y0, x1, y1);\n\n  function partition(i, j, value, x0, y0, x1, y1) {\n    if (i >= j - 1) {\n      var node = nodes[i];\n      node.x0 = x0, node.y0 = y0;\n      node.x1 = x1, node.y1 = y1;\n      return;\n    }\n\n    var valueOffset = sums[i],\n        valueTarget = (value / 2) + valueOffset,\n        k = i + 1,\n        hi = j - 1;\n\n    while (k < hi) {\n      var mid = k + hi >>> 1;\n      if (sums[mid] < valueTarget) k = mid + 1;\n      else hi = mid;\n    }\n\n    if ((valueTarget - sums[k - 1]) < (sums[k] - valueTarget) && i + 1 < k) --k;\n\n    var valueLeft = sums[k] - valueOffset,\n        valueRight = value - valueLeft;\n\n    if ((x1 - x0) > (y1 - y0)) {\n      var xk = value ? (x0 * valueRight + x1 * valueLeft) / value : x1;\n      partition(i, k, valueLeft, x0, y0, xk, y1);\n      partition(k, j, valueRight, xk, y0, x1, y1);\n    } else {\n      var yk = value ? (y0 * valueRight + y1 * valueLeft) / value : y1;\n      partition(i, k, valueLeft, x0, y0, x1, yk);\n      partition(k, j, valueRight, x0, yk, x1, y1);\n    }\n  }\n}\n", "import dice from \"./dice.js\";\nimport slice from \"./slice.js\";\n\nexport default function(parent, x0, y0, x1, y1) {\n  (parent.depth & 1 ? slice : dice)(parent, x0, y0, x1, y1);\n}\n", "import treemapDice from \"./dice.js\";\nimport treemapSlice from \"./slice.js\";\nimport {phi, squarifyRatio} from \"./squarify.js\";\n\nexport default (function custom(ratio) {\n\n  function resquarify(parent, x0, y0, x1, y1) {\n    if ((rows = parent._squarify) && (rows.ratio === ratio)) {\n      var rows,\n          row,\n          nodes,\n          i,\n          j = -1,\n          n,\n          m = rows.length,\n          value = parent.value;\n\n      while (++j < m) {\n        row = rows[j], nodes = row.children;\n        for (i = row.value = 0, n = nodes.length; i < n; ++i) row.value += nodes[i].value;\n        if (row.dice) treemapDice(row, x0, y0, x1, value ? y0 += (y1 - y0) * row.value / value : y1);\n        else treemapSlice(row, x0, y0, value ? x0 += (x1 - x0) * row.value / value : x1, y1);\n        value -= row.value;\n      }\n    } else {\n      parent._squarify = rows = squarifyRatio(ratio, parent, x0, y0, x1, y1);\n      rows.ratio = ratio;\n    }\n  }\n\n  resquarify.ratio = function(x) {\n    return custom((x = +x) > 1 ? x : 1);\n  };\n\n  return resquarify;\n})(phi);\n"], "mappings": ";AAAA,SAAS,kBAAkBA,IAAG,GAAG;AAC/B,SAAOA,GAAE,WAAW,EAAE,SAAS,IAAI;AACrC;AAEA,SAAS,MAAM,UAAU;AACvB,SAAO,SAAS,OAAO,aAAa,CAAC,IAAI,SAAS;AACpD;AAEA,SAAS,YAAY,GAAGC,IAAG;AACzB,SAAO,IAAIA,GAAE;AACf;AAEA,SAAS,KAAK,UAAU;AACtB,SAAO,IAAI,SAAS,OAAO,YAAY,CAAC;AAC1C;AAEA,SAAS,WAAW,GAAGA,IAAG;AACxB,SAAO,KAAK,IAAI,GAAGA,GAAE,CAAC;AACxB;AAEA,SAAS,SAAS,MAAM;AACtB,MAAI;AACJ,SAAO,WAAW,KAAK,SAAU,QAAO,SAAS,CAAC;AAClD,SAAO;AACT;AAEA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,SAAO,WAAW,KAAK,SAAU,QAAO,SAAS,SAAS,SAAS,CAAC;AACpE,SAAO;AACT;AAEe,SAAR,kBAAmB;AACxB,MAAI,aAAa,mBACb,KAAK,GACL,KAAK,GACL,WAAW;AAEf,WAAS,QAAQ,MAAM;AACrB,QAAI,cACA,IAAI;AAGR,SAAK,UAAU,SAAS,MAAM;AAC5B,UAAI,WAAW,KAAK;AACpB,UAAI,UAAU;AACZ,aAAK,IAAI,MAAM,QAAQ;AACvB,aAAK,IAAI,KAAK,QAAQ;AAAA,MACxB,OAAO;AACL,aAAK,IAAI,eAAe,KAAK,WAAW,MAAM,YAAY,IAAI;AAC9D,aAAK,IAAI;AACT,uBAAe;AAAA,MACjB;AAAA,IACF,CAAC;AAED,QAAI,OAAO,SAAS,IAAI,GACpB,QAAQ,UAAU,IAAI,GACtB,KAAK,KAAK,IAAI,WAAW,MAAM,KAAK,IAAI,GACxC,KAAK,MAAM,IAAI,WAAW,OAAO,IAAI,IAAI;AAG7C,WAAO,KAAK,UAAU,WAAW,SAAS,MAAM;AAC9C,WAAK,KAAK,KAAK,IAAI,KAAK,KAAK;AAC7B,WAAK,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,IAC/B,IAAI,SAAS,MAAM;AACjB,WAAK,KAAK,KAAK,IAAI,OAAO,KAAK,MAAM;AACrC,WAAK,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM;AAAA,IAClD,CAAC;AAAA,EACH;AAEA,UAAQ,aAAa,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,aAAa,GAAG,WAAW;AAAA,EACxD;AAEA,UAAQ,OAAO,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,WAAW,OAAO,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,WAAY,WAAW,OAAO,CAAC,IAAI,EAAE;AAAA,EAC5G;AAEA,UAAQ,WAAW,SAAS,GAAG;AAC7B,WAAO,UAAU,UAAU,WAAW,MAAM,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,WAAY,WAAW,CAAC,IAAI,EAAE,IAAI;AAAA,EACxG;AAEA,SAAO;AACT;;;ACnFA,SAAS,MAAM,MAAM;AACnB,MAAI,MAAM,GACN,WAAW,KAAK,UAChB,IAAI,YAAY,SAAS;AAC7B,MAAI,CAAC,EAAG,OAAM;AAAA,MACT,QAAO,EAAE,KAAK,EAAG,QAAO,SAAS,CAAC,EAAE;AACzC,OAAK,QAAQ;AACf;AAEe,SAAR,gBAAmB;AACxB,SAAO,KAAK,UAAU,KAAK;AAC7B;;;ACXe,SAAR,aAAiB,UAAU,MAAM;AACtC,MAAI,QAAQ;AACZ,aAAW,QAAQ,MAAM;AACvB,aAAS,KAAK,MAAM,MAAM,EAAE,OAAO,IAAI;AAAA,EACzC;AACA,SAAO;AACT;;;ACNe,SAAR,mBAAiB,UAAU,MAAM;AACtC,MAAI,OAAO,MAAM,QAAQ,CAAC,IAAI,GAAG,UAAU,GAAG,QAAQ;AACtD,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,aAAS,KAAK,MAAM,MAAM,EAAE,OAAO,IAAI;AACvC,QAAI,WAAW,KAAK,UAAU;AAC5B,WAAK,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACzC,cAAM,KAAK,SAAS,CAAC,CAAC;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACXe,SAAR,kBAAiB,UAAU,MAAM;AACtC,MAAI,OAAO,MAAM,QAAQ,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,UAAU,GAAG,GAAG,QAAQ;AACpE,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,SAAK,KAAK,IAAI;AACd,QAAI,WAAW,KAAK,UAAU;AAC5B,WAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,cAAM,KAAK,SAAS,CAAC,CAAC;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO,KAAK,IAAI,GAAG;AACxB,aAAS,KAAK,MAAM,MAAM,EAAE,OAAO,IAAI;AAAA,EACzC;AACA,SAAO;AACT;;;ACde,SAAR,aAAiB,UAAU,MAAM;AACtC,MAAI,QAAQ;AACZ,aAAW,QAAQ,MAAM;AACvB,QAAI,SAAS,KAAK,MAAM,MAAM,EAAE,OAAO,IAAI,GAAG;AAC5C,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;ACPe,SAAR,YAAiB,OAAO;AAC7B,SAAO,KAAK,UAAU,SAAS,MAAM;AACnC,QAAI,MAAM,CAAC,MAAM,KAAK,IAAI,KAAK,GAC3B,WAAW,KAAK,UAChB,IAAI,YAAY,SAAS;AAC7B,WAAO,EAAE,KAAK,EAAG,QAAO,SAAS,CAAC,EAAE;AACpC,SAAK,QAAQ;AAAA,EACf,CAAC;AACH;;;ACRe,SAAR,aAAiB,SAAS;AAC/B,SAAO,KAAK,WAAW,SAAS,MAAM;AACpC,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,KAAK,OAAO;AAAA,IAC5B;AAAA,EACF,CAAC;AACH;;;ACNe,SAAR,aAAiB,KAAK;AAC3B,MAAI,QAAQ,MACR,WAAW,oBAAoB,OAAO,GAAG,GACzC,QAAQ,CAAC,KAAK;AAClB,SAAO,UAAU,UAAU;AACzB,YAAQ,MAAM;AACd,UAAM,KAAK,KAAK;AAAA,EAClB;AACA,MAAI,IAAI,MAAM;AACd,SAAO,QAAQ,UAAU;AACvB,UAAM,OAAO,GAAG,GAAG,GAAG;AACtB,UAAM,IAAI;AAAA,EACZ;AACA,SAAO;AACT;AAEA,SAAS,oBAAoBC,IAAG,GAAG;AACjC,MAAIA,OAAM,EAAG,QAAOA;AACpB,MAAI,SAASA,GAAE,UAAU,GACrB,SAAS,EAAE,UAAU,GACrBC,KAAI;AACR,EAAAD,KAAI,OAAO,IAAI;AACf,MAAI,OAAO,IAAI;AACf,SAAOA,OAAM,GAAG;AACd,IAAAC,KAAID;AACJ,IAAAA,KAAI,OAAO,IAAI;AACf,QAAI,OAAO,IAAI;AAAA,EACjB;AACA,SAAOC;AACT;;;AC7Be,SAAR,oBAAmB;AACxB,MAAI,OAAO,MAAM,QAAQ,CAAC,IAAI;AAC9B,SAAO,OAAO,KAAK,QAAQ;AACzB,UAAM,KAAK,IAAI;AAAA,EACjB;AACA,SAAO;AACT;;;ACNe,SAAR,sBAAmB;AACxB,SAAO,MAAM,KAAK,IAAI;AACxB;;;ACFe,SAAR,iBAAmB;AACxB,MAAI,SAAS,CAAC;AACd,OAAK,WAAW,SAAS,MAAM;AAC7B,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO,KAAK,IAAI;AAAA,IAClB;AAAA,EACF,CAAC;AACD,SAAO;AACT;;;ACRe,SAAR,gBAAmB;AACxB,MAAI,OAAO,MAAM,QAAQ,CAAC;AAC1B,OAAK,KAAK,SAAS,MAAM;AACvB,QAAI,SAAS,MAAM;AACjB,YAAM,KAAK,EAAC,QAAQ,KAAK,QAAQ,QAAQ,KAAI,CAAC;AAAA,IAChD;AAAA,EACF,CAAC;AACD,SAAO;AACT;;;ACRe,UAAR,mBAAoB;AACzB,MAAI,OAAO,MAAM,SAAS,OAAO,CAAC,IAAI,GAAG,UAAU,GAAG;AACtD,KAAG;AACD,cAAU,KAAK,QAAQ,GAAG,OAAO,CAAC;AAClC,WAAO,OAAO,QAAQ,IAAI,GAAG;AAC3B,YAAM;AACN,UAAI,WAAW,KAAK,UAAU;AAC5B,aAAK,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,eAAK,KAAK,SAAS,CAAC,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AAAA,EACF,SAAS,KAAK;AAChB;;;ACCe,SAAR,UAA2B,MAAM,UAAU;AAChD,MAAI,gBAAgB,KAAK;AACvB,WAAO,CAAC,QAAW,IAAI;AACvB,QAAI,aAAa,OAAW,YAAW;AAAA,EACzC,WAAW,aAAa,QAAW;AACjC,eAAW;AAAA,EACb;AAEA,MAAI,OAAO,IAAI,KAAK,IAAI,GACpB,MACA,QAAQ,CAAC,IAAI,GACb,OACA,QACA,GACA;AAEJ,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,SAAK,SAAS,SAAS,KAAK,IAAI,OAAO,KAAK,SAAS,MAAM,KAAK,MAAM,GAAG,SAAS;AAChF,WAAK,WAAW;AAChB,WAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,cAAM,KAAK,QAAQ,OAAO,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC;AAClD,cAAM,SAAS;AACf,cAAM,QAAQ,KAAK,QAAQ;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAEA,SAAO,KAAK,WAAW,aAAa;AACtC;AAEA,SAAS,YAAY;AACnB,SAAO,UAAU,IAAI,EAAE,WAAW,QAAQ;AAC5C;AAEA,SAAS,eAAe,GAAG;AACzB,SAAO,EAAE;AACX;AAEA,SAAS,YAAY,GAAG;AACtB,SAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI;AACnC;AAEA,SAAS,SAAS,MAAM;AACtB,MAAI,KAAK,KAAK,UAAU,OAAW,MAAK,QAAQ,KAAK,KAAK;AAC1D,OAAK,OAAO,KAAK,KAAK;AACxB;AAEO,SAAS,cAAc,MAAM;AAClC,MAAI,SAAS;AACb;AAAG,SAAK,SAAS;AAAA,UACT,OAAO,KAAK,WAAY,KAAK,SAAS,EAAE;AAClD;AAEO,SAAS,KAAK,MAAM;AACzB,OAAK,OAAO;AACZ,OAAK,QACL,KAAK,SAAS;AACd,OAAK,SAAS;AAChB;AAEA,KAAK,YAAY,UAAU,YAAY;AAAA,EACrC,aAAa;AAAA,EACb,OAAO;AAAA,EACP,MAAM;AAAA,EACN,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,KAAK;AAAA,EACL,MAAM;AAAA,EACN,MAAM;AAAA,EACN,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,CAAC,OAAO,QAAQ,GAAG;AACrB;;;AC1FO,SAAS,SAAS,GAAG;AAC1B,SAAO,KAAK,OAAO,OAAO,SAAS,CAAC;AACtC;AAEO,SAAS,SAAS,GAAG;AAC1B,MAAI,OAAO,MAAM,WAAY,OAAM,IAAI;AACvC,SAAO;AACT;;;ACPO,SAAS,eAAe;AAC7B,SAAO;AACT;AAEe,SAAR,iBAAiB,GAAG;AACzB,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;;;ACPA,IAAM,IAAI;AACV,IAAM,IAAI;AACV,IAAM,IAAI;AAEK,SAAR,cAAmB;AACxB,MAAI,IAAI;AACR,SAAO,OAAO,KAAK,IAAI,IAAI,KAAK,KAAK;AACvC;;;ACRe,SAAR,cAAiB,GAAG;AACzB,SAAO,OAAO,MAAM,YAAY,YAAY,IACxC,IACA,MAAM,KAAK,CAAC;AAClB;AAEO,SAAS,QAAQ,OAAO,QAAQ;AACrC,MAAIC,KAAI,MAAM,QACV,GACA;AAEJ,SAAOA,IAAG;AACR,QAAI,OAAO,IAAIA,OAAM;AACrB,QAAI,MAAMA,EAAC;AACX,UAAMA,EAAC,IAAI,MAAM,CAAC;AAClB,UAAM,CAAC,IAAI;AAAA,EACb;AAEA,SAAO;AACT;;;AChBe,SAAR,gBAAiB,SAAS;AAC/B,SAAO,kBAAkB,SAAS,YAAI,CAAC;AACzC;AAEO,SAAS,kBAAkB,SAAS,QAAQ;AACjD,MAAI,IAAI,GAAG,KAAK,UAAU,QAAQ,MAAM,KAAK,OAAO,GAAG,MAAM,GAAG,QAAQ,IAAI,CAAC,GAAG,GAAG;AAEnF,SAAO,IAAI,GAAG;AACZ,QAAI,QAAQ,CAAC;AACb,QAAI,KAAK,aAAa,GAAG,CAAC,EAAG,GAAE;AAAA,QAC1B,KAAI,aAAa,IAAI,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI;AAAA,EACpD;AAEA,SAAO;AACT;AAEA,SAAS,YAAY,GAAG,GAAG;AACzB,MAAI,GAAG;AAEP,MAAI,gBAAgB,GAAG,CAAC,EAAG,QAAO,CAAC,CAAC;AAGpC,OAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AAC7B,QAAI,YAAY,GAAG,EAAE,CAAC,CAAC,KAChB,gBAAgB,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;AACjD,aAAO,CAAC,EAAE,CAAC,GAAG,CAAC;AAAA,IACjB;AAAA,EACF;AAGA,OAAK,IAAI,GAAG,IAAI,EAAE,SAAS,GAAG,EAAE,GAAG;AACjC,SAAK,IAAI,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AACjC,UAAI,YAAY,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,KACrC,YAAY,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,KACxC,YAAY,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,KACxC,gBAAgB,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;AACvD,eAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAGA,QAAM,IAAI;AACZ;AAEA,SAAS,YAAYC,IAAG,GAAG;AACzB,MAAI,KAAKA,GAAE,IAAI,EAAE,GAAG,KAAK,EAAE,IAAIA,GAAE,GAAG,KAAK,EAAE,IAAIA,GAAE;AACjD,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C;AAEA,SAAS,aAAaA,IAAG,GAAG;AAC1B,MAAI,KAAKA,GAAE,IAAI,EAAE,IAAI,KAAK,IAAIA,GAAE,GAAG,EAAE,GAAG,CAAC,IAAI,MAAM,KAAK,EAAE,IAAIA,GAAE,GAAG,KAAK,EAAE,IAAIA,GAAE;AAChF,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C;AAEA,SAAS,gBAAgBA,IAAG,GAAG;AAC7B,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AACjC,QAAI,CAAC,aAAaA,IAAG,EAAE,CAAC,CAAC,GAAG;AAC1B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aAAa,GAAG;AACvB,UAAQ,EAAE,QAAQ;AAAA,IAChB,KAAK;AAAG,aAAO,cAAc,EAAE,CAAC,CAAC;AAAA,IACjC,KAAK;AAAG,aAAO,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,IACvC,KAAK;AAAG,aAAO,cAAc,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EAC/C;AACF;AAEA,SAAS,cAAcA,IAAG;AACxB,SAAO;AAAA,IACL,GAAGA,GAAE;AAAA,IACL,GAAGA,GAAE;AAAA,IACL,GAAGA,GAAE;AAAA,EACP;AACF;AAEA,SAAS,cAAcA,IAAG,GAAG;AAC3B,MAAI,KAAKA,GAAE,GAAG,KAAKA,GAAE,GAAG,KAAKA,GAAE,GAC3B,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,GAC3B,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IACzC,IAAI,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AACvC,SAAO;AAAA,IACL,IAAI,KAAK,KAAK,MAAM,IAAI,OAAO;AAAA,IAC/B,IAAI,KAAK,KAAK,MAAM,IAAI,OAAO;AAAA,IAC/B,IAAI,IAAI,KAAK,MAAM;AAAA,EACrB;AACF;AAEA,SAAS,cAAcA,IAAG,GAAGC,IAAG;AAC9B,MAAI,KAAKD,GAAE,GAAG,KAAKA,GAAE,GAAG,KAAKA,GAAE,GAC3B,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,GAC3B,KAAKC,GAAE,GAAG,KAAKA,GAAE,GAAG,KAAKA,GAAE,GAC3BC,MAAK,KAAK,IACV,KAAK,KAAK,IACV,KAAK,KAAK,IACV,KAAK,KAAK,IACVC,MAAK,KAAK,IACV,KAAK,KAAK,IACV,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAC9B,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IACnC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IACnC,KAAK,KAAK,KAAKD,MAAK,IACpB,MAAM,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,IACtC,MAAM,KAAKC,MAAK,KAAK,MAAM,IAC3B,MAAM,KAAK,KAAKD,MAAK,OAAO,KAAK,KAAK,IACtC,MAAMA,MAAK,KAAK,KAAKC,OAAM,IAC3B,IAAI,KAAK,KAAK,KAAK,KAAK,GACxB,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAC7B,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAC7B,IAAI,EAAE,KAAK,IAAI,CAAC,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,IAAI;AAClF,SAAO;AAAA,IACL,GAAG,KAAK,KAAK,KAAK;AAAA,IAClB,GAAG,KAAK,KAAK,KAAK;AAAA,IAClB;AAAA,EACF;AACF;;;ACtHA,SAAS,MAAM,GAAGC,IAAGC,IAAG;AACtB,MAAI,KAAK,EAAE,IAAID,GAAE,GAAG,GAAGE,KACnB,KAAK,EAAE,IAAIF,GAAE,GAAG,GAAG,IACnB,KAAK,KAAK,KAAK,KAAK;AACxB,MAAI,IAAI;AACN,IAAAE,MAAKF,GAAE,IAAIC,GAAE,GAAGC,OAAMA;AACtB,SAAK,EAAE,IAAID,GAAE,GAAG,MAAM;AACtB,QAAIC,MAAK,IAAI;AACX,WAAK,KAAK,KAAKA,QAAO,IAAI;AAC1B,UAAI,KAAK,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC;AAC1C,MAAAD,GAAE,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI;AACzB,MAAAA,GAAE,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI;AAAA,IAC3B,OAAO;AACL,WAAK,KAAKC,MAAK,OAAO,IAAI;AAC1B,UAAI,KAAK,KAAK,KAAK,IAAI,GAAGA,MAAK,KAAK,IAAI,CAAC,CAAC;AAC1C,MAAAD,GAAE,IAAID,GAAE,IAAI,IAAI,KAAK,IAAI;AACzB,MAAAC,GAAE,IAAID,GAAE,IAAI,IAAI,KAAK,IAAI;AAAA,IAC3B;AAAA,EACF,OAAO;AACL,IAAAC,GAAE,IAAID,GAAE,IAAIC,GAAE;AACd,IAAAA,GAAE,IAAID,GAAE;AAAA,EACV;AACF;AAEA,SAAS,WAAWA,IAAG,GAAG;AACxB,MAAI,KAAKA,GAAE,IAAI,EAAE,IAAI,MAAM,KAAK,EAAE,IAAIA,GAAE,GAAG,KAAK,EAAE,IAAIA,GAAE;AACxD,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;AAC5C;AAEA,SAAS,MAAM,MAAM;AACnB,MAAIA,KAAI,KAAK,GACT,IAAI,KAAK,KAAK,GACd,KAAKA,GAAE,IAAI,EAAE,GACb,MAAMA,GAAE,IAAI,EAAE,IAAI,EAAE,IAAIA,GAAE,KAAK,IAC/B,MAAMA,GAAE,IAAI,EAAE,IAAI,EAAE,IAAIA,GAAE,KAAK;AACnC,SAAO,KAAK,KAAK,KAAK;AACxB;AAEA,SAASG,MAAK,QAAQ;AACpB,OAAK,IAAI;AACT,OAAK,OAAO;AACZ,OAAK,WAAW;AAClB;AAEO,SAAS,mBAAmB,SAAS,QAAQ;AAClD,MAAI,EAAE,KAAK,UAAU,cAAM,OAAO,GAAG,QAAS,QAAO;AAErD,MAAIH,IAAG,GAAGC,IAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI;AAGrC,EAAAD,KAAI,QAAQ,CAAC,GAAGA,GAAE,IAAI,GAAGA,GAAE,IAAI;AAC/B,MAAI,EAAE,IAAI,GAAI,QAAOA,GAAE;AAGvB,MAAI,QAAQ,CAAC,GAAGA,GAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAIA,GAAE,GAAG,EAAE,IAAI;AAC7C,MAAI,EAAE,IAAI,GAAI,QAAOA,GAAE,IAAI,EAAE;AAG7B,QAAM,GAAGA,IAAGC,KAAI,QAAQ,CAAC,CAAC;AAG1B,EAAAD,KAAI,IAAIG,MAAKH,EAAC,GAAG,IAAI,IAAIG,MAAK,CAAC,GAAGF,KAAI,IAAIE,MAAKF,EAAC;AAChD,EAAAD,GAAE,OAAOC,GAAE,WAAW;AACtB,IAAE,OAAOD,GAAE,WAAWC;AACtB,EAAAA,GAAE,OAAO,EAAE,WAAWD;AAGtB,OAAM,MAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC5B,UAAMA,GAAE,GAAG,EAAE,GAAGC,KAAI,QAAQ,CAAC,CAAC,GAAGA,KAAI,IAAIE,MAAKF,EAAC;AAK/C,QAAI,EAAE,MAAM,IAAID,GAAE,UAAU,KAAK,EAAE,EAAE,GAAG,KAAKA,GAAE,EAAE;AACjD,OAAG;AACD,UAAI,MAAM,IAAI;AACZ,YAAI,WAAW,EAAE,GAAGC,GAAE,CAAC,GAAG;AACxB,cAAI,GAAGD,GAAE,OAAO,GAAG,EAAE,WAAWA,IAAG,EAAE;AACrC,mBAAS;AAAA,QACX;AACA,cAAM,EAAE,EAAE,GAAG,IAAI,EAAE;AAAA,MACrB,OAAO;AACL,YAAI,WAAW,EAAE,GAAGC,GAAE,CAAC,GAAG;AACxB,UAAAD,KAAI,GAAGA,GAAE,OAAO,GAAG,EAAE,WAAWA,IAAG,EAAE;AACrC,mBAAS;AAAA,QACX;AACA,cAAM,EAAE,EAAE,GAAG,IAAI,EAAE;AAAA,MACrB;AAAA,IACF,SAAS,MAAM,EAAE;AAGjB,IAAAC,GAAE,WAAWD,IAAGC,GAAE,OAAO,GAAGD,GAAE,OAAO,EAAE,WAAW,IAAIC;AAGtD,SAAK,MAAMD,EAAC;AACZ,YAAQC,KAAIA,GAAE,UAAU,GAAG;AACzB,WAAK,KAAK,MAAMA,EAAC,KAAK,IAAI;AACxB,QAAAD,KAAIC,IAAG,KAAK;AAAA,MACd;AAAA,IACF;AACA,QAAID,GAAE;AAAA,EACR;AAGA,EAAAA,KAAI,CAAC,EAAE,CAAC,GAAGC,KAAI;AAAG,UAAQA,KAAIA,GAAE,UAAU,EAAG,CAAAD,GAAE,KAAKC,GAAE,CAAC;AAAG,EAAAA,KAAI,kBAAkBD,IAAG,MAAM;AAGzF,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAAA,KAAI,QAAQ,CAAC,GAAGA,GAAE,KAAKC,GAAE,GAAGD,GAAE,KAAKC,GAAE;AAE7D,SAAOA,GAAE;AACX;AAEe,SAAR,iBAAiB,SAAS;AAC/B,qBAAmB,SAAS,YAAI,CAAC;AACjC,SAAO;AACT;;;AClHA,SAAS,cAAc,GAAG;AACxB,SAAO,KAAK,KAAK,EAAE,KAAK;AAC1B;AAEe,SAAR,eAAmB;AACxB,MAAI,SAAS,MACT,KAAK,GACL,KAAK,GACL,UAAU;AAEd,WAAS,KAAK,MAAM;AAClB,UAAM,SAAS,YAAI;AACnB,SAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK;AAC/B,QAAI,QAAQ;AACV,WAAK,WAAW,WAAW,MAAM,CAAC,EAC7B,UAAU,mBAAmB,SAAS,KAAK,MAAM,CAAC,EAClD,WAAW,eAAe,CAAC,CAAC;AAAA,IACnC,OAAO;AACL,WAAK,WAAW,WAAW,aAAa,CAAC,EACpC,UAAU,mBAAmB,cAAc,GAAG,MAAM,CAAC,EACrD,UAAU,mBAAmB,SAAS,KAAK,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,EACxE,WAAW,eAAe,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,KAAK,EAAE,CAAC;AAAA,IACjE;AACA,WAAO;AAAA,EACT;AAEA,OAAK,SAAS,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,SAAS,SAAS,CAAC,GAAG,QAAQ;AAAA,EAC3D;AAEA,OAAK,OAAO,SAAS,GAAG;AACtB,WAAO,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,IAAI,EAAE;AAAA,EACpE;AAEA,OAAK,UAAU,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,UAAU,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,QAAQ;AAAA,EAC3F;AAEA,SAAO;AACT;AAEA,SAAS,WAAW,QAAQ;AAC1B,SAAO,SAAS,MAAM;AACpB,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,IAAI,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI,KAAK,CAAC;AAAA,IACzC;AAAA,EACF;AACF;AAEA,SAAS,mBAAmB,SAAS,GAAG,QAAQ;AAC9C,SAAO,SAAS,MAAM;AACpB,QAAI,WAAW,KAAK,UAAU;AAC5B,UAAI,UACA,GACA,IAAI,SAAS,QACb,IAAI,QAAQ,IAAI,IAAI,KAAK,GACzB;AAEJ,UAAI,EAAG,MAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,UAAS,CAAC,EAAE,KAAK;AAChD,UAAI,mBAAmB,UAAU,MAAM;AACvC,UAAI,EAAG,MAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,UAAS,CAAC,EAAE,KAAK;AAChD,WAAK,IAAI,IAAI;AAAA,IACf;AAAA,EACF;AACF;AAEA,SAAS,eAAe,GAAG;AACzB,SAAO,SAAS,MAAM;AACpB,QAAI,SAAS,KAAK;AAClB,SAAK,KAAK;AACV,QAAI,QAAQ;AACV,WAAK,IAAI,OAAO,IAAI,IAAI,KAAK;AAC7B,WAAK,IAAI,OAAO,IAAI,IAAI,KAAK;AAAA,IAC/B;AAAA,EACF;AACF;;;AChFe,SAAR,cAAiB,MAAM;AAC5B,OAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC5B,OAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC5B,OAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC5B,OAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC9B;;;ACLe,SAAR,aAAiB,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC9C,MAAI,QAAQ,OAAO,UACf,MACA,IAAI,IACJ,IAAI,MAAM,QACV,IAAI,OAAO,UAAU,KAAK,MAAM,OAAO;AAE3C,SAAO,EAAE,IAAI,GAAG;AACd,WAAO,MAAM,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AACzC,SAAK,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,QAAQ;AAAA,EAC7C;AACF;;;ACRe,SAAR,oBAAmB;AACxB,MAAI,KAAK,GACL,KAAK,GACL,UAAU,GACV,QAAQ;AAEZ,WAAS,UAAU,MAAM;AACvB,QAAI,IAAI,KAAK,SAAS;AACtB,SAAK,KACL,KAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK,KAAK;AACf,SAAK,WAAW,aAAa,IAAI,CAAC,CAAC;AACnC,QAAI,MAAO,MAAK,WAAW,aAAS;AACpC,WAAO;AAAA,EACT;AAEA,WAAS,aAAaG,KAAI,GAAG;AAC3B,WAAO,SAAS,MAAM;AACpB,UAAI,KAAK,UAAU;AACjB,qBAAY,MAAM,KAAK,IAAIA,OAAM,KAAK,QAAQ,KAAK,GAAG,KAAK,IAAIA,OAAM,KAAK,QAAQ,KAAK,CAAC;AAAA,MAC1F;AACA,UAAI,KAAK,KAAK,IACV,KAAK,KAAK,IACV,KAAK,KAAK,KAAK,SACf,KAAK,KAAK,KAAK;AACnB,UAAI,KAAK,GAAI,MAAK,MAAM,KAAK,MAAM;AACnC,UAAI,KAAK,GAAI,MAAK,MAAM,KAAK,MAAM;AACnC,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAEA,YAAU,QAAQ,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,QAAQ,CAAC,CAAC,GAAG,aAAa;AAAA,EACvD;AAEA,YAAU,OAAO,SAAS,GAAG;AAC3B,WAAO,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,IAAI,EAAE;AAAA,EACzE;AAEA,YAAU,UAAU,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,UAAU,CAAC,GAAG,aAAa;AAAA,EACxD;AAEA,SAAO;AACT;;;AChDA,IAAI,UAAU,EAAC,OAAO,GAAE;AAAxB,IACI,YAAY,CAAC;AADjB,IAEI,UAAU,CAAC;AAEf,SAAS,UAAU,GAAG;AACpB,SAAO,EAAE;AACX;AAEA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,EAAE;AACX;AAEe,SAAR,mBAAmB;AACxB,MAAI,KAAK,WACL,WAAW,iBACX;AAEJ,WAAS,SAAS,MAAM;AACtB,QAAI,QAAQ,MAAM,KAAK,IAAI,GACvB,YAAY,IACZ,kBAAkB,UAClB,GACA,GACA,GACA,MACA,QACA,MACA,QACA,SACA,YAAY,oBAAI;AAEpB,QAAI,QAAQ,MAAM;AAChB,YAAM,IAAI,MAAM,IAAI,CAACC,IAAGC,OAAM,UAAU,KAAKD,IAAGC,IAAG,IAAI,CAAC,CAAC;AACzD,YAAM,IAAI,EAAE,IAAI,QAAQ;AACxB,YAAM,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE;AAC3B,iBAAWA,MAAK,GAAG;AACjB,YAAI,CAAC,EAAE,IAAIA,EAAC,GAAG;AACb,YAAE,IAAIA,EAAC;AACP,YAAE,KAAKA,EAAC;AACR,YAAE,KAAK,SAASA,EAAC,CAAC;AAClB,gBAAM,KAAK,OAAO;AAAA,QACpB;AAAA,MACF;AACA,kBAAY,CAAC,GAAGA,OAAM,EAAEA,EAAC;AACzB,wBAAkB,CAAC,GAAGA,OAAM,EAAEA,EAAC;AAAA,IACjC;AAEA,SAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AACxC,UAAI,MAAM,CAAC,GAAG,OAAO,MAAM,CAAC,IAAI,IAAI,KAAK,CAAC;AAC1C,WAAK,SAAS,UAAU,GAAG,GAAG,IAAI,MAAM,SAAS,UAAU,KAAK;AAC9D,kBAAU,KAAK,KAAK;AACpB,kBAAU,IAAI,SAAS,UAAU,IAAI,OAAO,IAAI,YAAY,IAAI;AAAA,MAClE;AACA,WAAK,SAAS,gBAAgB,GAAG,GAAG,IAAI,MAAM,SAAS,UAAU,KAAK;AACpE,aAAK,SAAS;AAAA,MAChB;AAAA,IACF;AAEA,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,aAAO,MAAM,CAAC;AACd,UAAI,SAAS,KAAK,QAAQ;AACxB,iBAAS,UAAU,IAAI,MAAM;AAC7B,YAAI,CAAC,OAAQ,OAAM,IAAI,MAAM,cAAc,MAAM;AACjD,YAAI,WAAW,UAAW,OAAM,IAAI,MAAM,gBAAgB,MAAM;AAChE,YAAI,OAAO,SAAU,QAAO,SAAS,KAAK,IAAI;AAAA,YACzC,QAAO,WAAW,CAAC,IAAI;AAC5B,aAAK,SAAS;AAAA,MAChB,OAAO;AACL,YAAI,KAAM,OAAM,IAAI,MAAM,gBAAgB;AAC1C,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,CAAC,KAAM,OAAM,IAAI,MAAM,SAAS;AAIpC,QAAI,QAAQ,MAAM;AAChB,aAAO,KAAK,SAAS,WAAW,KAAK,SAAS,WAAW,GAAG;AAC1D,eAAO,KAAK,SAAS,CAAC,GAAG,EAAE;AAAA,MAC7B;AACA,eAASA,KAAI,MAAM,SAAS,GAAGA,MAAK,GAAG,EAAEA,IAAG;AAC1C,eAAO,MAAMA,EAAC;AACd,YAAI,KAAK,SAAS,QAAS;AAC3B,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAEA,SAAK,SAAS;AACd,SAAK,WAAW,SAASC,OAAM;AAAE,MAAAA,MAAK,QAAQA,MAAK,OAAO,QAAQ;AAAG,QAAE;AAAA,IAAG,CAAC,EAAE,WAAW,aAAa;AACrG,SAAK,SAAS;AACd,QAAI,IAAI,EAAG,OAAM,IAAI,MAAM,OAAO;AAElC,WAAO;AAAA,EACT;AAEA,WAAS,KAAK,SAAS,GAAG;AACxB,WAAO,UAAU,UAAU,KAAK,SAAS,CAAC,GAAG,YAAY;AAAA,EAC3D;AAEA,WAAS,WAAW,SAAS,GAAG;AAC9B,WAAO,UAAU,UAAU,WAAW,SAAS,CAAC,GAAG,YAAY;AAAA,EACjE;AAEA,WAAS,OAAO,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,OAAO,SAAS,CAAC,GAAG,YAAY;AAAA,EAC7D;AAEA,SAAO;AACT;AAKA,SAAS,UAAU,MAAM;AACvB,SAAO,GAAG,IAAI;AACd,MAAI,IAAI,KAAK;AACb,MAAI,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,MAAM,IAAI,CAAC,EAAG,QAAO,KAAK,MAAM,GAAG,EAAE;AACtE,SAAO,KAAK,CAAC,MAAM,MAAM,OAAO,IAAI,IAAI;AAC1C;AAKA,SAAS,SAAS,MAAM;AACtB,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,EAAG,QAAO;AAClB,SAAO,EAAE,IAAI,EAAG,KAAI,MAAM,MAAM,CAAC,EAAG;AACpC,SAAO,KAAK,MAAM,GAAG,CAAC;AACxB;AAKA,SAAS,MAAM,MAAM,GAAG;AACtB,MAAI,KAAK,CAAC,MAAM,KAAK;AACnB,QAAI,IAAI;AACR,WAAO,IAAI,KAAK,KAAK,EAAE,CAAC,MAAM,KAAM,GAAE;AACtC,SAAK,IAAI,OAAO,EAAG,QAAO;AAAA,EAC5B;AACA,SAAO;AACT;;;AC9IA,SAASC,mBAAkBC,IAAG,GAAG;AAC/B,SAAOA,GAAE,WAAW,EAAE,SAAS,IAAI;AACrC;AAUA,SAAS,SAAS,GAAG;AACnB,MAAI,WAAW,EAAE;AACjB,SAAO,WAAW,SAAS,CAAC,IAAI,EAAE;AACpC;AAGA,SAAS,UAAU,GAAG;AACpB,MAAI,WAAW,EAAE;AACjB,SAAO,WAAW,SAAS,SAAS,SAAS,CAAC,IAAI,EAAE;AACtD;AAIA,SAAS,YAAY,IAAI,IAAI,OAAO;AAClC,MAAI,SAAS,SAAS,GAAG,IAAI,GAAG;AAChC,KAAG,KAAK;AACR,KAAG,KAAK;AACR,KAAG,KAAK;AACR,KAAG,KAAK;AACR,KAAG,KAAK;AACV;AAKA,SAAS,cAAc,GAAG;AACxB,MAAI,QAAQ,GACR,SAAS,GACT,WAAW,EAAE,UACb,IAAI,SAAS,QACb;AACJ,SAAO,EAAE,KAAK,GAAG;AACf,QAAI,SAAS,CAAC;AACd,MAAE,KAAK;AACP,MAAE,KAAK;AACP,aAAS,EAAE,KAAK,UAAU,EAAE;AAAA,EAC9B;AACF;AAIA,SAAS,aAAa,KAAK,GAAG,UAAU;AACtC,SAAO,IAAI,EAAE,WAAW,EAAE,SAAS,IAAI,IAAI;AAC7C;AAEA,SAAS,SAAS,MAAM,GAAG;AACzB,OAAK,IAAI;AACT,OAAK,SAAS;AACd,OAAK,WAAW;AAChB,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,IAAI;AACX;AAEA,SAAS,YAAY,OAAO,OAAO,KAAK,SAAS;AAEjD,SAAS,SAAS,MAAM;AACtB,MAAI,OAAO,IAAI,SAAS,MAAM,CAAC,GAC3B,MACA,QAAQ,CAAC,IAAI,GACb,OACA,UACA,GACA;AAEJ,SAAO,OAAO,MAAM,IAAI,GAAG;AACzB,QAAI,WAAW,KAAK,EAAE,UAAU;AAC9B,WAAK,WAAW,IAAI,MAAM,IAAI,SAAS,MAAM;AAC7C,WAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,GAAG;AAC3B,cAAM,KAAK,QAAQ,KAAK,SAAS,CAAC,IAAI,IAAI,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC;AAClE,cAAM,SAAS;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAEA,GAAC,KAAK,SAAS,IAAI,SAAS,MAAM,CAAC,GAAG,WAAW,CAAC,IAAI;AACtD,SAAO;AACT;AAGe,SAAR,eAAmB;AACxB,MAAI,aAAaD,oBACb,KAAK,GACL,KAAK,GACL,WAAW;AAEf,WAAS,KAAK,MAAM;AAClB,QAAI,IAAI,SAAS,IAAI;AAGrB,MAAE,UAAU,SAAS,GAAG,EAAE,OAAO,IAAI,CAAC,EAAE;AACxC,MAAE,WAAW,UAAU;AAGvB,QAAI,SAAU,MAAK,WAAW,QAAQ;AAAA,SAIjC;AACH,UAAI,OAAO,MACP,QAAQ,MACR,SAAS;AACb,WAAK,WAAW,SAAS,MAAM;AAC7B,YAAI,KAAK,IAAI,KAAK,EAAG,QAAO;AAC5B,YAAI,KAAK,IAAI,MAAM,EAAG,SAAQ;AAC9B,YAAI,KAAK,QAAQ,OAAO,MAAO,UAAS;AAAA,MAC1C,CAAC;AACD,UAAI,IAAI,SAAS,QAAQ,IAAI,WAAW,MAAM,KAAK,IAAI,GACnD,KAAK,IAAI,KAAK,GACd,KAAK,MAAM,MAAM,IAAI,IAAI,KACzB,KAAK,MAAM,OAAO,SAAS;AAC/B,WAAK,WAAW,SAAS,MAAM;AAC7B,aAAK,KAAK,KAAK,IAAI,MAAM;AACzB,aAAK,IAAI,KAAK,QAAQ;AAAA,MACxB,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AAMA,WAAS,UAAU,GAAG;AACpB,QAAI,WAAW,EAAE,UACb,WAAW,EAAE,OAAO,UACpB,IAAI,EAAE,IAAI,SAAS,EAAE,IAAI,CAAC,IAAI;AAClC,QAAI,UAAU;AACZ,oBAAc,CAAC;AACf,UAAI,YAAY,SAAS,CAAC,EAAE,IAAI,SAAS,SAAS,SAAS,CAAC,EAAE,KAAK;AACnE,UAAI,GAAG;AACL,UAAE,IAAI,EAAE,IAAI,WAAW,EAAE,GAAG,EAAE,CAAC;AAC/B,UAAE,IAAI,EAAE,IAAI;AAAA,MACd,OAAO;AACL,UAAE,IAAI;AAAA,MACR;AAAA,IACF,WAAW,GAAG;AACZ,QAAE,IAAI,EAAE,IAAI,WAAW,EAAE,GAAG,EAAE,CAAC;AAAA,IACjC;AACA,MAAE,OAAO,IAAI,UAAU,GAAG,GAAG,EAAE,OAAO,KAAK,SAAS,CAAC,CAAC;AAAA,EACxD;AAGA,WAAS,WAAW,GAAG;AACrB,MAAE,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO;AACvB,MAAE,KAAK,EAAE,OAAO;AAAA,EAClB;AAaA,WAAS,UAAU,GAAG,GAAG,UAAU;AACjC,QAAI,GAAG;AACL,UAAI,MAAM,GACN,MAAM,GACN,MAAM,GACN,MAAM,IAAI,OAAO,SAAS,CAAC,GAC3B,MAAM,IAAI,GACV,MAAM,IAAI,GACV,MAAM,IAAI,GACV,MAAM,IAAI,GACV;AACJ,aAAO,MAAM,UAAU,GAAG,GAAG,MAAM,SAAS,GAAG,GAAG,OAAO,KAAK;AAC5D,cAAM,SAAS,GAAG;AAClB,cAAM,UAAU,GAAG;AACnB,YAAI,IAAI;AACR,gBAAQ,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,WAAW,IAAI,GAAG,IAAI,CAAC;AAC3D,YAAI,QAAQ,GAAG;AACb,sBAAY,aAAa,KAAK,GAAG,QAAQ,GAAG,GAAG,KAAK;AACpD,iBAAO;AACP,iBAAO;AAAA,QACT;AACA,eAAO,IAAI;AACX,eAAO,IAAI;AACX,eAAO,IAAI;AACX,eAAO,IAAI;AAAA,MACb;AACA,UAAI,OAAO,CAAC,UAAU,GAAG,GAAG;AAC1B,YAAI,IAAI;AACR,YAAI,KAAK,MAAM;AAAA,MACjB;AACA,UAAI,OAAO,CAAC,SAAS,GAAG,GAAG;AACzB,YAAI,IAAI;AACR,YAAI,KAAK,MAAM;AACf,mBAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,WAAS,SAAS,MAAM;AACtB,SAAK,KAAK;AACV,SAAK,IAAI,KAAK,QAAQ;AAAA,EACxB;AAEA,OAAK,aAAa,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,aAAa,GAAG,QAAQ;AAAA,EACrD;AAEA,OAAK,OAAO,SAAS,GAAG;AACtB,WAAO,UAAU,UAAU,WAAW,OAAO,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,QAAS,WAAW,OAAO,CAAC,IAAI,EAAE;AAAA,EACzG;AAEA,OAAK,WAAW,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,WAAW,MAAM,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,QAAS,WAAW,CAAC,IAAI,EAAE,IAAI;AAAA,EACrG;AAEA,SAAO;AACT;;;AC5Oe,SAAR,cAAiB,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC9C,MAAI,QAAQ,OAAO,UACf,MACA,IAAI,IACJ,IAAI,MAAM,QACV,IAAI,OAAO,UAAU,KAAK,MAAM,OAAO;AAE3C,SAAO,EAAE,IAAI,GAAG;AACd,WAAO,MAAM,CAAC,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AACzC,SAAK,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,QAAQ;AAAA,EAC7C;AACF;;;ACRO,IAAI,OAAO,IAAI,KAAK,KAAK,CAAC,KAAK;AAE/B,SAAS,cAAc,OAAO,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC3D,MAAI,OAAO,CAAC,GACR,QAAQ,OAAO,UACf,KACA,WACA,KAAK,GACL,KAAK,GACL,IAAI,MAAM,QACV,IAAI,IACJ,QAAQ,OAAO,OACf,UACA,UACA,UACA,UACA,UACA,OACA;AAEJ,SAAO,KAAK,GAAG;AACb,SAAK,KAAK,IAAI,KAAK,KAAK;AAGxB;AAAG,iBAAW,MAAM,IAAI,EAAE;AAAA,WAAc,CAAC,YAAY,KAAK;AAC1D,eAAW,WAAW;AACtB,YAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,KAAK,QAAQ;AAC9C,WAAO,WAAW,WAAW;AAC7B,eAAW,KAAK,IAAI,WAAW,MAAM,OAAO,QAAQ;AAGpD,WAAO,KAAK,GAAG,EAAE,IAAI;AACnB,kBAAY,YAAY,MAAM,EAAE,EAAE;AAClC,UAAI,YAAY,SAAU,YAAW;AACrC,UAAI,YAAY,SAAU,YAAW;AACrC,aAAO,WAAW,WAAW;AAC7B,iBAAW,KAAK,IAAI,WAAW,MAAM,OAAO,QAAQ;AACpD,UAAI,WAAW,UAAU;AAAE,oBAAY;AAAW;AAAA,MAAO;AACzD,iBAAW;AAAA,IACb;AAGA,SAAK,KAAK,MAAM,EAAC,OAAO,UAAU,MAAM,KAAK,IAAI,UAAU,MAAM,MAAM,IAAI,EAAE,EAAC,CAAC;AAC/E,QAAI,IAAI,KAAM,cAAY,KAAK,IAAI,IAAI,IAAI,QAAQ,MAAM,KAAK,WAAW,QAAQ,EAAE;AAAA,QAC9E,eAAa,KAAK,IAAI,IAAI,QAAQ,MAAM,KAAK,WAAW,QAAQ,IAAI,EAAE;AAC3E,aAAS,UAAU,KAAK;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,IAAO,mBAAS,SAAS,OAAO,OAAO;AAErC,WAAS,SAAS,QAAQ,IAAI,IAAI,IAAI,IAAI;AACxC,kBAAc,OAAO,QAAQ,IAAI,IAAI,IAAI,EAAE;AAAA,EAC7C;AAEA,WAAS,QAAQ,SAAS,GAAG;AAC3B,WAAO,QAAQ,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EACpC;AAEA,SAAO;AACT,EAAG,GAAG;;;AC5DS,SAAR,kBAAmB;AACxB,MAAI,OAAO,kBACP,QAAQ,OACR,KAAK,GACL,KAAK,GACL,eAAe,CAAC,CAAC,GACjB,eAAe,cACf,aAAa,cACb,eAAe,cACf,gBAAgB,cAChB,cAAc;AAElB,WAAS,QAAQ,MAAM;AACrB,SAAK,KACL,KAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,WAAW,YAAY;AAC5B,mBAAe,CAAC,CAAC;AACjB,QAAI,MAAO,MAAK,WAAW,aAAS;AACpC,WAAO;AAAA,EACT;AAEA,WAAS,aAAa,MAAM;AAC1B,QAAI,IAAI,aAAa,KAAK,KAAK,GAC3B,KAAK,KAAK,KAAK,GACf,KAAK,KAAK,KAAK,GACf,KAAK,KAAK,KAAK,GACf,KAAK,KAAK,KAAK;AACnB,QAAI,KAAK,GAAI,MAAK,MAAM,KAAK,MAAM;AACnC,QAAI,KAAK,GAAI,MAAK,MAAM,KAAK,MAAM;AACnC,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,KAAK;AACV,QAAI,KAAK,UAAU;AACjB,UAAI,aAAa,KAAK,QAAQ,CAAC,IAAI,aAAa,IAAI,IAAI;AACxD,YAAM,YAAY,IAAI,IAAI;AAC1B,YAAM,WAAW,IAAI,IAAI;AACzB,YAAM,aAAa,IAAI,IAAI;AAC3B,YAAM,cAAc,IAAI,IAAI;AAC5B,UAAI,KAAK,GAAI,MAAK,MAAM,KAAK,MAAM;AACnC,UAAI,KAAK,GAAI,MAAK,MAAM,KAAK,MAAM;AACnC,WAAK,MAAM,IAAI,IAAI,IAAI,EAAE;AAAA,IAC3B;AAAA,EACF;AAEA,UAAQ,QAAQ,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,QAAQ,CAAC,CAAC,GAAG,WAAW;AAAA,EACrD;AAEA,UAAQ,OAAO,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,IAAI,EAAE;AAAA,EACvE;AAEA,UAAQ,OAAO,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,OAAO,SAAS,CAAC,GAAG,WAAW;AAAA,EAC5D;AAEA,UAAQ,UAAU,SAAS,GAAG;AAC5B,WAAO,UAAU,SAAS,QAAQ,aAAa,CAAC,EAAE,aAAa,CAAC,IAAI,QAAQ,aAAa;AAAA,EAC3F;AAEA,UAAQ,eAAe,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,eAAe,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,WAAW;AAAA,EACnG;AAEA,UAAQ,eAAe,SAAS,GAAG;AACjC,WAAO,UAAU,SAAS,QAAQ,WAAW,CAAC,EAAE,aAAa,CAAC,EAAE,cAAc,CAAC,EAAE,YAAY,CAAC,IAAI,QAAQ,WAAW;AAAA,EACvH;AAEA,UAAQ,aAAa,SAAS,GAAG;AAC/B,WAAO,UAAU,UAAU,aAAa,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,WAAW;AAAA,EACjG;AAEA,UAAQ,eAAe,SAAS,GAAG;AACjC,WAAO,UAAU,UAAU,eAAe,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,WAAW;AAAA,EACnG;AAEA,UAAQ,gBAAgB,SAAS,GAAG;AAClC,WAAO,UAAU,UAAU,gBAAgB,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,WAAW;AAAA,EACpG;AAEA,UAAQ,cAAc,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,cAAc,OAAO,MAAM,aAAa,IAAI,iBAAS,CAAC,CAAC,GAAG,WAAW;AAAA,EAClG;AAEA,SAAO;AACT;;;AC7Fe,SAAR,eAAiB,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC9C,MAAI,QAAQ,OAAO,UACf,GAAG,IAAI,MAAM,QACb,KAAK,OAAO,IAAI,MAAM,IAAI,CAAC;AAE/B,OAAK,KAAK,CAAC,IAAI,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtC,SAAK,IAAI,CAAC,IAAI,OAAO,MAAM,CAAC,EAAE;AAAA,EAChC;AAEA,YAAU,GAAG,GAAG,OAAO,OAAO,IAAI,IAAI,IAAI,EAAE;AAE5C,WAAS,UAAUE,IAAG,GAAG,OAAOC,KAAIC,KAAIC,KAAIC,KAAI;AAC9C,QAAIJ,MAAK,IAAI,GAAG;AACd,UAAI,OAAO,MAAMA,EAAC;AAClB,WAAK,KAAKC,KAAI,KAAK,KAAKC;AACxB,WAAK,KAAKC,KAAI,KAAK,KAAKC;AACxB;AAAA,IACF;AAEA,QAAI,cAAc,KAAKJ,EAAC,GACpB,cAAe,QAAQ,IAAK,aAC5B,IAAIA,KAAI,GACR,KAAK,IAAI;AAEb,WAAO,IAAI,IAAI;AACb,UAAI,MAAM,IAAI,OAAO;AACrB,UAAI,KAAK,GAAG,IAAI,YAAa,KAAI,MAAM;AAAA,UAClC,MAAK;AAAA,IACZ;AAEA,QAAK,cAAc,KAAK,IAAI,CAAC,IAAM,KAAK,CAAC,IAAI,eAAgBA,KAAI,IAAI,EAAG,GAAE;AAE1E,QAAI,YAAY,KAAK,CAAC,IAAI,aACtB,aAAa,QAAQ;AAEzB,QAAKG,MAAKF,MAAOG,MAAKF,KAAK;AACzB,UAAI,KAAK,SAASD,MAAK,aAAaE,MAAK,aAAa,QAAQA;AAC9D,gBAAUH,IAAG,GAAG,WAAWC,KAAIC,KAAI,IAAIE,GAAE;AACzC,gBAAU,GAAG,GAAG,YAAY,IAAIF,KAAIC,KAAIC,GAAE;AAAA,IAC5C,OAAO;AACL,UAAI,KAAK,SAASF,MAAK,aAAaE,MAAK,aAAa,QAAQA;AAC9D,gBAAUJ,IAAG,GAAG,WAAWC,KAAIC,KAAIC,KAAI,EAAE;AACzC,gBAAU,GAAG,GAAG,YAAYF,KAAI,IAAIE,KAAIC,GAAE;AAAA,IAC5C;AAAA,EACF;AACF;;;AC1Ce,SAAR,kBAAiB,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC9C,GAAC,OAAO,QAAQ,IAAI,gBAAQ,cAAM,QAAQ,IAAI,IAAI,IAAI,EAAE;AAC1D;;;ACDA,IAAO,qBAAS,SAASC,QAAO,OAAO;AAErC,WAAS,WAAW,QAAQ,IAAI,IAAI,IAAI,IAAI;AAC1C,SAAK,OAAO,OAAO,cAAe,KAAK,UAAU,OAAQ;AACvD,UAAI,MACA,KACA,OACA,GACA,IAAI,IACJ,GACAC,KAAI,KAAK,QACT,QAAQ,OAAO;AAEnB,aAAO,EAAE,IAAIA,IAAG;AACd,cAAM,KAAK,CAAC,GAAG,QAAQ,IAAI;AAC3B,aAAK,IAAI,IAAI,QAAQ,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,EAAG,KAAI,SAAS,MAAM,CAAC,EAAE;AAC5E,YAAI,IAAI,KAAM,cAAY,KAAK,IAAI,IAAI,IAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,QAAQ,QAAQ,EAAE;AAAA,YACtF,eAAa,KAAK,IAAI,IAAI,QAAQ,OAAO,KAAK,MAAM,IAAI,QAAQ,QAAQ,IAAI,EAAE;AACnF,iBAAS,IAAI;AAAA,MACf;AAAA,IACF,OAAO;AACL,aAAO,YAAY,OAAO,cAAc,OAAO,QAAQ,IAAI,IAAI,IAAI,EAAE;AACrE,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAEA,aAAW,QAAQ,SAAS,GAAG;AAC7B,WAAOD,SAAQ,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;AAAA,EACpC;AAEA,SAAO;AACT,EAAG,GAAG;", "names": ["a", "c", "a", "c", "m", "a", "c", "a2", "c2", "a", "c", "a2", "Node", "dy", "d", "i", "node", "defaultSeparation", "a", "i", "x0", "y0", "x1", "y1", "custom", "m"]}