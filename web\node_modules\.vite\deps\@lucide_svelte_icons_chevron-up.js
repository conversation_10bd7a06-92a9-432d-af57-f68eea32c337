import {
  Icon_default
} from "./chunk-6AKCZ4LB.js";
import "./chunk-C5KNTEDU.js";
import {
  check_target,
  hmr,
  legacy_api,
  rest_props,
  snippet,
  spread_props,
  wrap_snippet
} from "./chunk-CRCQ7E27.js";
import "./chunk-U7P2NEEE.js";
import {
  append,
  comment
} from "./chunk-OSNF6FE7.js";
import {
  FILENAME,
  HMR,
  first_child,
  noop,
  pop,
  push,
  set
} from "./chunk-PJ2X7CWE.js";
import "./chunk-VIZMNZTH.js";
import "./chunk-HNWPC2PS.js";
import "./chunk-RVAV4ZRS.js";
import "./chunk-KWPVD4H7.js";

// node_modules/@lucide/svelte/dist/icons/chevron-up.svelte
Chevron_up[FILENAME] = "node_modules/@lucide/svelte/dist/icons/chevron-up.svelte";
function Chevron_up($$anchor, $$props) {
  check_target(new.target);
  push($$props, true, Chevron_up);
  let props = rest_props($$props, ["$$slots", "$$events", "$$legacy"], "props");
  const iconNode = [["path", { "d": "m18 15-6-6-6 6" }]];
  var fragment = comment();
  var node = first_child(fragment);
  Icon_default(node, spread_props({ name: "chevron-up" }, () => props, {
    iconNode,
    children: wrap_snippet(Chevron_up, ($$anchor2, $$slotProps) => {
      var fragment_1 = comment();
      var node_1 = first_child(fragment_1);
      snippet(node_1, () => $$props.children ?? noop);
      append($$anchor2, fragment_1);
    }),
    $$slots: { default: true }
  }));
  append($$anchor, fragment);
  return pop({ ...legacy_api() });
}
if (import.meta.hot) {
  Chevron_up = hmr(Chevron_up, () => Chevron_up[HMR].source);
  import.meta.hot.accept((module) => {
    module.default[HMR].source = Chevron_up[HMR].source;
    set(Chevron_up[HMR].source, module.default[HMR].original);
  });
}
var chevron_up_default = Chevron_up;
export {
  chevron_up_default as default
};
/*! Bundled license information:

@lucide/svelte/dist/icons/chevron-up.svelte:
  (**
   * @license @lucide/svelte v0.482.0 - ISC
   *
   * ISC License
   *
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   *
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   *
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   *
   *)

@lucide/svelte/dist/icons/chevron-up.js:
  (**
   * @license @lucide/svelte v0.482.0 - ISC
   *
   * ISC License
   * 
   * Copyright (c) for portions of Lucide are held by Cole Bemis 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.
   * 
   * Permission to use, copy, modify, and/or distribute this software for any
   * purpose with or without fee is hereby granted, provided that the above
   * copyright notice and this permission notice appear in all copies.
   * 
   * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
   * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
   * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
   * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
   * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
   * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
   * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
   * 
   *)
*/
//# sourceMappingURL=@lucide_svelte_icons_chevron-up.js.map
