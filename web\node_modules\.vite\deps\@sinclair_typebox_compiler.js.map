{"version": 3, "sources": ["../../@sinclair/typebox/build/esm/system/system.mjs", "../../@sinclair/typebox/build/esm/errors/function.mjs", "../../@sinclair/typebox/build/esm/value/deref/deref.mjs", "../../@sinclair/typebox/build/esm/value/hash/hash.mjs", "../../@sinclair/typebox/build/esm/value/check/check.mjs", "../../@sinclair/typebox/build/esm/errors/errors.mjs", "../../@sinclair/typebox/build/esm/value/transform/decode.mjs", "../../@sinclair/typebox/build/esm/value/transform/encode.mjs", "../../@sinclair/typebox/build/esm/value/transform/has.mjs", "../../@sinclair/typebox/build/esm/compiler/compiler.mjs"], "sourcesContent": ["import { TypeRegistry, FormatRegistry } from '../type/registry/index.mjs';\nimport { Unsafe } from '../type/unsafe/index.mjs';\nimport { Kind } from '../type/symbols/index.mjs';\nimport { TypeBoxError } from '../type/error/index.mjs';\n// ------------------------------------------------------------------\n// Errors\n// ------------------------------------------------------------------\nexport class TypeSystemDuplicateTypeKind extends TypeBoxError {\n    constructor(kind) {\n        super(`Duplicate type kind '${kind}' detected`);\n    }\n}\nexport class TypeSystemDuplicateFormat extends TypeBoxError {\n    constructor(kind) {\n        super(`Duplicate string format '${kind}' detected`);\n    }\n}\n/** Creates user defined types and formats and provides overrides for value checking behaviours */\nexport var TypeSystem;\n(function (TypeSystem) {\n    /** Creates a new type */\n    function Type(kind, check) {\n        if (TypeRegistry.Has(kind))\n            throw new TypeSystemDuplicateTypeKind(kind);\n        TypeRegistry.Set(kind, check);\n        return (options = {}) => Unsafe({ ...options, [Kind]: kind });\n    }\n    TypeSystem.Type = Type;\n    /** Creates a new string format */\n    function Format(format, check) {\n        if (FormatRegistry.Has(format))\n            throw new TypeSystemDuplicateFormat(format);\n        FormatRegistry.Set(format, check);\n        return format;\n    }\n    TypeSystem.Format = Format;\n})(TypeSystem || (TypeSystem = {}));\n", "import { Kind } from '../type/symbols/index.mjs';\nimport { ValueErrorType } from './errors.mjs';\n/** Creates an error message using en-US as the default locale */\nexport function DefaultErrorFunction(error) {\n    switch (error.errorType) {\n        case ValueErrorType.ArrayContains:\n            return 'Expected array to contain at least one matching value';\n        case ValueErrorType.ArrayMaxContains:\n            return `Expected array to contain no more than ${error.schema.maxContains} matching values`;\n        case ValueErrorType.ArrayMinContains:\n            return `Expected array to contain at least ${error.schema.minContains} matching values`;\n        case ValueErrorType.ArrayMaxItems:\n            return `Expected array length to be less or equal to ${error.schema.maxItems}`;\n        case ValueErrorType.ArrayMinItems:\n            return `Expected array length to be greater or equal to ${error.schema.minItems}`;\n        case ValueErrorType.ArrayUniqueItems:\n            return 'Expected array elements to be unique';\n        case ValueErrorType.Array:\n            return 'Expected array';\n        case ValueErrorType.AsyncIterator:\n            return 'Expected AsyncIterator';\n        case ValueErrorType.BigIntExclusiveMaximum:\n            return `Expected bigint to be less than ${error.schema.exclusiveMaximum}`;\n        case ValueErrorType.BigIntExclusiveMinimum:\n            return `Expected bigint to be greater than ${error.schema.exclusiveMinimum}`;\n        case ValueErrorType.BigIntMaximum:\n            return `Expected bigint to be less or equal to ${error.schema.maximum}`;\n        case ValueErrorType.BigIntMinimum:\n            return `Expected bigint to be greater or equal to ${error.schema.minimum}`;\n        case ValueErrorType.BigIntMultipleOf:\n            return `Expected bigint to be a multiple of ${error.schema.multipleOf}`;\n        case ValueErrorType.BigInt:\n            return 'Expected bigint';\n        case ValueErrorType.Boolean:\n            return 'Expected boolean';\n        case ValueErrorType.DateExclusiveMinimumTimestamp:\n            return `Expected Date timestamp to be greater than ${error.schema.exclusiveMinimumTimestamp}`;\n        case ValueErrorType.DateExclusiveMaximumTimestamp:\n            return `Expected Date timestamp to be less than ${error.schema.exclusiveMaximumTimestamp}`;\n        case ValueErrorType.DateMinimumTimestamp:\n            return `Expected Date timestamp to be greater or equal to ${error.schema.minimumTimestamp}`;\n        case ValueErrorType.DateMaximumTimestamp:\n            return `Expected Date timestamp to be less or equal to ${error.schema.maximumTimestamp}`;\n        case ValueErrorType.DateMultipleOfTimestamp:\n            return `Expected Date timestamp to be a multiple of ${error.schema.multipleOfTimestamp}`;\n        case ValueErrorType.Date:\n            return 'Expected Date';\n        case ValueErrorType.Function:\n            return 'Expected function';\n        case ValueErrorType.IntegerExclusiveMaximum:\n            return `Expected integer to be less than ${error.schema.exclusiveMaximum}`;\n        case ValueErrorType.IntegerExclusiveMinimum:\n            return `Expected integer to be greater than ${error.schema.exclusiveMinimum}`;\n        case ValueErrorType.IntegerMaximum:\n            return `Expected integer to be less or equal to ${error.schema.maximum}`;\n        case ValueErrorType.IntegerMinimum:\n            return `Expected integer to be greater or equal to ${error.schema.minimum}`;\n        case ValueErrorType.IntegerMultipleOf:\n            return `Expected integer to be a multiple of ${error.schema.multipleOf}`;\n        case ValueErrorType.Integer:\n            return 'Expected integer';\n        case ValueErrorType.IntersectUnevaluatedProperties:\n            return 'Unexpected property';\n        case ValueErrorType.Intersect:\n            return 'Expected all values to match';\n        case ValueErrorType.Iterator:\n            return 'Expected Iterator';\n        case ValueErrorType.Literal:\n            return `Expected ${typeof error.schema.const === 'string' ? `'${error.schema.const}'` : error.schema.const}`;\n        case ValueErrorType.Never:\n            return 'Never';\n        case ValueErrorType.Not:\n            return 'Value should not match';\n        case ValueErrorType.Null:\n            return 'Expected null';\n        case ValueErrorType.NumberExclusiveMaximum:\n            return `Expected number to be less than ${error.schema.exclusiveMaximum}`;\n        case ValueErrorType.NumberExclusiveMinimum:\n            return `Expected number to be greater than ${error.schema.exclusiveMinimum}`;\n        case ValueErrorType.NumberMaximum:\n            return `Expected number to be less or equal to ${error.schema.maximum}`;\n        case ValueErrorType.NumberMinimum:\n            return `Expected number to be greater or equal to ${error.schema.minimum}`;\n        case ValueErrorType.NumberMultipleOf:\n            return `Expected number to be a multiple of ${error.schema.multipleOf}`;\n        case ValueErrorType.Number:\n            return 'Expected number';\n        case ValueErrorType.Object:\n            return 'Expected object';\n        case ValueErrorType.ObjectAdditionalProperties:\n            return 'Unexpected property';\n        case ValueErrorType.ObjectMaxProperties:\n            return `Expected object to have no more than ${error.schema.maxProperties} properties`;\n        case ValueErrorType.ObjectMinProperties:\n            return `Expected object to have at least ${error.schema.minProperties} properties`;\n        case ValueErrorType.ObjectRequiredProperty:\n            return 'Expected required property';\n        case ValueErrorType.Promise:\n            return 'Expected Promise';\n        case ValueErrorType.RegExp:\n            return 'Expected string to match regular expression';\n        case ValueErrorType.StringFormatUnknown:\n            return `Unknown format '${error.schema.format}'`;\n        case ValueErrorType.StringFormat:\n            return `Expected string to match '${error.schema.format}' format`;\n        case ValueErrorType.StringMaxLength:\n            return `Expected string length less or equal to ${error.schema.maxLength}`;\n        case ValueErrorType.StringMinLength:\n            return `Expected string length greater or equal to ${error.schema.minLength}`;\n        case ValueErrorType.StringPattern:\n            return `Expected string to match '${error.schema.pattern}'`;\n        case ValueErrorType.String:\n            return 'Expected string';\n        case ValueErrorType.Symbol:\n            return 'Expected symbol';\n        case ValueErrorType.TupleLength:\n            return `Expected tuple to have ${error.schema.maxItems || 0} elements`;\n        case ValueErrorType.Tuple:\n            return 'Expected tuple';\n        case ValueErrorType.Uint8ArrayMaxByteLength:\n            return `Expected byte length less or equal to ${error.schema.maxByteLength}`;\n        case ValueErrorType.Uint8ArrayMinByteLength:\n            return `Expected byte length greater or equal to ${error.schema.minByteLength}`;\n        case ValueErrorType.Uint8Array:\n            return 'Expected Uint8Array';\n        case ValueErrorType.Undefined:\n            return 'Expected undefined';\n        case ValueErrorType.Union:\n            return 'Expected union value';\n        case ValueErrorType.Void:\n            return 'Expected void';\n        case ValueErrorType.Kind:\n            return `Expected kind '${error.schema[Kind]}'`;\n        default:\n            return 'Unknown error type';\n    }\n}\n/** Manages error message providers */\nlet errorFunction = DefaultErrorFunction;\n/** Sets the error function used to generate error messages. */\nexport function SetErrorFunction(callback) {\n    errorFunction = callback;\n}\n/** Gets the error function used to generate error messages */\nexport function GetErrorFunction() {\n    return errorFunction;\n}\n", "import { TypeBoxError } from '../../type/error/index.mjs';\nimport { Kind } from '../../type/symbols/index.mjs';\nimport { IsString } from '../guard/guard.mjs';\nexport class TypeDereferenceError extends TypeBoxError {\n    constructor(schema) {\n        super(`Unable to dereference schema with $id '${schema.$ref}'`);\n        this.schema = schema;\n    }\n}\nfunction Resolve(schema, references) {\n    const target = references.find((target) => target.$id === schema.$ref);\n    if (target === undefined)\n        throw new TypeDereferenceError(schema);\n    return Deref(target, references);\n}\n/** `[Internal]` Pushes a schema onto references if the schema has an $id and does not exist on references */\nexport function Pushref(schema, references) {\n    if (!IsString(schema.$id) || references.some((target) => target.$id === schema.$id))\n        return references;\n    references.push(schema);\n    return references;\n}\n/** `[Internal]` Dereferences a schema from the references array or throws if not found */\nexport function Deref(schema, references) {\n    // prettier-ignore\n    return (schema[Kind] === 'This' || schema[Kind] === 'Ref')\n        ? Resolve(schema, references)\n        : schema;\n}\n", "import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ull, Is<PERSON><PERSON>ber, IsObject, IsString, IsSymbol, IsUint8Array, IsUndefined } from '../guard/index.mjs';\nimport { TypeBoxError } from '../../type/error/index.mjs';\n// ------------------------------------------------------------------\n// Errors\n// ------------------------------------------------------------------\nexport class ValueHashError extends TypeBoxError {\n    constructor(value) {\n        super(`Unable to hash value`);\n        this.value = value;\n    }\n}\n// ------------------------------------------------------------------\n// ByteMarker\n// ------------------------------------------------------------------\nvar ByteMarker;\n(function (ByteMarker) {\n    ByteMarker[ByteMarker[\"Undefined\"] = 0] = \"Undefined\";\n    ByteMarker[ByteMarker[\"Null\"] = 1] = \"Null\";\n    ByteMarker[ByteMarker[\"Boolean\"] = 2] = \"Boolean\";\n    ByteMarker[ByteMarker[\"Number\"] = 3] = \"Number\";\n    ByteMarker[ByteMarker[\"String\"] = 4] = \"String\";\n    ByteMarker[ByteMarker[\"Object\"] = 5] = \"Object\";\n    ByteMarker[ByteMarker[\"Array\"] = 6] = \"Array\";\n    ByteMarker[ByteMarker[\"Date\"] = 7] = \"Date\";\n    ByteMarker[ByteMarker[\"Uint8Array\"] = 8] = \"Uint8Array\";\n    ByteMarker[ByteMarker[\"Symbol\"] = 9] = \"Symbol\";\n    ByteMarker[ByteMarker[\"BigInt\"] = 10] = \"BigInt\";\n})(ByteMarker || (ByteMarker = {}));\n// ------------------------------------------------------------------\n// State\n// ------------------------------------------------------------------\nlet Accumulator = BigInt('14695981039346656037');\nconst [Prime, Size] = [BigInt('1099511628211'), BigInt('18446744073709551616' /* 2 ^ 64 */)];\nconst Bytes = Array.from({ length: 256 }).map((_, i) => BigInt(i));\nconst F64 = new Float64Array(1);\nconst F64In = new DataView(F64.buffer);\nconst F64Out = new Uint8Array(F64.buffer);\n// ------------------------------------------------------------------\n// NumberToBytes\n// ------------------------------------------------------------------\nfunction* NumberToBytes(value) {\n    const byteCount = value === 0 ? 1 : Math.ceil(Math.floor(Math.log2(value) + 1) / 8);\n    for (let i = 0; i < byteCount; i++) {\n        yield (value >> (8 * (byteCount - 1 - i))) & 0xff;\n    }\n}\n// ------------------------------------------------------------------\n// Hashing Functions\n// ------------------------------------------------------------------\nfunction ArrayType(value) {\n    FNV1A64(ByteMarker.Array);\n    for (const item of value) {\n        Visit(item);\n    }\n}\nfunction BooleanType(value) {\n    FNV1A64(ByteMarker.Boolean);\n    FNV1A64(value ? 1 : 0);\n}\nfunction BigIntType(value) {\n    FNV1A64(ByteMarker.BigInt);\n    F64In.setBigInt64(0, value);\n    for (const byte of F64Out) {\n        FNV1A64(byte);\n    }\n}\nfunction DateType(value) {\n    FNV1A64(ByteMarker.Date);\n    Visit(value.getTime());\n}\nfunction NullType(value) {\n    FNV1A64(ByteMarker.Null);\n}\nfunction NumberType(value) {\n    FNV1A64(ByteMarker.Number);\n    F64In.setFloat64(0, value);\n    for (const byte of F64Out) {\n        FNV1A64(byte);\n    }\n}\nfunction ObjectType(value) {\n    FNV1A64(ByteMarker.Object);\n    for (const key of globalThis.Object.getOwnPropertyNames(value).sort()) {\n        Visit(key);\n        Visit(value[key]);\n    }\n}\nfunction StringType(value) {\n    FNV1A64(ByteMarker.String);\n    for (let i = 0; i < value.length; i++) {\n        for (const byte of NumberToBytes(value.charCodeAt(i))) {\n            FNV1A64(byte);\n        }\n    }\n}\nfunction SymbolType(value) {\n    FNV1A64(ByteMarker.Symbol);\n    Visit(value.description);\n}\nfunction Uint8ArrayType(value) {\n    FNV1A64(ByteMarker.Uint8Array);\n    for (let i = 0; i < value.length; i++) {\n        FNV1A64(value[i]);\n    }\n}\nfunction UndefinedType(value) {\n    return FNV1A64(ByteMarker.Undefined);\n}\nfunction Visit(value) {\n    if (IsArray(value))\n        return ArrayType(value);\n    if (IsBoolean(value))\n        return BooleanType(value);\n    if (IsBigInt(value))\n        return BigIntType(value);\n    if (IsDate(value))\n        return DateType(value);\n    if (IsNull(value))\n        return NullType(value);\n    if (IsNumber(value))\n        return NumberType(value);\n    if (IsObject(value))\n        return ObjectType(value);\n    if (IsString(value))\n        return StringType(value);\n    if (IsSymbol(value))\n        return SymbolType(value);\n    if (IsUint8Array(value))\n        return Uint8ArrayType(value);\n    if (IsUndefined(value))\n        return UndefinedType(value);\n    throw new ValueHashError(value);\n}\nfunction FNV1A64(byte) {\n    Accumulator = Accumulator ^ Bytes[byte];\n    Accumulator = (Accumulator * Prime) % Size;\n}\n// ------------------------------------------------------------------\n// Hash\n// ------------------------------------------------------------------\n/** Creates a FNV1A-64 non cryptographic hash of the given value */\nexport function Hash(value) {\n    Accumulator = BigInt('14695981039346656037');\n    Visit(value);\n    return Accumulator;\n}\n", "import { TypeSystemPolicy } from '../../system/index.mjs';\nimport { <PERSON><PERSON>, <PERSON>ushref } from '../deref/index.mjs';\nimport { Hash } from '../hash/index.mjs';\nimport { Kind } from '../../type/symbols/index.mjs';\nimport { KeyOfPattern } from '../../type/keyof/index.mjs';\nimport { ExtendsUndefinedCheck } from '../../type/extends/index.mjs';\nimport { TypeRegistry, FormatRegistry } from '../../type/registry/index.mjs';\nimport { TypeBoxError } from '../../type/error/index.mjs';\nimport { Never } from '../../type/never/index.mjs';\n// ------------------------------------------------------------------\n// ValueGuard\n// ------------------------------------------------------------------\nimport { IsArray, IsUint8Array, IsDate, IsPromise, IsFunction, IsAsyncIterator, IsIterator, IsBoolean, IsNumber, IsBigInt, IsString, IsSymbol, IsInteger, IsNull, IsUndefined } from '../guard/index.mjs';\n// ------------------------------------------------------------------\n// KindGuard\n// ------------------------------------------------------------------\nimport { IsSchema } from '../../type/guard/kind.mjs';\n// ------------------------------------------------------------------\n// Errors\n// ------------------------------------------------------------------\nexport class ValueCheckUnknownTypeError extends TypeBoxError {\n    constructor(schema) {\n        super(`Unknown type`);\n        this.schema = schema;\n    }\n}\n// ------------------------------------------------------------------\n// TypeGuards\n// ------------------------------------------------------------------\nfunction IsAnyOrUnknown(schema) {\n    return schema[Kind] === 'Any' || schema[Kind] === 'Unknown';\n}\n// ------------------------------------------------------------------\n// Guards\n// ------------------------------------------------------------------\nfunction IsDefined(value) {\n    return value !== undefined;\n}\n// ------------------------------------------------------------------\n// Types\n// ------------------------------------------------------------------\nfunction FromAny(schema, references, value) {\n    return true;\n}\nfunction FromArgument(schema, references, value) {\n    return true;\n}\nfunction FromArray(schema, references, value) {\n    if (!IsArray(value))\n        return false;\n    if (IsDefined(schema.minItems) && !(value.length >= schema.minItems)) {\n        return false;\n    }\n    if (IsDefined(schema.maxItems) && !(value.length <= schema.maxItems)) {\n        return false;\n    }\n    if (!value.every((value) => Visit(schema.items, references, value))) {\n        return false;\n    }\n    // prettier-ignore\n    if (schema.uniqueItems === true && !((function () { const set = new Set(); for (const element of value) {\n        const hashed = Hash(element);\n        if (set.has(hashed)) {\n            return false;\n        }\n        else {\n            set.add(hashed);\n        }\n    } return true; })())) {\n        return false;\n    }\n    // contains\n    if (!(IsDefined(schema.contains) || IsNumber(schema.minContains) || IsNumber(schema.maxContains))) {\n        return true; // exit\n    }\n    const containsSchema = IsDefined(schema.contains) ? schema.contains : Never();\n    const containsCount = value.reduce((acc, value) => (Visit(containsSchema, references, value) ? acc + 1 : acc), 0);\n    if (containsCount === 0) {\n        return false;\n    }\n    if (IsNumber(schema.minContains) && containsCount < schema.minContains) {\n        return false;\n    }\n    if (IsNumber(schema.maxContains) && containsCount > schema.maxContains) {\n        return false;\n    }\n    return true;\n}\nfunction FromAsyncIterator(schema, references, value) {\n    return IsAsyncIterator(value);\n}\nfunction FromBigInt(schema, references, value) {\n    if (!IsBigInt(value))\n        return false;\n    if (IsDefined(schema.exclusiveMaximum) && !(value < schema.exclusiveMaximum)) {\n        return false;\n    }\n    if (IsDefined(schema.exclusiveMinimum) && !(value > schema.exclusiveMinimum)) {\n        return false;\n    }\n    if (IsDefined(schema.maximum) && !(value <= schema.maximum)) {\n        return false;\n    }\n    if (IsDefined(schema.minimum) && !(value >= schema.minimum)) {\n        return false;\n    }\n    if (IsDefined(schema.multipleOf) && !(value % schema.multipleOf === BigInt(0))) {\n        return false;\n    }\n    return true;\n}\nfunction FromBoolean(schema, references, value) {\n    return IsBoolean(value);\n}\nfunction FromConstructor(schema, references, value) {\n    return Visit(schema.returns, references, value.prototype);\n}\nfunction FromDate(schema, references, value) {\n    if (!IsDate(value))\n        return false;\n    if (IsDefined(schema.exclusiveMaximumTimestamp) && !(value.getTime() < schema.exclusiveMaximumTimestamp)) {\n        return false;\n    }\n    if (IsDefined(schema.exclusiveMinimumTimestamp) && !(value.getTime() > schema.exclusiveMinimumTimestamp)) {\n        return false;\n    }\n    if (IsDefined(schema.maximumTimestamp) && !(value.getTime() <= schema.maximumTimestamp)) {\n        return false;\n    }\n    if (IsDefined(schema.minimumTimestamp) && !(value.getTime() >= schema.minimumTimestamp)) {\n        return false;\n    }\n    if (IsDefined(schema.multipleOfTimestamp) && !(value.getTime() % schema.multipleOfTimestamp === 0)) {\n        return false;\n    }\n    return true;\n}\nfunction FromFunction(schema, references, value) {\n    return IsFunction(value);\n}\nfunction FromImport(schema, references, value) {\n    const definitions = globalThis.Object.values(schema.$defs);\n    const target = schema.$defs[schema.$ref];\n    return Visit(target, [...references, ...definitions], value);\n}\nfunction FromInteger(schema, references, value) {\n    if (!IsInteger(value)) {\n        return false;\n    }\n    if (IsDefined(schema.exclusiveMaximum) && !(value < schema.exclusiveMaximum)) {\n        return false;\n    }\n    if (IsDefined(schema.exclusiveMinimum) && !(value > schema.exclusiveMinimum)) {\n        return false;\n    }\n    if (IsDefined(schema.maximum) && !(value <= schema.maximum)) {\n        return false;\n    }\n    if (IsDefined(schema.minimum) && !(value >= schema.minimum)) {\n        return false;\n    }\n    if (IsDefined(schema.multipleOf) && !(value % schema.multipleOf === 0)) {\n        return false;\n    }\n    return true;\n}\nfunction FromIntersect(schema, references, value) {\n    const check1 = schema.allOf.every((schema) => Visit(schema, references, value));\n    if (schema.unevaluatedProperties === false) {\n        const keyPattern = new RegExp(KeyOfPattern(schema));\n        const check2 = Object.getOwnPropertyNames(value).every((key) => keyPattern.test(key));\n        return check1 && check2;\n    }\n    else if (IsSchema(schema.unevaluatedProperties)) {\n        const keyCheck = new RegExp(KeyOfPattern(schema));\n        const check2 = Object.getOwnPropertyNames(value).every((key) => keyCheck.test(key) || Visit(schema.unevaluatedProperties, references, value[key]));\n        return check1 && check2;\n    }\n    else {\n        return check1;\n    }\n}\nfunction FromIterator(schema, references, value) {\n    return IsIterator(value);\n}\nfunction FromLiteral(schema, references, value) {\n    return value === schema.const;\n}\nfunction FromNever(schema, references, value) {\n    return false;\n}\nfunction FromNot(schema, references, value) {\n    return !Visit(schema.not, references, value);\n}\nfunction FromNull(schema, references, value) {\n    return IsNull(value);\n}\nfunction FromNumber(schema, references, value) {\n    if (!TypeSystemPolicy.IsNumberLike(value))\n        return false;\n    if (IsDefined(schema.exclusiveMaximum) && !(value < schema.exclusiveMaximum)) {\n        return false;\n    }\n    if (IsDefined(schema.exclusiveMinimum) && !(value > schema.exclusiveMinimum)) {\n        return false;\n    }\n    if (IsDefined(schema.minimum) && !(value >= schema.minimum)) {\n        return false;\n    }\n    if (IsDefined(schema.maximum) && !(value <= schema.maximum)) {\n        return false;\n    }\n    if (IsDefined(schema.multipleOf) && !(value % schema.multipleOf === 0)) {\n        return false;\n    }\n    return true;\n}\nfunction FromObject(schema, references, value) {\n    if (!TypeSystemPolicy.IsObjectLike(value))\n        return false;\n    if (IsDefined(schema.minProperties) && !(Object.getOwnPropertyNames(value).length >= schema.minProperties)) {\n        return false;\n    }\n    if (IsDefined(schema.maxProperties) && !(Object.getOwnPropertyNames(value).length <= schema.maxProperties)) {\n        return false;\n    }\n    const knownKeys = Object.getOwnPropertyNames(schema.properties);\n    for (const knownKey of knownKeys) {\n        const property = schema.properties[knownKey];\n        if (schema.required && schema.required.includes(knownKey)) {\n            if (!Visit(property, references, value[knownKey])) {\n                return false;\n            }\n            if ((ExtendsUndefinedCheck(property) || IsAnyOrUnknown(property)) && !(knownKey in value)) {\n                return false;\n            }\n        }\n        else {\n            if (TypeSystemPolicy.IsExactOptionalProperty(value, knownKey) && !Visit(property, references, value[knownKey])) {\n                return false;\n            }\n        }\n    }\n    if (schema.additionalProperties === false) {\n        const valueKeys = Object.getOwnPropertyNames(value);\n        // optimization: value is valid if schemaKey length matches the valueKey length\n        if (schema.required && schema.required.length === knownKeys.length && valueKeys.length === knownKeys.length) {\n            return true;\n        }\n        else {\n            return valueKeys.every((valueKey) => knownKeys.includes(valueKey));\n        }\n    }\n    else if (typeof schema.additionalProperties === 'object') {\n        const valueKeys = Object.getOwnPropertyNames(value);\n        return valueKeys.every((key) => knownKeys.includes(key) || Visit(schema.additionalProperties, references, value[key]));\n    }\n    else {\n        return true;\n    }\n}\nfunction FromPromise(schema, references, value) {\n    return IsPromise(value);\n}\nfunction FromRecord(schema, references, value) {\n    if (!TypeSystemPolicy.IsRecordLike(value)) {\n        return false;\n    }\n    if (IsDefined(schema.minProperties) && !(Object.getOwnPropertyNames(value).length >= schema.minProperties)) {\n        return false;\n    }\n    if (IsDefined(schema.maxProperties) && !(Object.getOwnPropertyNames(value).length <= schema.maxProperties)) {\n        return false;\n    }\n    const [patternKey, patternSchema] = Object.entries(schema.patternProperties)[0];\n    const regex = new RegExp(patternKey);\n    // prettier-ignore\n    const check1 = Object.entries(value).every(([key, value]) => {\n        return (regex.test(key)) ? Visit(patternSchema, references, value) : true;\n    });\n    // prettier-ignore\n    const check2 = typeof schema.additionalProperties === 'object' ? Object.entries(value).every(([key, value]) => {\n        return (!regex.test(key)) ? Visit(schema.additionalProperties, references, value) : true;\n    }) : true;\n    const check3 = schema.additionalProperties === false\n        ? Object.getOwnPropertyNames(value).every((key) => {\n            return regex.test(key);\n        })\n        : true;\n    return check1 && check2 && check3;\n}\nfunction FromRef(schema, references, value) {\n    return Visit(Deref(schema, references), references, value);\n}\nfunction FromRegExp(schema, references, value) {\n    const regex = new RegExp(schema.source, schema.flags);\n    if (IsDefined(schema.minLength)) {\n        if (!(value.length >= schema.minLength))\n            return false;\n    }\n    if (IsDefined(schema.maxLength)) {\n        if (!(value.length <= schema.maxLength))\n            return false;\n    }\n    return regex.test(value);\n}\nfunction FromString(schema, references, value) {\n    if (!IsString(value)) {\n        return false;\n    }\n    if (IsDefined(schema.minLength)) {\n        if (!(value.length >= schema.minLength))\n            return false;\n    }\n    if (IsDefined(schema.maxLength)) {\n        if (!(value.length <= schema.maxLength))\n            return false;\n    }\n    if (IsDefined(schema.pattern)) {\n        const regex = new RegExp(schema.pattern);\n        if (!regex.test(value))\n            return false;\n    }\n    if (IsDefined(schema.format)) {\n        if (!FormatRegistry.Has(schema.format))\n            return false;\n        const func = FormatRegistry.Get(schema.format);\n        return func(value);\n    }\n    return true;\n}\nfunction FromSymbol(schema, references, value) {\n    return IsSymbol(value);\n}\nfunction FromTemplateLiteral(schema, references, value) {\n    return IsString(value) && new RegExp(schema.pattern).test(value);\n}\nfunction FromThis(schema, references, value) {\n    return Visit(Deref(schema, references), references, value);\n}\nfunction FromTuple(schema, references, value) {\n    if (!IsArray(value)) {\n        return false;\n    }\n    if (schema.items === undefined && !(value.length === 0)) {\n        return false;\n    }\n    if (!(value.length === schema.maxItems)) {\n        return false;\n    }\n    if (!schema.items) {\n        return true;\n    }\n    for (let i = 0; i < schema.items.length; i++) {\n        if (!Visit(schema.items[i], references, value[i]))\n            return false;\n    }\n    return true;\n}\nfunction FromUndefined(schema, references, value) {\n    return IsUndefined(value);\n}\nfunction FromUnion(schema, references, value) {\n    return schema.anyOf.some((inner) => Visit(inner, references, value));\n}\nfunction FromUint8Array(schema, references, value) {\n    if (!IsUint8Array(value)) {\n        return false;\n    }\n    if (IsDefined(schema.maxByteLength) && !(value.length <= schema.maxByteLength)) {\n        return false;\n    }\n    if (IsDefined(schema.minByteLength) && !(value.length >= schema.minByteLength)) {\n        return false;\n    }\n    return true;\n}\nfunction FromUnknown(schema, references, value) {\n    return true;\n}\nfunction FromVoid(schema, references, value) {\n    return TypeSystemPolicy.IsVoidLike(value);\n}\nfunction FromKind(schema, references, value) {\n    if (!TypeRegistry.Has(schema[Kind]))\n        return false;\n    const func = TypeRegistry.Get(schema[Kind]);\n    return func(schema, value);\n}\nfunction Visit(schema, references, value) {\n    const references_ = IsDefined(schema.$id) ? Pushref(schema, references) : references;\n    const schema_ = schema;\n    switch (schema_[Kind]) {\n        case 'Any':\n            return FromAny(schema_, references_, value);\n        case 'Argument':\n            return FromArgument(schema_, references_, value);\n        case 'Array':\n            return FromArray(schema_, references_, value);\n        case 'AsyncIterator':\n            return FromAsyncIterator(schema_, references_, value);\n        case 'BigInt':\n            return FromBigInt(schema_, references_, value);\n        case 'Boolean':\n            return FromBoolean(schema_, references_, value);\n        case 'Constructor':\n            return FromConstructor(schema_, references_, value);\n        case 'Date':\n            return FromDate(schema_, references_, value);\n        case 'Function':\n            return FromFunction(schema_, references_, value);\n        case 'Import':\n            return FromImport(schema_, references_, value);\n        case 'Integer':\n            return FromInteger(schema_, references_, value);\n        case 'Intersect':\n            return FromIntersect(schema_, references_, value);\n        case 'Iterator':\n            return FromIterator(schema_, references_, value);\n        case 'Literal':\n            return FromLiteral(schema_, references_, value);\n        case 'Never':\n            return FromNever(schema_, references_, value);\n        case 'Not':\n            return FromNot(schema_, references_, value);\n        case 'Null':\n            return FromNull(schema_, references_, value);\n        case 'Number':\n            return FromNumber(schema_, references_, value);\n        case 'Object':\n            return FromObject(schema_, references_, value);\n        case 'Promise':\n            return FromPromise(schema_, references_, value);\n        case 'Record':\n            return FromRecord(schema_, references_, value);\n        case 'Ref':\n            return FromRef(schema_, references_, value);\n        case 'RegExp':\n            return FromRegExp(schema_, references_, value);\n        case 'String':\n            return FromString(schema_, references_, value);\n        case 'Symbol':\n            return FromSymbol(schema_, references_, value);\n        case 'TemplateLiteral':\n            return FromTemplateLiteral(schema_, references_, value);\n        case 'This':\n            return FromThis(schema_, references_, value);\n        case 'Tuple':\n            return FromTuple(schema_, references_, value);\n        case 'Undefined':\n            return FromUndefined(schema_, references_, value);\n        case 'Union':\n            return FromUnion(schema_, references_, value);\n        case 'Uint8Array':\n            return FromUint8Array(schema_, references_, value);\n        case 'Unknown':\n            return FromUnknown(schema_, references_, value);\n        case 'Void':\n            return FromVoid(schema_, references_, value);\n        default:\n            if (!TypeRegistry.Has(schema_[Kind]))\n                throw new ValueCheckUnknownTypeError(schema_);\n            return FromKind(schema_, references_, value);\n    }\n}\n/** Returns true if the value matches the given type. */\nexport function Check(...args) {\n    return args.length === 3 ? Visit(args[0], args[1], args[2]) : Visit(args[0], [], args[1]);\n}\n", "import { TypeSystemPolicy } from '../system/index.mjs';\nimport { KeyOfPattern } from '../type/keyof/index.mjs';\nimport { TypeRegistry, FormatRegistry } from '../type/registry/index.mjs';\nimport { ExtendsUndefinedCheck } from '../type/extends/extends-undefined.mjs';\nimport { GetErrorFunction } from './function.mjs';\nimport { TypeBoxError } from '../type/error/index.mjs';\nimport { Deref } from '../value/deref/index.mjs';\nimport { Hash } from '../value/hash/index.mjs';\nimport { Check } from '../value/check/index.mjs';\nimport { Kind } from '../type/symbols/index.mjs';\nimport { Never } from '../type/never/index.mjs';\n// ------------------------------------------------------------------\n// ValueGuard\n// ------------------------------------------------------------------\n// prettier-ignore\nimport { IsArray, IsUint8Array, IsDate, IsPromise, IsFunction, IsAsyncIterator, IsIterator, IsBoolean, IsNumber, IsBigInt, IsString, IsSymbol, IsInteger, IsNull, IsUndefined } from '../value/guard/index.mjs';\n// ------------------------------------------------------------------\n// ValueErrorType\n// ------------------------------------------------------------------\nexport var ValueErrorType;\n(function (ValueErrorType) {\n    ValueErrorType[ValueErrorType[\"ArrayContains\"] = 0] = \"ArrayContains\";\n    ValueErrorType[ValueErrorType[\"ArrayMaxContains\"] = 1] = \"ArrayMaxContains\";\n    ValueErrorType[ValueErrorType[\"ArrayMaxItems\"] = 2] = \"ArrayMaxItems\";\n    ValueErrorType[ValueErrorType[\"ArrayMinContains\"] = 3] = \"ArrayMinContains\";\n    ValueErrorType[ValueErrorType[\"ArrayMinItems\"] = 4] = \"ArrayMinItems\";\n    ValueErrorType[ValueErrorType[\"ArrayUniqueItems\"] = 5] = \"ArrayUniqueItems\";\n    ValueErrorType[ValueErrorType[\"Array\"] = 6] = \"Array\";\n    ValueErrorType[ValueErrorType[\"AsyncIterator\"] = 7] = \"AsyncIterator\";\n    ValueErrorType[ValueErrorType[\"BigIntExclusiveMaximum\"] = 8] = \"BigIntExclusiveMaximum\";\n    ValueErrorType[ValueErrorType[\"BigIntExclusiveMinimum\"] = 9] = \"BigIntExclusiveMinimum\";\n    ValueErrorType[ValueErrorType[\"BigIntMaximum\"] = 10] = \"BigIntMaximum\";\n    ValueErrorType[ValueErrorType[\"BigIntMinimum\"] = 11] = \"BigIntMinimum\";\n    ValueErrorType[ValueErrorType[\"BigIntMultipleOf\"] = 12] = \"BigIntMultipleOf\";\n    ValueErrorType[ValueErrorType[\"BigInt\"] = 13] = \"BigInt\";\n    ValueErrorType[ValueErrorType[\"Boolean\"] = 14] = \"Boolean\";\n    ValueErrorType[ValueErrorType[\"DateExclusiveMaximumTimestamp\"] = 15] = \"DateExclusiveMaximumTimestamp\";\n    ValueErrorType[ValueErrorType[\"DateExclusiveMinimumTimestamp\"] = 16] = \"DateExclusiveMinimumTimestamp\";\n    ValueErrorType[ValueErrorType[\"DateMaximumTimestamp\"] = 17] = \"DateMaximumTimestamp\";\n    ValueErrorType[ValueErrorType[\"DateMinimumTimestamp\"] = 18] = \"DateMinimumTimestamp\";\n    ValueErrorType[ValueErrorType[\"DateMultipleOfTimestamp\"] = 19] = \"DateMultipleOfTimestamp\";\n    ValueErrorType[ValueErrorType[\"Date\"] = 20] = \"Date\";\n    ValueErrorType[ValueErrorType[\"Function\"] = 21] = \"Function\";\n    ValueErrorType[ValueErrorType[\"IntegerExclusiveMaximum\"] = 22] = \"IntegerExclusiveMaximum\";\n    ValueErrorType[ValueErrorType[\"IntegerExclusiveMinimum\"] = 23] = \"IntegerExclusiveMinimum\";\n    ValueErrorType[ValueErrorType[\"IntegerMaximum\"] = 24] = \"IntegerMaximum\";\n    ValueErrorType[ValueErrorType[\"IntegerMinimum\"] = 25] = \"IntegerMinimum\";\n    ValueErrorType[ValueErrorType[\"IntegerMultipleOf\"] = 26] = \"IntegerMultipleOf\";\n    ValueErrorType[ValueErrorType[\"Integer\"] = 27] = \"Integer\";\n    ValueErrorType[ValueErrorType[\"IntersectUnevaluatedProperties\"] = 28] = \"IntersectUnevaluatedProperties\";\n    ValueErrorType[ValueErrorType[\"Intersect\"] = 29] = \"Intersect\";\n    ValueErrorType[ValueErrorType[\"Iterator\"] = 30] = \"Iterator\";\n    ValueErrorType[ValueErrorType[\"Kind\"] = 31] = \"Kind\";\n    ValueErrorType[ValueErrorType[\"Literal\"] = 32] = \"Literal\";\n    ValueErrorType[ValueErrorType[\"Never\"] = 33] = \"Never\";\n    ValueErrorType[ValueErrorType[\"Not\"] = 34] = \"Not\";\n    ValueErrorType[ValueErrorType[\"Null\"] = 35] = \"Null\";\n    ValueErrorType[ValueErrorType[\"NumberExclusiveMaximum\"] = 36] = \"NumberExclusiveMaximum\";\n    ValueErrorType[ValueErrorType[\"NumberExclusiveMinimum\"] = 37] = \"NumberExclusiveMinimum\";\n    ValueErrorType[ValueErrorType[\"NumberMaximum\"] = 38] = \"NumberMaximum\";\n    ValueErrorType[ValueErrorType[\"NumberMinimum\"] = 39] = \"NumberMinimum\";\n    ValueErrorType[ValueErrorType[\"NumberMultipleOf\"] = 40] = \"NumberMultipleOf\";\n    ValueErrorType[ValueErrorType[\"Number\"] = 41] = \"Number\";\n    ValueErrorType[ValueErrorType[\"ObjectAdditionalProperties\"] = 42] = \"ObjectAdditionalProperties\";\n    ValueErrorType[ValueErrorType[\"ObjectMaxProperties\"] = 43] = \"ObjectMaxProperties\";\n    ValueErrorType[ValueErrorType[\"ObjectMinProperties\"] = 44] = \"ObjectMinProperties\";\n    ValueErrorType[ValueErrorType[\"ObjectRequiredProperty\"] = 45] = \"ObjectRequiredProperty\";\n    ValueErrorType[ValueErrorType[\"Object\"] = 46] = \"Object\";\n    ValueErrorType[ValueErrorType[\"Promise\"] = 47] = \"Promise\";\n    ValueErrorType[ValueErrorType[\"RegExp\"] = 48] = \"RegExp\";\n    ValueErrorType[ValueErrorType[\"StringFormatUnknown\"] = 49] = \"StringFormatUnknown\";\n    ValueErrorType[ValueErrorType[\"StringFormat\"] = 50] = \"StringFormat\";\n    ValueErrorType[ValueErrorType[\"StringMaxLength\"] = 51] = \"StringMaxLength\";\n    ValueErrorType[ValueErrorType[\"StringMinLength\"] = 52] = \"StringMinLength\";\n    ValueErrorType[ValueErrorType[\"StringPattern\"] = 53] = \"StringPattern\";\n    ValueErrorType[ValueErrorType[\"String\"] = 54] = \"String\";\n    ValueErrorType[ValueErrorType[\"Symbol\"] = 55] = \"Symbol\";\n    ValueErrorType[ValueErrorType[\"TupleLength\"] = 56] = \"TupleLength\";\n    ValueErrorType[ValueErrorType[\"Tuple\"] = 57] = \"Tuple\";\n    ValueErrorType[ValueErrorType[\"Uint8ArrayMaxByteLength\"] = 58] = \"Uint8ArrayMaxByteLength\";\n    ValueErrorType[ValueErrorType[\"Uint8ArrayMinByteLength\"] = 59] = \"Uint8ArrayMinByteLength\";\n    ValueErrorType[ValueErrorType[\"Uint8Array\"] = 60] = \"Uint8Array\";\n    ValueErrorType[ValueErrorType[\"Undefined\"] = 61] = \"Undefined\";\n    ValueErrorType[ValueErrorType[\"Union\"] = 62] = \"Union\";\n    ValueErrorType[ValueErrorType[\"Void\"] = 63] = \"Void\";\n})(ValueErrorType || (ValueErrorType = {}));\n// ------------------------------------------------------------------\n// ValueErrors\n// ------------------------------------------------------------------\nexport class ValueErrorsUnknownTypeError extends TypeBoxError {\n    constructor(schema) {\n        super('Unknown type');\n        this.schema = schema;\n    }\n}\n// ------------------------------------------------------------------\n// EscapeKey\n// ------------------------------------------------------------------\nfunction EscapeKey(key) {\n    return key.replace(/~/g, '~0').replace(/\\//g, '~1'); // RFC6901 Path\n}\n// ------------------------------------------------------------------\n// Guards\n// ------------------------------------------------------------------\nfunction IsDefined(value) {\n    return value !== undefined;\n}\n// ------------------------------------------------------------------\n// ValueErrorIterator\n// ------------------------------------------------------------------\nexport class ValueErrorIterator {\n    constructor(iterator) {\n        this.iterator = iterator;\n    }\n    [Symbol.iterator]() {\n        return this.iterator;\n    }\n    /** Returns the first value error or undefined if no errors */\n    First() {\n        const next = this.iterator.next();\n        return next.done ? undefined : next.value;\n    }\n}\n// --------------------------------------------------------------------------\n// Create\n// --------------------------------------------------------------------------\nfunction Create(errorType, schema, path, value, errors = []) {\n    return {\n        type: errorType,\n        schema,\n        path,\n        value,\n        message: GetErrorFunction()({ errorType, path, schema, value, errors }),\n        errors,\n    };\n}\n// --------------------------------------------------------------------------\n// Types\n// --------------------------------------------------------------------------\nfunction* FromAny(schema, references, path, value) { }\nfunction* FromArgument(schema, references, path, value) { }\nfunction* FromArray(schema, references, path, value) {\n    if (!IsArray(value)) {\n        return yield Create(ValueErrorType.Array, schema, path, value);\n    }\n    if (IsDefined(schema.minItems) && !(value.length >= schema.minItems)) {\n        yield Create(ValueErrorType.ArrayMinItems, schema, path, value);\n    }\n    if (IsDefined(schema.maxItems) && !(value.length <= schema.maxItems)) {\n        yield Create(ValueErrorType.ArrayMaxItems, schema, path, value);\n    }\n    for (let i = 0; i < value.length; i++) {\n        yield* Visit(schema.items, references, `${path}/${i}`, value[i]);\n    }\n    // prettier-ignore\n    if (schema.uniqueItems === true && !((function () { const set = new Set(); for (const element of value) {\n        const hashed = Hash(element);\n        if (set.has(hashed)) {\n            return false;\n        }\n        else {\n            set.add(hashed);\n        }\n    } return true; })())) {\n        yield Create(ValueErrorType.ArrayUniqueItems, schema, path, value);\n    }\n    // contains\n    if (!(IsDefined(schema.contains) || IsDefined(schema.minContains) || IsDefined(schema.maxContains))) {\n        return;\n    }\n    const containsSchema = IsDefined(schema.contains) ? schema.contains : Never();\n    const containsCount = value.reduce((acc, value, index) => (Visit(containsSchema, references, `${path}${index}`, value).next().done === true ? acc + 1 : acc), 0);\n    if (containsCount === 0) {\n        yield Create(ValueErrorType.ArrayContains, schema, path, value);\n    }\n    if (IsNumber(schema.minContains) && containsCount < schema.minContains) {\n        yield Create(ValueErrorType.ArrayMinContains, schema, path, value);\n    }\n    if (IsNumber(schema.maxContains) && containsCount > schema.maxContains) {\n        yield Create(ValueErrorType.ArrayMaxContains, schema, path, value);\n    }\n}\nfunction* FromAsyncIterator(schema, references, path, value) {\n    if (!IsAsyncIterator(value))\n        yield Create(ValueErrorType.AsyncIterator, schema, path, value);\n}\nfunction* FromBigInt(schema, references, path, value) {\n    if (!IsBigInt(value))\n        return yield Create(ValueErrorType.BigInt, schema, path, value);\n    if (IsDefined(schema.exclusiveMaximum) && !(value < schema.exclusiveMaximum)) {\n        yield Create(ValueErrorType.BigIntExclusiveMaximum, schema, path, value);\n    }\n    if (IsDefined(schema.exclusiveMinimum) && !(value > schema.exclusiveMinimum)) {\n        yield Create(ValueErrorType.BigIntExclusiveMinimum, schema, path, value);\n    }\n    if (IsDefined(schema.maximum) && !(value <= schema.maximum)) {\n        yield Create(ValueErrorType.BigIntMaximum, schema, path, value);\n    }\n    if (IsDefined(schema.minimum) && !(value >= schema.minimum)) {\n        yield Create(ValueErrorType.BigIntMinimum, schema, path, value);\n    }\n    if (IsDefined(schema.multipleOf) && !(value % schema.multipleOf === BigInt(0))) {\n        yield Create(ValueErrorType.BigIntMultipleOf, schema, path, value);\n    }\n}\nfunction* FromBoolean(schema, references, path, value) {\n    if (!IsBoolean(value))\n        yield Create(ValueErrorType.Boolean, schema, path, value);\n}\nfunction* FromConstructor(schema, references, path, value) {\n    yield* Visit(schema.returns, references, path, value.prototype);\n}\nfunction* FromDate(schema, references, path, value) {\n    if (!IsDate(value))\n        return yield Create(ValueErrorType.Date, schema, path, value);\n    if (IsDefined(schema.exclusiveMaximumTimestamp) && !(value.getTime() < schema.exclusiveMaximumTimestamp)) {\n        yield Create(ValueErrorType.DateExclusiveMaximumTimestamp, schema, path, value);\n    }\n    if (IsDefined(schema.exclusiveMinimumTimestamp) && !(value.getTime() > schema.exclusiveMinimumTimestamp)) {\n        yield Create(ValueErrorType.DateExclusiveMinimumTimestamp, schema, path, value);\n    }\n    if (IsDefined(schema.maximumTimestamp) && !(value.getTime() <= schema.maximumTimestamp)) {\n        yield Create(ValueErrorType.DateMaximumTimestamp, schema, path, value);\n    }\n    if (IsDefined(schema.minimumTimestamp) && !(value.getTime() >= schema.minimumTimestamp)) {\n        yield Create(ValueErrorType.DateMinimumTimestamp, schema, path, value);\n    }\n    if (IsDefined(schema.multipleOfTimestamp) && !(value.getTime() % schema.multipleOfTimestamp === 0)) {\n        yield Create(ValueErrorType.DateMultipleOfTimestamp, schema, path, value);\n    }\n}\nfunction* FromFunction(schema, references, path, value) {\n    if (!IsFunction(value))\n        yield Create(ValueErrorType.Function, schema, path, value);\n}\nfunction* FromImport(schema, references, path, value) {\n    const definitions = globalThis.Object.values(schema.$defs);\n    const target = schema.$defs[schema.$ref];\n    yield* Visit(target, [...references, ...definitions], path, value);\n}\nfunction* FromInteger(schema, references, path, value) {\n    if (!IsInteger(value))\n        return yield Create(ValueErrorType.Integer, schema, path, value);\n    if (IsDefined(schema.exclusiveMaximum) && !(value < schema.exclusiveMaximum)) {\n        yield Create(ValueErrorType.IntegerExclusiveMaximum, schema, path, value);\n    }\n    if (IsDefined(schema.exclusiveMinimum) && !(value > schema.exclusiveMinimum)) {\n        yield Create(ValueErrorType.IntegerExclusiveMinimum, schema, path, value);\n    }\n    if (IsDefined(schema.maximum) && !(value <= schema.maximum)) {\n        yield Create(ValueErrorType.IntegerMaximum, schema, path, value);\n    }\n    if (IsDefined(schema.minimum) && !(value >= schema.minimum)) {\n        yield Create(ValueErrorType.IntegerMinimum, schema, path, value);\n    }\n    if (IsDefined(schema.multipleOf) && !(value % schema.multipleOf === 0)) {\n        yield Create(ValueErrorType.IntegerMultipleOf, schema, path, value);\n    }\n}\nfunction* FromIntersect(schema, references, path, value) {\n    let hasError = false;\n    for (const inner of schema.allOf) {\n        for (const error of Visit(inner, references, path, value)) {\n            hasError = true;\n            yield error;\n        }\n    }\n    if (hasError) {\n        return yield Create(ValueErrorType.Intersect, schema, path, value);\n    }\n    if (schema.unevaluatedProperties === false) {\n        const keyCheck = new RegExp(KeyOfPattern(schema));\n        for (const valueKey of Object.getOwnPropertyNames(value)) {\n            if (!keyCheck.test(valueKey)) {\n                yield Create(ValueErrorType.IntersectUnevaluatedProperties, schema, `${path}/${valueKey}`, value);\n            }\n        }\n    }\n    if (typeof schema.unevaluatedProperties === 'object') {\n        const keyCheck = new RegExp(KeyOfPattern(schema));\n        for (const valueKey of Object.getOwnPropertyNames(value)) {\n            if (!keyCheck.test(valueKey)) {\n                const next = Visit(schema.unevaluatedProperties, references, `${path}/${valueKey}`, value[valueKey]).next();\n                if (!next.done)\n                    yield next.value; // yield interior\n            }\n        }\n    }\n}\nfunction* FromIterator(schema, references, path, value) {\n    if (!IsIterator(value))\n        yield Create(ValueErrorType.Iterator, schema, path, value);\n}\nfunction* FromLiteral(schema, references, path, value) {\n    if (!(value === schema.const))\n        yield Create(ValueErrorType.Literal, schema, path, value);\n}\nfunction* FromNever(schema, references, path, value) {\n    yield Create(ValueErrorType.Never, schema, path, value);\n}\nfunction* FromNot(schema, references, path, value) {\n    if (Visit(schema.not, references, path, value).next().done === true)\n        yield Create(ValueErrorType.Not, schema, path, value);\n}\nfunction* FromNull(schema, references, path, value) {\n    if (!IsNull(value))\n        yield Create(ValueErrorType.Null, schema, path, value);\n}\nfunction* FromNumber(schema, references, path, value) {\n    if (!TypeSystemPolicy.IsNumberLike(value))\n        return yield Create(ValueErrorType.Number, schema, path, value);\n    if (IsDefined(schema.exclusiveMaximum) && !(value < schema.exclusiveMaximum)) {\n        yield Create(ValueErrorType.NumberExclusiveMaximum, schema, path, value);\n    }\n    if (IsDefined(schema.exclusiveMinimum) && !(value > schema.exclusiveMinimum)) {\n        yield Create(ValueErrorType.NumberExclusiveMinimum, schema, path, value);\n    }\n    if (IsDefined(schema.maximum) && !(value <= schema.maximum)) {\n        yield Create(ValueErrorType.NumberMaximum, schema, path, value);\n    }\n    if (IsDefined(schema.minimum) && !(value >= schema.minimum)) {\n        yield Create(ValueErrorType.NumberMinimum, schema, path, value);\n    }\n    if (IsDefined(schema.multipleOf) && !(value % schema.multipleOf === 0)) {\n        yield Create(ValueErrorType.NumberMultipleOf, schema, path, value);\n    }\n}\nfunction* FromObject(schema, references, path, value) {\n    if (!TypeSystemPolicy.IsObjectLike(value))\n        return yield Create(ValueErrorType.Object, schema, path, value);\n    if (IsDefined(schema.minProperties) && !(Object.getOwnPropertyNames(value).length >= schema.minProperties)) {\n        yield Create(ValueErrorType.ObjectMinProperties, schema, path, value);\n    }\n    if (IsDefined(schema.maxProperties) && !(Object.getOwnPropertyNames(value).length <= schema.maxProperties)) {\n        yield Create(ValueErrorType.ObjectMaxProperties, schema, path, value);\n    }\n    const requiredKeys = Array.isArray(schema.required) ? schema.required : [];\n    const knownKeys = Object.getOwnPropertyNames(schema.properties);\n    const unknownKeys = Object.getOwnPropertyNames(value);\n    for (const requiredKey of requiredKeys) {\n        if (unknownKeys.includes(requiredKey))\n            continue;\n        yield Create(ValueErrorType.ObjectRequiredProperty, schema.properties[requiredKey], `${path}/${EscapeKey(requiredKey)}`, undefined);\n    }\n    if (schema.additionalProperties === false) {\n        for (const valueKey of unknownKeys) {\n            if (!knownKeys.includes(valueKey)) {\n                yield Create(ValueErrorType.ObjectAdditionalProperties, schema, `${path}/${EscapeKey(valueKey)}`, value[valueKey]);\n            }\n        }\n    }\n    if (typeof schema.additionalProperties === 'object') {\n        for (const valueKey of unknownKeys) {\n            if (knownKeys.includes(valueKey))\n                continue;\n            yield* Visit(schema.additionalProperties, references, `${path}/${EscapeKey(valueKey)}`, value[valueKey]);\n        }\n    }\n    for (const knownKey of knownKeys) {\n        const property = schema.properties[knownKey];\n        if (schema.required && schema.required.includes(knownKey)) {\n            yield* Visit(property, references, `${path}/${EscapeKey(knownKey)}`, value[knownKey]);\n            if (ExtendsUndefinedCheck(schema) && !(knownKey in value)) {\n                yield Create(ValueErrorType.ObjectRequiredProperty, property, `${path}/${EscapeKey(knownKey)}`, undefined);\n            }\n        }\n        else {\n            if (TypeSystemPolicy.IsExactOptionalProperty(value, knownKey)) {\n                yield* Visit(property, references, `${path}/${EscapeKey(knownKey)}`, value[knownKey]);\n            }\n        }\n    }\n}\nfunction* FromPromise(schema, references, path, value) {\n    if (!IsPromise(value))\n        yield Create(ValueErrorType.Promise, schema, path, value);\n}\nfunction* FromRecord(schema, references, path, value) {\n    if (!TypeSystemPolicy.IsRecordLike(value))\n        return yield Create(ValueErrorType.Object, schema, path, value);\n    if (IsDefined(schema.minProperties) && !(Object.getOwnPropertyNames(value).length >= schema.minProperties)) {\n        yield Create(ValueErrorType.ObjectMinProperties, schema, path, value);\n    }\n    if (IsDefined(schema.maxProperties) && !(Object.getOwnPropertyNames(value).length <= schema.maxProperties)) {\n        yield Create(ValueErrorType.ObjectMaxProperties, schema, path, value);\n    }\n    const [patternKey, patternSchema] = Object.entries(schema.patternProperties)[0];\n    const regex = new RegExp(patternKey);\n    for (const [propertyKey, propertyValue] of Object.entries(value)) {\n        if (regex.test(propertyKey))\n            yield* Visit(patternSchema, references, `${path}/${EscapeKey(propertyKey)}`, propertyValue);\n    }\n    if (typeof schema.additionalProperties === 'object') {\n        for (const [propertyKey, propertyValue] of Object.entries(value)) {\n            if (!regex.test(propertyKey))\n                yield* Visit(schema.additionalProperties, references, `${path}/${EscapeKey(propertyKey)}`, propertyValue);\n        }\n    }\n    if (schema.additionalProperties === false) {\n        for (const [propertyKey, propertyValue] of Object.entries(value)) {\n            if (regex.test(propertyKey))\n                continue;\n            return yield Create(ValueErrorType.ObjectAdditionalProperties, schema, `${path}/${EscapeKey(propertyKey)}`, propertyValue);\n        }\n    }\n}\nfunction* FromRef(schema, references, path, value) {\n    yield* Visit(Deref(schema, references), references, path, value);\n}\nfunction* FromRegExp(schema, references, path, value) {\n    if (!IsString(value))\n        return yield Create(ValueErrorType.String, schema, path, value);\n    if (IsDefined(schema.minLength) && !(value.length >= schema.minLength)) {\n        yield Create(ValueErrorType.StringMinLength, schema, path, value);\n    }\n    if (IsDefined(schema.maxLength) && !(value.length <= schema.maxLength)) {\n        yield Create(ValueErrorType.StringMaxLength, schema, path, value);\n    }\n    const regex = new RegExp(schema.source, schema.flags);\n    if (!regex.test(value)) {\n        return yield Create(ValueErrorType.RegExp, schema, path, value);\n    }\n}\nfunction* FromString(schema, references, path, value) {\n    if (!IsString(value))\n        return yield Create(ValueErrorType.String, schema, path, value);\n    if (IsDefined(schema.minLength) && !(value.length >= schema.minLength)) {\n        yield Create(ValueErrorType.StringMinLength, schema, path, value);\n    }\n    if (IsDefined(schema.maxLength) && !(value.length <= schema.maxLength)) {\n        yield Create(ValueErrorType.StringMaxLength, schema, path, value);\n    }\n    if (IsString(schema.pattern)) {\n        const regex = new RegExp(schema.pattern);\n        if (!regex.test(value)) {\n            yield Create(ValueErrorType.StringPattern, schema, path, value);\n        }\n    }\n    if (IsString(schema.format)) {\n        if (!FormatRegistry.Has(schema.format)) {\n            yield Create(ValueErrorType.StringFormatUnknown, schema, path, value);\n        }\n        else {\n            const format = FormatRegistry.Get(schema.format);\n            if (!format(value)) {\n                yield Create(ValueErrorType.StringFormat, schema, path, value);\n            }\n        }\n    }\n}\nfunction* FromSymbol(schema, references, path, value) {\n    if (!IsSymbol(value))\n        yield Create(ValueErrorType.Symbol, schema, path, value);\n}\nfunction* FromTemplateLiteral(schema, references, path, value) {\n    if (!IsString(value))\n        return yield Create(ValueErrorType.String, schema, path, value);\n    const regex = new RegExp(schema.pattern);\n    if (!regex.test(value)) {\n        yield Create(ValueErrorType.StringPattern, schema, path, value);\n    }\n}\nfunction* FromThis(schema, references, path, value) {\n    yield* Visit(Deref(schema, references), references, path, value);\n}\nfunction* FromTuple(schema, references, path, value) {\n    if (!IsArray(value))\n        return yield Create(ValueErrorType.Tuple, schema, path, value);\n    if (schema.items === undefined && !(value.length === 0)) {\n        return yield Create(ValueErrorType.TupleLength, schema, path, value);\n    }\n    if (!(value.length === schema.maxItems)) {\n        return yield Create(ValueErrorType.TupleLength, schema, path, value);\n    }\n    if (!schema.items) {\n        return;\n    }\n    for (let i = 0; i < schema.items.length; i++) {\n        yield* Visit(schema.items[i], references, `${path}/${i}`, value[i]);\n    }\n}\nfunction* FromUndefined(schema, references, path, value) {\n    if (!IsUndefined(value))\n        yield Create(ValueErrorType.Undefined, schema, path, value);\n}\nfunction* FromUnion(schema, references, path, value) {\n    if (Check(schema, references, value))\n        return;\n    const errors = schema.anyOf.map((variant) => new ValueErrorIterator(Visit(variant, references, path, value)));\n    yield Create(ValueErrorType.Union, schema, path, value, errors);\n}\nfunction* FromUint8Array(schema, references, path, value) {\n    if (!IsUint8Array(value))\n        return yield Create(ValueErrorType.Uint8Array, schema, path, value);\n    if (IsDefined(schema.maxByteLength) && !(value.length <= schema.maxByteLength)) {\n        yield Create(ValueErrorType.Uint8ArrayMaxByteLength, schema, path, value);\n    }\n    if (IsDefined(schema.minByteLength) && !(value.length >= schema.minByteLength)) {\n        yield Create(ValueErrorType.Uint8ArrayMinByteLength, schema, path, value);\n    }\n}\nfunction* FromUnknown(schema, references, path, value) { }\nfunction* FromVoid(schema, references, path, value) {\n    if (!TypeSystemPolicy.IsVoidLike(value))\n        yield Create(ValueErrorType.Void, schema, path, value);\n}\nfunction* FromKind(schema, references, path, value) {\n    const check = TypeRegistry.Get(schema[Kind]);\n    if (!check(schema, value))\n        yield Create(ValueErrorType.Kind, schema, path, value);\n}\nfunction* Visit(schema, references, path, value) {\n    const references_ = IsDefined(schema.$id) ? [...references, schema] : references;\n    const schema_ = schema;\n    switch (schema_[Kind]) {\n        case 'Any':\n            return yield* FromAny(schema_, references_, path, value);\n        case 'Argument':\n            return yield* FromArgument(schema_, references_, path, value);\n        case 'Array':\n            return yield* FromArray(schema_, references_, path, value);\n        case 'AsyncIterator':\n            return yield* FromAsyncIterator(schema_, references_, path, value);\n        case 'BigInt':\n            return yield* FromBigInt(schema_, references_, path, value);\n        case 'Boolean':\n            return yield* FromBoolean(schema_, references_, path, value);\n        case 'Constructor':\n            return yield* FromConstructor(schema_, references_, path, value);\n        case 'Date':\n            return yield* FromDate(schema_, references_, path, value);\n        case 'Function':\n            return yield* FromFunction(schema_, references_, path, value);\n        case 'Import':\n            return yield* FromImport(schema_, references_, path, value);\n        case 'Integer':\n            return yield* FromInteger(schema_, references_, path, value);\n        case 'Intersect':\n            return yield* FromIntersect(schema_, references_, path, value);\n        case 'Iterator':\n            return yield* FromIterator(schema_, references_, path, value);\n        case 'Literal':\n            return yield* FromLiteral(schema_, references_, path, value);\n        case 'Never':\n            return yield* FromNever(schema_, references_, path, value);\n        case 'Not':\n            return yield* FromNot(schema_, references_, path, value);\n        case 'Null':\n            return yield* FromNull(schema_, references_, path, value);\n        case 'Number':\n            return yield* FromNumber(schema_, references_, path, value);\n        case 'Object':\n            return yield* FromObject(schema_, references_, path, value);\n        case 'Promise':\n            return yield* FromPromise(schema_, references_, path, value);\n        case 'Record':\n            return yield* FromRecord(schema_, references_, path, value);\n        case 'Ref':\n            return yield* FromRef(schema_, references_, path, value);\n        case 'RegExp':\n            return yield* FromRegExp(schema_, references_, path, value);\n        case 'String':\n            return yield* FromString(schema_, references_, path, value);\n        case 'Symbol':\n            return yield* FromSymbol(schema_, references_, path, value);\n        case 'TemplateLiteral':\n            return yield* FromTemplateLiteral(schema_, references_, path, value);\n        case 'This':\n            return yield* FromThis(schema_, references_, path, value);\n        case 'Tuple':\n            return yield* FromTuple(schema_, references_, path, value);\n        case 'Undefined':\n            return yield* FromUndefined(schema_, references_, path, value);\n        case 'Union':\n            return yield* FromUnion(schema_, references_, path, value);\n        case 'Uint8Array':\n            return yield* FromUint8Array(schema_, references_, path, value);\n        case 'Unknown':\n            return yield* FromUnknown(schema_, references_, path, value);\n        case 'Void':\n            return yield* FromVoid(schema_, references_, path, value);\n        default:\n            if (!TypeRegistry.Has(schema_[Kind]))\n                throw new ValueErrorsUnknownTypeError(schema);\n            return yield* FromKind(schema_, references_, path, value);\n    }\n}\n/** Returns an iterator for each error in this value. */\nexport function Errors(...args) {\n    const iterator = args.length === 3 ? Visit(args[0], args[1], '', args[2]) : Visit(args[0], [], '', args[1]);\n    return new ValueErrorIterator(iterator);\n}\n", "import { TypeSystemPolicy } from '../../system/policy.mjs';\nimport { Kind, TransformKind } from '../../type/symbols/index.mjs';\nimport { TypeBoxError } from '../../type/error/index.mjs';\nimport { KeyOfPropertyKeys, KeyOfPropertyEntries } from '../../type/keyof/index.mjs';\nimport { Deref, <PERSON>ushref } from '../deref/index.mjs';\nimport { Check } from '../check/index.mjs';\n// ------------------------------------------------------------------\n// ValueGuard\n// ------------------------------------------------------------------\nimport { Has<PERSON>roper<PERSON><PERSON><PERSON>, IsO<PERSON>, Is<PERSON><PERSON>y, IsValueType, IsUndefined as IsUndefinedValue } from '../guard/index.mjs';\n// ------------------------------------------------------------------\n// KindGuard\n// ------------------------------------------------------------------\nimport { IsTransform, IsSchema, IsUndefined } from '../../type/guard/kind.mjs';\n// ------------------------------------------------------------------\n// Errors\n// ------------------------------------------------------------------\n// thrown externally\n// prettier-ignore\nexport class TransformDecodeCheckError extends TypeBoxError {\n    constructor(schema, value, error) {\n        super(`Unable to decode value as it does not match the expected schema`);\n        this.schema = schema;\n        this.value = value;\n        this.error = error;\n    }\n}\n// prettier-ignore\nexport class TransformDecodeError extends TypeBoxError {\n    constructor(schema, path, value, error) {\n        super(error instanceof Error ? error.message : 'Unknown error');\n        this.schema = schema;\n        this.path = path;\n        this.value = value;\n        this.error = error;\n    }\n}\n// ------------------------------------------------------------------\n// Decode\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction Default(schema, path, value) {\n    try {\n        return IsTransform(schema) ? schema[TransformKind].Decode(value) : value;\n    }\n    catch (error) {\n        throw new TransformDecodeError(schema, path, value, error);\n    }\n}\n// prettier-ignore\nfunction FromArray(schema, references, path, value) {\n    return (IsArray(value))\n        ? Default(schema, path, value.map((value, index) => Visit(schema.items, references, `${path}/${index}`, value)))\n        : Default(schema, path, value);\n}\n// prettier-ignore\nfunction FromIntersect(schema, references, path, value) {\n    if (!IsObject(value) || IsValueType(value))\n        return Default(schema, path, value);\n    const knownEntries = KeyOfPropertyEntries(schema);\n    const knownKeys = knownEntries.map(entry => entry[0]);\n    const knownProperties = { ...value };\n    for (const [knownKey, knownSchema] of knownEntries)\n        if (knownKey in knownProperties) {\n            knownProperties[knownKey] = Visit(knownSchema, references, `${path}/${knownKey}`, knownProperties[knownKey]);\n        }\n    if (!IsTransform(schema.unevaluatedProperties)) {\n        return Default(schema, path, knownProperties);\n    }\n    const unknownKeys = Object.getOwnPropertyNames(knownProperties);\n    const unevaluatedProperties = schema.unevaluatedProperties;\n    const unknownProperties = { ...knownProperties };\n    for (const key of unknownKeys)\n        if (!knownKeys.includes(key)) {\n            unknownProperties[key] = Default(unevaluatedProperties, `${path}/${key}`, unknownProperties[key]);\n        }\n    return Default(schema, path, unknownProperties);\n}\n// prettier-ignore\nfunction FromImport(schema, references, path, value) {\n    const additional = globalThis.Object.values(schema.$defs);\n    const target = schema.$defs[schema.$ref];\n    const result = Visit(target, [...references, ...additional], path, value);\n    return Default(schema, path, result);\n}\nfunction FromNot(schema, references, path, value) {\n    return Default(schema, path, Visit(schema.not, references, path, value));\n}\n// prettier-ignore\nfunction FromObject(schema, references, path, value) {\n    if (!IsObject(value))\n        return Default(schema, path, value);\n    const knownKeys = KeyOfPropertyKeys(schema);\n    const knownProperties = { ...value };\n    for (const key of knownKeys) {\n        if (!HasPropertyKey(knownProperties, key))\n            continue;\n        // if the property value is undefined, but the target is not, nor does it satisfy exact optional \n        // property policy, then we need to continue. This is a special case for optional property handling \n        // where a transforms wrapped in a optional modifiers should not run.\n        if (IsUndefinedValue(knownProperties[key]) && (!IsUndefined(schema.properties[key]) ||\n            TypeSystemPolicy.IsExactOptionalProperty(knownProperties, key)))\n            continue;\n        // decode property\n        knownProperties[key] = Visit(schema.properties[key], references, `${path}/${key}`, knownProperties[key]);\n    }\n    if (!IsSchema(schema.additionalProperties)) {\n        return Default(schema, path, knownProperties);\n    }\n    const unknownKeys = Object.getOwnPropertyNames(knownProperties);\n    const additionalProperties = schema.additionalProperties;\n    const unknownProperties = { ...knownProperties };\n    for (const key of unknownKeys)\n        if (!knownKeys.includes(key)) {\n            unknownProperties[key] = Default(additionalProperties, `${path}/${key}`, unknownProperties[key]);\n        }\n    return Default(schema, path, unknownProperties);\n}\n// prettier-ignore\nfunction FromRecord(schema, references, path, value) {\n    if (!IsObject(value))\n        return Default(schema, path, value);\n    const pattern = Object.getOwnPropertyNames(schema.patternProperties)[0];\n    const knownKeys = new RegExp(pattern);\n    const knownProperties = { ...value };\n    for (const key of Object.getOwnPropertyNames(value))\n        if (knownKeys.test(key)) {\n            knownProperties[key] = Visit(schema.patternProperties[pattern], references, `${path}/${key}`, knownProperties[key]);\n        }\n    if (!IsSchema(schema.additionalProperties)) {\n        return Default(schema, path, knownProperties);\n    }\n    const unknownKeys = Object.getOwnPropertyNames(knownProperties);\n    const additionalProperties = schema.additionalProperties;\n    const unknownProperties = { ...knownProperties };\n    for (const key of unknownKeys)\n        if (!knownKeys.test(key)) {\n            unknownProperties[key] = Default(additionalProperties, `${path}/${key}`, unknownProperties[key]);\n        }\n    return Default(schema, path, unknownProperties);\n}\n// prettier-ignore\nfunction FromRef(schema, references, path, value) {\n    const target = Deref(schema, references);\n    return Default(schema, path, Visit(target, references, path, value));\n}\n// prettier-ignore\nfunction FromThis(schema, references, path, value) {\n    const target = Deref(schema, references);\n    return Default(schema, path, Visit(target, references, path, value));\n}\n// prettier-ignore\nfunction FromTuple(schema, references, path, value) {\n    return (IsArray(value) && IsArray(schema.items))\n        ? Default(schema, path, schema.items.map((schema, index) => Visit(schema, references, `${path}/${index}`, value[index])))\n        : Default(schema, path, value);\n}\n// prettier-ignore\nfunction FromUnion(schema, references, path, value) {\n    for (const subschema of schema.anyOf) {\n        if (!Check(subschema, references, value))\n            continue;\n        // note: ensure interior is decoded first\n        const decoded = Visit(subschema, references, path, value);\n        return Default(schema, path, decoded);\n    }\n    return Default(schema, path, value);\n}\n// prettier-ignore\nfunction Visit(schema, references, path, value) {\n    const references_ = Pushref(schema, references);\n    const schema_ = schema;\n    switch (schema[Kind]) {\n        case 'Array':\n            return FromArray(schema_, references_, path, value);\n        case 'Import':\n            return FromImport(schema_, references_, path, value);\n        case 'Intersect':\n            return FromIntersect(schema_, references_, path, value);\n        case 'Not':\n            return FromNot(schema_, references_, path, value);\n        case 'Object':\n            return FromObject(schema_, references_, path, value);\n        case 'Record':\n            return FromRecord(schema_, references_, path, value);\n        case 'Ref':\n            return FromRef(schema_, references_, path, value);\n        case 'Symbol':\n            return Default(schema_, path, value);\n        case 'This':\n            return FromThis(schema_, references_, path, value);\n        case 'Tuple':\n            return FromTuple(schema_, references_, path, value);\n        case 'Union':\n            return FromUnion(schema_, references_, path, value);\n        default:\n            return Default(schema_, path, value);\n    }\n}\n/**\n * `[Internal]` Decodes the value and returns the result. This function requires that\n * the caller `Check` the value before use. Passing unchecked values may result in\n * undefined behavior. Refer to the `Value.Decode()` for implementation details.\n */\nexport function TransformDecode(schema, references, value) {\n    return Visit(schema, references, '', value);\n}\n", "import { TypeSystemPolicy } from '../../system/policy.mjs';\nimport { Kind, TransformKind } from '../../type/symbols/index.mjs';\nimport { TypeBoxError } from '../../type/error/index.mjs';\nimport { KeyOfPropertyKeys, KeyOfPropertyEntries } from '../../type/keyof/index.mjs';\nimport { Deref, <PERSON>ushref } from '../deref/index.mjs';\nimport { Check } from '../check/index.mjs';\n// ------------------------------------------------------------------\n// ValueGuard\n// ------------------------------------------------------------------\nimport { Has<PERSON>roper<PERSON><PERSON><PERSON>, IsO<PERSON>, Is<PERSON><PERSON>y, IsValueType, IsUndefined as IsUndefinedValue } from '../guard/index.mjs';\n// ------------------------------------------------------------------\n// KindGuard\n// ------------------------------------------------------------------\nimport { IsTransform, IsSchema, IsUndefined } from '../../type/guard/kind.mjs';\n// ------------------------------------------------------------------\n// Errors\n// ------------------------------------------------------------------\n// prettier-ignore\nexport class TransformEncodeCheckError extends TypeBoxError {\n    constructor(schema, value, error) {\n        super(`The encoded value does not match the expected schema`);\n        this.schema = schema;\n        this.value = value;\n        this.error = error;\n    }\n}\n// prettier-ignore\nexport class TransformEncodeError extends TypeBoxError {\n    constructor(schema, path, value, error) {\n        super(`${error instanceof Error ? error.message : 'Unknown error'}`);\n        this.schema = schema;\n        this.path = path;\n        this.value = value;\n        this.error = error;\n    }\n}\n// ------------------------------------------------------------------\n// Encode\n// ------------------------------------------------------------------\n// prettier-ignore\nfunction Default(schema, path, value) {\n    try {\n        return IsTransform(schema) ? schema[TransformKind].Encode(value) : value;\n    }\n    catch (error) {\n        throw new TransformEncodeError(schema, path, value, error);\n    }\n}\n// prettier-ignore\nfunction FromArray(schema, references, path, value) {\n    const defaulted = Default(schema, path, value);\n    return IsArray(defaulted)\n        ? defaulted.map((value, index) => Visit(schema.items, references, `${path}/${index}`, value))\n        : defaulted;\n}\n// prettier-ignore\nfunction FromImport(schema, references, path, value) {\n    const additional = globalThis.Object.values(schema.$defs);\n    const target = schema.$defs[schema.$ref];\n    const result = Default(schema, path, value);\n    return Visit(target, [...references, ...additional], path, result);\n}\n// prettier-ignore\nfunction FromIntersect(schema, references, path, value) {\n    const defaulted = Default(schema, path, value);\n    if (!IsObject(value) || IsValueType(value))\n        return defaulted;\n    const knownEntries = KeyOfPropertyEntries(schema);\n    const knownKeys = knownEntries.map(entry => entry[0]);\n    const knownProperties = { ...defaulted };\n    for (const [knownKey, knownSchema] of knownEntries)\n        if (knownKey in knownProperties) {\n            knownProperties[knownKey] = Visit(knownSchema, references, `${path}/${knownKey}`, knownProperties[knownKey]);\n        }\n    if (!IsTransform(schema.unevaluatedProperties)) {\n        return knownProperties;\n    }\n    const unknownKeys = Object.getOwnPropertyNames(knownProperties);\n    const unevaluatedProperties = schema.unevaluatedProperties;\n    const properties = { ...knownProperties };\n    for (const key of unknownKeys)\n        if (!knownKeys.includes(key)) {\n            properties[key] = Default(unevaluatedProperties, `${path}/${key}`, properties[key]);\n        }\n    return properties;\n}\n// prettier-ignore\nfunction FromNot(schema, references, path, value) {\n    return Default(schema.not, path, Default(schema, path, value));\n}\n// prettier-ignore\nfunction FromObject(schema, references, path, value) {\n    const defaulted = Default(schema, path, value);\n    if (!IsObject(defaulted))\n        return defaulted;\n    const knownKeys = KeyOfPropertyKeys(schema);\n    const knownProperties = { ...defaulted };\n    for (const key of knownKeys) {\n        if (!HasPropertyKey(knownProperties, key))\n            continue;\n        // if the property value is undefined, but the target is not, nor does it satisfy exact optional \n        // property policy, then we need to continue. This is a special case for optional property handling \n        // where a transforms wrapped in a optional modifiers should not run.\n        if (IsUndefinedValue(knownProperties[key]) && (!IsUndefined(schema.properties[key]) ||\n            TypeSystemPolicy.IsExactOptionalProperty(knownProperties, key)))\n            continue;\n        // encode property\n        knownProperties[key] = Visit(schema.properties[key], references, `${path}/${key}`, knownProperties[key]);\n    }\n    if (!IsSchema(schema.additionalProperties)) {\n        return knownProperties;\n    }\n    const unknownKeys = Object.getOwnPropertyNames(knownProperties);\n    const additionalProperties = schema.additionalProperties;\n    const properties = { ...knownProperties };\n    for (const key of unknownKeys)\n        if (!knownKeys.includes(key)) {\n            properties[key] = Default(additionalProperties, `${path}/${key}`, properties[key]);\n        }\n    return properties;\n}\n// prettier-ignore\nfunction FromRecord(schema, references, path, value) {\n    const defaulted = Default(schema, path, value);\n    if (!IsObject(value))\n        return defaulted;\n    const pattern = Object.getOwnPropertyNames(schema.patternProperties)[0];\n    const knownKeys = new RegExp(pattern);\n    const knownProperties = { ...defaulted };\n    for (const key of Object.getOwnPropertyNames(value))\n        if (knownKeys.test(key)) {\n            knownProperties[key] = Visit(schema.patternProperties[pattern], references, `${path}/${key}`, knownProperties[key]);\n        }\n    if (!IsSchema(schema.additionalProperties)) {\n        return knownProperties;\n    }\n    const unknownKeys = Object.getOwnPropertyNames(knownProperties);\n    const additionalProperties = schema.additionalProperties;\n    const properties = { ...knownProperties };\n    for (const key of unknownKeys)\n        if (!knownKeys.test(key)) {\n            properties[key] = Default(additionalProperties, `${path}/${key}`, properties[key]);\n        }\n    return properties;\n}\n// prettier-ignore\nfunction FromRef(schema, references, path, value) {\n    const target = Deref(schema, references);\n    const resolved = Visit(target, references, path, value);\n    return Default(schema, path, resolved);\n}\n// prettier-ignore\nfunction FromThis(schema, references, path, value) {\n    const target = Deref(schema, references);\n    const resolved = Visit(target, references, path, value);\n    return Default(schema, path, resolved);\n}\n// prettier-ignore\nfunction FromTuple(schema, references, path, value) {\n    const value1 = Default(schema, path, value);\n    return IsArray(schema.items) ? schema.items.map((schema, index) => Visit(schema, references, `${path}/${index}`, value1[index])) : [];\n}\n// prettier-ignore\nfunction FromUnion(schema, references, path, value) {\n    // test value against union variants\n    for (const subschema of schema.anyOf) {\n        if (!Check(subschema, references, value))\n            continue;\n        const value1 = Visit(subschema, references, path, value);\n        return Default(schema, path, value1);\n    }\n    // test transformed value against union variants\n    for (const subschema of schema.anyOf) {\n        const value1 = Visit(subschema, references, path, value);\n        if (!Check(schema, references, value1))\n            continue;\n        return Default(schema, path, value1);\n    }\n    return Default(schema, path, value);\n}\n// prettier-ignore\nfunction Visit(schema, references, path, value) {\n    const references_ = Pushref(schema, references);\n    const schema_ = schema;\n    switch (schema[Kind]) {\n        case 'Array':\n            return FromArray(schema_, references_, path, value);\n        case 'Import':\n            return FromImport(schema_, references_, path, value);\n        case 'Intersect':\n            return FromIntersect(schema_, references_, path, value);\n        case 'Not':\n            return FromNot(schema_, references_, path, value);\n        case 'Object':\n            return FromObject(schema_, references_, path, value);\n        case 'Record':\n            return FromRecord(schema_, references_, path, value);\n        case 'Ref':\n            return FromRef(schema_, references_, path, value);\n        case 'This':\n            return FromThis(schema_, references_, path, value);\n        case 'Tuple':\n            return FromTuple(schema_, references_, path, value);\n        case 'Union':\n            return FromUnion(schema_, references_, path, value);\n        default:\n            return Default(schema_, path, value);\n    }\n}\n/**\n * `[Internal]` Encodes the value and returns the result. This function expects the\n * caller to pass a statically checked value. This function does not check the encoded\n * result, meaning the result should be passed to `Check` before use. Refer to the\n * `Value.Encode()` function for implementation details.\n */\nexport function TransformEncode(schema, references, value) {\n    return Visit(schema, references, '', value);\n}\n", "import { Deref, <PERSON>ushref } from '../deref/index.mjs';\nimport { Kind } from '../../type/symbols/index.mjs';\n// ------------------------------------------------------------------\n// KindGuard\n// ------------------------------------------------------------------\nimport { IsTransform, IsSchema } from '../../type/guard/kind.mjs';\n// ------------------------------------------------------------------\n// ValueGuard\n// ------------------------------------------------------------------\nimport { IsUndefined } from '../guard/index.mjs';\n// prettier-ignore\nfunction FromArray(schema, references) {\n    return IsTransform(schema) || Visit(schema.items, references);\n}\n// prettier-ignore\nfunction FromAsyncIterator(schema, references) {\n    return IsTransform(schema) || Visit(schema.items, references);\n}\n// prettier-ignore\nfunction FromConstructor(schema, references) {\n    return IsTransform(schema) || Visit(schema.returns, references) || schema.parameters.some((schema) => Visit(schema, references));\n}\n// prettier-ignore\nfunction FromFunction(schema, references) {\n    return IsTransform(schema) || Visit(schema.returns, references) || schema.parameters.some((schema) => Visit(schema, references));\n}\n// prettier-ignore\nfunction FromIntersect(schema, references) {\n    return IsTransform(schema) || IsTransform(schema.unevaluatedProperties) || schema.allOf.some((schema) => Visit(schema, references));\n}\n// prettier-ignore\nfunction FromImport(schema, references) {\n    const additional = globalThis.Object.getOwnPropertyNames(schema.$defs).reduce((result, key) => [...result, schema.$defs[key]], []);\n    const target = schema.$defs[schema.$ref];\n    return IsTransform(schema) || Visit(target, [...additional, ...references]);\n}\n// prettier-ignore\nfunction FromIterator(schema, references) {\n    return IsTransform(schema) || Visit(schema.items, references);\n}\n// prettier-ignore\nfunction FromNot(schema, references) {\n    return IsTransform(schema) || Visit(schema.not, references);\n}\n// prettier-ignore\nfunction FromObject(schema, references) {\n    return (IsTransform(schema) ||\n        Object.values(schema.properties).some((schema) => Visit(schema, references)) ||\n        (IsSchema(schema.additionalProperties) && Visit(schema.additionalProperties, references)));\n}\n// prettier-ignore\nfunction FromPromise(schema, references) {\n    return IsTransform(schema) || Visit(schema.item, references);\n}\n// prettier-ignore\nfunction FromRecord(schema, references) {\n    const pattern = Object.getOwnPropertyNames(schema.patternProperties)[0];\n    const property = schema.patternProperties[pattern];\n    return IsTransform(schema) || Visit(property, references) || (IsSchema(schema.additionalProperties) && IsTransform(schema.additionalProperties));\n}\n// prettier-ignore\nfunction FromRef(schema, references) {\n    if (IsTransform(schema))\n        return true;\n    return Visit(Deref(schema, references), references);\n}\n// prettier-ignore\nfunction FromThis(schema, references) {\n    if (IsTransform(schema))\n        return true;\n    return Visit(Deref(schema, references), references);\n}\n// prettier-ignore\nfunction FromTuple(schema, references) {\n    return IsTransform(schema) || (!IsUndefined(schema.items) && schema.items.some((schema) => Visit(schema, references)));\n}\n// prettier-ignore\nfunction FromUnion(schema, references) {\n    return IsTransform(schema) || schema.anyOf.some((schema) => Visit(schema, references));\n}\n// prettier-ignore\nfunction Visit(schema, references) {\n    const references_ = Pushref(schema, references);\n    const schema_ = schema;\n    if (schema.$id && visited.has(schema.$id))\n        return false;\n    if (schema.$id)\n        visited.add(schema.$id);\n    switch (schema[Kind]) {\n        case 'Array':\n            return FromArray(schema_, references_);\n        case 'AsyncIterator':\n            return FromAsyncIterator(schema_, references_);\n        case 'Constructor':\n            return FromConstructor(schema_, references_);\n        case 'Function':\n            return FromFunction(schema_, references_);\n        case 'Import':\n            return FromImport(schema_, references_);\n        case 'Intersect':\n            return FromIntersect(schema_, references_);\n        case 'Iterator':\n            return FromIterator(schema_, references_);\n        case 'Not':\n            return FromNot(schema_, references_);\n        case 'Object':\n            return FromObject(schema_, references_);\n        case 'Promise':\n            return FromPromise(schema_, references_);\n        case 'Record':\n            return FromRecord(schema_, references_);\n        case 'Ref':\n            return FromRef(schema_, references_);\n        case 'This':\n            return FromThis(schema_, references_);\n        case 'Tuple':\n            return FromTuple(schema_, references_);\n        case 'Union':\n            return FromUnion(schema_, references_);\n        default:\n            return IsTransform(schema);\n    }\n}\nconst visited = new Set();\n/** Returns true if this schema contains a transform codec */\nexport function HasTransform(schema, references) {\n    visited.clear();\n    return Visit(schema, references);\n}\n", "import { TransformEncode, TransformDecode, HasTransform, TransformDecodeCheckError, TransformEncodeCheckError } from '../value/transform/index.mjs';\nimport { Errors } from '../errors/index.mjs';\nimport { TypeSystemPolicy } from '../system/index.mjs';\nimport { TypeBoxError } from '../type/error/index.mjs';\nimport { Deref } from '../value/deref/index.mjs';\nimport { Hash } from '../value/hash/index.mjs';\nimport { Kind } from '../type/symbols/index.mjs';\nimport { TypeRegistry, FormatRegistry } from '../type/registry/index.mjs';\nimport { KeyOfPattern } from '../type/keyof/index.mjs';\nimport { ExtendsUndefinedCheck } from '../type/extends/extends-undefined.mjs';\nimport { Never } from '../type/never/index.mjs';\nimport { Ref } from '../type/ref/index.mjs';\n// ------------------------------------------------------------------\n// ValueGuard\n// ------------------------------------------------------------------\nimport { IsArray, IsString, IsNumber, IsBigInt } from '../value/guard/index.mjs';\n// ------------------------------------------------------------------\n// TypeGuard\n// ------------------------------------------------------------------\nimport { IsSchema } from '../type/guard/type.mjs';\n// ------------------------------------------------------------------\n// TypeCheck\n// ------------------------------------------------------------------\nexport class TypeCheck {\n    constructor(schema, references, checkFunc, code) {\n        this.schema = schema;\n        this.references = references;\n        this.checkFunc = checkFunc;\n        this.code = code;\n        this.hasTransform = HasTransform(schema, references);\n    }\n    /** Returns the generated assertion code used to validate this type. */\n    Code() {\n        return this.code;\n    }\n    /** Returns the schema type used to validate */\n    Schema() {\n        return this.schema;\n    }\n    /** Returns reference types used to validate */\n    References() {\n        return this.references;\n    }\n    /** Returns an iterator for each error in this value. */\n    Errors(value) {\n        return Errors(this.schema, this.references, value);\n    }\n    /** Returns true if the value matches the compiled type. */\n    Check(value) {\n        return this.checkFunc(value);\n    }\n    /** Decodes a value or throws if error */\n    Decode(value) {\n        if (!this.checkFunc(value))\n            throw new TransformDecodeCheckError(this.schema, value, this.Errors(value).First());\n        return (this.hasTransform ? TransformDecode(this.schema, this.references, value) : value);\n    }\n    /** Encodes a value or throws if error */\n    Encode(value) {\n        const encoded = this.hasTransform ? TransformEncode(this.schema, this.references, value) : value;\n        if (!this.checkFunc(encoded))\n            throw new TransformEncodeCheckError(this.schema, value, this.Errors(value).First());\n        return encoded;\n    }\n}\n// ------------------------------------------------------------------\n// Character\n// ------------------------------------------------------------------\nvar Character;\n(function (Character) {\n    function DollarSign(code) {\n        return code === 36;\n    }\n    Character.DollarSign = DollarSign;\n    function IsUnderscore(code) {\n        return code === 95;\n    }\n    Character.IsUnderscore = IsUnderscore;\n    function IsAlpha(code) {\n        return (code >= 65 && code <= 90) || (code >= 97 && code <= 122);\n    }\n    Character.IsAlpha = IsAlpha;\n    function IsNumeric(code) {\n        return code >= 48 && code <= 57;\n    }\n    Character.IsNumeric = IsNumeric;\n})(Character || (Character = {}));\n// ------------------------------------------------------------------\n// MemberExpression\n// ------------------------------------------------------------------\nvar MemberExpression;\n(function (MemberExpression) {\n    function IsFirstCharacterNumeric(value) {\n        if (value.length === 0)\n            return false;\n        return Character.IsNumeric(value.charCodeAt(0));\n    }\n    function IsAccessor(value) {\n        if (IsFirstCharacterNumeric(value))\n            return false;\n        for (let i = 0; i < value.length; i++) {\n            const code = value.charCodeAt(i);\n            const check = Character.IsAlpha(code) || Character.IsNumeric(code) || Character.DollarSign(code) || Character.IsUnderscore(code);\n            if (!check)\n                return false;\n        }\n        return true;\n    }\n    function EscapeHyphen(key) {\n        return key.replace(/'/g, \"\\\\'\");\n    }\n    function Encode(object, key) {\n        return IsAccessor(key) ? `${object}.${key}` : `${object}['${EscapeHyphen(key)}']`;\n    }\n    MemberExpression.Encode = Encode;\n})(MemberExpression || (MemberExpression = {}));\n// ------------------------------------------------------------------\n// Identifier\n// ------------------------------------------------------------------\nvar Identifier;\n(function (Identifier) {\n    function Encode($id) {\n        const buffer = [];\n        for (let i = 0; i < $id.length; i++) {\n            const code = $id.charCodeAt(i);\n            if (Character.IsNumeric(code) || Character.IsAlpha(code)) {\n                buffer.push($id.charAt(i));\n            }\n            else {\n                buffer.push(`_${code}_`);\n            }\n        }\n        return buffer.join('').replace(/__/g, '_');\n    }\n    Identifier.Encode = Encode;\n})(Identifier || (Identifier = {}));\n// ------------------------------------------------------------------\n// LiteralString\n// ------------------------------------------------------------------\nvar LiteralString;\n(function (LiteralString) {\n    function Escape(content) {\n        return content.replace(/'/g, \"\\\\'\");\n    }\n    LiteralString.Escape = Escape;\n})(LiteralString || (LiteralString = {}));\n// ------------------------------------------------------------------\n// Errors\n// ------------------------------------------------------------------\nexport class TypeCompilerUnknownTypeError extends TypeBoxError {\n    constructor(schema) {\n        super('Unknown type');\n        this.schema = schema;\n    }\n}\nexport class TypeCompilerTypeGuardError extends TypeBoxError {\n    constructor(schema) {\n        super('Preflight validation check failed to guard for the given schema');\n        this.schema = schema;\n    }\n}\n// ------------------------------------------------------------------\n// Policy\n// ------------------------------------------------------------------\nexport var Policy;\n(function (Policy) {\n    function IsExactOptionalProperty(value, key, expression) {\n        return TypeSystemPolicy.ExactOptionalPropertyTypes ? `('${key}' in ${value} ? ${expression} : true)` : `(${MemberExpression.Encode(value, key)} !== undefined ? ${expression} : true)`;\n    }\n    Policy.IsExactOptionalProperty = IsExactOptionalProperty;\n    function IsObjectLike(value) {\n        return !TypeSystemPolicy.AllowArrayObject ? `(typeof ${value} === 'object' && ${value} !== null && !Array.isArray(${value}))` : `(typeof ${value} === 'object' && ${value} !== null)`;\n    }\n    Policy.IsObjectLike = IsObjectLike;\n    function IsRecordLike(value) {\n        return !TypeSystemPolicy.AllowArrayObject\n            ? `(typeof ${value} === 'object' && ${value} !== null && !Array.isArray(${value}) && !(${value} instanceof Date) && !(${value} instanceof Uint8Array))`\n            : `(typeof ${value} === 'object' && ${value} !== null && !(${value} instanceof Date) && !(${value} instanceof Uint8Array))`;\n    }\n    Policy.IsRecordLike = IsRecordLike;\n    function IsNumberLike(value) {\n        return TypeSystemPolicy.AllowNaN ? `typeof ${value} === 'number'` : `Number.isFinite(${value})`;\n    }\n    Policy.IsNumberLike = IsNumberLike;\n    function IsVoidLike(value) {\n        return TypeSystemPolicy.AllowNullVoid ? `(${value} === undefined || ${value} === null)` : `${value} === undefined`;\n    }\n    Policy.IsVoidLike = IsVoidLike;\n})(Policy || (Policy = {}));\n/** Compiles Types for Runtime Type Checking */\nexport var TypeCompiler;\n(function (TypeCompiler) {\n    // ----------------------------------------------------------------\n    // Guards\n    // ----------------------------------------------------------------\n    function IsAnyOrUnknown(schema) {\n        return schema[Kind] === 'Any' || schema[Kind] === 'Unknown';\n    }\n    // ----------------------------------------------------------------\n    // Types\n    // ----------------------------------------------------------------\n    function* FromAny(schema, references, value) {\n        yield 'true';\n    }\n    function* FromArgument(schema, references, value) {\n        yield 'true';\n    }\n    function* FromArray(schema, references, value) {\n        yield `Array.isArray(${value})`;\n        const [parameter, accumulator] = [CreateParameter('value', 'any'), CreateParameter('acc', 'number')];\n        if (IsNumber(schema.maxItems))\n            yield `${value}.length <= ${schema.maxItems}`;\n        if (IsNumber(schema.minItems))\n            yield `${value}.length >= ${schema.minItems}`;\n        const elementExpression = CreateExpression(schema.items, references, 'value');\n        yield `${value}.every((${parameter}) => ${elementExpression})`;\n        if (IsSchema(schema.contains) || IsNumber(schema.minContains) || IsNumber(schema.maxContains)) {\n            const containsSchema = IsSchema(schema.contains) ? schema.contains : Never();\n            const checkExpression = CreateExpression(containsSchema, references, 'value');\n            const checkMinContains = IsNumber(schema.minContains) ? [`(count >= ${schema.minContains})`] : [];\n            const checkMaxContains = IsNumber(schema.maxContains) ? [`(count <= ${schema.maxContains})`] : [];\n            const checkCount = `const count = value.reduce((${accumulator}, ${parameter}) => ${checkExpression} ? acc + 1 : acc, 0)`;\n            const check = [`(count > 0)`, ...checkMinContains, ...checkMaxContains].join(' && ');\n            yield `((${parameter}) => { ${checkCount}; return ${check}})(${value})`;\n        }\n        if (schema.uniqueItems === true) {\n            const check = `const hashed = hash(element); if(set.has(hashed)) { return false } else { set.add(hashed) } } return true`;\n            const block = `const set = new Set(); for(const element of value) { ${check} }`;\n            yield `((${parameter}) => { ${block} )(${value})`;\n        }\n    }\n    function* FromAsyncIterator(schema, references, value) {\n        yield `(typeof value === 'object' && Symbol.asyncIterator in ${value})`;\n    }\n    function* FromBigInt(schema, references, value) {\n        yield `(typeof ${value} === 'bigint')`;\n        if (IsBigInt(schema.exclusiveMaximum))\n            yield `${value} < BigInt(${schema.exclusiveMaximum})`;\n        if (IsBigInt(schema.exclusiveMinimum))\n            yield `${value} > BigInt(${schema.exclusiveMinimum})`;\n        if (IsBigInt(schema.maximum))\n            yield `${value} <= BigInt(${schema.maximum})`;\n        if (IsBigInt(schema.minimum))\n            yield `${value} >= BigInt(${schema.minimum})`;\n        if (IsBigInt(schema.multipleOf))\n            yield `(${value} % BigInt(${schema.multipleOf})) === 0`;\n    }\n    function* FromBoolean(schema, references, value) {\n        yield `(typeof ${value} === 'boolean')`;\n    }\n    function* FromConstructor(schema, references, value) {\n        yield* Visit(schema.returns, references, `${value}.prototype`);\n    }\n    function* FromDate(schema, references, value) {\n        yield `(${value} instanceof Date) && Number.isFinite(${value}.getTime())`;\n        if (IsNumber(schema.exclusiveMaximumTimestamp))\n            yield `${value}.getTime() < ${schema.exclusiveMaximumTimestamp}`;\n        if (IsNumber(schema.exclusiveMinimumTimestamp))\n            yield `${value}.getTime() > ${schema.exclusiveMinimumTimestamp}`;\n        if (IsNumber(schema.maximumTimestamp))\n            yield `${value}.getTime() <= ${schema.maximumTimestamp}`;\n        if (IsNumber(schema.minimumTimestamp))\n            yield `${value}.getTime() >= ${schema.minimumTimestamp}`;\n        if (IsNumber(schema.multipleOfTimestamp))\n            yield `(${value}.getTime() % ${schema.multipleOfTimestamp}) === 0`;\n    }\n    function* FromFunction(schema, references, value) {\n        yield `(typeof ${value} === 'function')`;\n    }\n    function* FromImport(schema, references, value) {\n        const members = globalThis.Object.getOwnPropertyNames(schema.$defs).reduce((result, key) => {\n            return [...result, schema.$defs[key]];\n        }, []);\n        yield* Visit(Ref(schema.$ref), [...references, ...members], value);\n    }\n    function* FromInteger(schema, references, value) {\n        yield `Number.isInteger(${value})`;\n        if (IsNumber(schema.exclusiveMaximum))\n            yield `${value} < ${schema.exclusiveMaximum}`;\n        if (IsNumber(schema.exclusiveMinimum))\n            yield `${value} > ${schema.exclusiveMinimum}`;\n        if (IsNumber(schema.maximum))\n            yield `${value} <= ${schema.maximum}`;\n        if (IsNumber(schema.minimum))\n            yield `${value} >= ${schema.minimum}`;\n        if (IsNumber(schema.multipleOf))\n            yield `(${value} % ${schema.multipleOf}) === 0`;\n    }\n    function* FromIntersect(schema, references, value) {\n        const check1 = schema.allOf.map((schema) => CreateExpression(schema, references, value)).join(' && ');\n        if (schema.unevaluatedProperties === false) {\n            const keyCheck = CreateVariable(`${new RegExp(KeyOfPattern(schema))};`);\n            const check2 = `Object.getOwnPropertyNames(${value}).every(key => ${keyCheck}.test(key))`;\n            yield `(${check1} && ${check2})`;\n        }\n        else if (IsSchema(schema.unevaluatedProperties)) {\n            const keyCheck = CreateVariable(`${new RegExp(KeyOfPattern(schema))};`);\n            const check2 = `Object.getOwnPropertyNames(${value}).every(key => ${keyCheck}.test(key) || ${CreateExpression(schema.unevaluatedProperties, references, `${value}[key]`)})`;\n            yield `(${check1} && ${check2})`;\n        }\n        else {\n            yield `(${check1})`;\n        }\n    }\n    function* FromIterator(schema, references, value) {\n        yield `(typeof value === 'object' && Symbol.iterator in ${value})`;\n    }\n    function* FromLiteral(schema, references, value) {\n        if (typeof schema.const === 'number' || typeof schema.const === 'boolean') {\n            yield `(${value} === ${schema.const})`;\n        }\n        else {\n            yield `(${value} === '${LiteralString.Escape(schema.const)}')`;\n        }\n    }\n    function* FromNever(schema, references, value) {\n        yield `false`;\n    }\n    function* FromNot(schema, references, value) {\n        const expression = CreateExpression(schema.not, references, value);\n        yield `(!${expression})`;\n    }\n    function* FromNull(schema, references, value) {\n        yield `(${value} === null)`;\n    }\n    function* FromNumber(schema, references, value) {\n        yield Policy.IsNumberLike(value);\n        if (IsNumber(schema.exclusiveMaximum))\n            yield `${value} < ${schema.exclusiveMaximum}`;\n        if (IsNumber(schema.exclusiveMinimum))\n            yield `${value} > ${schema.exclusiveMinimum}`;\n        if (IsNumber(schema.maximum))\n            yield `${value} <= ${schema.maximum}`;\n        if (IsNumber(schema.minimum))\n            yield `${value} >= ${schema.minimum}`;\n        if (IsNumber(schema.multipleOf))\n            yield `(${value} % ${schema.multipleOf}) === 0`;\n    }\n    function* FromObject(schema, references, value) {\n        yield Policy.IsObjectLike(value);\n        if (IsNumber(schema.minProperties))\n            yield `Object.getOwnPropertyNames(${value}).length >= ${schema.minProperties}`;\n        if (IsNumber(schema.maxProperties))\n            yield `Object.getOwnPropertyNames(${value}).length <= ${schema.maxProperties}`;\n        const knownKeys = Object.getOwnPropertyNames(schema.properties);\n        for (const knownKey of knownKeys) {\n            const memberExpression = MemberExpression.Encode(value, knownKey);\n            const property = schema.properties[knownKey];\n            if (schema.required && schema.required.includes(knownKey)) {\n                yield* Visit(property, references, memberExpression);\n                if (ExtendsUndefinedCheck(property) || IsAnyOrUnknown(property))\n                    yield `('${knownKey}' in ${value})`;\n            }\n            else {\n                const expression = CreateExpression(property, references, memberExpression);\n                yield Policy.IsExactOptionalProperty(value, knownKey, expression);\n            }\n        }\n        if (schema.additionalProperties === false) {\n            if (schema.required && schema.required.length === knownKeys.length) {\n                yield `Object.getOwnPropertyNames(${value}).length === ${knownKeys.length}`;\n            }\n            else {\n                const keys = `[${knownKeys.map((key) => `'${key}'`).join(', ')}]`;\n                yield `Object.getOwnPropertyNames(${value}).every(key => ${keys}.includes(key))`;\n            }\n        }\n        if (typeof schema.additionalProperties === 'object') {\n            const expression = CreateExpression(schema.additionalProperties, references, `${value}[key]`);\n            const keys = `[${knownKeys.map((key) => `'${key}'`).join(', ')}]`;\n            yield `(Object.getOwnPropertyNames(${value}).every(key => ${keys}.includes(key) || ${expression}))`;\n        }\n    }\n    function* FromPromise(schema, references, value) {\n        yield `${value} instanceof Promise`;\n    }\n    function* FromRecord(schema, references, value) {\n        yield Policy.IsRecordLike(value);\n        if (IsNumber(schema.minProperties))\n            yield `Object.getOwnPropertyNames(${value}).length >= ${schema.minProperties}`;\n        if (IsNumber(schema.maxProperties))\n            yield `Object.getOwnPropertyNames(${value}).length <= ${schema.maxProperties}`;\n        const [patternKey, patternSchema] = Object.entries(schema.patternProperties)[0];\n        const variable = CreateVariable(`${new RegExp(patternKey)}`);\n        const check1 = CreateExpression(patternSchema, references, 'value');\n        const check2 = IsSchema(schema.additionalProperties) ? CreateExpression(schema.additionalProperties, references, value) : schema.additionalProperties === false ? 'false' : 'true';\n        const expression = `(${variable}.test(key) ? ${check1} : ${check2})`;\n        yield `(Object.entries(${value}).every(([key, value]) => ${expression}))`;\n    }\n    function* FromRef(schema, references, value) {\n        const target = Deref(schema, references);\n        // Reference: If we have seen this reference before we can just yield and return the function call.\n        // If this isn't the case we defer to visit to generate and set the function for subsequent passes.\n        if (state.functions.has(schema.$ref))\n            return yield `${CreateFunctionName(schema.$ref)}(${value})`;\n        yield* Visit(target, references, value);\n    }\n    function* FromRegExp(schema, references, value) {\n        const variable = CreateVariable(`${new RegExp(schema.source, schema.flags)};`);\n        yield `(typeof ${value} === 'string')`;\n        if (IsNumber(schema.maxLength))\n            yield `${value}.length <= ${schema.maxLength}`;\n        if (IsNumber(schema.minLength))\n            yield `${value}.length >= ${schema.minLength}`;\n        yield `${variable}.test(${value})`;\n    }\n    function* FromString(schema, references, value) {\n        yield `(typeof ${value} === 'string')`;\n        if (IsNumber(schema.maxLength))\n            yield `${value}.length <= ${schema.maxLength}`;\n        if (IsNumber(schema.minLength))\n            yield `${value}.length >= ${schema.minLength}`;\n        if (schema.pattern !== undefined) {\n            const variable = CreateVariable(`${new RegExp(schema.pattern)};`);\n            yield `${variable}.test(${value})`;\n        }\n        if (schema.format !== undefined) {\n            yield `format('${schema.format}', ${value})`;\n        }\n    }\n    function* FromSymbol(schema, references, value) {\n        yield `(typeof ${value} === 'symbol')`;\n    }\n    function* FromTemplateLiteral(schema, references, value) {\n        yield `(typeof ${value} === 'string')`;\n        const variable = CreateVariable(`${new RegExp(schema.pattern)};`);\n        yield `${variable}.test(${value})`;\n    }\n    function* FromThis(schema, references, value) {\n        // Note: This types are assured to be hoisted prior to this call. Just yield the function.\n        yield `${CreateFunctionName(schema.$ref)}(${value})`;\n    }\n    function* FromTuple(schema, references, value) {\n        yield `Array.isArray(${value})`;\n        if (schema.items === undefined)\n            return yield `${value}.length === 0`;\n        yield `(${value}.length === ${schema.maxItems})`;\n        for (let i = 0; i < schema.items.length; i++) {\n            const expression = CreateExpression(schema.items[i], references, `${value}[${i}]`);\n            yield `${expression}`;\n        }\n    }\n    function* FromUndefined(schema, references, value) {\n        yield `${value} === undefined`;\n    }\n    function* FromUnion(schema, references, value) {\n        const expressions = schema.anyOf.map((schema) => CreateExpression(schema, references, value));\n        yield `(${expressions.join(' || ')})`;\n    }\n    function* FromUint8Array(schema, references, value) {\n        yield `${value} instanceof Uint8Array`;\n        if (IsNumber(schema.maxByteLength))\n            yield `(${value}.length <= ${schema.maxByteLength})`;\n        if (IsNumber(schema.minByteLength))\n            yield `(${value}.length >= ${schema.minByteLength})`;\n    }\n    function* FromUnknown(schema, references, value) {\n        yield 'true';\n    }\n    function* FromVoid(schema, references, value) {\n        yield Policy.IsVoidLike(value);\n    }\n    function* FromKind(schema, references, value) {\n        const instance = state.instances.size;\n        state.instances.set(instance, schema);\n        yield `kind('${schema[Kind]}', ${instance}, ${value})`;\n    }\n    function* Visit(schema, references, value, useHoisting = true) {\n        const references_ = IsString(schema.$id) ? [...references, schema] : references;\n        const schema_ = schema;\n        // --------------------------------------------------------------\n        // Hoisting\n        // --------------------------------------------------------------\n        if (useHoisting && IsString(schema.$id)) {\n            const functionName = CreateFunctionName(schema.$id);\n            if (state.functions.has(functionName)) {\n                return yield `${functionName}(${value})`;\n            }\n            else {\n                // Note: In the case of cyclic types, we need to create a 'functions' record\n                // to prevent infinitely re-visiting the CreateFunction. Subsequent attempts\n                // to visit will be caught by the above condition.\n                state.functions.set(functionName, '<deferred>');\n                const functionCode = CreateFunction(functionName, schema, references, 'value', false);\n                state.functions.set(functionName, functionCode);\n                return yield `${functionName}(${value})`;\n            }\n        }\n        switch (schema_[Kind]) {\n            case 'Any':\n                return yield* FromAny(schema_, references_, value);\n            case 'Argument':\n                return yield* FromArgument(schema_, references_, value);\n            case 'Array':\n                return yield* FromArray(schema_, references_, value);\n            case 'AsyncIterator':\n                return yield* FromAsyncIterator(schema_, references_, value);\n            case 'BigInt':\n                return yield* FromBigInt(schema_, references_, value);\n            case 'Boolean':\n                return yield* FromBoolean(schema_, references_, value);\n            case 'Constructor':\n                return yield* FromConstructor(schema_, references_, value);\n            case 'Date':\n                return yield* FromDate(schema_, references_, value);\n            case 'Function':\n                return yield* FromFunction(schema_, references_, value);\n            case 'Import':\n                return yield* FromImport(schema_, references_, value);\n            case 'Integer':\n                return yield* FromInteger(schema_, references_, value);\n            case 'Intersect':\n                return yield* FromIntersect(schema_, references_, value);\n            case 'Iterator':\n                return yield* FromIterator(schema_, references_, value);\n            case 'Literal':\n                return yield* FromLiteral(schema_, references_, value);\n            case 'Never':\n                return yield* FromNever(schema_, references_, value);\n            case 'Not':\n                return yield* FromNot(schema_, references_, value);\n            case 'Null':\n                return yield* FromNull(schema_, references_, value);\n            case 'Number':\n                return yield* FromNumber(schema_, references_, value);\n            case 'Object':\n                return yield* FromObject(schema_, references_, value);\n            case 'Promise':\n                return yield* FromPromise(schema_, references_, value);\n            case 'Record':\n                return yield* FromRecord(schema_, references_, value);\n            case 'Ref':\n                return yield* FromRef(schema_, references_, value);\n            case 'RegExp':\n                return yield* FromRegExp(schema_, references_, value);\n            case 'String':\n                return yield* FromString(schema_, references_, value);\n            case 'Symbol':\n                return yield* FromSymbol(schema_, references_, value);\n            case 'TemplateLiteral':\n                return yield* FromTemplateLiteral(schema_, references_, value);\n            case 'This':\n                return yield* FromThis(schema_, references_, value);\n            case 'Tuple':\n                return yield* FromTuple(schema_, references_, value);\n            case 'Undefined':\n                return yield* FromUndefined(schema_, references_, value);\n            case 'Union':\n                return yield* FromUnion(schema_, references_, value);\n            case 'Uint8Array':\n                return yield* FromUint8Array(schema_, references_, value);\n            case 'Unknown':\n                return yield* FromUnknown(schema_, references_, value);\n            case 'Void':\n                return yield* FromVoid(schema_, references_, value);\n            default:\n                if (!TypeRegistry.Has(schema_[Kind]))\n                    throw new TypeCompilerUnknownTypeError(schema);\n                return yield* FromKind(schema_, references_, value);\n        }\n    }\n    // ----------------------------------------------------------------\n    // Compiler State\n    // ----------------------------------------------------------------\n    // prettier-ignore\n    const state = {\n        language: 'javascript', // target language\n        functions: new Map(), // local functions\n        variables: new Map(), // local variables\n        instances: new Map() // exterior kind instances\n    };\n    // ----------------------------------------------------------------\n    // Compiler Factory\n    // ----------------------------------------------------------------\n    function CreateExpression(schema, references, value, useHoisting = true) {\n        return `(${[...Visit(schema, references, value, useHoisting)].join(' && ')})`;\n    }\n    function CreateFunctionName($id) {\n        return `check_${Identifier.Encode($id)}`;\n    }\n    function CreateVariable(expression) {\n        const variableName = `local_${state.variables.size}`;\n        state.variables.set(variableName, `const ${variableName} = ${expression}`);\n        return variableName;\n    }\n    function CreateFunction(name, schema, references, value, useHoisting = true) {\n        const [newline, pad] = ['\\n', (length) => ''.padStart(length, ' ')];\n        const parameter = CreateParameter('value', 'any');\n        const returns = CreateReturns('boolean');\n        const expression = [...Visit(schema, references, value, useHoisting)].map((expression) => `${pad(4)}${expression}`).join(` &&${newline}`);\n        return `function ${name}(${parameter})${returns} {${newline}${pad(2)}return (${newline}${expression}${newline}${pad(2)})\\n}`;\n    }\n    function CreateParameter(name, type) {\n        const annotation = state.language === 'typescript' ? `: ${type}` : '';\n        return `${name}${annotation}`;\n    }\n    function CreateReturns(type) {\n        return state.language === 'typescript' ? `: ${type}` : '';\n    }\n    // ----------------------------------------------------------------\n    // Compile\n    // ----------------------------------------------------------------\n    function Build(schema, references, options) {\n        const functionCode = CreateFunction('check', schema, references, 'value'); // will populate functions and variables\n        const parameter = CreateParameter('value', 'any');\n        const returns = CreateReturns('boolean');\n        const functions = [...state.functions.values()];\n        const variables = [...state.variables.values()];\n        // prettier-ignore\n        const checkFunction = IsString(schema.$id) // ensure top level schemas with $id's are hoisted\n            ? `return function check(${parameter})${returns} {\\n  return ${CreateFunctionName(schema.$id)}(value)\\n}`\n            : `return ${functionCode}`;\n        return [...variables, ...functions, checkFunction].join('\\n');\n    }\n    /** Generates the code used to assert this type and returns it as a string */\n    function Code(...args) {\n        const defaults = { language: 'javascript' };\n        // prettier-ignore\n        const [schema, references, options] = (args.length === 2 && IsArray(args[1]) ? [args[0], args[1], defaults] :\n            args.length === 2 && !IsArray(args[1]) ? [args[0], [], args[1]] :\n                args.length === 3 ? [args[0], args[1], args[2]] :\n                    args.length === 1 ? [args[0], [], defaults] :\n                        [null, [], defaults]);\n        // compiler-reset\n        state.language = options.language;\n        state.variables.clear();\n        state.functions.clear();\n        state.instances.clear();\n        if (!IsSchema(schema))\n            throw new TypeCompilerTypeGuardError(schema);\n        for (const schema of references)\n            if (!IsSchema(schema))\n                throw new TypeCompilerTypeGuardError(schema);\n        return Build(schema, references, options);\n    }\n    TypeCompiler.Code = Code;\n    /** Compiles a TypeBox type for optimal runtime type checking. Types must be valid TypeBox types of TSchema */\n    function Compile(schema, references = []) {\n        const generatedCode = Code(schema, references, { language: 'javascript' });\n        const compiledFunction = globalThis.Function('kind', 'format', 'hash', generatedCode);\n        const instances = new Map(state.instances);\n        function typeRegistryFunction(kind, instance, value) {\n            if (!TypeRegistry.Has(kind) || !instances.has(instance))\n                return false;\n            const checkFunc = TypeRegistry.Get(kind);\n            const schema = instances.get(instance);\n            return checkFunc(schema, value);\n        }\n        function formatRegistryFunction(format, value) {\n            if (!FormatRegistry.Has(format))\n                return false;\n            const checkFunc = FormatRegistry.Get(format);\n            return checkFunc(value);\n        }\n        function hashFunction(value) {\n            return Hash(value);\n        }\n        const checkFunction = compiledFunction(typeRegistryFunction, formatRegistryFunction, hashFunction);\n        return new TypeCheck(schema, references, checkFunction, generatedCode);\n    }\n    TypeCompiler.Compile = Compile;\n})(TypeCompiler || (TypeCompiler = {}));\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,IAAM,8BAAN,cAA0C,aAAa;AAAA,EAC1D,YAAY,MAAM;AACd,UAAM,wBAAwB,IAAI,YAAY;AAAA,EAClD;AACJ;AACO,IAAM,4BAAN,cAAwC,aAAa;AAAA,EACxD,YAAY,MAAM;AACd,UAAM,4BAA4B,IAAI,YAAY;AAAA,EACtD;AACJ;AAEO,IAAI;AAAA,CACV,SAAUA,aAAY;AAEnB,WAAS,KAAK,MAAM,OAAO;AACvB,QAAI,aAAa,IAAI,IAAI;AACrB,YAAM,IAAI,4BAA4B,IAAI;AAC9C,iBAAa,IAAI,MAAM,KAAK;AAC5B,WAAO,CAAC,UAAU,CAAC,MAAM,OAAO,EAAE,GAAG,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC;AAAA,EAChE;AACA,EAAAA,YAAW,OAAO;AAElB,WAAS,OAAO,QAAQ,OAAO;AAC3B,QAAI,eAAe,IAAI,MAAM;AACzB,YAAM,IAAI,0BAA0B,MAAM;AAC9C,mBAAe,IAAI,QAAQ,KAAK;AAChC,WAAO;AAAA,EACX;AACA,EAAAA,YAAW,SAAS;AACxB,GAAG,eAAe,aAAa,CAAC,EAAE;;;ACjC3B,SAAS,qBAAqB,OAAO;AACxC,UAAQ,MAAM,WAAW;AAAA,IACrB,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO,0CAA0C,MAAM,OAAO,WAAW;AAAA,IAC7E,KAAK,eAAe;AAChB,aAAO,sCAAsC,MAAM,OAAO,WAAW;AAAA,IACzE,KAAK,eAAe;AAChB,aAAO,gDAAgD,MAAM,OAAO,QAAQ;AAAA,IAChF,KAAK,eAAe;AAChB,aAAO,mDAAmD,MAAM,OAAO,QAAQ;AAAA,IACnF,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO,mCAAmC,MAAM,OAAO,gBAAgB;AAAA,IAC3E,KAAK,eAAe;AAChB,aAAO,sCAAsC,MAAM,OAAO,gBAAgB;AAAA,IAC9E,KAAK,eAAe;AAChB,aAAO,0CAA0C,MAAM,OAAO,OAAO;AAAA,IACzE,KAAK,eAAe;AAChB,aAAO,6CAA6C,MAAM,OAAO,OAAO;AAAA,IAC5E,KAAK,eAAe;AAChB,aAAO,uCAAuC,MAAM,OAAO,UAAU;AAAA,IACzE,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO,8CAA8C,MAAM,OAAO,yBAAyB;AAAA,IAC/F,KAAK,eAAe;AAChB,aAAO,2CAA2C,MAAM,OAAO,yBAAyB;AAAA,IAC5F,KAAK,eAAe;AAChB,aAAO,qDAAqD,MAAM,OAAO,gBAAgB;AAAA,IAC7F,KAAK,eAAe;AAChB,aAAO,kDAAkD,MAAM,OAAO,gBAAgB;AAAA,IAC1F,KAAK,eAAe;AAChB,aAAO,+CAA+C,MAAM,OAAO,mBAAmB;AAAA,IAC1F,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO,oCAAoC,MAAM,OAAO,gBAAgB;AAAA,IAC5E,KAAK,eAAe;AAChB,aAAO,uCAAuC,MAAM,OAAO,gBAAgB;AAAA,IAC/E,KAAK,eAAe;AAChB,aAAO,2CAA2C,MAAM,OAAO,OAAO;AAAA,IAC1E,KAAK,eAAe;AAChB,aAAO,8CAA8C,MAAM,OAAO,OAAO;AAAA,IAC7E,KAAK,eAAe;AAChB,aAAO,wCAAwC,MAAM,OAAO,UAAU;AAAA,IAC1E,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO,YAAY,OAAO,MAAM,OAAO,UAAU,WAAW,IAAI,MAAM,OAAO,KAAK,MAAM,MAAM,OAAO,KAAK;AAAA,IAC9G,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO,mCAAmC,MAAM,OAAO,gBAAgB;AAAA,IAC3E,KAAK,eAAe;AAChB,aAAO,sCAAsC,MAAM,OAAO,gBAAgB;AAAA,IAC9E,KAAK,eAAe;AAChB,aAAO,0CAA0C,MAAM,OAAO,OAAO;AAAA,IACzE,KAAK,eAAe;AAChB,aAAO,6CAA6C,MAAM,OAAO,OAAO;AAAA,IAC5E,KAAK,eAAe;AAChB,aAAO,uCAAuC,MAAM,OAAO,UAAU;AAAA,IACzE,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO,wCAAwC,MAAM,OAAO,aAAa;AAAA,IAC7E,KAAK,eAAe;AAChB,aAAO,oCAAoC,MAAM,OAAO,aAAa;AAAA,IACzE,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO,mBAAmB,MAAM,OAAO,MAAM;AAAA,IACjD,KAAK,eAAe;AAChB,aAAO,6BAA6B,MAAM,OAAO,MAAM;AAAA,IAC3D,KAAK,eAAe;AAChB,aAAO,2CAA2C,MAAM,OAAO,SAAS;AAAA,IAC5E,KAAK,eAAe;AAChB,aAAO,8CAA8C,MAAM,OAAO,SAAS;AAAA,IAC/E,KAAK,eAAe;AAChB,aAAO,6BAA6B,MAAM,OAAO,OAAO;AAAA,IAC5D,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO,0BAA0B,MAAM,OAAO,YAAY,CAAC;AAAA,IAC/D,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO,yCAAyC,MAAM,OAAO,aAAa;AAAA,IAC9E,KAAK,eAAe;AAChB,aAAO,4CAA4C,MAAM,OAAO,aAAa;AAAA,IACjF,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO;AAAA,IACX,KAAK,eAAe;AAChB,aAAO,kBAAkB,MAAM,OAAO,IAAI,CAAC;AAAA,IAC/C;AACI,aAAO;AAAA,EACf;AACJ;AAEA,IAAI,gBAAgB;AAMb,SAAS,mBAAmB;AAC/B,SAAO;AACX;;;AC/IO,IAAM,uBAAN,cAAmC,aAAa;AAAA,EACnD,YAAY,QAAQ;AAChB,UAAM,0CAA0C,OAAO,IAAI,GAAG;AAC9D,SAAK,SAAS;AAAA,EAClB;AACJ;AACA,SAAS,QAAQ,QAAQ,YAAY;AACjC,QAAM,SAAS,WAAW,KAAK,CAACC,YAAWA,QAAO,QAAQ,OAAO,IAAI;AACrE,MAAI,WAAW;AACX,UAAM,IAAI,qBAAqB,MAAM;AACzC,SAAO,MAAM,QAAQ,UAAU;AACnC;AAEO,SAAS,QAAQ,QAAQ,YAAY;AACxC,MAAI,CAAC,SAAS,OAAO,GAAG,KAAK,WAAW,KAAK,CAAC,WAAW,OAAO,QAAQ,OAAO,GAAG;AAC9E,WAAO;AACX,aAAW,KAAK,MAAM;AACtB,SAAO;AACX;AAEO,SAAS,MAAM,QAAQ,YAAY;AAEtC,SAAQ,OAAO,IAAI,MAAM,UAAU,OAAO,IAAI,MAAM,QAC9C,QAAQ,QAAQ,UAAU,IAC1B;AACV;;;ACvBO,IAAM,iBAAN,cAA6B,aAAa;AAAA,EAC7C,YAAY,OAAO;AACf,UAAM,sBAAsB;AAC5B,SAAK,QAAQ;AAAA,EACjB;AACJ;AAIA,IAAI;AAAA,CACH,SAAUC,aAAY;AACnB,EAAAA,YAAWA,YAAW,WAAW,IAAI,CAAC,IAAI;AAC1C,EAAAA,YAAWA,YAAW,MAAM,IAAI,CAAC,IAAI;AACrC,EAAAA,YAAWA,YAAW,SAAS,IAAI,CAAC,IAAI;AACxC,EAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACvC,EAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACvC,EAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACvC,EAAAA,YAAWA,YAAW,OAAO,IAAI,CAAC,IAAI;AACtC,EAAAA,YAAWA,YAAW,MAAM,IAAI,CAAC,IAAI;AACrC,EAAAA,YAAWA,YAAW,YAAY,IAAI,CAAC,IAAI;AAC3C,EAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACvC,EAAAA,YAAWA,YAAW,QAAQ,IAAI,EAAE,IAAI;AAC5C,GAAG,eAAe,aAAa,CAAC,EAAE;AAIlC,IAAI,cAAc,OAAO,sBAAsB;AAC/C,IAAM,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,eAAe,GAAG;AAAA,EAAO;AAAA;AAAmC,CAAC;AAC3F,IAAM,QAAQ,MAAM,KAAK,EAAE,QAAQ,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,OAAO,CAAC,CAAC;AACjE,IAAM,MAAM,IAAI,aAAa,CAAC;AAC9B,IAAM,QAAQ,IAAI,SAAS,IAAI,MAAM;AACrC,IAAM,SAAS,IAAI,WAAW,IAAI,MAAM;AAIxC,UAAU,cAAc,OAAO;AAC3B,QAAM,YAAY,UAAU,IAAI,IAAI,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,IAAI,CAAC,IAAI,CAAC;AAClF,WAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,UAAO,SAAU,KAAK,YAAY,IAAI,KAAO;AAAA,EACjD;AACJ;AAIA,SAAS,UAAU,OAAO;AACtB,UAAQ,WAAW,KAAK;AACxB,aAAW,QAAQ,OAAO;AACtB,UAAM,IAAI;AAAA,EACd;AACJ;AACA,SAAS,YAAY,OAAO;AACxB,UAAQ,WAAW,OAAO;AAC1B,UAAQ,QAAQ,IAAI,CAAC;AACzB;AACA,SAAS,WAAW,OAAO;AACvB,UAAQ,WAAW,MAAM;AACzB,QAAM,YAAY,GAAG,KAAK;AAC1B,aAAW,QAAQ,QAAQ;AACvB,YAAQ,IAAI;AAAA,EAChB;AACJ;AACA,SAAS,SAAS,OAAO;AACrB,UAAQ,WAAW,IAAI;AACvB,QAAM,MAAM,QAAQ,CAAC;AACzB;AACA,SAAS,SAAS,OAAO;AACrB,UAAQ,WAAW,IAAI;AAC3B;AACA,SAAS,WAAW,OAAO;AACvB,UAAQ,WAAW,MAAM;AACzB,QAAM,WAAW,GAAG,KAAK;AACzB,aAAW,QAAQ,QAAQ;AACvB,YAAQ,IAAI;AAAA,EAChB;AACJ;AACA,SAAS,WAAW,OAAO;AACvB,UAAQ,WAAW,MAAM;AACzB,aAAW,OAAO,WAAW,OAAO,oBAAoB,KAAK,EAAE,KAAK,GAAG;AACnE,UAAM,GAAG;AACT,UAAM,MAAM,GAAG,CAAC;AAAA,EACpB;AACJ;AACA,SAAS,WAAW,OAAO;AACvB,UAAQ,WAAW,MAAM;AACzB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,eAAW,QAAQ,cAAc,MAAM,WAAW,CAAC,CAAC,GAAG;AACnD,cAAQ,IAAI;AAAA,IAChB;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,OAAO;AACvB,UAAQ,WAAW,MAAM;AACzB,QAAM,MAAM,WAAW;AAC3B;AACA,SAAS,eAAe,OAAO;AAC3B,UAAQ,WAAW,UAAU;AAC7B,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAQ,MAAM,CAAC,CAAC;AAAA,EACpB;AACJ;AACA,SAAS,cAAc,OAAO;AAC1B,SAAO,QAAQ,WAAW,SAAS;AACvC;AACA,SAAS,MAAM,OAAO;AAClB,MAAI,QAAQ,KAAK;AACb,WAAO,UAAU,KAAK;AAC1B,MAAI,UAAU,KAAK;AACf,WAAO,YAAY,KAAK;AAC5B,MAAI,SAAS,KAAK;AACd,WAAO,WAAW,KAAK;AAC3B,MAAI,OAAO,KAAK;AACZ,WAAO,SAAS,KAAK;AACzB,MAAI,OAAO,KAAK;AACZ,WAAO,SAAS,KAAK;AACzB,MAAI,SAAS,KAAK;AACd,WAAO,WAAW,KAAK;AAC3B,MAAI,SAAS,KAAK;AACd,WAAO,WAAW,KAAK;AAC3B,MAAI,SAAS,KAAK;AACd,WAAO,WAAW,KAAK;AAC3B,MAAI,SAAS,KAAK;AACd,WAAO,WAAW,KAAK;AAC3B,MAAI,aAAa,KAAK;AAClB,WAAO,eAAe,KAAK;AAC/B,MAAI,YAAY,KAAK;AACjB,WAAO,cAAc,KAAK;AAC9B,QAAM,IAAI,eAAe,KAAK;AAClC;AACA,SAAS,QAAQ,MAAM;AACnB,gBAAc,cAAc,MAAM,IAAI;AACtC,gBAAe,cAAc,QAAS;AAC1C;AAKO,SAAS,KAAK,OAAO;AACxB,gBAAc,OAAO,sBAAsB;AAC3C,QAAM,KAAK;AACX,SAAO;AACX;;;AC7HO,IAAM,6BAAN,cAAyC,aAAa;AAAA,EACzD,YAAY,QAAQ;AAChB,UAAM,cAAc;AACpB,SAAK,SAAS;AAAA,EAClB;AACJ;AAIA,SAAS,eAAe,QAAQ;AAC5B,SAAO,OAAO,IAAI,MAAM,SAAS,OAAO,IAAI,MAAM;AACtD;AAIA,SAAS,UAAU,OAAO;AACtB,SAAO,UAAU;AACrB;AAIA,SAAS,QAAQ,QAAQ,YAAY,OAAO;AACxC,SAAO;AACX;AACA,SAAS,aAAa,QAAQ,YAAY,OAAO;AAC7C,SAAO;AACX;AACA,SAAS,UAAU,QAAQ,YAAY,OAAO;AAC1C,MAAI,CAAC,QAAQ,KAAK;AACd,WAAO;AACX,MAAI,UAAU,OAAO,QAAQ,KAAK,EAAE,MAAM,UAAU,OAAO,WAAW;AAClE,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,QAAQ,KAAK,EAAE,MAAM,UAAU,OAAO,WAAW;AAClE,WAAO;AAAA,EACX;AACA,MAAI,CAAC,MAAM,MAAM,CAACC,WAAUC,OAAM,OAAO,OAAO,YAAYD,MAAK,CAAC,GAAG;AACjE,WAAO;AAAA,EACX;AAEA,MAAI,OAAO,gBAAgB,QAAQ,CAAG,WAAY;AAAE,UAAM,MAAM,oBAAI,IAAI;AAAG,eAAW,WAAW,OAAO;AACpG,YAAM,SAAS,KAAK,OAAO;AAC3B,UAAI,IAAI,IAAI,MAAM,GAAG;AACjB,eAAO;AAAA,MACX,OACK;AACD,YAAI,IAAI,MAAM;AAAA,MAClB;AAAA,IACJ;AAAE,WAAO;AAAA,EAAM,EAAG,GAAI;AAClB,WAAO;AAAA,EACX;AAEA,MAAI,EAAE,UAAU,OAAO,QAAQ,KAAK,SAAS,OAAO,WAAW,KAAK,SAAS,OAAO,WAAW,IAAI;AAC/F,WAAO;AAAA,EACX;AACA,QAAM,iBAAiB,UAAU,OAAO,QAAQ,IAAI,OAAO,WAAW,MAAM;AAC5E,QAAM,gBAAgB,MAAM,OAAO,CAAC,KAAKA,WAAWC,OAAM,gBAAgB,YAAYD,MAAK,IAAI,MAAM,IAAI,KAAM,CAAC;AAChH,MAAI,kBAAkB,GAAG;AACrB,WAAO;AAAA,EACX;AACA,MAAI,SAAS,OAAO,WAAW,KAAK,gBAAgB,OAAO,aAAa;AACpE,WAAO;AAAA,EACX;AACA,MAAI,SAAS,OAAO,WAAW,KAAK,gBAAgB,OAAO,aAAa;AACpE,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,QAAQ,YAAY,OAAO;AAClD,SAAO,gBAAgB,KAAK;AAChC;AACA,SAAS,WAAW,QAAQ,YAAY,OAAO;AAC3C,MAAI,CAAC,SAAS,KAAK;AACf,WAAO;AACX,MAAI,UAAU,OAAO,gBAAgB,KAAK,EAAE,QAAQ,OAAO,mBAAmB;AAC1E,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,gBAAgB,KAAK,EAAE,QAAQ,OAAO,mBAAmB;AAC1E,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,OAAO,KAAK,EAAE,SAAS,OAAO,UAAU;AACzD,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,OAAO,KAAK,EAAE,SAAS,OAAO,UAAU;AACzD,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,UAAU,KAAK,EAAE,QAAQ,OAAO,eAAe,OAAO,CAAC,IAAI;AAC5E,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,YAAY,QAAQ,YAAY,OAAO;AAC5C,SAAO,UAAU,KAAK;AAC1B;AACA,SAAS,gBAAgB,QAAQ,YAAY,OAAO;AAChD,SAAOC,OAAM,OAAO,SAAS,YAAY,MAAM,SAAS;AAC5D;AACA,SAAS,SAAS,QAAQ,YAAY,OAAO;AACzC,MAAI,CAAC,OAAO,KAAK;AACb,WAAO;AACX,MAAI,UAAU,OAAO,yBAAyB,KAAK,EAAE,MAAM,QAAQ,IAAI,OAAO,4BAA4B;AACtG,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,yBAAyB,KAAK,EAAE,MAAM,QAAQ,IAAI,OAAO,4BAA4B;AACtG,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,gBAAgB,KAAK,EAAE,MAAM,QAAQ,KAAK,OAAO,mBAAmB;AACrF,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,gBAAgB,KAAK,EAAE,MAAM,QAAQ,KAAK,OAAO,mBAAmB;AACrF,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,mBAAmB,KAAK,EAAE,MAAM,QAAQ,IAAI,OAAO,wBAAwB,IAAI;AAChG,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,aAAa,QAAQ,YAAY,OAAO;AAC7C,SAAO,WAAW,KAAK;AAC3B;AACA,SAAS,WAAW,QAAQ,YAAY,OAAO;AAC3C,QAAM,cAAc,WAAW,OAAO,OAAO,OAAO,KAAK;AACzD,QAAM,SAAS,OAAO,MAAM,OAAO,IAAI;AACvC,SAAOA,OAAM,QAAQ,CAAC,GAAG,YAAY,GAAG,WAAW,GAAG,KAAK;AAC/D;AACA,SAAS,YAAY,QAAQ,YAAY,OAAO;AAC5C,MAAI,CAAC,UAAU,KAAK,GAAG;AACnB,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,gBAAgB,KAAK,EAAE,QAAQ,OAAO,mBAAmB;AAC1E,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,gBAAgB,KAAK,EAAE,QAAQ,OAAO,mBAAmB;AAC1E,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,OAAO,KAAK,EAAE,SAAS,OAAO,UAAU;AACzD,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,OAAO,KAAK,EAAE,SAAS,OAAO,UAAU;AACzD,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,UAAU,KAAK,EAAE,QAAQ,OAAO,eAAe,IAAI;AACpE,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,cAAc,QAAQ,YAAY,OAAO;AAC9C,QAAM,SAAS,OAAO,MAAM,MAAM,CAACC,YAAWD,OAAMC,SAAQ,YAAY,KAAK,CAAC;AAC9E,MAAI,OAAO,0BAA0B,OAAO;AACxC,UAAM,aAAa,IAAI,OAAO,aAAa,MAAM,CAAC;AAClD,UAAM,SAAS,OAAO,oBAAoB,KAAK,EAAE,MAAM,CAAC,QAAQ,WAAW,KAAK,GAAG,CAAC;AACpF,WAAO,UAAU;AAAA,EACrB,WACS,SAAS,OAAO,qBAAqB,GAAG;AAC7C,UAAM,WAAW,IAAI,OAAO,aAAa,MAAM,CAAC;AAChD,UAAM,SAAS,OAAO,oBAAoB,KAAK,EAAE,MAAM,CAAC,QAAQ,SAAS,KAAK,GAAG,KAAKD,OAAM,OAAO,uBAAuB,YAAY,MAAM,GAAG,CAAC,CAAC;AACjJ,WAAO,UAAU;AAAA,EACrB,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,aAAa,QAAQ,YAAY,OAAO;AAC7C,SAAO,WAAW,KAAK;AAC3B;AACA,SAAS,YAAY,QAAQ,YAAY,OAAO;AAC5C,SAAO,UAAU,OAAO;AAC5B;AACA,SAAS,UAAU,QAAQ,YAAY,OAAO;AAC1C,SAAO;AACX;AACA,SAAS,QAAQ,QAAQ,YAAY,OAAO;AACxC,SAAO,CAACA,OAAM,OAAO,KAAK,YAAY,KAAK;AAC/C;AACA,SAAS,SAAS,QAAQ,YAAY,OAAO;AACzC,SAAO,OAAO,KAAK;AACvB;AACA,SAAS,WAAW,QAAQ,YAAY,OAAO;AAC3C,MAAI,CAAC,iBAAiB,aAAa,KAAK;AACpC,WAAO;AACX,MAAI,UAAU,OAAO,gBAAgB,KAAK,EAAE,QAAQ,OAAO,mBAAmB;AAC1E,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,gBAAgB,KAAK,EAAE,QAAQ,OAAO,mBAAmB;AAC1E,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,OAAO,KAAK,EAAE,SAAS,OAAO,UAAU;AACzD,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,OAAO,KAAK,EAAE,SAAS,OAAO,UAAU;AACzD,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,UAAU,KAAK,EAAE,QAAQ,OAAO,eAAe,IAAI;AACpE,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,WAAW,QAAQ,YAAY,OAAO;AAC3C,MAAI,CAAC,iBAAiB,aAAa,KAAK;AACpC,WAAO;AACX,MAAI,UAAU,OAAO,aAAa,KAAK,EAAE,OAAO,oBAAoB,KAAK,EAAE,UAAU,OAAO,gBAAgB;AACxG,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,aAAa,KAAK,EAAE,OAAO,oBAAoB,KAAK,EAAE,UAAU,OAAO,gBAAgB;AACxG,WAAO;AAAA,EACX;AACA,QAAM,YAAY,OAAO,oBAAoB,OAAO,UAAU;AAC9D,aAAW,YAAY,WAAW;AAC9B,UAAM,WAAW,OAAO,WAAW,QAAQ;AAC3C,QAAI,OAAO,YAAY,OAAO,SAAS,SAAS,QAAQ,GAAG;AACvD,UAAI,CAACA,OAAM,UAAU,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC/C,eAAO;AAAA,MACX;AACA,WAAK,sBAAsB,QAAQ,KAAK,eAAe,QAAQ,MAAM,EAAE,YAAY,QAAQ;AACvF,eAAO;AAAA,MACX;AAAA,IACJ,OACK;AACD,UAAI,iBAAiB,wBAAwB,OAAO,QAAQ,KAAK,CAACA,OAAM,UAAU,YAAY,MAAM,QAAQ,CAAC,GAAG;AAC5G,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,OAAO,yBAAyB,OAAO;AACvC,UAAM,YAAY,OAAO,oBAAoB,KAAK;AAElD,QAAI,OAAO,YAAY,OAAO,SAAS,WAAW,UAAU,UAAU,UAAU,WAAW,UAAU,QAAQ;AACzG,aAAO;AAAA,IACX,OACK;AACD,aAAO,UAAU,MAAM,CAAC,aAAa,UAAU,SAAS,QAAQ,CAAC;AAAA,IACrE;AAAA,EACJ,WACS,OAAO,OAAO,yBAAyB,UAAU;AACtD,UAAM,YAAY,OAAO,oBAAoB,KAAK;AAClD,WAAO,UAAU,MAAM,CAAC,QAAQ,UAAU,SAAS,GAAG,KAAKA,OAAM,OAAO,sBAAsB,YAAY,MAAM,GAAG,CAAC,CAAC;AAAA,EACzH,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,YAAY,QAAQ,YAAY,OAAO;AAC5C,SAAO,UAAU,KAAK;AAC1B;AACA,SAAS,WAAW,QAAQ,YAAY,OAAO;AAC3C,MAAI,CAAC,iBAAiB,aAAa,KAAK,GAAG;AACvC,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,aAAa,KAAK,EAAE,OAAO,oBAAoB,KAAK,EAAE,UAAU,OAAO,gBAAgB;AACxG,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,aAAa,KAAK,EAAE,OAAO,oBAAoB,KAAK,EAAE,UAAU,OAAO,gBAAgB;AACxG,WAAO;AAAA,EACX;AACA,QAAM,CAAC,YAAY,aAAa,IAAI,OAAO,QAAQ,OAAO,iBAAiB,EAAE,CAAC;AAC9E,QAAM,QAAQ,IAAI,OAAO,UAAU;AAEnC,QAAM,SAAS,OAAO,QAAQ,KAAK,EAAE,MAAM,CAAC,CAAC,KAAKD,MAAK,MAAM;AACzD,WAAQ,MAAM,KAAK,GAAG,IAAKC,OAAM,eAAe,YAAYD,MAAK,IAAI;AAAA,EACzE,CAAC;AAED,QAAM,SAAS,OAAO,OAAO,yBAAyB,WAAW,OAAO,QAAQ,KAAK,EAAE,MAAM,CAAC,CAAC,KAAKA,MAAK,MAAM;AAC3G,WAAQ,CAAC,MAAM,KAAK,GAAG,IAAKC,OAAM,OAAO,sBAAsB,YAAYD,MAAK,IAAI;AAAA,EACxF,CAAC,IAAI;AACL,QAAM,SAAS,OAAO,yBAAyB,QACzC,OAAO,oBAAoB,KAAK,EAAE,MAAM,CAAC,QAAQ;AAC/C,WAAO,MAAM,KAAK,GAAG;AAAA,EACzB,CAAC,IACC;AACN,SAAO,UAAU,UAAU;AAC/B;AACA,SAAS,QAAQ,QAAQ,YAAY,OAAO;AACxC,SAAOC,OAAM,MAAM,QAAQ,UAAU,GAAG,YAAY,KAAK;AAC7D;AACA,SAAS,WAAW,QAAQ,YAAY,OAAO;AAC3C,QAAM,QAAQ,IAAI,OAAO,OAAO,QAAQ,OAAO,KAAK;AACpD,MAAI,UAAU,OAAO,SAAS,GAAG;AAC7B,QAAI,EAAE,MAAM,UAAU,OAAO;AACzB,aAAO;AAAA,EACf;AACA,MAAI,UAAU,OAAO,SAAS,GAAG;AAC7B,QAAI,EAAE,MAAM,UAAU,OAAO;AACzB,aAAO;AAAA,EACf;AACA,SAAO,MAAM,KAAK,KAAK;AAC3B;AACA,SAAS,WAAW,QAAQ,YAAY,OAAO;AAC3C,MAAI,CAAC,SAAS,KAAK,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,SAAS,GAAG;AAC7B,QAAI,EAAE,MAAM,UAAU,OAAO;AACzB,aAAO;AAAA,EACf;AACA,MAAI,UAAU,OAAO,SAAS,GAAG;AAC7B,QAAI,EAAE,MAAM,UAAU,OAAO;AACzB,aAAO;AAAA,EACf;AACA,MAAI,UAAU,OAAO,OAAO,GAAG;AAC3B,UAAM,QAAQ,IAAI,OAAO,OAAO,OAAO;AACvC,QAAI,CAAC,MAAM,KAAK,KAAK;AACjB,aAAO;AAAA,EACf;AACA,MAAI,UAAU,OAAO,MAAM,GAAG;AAC1B,QAAI,CAAC,eAAe,IAAI,OAAO,MAAM;AACjC,aAAO;AACX,UAAM,OAAO,eAAe,IAAI,OAAO,MAAM;AAC7C,WAAO,KAAK,KAAK;AAAA,EACrB;AACA,SAAO;AACX;AACA,SAAS,WAAW,QAAQ,YAAY,OAAO;AAC3C,SAAO,SAAS,KAAK;AACzB;AACA,SAAS,oBAAoB,QAAQ,YAAY,OAAO;AACpD,SAAO,SAAS,KAAK,KAAK,IAAI,OAAO,OAAO,OAAO,EAAE,KAAK,KAAK;AACnE;AACA,SAAS,SAAS,QAAQ,YAAY,OAAO;AACzC,SAAOA,OAAM,MAAM,QAAQ,UAAU,GAAG,YAAY,KAAK;AAC7D;AACA,SAAS,UAAU,QAAQ,YAAY,OAAO;AAC1C,MAAI,CAAC,QAAQ,KAAK,GAAG;AACjB,WAAO;AAAA,EACX;AACA,MAAI,OAAO,UAAU,UAAa,EAAE,MAAM,WAAW,IAAI;AACrD,WAAO;AAAA,EACX;AACA,MAAI,EAAE,MAAM,WAAW,OAAO,WAAW;AACrC,WAAO;AAAA,EACX;AACA,MAAI,CAAC,OAAO,OAAO;AACf,WAAO;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,MAAM,QAAQ,KAAK;AAC1C,QAAI,CAACA,OAAM,OAAO,MAAM,CAAC,GAAG,YAAY,MAAM,CAAC,CAAC;AAC5C,aAAO;AAAA,EACf;AACA,SAAO;AACX;AACA,SAAS,cAAc,QAAQ,YAAY,OAAO;AAC9C,SAAO,YAAY,KAAK;AAC5B;AACA,SAAS,UAAU,QAAQ,YAAY,OAAO;AAC1C,SAAO,OAAO,MAAM,KAAK,CAAC,UAAUA,OAAM,OAAO,YAAY,KAAK,CAAC;AACvE;AACA,SAAS,eAAe,QAAQ,YAAY,OAAO;AAC/C,MAAI,CAAC,aAAa,KAAK,GAAG;AACtB,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,aAAa,KAAK,EAAE,MAAM,UAAU,OAAO,gBAAgB;AAC5E,WAAO;AAAA,EACX;AACA,MAAI,UAAU,OAAO,aAAa,KAAK,EAAE,MAAM,UAAU,OAAO,gBAAgB;AAC5E,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,YAAY,QAAQ,YAAY,OAAO;AAC5C,SAAO;AACX;AACA,SAAS,SAAS,QAAQ,YAAY,OAAO;AACzC,SAAO,iBAAiB,WAAW,KAAK;AAC5C;AACA,SAAS,SAAS,QAAQ,YAAY,OAAO;AACzC,MAAI,CAAC,aAAa,IAAI,OAAO,IAAI,CAAC;AAC9B,WAAO;AACX,QAAM,OAAO,aAAa,IAAI,OAAO,IAAI,CAAC;AAC1C,SAAO,KAAK,QAAQ,KAAK;AAC7B;AACA,SAASA,OAAM,QAAQ,YAAY,OAAO;AACtC,QAAM,cAAc,UAAU,OAAO,GAAG,IAAI,QAAQ,QAAQ,UAAU,IAAI;AAC1E,QAAM,UAAU;AAChB,UAAQ,QAAQ,IAAI,GAAG;AAAA,IACnB,KAAK;AACD,aAAO,QAAQ,SAAS,aAAa,KAAK;AAAA,IAC9C,KAAK;AACD,aAAO,aAAa,SAAS,aAAa,KAAK;AAAA,IACnD,KAAK;AACD,aAAO,UAAU,SAAS,aAAa,KAAK;AAAA,IAChD,KAAK;AACD,aAAO,kBAAkB,SAAS,aAAa,KAAK;AAAA,IACxD,KAAK;AACD,aAAO,WAAW,SAAS,aAAa,KAAK;AAAA,IACjD,KAAK;AACD,aAAO,YAAY,SAAS,aAAa,KAAK;AAAA,IAClD,KAAK;AACD,aAAO,gBAAgB,SAAS,aAAa,KAAK;AAAA,IACtD,KAAK;AACD,aAAO,SAAS,SAAS,aAAa,KAAK;AAAA,IAC/C,KAAK;AACD,aAAO,aAAa,SAAS,aAAa,KAAK;AAAA,IACnD,KAAK;AACD,aAAO,WAAW,SAAS,aAAa,KAAK;AAAA,IACjD,KAAK;AACD,aAAO,YAAY,SAAS,aAAa,KAAK;AAAA,IAClD,KAAK;AACD,aAAO,cAAc,SAAS,aAAa,KAAK;AAAA,IACpD,KAAK;AACD,aAAO,aAAa,SAAS,aAAa,KAAK;AAAA,IACnD,KAAK;AACD,aAAO,YAAY,SAAS,aAAa,KAAK;AAAA,IAClD,KAAK;AACD,aAAO,UAAU,SAAS,aAAa,KAAK;AAAA,IAChD,KAAK;AACD,aAAO,QAAQ,SAAS,aAAa,KAAK;AAAA,IAC9C,KAAK;AACD,aAAO,SAAS,SAAS,aAAa,KAAK;AAAA,IAC/C,KAAK;AACD,aAAO,WAAW,SAAS,aAAa,KAAK;AAAA,IACjD,KAAK;AACD,aAAO,WAAW,SAAS,aAAa,KAAK;AAAA,IACjD,KAAK;AACD,aAAO,YAAY,SAAS,aAAa,KAAK;AAAA,IAClD,KAAK;AACD,aAAO,WAAW,SAAS,aAAa,KAAK;AAAA,IACjD,KAAK;AACD,aAAO,QAAQ,SAAS,aAAa,KAAK;AAAA,IAC9C,KAAK;AACD,aAAO,WAAW,SAAS,aAAa,KAAK;AAAA,IACjD,KAAK;AACD,aAAO,WAAW,SAAS,aAAa,KAAK;AAAA,IACjD,KAAK;AACD,aAAO,WAAW,SAAS,aAAa,KAAK;AAAA,IACjD,KAAK;AACD,aAAO,oBAAoB,SAAS,aAAa,KAAK;AAAA,IAC1D,KAAK;AACD,aAAO,SAAS,SAAS,aAAa,KAAK;AAAA,IAC/C,KAAK;AACD,aAAO,UAAU,SAAS,aAAa,KAAK;AAAA,IAChD,KAAK;AACD,aAAO,cAAc,SAAS,aAAa,KAAK;AAAA,IACpD,KAAK;AACD,aAAO,UAAU,SAAS,aAAa,KAAK;AAAA,IAChD,KAAK;AACD,aAAO,eAAe,SAAS,aAAa,KAAK;AAAA,IACrD,KAAK;AACD,aAAO,YAAY,SAAS,aAAa,KAAK;AAAA,IAClD,KAAK;AACD,aAAO,SAAS,SAAS,aAAa,KAAK;AAAA,IAC/C;AACI,UAAI,CAAC,aAAa,IAAI,QAAQ,IAAI,CAAC;AAC/B,cAAM,IAAI,2BAA2B,OAAO;AAChD,aAAO,SAAS,SAAS,aAAa,KAAK;AAAA,EACnD;AACJ;AAEO,SAAS,SAAS,MAAM;AAC3B,SAAO,KAAK,WAAW,IAAIA,OAAM,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,IAAIA,OAAM,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;AAC5F;;;ACjcO,IAAI;AAAA,CACV,SAAUE,iBAAgB;AACvB,EAAAA,gBAAeA,gBAAe,eAAe,IAAI,CAAC,IAAI;AACtD,EAAAA,gBAAeA,gBAAe,kBAAkB,IAAI,CAAC,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,eAAe,IAAI,CAAC,IAAI;AACtD,EAAAA,gBAAeA,gBAAe,kBAAkB,IAAI,CAAC,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,eAAe,IAAI,CAAC,IAAI;AACtD,EAAAA,gBAAeA,gBAAe,kBAAkB,IAAI,CAAC,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,OAAO,IAAI,CAAC,IAAI;AAC9C,EAAAA,gBAAeA,gBAAe,eAAe,IAAI,CAAC,IAAI;AACtD,EAAAA,gBAAeA,gBAAe,wBAAwB,IAAI,CAAC,IAAI;AAC/D,EAAAA,gBAAeA,gBAAe,wBAAwB,IAAI,CAAC,IAAI;AAC/D,EAAAA,gBAAeA,gBAAe,eAAe,IAAI,EAAE,IAAI;AACvD,EAAAA,gBAAeA,gBAAe,eAAe,IAAI,EAAE,IAAI;AACvD,EAAAA,gBAAeA,gBAAe,kBAAkB,IAAI,EAAE,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,EAAE,IAAI;AAChD,EAAAA,gBAAeA,gBAAe,SAAS,IAAI,EAAE,IAAI;AACjD,EAAAA,gBAAeA,gBAAe,+BAA+B,IAAI,EAAE,IAAI;AACvE,EAAAA,gBAAeA,gBAAe,+BAA+B,IAAI,EAAE,IAAI;AACvE,EAAAA,gBAAeA,gBAAe,sBAAsB,IAAI,EAAE,IAAI;AAC9D,EAAAA,gBAAeA,gBAAe,sBAAsB,IAAI,EAAE,IAAI;AAC9D,EAAAA,gBAAeA,gBAAe,yBAAyB,IAAI,EAAE,IAAI;AACjE,EAAAA,gBAAeA,gBAAe,MAAM,IAAI,EAAE,IAAI;AAC9C,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,EAAE,IAAI;AAClD,EAAAA,gBAAeA,gBAAe,yBAAyB,IAAI,EAAE,IAAI;AACjE,EAAAA,gBAAeA,gBAAe,yBAAyB,IAAI,EAAE,IAAI;AACjE,EAAAA,gBAAeA,gBAAe,gBAAgB,IAAI,EAAE,IAAI;AACxD,EAAAA,gBAAeA,gBAAe,gBAAgB,IAAI,EAAE,IAAI;AACxD,EAAAA,gBAAeA,gBAAe,mBAAmB,IAAI,EAAE,IAAI;AAC3D,EAAAA,gBAAeA,gBAAe,SAAS,IAAI,EAAE,IAAI;AACjD,EAAAA,gBAAeA,gBAAe,gCAAgC,IAAI,EAAE,IAAI;AACxE,EAAAA,gBAAeA,gBAAe,WAAW,IAAI,EAAE,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,UAAU,IAAI,EAAE,IAAI;AAClD,EAAAA,gBAAeA,gBAAe,MAAM,IAAI,EAAE,IAAI;AAC9C,EAAAA,gBAAeA,gBAAe,SAAS,IAAI,EAAE,IAAI;AACjD,EAAAA,gBAAeA,gBAAe,OAAO,IAAI,EAAE,IAAI;AAC/C,EAAAA,gBAAeA,gBAAe,KAAK,IAAI,EAAE,IAAI;AAC7C,EAAAA,gBAAeA,gBAAe,MAAM,IAAI,EAAE,IAAI;AAC9C,EAAAA,gBAAeA,gBAAe,wBAAwB,IAAI,EAAE,IAAI;AAChE,EAAAA,gBAAeA,gBAAe,wBAAwB,IAAI,EAAE,IAAI;AAChE,EAAAA,gBAAeA,gBAAe,eAAe,IAAI,EAAE,IAAI;AACvD,EAAAA,gBAAeA,gBAAe,eAAe,IAAI,EAAE,IAAI;AACvD,EAAAA,gBAAeA,gBAAe,kBAAkB,IAAI,EAAE,IAAI;AAC1D,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,EAAE,IAAI;AAChD,EAAAA,gBAAeA,gBAAe,4BAA4B,IAAI,EAAE,IAAI;AACpE,EAAAA,gBAAeA,gBAAe,qBAAqB,IAAI,EAAE,IAAI;AAC7D,EAAAA,gBAAeA,gBAAe,qBAAqB,IAAI,EAAE,IAAI;AAC7D,EAAAA,gBAAeA,gBAAe,wBAAwB,IAAI,EAAE,IAAI;AAChE,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,EAAE,IAAI;AAChD,EAAAA,gBAAeA,gBAAe,SAAS,IAAI,EAAE,IAAI;AACjD,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,EAAE,IAAI;AAChD,EAAAA,gBAAeA,gBAAe,qBAAqB,IAAI,EAAE,IAAI;AAC7D,EAAAA,gBAAeA,gBAAe,cAAc,IAAI,EAAE,IAAI;AACtD,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,EAAE,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,iBAAiB,IAAI,EAAE,IAAI;AACzD,EAAAA,gBAAeA,gBAAe,eAAe,IAAI,EAAE,IAAI;AACvD,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,EAAE,IAAI;AAChD,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,EAAE,IAAI;AAChD,EAAAA,gBAAeA,gBAAe,aAAa,IAAI,EAAE,IAAI;AACrD,EAAAA,gBAAeA,gBAAe,OAAO,IAAI,EAAE,IAAI;AAC/C,EAAAA,gBAAeA,gBAAe,yBAAyB,IAAI,EAAE,IAAI;AACjE,EAAAA,gBAAeA,gBAAe,yBAAyB,IAAI,EAAE,IAAI;AACjE,EAAAA,gBAAeA,gBAAe,YAAY,IAAI,EAAE,IAAI;AACpD,EAAAA,gBAAeA,gBAAe,WAAW,IAAI,EAAE,IAAI;AACnD,EAAAA,gBAAeA,gBAAe,OAAO,IAAI,EAAE,IAAI;AAC/C,EAAAA,gBAAeA,gBAAe,MAAM,IAAI,EAAE,IAAI;AAClD,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAInC,IAAM,8BAAN,cAA0C,aAAa;AAAA,EAC1D,YAAY,QAAQ;AAChB,UAAM,cAAc;AACpB,SAAK,SAAS;AAAA,EAClB;AACJ;AAIA,SAAS,UAAU,KAAK;AACpB,SAAO,IAAI,QAAQ,MAAM,IAAI,EAAE,QAAQ,OAAO,IAAI;AACtD;AAIA,SAASC,WAAU,OAAO;AACtB,SAAO,UAAU;AACrB;AAIO,IAAM,qBAAN,MAAyB;AAAA,EAC5B,YAAY,UAAU;AAClB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,CAAC,OAAO,QAAQ,IAAI;AAChB,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,QAAQ;AACJ,UAAM,OAAO,KAAK,SAAS,KAAK;AAChC,WAAO,KAAK,OAAO,SAAY,KAAK;AAAA,EACxC;AACJ;AAIA,SAAS,OAAO,WAAW,QAAQ,MAAM,OAAO,SAAS,CAAC,GAAG;AACzD,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,iBAAiB,EAAE,EAAE,WAAW,MAAM,QAAQ,OAAO,OAAO,CAAC;AAAA,IACtE;AAAA,EACJ;AACJ;AAIA,UAAUC,SAAQ,QAAQ,YAAY,MAAM,OAAO;AAAE;AACrD,UAAUC,cAAa,QAAQ,YAAY,MAAM,OAAO;AAAE;AAC1D,UAAUC,WAAU,QAAQ,YAAY,MAAM,OAAO;AACjD,MAAI,CAAC,QAAQ,KAAK,GAAG;AACjB,WAAO,MAAM,OAAO,eAAe,OAAO,QAAQ,MAAM,KAAK;AAAA,EACjE;AACA,MAAIH,WAAU,OAAO,QAAQ,KAAK,EAAE,MAAM,UAAU,OAAO,WAAW;AAClE,UAAM,OAAO,eAAe,eAAe,QAAQ,MAAM,KAAK;AAAA,EAClE;AACA,MAAIA,WAAU,OAAO,QAAQ,KAAK,EAAE,MAAM,UAAU,OAAO,WAAW;AAClE,UAAM,OAAO,eAAe,eAAe,QAAQ,MAAM,KAAK;AAAA,EAClE;AACA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,WAAOI,OAAM,OAAO,OAAO,YAAY,GAAG,IAAI,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,EACnE;AAEA,MAAI,OAAO,gBAAgB,QAAQ,CAAG,WAAY;AAAE,UAAM,MAAM,oBAAI,IAAI;AAAG,eAAW,WAAW,OAAO;AACpG,YAAM,SAAS,KAAK,OAAO;AAC3B,UAAI,IAAI,IAAI,MAAM,GAAG;AACjB,eAAO;AAAA,MACX,OACK;AACD,YAAI,IAAI,MAAM;AAAA,MAClB;AAAA,IACJ;AAAE,WAAO;AAAA,EAAM,EAAG,GAAI;AAClB,UAAM,OAAO,eAAe,kBAAkB,QAAQ,MAAM,KAAK;AAAA,EACrE;AAEA,MAAI,EAAEJ,WAAU,OAAO,QAAQ,KAAKA,WAAU,OAAO,WAAW,KAAKA,WAAU,OAAO,WAAW,IAAI;AACjG;AAAA,EACJ;AACA,QAAM,iBAAiBA,WAAU,OAAO,QAAQ,IAAI,OAAO,WAAW,MAAM;AAC5E,QAAM,gBAAgB,MAAM,OAAO,CAAC,KAAKK,QAAO,UAAWD,OAAM,gBAAgB,YAAY,GAAG,IAAI,GAAG,KAAK,IAAIC,MAAK,EAAE,KAAK,EAAE,SAAS,OAAO,MAAM,IAAI,KAAM,CAAC;AAC/J,MAAI,kBAAkB,GAAG;AACrB,UAAM,OAAO,eAAe,eAAe,QAAQ,MAAM,KAAK;AAAA,EAClE;AACA,MAAI,SAAS,OAAO,WAAW,KAAK,gBAAgB,OAAO,aAAa;AACpE,UAAM,OAAO,eAAe,kBAAkB,QAAQ,MAAM,KAAK;AAAA,EACrE;AACA,MAAI,SAAS,OAAO,WAAW,KAAK,gBAAgB,OAAO,aAAa;AACpE,UAAM,OAAO,eAAe,kBAAkB,QAAQ,MAAM,KAAK;AAAA,EACrE;AACJ;AACA,UAAUC,mBAAkB,QAAQ,YAAY,MAAM,OAAO;AACzD,MAAI,CAAC,gBAAgB,KAAK;AACtB,UAAM,OAAO,eAAe,eAAe,QAAQ,MAAM,KAAK;AACtE;AACA,UAAUC,YAAW,QAAQ,YAAY,MAAM,OAAO;AAClD,MAAI,CAAC,SAAS,KAAK;AACf,WAAO,MAAM,OAAO,eAAe,QAAQ,QAAQ,MAAM,KAAK;AAClE,MAAIP,WAAU,OAAO,gBAAgB,KAAK,EAAE,QAAQ,OAAO,mBAAmB;AAC1E,UAAM,OAAO,eAAe,wBAAwB,QAAQ,MAAM,KAAK;AAAA,EAC3E;AACA,MAAIA,WAAU,OAAO,gBAAgB,KAAK,EAAE,QAAQ,OAAO,mBAAmB;AAC1E,UAAM,OAAO,eAAe,wBAAwB,QAAQ,MAAM,KAAK;AAAA,EAC3E;AACA,MAAIA,WAAU,OAAO,OAAO,KAAK,EAAE,SAAS,OAAO,UAAU;AACzD,UAAM,OAAO,eAAe,eAAe,QAAQ,MAAM,KAAK;AAAA,EAClE;AACA,MAAIA,WAAU,OAAO,OAAO,KAAK,EAAE,SAAS,OAAO,UAAU;AACzD,UAAM,OAAO,eAAe,eAAe,QAAQ,MAAM,KAAK;AAAA,EAClE;AACA,MAAIA,WAAU,OAAO,UAAU,KAAK,EAAE,QAAQ,OAAO,eAAe,OAAO,CAAC,IAAI;AAC5E,UAAM,OAAO,eAAe,kBAAkB,QAAQ,MAAM,KAAK;AAAA,EACrE;AACJ;AACA,UAAUQ,aAAY,QAAQ,YAAY,MAAM,OAAO;AACnD,MAAI,CAAC,UAAU,KAAK;AAChB,UAAM,OAAO,eAAe,SAAS,QAAQ,MAAM,KAAK;AAChE;AACA,UAAUC,iBAAgB,QAAQ,YAAY,MAAM,OAAO;AACvD,SAAOL,OAAM,OAAO,SAAS,YAAY,MAAM,MAAM,SAAS;AAClE;AACA,UAAUM,UAAS,QAAQ,YAAY,MAAM,OAAO;AAChD,MAAI,CAAC,OAAO,KAAK;AACb,WAAO,MAAM,OAAO,eAAe,MAAM,QAAQ,MAAM,KAAK;AAChE,MAAIV,WAAU,OAAO,yBAAyB,KAAK,EAAE,MAAM,QAAQ,IAAI,OAAO,4BAA4B;AACtG,UAAM,OAAO,eAAe,+BAA+B,QAAQ,MAAM,KAAK;AAAA,EAClF;AACA,MAAIA,WAAU,OAAO,yBAAyB,KAAK,EAAE,MAAM,QAAQ,IAAI,OAAO,4BAA4B;AACtG,UAAM,OAAO,eAAe,+BAA+B,QAAQ,MAAM,KAAK;AAAA,EAClF;AACA,MAAIA,WAAU,OAAO,gBAAgB,KAAK,EAAE,MAAM,QAAQ,KAAK,OAAO,mBAAmB;AACrF,UAAM,OAAO,eAAe,sBAAsB,QAAQ,MAAM,KAAK;AAAA,EACzE;AACA,MAAIA,WAAU,OAAO,gBAAgB,KAAK,EAAE,MAAM,QAAQ,KAAK,OAAO,mBAAmB;AACrF,UAAM,OAAO,eAAe,sBAAsB,QAAQ,MAAM,KAAK;AAAA,EACzE;AACA,MAAIA,WAAU,OAAO,mBAAmB,KAAK,EAAE,MAAM,QAAQ,IAAI,OAAO,wBAAwB,IAAI;AAChG,UAAM,OAAO,eAAe,yBAAyB,QAAQ,MAAM,KAAK;AAAA,EAC5E;AACJ;AACA,UAAUW,cAAa,QAAQ,YAAY,MAAM,OAAO;AACpD,MAAI,CAAC,WAAW,KAAK;AACjB,UAAM,OAAO,eAAe,UAAU,QAAQ,MAAM,KAAK;AACjE;AACA,UAAUC,YAAW,QAAQ,YAAY,MAAM,OAAO;AAClD,QAAM,cAAc,WAAW,OAAO,OAAO,OAAO,KAAK;AACzD,QAAM,SAAS,OAAO,MAAM,OAAO,IAAI;AACvC,SAAOR,OAAM,QAAQ,CAAC,GAAG,YAAY,GAAG,WAAW,GAAG,MAAM,KAAK;AACrE;AACA,UAAUS,aAAY,QAAQ,YAAY,MAAM,OAAO;AACnD,MAAI,CAAC,UAAU,KAAK;AAChB,WAAO,MAAM,OAAO,eAAe,SAAS,QAAQ,MAAM,KAAK;AACnE,MAAIb,WAAU,OAAO,gBAAgB,KAAK,EAAE,QAAQ,OAAO,mBAAmB;AAC1E,UAAM,OAAO,eAAe,yBAAyB,QAAQ,MAAM,KAAK;AAAA,EAC5E;AACA,MAAIA,WAAU,OAAO,gBAAgB,KAAK,EAAE,QAAQ,OAAO,mBAAmB;AAC1E,UAAM,OAAO,eAAe,yBAAyB,QAAQ,MAAM,KAAK;AAAA,EAC5E;AACA,MAAIA,WAAU,OAAO,OAAO,KAAK,EAAE,SAAS,OAAO,UAAU;AACzD,UAAM,OAAO,eAAe,gBAAgB,QAAQ,MAAM,KAAK;AAAA,EACnE;AACA,MAAIA,WAAU,OAAO,OAAO,KAAK,EAAE,SAAS,OAAO,UAAU;AACzD,UAAM,OAAO,eAAe,gBAAgB,QAAQ,MAAM,KAAK;AAAA,EACnE;AACA,MAAIA,WAAU,OAAO,UAAU,KAAK,EAAE,QAAQ,OAAO,eAAe,IAAI;AACpE,UAAM,OAAO,eAAe,mBAAmB,QAAQ,MAAM,KAAK;AAAA,EACtE;AACJ;AACA,UAAUc,eAAc,QAAQ,YAAY,MAAM,OAAO;AACrD,MAAI,WAAW;AACf,aAAW,SAAS,OAAO,OAAO;AAC9B,eAAW,SAASV,OAAM,OAAO,YAAY,MAAM,KAAK,GAAG;AACvD,iBAAW;AACX,YAAM;AAAA,IACV;AAAA,EACJ;AACA,MAAI,UAAU;AACV,WAAO,MAAM,OAAO,eAAe,WAAW,QAAQ,MAAM,KAAK;AAAA,EACrE;AACA,MAAI,OAAO,0BAA0B,OAAO;AACxC,UAAM,WAAW,IAAI,OAAO,aAAa,MAAM,CAAC;AAChD,eAAW,YAAY,OAAO,oBAAoB,KAAK,GAAG;AACtD,UAAI,CAAC,SAAS,KAAK,QAAQ,GAAG;AAC1B,cAAM,OAAO,eAAe,gCAAgC,QAAQ,GAAG,IAAI,IAAI,QAAQ,IAAI,KAAK;AAAA,MACpG;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,OAAO,OAAO,0BAA0B,UAAU;AAClD,UAAM,WAAW,IAAI,OAAO,aAAa,MAAM,CAAC;AAChD,eAAW,YAAY,OAAO,oBAAoB,KAAK,GAAG;AACtD,UAAI,CAAC,SAAS,KAAK,QAAQ,GAAG;AAC1B,cAAM,OAAOA,OAAM,OAAO,uBAAuB,YAAY,GAAG,IAAI,IAAI,QAAQ,IAAI,MAAM,QAAQ,CAAC,EAAE,KAAK;AAC1G,YAAI,CAAC,KAAK;AACN,gBAAM,KAAK;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,UAAUW,cAAa,QAAQ,YAAY,MAAM,OAAO;AACpD,MAAI,CAAC,WAAW,KAAK;AACjB,UAAM,OAAO,eAAe,UAAU,QAAQ,MAAM,KAAK;AACjE;AACA,UAAUC,aAAY,QAAQ,YAAY,MAAM,OAAO;AACnD,MAAI,EAAE,UAAU,OAAO;AACnB,UAAM,OAAO,eAAe,SAAS,QAAQ,MAAM,KAAK;AAChE;AACA,UAAUC,WAAU,QAAQ,YAAY,MAAM,OAAO;AACjD,QAAM,OAAO,eAAe,OAAO,QAAQ,MAAM,KAAK;AAC1D;AACA,UAAUC,SAAQ,QAAQ,YAAY,MAAM,OAAO;AAC/C,MAAId,OAAM,OAAO,KAAK,YAAY,MAAM,KAAK,EAAE,KAAK,EAAE,SAAS;AAC3D,UAAM,OAAO,eAAe,KAAK,QAAQ,MAAM,KAAK;AAC5D;AACA,UAAUe,UAAS,QAAQ,YAAY,MAAM,OAAO;AAChD,MAAI,CAAC,OAAO,KAAK;AACb,UAAM,OAAO,eAAe,MAAM,QAAQ,MAAM,KAAK;AAC7D;AACA,UAAUC,YAAW,QAAQ,YAAY,MAAM,OAAO;AAClD,MAAI,CAAC,iBAAiB,aAAa,KAAK;AACpC,WAAO,MAAM,OAAO,eAAe,QAAQ,QAAQ,MAAM,KAAK;AAClE,MAAIpB,WAAU,OAAO,gBAAgB,KAAK,EAAE,QAAQ,OAAO,mBAAmB;AAC1E,UAAM,OAAO,eAAe,wBAAwB,QAAQ,MAAM,KAAK;AAAA,EAC3E;AACA,MAAIA,WAAU,OAAO,gBAAgB,KAAK,EAAE,QAAQ,OAAO,mBAAmB;AAC1E,UAAM,OAAO,eAAe,wBAAwB,QAAQ,MAAM,KAAK;AAAA,EAC3E;AACA,MAAIA,WAAU,OAAO,OAAO,KAAK,EAAE,SAAS,OAAO,UAAU;AACzD,UAAM,OAAO,eAAe,eAAe,QAAQ,MAAM,KAAK;AAAA,EAClE;AACA,MAAIA,WAAU,OAAO,OAAO,KAAK,EAAE,SAAS,OAAO,UAAU;AACzD,UAAM,OAAO,eAAe,eAAe,QAAQ,MAAM,KAAK;AAAA,EAClE;AACA,MAAIA,WAAU,OAAO,UAAU,KAAK,EAAE,QAAQ,OAAO,eAAe,IAAI;AACpE,UAAM,OAAO,eAAe,kBAAkB,QAAQ,MAAM,KAAK;AAAA,EACrE;AACJ;AACA,UAAUqB,YAAW,QAAQ,YAAY,MAAM,OAAO;AAClD,MAAI,CAAC,iBAAiB,aAAa,KAAK;AACpC,WAAO,MAAM,OAAO,eAAe,QAAQ,QAAQ,MAAM,KAAK;AAClE,MAAIrB,WAAU,OAAO,aAAa,KAAK,EAAE,OAAO,oBAAoB,KAAK,EAAE,UAAU,OAAO,gBAAgB;AACxG,UAAM,OAAO,eAAe,qBAAqB,QAAQ,MAAM,KAAK;AAAA,EACxE;AACA,MAAIA,WAAU,OAAO,aAAa,KAAK,EAAE,OAAO,oBAAoB,KAAK,EAAE,UAAU,OAAO,gBAAgB;AACxG,UAAM,OAAO,eAAe,qBAAqB,QAAQ,MAAM,KAAK;AAAA,EACxE;AACA,QAAM,eAAe,MAAM,QAAQ,OAAO,QAAQ,IAAI,OAAO,WAAW,CAAC;AACzE,QAAM,YAAY,OAAO,oBAAoB,OAAO,UAAU;AAC9D,QAAM,cAAc,OAAO,oBAAoB,KAAK;AACpD,aAAW,eAAe,cAAc;AACpC,QAAI,YAAY,SAAS,WAAW;AAChC;AACJ,UAAM,OAAO,eAAe,wBAAwB,OAAO,WAAW,WAAW,GAAG,GAAG,IAAI,IAAI,UAAU,WAAW,CAAC,IAAI,MAAS;AAAA,EACtI;AACA,MAAI,OAAO,yBAAyB,OAAO;AACvC,eAAW,YAAY,aAAa;AAChC,UAAI,CAAC,UAAU,SAAS,QAAQ,GAAG;AAC/B,cAAM,OAAO,eAAe,4BAA4B,QAAQ,GAAG,IAAI,IAAI,UAAU,QAAQ,CAAC,IAAI,MAAM,QAAQ,CAAC;AAAA,MACrH;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,OAAO,OAAO,yBAAyB,UAAU;AACjD,eAAW,YAAY,aAAa;AAChC,UAAI,UAAU,SAAS,QAAQ;AAC3B;AACJ,aAAOI,OAAM,OAAO,sBAAsB,YAAY,GAAG,IAAI,IAAI,UAAU,QAAQ,CAAC,IAAI,MAAM,QAAQ,CAAC;AAAA,IAC3G;AAAA,EACJ;AACA,aAAW,YAAY,WAAW;AAC9B,UAAM,WAAW,OAAO,WAAW,QAAQ;AAC3C,QAAI,OAAO,YAAY,OAAO,SAAS,SAAS,QAAQ,GAAG;AACvD,aAAOA,OAAM,UAAU,YAAY,GAAG,IAAI,IAAI,UAAU,QAAQ,CAAC,IAAI,MAAM,QAAQ,CAAC;AACpF,UAAI,sBAAsB,MAAM,KAAK,EAAE,YAAY,QAAQ;AACvD,cAAM,OAAO,eAAe,wBAAwB,UAAU,GAAG,IAAI,IAAI,UAAU,QAAQ,CAAC,IAAI,MAAS;AAAA,MAC7G;AAAA,IACJ,OACK;AACD,UAAI,iBAAiB,wBAAwB,OAAO,QAAQ,GAAG;AAC3D,eAAOA,OAAM,UAAU,YAAY,GAAG,IAAI,IAAI,UAAU,QAAQ,CAAC,IAAI,MAAM,QAAQ,CAAC;AAAA,MACxF;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,UAAUkB,aAAY,QAAQ,YAAY,MAAM,OAAO;AACnD,MAAI,CAAC,UAAU,KAAK;AAChB,UAAM,OAAO,eAAe,SAAS,QAAQ,MAAM,KAAK;AAChE;AACA,UAAUC,YAAW,QAAQ,YAAY,MAAM,OAAO;AAClD,MAAI,CAAC,iBAAiB,aAAa,KAAK;AACpC,WAAO,MAAM,OAAO,eAAe,QAAQ,QAAQ,MAAM,KAAK;AAClE,MAAIvB,WAAU,OAAO,aAAa,KAAK,EAAE,OAAO,oBAAoB,KAAK,EAAE,UAAU,OAAO,gBAAgB;AACxG,UAAM,OAAO,eAAe,qBAAqB,QAAQ,MAAM,KAAK;AAAA,EACxE;AACA,MAAIA,WAAU,OAAO,aAAa,KAAK,EAAE,OAAO,oBAAoB,KAAK,EAAE,UAAU,OAAO,gBAAgB;AACxG,UAAM,OAAO,eAAe,qBAAqB,QAAQ,MAAM,KAAK;AAAA,EACxE;AACA,QAAM,CAAC,YAAY,aAAa,IAAI,OAAO,QAAQ,OAAO,iBAAiB,EAAE,CAAC;AAC9E,QAAM,QAAQ,IAAI,OAAO,UAAU;AACnC,aAAW,CAAC,aAAa,aAAa,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC9D,QAAI,MAAM,KAAK,WAAW;AACtB,aAAOI,OAAM,eAAe,YAAY,GAAG,IAAI,IAAI,UAAU,WAAW,CAAC,IAAI,aAAa;AAAA,EAClG;AACA,MAAI,OAAO,OAAO,yBAAyB,UAAU;AACjD,eAAW,CAAC,aAAa,aAAa,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC9D,UAAI,CAAC,MAAM,KAAK,WAAW;AACvB,eAAOA,OAAM,OAAO,sBAAsB,YAAY,GAAG,IAAI,IAAI,UAAU,WAAW,CAAC,IAAI,aAAa;AAAA,IAChH;AAAA,EACJ;AACA,MAAI,OAAO,yBAAyB,OAAO;AACvC,eAAW,CAAC,aAAa,aAAa,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC9D,UAAI,MAAM,KAAK,WAAW;AACtB;AACJ,aAAO,MAAM,OAAO,eAAe,4BAA4B,QAAQ,GAAG,IAAI,IAAI,UAAU,WAAW,CAAC,IAAI,aAAa;AAAA,IAC7H;AAAA,EACJ;AACJ;AACA,UAAUoB,SAAQ,QAAQ,YAAY,MAAM,OAAO;AAC/C,SAAOpB,OAAM,MAAM,QAAQ,UAAU,GAAG,YAAY,MAAM,KAAK;AACnE;AACA,UAAUqB,YAAW,QAAQ,YAAY,MAAM,OAAO;AAClD,MAAI,CAAC,SAAS,KAAK;AACf,WAAO,MAAM,OAAO,eAAe,QAAQ,QAAQ,MAAM,KAAK;AAClE,MAAIzB,WAAU,OAAO,SAAS,KAAK,EAAE,MAAM,UAAU,OAAO,YAAY;AACpE,UAAM,OAAO,eAAe,iBAAiB,QAAQ,MAAM,KAAK;AAAA,EACpE;AACA,MAAIA,WAAU,OAAO,SAAS,KAAK,EAAE,MAAM,UAAU,OAAO,YAAY;AACpE,UAAM,OAAO,eAAe,iBAAiB,QAAQ,MAAM,KAAK;AAAA,EACpE;AACA,QAAM,QAAQ,IAAI,OAAO,OAAO,QAAQ,OAAO,KAAK;AACpD,MAAI,CAAC,MAAM,KAAK,KAAK,GAAG;AACpB,WAAO,MAAM,OAAO,eAAe,QAAQ,QAAQ,MAAM,KAAK;AAAA,EAClE;AACJ;AACA,UAAU0B,YAAW,QAAQ,YAAY,MAAM,OAAO;AAClD,MAAI,CAAC,SAAS,KAAK;AACf,WAAO,MAAM,OAAO,eAAe,QAAQ,QAAQ,MAAM,KAAK;AAClE,MAAI1B,WAAU,OAAO,SAAS,KAAK,EAAE,MAAM,UAAU,OAAO,YAAY;AACpE,UAAM,OAAO,eAAe,iBAAiB,QAAQ,MAAM,KAAK;AAAA,EACpE;AACA,MAAIA,WAAU,OAAO,SAAS,KAAK,EAAE,MAAM,UAAU,OAAO,YAAY;AACpE,UAAM,OAAO,eAAe,iBAAiB,QAAQ,MAAM,KAAK;AAAA,EACpE;AACA,MAAI,SAAS,OAAO,OAAO,GAAG;AAC1B,UAAM,QAAQ,IAAI,OAAO,OAAO,OAAO;AACvC,QAAI,CAAC,MAAM,KAAK,KAAK,GAAG;AACpB,YAAM,OAAO,eAAe,eAAe,QAAQ,MAAM,KAAK;AAAA,IAClE;AAAA,EACJ;AACA,MAAI,SAAS,OAAO,MAAM,GAAG;AACzB,QAAI,CAAC,eAAe,IAAI,OAAO,MAAM,GAAG;AACpC,YAAM,OAAO,eAAe,qBAAqB,QAAQ,MAAM,KAAK;AAAA,IACxE,OACK;AACD,YAAM,SAAS,eAAe,IAAI,OAAO,MAAM;AAC/C,UAAI,CAAC,OAAO,KAAK,GAAG;AAChB,cAAM,OAAO,eAAe,cAAc,QAAQ,MAAM,KAAK;AAAA,MACjE;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,UAAU2B,YAAW,QAAQ,YAAY,MAAM,OAAO;AAClD,MAAI,CAAC,SAAS,KAAK;AACf,UAAM,OAAO,eAAe,QAAQ,QAAQ,MAAM,KAAK;AAC/D;AACA,UAAUC,qBAAoB,QAAQ,YAAY,MAAM,OAAO;AAC3D,MAAI,CAAC,SAAS,KAAK;AACf,WAAO,MAAM,OAAO,eAAe,QAAQ,QAAQ,MAAM,KAAK;AAClE,QAAM,QAAQ,IAAI,OAAO,OAAO,OAAO;AACvC,MAAI,CAAC,MAAM,KAAK,KAAK,GAAG;AACpB,UAAM,OAAO,eAAe,eAAe,QAAQ,MAAM,KAAK;AAAA,EAClE;AACJ;AACA,UAAUC,UAAS,QAAQ,YAAY,MAAM,OAAO;AAChD,SAAOzB,OAAM,MAAM,QAAQ,UAAU,GAAG,YAAY,MAAM,KAAK;AACnE;AACA,UAAU0B,WAAU,QAAQ,YAAY,MAAM,OAAO;AACjD,MAAI,CAAC,QAAQ,KAAK;AACd,WAAO,MAAM,OAAO,eAAe,OAAO,QAAQ,MAAM,KAAK;AACjE,MAAI,OAAO,UAAU,UAAa,EAAE,MAAM,WAAW,IAAI;AACrD,WAAO,MAAM,OAAO,eAAe,aAAa,QAAQ,MAAM,KAAK;AAAA,EACvE;AACA,MAAI,EAAE,MAAM,WAAW,OAAO,WAAW;AACrC,WAAO,MAAM,OAAO,eAAe,aAAa,QAAQ,MAAM,KAAK;AAAA,EACvE;AACA,MAAI,CAAC,OAAO,OAAO;AACf;AAAA,EACJ;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,MAAM,QAAQ,KAAK;AAC1C,WAAO1B,OAAM,OAAO,MAAM,CAAC,GAAG,YAAY,GAAG,IAAI,IAAI,CAAC,IAAI,MAAM,CAAC,CAAC;AAAA,EACtE;AACJ;AACA,UAAU2B,eAAc,QAAQ,YAAY,MAAM,OAAO;AACrD,MAAI,CAAC,YAAY,KAAK;AAClB,UAAM,OAAO,eAAe,WAAW,QAAQ,MAAM,KAAK;AAClE;AACA,UAAUC,WAAU,QAAQ,YAAY,MAAM,OAAO;AACjD,MAAI,MAAM,QAAQ,YAAY,KAAK;AAC/B;AACJ,QAAM,SAAS,OAAO,MAAM,IAAI,CAAC,YAAY,IAAI,mBAAmB5B,OAAM,SAAS,YAAY,MAAM,KAAK,CAAC,CAAC;AAC5G,QAAM,OAAO,eAAe,OAAO,QAAQ,MAAM,OAAO,MAAM;AAClE;AACA,UAAU6B,gBAAe,QAAQ,YAAY,MAAM,OAAO;AACtD,MAAI,CAAC,aAAa,KAAK;AACnB,WAAO,MAAM,OAAO,eAAe,YAAY,QAAQ,MAAM,KAAK;AACtE,MAAIjC,WAAU,OAAO,aAAa,KAAK,EAAE,MAAM,UAAU,OAAO,gBAAgB;AAC5E,UAAM,OAAO,eAAe,yBAAyB,QAAQ,MAAM,KAAK;AAAA,EAC5E;AACA,MAAIA,WAAU,OAAO,aAAa,KAAK,EAAE,MAAM,UAAU,OAAO,gBAAgB;AAC5E,UAAM,OAAO,eAAe,yBAAyB,QAAQ,MAAM,KAAK;AAAA,EAC5E;AACJ;AACA,UAAUkC,aAAY,QAAQ,YAAY,MAAM,OAAO;AAAE;AACzD,UAAUC,UAAS,QAAQ,YAAY,MAAM,OAAO;AAChD,MAAI,CAAC,iBAAiB,WAAW,KAAK;AAClC,UAAM,OAAO,eAAe,MAAM,QAAQ,MAAM,KAAK;AAC7D;AACA,UAAUC,UAAS,QAAQ,YAAY,MAAM,OAAO;AAChD,QAAM,QAAQ,aAAa,IAAI,OAAO,IAAI,CAAC;AAC3C,MAAI,CAAC,MAAM,QAAQ,KAAK;AACpB,UAAM,OAAO,eAAe,MAAM,QAAQ,MAAM,KAAK;AAC7D;AACA,UAAUhC,OAAM,QAAQ,YAAY,MAAM,OAAO;AAC7C,QAAM,cAAcJ,WAAU,OAAO,GAAG,IAAI,CAAC,GAAG,YAAY,MAAM,IAAI;AACtE,QAAM,UAAU;AAChB,UAAQ,QAAQ,IAAI,GAAG;AAAA,IACnB,KAAK;AACD,aAAO,OAAOC,SAAQ,SAAS,aAAa,MAAM,KAAK;AAAA,IAC3D,KAAK;AACD,aAAO,OAAOC,cAAa,SAAS,aAAa,MAAM,KAAK;AAAA,IAChE,KAAK;AACD,aAAO,OAAOC,WAAU,SAAS,aAAa,MAAM,KAAK;AAAA,IAC7D,KAAK;AACD,aAAO,OAAOG,mBAAkB,SAAS,aAAa,MAAM,KAAK;AAAA,IACrE,KAAK;AACD,aAAO,OAAOC,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IAC9D,KAAK;AACD,aAAO,OAAOC,aAAY,SAAS,aAAa,MAAM,KAAK;AAAA,IAC/D,KAAK;AACD,aAAO,OAAOC,iBAAgB,SAAS,aAAa,MAAM,KAAK;AAAA,IACnE,KAAK;AACD,aAAO,OAAOC,UAAS,SAAS,aAAa,MAAM,KAAK;AAAA,IAC5D,KAAK;AACD,aAAO,OAAOC,cAAa,SAAS,aAAa,MAAM,KAAK;AAAA,IAChE,KAAK;AACD,aAAO,OAAOC,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IAC9D,KAAK;AACD,aAAO,OAAOC,aAAY,SAAS,aAAa,MAAM,KAAK;AAAA,IAC/D,KAAK;AACD,aAAO,OAAOC,eAAc,SAAS,aAAa,MAAM,KAAK;AAAA,IACjE,KAAK;AACD,aAAO,OAAOC,cAAa,SAAS,aAAa,MAAM,KAAK;AAAA,IAChE,KAAK;AACD,aAAO,OAAOC,aAAY,SAAS,aAAa,MAAM,KAAK;AAAA,IAC/D,KAAK;AACD,aAAO,OAAOC,WAAU,SAAS,aAAa,MAAM,KAAK;AAAA,IAC7D,KAAK;AACD,aAAO,OAAOC,SAAQ,SAAS,aAAa,MAAM,KAAK;AAAA,IAC3D,KAAK;AACD,aAAO,OAAOC,UAAS,SAAS,aAAa,MAAM,KAAK;AAAA,IAC5D,KAAK;AACD,aAAO,OAAOC,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IAC9D,KAAK;AACD,aAAO,OAAOC,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IAC9D,KAAK;AACD,aAAO,OAAOC,aAAY,SAAS,aAAa,MAAM,KAAK;AAAA,IAC/D,KAAK;AACD,aAAO,OAAOC,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IAC9D,KAAK;AACD,aAAO,OAAOC,SAAQ,SAAS,aAAa,MAAM,KAAK;AAAA,IAC3D,KAAK;AACD,aAAO,OAAOC,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IAC9D,KAAK;AACD,aAAO,OAAOC,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IAC9D,KAAK;AACD,aAAO,OAAOC,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IAC9D,KAAK;AACD,aAAO,OAAOC,qBAAoB,SAAS,aAAa,MAAM,KAAK;AAAA,IACvE,KAAK;AACD,aAAO,OAAOC,UAAS,SAAS,aAAa,MAAM,KAAK;AAAA,IAC5D,KAAK;AACD,aAAO,OAAOC,WAAU,SAAS,aAAa,MAAM,KAAK;AAAA,IAC7D,KAAK;AACD,aAAO,OAAOC,eAAc,SAAS,aAAa,MAAM,KAAK;AAAA,IACjE,KAAK;AACD,aAAO,OAAOC,WAAU,SAAS,aAAa,MAAM,KAAK;AAAA,IAC7D,KAAK;AACD,aAAO,OAAOC,gBAAe,SAAS,aAAa,MAAM,KAAK;AAAA,IAClE,KAAK;AACD,aAAO,OAAOC,aAAY,SAAS,aAAa,MAAM,KAAK;AAAA,IAC/D,KAAK;AACD,aAAO,OAAOC,UAAS,SAAS,aAAa,MAAM,KAAK;AAAA,IAC5D;AACI,UAAI,CAAC,aAAa,IAAI,QAAQ,IAAI,CAAC;AAC/B,cAAM,IAAI,4BAA4B,MAAM;AAChD,aAAO,OAAOC,UAAS,SAAS,aAAa,MAAM,KAAK;AAAA,EAChE;AACJ;AAEO,SAAS,UAAU,MAAM;AAC5B,QAAM,WAAW,KAAK,WAAW,IAAIhC,OAAM,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,IAAIA,OAAM,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC;AAC1G,SAAO,IAAI,mBAAmB,QAAQ;AAC1C;;;AC5jBO,IAAM,4BAAN,cAAwC,aAAa;AAAA,EACxD,YAAY,QAAQ,OAAO,OAAO;AAC9B,UAAM,iEAAiE;AACvE,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACjB;AACJ;AAEO,IAAM,uBAAN,cAAmC,aAAa;AAAA,EACnD,YAAY,QAAQ,MAAM,OAAO,OAAO;AACpC,UAAM,iBAAiB,QAAQ,MAAM,UAAU,eAAe;AAC9D,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACjB;AACJ;AAKA,SAAS,QAAQ,QAAQ,MAAM,OAAO;AAClC,MAAI;AACA,WAAO,YAAY,MAAM,IAAI,OAAO,aAAa,EAAE,OAAO,KAAK,IAAI;AAAA,EACvE,SACO,OAAO;AACV,UAAM,IAAI,qBAAqB,QAAQ,MAAM,OAAO,KAAK;AAAA,EAC7D;AACJ;AAEA,SAASiC,WAAU,QAAQ,YAAY,MAAM,OAAO;AAChD,SAAQ,QAAQ,KAAK,IACf,QAAQ,QAAQ,MAAM,MAAM,IAAI,CAACC,QAAO,UAAUC,OAAM,OAAO,OAAO,YAAY,GAAG,IAAI,IAAI,KAAK,IAAID,MAAK,CAAC,CAAC,IAC7G,QAAQ,QAAQ,MAAM,KAAK;AACrC;AAEA,SAASE,eAAc,QAAQ,YAAY,MAAM,OAAO;AACpD,MAAI,CAAC,SAAS,KAAK,KAAK,YAAY,KAAK;AACrC,WAAO,QAAQ,QAAQ,MAAM,KAAK;AACtC,QAAM,eAAe,qBAAqB,MAAM;AAChD,QAAM,YAAY,aAAa,IAAI,WAAS,MAAM,CAAC,CAAC;AACpD,QAAM,kBAAkB,EAAE,GAAG,MAAM;AACnC,aAAW,CAAC,UAAU,WAAW,KAAK;AAClC,QAAI,YAAY,iBAAiB;AAC7B,sBAAgB,QAAQ,IAAID,OAAM,aAAa,YAAY,GAAG,IAAI,IAAI,QAAQ,IAAI,gBAAgB,QAAQ,CAAC;AAAA,IAC/G;AACJ,MAAI,CAAC,YAAY,OAAO,qBAAqB,GAAG;AAC5C,WAAO,QAAQ,QAAQ,MAAM,eAAe;AAAA,EAChD;AACA,QAAM,cAAc,OAAO,oBAAoB,eAAe;AAC9D,QAAM,wBAAwB,OAAO;AACrC,QAAM,oBAAoB,EAAE,GAAG,gBAAgB;AAC/C,aAAW,OAAO;AACd,QAAI,CAAC,UAAU,SAAS,GAAG,GAAG;AAC1B,wBAAkB,GAAG,IAAI,QAAQ,uBAAuB,GAAG,IAAI,IAAI,GAAG,IAAI,kBAAkB,GAAG,CAAC;AAAA,IACpG;AACJ,SAAO,QAAQ,QAAQ,MAAM,iBAAiB;AAClD;AAEA,SAASE,YAAW,QAAQ,YAAY,MAAM,OAAO;AACjD,QAAM,aAAa,WAAW,OAAO,OAAO,OAAO,KAAK;AACxD,QAAM,SAAS,OAAO,MAAM,OAAO,IAAI;AACvC,QAAM,SAASF,OAAM,QAAQ,CAAC,GAAG,YAAY,GAAG,UAAU,GAAG,MAAM,KAAK;AACxE,SAAO,QAAQ,QAAQ,MAAM,MAAM;AACvC;AACA,SAASG,SAAQ,QAAQ,YAAY,MAAM,OAAO;AAC9C,SAAO,QAAQ,QAAQ,MAAMH,OAAM,OAAO,KAAK,YAAY,MAAM,KAAK,CAAC;AAC3E;AAEA,SAASI,YAAW,QAAQ,YAAY,MAAM,OAAO;AACjD,MAAI,CAAC,SAAS,KAAK;AACf,WAAO,QAAQ,QAAQ,MAAM,KAAK;AACtC,QAAM,YAAY,kBAAkB,MAAM;AAC1C,QAAM,kBAAkB,EAAE,GAAG,MAAM;AACnC,aAAW,OAAO,WAAW;AACzB,QAAI,CAAC,eAAe,iBAAiB,GAAG;AACpC;AAIJ,QAAI,YAAiB,gBAAgB,GAAG,CAAC,MAAM,CAACC,aAAY,OAAO,WAAW,GAAG,CAAC,KAC9E,iBAAiB,wBAAwB,iBAAiB,GAAG;AAC7D;AAEJ,oBAAgB,GAAG,IAAIL,OAAM,OAAO,WAAW,GAAG,GAAG,YAAY,GAAG,IAAI,IAAI,GAAG,IAAI,gBAAgB,GAAG,CAAC;AAAA,EAC3G;AACA,MAAI,CAAC,SAAS,OAAO,oBAAoB,GAAG;AACxC,WAAO,QAAQ,QAAQ,MAAM,eAAe;AAAA,EAChD;AACA,QAAM,cAAc,OAAO,oBAAoB,eAAe;AAC9D,QAAM,uBAAuB,OAAO;AACpC,QAAM,oBAAoB,EAAE,GAAG,gBAAgB;AAC/C,aAAW,OAAO;AACd,QAAI,CAAC,UAAU,SAAS,GAAG,GAAG;AAC1B,wBAAkB,GAAG,IAAI,QAAQ,sBAAsB,GAAG,IAAI,IAAI,GAAG,IAAI,kBAAkB,GAAG,CAAC;AAAA,IACnG;AACJ,SAAO,QAAQ,QAAQ,MAAM,iBAAiB;AAClD;AAEA,SAASM,YAAW,QAAQ,YAAY,MAAM,OAAO;AACjD,MAAI,CAAC,SAAS,KAAK;AACf,WAAO,QAAQ,QAAQ,MAAM,KAAK;AACtC,QAAM,UAAU,OAAO,oBAAoB,OAAO,iBAAiB,EAAE,CAAC;AACtE,QAAM,YAAY,IAAI,OAAO,OAAO;AACpC,QAAM,kBAAkB,EAAE,GAAG,MAAM;AACnC,aAAW,OAAO,OAAO,oBAAoB,KAAK;AAC9C,QAAI,UAAU,KAAK,GAAG,GAAG;AACrB,sBAAgB,GAAG,IAAIN,OAAM,OAAO,kBAAkB,OAAO,GAAG,YAAY,GAAG,IAAI,IAAI,GAAG,IAAI,gBAAgB,GAAG,CAAC;AAAA,IACtH;AACJ,MAAI,CAAC,SAAS,OAAO,oBAAoB,GAAG;AACxC,WAAO,QAAQ,QAAQ,MAAM,eAAe;AAAA,EAChD;AACA,QAAM,cAAc,OAAO,oBAAoB,eAAe;AAC9D,QAAM,uBAAuB,OAAO;AACpC,QAAM,oBAAoB,EAAE,GAAG,gBAAgB;AAC/C,aAAW,OAAO;AACd,QAAI,CAAC,UAAU,KAAK,GAAG,GAAG;AACtB,wBAAkB,GAAG,IAAI,QAAQ,sBAAsB,GAAG,IAAI,IAAI,GAAG,IAAI,kBAAkB,GAAG,CAAC;AAAA,IACnG;AACJ,SAAO,QAAQ,QAAQ,MAAM,iBAAiB;AAClD;AAEA,SAASO,SAAQ,QAAQ,YAAY,MAAM,OAAO;AAC9C,QAAM,SAAS,MAAM,QAAQ,UAAU;AACvC,SAAO,QAAQ,QAAQ,MAAMP,OAAM,QAAQ,YAAY,MAAM,KAAK,CAAC;AACvE;AAEA,SAASQ,UAAS,QAAQ,YAAY,MAAM,OAAO;AAC/C,QAAM,SAAS,MAAM,QAAQ,UAAU;AACvC,SAAO,QAAQ,QAAQ,MAAMR,OAAM,QAAQ,YAAY,MAAM,KAAK,CAAC;AACvE;AAEA,SAASS,WAAU,QAAQ,YAAY,MAAM,OAAO;AAChD,SAAQ,QAAQ,KAAK,KAAK,QAAQ,OAAO,KAAK,IACxC,QAAQ,QAAQ,MAAM,OAAO,MAAM,IAAI,CAACC,SAAQ,UAAUV,OAAMU,SAAQ,YAAY,GAAG,IAAI,IAAI,KAAK,IAAI,MAAM,KAAK,CAAC,CAAC,CAAC,IACtH,QAAQ,QAAQ,MAAM,KAAK;AACrC;AAEA,SAASC,WAAU,QAAQ,YAAY,MAAM,OAAO;AAChD,aAAW,aAAa,OAAO,OAAO;AAClC,QAAI,CAAC,MAAM,WAAW,YAAY,KAAK;AACnC;AAEJ,UAAM,UAAUX,OAAM,WAAW,YAAY,MAAM,KAAK;AACxD,WAAO,QAAQ,QAAQ,MAAM,OAAO;AAAA,EACxC;AACA,SAAO,QAAQ,QAAQ,MAAM,KAAK;AACtC;AAEA,SAASA,OAAM,QAAQ,YAAY,MAAM,OAAO;AAC5C,QAAM,cAAc,QAAQ,QAAQ,UAAU;AAC9C,QAAM,UAAU;AAChB,UAAQ,OAAO,IAAI,GAAG;AAAA,IAClB,KAAK;AACD,aAAOF,WAAU,SAAS,aAAa,MAAM,KAAK;AAAA,IACtD,KAAK;AACD,aAAOI,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IACvD,KAAK;AACD,aAAOD,eAAc,SAAS,aAAa,MAAM,KAAK;AAAA,IAC1D,KAAK;AACD,aAAOE,SAAQ,SAAS,aAAa,MAAM,KAAK;AAAA,IACpD,KAAK;AACD,aAAOC,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IACvD,KAAK;AACD,aAAOE,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IACvD,KAAK;AACD,aAAOC,SAAQ,SAAS,aAAa,MAAM,KAAK;AAAA,IACpD,KAAK;AACD,aAAO,QAAQ,SAAS,MAAM,KAAK;AAAA,IACvC,KAAK;AACD,aAAOC,UAAS,SAAS,aAAa,MAAM,KAAK;AAAA,IACrD,KAAK;AACD,aAAOC,WAAU,SAAS,aAAa,MAAM,KAAK;AAAA,IACtD,KAAK;AACD,aAAOE,WAAU,SAAS,aAAa,MAAM,KAAK;AAAA,IACtD;AACI,aAAO,QAAQ,SAAS,MAAM,KAAK;AAAA,EAC3C;AACJ;AAMO,SAAS,gBAAgB,QAAQ,YAAY,OAAO;AACvD,SAAOX,OAAM,QAAQ,YAAY,IAAI,KAAK;AAC9C;;;AC5LO,IAAM,4BAAN,cAAwC,aAAa;AAAA,EACxD,YAAY,QAAQ,OAAO,OAAO;AAC9B,UAAM,sDAAsD;AAC5D,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACjB;AACJ;AAEO,IAAM,uBAAN,cAAmC,aAAa;AAAA,EACnD,YAAY,QAAQ,MAAM,OAAO,OAAO;AACpC,UAAM,GAAG,iBAAiB,QAAQ,MAAM,UAAU,eAAe,EAAE;AACnE,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACjB;AACJ;AAKA,SAASY,SAAQ,QAAQ,MAAM,OAAO;AAClC,MAAI;AACA,WAAO,YAAY,MAAM,IAAI,OAAO,aAAa,EAAE,OAAO,KAAK,IAAI;AAAA,EACvE,SACO,OAAO;AACV,UAAM,IAAI,qBAAqB,QAAQ,MAAM,OAAO,KAAK;AAAA,EAC7D;AACJ;AAEA,SAASC,WAAU,QAAQ,YAAY,MAAM,OAAO;AAChD,QAAM,YAAYD,SAAQ,QAAQ,MAAM,KAAK;AAC7C,SAAO,QAAQ,SAAS,IAClB,UAAU,IAAI,CAACE,QAAO,UAAUC,OAAM,OAAO,OAAO,YAAY,GAAG,IAAI,IAAI,KAAK,IAAID,MAAK,CAAC,IAC1F;AACV;AAEA,SAASE,YAAW,QAAQ,YAAY,MAAM,OAAO;AACjD,QAAM,aAAa,WAAW,OAAO,OAAO,OAAO,KAAK;AACxD,QAAM,SAAS,OAAO,MAAM,OAAO,IAAI;AACvC,QAAM,SAASJ,SAAQ,QAAQ,MAAM,KAAK;AAC1C,SAAOG,OAAM,QAAQ,CAAC,GAAG,YAAY,GAAG,UAAU,GAAG,MAAM,MAAM;AACrE;AAEA,SAASE,eAAc,QAAQ,YAAY,MAAM,OAAO;AACpD,QAAM,YAAYL,SAAQ,QAAQ,MAAM,KAAK;AAC7C,MAAI,CAAC,SAAS,KAAK,KAAK,YAAY,KAAK;AACrC,WAAO;AACX,QAAM,eAAe,qBAAqB,MAAM;AAChD,QAAM,YAAY,aAAa,IAAI,WAAS,MAAM,CAAC,CAAC;AACpD,QAAM,kBAAkB,EAAE,GAAG,UAAU;AACvC,aAAW,CAAC,UAAU,WAAW,KAAK;AAClC,QAAI,YAAY,iBAAiB;AAC7B,sBAAgB,QAAQ,IAAIG,OAAM,aAAa,YAAY,GAAG,IAAI,IAAI,QAAQ,IAAI,gBAAgB,QAAQ,CAAC;AAAA,IAC/G;AACJ,MAAI,CAAC,YAAY,OAAO,qBAAqB,GAAG;AAC5C,WAAO;AAAA,EACX;AACA,QAAM,cAAc,OAAO,oBAAoB,eAAe;AAC9D,QAAM,wBAAwB,OAAO;AACrC,QAAM,aAAa,EAAE,GAAG,gBAAgB;AACxC,aAAW,OAAO;AACd,QAAI,CAAC,UAAU,SAAS,GAAG,GAAG;AAC1B,iBAAW,GAAG,IAAIH,SAAQ,uBAAuB,GAAG,IAAI,IAAI,GAAG,IAAI,WAAW,GAAG,CAAC;AAAA,IACtF;AACJ,SAAO;AACX;AAEA,SAASM,SAAQ,QAAQ,YAAY,MAAM,OAAO;AAC9C,SAAON,SAAQ,OAAO,KAAK,MAAMA,SAAQ,QAAQ,MAAM,KAAK,CAAC;AACjE;AAEA,SAASO,YAAW,QAAQ,YAAY,MAAM,OAAO;AACjD,QAAM,YAAYP,SAAQ,QAAQ,MAAM,KAAK;AAC7C,MAAI,CAAC,SAAS,SAAS;AACnB,WAAO;AACX,QAAM,YAAY,kBAAkB,MAAM;AAC1C,QAAM,kBAAkB,EAAE,GAAG,UAAU;AACvC,aAAW,OAAO,WAAW;AACzB,QAAI,CAAC,eAAe,iBAAiB,GAAG;AACpC;AAIJ,QAAI,YAAiB,gBAAgB,GAAG,CAAC,MAAM,CAACQ,aAAY,OAAO,WAAW,GAAG,CAAC,KAC9E,iBAAiB,wBAAwB,iBAAiB,GAAG;AAC7D;AAEJ,oBAAgB,GAAG,IAAIL,OAAM,OAAO,WAAW,GAAG,GAAG,YAAY,GAAG,IAAI,IAAI,GAAG,IAAI,gBAAgB,GAAG,CAAC;AAAA,EAC3G;AACA,MAAI,CAAC,SAAS,OAAO,oBAAoB,GAAG;AACxC,WAAO;AAAA,EACX;AACA,QAAM,cAAc,OAAO,oBAAoB,eAAe;AAC9D,QAAM,uBAAuB,OAAO;AACpC,QAAM,aAAa,EAAE,GAAG,gBAAgB;AACxC,aAAW,OAAO;AACd,QAAI,CAAC,UAAU,SAAS,GAAG,GAAG;AAC1B,iBAAW,GAAG,IAAIH,SAAQ,sBAAsB,GAAG,IAAI,IAAI,GAAG,IAAI,WAAW,GAAG,CAAC;AAAA,IACrF;AACJ,SAAO;AACX;AAEA,SAASS,YAAW,QAAQ,YAAY,MAAM,OAAO;AACjD,QAAM,YAAYT,SAAQ,QAAQ,MAAM,KAAK;AAC7C,MAAI,CAAC,SAAS,KAAK;AACf,WAAO;AACX,QAAM,UAAU,OAAO,oBAAoB,OAAO,iBAAiB,EAAE,CAAC;AACtE,QAAM,YAAY,IAAI,OAAO,OAAO;AACpC,QAAM,kBAAkB,EAAE,GAAG,UAAU;AACvC,aAAW,OAAO,OAAO,oBAAoB,KAAK;AAC9C,QAAI,UAAU,KAAK,GAAG,GAAG;AACrB,sBAAgB,GAAG,IAAIG,OAAM,OAAO,kBAAkB,OAAO,GAAG,YAAY,GAAG,IAAI,IAAI,GAAG,IAAI,gBAAgB,GAAG,CAAC;AAAA,IACtH;AACJ,MAAI,CAAC,SAAS,OAAO,oBAAoB,GAAG;AACxC,WAAO;AAAA,EACX;AACA,QAAM,cAAc,OAAO,oBAAoB,eAAe;AAC9D,QAAM,uBAAuB,OAAO;AACpC,QAAM,aAAa,EAAE,GAAG,gBAAgB;AACxC,aAAW,OAAO;AACd,QAAI,CAAC,UAAU,KAAK,GAAG,GAAG;AACtB,iBAAW,GAAG,IAAIH,SAAQ,sBAAsB,GAAG,IAAI,IAAI,GAAG,IAAI,WAAW,GAAG,CAAC;AAAA,IACrF;AACJ,SAAO;AACX;AAEA,SAASU,SAAQ,QAAQ,YAAY,MAAM,OAAO;AAC9C,QAAM,SAAS,MAAM,QAAQ,UAAU;AACvC,QAAM,WAAWP,OAAM,QAAQ,YAAY,MAAM,KAAK;AACtD,SAAOH,SAAQ,QAAQ,MAAM,QAAQ;AACzC;AAEA,SAASW,UAAS,QAAQ,YAAY,MAAM,OAAO;AAC/C,QAAM,SAAS,MAAM,QAAQ,UAAU;AACvC,QAAM,WAAWR,OAAM,QAAQ,YAAY,MAAM,KAAK;AACtD,SAAOH,SAAQ,QAAQ,MAAM,QAAQ;AACzC;AAEA,SAASY,WAAU,QAAQ,YAAY,MAAM,OAAO;AAChD,QAAM,SAASZ,SAAQ,QAAQ,MAAM,KAAK;AAC1C,SAAO,QAAQ,OAAO,KAAK,IAAI,OAAO,MAAM,IAAI,CAACa,SAAQ,UAAUV,OAAMU,SAAQ,YAAY,GAAG,IAAI,IAAI,KAAK,IAAI,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC;AACxI;AAEA,SAASC,WAAU,QAAQ,YAAY,MAAM,OAAO;AAEhD,aAAW,aAAa,OAAO,OAAO;AAClC,QAAI,CAAC,MAAM,WAAW,YAAY,KAAK;AACnC;AACJ,UAAM,SAASX,OAAM,WAAW,YAAY,MAAM,KAAK;AACvD,WAAOH,SAAQ,QAAQ,MAAM,MAAM;AAAA,EACvC;AAEA,aAAW,aAAa,OAAO,OAAO;AAClC,UAAM,SAASG,OAAM,WAAW,YAAY,MAAM,KAAK;AACvD,QAAI,CAAC,MAAM,QAAQ,YAAY,MAAM;AACjC;AACJ,WAAOH,SAAQ,QAAQ,MAAM,MAAM;AAAA,EACvC;AACA,SAAOA,SAAQ,QAAQ,MAAM,KAAK;AACtC;AAEA,SAASG,OAAM,QAAQ,YAAY,MAAM,OAAO;AAC5C,QAAM,cAAc,QAAQ,QAAQ,UAAU;AAC9C,QAAM,UAAU;AAChB,UAAQ,OAAO,IAAI,GAAG;AAAA,IAClB,KAAK;AACD,aAAOF,WAAU,SAAS,aAAa,MAAM,KAAK;AAAA,IACtD,KAAK;AACD,aAAOG,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IACvD,KAAK;AACD,aAAOC,eAAc,SAAS,aAAa,MAAM,KAAK;AAAA,IAC1D,KAAK;AACD,aAAOC,SAAQ,SAAS,aAAa,MAAM,KAAK;AAAA,IACpD,KAAK;AACD,aAAOC,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IACvD,KAAK;AACD,aAAOE,YAAW,SAAS,aAAa,MAAM,KAAK;AAAA,IACvD,KAAK;AACD,aAAOC,SAAQ,SAAS,aAAa,MAAM,KAAK;AAAA,IACpD,KAAK;AACD,aAAOC,UAAS,SAAS,aAAa,MAAM,KAAK;AAAA,IACrD,KAAK;AACD,aAAOC,WAAU,SAAS,aAAa,MAAM,KAAK;AAAA,IACtD,KAAK;AACD,aAAOE,WAAU,SAAS,aAAa,MAAM,KAAK;AAAA,IACtD;AACI,aAAOd,SAAQ,SAAS,MAAM,KAAK;AAAA,EAC3C;AACJ;AAOO,SAAS,gBAAgB,QAAQ,YAAY,OAAO;AACvD,SAAOG,OAAM,QAAQ,YAAY,IAAI,KAAK;AAC9C;;;AC9MA,SAASY,WAAU,QAAQ,YAAY;AACnC,SAAO,YAAY,MAAM,KAAKC,OAAM,OAAO,OAAO,UAAU;AAChE;AAEA,SAASC,mBAAkB,QAAQ,YAAY;AAC3C,SAAO,YAAY,MAAM,KAAKD,OAAM,OAAO,OAAO,UAAU;AAChE;AAEA,SAASE,iBAAgB,QAAQ,YAAY;AACzC,SAAO,YAAY,MAAM,KAAKF,OAAM,OAAO,SAAS,UAAU,KAAK,OAAO,WAAW,KAAK,CAACG,YAAWH,OAAMG,SAAQ,UAAU,CAAC;AACnI;AAEA,SAASC,cAAa,QAAQ,YAAY;AACtC,SAAO,YAAY,MAAM,KAAKJ,OAAM,OAAO,SAAS,UAAU,KAAK,OAAO,WAAW,KAAK,CAACG,YAAWH,OAAMG,SAAQ,UAAU,CAAC;AACnI;AAEA,SAASE,eAAc,QAAQ,YAAY;AACvC,SAAO,YAAY,MAAM,KAAK,YAAY,OAAO,qBAAqB,KAAK,OAAO,MAAM,KAAK,CAACF,YAAWH,OAAMG,SAAQ,UAAU,CAAC;AACtI;AAEA,SAASG,YAAW,QAAQ,YAAY;AACpC,QAAM,aAAa,WAAW,OAAO,oBAAoB,OAAO,KAAK,EAAE,OAAO,CAAC,QAAQ,QAAQ,CAAC,GAAG,QAAQ,OAAO,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AACjI,QAAM,SAAS,OAAO,MAAM,OAAO,IAAI;AACvC,SAAO,YAAY,MAAM,KAAKN,OAAM,QAAQ,CAAC,GAAG,YAAY,GAAG,UAAU,CAAC;AAC9E;AAEA,SAASO,cAAa,QAAQ,YAAY;AACtC,SAAO,YAAY,MAAM,KAAKP,OAAM,OAAO,OAAO,UAAU;AAChE;AAEA,SAASQ,SAAQ,QAAQ,YAAY;AACjC,SAAO,YAAY,MAAM,KAAKR,OAAM,OAAO,KAAK,UAAU;AAC9D;AAEA,SAASS,YAAW,QAAQ,YAAY;AACpC,SAAQ,YAAY,MAAM,KACtB,OAAO,OAAO,OAAO,UAAU,EAAE,KAAK,CAACN,YAAWH,OAAMG,SAAQ,UAAU,CAAC,KAC1E,SAAS,OAAO,oBAAoB,KAAKH,OAAM,OAAO,sBAAsB,UAAU;AAC/F;AAEA,SAASU,aAAY,QAAQ,YAAY;AACrC,SAAO,YAAY,MAAM,KAAKV,OAAM,OAAO,MAAM,UAAU;AAC/D;AAEA,SAASW,YAAW,QAAQ,YAAY;AACpC,QAAM,UAAU,OAAO,oBAAoB,OAAO,iBAAiB,EAAE,CAAC;AACtE,QAAM,WAAW,OAAO,kBAAkB,OAAO;AACjD,SAAO,YAAY,MAAM,KAAKX,OAAM,UAAU,UAAU,KAAM,SAAS,OAAO,oBAAoB,KAAK,YAAY,OAAO,oBAAoB;AAClJ;AAEA,SAASY,SAAQ,QAAQ,YAAY;AACjC,MAAI,YAAY,MAAM;AAClB,WAAO;AACX,SAAOZ,OAAM,MAAM,QAAQ,UAAU,GAAG,UAAU;AACtD;AAEA,SAASa,UAAS,QAAQ,YAAY;AAClC,MAAI,YAAY,MAAM;AAClB,WAAO;AACX,SAAOb,OAAM,MAAM,QAAQ,UAAU,GAAG,UAAU;AACtD;AAEA,SAASc,WAAU,QAAQ,YAAY;AACnC,SAAO,YAAY,MAAM,KAAM,CAAC,YAAY,OAAO,KAAK,KAAK,OAAO,MAAM,KAAK,CAACX,YAAWH,OAAMG,SAAQ,UAAU,CAAC;AACxH;AAEA,SAASY,WAAU,QAAQ,YAAY;AACnC,SAAO,YAAY,MAAM,KAAK,OAAO,MAAM,KAAK,CAACZ,YAAWH,OAAMG,SAAQ,UAAU,CAAC;AACzF;AAEA,SAASH,OAAM,QAAQ,YAAY;AAC/B,QAAM,cAAc,QAAQ,QAAQ,UAAU;AAC9C,QAAM,UAAU;AAChB,MAAI,OAAO,OAAO,QAAQ,IAAI,OAAO,GAAG;AACpC,WAAO;AACX,MAAI,OAAO;AACP,YAAQ,IAAI,OAAO,GAAG;AAC1B,UAAQ,OAAO,IAAI,GAAG;AAAA,IAClB,KAAK;AACD,aAAOD,WAAU,SAAS,WAAW;AAAA,IACzC,KAAK;AACD,aAAOE,mBAAkB,SAAS,WAAW;AAAA,IACjD,KAAK;AACD,aAAOC,iBAAgB,SAAS,WAAW;AAAA,IAC/C,KAAK;AACD,aAAOE,cAAa,SAAS,WAAW;AAAA,IAC5C,KAAK;AACD,aAAOE,YAAW,SAAS,WAAW;AAAA,IAC1C,KAAK;AACD,aAAOD,eAAc,SAAS,WAAW;AAAA,IAC7C,KAAK;AACD,aAAOE,cAAa,SAAS,WAAW;AAAA,IAC5C,KAAK;AACD,aAAOC,SAAQ,SAAS,WAAW;AAAA,IACvC,KAAK;AACD,aAAOC,YAAW,SAAS,WAAW;AAAA,IAC1C,KAAK;AACD,aAAOC,aAAY,SAAS,WAAW;AAAA,IAC3C,KAAK;AACD,aAAOC,YAAW,SAAS,WAAW;AAAA,IAC1C,KAAK;AACD,aAAOC,SAAQ,SAAS,WAAW;AAAA,IACvC,KAAK;AACD,aAAOC,UAAS,SAAS,WAAW;AAAA,IACxC,KAAK;AACD,aAAOC,WAAU,SAAS,WAAW;AAAA,IACzC,KAAK;AACD,aAAOC,WAAU,SAAS,WAAW;AAAA,IACzC;AACI,aAAO,YAAY,MAAM;AAAA,EACjC;AACJ;AACA,IAAM,UAAU,oBAAI,IAAI;AAEjB,SAAS,aAAa,QAAQ,YAAY;AAC7C,UAAQ,MAAM;AACd,SAAOf,OAAM,QAAQ,UAAU;AACnC;;;ACzGO,IAAM,YAAN,MAAgB;AAAA,EACnB,YAAY,QAAQ,YAAY,WAAW,MAAM;AAC7C,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,eAAe,aAAa,QAAQ,UAAU;AAAA,EACvD;AAAA;AAAA,EAEA,OAAO;AACH,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,SAAS;AACL,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,aAAa;AACT,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA,EAEA,OAAO,OAAO;AACV,WAAO,OAAO,KAAK,QAAQ,KAAK,YAAY,KAAK;AAAA,EACrD;AAAA;AAAA,EAEA,MAAM,OAAO;AACT,WAAO,KAAK,UAAU,KAAK;AAAA,EAC/B;AAAA;AAAA,EAEA,OAAO,OAAO;AACV,QAAI,CAAC,KAAK,UAAU,KAAK;AACrB,YAAM,IAAI,0BAA0B,KAAK,QAAQ,OAAO,KAAK,OAAO,KAAK,EAAE,MAAM,CAAC;AACtF,WAAQ,KAAK,eAAe,gBAAgB,KAAK,QAAQ,KAAK,YAAY,KAAK,IAAI;AAAA,EACvF;AAAA;AAAA,EAEA,OAAO,OAAO;AACV,UAAM,UAAU,KAAK,eAAe,gBAAgB,KAAK,QAAQ,KAAK,YAAY,KAAK,IAAI;AAC3F,QAAI,CAAC,KAAK,UAAU,OAAO;AACvB,YAAM,IAAI,0BAA0B,KAAK,QAAQ,OAAO,KAAK,OAAO,KAAK,EAAE,MAAM,CAAC;AACtF,WAAO;AAAA,EACX;AACJ;AAIA,IAAI;AAAA,CACH,SAAUgB,YAAW;AAClB,WAAS,WAAW,MAAM;AACtB,WAAO,SAAS;AAAA,EACpB;AACA,EAAAA,WAAU,aAAa;AACvB,WAAS,aAAa,MAAM;AACxB,WAAO,SAAS;AAAA,EACpB;AACA,EAAAA,WAAU,eAAe;AACzB,WAAS,QAAQ,MAAM;AACnB,WAAQ,QAAQ,MAAM,QAAQ,MAAQ,QAAQ,MAAM,QAAQ;AAAA,EAChE;AACA,EAAAA,WAAU,UAAU;AACpB,WAAS,UAAU,MAAM;AACrB,WAAO,QAAQ,MAAM,QAAQ;AAAA,EACjC;AACA,EAAAA,WAAU,YAAY;AAC1B,GAAG,cAAc,YAAY,CAAC,EAAE;AAIhC,IAAI;AAAA,CACH,SAAUC,mBAAkB;AACzB,WAAS,wBAAwB,OAAO;AACpC,QAAI,MAAM,WAAW;AACjB,aAAO;AACX,WAAO,UAAU,UAAU,MAAM,WAAW,CAAC,CAAC;AAAA,EAClD;AACA,WAAS,WAAW,OAAO;AACvB,QAAI,wBAAwB,KAAK;AAC7B,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAM,OAAO,MAAM,WAAW,CAAC;AAC/B,YAAM,QAAQ,UAAU,QAAQ,IAAI,KAAK,UAAU,UAAU,IAAI,KAAK,UAAU,WAAW,IAAI,KAAK,UAAU,aAAa,IAAI;AAC/H,UAAI,CAAC;AACD,eAAO;AAAA,IACf;AACA,WAAO;AAAA,EACX;AACA,WAAS,aAAa,KAAK;AACvB,WAAO,IAAI,QAAQ,MAAM,KAAK;AAAA,EAClC;AACA,WAAS,OAAO,QAAQ,KAAK;AACzB,WAAO,WAAW,GAAG,IAAI,GAAG,MAAM,IAAI,GAAG,KAAK,GAAG,MAAM,KAAK,aAAa,GAAG,CAAC;AAAA,EACjF;AACA,EAAAA,kBAAiB,SAAS;AAC9B,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AAI9C,IAAI;AAAA,CACH,SAAUC,aAAY;AACnB,WAAS,OAAO,KAAK;AACjB,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,YAAM,OAAO,IAAI,WAAW,CAAC;AAC7B,UAAI,UAAU,UAAU,IAAI,KAAK,UAAU,QAAQ,IAAI,GAAG;AACtD,eAAO,KAAK,IAAI,OAAO,CAAC,CAAC;AAAA,MAC7B,OACK;AACD,eAAO,KAAK,IAAI,IAAI,GAAG;AAAA,MAC3B;AAAA,IACJ;AACA,WAAO,OAAO,KAAK,EAAE,EAAE,QAAQ,OAAO,GAAG;AAAA,EAC7C;AACA,EAAAA,YAAW,SAAS;AACxB,GAAG,eAAe,aAAa,CAAC,EAAE;AAIlC,IAAI;AAAA,CACH,SAAUC,gBAAe;AACtB,WAAS,OAAO,SAAS;AACrB,WAAO,QAAQ,QAAQ,MAAM,KAAK;AAAA,EACtC;AACA,EAAAA,eAAc,SAAS;AAC3B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAIjC,IAAM,+BAAN,cAA2C,aAAa;AAAA,EAC3D,YAAY,QAAQ;AAChB,UAAM,cAAc;AACpB,SAAK,SAAS;AAAA,EAClB;AACJ;AACO,IAAM,6BAAN,cAAyC,aAAa;AAAA,EACzD,YAAY,QAAQ;AAChB,UAAM,iEAAiE;AACvE,SAAK,SAAS;AAAA,EAClB;AACJ;AAIO,IAAI;AAAA,CACV,SAAUC,SAAQ;AACf,WAAS,wBAAwB,OAAO,KAAK,YAAY;AACrD,WAAO,iBAAiB,6BAA6B,KAAK,GAAG,QAAQ,KAAK,MAAM,UAAU,aAAa,IAAI,iBAAiB,OAAO,OAAO,GAAG,CAAC,oBAAoB,UAAU;AAAA,EAChL;AACA,EAAAA,QAAO,0BAA0B;AACjC,WAAS,aAAa,OAAO;AACzB,WAAO,CAAC,iBAAiB,mBAAmB,WAAW,KAAK,oBAAoB,KAAK,+BAA+B,KAAK,OAAO,WAAW,KAAK,oBAAoB,KAAK;AAAA,EAC7K;AACA,EAAAA,QAAO,eAAe;AACtB,WAAS,aAAa,OAAO;AACzB,WAAO,CAAC,iBAAiB,mBACnB,WAAW,KAAK,oBAAoB,KAAK,+BAA+B,KAAK,UAAU,KAAK,0BAA0B,KAAK,6BAC3H,WAAW,KAAK,oBAAoB,KAAK,kBAAkB,KAAK,0BAA0B,KAAK;AAAA,EACzG;AACA,EAAAA,QAAO,eAAe;AACtB,WAAS,aAAa,OAAO;AACzB,WAAO,iBAAiB,WAAW,UAAU,KAAK,kBAAkB,mBAAmB,KAAK;AAAA,EAChG;AACA,EAAAA,QAAO,eAAe;AACtB,WAAS,WAAW,OAAO;AACvB,WAAO,iBAAiB,gBAAgB,IAAI,KAAK,qBAAqB,KAAK,eAAe,GAAG,KAAK;AAAA,EACtG;AACA,EAAAA,QAAO,aAAa;AACxB,GAAG,WAAW,SAAS,CAAC,EAAE;AAEnB,IAAI;AAAA,CACV,SAAUC,eAAc;AAIrB,WAASC,gBAAe,QAAQ;AAC5B,WAAO,OAAO,IAAI,MAAM,SAAS,OAAO,IAAI,MAAM;AAAA,EACtD;AAIA,YAAUC,SAAQ,QAAQ,YAAY,OAAO;AACzC,UAAM;AAAA,EACV;AACA,YAAUC,cAAa,QAAQ,YAAY,OAAO;AAC9C,UAAM;AAAA,EACV;AACA,YAAUC,WAAU,QAAQ,YAAY,OAAO;AAC3C,UAAM,iBAAiB,KAAK;AAC5B,UAAM,CAAC,WAAW,WAAW,IAAI,CAAC,gBAAgB,SAAS,KAAK,GAAG,gBAAgB,OAAO,QAAQ,CAAC;AACnG,QAAI,SAAS,OAAO,QAAQ;AACxB,YAAM,GAAG,KAAK,cAAc,OAAO,QAAQ;AAC/C,QAAI,SAAS,OAAO,QAAQ;AACxB,YAAM,GAAG,KAAK,cAAc,OAAO,QAAQ;AAC/C,UAAM,oBAAoB,iBAAiB,OAAO,OAAO,YAAY,OAAO;AAC5E,UAAM,GAAG,KAAK,WAAW,SAAS,QAAQ,iBAAiB;AAC3D,QAAIC,UAAS,OAAO,QAAQ,KAAK,SAAS,OAAO,WAAW,KAAK,SAAS,OAAO,WAAW,GAAG;AAC3F,YAAM,iBAAiBA,UAAS,OAAO,QAAQ,IAAI,OAAO,WAAW,MAAM;AAC3E,YAAM,kBAAkB,iBAAiB,gBAAgB,YAAY,OAAO;AAC5E,YAAM,mBAAmB,SAAS,OAAO,WAAW,IAAI,CAAC,aAAa,OAAO,WAAW,GAAG,IAAI,CAAC;AAChG,YAAM,mBAAmB,SAAS,OAAO,WAAW,IAAI,CAAC,aAAa,OAAO,WAAW,GAAG,IAAI,CAAC;AAChG,YAAM,aAAa,+BAA+B,WAAW,KAAK,SAAS,QAAQ,eAAe;AAClG,YAAM,QAAQ,CAAC,eAAe,GAAG,kBAAkB,GAAG,gBAAgB,EAAE,KAAK,MAAM;AACnF,YAAM,KAAK,SAAS,UAAU,UAAU,YAAY,KAAK,MAAM,KAAK;AAAA,IACxE;AACA,QAAI,OAAO,gBAAgB,MAAM;AAC7B,YAAM,QAAQ;AACd,YAAM,QAAQ,wDAAwD,KAAK;AAC3E,YAAM,KAAK,SAAS,UAAU,KAAK,MAAM,KAAK;AAAA,IAClD;AAAA,EACJ;AACA,YAAUC,mBAAkB,QAAQ,YAAY,OAAO;AACnD,UAAM,yDAAyD,KAAK;AAAA,EACxE;AACA,YAAUC,YAAW,QAAQ,YAAY,OAAO;AAC5C,UAAM,WAAW,KAAK;AACtB,QAAI,SAAS,OAAO,gBAAgB;AAChC,YAAM,GAAG,KAAK,aAAa,OAAO,gBAAgB;AACtD,QAAI,SAAS,OAAO,gBAAgB;AAChC,YAAM,GAAG,KAAK,aAAa,OAAO,gBAAgB;AACtD,QAAI,SAAS,OAAO,OAAO;AACvB,YAAM,GAAG,KAAK,cAAc,OAAO,OAAO;AAC9C,QAAI,SAAS,OAAO,OAAO;AACvB,YAAM,GAAG,KAAK,cAAc,OAAO,OAAO;AAC9C,QAAI,SAAS,OAAO,UAAU;AAC1B,YAAM,IAAI,KAAK,aAAa,OAAO,UAAU;AAAA,EACrD;AACA,YAAUC,aAAY,QAAQ,YAAY,OAAO;AAC7C,UAAM,WAAW,KAAK;AAAA,EAC1B;AACA,YAAUC,iBAAgB,QAAQ,YAAY,OAAO;AACjD,WAAOC,OAAM,OAAO,SAAS,YAAY,GAAG,KAAK,YAAY;AAAA,EACjE;AACA,YAAUC,UAAS,QAAQ,YAAY,OAAO;AAC1C,UAAM,IAAI,KAAK,wCAAwC,KAAK;AAC5D,QAAI,SAAS,OAAO,yBAAyB;AACzC,YAAM,GAAG,KAAK,gBAAgB,OAAO,yBAAyB;AAClE,QAAI,SAAS,OAAO,yBAAyB;AACzC,YAAM,GAAG,KAAK,gBAAgB,OAAO,yBAAyB;AAClE,QAAI,SAAS,OAAO,gBAAgB;AAChC,YAAM,GAAG,KAAK,iBAAiB,OAAO,gBAAgB;AAC1D,QAAI,SAAS,OAAO,gBAAgB;AAChC,YAAM,GAAG,KAAK,iBAAiB,OAAO,gBAAgB;AAC1D,QAAI,SAAS,OAAO,mBAAmB;AACnC,YAAM,IAAI,KAAK,gBAAgB,OAAO,mBAAmB;AAAA,EACjE;AACA,YAAUC,cAAa,QAAQ,YAAY,OAAO;AAC9C,UAAM,WAAW,KAAK;AAAA,EAC1B;AACA,YAAUC,YAAW,QAAQ,YAAY,OAAO;AAC5C,UAAM,UAAU,WAAW,OAAO,oBAAoB,OAAO,KAAK,EAAE,OAAO,CAAC,QAAQ,QAAQ;AACxF,aAAO,CAAC,GAAG,QAAQ,OAAO,MAAM,GAAG,CAAC;AAAA,IACxC,GAAG,CAAC,CAAC;AACL,WAAOH,OAAM,IAAI,OAAO,IAAI,GAAG,CAAC,GAAG,YAAY,GAAG,OAAO,GAAG,KAAK;AAAA,EACrE;AACA,YAAUI,aAAY,QAAQ,YAAY,OAAO;AAC7C,UAAM,oBAAoB,KAAK;AAC/B,QAAI,SAAS,OAAO,gBAAgB;AAChC,YAAM,GAAG,KAAK,MAAM,OAAO,gBAAgB;AAC/C,QAAI,SAAS,OAAO,gBAAgB;AAChC,YAAM,GAAG,KAAK,MAAM,OAAO,gBAAgB;AAC/C,QAAI,SAAS,OAAO,OAAO;AACvB,YAAM,GAAG,KAAK,OAAO,OAAO,OAAO;AACvC,QAAI,SAAS,OAAO,OAAO;AACvB,YAAM,GAAG,KAAK,OAAO,OAAO,OAAO;AACvC,QAAI,SAAS,OAAO,UAAU;AAC1B,YAAM,IAAI,KAAK,MAAM,OAAO,UAAU;AAAA,EAC9C;AACA,YAAUC,eAAc,QAAQ,YAAY,OAAO;AAC/C,UAAM,SAAS,OAAO,MAAM,IAAI,CAACC,YAAW,iBAAiBA,SAAQ,YAAY,KAAK,CAAC,EAAE,KAAK,MAAM;AACpG,QAAI,OAAO,0BAA0B,OAAO;AACxC,YAAM,WAAW,eAAe,GAAG,IAAI,OAAO,aAAa,MAAM,CAAC,CAAC,GAAG;AACtE,YAAM,SAAS,8BAA8B,KAAK,kBAAkB,QAAQ;AAC5E,YAAM,IAAI,MAAM,OAAO,MAAM;AAAA,IACjC,WACSX,UAAS,OAAO,qBAAqB,GAAG;AAC7C,YAAM,WAAW,eAAe,GAAG,IAAI,OAAO,aAAa,MAAM,CAAC,CAAC,GAAG;AACtE,YAAM,SAAS,8BAA8B,KAAK,kBAAkB,QAAQ,iBAAiB,iBAAiB,OAAO,uBAAuB,YAAY,GAAG,KAAK,OAAO,CAAC;AACxK,YAAM,IAAI,MAAM,OAAO,MAAM;AAAA,IACjC,OACK;AACD,YAAM,IAAI,MAAM;AAAA,IACpB;AAAA,EACJ;AACA,YAAUY,cAAa,QAAQ,YAAY,OAAO;AAC9C,UAAM,oDAAoD,KAAK;AAAA,EACnE;AACA,YAAUC,aAAY,QAAQ,YAAY,OAAO;AAC7C,QAAI,OAAO,OAAO,UAAU,YAAY,OAAO,OAAO,UAAU,WAAW;AACvE,YAAM,IAAI,KAAK,QAAQ,OAAO,KAAK;AAAA,IACvC,OACK;AACD,YAAM,IAAI,KAAK,SAAS,cAAc,OAAO,OAAO,KAAK,CAAC;AAAA,IAC9D;AAAA,EACJ;AACA,YAAUC,WAAU,QAAQ,YAAY,OAAO;AAC3C,UAAM;AAAA,EACV;AACA,YAAUC,SAAQ,QAAQ,YAAY,OAAO;AACzC,UAAM,aAAa,iBAAiB,OAAO,KAAK,YAAY,KAAK;AACjE,UAAM,KAAK,UAAU;AAAA,EACzB;AACA,YAAUC,UAAS,QAAQ,YAAY,OAAO;AAC1C,UAAM,IAAI,KAAK;AAAA,EACnB;AACA,YAAUC,YAAW,QAAQ,YAAY,OAAO;AAC5C,UAAM,OAAO,aAAa,KAAK;AAC/B,QAAI,SAAS,OAAO,gBAAgB;AAChC,YAAM,GAAG,KAAK,MAAM,OAAO,gBAAgB;AAC/C,QAAI,SAAS,OAAO,gBAAgB;AAChC,YAAM,GAAG,KAAK,MAAM,OAAO,gBAAgB;AAC/C,QAAI,SAAS,OAAO,OAAO;AACvB,YAAM,GAAG,KAAK,OAAO,OAAO,OAAO;AACvC,QAAI,SAAS,OAAO,OAAO;AACvB,YAAM,GAAG,KAAK,OAAO,OAAO,OAAO;AACvC,QAAI,SAAS,OAAO,UAAU;AAC1B,YAAM,IAAI,KAAK,MAAM,OAAO,UAAU;AAAA,EAC9C;AACA,YAAUC,YAAW,QAAQ,YAAY,OAAO;AAC5C,UAAM,OAAO,aAAa,KAAK;AAC/B,QAAI,SAAS,OAAO,aAAa;AAC7B,YAAM,8BAA8B,KAAK,eAAe,OAAO,aAAa;AAChF,QAAI,SAAS,OAAO,aAAa;AAC7B,YAAM,8BAA8B,KAAK,eAAe,OAAO,aAAa;AAChF,UAAM,YAAY,OAAO,oBAAoB,OAAO,UAAU;AAC9D,eAAW,YAAY,WAAW;AAC9B,YAAM,mBAAmB,iBAAiB,OAAO,OAAO,QAAQ;AAChE,YAAM,WAAW,OAAO,WAAW,QAAQ;AAC3C,UAAI,OAAO,YAAY,OAAO,SAAS,SAAS,QAAQ,GAAG;AACvD,eAAOb,OAAM,UAAU,YAAY,gBAAgB;AACnD,YAAI,sBAAsB,QAAQ,KAAKT,gBAAe,QAAQ;AAC1D,gBAAM,KAAK,QAAQ,QAAQ,KAAK;AAAA,MACxC,OACK;AACD,cAAM,aAAa,iBAAiB,UAAU,YAAY,gBAAgB;AAC1E,cAAM,OAAO,wBAAwB,OAAO,UAAU,UAAU;AAAA,MACpE;AAAA,IACJ;AACA,QAAI,OAAO,yBAAyB,OAAO;AACvC,UAAI,OAAO,YAAY,OAAO,SAAS,WAAW,UAAU,QAAQ;AAChE,cAAM,8BAA8B,KAAK,gBAAgB,UAAU,MAAM;AAAA,MAC7E,OACK;AACD,cAAM,OAAO,IAAI,UAAU,IAAI,CAAC,QAAQ,IAAI,GAAG,GAAG,EAAE,KAAK,IAAI,CAAC;AAC9D,cAAM,8BAA8B,KAAK,kBAAkB,IAAI;AAAA,MACnE;AAAA,IACJ;AACA,QAAI,OAAO,OAAO,yBAAyB,UAAU;AACjD,YAAM,aAAa,iBAAiB,OAAO,sBAAsB,YAAY,GAAG,KAAK,OAAO;AAC5F,YAAM,OAAO,IAAI,UAAU,IAAI,CAAC,QAAQ,IAAI,GAAG,GAAG,EAAE,KAAK,IAAI,CAAC;AAC9D,YAAM,+BAA+B,KAAK,kBAAkB,IAAI,qBAAqB,UAAU;AAAA,IACnG;AAAA,EACJ;AACA,YAAUuB,aAAY,QAAQ,YAAY,OAAO;AAC7C,UAAM,GAAG,KAAK;AAAA,EAClB;AACA,YAAUC,YAAW,QAAQ,YAAY,OAAO;AAC5C,UAAM,OAAO,aAAa,KAAK;AAC/B,QAAI,SAAS,OAAO,aAAa;AAC7B,YAAM,8BAA8B,KAAK,eAAe,OAAO,aAAa;AAChF,QAAI,SAAS,OAAO,aAAa;AAC7B,YAAM,8BAA8B,KAAK,eAAe,OAAO,aAAa;AAChF,UAAM,CAAC,YAAY,aAAa,IAAI,OAAO,QAAQ,OAAO,iBAAiB,EAAE,CAAC;AAC9E,UAAM,WAAW,eAAe,GAAG,IAAI,OAAO,UAAU,CAAC,EAAE;AAC3D,UAAM,SAAS,iBAAiB,eAAe,YAAY,OAAO;AAClE,UAAM,SAASpB,UAAS,OAAO,oBAAoB,IAAI,iBAAiB,OAAO,sBAAsB,YAAY,KAAK,IAAI,OAAO,yBAAyB,QAAQ,UAAU;AAC5K,UAAM,aAAa,IAAI,QAAQ,gBAAgB,MAAM,MAAM,MAAM;AACjE,UAAM,mBAAmB,KAAK,6BAA6B,UAAU;AAAA,EACzE;AACA,YAAUqB,SAAQ,QAAQ,YAAY,OAAO;AACzC,UAAM,SAAS,MAAM,QAAQ,UAAU;AAGvC,QAAI,MAAM,UAAU,IAAI,OAAO,IAAI;AAC/B,aAAO,MAAM,GAAG,mBAAmB,OAAO,IAAI,CAAC,IAAI,KAAK;AAC5D,WAAOhB,OAAM,QAAQ,YAAY,KAAK;AAAA,EAC1C;AACA,YAAUiB,YAAW,QAAQ,YAAY,OAAO;AAC5C,UAAM,WAAW,eAAe,GAAG,IAAI,OAAO,OAAO,QAAQ,OAAO,KAAK,CAAC,GAAG;AAC7E,UAAM,WAAW,KAAK;AACtB,QAAI,SAAS,OAAO,SAAS;AACzB,YAAM,GAAG,KAAK,cAAc,OAAO,SAAS;AAChD,QAAI,SAAS,OAAO,SAAS;AACzB,YAAM,GAAG,KAAK,cAAc,OAAO,SAAS;AAChD,UAAM,GAAG,QAAQ,SAAS,KAAK;AAAA,EACnC;AACA,YAAUC,YAAW,QAAQ,YAAY,OAAO;AAC5C,UAAM,WAAW,KAAK;AACtB,QAAI,SAAS,OAAO,SAAS;AACzB,YAAM,GAAG,KAAK,cAAc,OAAO,SAAS;AAChD,QAAI,SAAS,OAAO,SAAS;AACzB,YAAM,GAAG,KAAK,cAAc,OAAO,SAAS;AAChD,QAAI,OAAO,YAAY,QAAW;AAC9B,YAAM,WAAW,eAAe,GAAG,IAAI,OAAO,OAAO,OAAO,CAAC,GAAG;AAChE,YAAM,GAAG,QAAQ,SAAS,KAAK;AAAA,IACnC;AACA,QAAI,OAAO,WAAW,QAAW;AAC7B,YAAM,WAAW,OAAO,MAAM,MAAM,KAAK;AAAA,IAC7C;AAAA,EACJ;AACA,YAAUC,YAAW,QAAQ,YAAY,OAAO;AAC5C,UAAM,WAAW,KAAK;AAAA,EAC1B;AACA,YAAUC,qBAAoB,QAAQ,YAAY,OAAO;AACrD,UAAM,WAAW,KAAK;AACtB,UAAM,WAAW,eAAe,GAAG,IAAI,OAAO,OAAO,OAAO,CAAC,GAAG;AAChE,UAAM,GAAG,QAAQ,SAAS,KAAK;AAAA,EACnC;AACA,YAAUC,UAAS,QAAQ,YAAY,OAAO;AAE1C,UAAM,GAAG,mBAAmB,OAAO,IAAI,CAAC,IAAI,KAAK;AAAA,EACrD;AACA,YAAUC,WAAU,QAAQ,YAAY,OAAO;AAC3C,UAAM,iBAAiB,KAAK;AAC5B,QAAI,OAAO,UAAU;AACjB,aAAO,MAAM,GAAG,KAAK;AACzB,UAAM,IAAI,KAAK,eAAe,OAAO,QAAQ;AAC7C,aAAS,IAAI,GAAG,IAAI,OAAO,MAAM,QAAQ,KAAK;AAC1C,YAAM,aAAa,iBAAiB,OAAO,MAAM,CAAC,GAAG,YAAY,GAAG,KAAK,IAAI,CAAC,GAAG;AACjF,YAAM,GAAG,UAAU;AAAA,IACvB;AAAA,EACJ;AACA,YAAUC,eAAc,QAAQ,YAAY,OAAO;AAC/C,UAAM,GAAG,KAAK;AAAA,EAClB;AACA,YAAUC,WAAU,QAAQ,YAAY,OAAO;AAC3C,UAAM,cAAc,OAAO,MAAM,IAAI,CAAClB,YAAW,iBAAiBA,SAAQ,YAAY,KAAK,CAAC;AAC5F,UAAM,IAAI,YAAY,KAAK,MAAM,CAAC;AAAA,EACtC;AACA,YAAUmB,gBAAe,QAAQ,YAAY,OAAO;AAChD,UAAM,GAAG,KAAK;AACd,QAAI,SAAS,OAAO,aAAa;AAC7B,YAAM,IAAI,KAAK,cAAc,OAAO,aAAa;AACrD,QAAI,SAAS,OAAO,aAAa;AAC7B,YAAM,IAAI,KAAK,cAAc,OAAO,aAAa;AAAA,EACzD;AACA,YAAUC,aAAY,QAAQ,YAAY,OAAO;AAC7C,UAAM;AAAA,EACV;AACA,YAAUC,UAAS,QAAQ,YAAY,OAAO;AAC1C,UAAM,OAAO,WAAW,KAAK;AAAA,EACjC;AACA,YAAUC,UAAS,QAAQ,YAAY,OAAO;AAC1C,UAAM,WAAW,MAAM,UAAU;AACjC,UAAM,UAAU,IAAI,UAAU,MAAM;AACpC,UAAM,SAAS,OAAO,IAAI,CAAC,MAAM,QAAQ,KAAK,KAAK;AAAA,EACvD;AACA,YAAU5B,OAAM,QAAQ,YAAY,OAAO,cAAc,MAAM;AAC3D,UAAM,cAAc,SAAS,OAAO,GAAG,IAAI,CAAC,GAAG,YAAY,MAAM,IAAI;AACrE,UAAM,UAAU;AAIhB,QAAI,eAAe,SAAS,OAAO,GAAG,GAAG;AACrC,YAAM,eAAe,mBAAmB,OAAO,GAAG;AAClD,UAAI,MAAM,UAAU,IAAI,YAAY,GAAG;AACnC,eAAO,MAAM,GAAG,YAAY,IAAI,KAAK;AAAA,MACzC,OACK;AAID,cAAM,UAAU,IAAI,cAAc,YAAY;AAC9C,cAAM,eAAe,eAAe,cAAc,QAAQ,YAAY,SAAS,KAAK;AACpF,cAAM,UAAU,IAAI,cAAc,YAAY;AAC9C,eAAO,MAAM,GAAG,YAAY,IAAI,KAAK;AAAA,MACzC;AAAA,IACJ;AACA,YAAQ,QAAQ,IAAI,GAAG;AAAA,MACnB,KAAK;AACD,eAAO,OAAOR,SAAQ,SAAS,aAAa,KAAK;AAAA,MACrD,KAAK;AACD,eAAO,OAAOC,cAAa,SAAS,aAAa,KAAK;AAAA,MAC1D,KAAK;AACD,eAAO,OAAOC,WAAU,SAAS,aAAa,KAAK;AAAA,MACvD,KAAK;AACD,eAAO,OAAOE,mBAAkB,SAAS,aAAa,KAAK;AAAA,MAC/D,KAAK;AACD,eAAO,OAAOC,YAAW,SAAS,aAAa,KAAK;AAAA,MACxD,KAAK;AACD,eAAO,OAAOC,aAAY,SAAS,aAAa,KAAK;AAAA,MACzD,KAAK;AACD,eAAO,OAAOC,iBAAgB,SAAS,aAAa,KAAK;AAAA,MAC7D,KAAK;AACD,eAAO,OAAOE,UAAS,SAAS,aAAa,KAAK;AAAA,MACtD,KAAK;AACD,eAAO,OAAOC,cAAa,SAAS,aAAa,KAAK;AAAA,MAC1D,KAAK;AACD,eAAO,OAAOC,YAAW,SAAS,aAAa,KAAK;AAAA,MACxD,KAAK;AACD,eAAO,OAAOC,aAAY,SAAS,aAAa,KAAK;AAAA,MACzD,KAAK;AACD,eAAO,OAAOC,eAAc,SAAS,aAAa,KAAK;AAAA,MAC3D,KAAK;AACD,eAAO,OAAOE,cAAa,SAAS,aAAa,KAAK;AAAA,MAC1D,KAAK;AACD,eAAO,OAAOC,aAAY,SAAS,aAAa,KAAK;AAAA,MACzD,KAAK;AACD,eAAO,OAAOC,WAAU,SAAS,aAAa,KAAK;AAAA,MACvD,KAAK;AACD,eAAO,OAAOC,SAAQ,SAAS,aAAa,KAAK;AAAA,MACrD,KAAK;AACD,eAAO,OAAOC,UAAS,SAAS,aAAa,KAAK;AAAA,MACtD,KAAK;AACD,eAAO,OAAOC,YAAW,SAAS,aAAa,KAAK;AAAA,MACxD,KAAK;AACD,eAAO,OAAOC,YAAW,SAAS,aAAa,KAAK;AAAA,MACxD,KAAK;AACD,eAAO,OAAOC,aAAY,SAAS,aAAa,KAAK;AAAA,MACzD,KAAK;AACD,eAAO,OAAOC,YAAW,SAAS,aAAa,KAAK;AAAA,MACxD,KAAK;AACD,eAAO,OAAOC,SAAQ,SAAS,aAAa,KAAK;AAAA,MACrD,KAAK;AACD,eAAO,OAAOC,YAAW,SAAS,aAAa,KAAK;AAAA,MACxD,KAAK;AACD,eAAO,OAAOC,YAAW,SAAS,aAAa,KAAK;AAAA,MACxD,KAAK;AACD,eAAO,OAAOC,YAAW,SAAS,aAAa,KAAK;AAAA,MACxD,KAAK;AACD,eAAO,OAAOC,qBAAoB,SAAS,aAAa,KAAK;AAAA,MACjE,KAAK;AACD,eAAO,OAAOC,UAAS,SAAS,aAAa,KAAK;AAAA,MACtD,KAAK;AACD,eAAO,OAAOC,WAAU,SAAS,aAAa,KAAK;AAAA,MACvD,KAAK;AACD,eAAO,OAAOC,eAAc,SAAS,aAAa,KAAK;AAAA,MAC3D,KAAK;AACD,eAAO,OAAOC,WAAU,SAAS,aAAa,KAAK;AAAA,MACvD,KAAK;AACD,eAAO,OAAOC,gBAAe,SAAS,aAAa,KAAK;AAAA,MAC5D,KAAK;AACD,eAAO,OAAOC,aAAY,SAAS,aAAa,KAAK;AAAA,MACzD,KAAK;AACD,eAAO,OAAOC,UAAS,SAAS,aAAa,KAAK;AAAA,MACtD;AACI,YAAI,CAAC,aAAa,IAAI,QAAQ,IAAI,CAAC;AAC/B,gBAAM,IAAI,6BAA6B,MAAM;AACjD,eAAO,OAAOC,UAAS,SAAS,aAAa,KAAK;AAAA,IAC1D;AAAA,EACJ;AAKA,QAAM,QAAQ;AAAA,IACV,UAAU;AAAA;AAAA,IACV,WAAW,oBAAI,IAAI;AAAA;AAAA,IACnB,WAAW,oBAAI,IAAI;AAAA;AAAA,IACnB,WAAW,oBAAI,IAAI;AAAA;AAAA,EACvB;AAIA,WAAS,iBAAiB,QAAQ,YAAY,OAAO,cAAc,MAAM;AACrE,WAAO,IAAI,CAAC,GAAG5B,OAAM,QAAQ,YAAY,OAAO,WAAW,CAAC,EAAE,KAAK,MAAM,CAAC;AAAA,EAC9E;AACA,WAAS,mBAAmB,KAAK;AAC7B,WAAO,SAAS,WAAW,OAAO,GAAG,CAAC;AAAA,EAC1C;AACA,WAAS,eAAe,YAAY;AAChC,UAAM,eAAe,SAAS,MAAM,UAAU,IAAI;AAClD,UAAM,UAAU,IAAI,cAAc,SAAS,YAAY,MAAM,UAAU,EAAE;AACzE,WAAO;AAAA,EACX;AACA,WAAS,eAAe,MAAM,QAAQ,YAAY,OAAO,cAAc,MAAM;AACzE,UAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,SAAS,QAAQ,GAAG,CAAC;AAClE,UAAM,YAAY,gBAAgB,SAAS,KAAK;AAChD,UAAM,UAAU,cAAc,SAAS;AACvC,UAAM,aAAa,CAAC,GAAGA,OAAM,QAAQ,YAAY,OAAO,WAAW,CAAC,EAAE,IAAI,CAAC6B,gBAAe,GAAG,IAAI,CAAC,CAAC,GAAGA,WAAU,EAAE,EAAE,KAAK,MAAM,OAAO,EAAE;AACxI,WAAO,YAAY,IAAI,IAAI,SAAS,IAAI,OAAO,KAAK,OAAO,GAAG,IAAI,CAAC,CAAC,WAAW,OAAO,GAAG,UAAU,GAAG,OAAO,GAAG,IAAI,CAAC,CAAC;AAAA;AAAA,EAC1H;AACA,WAAS,gBAAgB,MAAM,MAAM;AACjC,UAAM,aAAa,MAAM,aAAa,eAAe,KAAK,IAAI,KAAK;AACnE,WAAO,GAAG,IAAI,GAAG,UAAU;AAAA,EAC/B;AACA,WAAS,cAAc,MAAM;AACzB,WAAO,MAAM,aAAa,eAAe,KAAK,IAAI,KAAK;AAAA,EAC3D;AAIA,WAAS,MAAM,QAAQ,YAAY,SAAS;AACxC,UAAM,eAAe,eAAe,SAAS,QAAQ,YAAY,OAAO;AACxE,UAAM,YAAY,gBAAgB,SAAS,KAAK;AAChD,UAAM,UAAU,cAAc,SAAS;AACvC,UAAM,YAAY,CAAC,GAAG,MAAM,UAAU,OAAO,CAAC;AAC9C,UAAM,YAAY,CAAC,GAAG,MAAM,UAAU,OAAO,CAAC;AAE9C,UAAM,gBAAgB,SAAS,OAAO,GAAG,IACnC,yBAAyB,SAAS,IAAI,OAAO;AAAA,WAAgB,mBAAmB,OAAO,GAAG,CAAC;AAAA,KAC3F,UAAU,YAAY;AAC5B,WAAO,CAAC,GAAG,WAAW,GAAG,WAAW,aAAa,EAAE,KAAK,IAAI;AAAA,EAChE;AAEA,WAAS,QAAQ,MAAM;AACnB,UAAM,WAAW,EAAE,UAAU,aAAa;AAE1C,UAAM,CAAC,QAAQ,YAAY,OAAO,IAAK,KAAK,WAAW,KAAK,QAAQ,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,IACtG,KAAK,WAAW,KAAK,CAAC,QAAQ,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,IAC1D,KAAK,WAAW,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,IAC1C,KAAK,WAAW,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,IACtC,CAAC,MAAM,CAAC,GAAG,QAAQ;AAEnC,UAAM,WAAW,QAAQ;AACzB,UAAM,UAAU,MAAM;AACtB,UAAM,UAAU,MAAM;AACtB,UAAM,UAAU,MAAM;AACtB,QAAI,CAAClC,UAAS,MAAM;AAChB,YAAM,IAAI,2BAA2B,MAAM;AAC/C,eAAWW,WAAU;AACjB,UAAI,CAACX,UAASW,OAAM;AAChB,cAAM,IAAI,2BAA2BA,OAAM;AACnD,WAAO,MAAM,QAAQ,YAAY,OAAO;AAAA,EAC5C;AACA,EAAAhB,cAAa,OAAO;AAEpB,WAAS,QAAQ,QAAQ,aAAa,CAAC,GAAG;AACtC,UAAM,gBAAgB,KAAK,QAAQ,YAAY,EAAE,UAAU,aAAa,CAAC;AACzE,UAAM,mBAAmB,WAAW,SAAS,QAAQ,UAAU,QAAQ,aAAa;AACpF,UAAM,YAAY,IAAI,IAAI,MAAM,SAAS;AACzC,aAAS,qBAAqB,MAAM,UAAU,OAAO;AACjD,UAAI,CAAC,aAAa,IAAI,IAAI,KAAK,CAAC,UAAU,IAAI,QAAQ;AAClD,eAAO;AACX,YAAM,YAAY,aAAa,IAAI,IAAI;AACvC,YAAMgB,UAAS,UAAU,IAAI,QAAQ;AACrC,aAAO,UAAUA,SAAQ,KAAK;AAAA,IAClC;AACA,aAAS,uBAAuB,QAAQ,OAAO;AAC3C,UAAI,CAAC,eAAe,IAAI,MAAM;AAC1B,eAAO;AACX,YAAM,YAAY,eAAe,IAAI,MAAM;AAC3C,aAAO,UAAU,KAAK;AAAA,IAC1B;AACA,aAAS,aAAa,OAAO;AACzB,aAAO,KAAK,KAAK;AAAA,IACrB;AACA,UAAM,gBAAgB,iBAAiB,sBAAsB,wBAAwB,YAAY;AACjG,WAAO,IAAI,UAAU,QAAQ,YAAY,eAAe,aAAa;AAAA,EACzE;AACA,EAAAhB,cAAa,UAAU;AAC3B,GAAG,iBAAiB,eAAe,CAAC,EAAE;", "names": ["TypeSystem", "target", "ByteMarker", "value", "Visit", "schema", "ValueErrorType", "IsDefined", "FromAny", "FromArgument", "FromArray", "Visit", "value", "FromAsyncIterator", "FromBigInt", "FromBoolean", "FromConstructor", "FromDate", "FromFunction", "FromImport", "FromInteger", "FromIntersect", "FromIterator", "FromLiteral", "FromNever", "FromNot", "FromNull", "FromNumber", "FromObject", "FromPromise", "FromRecord", "FromRef", "FromRegExp", "FromString", "FromSymbol", "FromTemplateLiteral", "FromThis", "FromTuple", "FromUndefined", "FromUnion", "FromUint8Array", "FromUnknown", "FromVoid", "FromKind", "FromArray", "value", "Visit", "FromIntersect", "FromImport", "FromNot", "FromObject", "IsUndefined", "FromRecord", "FromRef", "FromThis", "FromTuple", "schema", "FromUnion", "<PERSON><PERSON><PERSON>", "FromArray", "value", "Visit", "FromImport", "FromIntersect", "FromNot", "FromObject", "IsUndefined", "FromRecord", "FromRef", "FromThis", "FromTuple", "schema", "FromUnion", "FromArray", "Visit", "FromAsyncIterator", "FromConstructor", "schema", "FromFunction", "FromIntersect", "FromImport", "FromIterator", "FromNot", "FromObject", "FromPromise", "FromRecord", "FromRef", "FromThis", "FromTuple", "FromUnion", "Character", "MemberExpression", "Identifier", "LiteralString", "Policy", "TypeCompiler", "IsAnyOrUnknown", "FromAny", "FromArgument", "FromArray", "IsSchema", "FromAsyncIterator", "FromBigInt", "FromBoolean", "FromConstructor", "Visit", "FromDate", "FromFunction", "FromImport", "FromInteger", "FromIntersect", "schema", "FromIterator", "FromLiteral", "FromNever", "FromNot", "FromNull", "FromNumber", "FromObject", "FromPromise", "FromRecord", "FromRef", "FromRegExp", "FromString", "FromSymbol", "FromTemplateLiteral", "FromThis", "FromTuple", "FromUndefined", "FromUnion", "FromUint8Array", "FromUnknown", "FromVoid", "FromKind", "expression"]}