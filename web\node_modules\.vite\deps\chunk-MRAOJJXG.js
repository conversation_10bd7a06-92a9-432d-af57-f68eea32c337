import {
  camelCase_default,
  get_default,
  mergeWith_default
} from "./chunk-LK7GAOJV.js";
import {
  __export
} from "./chunk-KWPVD4H7.js";

// node_modules/@layerstack/utils/dist/object.js
var object_exports = {};
__export(object_exports, {
  camelCaseKeys: () => camelCaseKeys,
  distinctKeys: () => distinctKeys,
  expireObject: () => expireObject,
  isEmptyObject: () => isEmptyObject,
  isLiteralObject: () => isLiteralObject,
  keysByValues: () => keysByValues,
  mapKeys: () => mapKeys,
  mapValues: () => mapValues,
  merge: () => merge,
  nestedFindByPredicate: () => nestedFindByPredicate,
  objectId: () => objectId,
  omit: () => omit,
  omitNil: () => omitNil,
  pick: () => pick,
  propAccessor: () => propAccessor
});

// node_modules/@layerstack/utils/dist/typeHelpers.js
function fail(message) {
  throw new Error(message);
}
function keys(o) {
  return Object.keys(o);
}
function entries(o) {
  if (o instanceof Map)
    return Array.from(o.entries());
  return Object.entries(o);
}
function fromEntries(entries2) {
  return Object.fromEntries(entries2);
}
function enumKeys(E) {
  return keys(E).filter((k) => typeof E[k] === "number");
}
function enumValues(E) {
  const keys2 = enumKeys(E);
  return keys2.map((k) => E[k]);
}
function assertNever(x) {
  throw new Error(`Unhandled enum case: ${x}`);
}

// node_modules/@layerstack/utils/dist/object.js
function isLiteralObject(obj) {
  return obj && typeof obj === "object" && obj.constructor === Object;
}
function isEmptyObject(obj) {
  return isLiteralObject(obj) && keys(obj).length === 0;
}
function camelCaseKeys(obj) {
  return keys(obj).reduce((acc, key) => (acc[camelCase_default(key ? String(key) : void 0)] = obj[key], acc), {});
}
function nestedFindByPredicate(obj, predicate, childrenProp) {
  const getChildrenProp = propAccessor(childrenProp ?? "children");
  if (predicate(obj)) {
    return obj;
  } else {
    const children = getChildrenProp(obj);
    if (children) {
      for (let o of children) {
        const match = nestedFindByPredicate(o, predicate, childrenProp);
        if (match) {
          return match;
        }
      }
    }
  }
}
function propAccessor(prop) {
  return typeof prop === "function" ? prop : typeof prop === "string" ? (d) => get_default(d, prop) : (x) => x;
}
var objIdMap = /* @__PURE__ */ new WeakMap();
var objectCount = 0;
function objectId(object) {
  if (!objIdMap.has(object))
    objIdMap.set(object, ++objectCount);
  return objIdMap.get(object);
}
function distinctKeys(...objs) {
  return [...new Set(flatten(objs.map((x) => keys(x))))];
}
function flatten(items) {
  return items.reduce((prev, next) => prev.concat(next), []);
}
function merge(object, source) {
  return mergeWith_default(object, source, (objValue, srcValue) => {
    if (Array.isArray(srcValue)) {
      return srcValue;
    }
  });
}
function expireObject(object, expiry) {
  const now = /* @__PURE__ */ new Date();
  if (expiry instanceof Date || typeof object !== "object" || object == null) {
    if (expiry < now) {
      return null;
    }
    return object;
  }
  for (let [prop, propExpiry] of entries(expiry)) {
    if (propExpiry instanceof Date) {
      if (propExpiry < now) {
        if (prop === "$default") {
          for (let objProp of keys(object)) {
            if (!(objProp in expiry)) {
              delete object[objProp];
            }
          }
          delete object[prop];
        } else {
          delete object[prop];
        }
      } else {
      }
    } else {
      const value = object[prop];
      if (value && typeof value === "object") {
        expireObject(value, propExpiry);
        if (isEmptyObject(value)) {
          delete object[prop];
        }
      }
    }
  }
  return isEmptyObject(object) ? null : object;
}
function omit(obj, keys2) {
  if (keys2.length === 0) {
    return obj;
  } else {
    return fromEntries(entries(obj).filter(([key]) => !keys2.includes(key)));
  }
}
function omitNil(obj) {
  if (keys.length === 0) {
    return obj;
  } else {
    return fromEntries(entries(obj).filter(([key, value]) => value != null));
  }
}
function pick(obj, keys2) {
  if (keys2.length === 0) {
    return obj;
  } else {
    return fromEntries(keys2.filter((key) => key in obj).map((key) => [key, obj[key]]));
  }
}
function keysByValues(obj) {
  return fromEntries(entries(obj).map(([key, value]) => [String(value), key]));
}
function mapKeys(obj, fn) {
  return fromEntries(entries(obj).map(([key, value]) => [fn(key), value]));
}
function mapValues(obj, fn) {
  return fromEntries(entries(obj).map(([key, value]) => [key, fn(value)]));
}

export {
  fail,
  keys,
  entries,
  fromEntries,
  enumKeys,
  enumValues,
  assertNever,
  isLiteralObject,
  isEmptyObject,
  camelCaseKeys,
  nestedFindByPredicate,
  propAccessor,
  objectId,
  distinctKeys,
  merge,
  expireObject,
  omit,
  omitNil,
  pick,
  keysByValues,
  mapKeys,
  mapValues,
  object_exports
};
//# sourceMappingURL=chunk-MRAOJJXG.js.map
