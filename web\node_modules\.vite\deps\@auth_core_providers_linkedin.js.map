{"version": 3, "sources": ["../../@auth/core/providers/linkedin.js"], "sourcesContent": ["/**\n * Add LinkedIn login to your page.\n *\n * ### Setup\n *\n * #### Callback URL\n * ```\n * https://example.com/api/auth/callback/linkedin\n * ```\n *\n * #### Configuration\n *```ts\n * import { Auth } from \"@auth/core\"\n * import LinkedIn from \"@auth/core/providers/linkedin\"\n *\n * const request = new Request(origin)\n * const response = await Auth(request, {\n *   providers: [\n *     LinkedIn({\n *       clientId: LINKEDIN_CLIENT_ID,\n *       clientSecret: LINKEDIN_CLIENT_SECRET,\n *     }),\n *   ],\n * })\n * ```\n *\n * ### Resources\n *\n *  - [LinkedIn OAuth documentation](https://docs.microsoft.com/en-us/linkedin/shared/authentication/authorization-code-flow)\n *  - [LinkedIn app console](https://www.linkedin.com/developers/apps/)\n *\n * ### Notes\n *\n * By default, Auth.js assumes that the LinkedIn provider is\n * based on the [OIDC](https://openid.net/specs/openid-connect-core-1_0.html) specification.\n *\n * :::tip\n *\n * The LinkedIn provider comes with a [default configuration](https://github.com/nextauthjs/next-auth/blob/main/packages/core/src/providers/linkedin.ts).\n * To override the defaults for your use case, check out [customizing a built-in OAuth provider](https://authjs.dev/guides/configuring-oauth-providers).\n *\n * :::\n *\n * :::info **Disclaimer**\n *\n * If you think you found a bug in the default configuration, you can [open an issue](https://authjs.dev/new/provider-issue).\n *\n * Auth.js strictly adheres to the specification and it cannot take responsibility for any deviation from\n * the spec by the provider. You can open an issue, but if the problem is non-compliance with the spec,\n * we might not pursue a resolution. You can ask for more help in [Discussions](https://authjs.dev/new/github-discussions).\n *\n * :::\n */\nexport default function LinkedIn(options) {\n    return {\n        id: \"linkedin\",\n        name: \"LinkedIn\",\n        type: \"oidc\",\n        client: { token_endpoint_auth_method: \"client_secret_post\" },\n        issuer: \"https://www.linkedin.com/oauth\",\n        style: { bg: \"#069\", text: \"#fff\" },\n        checks: [\"state\"],\n        options,\n    };\n}\n"], "mappings": ";;;AAqDe,SAAR,SAA0B,SAAS;AACtC,SAAO;AAAA,IACH,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,QAAQ,EAAE,4BAA4B,qBAAqB;AAAA,IAC3D,QAAQ;AAAA,IACR,OAAO,EAAE,IAAI,QAAQ,MAAM,OAAO;AAAA,IAClC,QAAQ,CAAC,OAAO;AAAA,IAChB;AAAA,EACJ;AACJ;", "names": []}