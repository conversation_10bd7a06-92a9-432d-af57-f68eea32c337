import {
  AccessDenied,
  AccountNotLinked,
  AdapterError,
  AuthError,
  CallbackRouteError,
  CredentialsSignin,
  DuplicateConditionalUI,
  EmailSignInError,
  ErrorPageLoop,
  EventError,
  ExperimentalFeatureNotEnabled,
  InvalidCallbackUrl,
  InvalidCheck,
  InvalidEndpoints,
  InvalidProvider,
  JWTSessionError,
  MissingAdapter,
  MissingAdapterMethods,
  MissingAuthorize,
  MissingCSRF,
  MissingSecret,
  MissingWebAuthnAutocomplete,
  OAuthAccountNotLinked,
  OAuthCallbackError,
  OAuthProfileParseError,
  OAuthSignInError,
  SessionTokenError,
  SignInError,
  SignOutError,
  UnknownAction,
  UnsupportedStrategy,
  UntrustedHost,
  Verification,
  WebAuthnVerificationError,
  isClientError
} from "./chunk-R3PG7WB2.js";
import "./chunk-KWPVD4H7.js";
export {
  AccessDenied,
  AccountNotLinked,
  AdapterError,
  AuthError,
  CallbackRouteError,
  CredentialsSignin,
  DuplicateConditionalUI,
  EmailSignInError,
  ErrorPageLoop,
  EventError,
  ExperimentalFeatureNotEnabled,
  InvalidCallbackUrl,
  InvalidCheck,
  InvalidEndpoints,
  InvalidProvider,
  JWTSessionError,
  MissingAdapter,
  MissingAdapterMethods,
  MissingAuthorize,
  MissingCSRF,
  MissingSecret,
  MissingWebAuthnAutocomplete,
  OAuthAccountNotLinked,
  OAuthCallbackError,
  OAuthProfileParseError,
  OAuthSignInError,
  SessionTokenError,
  SignInError,
  SignOutError,
  UnknownAction,
  UnsupportedStrategy,
  UntrustedHost,
  Verification,
  WebAuthnVerificationError,
  isClientError
};
//# sourceMappingURL=@auth_core_errors.js.map
